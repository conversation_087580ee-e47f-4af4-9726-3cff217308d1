package com.thj.boot.common.config.redis;

import org.redisson.api.*;
import org.redisson.api.listener.MessageListener;
import org.redisson.api.listener.PatternMessageListener;
import org.redisson.api.listener.PatternStatusListener;
import org.redisson.api.listener.StatusListener;
import org.redisson.client.codec.Codec;
import org.redisson.config.Config;

import java.util.concurrent.TimeUnit;

/**
 * Mock RedissonClient implementation for local development without Redis
 */
public class MockRedissonClient implements RedissonClient {

    @Override
    public RLock getLock(String name) {
        return new MockRLock();
    }

    @Override
    public RLock getFairLock(String name) {
        return new MockRLock();
    }

    @Override
    public RReadWriteLock getReadWriteLock(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBucket<V> getBucket(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBucket<V> getBucket(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RBuckets getBuckets() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RBuckets getBuckets(Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RHyperLogLog<V> getHyperLogLog(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RHyperLogLog<V> getHyperLogLog(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RList<V> getList(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RList<V> getList(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <K, V> RMap<K, V> getMap(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <K, V> RMap<K, V> getMap(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RSet<V> getSet(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RSet<V> getSet(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RSortedSet<V> getSortedSet(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RSortedSet<V> getSortedSet(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RScoredSortedSet<V> getScoredSortedSet(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RScoredSortedSet<V> getScoredSortedSet(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RLexSortedSet getLexSortedSet(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RLexSortedSet getLexSortedSet(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RQueue<V> getQueue(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RQueue<V> getQueue(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RRingBuffer<V> getRingBuffer(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RRingBuffer<V> getRingBuffer(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBlockingQueue<V> getBlockingQueue(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBlockingQueue<V> getBlockingQueue(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RDeque<V> getDeque(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RDeque<V> getDeque(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBlockingDeque<V> getBlockingDeque(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <V> RBlockingDeque<V> getBlockingDeque(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RAtomicLong getAtomicLong(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RAtomicDouble getAtomicDouble(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RCountDownLatch getCountDownLatch(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RBitSet getBitSet(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RSemaphore getSemaphore(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RPermitExpirableSemaphore getPermitExpirableSemaphore(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <T> RBloomFilter<T> getBloomFilter(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public <T> RBloomFilter<T> getBloomFilter(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RScript getScript() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RScript getScript(Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RScheduledExecutorService getExecutorService(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RScheduledExecutorService getExecutorService(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RRemoteService getRemoteService() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RRemoteService getRemoteService(Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RRemoteService getRemoteService(String name) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RRemoteService getRemoteService(String name, Codec codec) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RTransaction createTransaction(TransactionOptions options) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RBatch createBatch(BatchOptions options) {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RBatch createBatch() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public RKeys getKeys() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public Config getConfig() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public NodesGroup<Node> getNodesGroup() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public NodesGroup<ClusterNode> getClusterNodesGroup() {
        throw new UnsupportedOperationException("Mock implementation");
    }

    @Override
    public void shutdown() {
        // Do nothing in mock mode
    }

    @Override
    public void shutdown(long quietPeriod, long timeout, TimeUnit unit) {
        // Do nothing in mock mode
    }

    @Override
    public boolean isShutdown() {
        return false;
    }

    @Override
    public boolean isShuttingDown() {
        return false;
    }

    // Additional methods that might be required
    @Override
    public String getId() {
        return "mock-redisson-client";
    }

    /**
     * Mock RLock implementation
     */
    private static class MockRLock implements RLock {
        @Override
        public boolean tryLock(long waitTime, TimeUnit unit) {
            return true; // Always succeed in mock mode
        }

        @Override
        public void unlock() {
            // Do nothing in mock mode
        }

        @Override
        public boolean isLocked() { return false; }

        @Override
        public boolean isHeldByCurrentThread() { return false; }

        @Override
        public void lock() {}

        @Override
        public void lockInterruptibly() {}

        @Override
        public boolean tryLock() { return true; }

        @Override
        public boolean tryLock(long time, TimeUnit unit) { return true; }

        @Override
        public java.util.concurrent.locks.Condition newCondition() { return null; }

        @Override
        public String getName() { return "mock-lock"; }

        @Override
        public void forceUnlock() {}

        @Override
        public boolean isExists() { return false; }

        @Override
        public int getHoldCount() { return 0; }

        @Override
        public long remainTimeToLive() { return -1; }
    }
}
