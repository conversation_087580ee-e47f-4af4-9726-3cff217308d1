package com.thj.boot.common.config.redis;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis禁用配置类
 */
@Configuration
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "false")
public class RedisDisableConfig {

    /**
     * 创建 RedisTemplate Bean，使用 JDK 序列化
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        // 创建 RedisTemplate 对象
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        // 设置 Key 的序列化 - String 序列化，适合 String 类型的 Key
        redisTemplate.setKeySerializer(RedisSerializer.string());
        redisTemplate.setHashKeySerializer(RedisSerializer.string());
        // 设置 Value 的序列化 - JSON 序列化，适合 Object 类型的 Value
        redisTemplate.setValueSerializer(RedisSerializer.json());
        redisTemplate.setHashValueSerializer(RedisSerializer.json());
        return redisTemplate;
    }

    /**
     * 创建 RedisConnectionFactory Bean
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        return new MockRedisConnectionFactory();
    }

    /**
     * 创建 RedissonClient Bean - 返回null，让依赖的服务处理
     */
    @Bean
    @Primary
    public RedissonClient redissonClient() {
        return null; // 返回null，让依赖的服务自己处理
    }
}
