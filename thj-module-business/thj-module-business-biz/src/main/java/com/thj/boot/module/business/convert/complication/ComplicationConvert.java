package com.thj.boot.module.business.convert.complication;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.complication.ComplicationDO;
import com.thj.boot.module.business.pojo.complication.vo.ComplicationCreateReqVO;
import com.thj.boot.module.business.pojo.complication.vo.ComplicationRespVO;
import com.thj.boot.module.business.pojo.complication.vo.ComplicationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 并发症及处理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ComplicationConvert {

    ComplicationConvert INSTANCE = Mappers.getMapper(ComplicationConvert.class);

    ComplicationDO convert(ComplicationCreateReqVO bean);

    ComplicationDO convert(ComplicationUpdateReqVO bean);

    ComplicationRespVO convert(ComplicationDO bean);

    List<ComplicationRespVO> convertList(List<ComplicationDO> list);

    PageResult<ComplicationRespVO> convertPage(PageResult<ComplicationDO> page);


}
