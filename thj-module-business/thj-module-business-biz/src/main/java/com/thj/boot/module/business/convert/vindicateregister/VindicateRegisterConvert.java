package com.thj.boot.module.business.convert.vindicateregister;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.vindicateregister.VindicateRegisterDO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterRespVO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 维护登记-透析机-水处理机-CCDS-CDDS Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VindicateRegisterConvert {

    VindicateRegisterConvert INSTANCE = Mappers.getMapper(VindicateRegisterConvert.class);

    VindicateRegisterDO convert(VindicateRegisterCreateReqVO bean);

    VindicateRegisterDO convert(VindicateRegisterUpdateReqVO bean);

    VindicateRegisterRespVO convert(VindicateRegisterDO bean);

    List<VindicateRegisterRespVO> convertList(List<VindicateRegisterDO> list);

    PageResult<VindicateRegisterRespVO> convertPage(PageResult<VindicateRegisterDO> page);


}
