package com.thj.boot.module.business.pojo.dialysismanager.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisManagerPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 透析处方
     */
    private String prescription;
    /**
     * 透前评估
     */
    private String beforeEvaluate;
    /**
     * 双人核对
     */
    private String doubleCheck;
    /**
     * 透后评估
     */
    private String afterEvaluate;
    /**
     * 治疗小结
     */
    private String recoverSummary;
    /**
     * 0-未确认，1-已确认（透析处方）
     */
    private String prescriptionState;
    /**
     * 透析处方确认人
     */
    private String prescriptionUser;
    /**
     * 0-未确认，1-已确认（透前评估）
     */
    private String beforeEvaluateSate;
    /**
     * 透前评估确认人（透前评估）
     */
    private String beforeEvaluateUser;
    /**
     * 0-未确认，1-已确认（双人核对）
     */
    private String doubleCheckState;
    /**
     * 双人核对确认人（双人核对）
     */
    private String doubleCheckUser;
    /**
     * 0-未确认，1-已确认（透后评估）
     */
    private String afterEvaluateSate;
    /**
     * 透后评估确认人（透后评估）
     */
    private String afterEvaluateUser;
    /**
     * 0-未确认，1-已确认（治疗小结）
     */
    private String recoverSummaryState;
    /**
     * 治疗小结确认人（治疗小结）
     */
    private String recoverSummaryUser;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 透析日期
     */
    private Date dateWeek;
    /**
     * 班次
     */
    private String weekDay;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 类型：1-透析耗材，2-上机参数，3-药品，4-汇总
     */
    private Integer type;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 透析方式
     */
    private String dialysisMode;
    /**
     * 患者来源
     */
    private String patientSource;
}
