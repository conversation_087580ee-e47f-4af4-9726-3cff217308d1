package com.thj.boot.module.business.service.customform;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.customform.CustomFormConvert;
import com.thj.boot.module.business.dal.datado.customform.CustomFormDO;
import com.thj.boot.module.business.dal.mapper.customform.CustomFormMapper;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormCreateReqVO;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormPageReqVO;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 自定义表单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustomFormServiceImpl implements CustomFormService {

    @Resource
    private CustomFormMapper customFormMapper;

    @Override
    public Long createCustomForm(CustomFormCreateReqVO createReqVO) {
        // 插入
        CustomFormDO customForm = CustomFormConvert.INSTANCE.convert(createReqVO);
        customFormMapper.insert(customForm);
        // 返回
        return customForm.getId();
    }

    @Override
    public void updateCustomForm(CustomFormUpdateReqVO updateReqVO) {
        // 更新
        CustomFormDO updateObj = CustomFormConvert.INSTANCE.convert(updateReqVO);
        customFormMapper.updateById(updateObj);
    }

    @Override
    public void deleteCustomForm(Long id) {
        // 删除
        customFormMapper.deleteById(id);
    }


    @Override
    public CustomFormDO getCustomForm(Long id) {
        return customFormMapper.selectById(id);
    }

    @Override
    public List<CustomFormDO> getCustomFormList(Collection<Long> ids) {
        return customFormMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<CustomFormDO> getCustomFormPage(CustomFormPageReqVO pageReqVO) {
        return customFormMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CustomFormDO> getCustomFormList(CustomFormCreateReqVO createReqVO) {
        return customFormMapper.selectList(createReqVO);
    }

}
