package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 透析状态明细
 */
@Data
public class Outcome {

    /**
     * 透析状态
     */
    @JsonProperty("OUTCOME_STATUS")
    @NotNull
    private String outcomeStatus;

    /**
     * 转归日期 (YYYY-MM-DD)
     */
    @JsonProperty("OUTCOME_DATE")
    private String outcomeDate;

    /**
     * 主要死亡原因（透析状态_患者状态选死亡时显示）
     */
    @JsonProperty("OUTCOME_DEATH_CAUSE")
    private String deathCause;

    /**
     * 省（透析状态_转出情况选转其它透析机构时显示）
     */
    @JsonProperty("OUTCOME_TRANSTO_PROVINCE")
    private String transtoProvince;

    /**
     * 市（透析状态_转出情况选转其它透析机构时显示）
     */
    @JsonProperty("OUTCOME_TRANSTO_CITY")
    private String transtoCity;

    /**
     * 透析机构名称（透析状态_转出情况选转其它透析机构时显示）
     */
    @JsonProperty("OUTCOME_TRANSTO_FACILITY")
    private String transtoFacility;

}
