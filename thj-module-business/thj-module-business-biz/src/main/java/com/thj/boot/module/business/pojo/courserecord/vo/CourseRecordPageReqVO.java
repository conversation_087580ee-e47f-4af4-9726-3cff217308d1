package com.thj.boot.module.business.pojo.courserecord.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CourseRecordPageReqVO extends PageParam {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 患者id
     */
    private Integer patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 病程医生
     */
    private Long userId;
    /**
     * 病程名称(字典获取)
     */
    private String name;
    /**
     * 病程内容
     */
    private String content;
    /**
     * 打印状态0-未打印，1-已打印(字典获取)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 记录结束时间
     */
    private Date endTime;

}
