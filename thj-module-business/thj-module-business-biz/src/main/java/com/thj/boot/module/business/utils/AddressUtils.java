package com.thj.boot.module.business.utils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AddressUtils {

    public static String[] parseProvinceCityDistrict(String address) {
        // 正则表达式，匹配省、市、区
        String regex = "(?<province>[^省]+省|[^自治区]+自治区|[^市]+市)(?<city>[^市]+市|[^县]+县)(?<district>[^区]+区|[^县]+县)?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(address);

        if (matcher.find()) {
            String province = matcher.group("province");
            String city = matcher.group("city");
            String district = matcher.group("district"); // 可能为null

            return new String[]{province, city, district == null ? "" : district};
        }

        return new String[]{"", "", ""};
    }
}
