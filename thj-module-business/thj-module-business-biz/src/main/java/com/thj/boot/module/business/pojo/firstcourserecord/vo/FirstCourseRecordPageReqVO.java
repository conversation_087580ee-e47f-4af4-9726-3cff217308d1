package com.thj.boot.module.business.pojo.firstcourserecord.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FirstCourseRecordPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 时间
     */
    private Date startTime;
    /**
     * 主诉
     */
    private String mainSuit;
    /**
     * 现病史
     */
    private String nowDiseaseHistory;
    /**
     * 既往史
     */
    private String oldDiseaseHistory;
    /**
     * 体格检查
     */
    private String bodyInspect;
    /**
     * 鉴别诊断
     */
    private String identifyDiagnosis;
    /**
     * 诊疗计划
     */
    private String healingProject;
    /**
     * 初步诊断
     */
    private String initDiagnosis;
    /**
     * 医师签名
     */
    private String signature;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 备注
     */
    private String remark;

}
