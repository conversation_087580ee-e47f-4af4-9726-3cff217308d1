package com.thj.boot.module.business.convert.renalcheckinfo;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.renalexaminfo.RenalCheckInfoDO;
import com.thj.boot.module.business.pojo.renalcheckinfo.vo.RenalCheckInfoCreateReqVO;
import com.thj.boot.module.business.pojo.renalcheckinfo.vo.RenalCheckInfoExcelVO;
import com.thj.boot.module.business.pojo.renalcheckinfo.vo.RenalCheckInfoRespVO;
import com.thj.boot.module.business.pojo.renalcheckinfo.vo.RenalCheckInfoUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 肾科检查项目信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RenalCheckInfoConvert {

    RenalCheckInfoConvert INSTANCE = Mappers.getMapper(RenalCheckInfoConvert.class);

    RenalCheckInfoDO convert(RenalCheckInfoCreateReqVO bean);

    RenalCheckInfoDO convert(RenalCheckInfoUpdateReqVO bean);

    RenalCheckInfoRespVO convert(RenalCheckInfoDO bean);

    List<RenalCheckInfoRespVO> convertList(List<RenalCheckInfoDO> list);

    PageResult<RenalCheckInfoRespVO> convertPage(PageResult<RenalCheckInfoDO> page);

    List<RenalCheckInfoExcelVO> convertList02(List<RenalCheckInfoDO> list);

}
