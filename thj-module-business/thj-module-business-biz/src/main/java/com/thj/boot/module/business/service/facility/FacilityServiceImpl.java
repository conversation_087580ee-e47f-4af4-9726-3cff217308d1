package com.thj.boot.module.business.service.facility;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.facility.vo.FacilityExcelVO;
import com.thj.boot.module.business.convert.facility.FacilityConvert;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitygroup.FacilityGroupDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.mapper.disinfectionplan.DisinfectionPlanMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitygroup.FacilityGroupMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.pojo.facility.vo.FacilityCreateReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityPageReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityUpdateReqVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备-机号设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FacilityServiceImpl implements FacilityService {

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private FacilitySubareaMapper subareaMapper;

    @Resource
    private FacilityGroupMapper groupMapper;

    @Resource
    private DisinfectionPlanMapper disinfectionPlanMapper;

    @Resource
    private FacilityManagerMapper managerMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Override
    public Long createFacility(FacilityCreateReqVO createReqVO) {
        //校验机号唯一性
        checkCreateNameUnique(createReqVO);
        // 插入
        FacilityDO facility = FacilityConvert.INSTANCE.convert(createReqVO);
        //分区名称
        FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(facility.getSubareaId());
        facility.setSubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());
        //分组名称
        FacilityGroupDO facilityGroupDO = groupMapper.selectById(facility.getGroupId());
        facility.setGroupName(facilityGroupDO == null ? null : facilityGroupDO.getName());
        facilityMapper.insert(facility);
        // 返回
        return facility.getId();
    }

    private void checkCreateNameUnique(FacilityCreateReqVO createReqVO) {
        Long count = facilityMapper.selectCount(FacilityDO::getCode, createReqVO.getCode());
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.BOLLD_NO_EXITS);
        }
    }

    @Override
    public void updateFacility(FacilityUpdateReqVO updateReqVO) {
        checkUpdateNameUnique(updateReqVO);
        // 更新
        FacilityDO updateObj = FacilityConvert.INSTANCE.convert(updateReqVO);
        //分区名称
        FacilitySubareaDO facilitySubareaDO = subareaMapper.selectById(updateObj.getSubareaId());
        updateObj.setSubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());
        //分组名称
        FacilityGroupDO facilityGroupDO = groupMapper.selectById(updateObj.getGroupId());
        updateObj.setGroupName(facilityGroupDO == null ? null : facilityGroupDO.getName());
        facilityMapper.updateById(updateObj);
    }

    private void checkUpdateNameUnique(FacilityUpdateReqVO updateReqVO) {
        FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getCode, updateReqVO.getCode());
        if (facilityDO != null && !facilityDO.getId().equals(updateReqVO.getId())) {
            throw new ServiceException(GlobalErrorCodeConstants.NAME_EXITS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFacility(Long id) {
        // 删除
        facilityMapper.deleteById(id);
        //删除机号对应的消毒计划
        disinfectionPlanMapper.delete(new LambdaQueryWrapperX<DisinfectionPlanDO>().eq(DisinfectionPlanDO::getFacilityId, id));
    }


    @Override
    public FacilityDO getFacility(Long id) {
        return facilityMapper.selectById(id);
    }

    @Override
    public List<FacilityDO> getFacilityList(Collection<Long> ids) {
        return facilityMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<FacilityDO> getFacilityPage(FacilityPageReqVO pageReqVO) {
        return facilityMapper.selectPage(pageReqVO);
    }

    @Override
    public List<FacilityDO> getFacilityList(FacilityCreateReqVO createReqVO) {
        return facilityMapper.selectList(createReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFacility(List<FacilityExcelVO> facilityExcelVOS) {
        facilityExcelVOS.remove(0);
        if (CollectionUtil.isEmpty(facilityExcelVOS)) {
            throw new ServiceException(GlobalErrorCodeConstants.IMPORT_EXCEL_NOT_EMPTY);
        }
        int num = 2;
        List<FacilityManagerDO> managerDOS = Lists.newArrayList();
        for (FacilityExcelVO facilityExcelVO : facilityExcelVOS) {
            num++;
            //校验机号不能为
            if (StrUtil.isEmpty(facilityExcelVO.getFacilityNames())) {
                throw new ServiceException(GlobalErrorCodeConstants.FACILITY_CODE_EMPTY);
            }
            //透析分区不能为空
            if (StrUtil.isEmpty(facilityExcelVO.getFacilitySubName())) {
                throw new ServiceException(GlobalErrorCodeConstants.FACILITY_CODE_EMPTY);
            }
            //治疗模式不能为空
            if (StrUtil.isEmpty(facilityExcelVO.getTreatmentMode())) {
                throw new ServiceException(GlobalErrorCodeConstants.FACILITY_TREAT_EMPTY);
            }
            //序列号不能为空
            if (StrUtil.isEmpty(facilityExcelVO.getCode())) {
                throw new ServiceException(GlobalErrorCodeConstants.FACILITY_XULIE_EMPTY);
            }
            //判断机号唯一性
            FacilityDO facilityDO = facilityMapper.selectOne(new LambdaQueryWrapperX<FacilityDO>().eq(FacilityDO::getCode, facilityExcelVO.getFacilityNames()));
            if (facilityDO != null) {
                throw new ServiceException(GlobalErrorCodeConstants.FACILITY_CODE_CUNZAI, num + "", facilityExcelVO.getFacilityNames());
            }
            //判断透析分区名称是否存在
            FacilitySubareaDO facilitySubareaDO = subareaMapper.selectOne(FacilitySubareaDO::getName, facilityExcelVO.getFacilitySubName());
            if (facilitySubareaDO == null) {
                throw new ServiceException(GlobalErrorCodeConstants.EXITS_TOUXI, num + "", facilityExcelVO.getFacilitySubName());
            }
            //判断序列号是否唯一
            Long code = managerMapper.selectCount(FacilityManagerDO::getCode, facilityExcelVO.getCode());
            if (code > 0) {
                throw new ServiceException(GlobalErrorCodeConstants.EXITS_CODE, num + "", facilityExcelVO.getCode());
            }
            //新增机号设置
            FacilityDO facilityDO1 = new FacilityDO();
            facilityDO1.setCode(facilityExcelVO.getFacilityNames());
            facilityDO1.setSubareaId(facilitySubareaDO.getId());
            facilityDO1.setSort(1);
            facilityDO1.setSubareaName(facilitySubareaDO.getName());
            facilityMapper.insert(facilityDO1);
            //新增设备日常管理
            FacilityManagerDO managerDO = new FacilityManagerDO();
            List<String> collect = Arrays.stream(facilityExcelVO.getTreatmentMode().split(",")).collect(Collectors.toList());
            List<DictDataRespDTO> dialyzeWay = dictDataApi.getDictDataListByBatchLabel(collect, "dialyze_way");
            if (CollectionUtil.isNotEmpty(dialyzeWay)) {
                String collect1 = dialyzeWay.stream().map(DictDataRespDTO::getValue).collect(Collectors.joining(","));
                managerDO.setHealMode(collect1);
            }
            managerDO.setCode(facilityExcelVO.getCode());
            managerDO.setType(1 + "");
            managerDO.setFacilityId(facilityDO1.getId());
            managerDO.setFacilityCode(facilityExcelVO.getFacilityNames());
            managerDO.setFacilitySubareaId(facilitySubareaDO.getId());
            managerDO.setVender(facilityExcelVO.getVender());
            managerDO.setBuyTime(DateUtil.parse(facilityExcelVO.getBuyTime(), DatePattern.NORM_DATE_PATTERN));
            managerDOS.add(managerDO);
        }
        managerMapper.insertBatch(managerDOS);
    }

}
