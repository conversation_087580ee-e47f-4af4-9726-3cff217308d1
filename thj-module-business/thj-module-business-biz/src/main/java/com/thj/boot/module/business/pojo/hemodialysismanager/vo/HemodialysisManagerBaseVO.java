package com.thj.boot.module.business.pojo.hemodialysismanager.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 血液透析管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class HemodialysisManagerBaseVO {

    /**
     *
     */
    private Long id;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析时间
     */
    private Date hemodialysisTime;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 门店名称
     */
    private String deptName;
    /**
     * 每天时间段
     */
    private String dayState;
    /**
     * 机号
     */
    private Long facilityId;
    /**
     * 机型
     */
    private String facilityTypeId;
    /**
     * 目标脱水
     */
    private String dehydration;
    /**
     * 处方脱水量
     */
    private String prescriptionEhydratedLevel;
    /**
     * 透析方式
     */
    private String dialyzeWayValue;
    /**
     * 透析方式子级
     */
    private String childDialyzeWayValue;
    /**
     * 透析时长小时
     */
    private String duration;
    /**
     * 透析时长分钟
     */
    private String durationMin;
    /**
     * 置换总量
     */
    private String substituteTodal;
    /**
     * 置换方式
     */
    private String substituteModel;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 血管通路1
     */
    private String vascularAccessOne;
    /**
     * 血管通路2
     */
    private String vascularAccessTwo;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 透析液流量
     */
    private String dialysateFlowrate;
    /**
     * 钾(mmol/L)
     */
    private String potassium;
    /**
     * 配方钠(mmol/L)
     */
    private String formulaSodium;
    /**
     * 处方钠
     */
    private String prescriptionSodium;
    /**
     * 钙(mmol/L)
     */
    private String calcium;
    /**
     * 碳酸氢根(mmol/L)
     */
    private String bicarbonate;
    /**
     * 葡萄糖(mmol/L)
     */
    private String glucose;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 起始钠
     */
    private String initialSodium;
    /**
     * 钠曲线
     */
    private String sodiumCurve;
    /**
     * 超滤曲线
     */
    private String ultrafiltrationCurve;
    /**
     * 透析处方0-未确认，1-已确认
     */
    private Integer prescriptionState;
    /**
     * 透析处方-确认人id
     */
    private Long prescriptionUseId;
    /**
     * 透析处方-确认人名称
     */
    private String prescriptionUseName;
    /**
     * 摄氏度
     */
    private String degreeCelsius;
    /**
     * p次数
     */
    private String pno;
    /**
     * r次数
     */
    private String rno;
    /**
     * bp
     */
    private String bpNoOne;
    /**
     * bp
     */
    private String bpNoTwo;
    /**
     * 透前BP(mmHg)
     */
    private String beforebpnoThree;
    /**
     * 透前称重方式
     */
    private String weighingMethod;
    /**
     * 透前称重
     */
    private String dialyzeBeforeWeigh;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight ;
    /**
     * 透前透前衣物重
     */
    private String clothing;
    /**
     * 透前干体重
     */
    private String beforeDryWeight;
    /**
     * 透前上次透后体重
     */
    private String beforeDialyzeAfterWeigh;
    /**
     * 透前体重增加
     */
    private String beforeGainWeight;
    /**
     * 预增脱水量
     */
    private String preremovalWater;
    /**
     * 超滤总量
     */
    private String beforeUltrafiltrationtotal;
    /**
     * 透前json
     */
    private String beforeJson;
    /**
     * 0-false,1-true透析物品核查
     */
    private Boolean itemVerifyCorrect;
    /**
     * 透析物品核查差错
     */
    private String itemVerifyError;
    /**
     * 透析参数核查
     */
    private Boolean paramVerifyCorrect;
    /**
     * 透析参数核查差错
     */
    private String paramVerifyError;
    /**
     * 血管通路检查
     */
    private Boolean vascularCorrect;
    /**
     * 血管通路检查差错
     */
    private String vascularCorrectError;
    /**
     * 管道连接
     */
    private Boolean pipeConnectionCorrect;
    /**
     * 血管通路检查差错
     */
    private String pipeConnectionError;
    /**
     * 核对人员
     */
    private Long checkName;
    /**
     * 核对时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date checkTime;
    /**
     * 透后摄氏度
     */
    private String afterDegreeCelsius;
    /**
     * 透后p
     */
    private String afterPNo;
    /**
     * 透后r
     */
    private String afterRNo;
    /**
     * 透后bp
     */
    private String afterBpOne;
    /**
     * 透后bp
     */
    private String afterBpTwo;
    /**
     * 透后bP(次/分)
     */
    private String afterbpnoThree;
    /**
     * 实际超滤量（ml）
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 实际置换量
     */
    private String actualExchangeAmount;
    /**
     * 实际治疗时长 小时
     */
    private String treatmentHour;
    /**
     * 实际治疗时长min
     */
    private String treatmentMin;
    /**
     * 称重方式
     */
    private String afterWeighingMethod;
    /**
     * 透后称重
     */
    private String dialyzeAfterWeigh;
    /**
     * 透后衣物重
     */
    private String afterClothing;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeight;
    /**
     * 体重减少
     */
    private String weights;
    /**
     * 透后评估json
     */
    private String afterJson;
    /**
     * 透析小结
     */
    private String dialysisSummary;
    /**
     * 小结签名
     */
    private Long summarySignature;
    /**
     * 穿刺/换药
     */
    private String dressingChange;
    /**
     * 换药护士
     */
    private Long dressingChangeNurse;
    /**
     * 治疗护士
     */
    private Long treatmentNurse;
    /**
     * 上机护士
     */
    private Long computerNurse;
    /**
     * 预充管路
     */
    private Long piping;
    /**
     * 核对人员
     */
    private Long verificationPersonnel;
    /**
     * 下机护士
     */
    private Long offComputerNurse;
    /**
     * 治疗医生
     */
    private Long treatmentDoctor;
    /**
     * 透析器编号
     */
    private String dialyzeNumber;
    /**
     * 穿刺方式
     */
    private String punctureMethod;
    /**
     * 穿刺针
     */
    private String punctureNeedle;
    /**
     * 穿刺针型号
     */
    private String punctureNeedleModel;
    /**
     * 穿刺方向
     */
    private String punctureDirection;
    /**
     * 备注
     */
    private String remark;
    /**
     * 透前状态0-未确认，1-已确认
     */
    private Integer beforeState;
    /**
     * 透前确认人id
     */
    private Long beforeUserId;
    /**
     * 透前确认人名称
     */
    private String beforeUserName;
    /**
     * 双人核对状态0-未确认，1-已确认
     */
    private Integer checkState;
    /**
     * 双人核对确认人id
     */
    private Long checkUserId;
    /**
     * 双人核对确认人名称
     */
    private String checkUserName;
    /**
     * 监控记录状态0-未确认，1-已确认
     */
    private Integer monitState;
    /**
     * 监控记录确认人id
     */
    private Long monitUserId;
    /**
     * 监控记录确认人名称
     */
    private String monitUserName;
    /**
     * 透后评估状态0-未确认，1-已确认
     */
    private Integer afterState;
    /**
     * 透后评估确认人
     */
    private Long afterUserId;
    /**
     * 透后确认人名称
     */
    private String afterUserName;
    /**
     * 治疗小结状态0-未确认，1-已确认
     */
    private Integer healState;
    /**
     * 治疗小结确认人id
     */
    private Long healUserId;
    /**
     * 治疗小结确认人名称
     */
    private String healUserName;
    /**
     * 透析中入量json
     */
    private String dialysisIntakeData;
    /**
     * 透析中入量
     */
    private String dialysisIntake;
    /**
     * 治疗小结模版id
     */
    private String summaryContentValue;
    /**
     * 治疗小结内容
     */
    private String summaryContent;
    /**
     * 穿刺护士
     */
    private Long punctureNurse;
    /**
     * 病区id
     */
    private Long facilitySubareaId;
    /**
     * 患者签名
     */
    private String patientSignImg;
    /**
     * 抗凝剂公式
     */
    private String anticFormula;
    /**
     * 开始透析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startDialyzeTime;
    /**
     * 结束透析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endDialyzeTime;
    /**
     * 引血
     */
    private String inducingBlood;
    /**
     * 入科方式
     */
    private String enterWay;

    //导管外露长度
    private String catheterOuterLength;

    //A端位点
    private String siteA;

    //B端位点
    private String siteV;

    //A端位点穿刺次数
    private String siteAPunctureCount;

    //B端位点穿刺次数
    private String siteVPunctureCount;

}
