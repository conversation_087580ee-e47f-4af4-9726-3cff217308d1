package com.thj.boot.module.business.convert.pressuresore;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.pressuresore.PressureSoreDO;
import com.thj.boot.module.business.pojo.pressuresore.vo.PressureSoreCreateReqVO;
import com.thj.boot.module.business.pojo.pressuresore.vo.PressureSoreRespVO;
import com.thj.boot.module.business.pojo.pressuresore.vo.PressureSoreUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 压疮风险评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PressureSoreConvert {

    PressureSoreConvert INSTANCE = Mappers.getMapper(PressureSoreConvert.class);

    PressureSoreDO convert(PressureSoreCreateReqVO bean);

    PressureSoreDO convert(PressureSoreUpdateReqVO bean);

    PressureSoreRespVO convert(PressureSoreDO bean);

    List<PressureSoreRespVO> convertList(List<PressureSoreDO> list);

    PageResult<PressureSoreRespVO> convertPage(PageResult<PressureSoreDO> page);


}
