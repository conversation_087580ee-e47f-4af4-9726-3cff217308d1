package com.thj.boot.module.business.pojo.dialyzeoption.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialyzeOptionPageReqVO extends PageParam {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 透析模式（字典获取）
     */
    private String dialyzeDictValue;
    /**
     * 频率（字典获取）
     */
    private String frequencyDictValue;
    /**
     * 周期（字典获取）
     */
    private String cycleDictValue;
    /**
     * 次数（字典获取）
     */
    private String numberDictValue;
    /**
     * 状态（字典获取）
     */
    private String stateDictValue;
    /**
     * 医生
     */
    private Long userId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 处方名
     */
    private String prescription;
    /**
     * 透析json
     */
    private String content;
    /**
     * 抗凝剂json
     */
    private String anticoagulant;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 门店id
     */
    private Long deptId;

}
