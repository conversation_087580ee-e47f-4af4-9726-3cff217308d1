package com.thj.boot.module.business.utils;

import com.thj.boot.common.utils.StringUtils;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.*;

/**
 *
 *  对象比较工具类
 *
 * 深圳万狼科技有限公司
 * E-MAIL：<EMAIL>
 * 地址：深圳市宝安区西乡街道铁仔路40号九方广场2栋901
 * 网址：http://www.wanlang.cn/
 * 我们的宗旨是：让每一个网站都产生价值！
 *
 * @Classname ContrastObjUtil
 * @Description 万狼科技
 * @Version 1.0.0
 * @Date 2024/2/23 13:47
 * @<NAME_EMAIL>
 */
public class ContrastObjUtil {

    /**
     * 判断两个对象是否相等,取出不相等的结果
     * @param olds
     * @param news
     * @return
     */
    public static Map<String, String> getObjectComparison(Object olds, Object news) {
        Map<String, String> map = new HashMap<String, String>();
        Field[] declaredFields2 = olds.getClass().getDeclaredFields();
        Field[] declaredFields23 = news.getClass().getDeclaredFields();
        StringBuilder oldData = new StringBuilder();
        StringBuffer newData = new StringBuffer();
        for (int i = 0; i < declaredFields2.length; i++) {
            declaredFields2[i].setAccessible(true);
            declaredFields23[i].setAccessible(true);
            try {
                if (declaredFields2[i].get(olds) != null && declaredFields23[i].get(news) != null) {

                    if (!declaredFields2[i].get(olds).equals(declaredFields23[i].get(news))) {
                        oldData.append(declaredFields2[i].getName() + ":" + declaredFields2[i].get(olds)).append(",");
                        newData.append(declaredFields23[i].getName() + ":" + declaredFields23[i].get(news)).append(",");
                    }
                } else if (declaredFields2[i].get(olds) == null && declaredFields23[i].get(news) != null) {
                    oldData.append(declaredFields2[i].getName() + ":" + null).append(",");
                    newData.append(declaredFields23[i].getName() + ":" + declaredFields23[i].get(news)).append(",");
                } else if (declaredFields2[i].get(olds) != null && declaredFields23[i].get(news) == null) {
                    oldData.append(declaredFields2[i].getName() + ":" + declaredFields2[i].get(olds)).append(",");
                    newData.append(declaredFields23[i].getName() + ":" + null).append(",");
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        if (StringUtils.isNoneBlank(oldData.toString()) && StringUtils.isNoneBlank(newData.toString())) {
            map.put(oldData.toString().substring(0, oldData.length() - 1),
                    newData.toString().substring(0, newData.length() - 1));
        }
        return map;

    }

    public static void main(String[] args)  {
        User u = new User();
        u.setId(1);
        u.setName("mb");
        u.setMobile("123");
        u.setSb("123");
        ArrayList<Integer> list = new ArrayList<Integer>();
        for(int i =0; i <10; i++){
            list.add(i);
        }
        u.setList(list);
        User u1 = new User();
        u1.setId(2);
        u1.setName("sb");
        u1.setMobile("1233");
        u1.setSb("123");
        ArrayList<Integer> list1 = new ArrayList<Integer>();
        for(int i =0; i <20; i++){
            list1.add(i);
        }
        u1.setList(list1);
        Map<String, String> user = ContrastObjUtil.getObjectComparison(u, u1);
        LinkedHashMap<String,Object> xiugaiqian = new LinkedHashMap<>();
        for(Map.Entry<String, String> entry : user.entrySet()){
            System.out.println("修改前 :" + entry.getKey());
            System.out.println("修改后 :" + entry.getValue());
        }
    }

}


@Data
class  User{

    private int id ;

    private String name;

    private String time;

    private String mobile;

    private String sb;

    private String sb1;

    private List<Integer> list;

}
