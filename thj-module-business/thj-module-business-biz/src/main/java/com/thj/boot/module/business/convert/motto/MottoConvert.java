package com.thj.boot.module.business.convert.motto;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.motto.MottoDO;
import com.thj.boot.module.business.pojo.motto.vo.MottoCreateReqVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoRespVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 个性化 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MottoConvert {

    MottoConvert INSTANCE = Mappers.getMapper(MottoConvert.class);

    MottoDO convert(MottoCreateReqVO bean);

    MottoDO convert(MottoUpdateReqVO bean);

    MottoRespVO convert(MottoDO bean);

    List<MottoRespVO> convertList(List<MottoDO> list);

    PageResult<MottoRespVO> convertPage(PageResult<MottoDO> page);


}
