package com.thj.boot.module.business.service.jkmeans;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.dal.datado.jkmeans.JkMeansDO;
import com.thj.boot.module.business.dal.mapper.jkmeans.JkMeansMapper;
import com.thj.boot.module.business.pojo.jkmeans.vo.JkMeansPageReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 健康教育-患者教育资料 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JkMeansServiceImpl implements JkMeansService {

    @Resource
    private JkMeansMapper jkMeansMapper;

    @Override
    public void createJkMeans(JkMeansDO jkMeansDO) {
        // 插入
        //唯一校验判断
        JkMeansDO jkMeans = jkMeansMapper.selectOne(JkMeansDO::getContentId, jkMeansDO.getContentId(), JkMeansDO::getResourceType,
                jkMeansDO.getResourceType());
        if (StringUtils.isNull(jkMeans)) {
            jkMeansMapper.insert(jkMeansDO);
        }
    }

    @Override
    public void updateJkMeans(JkMeansDO updateObj) {
        // 更新
//        JkMeansDO updateObj = JkMeansConvert.INSTANCE.convert(updateReqVO);
        jkMeansMapper.updateById(updateObj);
    }

    @Override
    public void deleteJkMeans(Long id) {
        // 删除
        jkMeansMapper.deleteById(id);
    }


    @Override
    public JkMeansDO getJkMeans(Long id) {
        return jkMeansMapper.selectById(id);
    }

    @Override
    public List<JkMeansDO> getJkMeansList(Long resourceType,String year) {
        return jkMeansMapper.selectList(new LambdaQueryWrapper<JkMeansDO>().eq(JkMeansDO::getResourceType,resourceType).like(StringUtils.isNotNull(year),JkMeansDO::getMonths,year));
    }

    @Override
    public PageResult<JkMeansDO> getJkMeansPage(JkMeansPageReqVO pageReqVO) {
        return jkMeansMapper.selectPage(pageReqVO);
    }


    @Override
    public List<JkMeansDO> getMeansDOList(Long resourceType,String year) {
        return jkMeansMapper.selectList(new LambdaQueryWrapper<JkMeansDO>().eq(JkMeansDO::getResourceType,resourceType).eq(StringUtils.isNotNull(year),JkMeansDO::getMonths,year));
    }


}
