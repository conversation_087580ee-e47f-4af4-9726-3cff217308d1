package com.thj.boot.module.business.pojo.bloodpulsebody.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BloodPulseBodyPageReqVO extends PageParam {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 测量时间
     */
    private Date measureTime;
    /**
     * 血压
     */
    private String bloodPressure;
    /**
     * 脉搏
     */
    private String pulse;
    /**
     * 体温
     */
    private String temperature;
    /**
     * 测试场景（字典获取）
     */
    private String scenario;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

}
