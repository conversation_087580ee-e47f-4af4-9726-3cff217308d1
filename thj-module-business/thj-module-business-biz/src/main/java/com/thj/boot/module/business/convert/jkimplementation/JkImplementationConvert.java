package com.thj.boot.module.business.convert.jkimplementation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkimplementation.JkImplementationDO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationCreateReqVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationExcelVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationRespVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-患教实施 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkImplementationConvert {

    JkImplementationConvert INSTANCE = Mappers.getMapper(JkImplementationConvert.class);

    JkImplementationDO convert(JkImplementationCreateReqVO bean);

    JkImplementationDO convert(JkImplementationUpdateReqVO bean);

    JkImplementationRespVO convert(JkImplementationDO bean);

    List<JkImplementationRespVO> convertList(List<JkImplementationDO> list);

    PageResult<JkImplementationRespVO> convertPage(PageResult<JkImplementationDO> page);

    List<JkImplementationExcelVO> convertList02(List<JkImplementationDO> list);

}
