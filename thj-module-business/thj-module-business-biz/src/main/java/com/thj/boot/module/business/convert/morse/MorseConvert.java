package com.thj.boot.module.business.convert.morse;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.morse.MorseDO;
import com.thj.boot.module.business.pojo.morse.vo.MorseCreateReqVO;
import com.thj.boot.module.business.pojo.morse.vo.MorseRespVO;
import com.thj.boot.module.business.pojo.morse.vo.MorseUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * morse跌倒评估量 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MorseConvert {

    MorseConvert INSTANCE = Mappers.getMapper(MorseConvert.class);

    MorseDO convert(MorseCreateReqVO bean);

    MorseDO convert(MorseUpdateReqVO bean);

    MorseRespVO convert(MorseDO bean);

    List<MorseRespVO> convertList(List<MorseDO> list);

    PageResult<MorseRespVO> convertPage(PageResult<MorseDO> page);


}
