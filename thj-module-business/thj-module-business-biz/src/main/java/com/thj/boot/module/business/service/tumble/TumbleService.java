package com.thj.boot.module.business.service.tumble;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.tumble.TumbleDO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleCreateReqVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumblePageReqVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 跌倒评估 Service 接口
 *
 * <AUTHOR>
 */
public interface TumbleService {

    /**
     * 创建跌倒评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTumble( TumbleCreateReqVO createReqVO);

    /**
     * 更新跌倒评估
     *
     * @param updateReqVO 更新信息
     */
    void updateTumble( TumbleUpdateReqVO updateReqVO);

    /**
     * 删除跌倒评估
     *
     * @param id 编号
     */
    void deleteTumble(Long id);

    /**
     * 获得跌倒评估
     *
     * @param id 编号
     * @return 跌倒评估
     */
    TumbleDO getTumble(Long id);

    /**
     * 获得跌倒评估列表
     *
     * @param ids 编号
     * @return 跌倒评估列表
     */
    List<TumbleDO> getTumbleList(Collection<Long> ids);

    /**
     * 获得跌倒评估分页
     *
     * @param pageReqVO 分页查询
     * @return 跌倒评估分页
     */
    PageResult<TumbleDO> getTumblePage(TumblePageReqVO pageReqVO);

    /**
     * 获得跌倒评估列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 跌倒评估列表
     */
    List<TumbleDO> getTumbleList(TumbleCreateReqVO createReqVO);

}
