package com.thj.boot.module.business.convert.patient;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.pojo.drainteam.DrainTeamCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientExportVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientUpdateReqVO;
import com.thj.boot.module.system.api.patient.dto.PatientDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 患者管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatientConvert {

    PatientConvert INSTANCE = Mappers.getMapper(PatientConvert.class);

    PatientDO convert(PatientCreateReqVO bean);

    PatientDO convert(PatientUpdateReqVO bean);

    PatientRespVO convert(PatientDO bean);


    List<PatientRespVO> convertList(List<PatientDO> list);

    PageResult<PatientRespVO> convertPage(PageResult<PatientDO> page);

    PatientCreateReqVO convert(DrainTeamCreateReqVO createReqVO);

    PatientDTO convertDTO(PatientDO patientDO);

    PageResult<PatientExportVO> convertPage2(PageResult<PatientDO> patientDOPageResult);
}
