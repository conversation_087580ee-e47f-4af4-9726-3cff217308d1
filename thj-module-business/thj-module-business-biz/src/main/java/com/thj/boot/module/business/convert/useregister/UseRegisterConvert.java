package com.thj.boot.module.business.convert.useregister;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.useregister.vo.UserRegisterExcelVO;
import com.thj.boot.module.business.dal.datado.useregister.UseRegisterDO;
import com.thj.boot.module.business.pojo.useregister.vo.UseRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.useregister.vo.UseRegisterRespVO;
import com.thj.boot.module.business.pojo.useregister.vo.UseRegisterUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 使用登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UseRegisterConvert {

    UseRegisterConvert INSTANCE = Mappers.getMapper(UseRegisterConvert.class);

    UseRegisterDO convert(UseRegisterCreateReqVO bean);

    UseRegisterDO convert(UseRegisterUpdateReqVO bean);

    UseRegisterRespVO convert(UseRegisterDO bean);

    List<UseRegisterRespVO> convertList(List<UseRegisterDO> list);

    PageResult<UseRegisterRespVO> convertPage(PageResult<UseRegisterDO> page);


    List<UserRegisterExcelVO> convertExecl(List<UseRegisterRespVO> list);
}
