package com.thj.boot.module.business.convert.reachweigh;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.reachweigh.ReachWeighDO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighCreateReqVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 签到称重 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ReachWeighConvert {

    ReachWeighConvert INSTANCE = Mappers.getMapper(ReachWeighConvert.class);

    ReachWeighDO convert(ReachWeighCreateReqVO bean);

    ReachWeighDO convert(ReachWeighUpdateReqVO bean);

    ReachWeighRespVO convert(ReachWeighDO bean);

    List<ReachWeighRespVO> convertList(List<ReachWeighDO> list);

    PageResult<ReachWeighRespVO> convertPage(PageResult<ReachWeighDO> page);


}
