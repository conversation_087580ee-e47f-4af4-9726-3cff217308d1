package com.thj.boot.module.business.convert.mottohard;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.mottohard.MottoHardDO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardCreateReqVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardRespVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析方案 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MottoHardConvert {

    MottoHardConvert INSTANCE = Mappers.getMapper(MottoHardConvert.class);

    MottoHardDO convert(MottoHardCreateReqVO bean);

    MottoHardDO convert(MottoHardUpdateReqVO bean);

    MottoHardRespVO convert(MottoHardDO bean);

    List<MottoHardRespVO> convertList(List<MottoHardDO> list);

    PageResult<MottoHardRespVO> convertPage(PageResult<MottoHardDO> page);


}
