package com.thj.boot.module.business.service.dialysismanager;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.controller.admin.dialysismanager.vo.*;
import com.thj.boot.module.business.convert.dialysismanager.DialysisManagerConvert;
import com.thj.boot.module.business.convert.repairregister.RepairRegisterConvert;
import com.thj.boot.module.business.convert.teampatient.TeamPatientConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.complication.ComplicationDO;
import com.thj.boot.module.business.dal.datado.contradict.ContradictDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilityname.FacilityNameDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.jkimplementation.JkImplementationDO;
import com.thj.boot.module.business.dal.datado.outcomerecord.OutcomeRecordDO;
import com.thj.boot.module.business.dal.datado.passage.PassageDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.repairregister.RepairRegisterDO;
import com.thj.boot.module.business.dal.datado.teampatient.TeamPatientDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.contradict.ContradictMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysisdetection.DialysisDetectionMapper;
import com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.dialysisrecord.DialysisRecordMapper;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilityname.FacilityNameMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.repairregister.RepairRegisterMapper;
import com.thj.boot.module.business.dal.mapper.teampatient.TeamPatientMapper;
import com.thj.boot.module.business.pojo.departmentControl.DialysisTotalTimeVo;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerRespVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerUpdateReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightCreateReqVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO;
import com.thj.boot.module.business.service.departmentControl.param.AggregateAnalysisParams;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/23 14:25
 * @description
 */
@Service
@Slf4j
public class DialysisManagerServiceImpl implements DialysisManagerService {

    @Resource
    private TeamPatientMapper teamPatientMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private DialysisManagerMapper dialysisManagerMapper;

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;

    @Resource
    private DialysisDetectionMapper dialysisDetectionMapper;

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;

    @Resource
    private DialysisRecordMapper dialysisRecordMapper;

    @Resource
    private HisDrugMapper hisDrugMapper;

    @Resource
    private BloodRoadMapper bloodRoadMapper;

    @Resource
    private RepairRegisterMapper repairRegisterMapper;

    @Resource
    private FacilityManagerMapper facilityManagerMapper;

    @Resource
    private FacilityNameMapper facilityNameMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private OutcomeRecordMapper outcomeRecordMapper;

    @Resource
    private ContradictMapper contradictMapper;

    @Override
    public List<TeamPatientRespVO> patientList(TeamPatientCreateVO createVO) {
        MPJLambdaWrapper<TeamPatientDO> wrapper = new MPJLambdaWrapper<>(TeamPatientDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, TeamPatientDO::getPatientId)
                .selectAll(TeamPatientDO.class)
                .eq(TeamPatientDO::getDateWeek, createVO.getTeamTime())
                .eq(TeamPatientDO::getTempType, "0")
                .eq(TeamPatientDO::getWeekDay, createVO.getWeekFlag())
                .in(CollectionUtil.isNotEmpty(createVO.getFacilitySubareaIds()), TeamPatientDO::getFacilitySubareaId, createVO.getFacilitySubareaIds())
                .and(StrUtil.isNotEmpty(createVO.getMore()), i -> i
                        .like(PatientDO::getName, createVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, createVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, createVO.getMore()));
        List<TeamPatientDO> teamPatientDOS = teamPatientMapper.selectJoinList(TeamPatientDO.class, wrapper);
        List<TeamPatientRespVO> teamPatientRespVOS = TeamPatientConvert.INSTANCE.convertList3(teamPatientDOS);
        if (CollectionUtil.isNotEmpty(teamPatientRespVOS)) {
            teamPatientRespVOS = teamPatientRespVOS.stream().peek(teamPatientRespVO -> {
                PatientDO patientDO = patientMapper.selectById(teamPatientRespVO.getPatientId());
                teamPatientRespVO.setDialyzeNo(patientDO == null ? null : patientDO.getDialyzeNo());
                FacilityDO facilityDO = facilityMapper.selectById(teamPatientRespVO.getFacilityId());
                teamPatientRespVO.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
                DryWeightCreateReqVO createReqVO = new DryWeightCreateReqVO();
                createReqVO.setPatientId(teamPatientRespVO.getPatientId());
                List<DryWeightDO> dryWeightDOS = dryWeightMapper.selectList(createReqVO);
                if (CollectionUtil.isNotEmpty(dryWeightDOS)) {
                    DryWeightDO dryWeightDO = dryWeightDOS.get(0);
                    if (StrUtil.isNotEmpty(dryWeightDO.getState())) {
                        DictDataRespDTO weightType = dictDataApi.getDictData("patient_weight_type", dryWeightDO.getState());
                        teamPatientRespVO.setDryWeight(weightType.getLabel());
                    } else {
                        teamPatientRespVO.setDryWeight(dryWeightDO.getDryWeight());
                    }
                }
            }).collect(Collectors.toList());
        }
        return teamPatientRespVOS;
    }


    @Override
    public Long createDialysisManager(DialysisManagerCreateReqVO createReqVO) {
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        //患者排班信息
        JSONObject jsonObject = JSONUtil.parseObj(createReqVO.getTableContentInfo());
        //透析处方
        JSONObject prescription = JSONUtil.parseObj(createReqVO.getPrescription());
        DialysisManagerDO dialysisManagerDO1 = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, createReqVO.getPatientId(), DialysisManagerDO::getDateWeek, createReqVO.getDateWeek());
        DialysisManagerDO dialysisManagerDO = DialysisManagerConvert.INSTANCE.convert(createReqVO);
        long userId = StpUtil.getLoginIdAsLong();
        if (dialysisManagerDO1 == null) {
            // 插入
            if (StrUtil.isNotEmpty(createReqVO.getPrescriptionState())) {
                dialysisManagerDO.setPrescriptionUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getBeforeEvaluateSate())) {
                dialysisManagerDO.setBeforeEvaluateUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getDoubleCheckState())) {
                dialysisManagerDO.setDoubleCheckUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getDetectionState())) {
                TeamPatientDO teamPatientDO = teamPatientMapper.selectOne(new LambdaQueryWrapperX<TeamPatientDO>()
                        .eqIfPresent(TeamPatientDO::getDateWeek, createReqVO.getDateWeek())
                        .eqIfPresent(TeamPatientDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(TeamPatientDO::getWeekDay, jsonObject.getStr("weekDay")));
                if (teamPatientDO != null) {
                    //开始透析后才可以确认
                    if (!"2".equals(teamPatientDO.getState())) {
                        throw new ServiceException(GlobalErrorCodeConstants.START_DIALYSIS_SUCCESS);
                    }
                    dialysisManagerDO.setDetectionStateUser(userId);
                }
            }
            if (StrUtil.isNotEmpty(createReqVO.getAfterEvaluateSate())) {
                dialysisManagerDO.setAfterEvaluateUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getRecoverSummaryState())) {
                dialysisManagerDO.setRecoverSummaryUser(userId);
            }
            dialysisManagerDO.setWeekDay(jsonObject.getStr("dayState"));
            dialysisManagerDO.setFacilitySubareaId(jsonObject.getInt("facilitySubareaId") == null ? null : Long.valueOf(jsonObject.getInt("facilitySubareaId")));
            dialysisManagerDO.setFacilityId(jsonObject.getInt("facilityId") == null ? null : Long.valueOf(jsonObject.getInt("facilityId")));
            dialysisManagerDO.setDialysisMode(prescription.getStr("dialyzeWayValue"));
            dialysisManagerDO.setPatientSource(patientDO == null ? null : patientDO.getPatientSource());
            dialysisManagerMapper.insert(dialysisManagerDO);
        } else {
            //修改
            if (StrUtil.isNotEmpty(createReqVO.getPrescriptionState())) {
                dialysisManagerDO.setPrescriptionUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getBeforeEvaluateSate())) {
                dialysisManagerDO.setBeforeEvaluateUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getDoubleCheckState())) {
                dialysisManagerDO.setDoubleCheckUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getDetectionState())) {
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek())
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getDayState, jsonObject.getStr("dayState")));

                if (arrangeClassesDO != null) {
                    //开始透析后才可以确认
                    if (2 > Integer.valueOf(arrangeClassesDO.getState())) {
                        throw new ServiceException(GlobalErrorCodeConstants.START_DIALYSIS_SUCCESS);
                    }
                    dialysisManagerDO.setDetectionStateUser(userId);
                }
            }
            if (StrUtil.isNotEmpty(createReqVO.getAfterEvaluateSate())) {
                dialysisManagerDO.setAfterEvaluateUser(userId);
            }
            if (StrUtil.isNotEmpty(createReqVO.getRecoverSummaryState())) {
                dialysisManagerDO.setRecoverSummaryUser(userId);
            }
            DialysisManagerDO dialysisManagerDO2 = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, createReqVO.getPatientId(), DialysisManagerDO::getDateWeek, createReqVO.getDateWeek());
            if (dialysisManagerDO2 != null && "2".equals(dialysisManagerDO2.getPrescriptionState())) {
                //已更新
                dialysisManagerDO.setPrescriptionState("2");
            } else if (dialysisManagerDO2 != null && dialysisManagerDO2.getPrescriptionState().equals(createReqVO.getPrescriptionState())) {
                //已更新
                dialysisManagerDO.setPrescriptionState("2");
            }
            dialysisManagerDO.setDialysisMode(prescription.getStr("dialyzeWayValue"));
            dialysisManagerDO.setPatientSource(patientDO == null ? null : patientDO.getPatientSource());
            dialysisManagerMapper.update(dialysisManagerDO, new LambdaUpdateWrapper<DialysisManagerDO>()
                    .eq(DialysisManagerDO::getPatientId, createReqVO.getPatientId())
                    .eq(DialysisManagerDO::getDateWeek, createReqVO.getDateWeek()));
        }
        // 返回
        return dialysisManagerDO.getId();
    }

    @Override
    public void updateDialysisManager(DialysisManagerUpdateReqVO updateReqVO) {
        // 更新
        DialysisManagerDO updateObj = DialysisManagerConvert.INSTANCE.convert(updateReqVO);
        dialysisManagerMapper.updateById(updateObj);
    }

    @Override
    public void deleteDialysisManager(Long id) {
        // 删除
        dialysisManagerMapper.deleteById(id);
    }


    @Override
    public DialysisManagerRespVO getDialysisManager(Long id) {
        DialysisManagerDO dialysisManagerDO = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, id);
        DialysisManagerRespVO dialysisManagerRespVO = DialysisManagerConvert.INSTANCE.convert(dialysisManagerDO);
        if (dialysisManagerRespVO != null) {
            if (dialysisManagerRespVO.getPrescriptionUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getPrescriptionUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setPrescriptionUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getBeforeEvaluateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getBeforeEvaluateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setBeforeEvaluateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getDoubleCheckUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getDoubleCheckUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setDoubleCheckUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getDetectionStateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getDetectionStateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setDetectionStateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getAfterEvaluateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getAfterEvaluateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setAfterEvaluateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getRecoverSummaryUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getRecoverSummaryUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setRecoverSummaryUserName(adminUser.getNickname());
            }
        }

        return dialysisManagerRespVO;
    }

    @Override
    public List<DialysisManagerDO> getDialysisManagerList(Collection<Long> ids) {
        return dialysisManagerMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DialysisManagerRespVO> getDialysisManagerPage(DialysisManagerPageReqVO pageReqVO) {
        PageResult<DialysisManagerDO> dialysisManagerDOPageResult = dialysisManagerMapper.selectPage(pageReqVO);
        return DialysisManagerConvert.INSTANCE.convertPage(dialysisManagerDOPageResult);
    }

    @Override
    public List<DialysisManagerRespVO> getDialysisManagerList(DialysisManagerCreateReqVO createReqVO) {
        return DialysisManagerConvert.INSTANCE.convertList(dialysisManagerMapper.selectList(createReqVO));
    }

    @Override
    public void checkUserName(UserCreateReqDTO createReqDTO) {
        UserRespDTO adminUser = adminUserApi.getAdminUserInfo(createReqDTO.getId());
        if (adminUser == null || !BCrypt.checkpw(createReqDTO.getPassword(), adminUser.getPassword())) {
            throw new ServiceException(GlobalErrorCodeConstants.LOGINEROOR);
        }
    }

    @Override
    public PageResult<? extends DialyzeArrangeBaseVO> consumablePage(DialysisManagerPageReqVO pageVO) {
        //查询班次(全天)
        if ("0".equals(pageVO.getWeekDay())) {
            pageVO.setWeekDay(null);
        }
        PageResult<DialysisManagerDO> dialysisManagerDOPageResult = dialysisManagerMapper.selectPage(pageVO);
        //透析耗材
        if (1 == pageVO.getType()) {
            PageResult<DialysisConsumablesRespVO> dialysisConsumablesRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage2(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisConsumablesRespVOPageResult.getList())) {
                List<DialysisConsumablesRespVO> collect = dialysisConsumablesRespVOPageResult.getList().stream().peek(dialysisConsumablesRespVO -> {
                    //根据患者id查询患者基本信息
                    dialysisConsumablesRespVO = (DialysisConsumablesRespVO) getPatientInfo(dialysisConsumablesRespVO);
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(dialysisConsumablesRespVO.getFacilityId());
                    if (facilityDO != null) {
                        dialysisConsumablesRespVO.setFacilityName(facilityDO.getCode());
                    }
                    //处方状态 0-未确认，1-已确认，2-已更新
                    dialysisConsumablesRespVO.setPrescriptionState("0".equals(dialysisConsumablesRespVO.getPrescriptionState()) ? "未确认" : "1".equals(dialysisConsumablesRespVO.getPrescriptionState()) ? "已确认" : "已更新");
                    if (StrUtil.isNotEmpty(dialysisConsumablesRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisConsumablesRespVO.getPrescription());
                        //血透器
                        dialysisConsumablesRespVO.setHemodialysisDevice(jsonObject.getStr("hemodialysisDevice"));
                        //血滤器
                        dialysisConsumablesRespVO.setBloodFilter(jsonObject.getStr("bloodFilter"));
                        //灌流器
                        dialysisConsumablesRespVO.setPerfumer(jsonObject.getStr("perfumer"));
                        //甲
                        dialysisConsumablesRespVO.setPotassium(jsonObject.getStr("potassium"));
                        //钙
                        dialysisConsumablesRespVO.setCalcium(jsonObject.getStr("calcium"));
                        //葡萄糖
                        dialysisConsumablesRespVO.setGlucose(jsonObject.getStr("glucose"));
                    }
                    //治疗小结
                    if (StrUtil.isNotEmpty(dialysisConsumablesRespVO.getRecoverSummary())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisConsumablesRespVO.getRecoverSummary());
                        JSONArray punctureNeedle = jsonObject.getJSONArray("punctureNeedle");
                        if (CollectionUtil.isNotEmpty(punctureNeedle)) {
                            List<String> punctureNeedles = punctureNeedle.stream().map(value1 -> value1.toString()).collect(Collectors.toList());
                            List<DictDataRespDTO> punctureNeedle1 = dictDataApi.getDictDataListByBatchValue(punctureNeedles, "punctureNeedle");
                            if (CollectionUtil.isNotEmpty(punctureNeedle1)) {
                                String collect1 = punctureNeedle1.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                                dialysisConsumablesRespVO.setPunctureNeedle(collect1);
                            }
                        }
                        JSONArray punctureNeedleModel = jsonObject.getJSONArray("punctureNeedleModel");
                        if (CollectionUtil.isNotEmpty(punctureNeedleModel)) {
                            List<String> punctureNeedleModels = punctureNeedleModel.stream().map(value1 -> value1.toString()).collect(Collectors.toList());
                            List<DictDataRespDTO> punctureNeedle1 = dictDataApi.getDictDataListByBatchValue(punctureNeedleModels, "punctureNeedleModel");
                            if (CollectionUtil.isNotEmpty(punctureNeedle1)) {
                                String collect1 = punctureNeedle1.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                                dialysisConsumablesRespVO.setPunctureNeedleModel(collect1);
                            }
                        }
                    }
                }).collect(Collectors.toList());
                dialysisConsumablesRespVOPageResult.setList(collect);
                return dialysisConsumablesRespVOPageResult;
            }
        }
        if (2 == pageVO.getType()) {//上机参数
            PageResult<DialysisEmbarkationRespVO> dialysisEmbarkationRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage3(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisEmbarkationRespVOPageResult.getList())) {
                List<DialysisEmbarkationRespVO> collect = dialysisEmbarkationRespVOPageResult.getList().stream().peek(dialysisEmbarkationRespVO -> {
                    //根据患者id查询患者基本信息
                    dialysisEmbarkationRespVO = (DialysisEmbarkationRespVO) getPatientInfo(dialysisEmbarkationRespVO);
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(dialysisEmbarkationRespVO.getFacilityId());
                    if (facilityDO != null) {
                        dialysisEmbarkationRespVO.setFacilityName(facilityDO.getCode());
                    }
                    //处方状态 0-未确认，1-已确认，2-已更新
                    dialysisEmbarkationRespVO.setPrescriptionState("0".equals(dialysisEmbarkationRespVO.getPrescriptionState()) ? "未确认" : "1".equals(dialysisEmbarkationRespVO.getPrescriptionState()) ? "已确认" : "已更新");
                    if (StrUtil.isNotEmpty(dialysisEmbarkationRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisEmbarkationRespVO.getPrescription());
                        //干体重
                        dialysisEmbarkationRespVO.setDryWeight(jsonObject.getStr("dryWeight"));
                        //透析方式
                        String dialyzeWayValue = jsonObject.getStr("dialyzeWayValue");
                        if (StrUtil.isNotEmpty(dialyzeWayValue)) {
                            DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeWayValue);
                            dialysisEmbarkationRespVO.setDialyzeWayValue(dialyzeWay != null ? dialyzeWay.getLabel() : null);
                        }
                        //血滤器=人工肾
                        String bloodFilter = jsonObject.getStr("bloodFilter");
                        if (StrUtil.isNotEmpty(bloodFilter)) {
                            HisDrugDO drugDO = hisDrugMapper.selectById(bloodFilter);
                            if (drugDO != null) {
                                dialysisEmbarkationRespVO.setBloodFilter(drugDO.getFDrugName());
                            }
                        }

                        //血管通路
                        StringBuilder sb1 = new StringBuilder();
                        String vascularAccessOne = jsonObject.getStr("vascularAccessOne");
                        String vascularAccessTwo = jsonObject.getStr("vascularAccessTwo");
                        if (StrUtil.isNotEmpty(vascularAccessOne)) {
                            DictDataRespDTO dictData = dictDataApi.getDictData("blood_passage_one", vascularAccessOne);
                            sb1.append(dictData != null ? dictData.getLabel() : "");
                        }
                        sb1.append("/");
                        if (StrUtil.isNotEmpty(vascularAccessTwo)) {
                            DictDataRespDTO dictData = dictDataApi.getDictData("blood_passage_two", vascularAccessTwo);
                            sb1.append(dictData != null ? dictData.getLabel() : "");
                        }
                        dialysisEmbarkationRespVO.setVascularAccess(sb1.toString());

                        //处方脱水量
                        dialysisEmbarkationRespVO.setPrescriptionDehydratedLevel(jsonObject.getStr("prescriptionDehydratedLevel"));
                        //目标脱水量
                        dialysisEmbarkationRespVO.setTargetDehydratedLevel(jsonObject.getStr("targetDehydratedLevel"));
                        //置换总量
                        dialysisEmbarkationRespVO.setSubstituteTodal(jsonObject.getStr("substituteTodal"));
                        //血流量
                        dialysisEmbarkationRespVO.setBloodFlow(jsonObject.getStr("bloodFlow"));
                        //透析时长
                        dialysisEmbarkationRespVO.setDuration(jsonObject.getStr("duration"));
                        //钙
                        dialysisEmbarkationRespVO.setCalcium(jsonObject.getStr("calcium"));
                        //甲
                        dialysisEmbarkationRespVO.setPotassium(jsonObject.getStr("potassium"));
                        //配方钠
                        dialysisEmbarkationRespVO.setFormulaSodium(jsonObject.getStr("formulaSodium"));
                        //处方钠
                        dialysisEmbarkationRespVO.setPrescriptionSodium(jsonObject.getStr("prescriptionSodium"));
                        //碳酸氢根
                        dialysisEmbarkationRespVO.setBicarbonate(jsonObject.getStr("bicarbonate"));
                        //葡萄糖
                        dialysisEmbarkationRespVO.setGlucose(jsonObject.getStr("glucose"));

                        //抗凝剂(首剂)(维持/追加)(总量)
                        JSONObject anticoagulant1 = jsonObject.getJSONObject("anticoagulant");
                        if (anticoagulant1 != null) {
                            JSONArray drugTypeFrom = anticoagulant1.getJSONArray("drugTypeFrom");
                            if (CollectionUtil.isNotEmpty(drugTypeFrom)) {
                                String drugTypeName = drugTypeFrom.stream().map(drugtype -> {
                                    StringBuilder sb = new StringBuilder();
                                    JSONObject jsonObject1 = JSONUtil.parseObj(drugtype);
                                    return sb.append(StrUtil.isEmpty(jsonObject1.getStr("drugTypeName")) ? "" : jsonObject1.getStr("drugTypeName"))
                                            .append("/")
                                            .append(StrUtil.isEmpty(jsonObject1.getStr("firstDoseValue")) ? "" : jsonObject1.getStr("firstDoseValue"))
                                            .append("/")
                                            .append(StrUtil.isEmpty(jsonObject1.getStr("firstMethodNumber")) ? "" : jsonObject1.getStr("firstMethodNumber"))
                                            .append("/")
                                            .append(StrUtil.isEmpty(jsonObject1.getStr("opportunity")) ? "" : jsonObject1.getStr("opportunity"))
                                            .append("/")
                                            .append(StrUtil.isEmpty(jsonObject1.getStr("totalValue")) ? "" : jsonObject1.getStr("totalValue"));
                                }).collect(Collectors.joining(","));
                                dialysisEmbarkationRespVO.setAnticoagulant(drugTypeName);
                            }
                        }

                    }
                    if (StrUtil.isNotEmpty(dialysisEmbarkationRespVO.getBeforeEvaluate())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisEmbarkationRespVO.getBeforeEvaluate());
                        //超滤总量
                        dialysisEmbarkationRespVO.setUltrafiltrationTotal(jsonObject.getStr("ultrafiltrationTotal"));
                    }
                }).collect(Collectors.toList());
                dialysisEmbarkationRespVOPageResult.setList(collect);
                return dialysisEmbarkationRespVOPageResult;
            }
        }
        if (3 == pageVO.getType()) {//药品
            PageResult<DialysisDrugRespVO> dialysisDrugRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage4(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisDrugRespVOPageResult.getList())) {
                List<DialysisDrugRespVO> dialysisDrugRespVOList = getDialysisDrugRespVOList(dialysisDrugRespVOPageResult.getList());
                dialysisDrugRespVOPageResult.setList(dialysisDrugRespVOList);
                return dialysisDrugRespVOPageResult;
            }
        }
        if (4 == pageVO.getType()) {//汇总
            PageResult<DialysisCollectRespVO> dialysisCollectRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage5(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisCollectRespVOPageResult.getList())) {
                //药品
                PageResult<DialysisDrugRespVO> dialysisDrugRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage4(dialysisManagerDOPageResult);
                List<DialysisDrugRespVO> dialysisDrugRespVOList = getDialysisDrugRespVOList(dialysisDrugRespVOPageResult.getList());

                List<DialysisCollectRespVO> collect = dialysisCollectRespVOPageResult.getList().stream().peek(dialysisCollectRespVO -> {
                    //根据患者id查询患者基本信息
                    dialysisCollectRespVO = (DialysisCollectRespVO) getPatientInfo(dialysisCollectRespVO);
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(dialysisCollectRespVO.getFacilityId());
                    if (facilityDO != null) {
                        dialysisCollectRespVO.setFacilityName(facilityDO.getCode());
                    }
                    //处方状态 0-未确认，1-已确认，2-已更新
                    dialysisCollectRespVO.setPrescriptionState("0".equals(dialysisCollectRespVO.getPrescriptionState()) ? "未确认" : "1".equals(dialysisCollectRespVO.getPrescriptionState()) ? "已确认" : "已更新");
                    //分区
                    FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(dialysisCollectRespVO.getFacilitySubareaId());
                    if (facilitySubareaDO != null) {
                        dialysisCollectRespVO.setFacilitySubareaName(facilitySubareaDO.getName());
                    }
                    if (StrUtil.isNotEmpty(dialysisCollectRespVO.getBeforeEvaluate())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisCollectRespVO.getBeforeEvaluate());
                        //干体重
                        dialysisCollectRespVO.setDryWeight(jsonObject.getStr("dryWeight"));
                    }
                    if (StrUtil.isNotEmpty(dialysisCollectRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisCollectRespVO.getPrescription());
                        //血透器
                        dialysisCollectRespVO.setHemodialysisDevice(jsonObject.getStr("hemodialysisDevice"));
                        //血滤器
                        dialysisCollectRespVO.setBloodFilter(jsonObject.getStr("bloodFilter"));
                        //灌流器
                        dialysisCollectRespVO.setPerfumer(jsonObject.getStr("perfumer"));
                        //甲
                        dialysisCollectRespVO.setPotassium(jsonObject.getStr("potassium"));
                        //钙
                        dialysisCollectRespVO.setCalcium(jsonObject.getStr("calcium"));
                        //抗凝剂(首剂)(维持/追加)(总量)
                    }
                    //药品
                    if (CollectionUtil.isNotEmpty(dialysisDrugRespVOList)) {
                        DialysisCollectRespVO dialysisCollectRespVO1 = dialysisCollectRespVO;
                        boolean present = dialysisDrugRespVOList.stream().filter(dialysisDrugRespVO -> dialysisDrugRespVO.getPatientId() == dialysisCollectRespVO1.getPatientId()).findFirst().isPresent();
                        if (present) {
                            DialysisDrugRespVO dialysisDrugRespVO1 = dialysisDrugRespVOList.stream().filter(dialysisDrugRespVO -> dialysisDrugRespVO.getPatientId() == dialysisCollectRespVO1.getPatientId()).findFirst().get();
                            //促红素(医嘱)
                            dialysisCollectRespVO.setErythropoietin(dialysisDrugRespVO1.getErythropoietin());
                            //左卡尼汀(医嘱)
                            dialysisCollectRespVO.setCarnitine(dialysisDrugRespVO1.getCarnitine());
                            //蔗糖铁 (医嘱)
                            dialysisCollectRespVO.setIronSucrose(dialysisDrugRespVO1.getIronSucrose());
                            //帕立骨化醇(医嘱)
                            dialysisCollectRespVO.setParicalcitol(dialysisDrugRespVO1.getParicalcitol());
                            //骨化三醇(医嘱)
                            dialysisCollectRespVO.setCalcitriol(dialysisDrugRespVO1.getCalcitriol());
                            //尿激酶 (医嘱)
                            dialysisCollectRespVO.setUrokinase(dialysisDrugRespVO1.getUrokinase());
                            //葡萄糖酸钙(医嘱)
                            dialysisCollectRespVO.setCalciumGluconate(dialysisDrugRespVO1.getCalciumGluconate());
                        }
                    }

                }).collect(Collectors.toList());
                dialysisCollectRespVOPageResult.setList(collect);
                return dialysisCollectRespVOPageResult;
            }
        }
        return null;
    }

    private List<DialysisDrugRespVO> getDialysisDrugRespVOList(List<DialysisDrugRespVO> dialysisDrugRespVOPageResult) {
        return dialysisDrugRespVOPageResult.stream().peek(dialysisDrugRespVO -> {
            //根据患者id查询患者基本信息
            dialysisDrugRespVO = (DialysisDrugRespVO) getPatientInfo(dialysisDrugRespVO);
            //机号
            FacilityDO facilityDO = facilityMapper.selectById(dialysisDrugRespVO.getFacilityId());
            if (facilityDO != null) {
                dialysisDrugRespVO.setFacilityName(facilityDO.getCode());
            }
            //处方状态 0-未确认，1-已确认，2-已更新
            dialysisDrugRespVO.setPrescriptionState("0".equals(dialysisDrugRespVO.getPrescriptionState()) ? "未确认" : "1".equals(dialysisDrugRespVO.getPrescriptionState()) ? "已确认" : "已更新");
            //促红素(医嘱)
            List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPatientId, dialysisDrugRespVO.getPatientId());
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //促红素(医嘱)
                List<DialysisAdviceDO> erythropoietinList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("促红素")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(erythropoietinList)) {
                    String erythropoietin = getDrugInfo(erythropoietinList);
                    dialysisDrugRespVO.setErythropoietin(erythropoietin);

                }
            }

            //左卡尼汀(医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //左卡尼汀(医嘱)
                List<DialysisAdviceDO> carnitineList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("左卡尼汀")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(carnitineList)) {
                    String carnitine = getDrugInfo(carnitineList);
                    dialysisDrugRespVO.setCarnitine(carnitine);
                }
            }

            //蔗糖铁 (医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //蔗糖铁 (医嘱)
                List<DialysisAdviceDO> ironSucroseList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("蔗糖铁")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ironSucroseList)) {
                    String ironSucrose = getDrugInfo(ironSucroseList);
                    dialysisDrugRespVO.setIronSucrose(ironSucrose);
                }
            }

            //帕立骨化醇(医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //帕立骨化醇(医嘱)
                List<DialysisAdviceDO> paricalcitolList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("帕立骨化醇")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paricalcitolList)) {
                    String paricalcitol = getDrugInfo(paricalcitolList);
                    dialysisDrugRespVO.setParicalcitol(paricalcitol);
                }
            }
            //骨化三醇(医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //骨化三醇(医嘱)
                List<DialysisAdviceDO> calcitriolList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("骨化三醇")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calcitriolList)) {
                    String calcitriol = getDrugInfo(calcitriolList);
                    dialysisDrugRespVO.setCalcitriol(calcitriol);
                }
            }
            //尿激酶 (医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //尿激酶 (医嘱)
                List<DialysisAdviceDO> urokinaseList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("尿激酶")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(urokinaseList)) {
                    String urokinase = getDrugInfo(urokinaseList);
                    dialysisDrugRespVO.setUrokinase(urokinase);
                }
            }
            //葡萄糖酸钙(医嘱)
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                //葡萄糖酸钙(医嘱)
                List<DialysisAdviceDO> calciumGluconateList = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> StrUtil.isNotEmpty(dialysisAdviceDO.getAdviceName()) && dialysisAdviceDO.getAdviceName().contains("葡萄糖酸钙")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calciumGluconateList)) {
                    String calciumGluconate = getDrugInfo(calciumGluconateList);
                    dialysisDrugRespVO.setCalciumGluconate(calciumGluconate);
                }
            }

        }).collect(Collectors.toList());
    }

    private String getDrugInfo(List<DialysisAdviceDO> dialysisAdviceDOS) {
        return dialysisAdviceDOS.stream().map(dialysisAdviceDO -> {
            StringBuilder sb = new StringBuilder();
            //单次用量+制剂单位+医嘱描述
            StringBuilder append = sb.append(dialysisAdviceDO.getOneNo())
                    .append(dialysisAdviceDO.getFpreparaUnit())
                    .append("(" + dialysisAdviceDO.getAdviceDesprition() + ")");
            return append.toString();
        }).collect(Collectors.joining(","));
    }

    private DialyzeArrangeBaseVO getPatientInfo(DialyzeArrangeBaseVO dialyzeArrangeBaseVO) {
        PatientDO patientDO = patientMapper.selectById(dialyzeArrangeBaseVO.getPatientId());
        if (patientDO != null) {
            dialyzeArrangeBaseVO.setBirthday(patientDO.getBirthday());
            dialyzeArrangeBaseVO.setName(patientDO.getName());
            dialyzeArrangeBaseVO.setAge(patientDO.getAge());
            dialyzeArrangeBaseVO.setHospitalNo(patientDO.getHospitalNo());
        }
        return dialyzeArrangeBaseVO;
    }

    @Override
    public DialysisManagerRespVO getDialysisManagerInfo(DialysisManagerCreateReqVO createReqVO) {
        DialysisManagerDO dialysisManagerDO = dialysisManagerMapper.selectOne(new LambdaQueryWrapperX<DialysisManagerDO>()
                .eqIfPresent(DialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(DialysisManagerDO::getDateWeek, createReqVO.getDateWeek()));
        DialysisManagerRespVO dialysisManagerRespVO = DialysisManagerConvert.INSTANCE.convert(dialysisManagerDO);
        if (dialysisManagerRespVO != null) {
            if (dialysisManagerRespVO.getPrescriptionUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getPrescriptionUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setPrescriptionUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getBeforeEvaluateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getBeforeEvaluateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setBeforeEvaluateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getDoubleCheckUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getDoubleCheckUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setDoubleCheckUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getDetectionStateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getDetectionStateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setDetectionStateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getAfterEvaluateUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getAfterEvaluateUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setAfterEvaluateUserName(adminUser.getNickname());
            }
            if (dialysisManagerRespVO.getRecoverSummaryUser() != null) {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(dialysisManagerRespVO.getRecoverSummaryUser());
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                dialysisManagerRespVO.setRecoverSummaryUserName(adminUser.getNickname());
            }
        }
        return dialysisManagerRespVO;
    }

    @Override
    public PageResult<? extends DialyzeArrangeBaseVO> dialysisTestPage(DialysisManagerPageReqVO pageVO) {
        //查询班次(全天)
        if ("0".equals(pageVO.getWeekDay())) {
            pageVO.setWeekDay(null);
        }
        PageResult<DialysisManagerDO> dialysisManagerDOPageResult = dialysisManagerMapper.selectPage(pageVO);
        //血压
        if (1 == pageVO.getType()) {
            PageResult<DialysisBloodRespVO> dialysisBloodRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage6(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisBloodRespVOPageResult.getList())) {
                List<DialysisBloodRespVO> collect = dialysisBloodRespVOPageResult.getList().stream().peek(dialysisBloodRespVO -> {
                    //根据患者id查询患者基本信息
                    dialysisBloodRespVO = (DialysisBloodRespVO) getPatientInfo(dialysisBloodRespVO);
                    //透析模式
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getPrescription());
                        String dialyzeWayValue = jsonObject.getStr("dialyzeWayValue");
                        DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeWayValue);
                        dialysisBloodRespVO.setDialyzeDictValue(dialyzeWay != null ? dialyzeWay.getLabel() : null);
                    }
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(dialysisBloodRespVO.getFacilityId());
                    if (facilityDO != null) {
                        dialysisBloodRespVO.setFacilityName(facilityDO.getCode());
                    }
                    //干体重
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getBeforeEvaluate())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getBeforeEvaluate());
                        //干体重
                        dialysisBloodRespVO.setDryWeight(jsonObject.getStr("dryWeight"));
                    }
                    //目标脱水量
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getPrescription());
                        dialysisBloodRespVO.setTargetDehydratedLevel(jsonObject.getStr("targetDehydratedLevel"));
                    }
                    //超滤总量
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getUltrafiltrationTotal())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getUltrafiltrationTotal());
                        dialysisBloodRespVO.setUltrafiltrationTotal(jsonObject.getStr("ultrafiltrationTotal"));
                    }
                    //实际超滤量
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getAfterEvaluate())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getAfterEvaluate());
                        dialysisBloodRespVO.setActualUltrafiltrationCapacity(jsonObject.getStr("actualUltrafiltrationCapacity"));
                        //透后体重
                        dialysisBloodRespVO.setDialyzeAfterWeigh(jsonObject.getStr("dialyzeAfterWeigh"));
                    }
                    //根据当前时间和患者id查询检测记录
                    List<DialysisDetectionDO> dialysisDetectionDOS = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                            .between(DialysisDetectionDO::getDetectionTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                            .eq(DialysisDetectionDO::getPatientId, dialysisBloodRespVO.getPatientId())
                            .orderByAsc(DialysisDetectionDO::getId));
                    List<String> list = Lists.newArrayList();
                    if (CollectionUtil.isNotEmpty(dialysisDetectionDOS)) {
                        for (int i = 0; i < dialysisDetectionDOS.size(); i++) {
                            if (StrUtil.isEmpty(dialysisDetectionDOS.get(i).getBloodOne())) {
                                dialysisDetectionDOS.get(i).setBloodOne("");
                            }
                            if (StrUtil.isEmpty(dialysisDetectionDOS.get(i).getBloodTwo())) {
                                dialysisDetectionDOS.get(i).setBloodTwo("");
                            }
                            if (StrUtil.isEmpty(dialysisDetectionDOS.get(i).getPulse())) {
                                dialysisDetectionDOS.get(i).setPulse("");
                            }
                            if (0 == i) {
                                dialysisBloodRespVO.setOne(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            } else if (1 == i) {
                                dialysisBloodRespVO.setTwo(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            } else if (2 == i) {
                                dialysisBloodRespVO.setThree(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            } else if (3 == i) {
                                dialysisBloodRespVO.setFour(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            } else if (4 == i) {
                                dialysisBloodRespVO.setFive(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            } else {
                                list.add(dialysisDetectionDOS.get(i).getBloodOne() + "/" + dialysisDetectionDOS.get(i).getBloodTwo() + "," + dialysisDetectionDOS.get(i).getPulse());
                            }
                        }
                        //第n次
                        dialysisBloodRespVO.setMore(list);
                    }
                    if (StrUtil.isNotEmpty(dialysisBloodRespVO.getAfterEvaluate())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisBloodRespVO.getAfterEvaluate());
                        //透后血压
                        dialysisBloodRespVO.setAfterBlood(jsonObject.getStr("bpNoOne") + "/" + jsonObject.getStr("bpNoTwo") + "," + jsonObject.getStr("pNo"));
                    }
                }).collect(Collectors.toList());
                dialysisBloodRespVOPageResult.setList(collect);
                return dialysisBloodRespVOPageResult;
            }
        } else if (2 == pageVO.getType()) {//检测记录分页列表
            PageResult<DialysisTestRespVO> dialysisBloodRespVOPageResult = DialysisManagerConvert.INSTANCE.convertPage7(dialysisManagerDOPageResult);
            if (CollectionUtil.isNotEmpty(dialysisBloodRespVOPageResult.getList())) {
                List<DialysisTestRespVO> collect = dialysisBloodRespVOPageResult.getList().stream().peek(dialysisTestRespVO -> {
                    //根据患者id查询患者基本信息
                    dialysisTestRespVO = (DialysisTestRespVO) getPatientInfo(dialysisTestRespVO);
                    //透析模式
                    if (StrUtil.isNotEmpty(dialysisTestRespVO.getPrescription())) {
                        JSONObject jsonObject = JSONUtil.parseObj(dialysisTestRespVO.getPrescription());
                        String dialyzeWayValue = jsonObject.getStr("childDialyzeWayValue");
                        DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialyzeWayValue);
                        dialysisTestRespVO.setDialyzeDictValue(dialyzeWay == null ? null : dialyzeWay.getLabel());
                    }
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(dialysisTestRespVO.getFacilityId());
                    if (facilityDO != null) {
                        dialysisTestRespVO.setFacilityName(facilityDO.getCode());
                    }
                    //根据当前时间和患者id查询检测记录
                    List<DialysisDetectionDO> dialysisDetectionDOS = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                            .between(DialysisDetectionDO::getDetectionTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                            .eq(DialysisDetectionDO::getPatientId, dialysisTestRespVO.getPatientId())
                            .orderByAsc(DialysisDetectionDO::getId));
                    if (CollectionUtil.isNotEmpty(dialysisDetectionDOS)) {
                        //脉搏
                        List<String> plusList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getPulse).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(plusList)) {
                            //脉搏
                            dialysisTestRespVO.setPulse(plusList.stream().collect(Collectors.joining(",")));
                        }
                        //呼吸
                        List<String> breatheList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getBreathe).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(breatheList)) {
                            //呼吸
                            dialysisTestRespVO.setBreathe(breatheList.stream().collect(Collectors.joining(",")));
                        }
                        //ktv
                        List<String> ktvList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getKtv).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ktvList)) {
                            //ktv
                            dialysisTestRespVO.setKtv(ktvList.stream().collect(Collectors.joining(",")));
                        }
                        //血流量
                        List<String> bloodFlowList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getBloodFlow).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(bloodFlowList)) {
                            //血流量
                            dialysisTestRespVO.setBloodFlow(bloodFlowList.stream().collect(Collectors.joining(",")));
                        }
                        //静脉压
                        List<String> venousPressureList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getVenousPressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(venousPressureList)) {
                            //静脉压
                            dialysisTestRespVO.setVenousPressure(venousPressureList.stream().collect(Collectors.joining(",")));
                        }
                        //动脉压
                        List<String> arterialPressureList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getArterialPressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(arterialPressureList)) {
                            //动脉压
                            dialysisTestRespVO.setArterialPressure(arterialPressureList.stream().collect(Collectors.joining(",")));
                        }
                        //跨膜压
                        List<String> transmembranePressureList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getTransmembranePressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(transmembranePressureList)) {
                            //跨膜压
                            dialysisTestRespVO.setTransmembranePressure(transmembranePressureList.stream().collect(Collectors.joining(",")));
                        }
                        //超滤率
                        List<String> ultrafiltrationRateList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getUltrafiltrationRate).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ultrafiltrationRateList)) {
                            //超滤率
                            dialysisTestRespVO.setReplacementRate(ultrafiltrationRateList.stream().collect(Collectors.joining(",")));
                        }
                        //超滤量
                        List<String> ultrafiltrationCapacityList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getUltrafiltrationCapacity).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ultrafiltrationCapacityList)) {
                            //超滤量
                            dialysisTestRespVO.setUltrafiltrationCapacity(ultrafiltrationCapacityList.stream().collect(Collectors.joining(",")));
                        }
                        //钠浓度
                        List<String> sodiumConcentrationList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getSodiumConcentration).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(sodiumConcentrationList)) {
                            //钠浓度
                            dialysisTestRespVO.setSodiumConcentration(sodiumConcentrationList.stream().collect(Collectors.joining(",")));
                        }
                        //电导度
                        List<String> conductanceList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getConductance).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(conductanceList)) {
                            //电导度
                            dialysisTestRespVO.setConductance(conductanceList.stream().collect(Collectors.joining(",")));
                        }
                        //透析液温度
                        List<String> dialysateTemperatureList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getDialysateTemperature).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(dialysateTemperatureList)) {
                            //透析液温度
                            dialysisTestRespVO.setDialysateTemperature(dialysateTemperatureList.stream().collect(Collectors.joining(",")));
                        }
                        //置换率
                        List<String> replacementRateList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getReplacementRate).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(replacementRateList)) {
                            //置换率
                            dialysisTestRespVO.setReplacementRate(replacementRateList.stream().collect(Collectors.joining(",")));
                        }
                        //置换量
                        List<String> displacementQuantityList = dialysisDetectionDOS.stream().map(DialysisDetectionDO::getDisplacementQuantity).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(displacementQuantityList)) {
                            //置换量
                            dialysisTestRespVO.setDisplacementQuantity(displacementQuantityList.stream().collect(Collectors.joining(",")));
                        }
                    }
                }).collect(Collectors.toList());
                dialysisBloodRespVOPageResult.setList(collect);
                return dialysisBloodRespVOPageResult;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startDialyze(DialysisManagerCreateReqVO createReqVO) {
        DialysisManagerDO dialysisManagerDO1 = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, createReqVO.getPatientId(), DialysisManagerDO::getDateWeek, createReqVO.getDateWeek());
        if (dialysisManagerDO1 == null) {
            DialysisManagerDO dialysisManagerDO = DialysisManagerConvert.INSTANCE.convert(createReqVO);
            dialysisManagerMapper.insert(dialysisManagerDO);
        } else {
            dialysisManagerMapper.update(new DialysisManagerDO(), new LambdaUpdateWrapper<DialysisManagerDO>()
                    .set(DialysisManagerDO::getRecoverSummary, createReqVO.getRecoverSummary())
                    .eq(DialysisManagerDO::getPatientId, createReqVO.getPatientId())
                    .eq(DialysisManagerDO::getDateWeek, createReqVO.getDateWeek()));
        }
        //修改透析中状态
        arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek())
                .set(ArrangeClassesDO::getState, 2)
                .set(ArrangeClassesDO::getStartDialyzeTime, createReqVO.getStartTime()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endDialyze(DialysisManagerCreateReqVO createReqVO) {
        //检查透析处方确认状态
        DialysisManagerDO dialysisManagerDO = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, createReqVO.getPatientId(), DialysisManagerDO::getDateWeek, createReqVO.getDateWeek());
        if (dialysisManagerDO != null && "0".equals(dialysisManagerDO.getPrescriptionState())) {
            throw new ServiceException(GlobalErrorCodeConstants.END_DIALYSIS);
        }
        //检查下机护士密码是否正确
        AdminUserRespDTO user = adminUserApi.getUser(createReqVO.getUserId());
        if (user == null || !BCrypt.checkpw(createReqVO.getPassword(), user.getPassword())) {
            throw new ServiceException(GlobalErrorCodeConstants.LOGINEROOR);
        }
        //修改透析后状态-透析后
        arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek())
                .set(ArrangeClassesDO::getState, 3)
                .set(ArrangeClassesDO::getEndDialyzeTime, DateUtil.date()));
        //新增透析记录
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(ArrangeClassesDO::getPatientId, createReqVO.getPatientId(), ArrangeClassesDO::getTempType, 0, ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek());
        if (arrangeClassesDO != null) {
            PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
            FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
            FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(arrangeClassesDO.getFacilitySubareaId());
            DialysisRecordDO dialysisRecordDO = new DialysisRecordDO();
            dialysisRecordDO.setEndDialyzeTime(DateUtil.date());
            dialysisRecordDO.setDateWeek(DateUtil.parse(DateUtil.today()));
            dialysisRecordDO.setWeekDay(arrangeClassesDO.getDayState());
            dialysisRecordDO.setFacilityId(arrangeClassesDO.getFacilityId());
            dialysisRecordDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
            dialysisRecordDO.setFacilityName(facilityDO == null ? "" : facilityDO.getCode());
            dialysisRecordDO.setFacilitySubareaName(facilitySubareaDO == null ? "" : facilitySubareaDO.getName());
            dialysisRecordDO.setAreaCode(dialysisRecordDO.getFacilitySubareaName() + "/" + dialysisRecordDO.getFacilityName());
            dialysisRecordDO.setDialyzeWayValue(arrangeClassesDO.getDialysisName());
            dialysisRecordDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
            //透析处方-透析时长-目标脱水量
            if (dialysisManagerDO != null) {
                if (StrUtil.isNotEmpty(dialysisManagerDO.getPrescription())) {
                    JSONObject prescription = JSONUtil.parseObj(dialysisManagerDO.getPrescription());
                    dialysisRecordDO.setDuration(prescription.getStr("duration"));
                    dialysisRecordDO.setDryWeight(prescription.getStr("dryWeight"));
                    dialysisRecordDO.setTargetDehydratedLevel(prescription.getStr("targetDehydratedLevel"));
                }
                //透前评估-透前体重-超滤总量-脉搏透前-透前血压
                if (StrUtil.isNotEmpty(dialysisManagerDO.getBeforeEvaluate())) {
                    JSONObject beforeEvaluate = JSONUtil.parseObj(dialysisManagerDO.getBeforeEvaluate());
                    dialysisRecordDO.setDialyzeBeforeWeight(beforeEvaluate.getStr("dialyzeBeforeWeigh"));
                    dialysisRecordDO.setUltrafiltrationTotal(beforeEvaluate.getStr("ultrafiltrationTotal"));
                    dialysisRecordDO.setBeforePNo(beforeEvaluate.getStr("pNo"));
                    dialysisRecordDO.setBeforeBpNoOne(beforeEvaluate.getStr("bpNoOne"));
                    dialysisRecordDO.setBeforeBpNoTwo(beforeEvaluate.getStr("bpNoTwo"));


                }
                //透后评估-透后体重-实际超滤总量-实际治疗时长-脉搏透后-透后血压-透后症状
                if (StrUtil.isNotEmpty(dialysisManagerDO.getAfterEvaluate())) {
                    JSONObject afterEvaluate = JSONUtil.parseObj(dialysisManagerDO.getAfterEvaluate());
                    dialysisRecordDO.setDialyzeAfterWeigh(afterEvaluate.getStr("dialyzeAfterWeigh"));
                    dialysisRecordDO.setActualUltrafiltrationCapacity(afterEvaluate.getStr("actualUltrafiltrationCapacity"));
                    dialysisRecordDO.setTreatment(afterEvaluate.getStr("treatmentHour") + "/" + afterEvaluate.getStr("treatmentMin"));
                    dialysisRecordDO.setAfterPNo(afterEvaluate.getStr("pNo"));
                    dialysisRecordDO.setAfterBpNoOne(afterEvaluate.getStr("bpNoOne"));
                    dialysisRecordDO.setAfterBpNoTwo(afterEvaluate.getStr("bpNoTwo"));
                    dialysisRecordDO.setSymptom(afterEvaluate.getStr("motto59"));
                }
            }
            if (patientDO != null) {
                dialysisRecordDO.setPatientId(createReqVO.getPatientId());
                dialysisRecordDO.setPatientSource(patientDO.getPatientSource());
                dialysisRecordDO.setPatientName(patientDO.getName());
                dialysisRecordDO.setDialyzeNo(patientDO.getDialyzeNo());
                dialysisRecordDO.setSpellName(patientDO.getSpellName());

            }
            dialysisRecordMapper.insert(dialysisRecordDO);
        }

    }

    @Override
    public void updateState(DialysisManagerCreateReqVO createReqVO) {
        arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek())
                .set(ArrangeClassesDO::getState, createReqVO.getState()));
    }

    @Override
    public List<DialysisUseRespVO> userList(Long patientId) {
        List<DialysisUseRespVO> dialysisUseRespVOS = Lists.newArrayList();
        DialysisManagerDO dialysisManagerDO = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, patientId, DialysisManagerDO::getDateWeek, DateUtil.beginOfDay(DateUtil.date()));
        if (dialysisManagerDO != null && StrUtil.isNotEmpty(dialysisManagerDO.getPrescription())) {
            JSONObject jsonObject = JSONUtil.parseObj(dialysisManagerDO.getPrescription());
            //血透器
            if (StrUtil.isNotEmpty(jsonObject.getStr("hemodialysisDevice"))) {
                HisConsumablesDO hemodialysisDevice = hisConsumablesMapper.selectById(jsonObject.getStr("hemodialysisDevice"));
                if (hemodialysisDevice != null) {
                    DialysisUseRespVO dialysisUseRespVO = new DialysisUseRespVO();
                    dialysisUseRespVO.setTypeName("血透器");
                    dialysisUseRespVO.setSpecificationName(hemodialysisDevice.getConsumSpec());
                    dialysisUseRespVO.setNumber("1");
                    dialysisUseRespVO.setLotNumber(hemodialysisDevice.getApprovalNumber());
                    dialysisUseRespVOS.add(dialysisUseRespVO);
                }
            }
            //血滤器
            if (StrUtil.isNotEmpty(jsonObject.getStr("bloodFilter"))) {
                HisConsumablesDO bloodFilter = hisConsumablesMapper.selectById(jsonObject.getStr("bloodFilter"));
                if (bloodFilter != null) {
                    DialysisUseRespVO dialysisUseRespVO = new DialysisUseRespVO();
                    dialysisUseRespVO.setTypeName("血滤器");
                    dialysisUseRespVO.setSpecificationName(bloodFilter.getConsumSpec());
                    dialysisUseRespVO.setNumber("1");
                    dialysisUseRespVO.setLotNumber(bloodFilter.getApprovalNumber());
                    dialysisUseRespVOS.add(dialysisUseRespVO);
                }
            }
            //灌流器
            if (StrUtil.isNotEmpty(jsonObject.getStr("perfumer"))) {
                HisConsumablesDO perfumer = hisConsumablesMapper.selectById(jsonObject.getStr("perfumer"));
                if (perfumer != null) {
                    DialysisUseRespVO dialysisUseRespVO = new DialysisUseRespVO();
                    dialysisUseRespVO.setTypeName("灌流器");
                    dialysisUseRespVO.setSpecificationName(perfumer.getConsumSpec());
                    dialysisUseRespVO.setNumber("1");
                    dialysisUseRespVO.setLotNumber(perfumer.getApprovalNumber());
                    dialysisUseRespVOS.add(dialysisUseRespVO);
                }
            }
        }
        return dialysisUseRespVOS;
    }

    @Override
    public List<DialysisTotalTimeVo> getDialysisTotalTime(AggregateAnalysisParams pageParams) {
        return dialysisManagerMapper.getDialysisTotalTime(pageParams);
    }

    @Override
    public List<DialysisManagerDO> getList(String patientId) {
        MPJLambdaWrapper<DialysisManagerDO> wrapper = new MPJLambdaWrapper<DialysisManagerDO>(DialysisManagerDO.class);
        wrapper.eq(StringUtils.isNotNull(patientId), JkImplementationDO::getNurseId, patientId)
                .in(true, JkImplementationDO::getState, 3, 4)
                .orderByDesc(JkImplementationDO::getNurseId)
                .last(true, "limit 12");
        List<DialysisManagerDO> dialysisManagerDOS = dialysisManagerMapper.selectList(wrapper);
        return dialysisManagerMapper.selectList(wrapper);
    }

    @Override
    public TransferRecordsRespVO classesRecords(TransferRecordsReqVO recordsReqVO) {
        TransferRecordsRespVO transferRecordsRespVO = new TransferRecordsRespVO();
        //血管通路
        MPJLambdaWrapper<BloodRoadDO> wrapper = new MPJLambdaWrapper<>(BloodRoadDO.class);
        wrapper.leftJoin(PassageDO.class, PassageDO::getBloodId, BloodRoadDO::getId)
                .leftJoin(ComplicationDO.class, ComplicationDO::getBloodId, BloodRoadDO::getId)
                .select(BloodRoadDO::getDialyzeNo, BloodRoadDO::getPatientName, BloodRoadDO::getType)
                .select(PassageDO::getProject, PassageDO::getResult, PassageDO::getDispose)
                .select(ComplicationDO::getSymptom, ComplicationDO::getDescription)
                .selectAs(ComplicationDO::getType, "occurType")
                .eq(recordsReqVO.getRecordTime() != null, BloodRoadDO::getStartTime, recordsReqVO.getRecordTime());
        List<BloodPassageComplicationRespVO> bloodPassageComplicationRespVOS = bloodRoadMapper.selectJoinList(BloodPassageComplicationRespVO.class, wrapper);
        transferRecordsRespVO.setBloodPassageComplicationRespVOS(bloodPassageComplicationRespVOS);
        //故障透析机
        List<RepairRegisterDO> repairRegisterDOList = repairRegisterMapper.selectList(RepairRegisterDO::getRepairTime, recordsReqVO.getRecordTime(), RepairRegisterDO::getDeptId, recordsReqVO.getDeptId());
        List<RepairRegisterRespVO> repairRegisterRespVOS = RepairRegisterConvert.INSTANCE.convertList(repairRegisterDOList);
        if (CollectionUtil.isNotEmpty(repairRegisterRespVOS)) {
            repairRegisterRespVOS = repairRegisterRespVOS.stream().peek(repairRegisterRespVO -> {
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectById(repairRegisterRespVO.getManagerId());
                if (facilityManagerDO != null) {
                    if (StrUtil.isNotEmpty(facilityManagerDO.getFacilityCode())) {
                        repairRegisterRespVO.setFacilityCode(facilityManagerDO.getFacilityCode());
                        FacilityNameDO facilityNameDO = facilityNameMapper.selectById(facilityManagerDO.getFacilityNameId());
                        repairRegisterRespVO.setFaciltyName(facilityNameDO != null ? facilityNameDO.getName() : null);
                    }
                    if (StrUtil.isNotEmpty(facilityManagerDO.getFacilityTypeId())) {
                        List<Long> typeIds = Arrays.stream(facilityManagerDO.getFacilityTypeId().split(",")).map(Long::valueOf).collect(Collectors.toList());
                        List<FacilityNameDO> facilityNameDOList = facilityNameMapper.selectList(FacilityNameDO::getId, typeIds);
                        if (CollectionUtil.isNotEmpty(facilityNameDOList)) {
                            String model = facilityNameDOList.stream().map(FacilityNameDO::getName).collect(Collectors.joining("/"));
                            repairRegisterRespVO.setFaciltyMode(model);
                        }
                    }
                }
            }).collect(Collectors.toList());
            transferRecordsRespVO.setRepairRegisterRespVOS(repairRegisterRespVOS);
        }
        //透析例次
        //查询当天排班透析模式和数量
        List<TeamPatientDO> teamPatientDOS = teamPatientMapper.selectList(new LambdaQueryWrapperX<TeamPatientDO>()
                .eqIfPresent(TeamPatientDO::getWeekDay, recordsReqVO.getWeekDay())
                .eqIfPresent(TeamPatientDO::getDateWeek, recordsReqVO.getRecordTime())
                .inIfPresent(TeamPatientDO::getFacilitySubareaId, recordsReqVO.getFacilitySubareaId())
                .eqIfPresent(TeamPatientDO::getTempType, 0));
        if (CollectionUtil.isNotEmpty(teamPatientDOS)) {
            Map<String, List<TeamPatientDO>> teamPatientMaps = teamPatientDOS.stream().collect(Collectors.groupingBy(TeamPatientDO::getDialyzeName));
            List<Map<String, Object>> dialysisCounts = teamPatientMaps.entrySet().stream().map(entry -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("dialysisName", entry.getKey());
                map.put("count", entry.getValue().size());
                return map;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dialysisCounts)) {
                int sum = dialysisCounts.stream().mapToInt(map -> (Integer) map.get("count")).sum();
                transferRecordsRespVO.setSum(sum);
            }
            transferRecordsRespVO.setDialysisCounts(dialysisCounts);
        }

        //穿刺/换药
        int dressingChangeOne = 0, dressingChangeTwo = 0;
        List<DialysisManagerDO> dialysisManagerDOS = dialysisManagerMapper.selectList(new LambdaQueryWrapperX<DialysisManagerDO>()
                .eqIfPresent(DialysisManagerDO::getDateWeek, recordsReqVO.getRecordTime())
                .eqIfPresent(DialysisManagerDO::getWeekDay, recordsReqVO.getWeekDay())
                .eqIfPresent(DialysisManagerDO::getFacilitySubareaId, recordsReqVO.getFacilitySubareaId())
                .eqIfPresent(DialysisManagerDO::getDeptId, recordsReqVO.getDeptId()));
        if (CollectionUtil.isNotEmpty(dialysisManagerDOS)) {
            for (DialysisManagerDO dialysisManagerDO : dialysisManagerDOS) {
                if (StrUtil.isNotEmpty(dialysisManagerDO.getRecoverSummary())) {
                    JSONObject jsonObject = JSONUtil.parseObj(dialysisManagerDO.getRecoverSummary());
                    String dressingChange = jsonObject.getStr("dressingChange");
                    //1穿刺，2换药
                    if ("1".equals(dressingChange)) {
                        dressingChangeOne += 1;
                    } else if ("2".equals(dressingChange)) {
                        dressingChangeTwo += 1;
                    }
                }
            }
        }
        transferRecordsRespVO.setDressingChangeOne(dressingChangeOne);
        transferRecordsRespVO.setDressingChangeTwo(dressingChangeTwo);
        //患者病情
        List<DialysisManagerRespVO> dialysisManagerRespVOS = DialysisManagerConvert.INSTANCE.convertList(dialysisManagerDOS);
        if (CollectionUtil.isNotEmpty(dialysisManagerRespVOS)) {
            dialysisManagerRespVOS.stream().map(dialysisManagerRespVO -> {
                DialysisManagerInfoRespVO dialysisManagerRespVO1 = new DialysisManagerInfoRespVO();
                FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(dialysisManagerRespVO.getFacilitySubareaId());
                dialysisManagerRespVO1.setFacilitysubAreaName(facilitySubareaDO != null ? facilitySubareaDO.getName() : null);
                PatientDO patientDO = patientMapper.selectById(dialysisManagerRespVO.getPatientId());
                if (patientDO != null) {
                    dialysisManagerRespVO1.setName(patientDO.getName());
                    dialysisManagerRespVO1.setDialyzeNo(patientDO.getDialyzeNo());
                }

                return dialysisManagerRespVO1;
            }).collect(Collectors.toList());
        }

        return transferRecordsRespVO;
    }

    /**
     * 交班日志新
     * @param vo
     * @return
     */
    @Override
    public TransferRecordsRespVO newSlassesRecords(TransferRecordsReqVO vo, Long SystemDeptId){

        TransferRecordsRespVO overview = getOverview(vo,SystemDeptId);
        return overview;
    }

    /**
     * 概况
     * @return
     */
    private TransferRecordsRespVO getOverview(TransferRecordsReqVO vo, Long SystemDeptId){
        Date recordTime = vo.getRecordTime();
        String format = DateUtil.format(recordTime, "yyyy-MM-dd 00:00:00");
        TransferRecordsRespVO transferRecordsRespVO = new TransferRecordsRespVO();
        //排班人数
        transferRecordsRespVO.setDialyzeNumber(String.valueOf(arrangeClassesMapper.selectCount(ArrangeClassesDO::getClassesTime, vo.getRecordTime())));
        transferRecordsRespVO.setPenetration("0");
        /**
         * 转入、转出、住院
         */
        List<Map<String, Object>> list = outcomeRecordMapper.queryIndividuation();
        transferRecordsRespVO.setTrunIn("0");
        transferRecordsRespVO.setTrunOut("0");
        transferRecordsRespVO.setHospitalized("0");
        if(list.size() > 0){
            list.forEach(res ->{
                switch (res.get("description").toString()){
                    case "转入":
                        transferRecordsRespVO.setTrunIn(String.valueOf(res.get("total_amount")));
                        break;
                    case "转出":
                        transferRecordsRespVO.setTrunOut(String.valueOf(res.get("total_amount")));
                        break;
                    case "住院":
                        transferRecordsRespVO.setHospitalized(String.valueOf(res.get("total_amount")));
                        break;
                }
            });
        }
        //死亡
        transferRecordsRespVO.setDeathNumber(String.valueOf(outcomeRecordMapper.selectCount(OutcomeRecordDO::getClassify, "456")));
        //留治
        int i = Integer.valueOf(transferRecordsRespVO.getTrunIn())
                + Integer.valueOf(transferRecordsRespVO.getHospitalized())
                - Integer.valueOf(transferRecordsRespVO.getTrunOut());
        transferRecordsRespVO.setHospitalization(String.valueOf(i));
        //无肝素透析
        transferRecordsRespVO.setNonHeparin(String.valueOf(contradictMapper.selectCount(ContradictDO::getProtocolType, 2, ContradictDO::getDrugTypeId, 62)));
        //自体动静脉内瘘
        transferRecordsRespVO.setArtery(String.valueOf(hemodialysisManagerMapper.getNeilou(format)));
        transferRecordsRespVO.setLongDuct("0");
        transferRecordsRespVO.setTemporarilyDuct("0");
        transferRecordsRespVO.setLabour("0");
        transferRecordsRespVO.setClotting("0");
        // 超滤总量大于5L
        transferRecordsRespVO.setTotalUltrafiltration(String.valueOf(hemodialysisManagerMapper.selectCount(new LambdaQueryWrapperX<HemodialysisManagerDO>().eq(HemodialysisManagerDO::getHemodialysisTime,format).gt(HemodialysisManagerDO::getBeforeUltrafiltrationtotal, 5))));
        //体温大于37.5
        transferRecordsRespVO.setAnimal(String.valueOf(hemodialysisManagerMapper.selectCount(new LambdaQueryWrapperX<HemodialysisManagerDO>().eq(HemodialysisManagerDO::getHemodialysisTime,format).gt(HemodialysisManagerDO::getAfterDegreeCelsius, 37.5))));
        //血管通路
        transferRecordsRespVO.setBloodPassageComplicationRespVOS(getByList(format));
        //故障透析机
        transferRecordsRespVO.setRepairRegisterRespVOS(getRepairRegister(format));
        //透析例次
        transferRecordsRespVO.setDialysisCounts(getDialysisCounts(format));
        //穿刺、换药
        List<Map<String, Object>> puncture = getPuncture(format);
        transferRecordsRespVO.setDressingChangeOne(0);
        transferRecordsRespVO.setDressingChangeTwo(0);
        if(puncture.size() > 0){
            puncture.forEach(res ->{
                switch (res.get("types").toString()){
                    case "1": //穿刺
                        transferRecordsRespVO.setDressingChangeOne(Integer.valueOf(res.get("count").toString()));
                        break;
                    case "2": //换药
                        transferRecordsRespVO.setDressingChangeTwo(Integer.valueOf(res.get("count").toString()));
                        break;
                }
            });
        }
        transferRecordsRespVO.setPatientConditionVos(getDisease(format));
        //最新备注信息(日期)
        JSONObject remark = getRemark(recordTime, SystemDeptId);
        transferRecordsRespVO.setRemarks(remark.getStr("0"));
        transferRecordsRespVO.setRemarksDoctor(remark.getStr("1"));
        return transferRecordsRespVO;
    }

    /**
     * 血管通路
     * @return
     */
    private List<BloodPassageComplicationRespVO> getByList(String format){
        return bloodRoadMapper.getList(format);
    }

    /**
     * 故障透析机
     * @param format
     * @return
     */
    private List<RepairRegisterRespVO> getRepairRegister(String format){
        return bloodRoadMapper.getRepairRegister(format);
    }

    /**
     * 透析例次
     * @param format
     * @return
     */
    private List<Map<String,Object>> getDialysisCounts(String format){
        return hemodialysisManagerMapper.getDialysisCounts(format);
    }

    /**
     * 穿刺换药
     * @param format
     * @return
     */
    private List<Map<String,Object>> getPuncture(String format){
        return  hemodialysisManagerMapper.getPuncture(format);
    }

    /**
     患者病情
     * @param format
     * @return
     */
    private List<PatientConditionVo> getDisease(String format){
        return  hemodialysisManagerMapper.getDisease(format);
    }

    /**
     * 备注,最新
     * @return
     */
    private JSONObject getRemark(Date date,Long deptId){
        String format = DateUtil.format(date, "yyyy-MM-dd");
        HandoverLog handoverLog = getNewRemarks(deptId, 0, format);//护士
        JSONObject res = new JSONObject();
        if(!StrUtil.isEmptyIfStr(handoverLog)){
            res.set("0",handoverLog.getRemarks());
        }
        HandoverLog handoverLog1 = getNewRemarks(deptId, 1, format);//医生
        if(!StrUtil.isEmptyIfStr(handoverLog1)){
            res.set("1",handoverLog1.getRemarks());
        }
        return res;
    }

    @Override
    public void save(HandoverLog log){
        hemodialysisManagerMapper.saveLog(log);
    }

    @Override
    public HandoverLog getNewRemarks(Long deptId,Integer type,String format){
        return hemodialysisManagerMapper.getNewRemarks(deptId,type,format);
    }
}
