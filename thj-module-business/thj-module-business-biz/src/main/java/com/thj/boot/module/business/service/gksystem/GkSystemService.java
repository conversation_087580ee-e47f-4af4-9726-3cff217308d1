package com.thj.boot.module.business.service.gksystem;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gksystem.GkSystemDO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemCreateReqVO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemPageReqVO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 感控制度 Service 接口
 *
 * <AUTHOR>
 */
public interface GkSystemService {

    /**
     * 创建感控制度
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGkSystem(GkSystemCreateReqVO createReqVO);

    /**
     * 更新感控制度
     *
     * @param updateReqVO 更新信息
     */
    void updateGkSystem(GkSystemUpdateReqVO updateReqVO);

    /**
     * 删除感控制度
     *
     * @param id 编号
     */
    void deleteGkSystem(Long id);

    /**
     * 获得感控制度
     *
     * @param id 编号
     * @return 感控制度
     */
    GkSystemDO getGkSystem(Long id);

    /**
     * 获得感控制度列表
     *
     * @param ids 编号
     * @return 感控制度列表
     */
    List<GkSystemDO> getGkSystemList(Collection<Long> ids);

    /**
     * 获得感控制度分页
     *
     * @param pageReqVO 分页查询
     * @return 感控制度分页
     */
    PageResult<GkSystemDO> getGkSystemPage(GkSystemPageReqVO pageReqVO);


    /**
     * 根据分组查询感控制度
     * @param groupId
     * @return
     */
    List<GkSystemDO> getGroupList(Long groupId);

    void updateSort(List<GkSystemDO> list);

    List<GkSystemDO> h5List();



}
