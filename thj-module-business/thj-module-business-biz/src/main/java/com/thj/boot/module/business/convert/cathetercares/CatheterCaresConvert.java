package com.thj.boot.module.business.convert.cathetercares;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.cathetercares.CatheterCaresDO;
import com.thj.boot.module.business.pojo.cathetercares.vo.CatheterCaresCreateReqVO;
import com.thj.boot.module.business.pojo.cathetercares.vo.CatheterCaresRespVO;
import com.thj.boot.module.business.pojo.cathetercares.vo.CatheterCaresUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析导管皮肤出口护理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CatheterCaresConvert {

    CatheterCaresConvert INSTANCE = Mappers.getMapper(CatheterCaresConvert.class);

    CatheterCaresDO convert(CatheterCaresCreateReqVO bean);

    CatheterCaresDO convert(CatheterCaresUpdateReqVO bean);

    CatheterCaresRespVO convert(CatheterCaresDO bean);

    List<CatheterCaresRespVO> convertList(List<CatheterCaresDO> list);

    PageResult<CatheterCaresRespVO> convertPage(PageResult<CatheterCaresDO> page);


}
