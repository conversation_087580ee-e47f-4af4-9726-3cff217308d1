package com.thj.boot.module.business.pojo.facilitysubarea.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FacilitySubareaPageReqVO extends PageParam {

    /**
     * 分区设置id
     */
    private Long id;
    /**
     * 分区名称
     */
    private String name;
    /**
     * 分区类型(字典获取)
     */
    private String type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店id
     */
    private Long deptId;
}
