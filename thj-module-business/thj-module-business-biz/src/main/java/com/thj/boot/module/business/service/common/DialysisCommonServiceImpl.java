package com.thj.boot.module.business.service.common;

import com.google.common.collect.Maps;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/16 10:40
 * @description
 */
@Service
public class DialysisCommonServiceImpl implements DialysisCommonService {

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;

    @Override
    public Map<String, Object> getCommonList(String names) {
        Map<String, Object> map = Maps.newHashMap();
        List<String> nameList = Arrays.stream(names.split(",")).collect(Collectors.toList());
        for (int i = 0; i < nameList.size(); i++) {
            if ("滤过".equals(nameList.get(i))) {
                List<HisConsumablesDO> hisConsumablesDOList = hisConsumablesMapper.selectList(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .likeIfPresent(HisConsumablesDO::getConsumType, "滤过%器"));
                List<HisConsumablesDO> hisConsumablesDOList1 = hisConsumablesMapper.selectList(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .likeIfPresent(HisConsumablesDO::getConsumType, "血液透析%器"));
                if (CollectionUtils.isEmpty(hisConsumablesDOList)) {
                    map.put(i + "", hisConsumablesDOList1);
                }else {
                    List<Long> collect = hisConsumablesDOList.stream().map(HisConsumablesDO::getConsumId).collect(Collectors.toList());
                    hisConsumablesDOList1 = hisConsumablesDOList1.stream().filter(hisConsumablesDO -> !collect.contains(hisConsumablesDO.getConsumId())).collect(Collectors.toList());
                    hisConsumablesDOList.addAll(hisConsumablesDOList1);
                    map.put(i + "", hisConsumablesDOList);
                }

            }else {
                List<HisConsumablesDO> hisConsumablesDOList = hisConsumablesMapper.selectList(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .likeIfPresent(HisConsumablesDO::getConsumType, nameList.get(i)+"%器"));
                map.put(i + "", hisConsumablesDOList);
            }

        }
        return map;
    }
}
