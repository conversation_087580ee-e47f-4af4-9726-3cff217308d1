package com.thj.boot.module.business.service.informedconsent;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.informedconsent.InformedConsentDO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentCreateReqVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentPageReqVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentRespVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentUpdateReqVO;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

/**
 * 知情同意书 Service 接口
 *
 * <AUTHOR>
 */
public interface InformedConsentService {

    /**
     * 创建知情同意书
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInformedConsent(InformedConsentCreateReqVO createReqVO, HttpServletRequest request);

    /**
     * 更新知情同意书
     *
     * @param updateReqVO 更新信息
     */
    void updateInformedConsent( InformedConsentUpdateReqVO updateReqVO);

    /**
     * 删除知情同意书
     *
     * @param id 编号
     */
    void deleteInformedConsent(Long id);

    /**
     * 获得知情同意书
     *
     * @param id 编号
     * @return 知情同意书
     */
    InformedConsentDO getInformedConsent(Long id);

    /**
     * 获得知情同意书列表
     *
     * @param ids 编号
     * @return 知情同意书列表
     */
    List<InformedConsentDO> getInformedConsentList(Collection<Long> ids);

    /**
     * 获得知情同意书分页
     *
     * @param pageReqVO 分页查询
     * @return 知情同意书分页
     */
    PageResult<InformedConsentDO> getInformedConsentPage(InformedConsentPageReqVO pageReqVO);

    /**
     * 获得知情同意书列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 知情同意书列表
     */
    List<InformedConsentRespVO> getInformedConsentList(InformedConsentCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2024/1/17 14:31
     * @Description 新增文书
     **/
    Long createAppInformedConsent(InformedConsentCreateReqVO createReqVO);


}
