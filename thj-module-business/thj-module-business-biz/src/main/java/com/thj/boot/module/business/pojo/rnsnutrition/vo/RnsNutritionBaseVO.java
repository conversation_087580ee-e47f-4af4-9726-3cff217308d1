package com.thj.boot.module.business.pojo.rnsnutrition.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
* 营养状况评估 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RnsNutritionBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 疾病评分
     */
    private String illnessMark;
    /**
     * 营养评分
     */
    private String nutritionMark;
    /**
     * 年龄评分
     */
    private String ageMark;
    /**
     * 备注
     */
    private String remark;
    /**
     * 大json
     */
    private String evaluateContent;


}
