package com.thj.boot.module.business.service.labels;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.labels.LabelsDO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsCreateReqVO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsPageReqVO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsRespVO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 标签管理 Service 接口
 *
 * <AUTHOR>
 */
public interface LabelsService {

    /**
     * 创建标签管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLabels( LabelsCreateReqVO createReqVO);

    /**
     * 更新标签管理
     *
     * @param updateReqVO 更新信息
     */
    void updateLabels( LabelsUpdateReqVO updateReqVO);

    /**
     * 删除标签管理
     *
     * @param id 编号
     */
    void deleteLabels(Long id);

    /**
     * 获得标签管理
     *
     * @param id 编号
     * @return 标签管理
     */
    LabelsDO getLabels(Long id);

    /**
     * 获得标签管理列表
     *
     * @param ids 编号
     * @return 标签管理列表
     */
    List<LabelsDO> getLabelsList(Collection<Long> ids);

    /**
     * 获得标签管理分页
     *
     * @param pageReqVO 分页查询
     * @return 标签管理分页
     */
    PageResult<LabelsDO> getLabelsPage(LabelsPageReqVO pageReqVO);

    /**
     * 获得标签管理列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 标签管理列表
     */
    List<LabelsRespVO> getLabelsList(LabelsCreateReqVO createReqVO);

}
