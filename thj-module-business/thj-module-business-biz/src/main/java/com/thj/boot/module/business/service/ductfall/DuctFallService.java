package com.thj.boot.module.business.service.ductfall;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.ductfall.DuctFallDO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallCreateReqVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallPageReqVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 导管脱落风险评估 Service 接口
 *
 * <AUTHOR>
 */
public interface DuctFallService {

    /**
     * 创建导管脱落风险评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDuctFall( DuctFallCreateReqVO createReqVO);

    /**
     * 更新导管脱落风险评估
     *
     * @param updateReqVO 更新信息
     */
    void updateDuctFall( DuctFallUpdateReqVO updateReqVO);

    /**
     * 删除导管脱落风险评估
     *
     * @param id 编号
     */
    void deleteDuctFall(Long id);

    /**
     * 获得导管脱落风险评估
     *
     * @param id 编号
     * @return 导管脱落风险评估
     */
    DuctFallDO getDuctFall(Long id);

    /**
     * 获得导管脱落风险评估列表
     *
     * @param ids 编号
     * @return 导管脱落风险评估列表
     */
    List<DuctFallDO> getDuctFallList(Collection<Long> ids);

    /**
     * 获得导管脱落风险评估分页
     *
     * @param pageReqVO 分页查询
     * @return 导管脱落风险评估分页
     */
    PageResult<DuctFallDO> getDuctFallPage(DuctFallPageReqVO pageReqVO);

    /**
     * 获得导管脱落风险评估列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 导管脱落风险评估列表
     */
    List<DuctFallDO> getDuctFallList(DuctFallCreateReqVO exportReqVO);

}
