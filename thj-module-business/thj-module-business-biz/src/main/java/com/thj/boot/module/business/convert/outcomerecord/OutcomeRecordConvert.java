package com.thj.boot.module.business.convert.outcomerecord;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.outcomerecord.OutcomeRecordDO;
import com.thj.boot.module.business.pojo.outcomerecord.vo.OutcomeRecordCreateReqVO;
import com.thj.boot.module.business.pojo.outcomerecord.vo.OutcomeRecordRespVO;
import com.thj.boot.module.business.pojo.outcomerecord.vo.OutcomeRecordUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 转归记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OutcomeRecordConvert {

    OutcomeRecordConvert INSTANCE = Mappers.getMapper(OutcomeRecordConvert.class);

    OutcomeRecordDO convert(OutcomeRecordCreateReqVO bean);

    OutcomeRecordDO convert(OutcomeRecordUpdateReqVO bean);

    OutcomeRecordRespVO convert(OutcomeRecordDO bean);

    List<OutcomeRecordRespVO> convertList(List<OutcomeRecordDO> list);

    PageResult<OutcomeRecordRespVO> convertPage(PageResult<OutcomeRecordDO> page);


}
