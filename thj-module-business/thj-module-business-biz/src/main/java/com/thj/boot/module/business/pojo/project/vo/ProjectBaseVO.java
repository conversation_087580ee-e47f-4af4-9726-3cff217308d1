package com.thj.boot.module.business.pojo.project.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.math.BigDecimal;

/**
* 项目 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ProjectBaseVO extends BaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 拼音
     */
    private String nickName;
    /**
     * 单位
     */
    private String unit;
    /**
     * 医保编码
     */
    private String baoCode;
    /**
     * 给药途径
     */
    private String way;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 医保限价
     */
    private BigDecimal doctorPrice;
    /**
     * 项目编码
     */
    private String code;
    /**
     * 状态：0-开启，1-关闭
     */
    private String status;
    /**
     * 分类
     */
    private String type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 类型0-自动，1-手动
     */
    private Integer genres;

}
