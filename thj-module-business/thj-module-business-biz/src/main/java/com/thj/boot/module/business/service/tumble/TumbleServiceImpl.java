package com.thj.boot.module.business.service.tumble;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.tumble.TumbleConvert;
import com.thj.boot.module.business.dal.datado.tumble.TumbleDO;
import com.thj.boot.module.business.dal.mapper.tumble.TumbleMapper;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleCreateReqVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumblePageReqVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 跌倒评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TumbleServiceImpl implements TumbleService {

    @Resource
    private TumbleMapper tumbleMapper;

    @Override
    public Long createTumble(TumbleCreateReqVO createReqVO) {
        // 插入
        TumbleDO tumble = TumbleConvert.INSTANCE.convert(createReqVO);
        tumbleMapper.insert(tumble);
        // 返回
        return tumble.getId();
    }

    @Override
    public void updateTumble(TumbleUpdateReqVO updateReqVO) {
        // 更新
        TumbleDO updateObj = TumbleConvert.INSTANCE.convert(updateReqVO);
        tumbleMapper.updateById(updateObj);
    }

    @Override
    public void deleteTumble(Long id) {
        // 删除
        tumbleMapper.deleteById(id);
    }


    @Override
    public TumbleDO getTumble(Long id) {
        return tumbleMapper.selectById(id);
    }

    @Override
    public List<TumbleDO> getTumbleList(Collection<Long> ids) {
        return tumbleMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TumbleDO> getTumblePage(TumblePageReqVO pageReqVO) {
        return tumbleMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TumbleDO> getTumbleList(TumbleCreateReqVO createReqVO) {
        return tumbleMapper.selectList(createReqVO);
    }

}
