package com.thj.boot.module.business.pojo.renalproject.vo;

import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.pojo.renalcheckinfo.vo.RenalCheckInfoUpdateReqVO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class RenalProjectUpdateReqVO extends RenalProjectBaseVO {

    @NotNull(message = "不能为空")
    private Long id;

    private List<InfraResult> infraResults;

    private RenalCheckInfoUpdateReqVO createReqVO;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;
    private String projectInfo;

    private String list;

    private String order;

}
