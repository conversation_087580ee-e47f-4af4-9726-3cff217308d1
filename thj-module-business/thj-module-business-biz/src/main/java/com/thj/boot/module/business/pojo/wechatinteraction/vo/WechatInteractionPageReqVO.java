package com.thj.boot.module.business.pojo.wechatinteraction.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/25 16:34
 * @description
 */
@Data
public class WechatInteractionPageReqVO extends PageParam {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 推动时间
     */
    private Date sendTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 1-医嘱，2-公告，3-健康教育
     */
    private String type;
    /**
     * 推动内容
     */
    private String content;
    /**
     * 反馈时间
     */
    private Date feedbackTime;
    /**
     * 反馈内容
     */
    private String feedbackContent;
    /**
     * 患者id
     */
    private Long patientId;
}
