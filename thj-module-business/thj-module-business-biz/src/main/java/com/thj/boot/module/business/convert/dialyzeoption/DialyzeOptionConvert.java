package com.thj.boot.module.business.convert.dialyzeoption;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析方案 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialyzeOptionConvert {

    DialyzeOptionConvert INSTANCE = Mappers.getMapper(DialyzeOptionConvert.class);

    DialyzeOptionDO convert(DialyzeOptionCreateReqVO bean);

    DialyzeOptionDO convert(DialyzeOptionUpdateReqVO bean);

    DialyzeOptionRespVO convert(DialyzeOptionDO bean);

    List<DialyzeOptionRespVO> convertList(List<DialyzeOptionDO> list);

    PageResult<DialyzeOptionRespVO> convertPage(PageResult<DialyzeOptionDO> page);


}
