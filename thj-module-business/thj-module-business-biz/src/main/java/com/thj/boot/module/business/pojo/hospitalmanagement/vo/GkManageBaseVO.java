package com.thj.boot.module.business.pojo.hospitalmanagement.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

/**
* 医院感染管理质控 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class GkManageBaseVO extends BaseDO {
    /**
     * 序号
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 分值
     */
    private Integer score;
    /**
     * 内容
     */
    private String content;

}
