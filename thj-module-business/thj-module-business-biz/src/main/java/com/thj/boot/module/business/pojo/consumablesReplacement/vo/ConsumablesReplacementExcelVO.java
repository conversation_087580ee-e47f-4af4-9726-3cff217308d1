package com.thj.boot.module.business.pojo.consumablesReplacement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * 患者首次自疗详情 （导出）
 */
@Data
public class ConsumablesReplacementExcelVO implements Serializable {

    /**
     * 序号
     */
    @ExcelProperty("序号")
    private int index;


    /**
     * 序列号
     */
    @ExcelProperty("序列号")
    @ColumnWidth(25)
    private String code;

    /**
     * 设备名称id
     */
    @ExcelProperty("设备名称")
    @ColumnWidth(20)
    private Long facilityNameId;

    /**
     * 设备型号id
     */
    @ExcelProperty("设备型号")
    @ColumnWidth(20)
    private String facilityTypeId;

    /**
     * 透析机消毒液更换（有几个月没有更换）
     */
    @ExcelProperty("透析机消毒液更换")
    @ColumnWidth(30)
    private String liquidReplaceDiffMonths;


    /**
     * 细菌过滤器更换（有几个月没有更换）
     */
    @ExcelProperty("细菌过滤器更换")
    @ColumnWidth(30)
    private String bacterialFilterDiffMonths;

    /**
     * 空气滤网清洁（有几个月没有更换）
     */
    @ExcelProperty("空气滤网清洁")
    @ColumnWidth(30)
    private String airFilterDiffMonths;

}
