package com.thj.boot.module.business.convert.gkinroles;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkinroles.GkInRolesDO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesCreateReqVO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesRespVO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 感控培训参与角色关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkInRolesConvert {

    GkInRolesConvert INSTANCE = Mappers.getMapper(GkInRolesConvert.class);

    GkInRolesDO convert(GkInRolesCreateReqVO bean);

    GkInRolesDO convert(GkInRolesUpdateReqVO bean);

    GkInRolesRespVO convert(GkInRolesDO bean);

    List<GkInRolesRespVO> convertList(List<GkInRolesDO> list);

    PageResult<GkInRolesRespVO> convertPage(PageResult<GkInRolesDO> page);


}
