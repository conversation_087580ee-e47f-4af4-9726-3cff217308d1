package com.thj.boot.module.business.convert.facilityname;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facilityname.FacilityNameDO;
import com.thj.boot.module.business.pojo.facilityname.vo.FacilityNameCreateReqVO;
import com.thj.boot.module.business.pojo.facilityname.vo.FacilityNameRespVO;
import com.thj.boot.module.business.pojo.facilityname.vo.FacilityNameUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 设备管理-名称-型号 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilityNameConvert {

    FacilityNameConvert INSTANCE = Mappers.getMapper(FacilityNameConvert.class);

    FacilityNameDO convert(FacilityNameCreateReqVO bean);

    FacilityNameDO convert(FacilityNameUpdateReqVO bean);

    FacilityNameRespVO convert(FacilityNameDO bean);

    List<FacilityNameRespVO> convertList(List<FacilityNameDO> list);

    PageResult<FacilityNameRespVO> convertPage(PageResult<FacilityNameDO> page);


}
