package com.thj.boot.module.business.convert.jkmeans;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkmeans.JkMeansDO;
import com.thj.boot.module.business.pojo.jkmeans.vo.JkMeansCreateReqVO;
import com.thj.boot.module.business.pojo.jkmeans.vo.JkMeansRespVO;
import com.thj.boot.module.business.pojo.jkmeans.vo.JkMeansUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-患者教育资料 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkMeansConvert {

    JkMeansConvert INSTANCE = Mappers.getMapper(JkMeansConvert.class);

    JkMeansDO convert(JkMeansCreateReqVO bean);

    JkMeansDO convert(JkMeansUpdateReqVO bean);

    JkMeansRespVO convert(JkMeansDO bean);

    List<JkMeansRespVO> convertList(List<JkMeansDO> list);

    PageResult<JkMeansRespVO> convertPage(PageResult<JkMeansDO> page);


}
