package com.thj.boot.module.business.pojo.autologousartificial.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AutologousArtificialPageReqVO extends PageParam {


    /**
     * 序号
     */
    @TableId
    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 患者清洗穿刺部位（对/错）
     */
    private String cleansingSite;
    /**
     * 戴口罩（对/错）
     */
    private String wearingMask;
    /**
     * 正确行手卫生（对/错）
     */
    private String handHygiene;
    /**
     * 戴新洁净手套（对/错）
     */
    private String cleanGloves;
    /**
     * 正确消毒8cm（对/错）
     */
    private String correctDisinfection;
    /**
     * 正确消毒第二遍（对/错）
     */
    private String correctSecond;
    /**
     * 消毒后待干（对/错）
     */
    private String waitingAfter;
    /**
     * 无菌穿刺（对/错）
     */
    private String sterilePuncture;
    /**
     * 无菌连接管路（对/错）
     */
    private String sterilePipeline;
    /**
     * 脱手套并正确行手卫生（对/错）
     */
    private String gloveHandHygiene;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTime;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;
}
