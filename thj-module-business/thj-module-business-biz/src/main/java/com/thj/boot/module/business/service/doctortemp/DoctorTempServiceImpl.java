package com.thj.boot.module.business.service.doctortemp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.doctortemp.DoctorTempConvert;
import com.thj.boot.module.business.dal.datado.doctortemp.DoctorTempDO;
import com.thj.boot.module.business.dal.mapper.doctortemp.DoctorTempMapper;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempCreateReqVO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempPageReqVO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempRespVO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 医嘱模版 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorTempServiceImpl implements DoctorTempService {

    @Resource
    private DoctorTempMapper doctorTempMapper;

    @Override
    public Long createDoctorTemp(DoctorTempCreateReqVO createReqVO) {
        if (createReqVO.getPid() == null) {
            createReqVO.setPid(0L);
        }
        //检测模版名称唯一性
        checkTempName(createReqVO.getName(), 0);
        // 插入
        DoctorTempDO doctorTemp = DoctorTempConvert.INSTANCE.convert(createReqVO);
        doctorTempMapper.insert(doctorTemp);
        // 返回
        return doctorTemp.getId();
    }

    private void checkTempName(String name, Integer i) {
        Long count = doctorTempMapper.selectCount(DoctorTempDO::getName, name);
        if (count > 0 && 0 == i) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        } else if (count > 1) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        }
    }

    @Override
    public void updateDoctorTemp(DoctorTempUpdateReqVO updateReqVO) {
        // 更新
        DoctorTempDO updateObj = DoctorTempConvert.INSTANCE.convert(updateReqVO);
        doctorTempMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorTemp(Long id) {
        //查看是否有子级|有子级不可删除
        Long count = doctorTempMapper.selectCount(DoctorTempDO::getPid, id);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.MOTTO_JOIN);
        }
        // 删除
        doctorTempMapper.deleteById(id);
        doctorTempMapper.delete(new LambdaQueryWrapper<DoctorTempDO>().eq(DoctorTempDO::getPid, id));
    }


    @Override
    public List<DoctorTempRespVO> getDoctorTemp(Long id) {
        List<DoctorTempDO> doctorTempDOS = doctorTempMapper.selectList(DoctorTempDO::getPid, id);
        return DoctorTempConvert.INSTANCE.convertList(doctorTempDOS);
    }

    @Override
    public List<DoctorTempDO> getDoctorTempList(Collection<Long> ids) {
        return doctorTempMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DoctorTempDO> getDoctorTempPage(DoctorTempPageReqVO pageReqVO) {
        return doctorTempMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DoctorTempDO> getDoctorTempList(DoctorTempCreateReqVO createReqVO) {
        return doctorTempMapper.selectList(createReqVO);
    }

    @Override
    public Long createChildren(DoctorTempCreateReqVO createReqVO) {
        DoctorTempDO doctorTempDO = DoctorTempConvert.INSTANCE.convert(createReqVO);
        doctorTempMapper.insert(doctorTempDO);
        return doctorTempDO.getId();
    }
}
