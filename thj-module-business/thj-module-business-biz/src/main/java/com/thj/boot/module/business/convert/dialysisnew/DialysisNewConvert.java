package com.thj.boot.module.business.convert.dialysisnew;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dialysisnew.DialysisNewDO;
import com.thj.boot.module.business.pojo.dialysisnew.vo.DialysisNewCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisnew.vo.DialysisNewRespVO;
import com.thj.boot.module.business.pojo.dialysisnew.vo.DialysisNewUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析室手卫生检查表（新） Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisNewConvert {

    DialysisNewConvert INSTANCE = Mappers.getMapper(DialysisNewConvert.class);

    DialysisNewDO convert(DialysisNewCreateReqVO bean);

    DialysisNewDO convert(DialysisNewUpdateReqVO bean);

    DialysisNewRespVO convert(DialysisNewDO bean);

    List<DialysisNewRespVO> convertList(List<DialysisNewDO> list);

    PageResult<DialysisNewRespVO> convertPage(PageResult<DialysisNewDO> page);


}
