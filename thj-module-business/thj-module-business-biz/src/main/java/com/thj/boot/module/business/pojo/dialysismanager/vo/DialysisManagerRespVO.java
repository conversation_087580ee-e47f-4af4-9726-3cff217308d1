package com.thj.boot.module.business.pojo.dialysismanager.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisManagerRespVO extends DialysisManagerBaseVO {
    /**
     * 透析处方确认人
     */
    private String prescriptionUserName;
    /**
     * 透前评估确认人（透前评估）
     */
    private String beforeEvaluateUserName;
    /**
     * 双人核对确认人（双人核对）
     */
    private String doubleCheckUserName;
    /**
     * 检测记录确认人
     */
    private String detectionStateUserName;
    /**
     * 透后评估确认人（透后评估）
     */
    private String afterEvaluateUserName;
    /**
     * 治疗小结确认人（治疗小结）
     */
    private String recoverSummaryUserName;
    /**
     * 机号
     */
    private String facilityName;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 姓名
     */
    private String name;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 住院号
     */
    private Integer hospitalNo;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;
    /**
     * 甲
     */
    private String potassium;
    /**
     * 钙
     */
    private String calcium;
    /**
     * 葡萄糖
     */
    private String glucose;

    private String healModel;

}
