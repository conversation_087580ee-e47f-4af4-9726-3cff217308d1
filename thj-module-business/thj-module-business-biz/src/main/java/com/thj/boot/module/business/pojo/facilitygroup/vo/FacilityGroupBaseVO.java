package com.thj.boot.module.business.pojo.facilitygroup.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

/**
* 分组设置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class FacilityGroupBaseVO extends BaseDO {

    /**
     * 分组设置主键id
     */
    private Long id;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 血压计编号
     */
    private String bloodNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店id
     */
    private Long deptId;

}
