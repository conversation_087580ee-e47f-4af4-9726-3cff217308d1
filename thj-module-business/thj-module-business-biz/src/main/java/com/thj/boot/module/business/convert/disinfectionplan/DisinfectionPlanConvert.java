package com.thj.boot.module.business.convert.disinfectionplan;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanRespVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 消毒计划 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DisinfectionPlanConvert {

    DisinfectionPlanConvert INSTANCE = Mappers.getMapper(DisinfectionPlanConvert.class);

    DisinfectionPlanDO convert(DisinfectionPlanCreateReqVO bean);

    DisinfectionPlanDO convert(DisinfectionPlanUpdateReqVO bean);

    DisinfectionPlanRespVO convert(DisinfectionPlanDO bean);

    List<DisinfectionPlanRespVO> convertList(List<DisinfectionPlanDO> list);

    PageResult<DisinfectionPlanRespVO> convertPage(PageResult<DisinfectionPlanDO> page);


}
