package com.thj.boot.module.business.convert.medicalpage;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.medicalpage.MedicalPageDO;
import com.thj.boot.module.business.pojo.medicalpage.vo.MedicalPageCreateReqVO;
import com.thj.boot.module.business.pojo.medicalpage.vo.MedicalPageRespVO;
import com.thj.boot.module.business.pojo.medicalpage.vo.MedicalPageUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 病历首页 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicalPageConvert {

    MedicalPageConvert INSTANCE = Mappers.getMapper(MedicalPageConvert.class);

    MedicalPageDO convert(MedicalPageCreateReqVO bean);

    MedicalPageDO convert(MedicalPageUpdateReqVO bean);

    MedicalPageRespVO convert(MedicalPageDO bean);

    List<MedicalPageRespVO> convertList(List<MedicalPageDO> list);

    PageResult<MedicalPageRespVO> convertPage(PageResult<MedicalPageDO> page);


}
