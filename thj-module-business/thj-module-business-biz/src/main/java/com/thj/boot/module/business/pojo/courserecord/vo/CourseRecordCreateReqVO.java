package com.thj.boot.module.business.pojo.courserecord.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CourseRecordCreateReqVO extends CourseRecordBaseVO {

    /**
     * 开始时间
     */
    private Date recordStartTime;
    /**
     * 记录结束时间
     */
    private Date recordEndTime;

    // 患者id
    private Long patientId;

    //生成年份
    private String generateYear;

    // 生成季度
    private Integer generateSeason;

}
