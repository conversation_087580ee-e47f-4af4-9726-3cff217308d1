package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 阿加曲班对象
 */
@Data
public class AnticoArgatro {

    /**
     * 首剂量
     */
    @JsonProperty("ANTICO_ARGATRO_INITIAL")
    private String argatroInitial;

    /**
     * 首剂量单位
     */
    @JsonProperty("ANTICO_ARGATRO_INITIAL_UNIT")
    private String argatroInitialUnit;

    /**
     * 追加速率
     */
    @JsonProperty("ANTICO_ARGATRO_INFUSION_RATE")
    private String argatroInfusionRate;

    /**
     * 追加时间（小时）
     */
    @JsonProperty("ANTICO_ARGATRO_INFUSION_HR")
    private String argatroInfusionHr;

    /**
     * 追加时间（分钟）
     */
    @JsonProperty("ANTICO_ARGATRO_INFUSION_MIN")
    private String argatroInfusionMin;

}
