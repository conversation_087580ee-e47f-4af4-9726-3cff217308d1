package com.thj.boot.module.business.convert.inspectsettings;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.inspectsettings.InspectSettingsDO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsCreateReqVO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsRespVO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 检验设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectSettingsConvert {

    InspectSettingsConvert INSTANCE = Mappers.getMapper(InspectSettingsConvert.class);

    InspectSettingsDO convert(InspectSettingsCreateReqVO bean);

    InspectSettingsDO convert(InspectSettingsUpdateReqVO bean);

    InspectSettingsRespVO convert(InspectSettingsDO bean);

    List<InspectSettingsRespVO> convertList(List<InspectSettingsDO> list);

    PageResult<InspectSettingsRespVO> convertPage(PageResult<InspectSettingsDO> page);


}
