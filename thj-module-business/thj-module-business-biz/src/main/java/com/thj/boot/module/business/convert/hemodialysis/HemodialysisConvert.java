package com.thj.boot.module.business.convert.hemodialysis;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.hemodialysismanager.vo.DialysisPreparationRespVO;
import com.thj.boot.module.business.controller.admin.hemodialysismanager.vo.PreparationExcelVO;
import com.thj.boot.module.business.dal.datado.hemodialysis.HemodialysisDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.pojo.hemodialysis.vo.HemodialysisCreateReqVO;
import com.thj.boot.module.business.pojo.hemodialysis.vo.HemodialysisRespVO;
import com.thj.boot.module.business.pojo.hemodialysis.vo.HemodialysisUpdateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析患者评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HemodialysisConvert {

    HemodialysisConvert INSTANCE = Mappers.getMapper(HemodialysisConvert.class);

    HemodialysisDO convert(HemodialysisCreateReqVO bean);

    HemodialysisDO convert(HemodialysisUpdateReqVO bean);

    HemodialysisRespVO convert(HemodialysisDO bean);

    List<HemodialysisRespVO> convertList(List<HemodialysisDO> list);

    PageResult<HemodialysisRespVO> convertPage(PageResult<HemodialysisDO> page);


    HemodialysisManagerRespVO convert2(HemodialysisManagerDO hemodialysisManagerDO);

    PageResult<PreparationExcelVO> convertExcel(PageResult<DialysisPreparationRespVO> dialysisPreparation);
}
