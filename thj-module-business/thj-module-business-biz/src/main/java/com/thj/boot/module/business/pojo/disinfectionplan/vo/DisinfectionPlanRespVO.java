package com.thj.boot.module.business.pojo.disinfectionplan.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DisinfectionPlanRespVO extends DisinfectionPlanBaseVO {

    /**
     * 开始透析时间
     */
    private Date startDialyzeTime;
    /**
     * 结束透析时间
     */
    private Date endDialyzeTime;
    /**
     * 处方脱水量
     */
    private String prescriptionEhydratedLevel;
    /**
     * 实际超滤量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 透析时长小时
     */
    private String duration;
    /**
     * 透析时长分钟
     */
    private String durationMin;
    /**
     * 体重减少
     */
    private String weights;

}
