package com.thj.boot.module.business.pojo.patient.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 患者管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PatientBaseVO extends BaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 患者类型
     */
    private String patientType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 拼音
     */
    private String spellName;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 1-男，2-女
     */
    private String sex;
    /**
     * 身份证类型
     */
    private String idCardType;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 住院号
     */
    private String hospitalNo;
    /**
     * 病区
     */
    private String endemicArea;
    /**
     * 床号
     */
    private String bedNo;
    /**
     * 婚姻状况
     */
    private String marriage;
    /**
     * 报销方式
     */
    private String applyWay;
    /**
     * 医保号
     */
    private String baoNo;
    /**
     * 医保到期日期
     */
    private Date baoEndTime;
    /**
     * 医保到期提醒时间
     */
    private Date baoEndWarnTime;
    /**
     * 身高
     */
    private String stature;
    /**
     * 初始体重
     */
    private String initWeight;
    /**
     * 血型
     */
    private String bloodType;
    /**
     * RH
     */
    private String rh;
    /**
     * 文化程度
     */
    private String culture;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 吸烟
     */
    private String smoking;
    /**
     * 视力障碍
     */
    private String eyesight;
    /**
     * 饮酒
     */
    private String tipple;
    /**
     * 宗教信仰
     */
    private String religion;
    /**
     * 民族
     */
    private String nation;
    /**
     * 电话(本人)
     */
    private String mobile;
    /**
     * 电话(家属)
     */
    private String mobileMan;
    /**
     * 家庭住址
     */
    private String address;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 接收日期
     */
    private Date receiveTime;
    /**
     * 首次透析日期
     */
    private Date firstReceiveTime;
    /**
     * 透析年龄
     */
    private String dialyzeAge;
    /**
     * 诱导期
     */
    private Date induceTime;
    /**
     * 初始透析次数
     */
    private String initDialyzeNo;
    /**
     * 透析总次数
     */
    private String dialyzeTotal;
    /**
     * 传染病
     */
    private String infect;
    /**
     * 角色是医生用户id
     */
    private Long medic;
    /**
     * 角色是护士用户id
     */
    private Long nurse;
    /**
     * 诊断
     */
    private String describes;
    /**
     * 备注
     */
    private String remark;
    /**
     * 过敏史
     */
    private String allergy;
    /**
     * 肿瘤史
     */
    private String tumor;
    /**
     * 红细胞压积
     */
    private String erythrocyte;
    /**
     * 认证角色
     */
    private String authRole;
    /**
     * 认证姓名
     */
    private String authName;
    /**
     * 认证身份证号
     */
    private String authIdCard;
    /**
     * 认证任联系方式
     */
    private String authMobile;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 标签管理
     */
    private String labels;
    /**
     * rfid
     */
    private String rfid;
    /**
     * 邮编
     */
    private String postcode;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * openid
     */
    private String openId;
    /**
     * 0-未绑定，1-已绑定
     */
    private String state;
    /**
     * 患者类型 0-新患者，1-维持患者
     */
    private Integer patientState;
    /**
     * 调整后机号
     */
    private String adjustNumber;
    /**
     * 血滤器
     */
    private String hemodialysis;
    /**
     * 血透器
     */
    private String hemofiltration;
    /**
     * 灌流器
     */
    private String perfusion;
    /**
     * 入科方式
     */
    private String enterWay;
    /**
     * 未执行医嘱0-未执行，1-已执行
     */
    private Integer carryState;
    /**
     * 患者签名
     */
    private String signature;
    /**
     * 00-转入，01-转出
     */
    private String patientTypeSource;

    /**
     * cnrds同步标识 Y/N
     */
    private String cnrdsSyncFlag;


}
