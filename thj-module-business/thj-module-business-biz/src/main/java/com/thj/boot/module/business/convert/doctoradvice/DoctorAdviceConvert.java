package com.thj.boot.module.business.convert.doctoradvice;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.doctoradvice.DoctorAdviceDO;
import com.thj.boot.module.business.pojo.doctoradvice.vo.DoctorAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.doctoradvice.vo.DoctorAdviceRespVO;
import com.thj.boot.module.business.pojo.doctoradvice.vo.DoctorAdviceUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医嘱 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorAdviceConvert {

    DoctorAdviceConvert INSTANCE = Mappers.getMapper(DoctorAdviceConvert.class);

    DoctorAdviceDO convert(DoctorAdviceCreateReqVO bean);

    DoctorAdviceDO convert(DoctorAdviceUpdateReqVO bean);

    DoctorAdviceRespVO convert(DoctorAdviceDO bean);

    List<DoctorAdviceRespVO> convertList(List<DoctorAdviceDO> list);

    PageResult<DoctorAdviceRespVO> convertPage(PageResult<DoctorAdviceDO> page);


}
