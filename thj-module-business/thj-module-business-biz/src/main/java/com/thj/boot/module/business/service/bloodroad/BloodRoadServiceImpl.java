package com.thj.boot.module.business.service.bloodroad;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.dataobject.BaseDO;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.convert.bloodroad.BloodRoadConvert;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadCreateReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadPageReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadRespVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadUpdateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 血管通路 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BloodRoadServiceImpl implements BloodRoadService {

    @Resource
    private BloodRoadMapper bloodRoadMapper;

    @Resource
    private VascularAccessMapper vascularAccessMapper;

    @Override
    public Long createBloodRoad(BloodRoadCreateReqVO createReqVO) {
        // 插入
        BloodRoadDO bloodRoad = BloodRoadConvert.INSTANCE.convert(createReqVO);
        bloodRoadMapper.insert(bloodRoad);
        // 返回
        return bloodRoad.getId();
    }

    @Override
    public void updateBloodRoad(BloodRoadUpdateReqVO updateReqVO) {
        // 更新
        BloodRoadDO updateObj = BloodRoadConvert.INSTANCE.convert(updateReqVO);
        if (CollUtil.isNotEmpty(updateReqVO.getInfraResultList()))
            updateObj.setInfraIds(StringUtils.join(updateReqVO.getInfraResultList().stream().map(InfraResult::getId).collect(Collectors.toList()), StrUtil.COMMA));
        bloodRoadMapper.updateById(updateObj);
    }

    @Override
    public void deleteBloodRoad(Long id) {
        // 删除
        bloodRoadMapper.deleteById(id);
        //删除关联超声检查
    }


    @Override
    public BloodRoadRespVO getBloodRoad(Long id) {
        BloodRoadDO bloodRoadDO = bloodRoadMapper.selectById(id);
        BloodRoadRespVO bloodRoadRespVO = BloodRoadConvert.INSTANCE.convert(bloodRoadDO);
        BloodRoadDO bloodRoadDO1 = bloodRoadMapper.selectOne(BloodRoadDO::getPatientId, bloodRoadRespVO.getPatientId(), BloodRoadDO::getRecipe, "0");
        if (bloodRoadDO1 != null && bloodRoadDO1.getId() != id) {
            bloodRoadRespVO.setRecipeState(true);
        } else {
            bloodRoadRespVO.setRecipeState(false);
        }
        return bloodRoadRespVO;
    }

    @Override
    public List<BloodRoadDO> getBloodRoadList(Collection<Long> ids) {
        return bloodRoadMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BloodRoadDO> getBloodRoadPage(BloodRoadPageReqVO pageReqVO) {
        return bloodRoadMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BloodRoadRespVO> getBloodRoadList(BloodRoadCreateReqVO createReqVO) {
        List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(createReqVO);
        List<BloodRoadRespVO> bloodRoadRespVOS = BloodRoadConvert.INSTANCE.convertList(bloodRoadDOS);
        if (CollectionUtil.isNotEmpty(bloodRoadRespVOS)) {
            bloodRoadRespVOS = bloodRoadRespVOS.stream().peek(bloodRoadRespVO -> {
                List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(new LambdaQueryWrapperX<VascularAccessDO>()
                        .select(VascularAccessDO::getId, VascularAccessDO::getName)
                        .in(VascularAccessDO::getId, bloodRoadRespVO.getPart(), bloodRoadRespVO.getType()).orderByDesc(VascularAccessDO::getId));
                if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                    String collect = vascularAccessDOS.stream().map(VascularAccessDO::getName).collect(Collectors.joining());
                    bloodRoadRespVO.setPartType(collect);
                }
            }).collect(Collectors.toList());
        }
        return bloodRoadRespVOS;
    }

}
