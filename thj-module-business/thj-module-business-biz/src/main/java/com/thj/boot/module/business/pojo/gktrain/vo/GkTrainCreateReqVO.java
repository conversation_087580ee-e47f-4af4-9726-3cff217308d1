package com.thj.boot.module.business.pojo.gktrain.vo;

import com.thj.boot.module.business.dal.datado.gkinroles.GkInRolesDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GkTrainCreateReqVO extends GkTrainBaseVO {

    private Long id;
    /**
     *
     */
    private String title;
    /**
     * 参与人员
     */
    private String attendees;
    /**
     * 计划培训时间
     */
    private Date planTime;
    /**
     * 培训地点
     */
    private String planLocation;
    /**
     * 科室
     */
    private String location;
    /**
     * 制定人
     */
    private String creatorName;
    /**
     * 制定人ID
     */
    private Long creatorId;
    /**
     * 培训方式
     */
    private String trainingMode;
    /**
     * 主讲人id
     */
    private Long speakerId;
    /**
     * 主讲人姓名
     */
    private String speakerName;
    /**
     * 内容
     */
    private String content;
    /**
     * 文件地址
     */
    private String pathUrl;

    /**
     * 现场培训图片
     */
    private String imgs;

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String mouth;

    /**
     * 实际培训时间
     */
    private Date realityTime;

    /**
     * 实际参加人数
     */
    private int peopleNo;
    private List<GkInRolesDO> list;


}
