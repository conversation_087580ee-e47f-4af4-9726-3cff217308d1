package com.thj.boot.module.business.convert.hemodialysismanager;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.reachweigh.ReachWeighDO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HemodialysisManagerConvert {

    HemodialysisManagerConvert INSTANCE = Mappers.getMapper(HemodialysisManagerConvert.class);

    HemodialysisManagerDO convert(HemodialysisManagerCreateReqVO bean);

    HemodialysisManagerDO convert(HemodialysisManagerUpdateReqVO bean);

    HemodialysisManagerRespVO convert(HemodialysisManagerDO bean);

    List<HemodialysisManagerRespVO> convertList(List<HemodialysisManagerDO> list);

    PageResult<HemodialysisManagerRespVO> convertPage(PageResult<HemodialysisManagerDO> page);


    HemodialysisManagerRespVO convertReachWeight(ReachWeighDO reachWeighDO);
}
