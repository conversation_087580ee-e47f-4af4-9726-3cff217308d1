package com.thj.boot.module.business.convert.wechatinteraction;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.wechatinteraction.WechatInteractionDO;
import com.thj.boot.module.business.pojo.wechatinteraction.vo.WechatInteractionRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 微信互动 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WechatInteractionConvert {

    WechatInteractionConvert INSTANCE = Mappers.getMapper(WechatInteractionConvert.class);

    PageResult<WechatInteractionRespVO> convertPage(PageResult<WechatInteractionDO> wechatInteractionDOPageResult);
}
