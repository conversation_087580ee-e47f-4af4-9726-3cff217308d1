package com.thj.boot.module.business.convert.roomairmonitoring;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.roomairmonitoring.RoomAirMonitoringDO;
import com.thj.boot.module.business.pojo.roomairmonitoring.vo.RoomAirMonitoringCreateReqVO;
import com.thj.boot.module.business.pojo.roomairmonitoring.vo.RoomAirMonitoringRespVO;
import com.thj.boot.module.business.pojo.roomairmonitoring.vo.RoomAirMonitoringUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血透室空气物表监测登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RoomAirMonitoringConvert {

    RoomAirMonitoringConvert INSTANCE = Mappers.getMapper(RoomAirMonitoringConvert.class);

    RoomAirMonitoringDO convert(RoomAirMonitoringCreateReqVO bean);

    RoomAirMonitoringDO convert(RoomAirMonitoringUpdateReqVO bean);

    RoomAirMonitoringRespVO convert(RoomAirMonitoringDO bean);

    List<RoomAirMonitoringRespVO> convertList(List<RoomAirMonitoringDO> list);

    PageResult<RoomAirMonitoringRespVO> convertPage(PageResult<RoomAirMonitoringDO> page);


}
