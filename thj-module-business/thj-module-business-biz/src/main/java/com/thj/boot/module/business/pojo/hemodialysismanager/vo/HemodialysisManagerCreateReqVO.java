package com.thj.boot.module.business.pojo.hemodialysismanager.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.module.business.pojo.contradictAdvice.vo.ContradictAdviceCreateReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Valid
public class HemodialysisManagerCreateReqVO extends HemodialysisManagerBaseVO {

    /**
     * 患者透析方案主键id
     */
    private Long patientDialyzeId;
    /**
     * 脱水百分比
     */
    private String dehydrationPercent;
    /**
     * 引血
     */
    private String inducingBlood;
    /**
     * 透前评估=1，透析处方=2,检测记录=3
     */
    private Integer hemodialysisType;
    /**
     * 开始透析检测时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束透析检测时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 抗凝剂医嘱
     */
    private List<ContradictAdviceCreateReqVO> contradictAdviceDOS;

    /**
     * 监控记录时间
     */
    private Date dateWeek;

    /**
     * 检测记录时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Date detectionMin;

    /**
     * 增加时间
     */
    private Integer command;
    /**
     * 0-未签到,1-已签到，2-透析中，3-透析后，4-自查，5-归档
     */
    private Integer state;
    /**
     * 多个患者id
     */
    private List<Long> patientIdArr;

    /**
     * 开嘱医生
     */
    private Long  doctorId;

    private Date classesTime;

    /**
     * 透析时间  开始
     */
    private Date hemodialysisTimeStart;

    /**
     * 透析时间  结束
     */
    private Date hemodialysisTimeEnd;

}
