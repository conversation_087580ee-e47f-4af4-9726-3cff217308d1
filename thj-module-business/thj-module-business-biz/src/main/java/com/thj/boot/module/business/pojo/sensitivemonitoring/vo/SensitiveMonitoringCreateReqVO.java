package com.thj.boot.module.business.pojo.sensitivemonitoring.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SensitiveMonitoringCreateReqVO extends SensitiveMonitoringBaseVO {

    private Long id;
    /**
     * 日期
     */
    private LocalDateTime date;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 责任人
     */
    private String responsiblePerson;
    /**
     * 体外循环凝血（是/否）
     */
    private String extracorporealCirculationCoagulation;
    /**
     * 抗凝剂
     */
    private String anticoagulant;
    /**
     * 体外循环漏血（是/否）
     */
    private String extracorporealCirculationBleeding;
    /**
     * 漏血原因 （穿刺针脱离血管/透析器与血管路或血管路与穿刺针或中心静脉导管连接不严密/血液管路侧枝夹子未夹闭或者保护帽脱落）
     */
    private String bleedingReason;
    /**
     * 内瘘血肿 （是/否）
     */
    private String fistulaHematoma;
    /**
     * 穿刺针、时机、内瘘血栓形成 （多选，穿刺时/透析中/拔针后）
     */
    private String punctureNeedleFistulaThrombosisTiming;
    /**
     * 当日新办入院 （是/否）
     */
    private String newHospitalAdmissionToday;
    /**
     * 诊断
     */
    private String diagnosis;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 责任人id
     */
    private Long responsiblePersonId;

}
