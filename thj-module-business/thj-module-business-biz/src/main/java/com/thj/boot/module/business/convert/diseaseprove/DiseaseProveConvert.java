package com.thj.boot.module.business.convert.diseaseprove;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.diseaseprove.DiseaseProveDO;
import com.thj.boot.module.business.pojo.diseaseprove.vo.DiseaseProveCreateReqVO;
import com.thj.boot.module.business.pojo.diseaseprove.vo.DiseaseProveRespVO;
import com.thj.boot.module.business.pojo.diseaseprove.vo.DiseaseProveUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 诊断证明书 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DiseaseProveConvert {

    DiseaseProveConvert INSTANCE = Mappers.getMapper(DiseaseProveConvert.class);

    DiseaseProveDO convert(DiseaseProveCreateReqVO bean);

    DiseaseProveDO convert(DiseaseProveUpdateReqVO bean);

    DiseaseProveRespVO convert(DiseaseProveDO bean);

    List<DiseaseProveRespVO> convertList(List<DiseaseProveDO> list);

    PageResult<DiseaseProveRespVO> convertPage(PageResult<DiseaseProveDO> page);


}
