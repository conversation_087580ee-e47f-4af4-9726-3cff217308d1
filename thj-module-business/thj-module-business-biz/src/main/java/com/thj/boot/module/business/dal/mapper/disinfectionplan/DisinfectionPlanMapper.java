package com.thj.boot.module.business.dal.mapper.disinfectionplan;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanPageReqVO;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 消毒计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DisinfectionPlanMapper extends BaseMapperX<DisinfectionPlanDO> {

    default PageResult<DisinfectionPlanDO> selectPage(DisinfectionPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DisinfectionPlanDO>()
                .eqIfPresent(DisinfectionPlanDO::getDisinfectionDuration, reqVO.getDisinfectionDuration())
                .eqIfPresent(DisinfectionPlanDO::getWeekDay, reqVO.getWeekDay())
                .eqIfPresent(DisinfectionPlanDO::getMachineWay, reqVO.getMachineWay())
                .eqIfPresent(DisinfectionPlanDO::getMachineLiquid, reqVO.getMachineLiquid())
                .eqIfPresent(DisinfectionPlanDO::getLiquidCircuit, reqVO.getLiquidCircuit())
                .eqIfPresent(DisinfectionPlanDO::getLiquid, reqVO.getLiquid())
                .eqIfPresent(DisinfectionPlanDO::getBedWay, reqVO.getBedWay())
                .eqIfPresent(DisinfectionPlanDO::getBedWipe, reqVO.getBedWipe())
                .eqIfPresent(DisinfectionPlanDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DisinfectionPlanDO::getManagerId,reqVO.getManagerId())
                .orderByDesc(DisinfectionPlanDO::getId));
    }

    default List<DisinfectionPlanDO> selectList(DisinfectionPlanCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DisinfectionPlanDO>()
                .eqIfPresent(DisinfectionPlanDO::getDisinfectionDuration, reqVO.getDisinfectionDuration())
                .eqIfPresent(DisinfectionPlanDO::getWeekDay, reqVO.getWeekDay())
                .eqIfPresent(DisinfectionPlanDO::getMachineWay, reqVO.getMachineWay())
                .eqIfPresent(DisinfectionPlanDO::getMachineLiquid, reqVO.getMachineLiquid())
                .eqIfPresent(DisinfectionPlanDO::getLiquidCircuit, reqVO.getLiquidCircuit())
                .eqIfPresent(DisinfectionPlanDO::getLiquid, reqVO.getLiquid())
                .eqIfPresent(DisinfectionPlanDO::getBedWay, reqVO.getBedWay())
                .eqIfPresent(DisinfectionPlanDO::getBedWipe, reqVO.getBedWipe())
                .eqIfPresent(DisinfectionPlanDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DisinfectionPlanDO::getManagerId,reqVO.getManagerId())
                .orderByDesc(DisinfectionPlanDO::getId));
    }

}
