package com.thj.boot.module.business.pojo.ductfall.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
 * 导管脱落风险评估 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DuctFallBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 住院患者导管脱落危险因素评估记录表
     */
    private String mentality;
    /**
     * 备注
     */
    private String remark;
    /**
     * json
     */
    private String evaluateContent;

}
