package com.thj.boot.module.business.convert.bloodroad;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadCreateReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadRespVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血管通路 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BloodRoadConvert {

    BloodRoadConvert INSTANCE = Mappers.getMapper(BloodRoadConvert.class);

    BloodRoadDO convert(BloodRoadCreateReqVO bean);

    BloodRoadDO convert(BloodRoadUpdateReqVO bean);

    BloodRoadRespVO convert(BloodRoadDO bean);

    List<BloodRoadRespVO> convertList(List<BloodRoadDO> list);

    PageResult<BloodRoadRespVO> convertPage(PageResult<BloodRoadDO> page);


}
