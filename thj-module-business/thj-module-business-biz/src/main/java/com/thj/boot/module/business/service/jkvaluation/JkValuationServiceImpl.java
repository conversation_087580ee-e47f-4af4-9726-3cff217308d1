package com.thj.boot.module.business.service.jkvaluation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo;
import com.thj.boot.module.business.controller.admin.gk.vo.PatientInfoVo;
import com.thj.boot.module.business.convert.jkvaluation.JkValuationConvert;
import com.thj.boot.module.business.dal.datado.jkvaluation.JkValuationDO;
import com.thj.boot.module.business.dal.mapper.jkvaluation.JkValuationMapper;
import com.thj.boot.module.business.pojo.jkvaluation.vo.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 健康教育-教育测评 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JkValuationServiceImpl implements JkValuationService {

    @Resource
    private JkValuationMapper jkValuationMapper;

    @Override
    public Long createJkValuation(JkValuationCreateReqVO createReqVO) {
        // 插入
        JkValuationDO jkValuation = JkValuationConvert.INSTANCE.convert(createReqVO);
        jkValuationMapper.insert(jkValuation);
        // 返回
        return jkValuation.getId();
    }

    @Override
    public void updateJkValuation(JkValuationUpdateReqVO updateReqVO) {
        // 更新
        JkValuationDO updateObj = JkValuationConvert.INSTANCE.convert(updateReqVO);
        jkValuationMapper.saveOrUpdate(updateObj);
    }

    @Override
    public void deleteJkValuation(Long id) {
        // 删除
        jkValuationMapper.deleteById(id);
    }

    @Override
    public JkValuationDO getJkValuation(Long id) {
        return jkValuationMapper.selectById(id);
    }

    @Override
    public List<JkValuationDO> getJkValuationList(Collection<Long> ids) {
        return jkValuationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<JkValuationDO> getJkValuationPage(JkValuationPageReqVO pageReqVO) {
        return jkValuationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<JkValuationDO> getJkValuationList(JkValuationExportReqVO exportReqVO) {
        return jkValuationMapper.selectList(exportReqVO);
    }

    @Override
    public List<JkValuationVO> getList(JkValuationVO vo){
        List<JkValuationVO> jkValuationVOS = jkValuationMapper.getList(vo);
        return jkValuationVOS;
    }

    @Override
    public List<CollectiveVo> getPatientEdu(CollectiveVo vo){
       return jkValuationMapper.getPatientEdu(vo);
    }

    @Override
    public List<JkValuationDO> getPatientEdu(String patientId){
        return jkValuationMapper.selectList(JkValuationDO::getPatientId,patientId);
    }
    @Override
    public List<JkValuationDO> getPatientValuationList(PatientInfoVo vo){
        return jkValuationMapper.selectList(JkValuationDO::getPatientId,vo.getPatientId());
    }

    @Override
    public JkValuationDO getInfo(String patientId){
        return jkValuationMapper.selectOne(JkValuationDO::getPatientId,patientId);
    }

    @Override
    public void save(JkValuationDO vo){
        jkValuationMapper.saveOrUpdate(vo);
    }

    @Override
    public void update(JkValuationDO vo){
        jkValuationMapper.updateById(vo);
    }

    @Override
    public Map<String,Object> getKnowledge(){
        return jkValuationMapper.getKnowledge();
    }

}
