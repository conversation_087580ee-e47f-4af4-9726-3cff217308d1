package com.thj.boot.module.business.service.sitemarkers;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.sitemarkers.SiteMarkersDO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersCreateReqVO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersPageReqVO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 位点标记 Service 接口
 *
 * <AUTHOR>
 */
public interface SiteMarkersService {

    /**
     * 创建位点标记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSiteMarkers( SiteMarkersCreateReqVO createReqVO);

    /**
     * 更新位点标记
     *
     * @param updateReqVO 更新信息
     */
    void updateSiteMarkers( SiteMarkersUpdateReqVO updateReqVO);

    /**
     * 删除位点标记
     *
     * @param id 编号
     */
    void deleteSiteMarkers(Long id);

    /**
     * 获得位点标记
     *
     * @param id 编号
     * @return 位点标记
     */
    SiteMarkersDO getSiteMarkers(Long patientId);

    /**
     * 获得位点标记列表
     *
     * @param ids 编号
     * @return 位点标记列表
     */
    List<SiteMarkersDO> getSiteMarkersList(Collection<Long> ids);

    /**
     * 获得位点标记分页
     *
     * @param pageReqVO 分页查询
     * @return 位点标记分页
     */
    PageResult<SiteMarkersDO> getSiteMarkersPage(SiteMarkersPageReqVO pageReqVO);

    /**
     * 获得位点标记列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 位点标记列表
     */
    List<SiteMarkersDO> getSiteMarkersList(SiteMarkersCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2023/11/20 0:26
     * @Description 新增或者删除
     **/
    void createOrUpdateSiteMarkers(SiteMarkersUpdateReqVO updateReqVO);

    void updateSiteImage(SiteMarkersUpdateReqVO updateReqVO);

}
