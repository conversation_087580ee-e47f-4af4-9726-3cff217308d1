package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.utils.AddressUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 基本信息表单
 */
@Data
public class BasicInfoForm {

    /**
     * 姓名
     */
    @JsonProperty("INFO_NAME")
    @NotNull
    private String infoName;

    /**
     * 身份证号
     */
    @JsonProperty("INFO_ID")
    @NotNull
    private String infoId;

    /**
     *  首次肾脏替代治疗日期Date(YYYY-MM-DD)
     */
    @JsonProperty("INFO_FIRST_RRT_DATE")
    @NotNull
    private String firstRrtDate;

    /**
     * 首次肾脏替代治疗时肾功能(ml/min/1.73m^2) 0-100
     */
    @JsonProperty("INFO_FIRST_RRT_GFR")
    private String firstRrtGfr;

    /**
     * 首次肾脏替代治疗时血肌酐水平(umol/L) 10-5000
     */
    @JsonProperty("INFO_FIRST_RRT_SCR")
    private String firstRrtScr;

    /**
     * 首次透析时采用的通路类型
     */
    @JsonProperty("INFO_FIRST_RRT_ACC")
    @NotNull
    private String firstErtAcc;

    /**
     * 首次透析时采取的方式
     */
    @JsonProperty("INFO_FIRST_RRT_START")
    @NotNull
    private String firstRrtStart;

    /**
     *   身高(cm)
     */
    @JsonProperty("INFO_HEIGHT")
    private String height;

    /**
     * 性别
     */
    @JsonProperty("INFO_SEX")
    @NotNull
    private String sex;

    /**
     * 民族
     */
    @JsonProperty("INFO_RACE")
    private String race;

    /**
     * 婚姻状况
     */
    @JsonProperty("INFO_MARRIAGE")
    private String marriage;

    /**
     * 出生日期
     */
    @JsonProperty("INFO_BIRTHDAY")
    @NotNull
    private String birthday;

    /**
     * 教育程度
     */
    @JsonProperty("INFO_EDU")
    private String edu;

    /**
     * 职业
     */
    @JsonProperty("INFO_CAREER")
    private String career;

    /**
     * 费别
     */
    @JsonProperty("INFO_FEE")
    private List<String> fee;

    /**
     * 联系电话（手机）
     */
    @JsonProperty("INFO_PHONE")
    private String phone;

    /**
     * 通信地址
     */
    @JsonProperty("INFO_RESID")
    private BasicInfoPostalAddress resid;


    public void setResidByStr(String address) {
        if(StringUtils.isNotEmpty(address)){
            String[] values = AddressUtils.parseProvinceCityDistrict(address);
            if(StringUtils.isNotEmpty(values[0]) && StringUtils.isNotEmpty(values[1])
                && StringUtils.isNotEmpty(values[2]) ) {
                BasicInfoPostalAddress postalAddress = new BasicInfoPostalAddress();
                postalAddress.setProvince(values[0]);
                postalAddress.setCity(values[1]);
                postalAddress.setCounty(values[2]);
            }
        }

    }
}
