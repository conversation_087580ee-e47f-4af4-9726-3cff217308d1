package com.thj.boot.module.business.pojo.qualityinspection.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
 * 质量检测 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class QualityInspectionBaseVO extends BaseDO {

    /**
     * 质量检测主键id
     */
    private Long id;
    /**
     * 取样日期
     */
    private Date samplingTime;
    /**
     * 取样标本
     */
    private String specimen;
    /**
     * A浓缩液批号
     */
    private String aconcentration;
    /**
     * B浓缩液批号
     */
    private String bconcentration;
    /**
     * 采样部位
     */
    private String samplingSite;
    /**
     * 检测单位
     */
    private String detectionUnit;
    /**
     * 取样者
     */
    private Long userId;
    /**
     * 取样名称
     */
    private String userName;
    /**
     * 报告日期
     */
    private Date reportTime;
    /**
     * 检测结果
     */
    private String detectionResult;
    /**
     * 配方
     */
    private String formula;
    /**
     * na
     */
    private String na;
    /**
     * 电导度
     */
    private String conductance;
    /**
     * k
     */
    private String k;
    /**
     * ca
     */
    private String ca;
    /**
     * ci
     */
    private String ci;
    /**
     * hco
     */
    private String hco;
    /**
     * mg
     */
    private String mg;
    /**
     * ph
     */
    private String ph;
    /**
     * cocp
     */
    private String cocp;
    /**
     * 备注
     */
    private String remark;
    /**
     * 设备管理id
     */
    private Long managerId;
    /**
     * 1-细菌检测,2-内毒素检测,3-透析液离子浓度检测
     */
    private String type;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 1-透析机，2-ccds,3-cdds
     */
    private String symbol;

}
