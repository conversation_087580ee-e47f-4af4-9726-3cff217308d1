package com.thj.boot.module.business.service.drug;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.pojo.drug.vo.DrugCreateReqVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugPageReqVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 药品 Service 接口
 *
 * <AUTHOR>
 */
public interface DrugService {

    /**
     * 创建药品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDrug(DrugCreateReqVO createReqVO);

    /**
     * 更新药品
     *
     * @param updateReqVO 更新信息
     */
    void updateDrug(DrugUpdateReqVO updateReqVO);

    /**
     * 删除药品
     *
     * @param id 编号
     */
    void deleteDrug(Long id);

    /**
     * 获得药品
     *
     * @param id 编号
     * @return 药品
     */
    DrugDO getDrug(Long id);

    /**
     * 获得药品列表
     *
     * @param ids 编号
     * @return 药品列表
     */
    List<DrugDO> getDrugList(Collection<Long> ids);

    /**
     * 获得药品分页
     *
     * @param pageReqVO 分页查询
     * @return 药品分页
     */
    PageResult<DrugDO> getDrugPage(DrugPageReqVO pageReqVO);

    /**
     * 获得药品列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 药品列表
     */
    List<DrugDO> getDrugList(DrugCreateReqVO createReqVO);

}
