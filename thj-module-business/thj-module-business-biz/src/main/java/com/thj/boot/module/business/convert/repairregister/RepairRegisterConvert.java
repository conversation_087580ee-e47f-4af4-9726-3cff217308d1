package com.thj.boot.module.business.convert.repairregister;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.repairregister.RepairRegisterDO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 维修登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RepairRegisterConvert {

    RepairRegisterConvert INSTANCE = Mappers.getMapper(RepairRegisterConvert.class);

    RepairRegisterDO convert(RepairRegisterCreateReqVO bean);

    RepairRegisterDO convert(RepairRegisterUpdateReqVO bean);

    RepairRegisterRespVO convert(RepairRegisterDO bean);

    List<RepairRegisterRespVO> convertList(List<RepairRegisterDO> list);

    PageResult<RepairRegisterRespVO> convertPage(PageResult<RepairRegisterDO> page);


}
