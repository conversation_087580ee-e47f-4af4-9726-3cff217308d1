package com.thj.boot.module.business.convert.glasgow;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.glasgow.GlasgowDO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowCreateReqVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowRespVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Glasgow昏迷评分量 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GlasgowConvert {

    GlasgowConvert INSTANCE = Mappers.getMapper(GlasgowConvert.class);

    GlasgowDO convert(GlasgowCreateReqVO bean);

    GlasgowDO convert(GlasgowUpdateReqVO bean);

    GlasgowRespVO convert(GlasgowDO bean);

    List<GlasgowRespVO> convertList(List<GlasgowDO> list);

    PageResult<GlasgowRespVO> convertPage(PageResult<GlasgowDO> page);


}
