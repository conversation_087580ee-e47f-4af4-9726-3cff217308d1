package com.thj.boot.module.business.service.glasgow;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.glasgow.GlasgowDO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowCreateReqVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowPageReqVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * Glasgow昏迷评分量 Service 接口
 *
 * <AUTHOR>
 */
public interface GlasgowService {

    /**
     * 创建Glasgow昏迷评分量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGlasgow( GlasgowCreateReqVO createReqVO);

    /**
     * 更新Glasgow昏迷评分量
     *
     * @param updateReqVO 更新信息
     */
    void updateGlasgow( GlasgowUpdateReqVO updateReqVO);

    /**
     * 删除Glasgow昏迷评分量
     *
     * @param id 编号
     */
    void deleteGlasgow(Long id);

    /**
     * 获得Glasgow昏迷评分量
     *
     * @param id 编号
     * @return Glasgow昏迷评分量
     */
    GlasgowDO getGlasgow(Long id);

    /**
     * 获得Glasgow昏迷评分量列表
     *
     * @param ids 编号
     * @return Glasgow昏迷评分量列表
     */
    List<GlasgowDO> getGlasgowList(Collection<Long> ids);

    /**
     * 获得Glasgow昏迷评分量分页
     *
     * @param pageReqVO 分页查询
     * @return Glasgow昏迷评分量分页
     */
    PageResult<GlasgowDO> getGlasgowPage(GlasgowPageReqVO pageReqVO);

    /**
     * 获得Glasgow昏迷评分量列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return Glasgow昏迷评分量列表
     */
    List<GlasgowDO> getGlasgowList(GlasgowCreateReqVO createReqVO);

}
