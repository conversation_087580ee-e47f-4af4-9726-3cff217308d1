package com.thj.boot.module.business.convert.dryweight;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightCreateReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightRespVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 干体重 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DryWeightConvert {

    DryWeightConvert INSTANCE = Mappers.getMapper(DryWeightConvert.class);

    DryWeightDO convert(DryWeightCreateReqVO bean);

    DryWeightDO convert(DryWeightUpdateReqVO bean);

    DryWeightRespVO convert(DryWeightDO bean);

    List<DryWeightRespVO> convertList(List<DryWeightDO> list);

    PageResult<DryWeightRespVO> convertPage(PageResult<DryWeightDO> page);


}
