package com.thj.boot.module.business.service.teampatient;

import com.thj.boot.module.business.controller.admin.gk.vo.DialysisReadyVO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.pojo.teampatient.TeamPatientCreateReqVO;
import com.thj.boot.module.business.pojo.teampatient.TeamPatientRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/9 16:57
 * @description
 */
public interface TeamPatientService {

    /***
     * <AUTHOR>
     * @date  2024/1/9 17:01
     * @Description 排班详情
     **/
    TeamPatientRespVO getTeamPatient(TeamPatientCreateReqVO createReqVO);

    List<FacilityManagerDO> queryByList(DialysisReadyVO vo);
}
