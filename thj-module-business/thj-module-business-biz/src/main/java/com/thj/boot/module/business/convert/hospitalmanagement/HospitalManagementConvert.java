package com.thj.boot.module.business.convert.hospitalmanagement;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkmanage.GkManageDO;
import com.thj.boot.module.business.pojo.hospitalmanagement.vo.GkManageCreateReqVO;
import com.thj.boot.module.business.pojo.hospitalmanagement.vo.GkManageRespVO;
import com.thj.boot.module.business.pojo.hospitalmanagement.vo.GkManageUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医院感染管理质控 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HospitalManagementConvert {

    HospitalManagementConvert INSTANCE = Mappers.getMapper(HospitalManagementConvert.class);

    GkManageDO convert(GkManageCreateReqVO bean);

    GkManageDO convert(GkManageUpdateReqVO bean);

    GkManageRespVO convert(GkManageDO bean);

    List<GkManageRespVO> convertList(List<GkManageDO> list);

    PageResult<GkManageRespVO> convertPage(PageResult<GkManageDO> page);


}
