package com.thj.boot.module.business.convert.courserecord;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordRespVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 病程记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CourseRecordConvert {

    CourseRecordConvert INSTANCE = Mappers.getMapper(CourseRecordConvert.class);

    CourseRecordDO convert(CourseRecordCreateReqVO bean);

    CourseRecordDO convert(CourseRecordUpdateReqVO bean);

    CourseRecordRespVO convert(CourseRecordDO bean);

    List<CourseRecordRespVO> convertList(List<CourseRecordDO> list);

    PageResult<CourseRecordRespVO> convertPage(PageResult<CourseRecordDO> page);


}
