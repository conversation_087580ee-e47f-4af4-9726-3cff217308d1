package com.thj.boot.module.business.pojo.departmentControl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialysisTotalTimeVo {
    private Long patientId;

    private Date firstReceiveTime;

    private Long betweenDay;

    public DialysisTotalTimeVo(Long patientId, Date firstReceiveTime) {
        this.patientId = patientId;
        this.firstReceiveTime = firstReceiveTime;
    }
}
