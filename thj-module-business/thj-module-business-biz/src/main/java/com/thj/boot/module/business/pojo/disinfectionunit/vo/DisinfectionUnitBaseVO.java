package com.thj.boot.module.business.pojo.disinfectionunit.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;


/**
* 透析间常规消毒 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DisinfectionUnitBaseVO extends BaseDO {

    /**
     * 序号
     */
    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 透析单元清空（对/错）
     */
    private String unitEmptyingDialysis;
    /**
     * 正确行手卫生
     */
    private String handHygiene;
    /**
     * 消毒前清空透析大厅（对/错）
     */
    private String beforeDisinfection;
    /**
     * 戴新洁净手套（对/错）
     */
    private String wearingGloves;
    /**
     * 消毒所有物表（对/错）
     */
    private String disinfectingAllSurfaces;
    /**
     * 保证所有物表被消毒（对/错）
     */
    private String ensuringAllDisinfected;
    /**
     * 保证有足够待干时间（对/错）
     */
    private String dryingTime;
    /**
     * 操作时戴手套（对/错）
     */
    private String wearingGlovesOperation;
    /**
     * 消毒期间勿进外人（对/错）
     */
    private String duringDisinfection;
    /**
     * 消毒巾一物一用（对/错）
     */
    private String singleUseWipes;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTime;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;

}
