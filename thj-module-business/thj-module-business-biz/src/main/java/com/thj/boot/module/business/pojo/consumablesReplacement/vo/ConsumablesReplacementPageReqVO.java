package com.thj.boot.module.business.pojo.consumablesReplacement.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConsumablesReplacementPageReqVO extends PageParam {

    /**
     * 设备管理id
     */
    private Long id;

    /**
     * 序列号
     */
    private String code;

    /**
     * 设备名称id
     */
    private Long facilityNameId;

    /**
     * 设备型号id（搜索）
     */
    private String facilityTypeIds;

}
