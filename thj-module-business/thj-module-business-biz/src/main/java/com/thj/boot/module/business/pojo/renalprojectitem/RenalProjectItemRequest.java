package com.thj.boot.module.business.pojo.renalprojectitem;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RenalProjectItemRequest extends PageParam {
    /**
     *
     */
    private Long id;
    /**
     * 字典ID
     */
    private Long dictId;
    /**
     * 英文名
     */
    private String prop;
    /**
     * 中文名
     */
    private String label;

    private String content;
    /**
     * 单位
     */
    private String unit;

    private String otherUnit;

    private String line;

    private Integer contentType;//1输入 2下拉
    private Integer unitType;   //检查结果1固定，2下拉，3输入
    /**
     * 排序
     */
    private Byte sort;

    /**
     * 是否必填
     */
    private String required;
}
