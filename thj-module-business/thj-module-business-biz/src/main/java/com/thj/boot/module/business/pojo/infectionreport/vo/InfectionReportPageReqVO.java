package com.thj.boot.module.business.pojo.infectionreport.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InfectionReportPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String name;
    /**
     * 性别（字典获取）
     */
    private String sex;
    /**
     * 医院
     */
    private String hospital;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 电话
     */
    private String mobile;
    /**
     * 手机
     */
    private String phone;
    /**
     * 首次透析日期
     */
    private Date firstReceiveTime;
    /**
     * 当前医院开始透析日期
     */
    private Date startReceiveTime;
    /**
     * 透析器复用情况（字典获取）
     */
    private String reuse;
    /**
     * 输血史（字典获取）
     */
    private String blood;
    /**
     * 现住址
     */
    private String newAddress;
    /**
     * 户籍地址
     */
    private String oldAddress;
    /**
     * 传染病类型（字典获取）
     */
    private String type;
    /**
     * 诊断日期
     */
    private Date diagnosisTime;
    /**
     * 填表医师
     */
    private Long userId;
    /**
     * 信息1
     */
    private String one;
    /**
     * 信息2
     */
    private String two;
    /**
     * 信息3
     */
    private String three;
    /**
     * 信息4
     */
    private String four;
    /**
     * 信息5
     */
    private String five;
    /**
     * 信息6
     */
    private String six;
    /**
     * 信息7
     */
    private String serven;
    /**
     * 信息8
     */
    private String eight;
    /**
     * 其他信息1
     */
    private String otherOne;
    /**
     * 其他信息2
     */
    private String otherTwo;
    /**
     * 备注
     */
    private String remark;

}
