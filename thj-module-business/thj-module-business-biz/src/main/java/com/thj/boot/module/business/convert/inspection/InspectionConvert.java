package com.thj.boot.module.business.convert.inspection;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.inspection.InspectionDO;
import com.thj.boot.module.business.pojo.inspection.vo.InspectionCreateReqVO;
import com.thj.boot.module.business.pojo.inspection.vo.InspectionRespVO;
import com.thj.boot.module.business.pojo.inspection.vo.InspectionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 检验检查 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionConvert {

    InspectionConvert INSTANCE = Mappers.getMapper(InspectionConvert.class);

    InspectionDO convert(InspectionCreateReqVO bean);

    InspectionDO convert(InspectionUpdateReqVO bean);

    InspectionRespVO convert(InspectionDO bean);

    List<InspectionRespVO> convertList(List<InspectionDO> list);

    PageResult<InspectionRespVO> convertPage(PageResult<InspectionDO> page);


}
