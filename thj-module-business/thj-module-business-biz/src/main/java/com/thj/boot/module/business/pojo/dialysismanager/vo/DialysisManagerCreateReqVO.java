package com.thj.boot.module.business.pojo.dialysismanager.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisManagerCreateReqVO extends DialysisManagerBaseVO {
    /**
     * 当前患者状态
     * 0-未签到，1-已签到，2-透析中，3-透析后
     */
    private String state;

    /**
     * 患者排班信息
     */
    private String tableContentInfo;
    /**
     * 透析日期
     */
    private Date DateWeek;

    /**
     * 透析日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户密码
     */
    private String password;
    /**
     * 结束透析时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

}
