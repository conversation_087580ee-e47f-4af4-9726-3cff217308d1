package com.thj.boot.module.business.convert.ohpressure;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.ohpressure.OhPressureDO;
import com.thj.boot.module.business.pojo.ohpressure.vo.OhPressureCreateReqVO;
import com.thj.boot.module.business.pojo.ohpressure.vo.OhPressureRespVO;
import com.thj.boot.module.business.pojo.ohpressure.vo.OhPressureUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * OH压疮评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OhPressureConvert {

    OhPressureConvert INSTANCE = Mappers.getMapper(OhPressureConvert.class);

    OhPressureDO convert(OhPressureCreateReqVO bean);

    OhPressureDO convert(OhPressureUpdateReqVO bean);

    OhPressureRespVO convert(OhPressureDO bean);

    List<OhPressureRespVO> convertList(List<OhPressureDO> list);

    PageResult<OhPressureRespVO> convertPage(PageResult<OhPressureDO> page);


}
