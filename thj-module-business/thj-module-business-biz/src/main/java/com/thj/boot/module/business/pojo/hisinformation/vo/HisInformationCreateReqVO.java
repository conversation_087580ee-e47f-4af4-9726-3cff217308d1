package com.thj.boot.module.business.pojo.hisinformation.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HisInformationCreateReqVO extends HisInformationBaseVO {
    private Long itemId;
    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 项目单位
     */
    private String unit;
    /**
     * 医保收费等级
     */
    private String drugDirectoryType;
    /**
     * 医保编码
     */
    private String drugDirectoryCode;
    /**
     * 用法
     */
    private String usages;
    /**
     * 单价
     */
    private String unitPrice;
    /**
     * 项目编码
     */
    private String customCode;
    /**
     * 项目分类
     */
    private String itemType;
    /**
     * 是否外检
     */
    private String isExternalInspection;
    /**
     * 外检机构名称
     */
    private String externalInspectionName;
    /**
     * 外检机构编号
     */
    private String externalInspectionNumber;
    /**
     * 透析中心ID
     */
    private String hosipital;
    /**
     * 有效标志
     */
    private String enabledMark;
    /**
     * 有效名称
     */
    private String enabledName;
    /**
     * 审核标志
     */
    private String isAudit;
    /**
     * 政府指导价/采购价
     */
    private String purchasePrice;
    /**
     * 审核时间
     */
    private String auditTime;
    /**
     * 审核单价
     */
    private String auditPrice;
    /**
     * 审核医保编码
     */
    private String auditMedInsCode;
    /**
     * 限制使用标志
     */
    private String limitUseSign;
    /**
     * 限制使用范围
     */
    private String limitUseScope;
    /**
     *
     */
    private Long deptId;


}
