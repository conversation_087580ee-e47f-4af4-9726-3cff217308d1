package com.thj.boot.module.business.pojo.facilityblood.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FacilityBloodPageReqVO extends PageParam {

    /**
     * 透析机联机设置id
     */
    private Long id;
    /**
     * 设备编号
     */
    private String code;
    /**
     * 透析机号
     */
    private Long facilityId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店id
     */
    private Long deptId;
}
