package com.thj.boot.module.business.pojo.disinfectionunit.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DisinfectionUnitCreateReqVO extends DisinfectionUnitBaseVO {


    /**
     * 序号
     */
    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 透析单元清空（对/错）
     */
    private String unitEmptyingDialysis;
    /**
     * 正确行手卫生
     */
    private String handHygiene;
    /**
     * 消毒前清空透析大厅（对/错）
     */
    private String beforeDisinfection;
    /**
     * 戴新洁净手套（对/错）
     */
    private String wearingGloves;
    /**
     * 消毒所有物表（对/错）
     */
    private String disinfectingAllSurfaces;
    /**
     * 保证所有物表被消毒（对/错）
     */
    private String ensuringAllDisinfected;
    /**
     * 保证有足够待干时间（对/错）
     */
    private String dryingTime;
    /**
     * 操作时戴手套（对/错）
     */
    private String wearingGlovesOperation;
    /**
     * 消毒期间勿进外人（对/错）
     */
    private String duringDisinfection;
    /**
     * 消毒巾一物一用（对/错）
     */
    private String singleUseWipes;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTime;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;
}
