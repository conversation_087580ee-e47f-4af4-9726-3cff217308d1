package com.thj.boot.module.business.pojo.arrangeclasses.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArrangeClassesPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * a-上午，b-下午,c-晚上
     */
    private String dayState;
    /**
     * 1-周一，2-周二，3-周三，4-周四，5-周五，6-周六，7-周日
     */
    private String weekState;
    /**
     * 0-预约排班，1-模版1，2-模版2,3-模版3,4-模版4
     */
    private String tempType;
    /**
     * 排班日期
     */
    private Date classesTime;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 机号
     */
    private String facilityName;
    /**
     * 分区名称
     */
    private String faciitySubareaName;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析模式字典值
     */
    private String dialysisValue;
    /**
     * 透析模式名称
     */
    private String dialysisName;
    /**
     * 设备型号
     */
    private String equipmentType;
    /**
     * 设备id
     */
    private Long equipmentId;
    /**
     * 标签值
     */
    private String labels;
    /**
     * 患者来源
     */
    private String patientSrouce;
    /**
     * 传染病
     */
    private String infects;
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店id
     */
    private Long deptId;

}
