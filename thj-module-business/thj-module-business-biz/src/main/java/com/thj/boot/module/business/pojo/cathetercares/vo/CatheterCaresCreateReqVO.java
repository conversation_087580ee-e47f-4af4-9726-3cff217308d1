package com.thj.boot.module.business.pojo.cathetercares.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatheterCaresCreateReqVO extends CatheterCaresBaseVO {

    private Long id;
    /**
     * 检查日期
     */
    private LocalDate inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 戴口罩
     */
    private String wearingMask;
    /**
     * 正确行手卫生
     */
    private String correctHandHygiene;
    /**
     * 戴新洁净手套
     */
    private String wearingNewCleanGloves;
    /**
     * 正确皮肤消毒
     */
    private String correctSkinDisinfection;
    /**
     * 消毒直径大于10cm
     */
    private String disinfectionDiameterGt;
    /**
     * 消毒之后待干
     */
    private String waitingForDryingAfterDisinfection;
    /**
     * 不再触碰导管出口
     */
    private String noTouchingCatheterExitAfterDisinfection;
    /**
     * 无菌包扎
     */
    private String sterileDressing;
    /**
     * 脱手套
     */
    private String gloveRemoval;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTimeInMinutes;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;

}
