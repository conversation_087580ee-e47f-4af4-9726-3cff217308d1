package com.thj.boot.module.business.service.hisdrug;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.async.his.HisContentAsync;
import com.thj.boot.module.business.convert.hisdrug.HisDrugConvert;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.dal.mapper.drug.DrugMapper;
import com.thj.boot.module.business.dal.mapper.hiscombo.HisComboMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.hisinformation.HisInformationMapper;
import com.thj.boot.module.business.dal.mapper.project.ProjectMapper;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugCreateReqVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugPageReqVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugRespVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugUpdateReqVO;
import com.thj.boot.module.business.utils.PinYinUtil;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * his药品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HisDrugServiceImpl implements HisDrugService {

    @Resource
    private HisDrugMapper hisDrugMapper;

    @Resource
    private DeptApi deptApi;

    @Resource
    private HisContentAsync hisContentAsync;

    @Resource
    private HisInformationMapper hisInformationMapper;

    @Resource
    private DrugMapper drugMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private HisComboMapper hisComboMapper;

    @Autowired
    private HisConsumablesMapper hisConsumablesMapper;

    @Override
    public Long createHisDrug(HisDrugCreateReqVO createReqVO) {
        // 插入
        HisDrugDO hisDrug = HisDrugConvert.INSTANCE.convert(createReqVO);
        hisDrugMapper.insert(hisDrug);
        // 返回
        return hisDrug.getFDrugId();
    }

    @Override
    public void updateHisDrug(HisDrugUpdateReqVO updateReqVO) {
        // 更新
        HisDrugDO updateObj = HisDrugConvert.INSTANCE.convert(updateReqVO);
        hisDrugMapper.updateById(updateObj);
    }

    @Override
    public void deleteHisDrug(Long id) {
        // 删除
        hisDrugMapper.deleteById(id);
    }


    @Override
    public HisDrugDO getHisDrug(Long id) {
        return hisDrugMapper.selectById(id);
    }

    @Override
    public List<HisDrugDO> getHisDrugList(HisDrugCreateReqVO createReqVO) {
        return hisDrugMapper.selectList(createReqVO);
    }

    @Override
    public PageResult<HisDrugDO> getHisDrugPage(HisDrugPageReqVO pageReqVO) {
        return hisDrugMapper.selectPage(pageReqVO);
    }


    private static String url = "https://sf.balanlife.com:5000/api/His/Drug";

    @Override
    public void save(String hosipital, String systemdeptid) {

        Map<String, Object> res = new LinkedHashMap<>();
        res.put("currentPage", 1);
        res.put("pageSize", 50);
        res.put("sort", "desc");
        res.put("sidx", "F_CreatorTime");
        res.put("F_Hosipital", hosipital);
        String s = HttpUtil.get(url, res);
        JSONObject j = JSONObject.parseObject(s);
        JSONObject page = j.getJSONObject("data").getJSONObject("pagination");
        int total = Integer.parseInt(page.getString("total"));
        int count = total / 50;
        int remainder = total % 50;
        if (remainder > 0) {
            count = count + 1;
        }
        for (int i = 1; i <= count; i++) {
            res.put("currentPage", i);
            String data = HttpUtil.get(url, res);
            JSONObject jsonObject = JSONObject.parseObject(data);
            JSONArray array = jsonObject.getJSONObject("data").getJSONArray("list");
            for (Object o : array) {
                JSONObject jo = (JSONObject) o;
                HisDrugDO hisDrugDO = new HisDrugDO();
                hisDrugDO.setFDrugId(jo.getLong("F_DrugId"));
                Long l = hisDrugMapper.selectCount(HisDrugDO::getFDrugId, hisDrugDO.getFDrugId(), HisDrugDO::getDeptId, systemdeptid);
                if (l == 0L) {
                    hisDrugDO.setFDrugName(jo.getString("F_DrugName"));
                    hisDrugDO.setFDrugNumber(jo.getString("F_DrugNumber"));
                    hisDrugDO.setFDrugSpec(jo.getString("F_DrugSpec"));
                    hisDrugDO.setFPreparaUnit(jo.getString("F_PreparaUnit"));
                    hisDrugDO.setFDrugForm(jo.getString("F_DrugForm"));
                    hisDrugDO.setFPackUnit(jo.getString("F_PackUnit"));
                    hisDrugDO.setFDrugFormCount(jo.getString("F_DrugFormCount"));
                    hisDrugDO.setFDrugCategory(jo.getString("F_DrugCategory"));
                    hisDrugDO.setFDrugDirectoryType(jo.getString("F_DrugDirectoryType"));
                    hisDrugDO.setFDrugDirectoryCode(jo.getString("F_DrugDirectoryCode"));
                    hisDrugDO.setFDrugPrice(jo.getString("F_DrugPrice"));
                    hisDrugDO.setFRetailUnit(jo.getString("F_RetailUnit"));
                    hisDrugDO.setFDmin(jo.getString("F_Dmin"));
                    hisDrugDO.setFOnceUsing(jo.getString("F_OnceUsing"));
                    hisDrugDO.setFSpecUnit(jo.getString("F_SpecUnit"));
                    hisDrugDO.setFDrugChangjia(jo.getString("F_DrugChangjia"));
                    hisDrugDO.setFApprovalNumber(jo.getString("F_ApprovalNumber"));
                    hisDrugDO.setFLimitUseSign(jo.getString("F_LimitUseSign"));
                    hisDrugDO.setFLimitUseScope(jo.getString("F_LimitUseScope"));
                    hisDrugDO.setFHosipital(jo.getString("F_Hosipital"));
                    hisDrugDO.setFEnabledMark(jo.getString("F_EnabledMark"));
                    hisDrugDO.setFEnabledName(jo.getString("F_EnabledName"));
                    hisDrugDO.setFIsAudit(jo.getString("F_IsAudit"));
                    hisDrugDO.setFPurchasePrice(jo.getString("F_PurchasePrice"));
                    hisDrugDO.setFAuditTime(jo.getLong("F_AuditTime"));
                    hisDrugDO.setFAuditPrice(jo.getString("F_AuditPrice"));
                    hisDrugDO.setFAuditMedinsCode(jo.getString("F_AuditMedInsCode"));
                    hisDrugDO.setFStockUnit(jo.getString("F_StockUnit"));
                    hisDrugDO.setFStockDmin(jo.getString("F_StockDmin"));
                    hisDrugDO.setDeptId(Long.valueOf(systemdeptid));
                    hisDrugDO.setPinyin(PinYinUtil.getFirstSpell(hisDrugDO.getFDrugName()));
                    hisDrugMapper.saveOrUpdate(hisDrugDO);
                }

            }
        }

    }

    @Override
    public Boolean asyncHisDrug(HttpServletRequest request) {
        long userId = StpUtil.getLoginIdAsLong();
        Long deptId = Long.valueOf(request.getHeader("SystemDeptId"));
        DeptRespDTO dept = deptApi.getDept(deptId);
        if (dept != null) {
            Future<Boolean> booleanFuture = hisContentAsync.asyncHisDrug(dept.getHospitalId(), userId, deptId);
            try {
                return booleanFuture.get();
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    @Override
    public List<HisDrugRespVO> getHisDrugInformation(HisDrugCreateReqVO hisDrugCreateReqVO) {
        List<HisDrugRespVO> drugInformationRespVOS = Lists.newArrayList();
        List<HisDrugDO> hisDrugDOS = hisDrugMapper.selectList(new LambdaQueryWrapperX<HisDrugDO>()
                .likeIfPresent(HisDrugDO::getFDrugName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(hisDrugDOS)) {
            List<HisDrugRespVO> collect = hisDrugDOS.stream().map(hisDrugDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(hisDrugDO.getFDrugId());
                hisDrugRespVO.setFDrugName(hisDrugDO.getFDrugName());
                hisDrugRespVO.setFDrugSpec(hisDrugDO.getFDrugSpec());
                hisDrugRespVO.setFDrugForm(hisDrugDO.getFDrugForm());
                hisDrugRespVO.setFSpecUnit(hisDrugDO.getFSpecUnit());
                hisDrugRespVO.setFStockUnit(hisDrugDO.getFStockUnit());
                hisDrugRespVO.setFPreparaUnit(hisDrugDO.getFPreparaUnit());
                hisDrugRespVO.setFDrugNumber(hisDrugDO.getFDrugNumber());
                hisDrugRespVO.setFDrugChangjia(hisDrugDO.getFDrugChangjia());
                hisDrugRespVO.setFEnabledMark(hisDrugDO.getFEnabledMark());
                hisDrugRespVO.setGenres(hisDrugDO.getGenres());
                hisDrugRespVO.setPinyin(hisDrugDO.getPinyin());
                hisDrugRespVO.setDrugType(1);
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }
        List<HisInformationDO> hisInformationDOS = hisInformationMapper.selectList(new LambdaQueryWrapperX<HisInformationDO>()
                .likeIfPresent(HisInformationDO::getItemName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(hisInformationDOS)) {
            List<HisDrugRespVO> collect = hisInformationDOS.stream().map(hisInformationDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(hisInformationDO.getItemId());
                hisDrugRespVO.setFDrugName(hisInformationDO.getItemName());
                hisDrugRespVO.setFDrugSpec("");
                hisDrugRespVO.setFDrugForm("");
                hisDrugRespVO.setFSpecUnit(hisInformationDO.getUnit());
                hisDrugRespVO.setFStockUnit(hisInformationDO.getUnit());
                hisDrugRespVO.setFPreparaUnit("");
                hisDrugRespVO.setFDrugNumber("");
                hisDrugRespVO.setFDrugChangjia("");
                hisDrugRespVO.setGenres(hisInformationDO.getGenres());
                hisDrugRespVO.setFEnabledMark(hisInformationDO.getEnabledMark());
                if (hisInformationDO.getItemName().contains("注射")) {
                    hisDrugRespVO.setDrugType(2);
                }
                //hisDrugRespVO.setPinyin(hisInformationDO);
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }

        List<DrugDO> drugDOS = drugMapper.selectList(new LambdaQueryWrapperX<DrugDO>()
                .likeIfPresent(DrugDO::getName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(drugDOS)) {
            //剂量单位
            Map<String, String> agentUnit = getType("agent_unit");
            Map<String, String> mintUnit = getType("min_agent_unit");

            List<HisDrugRespVO> collect = drugDOS.stream().map(drugDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(drugDO.getId());
                hisDrugRespVO.setFDrugName(drugDO.getName());
                hisDrugRespVO.setFDrugSpec(drugDO.getSpec());
                hisDrugRespVO.setFDrugForm("");
                hisDrugRespVO.setFSpecUnit(agentUnit.get(drugDO.getDoseUnit()));
                hisDrugRespVO.setFStockUnit(mintUnit.get(drugDO.getMinDose()));
                hisDrugRespVO.setFPreparaUnit(drugDO.getMinDose());
                hisDrugRespVO.setFDrugNumber(drugDO.getItemCode());
                hisDrugRespVO.setFDrugChangjia(drugDO.getVender());
                hisDrugRespVO.setGenres(drugDO.getGenres());
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }

        List<ProjectDO> projectDOS = projectMapper.selectList(new LambdaQueryWrapperX<ProjectDO>()
                .likeIfPresent(ProjectDO::getName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(projectDOS)) {
            List<HisDrugRespVO> collect = projectDOS.stream().map(projectDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(projectDO.getId());
                hisDrugRespVO.setFDrugName(projectDO.getName());
                hisDrugRespVO.setFAuditPrice(projectDO.getUnit());
                hisDrugRespVO.setFDrugSpec("");
                hisDrugRespVO.setFDrugForm("");
                hisDrugRespVO.setFSpecUnit(projectDO.getUnit());
                hisDrugRespVO.setFStockUnit("");
                hisDrugRespVO.setFPreparaUnit("");
                hisDrugRespVO.setGenres(projectDO.getGenres());
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }

        List<HisComboDO> hisComboDOS = hisComboMapper.selectList(new LambdaQueryWrapperX<HisComboDO>()
                .likeIfPresent(HisComboDO::getPackageName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(hisComboDOS)) {
            List<HisDrugRespVO> collect = hisComboDOS.stream().map(hisComboDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(hisComboDO.getPackageId());
                hisDrugRespVO.setFDrugName(hisComboDO.getPackageName());
                hisDrugRespVO.setFAuditPrice("");
                hisDrugRespVO.setFDrugSpec("");
                hisDrugRespVO.setFDrugForm("");
                hisDrugRespVO.setFSpecUnit("");
                hisDrugRespVO.setFStockUnit("");
                hisDrugRespVO.setFPreparaUnit("");
                hisDrugRespVO.setGenres(hisComboDO.getGenres());
                hisDrugRespVO.setFEnabledMark(hisComboDO.getEnabledMark() + "");
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }
        // 添加耗材
        List<HisConsumablesDO> hisConsumablesDOS = hisConsumablesMapper.selectList(new LambdaQueryWrapperX<HisConsumablesDO>()
                .likeIfPresent(HisConsumablesDO::getConsumName, hisDrugCreateReqVO.getFDrugName()));
        if (CollectionUtil.isNotEmpty(hisConsumablesDOS)) {
            List<HisDrugRespVO> collect = hisConsumablesDOS.stream().map(hisConsumablesDO -> {
                HisDrugRespVO hisDrugRespVO = new HisDrugRespVO();
                hisDrugRespVO.setFDrugId(hisConsumablesDO.getConsumId());
                hisDrugRespVO.setFDrugName(hisConsumablesDO.getConsumName());
                hisDrugRespVO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                //hisDrugRespVO.setFDrugForm(hisConsumablesDO.get);
                hisDrugRespVO.setFSpecUnit(hisConsumablesDO.getUnit());
                hisDrugRespVO.setFStockUnit(hisConsumablesDO.getStockUnit());
                hisDrugRespVO.setFPreparaUnit(hisConsumablesDO.getUnit());
                hisDrugRespVO.setFDrugNumber(hisConsumablesDO.getConsumNumber());
                hisDrugRespVO.setFDrugChangjia(hisConsumablesDO.getManufacturer());
                hisDrugRespVO.setFEnabledMark(hisConsumablesDO.getEnabledMark());
                hisDrugRespVO.setGenres(hisConsumablesDO.getGenres());
                hisDrugRespVO.setDrugType(2);
                return hisDrugRespVO;
            }).collect(Collectors.toList());
            drugInformationRespVOS.addAll(collect);
        }

        return drugInformationRespVOS;
    }

    /**
     * 药品字典
     *
     * @return
     */
    private Map<String, String> getType(String type) {
        List<DictDataRespDTO> dictListData = dictDataApi.getDictListData(type);
        Map<String, String> map = new HashMap<>();
        dictListData.forEach(dictDataRespDTO -> {
            map.put(dictDataRespDTO.getValue(), dictDataRespDTO.getLabel());
        });
        return map;
    }

}
