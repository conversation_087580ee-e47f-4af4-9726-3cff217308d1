package com.thj.boot.module.business.convert.doctortemp;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.doctortemp.DoctorTempDO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempCreateReqVO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempRespVO;
import com.thj.boot.module.business.pojo.doctortemp.vo.DoctorTempUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医嘱模版 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorTempConvert {

    DoctorTempConvert INSTANCE = Mappers.getMapper(DoctorTempConvert.class);

    DoctorTempDO convert(DoctorTempCreateReqVO bean);

    DoctorTempDO convert(DoctorTempUpdateReqVO bean);

    DoctorTempRespVO convert(DoctorTempDO bean);

    List<DoctorTempRespVO> convertList(List<DoctorTempDO> list);

    PageResult<DoctorTempRespVO> convertPage(PageResult<DoctorTempDO> page);


}
