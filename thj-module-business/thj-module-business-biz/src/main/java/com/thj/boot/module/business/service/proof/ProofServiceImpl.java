package com.thj.boot.module.business.service.proof;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.proof.ProofConvert;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.proof.ProofDO;
import com.thj.boot.module.business.dal.mapper.diseasereason.DiseaseReasonMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.proof.ProofMapper;
import com.thj.boot.module.business.pojo.proof.vo.ProofCreateReqVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofPageReqVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofRespVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofUpdateReqVO;
import com.thj.boot.module.system.api.dept.DeptApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 疾病诊断-诊断证明 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProofServiceImpl implements ProofService {

    @Resource
    private ProofMapper proofMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private DeptApi deptApi;

    @Resource
    private DiseaseReasonMapper diseaseReasonMapper;

    @Override
    public Long createProof(ProofCreateReqVO createReqVO) {
        // 插入
        ProofDO proof = ProofConvert.INSTANCE.convert(createReqVO);
        proofMapper.insert(proof);
        // 返回
        return proof.getId();
    }

    @Override
    public void updateProof(ProofUpdateReqVO updateReqVO) {
        // 更新
        ProofDO updateObj = ProofConvert.INSTANCE.convert(updateReqVO);
        proofMapper.updateById(updateObj);
    }

    @Override
    public void deleteProof(Long id) {
        // 删除
        proofMapper.deleteById(id);
    }


    @Override
    public ProofRespVO getProof(Long patientId) {
        ProofDO proofDO = proofMapper.selectOne(ProofDO::getPatientId, patientId);
        PatientDO patientDO = patientMapper.selectById(patientId);
        ProofRespVO proofRespVO = ProofConvert.INSTANCE.convert(proofDO);
        //诊断
        String diseaseReasonNames = null;
        List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, patientId, DiseaseReasonDO::getSync, 1);
        if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
            diseaseReasonNames = diseaseReasonDOS.stream().sorted(Comparator.comparing(DiseaseReasonDO::getSorted,Comparator.nullsLast(Integer::compareTo))).map(diseaseReasonDO -> {
                //如果是自定义有值取自定义，三级和二级有值取三级，三级有值取三级，二级有值取二级
                String str = "";
                if (StrUtil.isNotEmpty(diseaseReasonDO.getCustomName())) {
                    str = diseaseReasonDO.getCustomName();
                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentThreeName())) {
                    str = diseaseReasonDO.getParentThreeName();
                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentTwoName())) {
                    str = diseaseReasonDO.getParentTwoName();
                }
                return str;
            }).collect(Collectors.toList()).stream().collect(Collectors.joining(","));
        }

        if (patientDO != null) {
            if (proofRespVO == null) {
                ProofRespVO proofRespVO1 = new ProofRespVO();
                proofRespVO1.setAddress(patientDO.getAddress());
                proofRespVO1.setPatientId(patientId);
                proofRespVO1.setNation(patientDO.getNation());
                proofRespVO1.setAge(patientDO.getAge());
                proofRespVO1.setIdCard(patientDO.getIdCard());
                proofRespVO1.setName(patientDO.getName());
                proofRespVO1.setSex(patientDO.getSex());
                proofRespVO1.setWorkUnit(patientDO.getWorkUnit());
                proofRespVO1.setOccupation(patientDO.getOccupation());
                proofRespVO1.setDialyzeNo(patientDO.getDialyzeNo());
                proofRespVO1.setDiseaseReasonNames(diseaseReasonNames);
                return proofRespVO1;
            }
            proofRespVO.setAddress(patientDO.getAddress());
            proofRespVO.setNation(patientDO.getNation());
            proofRespVO.setAge(patientDO.getAge());
            proofRespVO.setIdCard(patientDO.getIdCard());
            proofRespVO.setName(patientDO.getName());
            proofRespVO.setSex(patientDO.getSex());
            proofRespVO.setWorkUnit(patientDO.getWorkUnit());
            proofRespVO.setOccupation(patientDO.getOccupation());
            proofRespVO.setDialyzeNo(patientDO.getDialyzeNo());
            proofRespVO.setPatientId(patientId);
            proofRespVO.setDiseaseReasonNames(diseaseReasonNames);
        }
        return proofRespVO;
    }

    @Override
    public List<ProofDO> getProofList(Collection<Long> ids) {
        return proofMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProofDO> getProofPage(ProofPageReqVO pageReqVO) {
        return proofMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProofDO> getProofList(ProofCreateReqVO createReqVO) {
        return proofMapper.selectList(createReqVO);
    }

    @Override
    public void createOrUpdateProof(ProofUpdateReqVO updateReqVO) {
        ProofDO proofDO = ProofConvert.INSTANCE.convert(updateReqVO);
        proofMapper.saveOrUpdate(proofDO);
    }

}
