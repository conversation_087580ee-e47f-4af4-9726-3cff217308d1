package com.thj.boot.module.business.convert.firstcourserecord;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.firstcourserecord.FirstCourseRecordDO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordRespVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 首次病程记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FirstCourseRecordConvert {

    FirstCourseRecordConvert INSTANCE = Mappers.getMapper(FirstCourseRecordConvert.class);

    FirstCourseRecordDO convert(FirstCourseRecordCreateReqVO bean);

    FirstCourseRecordDO convert(FirstCourseRecordUpdateReqVO bean);

    FirstCourseRecordRespVO convert(FirstCourseRecordDO bean);

    List<FirstCourseRecordRespVO> convertList(List<FirstCourseRecordDO> list);

    PageResult<FirstCourseRecordRespVO> convertPage(PageResult<FirstCourseRecordDO> page);


}
