package com.thj.boot.module.business.service.morse;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.morse.MorseDO;
import com.thj.boot.module.business.pojo.morse.vo.MorseCreateReqVO;
import com.thj.boot.module.business.pojo.morse.vo.MorsePageReqVO;
import com.thj.boot.module.business.pojo.morse.vo.MorseUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * morse跌倒评估量 Service 接口
 *
 * <AUTHOR>
 */
public interface MorseService {

    /**
     * 创建morse跌倒评估量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMorse( MorseCreateReqVO createReqVO);

    /**
     * 更新morse跌倒评估量
     *
     * @param updateReqVO 更新信息
     */
    void updateMorse( MorseUpdateReqVO updateReqVO);

    /**
     * 删除morse跌倒评估量
     *
     * @param id 编号
     */
    void deleteMorse(Long id);

    /**
     * 获得morse跌倒评估量
     *
     * @param id 编号
     * @return morse跌倒评估量
     */
    MorseDO getMorse(Long id);

    /**
     * 获得morse跌倒评估量列表
     *
     * @param ids 编号
     * @return morse跌倒评估量列表
     */
    List<MorseDO> getMorseList(Collection<Long> ids);

    /**
     * 获得morse跌倒评估量分页
     *
     * @param pageReqVO 分页查询
     * @return morse跌倒评估量分页
     */
    PageResult<MorseDO> getMorsePage(MorsePageReqVO pageReqVO);

    /**
     * 获得morse跌倒评估量列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return morse跌倒评估量列表
     */
    List<MorseDO> getMorseList(MorseCreateReqVO exportReqVO);

}
