package com.thj.boot.module.business.pojo.doctortemp.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DoctorTempPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 模版名称
     */
    private String  title;
    /**
     * 0-临时医嘱，1-长期医嘱
     */
    private Boolean type;
    /**
     * 开嘱医生
     */
    private Long userId;
    /**
     * 医嘱名称
     */
    private String name;
    /**
     * 描述
     */
    private String describe;
    /**
     * 单次用量
     */
    private String once;
    /**
     * 开药数量
     */
    private String prescribe;
    /**
     * 给药途径
     */
    private String way;
    /**
     * 执行频次
     */
    private String frequency;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序
     */
    private Integer sort;


}
