package com.thj.boot.module.business.convert.contradict;

import com.thj.boot.module.business.dal.datado.contradict.ContradictDO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictCreateReqVO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 抗凝剂 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ContradictConvert {

    ContradictConvert INSTANCE = Mappers.getMapper(ContradictConvert.class);


    List<ContradictRespVO> convertList(List<ContradictDO> contradictDOS);

    List<ContradictDO> convertList2(List<ContradictCreateReqVO> contradictCreateReqVOS);
}
