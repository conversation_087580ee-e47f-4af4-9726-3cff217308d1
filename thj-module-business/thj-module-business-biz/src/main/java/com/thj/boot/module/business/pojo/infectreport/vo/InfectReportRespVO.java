package com.thj.boot.module.business.pojo.infectreport.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InfectReportRespVO extends InfectReportBaseVO {

    /**
     * 姓名
     */
    private String name;
    /**
     * 1-男，2-女
     */
    private String sex;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 电话(本人)
     */
    private String mobile;
    /**
     * 首次透析日期
     */
    private Date firstReceiveTime;
    /**
     * 初始透析次数
     */
    private String initDialyzeNo;
    /**
     * 家庭住址
     */
    private String address;
    /**
     * 医院
     */
    private String hospitalName;
    /**
     * 传染病类型
     */
    private List<String> getInfectType;

}
