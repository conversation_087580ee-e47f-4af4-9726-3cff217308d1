package com.thj.boot.module.business.convert.infectionreport;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.infectionreport.InfectionReportDO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportCreateReqVO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportRespVO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 传染病报告单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InfectionReportConvert {

    InfectionReportConvert INSTANCE = Mappers.getMapper(InfectionReportConvert.class);

    InfectionReportDO convert(InfectionReportCreateReqVO bean);

    InfectionReportDO convert(InfectionReportUpdateReqVO bean);

    InfectionReportRespVO convert(InfectionReportDO bean);

    List<InfectionReportRespVO> convertList(List<InfectionReportDO> list);

    PageResult<InfectionReportRespVO> convertPage(PageResult<InfectionReportDO> page);


}
