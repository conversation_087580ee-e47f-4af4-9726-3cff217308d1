package com.thj.boot.module.business.pojo.jkeducation.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JkEducationPageReqVO extends PageParam {
    private Long id;
    /**
     * 教育时间
     */
    private LocalDateTime educationTime;
    /**
     * 教育主题
     */
    private String educationTopic;
    /**
     * 教育类型/方法
     */
    private String educationMethods;
    /**
     * 教育目标
     */
    private String goal;
    /**
     * 教育对象
     */
    private String target;
    /**
     * 教育护士
     */
    private Long educationNurse;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 评价护士
     */
    private Long evaluateNurse;
    /**
     * 评价日期
     */
    private LocalDateTime evaluateTime;
    /**
     * 是否达标
     */
    private String reach;
}
