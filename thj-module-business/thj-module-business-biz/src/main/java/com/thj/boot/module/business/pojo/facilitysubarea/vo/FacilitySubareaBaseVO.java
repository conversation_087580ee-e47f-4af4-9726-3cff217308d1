package com.thj.boot.module.business.pojo.facilitysubarea.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

/**
 * 设备管理-分区设置 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FacilitySubareaBaseVO extends BaseDO {

    /**
     * 分区设置id
     */
    private Long id;
    /**
     * 分区名称
     */
    private String name;
    /**
     * 分区类型(字典获取)
     */
    private String type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 是否传染病区，0-否，1-是
     */
    private Integer state;

}
