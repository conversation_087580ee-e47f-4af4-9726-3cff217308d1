package com.thj.boot.module.business.convert.painevaluate;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.painevaluate.PainEvaluateDO;
import com.thj.boot.module.business.pojo.painevaluate.vo.PainEvaluateCreateReqVO;
import com.thj.boot.module.business.pojo.painevaluate.vo.PainEvaluateRespVO;
import com.thj.boot.module.business.pojo.painevaluate.vo.PainEvaluateUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 疼痛评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PainEvaluateConvert {

    PainEvaluateConvert INSTANCE = Mappers.getMapper(PainEvaluateConvert.class);

    PainEvaluateDO convert(PainEvaluateCreateReqVO bean);

    PainEvaluateDO convert(PainEvaluateUpdateReqVO bean);

    PainEvaluateRespVO convert(PainEvaluateDO bean);

    List<PainEvaluateRespVO> convertList(List<PainEvaluateDO> list);

    PageResult<PainEvaluateRespVO> convertPage(PageResult<PainEvaluateDO> page);


}
