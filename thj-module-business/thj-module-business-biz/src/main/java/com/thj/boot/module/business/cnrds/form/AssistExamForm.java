package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 辅助检查 表单
 */
@Data
public class AssistExamForm {

    // 年
    @JsonProperty("EXAM_YEAR")
    @NotNull
    private String examYear;

    // 时间维度
    @JsonProperty("EXAM_DIMENSION")
    @NotNull
    private String examDimension;

    // 胸部X线是否检查
    @JsonProperty("EXAM_XRAY_YN")
    private String examXrayYn;

    // 心胸比
    @JsonProperty("EXAM_XRAY_CTP")
    private String examXrayCtp;

    // 诊断
    @JsonProperty("EXAM_XRAY_DIAGNOSIS")
    private List<String> examXrayDiagnosis;

    // 心电图是否检查
    @JsonProperty("EXAM_ECG_YN")
    private String examEcgYn;

    // 诊断
    @JsonProperty("EXAM_ECG_DIAGNOSIS")
    private List<String> examEcgDiagnosis;

    // 超声心动图是否检查
    @JsonProperty("EXAM_UCG_YN")
    private String examUcgYn;

    // 射血分数
    @JsonProperty("EXAM_UCG_EF")
    private String examUcgEf;

    // 心包积液
    @JsonProperty("EXAM_UCG_PE")
    private String examUcgPe;

    // 左房扩大
    @JsonProperty("EXAM_UCG_LA_ENLARGEMENT")
    private String examUcgLaEnlargement;

    // 右房扩大
    @JsonProperty("EXAM_UCG_RA_ENLARGEMENT")
    private String examUcgRaEnlargement;

    // 左室扩大
    @JsonProperty("EXAM_UCG_LV_ENLARGEMENT")
    private String examUcgLvEnlargement;

    // 右室扩大
    @JsonProperty("EXAM_UCG_RV_ENLARGEMENT")
    private String examUcgRvEnlargement;

    // 左室增厚
    @JsonProperty("EXAM_UCG_LVW_THICKEN")
    private String examUcgLvwThicken;

    // 室间隔增厚
    @JsonProperty("EXAM_UCG_IVS_THICKEN")
    private String examUcgIvsThicken;

    // 左室舒张功能减低
    @JsonProperty("EXAM_UCG_LV_DIASTOLIC")
    private String examUcgLvDiastolic;

    // 二尖瓣钙化
    @JsonProperty("EXAM_UCG_MV_CALCIFICATION")
    private String examUcgMvCalcification;

    // 二尖瓣反流
    @JsonProperty("EXAM_UCG_MV_REGURGITATION")
    private String examUcgMvRegurgitation;

    // 三尖瓣反流
    @JsonProperty("EXAM_UCG_TV_REGURGITATION")
    private String examUcgTvRegurgitation;

    // 主动脉瓣反流
    @JsonProperty("EXAM_UCG_AV_REGURGITATION")
    private String examUcgAvRegurgitation;

}
