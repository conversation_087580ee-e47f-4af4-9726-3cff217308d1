package com.thj.boot.module.business.convert.facilityblood;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facilityblood.FacilityBloodDO;
import com.thj.boot.module.business.pojo.facilityblood.vo.FacilityBloodCreateReqVO;
import com.thj.boot.module.business.pojo.facilityblood.vo.FacilityBloodRespVO;
import com.thj.boot.module.business.pojo.facilityblood.vo.FacilityBloodUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析机联机设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilityBloodConvert {

    FacilityBloodConvert INSTANCE = Mappers.getMapper(FacilityBloodConvert.class);

    FacilityBloodDO convert(FacilityBloodCreateReqVO bean);

    FacilityBloodDO convert(FacilityBloodUpdateReqVO bean);

    FacilityBloodRespVO convert(FacilityBloodDO bean);

    List<FacilityBloodRespVO> convertList(List<FacilityBloodDO> list);

    PageResult<FacilityBloodRespVO> convertPage(PageResult<FacilityBloodDO> page);


}
