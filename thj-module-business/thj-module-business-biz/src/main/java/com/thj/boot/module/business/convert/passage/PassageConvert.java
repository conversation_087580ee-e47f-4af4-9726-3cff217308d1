package com.thj.boot.module.business.convert.passage;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.passage.PassageDO;
import com.thj.boot.module.business.pojo.passage.vo.PassageCreateReqVO;
import com.thj.boot.module.business.pojo.passage.vo.PassageRespVO;
import com.thj.boot.module.business.pojo.passage.vo.PassageUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 通路评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PassageConvert {

    PassageConvert INSTANCE = Mappers.getMapper(PassageConvert.class);

    PassageDO convert(PassageCreateReqVO bean);

    PassageDO convert(PassageUpdateReqVO bean);

    PassageRespVO convert(PassageDO bean);

    List<PassageRespVO> convertList(List<PassageDO> list);

    PageResult<PassageRespVO> convertPage(PageResult<PassageDO> page);


}
