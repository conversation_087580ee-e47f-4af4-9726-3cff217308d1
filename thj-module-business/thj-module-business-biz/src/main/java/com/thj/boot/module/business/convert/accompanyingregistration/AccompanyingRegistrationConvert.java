package com.thj.boot.module.business.convert.accompanyingregistration;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.accompanyingregistration.AccompanyingRegistrationDO;
import com.thj.boot.module.business.pojo.accompanyingregistration.vo.AccompanyingRegistrationCreateReqVO;
import com.thj.boot.module.business.pojo.accompanyingregistration.vo.AccompanyingRegistrationRespVO;
import com.thj.boot.module.business.pojo.accompanyingregistration.vo.AccompanyingRegistrationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 患者及陪护预检筛查登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AccompanyingRegistrationConvert {

    AccompanyingRegistrationConvert INSTANCE = Mappers.getMapper(AccompanyingRegistrationConvert.class);

    AccompanyingRegistrationDO convert(AccompanyingRegistrationCreateReqVO bean);

    AccompanyingRegistrationDO convert(AccompanyingRegistrationUpdateReqVO bean);

    AccompanyingRegistrationRespVO convert(AccompanyingRegistrationDO bean);

    List<AccompanyingRegistrationRespVO> convertList(List<AccompanyingRegistrationDO> list);

    PageResult<AccompanyingRegistrationRespVO> convertPage(PageResult<AccompanyingRegistrationDO> page);


}
