package com.thj.boot.module.business.pojo.roomairmonitoring.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
* 血透室空气物表监测登记 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RoomAirMonitoringBaseVO extends BaseDO {

    /**
     * 序号
     */
    private Long id;
    /**
     * 登记日期
     */
    private Date registrationDate;
    /**
     * 空气培养≤4cfu（5min*9cm 直径平皿）
     */
    private String airCulture4cfu;
    /**
     * 工作人员手≤10cfu/cm²
     */
    private String staffHand;
    /**
     * 物表≤10cfu/cm²
     */
    private String surface;
    /**
     * 达标率（%）
     */
    private String complianceRate;
    /**
     * 登记人
     */
    private String registrant;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 登记人id
     */
    private Long registrantId;

}
