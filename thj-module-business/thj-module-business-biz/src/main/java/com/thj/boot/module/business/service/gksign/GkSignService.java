package com.thj.boot.module.business.service.gksign;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gksign.GkSignDO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignCreateReqVO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignPageReqVO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 感控培训签到 Service 接口
 *
 * <AUTHOR>
 */
public interface GkSignService {

    /**
     * 创建感控培训签到
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGkSign(GkSignCreateReqVO createReqVO);

    /**
     * 更新感控培训签到
     *
     * @param updateReqVO 更新信息
     */
    void updateGkSign(GkSignUpdateReqVO updateReqVO);

    /**
     * 删除感控培训签到
     *
     * @param id 编号
     */
    void deleteGkSign(Long id);

    /**
     * 获得感控培训签到
     *
     * @param id 编号
     * @return 感控培训签到
     */
    GkSignDO getGkSign(Long id);

    /**
     * 获得感控培训签到列表
     *
     * @param ids 编号
     * @return 感控培训签到列表
     */
    List<GkSignDO> getGkSignList(Collection<Long> ids);

    /**
     * 获得感控培训签到分页
     *
     * @param pageReqVO 分页查询
     * @return 感控培训签到分页
     */
    PageResult<GkSignDO> getGkSignPage(GkSignPageReqVO pageReqVO);


}
