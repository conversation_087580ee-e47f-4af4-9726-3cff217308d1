package com.thj.boot.module.business.pojo.unitimprovement.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
* 血液透析室持续质量改进书 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UnitImprovementBaseVO extends BaseDO {
    private Long id;
    /**
     * 改进项目
     */
    private String improvementProject;
    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    /**
     * 负责人
     */
    private String responsiblePerson;
    /**
     * 参与人员
     */
    private String participants;
    /**
     * 问题
     */
    private String issue;
    /**
     * 原因分析
     */
    private String causeAnalysis;
    /**
     * 预期目标
     */
    private String expectedGoal;
    /**
     * 改进措施
     */
    private String improvementMeasures;
    /**
     * 改进效果
     */
    private String improvementEffect;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 负责人id
     */
    private Long responsiblePersonId;


}
