package com.thj.boot.module.business.dal.datado.drug;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 药品 DO
 *
 * <AUTHOR>
 */
@TableName("wl_drug")
@KeySequence("wl_drug_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrugDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 药品名称
     */
    private String name;
    /**
     * 拼音
     */
    private String nickName;
    /**
     * 物品编码
     */
    private String itemCode;
    /**
     * 医保编码
     */
    private String baoCode;
    /**
     * 剂型
     */
    private String dose;
    /**
     * 剂量单位
     */
    private String doseUnit;
    /**
     * 包装类型
     */
    private String packType;
    /**
     * 最小包装数量
     */
    private String minPack;
    /**
     * 最小制剂单位
     */
    private String minDose;
    /**
     * 入库包装单位
     */
    private String inUnit;
    /**
     * 拆分规格
     */
    private String partSpecOne;
    /**
     * 拆分规格2
     */
    private String partSpecTwo;
    /**
     * 拆分规格3
     */
    private String partSpecThree;
    /**
     * 规格
     */
    private String spec;
    /**
     * 给药途径
     */
    private String way;
    /**
     * 执行频率
     */
    private String frequency;
    /**
     * 单次用量
     */
    private String once;
    /**
     * 进货价
     */
    private BigDecimal price;
    /**
     * 价格类别
     */
    private String priceType;
    /**
     * 加成比例
     */
    private String scale;
    /**
     * 零售价
     */
    private String retail;
    /**
     * 生产厂家
     */
    private String vender;
    /**
     * 库存警告数
     */
    private String repertory;
    /**
     * 有效期预警天数
     */
    private String valid;
    /**
     * 状态：0-开启，1-关闭
     */
    private String status;
    /**
     * 分类标题
     */
    private String category;
    /**
     * 分类详情
     */
    private String categoryInfo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 药品分类单级
     */
    private Long drugTypeId;
    /**
     * 药品分类多级
     */
    private String drugTypeIds;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 类型0-自动，1-手动
     */
    private Integer genres;
}
