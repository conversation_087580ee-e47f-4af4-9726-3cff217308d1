package com.thj.boot.module.business.convert.mottoadvice;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.mottoadvice.MottoAdviceDO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医嘱摸模板 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MottoAdviceConvert {

    MottoAdviceConvert INSTANCE = Mappers.getMapper(MottoAdviceConvert.class);

    MottoAdviceDO convert(MottoAdviceCreateReqVO bean);

    MottoAdviceDO convert(MottoAdviceUpdateReqVO bean);

    MottoAdviceRespVO convert(MottoAdviceDO bean);

    List<MottoAdviceRespVO> convertList(List<MottoAdviceDO> list);

    PageResult<MottoAdviceRespVO> convertPage(PageResult<MottoAdviceDO> page);


}
