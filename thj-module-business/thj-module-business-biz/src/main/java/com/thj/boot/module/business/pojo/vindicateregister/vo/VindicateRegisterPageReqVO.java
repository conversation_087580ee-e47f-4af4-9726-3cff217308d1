package com.thj.boot.module.business.pojo.vindicateregister.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VindicateRegisterPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 设备id
     */
    private Long managerId;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 设备配置齐全
     */
    private String completeState;
    /**
     * 接通电源
     */
    private String powerState;
    /**
     * 面板显示
     */
    private String panelState;
    /**
     * 各种操作开关
     */
    private String variousState;
    /**
     * 表面除尘处理
     */
    private String surfaceState;
    /**
     * 参数矫正
     */
    private String parameterState;
    /**
     * 清洗AB液杆
     */
    private String cleanState;
    /**
     * 清洗旁路接头
     */
    private String bypassState;
    /**
     * 各级压力确认
     */
    private String recognition;
    /**
     * 增压泵运行情况
     */
    private String booster;
    /**
     * 滤芯更换
     */
    private String cartridge;
    /**
     * 内部泄漏情况
     */
    private String leakage;
    /**
     * 维护日期
     */
    private Date maintenanceTime;
    /**
     * 1-透析机，2-水处理机，3-CCDS,4-CDDS
     */
    private String type;

}
