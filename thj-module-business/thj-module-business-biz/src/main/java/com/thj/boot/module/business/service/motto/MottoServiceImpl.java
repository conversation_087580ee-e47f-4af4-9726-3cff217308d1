package com.thj.boot.module.business.service.motto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.constant.Constants;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.CollectionUtils;
import com.thj.boot.module.business.convert.motto.MottoConvert;
import com.thj.boot.module.business.convert.mottosimple.MottoSimpleConvert;
import com.thj.boot.module.business.dal.datado.motto.MottoDO;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.dal.mapper.motto.MottoMapper;
import com.thj.boot.module.business.dal.mapper.mottoadvice.MottoAdviceMapper;
import com.thj.boot.module.business.dal.mapper.mottosimple.MottoSimpleMapper;
import com.thj.boot.module.business.pojo.motto.vo.MottoCreateReqVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoPageReqVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoRespVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoUpdateReqVO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleBaseVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleRespVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个性化 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MottoServiceImpl implements MottoService {

    @Resource
    private MottoMapper mottoMapper;

    @Resource
    private MottoSimpleMapper mottoSimpleMapper;

    @Resource
    private MottoAdviceMapper mottoAdviceMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Value("${file.staticPath}")
    private String staticPath;

    @CacheEvict(cacheNames = "mottoSimpleMap", key = "'motto'")
    @Override
    public Long createMotto(MottoCreateReqVO createReqVO) {
        //检查是否存在相同的名称
        checkByName(createReqVO.getTitle(), 0);
        // 插入
        MottoDO motto = MottoConvert.INSTANCE.convert(createReqVO);
        if (StrUtil.isNotEmpty(motto.getIcon())) {
            String icon = StrUtil.subAfter(motto.getIcon(), staticPath, false);
            motto.setIcon(icon);
        }
        mottoMapper.insert(motto);
        // 返回
        return motto.getId();
    }

    @Cacheable(cacheNames = "mottoSimpleMap", key = "'motto'")
    @Override
    public Map<String, String> getMottoSimpleMap() {
        MottoDO mottoDO = mottoMapper.selectOne(MottoDO::getTitle, "转归");
        return CollectionUtils.convertMap(mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, mottoDO.getId()), MottoSimpleDO::getPname, mottoSimpleDO -> String.valueOf(mottoSimpleDO.getId()));
    }

    private void checkByName(String title, Integer i) {
        Long count = mottoMapper.selectCount(MottoDO::getTitle, title);
        if (count > 0 && 0 == i) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        } else if (count > 1) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        }
    }

    @CacheEvict(cacheNames = "mottoSimpleMap", key = "'motto'")
    @Override
    public void updateMotto(MottoUpdateReqVO updateReqVO) {
        checkByName(updateReqVO.getTitle(), 1);
        // 更新
        MottoDO updateObj = MottoConvert.INSTANCE.convert(updateReqVO);
        if (StrUtil.isNotEmpty(updateObj.getIcon())) {
            String icon = StrUtil.subAfter(updateObj.getIcon(), staticPath, false);
            updateObj.setIcon(icon);
        }
        mottoMapper.updateById(updateObj);
    }

    @CacheEvict(cacheNames = "mottoSimpleMap", key = "'motto'")
    @Override
    public void deleteMotto(Long id) {
        //在删除之前检测是否存在关联|有关联不可删除
        Long count = mottoSimpleMapper.selectCount(MottoSimpleDO::getMottoId, id);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.MOTTO_JOIN);
        }
        // 删除
        mottoMapper.deleteById(id);
    }


    @Override
    public List<MottoSimpleRespVO> getMotto(MottoCreateReqVO createReqVO, HttpServletRequest request) {
        String systemDeptId = request.getHeader("Systemdeptid");
        createReqVO.setDeptId(Long.valueOf(systemDeptId));
        if (StrUtil.isNotEmpty(createReqVO.getIds())) {
            List<Long> ids = Arrays.stream(createReqVO.getIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            createReqVO.setIdList(ids);
        }
        //获取关联子级详情
        List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(createReqVO);
        List<MottoSimpleRespVO> mottoSimpleRespVOS = MottoSimpleConvert.INSTANCE.convertList(mottoSimpleDOS)
                .stream()
                .sorted((obj1, obj2) -> {
                    // 获取排序
                    Integer sort1 = obj1.getSort();
                    Integer sort2 = obj2.getSort();

                    // 没有排序的后排放
                    if (sort1 == null && sort2 == null) return 0;
                    if (sort1 == null) return 1;
                    if (sort2 == null) return -1;
                    return sort1.compareTo(sort2);
                }).collect(Collectors.toList());
        List<MottoSimpleRespVO> mottoSimpleDOList = MottoSimpleConvert.INSTANCE.convertList(mottoSimpleMapper.selectList());

        if (CollectionUtil.isNotEmpty(mottoSimpleRespVOS)) {
            if (Constants.PRIVACY.equals(createReqVO.getType())) {//转归
                //如果是多级需要加上mottoId
                List<MottoSimpleRespVO> rootList = mottoSimpleRespVOS.stream().filter(mottoSimpleRespVO -> 0 == mottoSimpleRespVO.getPid()).collect(Collectors.toList());
                List<MottoSimpleRespVO> subList = mottoSimpleRespVOS.stream().filter(mottoSimpleRespVO -> 0 != mottoSimpleRespVO.getPid()).sorted(Comparator.comparingInt(MottoSimpleBaseVO::getSort)).collect(Collectors.toList());
                rootList.forEach(root -> busort(root, subList));
                return rootList;
            }
            //通用
            mottoSimpleRespVOS = mottoSimpleRespVOS.stream().peek(mottoSimpleRespVO -> {
                List<MottoSimpleRespVO> simpleRespVOS = mottoSimpleDOList.stream().filter(item -> item.getPid() != null && item.getPid().equals(mottoSimpleRespVO.getId())).collect(Collectors.toList());
                //升序排列
                if (CollectionUtil.isNotEmpty(simpleRespVOS)) {
                    mottoSimpleRespVO.setChildren(simpleRespVOS.stream().sorted(Comparator.comparingInt(MottoSimpleBaseVO::getSort)).collect(Collectors.toList()));
                }
                List<MottoAdviceRespVO> mottoAdviceRespVOS = mottoAdviceMapper.queryList(mottoSimpleRespVO.getId(), "0");

                List<MottoAdviceRespVO> mottoAdviceRespVOS2 = mottoAdviceMapper.queryConsumeList(mottoSimpleRespVO.getId());

                List<MottoAdviceRespVO> mottoAdviceRespVOS3 = mottoAdviceMapper.queryComboList(mottoSimpleRespVO.getId());

                List<MottoAdviceRespVO> infoList = mottoAdviceMapper.queryInfoList(mottoSimpleRespVO.getId());
                mottoSimpleRespVO.setNumber(mottoAdviceRespVOS.size() + mottoAdviceRespVOS2.size() +mottoAdviceRespVOS3.size() + infoList.size() );

            }).collect(Collectors.toList());
        }
        return mottoSimpleRespVOS;
    }

    private void busort(MottoSimpleRespVO root, List<MottoSimpleRespVO> subList) {
        List<MottoSimpleRespVO> childrenList = subList.stream().filter(mottoSimpleRespVO -> mottoSimpleRespVO.getPid().equals(Long.valueOf(root.getId()))).sorted((a, b) -> a.getSort() - b.getSort()).collect(Collectors.toList());
        if (!CollUtil.isEmpty(childrenList)) {
            root.setChildren(childrenList);
            childrenList.forEach(sysMenu -> busort(sysMenu, subList));
        } else {
            root.setChildren(null);
        }
    }


    @Override
    public PageResult<MottoDO> getMottoPage(MottoPageReqVO pageReqVO) {
        PageResult<MottoDO> mottoDOPageResult = mottoMapper.selectPage(pageReqVO);
        if (CollectionUtil.isNotEmpty(mottoDOPageResult.getList())) {
            List<MottoDO> collect = mottoDOPageResult.getList().stream().peek(mottoDO -> {
                if (StrUtil.isNotEmpty(mottoDO.getIcon())) {
                    mottoDO.setIcon(staticPath + mottoDO.getIcon());
                }
            }).collect(Collectors.toList());
            mottoDOPageResult.setList(collect);
        }

        return mottoDOPageResult;
    }

    @Override
    public List<MottoRespVO> getMottoList(MottoCreateReqVO createReqVO) {
        List<MottoDO> mottoDOS = mottoMapper.selectList(createReqVO);
        List<MottoRespVO> mottoRespVOS = MottoConvert.INSTANCE.convertList(mottoDOS);
        if (CollectionUtil.isNotEmpty(mottoRespVOS)) {
            mottoRespVOS = mottoRespVOS.stream().peek(mottoRespVO -> {
                if (StrUtil.isNotEmpty(mottoRespVO.getIcon())) {
                    mottoRespVO.setIcon(staticPath + mottoRespVO.getIcon());
                }
                //通用标签名称
                List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, mottoRespVO.getId(), MottoSimpleDO::getPid, 0);
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                    mottoRespVO.setPnames(mottoSimpleDOS.stream().map(MottoSimpleDO::getPname).collect(Collectors.toList()));
                }
                //透析方案字典获取
                if (2 == mottoRespVO.getId()) {
                    List<DictDataRespDTO> dialyzeWay = dictDataApi.getDictListData("dialyze_way");
                    if (CollectionUtil.isNotEmpty(dialyzeWay)) {
                        mottoRespVO.setPnames(dialyzeWay.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList()));
                    }
                }
            }).collect(Collectors.toList());
            return mottoRespVOS;
        }
        return mottoRespVOS;
    }

    @Override
    public MottoRespVO getMottoInfo(MottoCreateReqVO createReqVO) {
        MottoDO mottoDO = mottoMapper.selectById(createReqVO.getId());
        if (mottoDO != null && StrUtil.isNotEmpty(mottoDO.getIcon())) {
            mottoDO.setIcon(staticPath + mottoDO.getIcon());
        }
        return MottoConvert.INSTANCE.convert(mottoDO);
    }


}
