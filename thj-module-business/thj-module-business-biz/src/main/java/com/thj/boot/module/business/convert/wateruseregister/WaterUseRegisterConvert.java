package com.thj.boot.module.business.convert.wateruseregister;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.wateruseregister.WaterUseRegisterDO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterRespVO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 水处理机使用登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WaterUseRegisterConvert {

    WaterUseRegisterConvert INSTANCE = Mappers.getMapper(WaterUseRegisterConvert.class);

    WaterUseRegisterDO convert(WaterUseRegisterCreateReqVO bean);

    WaterUseRegisterDO convert(WaterUseRegisterUpdateReqVO bean);

    WaterUseRegisterRespVO convert(WaterUseRegisterDO bean);

    List<WaterUseRegisterRespVO> convertList(List<WaterUseRegisterDO> list);

    PageResult<WaterUseRegisterRespVO> convertPage(PageResult<WaterUseRegisterDO> page);


}
