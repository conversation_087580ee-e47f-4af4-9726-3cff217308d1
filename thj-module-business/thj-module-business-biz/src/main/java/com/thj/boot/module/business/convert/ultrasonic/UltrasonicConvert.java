package com.thj.boot.module.business.convert.ultrasonic;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.ultrasonic.UltrasonicDO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicCreateReqVO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicRespVO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 超声检查 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UltrasonicConvert {

    UltrasonicConvert INSTANCE = Mappers.getMapper(UltrasonicConvert.class);

    UltrasonicDO convert(UltrasonicCreateReqVO bean);

    UltrasonicDO convert(UltrasonicUpdateReqVO bean);

    UltrasonicRespVO convert(UltrasonicDO bean);

    List<UltrasonicRespVO> convertList(List<UltrasonicDO> list);

    PageResult<UltrasonicRespVO> convertPage(PageResult<UltrasonicDO> page);


}
