package com.thj.boot.module.business.pojo.hemodialysismonitoring.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;


/**
* 血透事件监测 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HemodialysisMonitoringBaseVO extends BaseDO {

    /**
     * 序号
     */
    @TableId
    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查人
     */
    private String inspectedPerson;
    /**
     * 血管通路
     */
    private String vascularAccess;
    /**
     * 静脉使用抗生素（必填，对/错）
     */
    private String antibiotics;
    /**
     * 血培养阳性（必填，对/错）
     */
    private String positiveBloodCulture;
    /**
     * 伤口有脓液/红肿（必填，对/错）
     */
    private String purulentDischarge;
    /**
     * T≥38℃（必填，对/错）
     */
    private String celsius;
    /**
     * 寒颤高热（必填，对/错）
     */
    private String rigorsHighFever;
    /**
     * 血压下降（必填，对/错）
     */
    private String bloodPressureDrop;
    /**
     * 蜂窝组织炎（必填，对/错）
     */
    private String cellulitis;
    /**
     * 肺炎/呼吸道感染（必填，对/错）
     */
    private String pneumoniaRespiratoryInfection;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 最近一次操作人
     */
    private String lastOperator;
    /**
     * 评估人
     */
    private String evaluator;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;
    /**
     * 评估人id
     */
    private Long evaluatorId;

}
