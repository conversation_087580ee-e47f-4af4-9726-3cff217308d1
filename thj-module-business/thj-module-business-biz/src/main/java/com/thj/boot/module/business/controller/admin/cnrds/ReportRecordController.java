package com.thj.boot.module.business.controller.admin.cnrds;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.thj.boot.common.dataobject.BaseDO;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.controller.admin.cnrds.vo.ReportRecordPageReqVO;
import com.thj.boot.module.business.controller.admin.cnrds.vo.ReportRecordQueryVO;
import com.thj.boot.module.business.convert.courserecord.CourseRecordConvert;
import com.thj.boot.module.business.dal.datado.cnrds.CnrdsReportRecordDO;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.dal.mapper.cnrds.CnrdsReportRecordMapper;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordRespVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 药品
 */
@RestController
@RequestMapping("/business/reportRecord")
@Validated
public class ReportRecordController {

    @Resource
    private CnrdsReportRecordMapper cnrdsReportRecordMapper;

    @PostMapping("/getRecordListPage")
    public CommonResult<PageResult<CnrdsReportRecordDO>> getRecordList(@RequestBody ReportRecordPageReqVO reqVO, HttpServletRequest request){
        String systemdeptid = request.getHeader("systemdeptid");
        QueryWrapper<CnrdsReportRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotEmpty(systemdeptid) && Integer.valueOf(systemdeptid)  > 0, BaseDO::getDeptId, systemdeptid )
                .like(StringUtils.isNotEmpty(reqVO.getPatientName()), CnrdsReportRecordDO::getPatientName, reqVO.getPatientName())
                .ge(reqVO.getStartTime() != null, BaseDO::getCreateTime, reqVO.getStartTime())
                .le(reqVO.getEndTime() != null, BaseDO::getCreateTime, reqVO.getEndTime())
                .orderByDesc(CnrdsReportRecordDO::getId);
        return success(cnrdsReportRecordMapper.selectPage(reqVO, queryWrapper));
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<CnrdsReportRecordDO> getRecord(@RequestParam("id") Long id) {
      return success(cnrdsReportRecordMapper.selectById(id));
    }








}
