package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 枸橼酸钠对象
 */
@Data
public class AnticoCitrate {

    /**
     * 浓度选择
     */
    @JsonProperty("ANTICO_CITRATE_CONCENTRATION")
    private String citrateConcentration;

    /**
     * 其它浓度
     */
    @JsonProperty("ANTICO_CITRATE_OTHER")
    private String citrateOther;

    /**
     * 速率
     */
    @JsonProperty("ANTICO_CITRATE_RATE")
    private String citrateRate;

    /**
     * 使用时间（小时）
     */
    @JsonProperty("ANTICO_CITRATE_HR")
    private String citrateHr;

    /**
     * 使用时间（分钟）
     */
    @JsonProperty("ANTICO_CITRATE_MIN")
    private String citrateMin;

}
