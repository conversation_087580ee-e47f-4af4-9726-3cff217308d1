package com.thj.boot.module.business.pojo.roomairmonitoring.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RoomAirMonitoringPageReqVO extends PageParam {

    /**
     * 序号
     */
    private Long id;
    /**
     * 登记日期
     */
    private Date registrationDate;
    /**
     * 空气培养≤4cfu（5min*9cm 直径平皿）
     */
    private String airCulture4cfu;
    /**
     * 工作人员手≤10cfu/cm²
     */
    private String staffHand;
    /**
     * 物表≤10cfu/cm²
     */
    private String surface;
    /**
     * 达标率（%）
     */
    private String complianceRate;
    /**
     * 登记人
     */
    private String registrant;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 登记人id
     */
    private Long registrantId;

}
