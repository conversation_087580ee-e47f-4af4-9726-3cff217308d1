package com.thj.boot.module.business.convert.catheterization;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.catheterization.CatheterizationDO;
import com.thj.boot.module.business.pojo.catheterization.vo.CatheterizationCreateReqVO;
import com.thj.boot.module.business.pojo.catheterization.vo.CatheterizationRespVO;
import com.thj.boot.module.business.pojo.catheterization.vo.CatheterizationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析导管断开操作 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CatheterizationConvert {

    CatheterizationConvert INSTANCE = Mappers.getMapper(CatheterizationConvert.class);

    CatheterizationDO convert(CatheterizationCreateReqVO bean);

    CatheterizationDO convert(CatheterizationUpdateReqVO bean);

    CatheterizationRespVO convert(CatheterizationDO bean);

    List<CatheterizationRespVO> convertList(List<CatheterizationDO> list);

    PageResult<CatheterizationRespVO> convertPage(PageResult<CatheterizationDO> page);


}
