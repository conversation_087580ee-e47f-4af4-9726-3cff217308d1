package com.thj.boot.module.business.pojo.departmentControl;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PreChartResp<T> extends BaseChartResp<T> {

    private Integer type = 2;

    public PreChartResp(List<T> values, String unit) {
        super(values);
        setUnit(unit);
    }
    public PreChartResp(List<T> values, List<Double> rates, String unit) {
        super(values);
        setUnit(unit);
        setRates(rates);
    }
    public PreChartResp(List<T> values, Statistics<?> statistics, String unit) {
        super(values, statistics);
        setUnit(unit);
    }


}

