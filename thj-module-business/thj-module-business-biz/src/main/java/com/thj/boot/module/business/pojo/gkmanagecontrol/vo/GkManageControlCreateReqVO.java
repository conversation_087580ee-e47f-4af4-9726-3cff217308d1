package com.thj.boot.module.business.pojo.gkmanagecontrol.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GkManageControlCreateReqVO extends GkManageControlBaseVO {

    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 扣分
     */
    private Integer deductPoints;
    /**
     * 问题
     */
    private String problem;
    /**
     * 措施
     */
    private String measure;
    /**
     * 结果
     */
    private String result;
    /**
     * 验收日期
     */
    private String acceptTime;
    /**
     * 验收人ID
     */
    private Long inspectId;
    /**
     * 验收人
     */
    private String inspectName;

    private Integer year;

}
