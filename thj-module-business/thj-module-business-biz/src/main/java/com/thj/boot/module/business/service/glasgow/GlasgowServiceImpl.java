package com.thj.boot.module.business.service.glasgow;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.glasgow.GlasgowConvert;
import com.thj.boot.module.business.dal.datado.glasgow.GlasgowDO;
import com.thj.boot.module.business.dal.mapper.glasgow.GlasgowMapper;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowCreateReqVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowPageReqVO;
import com.thj.boot.module.business.pojo.glasgow.vo.GlasgowUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * Glasgow昏迷评分量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GlasgowServiceImpl implements GlasgowService {

    @Resource
    private GlasgowMapper glasgowMapper;

    @Override
    public Long createGlasgow(GlasgowCreateReqVO createReqVO) {
        // 插入
        GlasgowDO glasgow = GlasgowConvert.INSTANCE.convert(createReqVO);
        glasgowMapper.insert(glasgow);
        // 返回
        return glasgow.getId();
    }

    @Override
    public void updateGlasgow(GlasgowUpdateReqVO updateReqVO) {
        // 更新
        GlasgowDO updateObj = GlasgowConvert.INSTANCE.convert(updateReqVO);
        glasgowMapper.updateById(updateObj);
    }

    @Override
    public void deleteGlasgow(Long id) {
        // 删除
        glasgowMapper.deleteById(id);
    }


    @Override
    public GlasgowDO getGlasgow(Long id) {
        return glasgowMapper.selectById(id);
    }

    @Override
    public List<GlasgowDO> getGlasgowList(Collection<Long> ids) {
        return glasgowMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GlasgowDO> getGlasgowPage(GlasgowPageReqVO pageReqVO) {
        return glasgowMapper.selectPage(pageReqVO);
    }

    @Override
    public List<GlasgowDO> getGlasgowList(GlasgowCreateReqVO createReqVO) {
        return glasgowMapper.selectList(createReqVO);
    }

}
