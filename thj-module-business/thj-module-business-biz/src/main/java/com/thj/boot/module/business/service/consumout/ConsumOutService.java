package com.thj.boot.module.business.service.consumout;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.consumout.ConsumOutDO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutExportReqVO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutPageReqVO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutUpdateReqVO;

import java.util.List;

/**
 * 耗材出库记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsumOutService {

    /**
     * 创建耗材出库记录
     *
     * @param consumOut 创建信息
     * @return 编号
     */
    Long createConsumOut(ConsumOutDO consumOut);

    /**
     * 更新耗材出库记录
     *
     * @param updateReqVO 更新信息
     */
    void updateConsumOut(ConsumOutUpdateReqVO updateReqVO);

    /**
     * 删除耗材出库记录
     *
     * @param id 编号
     */
    void deleteConsumOut(Long id);

    /**
     * 获得耗材出库记录
     *
     * @param id 编号
     * @return 耗材出库记录
     */
    ConsumOutDO getConsumOut(Long id);

    /**
     * 获得耗材出库记录列表
     *
     * @param patientId 编号
     * @return 耗材出库记录列表
     */
    List<ConsumOutDO> getConsumOutList(Long patientId);

    /**
     * 获得耗材出库记录分页
     *
     * @param pageReqVO 分页查询
     * @return 耗材出库记录分页
     */
    PageResult<ConsumOutDO> getConsumOutPage(ConsumOutPageReqVO pageReqVO);

    /**
     * 获得耗材出库记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 耗材出库记录列表
     */
    List<ConsumOutDO> getConsumOutList(ConsumOutExportReqVO exportReqVO);

}
