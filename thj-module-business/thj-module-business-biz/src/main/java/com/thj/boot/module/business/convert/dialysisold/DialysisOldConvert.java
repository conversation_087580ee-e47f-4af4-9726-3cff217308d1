package com.thj.boot.module.business.convert.dialysisold;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dialysisold.DialysisOldDO;
import com.thj.boot.module.business.pojo.dialysisold.vo.DialysisOldCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisold.vo.DialysisOldRespVO;
import com.thj.boot.module.business.pojo.dialysisold.vo.DialysisOldUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析室手卫生检查 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisOldConvert {

    DialysisOldConvert INSTANCE = Mappers.getMapper(DialysisOldConvert.class);

    DialysisOldDO convert(DialysisOldCreateReqVO bean);

    DialysisOldDO convert(DialysisOldUpdateReqVO bean);

    DialysisOldRespVO convert(DialysisOldDO bean);

    List<DialysisOldRespVO> convertList(List<DialysisOldDO> list);

    PageResult<DialysisOldRespVO> convertPage(PageResult<DialysisOldDO> page);

    List<DialysisOldCreateReqVO> convertList02(List<DialysisOldDO> list);

}
