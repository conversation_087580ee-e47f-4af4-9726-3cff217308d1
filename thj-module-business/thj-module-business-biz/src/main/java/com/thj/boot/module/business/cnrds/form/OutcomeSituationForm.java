package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 转归情况表单
 */
@Data
public class OutcomeSituationForm {

    /**
     * 患者状态
     */
    @JsonProperty("OUTCOME_STATUS_LATEST")
    private String outcomeStatusLatest;

    /**
     * 透析状态
     */
    @JsonProperty("OUTCOME_LIST")
    private List<Outcome> outcomeList;
}
