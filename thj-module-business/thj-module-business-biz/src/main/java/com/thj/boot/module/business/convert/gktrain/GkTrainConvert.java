package com.thj.boot.module.business.convert.gktrain;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gktrain.GkTrainDO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainCreateReqVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainRespVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 感控培训 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkTrainConvert {

    GkTrainConvert INSTANCE = Mappers.getMapper(GkTrainConvert.class);

    GkTrainDO convert(GkTrainCreateReqVO bean);

    GkTrainDO convert(GkTrainUpdateReqVO bean);

    GkTrainRespVO convert(GkTrainDO bean);

    List<GkTrainRespVO> convertList(List<GkTrainDO> list);

    PageResult<GkTrainRespVO> convertPage(PageResult<GkTrainDO> page);


}
