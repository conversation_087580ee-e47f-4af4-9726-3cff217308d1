package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ESA 表单
 */
@Data
public class EsaForm {

    // 年
    @JsonProperty("ESA_YEAR")
    @NotNull
    private String esaYear;

    // 时间维度
    @JsonProperty("ESA_DIMENSION")
    @NotNull
    private String esaDimension;

    // 有无变化
    @JsonProperty("ESA_CHANGE_YN")
    @NotNull
    private String esaChangeYN;

    // 是否使用
    @JsonProperty("ESA_TREATMENT")
    @NotNull
    private String esaTreatment;

    // 制剂类型
    @JsonProperty("ESA_TYPE")
    @NotNull
    private String esaType;

    // 生产商
    @JsonProperty("ESA_MANUFACTURER")
    private String esaManufacturer;

    // 用药方式
    @JsonProperty("ESA_ADMIN")
    private String esaAdmin;

    // 是否为首次
    @JsonProperty("ESA_FIRST_YN")
    private Boolean esaFirstYN;

    // 剂量
    @JsonProperty("ESA_DOSE")
    private EsaDose esaDose;

}
