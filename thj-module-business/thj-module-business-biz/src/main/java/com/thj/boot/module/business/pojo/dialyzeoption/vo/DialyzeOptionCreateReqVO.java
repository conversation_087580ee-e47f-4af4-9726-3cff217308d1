package com.thj.boot.module.business.pojo.dialyzeoption.vo;

import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictCreateReqVO;
import com.thj.boot.module.business.pojo.contradictAdvice.vo.ContradictAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolCreateReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialyzeOptionCreateReqVO extends DialyzeOptionBaseVO {

    /**
     * 多个患者id
     */
    private String patientIds;
    /**
     * 子级
     */
    private List<DialyzeOptionDO> children;
    /**
     * 多个透析模式
     */
    private List<DialyzeOptionCreateReqVO> dialyzeOptionCreateReqVOS;
    /**
     * 透析方案右侧详情
     */
    private DialysisProtocolCreateReqVO contents;
    /**
     * 抗凝剂类型
     */
    private List<ContradictCreateReqVO> contradictDOS;
    /**
     * 抗凝剂医嘱名称
     */
    private List<ContradictAdviceCreateReqVO> contradictAdviceDOS;
}
