package com.thj.boot.module.business.convert.arrangeclasses;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.arrangeclasses.vo.DialyzeBudgeExeclVO;
import com.thj.boot.module.business.controller.admin.drainteam.vo.DialyzBudgetRespVO;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 新排班 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ArrangeClassesConvert {

    ArrangeClassesConvert INSTANCE = Mappers.getMapper(ArrangeClassesConvert.class);

    ArrangeClassesDO convert(ArrangeClassesCreateReqVO bean);

    ArrangeClassesDO convert(ArrangeClassesUpdateReqVO bean);

    ArrangeClassesRespVO convert(ArrangeClassesDO bean);

    List<ArrangeClassesRespVO> convertList(List<ArrangeClassesDO> list);

    PageResult<ArrangeClassesRespVO> convertPage(PageResult<ArrangeClassesDO> page);


    List<DialyzeBudgeExeclVO> convertListExcel(List<DialyzBudgetRespVO> dialyzBudgetRespVOS);
}
