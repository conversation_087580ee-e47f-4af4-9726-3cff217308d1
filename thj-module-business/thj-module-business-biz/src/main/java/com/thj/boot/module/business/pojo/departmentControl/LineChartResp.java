package com.thj.boot.module.business.pojo.departmentControl;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class LineChartResp<T> extends BaseChartResp<T>{

    private List<String> xAxisDafaList;

    public LineChartResp(List<T> values, List<String> xAxisDafaList) {
        super(values);
        this.xAxisDafaList = xAxisDafaList;
    }
}
