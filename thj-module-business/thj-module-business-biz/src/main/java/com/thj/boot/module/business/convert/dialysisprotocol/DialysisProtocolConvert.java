package com.thj.boot.module.business.convert.dialysisprotocol;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolRespVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 个性化-透析方案 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisProtocolConvert {

    DialysisProtocolConvert INSTANCE = Mappers.getMapper(DialysisProtocolConvert.class);

    DialysisProtocolDO convert(DialysisProtocolCreateReqVO bean);

    DialysisProtocolDO convert(DialysisProtocolUpdateReqVO bean);

    DialysisProtocolRespVO convert(DialysisProtocolDO bean);

    List<DialysisProtocolRespVO> convertList(List<DialysisProtocolDO> list);

    PageResult<DialysisProtocolRespVO> convertPage(PageResult<DialysisProtocolDO> page);


}
