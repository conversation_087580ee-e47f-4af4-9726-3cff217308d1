package com.thj.boot.module.business.pojo.dialysisrecord.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisRecordCreateReqVO extends DialysisRecordBaseVO {
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 透析日期 开始
     */
    private Date dateWeekStart;

    /**
     * 透析日期 结束
     */
    private Date dateWeekEnd;
}
