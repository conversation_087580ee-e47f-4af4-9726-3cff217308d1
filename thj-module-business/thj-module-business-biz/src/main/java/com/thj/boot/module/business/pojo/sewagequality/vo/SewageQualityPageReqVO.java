package com.thj.boot.module.business.pojo.sewagequality.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SewageQualityPageReqVO extends PageParam {

    /**
     * 污水质量检测主键id
     */
    private Long id;
    /**
     * 登记日期
     */
    private Date startTime;
    /**
     * 类大肠菌
     */
    private String coliform;
    /**
     * cod
     */
    private String cod;
    /**
     * bod
     */
    private String bod;
    /**
     * ss
     */
    private String ss;
    /**
     * 沙门氏菌
     */
    private String salmonella;
    /**
     * 志贺氏菌
     */
    private String shigella;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 设备管理id
     */
    private Long managerId;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 备注
     */
    private String remark;

}
