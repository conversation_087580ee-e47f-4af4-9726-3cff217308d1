package com.thj.boot.module.business.service.gkinroles;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkinroles.GkInRolesDO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesCreateReqVO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesPageReqVO;
import com.thj.boot.module.business.pojo.gkinroles.vo.GkInRolesUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 感控培训参与角色关联 Service 接口
 *
 * <AUTHOR>
 */
public interface GkInRolesService {

    /**
     * 创建感控培训参与角色关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGkInRoles(GkInRolesCreateReqVO createReqVO);

    /**
     * 更新感控培训参与角色关联
     *
     * @param updateReqVO 更新信息
     */
    void updateGkInRoles(GkInRolesUpdateReqVO updateReqVO);

    /**
     * 删除感控培训参与角色关联
     *
     * @param id 编号
     */
    void deleteGkInRoles(Long id);

    /**
     * 获得感控培训参与角色关联
     *
     * @param id 编号
     * @return 感控培训参与角色关联
     */
    GkInRolesDO getGkInRoles(Long id);

    /**
     * 获得感控培训参与角色关联列表
     *
     * @param ids 编号
     * @return 感控培训参与角色关联列表
     */
    List<GkInRolesDO> getGkInRolesList(Collection<Long> ids);

    /**
     * 获得感控培训参与角色关联分页
     *
     * @param pageReqVO 分页查询
     * @return 感控培训参与角色关联分页
     */
    PageResult<GkInRolesDO> getGkInRolesPage(GkInRolesPageReqVO pageReqVO);


}
