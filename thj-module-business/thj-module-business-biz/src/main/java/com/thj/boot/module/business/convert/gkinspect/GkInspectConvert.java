package com.thj.boot.module.business.convert.gkinspect;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkinspect.GkInspectDO;
import com.thj.boot.module.business.pojo.gkinspect.vo.GkInspectCreateReqVO;
import com.thj.boot.module.business.pojo.gkinspect.vo.GkInspectRespVO;
import com.thj.boot.module.business.pojo.gkinspect.vo.GkInspectUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 感控检查 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkInspectConvert {

    GkInspectConvert INSTANCE = Mappers.getMapper(GkInspectConvert.class);

    GkInspectDO convert(GkInspectCreateReqVO bean);

    GkInspectDO convert(GkInspectUpdateReqVO bean);

    GkInspectRespVO convert(GkInspectDO bean);

    List<GkInspectRespVO> convertList(List<GkInspectDO> list);

    PageResult<GkInspectRespVO> convertPage(PageResult<GkInspectDO> page);


}
