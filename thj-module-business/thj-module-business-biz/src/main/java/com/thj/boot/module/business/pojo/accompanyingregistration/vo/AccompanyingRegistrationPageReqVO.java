package com.thj.boot.module.business.pojo.accompanyingregistration.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccompanyingRegistrationPageReqVO extends PageParam {

    /**
     * 序号
     */
    private Long id;
    /**
     * 日期
     */
    private Date date;
    /**
     * 班次
     */
    private String shift;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 机号
     */
    private String machineNumber;
    /**
     * 交通工具 多选，自驾/公共交通工具/步行/住院/中心车辆接送/骑行）
     */
    private String modeOfTransport;
    /**
     * 患者体温(第1次)
     */
    private String patientTemperature1st;
    /**
     * 患者体温(第2次)
     */
    private String patientTemperature2nd;
    /**
     * 患者健康码 （无/24h阴性/48h阴性/72h阴性/阴性/阳性）
     */
    private String patientHealthCode;
    /**
     * 患者行程码 （无码/绿码/黄码 /红码）
     */
    private String patientTravelCode;
    /**
     * 患者核酸检测
     */
    private String patientNucleicAcidTest;
    /**
     * 家属姓名
     */
    private String familyMemberName;
    /**
     * 家属体温
     */
    private String familyMemberTemperature;
    /**
     * 家属健康码
     */
    private String familyMemberHealthCode;
    /**
     * 家属行程码
     */
    private String familyMemberTravelCode;
    /**
     * 家属核酸检测
     */
    private String familyMemberNucleicAcidTest;
    /**
     * 联系方式
     */
    private String contactInformation;
    /**
     * 家属症状 （多选，无/发热/咳嗽/呼吸困难/嗅觉味觉减退/腹泻/乏力/鼻塞/流涕/咽痛/肌痛/结膜炎）
     */
    private String familyMemberSymptoms;
    /**
     * 防控分区
     */
    private String preventionAndControlZone;
    /**
     * 家庭住址
     */
    private String homeAddress;
    /**
     * 流行病学史 （多选，7天内未到过中高风险地区，无密切接触史，无发热、咳嗽等呼吸道症状/14天内未到过中高风险地区，无密切接触史，无发热、咳嗽等呼吸道症状/14天内有中高风险疫情地区或其他有病例报告省市以及国外旅行史或居住史/14天内与新冠病毒感染者(或核酸检测阳性者)有接触史/14内曾接触过来自中高风险疫情地区或其他有病例报告疫区及来自国外的发热或有呼吸道症状的患者/2周内家庭或办公室等场所，有出现2例及以上发热和/或呼吸道症状的病例）
     */
    private String epidemiologicalHistory;
    /**
     * 处置情况多选，按常规透析/单人单间透析/单班透析/专人专用通道引导至发热门诊/住院诊疗/转定点医院治疗/居家健康观察/集中医学隔离）
     */
    private String disposition;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 患者签名
     */
    private String patientSignature;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 患者id
     */
    private Long patientId;

}
