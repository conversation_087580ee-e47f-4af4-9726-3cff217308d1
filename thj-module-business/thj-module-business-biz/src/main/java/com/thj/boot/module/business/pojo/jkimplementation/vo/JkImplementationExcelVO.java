package com.thj.boot.module.business.pojo.jkimplementation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 健康教育-患教实施 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class JkImplementationExcelVO {

    @ExcelProperty("")
    private Long id;

    @ExcelProperty("")
    private LocalDateTime createTime;

    @ExcelProperty("患者id")
    private Long patientId;

    @ExcelProperty("患者名称")
    private String patientName;

    @ExcelProperty("透析次数")
    private Integer dialyzeTotal;

    @ExcelProperty("透析号")
    private String dialyzeNo;

    @ExcelProperty("教育次数")
    private Integer cunt;

    @ExcelProperty("今日排版")
    private String dayScheduling;

    @ExcelProperty("透析机号")
    private String machineNum;

    @ExcelProperty("")
    private Long nurseId;

    @ExcelProperty("")
    private String nurseName;

    @ExcelProperty("最后教育时间")
    private String lastTime;

    @ExcelProperty("年月")
    private String createMouth;

    @ExcelProperty("教育情况: 0 未教育,1已教育")
    private String state;

    @ExcelProperty("微信绑定")
    private String binding;

    @ExcelProperty("病区")
    private String ward;

    @ExcelProperty("拼音")
    private String pinyin;

}
