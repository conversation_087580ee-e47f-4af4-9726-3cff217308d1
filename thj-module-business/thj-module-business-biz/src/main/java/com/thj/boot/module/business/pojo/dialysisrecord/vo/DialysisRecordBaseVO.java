package com.thj.boot.module.business.pojo.dialysisrecord.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
* 透析记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DialysisRecordBaseVO extends BaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 透析日期
     */
    @ExcelProperty(value = "透析日期",format = DatePattern.NORM_DATE_PATTERN)
    private Date dateWeek;
    /**
     * 班次
     */
    @ExcelProperty("班次")
    private String weekDay;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 机号名称
     */
    private String facilityName;
    /**
     * 分区名称
     */
    private String facilitySubareaName;
    /**
     * 分区+机号
     */
    @ExcelProperty("分区-机号")
    private String areaCode;
    /**
     * 透析模式名称
     */
    @ExcelProperty("透析模式")
    private String dialyzeWayValue;
    /**
     * 透析模式id
     */
    private String dialyzeDictValue;
    /**
     * 透析时长
     */
    private String duration;
    /**
     * 干体重
     */
    @ExcelProperty("干体重")
    private String dryWeight;
    /**
     * 透前体重
     */
    @ExcelProperty("透前体重")
    private String dialyzeBeforeWeight;
    /**
     * 透后体重
     */
    @ExcelProperty("透后体重")
    private String dialyzeAfterWeigh;
    /**
     * 超滤总量
     */
    @ExcelProperty("超滤总量")
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量
     */
    @ExcelProperty("实际超滤量")
    private String actualUltrafiltrationCapacity;
    /**
     * 结束透析时间
     */
    @ExcelProperty("结束透析时间")
    private Date endDialyzeTime;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 患者拼音
     */
    private String spellName;
    /**
     * 实际治疗时长
     */
    private String treatment;
    /**
     * 目标脱水量
     */
    private String targetDehydratedLevel;
    /**
     * 脉搏透前
     */
    private String beforePNo;
    /**
     * 脉搏透后
     */
    private String afterPNo;
    /**
     * 透前血压
     */
    private String beforeBpNoOne;
    /**
     * 透前血压
     */
    private String beforeBpNoTwo;
    /**
     * 透后血压
     */
    private String afterBpNoOne;
    /**
     * 透后血压
     */
    private String afterBpNoTwo;
    /**
     * 透中最低压
     */
    private String lowestPressure;
    /**
     * 透中最高压
     */
    private String highestPressure;
    /**
     * 症状
     */
    private String symptom;
    /**
     * 备注
     */
    private String remark;


}
