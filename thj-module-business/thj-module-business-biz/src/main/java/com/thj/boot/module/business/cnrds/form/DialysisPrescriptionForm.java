package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 透析处方表单
 */
@Data
public class DialysisPrescriptionForm {

    /**
     * 年份
     */
    @NotNull
    @JsonProperty("DIA_YEAR")
    private String diaYear;

    /**
     * 时间维度
     */
    @NotNull
    @JsonProperty("DIA_DIMENSION")
    private String dimension;

    /**
     * 有无变化
     */
    @NotNull
    @JsonProperty("DIA_CHANGE_YN")
    private String changeYn;

    /**
     * 透析治疗频次
     */
    @NotNull
    @JsonProperty("DIA_FREQ")
    private String freq;

    /**
     * 血液透析每次治疗时间（小时/次）
     */
    @NotNull
    @JsonProperty("DIA_DURATION")
    private String duration;

    /**
     * HDF治疗
     */
    @NotNull
    @JsonProperty("DIA_HDF_YN")
    private String hdfYn;

    /**
     * HDF次数
     */
    @NotNull
    @JsonProperty("DIA_HDF_FREQ")
    private String diaHdfFreq;

    /**
     * HP治疗
     */
    @NotNull
    @JsonProperty("DIA_HP_YN")
    private String hpYn;

    /**
     * HP次数
     */
    @NotNull
    @JsonProperty("DIA_HP_FREQ")
    private String hpFreq;

    /**
     * 透析浓缩液
     */
    @JsonProperty("DIA_DIALYSATE")
    private String dialysate;

    /**
     * 中心供液
     */
    @JsonProperty("DIA_DIALYSATE_CENTRAL")
    private String dialysateCentral;

    /**
     * 钾离子浓度
     */
    @JsonProperty("DIA_DIALYSATE_CENTRAL_K")
    private String dialysateCentralK;

    /**
     * 钙离子浓度
     */
    @JsonProperty("DIA_DIALYSATE_CENTRAL_CA")
    private String dialysateCentralCa;

    /**
     * 碳酸氢根浓度
     */
    @JsonProperty("DIA_DIALYSATE_CENTRAL_HCO")
    private String dialysateCentralHco;

    /**
     * 透析浓缩A液
     */
    @JsonProperty("DIA_DIALYSATE_A")
    private String dialysateA;

    /**
     * 透析液钾离子浓度
     */
    @JsonProperty("DIA_DIALYSATE_A_K")
    private String dialysateAK;

    /**
     * 透析液钙离子浓度
     */
    @JsonProperty("DIA_DIALYSATE_A_CA")
    private String dialysateACa;

    /**
     * 含糖透析液
     */
    @JsonProperty("DIA_DIALYSATE_A_GLU")
    private String dialysateAGlu;

    /**
     * 透析浓缩B液
     */
    @JsonProperty("DIA_DIALYSATE_B")
    private String dialysateB;

    /**
     * 类型
     */
    @NotNull
    @JsonProperty("DIA_DIALYZER_TYPE")
    private List<String> dialyzerType;

    /**
     * 通量
     */
    @NotNull
    @JsonProperty("DIA_DIALYZER_FLUX")
    private String dialyzerFlux;

    /**
     * 膜类型
     */
    @JsonProperty("DIA_DIALYZER_MEM")
    private String dialyzerMem;

    /**
     * 膜面积
     */
    @JsonProperty("DIA_DIALYZER_AREA")
    private String dialyzerArea;

    /**
     * 是否为首次
     */
    @JsonProperty("DIA_FIRST_YN")
    private Boolean firstYn;


}
