package com.thj.boot.module.business.convert.conceal;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.conceal.ConcealDO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealCreateReqVO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealRespVO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 隐私策略 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ConcealConvert {

    ConcealConvert INSTANCE = Mappers.getMapper(ConcealConvert.class);

    ConcealDO convert(ConcealCreateReqVO bean);

    ConcealDO convert(ConcealUpdateReqVO bean);

    ConcealRespVO convert(ConcealDO bean);

    List<ConcealRespVO> convertList(List<ConcealDO> list);

    PageResult<ConcealRespVO> convertPage(PageResult<ConcealDO> page);

}
