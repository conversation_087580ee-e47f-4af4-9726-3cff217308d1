package com.thj.boot.module.business.pojo.mottohard.vo;

import com.thj.boot.module.business.dal.datado.anticoagulant.AnticoagulantDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MottoHardRespVO extends MottoHardBaseVO {

    /**
     * 首剂
     */
    private List<AnticoagulantDO> anticoagulantDOS;
    /**
     * 血管通路
     */
    private String bloodPart;
    private String bloodType;
}
