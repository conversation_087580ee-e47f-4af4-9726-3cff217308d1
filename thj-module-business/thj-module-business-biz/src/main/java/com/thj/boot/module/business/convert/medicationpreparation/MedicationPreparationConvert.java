package com.thj.boot.module.business.convert.medicationpreparation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.medicationpreparation.MedicationPreparationDO;
import com.thj.boot.module.business.pojo.medicationpreparation.vo.MedicationPreparationCreateReqVO;
import com.thj.boot.module.business.pojo.medicationpreparation.vo.MedicationPreparationRespVO;
import com.thj.boot.module.business.pojo.medicationpreparation.vo.MedicationPreparationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 给药准备 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicationPreparationConvert {

    MedicationPreparationConvert INSTANCE = Mappers.getMapper(MedicationPreparationConvert.class);

    MedicationPreparationDO convert(MedicationPreparationCreateReqVO bean);

    MedicationPreparationDO convert(MedicationPreparationUpdateReqVO bean);

    MedicationPreparationRespVO convert(MedicationPreparationDO bean);

    List<MedicationPreparationRespVO> convertList(List<MedicationPreparationDO> list);

    PageResult<MedicationPreparationRespVO> convertPage(PageResult<MedicationPreparationDO> page);


}
