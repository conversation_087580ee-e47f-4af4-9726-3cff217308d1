package com.thj.boot.module.business.convert.mottosimple;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleCreateReqVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleRespVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 简单个性化 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MottoSimpleConvert {

    MottoSimpleConvert INSTANCE = Mappers.getMapper(MottoSimpleConvert.class);

    MottoSimpleDO convert(MottoSimpleCreateReqVO bean);

    MottoSimpleDO convert(MottoSimpleUpdateReqVO bean);

    MottoSimpleRespVO convert(MottoSimpleDO bean);

    List<MottoSimpleRespVO> convertList(List<MottoSimpleDO> list);

    PageResult<MottoSimpleRespVO> convertPage(PageResult<MottoSimpleDO> page);

    List<MottoSimpleCreateReqVO> convertList02(List<MottoSimpleDO> list);

    List<MottoSimpleDO> convertList03(List<MottoSimpleUpdateReqVO> updateReqVOs);
}
