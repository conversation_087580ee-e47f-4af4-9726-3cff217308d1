package com.thj.boot.module.business.convert.facilitygroup;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facilitygroup.FacilityGroupDO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupCreateReqVO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupRespVO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 分组设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilityGroupConvert {

    FacilityGroupConvert INSTANCE = Mappers.getMapper(FacilityGroupConvert.class);

    FacilityGroupDO convert(FacilityGroupCreateReqVO bean);

    FacilityGroupDO convert(FacilityGroupUpdateReqVO bean);

    FacilityGroupRespVO convert(FacilityGroupDO bean);

    List<FacilityGroupRespVO> convertList(List<FacilityGroupDO> list);

    PageResult<FacilityGroupRespVO> convertPage(PageResult<FacilityGroupDO> page);


}
