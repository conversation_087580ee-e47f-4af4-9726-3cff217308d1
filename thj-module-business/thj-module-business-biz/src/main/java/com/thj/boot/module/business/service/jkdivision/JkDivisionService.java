package com.thj.boot.module.business.service.jkdivision;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.gk.vo.EstimateVo;
import com.thj.boot.module.business.dal.datado.jkdivision.JkDivisionDO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionCreateReqVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionPageReqVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionRespVO;

import java.util.Collection;
import java.util.List;

/**
 * 健康教育-护士患者分组 Service 接口
 *
 * <AUTHOR>
 */
public interface JkDivisionService {

    /**
     * 创建健康教育-护士患者分组
     *
     * @param jkDivisionDO 创建信息
     * @return 编号
     */
    Long createJkDivision(JkDivisionDO jkDivisionDO);

    /**
     * 更新健康教育-护士患者分组
     *
     * @param jkDivisionDO 更新信息
     */
    void updateJkDivision(JkDivisionDO jkDivisionDO);

    /**
     * 删除健康教育-护士患者分组
     *
     * @param id 编号
     */
    void deleteJkDivision(Long id);

    /**
     * 获得健康教育-护士患者分组
     *
     * @param id 编号
     * @return 健康教育-护士患者分组
     */
    JkDivisionDO getJkDivision(Long id);

    /**
     * 获得健康教育-护士患者分组列表
     *
     * @param ids 编号
     * @return 健康教育-护士患者分组列表
     */
    List<JkDivisionDO> getJkDivisionList(Collection<Long> ids);

    /**
     * 获得健康教育-护士患者分组分页
     *
     * @param pageReqVO 分页查询
     * @return 健康教育-护士患者分组分页
     */
    PageResult<JkDivisionDO> getJkDivisionPage(JkDivisionPageReqVO pageReqVO);

    /**
     * 维持透析患者列表
     * @param vo
     * @return
     */
    List<JkDivisionCreateReqVO> queryByList(JkDivisionCreateReqVO vo);

    List<JkDivisionCreateReqVO> queryByNewList(JkDivisionCreateReqVO vo);

    /**
     * 查看分组
     * @return
     */
    List<JkDivisionRespVO> queryNurse(String name,Long deptId);

    JkDivisionDO queryById(String patientId);

    List<EstimateVo> queryEstimate(EstimateVo vo);

//    List<Map<String,Integer>> queryNewCount();

}
