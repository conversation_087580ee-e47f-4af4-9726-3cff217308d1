package com.thj.boot.module.business.pojo.disinfectionrecords.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DisinfectionRecordsPageReqVO extends PageParam {

    /**
     * 序号
     */
    @TableId
    private Long id;
    /**
     * 消毒时间
     */
    private LocalDateTime disinfectionTime;
    /**
     * 消毒方式
     */
    private String disinfectionMethod;
    /**
     * 消毒时长（h）
     */
    private String disinfectionDurationH;
    /**
     * 责任人
     */
    private String responsiblePerson;
    /**
     * 分区
     */
    private String zone;
    /**
     * 是否更换床单
     */
    private String changedBeddings;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 责任人id
     */
    private Long responsiblePersonId;
}
