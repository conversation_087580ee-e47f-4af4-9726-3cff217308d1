package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 诊断信息表单
 */
@Data
public class DiagnosisInfoForm {

    /**
     * 诊断年份
     */
    @JsonProperty("PRICAU_TIME")
    private DiagnosisPricauTime pricauTime;

    /**
     * 主要原发疾病
     */
    @JsonProperty("PRICAU_PRIMARY_CAUSE")
    @NotNull
    private String primaryCause;

    /**
     * 原发性肾小球疾病
     */
    @JsonProperty("PRICAU_PRIMARY_GD")
    private List<String> pricauPrimaryGd;

    /**
     * 其它继发性肾小球疾病
     */
    @JsonProperty("PRICAU_SECONDARY_GD")
    private List<String> pricauSecondaryGd;

    /**
     * 遗传性及先天性疾病
     */
    @JsonProperty("PRICAU_CONGENIAL")
    private List<String> pricauCongenial;

    /**
     * 肾小管间质疾病
     */
    @JsonProperty("PRICAU_TUBULOINTERSTITIAL")
    private List<String> pricauTubulointerstitial;

    /**
     * 药物性肾损害
     */
    @JsonProperty("PRICAU_DRUG_ASSO")
    private List<String> pricauDrugAsso;

    /**
     * 泌尿系感染和结石
     */
    @JsonProperty("PRICAU_INFECT_STONE")
    private List<String> pricauInfectStone;

    /**
     * 病理（是否做过肾活检）
     */
    @JsonProperty("PATHO_YESNO")
    @NotNull
    private String pathoYesNo;

    /**
     * 检查年份
     */
    @JsonProperty("PATHO_TIME")
    private DiagnosisPricauTime pathoTime;

    /**
     * 病理诊断分类
     */
    @JsonProperty("PATHO_FL")
    private List<String> pathoFl;

    /**
     * 原发性肾小球疾病
     */
    @JsonProperty("PATHO_PRIMARY_GD")
    private List<String> pathoPrimaryGd;

    /**
     * 继发性肾小球疾病
     */
    @JsonProperty("PATHO_SECONDARY_GD")
    private List<String> pathoSecondaryGd;

    /**
     * 遗传性及先天性肾病
     */
    @JsonProperty("PATHO_CONGENIAL")
    private List<String> pathoCongenial;

    /**
     * 肾小管间质疾病
     */
    @JsonProperty("PATHO_TUBULOINTERSTITIAL")
    private List<String> pathoTubulointerstitial;


}
