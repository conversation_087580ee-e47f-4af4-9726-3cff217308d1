package com.thj.boot.module.business.service.hemodialysismanager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.thj.boot.common.constant.Constants;
import com.thj.boot.common.exception.ErrorCode;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.redis.RedisUtils;
import com.thj.boot.module.business.cnrds.CnrdsAutoReportManager;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.DialysisTimeVo;
import com.thj.boot.module.business.controller.admin.gk.vo.RecentlyDialysisListVo;
import com.thj.boot.module.business.controller.admin.hemodialysismanager.vo.*;
import com.thj.boot.module.business.convert.contradict.ContradictConvert;
import com.thj.boot.module.business.convert.dialysisadvice.DialysisAdviceConvert;
import com.thj.boot.module.business.convert.dialysisdetection.DialysisDetectionConvert;
import com.thj.boot.module.business.convert.facilitymanager.FacilityManagerConvert;
import com.thj.boot.module.business.convert.hemodialysismanager.HemodialysisManagerConvert;
import com.thj.boot.module.business.convert.reachweigh.ReachWeighConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.contradict.ContradictDO;
import com.thj.boot.module.business.dal.datado.contradictadvice.ContradictAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.dal.datado.drugtype.DrugTypeDO;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.datado.exam.PackageDetailDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilityname.FacilityNameDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.hisdictdata.HisDictData;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.dal.datado.reachweigh.ReachWeighDO;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.contradict.ContradictMapper;
import com.thj.boot.module.business.dal.mapper.contradictadvice.ContradictAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysisdetection.DialysisDetectionMapper;
import com.thj.boot.module.business.dal.mapper.dialysisprotocol.DialysisProtocolMapper;
import com.thj.boot.module.business.dal.mapper.dialysisrecord.DialysisRecordMapper;
import com.thj.boot.module.business.dal.mapper.drug.DrugMapper;
import com.thj.boot.module.business.dal.mapper.drugtype.DrugTypeMapper;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.exam.PackageDetailMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilityname.FacilityNameMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.hiscombo.HisComboMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.hisdictdata.HisDictDataMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.hisinformation.HisInformationMapper;
import com.thj.boot.module.business.dal.mapper.mottosimple.MottoSimpleMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.project.ProjectMapper;
import com.thj.boot.module.business.dal.mapper.reachweigh.ReachWeighMapper;
import com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesLastVo;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import com.thj.boot.module.business.pojo.contradictAdvice.vo.ContradictAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.*;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerUpdateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighUpdateReqVO;
import com.thj.boot.module.business.service.dialyzeoption.DialyzeOptionService;
import com.thj.boot.module.business.service.mottohard.MottoHardService;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import com.thj.boot.module.business.service.patient.PatientService;
import com.thj.boot.module.business.service.reachweigh.ReachWeighService;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.pojo.dept.vo.DeptCreateReqVO;
import com.thj.boot.module.system.pojo.dept.vo.DeptRespVO;
import com.thj.boot.module.system.service.dept.DeptService;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 血液透析管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class HemodialysisManagerServiceImpl implements HemodialysisManagerService {

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private FacilityManagerMapper facilityManagerMapper;

    @Resource
    private ReachWeighMapper reachWeighMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private DialysisDetectionMapper dialysisDetectionMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;

    @Resource
    private DialysisRecordMapper dialysisRecordMapper;

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private FacilityNameMapper facilityNameMapper;

    @Resource
    private ContradictMapper contradictMapper;

    @Resource
    private DrugTypeMapper drugTypeMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private BloodRoadMapper bloodRoadMapper;

    @Resource
    private MottoSimpleMapper mottoSimpleMapper;

    @Resource
    private DrugMapper drugMapper;

    @Resource
    private VascularAccessMapper vascularAccessMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private ContradictAdviceMapper contradictAdviceMapper;

    @Resource
    private HisInformationMapper hisInformationMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DialysisProtocolMapper dialysisProtocolMapper;

    @Resource
    private HisDrugMapper hisDrugMapper;

    @Autowired
    private HisComboMapper hisComboMapper;

    @Autowired
    private DialyzeOptionService dialyzeOptionService;

    @Autowired
    private MottoHardService mottoHardService;

    @Autowired
    private HisDictDataMapper hisDictDataMapper;

    @Value("${his.advice}")
    private String adviceUrl;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private DeptApi deptApi;
    @Resource
    private CnrdsAutoReportManager cnrdsManager;

    @Resource
    private DeptService deptService;

    @Resource
    private PatientService patientService;

    @Resource
    private ReachWeighService reachWeighService;

    @Autowired
    private PackageDetailMapper packageDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createHemodialysisManager(HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:dialyze:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                ArrangeClassesDO arrangeClassesDO = getArrangeClassOne(createReqVO);
                if (arrangeClassesDO == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.ARRANGE_NO_TEAM);
                }
                if (arrangeClassesDO != null && 0 == arrangeClassesDO.getState()) {
                    // 患者未签到，先进行签到
                    if (StrUtil.isNotBlank(createReqVO.getDialyzeBeforeWeigh()) && StrUtil.isNotBlank(createReqVO.getDialyzeBeforeWeight())) {
                        ReachWeighUpdateReqVO updateReqVO = BeanUtil.copyProperties(createReqVO, ReachWeighUpdateReqVO.class);
                        reachWeighService.saveOrUpdateReachWeigh(updateReqVO, request);
                    } else {
                        throw new ServiceException(GlobalErrorCodeConstants.NO_REACH_SIGN);
                    }
                }

                if (createReqVO.getMonitState() != null || createReqVO.getAfterState() != null || createReqVO.getHealState() != null) {
                    if (arrangeClassesDO != null) {
                        if (arrangeClassesDO.getState() < 1) {
                            throw new ServiceException(GlobalErrorCodeConstants.END_DIALYSIS_VERIFY);
                        }
                    }
                }
                if (createReqVO.getMonitState() != null && arrangeClassesDO.getState() < 2) {
                    throw new ServiceException(GlobalErrorCodeConstants.START_DIALYSIS);
                }

                //患者干体重
                if (StrUtil.isNotEmpty(createReqVO.getDryWeight())) {
                    if ("待定".equals(createReqVO.getDryWeight())) {
                        createReqVO.setDryWeight("-1");
                    } else if ("卧床".equals(createReqVO.getDryWeight())) {
                        createReqVO.setDryWeight("-2");
                    }
                }
                createReqVO.setDialyzeBeforeWeight(getDialysisBeforeWeight(createReqVO));
                createReqVO.setBeforeGainWeight(getDialysisBeforeGainWeight(createReqVO));
                createReqVO.setBeforeUltrafiltrationtotal(getBeforeUltrafiltrationtotal(createReqVO));
                HemodialysisManagerRespVO hemodialysisManagerRespVO = getHemodialysisManagerOne(createReqVO);
                HemodialysisManagerDO hemodialysisManagerDO = HemodialysisManagerConvert.INSTANCE.convert(createReqVO);
                ReachWeighDO reachWeighDO = new ReachWeighDO();
                if (createReqVO.getBeforeState() != null) {
                    reachWeighDO.setClothing(hemodialysisManagerDO.getClothing());
                    reachWeighDO.setDryWeight(createReqVO.getDryWeight());
                    reachWeighDO.setDialyzeBeforeWeigh(hemodialysisManagerDO.getDialyzeBeforeWeigh());
                    reachWeighDO.setDialyzeBeforeWeight(getBeforeWeight(reachWeighDO));
                    reachWeighDO.setDehydration(getTargetWater(reachWeighDO));
                    reachWeighDO.setDehydrationPercent(getTargetScale(reachWeighDO));
                    reachWeighDO.setDegreeCelsius(hemodialysisManagerDO.getDegreeCelsius());
                    reachWeighDO.setPno(hemodialysisManagerDO.getPno());
                    reachWeighDO.setRno(hemodialysisManagerDO.getRno());
                    reachWeighDO.setBpNoOne(hemodialysisManagerDO.getBpNoOne());
                    reachWeighDO.setBpNoTwo(hemodialysisManagerDO.getBpNoTwo());
                    hemodialysisManagerDO.setDehydration(getTargetWater(reachWeighDO));
                }
                if (createReqVO.getAfterState() != null) {
                    reachWeighDO.setAfterDegreeCelsius(hemodialysisManagerDO.getAfterDegreeCelsius());
                    reachWeighDO.setAfterClothing(hemodialysisManagerDO.getAfterClothing());
                    reachWeighDO.setAfterPNo(hemodialysisManagerDO.getAfterPNo());
                    reachWeighDO.setAfterRNo(hemodialysisManagerDO.getAfterRNo());
                    reachWeighDO.setAfterBpOne(hemodialysisManagerDO.getAfterBpOne());
                    reachWeighDO.setAfterBpTwo(hemodialysisManagerDO.getAfterBpTwo());
                    reachWeighDO.setDialyzeAfterWeigh(hemodialysisManagerDO.getDialyzeAfterWeigh());
                    reachWeighDO.setDialyzeAfterWeight(getAfterWeight(reachWeighDO));
                    reachWeighDO.setWeights(getWeights(reachWeighDO));
                    hemodialysisManagerDO.setWeights(getWeights(reachWeighDO));
                }
                if (hemodialysisManagerRespVO == null) {
                    Long count = hemodialysisManagerMapper.selectCount(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                            .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                            .eq(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime()));
                    if (count == 0) {
                        if (!StringUtils.isEmpty(arrangeClassesDO) && StringUtils.isEmpty(hemodialysisManagerDO.getFacilityId())) {
                            hemodialysisManagerDO.setFacilityId(arrangeClassesDO.getFacilityId());
                            hemodialysisManagerDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                        }
                        hemodialysisManagerMapper.insert(hemodialysisManagerDO);
                    }
                } else {
                    updateDialysisManager(hemodialysisManagerDO);
                    // 透析方式变化
                    HemodialysisManagerDO hemodialysisManagerDO1 = hemodialysisManagerMapper.selectById(hemodialysisManagerDO.getId());
                    if (!StringUtils.isEmpty(hemodialysisManagerDO1)) {
                        hemodialysisManagerDO1.setBloodFilter(hemodialysisManagerDO.getBloodFilter());
                        hemodialysisManagerDO1.setHemodialysisDevice(hemodialysisManagerDO.getHemodialysisDevice());
                        hemodialysisManagerDO1.setPerfumer(hemodialysisManagerDO.getPerfumer());
                        hemodialysisManagerDO1.setSubstituteModel(hemodialysisManagerDO.getSubstituteModel());
                        hemodialysisManagerDO1.setSubstituteTodal(hemodialysisManagerDO.getSubstituteTodal());
                        hemodialysisManagerMapper.updateTypeById(hemodialysisManagerDO1);
                    }
                }
                if (hemodialysisManagerDO.getPrescriptionState() != null) {
                    int compare = DateUtil.compare(createReqVO.getHemodialysisTime(), DateUtil.beginOfDay(new Date()));
                    if (0 == compare) {
                        if (CollectionUtil.isNotEmpty(createReqVO.getContradictAdviceDOS())) {
                            List<DialysisAdviceDO> dialysisAdviceList = getDialysisAdviceList(createReqVO);
                            if (CollectionUtil.isNotEmpty(dialysisAdviceList)) {
                                List<String> drugIds = dialysisAdviceList.stream().map(DialysisAdviceDO::getAdviceId).map(String::valueOf).collect(Collectors.toList());
                                List<String> drugIdStr = createReqVO.getContradictAdviceDOS().stream().map(ContradictAdviceCreateReqVO::getDrugId).collect(Collectors.toList());
                                Collection intersection = CollectionUtils.intersection(drugIds, drugIdStr);
                                if (intersection.size() == 0) {
                                    insertBatchAdvice(createReqVO,arrangeClassesDO,hemodialysisManagerDO,SystemDeptId);
                                }
                            } else {
                                insertBatchAdvice(createReqVO,arrangeClassesDO,hemodialysisManagerDO,SystemDeptId);
                            }
                        }
                    }

                }
                updateReachWeight(reachWeighDO, hemodialysisManagerDO);
            } else {
                return;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    private void insertBatchAdvice(HemodialysisManagerCreateReqVO createReqVO, ArrangeClassesDO arrangeClassesDO, HemodialysisManagerDO hemodialysisManagerDO, String systemDeptId) {
        List<DialysisAdviceDO> dialysisAdviceDOS = Lists.newArrayList();
        PatientDO patientDO = patientMapper.selectOne(PatientDO::getId, createReqVO.getPatientId());
        DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", hemodialysisManagerDO.getDialyzeWayValue());
        ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();

        for (ContradictAdviceCreateReqVO contradictAdviceDO : createReqVO.getContradictAdviceDOS()) {
            DrugDO drugDO = drugMapper.selectOne(DrugDO::getId, contradictAdviceDO.getDrugId());
            HisDrugDO hisDrugDO = hisDrugMapper.selectOne(HisDrugDO::getFDrugId, contradictAdviceDO.getDrugId());
            if (drugDO != null) {
                DictDataRespDTO doseUnitDict = dictDataApi.getDictData("agent_unit", drugDO.getDoseUnit());
                DictDataRespDTO minDoseDict = dictDataApi.getDictData("min_agent_unit", drugDO.getMinDose());
                DialysisAdviceDO dialysisAdviceDO = new DialysisAdviceDO();
                dialysisAdviceDO.setFpreparaUnit(doseUnitDict != null ? doseUnitDict.getLabel() : "");
                dialysisAdviceDO.setFspecUnit(minDoseDict != null ? minDoseDict.getLabel() : "");
                dialysisAdviceDO.setType("0");
                dialysisAdviceDO.setAdviceId(Long.valueOf(contradictAdviceDO.getDrugId()));
                dialysisAdviceDO.setPatientId(createReqVO.getPatientId());
                // 患者名字 透析号
                dialysisAdviceDO.setPatientName(patientDO.getName());
                dialysisAdviceDO.setDialyzeNo(patientDO.getDialyzeNo());
                dialysisAdviceDO.setDialyzeDictValue(hemodialysisManagerDO.getDialyzeWayValue());
                dialysisAdviceDO.setDialyzeName(dialyzeWay.getLabel());
                dialysisAdviceDO.setFacilityId(createReqVO.getFacilityId());
                dialysisAdviceDO.setStartTime(new Date());
                if (!StringUtils.isEmpty(arrangeClassesDO)) {
                    dialysisAdviceDO.setWeekDay(arrangeClassesDO.getDayState());
                    //dialysisAdviceDO.setPatientName(arrangeClassesDO.getPatientName());
                }
                dialysisAdviceDO.setAdviceTime(new Date());
                dialysisAdviceDO.setAdviceDesprition(drugDO.getSpec());
                dialysisAdviceDO.setAdviceUser(createReqVO.getDoctorId());
                dialysisAdviceDO.setAdviceName(drugDO.getName());
                dialysisAdviceDO.setOneNo(contradictAdviceDO.getSingleDose());
                dialysisAdviceDO.setPrescribeNo(contradictAdviceDO.getPrescriptionQuantity());
                dialysisAdviceDO.setDrugWay(contradictAdviceDO.getMedicineWayValue());
                dialysisAdviceDO.setFrequency(contradictAdviceDO.getFrequencyValue());
                dialysisAdviceDO.setSpecification(drugDO.getSpec());
                dialysisAdviceDOS.add(dialysisAdviceDO);
            }
            if (hisDrugDO != null) {
                DialysisAdviceDO dialysisAdviceDO = new DialysisAdviceDO();
                dialysisAdviceDO.setFpreparaUnit(hisDrugDO.getFSpecUnit());
                dialysisAdviceDO.setFspecUnit(hisDrugDO.getFPreparaUnit());
                dialysisAdviceDO.setType("0");
                dialysisAdviceDO.setAdviceId(Long.valueOf(contradictAdviceDO.getDrugId()));
                dialysisAdviceDO.setPatientId(createReqVO.getPatientId());
                dialysisAdviceDO.setDateWeek(createReqVO.getHemodialysisTime());
                dialysisAdviceDO.setStartTime(new Date());
                dialysisAdviceDO.setAdviceTime(new Date());
                dialysisAdviceDO.setDialyzeDictValue(hemodialysisManagerDO.getDialyzeWayValue());
                dialysisAdviceDO.setPatientName(patientDO.getName());
                dialysisAdviceDO.setDialyzeNo(patientDO.getDialyzeNo());
                dialysisAdviceDO.setDialyzeName(dialyzeWay.getLabel());
                dialysisAdviceDO.setFacilityId(createReqVO.getFacilityId());
                if (!StringUtils.isEmpty(arrangeClassesDO)) {
                    dialysisAdviceDO.setWeekDay(arrangeClassesDO.getDayState());
                    //dialysisAdviceDO.setPatientName(arrangeClassesDO.getPatientName());
                }
                dialysisAdviceDO.setAdviceDesprition(hisDrugDO.getFDrugSpec());
                dialysisAdviceDO.setAdviceUser(createReqVO.getDoctorId());
                dialysisAdviceDO.setAdviceName(hisDrugDO.getFDrugName());
                dialysisAdviceDO.setSpecification(hisDrugDO.getFDrugSpec());
                dialysisAdviceDO.setOneNo(contradictAdviceDO.getSingleDose());
                dialysisAdviceDO.setPrescribeNo(contradictAdviceDO.getPrescriptionQuantity());
                dialysisAdviceDO.setDrugWay(contradictAdviceDO.getMedicineWayValue());
                dialysisAdviceDO.setFrequency(contradictAdviceDO.getFrequencyValue());
                dialysisAdviceDOS.add(dialysisAdviceDO);
            }
        }
        dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);

        if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS)) {
            AdminUserRespDTO user = adminUserApi.getUser(dialysisAdviceDOS.get(0).getAdviceUser());
            String synFlag = redisTemplate.opsForValue().get("synAdvice:" + systemDeptId);
            if (!StringUtils.isEmpty(user) && !org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS) && !StringUtils.isEmpty(synFlag)) {
                dialysisAdviceDOS.forEach(dialysisAdviceDO -> {
                    Long adviceId = dialysisAdviceDO.getAdviceId();
                    if (adviceId > 10000000 && "0".equals(dialysisAdviceDO.getType())) {
                        SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();

                        PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO.getPatientId());
                        if (!StringUtils.isEmpty(patientDO1)) {
                            synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                        }
                        //Date dateWeek = createReqVO.getDateWeek();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (StringUtils.isEmpty(dialysisAdviceDO.getStartTime())) {

                            String format = simpleDateFormat.format(new Date());

                            synToHisAdviceVo.setDpDate(format);
                        }else {
                            synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO.getStartTime()));
                        }
                        synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO.getAdviceId()));
                        synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                        synToHisAdviceVo.setDrugUsage(dialysisAdviceDO.getDrugWay());
                        synToHisAdviceVo.setOnceUsing(dialysisAdviceDO.getOneNo());
                        synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO.getFpreparaUnit());
                        synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO.getId()));
                        synToHisAdviceVo.setDoctorMobile(user.getMobile());
                        synToHisAdviceVo.setDrugName(dialysisAdviceDO.getAdviceName());
                        synToHisAdviceVo.setDrugDays(1);
                        synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                        if (StringUtils.isEmpty(dialysisAdviceDO.getPrescribeNo())) {
                            dialysisAdviceDO.setPrescribeNo("1");
                        }
                        if (StringUtils.isEmpty(dialysisAdviceDO.getOneNo())) {
                            dialysisAdviceDO.setOneNo("1");
                        }
                        synToHisAdviceVo.setDrugNum(Integer.valueOf(dialysisAdviceDO.getPrescribeNo()));
                        DeptRespDTO dept = deptApi.getDept(Long.valueOf(systemDeptId));
                        synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                        synToHisAdviceVos.add(synToHisAdviceVo);
                        AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                        if (!StringUtils.isEmpty(adviceResult)) {
                            AdviceData data = adviceResult.getData();
                            if (-1 == data.getInfcode()) {
                                throw new ServiceException(data.getInfcode(), data.getMessage());
                            }else if (0 == data.getInfcode()) {
                                // 更新advice
                                List<AdviceOutput> output = data.getOutput();
                                if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                    AdviceOutput adviceOutput = output.get(0);
                                    dialysisAdviceDO.setDpId(adviceOutput.getDpId());
                                    dialysisAdviceDO.setDpdId(adviceOutput.getDpdId());
                                    dialysisAdviceMapper.updateById(dialysisAdviceDO);
                                }
                            }
                        }else {
                            throw new ServiceException(-1, "同步失败");
                        }
                    }

                });
            }
        }

    }

    private void updateReachWeight(ReachWeighDO reachWeighDO, HemodialysisManagerDO hemodialysisManagerDO) {
        reachWeighMapper.update(reachWeighDO, new LambdaUpdateWrapper<ReachWeighDO>()
                .eq(ReachWeighDO::getPatientId, hemodialysisManagerDO.getPatientId())
                .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(hemodialysisManagerDO.getHemodialysisTime()), DateUtil.endOfDay(hemodialysisManagerDO.getHemodialysisTime())));
    }


    private String getTargetScale(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDehydration()) && StrUtil.isNotEmpty(reachWeighDO1.getDryWeight())) {
            //干体重不能为0
            if (!"0".equals(reachWeighDO1.getDryWeight())) {
                BigDecimal multiply = Convert.toBigDecimal(reachWeighDO1.getDehydration())
                        .divide(Convert.toBigDecimal(reachWeighDO1.getDryWeight()), 4, RoundingMode.DOWN)
                        .multiply(Convert.toBigDecimal("100").stripTrailingZeros());
                return multiply.toString();
            }
        }
        return null;
    }

    private String getTargetWater(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getClothing()) && StrUtil.isNotEmpty(reachWeighDO1.getDryWeight())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeigh())
                    .subtract(Convert.toBigDecimal(reachWeighDO1.getClothing()))
                    .subtract(Convert.toBigDecimal(reachWeighDO1.getDryWeight()));
            return subtract.toString();
        }
        return null;
    }

    private String getBeforeWeight(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getClothing())) {
            BigDecimal multiply1 = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeigh()).subtract(Convert.toBigDecimal(reachWeighDO1.getClothing()));
            return multiply1.toString();
        }
        return null;
    }

    private void updateDialysisManager(HemodialysisManagerDO hemodialysisManagerDO) {
        hemodialysisManagerMapper.update(hemodialysisManagerDO, new LambdaUpdateWrapper<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, hemodialysisManagerDO.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, hemodialysisManagerDO.getHemodialysisTime()));
    }

    private String getBeforeUltrafiltrationtotal(HemodialysisManagerCreateReqVO createReqVO) {
        if (StrUtil.isNotEmpty(createReqVO.getPrescriptionEhydratedLevel()) && StrUtil.isNotEmpty(createReqVO.getPreremovalWater())) {
            BigDecimal add = Convert.toBigDecimal(createReqVO.getPrescriptionEhydratedLevel()).add(Convert.toBigDecimal(createReqVO.getPreremovalWater()));
            return add.toString();
        }
        return null;

    }

    private String getDialysisBeforeGainWeight(HemodialysisManagerCreateReqVO createReqVO) {
        if (StrUtil.isNotEmpty(createReqVO.getDialyzeBeforeWeight()) && StrUtil.isNotEmpty(createReqVO.getBeforeDialyzeAfterWeigh()) && !"/".equals(createReqVO.getBeforeDialyzeAfterWeigh())) {
            BigDecimal subtract = Convert.toBigDecimal(createReqVO.getDialyzeBeforeWeight()).subtract(Convert.toBigDecimal(createReqVO.getBeforeDialyzeAfterWeigh()));
            return subtract.toString();
        }
        return null;
    }

    private String getDialysisBeforeWeight(HemodialysisManagerCreateReqVO createReqVO) {
        if (StrUtil.isNotEmpty(createReqVO.getDialyzeBeforeWeigh()) && StrUtil.isNotEmpty(createReqVO.getClothing())) {
            BigDecimal subtract = Convert.toBigDecimal(createReqVO.getDialyzeBeforeWeigh()).subtract(Convert.toBigDecimal(createReqVO.getClothing()));
            return subtract.toString();
        }
        return null;
    }

    private HemodialysisManagerRespVO getHemodialysisManagerOne(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerDO managerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime()));
        return HemodialysisManagerConvert.INSTANCE.convert(managerDO);
    }

    private ArrangeClassesDO getArrangeClassOne(HemodialysisManagerCreateReqVO createReqVO) {
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .select(ArrangeClassesDO::getState,ArrangeClassesDO::getFacilityId,ArrangeClassesDO::getFacilitySubareaId,ArrangeClassesDO::getFacilityName,ArrangeClassesDO::getFaciitySubareaName,ArrangeClassesDO::getDayState));
        return arrangeClassesDO;
    }

    @Override
    public void updateHemodialysisManager(HemodialysisManagerUpdateReqVO updateReqVO) {
        // 更新
        HemodialysisManagerDO updateObj = HemodialysisManagerConvert.INSTANCE.convert(updateReqVO);
        hemodialysisManagerMapper.updateById(updateObj);
    }

    @Override
    public void deleteHemodialysisManager(Long id) {
        // 删除
        hemodialysisManagerMapper.deleteById(id);
    }


    @Override
    public HemodialysisManagerRespVO getHemodialysisManager(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerRespVO hemodialysisManagerRespVO = getHemodialysisManagerOne(createReqVO);
        String patientDryWeight = getPatientNewDryWeight(createReqVO.getPatientId());
        HemodialysisManagerDO defaultValue = getDefaultValue(createReqVO);
        ReachWeighRespVO reachWeighRespVO = getReachWeightOne(createReqVO);

        if (hemodialysisManagerRespVO != null) {
            if (StringUtils.isEmpty(hemodialysisManagerRespVO.getDryWeight())) {
                hemodialysisManagerRespVO.setDryWeight(patientDryWeight);
            }

            if (StringUtils.isEmpty(hemodialysisManagerRespVO.getBeforeDialyzeAfterWeigh()) && !StringUtils.isEmpty(defaultValue)) {
                hemodialysisManagerRespVO.setBeforeDialyzeAfterWeigh(defaultValue.getDialyzeAfterWeight());
            }

            List<DialysisDetectionDO> dialysisDetectionDOList = getDialysisDetectionOne(createReqVO);
            if (CollectionUtil.isNotEmpty(dialysisDetectionDOList)) {
                for (DialysisDetectionDO dialysisDetectionDO : dialysisDetectionDOList) {
                    if (dialysisDetectionDO != null && StrUtil.isNotEmpty(dialysisDetectionDO.getUltrafiltrationCapacity()) && !"/".equals(dialysisDetectionDO.getUltrafiltrationCapacity())) {
                        hemodialysisManagerRespVO.setActualExchangeAmount(StrUtil.isNotEmpty(dialysisDetectionDO.getDisplacementQuantity()) ? dialysisDetectionDO.getDisplacementQuantity() : "/");
                        hemodialysisManagerRespVO.setActualUltrafiltrationCapacity(dialysisDetectionDO.getUltrafiltrationCapacity());
                        break;
                    }
                }
            }
            List<DialysisAdviceDO> dialysisAdviceDOS = getDialysisAdviceList(createReqVO);
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                List<DialysisAdviceDO> dialysisAdviceDOS1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> "0".equals(dialysisAdviceDO.getState())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS1)) {
                    hemodialysisManagerRespVO.setAdviceState("0");
                } else {
                    hemodialysisManagerRespVO.setAdviceState("1");
                }
            } else {
                hemodialysisManagerRespVO.setAdviceState("0");
            }

            if (hemodialysisManagerRespVO.getStartDialyzeTime() != null) {
                String time = time(hemodialysisManagerRespVO);
                if (StrUtil.isNotEmpty(time)) {
                    List<String> list = Arrays.asList(time.split(","));
                    if (list.size() == 2) {
                        hemodialysisManagerRespVO.setTreatmentHour(list.get(0));
                        hemodialysisManagerRespVO.setTreatmentMin(list.get(1));
                    }
                }
            }

            FacilityNameDO facilityNameDO = getFacilityManagerOne(hemodialysisManagerRespVO.getFacilityId());
            hemodialysisManagerRespVO.setState(facilityNameDO != null ? facilityNameDO.getState() : null);

            HisConsumablesDO hemodialysisDevice = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                    .eq(HisConsumablesDO::getConsumId, hemodialysisManagerRespVO.getHemodialysisDevice())
                    .select(HisConsumablesDO::getConsumSpec));
            HisConsumablesDO bloodFilter = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                    .eq(HisConsumablesDO::getConsumId, hemodialysisManagerRespVO.getBloodFilter())
                    .select(HisConsumablesDO::getConsumSpec));
            HisConsumablesDO perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                    .eq(HisConsumablesDO::getConsumId, hemodialysisManagerRespVO.getPerfumer())
                    .select(HisConsumablesDO::getConsumSpec));

            StringBuilder sb1 = new StringBuilder();
            if (hemodialysisDevice != null) {
                sb1.append(hemodialysisDevice.getConsumSpec() + ",");
            }
            if (bloodFilter != null) {
                sb1.append(bloodFilter.getConsumSpec() + ",");
            }
            if (perfumer != null) {
                sb1.append(perfumer.getConsumSpec() + ",");
            }
            hemodialysisManagerRespVO.setArtificialKidney(sb1.toString().replaceAll(",+$", ""));

            MPJLambdaWrapper<ContradictDO> wrapper = new MPJLambdaWrapper<>(ContradictDO.class);
            wrapper.leftJoin(DrugTypeDO.class, DrugTypeDO::getId, ContradictDO::getDrugTypeId)
                    .eq(ContradictDO::getPatientId, createReqVO.getPatientId())
                    .eq(ContradictDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                    .eq(ContradictDO::getProtocolType, 2)
                    .select(ContradictDO::getDrugTypeId, ContradictDO::getId, ContradictDO::getFirstDoseValue, ContradictDO::getFirstMethodValue
                            , ContradictDO::getFirstMethodNumber, ContradictDO::getOpportunity, ContradictDO::getTotalValue)
                    .selectAs(DrugTypeDO::getName, "drugNameStr")
                    .select(DrugTypeDO::getDoseUnit);
            List<ContradictRespVO> contradictRespVOList = contradictMapper.selectJoinList(ContradictRespVO.class, wrapper);
            if (CollectionUtil.isNotEmpty(contradictRespVOList)) {
                contradictRespVOList = contradictRespVOList.stream().peek(contradictRespVO -> {
                    MPJLambdaWrapper<ContradictAdviceDO> contradictAdviceWapper = new MPJLambdaWrapper<>(ContradictAdviceDO.class);
                    contradictAdviceWapper.leftJoin(HisDrugDO.class, HisDrugDO::getFDrugId, ContradictAdviceDO::getDrugId)
                            .selectAs(HisDrugDO::getFDrugName, "drugNameStr")
                            .eq(ContradictAdviceDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                            .eq(ContradictAdviceDO::getPatientId, createReqVO.getPatientId())
                            .eq(ContradictAdviceDO::getProtocolType, 2)
                            .eq(ContradictAdviceDO::getDrugTypeId, contradictRespVO.getDrugTypeId());
                    List<ContradictRespVO> contradictRespVOList1 = contradictAdviceMapper.selectJoinList(ContradictRespVO.class, contradictAdviceWapper);
                    if (CollectionUtil.isNotEmpty(contradictRespVOList1)) {
                        String collect = contradictRespVOList1.stream().map(ContradictRespVO::getDrugNameStr).collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
                        contradictRespVO.setDrugNameStr(collect);
                    }
                }).collect(Collectors.toList());
            }

            hemodialysisManagerRespVO.setContradictRespVOS(contradictRespVOList);
            if (hemodialysisManagerRespVO.getBeforeState() == null) {
                hemodialysisManagerRespVO.setPreremovalWater(defaultValue != null && StrUtil.isNotEmpty(defaultValue.getPreremovalWater()) ? defaultValue.getPreremovalWater() : "0");
                hemodialysisManagerRespVO.setBeforeDialyzeAfterWeigh(defaultValue != null ? defaultValue.getDialyzeAfterWeight() : "/");
                hemodialysisManagerRespVO.setClothing(defaultValue != null ? defaultValue.getClothing() : "0");
                String lastTimeDefaultBeforeJson = getLastTimeDefaultBeforeJson(createReqVO);
                hemodialysisManagerRespVO.setBeforeJson(lastTimeDefaultBeforeJson);
                if (reachWeighRespVO != null) {
                    hemodialysisManagerRespVO.setDegreeCelsius(reachWeighRespVO.getDegreeCelsius());
                    hemodialysisManagerRespVO.setPno(reachWeighRespVO.getPno());
                    hemodialysisManagerRespVO.setRno(reachWeighRespVO.getRno());
                    hemodialysisManagerRespVO.setBpNoOne(reachWeighRespVO.getBpNoOne());
                    hemodialysisManagerRespVO.setBpNoTwo(reachWeighRespVO.getBpNoTwo());
                    hemodialysisManagerRespVO.setClothing(reachWeighRespVO.getClothing());
                    hemodialysisManagerRespVO.setDialyzeBeforeWeight(reachWeighRespVO.getDialyzeBeforeWeight());
                    hemodialysisManagerRespVO.setDialyzeBeforeWeigh(reachWeighRespVO.getDialyzeBeforeWeigh());
                    hemodialysisManagerRespVO.setDehydration(reachWeighRespVO.getDehydration());
                }
            }


            if (hemodialysisManagerRespVO.getAfterState() == null) {
                if (reachWeighRespVO != null) {
                    hemodialysisManagerRespVO.setAfterDegreeCelsius(reachWeighRespVO.getAfterDegreeCelsius());
                    hemodialysisManagerRespVO.setAfterPNo(reachWeighRespVO.getAfterPNo());
                    hemodialysisManagerRespVO.setAfterRNo(reachWeighRespVO.getAfterRNo());
                    hemodialysisManagerRespVO.setAfterBpOne(reachWeighRespVO.getAfterBpOne());
                    hemodialysisManagerRespVO.setAfterBpTwo(reachWeighRespVO.getAfterBpTwo());
                    hemodialysisManagerRespVO.setDialyzeAfterWeigh(reachWeighRespVO.getDialyzeAfterWeigh());
                    hemodialysisManagerRespVO.setDialyzeAfterWeight(reachWeighRespVO.getDialyzeAfterWeight());
                    hemodialysisManagerRespVO.setWeights(reachWeighRespVO.getWeights());
                }

                String lastTimeDefaultAfterJson = getLastTimeDefaultAfterJson(createReqVO);
                hemodialysisManagerRespVO.setAfterJson(lastTimeDefaultAfterJson);

            }
            return hemodialysisManagerRespVO;
        }
        HemodialysisManagerRespVO managerRespVO = new HemodialysisManagerRespVO();
        managerRespVO.setPreremovalWater(defaultValue != null && StrUtil.isNotEmpty(defaultValue.getPreremovalWater()) ? defaultValue.getPreremovalWater() : "0");
        if (!StringUtils.isEmpty(hemodialysisManagerRespVO) && !StringUtils.isEmpty(hemodialysisManagerRespVO.getDryWeight())) {
            managerRespVO.setDryWeight(hemodialysisManagerRespVO.getDryWeight());
        }else {
            managerRespVO.setDryWeight(patientDryWeight);
        }

        managerRespVO.setBeforeDialyzeAfterWeigh(defaultValue != null ? defaultValue.getDialyzeAfterWeight() : "/");
        managerRespVO.setClothing(defaultValue != null ? defaultValue.getClothing() : "0");
        String lastTimeDefaultBeforeJson = getLastTimeDefaultBeforeJson(createReqVO);
        managerRespVO.setBeforeJson(lastTimeDefaultBeforeJson);
        String lastTimeDefaultAfterJson = getLastTimeDefaultAfterJson(createReqVO);
        managerRespVO.setAfterJson(lastTimeDefaultAfterJson);
        if (reachWeighRespVO != null) {
            //透前
            managerRespVO.setDegreeCelsius(reachWeighRespVO.getDegreeCelsius());
            managerRespVO.setPno(reachWeighRespVO.getPno());
            managerRespVO.setRno(reachWeighRespVO.getRno());
            managerRespVO.setBpNoOne(reachWeighRespVO.getBpNoOne());
            managerRespVO.setBpNoTwo(reachWeighRespVO.getBpNoTwo());
            managerRespVO.setDialyzeBeforeWeight(reachWeighRespVO.getDialyzeBeforeWeight());
            managerRespVO.setDialyzeBeforeWeigh(reachWeighRespVO.getDialyzeBeforeWeigh());
            managerRespVO.setDehydration(reachWeighRespVO.getDehydration());
            //透后
            managerRespVO.setAfterDegreeCelsius(reachWeighRespVO.getAfterDegreeCelsius());
            managerRespVO.setAfterPNo(reachWeighRespVO.getAfterPNo());
            managerRespVO.setAfterRNo(reachWeighRespVO.getAfterRNo());
            managerRespVO.setAfterBpOne(reachWeighRespVO.getAfterBpOne());
            managerRespVO.setAfterBpTwo(reachWeighRespVO.getAfterBpTwo());
            managerRespVO.setDialyzeAfterWeigh(reachWeighRespVO.getDialyzeAfterWeigh());
            managerRespVO.setDialyzeAfterWeight(reachWeighRespVO.getDialyzeAfterWeight());
            managerRespVO.setWeights(reachWeighRespVO.getWeights());
        }
        return managerRespVO;
    }


    private String getWeights(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeight()) && StrUtil.isNotEmpty(reachWeighDO1.getDialyzeAfterWeight())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeight()).subtract(Convert.toBigDecimal(reachWeighDO1.getDialyzeAfterWeight()));
            return subtract.toString();
        }
        return null;
    }

    private String getAfterWeight(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeAfterWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getAfterClothing())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeAfterWeigh()).subtract(Convert.toBigDecimal(reachWeighDO1.getAfterClothing()));
            return subtract.toString();
        }
        return null;
    }


    private FacilityNameDO getFacilityManagerOne(Long facilityId) {
        FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityId);
        if (facilityManagerDO != null) {
            FacilityNameDO facilityNameDO = facilityNameMapper.selectById(facilityManagerDO.getFacilityNameId());
            return facilityNameDO;
        }
        return null;
    }

    private String getLastTimeDefaultAfterJson(HemodialysisManagerCreateReqVO createReqVO) {
        List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, 5);
        if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
            List<Map<String, Object>> collect = mottoSimpleDOS.stream().map(mottoSimpleDO -> {
                Map<String, Object> map = Maps.newHashMap();
                List<MottoSimpleDO> mottoSimpleDOS1 = mottoSimpleMapper.selectList(MottoSimpleDO::getPid, mottoSimpleDO.getId());
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                    //默认值
                    //正常
                    MottoSimpleDO mottoSimpleDO2 =
                            mottoSimpleDOS1.stream().filter(mottoSimpleDO1 -> 1 == mottoSimpleDO1.getAcquiesce()).findFirst().get();
                    map.put("motto" + mottoSimpleDO.getId(), StrUtil.isEmpty(mottoSimpleDO2.getPname()) ? "" :
                            mottoSimpleDO2.getPname());
                    map.put("motto" + mottoSimpleDO.getId() + "Value",
                            com.google.common.collect.Lists.newArrayList(mottoSimpleDO2.getId()));
                    map.put("motto" + mottoSimpleDO.getId() + "Extra", "");
                }
                return map;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                Map<String, Object> collect1 =
                        collect.stream().flatMap(map -> map.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey,
                                Map.Entry::getValue));
                //查询患者血管通路-通路类型是否默认到处方
                List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(BloodRoadDO::getPatientId, createReqVO.getPatientId());
                //正常
                List<MottoSimpleDO> mottoSimpleDOS1 = mottoSimpleMapper.selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                        .eqIfPresent(MottoSimpleDO::getAcquiesce, 1)
                        .inIfPresent(MottoSimpleDO::getPid, 57, 58));
                //斜杠
                List<MottoSimpleDO> mottoSimpleDOS2 = mottoSimpleMapper.selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                        .eqIfPresent(MottoSimpleDO::getAcquiesce, 2)
                        .inIfPresent(MottoSimpleDO::getPid, 57, 58));

                if (CollectionUtil.isEmpty(bloodRoadDOS)) {
                    MottoSimpleDO simpleDO1 =
                            mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 57).findFirst().get();
                    MottoSimpleDO simpleDO2 =
                            mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 58).findFirst().get();
                    collect1.put("motto57", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                    collect1.put("motto57Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                    collect1.put("motto57Extra", "");
                    collect1.put("motto58", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                    collect1.put("motto58Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                    collect1.put("motto58Extra", "");
                } else if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                    Set<String> collect2 = bloodRoadDOS.stream().map(BloodRoadDO::getRecipe).collect(Collectors.toSet());
                    if (collect2.size() > 0) {
                        if (collect2.size() == 1 && collect2.contains("1")) {
                            MottoSimpleDO simpleDO1 =
                                    mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 57).findFirst().get();
                            MottoSimpleDO simpleDO2 =
                                    mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 58).findFirst().get();
                            collect1.put("motto57", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                            collect1.put("motto57Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                            collect1.put("motto57Extra", "");
                            collect1.put("motto58", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                            collect1.put("motto58Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                            collect1.put("motto58Extra", "");
                        } else {
                            List<BloodRoadDO> bloodRoadDOList =
                                    bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                            BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                            VascularAccessDO vascularAccessDO = vascularAccessMapper.selectOne(VascularAccessDO::getId,
                                    bloodRoadDO.getType());
                            if (vascularAccessDO != null) {
                                if (0 == vascularAccessDO.getAccessState()) {
                                    if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                                        MottoSimpleDO simpleDO1 =
                                                mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 57).findFirst().get();
                                        MottoSimpleDO simpleDO2 =
                                                mottoSimpleDOS1.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 58).findFirst().get();
                                        collect1.put("motto57", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                                        collect1.put("motto57Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                                        collect1.put("motto57Extra", "");
                                        collect1.put("motto58", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                                        collect1.put("motto58Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                                        collect1.put("motto58Extra", "");
                                    }
                                } else if (1 == vascularAccessDO.getAccessState()) {
                                    if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                                        MottoSimpleDO simpleDO1 =
                                                mottoSimpleDOS1.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 57).findFirst().get();
                                        MottoSimpleDO simpleDO2 =
                                                mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 58).findFirst().get();
                                        collect1.put("motto57", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                                        collect1.put("motto57Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                                        collect1.put("motto57Extra", "");
                                        collect1.put("motto58", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                                        collect1.put("motto58Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                                        collect1.put("motto58Extra", "");
                                    }
                                }
                            }
                        }
                    }
                }
                String s = JSONUtil.toJsonStr(collect1);
                return s;
            }
        }
        return null;
    }

    private List<DialysisAdviceDO> getDialysisAdviceList(HemodialysisManagerCreateReqVO createReqVO) {
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                .eqIfPresent(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                .eqIfPresent(DialysisAdviceDO::getType, "0")
                .eqIfPresent(DialysisAdviceDO::getDateWeek, createReqVO.getHemodialysisTime())
                .select(DialysisAdviceDO::getState, DialysisAdviceDO::getAdviceId));
        return dialysisAdviceDOS;
    }

    private List<DialysisDetectionDO> getDialysisDetectionOne(HemodialysisManagerCreateReqVO createReqVO) {
        List<DialysisDetectionDO> dialysisDetectionDOS = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eq(DialysisDetectionDO::getPatientId, createReqVO.getPatientId())
                .eq(DialysisDetectionDO::getDateWeek, createReqVO.getHemodialysisTime())
                .select(DialysisDetectionDO::getUltrafiltrationCapacity, DialysisDetectionDO::getDisplacementQuantity)
                .orderByDesc(DialysisDetectionDO::getDetectionMin).last("limit 10"));
        return dialysisDetectionDOS;
    }

    private ReachWeighRespVO getReachWeightOne(HemodialysisManagerCreateReqVO createReqVO) {
        if (!StringUtils.isEmpty(createReqVO.getHemodialysisTime())) {
            ReachWeighDO reachWeighDO = reachWeighMapper.selectOne(new LambdaQueryWrapperX<ReachWeighDO>()
                    .eq(ReachWeighDO::getPatientId, createReqVO.getPatientId())
                    .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(createReqVO.getHemodialysisTime()), DateUtil.endOfDay(createReqVO.getHemodialysisTime())));
            return ReachWeighConvert.INSTANCE.convert(reachWeighDO);
        }
        return null;

    }

    private String getLastTimeDefaultBeforeJson(HemodialysisManagerCreateReqVO createReqVO) {
        List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, 3);
        if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
            List<Map<String, Object>> collect = mottoSimpleDOS.stream().map(mottoSimpleDO -> {
                Map<String, Object> map = Maps.newHashMap();
                List<MottoSimpleDO> mottoSimpleDOS1 = mottoSimpleMapper.selectList(MottoSimpleDO::getPid, mottoSimpleDO.getId());
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                    //默认值
                    //正常
                    MottoSimpleDO mottoSimpleDO2 =
                            mottoSimpleDOS1.stream().filter(mottoSimpleDO1 -> 1 == mottoSimpleDO1.getAcquiesce()).findFirst().get();
                    map.put("motto" + mottoSimpleDO.getId(), StrUtil.isEmpty(mottoSimpleDO2.getPname()) ? "" :
                            mottoSimpleDO2.getPname());
                    map.put("motto" + mottoSimpleDO.getId() + "Value",
                            com.google.common.collect.Lists.newArrayList(mottoSimpleDO2.getId()));
                    map.put("motto" + mottoSimpleDO.getId() + "Extra", "");
                }
                return map;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                Map<String, Object> collect1 =
                        collect.stream().flatMap(map -> map.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey,
                                Map.Entry::getValue));
                //查询患者血管通路-通路类型是否默认到处方
                List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(BloodRoadDO::getPatientId, createReqVO.getPatientId());
                //正常
                List<MottoSimpleDO> mottoSimpleDOS1 = mottoSimpleMapper.selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                        .eqIfPresent(MottoSimpleDO::getAcquiesce, 1)
                        .inIfPresent(MottoSimpleDO::getPid, 214, 215));
                //斜杠
                List<MottoSimpleDO> mottoSimpleDOS2 = mottoSimpleMapper.selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                        .eqIfPresent(MottoSimpleDO::getAcquiesce, 2)
                        .inIfPresent(MottoSimpleDO::getPid, 214, 215));

                if (CollectionUtil.isEmpty(bloodRoadDOS)) {
                    MottoSimpleDO simpleDO1 =
                            mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 214).findFirst().get();
                    MottoSimpleDO simpleDO2 =
                            mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 215).findFirst().get();
                    collect1.put("motto214", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                    collect1.put("motto214Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                    collect1.put("motto214Extra", "");
                    collect1.put("motto215", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                    collect1.put("motto215Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                    collect1.put("motto215Extra", "");
                } else if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                    Set<String> collect2 = bloodRoadDOS.stream().map(BloodRoadDO::getRecipe).collect(Collectors.toSet());
                    if (collect2.size() > 0) {
                        if (collect2.size() == 1 && collect2.contains("1")) {
                            MottoSimpleDO simpleDO1 =
                                    mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 214).findFirst().get();
                            MottoSimpleDO simpleDO2 =
                                    mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 215).findFirst().get();
                            collect1.put("motto214", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                            collect1.put("motto214Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                            collect1.put("motto214Extra", "");
                            collect1.put("motto215", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                            collect1.put("motto215Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                            collect1.put("motto215Extra", "");
                        } else {
                            List<BloodRoadDO> bloodRoadDOList =
                                    bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                            BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                            VascularAccessDO vascularAccessDO = vascularAccessMapper.selectOne(VascularAccessDO::getId,
                                    bloodRoadDO.getType());
                            if (vascularAccessDO != null) {
                                if (0 == vascularAccessDO.getAccessState()) {
                                    if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                                        MottoSimpleDO simpleDO1 =
                                                mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 214).findFirst().get();
                                        MottoSimpleDO simpleDO2 =
                                                mottoSimpleDOS1.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 215).findFirst().get();
                                        collect1.put("motto214", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                                        collect1.put("motto214Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                                        collect1.put("motto214Extra", "");
                                        collect1.put("motto215", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                                        collect1.put("motto215Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                                        collect1.put("motto215Extra", "");
                                    }
                                } else if (1 == vascularAccessDO.getAccessState()) {
                                    if (CollectionUtil.isNotEmpty(mottoSimpleDOS1)) {
                                        MottoSimpleDO simpleDO1 =
                                                mottoSimpleDOS1.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 214).findFirst().get();
                                        MottoSimpleDO simpleDO2 =
                                                mottoSimpleDOS2.stream().filter(mottoSimpleDO -> mottoSimpleDO.getPid() == 215).findFirst().get();
                                        collect1.put("motto214", StrUtil.isEmpty(simpleDO1.getPname()) ? "" : simpleDO1.getPname());
                                        collect1.put("motto214Value", com.google.common.collect.Lists.newArrayList(simpleDO1.getId()));
                                        collect1.put("motto214Extra", "");
                                        collect1.put("motto215", StrUtil.isEmpty(simpleDO2.getPname()) ? "" : simpleDO2.getPname());
                                        collect1.put("motto215Value", com.google.common.collect.Lists.newArrayList(simpleDO2.getId()));
                                        collect1.put("motto215Extra", "");
                                    }
                                }
                            }
                        }
                    }
                }
                String s = JSONUtil.toJsonStr(collect1);
                return s;
            }
        }
        return null;
    }


    private HemodialysisManagerDO getDefaultValue(HemodialysisManagerCreateReqVO createReqVO) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND,0);
        Date todayMidnight = calendar.getTime();
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .lt(HemodialysisManagerDO::getHemodialysisTime,todayMidnight)
                .select(HemodialysisManagerDO::getDialyzeAfterWeight, HemodialysisManagerDO::getClothing, HemodialysisManagerDO::getPreremovalWater)
                .orderByDesc(HemodialysisManagerDO::getId).last("limit 1"));
        return hemodialysisManagerDO;
    }

    //获取患者最新干体重信息
    private String getPatientNewDryWeight(Long patientId) {
        DryWeightDO dryWeightDO = dryWeightMapper.selectOne(new LambdaQueryWrapperX<DryWeightDO>()
                .eq(DryWeightDO::getPatientId, patientId)
                .orderByDesc(DryWeightDO::getId)
                .select(DryWeightDO::getDryWeight).last("limit 1"));
        if (dryWeightDO != null) {
            if ("-1".equals(dryWeightDO.getDryWeight())) {
                dryWeightDO.setDryWeight("待定");
            } else if ("-2".equals(dryWeightDO.getDryWeight())) {
                dryWeightDO.setDryWeight("卧床");
            }
            return dryWeightDO.getDryWeight();
        }
        return "0";
    }

    @Override
    public PageResult<HemodialysisManagerDO> getHemodialysisManagerPage(HemodialysisManagerPageReqVO pageReqVO) {
        return hemodialysisManagerMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HemodialysisManagerDO> getHemodialysisManagerList(HemodialysisManagerCreateReqVO createReqVO) {
        return hemodialysisManagerMapper.selectList(createReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long startDialysis(HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:startDialysis:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()),
                                DateUtil.endOfDay(DateUtil.date()))
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0));
                if (arrangeClassesDO != null && arrangeClassesDO.getState() == 2) {
                    throw new ServiceException(GlobalErrorCodeConstants.PATIENT_DIALYSIS);
                }
                //必须签到
                ArrangeClassesDO arrangeClassesDO1 = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .select(ArrangeClassesDO::getState));
                if (arrangeClassesDO1 != null && 0 == arrangeClassesDO1.getState()) {
                    throw new ServiceException(GlobalErrorCodeConstants.NO_REACH_SIGN);
                }
                HemodialysisManagerDO hemodialysisManagerDO1 = HemodialysisManagerConvert.INSTANCE.convert(createReqVO);
                hemodialysisManagerDO1.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());

                hemodialysisManagerDO1.setStartDialyzeTime(createReqVO.getStartTime());
                hemodialysisManagerDO1.setEnterWay(createReqVO.getEnterWay());
                hemodialysisManagerDO1.setInducingBlood(createReqVO.getInducingBlood());

                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .betweenIfPresent(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(DateUtil.date()),
                                DateUtil.endOfDay(DateUtil.date())));

                DialysisDetectionDO dialysisDetectionDO = new DialysisDetectionDO();
                dialysisDetectionDO.setDetectionTime(createReqVO.getStartTime());
                String format = DateUtil.format(createReqVO.getStartTime(), DatePattern.NORM_TIME_PATTERN);
                DateTime parse = DateUtil.parse(format, DatePattern.NORM_TIME_PATTERN);
                dialysisDetectionDO.setDetectionMin(parse);
                dialysisDetectionDO.setDateWeek(DateUtil.beginOfDay(createReqVO.getStartTime()));
                dialysisDetectionDO.setPatientId(createReqVO.getPatientId());
                dialysisDetectionDO.setDialyzeState(0);

                Map<String, Object> map = Maps.newHashMap();
                List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, 4);
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                    for (MottoSimpleDO mottoSimpleDO : mottoSimpleDOS) {
                        Map<String, Object> children = Maps.newHashMap();
                        if (51 == mottoSimpleDO.getId()) {
                            children.put("value", com.google.common.collect.Lists.newArrayList());
                            children.put("label", "");
                            children.put("text", com.google.common.collect.Lists.newArrayList());
                            children.put("patientId", "");
                            children.put("remark", "引血" + createReqVO.getInducingBlood() + "ml/min");
                        } else {
                            children.put("value", com.google.common.collect.Lists.newArrayList());
                            children.put("label", "");
                            children.put("text", com.google.common.collect.Lists.newArrayList());
                            children.put("patientId", "");
                            children.put("remark", "");
                        }
                        map.put(mottoSimpleDO.getId() + "", children);
                    }
                    dialysisDetectionDO.setSymptom(JSONUtil.toJsonStr(map));
                }
                if (hemodialysisManagerDO != null) {
                    hemodialysisManagerMapper.update(hemodialysisManagerDO1, new LambdaQueryWrapperX<HemodialysisManagerDO>()
                            .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                            .eqIfPresent(HemodialysisManagerDO::getDeptId, createReqVO.getDeptId())
                            .betweenIfPresent(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(DateUtil.date()),
                                    DateUtil.endOfDay(DateUtil.date())));
                    //新增检测记录
                    dialysisDetectionDO.setBloodFlow(hemodialysisManagerDO.getBloodFlow());
                    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat sf1 = new SimpleDateFormat("HH:mm");
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    String s = sf.format(dialysisDetectionDO.getDetectionTime()) + " " + sf1.format(dialysisDetectionDO.getDetectionMin());
                    try {
                        dialysisDetectionDO.setDetectionMin(sdf2.parse(s));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }

                    dialysisDetectionMapper.insert(dialysisDetectionDO);
                } else {
                    hemodialysisManagerDO1.setFacilityId(arrangeClassesDO.getFacilityId());
                    hemodialysisManagerMapper.insert(hemodialysisManagerDO1);
                    //都没有确认直接开始透析，获取患者透析方案中的血流量
                    //前端没有传值，后续客户反馈在优化
                    //DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectOne(new LambdaQueryWrapperX<DialysisProtocolDO>()
                    //        .eqIfPresent(DialysisProtocolDO::getPatientId, createReqVO.getPatientId())
                    //        .eqIfPresent(DialysisProtocolDO::getProtocolType, 1)
                    //        .eqIfPresent(DialysisProtocolDO::getPatientDialyzeId, createReqVO.getPatientDialyzeId()));
                    //if (dialysisProtocolDO != null) {
                    //    dialysisDetectionDO.setBloodFlow(dialysisProtocolDO.getBloodFlow());
                    //}
                    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat sf1 = new SimpleDateFormat("HH:mm");
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    String s = sf.format(dialysisDetectionDO.getDetectionTime()) + " " + sf1.format(dialysisDetectionDO.getDetectionMin());
                    try {
                        dialysisDetectionDO.setDetectionMin(sdf2.parse(s));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    dialysisDetectionMapper.insert(dialysisDetectionDO);
                }
                if (arrangeClassesDO != null) {
                    arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                            .set(ArrangeClassesDO::getState, 2)
                            .set(ArrangeClassesDO::getStartDialyzeTime, DateUtil.date())
                            .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                            .between(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date()))
                            .eq(ArrangeClassesDO::getTempType, 0));
                }
                return dialysisDetectionDO.getId();
            } else {
                return null;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("开始透析异常信息：{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long endDialysis(HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:endDialysis:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0));
                if (arrangeClassesDO != null && arrangeClassesDO.getState() == 3) {
                    throw new ServiceException(GlobalErrorCodeConstants.PATIENT_DIALYSIS_END);
                }
                //透析处方未确认不可透析
                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime()));
                if (hemodialysisManagerDO == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.DIALYSIS_PRESCRIPTION);
                }
                if (hemodialysisManagerDO != null && hemodialysisManagerDO.getPrescriptionState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.DIALYSIS_PRESCRIPTION);
                }
                //下机护士
                hemodialysisManagerDO.setOffComputerNurse(createReqVO.getOffComputerNurse());
                DialysisDetectionDO dialysisDetectionDO = new DialysisDetectionDO();
                dialysisDetectionDO.setBloodFlow("100");
                dialysisDetectionDO.setDetectionTime(createReqVO.getEndTime());
                String format = DateUtil.format(createReqVO.getEndTime(), DatePattern.NORM_TIME_PATTERN);
                DateTime parse = DateUtil.parse(format, DatePattern.NORM_TIME_PATTERN);
                dialysisDetectionDO.setDetectionMin(parse);
                dialysisDetectionDO.setDateWeek(DateUtil.beginOfDay(createReqVO.getEndTime()));
                dialysisDetectionDO.setPatientId(createReqVO.getPatientId());
                dialysisDetectionDO.setDialyzeState(1);
                Map<String, Object> map = Maps.newHashMap();
                List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, 4);
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                    for (MottoSimpleDO mottoSimpleDO : mottoSimpleDOS) {
                        Map<String, Object> children = Maps.newHashMap();
                        if (51 == mottoSimpleDO.getId()) {
                            children.put("value", com.google.common.collect.Lists.newArrayList());
                            children.put("label", "");
                            children.put("text", com.google.common.collect.Lists.newArrayList());
                            children.put("patientId", "");
                            children.put("remark", "");
                        } else {
                            children.put("value", com.google.common.collect.Lists.newArrayList());
                            children.put("label", "");
                            children.put("text", com.google.common.collect.Lists.newArrayList());
                            children.put("patientId", "");
                            children.put("remark", "");
                        }
                        map.put(mottoSimpleDO.getId() + "", children);
                    }
                    dialysisDetectionDO.setSymptom(JSONUtil.toJsonStr(map));
                }
                if (arrangeClassesDO != null) {
                    arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                            .set(ArrangeClassesDO::getState, 3)
                            .set(ArrangeClassesDO::getEndDialyzeTime, createReqVO.getEndTime())
                            .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                            .eq(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                            .eq(ArrangeClassesDO::getTempType, 0));
                }
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                SimpleDateFormat sf1 = new SimpleDateFormat("HH:mm");
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String s = sf.format(dialysisDetectionDO.getDetectionTime()) + " " + sf1.format(dialysisDetectionDO.getDetectionMin());
                try {
                    dialysisDetectionDO.setDetectionMin(sdf2.parse(s));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                //新增检测记录
                dialysisDetectionMapper.insert(dialysisDetectionDO);
                //新增透析记录
                ArrangeClassesDO arrangeClassesDO1 = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0));
                if (arrangeClassesDO1 != null) {
                    PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
                    FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
                    FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(arrangeClassesDO.getFacilitySubareaId());
                    DialysisRecordDO dialysisRecordDO = new DialysisRecordDO();
                    dialysisRecordDO.setEndDialyzeTime(createReqVO.getEndTime());
                    dialysisRecordDO.setDateWeek(createReqVO.getHemodialysisTime());
                    dialysisRecordDO.setWeekDay(arrangeClassesDO.getDayState());
                    dialysisRecordDO.setFacilityId(arrangeClassesDO.getFacilityId());
                    dialysisRecordDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                    dialysisRecordDO.setFacilityName(facilityDO == null ? "" : facilityDO.getCode());
                    dialysisRecordDO.setFacilitySubareaName(facilitySubareaDO == null ? "" : facilitySubareaDO.getName());
                    dialysisRecordDO.setAreaCode(dialysisRecordDO.getFacilitySubareaName() + "/" + dialysisRecordDO.getFacilityName());
                    dialysisRecordDO.setDialyzeWayValue(arrangeClassesDO.getDialysisName());
                    dialysisRecordDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                    //透析处方-透析时长-目标脱水量-干体重
                    if (hemodialysisManagerDO != null) {
                        dialysisRecordDO.setDuration(hemodialysisManagerDO.getDuration());
                        dialysisRecordDO.setDryWeight(hemodialysisManagerDO.getDryWeight());
                        dialysisRecordDO.setTargetDehydratedLevel(hemodialysisManagerDO.getDehydration());
                        //透前评估-透前体重-超滤总量-脉搏透前-透前血压
                        dialysisRecordDO.setDialyzeBeforeWeight(hemodialysisManagerDO.getDialyzeBeforeWeight());
                        dialysisRecordDO.setUltrafiltrationTotal(hemodialysisManagerDO.getBeforeUltrafiltrationtotal());
                        dialysisRecordDO.setBeforePNo(hemodialysisManagerDO.getPno());
                        dialysisRecordDO.setBeforeBpNoOne(hemodialysisManagerDO.getBpNoOne());
                        dialysisRecordDO.setBeforeBpNoTwo(hemodialysisManagerDO.getBpNoTwo());
                        //透后评估-透后体重-实际超滤总量-实际治疗时长-脉搏透后-透后血压-透后症状
                        dialysisRecordDO.setDialyzeAfterWeigh(hemodialysisManagerDO.getDialyzeAfterWeight());
                        dialysisRecordDO.setActualUltrafiltrationCapacity(hemodialysisManagerDO.getActualUltrafiltrationCapacity());
                        dialysisRecordDO.setTreatment(hemodialysisManagerDO.getTreatmentHour() + "/" + hemodialysisManagerDO.getTreatmentMin());
                        dialysisRecordDO.setAfterPNo(hemodialysisManagerDO.getAfterPNo());
                        dialysisRecordDO.setAfterBpNoOne(hemodialysisManagerDO.getAfterBpOne());
                        dialysisRecordDO.setAfterBpNoTwo(hemodialysisManagerDO.getBpNoTwo());
                        if (patientDO != null) {
                            dialysisRecordDO.setPatientId(createReqVO.getPatientId());
                            dialysisRecordDO.setPatientSource(patientDO.getPatientSource());
                            dialysisRecordDO.setPatientName(patientDO.getName());
                            dialysisRecordDO.setDialyzeNo(patientDO.getDialyzeNo());
                            dialysisRecordDO.setSpellName(patientDO.getSpellName());
                        }
                        hemodialysisManagerDO.setEndDialyzeTime(createReqVO.getEndTime());
                        hemodialysisManagerMapper.updateById(hemodialysisManagerDO);
                        dialysisRecordMapper.insert(dialysisRecordDO);

                        Object cnrdsReportFlag = RedisUtils.getCacheObject(Constants.CNRDS_REPORT_FLAG);
                        if("Y".equals(cnrdsReportFlag)) {
                            //异步执行  同步患者信息到国网
                            if ("N".equals(patientDO.getCnrdsSyncFlag())) {
                                try {
                                    cnrdsManager.reportPatientInfo(patientDO.getId());
                                } catch (Exception e) {
                                    log.info(e.getMessage(), e);
                                }
                            }
                        }
                    }
                }
                return dialysisDetectionDO.getId();
            } else {
                return null;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("结束透析异常信息:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
        return null;
    }

    private String time(HemodialysisManagerRespVO hemodialysisManagerRespVO) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            List<DialysisDetectionDO> dialysisDetectionDOS =
                    dialysisDetectionMapper.selectList(new MPJLambdaWrapper<>(DialysisDetectionDO.class).eq(DialysisDetectionDO::getPatientId,
                            hemodialysisManagerRespVO.getPatientId())
                            .like(DialysisDetectionDO::getDateWeek, sdf.format(hemodialysisManagerRespVO.getStartDialyzeTime()))
                            .in(DialysisDetectionDO::getDialyzeState, 0, 1));
            DialysisTimeVo vo = new DialysisTimeVo();
            if (CollectionUtil.isNotEmpty(dialysisDetectionDOS)) {
                List<DialysisDetectionDO> collect = dialysisDetectionDOS.stream().filter(dialysisDetectionDO -> 1 == dialysisDetectionDO.getDialyzeState()).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    return null;
                }
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dialysisDetectionDOS.forEach(detectionDO -> {
                    switch (detectionDO.getDialyzeState()) {
                        case 0:
                            vo.setStartTime(sf.format(detectionDO.getDetectionMin()));
                            break;
                        case 1:
                            vo.setEndTime(sf.format(detectionDO.getDetectionMin()));
                            break;
                    }
                });
                LocalDateTime startTime = LocalDateTime.ofInstant(sf.parse(vo.getStartTime()).toInstant(), ZoneId.systemDefault());
                LocalDateTime endTime = LocalDateTime.ofInstant(sf.parse(vo.getEndTime()).toInstant(), ZoneId.systemDefault());
                Duration duration = Duration.between(startTime, endTime);
                long seconds = duration.getSeconds();
                int h = seconds > 3600 ? (int) (seconds / 3600) : 0;
                int m = (seconds - 3600 * h) > 60 ? (int) ((seconds - 3600 * h) / 60) : 0;
                return h + "," + m;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public void pigeonholeDialysis(HemodialysisManagerCreateReqVO createReqVO) {
        //必须都确认和临时医嘱全部执行才可以自查
        if (4 == createReqVO.getState()) {
            HemodialysisManagerDO managerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                    .eqIfPresent(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                    .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId()));
            if (managerDO != null) {
                //透前评估未确认
                if (managerDO.getBeforeState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.BEFORE_STATE);
                }
                //透析处方未确认
                if (managerDO.getPrescriptionState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.PRESCRIPTION_STATE);
                }
                //临时医嘱未执行
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eqIfPresent(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(DialysisAdviceDO::getType, "0")
                        .eqIfPresent(DialysisAdviceDO::getDateWeek, createReqVO.getHemodialysisTime())
                        .select(DialysisAdviceDO::getState,DialysisAdviceDO::getDrugType));
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                    List<DialysisAdviceDO> dialysisAdviceDOS1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> "0".equals(dialysisAdviceDO.getState()) && (StringUtils.isEmpty(dialysisAdviceDO.getDrugType()) || dialysisAdviceDO.getDrugType() !=2)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(dialysisAdviceDOS1)) {
                        throw new ServiceException(GlobalErrorCodeConstants.NO_ADVICE);
                    }
                }
                //双人核对未确认
                if (managerDO.getCheckState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.CHECK_STATE);
                }
                //检测记录未确认
                if (managerDO.getMonitState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.MONIT_STATE);
                }
                //透后评估未确认
                if (managerDO.getAfterState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.AFTER_STATE);
                }
                //治疗小结未确认
                if (managerDO.getHealState() == null) {
                    throw new ServiceException(GlobalErrorCodeConstants.HEAL_STATE);
                }

            }
        }
        arrangeClassesMapper.update(new ArrangeClassesDO(), new LambdaUpdateWrapper<ArrangeClassesDO>()
                .set(ArrangeClassesDO::getState, createReqVO.getState())
                .eq(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                .eq(ArrangeClassesDO::getTempType, 0));
    }

    @Override
    public List<HemodialysisManagerRespVO> consumablePage(HemodialysisManagerRespVO pageVO) {
        List<HemodialysisManagerDO> hemodialysisManagerDOS =
                hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eqIfPresent(HemodialysisManagerDO::getHemodialysisTime, pageVO.getHemodialysisTime()));
        List<HemodialysisManagerRespVO> hemodialysisManagerRespVOS =
                HemodialysisManagerConvert.INSTANCE.convertList(hemodialysisManagerDOS);
        if (CollectionUtil.isNotEmpty(hemodialysisManagerRespVOS)) {

        }
        return null;
    }

    @Override
    public List<HemodialysisManagerRespVO> useInstrumentInfo(HemodialysisManagerCreateReqVO createReqVO) {
        List list = Lists.newArrayList();
        HemodialysisManagerDO hemodialysisManagerDO =
                hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .betweenIfPresent(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(new Date()),
                                DateUtil.endOfDay(new Date())));
        if (hemodialysisManagerDO != null) {
            if (StrUtil.isNotEmpty(hemodialysisManagerDO.getHemodialysisDevice())) {
                HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectById(hemodialysisManagerDO.getHemodialysisDevice());
                HemodialysisManagerRespVO hemodialysisManagerRespVO = new HemodialysisManagerRespVO();
                hemodialysisManagerRespVO.setInstrumentName("血透器");
                hemodialysisManagerRespVO.setNum(1);
                if (hisConsumablesDO != null) {
                    hemodialysisManagerRespVO.setSpecification(hisConsumablesDO.getConsumType());
                    hemodialysisManagerRespVO.setLotNumber(hisConsumablesDO.getApprovalNumber());
                }
                list.add(hemodialysisManagerRespVO);
            }
            if (StrUtil.isNotEmpty(hemodialysisManagerDO.getBloodFilter())) {
                HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectById(hemodialysisManagerDO.getBloodFilter());
                HemodialysisManagerRespVO hemodialysisManagerRespVO = new HemodialysisManagerRespVO();
                hemodialysisManagerRespVO.setInstrumentName("血滤器");
                hemodialysisManagerRespVO.setNum(1);
                if (hisConsumablesDO != null) {
                    hemodialysisManagerRespVO.setSpecification(hisConsumablesDO.getConsumType());
                    hemodialysisManagerRespVO.setLotNumber(hisConsumablesDO.getApprovalNumber());
                }
                list.add(hemodialysisManagerRespVO);
            }
            if (StrUtil.isNotEmpty(hemodialysisManagerDO.getPerfumer())) {
                HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectById(hemodialysisManagerDO.getPerfumer());
                HemodialysisManagerRespVO hemodialysisManagerRespVO = new HemodialysisManagerRespVO();
                hemodialysisManagerRespVO.setInstrumentName("灌流器");
                hemodialysisManagerRespVO.setNum(1);
                if (hisConsumablesDO != null) {
                    hemodialysisManagerRespVO.setSpecification(hisConsumablesDO.getConsumType());
                    hemodialysisManagerRespVO.setLotNumber(hisConsumablesDO.getApprovalNumber());
                }
                list.add(hemodialysisManagerRespVO);
            }
        }
        return list;
    }

    @Override
    public List<HemodialysisManagerPrintRespVO> getPrintInfo(HemodialysisManagerPrintRespVO printRespVO) {
        List<String> collect = Arrays.stream(printRespVO.getPatientIds().split(",")).collect(Collectors.toList());
        List<HemodialysisManagerPrintRespVO> printRespVOS = Lists.newArrayList();
        List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getId, collect);
        if (CollectionUtil.isNotEmpty(patientDOS)) {
            for (PatientDO patientDO : patientDOS) {
                HemodialysisManagerPrintRespVO hemodialysisManagerPrintRespVO = new HemodialysisManagerPrintRespVO();
                BeanUtil.copyProperties(patientDO, hemodialysisManagerPrintRespVO);
                hemodialysisManagerPrintRespVO.setPatientId(patientDO.getId());
                //血液透析
                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(HemodialysisManagerDO::getPatientId
                        , patientDO.getId(), HemodialysisManagerDO::getHemodialysisTime, printRespVO.getHemodialysisTime());
                BeanUtil.copyProperties(hemodialysisManagerDO, hemodialysisManagerPrintRespVO);
                if (hemodialysisManagerDO != null) {
                    //机型
                    if (StrUtil.isNotEmpty(hemodialysisManagerDO.getFacilityTypeId())) {
                        List<String> idList =
                                Arrays.stream(hemodialysisManagerDO.getFacilityTypeId().split(",")).collect(Collectors.toList());
                        List<FacilityNameDO> facilityNameDOList = facilityNameMapper.selectList(FacilityNameDO::getId, idList);
                        if (CollectionUtil.isNotEmpty(facilityNameDOList)) {
                            String facilityTypeName =
                                    facilityNameDOList.stream().map(FacilityNameDO::getName).collect(Collectors.joining("/"));
                            hemodialysisManagerPrintRespVO.setFacilityTypeName(facilityTypeName);
                        }
                    }
                    //病区
                    if (hemodialysisManagerDO.getFacilitySubareaId() != null) {
                        FacilitySubareaDO facilitySubareaDO =
                                facilitySubareaMapper.selectById(hemodialysisManagerDO.getFacilitySubareaId());
                        hemodialysisManagerPrintRespVO.setFacilitySubareaName(facilitySubareaDO == null ? null :
                                facilitySubareaDO.getName());
                    }
                }
                //临时医嘱
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPatientId,
                        patientDO.getId(), DialysisAdviceDO::getDateWeek, printRespVO.getHemodialysisTime());
                hemodialysisManagerPrintRespVO.setDialysisAdviceDOS(dialysisAdviceDOS);
                //检测记录
                List<DialysisDetectionDO> dialysisDetectionDOS = dialysisDetectionMapper.selectList(DialysisDetectionDO::getPatientId
                        , patientDO.getId(), DialysisDetectionDO::getDateWeek, printRespVO.getHemodialysisTime());
                hemodialysisManagerPrintRespVO.setDialysisDetectionDOS(dialysisDetectionDOS);

                //抗凝剂
                List<ContradictDO> contradictDOList = contradictMapper.selectList(new LambdaQueryWrapperX<ContradictDO>()
                        .eqIfPresent(ContradictDO::getPatientId, patientDO.getId())
                        .eqIfPresent(ContradictDO::getProtocolType, 2));
                List<ContradictRespVO> contradictRespVOList = ContradictConvert.INSTANCE.convertList(contradictDOList);
                if (CollectionUtil.isNotEmpty(contradictRespVOList)) {
                    contradictRespVOList.stream().peek(contradictRespVO -> {
                        DrugTypeDO drugTypeDO = drugTypeMapper.selectById(contradictRespVO.getDrugTypeId());
                        contradictRespVO.setDoseUnit(drugTypeDO != null ? drugTypeDO.getDoseUnit() : null);
                    });
                    hemodialysisManagerPrintRespVO.setContradictDOS(contradictRespVOList);
                }

                printRespVOS.add(hemodialysisManagerPrintRespVO);
            }
        }
        return printRespVOS;
    }

    @Override
    public HemodialysisManagerRespVO getDailysisTime(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(HemodialysisManagerDO::getPatientId,
                createReqVO.getPatientId(), HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime());
        HemodialysisManagerRespVO hemodialysisManagerRespVO = HemodialysisManagerConvert.INSTANCE.convert(hemodialysisManagerDO);
        if (hemodialysisManagerRespVO != null && StrUtil.isNotEmpty(hemodialysisManagerRespVO.getDuration()) && StrUtil.isNotEmpty(hemodialysisManagerRespVO.getDurationMin())) {
            DateTime dateTime = DateUtil.offsetMinute(hemodialysisManagerRespVO.getStartDialyzeTime(),
                    Integer.valueOf(hemodialysisManagerRespVO.getDuration()) * 60 + Integer.valueOf(hemodialysisManagerRespVO.getDurationMin()));
            hemodialysisManagerRespVO.setEndDialyzeTime(dateTime);
            return hemodialysisManagerRespVO;
        }
        return null;
    }

    @Override
    public List<DialysisAdviceRespVO> medicatePush(HemodialysisManagerCreateReqVO createReqVO) {
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                .eq(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                .eq(DialysisAdviceDO::getMedicateState, 1)
                .eq(DialysisAdviceDO::getStopStatus, 0)
                .eq(DialysisAdviceDO::getDeleted,0)
                .eq(DialysisAdviceDO::getType,1)
                .apply("create_time <'2024-12-13 00:00:00'"));
        if (!StringUtils.isEmpty(createReqVO.getClassesTime())) {
            List<DialysisAdviceDO> dialysisAdviceList;
            List<DialysisAdviceDO> dialysisAdviceDOS1 = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                    .eq(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                    .eq(DialysisAdviceDO::getStopStatus, 0)
                    .eq(DialysisAdviceDO::getDeleted, 0)
                    .eq(DialysisAdviceDO::getAdviceTime, createReqVO.getClassesTime())
                    .isNotNull(DialysisAdviceDO::getLongAdviceId)
                    .isNull(DialysisAdviceDO::getDateWeek)
                    .eq(DialysisAdviceDO::getPushStatus, 1));
            if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS1)) {
                dialysisAdviceDOS1 = dialysisAdviceDOS1.stream().filter(dialysisAdviceDO -> {
                    DialysisAdviceDO dialysisAdviceDO1 = dialysisAdviceMapper.selectById(dialysisAdviceDO.getLongAdviceId());
                    if (StringUtils.isEmpty(dialysisAdviceDO1) || dialysisAdviceDO1.getDeleted() || !"0".equals(dialysisAdviceDO1.getStopStatus())) {
                        return false;
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
            }
            if(!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS) && !org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS1)) {
                List<String> collect = dialysisAdviceDOS1.stream().map(DialysisAdviceDO::getLongAdviceId).map(dialysisAdvice -> String.valueOf(dialysisAdvice)).collect(Collectors.toList());
                dialysisAdviceDOS = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> !collect.contains(String.valueOf(dialysisAdviceDO.getId()))).collect(Collectors.toList());
            }
            // 查询已推送但不在今天
            dialysisAdviceList = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                    .eq(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                    .eq(DialysisAdviceDO::getMedicateState, 1)
                    .eq(DialysisAdviceDO::getStopStatus, 0)
                    .eq(DialysisAdviceDO::getDeleted,0)
                    .eq(DialysisAdviceDO::getType,1)
                    .apply("create_time > '2024-12-13 00:00:00'"));
            if ( !org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceList)) {
                List<String> collect = dialysisAdviceDOS1.stream().map(DialysisAdviceDO::getLongAdviceId).map(dialysisAdvice -> String.valueOf(dialysisAdvice)).collect(Collectors.toList());
                dialysisAdviceList = dialysisAdviceList.stream().filter(dialysisAdviceDO -> {
                    dialysisAdviceDO.setMedicateState(0);
                    if (!collect.contains(String.valueOf(dialysisAdviceDO.getId()))){

                        return true;
                    }else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }

            dialysisAdviceDOS.addAll(dialysisAdviceDOS1);
            dialysisAdviceDOS.addAll(dialysisAdviceList);
        }
        // 查询未推送医嘱
        List<DialysisAdviceDO> dialysisAdviceUnPushDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                        .eq(DialysisAdviceDO::getMedicateState, 0)
                        .eq(DialysisAdviceDO::getStopStatus, 0)
                        .eq(DialysisAdviceDO::getDeleted,0)
                        .eq(DialysisAdviceDO::getType,1));
        dialysisAdviceDOS.addAll(dialysisAdviceUnPushDOS);

        List<DialysisAdviceRespVO> dialysisAdviceRespVOS = DialysisAdviceConvert.INSTANCE.convertList(dialysisAdviceDOS);
        List<DialysisAdviceRespVO> dialysisAdviceRespVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dialysisAdviceRespVOS)) {
            /*Iterator<DialysisAdviceRespVO> iterator = dialysisAdviceRespVOS.iterator();
            // 筛除已停用医嘱
            while(iterator.hasNext()) {
                Long adviceId = iterator.next().getAdviceId();
                HisDrugDO hisDrugDO = hisDrugMapper.selectById(adviceId);
                if (!StringUtils.isEmpty(hisDrugDO)) {
                    if (hisDrugDO.getFEnabledMark().equals("0")) {
                        iterator.remove();
                    }
                }else {
                    DrugDO drugDO = drugMapper.selectById(adviceId);
                    if (!StringUtils.isEmpty(drugDO)) {
                        if (drugDO.getDeleted()) {
                            iterator.remove();
                        }
                    }else {
                        iterator.remove();
                    }
                }
            }*/


            dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().peek(dialysisAdviceRespVO -> {
                //开嘱医生
                AdminUserRespDTO user = adminUserApi.getUser(dialysisAdviceRespVO.getAdviceUser());
                dialysisAdviceRespVO.setAdviceUserName(user != null ? user.getNickname() : null);
                //开始时间
                String format = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
                if (dialysisAdviceRespVO.getStartTime() != null) {
                    DateTime parse = DateUtil.parse(format + " " + DateUtil.format(dialysisAdviceRespVO.getStartTime(),
                            DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
                    dialysisAdviceRespVO.setStartTime(parse);
                }
                //医嘱时间
                dialysisAdviceRespVO.setAdviceTime(new Date());
                if (0==user.getStatus()) {
                    dialysisAdviceRespVOList.add(dialysisAdviceRespVO);
                }
            }).collect(Collectors.toList());
        }
        return dialysisAdviceRespVOList;
    }

    @Override
    public List<RecentlyDialysisListVo> getRecentlyDialysisList(String patientId) {
        return hemodialysisManagerMapper.getRecentlyDialysisList(patientId);
    }

    @Override
    public HemodialysisManagerRespVO getLastHealInfo(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerDO managerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eqIfPresent(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .select(HemodialysisManagerDO::getPunctureNeedle, HemodialysisManagerDO::getPunctureMethod
                        , HemodialysisManagerDO::getInducingBlood, HemodialysisManagerDO::getEnterWay)
                .last("limit 1")
                .orderByDesc(HemodialysisManagerDO::getHemodialysisTime));
        HemodialysisManagerRespVO hemodialysisManagerRespVO = HemodialysisManagerConvert.INSTANCE.convert(managerDO);
        if (hemodialysisManagerRespVO != null) {
            if (StrUtil.isNotEmpty(hemodialysisManagerRespVO.getPunctureNeedle())) {
                List<String> collect = Arrays.stream(hemodialysisManagerRespVO.getPunctureNeedle().split(",")).collect(Collectors.toList());
                hemodialysisManagerRespVO.setPunctureNeedles(collect);
            }
        }
        return hemodialysisManagerRespVO;
    }

    @Override
    public List<ArrangeClassesDO> getLastRecord(ArrangeClassesLastVo vo) {
        return hemodialysisManagerMapper.getLastRecord(vo);
    }

    @Override
    public PageResult<DialysisPreparationRespVO> getDialysisPreparation(HemodialysisManagerPageReqVO pageReqVO) {
        IPage<DialysisPreparationRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        MPJLambdaWrapper<ArrangeClassesDO> wrapper = new MPJLambdaWrapper<>(ArrangeClassesDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, ArrangeClassesDO::getPatientId)
                .select(PatientDO::getBirthday, PatientDO::getName, PatientDO::getAge, PatientDO::getHospitalNo, PatientDO::getDialyzeNo, PatientDO::getSex)
                .selectAs(PatientDO::getId, "patientId")
                .selectAs(ArrangeClassesDO::getFacilityName, "code")
                .selectAs(ArrangeClassesDO::getDialysisValue, "dialyzeWayValue")
                .selectAs(ArrangeClassesDO::getFaciitySubareaName, "subareaName")
                .select(ArrangeClassesDO::getFacilityId, ArrangeClassesDO::getFacilitySubareaId, ArrangeClassesDO::getDayState,ArrangeClassesDO::getProtocolId)
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(ArrangeClassesDO::getClassesTime, pageReqVO.getHemodialysisTime())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getDayStateList()), ArrangeClassesDO::getDayState, pageReqVO.getDayStateList())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getFacilitySubareaIdList()), ArrangeClassesDO::getFacilitySubareaId, pageReqVO.getFacilitySubareaIdList());
        IPage<DialysisPreparationRespVO> hemodialysisManagerRespVOIPage = arrangeClassesMapper.selectJoinPage(page, DialysisPreparationRespVO.class, wrapper);
        PageResult<DialysisPreparationRespVO> pageResult = new PageResult<>();
        List<DialysisPreparationRespVO> records = hemodialysisManagerRespVOIPage.getRecords();
        if (!org.springframework.util.CollectionUtils.isEmpty(records)) {
            records = records.stream().sorted(Comparator.comparing(DialysisPreparationRespVO::getFacilityId)).collect(Collectors.toList());
        }
        pageResult.setList(records);
        pageResult.setTotal(hemodialysisManagerRespVOIPage.getTotal());
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<DialysisPreparationRespVO> dialysisPreparationRespVOS = pageResult.getList().stream().peek(dialysisPreparationRespVO -> {
                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .select(HemodialysisManagerDO::getPatientId, HemodialysisManagerDO::getId, HemodialysisManagerDO::getFacilityId, HemodialysisManagerDO::getDryWeight, HemodialysisManagerDO::getFacilitySubareaId, HemodialysisManagerDO::getPrescriptionState, HemodialysisManagerDO::getFacilityTypeId
                                , HemodialysisManagerDO::getHemodialysisDevice, HemodialysisManagerDO::getBloodFilter, HemodialysisManagerDO::getPerfumer, HemodialysisManagerDO::getPotassium, HemodialysisManagerDO::getCalcium
                                , HemodialysisManagerDO::getGlucose, HemodialysisManagerDO::getVascularAccessOne, HemodialysisManagerDO::getVascularAccessTwo, HemodialysisManagerDO::getPunctureNeedle, HemodialysisManagerDO::getPunctureNeedleModel, HemodialysisManagerDO::getDayState
                                , HemodialysisManagerDO::getDialyzeBeforeWeight, HemodialysisManagerDO::getBpNoOne, HemodialysisManagerDO::getBpNoTwo, HemodialysisManagerDO::getDehydration, HemodialysisManagerDO::getPrescriptionEhydratedLevel
                                , HemodialysisManagerDO::getSubstituteTodal, HemodialysisManagerDO::getBeforeUltrafiltrationtotal, HemodialysisManagerDO::getBloodFlow, HemodialysisManagerDO::getDialyzeWayValue
                                , HemodialysisManagerDO::getDuration, HemodialysisManagerDO::getDurationMin, HemodialysisManagerDO::getFormulaSodium, HemodialysisManagerDO::getPrescriptionSodium, HemodialysisManagerDO::getBicarbonate, HemodialysisManagerDO::getHemodialysisTime,HemodialysisManagerDO::getDialysateFlowrate)
                        .eq(HemodialysisManagerDO::getHemodialysisTime, pageReqVO.getHemodialysisTime())
                        .eq(HemodialysisManagerDO::getPatientId, dialysisPreparationRespVO.getPatientId()));
                if (hemodialysisManagerDO != null) {
                    BeanUtil.copyProperties(hemodialysisManagerDO, dialysisPreparationRespVO, CopyOptions.create().setIgnoreNullValue(true));
                    dialysisPreparationRespVO.setProtocolType(2);
                    dialysisPreparationRespVO.setPrescriptionName("已确认");
                } else {
                    DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectOne(new LambdaQueryWrapperX<DialysisProtocolDO>()
                            .eq(DialysisProtocolDO::getProtocolType, 1)
                            .eq(DialysisProtocolDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                            .eq(DialysisProtocolDO::getDialyzeId, dialysisPreparationRespVO.getDialyzeWayValue())
                            .eqIfPresent(DialysisProtocolDO::getId,dialysisPreparationRespVO.getProtocolId()));
                    if (dialysisProtocolDO != null) {
                        BeanUtil.copyProperties(dialysisProtocolDO, dialysisPreparationRespVO);
                    }
                    FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(new LambdaQueryWrapperX<FacilityManagerDO>()
                            .select(FacilityManagerDO::getId, FacilityManagerDO::getFacilityTypeId)
                            .eq(FacilityManagerDO::getFacilityId, dialysisPreparationRespVO.getFacilityId()));
                    if (facilityManagerDO != null) {
                        dialysisPreparationRespVO.setFacilityTypeId(facilityManagerDO.getFacilityTypeId());
                    }
                    dialysisPreparationRespVO.setProtocolType(1);
                    dialysisPreparationRespVO.setPrescriptionName("未确认");
                    dialysisPreparationRespVO.setHemodialysisTime(pageReqVO.getHemodialysisTime());
                }
                //设备型号
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getFacilityTypeId())) {
                    List<Long> facilityNameIds = Arrays.stream(dialysisPreparationRespVO.getFacilityTypeId().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    List<FacilityNameDO> facilityNameDOList = facilityNameMapper.selectList(new LambdaQueryWrapperX<FacilityNameDO>()
                            .select(FacilityNameDO::getName)
                            .in(FacilityNameDO::getId, facilityNameIds)
                            .eq(FacilityNameDO::getType, 2));
                    if (CollectionUtil.isNotEmpty(facilityNameDOList)) {
                        String facilityTypeNames = facilityNameDOList.stream().map(FacilityNameDO::getName).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setFacilityTypeName(facilityTypeNames);
                    }
                }
                //性别
                dialysisPreparationRespVO.setSex("1".equals(dialysisPreparationRespVO.getSex()) ? "男" : "女");
                //处方状态
                //dialysisPreparationRespVO.setPrescriptionName(dialysisPreparationRespVO.getPrescriptionState() == null ? "未确认" : "已确认");
                //血透器
                HisConsumablesDO hemodialysisDevice = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getHemodialysisDevice())
                        .select(HisConsumablesDO::getConsumSpec));
                dialysisPreparationRespVO.setHemodialysisDeviceName(hemodialysisDevice == null ? null : hemodialysisDevice.getConsumSpec());
                //血滤器
                HisConsumablesDO bloodFilter = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getBloodFilter())
                        .select(HisConsumablesDO::getConsumSpec));
                dialysisPreparationRespVO.setBloodFilterName(bloodFilter == null ? null : bloodFilter.getConsumSpec());
                HisConsumablesDO perfumer= null;
                //灌流器
                if (!StringUtils.isEmpty(dialysisPreparationRespVO.getPerfumer())) {
                    perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                            .eqIfPresent(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getPerfumer())
                            .select(HisConsumablesDO::getConsumSpec));

                }
                if (StringUtils.isEmpty(dialysisPreparationRespVO.getPerfumer()) && !StringUtils.isEmpty(dialysisPreparationRespVO.getPerfume())) {
                    perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                            .eqIfPresent(HisConsumablesDO::getConsumId,dialysisPreparationRespVO.getPerfume())
                            .select(HisConsumablesDO::getConsumSpec));
                }

                dialysisPreparationRespVO.setPerfumerName(perfumer == null ? null : perfumer.getConsumSpec());

                //人工肾
                StringBuilder sb1 = new StringBuilder();
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getHemodialysisDevice())) {
                    sb1.append(hemodialysisDevice == null ? "" : hemodialysisDevice.getConsumSpec()).append(",");
                }
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getBloodFilter())) {
                    sb1.append(bloodFilter == null ? "" : bloodFilter.getConsumSpec()).append(",");
                }
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfumer()) || StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfume())) {
                    sb1.append(perfumer == null ? null : perfumer.getConsumSpec());
                }

                dialysisPreparationRespVO.setArtificialKidney(sb1.toString().replaceAll(",+$", ""));
                //管路
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getVascularAccessOne()) && StrUtil.isNotEmpty(dialysisPreparationRespVO.getVascularAccessTwo())) {
                    List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(new LambdaQueryWrapperX<VascularAccessDO>()
                            .select(VascularAccessDO::getName)
                            .in(VascularAccessDO::getId, dialysisPreparationRespVO.getVascularAccessOne(), dialysisPreparationRespVO.getVascularAccessTwo()));
                    if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                        String vascular = "";
                        if (vascularAccessDOS.size() > 1) {
                            VascularAccessDO vascularAccessDO = vascularAccessDOS.get(1);
                            if(vascularAccessDO.getName().contains("内瘘")){
                                vascular+="内瘘";
                            }else if (vascularAccessDO.getName().contains("导管")) {
                                vascular +="导管";
                            }
                            VascularAccessDO vascularAccessDO1 = vascularAccessDOS.get(0);
                            vascular  = vascular+"("+ vascularAccessDO1.getName() +")";
                        }
                        dialysisPreparationRespVO.setPipeline(vascular);
                    }
                }else {
                    DialyzeOptionRespVO dialyzeInfo = dialyzeOptionService.getDialyzeInfo("1", dialysisPreparationRespVO.getPatientId(), null);
                    List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(new LambdaQueryWrapperX<VascularAccessDO>()
                            .select(VascularAccessDO::getName)
                            .in(VascularAccessDO::getId, dialyzeInfo.getBloodPart(), dialyzeInfo.getBloodType()));
                    if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {

                        if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                            String vascular = "";
                            if (vascularAccessDOS.size() > 1) {
                                VascularAccessDO vascularAccessDO = vascularAccessDOS.get(1);
                                if(vascularAccessDO.getName().contains("内瘘")){
                                    vascular+="内瘘";
                                }else if (vascularAccessDO.getName().contains("导管")) {
                                    vascular +="导管";
                                }
                                VascularAccessDO vascularAccessDO1 = vascularAccessDOS.get(0);
                                vascular  = vascular+"("+ vascularAccessDO1.getName() +")";
                            }
                            dialysisPreparationRespVO.setPipeline(vascular);
                        }
                    }
                }
                String pipeline = dialysisPreparationRespVO.getPipeline();
                if (!StringUtils.isEmpty(pipeline) && pipeline.contains("内瘘")) {
                    dialysisPreparationRespVO.setInternalPackage(1);
                    dialysisPreparationRespVO.setUpMidPackage(0);
                    dialysisPreparationRespVO.setDownMidPackage(0);
                }else if (!StringUtils.isEmpty(pipeline) &&pipeline.contains("导管")) {
                    dialysisPreparationRespVO.setUpMidPackage(1);
                    dialysisPreparationRespVO.setDownMidPackage(1);
                    dialysisPreparationRespVO.setInternalPackage(0);
                }
                //穿刺针
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedle())) {
                    List<String> needleIds = Arrays.stream(dialysisPreparationRespVO.getPunctureNeedle().split(",")).collect(Collectors.toList());
                    List<DictDataRespDTO> punctureNeedle = dictDataApi.getDictDataListByBatchValue(needleIds, "punctureNeedle");
                    if (CollectionUtil.isNotEmpty(punctureNeedle)) {
                        String punctureNeedleName = punctureNeedle.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setPunctureNeedleName(punctureNeedleName);
                    }
                }
                //穿刺型号
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleModel())) {
                    List<String> needleModelIds = Arrays.stream(dialysisPreparationRespVO.getPunctureNeedleModel().split(",")).collect(Collectors.toList());
                    List<DictDataRespDTO> punctureNeedleModel = dictDataApi.getDictDataListByBatchValue(needleModelIds, "punctureNeedleModel");
                    if (CollectionUtil.isNotEmpty(punctureNeedleModel)) {
                        String punctureNeedleModelName = punctureNeedleModel.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setPunctureNeedleModelName(punctureNeedleModelName);
                    }
                }

                //透前血压
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(StrUtil.isNotEmpty(dialysisPreparationRespVO.getBpNoOne()) ? dialysisPreparationRespVO.getBpNoOne() : "")
                        .append("/")
                        .append(StrUtil.isNotEmpty(dialysisPreparationRespVO.getBpNoTwo()) ? dialysisPreparationRespVO.getBpNoTwo() : "");
                dialysisPreparationRespVO.setBp(stringBuilder.toString());

                //抗凝剂（首剂）（维持/追加）（总量）
                MPJLambdaWrapper<ContradictDO> contradictWrapper = new MPJLambdaWrapper<>(ContradictDO.class);
                contradictWrapper.leftJoin(DrugTypeDO.class, DrugTypeDO::getId, ContradictDO::getDrugTypeId)
                        .select(ContradictDO::getDrugTypeId, ContradictDO::getFirstDoseValue, ContradictDO::getFirstMethodNumber, ContradictDO::getOpportunity, ContradictDO::getTotalValue)
                        .selectAs(DrugTypeDO::getName, "drugTypeName")
                        .eq(ContradictDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(ContradictDO::getProtocolType, 1)
                        .eq(ContradictDO::getDialyzeId,dialysisPreparationRespVO.getDialyzeWayValue())
                        .orderByDesc(ContradictDO::getCreateTime);
                List<ContradictRespVO> contradictRespVOList = contradictMapper.selectJoinList(ContradictRespVO.class, contradictWrapper);
                if (CollectionUtil.isNotEmpty(contradictRespVOList)) {
                    ContradictRespVO contradictRespVO = contradictRespVOList.get(0);

                    StringBuilder sb = new StringBuilder();
                    sb.append(StrUtil.isNotEmpty(contradictRespVO.getDrugTypeName()) ? contradictRespVO.getDrugTypeName() : "")
                            .append(StrUtil.isNotEmpty(contradictRespVO.getFirstDoseValue()) ? "(" + contradictRespVO.getFirstDoseValue() + ")" : "")
                            .append(StrUtil.isNotEmpty(contradictRespVO.getFirstMethodNumber()) ? "(" + contradictRespVO.getFirstMethodNumber() + ")" : "")
                            .append(StrUtil.isNotEmpty(contradictRespVO.getOpportunity()) ? "(" + contradictRespVO.getOpportunity() + ")" : "")
                            .append(StrUtil.isNotEmpty(contradictRespVO.getTotalValue()) ? "(" + contradictRespVO.getTotalValue() + ")" : "");
                     String anticoagulant = sb.toString();
                    //dialysisPreparationRespVO.setAnticoagulant(anticoagulant);
                    dialysisPreparationRespVO.setAnticoagulantName(anticoagulant);
                    //抗凝剂类型名称
                    //String anticoagulantName = contradictRespVOList.stream().map(contradictRespVO -> {
                    //    StringBuilder sb = new StringBuilder();
                    //    sb.append(StrUtil.isNotEmpty(contradictRespVO.getDrugTypeName()) ? contradictRespVO.getDrugTypeName() : "");
                    //    return sb.toString();
                    //}).collect(Collectors.joining(","));
                    //dialysisPreparationRespVO.setAnticoagulantName(anticoagulantName);
                }

                //透析时长
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getDuration())) {
                    if ((StringUtils.isEmpty(dialysisPreparationRespVO.getDurationMin()) || "00".equals(dialysisPreparationRespVO.getDurationMin()))){
                        dialysisPreparationRespVO.setDurationMin("0");
                    }

                    if (!"0".equals(dialysisPreparationRespVO.getDurationMin())) {
                        BigDecimal divide = Convert.toBigDecimal(dialysisPreparationRespVO.getDurationMin()).divide(Convert.toBigDecimal("60"), 2, RoundingMode.DOWN);
                        BigDecimal add = Convert.toBigDecimal(dialysisPreparationRespVO.getDuration()).add(divide);
                        dialysisPreparationRespVO.setDialysisDuration(add.toString());
                    } else {
                        dialysisPreparationRespVO.setDialysisDuration(dialysisPreparationRespVO.getDuration());
                    }
                }

                //透析方式
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialysisPreparationRespVO.getDialyzeWayValue());
                dialysisPreparationRespVO.setDialyzeWayValueName(dialyzeWay != null ? dialyzeWay.getLabel() : null);


                //医嘱和his药品
                MPJLambdaWrapper<DialysisAdviceDO> dialysisAdviceHisWrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
                dialysisAdviceHisWrapper.leftJoin(HisDrugDO.class, HisDrugDO::getFDrugId, DialysisAdviceDO::getAdviceId)
                        .select(HisDrugDO::getFDrugName, HisDrugDO::getFDrugSpec, HisDrugDO::getFOnceUsing, HisDrugDO::getFSpecUnit)
                        .select(DialysisAdviceDO::getPrescribeNo, DialysisAdviceDO::getDrugWay, DialysisAdviceDO::getFrequency, DialysisAdviceDO::getMedicateState,DialysisAdviceDO::getPushStatus,DialysisAdviceDO::getDeleted,DialysisAdviceDO::getStopStatus)
                        .eq(DialysisAdviceDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(DialysisAdviceDO::getAdviceTime, pageReqVO.getHemodialysisTime())
                        .eq(DialysisAdviceDO::getType,"0")
                        .eq(DialysisAdviceDO::getDeleted,0);
                //医嘱和自建药品
                MPJLambdaWrapper<DialysisAdviceDO> dialysisAdviceWrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
                dialysisAdviceWrapper.leftJoin(DrugDO.class, DrugDO::getId, DialysisAdviceDO::getAdviceId)
                        .selectAs(DrugDO::getName, "fDrugName")
                        .selectAs(DrugDO::getSpec, "fDrugSpec")
                        .selectAs(DrugDO::getOnce, "fOnceUsing")
                        .selectAs(DrugDO::getDoseUnit, "fSpecUnit")
                        .select(DialysisAdviceDO::getPrescribeNo, DialysisAdviceDO::getDrugWay, DialysisAdviceDO::getFrequency, DialysisAdviceDO::getMedicateState,DialysisAdviceDO::getPushStatus,DialysisAdviceDO::getDeleted,DialysisAdviceDO::getStopStatus)
                        .eq(DialysisAdviceDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(DialysisAdviceDO::getAdviceTime, pageReqVO.getHemodialysisTime())
                        .eq(DialysisAdviceDO::getType,"0")
                        .eq(DialysisAdviceDO::getDeleted,0);

                List<DialysisAdviceItemRespVO> dialysisAdviceItemHisRespVOS = dialysisAdviceMapper.selectJoinList(DialysisAdviceItemRespVO.class, dialysisAdviceHisWrapper);
                List<DialysisAdviceItemRespVO> dialysisAdviceItemRespVOS = dialysisAdviceMapper.selectJoinList(DialysisAdviceItemRespVO.class, dialysisAdviceWrapper);
                dialysisAdviceItemHisRespVOS.addAll(dialysisAdviceItemRespVOS);
                if (CollectionUtil.isNotEmpty(dialysisAdviceItemHisRespVOS)) {
                    //未推送
                    List<String> erythropoietinList = Lists.newArrayList();
                    List<String> carnitineList = Lists.newArrayList();
                    List<String> ironSucroseList = Lists.newArrayList();
                    List<String> paricalcitolList = Lists.newArrayList();
                    List<String> calcitriolList = Lists.newArrayList();
                    List<String> urokinaseList = Lists.newArrayList();
                    List<String> calciumGluconateList = Lists.newArrayList();
                    List<DialysisAdviceItemRespVO> noDialysisAdviceItemRespVO = dialysisAdviceItemHisRespVOS.stream().filter(dialysisAdviceItemRespVO -> (StringUtils.isEmpty(dialysisAdviceItemRespVO.getPushStatus()) || 1 !=dialysisAdviceItemRespVO.getPushStatus()) && !dialysisAdviceItemRespVO.getStopStatus()&& !dialysisAdviceItemRespVO.getDeleted() ).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(noDialysisAdviceItemRespVO)) {
                        for (DialysisAdviceItemRespVO dialysisAdviceItemRespVO : noDialysisAdviceItemRespVO) {
                            //显示格式：名称+规格+用量/单位+数量/单位+用法+频率
                            DictDataRespDTO medicineWay = dictDataApi.getDictData("medicine_way", dialysisAdviceItemRespVO.getDrugWay());
                            DictDataRespDTO frequency = dictDataApi.getDictData("frequency", dialysisAdviceItemRespVO.getFrequency());
                            StringBuilder sb = new StringBuilder();
                            sb.append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFDrugSpec()) ? dialysisAdviceItemRespVO.getFDrugSpec() : "")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFOnceUsing()) ? dialysisAdviceItemRespVO.getFOnceUsing() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFSpecUnit()) ? dialysisAdviceItemRespVO.getFSpecUnit() : "")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getPrescribeNo()) ? dialysisAdviceItemRespVO.getPrescribeNo() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFSpecUnit()) ? dialysisAdviceItemRespVO.getFSpecUnit() : "")
                                    .append(medicineWay != null ? medicineWay.getLabel() : "")
                                    .append(frequency != null ? frequency.getLabel() : "");
                            if (dialysisAdviceItemRespVO.getFDrugName().contains("促红素")) {
                                erythropoietinList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("左卡尼汀")) {
                                carnitineList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("蔗糖铁")) {
                                ironSucroseList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("帕立骨化醇")) {
                                paricalcitolList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("骨化三醇")) {
                                calcitriolList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("尿激酶")) {
                                urokinaseList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("葡萄糖酸钙")) {
                                calciumGluconateList.add(sb.toString());
                            }
                        }

                        if (CollectionUtil.isNotEmpty(erythropoietinList)) {
                            String collect = erythropoietinList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setErythropoietin(collect);
                        }
                        if (CollectionUtil.isNotEmpty(carnitineList)) {
                            String collect = carnitineList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCarnitine(collect);
                        }
                        if (CollectionUtil.isNotEmpty(ironSucroseList)) {
                            String collect = ironSucroseList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setIronSucrose(collect);
                        }
                        if (CollectionUtil.isNotEmpty(paricalcitolList)) {
                            String collect = paricalcitolList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setParicalcitol(collect);
                        }
                        if (CollectionUtil.isNotEmpty(calcitriolList)) {
                            String collect = calcitriolList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalcitriol(collect);
                        }
                        if (CollectionUtil.isNotEmpty(urokinaseList)) {
                            String collect = urokinaseList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setUrokinase(collect);
                        }
                        if (CollectionUtil.isNotEmpty(calciumGluconateList)) {
                            String collect = calciumGluconateList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalciumGluconate(collect);
                        }
                    }


                    //已推送
                    List<DialysisAdviceItemRespVO> dialysisAdviceItemRespVO = dialysisAdviceItemHisRespVOS.stream().filter(dialysisAdviceItemRespVO1 ->!StringUtils.isEmpty(dialysisAdviceItemRespVO1.getPushStatus()) && 1 == dialysisAdviceItemRespVO1.getPushStatus() && !dialysisAdviceItemRespVO1.getStopStatus() && !dialysisAdviceItemRespVO1.getDeleted()).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(dialysisAdviceItemRespVO)) {
                        List<String> erythropoietinPushList = Lists.newArrayList();
                        List<String> carnitinePushList = Lists.newArrayList();
                        List<String> ironSucrosePushList = Lists.newArrayList();
                        List<String> paricalcitolPushList = Lists.newArrayList();
                        List<String> calcitriolPushList = Lists.newArrayList();
                        List<String> urokinasePushList = Lists.newArrayList();
                        List<String> calciumGluconatePushList = Lists.newArrayList();
                        for (DialysisAdviceItemRespVO adviceItemRespVO : dialysisAdviceItemRespVO) {
                            //显示格式：名称+规格+用量/单位+数量/单位+用法+频率
                            DictDataRespDTO medicineWay = dictDataApi.getDictData("medicine_way", adviceItemRespVO.getDrugWay());
                            DictDataRespDTO frequency = dictDataApi.getDictData("frequency", adviceItemRespVO.getFrequency());
                            StringBuilder sb = new StringBuilder();
                            sb.append(StrUtil.isNotEmpty(adviceItemRespVO.getFDrugSpec()) ? adviceItemRespVO.getFDrugSpec() : "")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFOnceUsing()) ? adviceItemRespVO.getFOnceUsing() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFSpecUnit()) ? adviceItemRespVO.getFSpecUnit() : "")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getPrescribeNo()) ? adviceItemRespVO.getPrescribeNo() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFSpecUnit()) ? adviceItemRespVO.getFSpecUnit() : "")
                                    .append(medicineWay != null ? medicineWay.getLabel() : "")
                                    .append(frequency != null ? frequency.getLabel() : "");
                            if (adviceItemRespVO.getFDrugName().contains("促红素")) {
                                erythropoietinPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("左卡尼汀")) {
                                carnitinePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("蔗糖铁")) {
                                ironSucrosePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("帕立骨化醇")) {
                                paricalcitolPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("骨化三醇")) {
                                calcitriolPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("尿激酶")) {
                                urokinasePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("葡萄糖酸钙")) {
                                calciumGluconatePushList.add(sb.toString());
                            }
                        }
                        if (CollectionUtil.isNotEmpty(erythropoietinPushList)) {
                            String collect = erythropoietinPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setErythropoietinPush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(carnitinePushList)) {
                            String collect = carnitinePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCarnitinePush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(ironSucrosePushList)) {
                            String collect = ironSucrosePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setIronSucrosePush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(paricalcitolPushList)) {
                            String collect = paricalcitolPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setParicalcitolPush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(calcitriolPushList)) {
                            String collect = calcitriolPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalcitriolPush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(urokinasePushList)) {
                            String collect = urokinasePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setUrokinasePush(collect);
                        }
                        if (CollectionUtil.isNotEmpty(calciumGluconatePushList)) {
                            String collect = calciumGluconatePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalciumGluconatePush(collect);
                        }
                    }
                }


            }).collect(Collectors.toList());
            if (1 == pageReqVO.getType()) {
                pageResult.setList(dialysisPreparationRespVOS);
            } else {
                //透前准备
                List<DialysisPreparationRespVO> list = Lists.newArrayList();
                //血透器
                Map<String, List<String>> hemodialysisDeviceGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getHemodialysisDevice()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getHemodialysisDevice, Collectors.mapping(DialysisPreparationRespVO::getHemodialysisDeviceName, Collectors.toList())));
                if (MapUtil.isNotEmpty(hemodialysisDeviceGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = hemodialysisDeviceGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("血透器");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
                //血滤器
                Map<String, List<String>> bloodFilterGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getBloodFilter()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getBloodFilter, Collectors.mapping(DialysisPreparationRespVO::getBloodFilterName, Collectors.toList())));
                if (MapUtil.isNotEmpty(bloodFilterGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = bloodFilterGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("血滤器");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }

                //灌流器
                Map<String, List<String>> perfumerGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfumer()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getPerfumer, Collectors.mapping(DialysisPreparationRespVO::getPerfumerName, Collectors.toList())));
                if (MapUtil.isNotEmpty(perfumerGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = perfumerGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("灌流器");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }

                //钾
                Map<String, List<String>> potassiumGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPotassium()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getPotassium, Collectors.mapping(DialysisPreparationRespVO::getPotassium, Collectors.toList())));
                if (MapUtil.isNotEmpty(potassiumGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = potassiumGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("钾");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }

                //透析液钙
                Map<String, List<String>> calciumGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcium()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getCalcium, Collectors.mapping(DialysisPreparationRespVO::getCalcium, Collectors.toList())));
                if (MapUtil.isNotEmpty(calciumGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = calciumGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("透析液钙");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }

                //葡萄糖
                Map<String, List<String>> glucoseGroup = dialysisPreparationRespVOS.stream()
                        .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getGlucose()))
                        .collect(Collectors.groupingBy(DialysisPreparationRespVO::getGlucose, Collectors.mapping(DialysisPreparationRespVO::getGlucose, Collectors.toList())));
                if (MapUtil.isNotEmpty(glucoseGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = glucoseGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("葡萄糖");
                        dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
                //穿刺针
                List<String> punctureNeedleStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleName()))
                        .map(DialysisPreparationRespVO::getPunctureNeedleName).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(punctureNeedleStr)) {
                    Map<String, List<String>> punctureNeedleGroup = punctureNeedleStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(punctureNeedleGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = punctureNeedleGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("穿刺针");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }

                //穿刺型号
                List<String> punctureNeedleModelStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleModelName()))
                        .map(DialysisPreparationRespVO::getPunctureNeedleModelName).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(punctureNeedleModelStr)) {
                    Map<String, List<String>> punctureNeedleModelGroup = punctureNeedleModelStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(punctureNeedleModelGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = punctureNeedleModelGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("穿刺型号");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //抗凝剂
                List<String> anticoagulantStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getAnticoagulantName()))
                        .map(DialysisPreparationRespVO::getAnticoagulantName).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(anticoagulantStr)) {
                    List<DrugTypeRespVO> drugTypeList = mottoHardService.getDrugTypeList(5l);
                    for (DrugTypeRespVO drugTypeRespVO : drugTypeList) {
                        long count = anticoagulantStr.stream().filter(item -> item.indexOf(drugTypeRespVO.getName()) != -1).count();
                        if(count > 0){
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("抗凝剂");
                            dialysisItemRespVO.setItemSpecification(drugTypeRespVO.getName());
                            dialysisItemRespVO.setNumber(count + "");
                            list.add(dialysisItemRespVO);
                        }
                    }

                }

                //穿刺包数量，根据血管通路来统计，如有1个内瘘则统计1个内瘘穿刺包。
                // 换药包有上机换药包和下机换药包，当血管通路选择是导管时，需要1个上机换药包和1个下机换药包和1个封管液进行统计。
                long nwCount = dialysisPreparationRespVOS.stream().filter(v -> !StringUtils.isEmpty(v.getPipeline())
                        && v.getPipeline().indexOf("内瘘") != -1).count();
                if(nwCount > 0){
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("穿刺包");
                    dialysisItemRespVO.setItemSpecification("内瘘穿刺包");
                    dialysisItemRespVO.setNumber(nwCount + "");
                    list.add(dialysisItemRespVO);
                }
                long dgCount = dialysisPreparationRespVOS.stream().filter(v -> !StringUtils.isEmpty(v.getPipeline())
                        && v.getPipeline().indexOf("导管") != -1).count();
                if(dgCount > 0){
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("换药包");
                    dialysisItemRespVO.setItemSpecification("上机换药包");
                    dialysisItemRespVO.setNumber(nwCount + "");
                    list.add(dialysisItemRespVO);

                    DialysisItemRespVO dialysisItemRespVO2 = new DialysisItemRespVO();
                    dialysisItemRespVO2.setItemType("换药包");
                    dialysisItemRespVO2.setItemSpecification("下机换药包");
                    dialysisItemRespVO2.setNumber(nwCount + "");
                    list.add(dialysisItemRespVO2);

                    DialysisItemRespVO dialysisItemRespVO3 = new DialysisItemRespVO();
                    dialysisItemRespVO3.setItemType("封管液");
                    dialysisItemRespVO3.setItemSpecification("封管液");
                    dialysisItemRespVO3.setNumber(nwCount + "");
                    list.add(dialysisItemRespVO3);
                }

                //(医嘱)促红素
                List<String> erythropoietinStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getErythropoietin()))
                        .map(DialysisPreparationRespVO::getErythropoietin).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(erythropoietinStr)) {
                    Map<String, List<String>> erythropoietinStrGroup = erythropoietinStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(erythropoietinStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = erythropoietinStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)促红素");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }

                //(医嘱)左卡尼汀
                List<String> carnitineStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCarnitine()))
                        .map(DialysisPreparationRespVO::getCarnitine).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(carnitineStr)) {
                    Map<String, List<String>> carnitineStrGroup = carnitineStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(carnitineStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = carnitineStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)左卡尼汀");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(医嘱)蔗糖铁
                List<String> ironSucroseStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getIronSucrose()))
                        .map(DialysisPreparationRespVO::getIronSucrose).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ironSucroseStr)) {
                    Map<String, List<String>> ironSucroseStrGroup = ironSucroseStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(ironSucroseStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = ironSucroseStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)蔗糖铁");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(医嘱)帕立骨化醇
                List<String> paricalcitolStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getParicalcitol()))
                        .map(DialysisPreparationRespVO::getParicalcitol).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paricalcitolStr)) {
                    Map<String, List<String>> paricalcitolStrGroup = paricalcitolStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(paricalcitolStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = paricalcitolStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)帕立骨化醇");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(医嘱)骨化三醇
                List<String> calcitriolStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcitriol()))
                        .map(DialysisPreparationRespVO::getCalcitriol).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calcitriolStr)) {
                    Map<String, List<String>> calcitriolStrGroup = calcitriolStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(calcitriolStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = calcitriolStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)骨化三醇");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(医嘱)尿激酶
                List<String> urokinaseStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getUrokinase()))
                        .map(DialysisPreparationRespVO::getUrokinase).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(urokinaseStr)) {
                    Map<String, List<String>> urokinaseStrGroup = urokinaseStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(urokinaseStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = urokinaseStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)尿激酶");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(医嘱)葡萄糖酸钙
                List<String> calciumGluconateStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalciumGluconate()))
                        .map(DialysisPreparationRespVO::getCalciumGluconate).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calciumGluconateStr)) {
                    Map<String, List<String>> calciumGluconateStrGroup = calciumGluconateStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(calciumGluconateStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = calciumGluconateStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(医嘱)葡萄糖酸钙");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }

                //(推送)促红素
                List<String> erythropoietinPushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getErythropoietinPush()))
                        .map(DialysisPreparationRespVO::getErythropoietinPush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(erythropoietinPushStr)) {
                    Map<String, List<String>> erythropoietinPushStrGroup = erythropoietinPushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(erythropoietinPushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = erythropoietinPushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)促红素");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }

                //(推送)左卡尼汀
                List<String> carnitinePushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCarnitinePush()))
                        .map(DialysisPreparationRespVO::getCarnitinePush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(carnitinePushStr)) {
                    Map<String, List<String>> carnitinePushStrGroup = carnitinePushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(carnitinePushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = carnitinePushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)左卡尼汀");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(推送)蔗糖铁
                List<String> ironSucrosePushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getIronSucrosePush()))
                        .map(DialysisPreparationRespVO::getIronSucrosePush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ironSucrosePushStr)) {
                    Map<String, List<String>> ironSucrosePushStrGroup = ironSucrosePushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(ironSucrosePushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = ironSucrosePushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)蔗糖铁");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(推送)帕立骨化醇
                List<String> paricalcitolPushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getParicalcitolPush()))
                        .map(DialysisPreparationRespVO::getParicalcitolPush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paricalcitolPushStr)) {
                    Map<String, List<String>> paricalcitolPushStrGroup = paricalcitolPushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(paricalcitolPushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = paricalcitolPushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)帕立骨化醇");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(推送)骨化三醇
                List<String> calcitriolPushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcitriolPush()))
                        .map(DialysisPreparationRespVO::getCalcitriolPush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calcitriolPushStr)) {
                    Map<String, List<String>> calcitriolPushStrGroup = calcitriolPushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(calcitriolPushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = calcitriolPushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)骨化三醇");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(推送)尿激酶
                List<String> urokinasePushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getUrokinasePush()))
                        .map(DialysisPreparationRespVO::getUrokinasePush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(urokinasePushStr)) {
                    Map<String, List<String>> urokinasePushStrGroup = urokinasePushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(urokinasePushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = urokinasePushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)尿激酶");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
                //(推送)葡萄糖酸钙
                List<String> calciumGluconatePushStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalciumGluconatePush()))
                        .map(DialysisPreparationRespVO::getCalciumGluconatePush).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(calciumGluconatePushStr)) {
                    Map<String, List<String>> calciumGluconatePushStrGroup = calciumGluconatePushStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(calciumGluconatePushStrGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = calciumGluconatePushStrGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("(推送)葡萄糖酸钙");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }

                pageResult.setList(list);
                pageResult.setTotal(Long.valueOf(list.size()));
            }
        }
        return pageResult;
    }

    @Override
    public List<HemodialysisManagerPrintDialyzeRespVO> getPrintDialyze(HemodialysisManagerCreateReqVO createReqVO) {
        //透析后才可以打印
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                .in(ArrangeClassesDO::getPatientId, createReqVO.getPatientIdArr())
                .select(ArrangeClassesDO::getState));
        MPJLambdaWrapper<HemodialysisManagerDO> wrapper = new MPJLambdaWrapper<>(HemodialysisManagerDO.class);
        wrapper.leftJoin(ArrangeClassesDO.class, ArrangeClassesDO::getPatientId, HemodialysisManagerDO::getPatientId)
                .leftJoin(PatientDO.class, PatientDO::getId, HemodialysisManagerDO::getPatientId)
                .eq(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                .in(HemodialysisManagerDO::getPatientId, createReqVO.getPatientIdArr())
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(ArrangeClassesDO::getClassesTime, createReqVO.getHemodialysisTime())
                .selectAs(PatientDO::getName, "patientName")
                .selectAs(HemodialysisManagerDO::getFacilityId, "bedNo")
                .selectAs(ArrangeClassesDO::getFacilityName, "arrayBed")
                .select(PatientDO::getSex, PatientDO::getAge, PatientDO::getDialyzeNo)
                .select(ArrangeClassesDO::getFaciitySubareaName)
                .select(HemodialysisManagerDO::getPatientId, HemodialysisManagerDO::getSubstituteModel, HemodialysisManagerDO::getSubstituteTodal, HemodialysisManagerDO::getDuration, HemodialysisManagerDO::getDurationMin
                        , HemodialysisManagerDO::getPrescriptionEhydratedLevel, HemodialysisManagerDO::getDialysateFlowrate, HemodialysisManagerDO::getDoctorId
                        , HemodialysisManagerDO::getHemodialysisDevice, HemodialysisManagerDO::getBloodFilter, HemodialysisManagerDO::getPerfumer, HemodialysisManagerDO::getVerificationPersonnel
                        , HemodialysisManagerDO::getVascularAccessOne, HemodialysisManagerDO::getVascularAccessTwo, HemodialysisManagerDO::getTreatmentNurse
                        , HemodialysisManagerDO::getSummaryContent, HemodialysisManagerDO::getTreatmentDoctor, HemodialysisManagerDO::getComputerNurse, HemodialysisManagerDO::getOffComputerNurse
                        , HemodialysisManagerDO::getBeforeDialyzeAfterWeigh, HemodialysisManagerDO::getDialyzeBeforeWeight, HemodialysisManagerDO::getBeforeGainWeight, HemodialysisManagerDO::getDryWeight
                        , HemodialysisManagerDO::getDialyzeAfterWeight, HemodialysisManagerDO::getWeights, HemodialysisManagerDO::getHemodialysisTime, HemodialysisManagerDO::getDialyzeWayValue
                        , HemodialysisManagerDO::getActualUltrafiltrationCapacity, HemodialysisManagerDO::getDehydration, HemodialysisManagerDO::getBeforeJson, HemodialysisManagerDO::getComputerNurse,HemodialysisManagerDO::getTreatmentHour,HemodialysisManagerDO::getTreatmentMin);
        List<HemodialysisManagerPrintDialyzeRespVO> hemodialysisManagerPrintDialyzeRespVOS = hemodialysisManagerMapper.selectJoinList(HemodialysisManagerPrintDialyzeRespVO.class, wrapper);
        if (CollectionUtil.isNotEmpty(hemodialysisManagerPrintDialyzeRespVOS)) {
            hemodialysisManagerPrintDialyzeRespVOS = hemodialysisManagerPrintDialyzeRespVOS.stream().peek(hemodialysisManagerPrintDialyzeRespVO -> {
                //透析床号
                String bedNo = hemodialysisManagerPrintDialyzeRespVO.getBedNo();
                if (!StringUtils.isEmpty(bedNo)) {
                    FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getId, bedNo);

                    if (!StringUtils.isEmpty(facilityDO)) {
                        FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityDO.getId(), FacilityManagerDO::getDeleted, 0);
                        if (!StringUtils.isEmpty(facilityManagerDO)) {
                            Long facilityNameId = facilityManagerDO.getFacilityNameId();
                            String facilityTypeId = facilityManagerDO.getFacilityTypeId();
                            String[] facilityIds = facilityTypeId.split(",");
                            Long facilityIdOne = Long.valueOf(facilityIds[0]);
                            Long facilityIdTwo = Long.valueOf(facilityIds[1]);
                            String typeName = null;
                            String facilityOne = null;
                            String facilityTwo = null;
                            hemodialysisManagerPrintDialyzeRespVO.setSort(facilityDO.getSort());

                            List<FacilityNameDO> facilityNameDOS = facilityNameMapper.selectList();
                            for (FacilityNameDO facilityNameDO : facilityNameDOS) {
                                if (facilityNameDO.getId().equals(facilityNameId)) {
                                    typeName = facilityNameDO.getName();
                                }
                                if (facilityNameDO.getId().equals(facilityIdOne)) {
                                    facilityOne = facilityNameDO.getName();
                                }
                                if (facilityNameDO.getId().equals(facilityIdTwo)) {
                                    facilityTwo = facilityNameDO.getName();
                                }
                            }
                            String machine = typeName + " " + facilityOne + "/" + facilityTwo;
                            hemodialysisManagerPrintDialyzeRespVO.setMachine(machine);
                        }
                        if(!StringUtils.isEmpty(facilityDO.getSubareaId())) {
                            FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(facilityDO.getSubareaId());
                            if (!StringUtils.isEmpty(facilitySubareaDO)) {
                                hemodialysisManagerPrintDialyzeRespVO.setFaciitySubareaName(facilitySubareaDO.getName());
                            }
                        }
                    }
                    hemodialysisManagerPrintDialyzeRespVO.setBedNo(facilityDO.getCode());
                }else if (StringUtils.isEmpty(bedNo) && !StringUtils.isEmpty(hemodialysisManagerPrintDialyzeRespVO.getArrayBed())) {
                    hemodialysisManagerPrintDialyzeRespVO.setBedNo(hemodialysisManagerPrintDialyzeRespVO.getArrayBed());
                }
                //治疗方式
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", hemodialysisManagerPrintDialyzeRespVO.getDialyzeWayValue());
                hemodialysisManagerPrintDialyzeRespVO.setDialysisName(dialyzeWay != null ? dialyzeWay.getLabel() : "/");
                //置换量
                if (StrUtil.isEmpty(hemodialysisManagerPrintDialyzeRespVO.getSubstituteTodal()) || "0".equals(hemodialysisManagerPrintDialyzeRespVO.getSubstituteTodal())) {
                    hemodialysisManagerPrintDialyzeRespVO.setSubstituteTodal("/");
                }
                //上机前病情
                if (StrUtil.isNotEmpty(hemodialysisManagerPrintDialyzeRespVO.getBeforeJson())) {
                    JSONObject jsonObject = JSONUtil.parseObj(hemodialysisManagerPrintDialyzeRespVO.getBeforeJson());
                    String motto122 = jsonObject.getStr("motto122");
                    hemodialysisManagerPrintDialyzeRespVO.setBeforeJson(motto122);
                }
                //性别
                if (StrUtil.isNotEmpty(hemodialysisManagerPrintDialyzeRespVO.getSex())) {
                    hemodialysisManagerPrintDialyzeRespVO.setSex("1".equals(hemodialysisManagerPrintDialyzeRespVO.getSex()) ? "男" : "女");
                }
                AdminUserRespDTO userInfo = adminUserApi.getUser(hemodialysisManagerPrintDialyzeRespVO.getComputerNurse());
                hemodialysisManagerPrintDialyzeRespVO.setPrescriptionUseName(userInfo != null && StrUtil.isNotEmpty(userInfo.getNickname()) ? userInfo.getNickname() : "");
                //置换方式
                DictDataRespDTO dataApiDictData = dictDataApi.getDictData("zhihuan", hemodialysisManagerPrintDialyzeRespVO.getSubstituteModel());
                hemodialysisManagerPrintDialyzeRespVO.setSubstituteModel(dataApiDictData != null ? dataApiDictData.getLabel() : "/");
                //抗凝剂
                MPJLambdaWrapper<ContradictDO> contradictWrapper = new MPJLambdaWrapper<>(ContradictDO.class);
                contradictWrapper.leftJoin(DrugTypeDO.class, DrugTypeDO::getId, ContradictDO::getDrugTypeId)
                        .select(ContradictDO::getDrugTypeId, ContradictDO::getFlushingVolume, ContradictDO::getTimesHour, ContradictDO::getFirstDoseValue, ContradictDO::getFirstMethodNumber, ContradictDO::getOpportunity, ContradictDO::getTotalValue)
                        .selectAs(DrugTypeDO::getName, "drugTypeName")
                        .select(DrugTypeDO::getDoseUnit)
                        .eq(ContradictDO::getHemodialysisTime, hemodialysisManagerPrintDialyzeRespVO.getHemodialysisTime())
                        .eq(ContradictDO::getPatientId, hemodialysisManagerPrintDialyzeRespVO.getPatientId())
                        .eq(ContradictDO::getProtocolType, 2);
                List<ContradictRespVO> contradictRespVOList = contradictMapper.selectJoinList(ContradictRespVO.class, contradictWrapper);
                if (CollectionUtil.isNotEmpty(contradictRespVOList)) {
                    contradictRespVOList = contradictRespVOList.stream().peek(contradictRespVO -> {
                        MPJLambdaWrapper<ContradictAdviceDO> contradictAdviceWapper = new MPJLambdaWrapper<>(ContradictAdviceDO.class);
                        contradictAdviceWapper.leftJoin(HisDrugDO.class, HisDrugDO::getFDrugId, ContradictAdviceDO::getDrugId)
                                .selectAs(HisDrugDO::getFDrugName, "drugNameStr")
                                .eq(ContradictAdviceDO::getHemodialysisTime, hemodialysisManagerPrintDialyzeRespVO.getHemodialysisTime())
                                .eq(ContradictAdviceDO::getPatientId, hemodialysisManagerPrintDialyzeRespVO.getPatientId())
                                .eq(ContradictAdviceDO::getProtocolType, 2)
                                .eq(ContradictAdviceDO::getDrugTypeId, contradictRespVO.getDrugTypeId());
                        List<ContradictRespVO> contradictRespVOList1 = contradictAdviceMapper.selectJoinList(ContradictRespVO.class, contradictAdviceWapper);
                        if (CollectionUtil.isNotEmpty(contradictRespVOList1)) {
                            String collect = contradictRespVOList1.stream().map(ContradictRespVO::getDrugNameStr).collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
                            contradictRespVO.setDrugTypeName(collect);
                        }
                    }).collect(Collectors.toList());
                }
                hemodialysisManagerPrintDialyzeRespVO.setContradictRespVOS(contradictRespVOList);

                //医师签名
                AdminUserRespDTO user = adminUserApi.getUser(hemodialysisManagerPrintDialyzeRespVO.getDoctorId());
                if (user != null) {
                    hemodialysisManagerPrintDialyzeRespVO.setDoctorName(user.getNickname());
                }

                //血透器
                HisConsumablesDO hemodialysisDevice = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, hemodialysisManagerPrintDialyzeRespVO.getHemodialysisDevice())
                        .select(HisConsumablesDO::getConsumSpec));
                hemodialysisManagerPrintDialyzeRespVO.setHemodialysisDeviceName(hemodialysisDevice == null ? "/" : hemodialysisDevice.getConsumSpec());
                //血滤器
                HisConsumablesDO bloodFilter = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, hemodialysisManagerPrintDialyzeRespVO.getBloodFilter())
                        .select(HisConsumablesDO::getConsumSpec));
                hemodialysisManagerPrintDialyzeRespVO.setBloodFilterName(bloodFilter == null ? "/" : bloodFilter.getConsumSpec());
                //灌流器
                HisConsumablesDO perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, hemodialysisManagerPrintDialyzeRespVO.getPerfumer())
                        .select(HisConsumablesDO::getConsumSpec));
                hemodialysisManagerPrintDialyzeRespVO.setPerfumerName(perfumer == null ? "/" : perfumer.getConsumSpec());
                //管路
                if (StrUtil.isNotEmpty(hemodialysisManagerPrintDialyzeRespVO.getVascularAccessOne()) && StrUtil.isNotEmpty(hemodialysisManagerPrintDialyzeRespVO.getVascularAccessTwo())) {
                    List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(new LambdaQueryWrapperX<VascularAccessDO>()
                            .select(VascularAccessDO::getName)
                            .in(VascularAccessDO::getId, hemodialysisManagerPrintDialyzeRespVO.getVascularAccessOne(), hemodialysisManagerPrintDialyzeRespVO.getVascularAccessTwo()));
                    if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                        hemodialysisManagerPrintDialyzeRespVO.setPipeline(vascularAccessDOS.stream().map(VascularAccessDO::getName).collect(Collectors.joining(",")));
                    }
                }
                //护士签名
                StringBuilder sb = new StringBuilder();
                AdminUserRespDTO user3 = adminUserApi.getUser(hemodialysisManagerPrintDialyzeRespVO.getTreatmentNurse());
                if (user3 != null) {
                    sb.append(user3.getNickname());
                }
                sb.append("/");
                AdminUserRespDTO user4 = adminUserApi.getUser(hemodialysisManagerPrintDialyzeRespVO.getVerificationPersonnel());
                if (user4 != null) {
                    sb.append(user4.getNickname());
                }
                hemodialysisManagerPrintDialyzeRespVO.setOffComputerNurseName(sb.toString());

                //治疗医生
                AdminUserRespDTO user2 = adminUserApi.getUser(hemodialysisManagerPrintDialyzeRespVO.getTreatmentDoctor());
                if (user2 != null) {
                    hemodialysisManagerPrintDialyzeRespVO.setTreatmentDoctorName(user2.getNickname());
                }
                //监测记录
                List<DialysisDetectionDO> dialysisDetectionDOS = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                        .eqIfPresent(DialysisDetectionDO::getPatientId, hemodialysisManagerPrintDialyzeRespVO.getPatientId())
                        .eqIfPresent(DialysisDetectionDO::getDateWeek, hemodialysisManagerPrintDialyzeRespVO.getHemodialysisTime())
                        .select(DialysisDetectionDO::getTransmembranePressure, DialysisDetectionDO::getVenousPressure, DialysisDetectionDO::getBloodFlow, DialysisDetectionDO::getUltrafiltrationCapacity
                                , DialysisDetectionDO::getDetectionMin, DialysisDetectionDO::getBodyTemperature, DialysisDetectionDO::getPulse, DialysisDetectionDO::getBreathe
                                , DialysisDetectionDO::getBloodOne, DialysisDetectionDO::getBloodTwo, DialysisDetectionDO::getSpo, DialysisDetectionDO::getBodyTemperature,
                                DialysisDetectionDO::getSymptom,DialysisDetectionDO::getBloodState)
                        .orderByAsc(DialysisDetectionDO::getDetectionMin));
                List<DialysisDetectionRespVO> dialysisDetectionRespVOS = DialysisDetectionConvert.INSTANCE.convertList(dialysisDetectionDOS);
                if (CollectionUtil.isNotEmpty(dialysisDetectionRespVOS)) {
                    dialysisDetectionRespVOS = dialysisDetectionRespVOS.stream().peek(dialysisDetectionRespVO -> {
                        if (StrUtil.isEmpty(dialysisDetectionRespVO.getTransmembranePressure())) {
                            dialysisDetectionRespVO.setTransmembranePressure("/");
                        }
                        if (StrUtil.isEmpty(dialysisDetectionRespVO.getVenousPressure())) {
                            dialysisDetectionRespVO.setVenousPressure("/");
                        }
                        if (StrUtil.isEmpty(dialysisDetectionRespVO.getBloodOne())) {
                            dialysisDetectionRespVO.setBloodOne("");
                        }
                        if (StrUtil.isEmpty(dialysisDetectionRespVO.getBloodTwo())) {
                            dialysisDetectionRespVO.setBloodTwo("");
                        }
                        StringBuilder stringBuilder = new StringBuilder();
                        JSONObject entries = JSONUtil.parseObj(dialysisDetectionRespVO.getSymptom());

                        String remark = entries.getJSONObject("50").getStr("remark");
                        if (StrUtil.isNotEmpty(remark)) {
                            stringBuilder.append(remark).append(",");
                        }

                        String remark2 = entries.getJSONObject("51").getStr("remark");
                        if (StrUtil.isNotEmpty(remark2)) {
                            stringBuilder.append(remark2).append(",");
                        }
                        String remark3 = entries.getJSONObject("52").getStr("remark");
                        if (StrUtil.isNotEmpty(remark3)) {
                            stringBuilder.append(remark3).append(",");
                        }
                        dialysisDetectionRespVO.setDispose(stringBuilder.toString().replaceAll(",+$", ""));
                    }).collect(Collectors.toList());
                }
                hemodialysisManagerPrintDialyzeRespVO.setDialysisDetectionRespVOS(dialysisDetectionRespVOS);
                //临时医嘱
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eqIfPresent(DialysisAdviceDO::getPatientId, hemodialysisManagerPrintDialyzeRespVO.getPatientId())
                        .eqIfPresent(DialysisAdviceDO::getDateWeek, hemodialysisManagerPrintDialyzeRespVO.getHemodialysisTime())
                        .eqIfPresent(DialysisAdviceDO::getType, "0")
                        .and(i->i.isNull(DialysisAdviceDO::getDrugType).or().ne(DialysisAdviceDO::getDrugType,2))
                        .select(DialysisAdviceDO::getStartTime, DialysisAdviceDO::getCheckUser, DialysisAdviceDO::getActivateUser, DialysisAdviceDO::getAdviceDesprition
                                , DialysisAdviceDO::getDrugWay, DialysisAdviceDO::getFrequency, DialysisAdviceDO::getAdviceName, DialysisAdviceDO::getSpecification
                                , DialysisAdviceDO::getOneNo, DialysisAdviceDO::getFspecUnit, DialysisAdviceDO::getPrescribeNo, DialysisAdviceDO::getFpreparaUnit, DialysisAdviceDO::getAdviceId,DialysisAdviceDO::getDeptId)
                        .orderByAsc(DialysisAdviceDO::getStartTime));
                List<DialysisAdviceRespVO> dialysisAdviceRespVOS = DialysisAdviceConvert.INSTANCE.convertList(dialysisAdviceDOS);
                if (CollectionUtil.isNotEmpty(dialysisAdviceRespVOS)) {
                    dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().peek(dialysisAdviceRespVO -> {
                        DictDataRespDTO medicineWay = dictDataApi.getDictData("medicine_way", dialysisAdviceRespVO.getDrugWay());
                        DictDataRespDTO frequency = dictDataApi.getDictData("frequency", dialysisAdviceRespVO.getFrequency());
                        //his项目
                        HisInformationDO hisInformationDO = hisInformationMapper.selectOne(new LambdaQueryWrapperX<HisInformationDO>()
                                .eq(HisInformationDO::getItemId, dialysisAdviceRespVO.getAdviceId())
                                .select(HisInformationDO::getItemName));

                        HisComboDO hisComboDO = hisComboMapper.selectOne(new LambdaQueryWrapperX<HisComboDO>()
                                .eq(HisComboDO::getPackageId, dialysisAdviceRespVO.getAdviceId())
                                .select(HisComboDO::getPackageName));
                        String showNameFlag = redisTemplate.opsForValue().get("showName");
                        if (!StringUtils.isEmpty(hisComboDO) && showNameFlag.contains(String.valueOf(dialysisAdviceRespVO.getDeptId()))) {
                            List<PackageDetailDO> packageDetailDOS = packageDetailMapper.selectList(PackageDetailDO::getPackageId, String.valueOf(dialysisAdviceRespVO.getAdviceId()));
                            if (CollectionUtil.isNotEmpty(packageDetailDOS)) {
                                String collect1 = packageDetailDOS.stream().map(PackageDetailDO::getItemName).collect(Collectors.joining(" , "));
                                dialysisAdviceRespVO.setAdviceName(dialysisAdviceRespVO.getAdviceName() + " ( " + collect1 + " ) ");
                                dialysisAdviceRespVO.setShowName(1);
                            }
                        }
                        //自建项目
                        ProjectDO projectDO = projectMapper.selectOne(new LambdaQueryWrapperX<ProjectDO>()
                                .eq(ProjectDO::getId, dialysisAdviceRespVO.getAdviceId())
                                .select(ProjectDO::getName));
                        //自建药品
                        DrugDO drugDO = drugMapper.selectOne(new LambdaQueryWrapperX<DrugDO>()
                                .eq(DrugDO::getId, dialysisAdviceRespVO.getAdviceId())
                                .select(DrugDO::getName));

                        StringBuilder sb1 = new StringBuilder();
                        //adviceName+specification+oneNo+fprepareUnit+prescribeNo+fspecUnit+给药途径+执行频率
                        sb1.append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getAdviceName()) ? dialysisAdviceRespVO.getAdviceName() : hisInformationDO != null ? hisInformationDO.getItemName() + "/":hisComboDO != null?hisComboDO.getPackageName() + "/" : projectDO != null ?projectDO.getName() + "/" : drugDO !=null?drugDO.getName()+ "/":"")
                                .append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getAdviceDesprition()) ? "(" + dialysisAdviceRespVO.getAdviceDesprition() + ")" + "/" : "")
                                .append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getOneNo()) ? dialysisAdviceRespVO.getOneNo() + "*" : "")
                                .append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getOneNo()) && StrUtil.isNotEmpty(dialysisAdviceRespVO.getFpreparaUnit()) ? dialysisAdviceRespVO.getFpreparaUnit() + "/" : "")
                                .append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getPrescribeNo()) ? dialysisAdviceRespVO.getPrescribeNo() + "*" : "")
                                .append(StrUtil.isNotEmpty(dialysisAdviceRespVO.getPrescribeNo()) && StrUtil.isNotEmpty(dialysisAdviceRespVO.getFspecUnit()) ? dialysisAdviceRespVO.getFspecUnit() + "/" : "")
                                .append(medicineWay != null ? medicineWay.getLabel() + "/" : "")
                                .append(frequency != null ? frequency.getLabel() : "");
                        dialysisAdviceRespVO.setAdviceContent(sb1.toString().replaceAll("/+$", ""));
                        //核对人员
                        AdminUserRespDTO user6 = adminUserApi.getUser(dialysisAdviceRespVO.getCheckUser());
                        dialysisAdviceRespVO.setCheckUserName(user6 != null ? user6.getNickname() : null);
                        //执行人员
                        AdminUserRespDTO user5 = adminUserApi.getUser(dialysisAdviceRespVO.getActivateUser());
                        dialysisAdviceRespVO.setActivateUserName(user5 != null ? user5.getNickname() : null);

                    }).collect(Collectors.toList());
                }
                hemodialysisManagerPrintDialyzeRespVO.setDialysisAdviceRespVOS(dialysisAdviceRespVOS);

                //体重信息
                Map<String, Object> map = Maps.newHashMap();
                map.put("beforeDialyzeAfterWeigh", hemodialysisManagerPrintDialyzeRespVO.getBeforeDialyzeAfterWeigh());
                map.put("dialyzeBeforeWeight", hemodialysisManagerPrintDialyzeRespVO.getDialyzeBeforeWeight());
                map.put("beforeGainWeight", hemodialysisManagerPrintDialyzeRespVO.getBeforeGainWeight());
                map.put("dryWeight", hemodialysisManagerPrintDialyzeRespVO.getDryWeight());
                map.put("dehydration", hemodialysisManagerPrintDialyzeRespVO.getDehydration());
                map.put("actualUltrafiltrationCapacity", hemodialysisManagerPrintDialyzeRespVO.getActualUltrafiltrationCapacity());
                map.put("dialyzeAfterWeight", hemodialysisManagerPrintDialyzeRespVO.getDialyzeAfterWeight());
                map.put("weights", hemodialysisManagerPrintDialyzeRespVO.getWeights());
                hemodialysisManagerPrintDialyzeRespVO.setWeightInfo(map);


            }).collect(Collectors.toList());
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(hemodialysisManagerPrintDialyzeRespVOS)) {
            hemodialysisManagerPrintDialyzeRespVOS = hemodialysisManagerPrintDialyzeRespVOS.stream().sorted(Comparator.comparing(HemodialysisManagerPrintDialyzeRespVO::getSort)).collect(Collectors.toList());
        }
        return hemodialysisManagerPrintDialyzeRespVOS;
    }

    @Override
    public void updateDryWeight(HemodialysisReachReqVO createReqVO) {
        //不管有没有确认修改签到称重表
        Long count = reachWeighMapper.selectCount(new LambdaQueryWrapperX<ReachWeighDO>()
                .eq(ReachWeighDO::getPatientId, createReqVO.getPatientId())
                .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(createReqVO.getHemodialysisTime()), DateUtil.endOfDay(createReqVO.getHemodialysisTime())));
        ReachWeighDO reachWeighDO = new ReachWeighDO();
        BeanUtil.copyProperties(createReqVO, reachWeighDO);
        reachWeighDO.setId(null);
        if (count > 0) {
            //修改
            reachWeighMapper.update(reachWeighDO, new LambdaUpdateWrapper<ReachWeighDO>()
                    .eq(ReachWeighDO::getPatientId, createReqVO.getPatientId())
                    .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(createReqVO.getHemodialysisTime()), DateUtil.endOfDay(createReqVO.getHemodialysisTime())));
        } else {
            //新增
            reachWeighMapper.insert(reachWeighDO);
        }
    }

    @Override
    public Map<String, Object> getHemodialysisManagerLast(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .select(HemodialysisManagerDO::getId, HemodialysisManagerDO::getHemodialysisTime, HemodialysisManagerDO::getDialyzeBeforeWeight, HemodialysisManagerDO::getDuration, HemodialysisManagerDO::getActualUltrafiltrationCapacity, HemodialysisManagerDO::getDialyzeAfterWeight)
                .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .le(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                .orderByDesc(HemodialysisManagerDO::getHemodialysisTime)
                .last("limit 1"));
        return new HashMap<String, Object>() {{
            put("dialyzeBeforeWeight", hemodialysisManagerDO.getDialyzeBeforeWeight());
            put("duration", hemodialysisManagerDO.getDuration());
            put("actualUltrafiltrationCapacity", hemodialysisManagerDO.getActualUltrafiltrationCapacity());
            put("dialyzeAfterWeight", hemodialysisManagerDO.getDialyzeAfterWeight());
            put("hemodialysisTime", hemodialysisManagerDO.getHemodialysisTime());
        }};
    }

    @Override
    public List<DialysisPreparationRespVO> getPreparationStatistics(HemodialysisManagerPageReqVO pageReqVO) {
        MPJLambdaWrapper<ArrangeClassesDO> wrapper = new MPJLambdaWrapper<>(ArrangeClassesDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, ArrangeClassesDO::getPatientId)
                .select(PatientDO::getBirthday, PatientDO::getName, PatientDO::getAge, PatientDO::getHospitalNo, PatientDO::getDialyzeNo, PatientDO::getSex)
                .selectAs(PatientDO::getId, "patientId")
                .selectAs(ArrangeClassesDO::getFacilityName, "code")
                .selectAs(ArrangeClassesDO::getDialysisValue, "dialyzeWayValue")
                .selectAs(ArrangeClassesDO::getFaciitySubareaName, "subareaName")
                .select(ArrangeClassesDO::getFacilityId, ArrangeClassesDO::getFacilitySubareaId)
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(ArrangeClassesDO::getClassesTime, pageReqVO.getHemodialysisTime())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getDayStateList()), ArrangeClassesDO::getDayState, pageReqVO.getDayStateList())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getFacilitySubareaIdList()), ArrangeClassesDO::getFacilitySubareaId, pageReqVO.getFacilitySubareaIdList());
        List<DialysisPreparationRespVO> dialysisPreparationRespVOList = arrangeClassesMapper.selectJoinList(DialysisPreparationRespVO.class, wrapper);
        if (CollectionUtil.isNotEmpty(dialysisPreparationRespVOList)) {
            List<DialysisPreparationRespVO> dialysisPreparationRespVOS = dialysisPreparationRespVOList.stream().peek(dialysisPreparationRespVO -> {
                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .select(HemodialysisManagerDO::getPatientId, HemodialysisManagerDO::getId, HemodialysisManagerDO::getFacilityId, HemodialysisManagerDO::getDryWeight, HemodialysisManagerDO::getFacilitySubareaId, HemodialysisManagerDO::getPrescriptionState, HemodialysisManagerDO::getFacilityTypeId
                                , HemodialysisManagerDO::getHemodialysisDevice, HemodialysisManagerDO::getBloodFilter, HemodialysisManagerDO::getPerfumer, HemodialysisManagerDO::getPotassium, HemodialysisManagerDO::getCalcium
                                , HemodialysisManagerDO::getGlucose, HemodialysisManagerDO::getVascularAccessOne, HemodialysisManagerDO::getVascularAccessTwo, HemodialysisManagerDO::getPunctureNeedle, HemodialysisManagerDO::getPunctureNeedleModel, HemodialysisManagerDO::getDayState
                                , HemodialysisManagerDO::getDialyzeBeforeWeight, HemodialysisManagerDO::getBpNoOne, HemodialysisManagerDO::getBpNoTwo, HemodialysisManagerDO::getDehydration, HemodialysisManagerDO::getPrescriptionEhydratedLevel
                                , HemodialysisManagerDO::getSubstituteTodal, HemodialysisManagerDO::getBeforeUltrafiltrationtotal, HemodialysisManagerDO::getBloodFlow, HemodialysisManagerDO::getDialyzeWayValue
                                , HemodialysisManagerDO::getDuration, HemodialysisManagerDO::getDurationMin, HemodialysisManagerDO::getFormulaSodium, HemodialysisManagerDO::getPrescriptionSodium, HemodialysisManagerDO::getBicarbonate, HemodialysisManagerDO::getHemodialysisTime)
                        .eq(HemodialysisManagerDO::getHemodialysisTime, pageReqVO.getHemodialysisTime())
                        .eq(HemodialysisManagerDO::getPatientId, dialysisPreparationRespVO.getPatientId()));
                if (hemodialysisManagerDO != null) {
                    BeanUtil.copyProperties(hemodialysisManagerDO, dialysisPreparationRespVO);
                    dialysisPreparationRespVO.setProtocolType(2);
                    dialysisPreparationRespVO.setPrescriptionName("已确认");
                } else {
                    DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectOne(new LambdaQueryWrapperX<DialysisProtocolDO>()
                            .eq(DialysisProtocolDO::getProtocolType, 1)
                            .eq(DialysisProtocolDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                            .eq(DialysisProtocolDO::getDialyzeId, dialysisPreparationRespVO.getDialyzeWayValue()));
                    if (dialysisProtocolDO != null) {
                        BeanUtil.copyProperties(dialysisProtocolDO, dialysisPreparationRespVO);
                    }
                    FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(new LambdaQueryWrapperX<FacilityManagerDO>()
                            .select(FacilityManagerDO::getId, FacilityManagerDO::getFacilityTypeId)
                            .eq(FacilityManagerDO::getFacilityId, dialysisPreparationRespVO.getFacilityId()));
                    if (facilityManagerDO != null) {
                        dialysisPreparationRespVO.setFacilityTypeId(facilityManagerDO.getFacilityTypeId());
                    }
                    dialysisPreparationRespVO.setProtocolType(1);
                    dialysisPreparationRespVO.setPrescriptionName("未确认");
                }
                //设备型号
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getFacilityTypeId())) {
                    List<Long> facilityNameIds = Arrays.stream(dialysisPreparationRespVO.getFacilityTypeId().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    List<FacilityNameDO> facilityNameDOList = facilityNameMapper.selectList(new LambdaQueryWrapperX<FacilityNameDO>()
                            .select(FacilityNameDO::getName)
                            .in(FacilityNameDO::getId, facilityNameIds)
                            .eq(FacilityNameDO::getType, 2));
                    if (CollectionUtil.isNotEmpty(facilityNameDOList)) {
                        String facilityTypeNames = facilityNameDOList.stream().map(FacilityNameDO::getName).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setFacilityTypeName(facilityTypeNames);
                    }
                }
                //性别
                dialysisPreparationRespVO.setSex("1".equals(dialysisPreparationRespVO.getSex()) ? "男" : "女");
                //处方状态
                //dialysisPreparationRespVO.setPrescriptionName(dialysisPreparationRespVO.getPrescriptionState() == null ? "未确认" : "已确认");
                //血透器
                HisConsumablesDO hemodialysisDevice = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getHemodialysisDevice())
                        .select(HisConsumablesDO::getConsumSpec));
                dialysisPreparationRespVO.setHemodialysisDeviceName(hemodialysisDevice == null ? null : hemodialysisDevice.getConsumSpec());
                //血滤器
                HisConsumablesDO bloodFilter = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getBloodFilter())
                        .select(HisConsumablesDO::getConsumSpec));
                dialysisPreparationRespVO.setBloodFilterName(bloodFilter == null ? null : bloodFilter.getConsumSpec());
                //灌流器
                HisConsumablesDO perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                        .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getPerfumer())
                        .select(HisConsumablesDO::getConsumSpec));
                if (StringUtils.isEmpty(perfumer)) {
                    perfumer = hisConsumablesMapper.selectOne(new LambdaQueryWrapperX<HisConsumablesDO>()
                            .eq(HisConsumablesDO::getConsumId, dialysisPreparationRespVO.getPerfume())
                            .select(HisConsumablesDO::getConsumSpec));
                }

                dialysisPreparationRespVO.setPerfumerName(perfumer == null ? null : perfumer.getConsumSpec());

                //人工肾
                StringBuilder sb1 = new StringBuilder();
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getHemodialysisDevice())) {
                    sb1.append(hemodialysisDevice == null ? "" : hemodialysisDevice.getConsumSpec()).append(",");
                }
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getBloodFilter())) {
                    sb1.append(bloodFilter == null ? "" : bloodFilter.getConsumSpec()).append(",");
                }
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfumer()) || StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfume())) {
                    sb1.append(perfumer == null ? "" : perfumer.getConsumSpec());
                }

                dialysisPreparationRespVO.setArtificialKidney(sb1.toString().replaceAll(",+$", ""));


                //管路
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getVascularAccessOne()) && StrUtil.isNotEmpty(dialysisPreparationRespVO.getVascularAccessTwo())) {
                    List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(new LambdaQueryWrapperX<VascularAccessDO>()
                            .select(VascularAccessDO::getName)
                            .in(VascularAccessDO::getId, dialysisPreparationRespVO.getVascularAccessOne(), dialysisPreparationRespVO.getVascularAccessTwo()));
                    if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                        dialysisPreparationRespVO.setPipeline(vascularAccessDOS.stream().map(VascularAccessDO::getName).collect(Collectors.joining(",")));
                    }
                }
                //穿刺针
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedle())) {
                    List<String> needleIds = Arrays.stream(dialysisPreparationRespVO.getPunctureNeedle().split(",")).collect(Collectors.toList());
                    List<DictDataRespDTO> punctureNeedle = dictDataApi.getDictDataListByBatchValue(needleIds, "punctureNeedle");
                    if (CollectionUtil.isNotEmpty(punctureNeedle)) {
                        String punctureNeedleName = punctureNeedle.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setPunctureNeedleName(punctureNeedleName);
                    }
                }
                //穿刺型号
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleModel())) {
                    List<String> needleModelIds = Arrays.stream(dialysisPreparationRespVO.getPunctureNeedleModel().split(",")).collect(Collectors.toList());
                    List<DictDataRespDTO> punctureNeedleModel = dictDataApi.getDictDataListByBatchValue(needleModelIds, "punctureNeedleModel");
                    if (CollectionUtil.isNotEmpty(punctureNeedleModel)) {
                        String punctureNeedleModelName = punctureNeedleModel.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                        dialysisPreparationRespVO.setPunctureNeedleModelName(punctureNeedleModelName);
                    }
                }

                //透前血压
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(StrUtil.isNotEmpty(dialysisPreparationRespVO.getBpNoOne()) ? dialysisPreparationRespVO.getBpNoOne() : "").append("/")
                        .append(StrUtil.isNotEmpty(dialysisPreparationRespVO.getBpNoTwo()) ? dialysisPreparationRespVO.getBpNoTwo() : "");
                dialysisPreparationRespVO.setBp(stringBuilder.toString());

                //抗凝剂（首剂）（维持/追加）（总量）
                MPJLambdaWrapper<ContradictDO> contradictWrapper = new MPJLambdaWrapper<>(ContradictDO.class);
                contradictWrapper.leftJoin(DrugTypeDO.class, DrugTypeDO::getId, ContradictDO::getDrugTypeId)
                        .select(ContradictDO::getDrugTypeId, ContradictDO::getFirstDoseValue, ContradictDO::getFirstMethodNumber, ContradictDO::getOpportunity, ContradictDO::getTotalValue)
                        .selectAs(DrugTypeDO::getName, "drugTypeName")
                        .eq(dialysisPreparationRespVO.getHemodialysisTime() != null, ContradictDO::getHemodialysisTime, dialysisPreparationRespVO.getHemodialysisTime())
                        .eq(ContradictDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(ContradictDO::getProtocolType, dialysisPreparationRespVO.getProtocolType());
                List<ContradictRespVO> contradictRespVOList = contradictMapper.selectJoinList(ContradictRespVO.class, contradictWrapper);
                if (CollectionUtil.isNotEmpty(contradictRespVOList)) {
                    String anticoagulant = contradictRespVOList.stream().map(contradictRespVO -> {
                        StringBuilder sb = new StringBuilder();
                        sb.append(StrUtil.isNotEmpty(contradictRespVO.getDrugTypeName()) ? contradictRespVO.getDrugTypeName() : "")
                                .append(StrUtil.isNotEmpty(contradictRespVO.getFirstDoseValue()) ? "(" + contradictRespVO.getFirstDoseValue() + ")" : "")
                                .append(StrUtil.isNotEmpty(contradictRespVO.getFirstMethodNumber()) ? "(" + contradictRespVO.getFirstMethodNumber() + ")" : "")
                                .append(StrUtil.isNotEmpty(contradictRespVO.getOpportunity()) ? "(" + contradictRespVO.getOpportunity() + ")" : "")
                                .append(StrUtil.isNotEmpty(contradictRespVO.getTotalValue()) ? "(" + contradictRespVO.getTotalValue() + ")" : "");
                        return sb.toString();
                    }).collect(Collectors.joining("/"));
                    //dialysisPreparationRespVO.setAnticoagulant(anticoagulant);
                    dialysisPreparationRespVO.setAnticoagulantName(anticoagulant);
                    //抗凝剂类型名称
                    //String anticoagulantName = contradictRespVOList.stream().map(contradictRespVO -> {
                    //    StringBuilder sb = new StringBuilder();
                    //    sb.append(StrUtil.isNotEmpty(contradictRespVO.getDrugTypeName()) ? contradictRespVO.getDrugTypeName() : "");
                    //    return sb.toString();
                    //}).collect(Collectors.joining(","));
                    //dialysisPreparationRespVO.setAnticoagulantName(anticoagulantName);
                }

                //透析时长
                if (StrUtil.isNotEmpty(dialysisPreparationRespVO.getDuration()) && StrUtil.isNotEmpty(dialysisPreparationRespVO.getDurationMin())) {
                    if (!"0".equals(dialysisPreparationRespVO.getDurationMin())) {
                        BigDecimal divide = Convert.toBigDecimal(dialysisPreparationRespVO.getDurationMin()).divide(Convert.toBigDecimal("60"), 2, RoundingMode.DOWN);
                        BigDecimal add = Convert.toBigDecimal(dialysisPreparationRespVO.getDuration()).add(divide);
                        dialysisPreparationRespVO.setDialysisDuration(add.toString());
                    } else {
                        dialysisPreparationRespVO.setDialysisDuration(dialysisPreparationRespVO.getDuration());
                    }
                }

                //透析方式
                DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", dialysisPreparationRespVO.getDialyzeWayValue());
                dialysisPreparationRespVO.setDialyzeWayValueName(dialyzeWay != null ? dialyzeWay.getLabel() : null);


                //医嘱和his药品
                MPJLambdaWrapper<DialysisAdviceDO> dialysisAdviceHisWrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
                dialysisAdviceHisWrapper.leftJoin(HisDrugDO.class, HisDrugDO::getFDrugId, DialysisAdviceDO::getAdviceId)
                        .select(HisDrugDO::getFDrugName, HisDrugDO::getFDrugSpec, HisDrugDO::getFOnceUsing, HisDrugDO::getFSpecUnit)
                        .select(DialysisAdviceDO::getPrescribeNo, DialysisAdviceDO::getDrugWay, DialysisAdviceDO::getFrequency, DialysisAdviceDO::getMedicateState)
                        .eq(DialysisAdviceDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(DialysisAdviceDO::getDateWeek, pageReqVO.getHemodialysisTime());
                //医嘱和自建药品
                MPJLambdaWrapper<DialysisAdviceDO> dialysisAdviceWrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
                dialysisAdviceWrapper.leftJoin(DrugDO.class, DrugDO::getId, DialysisAdviceDO::getAdviceId)
                        .selectAs(DrugDO::getName, "fDrugName")
                        .selectAs(DrugDO::getSpec, "fDrugSpec")
                        .selectAs(DrugDO::getOnce, "fOnceUsing")
                        .selectAs(DrugDO::getDoseUnit, "fSpecUnit")
                        .select(DialysisAdviceDO::getPrescribeNo, DialysisAdviceDO::getDrugWay, DialysisAdviceDO::getFrequency, DialysisAdviceDO::getMedicateState)
                        .eq(DialysisAdviceDO::getPatientId, dialysisPreparationRespVO.getPatientId())
                        .eq(DialysisAdviceDO::getDateWeek, pageReqVO.getHemodialysisTime());

                List<DialysisAdviceItemRespVO> dialysisAdviceItemHisRespVOS = dialysisAdviceMapper.selectJoinList(DialysisAdviceItemRespVO.class, dialysisAdviceHisWrapper);
                List<DialysisAdviceItemRespVO> dialysisAdviceItemRespVOS = dialysisAdviceMapper.selectJoinList(DialysisAdviceItemRespVO.class, dialysisAdviceWrapper);
                dialysisAdviceItemHisRespVOS.addAll(dialysisAdviceItemRespVOS);
                if (CollectionUtil.isNotEmpty(dialysisAdviceItemHisRespVOS)) {
                    //未推送
                    List<String> erythropoietinList = Lists.newArrayList();
                    List<String> carnitineList = Lists.newArrayList();
                    List<String> ironSucroseList = Lists.newArrayList();
                    List<String> paricalcitolList = Lists.newArrayList();
                    List<String> calcitriolList = Lists.newArrayList();
                    List<String> urokinaseList = Lists.newArrayList();
                    List<String> calciumGluconateList = Lists.newArrayList();
                    List<DialysisAdviceItemRespVO> noDialysisAdviceItemRespVO = dialysisAdviceItemHisRespVOS.stream().filter(dialysisAdviceItemRespVO -> 0 == dialysisAdviceItemRespVO.getMedicateState()).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(noDialysisAdviceItemRespVO)) {
                        for (DialysisAdviceItemRespVO dialysisAdviceItemRespVO : noDialysisAdviceItemRespVO) {
                            //显示格式：名称+规格+用量/单位+数量/单位+用法+频率
                            DictDataRespDTO medicineWay = dictDataApi.getDictData("medicine_way", dialysisAdviceItemRespVO.getDrugWay());
                            DictDataRespDTO frequency = dictDataApi.getDictData("frequency", dialysisAdviceItemRespVO.getFrequency());
                            StringBuilder sb = new StringBuilder();
                            sb.append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFDrugSpec()) ? dialysisAdviceItemRespVO.getFDrugSpec() : "")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFOnceUsing()) ? dialysisAdviceItemRespVO.getFOnceUsing() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFSpecUnit()) ? dialysisAdviceItemRespVO.getFSpecUnit() : "")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getPrescribeNo()) ? dialysisAdviceItemRespVO.getPrescribeNo() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(dialysisAdviceItemRespVO.getFSpecUnit()) ? dialysisAdviceItemRespVO.getFSpecUnit() : "")
                                    .append(medicineWay != null ? medicineWay.getLabel() : "")
                                    .append(frequency != null ? frequency.getLabel() : "");
                            if (dialysisAdviceItemRespVO.getFDrugName().contains("促红素")) {
                                erythropoietinList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("左卡尼汀")) {
                                carnitineList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("蔗糖铁")) {
                                ironSucroseList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("帕立骨化醇")) {
                                paricalcitolList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("骨化三醇")) {
                                calcitriolList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("尿激酶")) {
                                urokinaseList.add(sb.toString());
                            } else if (dialysisAdviceItemRespVO.getFDrugName().contains("葡萄糖酸钙")) {
                                calciumGluconateList.add(sb.toString());
                            }
                        }

                        if (CollectionUtil.isNotEmpty(erythropoietinList)) {
                            String collect = erythropoietinList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setErythropoietin(collect);
                        } else if (CollectionUtil.isNotEmpty(carnitineList)) {
                            String collect = carnitineList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCarnitine(collect);
                        } else if (CollectionUtil.isNotEmpty(ironSucroseList)) {
                            String collect = ironSucroseList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setIronSucrose(collect);
                        } else if (CollectionUtil.isNotEmpty(paricalcitolList)) {
                            String collect = paricalcitolList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setParicalcitol(collect);
                        } else if (CollectionUtil.isNotEmpty(calcitriolList)) {
                            String collect = calcitriolList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalcitriol(collect);
                        } else if (CollectionUtil.isNotEmpty(urokinaseList)) {
                            String collect = urokinaseList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setUrokinase(collect);
                        } else if (CollectionUtil.isNotEmpty(calciumGluconateList)) {
                            String collect = calciumGluconateList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalciumGluconate(collect);
                        }
                    }


                    //已推送
                    List<DialysisAdviceItemRespVO> dialysisAdviceItemRespVO = dialysisAdviceItemHisRespVOS.stream().filter(dialysisAdviceItemRespVO1 -> 1 == dialysisAdviceItemRespVO1.getMedicateState()).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(dialysisAdviceItemRespVO)) {
                        List<String> erythropoietinPushList = Lists.newArrayList();
                        List<String> carnitinePushList = Lists.newArrayList();
                        List<String> ironSucrosePushList = Lists.newArrayList();
                        List<String> paricalcitolPushList = Lists.newArrayList();
                        List<String> calcitriolPushList = Lists.newArrayList();
                        List<String> urokinasePushList = Lists.newArrayList();
                        List<String> calciumGluconatePushList = Lists.newArrayList();
                        for (DialysisAdviceItemRespVO adviceItemRespVO : dialysisAdviceItemRespVO) {
                            //显示格式：名称+规格+用量/单位+数量/单位+用法+频率
                            DictDataRespDTO medicineWay = dictDataApi.getDictData("medicine_way", adviceItemRespVO.getDrugWay());
                            DictDataRespDTO frequency = dictDataApi.getDictData("frequency", adviceItemRespVO.getFrequency());
                            StringBuilder sb = new StringBuilder();
                            sb.append(StrUtil.isNotEmpty(adviceItemRespVO.getFDrugSpec()) ? adviceItemRespVO.getFDrugSpec() : "")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFOnceUsing()) ? adviceItemRespVO.getFOnceUsing() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFSpecUnit()) ? adviceItemRespVO.getFSpecUnit() : "")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getPrescribeNo()) ? adviceItemRespVO.getPrescribeNo() : "")
                                    .append("/")
                                    .append(StrUtil.isNotEmpty(adviceItemRespVO.getFSpecUnit()) ? adviceItemRespVO.getFSpecUnit() : "")
                                    .append(medicineWay != null ? medicineWay.getLabel() : "")
                                    .append(frequency != null ? frequency.getLabel() : "");
                            if (adviceItemRespVO.getFDrugName().contains("促红素")) {
                                erythropoietinPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("左卡尼汀")) {
                                carnitinePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("蔗糖铁")) {
                                ironSucrosePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("帕立骨化醇")) {
                                paricalcitolPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("骨化三醇")) {
                                calcitriolPushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("尿激酶")) {
                                urokinasePushList.add(sb.toString());
                            } else if (adviceItemRespVO.getFDrugName().contains("葡萄糖酸钙")) {
                                calciumGluconatePushList.add(sb.toString());
                            }
                        }
                        if (CollectionUtil.isNotEmpty(erythropoietinPushList)) {
                            String collect = erythropoietinPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setErythropoietinPush(collect);
                        } else if (CollectionUtil.isNotEmpty(carnitinePushList)) {
                            String collect = carnitinePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCarnitinePush(collect);
                        } else if (CollectionUtil.isNotEmpty(ironSucrosePushList)) {
                            String collect = ironSucrosePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setIronSucrosePush(collect);
                        } else if (CollectionUtil.isNotEmpty(paricalcitolPushList)) {
                            String collect = paricalcitolPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setParicalcitolPush(collect);
                        } else if (CollectionUtil.isNotEmpty(calcitriolPushList)) {
                            String collect = calcitriolPushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalcitriolPush(collect);
                        } else if (CollectionUtil.isNotEmpty(urokinasePushList)) {
                            String collect = urokinasePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setUrokinasePush(collect);
                        } else if (CollectionUtil.isNotEmpty(calciumGluconatePushList)) {
                            String collect = calciumGluconatePushList.stream().collect(Collectors.joining(","));
                            dialysisPreparationRespVO.setCalciumGluconatePush(collect);
                        }
                    }
                }


            }).collect(Collectors.toList());
            //透前准备
            List<DialysisPreparationRespVO> list = Lists.newArrayList();
            //血透器
            Map<String, List<String>> hemodialysisDeviceGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getHemodialysisDevice()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getHemodialysisDevice, Collectors.mapping(DialysisPreparationRespVO::getHemodialysisDeviceName, Collectors.toList())));
            if (MapUtil.isNotEmpty(hemodialysisDeviceGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = hemodialysisDeviceGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("血透器");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }
            //血滤器
            Map<String, List<String>> bloodFilterGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getBloodFilter()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getBloodFilter, Collectors.mapping(DialysisPreparationRespVO::getBloodFilterName, Collectors.toList())));
            if (MapUtil.isNotEmpty(bloodFilterGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = bloodFilterGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("血滤器");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }

            //灌流器
            Map<String, List<String>> perfumerGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfumer()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getPerfumer, Collectors.mapping(DialysisPreparationRespVO::getPerfumerName, Collectors.toList())));
            Map<String, List<String>> perfumerGroup1 = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPerfume()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getPerfume, Collectors.mapping(DialysisPreparationRespVO::getPerfumerName, Collectors.toList())));
            perfumerGroup.putAll(perfumerGroup1);
            if (MapUtil.isNotEmpty(perfumerGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = perfumerGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("灌流器");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }

            //钾
            Map<String, List<String>> potassiumGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPotassium()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getPotassium, Collectors.mapping(DialysisPreparationRespVO::getPotassium, Collectors.toList())));
            if (MapUtil.isNotEmpty(potassiumGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = potassiumGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("钾");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }

            //透析液钙
            Map<String, List<String>> calciumGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcium()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getCalcium, Collectors.mapping(DialysisPreparationRespVO::getCalcium, Collectors.toList())));
            if (MapUtil.isNotEmpty(calciumGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = calciumGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("透析液钙");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }

            //葡萄糖
            Map<String, List<String>> glucoseGroup = dialysisPreparationRespVOS.stream()
                    .filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getGlucose()))
                    .collect(Collectors.groupingBy(DialysisPreparationRespVO::getGlucose, Collectors.mapping(DialysisPreparationRespVO::getGlucose, Collectors.toList())));
            if (MapUtil.isNotEmpty(glucoseGroup)) {
                List<DialysisItemRespVO> dialysisItemRespVOS = glucoseGroup.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    dialysisItemRespVO.setItemType("葡萄糖");
                    dialysisItemRespVO.setItemSpecification(entry.getValue().get(0));
                    dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }
            //穿刺针
            List<String> punctureNeedleStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleName()))
                    .map(DialysisPreparationRespVO::getPunctureNeedleName).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(punctureNeedleStr)) {
                Map<String, List<String>> punctureNeedleGroup = punctureNeedleStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(punctureNeedleGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = punctureNeedleGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("穿刺针");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }

            //穿刺型号
            List<String> punctureNeedleModelStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getPunctureNeedleModelName()))
                    .map(DialysisPreparationRespVO::getPunctureNeedleModelName).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(punctureNeedleModelStr)) {
                Map<String, List<String>> punctureNeedleModelGroup = punctureNeedleModelStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(punctureNeedleModelGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = punctureNeedleModelGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("穿刺型号");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //抗凝剂（透析耗材不展示 抗凝剂）
            if (1 != pageReqVO.getType()) {
                List<String> anticoagulantStr = dialysisPreparationRespVOS.stream().
                        filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getAnticoagulantName()))
                        .map(DialysisPreparationRespVO::getAnticoagulantName).map(preparation -> {
                            return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                        }).flatMap(s -> s.stream()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(anticoagulantStr)) {
                    Map<String, List<String>> anticoagulantNameGroup = anticoagulantStr.stream().collect(Collectors.groupingBy(s -> s));
                    if (MapUtil.isNotEmpty(anticoagulantNameGroup)) {
                        List<DialysisItemRespVO> dialysisItemRespVOS = anticoagulantNameGroup.entrySet().stream().map(entry -> {
                            DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                            dialysisItemRespVO.setItemType("抗凝剂");
                            dialysisItemRespVO.setItemSpecification(entry.getKey());
                            dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                            return dialysisItemRespVO;
                        }).collect(Collectors.toList());
                        list.addAll(dialysisItemRespVOS);
                    }
                }
            }
            //(医嘱)促红素
            List<String> erythropoietinStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getErythropoietin()))
                    .map(DialysisPreparationRespVO::getErythropoietin).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(erythropoietinStr)) {
                Map<String, List<String>> erythropoietinStrGroup = erythropoietinStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(erythropoietinStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = erythropoietinStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)促红素");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }

            //(医嘱)左卡尼汀
            List<String> carnitineStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCarnitine()))
                    .map(DialysisPreparationRespVO::getCarnitine).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(carnitineStr)) {
                Map<String, List<String>> carnitineStrGroup = carnitineStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(carnitineStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = carnitineStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)左卡尼汀");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(医嘱)蔗糖铁
            List<String> ironSucroseStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getIronSucrose()))
                    .map(DialysisPreparationRespVO::getIronSucrose).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ironSucroseStr)) {
                Map<String, List<String>> ironSucroseStrGroup = ironSucroseStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(ironSucroseStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = ironSucroseStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)蔗糖铁");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(医嘱)帕立骨化醇
            List<String> paricalcitolStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getParicalcitol()))
                    .map(DialysisPreparationRespVO::getParicalcitol).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(paricalcitolStr)) {
                Map<String, List<String>> paricalcitolStrGroup = paricalcitolStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(paricalcitolStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = paricalcitolStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)帕立骨化醇");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(医嘱)骨化三醇
            List<String> calcitriolStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcitriol()))
                    .map(DialysisPreparationRespVO::getCalcitriol).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(calcitriolStr)) {
                Map<String, List<String>> calcitriolStrGroup = calcitriolStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(calcitriolStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = calcitriolStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)骨化三醇");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(医嘱)尿激酶
            List<String> urokinaseStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getUrokinase()))
                    .map(DialysisPreparationRespVO::getUrokinase).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(urokinaseStr)) {
                Map<String, List<String>> urokinaseStrGroup = urokinaseStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(urokinaseStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = urokinaseStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)尿激酶");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(医嘱)葡萄糖酸钙
            List<String> calciumGluconateStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalciumGluconate()))
                    .map(DialysisPreparationRespVO::getCalciumGluconate).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(calciumGluconateStr)) {
                Map<String, List<String>> calciumGluconateStrGroup = calciumGluconateStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(calciumGluconateStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = calciumGluconateStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(医嘱)葡萄糖酸钙");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }

            //(推送)促红素
            List<String> erythropoietinPushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getErythropoietinPush()))
                    .map(DialysisPreparationRespVO::getErythropoietinPush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(erythropoietinPushStr)) {
                Map<String, List<String>> erythropoietinPushStrGroup = erythropoietinPushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(erythropoietinPushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = erythropoietinPushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)促红素");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }

            //(推送)左卡尼汀
            List<String> carnitinePushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCarnitinePush()))
                    .map(DialysisPreparationRespVO::getCarnitinePush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(carnitinePushStr)) {
                Map<String, List<String>> carnitinePushStrGroup = carnitinePushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(carnitinePushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = carnitinePushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)左卡尼汀");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(推送)蔗糖铁
            List<String> ironSucrosePushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getIronSucrosePush()))
                    .map(DialysisPreparationRespVO::getIronSucrosePush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ironSucrosePushStr)) {
                Map<String, List<String>> ironSucrosePushStrGroup = ironSucrosePushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(ironSucrosePushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = ironSucrosePushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)蔗糖铁");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(推送)帕立骨化醇
            List<String> paricalcitolPushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> !StringUtils.isEmpty(dialysisPreparationRespVO.getParicalcitolPush()))
                    .map(DialysisPreparationRespVO::getParicalcitolPush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(paricalcitolPushStr)) {
                Map<String, List<String>> paricalcitolPushStrGroup = paricalcitolPushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(paricalcitolPushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = paricalcitolPushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)帕立骨化醇");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(推送)骨化三醇
            List<String> calcitriolPushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalcitriolPush()))
                    .map(DialysisPreparationRespVO::getCalcitriolPush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(calcitriolPushStr)) {
                Map<String, List<String>> calcitriolPushStrGroup = calcitriolPushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(calcitriolPushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = calcitriolPushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)骨化三醇");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(推送)尿激酶
            List<String> urokinasePushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getUrokinasePush()))
                    .map(DialysisPreparationRespVO::getUrokinasePush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(urokinasePushStr)) {
                Map<String, List<String>> urokinasePushStrGroup = urokinasePushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(urokinasePushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = urokinasePushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)尿激酶");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }
            //(推送)葡萄糖酸钙
            List<String> calciumGluconatePushStr = dialysisPreparationRespVOS.stream().
                    filter(dialysisPreparationRespVO -> StrUtil.isNotEmpty(dialysisPreparationRespVO.getCalciumGluconatePush()))
                    .map(DialysisPreparationRespVO::getCalciumGluconatePush).map(preparation -> {
                        return Arrays.stream(preparation.split(",")).collect(Collectors.toList());
                    }).flatMap(s -> s.stream()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(calciumGluconatePushStr)) {
                Map<String, List<String>> calciumGluconatePushStrGroup = calciumGluconatePushStr.stream().collect(Collectors.groupingBy(s -> s));
                if (MapUtil.isNotEmpty(calciumGluconatePushStrGroup)) {
                    List<DialysisItemRespVO> dialysisItemRespVOS = calciumGluconatePushStrGroup.entrySet().stream().map(entry -> {
                        DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                        dialysisItemRespVO.setItemType("(推送)葡萄糖酸钙");
                        dialysisItemRespVO.setItemSpecification(entry.getKey());
                        dialysisItemRespVO.setNumber(entry.getValue().size() + "");
                        return dialysisItemRespVO;
                    }).collect(Collectors.toList());
                    list.addAll(dialysisItemRespVOS);
                }
            }

            // 透析材料 换药包和穿刺包 的统计
            if (1 == pageReqVO.getType()) {
                HemodialysisManagerPageReqVO hemodialysisManagerPageReqVO = BeanUtil.copyProperties(pageReqVO, HemodialysisManagerPageReqVO.class);
                hemodialysisManagerPageReqVO.setPageSize(9999);
                PageResult<DialysisPreparationRespVO> dialysisPreparation = this.getDialysisPreparation(hemodialysisManagerPageReqVO);

                // 存储 换药包和穿刺包 的统计数量
                Map<String, Integer> countMap = new HashMap<>();
                countMap.put("upMidPackage", 0);
                countMap.put("downMidPackage", 0);
                countMap.put("internalPackage", 0);

                // 统计总数到 countMap
                dialysisPreparation.getList().stream().forEach(dialysisPreparationRespVO -> {
                    // 换药包 上机换药包
                    Integer upMidPackage = dialysisPreparationRespVO.getUpMidPackage();
                    // 换药包 下机换药包
                    Integer downMidPackage = dialysisPreparationRespVO.getDownMidPackage();
                    // 穿刺包 内瘘穿刺包
                    Integer internalPackage = dialysisPreparationRespVO.getInternalPackage();

                    if (upMidPackage != null && upMidPackage > 0) {
                        countMap.put("upMidPackage", countMap.get("upMidPackage") + upMidPackage);
                    }
                    if (downMidPackage != null && downMidPackage > 0) {
                        countMap.put("downMidPackage", countMap.get("downMidPackage") + downMidPackage);
                    }
                    if (internalPackage != null && internalPackage > 0) {
                        countMap.put("internalPackage", countMap.get("internalPackage") + internalPackage);
                    }

                });

                // 遍历 countMap 封装数据
                List<DialysisItemRespVO> dialysisItemRespVOS = countMap.entrySet().stream().map(entry -> {
                    DialysisItemRespVO dialysisItemRespVO = new DialysisItemRespVO();
                    String key = entry.getKey();
                    if ("upMidPackage".equals(key)) {
                        dialysisItemRespVO.setItemType("换药包");
                        dialysisItemRespVO.setItemSpecification("上机换药包");
                    } else if ("downMidPackage".equals(key)) {
                        dialysisItemRespVO.setItemType("换药包");
                        dialysisItemRespVO.setItemSpecification("下机换药包");
                    } else if ("internalPackage".equals(key)) {
                        dialysisItemRespVO.setItemType("穿刺包");
                        dialysisItemRespVO.setItemSpecification("内瘘穿刺包");
                    }
                    dialysisItemRespVO.setNumber(entry.getValue() + "");
                    return dialysisItemRespVO;
                }).collect(Collectors.toList());
                list.addAll(dialysisItemRespVOS);
            }
            return list;
        }
        return null;
    }

    private AdviceResult postUrl(String url, List<SynToHisAdviceVo> synToHisAdviceVo) {
        synToHisAdviceVo.forEach(synToHisAdviceVo1 -> {
            // 查询给药途径
            if (!StringUtils.isEmpty(synToHisAdviceVo1.getDrugUsage())) {
                String drugUsage = synToHisAdviceVo1.getDrugUsage();
                String usageTypeId = "314937670834849029";
                HisDictData hisDictData = hisDictDataMapper.selectOne(HisDictData::getFDictionaryTypeId, usageTypeId, HisDictData::getFHmsValue, drugUsage);
                if (!StringUtils.isEmpty(hisDictData)) {
                    synToHisAdviceVo1.setDrugUsage(hisDictData.getFId());
                }else {
                    synToHisAdviceVo1.setDrugUsage("15");
                }
            }

            if (!StringUtils.isEmpty(synToHisAdviceVo1.getDrugInterval())) {
                String drugInterval = synToHisAdviceVo1.getDrugInterval();
                String interval = "308773635894543621";
                HisDictData hisDictData = hisDictDataMapper.selectOne(HisDictData::getFDictionaryTypeId, interval, HisDictData::getFHmsValue, drugInterval);
                if (!StringUtils.isEmpty(hisDictData)) {
                    synToHisAdviceVo1.setDrugInterval(hisDictData.getFId());
                }
            }
        });

        CloseableHttpClient httpClient = HttpClients.createDefault();
        Gson gson = new Gson();
        String req = gson.toJson(synToHisAdviceVo);
        log.info("requestParam：{}",req);
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).setSocketTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        StringEntity stringEntity = new StringEntity(req, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            HttpResponse response = httpClient.execute(httpPost);
            // 读取响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    result.append(line.trim());
                }
                if (!StringUtils.isEmpty(result)) {
                    String s = result.toString();
                    AdviceResult adviceResult = gson.fromJson(s, AdviceResult.class);
                    log.info("result:{}",adviceResult);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        return adviceResult;
                    }
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try{
                assert httpClient != null;
                httpClient.close();
            } catch (Exception ee){
                ee.printStackTrace();
            }

        }
        return null;
    }

    @Override
    public List<HemodialysisSiteRecordRespVO> getSiteRecordList(Long patientId, Integer size) {
        QueryWrapper<HemodialysisManagerDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HemodialysisManagerDO::getPatientId, patientId)
                .orderByDesc(HemodialysisManagerDO::getHemodialysisTime);
        if(size != null && size > 0){
            queryWrapper.last("limit " + size);
        }

        List<HemodialysisManagerDO> hemodialysisManagerDOS = hemodialysisManagerMapper.selectList(queryWrapper);
        List<HemodialysisSiteRecordRespVO> rlist = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(hemodialysisManagerDOS)){
            for (HemodialysisManagerDO hemodialysisManagerDO : hemodialysisManagerDOS) {
                HemodialysisSiteRecordRespVO respVO = new HemodialysisSiteRecordRespVO();
                respVO.setHemodialysisTime(hemodialysisManagerDO.getHemodialysisTime());
                /** 透前内瘘和导管 */
                String beforeJson = hemodialysisManagerDO.getBeforeJson();
                if(!StringUtils.isEmpty(beforeJson)) {
                    com.alibaba.fastjson.JSONObject beforeJsonObject = com.alibaba.fastjson.JSONObject.parseObject(beforeJson);
                    QueryWrapper<MottoSimpleDO> beforeQueryWrapper = new QueryWrapper<>();
                    beforeQueryWrapper.lambda().eq(MottoSimpleDO::getMottoId, 3);
                    List<MottoSimpleDO> beforeMottoSimpleDOS = mottoSimpleMapper.selectList(beforeQueryWrapper);
                    //内瘘
                    Optional<MottoSimpleDO> nlOptional = beforeMottoSimpleDOS.stream().filter(v -> "内瘘".equals(v.getPname())).findFirst();
                    if (nlOptional.isPresent()) {
                        Long nlItemId = nlOptional.get().getId();
                        Object value = beforeJsonObject.get("motto" + nlItemId);
                        respVO.setInternalFistula("透前：" + (StringUtils.isEmpty(value) ? "" : value));
                    }
                    //导管
                    Optional<MottoSimpleDO> dgOptional = beforeMottoSimpleDOS.stream().filter(v -> "导管".equals(v.getPname())).findFirst();
                    if (dgOptional.isPresent()) {
                        Long dgItemId = nlOptional.get().getId();
                        Object value = beforeJsonObject.get("motto" + dgItemId);
                        respVO.setConduit("透前：" + (StringUtils.isEmpty(value) ? "" : value));
                    }
                }else{
                    respVO.setInternalFistula("透前：");
                    respVO.setConduit("透前：");
                }

                /** 透后内瘘和导管 */
                String afterJson = hemodialysisManagerDO.getAfterJson();
                if(!StringUtils.isEmpty(afterJson)) {
                    com.alibaba.fastjson.JSONObject afterJsonObject = com.alibaba.fastjson.JSONObject.parseObject(afterJson);
                    QueryWrapper<MottoSimpleDO> afterQueryWrapper = new QueryWrapper<>();
                    afterQueryWrapper.lambda().eq(MottoSimpleDO::getMottoId, 5);
                    List<MottoSimpleDO> afterMottoSimpleDOS = mottoSimpleMapper.selectList(afterQueryWrapper);
                    //内瘘
                    Optional<MottoSimpleDO> nlOptional = afterMottoSimpleDOS.stream().filter(v -> "内瘘".equals(v.getPname())).findFirst();
                    if (nlOptional.isPresent()) {
                        Long nlItemId = nlOptional.get().getId();
                        Object value = afterJsonObject.get("motto" + nlItemId);
                        respVO.setInternalFistula(respVO.getInternalFistula() + "  透后：" + (StringUtils.isEmpty(value) ? "" : value));
                    }
                    //导管
                    Optional<MottoSimpleDO> dgOptional = afterMottoSimpleDOS.stream().filter(v -> "导管".equals(v.getPname())).findFirst();
                    if (dgOptional.isPresent()) {
                        Long dgItemId = nlOptional.get().getId();
                        Object value = afterJsonObject.get("motto" + dgItemId);
                        respVO.setConduit(respVO.getConduit() + "  透后：" + (StringUtils.isEmpty(value) ? "" : value));
                    }
                }else{
                    respVO.setInternalFistula(respVO.getInternalFistula() + "  透后：");
                    respVO.setConduit(respVO.getConduit() + "  透后：");
                }


                //穿刺  位点： A端位点 B端位点 穿刺型号
                if("1".equals(hemodialysisManagerDO.getDressingChange())){
                    StringBuilder builder = new StringBuilder();
                    builder.append("A端：" + (StringUtils.isEmpty(hemodialysisManagerDO.getSiteA()) ? "" : hemodialysisManagerDO.getSiteA()));
                    builder.append("   V端：" + (StringUtils.isEmpty(hemodialysisManagerDO.getSiteV()) ? "" : hemodialysisManagerDO.getSiteV()));
                    builder.append("   穿刺针型号：" + (StringUtils.isEmpty(hemodialysisManagerDO.getPunctureNeedleModel()) ? "" : hemodialysisManagerDO.getPunctureNeedleModel()));
                    respVO.setSite(builder.toString());
                }else if("2".equals(hemodialysisManagerDO.getDressingChange())){  //换药
                    String length = StringUtils.isEmpty(hemodialysisManagerDO.getCatheterOuterLength()) ? "" : hemodialysisManagerDO.getCatheterOuterLength();
                    respVO.setSite("导管外露长度：" + length + "cm");
                }

                rlist.add(respVO);
            }
        }

        return rlist;
    }

    @Override
    public PageResult<HemodialysisManagerRespVO> getPatientTreatRecordDetail(HemodialysisManagerPageReqVO pageReqVO) {
        return  null;
    }
}
