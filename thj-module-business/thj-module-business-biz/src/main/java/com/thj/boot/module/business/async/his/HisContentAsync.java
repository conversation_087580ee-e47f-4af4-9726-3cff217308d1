package com.thj.boot.module.business.async.his;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.thj.boot.common.async.ConstantFiled;
import com.thj.boot.common.async.threads.HisThreadLocal;
import com.thj.boot.common.tenantLine.TenantContextHolder;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.dal.mapper.hiscombo.HisComboMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.hisinformation.HisInformationMapper;
import com.thj.boot.module.business.utils.PinYinUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/18 17:12
 * @description
 */
@Component
@Slf4j
public class HisContentAsync {
    @Value("${his.drug}")
    private String drug;

    @Value("${his.consumables}")
    private String consumables;

    @Value("${his.prescriptionItem}")
    private String prescriptionItem;

    @Value("${his.prescriptionItemType}")
    private String prescriptionItemType;

    @Value("${his.combo}")
    private String combo;


    @Resource
    private HisDrugMapper hisDrugMapper;

    @Resource
    private HisConsumablesMapper hisConsumablesMapper;

    @Resource
    private HisInformationMapper hisInformationMapper;

    @Resource
    private HisComboMapper hisComboMapper;

    @Async(ConstantFiled.BAILUN_THREAD_POOL_DRUG)
    public Future<Boolean> asyncHisDrug(String hospitalId, Long userId, Long deptId) {
        int currentPage = 1;
        Map<String, Object> res = new LinkedHashMap<>();
        List<HisDrugDO> hisDrugDOS = Lists.newArrayList();
        TenantContextHolder.setTenantId(deptId);
        HisThreadLocal.setUserId(userId);
        for (; ; ) {
            res.put("pageSize", 500);
            res.put("sort", "desc");
            res.put("sidx", "F_CreatorTime");
            res.put("F_Hosipital", hospitalId);
            res.put("F_EnabledMark", 9);
            String result = HttpUtil.get(drug, res);
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Integer code = jsonObject.getInt("code");
            if (200 == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                if (list.size() > 0) {
                    for (Object o : list) {
                        JSONObject jo = JSONUtil.parseObj(o);
                        HisDrugDO hisDrugDO = new HisDrugDO();
                        hisDrugDO.setFDrugId(jo.getLong("F_DrugId"));
                        hisDrugDO.setFDrugName(jo.getStr("F_DrugName"));
                        hisDrugDO.setFDrugNumber(jo.getStr("F_DrugNumber"));
                        hisDrugDO.setFDrugSpec(jo.getStr("F_DrugSpec"));
                        hisDrugDO.setFPreparaUnit(jo.getStr("F_PreparaUnit"));
                        hisDrugDO.setFDrugForm(jo.getStr("F_DrugForm"));
                        hisDrugDO.setFPackUnit(jo.getStr("F_PackUnit"));
                        hisDrugDO.setFDrugFormCount(jo.getStr("F_DrugFormCount"));
                        hisDrugDO.setFDrugCategory(jo.getStr("F_DrugCategory"));
                        hisDrugDO.setFDrugDirectoryType(jo.getStr("F_DrugDirectoryType"));
                        hisDrugDO.setFDrugDirectoryCode(jo.getStr("F_DrugDirectoryCode"));
                        hisDrugDO.setFDrugPrice(jo.getStr("F_DrugPrice"));
                        hisDrugDO.setFRetailUnit(jo.getStr("F_RetailUnit"));
                        hisDrugDO.setFDmin(jo.getStr("F_Dmin"));
                        hisDrugDO.setFOnceUsing(jo.getStr("F_OnceUsing"));
                        hisDrugDO.setFSpecUnit(jo.getStr("F_SpecUnit"));
                        hisDrugDO.setFDrugChangjia(jo.getStr("F_DrugChangjia"));
                        hisDrugDO.setFApprovalNumber(jo.getStr("F_ApprovalNumber"));
                        hisDrugDO.setFLimitUseSign(jo.getStr("F_LimitUseSign"));
                        hisDrugDO.setFLimitUseScope(jo.getStr("F_LimitUseScope"));
                        hisDrugDO.setFHosipital(jo.getStr("F_Hosipital"));
                        hisDrugDO.setFEnabledMark(jo.getStr("F_EnabledMark"));
                        hisDrugDO.setFEnabledName(jo.getStr("F_EnabledName"));
                        hisDrugDO.setFIsAudit(jo.getStr("F_IsAudit"));
                        hisDrugDO.setFPurchasePrice(jo.getStr("F_PurchasePrice"));
                        hisDrugDO.setFAuditTime(jo.getLong("F_AuditTime"));
                        hisDrugDO.setFAuditPrice(jo.getStr("F_AuditPrice"));
                        hisDrugDO.setFAuditMedinsCode(jo.getStr("F_AuditMedInsCode"));
                        hisDrugDO.setFStockUnit(jo.getStr("F_StockUnit"));
                        hisDrugDO.setFStockDmin(jo.getStr("F_StockDmin"));
                        hisDrugDO.setFAnticType(jo.getStr("F_AnticType"));
                        try{

                        hisDrugDO.setPinyin(PinYinUtil.getFirstSpell(hisDrugDO.getFDrugName()));
                        }catch (Exception e) {
                            e.printStackTrace();
                        }
                        hisDrugDOS.add(hisDrugDO);

                    }
                    try {
                        hisDrugMapper.deleteHisDrug();
                        hisDrugMapper.insertBatch(hisDrugDOS);
                    } catch (Exception e){
                        e.printStackTrace();
                    }
                    finally {
                        HisThreadLocal.clear();
                    }
                    currentPage++;
                    res.put("currentPage", currentPage);
                }
                break;
            }
            break;
        }
        return new AsyncResult<>(true);
    }

    @Async(ConstantFiled.BAILUN_THREAD_POOL_CONSUMABLES)
    public Future<Boolean> asyncHisConsumables(String hospitalId, Long userId, Long deptId) {
        int currentPage = 1;
        Map<String, Object> res = new LinkedHashMap<>();
        List<HisConsumablesDO> hisConsumablesDOS = Lists.newArrayList();
        TenantContextHolder.setTenantId(deptId);
        HisThreadLocal.setUserId(userId);
        for (; ; ) {
            res.put("pageSize", 500);
            res.put("sort", "desc");
            res.put("sidx", "F_CreatorTime");
            res.put("F_Hosipital", hospitalId);
            res.put("F_EnabledMark", 9);
            String result = HttpUtil.get(consumables, res);
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Integer code = jsonObject.getInt("code");
            if (200 == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                if (list.size() > 0) {
                    for (Object o : list) {
                        JSONObject jo = JSONUtil.parseObj(o);
                        HisConsumablesDO hisConsumablesDO = new HisConsumablesDO();
                        hisConsumablesDO.setConsumId(jo.getLong("F_ConsumId"));
                        hisConsumablesDO.setConsumNumber(jo.getStr("F_ConsumNumber"));
                        hisConsumablesDO.setConsumName(jo.getStr("F_ConsumName"));
                        hisConsumablesDO.setDrugDirectoryType(jo.getStr("F_DrugDirectoryType"));
                        hisConsumablesDO.setMedicalinsCode(jo.getStr("F_MedicalInsCode"));
                        hisConsumablesDO.setConsumSpec(jo.getStr("F_ConsumSpec"));
                        hisConsumablesDO.setConsumType(jo.getStr("F_ConsumType"));
                        hisConsumablesDO.setDrugPrice(jo.getStr("F_DrugPrice"));
                        hisConsumablesDO.setUnit(jo.getStr("F_Unit"));
                        hisConsumablesDO.setApprovalNumber(jo.getStr("F_ApprovalNumber"));
                        hisConsumablesDO.setManufacturer(jo.getStr("F_Manufacturer"));
                        hisConsumablesDO.setHosipital(jo.getStr("F_Hosipital"));
                        hisConsumablesDO.setEnabledMark(jo.getStr("F_EnabledMark"));
                        hisConsumablesDO.setEnabledName(jo.getStr("F_EnabledName"));
                        hisConsumablesDO.setIsAudit(jo.getStr("F_IsAudit"));
                        hisConsumablesDO.setPurchasePrice(jo.getStr("F_PurchasePrice"));
                        hisConsumablesDO.setAuditTime(jo.getStr("F_AuditTime"));
                        hisConsumablesDO.setAuditPrice(jo.getStr("F_AuditPrice"));
                        hisConsumablesDO.setAuditMedinsCode(jo.getStr("F_AuditMedInsCode"));
                        hisConsumablesDO.setStockUnit(jo.getStr("F_StockUnit"));
                        hisConsumablesDO.setLimitUseSign(jo.getStr("F_LimitUseSign"));
                        hisConsumablesDO.setLimitUseScope(jo.getStr("F_LimitUseScope"));
                        hisConsumablesDOS.add(hisConsumablesDO);
                    }
                    try {
                        hisConsumablesMapper.deleteHisConsumable();
                        hisConsumablesMapper.insertBatch(hisConsumablesDOS);
                    } finally {
                        HisThreadLocal.clear();
                    }
                    currentPage++;
                    res.put("currentPage", currentPage);
                }
                break;
            }
            break;
        }
        return new AsyncResult<>(true);
    }

    @Async(ConstantFiled.BAILUN_THREAD_POOL_PRESCRIPTIONITEM)
    public Future<Boolean> asyncHisPrescriptionitem(String hospitalId, Long userId, Long deptId) {
        //项目分类
        String body = HttpUtil.get(prescriptionItemType);
        JSONObject jsonObject = JSONUtil.parseObj(body);
        Integer code = jsonObject.getInt("code");
        TenantContextHolder.setTenantId(deptId);
        HisThreadLocal.setUserId(userId);
        List<HisInformationDO> hisInformationDOS = Lists.newArrayList();
        if (200 == code) {
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray list = data.getJSONArray("list");
            if (list.size() > 0) {
                List<Map> mapList = JSONUtil.toList(list, Map.class);
                if (CollectionUtil.isNotEmpty(mapList)) {
                    List<String> enCodes = mapList.stream().map(map -> {
                        return map.get("enCode") + "";
                    }).collect(Collectors.toList());
                    for (String enCode : enCodes) {
                        Map<String, Object> res = new LinkedHashMap<>();
                        res.put("sidx", "F_CreatorTime");
                        res.put("F_Hosipital", hospitalId);
                        res.put("F_EnabledMark", 9);
                        res.put("F_ItemType", enCode);
                        String result = HttpUtil.get(prescriptionItem, res);
                        JSONObject jsonObject1 = JSONUtil.parseObj(result);
                        Integer code1 = jsonObject1.getInt("code");
                        if (200 == code1) {
                            JSONArray data1 = jsonObject1.getJSONArray("data");
                            if (data1.size() > 0) {
                                for (Object o : data1) {
                                    JSONObject jo = JSONUtil.parseObj(o);
                                    HisInformationDO hisInformationDO = new HisInformationDO();
                                    hisInformationDO.setItemId(jo.getLong("F_ItemId"));
                                    hisInformationDO.setItemName(jo.getStr("F_ItemName"));
                                    hisInformationDO.setUnit(jo.getStr("F_Unit"));
                                    hisInformationDO.setDrugDirectoryType(jo.getStr("F_DrugDirectoryType"));
                                    hisInformationDO.setDrugDirectoryCode(jo.getStr("F_DrugDirectoryCode"));
                                    hisInformationDO.setUsages(jo.getStr("F_Usage"));
                                    hisInformationDO.setUnitPrice(jo.getStr("F_UnitPrice"));
                                    hisInformationDO.setCustomCode(jo.getStr("F_CustomCode"));
                                    hisInformationDO.setItemType(jo.getStr("F_ItemType"));
                                    hisInformationDO.setIsExternalInspection(jo.getStr("F_IsExternalInspection"));
                                    hisInformationDO.setExternalInspectionName(jo.getStr("F_ExternalInspectionName"));
                                    hisInformationDO.setExternalInspectionNumber(jo.getStr("F_ExternalInspectionNumber"));
                                    hisInformationDO.setHosipital(jo.getStr("F_Hosipital"));
                                    hisInformationDO.setEnabledMark(jo.getStr("F_EnabledMark"));
                                    hisInformationDO.setEnabledName(jo.getStr("F_EnabledName"));
                                    hisInformationDO.setIsAudit(jo.getStr("F_IsAudit"));
                                    hisInformationDO.setPurchasePrice(jo.getStr("F_PurchasePrice"));
                                    hisInformationDO.setAuditTime(jo.getStr("F_AuditTime"));
                                    hisInformationDO.setAuditPrice(jo.getStr("F_AuditPrice"));
                                    hisInformationDO.setAuditMedInsCode(jo.getStr("F_AuditMedInsCode"));
                                    hisInformationDO.setLimitUseSign(jo.getStr("F_LimitUseSign"));
                                    hisInformationDO.setLimitUseScope(jo.getStr("F_LimitUseScope"));
                                    hisInformationDOS.add(hisInformationDO);
                                }
                            }
                        }
                    }
                    try {
                        hisInformationMapper.deleteHisInformation();
                        hisInformationMapper.insertBatch(hisInformationDOS);
                    } finally {
                        HisThreadLocal.clear();
                    }
                }
            }
        }
        return new AsyncResult<>(true);
    }

    @Async(ConstantFiled.BAILUN_THREAD_POOL_PRESCRIPTIONITEM)
    public Future<Boolean> asyncHisCombo(String hospitalId, long userId, Long deptId) {
        int currentPage = 1;
        Map<String, Object> res = new LinkedHashMap<>();
        List<HisComboDO> hisComboDOS = Lists.newArrayList();
        TenantContextHolder.setTenantId(deptId);
        HisThreadLocal.setUserId(userId);
        for (; ; ) {
            res.put("pageSize", 500);
            res.put("sort", "desc");
            res.put("sidx", "F_CreatorTime");
            res.put("F_Hosipital", hospitalId);
            res.put("F_EnabledMark", 9);
            String result = HttpUtil.get(combo, res);
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Integer code = jsonObject.getInt("code");
            if (200 == code) {
                JSONObject data = jsonObject.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                if (list.size() > 0) {
                    for (Object o : list) {
                        JSONObject jo = JSONUtil.parseObj(o);
                        HisComboDO hisComboDO = new HisComboDO();
                        hisComboDO.setPackageId(jo.getLong("F_PackageId"));
                        hisComboDO.setEnabledMark(jo.getInt("F_EnabledMark"));
                        hisComboDO.setHosipital(jo.getStr("F_Hosipital"));
                        hisComboDO.setPackageName(jo.getStr("F_PackageName"));
                        hisComboDO.setPackagePrice(jo.getBigDecimal("F_Price"));
                        hisComboDOS.add(hisComboDO);
                    }
                    try {
                        hisComboMapper.deleteHisCombo();
                        hisComboMapper.insertBatch(hisComboDOS);
                    } finally {
                        HisThreadLocal.clear();
                    }
                    currentPage++;
                    res.put("currentPage", currentPage);
                }
                break;
            }
            break;
        }
        return new AsyncResult<>(true);
    }
}
