package com.thj.boot.module.business.convert.renalprojectinfo;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.renalprojectinfo.RenalProjectInfoDO;
import com.thj.boot.module.business.pojo.renalprojectinfo.vo.RenalProjectInfoCreateReqVO;
import com.thj.boot.module.business.pojo.renalprojectinfo.vo.RenalProjectInfoExcelVO;
import com.thj.boot.module.business.pojo.renalprojectinfo.vo.RenalProjectInfoRespVO;
import com.thj.boot.module.business.pojo.renalprojectinfo.vo.RenalProjectInfoUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 肾科检查项目信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RenalProjectInfoConvert {

    RenalProjectInfoConvert INSTANCE = Mappers.getMapper(RenalProjectInfoConvert.class);

    RenalProjectInfoDO convert(RenalProjectInfoCreateReqVO bean);

    RenalProjectInfoDO convert(RenalProjectInfoUpdateReqVO bean);

    RenalProjectInfoRespVO convert(RenalProjectInfoDO bean);

    List<RenalProjectInfoRespVO> convertList(List<RenalProjectInfoDO> list);

    PageResult<RenalProjectInfoRespVO> convertPage(PageResult<RenalProjectInfoDO> page);

    List<RenalProjectInfoExcelVO> convertList02(List<RenalProjectInfoDO> list);

}
