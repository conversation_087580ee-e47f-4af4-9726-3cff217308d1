package com.thj.boot.module.business.service.mottoadvice;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.mottoadvice.MottoAdviceConvert;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.dal.datado.mottoadvice.MottoAdviceDO;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.dal.mapper.drug.DrugMapper;
import com.thj.boot.module.business.dal.mapper.hiscombo.HisComboMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.hisinformation.HisInformationMapper;
import com.thj.boot.module.business.dal.mapper.mottoadvice.MottoAdviceMapper;
import com.thj.boot.module.business.dal.mapper.project.ProjectMapper;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdvicePageReqVO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO;
import com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceUpdateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 医嘱摸模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MottoAdviceServiceImpl implements MottoAdviceService {

    @Resource
    private MottoAdviceMapper mottoAdviceMapper;

    @Resource
    private HisDrugMapper hisDrugMapper;

    @Resource
    private HisInformationMapper hisInformationMapper;

    @Resource
    private DrugMapper drugMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private HisComboMapper hisComboMapper;

    @Override
    public Long createMottoAdvice(MottoAdviceDO mottoAdvice) {
        // 插入
//        MottoAdviceDO mottoAdvice = MottoAdviceConvert.INSTANCE.convert(createReqVO);
        mottoAdviceMapper.saveOrUpdate(mottoAdvice);
        // 返回
        return mottoAdvice.getId();
    }

    @Override
    public void updateMottoAdvice(MottoAdviceUpdateReqVO updateReqVO) {
        // 更新
        MottoAdviceDO updateObj = MottoAdviceConvert.INSTANCE.convert(updateReqVO);

        mottoAdviceMapper.updateById(updateObj);
        mottoAdviceMapper.updateByPid(updateObj.getAdviceType(), updateObj.getId());
    }

    @Override
    public void deleteMottoAdvice(Long id) {
        // 校验存在
        // 删除
        mottoAdviceMapper.deleteById(id);
        //删除子药
        mottoAdviceMapper.deleteByParentId(id);
    }


    @Override
    public MottoAdviceRespVO getMottoAdvice(Long id) {
        MottoAdviceDO mottoAdviceDO = mottoAdviceMapper.selectById(id);
        return MottoAdviceConvert.INSTANCE.convert(mottoAdviceDO);
    }

    @Override
    public List<MottoAdviceRespVO> getMottoAdviceList(Long templateId, String adviceType) {
        List<MottoAdviceRespVO> list = mottoAdviceMapper.queryList(templateId, adviceType);
        List<MottoAdviceRespVO> drugList1 = mottoAdviceMapper.queryDrugList(templateId);
        list.addAll(drugList1);
        if (StringUtils.isEmpty(adviceType)) {
            List<MottoAdviceRespVO> mottoAdviceRespVOS = mottoAdviceMapper.queryConsumeList(templateId);
            list.addAll(mottoAdviceRespVOS);

            List<MottoAdviceRespVO> mottoAdviceRespVOS1 = mottoAdviceMapper.queryComboList(templateId);
            list.addAll(mottoAdviceRespVOS1);

            List<MottoAdviceRespVO> infoList = mottoAdviceMapper.queryInfoList(templateId);
            list.addAll(infoList);

            List<MottoAdviceRespVO> projectList = mottoAdviceMapper.queryProjectList(templateId);
            list.addAll(projectList);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            list = list.stream().peek(mottoAdviceRespVO -> {
                mottoAdviceRespVO.setFEnabledMark("1");
                HisDrugDO hisDrugDO = hisDrugMapper.selectOne(new LambdaQueryWrapper<HisDrugDO>()
                        .eq(HisDrugDO::getFDrugId, mottoAdviceRespVO.getDrugId())
                        .select(HisDrugDO::getFDrugName,HisDrugDO::getFDrugSpec,HisDrugDO::getFEnabledMark));
                if (hisDrugDO != null) {
                    mottoAdviceRespVO.setFDrugName(hisDrugDO.getFDrugName());
                    mottoAdviceRespVO.setFDrugSpec(hisDrugDO.getFDrugSpec());
                    mottoAdviceRespVO.setFEnabledMark(hisDrugDO.getFEnabledMark());
                }
                HisInformationDO hisInformationDO = hisInformationMapper.selectOne(new LambdaQueryWrapper<HisInformationDO>()
                        .eq(HisInformationDO::getItemId, mottoAdviceRespVO.getDrugId())
                        .select(HisInformationDO::getItemName,HisInformationDO::getEnabledMark));
                if (hisInformationDO != null) {
                    mottoAdviceRespVO.setFDrugName(hisInformationDO.getItemName());
                    mottoAdviceRespVO.setFEnabledMark(hisInformationDO.getEnabledMark());
                }
                DrugDO drugDO = drugMapper.selectOne(new LambdaQueryWrapper<DrugDO>()
                        .eq(DrugDO::getId, mottoAdviceRespVO.getDrugId())
                        .select(DrugDO::getName));
                if (drugDO != null) {
                    mottoAdviceRespVO.setFDrugName(drugDO.getName());
                }
                ProjectDO projectDO = projectMapper.selectOne(new LambdaQueryWrapper<ProjectDO>()
                        .eq(ProjectDO::getId, mottoAdviceRespVO.getDrugId())
                        .select(ProjectDO::getName));
                if (projectDO != null) {
                    mottoAdviceRespVO.setFDrugName(projectDO.getName());
                }

                HisComboDO hisComboDO = hisComboMapper.selectOne(new LambdaQueryWrapper<HisComboDO>()
                        .eq(HisComboDO::getPackageId, mottoAdviceRespVO.getDrugId())
                        .select(HisComboDO::getPackageName));
                if (hisComboDO != null) {
                    mottoAdviceRespVO.setFDrugName(hisComboDO.getPackageName());
                }

            }).filter(mottoAdviceRespVO -> "1".equals(mottoAdviceRespVO.getFEnabledMark())).collect(Collectors.toList());
        }
        return list.stream()
                .sorted((obj1, obj2) -> {
                    // 获取排序
                    Integer sort1 = obj1.getSort();
                    Integer sort2 = obj2.getSort();

                    // 没有排序的后排放
                    if (sort1 == null && sort2 == null) return 0;
                    if (sort1 == null) return 1;
                    if (sort2 == null) return -1;
                    return sort1.compareTo(sort2);
                }).collect(Collectors.toList());
    }

    private void traverseList(JSONObject jsonObject) {
        JSONArray children = jsonObject.getJSONArray("children");
        if (children.isEmpty()) {
            return;
        }
        List<JSONObject> objects = children.toJavaList(JSONObject.class);
        for (JSONObject item : objects) {
            traverseList(item);
        }
    }


    @Override
    public PageResult<MottoAdviceDO> getMottoAdvicePage(MottoAdvicePageReqVO pageReqVO) {
        return mottoAdviceMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MottoAdviceRespVO> getMottoAdviceLists(String templateIds) {

        List<Long> idList = Arrays.stream(templateIds.split(",")).map(Long::valueOf).collect(Collectors.toList());

        List<MottoAdviceRespVO> allList = new ArrayList<>();
        List<MottoAdviceRespVO> list = mottoAdviceMapper.queryLists(idList);
        allList.addAll(list);

/*        List<MottoAdviceRespVO> consumeList = mottoAdviceMapper.queryConsumeList(idList);
        allList.addAll(consumeList);

        List<MottoAdviceRespVO> comboList = mottoAdviceMapper.queryComboList(idList);
        allList.addAll(comboList);

        List<MottoAdviceRespVO> infoList = mottoAdviceMapper.queryInfoList(idList);
        allList.addAll(infoList);*/
        return allList;
    }

    @Override
    public void updateSortMottoSimple(List<MottoAdviceUpdateReqVO> updateReqVOs) {
        List<MottoAdviceDO> mottoAdviceDOList = updateReqVOs.stream()
                .map(updateReqVO -> {
                    MottoAdviceDO mottoAdviceDO = mottoAdviceMapper.selectById(updateReqVO.getId());
                    if (mottoAdviceDO != null) {
                        mottoAdviceDO.setSort(updateReqVO.getSort());
                    }
                    return mottoAdviceDO;
                })
                .filter(mottoAdviceDO -> mottoAdviceDO != null)
                .collect(Collectors.toList());

        if (!mottoAdviceDOList.isEmpty()) {
            mottoAdviceMapper.updateBatch(mottoAdviceDOList);
        }
    }


}
