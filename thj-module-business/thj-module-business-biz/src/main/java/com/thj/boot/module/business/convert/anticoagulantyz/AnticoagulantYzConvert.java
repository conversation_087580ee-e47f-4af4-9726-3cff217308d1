package com.thj.boot.module.business.convert.anticoagulantyz;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.anticoagulantyz.AnticoagulantYzDO;
import com.thj.boot.module.business.pojo.anticoagulantyz.vo.AnticoagulantYzCreateReqVO;
import com.thj.boot.module.business.pojo.anticoagulantyz.vo.AnticoagulantYzRespVO;
import com.thj.boot.module.business.pojo.anticoagulantyz.vo.AnticoagulantYzUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医嘱 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AnticoagulantYzConvert {

    AnticoagulantYzConvert INSTANCE = Mappers.getMapper(AnticoagulantYzConvert.class);

    AnticoagulantYzDO convert(AnticoagulantYzCreateReqVO bean);

    AnticoagulantYzDO convert(AnticoagulantYzUpdateReqVO bean);

    AnticoagulantYzRespVO convert(AnticoagulantYzDO bean);

    List<AnticoagulantYzRespVO> convertList(List<AnticoagulantYzDO> list);

    PageResult<AnticoagulantYzRespVO> convertPage(PageResult<AnticoagulantYzDO> page);

}
