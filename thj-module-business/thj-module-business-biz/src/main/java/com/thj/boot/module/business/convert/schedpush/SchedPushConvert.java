package com.thj.boot.module.business.convert.schedpush;

import com.thj.boot.module.business.dal.datado.schedpush.SchedPushDO;
import com.thj.boot.module.business.pojo.schedpush.vo.SchedPushRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 敏感指标监测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SchedPushConvert {

    SchedPushConvert INSTANCE = Mappers.getMapper(SchedPushConvert.class);


    List<SchedPushRespVO> convertList(List<SchedPushDO> schedPushDOS);

    List<SchedPushDO> convertList2(List<SchedPushRespVO> schedPushRespVOS);
}
