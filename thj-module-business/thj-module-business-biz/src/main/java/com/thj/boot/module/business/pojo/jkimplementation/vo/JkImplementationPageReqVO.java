package com.thj.boot.module.business.pojo.jkimplementation.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JkImplementationPageReqVO extends PageParam {

    private Long id;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析次数
     */
    private Integer dialyzeTotal;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 教育次数
     */
    private Integer cunt;
    /**
     * 今日排版
     */
    private String dayScheduling;
    /**
     * 透析机号
     */
    private String machineNum;
    /**
     *
     */
    private Long nurseId;
    /**
     *
     */
    private String nurseName;
    /**
     * 最后教育时间
     */
    private String lastTime;
    /**
     * 年月
     */
    private String createMouth;
    /**
     * 教育情况: 0 未教育,1已教育
     */
    private String state;
    /**
     * 微信绑定
     */
    private String binding;
    /**
     * 病区
     */
    private String ward;
    /**
     * 拼音
     */
    private String pinyin;


}
