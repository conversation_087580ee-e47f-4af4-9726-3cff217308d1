package com.thj.boot.module.business.convert.muscleforce;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.muscleforce.MuscleForceDO;
import com.thj.boot.module.business.pojo.muscleforce.vo.MuscleForceCreateReqVO;
import com.thj.boot.module.business.pojo.muscleforce.vo.MuscleForceRespVO;
import com.thj.boot.module.business.pojo.muscleforce.vo.MuscleForceUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 肌力评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MuscleForceConvert {

    MuscleForceConvert INSTANCE = Mappers.getMapper(MuscleForceConvert.class);

    MuscleForceDO convert(MuscleForceCreateReqVO bean);

    MuscleForceDO convert(MuscleForceUpdateReqVO bean);

    MuscleForceRespVO convert(MuscleForceDO bean);

    List<MuscleForceRespVO> convertList(List<MuscleForceDO> list);

    PageResult<MuscleForceRespVO> convertPage(PageResult<MuscleForceDO> page);


}
