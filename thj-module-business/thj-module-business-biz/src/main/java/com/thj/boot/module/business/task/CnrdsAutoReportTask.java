package com.thj.boot.module.business.task;

import com.thj.boot.module.business.cnrds.CnrdsAutoReportManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;

@Component
@Slf4j
public class CnrdsAutoReportTask {

    @Autowired
    private CnrdsAutoReportManager cnrdsAutoReportManager;

    /**
     * 初始化下一年的同步管理数据
     * 每年12月25-27号23点执行
     */
    @Scheduled(cron = "0 0 2 25-27 12 *")
    //@Scheduled(cron = "0 0/3 * * * ?")
    public void initAllPatientSyncDataTask() {
        log.info("initAllPatientSyncDataTask start");
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        cnrdsAutoReportManager.initAllPatientSyncData(String.valueOf(currentYear + 1));
        log.info("initAllPatientSyncDataTask end");
    }

    /**
     * 更新每个患者同步管理标识状态，更新为N
     * 每月25-27号23点
     */
    @Scheduled(cron = "0 0 1 25-27 * *")
    //@Scheduled(cron = "0 0/2 * * * ?")
    public void updateReportManagerSyncFlagTask(){
        log.info("updateReportManagerSyncFlagTask start");
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        cnrdsAutoReportManager.updateReportManagerSyncFlag(String.valueOf(currentYear + 1), null);
        log.info("updateReportManagerSyncFlagTask end");
    }

    /**
     * 上报数据任务
     * 每月1-23号1点
     */
    @Scheduled(cron = "0 0 1 1-10 * *")
    //@Scheduled(cron = "0 0/2 * * * ?")
    public void reportDataTask(){
        log.info("reportDataTask start");
        cnrdsAutoReportManager.reportDataTask(null,null, null);
        log.info("reportDataTask end");
    }





}
