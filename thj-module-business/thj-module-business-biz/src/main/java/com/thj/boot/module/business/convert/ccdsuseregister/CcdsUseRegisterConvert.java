package com.thj.boot.module.business.convert.ccdsuseregister;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.ccdsuseregister.CcdsUseRegisterDO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterRespVO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ccds使用登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CcdsUseRegisterConvert {

    CcdsUseRegisterConvert INSTANCE = Mappers.getMapper(CcdsUseRegisterConvert.class);

    CcdsUseRegisterDO convert(CcdsUseRegisterCreateReqVO bean);

    CcdsUseRegisterDO convert(CcdsUseRegisterUpdateReqVO bean);

    CcdsUseRegisterRespVO convert(CcdsUseRegisterDO bean);

    List<CcdsUseRegisterRespVO> convertList(List<CcdsUseRegisterDO> list);

    PageResult<CcdsUseRegisterRespVO> convertPage(PageResult<CcdsUseRegisterDO> page);


}
