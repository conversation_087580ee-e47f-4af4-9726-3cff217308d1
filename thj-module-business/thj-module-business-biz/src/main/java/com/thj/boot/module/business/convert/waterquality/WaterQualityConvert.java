package com.thj.boot.module.business.convert.waterquality;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.waterquality.WaterQualityDO;
import com.thj.boot.module.business.pojo.waterquality.vo.WaterQualityCreateReqVO;
import com.thj.boot.module.business.pojo.waterquality.vo.WaterQualityRespVO;
import com.thj.boot.module.business.pojo.waterquality.vo.WaterQualityUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 水处理机质量检测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WaterQualityConvert {

    WaterQualityConvert INSTANCE = Mappers.getMapper(WaterQualityConvert.class);

    WaterQualityDO convert(WaterQualityCreateReqVO bean);

    WaterQualityDO convert(WaterQualityUpdateReqVO bean);

    WaterQualityRespVO convert(WaterQualityDO bean);

    List<WaterQualityRespVO> convertList(List<WaterQualityDO> list);

    PageResult<WaterQualityRespVO> convertPage(PageResult<WaterQualityDO> page);


}
