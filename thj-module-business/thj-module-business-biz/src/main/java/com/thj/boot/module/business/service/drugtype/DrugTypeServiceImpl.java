package com.thj.boot.module.business.service.drugtype;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.drugtype.DrugTypeConvert;
import com.thj.boot.module.business.dal.datado.drugtype.DrugTypeDO;
import com.thj.boot.module.business.dal.mapper.drugtype.DrugTypeMapper;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeCreateReqVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypePageReqVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 药品字典分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DrugTypeServiceImpl implements DrugTypeService {

    @Resource
    private DrugTypeMapper drugTypeMapper;

    @Override
    public Long createDrugType(DrugTypeCreateReqVO createReqVO) {
        //校验每个层级不能有重复名称
        checkCreateDrugName(createReqVO);
        // 插入
        DrugTypeDO drugType = DrugTypeConvert.INSTANCE.convert(createReqVO);
        drugTypeMapper.insert(drugType);
        // 返回
        return drugType.getId();
    }

    private void checkCreateDrugName(DrugTypeCreateReqVO createReqVO) {
        Long count = drugTypeMapper.selectCount(DrugTypeDO::getPid, createReqVO.getPid(), DrugTypeDO::getName, createReqVO.getName());
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.NAME_EXITS);
        }
    }

    @Override
    public void updateDrugType(DrugTypeUpdateReqVO updateReqVO) {
        checkUpdateDrugName(updateReqVO);
        // 更新
        DrugTypeDO updateObj = DrugTypeConvert.INSTANCE.convert(updateReqVO);
        drugTypeMapper.updateById(updateObj);
    }

    private void checkUpdateDrugName(DrugTypeUpdateReqVO updateReqVO) {
        DrugTypeDO drugTypeDO = drugTypeMapper.selectOne(DrugTypeDO::getName, updateReqVO.getName());
        if (drugTypeDO != null && drugTypeDO.getId() != updateReqVO.getId()) {
            throw new ServiceException(GlobalErrorCodeConstants.NAME_EXITS);
        }
    }

    @Override
    public void deleteDrugType(Long id) {
        Long count = drugTypeMapper.selectCount(DrugTypeDO::getPid, id);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.MOTTO_JOIN);
        }
        // 删除
        drugTypeMapper.deleteById(id);
    }


    @Override
    public DrugTypeDO getDrugType(Long id) {
        return drugTypeMapper.selectById(id);
    }

    @Override
    public List<DrugTypeDO> getDrugTypeList(Collection<Long> ids) {
        return drugTypeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DrugTypeDO> getDrugTypePage(DrugTypePageReqVO pageReqVO) {
        return drugTypeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DrugTypeRespVO> getDrugTypeList(DrugTypeCreateReqVO createReqVO) {
        List<DrugTypeDO> drugTypeDOS = drugTypeMapper.selectList(createReqVO);
        List<DrugTypeRespVO> drugTypeRespVOS = DrugTypeConvert.INSTANCE.convertList(drugTypeDOS);
        List<DrugTypeRespVO> rootList = null;
        if (CollectionUtil.isNotEmpty(drugTypeRespVOS)) {
            rootList = drugTypeRespVOS.stream().filter(drugTypeRespVO -> 0 == drugTypeRespVO.getPid()).collect(Collectors.toList());
            List<DrugTypeRespVO> subList = drugTypeRespVOS.stream().filter(drugTypeRespVO -> 0 != drugTypeRespVO.getPid()).collect(Collectors.toList());
            rootList.forEach(root -> busort(root, subList));
        }
        return rootList;
    }

    private void busort(DrugTypeRespVO root, List<DrugTypeRespVO> subList) {
        List<DrugTypeRespVO> childrenList = subList.stream().filter(drugTypeRespVO -> drugTypeRespVO.getPid().equals(Long.valueOf(root.getId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(childrenList)) {
            root.setChildren(childrenList);
            childrenList.forEach(sysMenu -> busort(sysMenu, subList));
        } else {
            root.setChildren(null);
        }
    }

}
