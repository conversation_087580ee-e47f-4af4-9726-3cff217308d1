package com.thj.boot.module.business.service.others;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.others.OthersConvert;
import com.thj.boot.module.business.dal.datado.others.OthersDO;
import com.thj.boot.module.business.dal.mapper.others.OthersMapper;
import com.thj.boot.module.business.pojo.others.vo.OthersCreateReqVO;
import com.thj.boot.module.business.pojo.others.vo.OthersPageReqVO;
import com.thj.boot.module.business.pojo.others.vo.OthersRespVO;
import com.thj.boot.module.business.pojo.others.vo.OthersUpdateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 其他-维修登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OthersServiceImpl implements OthersService {

    @Resource
    private OthersMapper othersMapper;

    @Override
    public Long createOthers(OthersCreateReqVO createReqVO) {
        // 插入
        OthersDO others = OthersConvert.INSTANCE.convert(createReqVO);
        othersMapper.insert(others);
        // 返回
        return others.getId();
    }

    @Override
    public void updateOthers(OthersUpdateReqVO updateReqVO) {
        // 更新
        OthersDO updateObj = OthersConvert.INSTANCE.convert(updateReqVO);
        othersMapper.updateById(updateObj);
    }

    @Override
    public void deleteOthers(Long id) {
        // 删除
        othersMapper.deleteById(id);
    }


    @Override
    public OthersDO getOthers(Long id) {
        return othersMapper.selectById(id);
    }

    @Override
    public List<OthersDO> getOthersList(Collection<Long> ids) {
        return othersMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<OthersDO> getOthersPage(OthersPageReqVO pageReqVO) {
        return othersMapper.selectPage(pageReqVO);
    }

    @Override
    public List<OthersDO> getOthersList(OthersCreateReqVO createReqVO) {
        return othersMapper.selectList(createReqVO);
    }

    @Override
    public OthersRespVO getNewMaintenanceRegister(OthersCreateReqVO createReqVO) {
        List<OthersDO> othersDOS = othersMapper.selectList(new LambdaQueryWrapperX<OthersDO>()
                .eqIfPresent(OthersDO::getManagerId, createReqVO.getManagerId())
                .orderByDesc(OthersDO::getId));
        List<OthersRespVO> othersRespVOS = OthersConvert.INSTANCE.convertList(othersDOS);
        OthersRespVO othersRespVO = null;
        if (CollectionUtil.isNotEmpty(othersRespVOS)) {
            othersRespVO = othersRespVOS.stream().findFirst().get();
        }
        return othersRespVO;
    }

}
