package com.thj.boot.module.business.convert.anticoagulant;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.anticoagulant.AnticoagulantDO;
import com.thj.boot.module.business.pojo.anticoagulant.vo.AnticoagulantCreateReqVO;
import com.thj.boot.module.business.pojo.anticoagulant.vo.AnticoagulantRespVO;
import com.thj.boot.module.business.pojo.anticoagulant.vo.AnticoagulantUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 抗凝剂类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AnticoagulantConvert {

    AnticoagulantConvert INSTANCE = Mappers.getMapper(AnticoagulantConvert.class);

    AnticoagulantDO convert(AnticoagulantCreateReqVO bean);

    AnticoagulantDO convert(AnticoagulantUpdateReqVO bean);

    AnticoagulantRespVO convert(AnticoagulantDO bean);

    List<AnticoagulantRespVO> convertList(List<AnticoagulantDO> list);

    PageResult<AnticoagulantRespVO> convertPage(PageResult<AnticoagulantDO> page);


}
