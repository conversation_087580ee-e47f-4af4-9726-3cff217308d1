package com.thj.boot.module.business.convert.tempcontent;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.tempcontent.TempContentDO;
import com.thj.boot.module.business.pojo.tempcontent.vo.TempContentCreateReqVO;
import com.thj.boot.module.business.pojo.tempcontent.vo.TempContentRespVO;
import com.thj.boot.module.business.pojo.tempcontent.vo.TempContentUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模版消息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TempContentConvert {

    TempContentConvert INSTANCE = Mappers.getMapper(TempContentConvert.class);

    TempContentDO convert(TempContentCreateReqVO bean);

    TempContentDO convert(TempContentUpdateReqVO bean);

    TempContentRespVO convert(TempContentDO bean);

    List<TempContentRespVO> convertList(List<TempContentDO> list);

    PageResult<TempContentRespVO> convertPage(PageResult<TempContentDO> page);


}
