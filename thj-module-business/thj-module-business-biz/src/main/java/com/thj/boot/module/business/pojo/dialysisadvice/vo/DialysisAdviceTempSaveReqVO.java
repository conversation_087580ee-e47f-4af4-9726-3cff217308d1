package com.thj.boot.module.business.pojo.dialysisadvice.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisAdviceTempSaveReqVO extends DialysisAdviceBaseVO {

    private String updateFrequency;


    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateActivateTime;
}
