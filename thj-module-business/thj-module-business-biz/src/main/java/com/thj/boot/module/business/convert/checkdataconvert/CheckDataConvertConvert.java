package com.thj.boot.module.business.convert.checkdataconvert;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.checkdataconvert.CheckDataConvertDO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertCreateReqVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertExcelVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertRespVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据转换 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CheckDataConvertConvert {

    CheckDataConvertConvert INSTANCE = Mappers.getMapper(CheckDataConvertConvert.class);

    CheckDataConvertDO convert(CheckDataConvertCreateReqVO bean);

    CheckDataConvertDO convert(CheckDataConvertUpdateReqVO bean);

    CheckDataConvertRespVO convert(CheckDataConvertDO bean);

    List<CheckDataConvertRespVO> convertList(List<CheckDataConvertDO> list);

    PageResult<CheckDataConvertRespVO> convertPage(PageResult<CheckDataConvertDO> page);

    List<CheckDataConvertExcelVO> convertList02(List<CheckDataConvertDO> list);

}
