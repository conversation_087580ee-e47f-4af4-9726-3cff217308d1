package com.thj.boot.module.business.convert.disinfectionunit;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.disinfectionunit.DisinfectionUnitDO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitRespVO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析间常规消毒 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DisinfectionUnitConvert {

    DisinfectionUnitConvert INSTANCE = Mappers.getMapper(DisinfectionUnitConvert.class);

    DisinfectionUnitDO convert(DisinfectionUnitCreateReqVO bean);

    DisinfectionUnitDO convert(DisinfectionUnitUpdateReqVO bean);

    DisinfectionUnitRespVO convert(DisinfectionUnitDO bean);

    List<DisinfectionUnitRespVO> convertList(List<DisinfectionUnitDO> list);

    PageResult<DisinfectionUnitRespVO> convertPage(PageResult<DisinfectionUnitDO> page);


}
