package com.thj.boot.module.business.service.infra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.thj.boot.common.utils.FileUtils;
import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.convert.infra.InfraConvert;
import com.thj.boot.module.business.dal.datado.infra.InfraDO;
import com.thj.boot.module.business.dal.mapper.infra.InfraMapper;
import com.thj.boot.module.business.pojo.infra.vo.InfraCreateReqVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraRespVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraUpdateReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/24 16:39
 * @description
 */
@Service
@Slf4j
public class InfraServiceImpl implements InfraService {

    @Value("${infra.hostname}")
    private String infraFile;

    @Resource
    private InfraMapper infraMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InfraResult uploadFile(InfraCreateReqVO createReqVO) {
        MultipartFile file = createReqVO.getFile();
        String originalFilename = StrUtil.isNotBlank(createReqVO.getFileName()) ? createReqVO.getFileName() : file.getOriginalFilename();
        try {
            byte[] content = IoUtil.readBytes(file.getInputStream());
            //生成随机后缀名
            String path = FileUtils.generatePath(content, originalFilename);
            //如果上传的文件名一致直接返回，否则新增
            //InfraDO infraObject = infraMapper.selectOne(InfraDO::getFileName, originalFilename);
            //if (infraObject != null) {
            //    //修改
            //    InfraDO updateInfraDO = new InfraDO();
            //    updateInfraDO.setId(infraObject.getId());
            //    updateInfraDO.setContent(content);
            //    updateInfraDO.setTableId(infraObject.getTableId());
            //    updateInfraDO.setTableName(infraObject.getTableName());
            //    updateInfraDO.setPatientId(infraObject.getPatientId());
            //    updateInfraDO.setRemark(infraObject.getRemark());
            //    String filePath = infraFile + infraObject.getId() + "/get/" + originalFilename;
            //    updateInfraDO.setFilePath(filePath);
            //    infraMapper.updateById(updateInfraDO);
            //    return new InfraResult(infraObject.getId(), infraObject.getPath(), infraObject.getFileName(), filePath);
            //}
            //上传文件到库
            InfraDO infraDO = new InfraDO();
            infraDO.setPath(path);
            infraDO.setFileName(originalFilename);
            infraDO.setContent(content);
            infraDO.setTableId(createReqVO.getTableId());
            infraDO.setTableName(createReqVO.getTableName());
            infraDO.setPatientId(createReqVO.getPatientId());
            infraDO.setRemark(createReqVO.getRemark());
            infraMapper.insert(infraDO);
            InfraDO updateInfraDO = new InfraDO();
            updateInfraDO.setId(infraDO.getId());
            updateInfraDO.setFilePath(infraFile + infraDO.getId() + "/get/" + originalFilename);
            infraMapper.updateById(updateInfraDO);
            infraDO.setFilePath(updateInfraDO.getFilePath());
            return new InfraResult(infraDO.getId(), infraDO.getPath(), infraDO.getFileName(), infraDO.getFilePath());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean save(List<InfraUpdateReqVO> reqVOS) {
        List<Long> infraIdList = reqVOS.stream().map(InfraUpdateReqVO::getId).collect(Collectors.toList());
        Map<Long, InfraDO> infraDOMap = infraMapper.selectList(InfraDO::getId, infraIdList).stream().collect(Collectors.toMap(InfraDO::getId, m -> m));
        ArrayList<InfraDO> updateList = new ArrayList<>();
        for (InfraUpdateReqVO reqVO : reqVOS) {
            InfraDO infraDO = infraDOMap.get(reqVO.getId());
            infraDO.setRemark(reqVO.getRemark());
            infraDO.setTableId(reqVO.getTableId());
            infraDO.setTableName(reqVO.getTableName());
            infraDO.setPatientId(reqVO.getPatientId());
            updateList.add(infraDO);
        }
        infraMapper.updateBatch(updateList);
        return true;
    }

    @Override
    public Boolean remove(List<Long> infraIds) {
        return infraMapper.deleteBatchIds(infraIds) > 0;
    }

    @Override
    public byte[] getFileContent(Long configId) {
        InfraDO infraDO = infraMapper.selectById(configId);
        return infraDO.getContent();
    }

    @Override
    public List<InfraRespVO> getInfraListByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) return null;
        return InfraConvert.INSTANCE.convertList(infraMapper.selectBatchIds(ids));
    }


    @Override
    public List<InfraRespVO> getInfraList(InfraDO infraDO) {
        return InfraConvert.INSTANCE.convertList(infraMapper.selectList(new QueryWrapper<>(infraDO)));
    }

    @Override
    public void updateInfra(InfraUpdateReqVO updateReqVO) {
        infraMapper.updateById(InfraConvert.INSTANCE.convert(updateReqVO));
    }
}
