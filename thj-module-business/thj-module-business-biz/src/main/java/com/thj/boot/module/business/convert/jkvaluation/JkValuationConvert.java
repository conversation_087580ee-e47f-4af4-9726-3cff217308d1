package com.thj.boot.module.business.convert.jkvaluation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkvaluation.JkValuationDO;
import com.thj.boot.module.business.pojo.jkvaluation.vo.JkValuationCreateReqVO;
import com.thj.boot.module.business.pojo.jkvaluation.vo.JkValuationRespVO;
import com.thj.boot.module.business.pojo.jkvaluation.vo.JkValuationUpdateReqVO;
import com.thj.boot.module.business.pojo.jkvaluation.vo.JkValuationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-教育测评 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkValuationConvert {

    JkValuationConvert INSTANCE = Mappers.getMapper(JkValuationConvert.class);

    JkValuationDO convert(JkValuationCreateReqVO bean);

    JkValuationDO convert(JkValuationUpdateReqVO bean);

    JkValuationRespVO convert(JkValuationDO bean);

    List<JkValuationRespVO> convertList(List<JkValuationDO> list);

    PageResult<JkValuationRespVO> convertPage(PageResult<JkValuationDO> page);

    List<JkValuationVO> convertList02(List<JkValuationDO> list);

}
