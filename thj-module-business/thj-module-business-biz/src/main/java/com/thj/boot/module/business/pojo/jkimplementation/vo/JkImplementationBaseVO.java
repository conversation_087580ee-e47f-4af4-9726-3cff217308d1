package com.thj.boot.module.business.pojo.jkimplementation.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
* 健康教育-患教实施 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class JkImplementationBaseVO extends BaseDO {
    private Long id;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析次数
     */
    private Integer dialyzeTotal;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 教育次数
     */
    private Integer cunt;
    /**
     * 今日排版
     */
    private String dayScheduling;
    /**
     * 透析机号
     */
    private String machineNum;
    /**
     *
     */
    private Long nurseId;
    /**
     *
     */
    private String nurseName;
    /**
     * 最后教育时间
     */
    private String lastTime;
    /**
     * 年月
     */
    private String createMouth;
    /**
     * 教育情况: 0 未教育,1已教育
     */
    private String state;
    /**
     * 微信绑定
     */
    private String binding;
    /**
     * 病区
     */
    private String ward;
    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 最新一次透析日期
     */
    private Date dialyzeTime;


}
