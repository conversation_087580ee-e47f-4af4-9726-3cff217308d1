package com.thj.boot.module.business.pojo.dialysisprotocol.vo;

import com.thj.boot.module.business.dal.datado.contradict.ContradictDO;
import com.thj.boot.module.business.dal.datado.contradictadvice.ContradictAdviceDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisProtocolCreateReqVO extends DialysisProtocolBaseVO {

    /**
     * 抗凝剂
     */
    private List<ContradictDO> contradictDOS;

    private List<ContradictAdviceDO> contradictAdviceDOS;
    /**
     * 透析日期
     */
    private Date classesTime;

}
