package com.thj.boot.module.business.service.conceal;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.conceal.ConcealDO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealCreateReqVO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealExportReqVO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealPageReqVO;
import com.thj.boot.module.business.pojo.conceal.vo.ConcealUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 隐私策略 Service 接口
 *
 * <AUTHOR>
 */
public interface ConcealService {

    /**
     * 创建隐私策略
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createConceal(ConcealCreateReqVO createReqVO);

    /**
     * 更新隐私策略
     *
     * @param updateReqVO 更新信息
     */
    void updateConceal(ConcealUpdateReqVO updateReqVO);

    /**
     * 删除隐私策略
     *
     * @param id 编号
     */
    void deleteConceal(Long id);

    /**
     * 获得隐私策略
     *
     * @param type 编号
     * @return 隐私策略
     */
    ConcealDO getConceal(String type);

    /**
     * 获得隐私策略列表
     *
     * @param ids 编号
     * @return 隐私策略列表
     */
    List<ConcealDO> getConcealList(Collection<Long> ids);

    /**
     * 获得隐私策略分页
     *
     * @param pageReqVO 分页查询
     * @return 隐私策略分页
     */
    PageResult<ConcealDO> getConcealPage(ConcealPageReqVO pageReqVO);

    /**
     * 获得隐私策略列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 隐私策略列表
     */
    List<ConcealDO> getConcealList(ConcealExportReqVO exportReqVO);

}
