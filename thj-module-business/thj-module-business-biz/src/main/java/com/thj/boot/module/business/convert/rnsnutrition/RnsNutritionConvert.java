package com.thj.boot.module.business.convert.rnsnutrition;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.rnsnutrition.RnsNutritionDO;
import com.thj.boot.module.business.pojo.rnsnutrition.vo.RnsNutritionCreateReqVO;
import com.thj.boot.module.business.pojo.rnsnutrition.vo.RnsNutritionRespVO;
import com.thj.boot.module.business.pojo.rnsnutrition.vo.RnsNutritionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 营养状况评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RnsNutritionConvert {

    RnsNutritionConvert INSTANCE = Mappers.getMapper(RnsNutritionConvert.class);

    RnsNutritionDO convert(RnsNutritionCreateReqVO bean);

    RnsNutritionDO convert(RnsNutritionUpdateReqVO bean);

    RnsNutritionRespVO convert(RnsNutritionDO bean);

    List<RnsNutritionRespVO> convertList(List<RnsNutritionDO> list);

    PageResult<RnsNutritionRespVO> convertPage(PageResult<RnsNutritionDO> page);


}
