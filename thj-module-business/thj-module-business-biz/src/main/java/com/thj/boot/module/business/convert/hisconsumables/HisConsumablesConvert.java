package com.thj.boot.module.business.convert.hisconsumables;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesCreateReqVO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesRespVO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中心耗材信息同步 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HisConsumablesConvert {

    HisConsumablesConvert INSTANCE = Mappers.getMapper(HisConsumablesConvert.class);

    HisConsumablesDO convert(HisConsumablesCreateReqVO bean);

    HisConsumablesDO convert(HisConsumablesUpdateReqVO bean);

    HisConsumablesRespVO convert(HisConsumablesDO bean);

    List<HisConsumablesRespVO> convertList(List<HisConsumablesDO> list);

    PageResult<HisConsumablesRespVO> convertPage(PageResult<HisConsumablesDO> page);


}
