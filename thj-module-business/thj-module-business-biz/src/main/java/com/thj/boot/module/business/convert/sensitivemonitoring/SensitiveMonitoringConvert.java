package com.thj.boot.module.business.convert.sensitivemonitoring;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.sensitivemonitoring.SensitiveMonitoringDO;
import com.thj.boot.module.business.pojo.sensitivemonitoring.vo.SensitiveMonitoringCreateReqVO;
import com.thj.boot.module.business.pojo.sensitivemonitoring.vo.SensitiveMonitoringRespVO;
import com.thj.boot.module.business.pojo.sensitivemonitoring.vo.SensitiveMonitoringUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 敏感指标监测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SensitiveMonitoringConvert {

    SensitiveMonitoringConvert INSTANCE = Mappers.getMapper(SensitiveMonitoringConvert.class);

    SensitiveMonitoringDO convert(SensitiveMonitoringCreateReqVO bean);

    SensitiveMonitoringDO convert(SensitiveMonitoringUpdateReqVO bean);

    SensitiveMonitoringRespVO convert(SensitiveMonitoringDO bean);

    List<SensitiveMonitoringRespVO> convertList(List<SensitiveMonitoringDO> list);

    PageResult<SensitiveMonitoringRespVO> convertPage(PageResult<SensitiveMonitoringDO> page);


}
