package com.thj.boot.module.business.pojo.hisconsumables.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HisConsumablesCreateReqVO extends HisConsumablesBaseVO {

    private Long consumId;
    /**
     * 耗材编码
     */
    private String consumNumber;
    /**
     * 耗材名称
     */
    private String consumName;
    /**
     * 医保收费等级
     */
    private String drugDirectoryType;
    /**
     * 医保编码
     */
    private String medicalinsCode;
    /**
     * 耗材规格
     */
    private String consumSpec;
    /**
     *
     耗材类型
     */
    private String consumType;
    /**
     * 零售价
     */
    private String drugPrice;
    /**
     * 耗材单位
     */
    private String unit;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     *
     生产厂家
     */
    private String manufacturer;
    /**
     *
     透析中心ID
     */
    private String hosipital;
    /**
     *
     有效标志
     */
    private String enabledMark;
    /**
     *
     有效名称
     */
    private String enabledName;
    /**
     * 审核标志
     */
    private String isAudit;
    /**
     *
     政府指导价/采购价
     */
    private String purchasePrice;
    /**
     *
     审核时间
     */
    private String auditTime;
    /**
     * 审核零售价
     */
    private String auditPrice;
    /**
     * 审核医保编码
     */
    private String auditMedinsCode;
    /**
     * 库存单位
     */
    private String stockUnit;
    /**
     * 限制使用标志
     */
    private String limitUseSign;
    /**
     * 限制使用范围
     */
    private String limitUseScope;
    /**
     *
     */
    private Long deptId;

}
