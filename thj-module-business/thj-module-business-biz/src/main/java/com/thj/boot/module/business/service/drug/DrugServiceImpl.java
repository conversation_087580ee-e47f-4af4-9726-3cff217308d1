package com.thj.boot.module.business.service.drug;

import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.drug.DrugConvert;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.dal.mapper.drug.DrugMapper;
import com.thj.boot.module.business.pojo.drug.vo.DrugBaseVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugCreateReqVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugPageReqVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 药品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DrugServiceImpl implements DrugService {

    @Resource
    private DrugMapper drugMapper;

    @Override
    public Long createDrug(DrugCreateReqVO createReqVO) {
        //检测物品编号和药品名称唯一性
        checkDrugCodeByName(createReqVO, 0);
        // 插入
        DrugDO drug = DrugConvert.INSTANCE.convert(createReqVO);
        drugMapper.insert(drug);
        // 返回
        return drug.getId();
    }

    private void checkDrugCodeByName(DrugBaseVO baseVO, Integer i) {
        Long count = drugMapper.selectCount(DrugDO::getItemCode, baseVO.getItemCode(), DrugDO::getName, baseVO.getName());
        if (count > 0 && 0 == i) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        } else if (count > 1) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        }
    }

    @Override
    public void updateDrug(DrugUpdateReqVO updateReqVO) {
        checkDrugCodeByName(updateReqVO, 1);
        // 更新
        DrugDO updateObj = DrugConvert.INSTANCE.convert(updateReqVO);
        drugMapper.updateById(updateObj);
    }

    @Override
    public void deleteDrug(Long id) {
        // 删除
        drugMapper.deleteById(id);
    }


    @Override
    public DrugDO getDrug(Long id) {
        return drugMapper.selectById(id);
    }

    @Override
    public List<DrugDO> getDrugList(Collection<Long> ids) {
        return drugMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DrugDO> getDrugPage(DrugPageReqVO pageReqVO) {
        return drugMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DrugDO> getDrugList(DrugCreateReqVO createReqVO) {
        return drugMapper.selectList(createReqVO);
    }

}
