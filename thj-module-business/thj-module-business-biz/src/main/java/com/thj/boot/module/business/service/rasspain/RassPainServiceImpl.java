package com.thj.boot.module.business.service.rasspain;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.rasspain.RassPainConvert;
import com.thj.boot.module.business.dal.datado.rasspain.RassPainDO;
import com.thj.boot.module.business.dal.mapper.rasspain.RassPainMapper;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainCreateReqVO;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainPageReqVO;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * RASS评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RassPainServiceImpl implements RassPainService {

    @Resource
    private RassPainMapper rassPainMapper;

    @Override
    public Long createRassPain(RassPainCreateReqVO createReqVO) {
        // 插入
        RassPainDO rassPain = RassPainConvert.INSTANCE.convert(createReqVO);
        rassPainMapper.insert(rassPain);
        // 返回
        return rassPain.getId();
    }

    @Override
    public void updateRassPain(RassPainUpdateReqVO updateReqVO) {
        // 更新
        RassPainDO updateObj = RassPainConvert.INSTANCE.convert(updateReqVO);
        rassPainMapper.updateById(updateObj);
    }

    @Override
    public void deleteRassPain(Long id) {
        // 删除
        rassPainMapper.deleteById(id);
    }


    @Override
    public RassPainDO getRassPain(Long id) {
        return rassPainMapper.selectById(id);
    }

    @Override
    public List<RassPainDO> getRassPainList(Collection<Long> ids) {
        return rassPainMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<RassPainDO> getRassPainPage(RassPainPageReqVO pageReqVO) {
        return rassPainMapper.selectPage(pageReqVO);
    }

    @Override
    public List<RassPainDO> getRassPainList(RassPainCreateReqVO createReqVO) {
        return rassPainMapper.selectList(createReqVO);
    }

}
