package com.thj.boot.module.business.convert.dialysisadvice;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysisadvice.vo.DialysisAdviceExcelVO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceRespVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析管理-医嘱 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisAdviceConvert {

    DialysisAdviceConvert INSTANCE = Mappers.getMapper(DialysisAdviceConvert.class);

    DialysisAdviceDO convert(DialysisAdviceCreateReqVO bean);

    DialysisAdviceDO convert(DialysisAdviceUpdateReqVO bean);

    DialysisAdviceRespVO convert(DialysisAdviceDO bean);

    List<DialysisAdviceRespVO> convertList(List<DialysisAdviceDO> list);

    List<DialysisAdviceDO> convertList2(List<DialysisAdviceCreateReqVO> createReqVOS);


    PageResult<DialysisAdviceRespVO> convertPage(PageResult<DialysisAdviceDO> page);


    List<DialysisAdviceExcelVO> convertList3(List<DialysisAdviceRespVO> adviceStatisticsList);
}
