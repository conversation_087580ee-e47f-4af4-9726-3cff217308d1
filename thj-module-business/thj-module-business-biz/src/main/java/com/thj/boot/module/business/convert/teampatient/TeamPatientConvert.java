package com.thj.boot.module.business.convert.teampatient;


import com.thj.boot.module.business.controller.admin.drainteam.vo.DrainTeamTotalWeekVO;
import com.thj.boot.module.business.dal.datado.teampatient.TeamPatientDO;
import com.thj.boot.module.business.pojo.teampatient.TeamPatientRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * 位点标记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TeamPatientConvert {

    TeamPatientConvert INSTANCE = Mappers.getMapper(TeamPatientConvert.class);


    List<TeamPatientDO> convertList(List<DrainTeamTotalWeekVO> drainTeamTotalWeekVOS);

    List<TeamPatientRespVO> convertList2(List<TeamPatientDO> teamPatientDOS);

    Set<TeamPatientRespVO> convertset(Set<TeamPatientDO> teamPatientDOSet);

    List<com.thj.boot.module.business.controller.admin.dialysismanager.vo.TeamPatientRespVO> convertList3(List<TeamPatientDO> teamPatientDOS);

    TeamPatientRespVO convert(TeamPatientDO teamPatientDO);
}
