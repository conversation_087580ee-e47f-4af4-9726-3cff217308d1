package com.thj.boot.module.business.pojo.dialysisprotocol.vo;

import com.thj.boot.module.business.dal.datado.contradictadvice.ContradictAdviceDO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisProtocolRespVO extends DialysisProtocolBaseVO {

    /**
     * 抗凝剂类型
     */
    private List<ContradictRespVO> contradictDOS;
    /**
     * 抗凝剂医嘱名称
     */
    private List<ContradictAdviceDO> contradictAdviceDOS;
}
