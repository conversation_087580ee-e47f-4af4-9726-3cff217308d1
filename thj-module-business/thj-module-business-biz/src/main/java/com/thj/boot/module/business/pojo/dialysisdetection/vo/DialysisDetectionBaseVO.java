package com.thj.boot.module.business.pojo.dialysisdetection.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
 * 透析检测 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DialysisDetectionBaseVO extends BaseDO {

    /**
     * 透析检测主键id
     */
    private Long id;
    /**
     * 检测记录日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date detectionTime;

    /**
     * 检测记录时间
     */
    @JsonFormat(pattern = "HH:mm")
//    @DateTimeFormat(pattern = "HH:mm") //入参
//    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm") //出参
    private Date detectionMin;
    /**
     * 脉搏
     */
    private String pulse;
    /**
     * 呼吸
     */
    private String breathe;
    /**
     * 血压状态1-上肢，2-下肢，3-拒测，4-测不出，5-斜杠
     */
    private String bloodState;
    /**
     * 血压状态文本框1
     */
    private String bloodOne;
    /**
     * 血压状态文本框2
     */
    private String bloodTwo;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 动脉压
     */
    private String arterialPressure;
    /**
     * 静脉压
     */
    private String venousPressure;
    /**
     * 跨膜压
     */
    private String transmembranePressure;
    /**
     * 超滤率
     */
    private String ultrafiltrationRate;
    /**
     * 超滤量
     */
    private String ultrafiltrationCapacity;
    /**
     * 钠浓度
     */
    private String sodiumConcentration;
    /**
     * 电导度
     */
    private String conductance;
    /**
     * 透析液温度
     */
    private String dialysateTemperature;
    /**
     * 置换率
     */
    private String replacementRate;
    /**
     * 置换量
     */
    private String displacementQuantity;
    /**
     * KT/V(在线)
     */
    private String ktv;
    /**
     * 血容量(ml)
     */
    private String bloodVolume;
    /**
     * 在线尿素
     */
    private String lineUrea;
    /**
     * 症状
     */
    private String symptom;
    /**
     * 处理
     */
    private String dispose;
    /**
     * 结果
     */
    private String results;
    /**
     * 监测护士
     */
    private Long userId;
    /**
     * 体温(℃)
     */
    private String bodyTemperature;
    /**
     * SpO2(%)
     */
    private String spo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 透析日期
     */
    private Date dateWeek;
    /**
     * 0-开始透析，1-结束透析
     */
    private Integer dialyzeState;

}
