package com.thj.boot.module.business.convert.healthcareregistration;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.healthcareregistration.HealthcareRegistrationDO;
import com.thj.boot.module.business.pojo.healthcareregistration.vo.HealthcareRegistrationCreateReqVO;
import com.thj.boot.module.business.pojo.healthcareregistration.vo.HealthcareRegistrationRespVO;
import com.thj.boot.module.business.pojo.healthcareregistration.vo.HealthcareRegistrationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医护人员筛查登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HealthcareRegistrationConvert {

    HealthcareRegistrationConvert INSTANCE = Mappers.getMapper(HealthcareRegistrationConvert.class);

    HealthcareRegistrationDO convert(HealthcareRegistrationCreateReqVO bean);

    HealthcareRegistrationDO convert(HealthcareRegistrationUpdateReqVO bean);

    HealthcareRegistrationRespVO convert(HealthcareRegistrationDO bean);

    List<HealthcareRegistrationRespVO> convertList(List<HealthcareRegistrationDO> list);

    PageResult<HealthcareRegistrationRespVO> convertPage(PageResult<HealthcareRegistrationDO> page);


}
