package com.thj.boot.module.business.pojo.dialysisadvice.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisAdviceCreateReqVO extends DialysisAdviceBaseVO {

    /**
     * 多个医嘱
     */
    private List<DialysisAdviceCreateReqVO> dialysisAdviceCreateReqVOS;

    /**
     * 子药
     */
    private List<DialysisAdviceCreateReqVO> children;

    /**
     * 血液透析左侧患者信息
     */
    private String tableContentInfo;

    /**
     * 续开标识符
     */
    private String continuation;
    /**
     * 透析管理医嘱时间
     */
    private Date dateWeek;
    /**
     * 多个患者id
     */
    private String patientIds;
    /**
     * 多个医嘱id
     */
    private String adviceIds;
    /**
     * 用药推送，临时医嘱开始时间
     */
    private String brevityTime;

    private Date[] dateList;

    private List<Long> dialysisIdList;

}
