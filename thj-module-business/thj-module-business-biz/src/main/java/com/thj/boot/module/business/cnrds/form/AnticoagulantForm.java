package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 抗凝剂表单
 */
@Data
public class AnticoagulantForm {

    /**
     * 年份
     */
    @NotNull
    @JsonProperty("ANTICO_YEAR")
    private String year;

    /**
     * 时间维度
     */
    @NotNull
    @JsonProperty("ANTICO_DIMENSION")
    private String dimension;

    /**
     * 有无变化
     */
    @NotNull
    @JsonProperty("ANTICO_CHANGE_YN")
    private String changeYn;

    /**
     * 抗凝剂
     */
    @NotNull
    @JsonProperty("ANTICO_AGENT")
    private String agent;

    /**
     * 是否首次
     */
    @JsonProperty("ANTICO_FIRST_YN")
    private Boolean firstYn;

    /**
     * 普通肝素对象
     */
    @JsonProperty("ANTICO_HEPARIN")
    private AnticoHeparin heparin;

    /**
     * 低分子肝素对象
     */
    @JsonProperty("ANTICO_LMWH")
    private AnticoLmwh lmwh;

    /**
     * 枸橼酸钠对象
     */
    @JsonProperty("ANTICO_CITRATE")
    private AnticoCitrate citrate;

    /**
     * 阿加曲班对象
     */
    @JsonProperty("ANTICO_ARGATRO")
    private AnticoArgatro argatro;



}
