package com.thj.boot.module.business.convert.jkestimate;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkestimate.JkEstimateDO;
import com.thj.boot.module.business.pojo.jkestimate.vo.JkEstimateCreateReqVO;
import com.thj.boot.module.business.pojo.jkestimate.vo.JkEstimateRespVO;
import com.thj.boot.module.business.pojo.jkestimate.vo.JkEstimateUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-健康教育评估信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkEstimateConvert {

    JkEstimateConvert INSTANCE = Mappers.getMapper(JkEstimateConvert.class);

    JkEstimateDO convert(JkEstimateCreateReqVO bean);

    JkEstimateDO convert(JkEstimateUpdateReqVO bean);

    JkEstimateRespVO convert(JkEstimateDO bean);

    List<JkEstimateRespVO> convertList(List<JkEstimateDO> list);

    PageResult<JkEstimateRespVO> convertPage(PageResult<JkEstimateDO> page);


}
