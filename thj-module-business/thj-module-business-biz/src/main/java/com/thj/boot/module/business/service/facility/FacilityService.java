package com.thj.boot.module.business.service.facility;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.facility.vo.FacilityExcelVO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityCreateReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityPageReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 设备-机号设置 Service 接口
 *
 * <AUTHOR>
 */
public interface FacilityService {

    /**
     * 创建设备-机号设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFacility( FacilityCreateReqVO createReqVO);

    /**
     * 更新设备-机号设置
     *
     * @param updateReqVO 更新信息
     */
    void updateFacility( FacilityUpdateReqVO updateReqVO);

    /**
     * 删除设备-机号设置
     *
     * @param id 编号
     */
    void deleteFacility(Long id);

    /**
     * 获得设备-机号设置
     *
     * @param id 编号
     * @return 设备-机号设置
     */
    FacilityDO getFacility(Long id);

    /**
     * 获得设备-机号设置列表
     *
     * @param ids 编号
     * @return 设备-机号设置列表
     */
    List<FacilityDO> getFacilityList(Collection<Long> ids);

    /**
     * 获得设备-机号设置分页
     *
     * @param pageReqVO 分页查询
     * @return 设备-机号设置分页
     */
    PageResult<FacilityDO> getFacilityPage(FacilityPageReqVO pageReqVO);

    /**
     * 获得设备-机号设置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 设备-机号设置列表
     */
    List<FacilityDO> getFacilityList(FacilityCreateReqVO createReqVO);

    void importFacility(List<FacilityExcelVO> facilityExcelVOS);
}
