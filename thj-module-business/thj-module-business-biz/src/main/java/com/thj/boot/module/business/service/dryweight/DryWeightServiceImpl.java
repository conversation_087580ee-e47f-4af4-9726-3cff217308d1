package com.thj.boot.module.business.service.dryweight;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.dryweight.DryWeightConvert;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.reachweigh.ReachWeighMapper;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightCreateReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightPageReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightRespVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 干体重 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DryWeightServiceImpl implements DryWeightService {

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private ReachWeighMapper reachWeighMapper;

    @Override
    public Long createDryWeight(DryWeightCreateReqVO createReqVO) {
        // 插入
        DryWeightDO dryWeight = DryWeightConvert.INSTANCE.convert(createReqVO);
        dryWeightMapper.insert(dryWeight);
        // 返回
        return dryWeight.getId();
    }

    @Override
    public void updateDryWeight(DryWeightUpdateReqVO updateReqVO) {
        // 更新
        DryWeightDO updateObj = DryWeightConvert.INSTANCE.convert(updateReqVO);
        dryWeightMapper.updateById(updateObj);
    }

    @Override
    public void deleteDryWeight(Long id) {
        // 删除
        dryWeightMapper.deleteById(id);
    }


    @Override
    public DryWeightDO getDryWeight(Long id) {
        return dryWeightMapper.selectById(id);
    }

    @Override
    public List<DryWeightDO> getDryWeightList(Collection<Long> ids) {
        return dryWeightMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DryWeightRespVO> getDryWeightPage(DryWeightPageReqVO pageReqVO) {
        PageResult<DryWeightDO> dryWeightDOPageResult = dryWeightMapper.selectPage(pageReqVO);
        PageResult<DryWeightRespVO> dryWeightRespVOPageResult = DryWeightConvert.INSTANCE.convertPage(dryWeightDOPageResult);
        List<DryWeightDO> dryWeightDOS = dryWeightMapper.selectList(DryWeightDO::getPatientId, pageReqVO.getPatientId());
        if (CollectionUtil.isNotEmpty(dryWeightDOS)) {
            DryWeightDO dryWeightDO = dryWeightDOS.stream().findFirst().get();
            if (CollectionUtil.isNotEmpty(dryWeightRespVOPageResult.getList())) {
                List<DryWeightRespVO> collect = dryWeightRespVOPageResult.getList().stream().peek(dryWeightRespVO -> {
                    if (dryWeightDO.getId() == dryWeightRespVO.getId()) {
                        dryWeightRespVO.setAdjustValue(null);
                    }
                }).collect(Collectors.toList());
                List<DryWeightRespVO> list = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DryWeightRespVO dryWeightRespVO = null;
                    if (i == collect.size() - 1) {
                        dryWeightRespVO = collect.get(i);
                        dryWeightRespVO.setAdjustValue("0");
                        list.add(dryWeightRespVO);
                    } else {
                        dryWeightRespVO = collect.get(i);
                        BigDecimal subtract =
                                new BigDecimal(isNumeric(dryWeightRespVO.getDryWeight()) ? dryWeightRespVO.getDryWeight() : "0").subtract(new BigDecimal(isNumeric(collect.get(i + 1).getDryWeight()) ? collect.get(i + 1).getDryWeight() : "0"));
                        dryWeightRespVO.setAdjustValue(subtract.toString());
                        list.add(dryWeightRespVO);
                    }
                }
                dryWeightRespVOPageResult.setList(list);
            }
        }
        return dryWeightRespVOPageResult;
    }

    @Override
    public List<DryWeightDO> getDryWeightList(DryWeightCreateReqVO createReqVO) {
        return dryWeightMapper.selectList(createReqVO);
    }


    public static Boolean isNumeric(String str) {
        if (Double.valueOf(str) < 0) {
            return false;
        } else {
            return true;
        }
        //?:0或1个, *:0或多个, +:1或多个
        //Pattern pattern = Pattern.compile("^(\\-|\\+)?\\d+(\\.\\d+)?$");
        //return pattern.matcher(str).matches();
    }


}
