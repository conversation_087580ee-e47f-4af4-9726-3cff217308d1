package com.thj.boot.module.business.convert.tumble;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.tumble.TumbleDO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleCreateReqVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleRespVO;
import com.thj.boot.module.business.pojo.tumble.vo.TumbleUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 跌倒评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TumbleConvert {

    TumbleConvert INSTANCE = Mappers.getMapper(TumbleConvert.class);

    TumbleDO convert(TumbleCreateReqVO bean);

    TumbleDO convert(TumbleUpdateReqVO bean);

    TumbleRespVO convert(TumbleDO bean);

    List<TumbleRespVO> convertList(List<TumbleDO> list);

    PageResult<TumbleRespVO> convertPage(PageResult<TumbleDO> page);


}
