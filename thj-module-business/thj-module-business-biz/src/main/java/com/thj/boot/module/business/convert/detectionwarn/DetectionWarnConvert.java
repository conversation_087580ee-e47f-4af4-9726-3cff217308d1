package com.thj.boot.module.business.convert.detectionwarn;


import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.DialysisDetectionWarnVO;
import com.thj.boot.module.business.dal.datado.detectionwarn.DetectionWarnDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 透析管理-医嘱 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DetectionWarnConvert {

    DetectionWarnConvert INSTANCE = Mappers.getMapper(DetectionWarnConvert.class);


    DetectionWarnDO convert(DialysisDetectionWarnVO warnVO);
}
