package com.thj.boot.module.business.convert.reason;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.reason.ReasonDO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonCreateReqVO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonRespVO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 诊断类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ReasonConvert {

    ReasonConvert INSTANCE = Mappers.getMapper(ReasonConvert.class);

    ReasonDO convert(ReasonCreateReqVO bean);

    ReasonDO convert(ReasonUpdateReqVO bean);

    ReasonRespVO convert(ReasonDO bean);

    List<ReasonRespVO> convertList(List<ReasonDO> list);

    PageResult<ReasonRespVO> convertPage(PageResult<ReasonDO> page);


}
