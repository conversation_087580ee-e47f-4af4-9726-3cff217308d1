package com.thj.boot.module.business.service.puncture;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.puncture.PunctureDO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureCreateReqVO;
import com.thj.boot.module.business.pojo.puncture.vo.PuncturePageReqVO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 穿刺信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PunctureService {

    /**
     * 创建穿刺信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPuncture( PunctureCreateReqVO createReqVO);

    /**
     * 更新穿刺信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePuncture( PunctureUpdateReqVO updateReqVO);

    /**
     * 删除穿刺信息
     *
     * @param id 编号
     */
    void deletePuncture(Long id);

    /**
     * 获得穿刺信息
     *
     * @param id 编号
     * @return 穿刺信息
     */
    PunctureDO getPuncture(Long id);

    /**
     * 获得穿刺信息列表
     *
     * @param ids 编号
     * @return 穿刺信息列表
     */
    List<PunctureDO> getPunctureList(Collection<Long> ids);

    /**
     * 获得穿刺信息分页
     *
     * @param pageReqVO 分页查询
     * @return 穿刺信息分页
     */
    PageResult<PunctureDO> getPuncturePage(PuncturePageReqVO pageReqVO);

    /**
     * 获得穿刺信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 穿刺信息列表
     */
    List<PunctureDO> getPunctureList(PunctureCreateReqVO createReqVO);

}
