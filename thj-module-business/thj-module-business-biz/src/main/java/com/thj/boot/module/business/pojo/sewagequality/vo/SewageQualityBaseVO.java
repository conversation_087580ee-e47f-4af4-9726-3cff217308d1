package com.thj.boot.module.business.pojo.sewagequality.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
* 污水质量检测 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SewageQualityBaseVO extends BaseDO {

    /**
     * 污水质量检测主键id
     */
    private Long id;
    /**
     * 登记日期
     */
    private Date startTime;
    /**
     * 类大肠菌
     */
    private String coliform;
    /**
     * cod
     */
    private String cod;
    /**
     * bod
     */
    private String bod;
    /**
     * ss
     */
    private String ss;
    /**
     * 沙门氏菌
     */
    private String salmonella;
    /**
     * 志贺氏菌
     */
    private String shigella;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 设备管理id
     */
    private Long managerId;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 备注
     */
    private String remark;
}
