package com.thj.boot.module.business.convert.others;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.others.OthersDO;
import com.thj.boot.module.business.pojo.others.vo.OthersCreateReqVO;
import com.thj.boot.module.business.pojo.others.vo.OthersRespVO;
import com.thj.boot.module.business.pojo.others.vo.OthersUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 其他-维修登记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OthersConvert {

    OthersConvert INSTANCE = Mappers.getMapper(OthersConvert.class);

    OthersDO convert(OthersCreateReqVO bean);

    OthersDO convert(OthersUpdateReqVO bean);

    OthersRespVO convert(OthersDO bean);

    List<OthersRespVO> convertList(List<OthersDO> list);

    PageResult<OthersRespVO> convertPage(PageResult<OthersDO> page);


}
