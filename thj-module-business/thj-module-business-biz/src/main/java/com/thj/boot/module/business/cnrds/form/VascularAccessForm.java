package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 血管通路表单
 */
@Data
public class VascularAccessForm {

    /**
     * 年
     */
    @NotNull
    @JsonProperty("ACC_YEAR")
    private String year;

    /**
     * 时间维度
     */
    @NotNull
    @JsonProperty("ACC_DIMENSION")
    private String dimension;

    /**
     * 血管通路有无改变
     */
    @NotNull
    @JsonProperty("ACC_CHANGE_YN")
    private String changeYn;

    /**
     * 血管通路类型
     */
    @NotNull
    @JsonProperty("ACC_TYPE")
    private String type;

    /**
     * 使用时间 Date(YYYY-MM-DD)
     * @return
     */
    @NotNull
    @JsonProperty("ACC_INITIAL_DATE")
    private String initialDate;

    /**
     *  血管通路位置(左-右)
     */
    @JsonProperty("ACC_POSITION_LR")
    private String positionLr;

    /**
     *  血管通路位置_静脉置管
     */
    @JsonProperty("ACC_POSITION_CATHETER")
    private String positionCatheter;

    /**
     * 血管通路位置_内瘘
     */
    @JsonProperty("ACC_POSITION_FISTULA")
    private String positionFistula;

    /**
     * 是否为首次
     */
    @JsonProperty("ACC_FIRST_YN")
    private Boolean firstYn;

}
