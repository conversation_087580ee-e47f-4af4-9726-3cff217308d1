package com.thj.boot.module.business.pojo.hemodialysismanager.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HemodialysisManagerPageReqVO extends PageParam {

        /**
         * 透析时间
         */
        private Date hemodialysisTime;
        /**
         * type=1透析耗材，上机准备，2-透前准备
         */
        private Integer type;
        /**
         * 时间段
         */
        private List<String> dayStateList;
        /**
         * 病区
         */
        private List<Long> facilitySubareaIdList;

        /**
         * 部门id
         */
        private Long deptId;

        /**
         * 开始日期
         */
        private Date startDate;

        /**
         * 结束日期
         */
        private Date endDate;

        /**
         * 搜索患者调教
         */
        private String more;


}
