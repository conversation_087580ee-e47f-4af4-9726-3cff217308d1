package com.thj.boot.module.business.pojo.ohpressure.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
* OH压疮评估 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class OhPressureBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * OH压疮评估表
     */
    private String posturalChange;
    /**
     * 备注
     */
    private String remark;

    /**
     * 评估json
     */
    private String evaluateContent;

}
