package com.thj.boot.module.business.pojo.medicationpreparation.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicationPreparationPageReqVO extends PageParam {

    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 戴口罩（对/错）
     */
    private String wearingMask;
    /**
     * 在治疗室准备药物（对/错）
     */
    private String treatmentRoom;
    /**
     * 准备区域洁净（对/错）
     */
    private String cleanPreparationArea;
    /**
     * 检查无菌用物及药品（对/错）
     */
    private String checkingMedications;
    /**
     * 正确行手卫生（对/错）
     */
    private String correctHandHygiene;
    /**
     * 药瓶抽药部位消毒（对/错）
     */
    private String extractionSite;
    /**
     * 使用新注射器和针头（对/错）
     */
    private String usingNeedle;
    /**
     * 操作过程无菌（对/错）
     */
    private String asepticProcess;
    /**
     * 所有一次性用物丢弃（对/错）
     */
    private String disposingAll;
    /**
     * 多剂量药品合理放置（对/错）
     */
    private String properMedications;
    /**
     * 每次取药必须使用新注射器和针头（对/错）
     */
    private String useNewExtraction;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTime;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;

}
