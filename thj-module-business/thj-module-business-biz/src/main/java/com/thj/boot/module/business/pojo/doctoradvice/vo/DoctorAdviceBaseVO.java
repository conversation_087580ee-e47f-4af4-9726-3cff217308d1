package com.thj.boot.module.business.pojo.doctoradvice.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

import java.util.Date;

/**
* 医嘱 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DoctorAdviceBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 医嘱类型(0-临时，1-长期，字典获取)
     */
    private String type;
    /**
     * 0-未停止，1-停止（字典获取）
     */
    private String over;
    /**
     * 给药途径(字典/his获取)
     */
    private String way;
    /**
     * 医嘱时间
     */
    private Date adviceTime;
    /**
     * 医嘱信息（his系统获取）
     */
    private String hisAdviceId;
    /**
     * 单次用量
     */
    private String onceNum;
    /**
     * 开药数量
     */
    private String prescribeNum;
    /**
     * 执行频率（字典/his获取）
     */
    private String frequency;
    /**
     * 开嘱医生
     */
    private Long userId;
    /**
     * 停嘱医生
     */
    private Long stopUserId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 停止时间
     */
    private Date overTime;
    /**
     * 停止原因
     */
    private String overDescribe;
    /**
     * 执行时间
     */
    private Date carryTime;
    /**
     * 执行人员
     */
    private String carryMan;
    /**
     * 核对人员
     */
    private String auditMan;
    /**
     * 开始时间
     */
    private Date startTime;

}
