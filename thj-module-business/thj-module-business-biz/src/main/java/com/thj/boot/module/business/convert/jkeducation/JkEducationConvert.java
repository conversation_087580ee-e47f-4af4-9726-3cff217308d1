package com.thj.boot.module.business.convert.jkeducation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkeducation.JkEducationDO;
import com.thj.boot.module.business.pojo.jkeducation.vo.JkEducationCreateReqVO;
import com.thj.boot.module.business.pojo.jkeducation.vo.JkEducationRespVO;
import com.thj.boot.module.business.pojo.jkeducation.vo.JkEducationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-患者健康教育 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkEducationConvert {

    JkEducationConvert INSTANCE = Mappers.getMapper(JkEducationConvert.class);

    JkEducationDO convert(JkEducationCreateReqVO bean);

    JkEducationDO convert(JkEducationUpdateReqVO bean);

    JkEducationRespVO convert(JkEducationDO bean);

    List<JkEducationRespVO> convertList(List<JkEducationDO> list);

    PageResult<JkEducationRespVO> convertPage(PageResult<JkEducationDO> page);


}
