package com.thj.boot.module.business.pojo.hemodialysismonitoring.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HemodialysisMonitoringCreateReqVO extends HemodialysisMonitoringBaseVO {

    /**
     * 序号
     */
    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查人
     */
    private String inspectedPerson;
    /**
     * 血管通路
     */
    private String vascularAccess;
    /**
     * 静脉使用抗生素（必填，对/错）
     */
    private String antibiotics;
    /**
     * 血培养阳性（必填，对/错）
     */
    private String positiveBloodCulture;
    /**
     * 伤口有脓液/红肿（必填，对/错）
     */
    private String purulentDischarge;
    /**
     * T≥38℃（必填，对/错）
     */
    private String celsius;
    /**
     * 寒颤高热（必填，对/错）
     */
    private String rigorsHighFever;
    /**
     * 血压下降（必填，对/错）
     */
    private String bloodPressureDrop;
    /**
     * 蜂窝组织炎（必填，对/错）
     */
    private String cellulitis;
    /**
     * 肺炎/呼吸道感染（必填，对/错）
     */
    private String pneumoniaRespiratoryInfection;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 最近一次操作人
     */
    private String lastOperator;
    /**
     * 评估人
     */
    private String evaluator;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;
    /**
     * 评估人id
     */
    private Long evaluatorId;

}
