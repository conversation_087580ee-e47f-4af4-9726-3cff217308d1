package com.thj.boot.module.business.pojo.tumble.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
 * 跌倒评估 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TumbleBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 危险评估
     */
    private String undergo;
    /**
     * 预防措施
     */
    private String takeTumble;
    /**
     * 预防效果
     */
    private String obstacles;
    /**
     * 透析间期跌倒事件
     */
    private String epilepsy;
    /**
     * 备注
     */
    private String remark;
    /**
     * 评估时间
     */
    private String evaluateContent;

}
