package com.thj.boot.module.business.pojo.outcomerecord.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutcomeRecordRespVO extends OutcomeRecordBaseVO {

    /**
     * 第一条标识符
     */
    private Integer firstNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sort;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String diadialyzeAge;

    private String age;
}
