package com.thj.boot.module.business.convert.facilitymanager;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerCreateReqVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 设备日常管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilityManagerConvert {

    FacilityManagerConvert INSTANCE = Mappers.getMapper(FacilityManagerConvert.class);

    FacilityManagerDO convert(FacilityManagerCreateReqVO bean);

    FacilityManagerDO convert(FacilityManagerUpdateReqVO bean);

    FacilityManagerRespVO convert(FacilityManagerDO bean);

    List<FacilityManagerRespVO> convertList(List<FacilityManagerDO> list);

    PageResult<FacilityManagerRespVO> convertPage(PageResult<FacilityManagerDO> page);


}
