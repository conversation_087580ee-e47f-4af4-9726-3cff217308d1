package com.thj.boot.module.business.convert.pruritus;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.pruritus.PruritusDO;
import com.thj.boot.module.business.pojo.pruritus.vo.PruritusCreateReqVO;
import com.thj.boot.module.business.pojo.pruritus.vo.PruritusRespVO;
import com.thj.boot.module.business.pojo.pruritus.vo.PruritusUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 瘙痒评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PruritusConvert {

    PruritusConvert INSTANCE = Mappers.getMapper(PruritusConvert.class);

    PruritusDO convert(PruritusCreateReqVO bean);

    PruritusDO convert(PruritusUpdateReqVO bean);

    PruritusRespVO convert(PruritusDO bean);

    List<PruritusRespVO> convertList(List<PruritusDO> list);

    PageResult<PruritusRespVO> convertPage(PageResult<PruritusDO> page);


}
