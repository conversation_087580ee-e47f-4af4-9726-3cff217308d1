package com.thj.boot.module.business.convert.bloodpulsebody;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.bloodpulsebody.BloodPulseBodyDO;
import com.thj.boot.module.business.pojo.bloodpulsebody.vo.BloodPulseBodyCreateReqVO;
import com.thj.boot.module.business.pojo.bloodpulsebody.vo.BloodPulseBodyRespVO;
import com.thj.boot.module.business.pojo.bloodpulsebody.vo.BloodPulseBodyUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血压/脉搏/体温 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BloodPulseBodyConvert {

    BloodPulseBodyConvert INSTANCE = Mappers.getMapper(BloodPulseBodyConvert.class);

    BloodPulseBodyDO convert(BloodPulseBodyCreateReqVO bean);

    BloodPulseBodyDO convert(BloodPulseBodyUpdateReqVO bean);

    BloodPulseBodyRespVO convert(BloodPulseBodyDO bean);

    List<BloodPulseBodyRespVO> convertList(List<BloodPulseBodyDO> list);

    PageResult<BloodPulseBodyRespVO> convertPage(PageResult<BloodPulseBodyDO> page);


}
