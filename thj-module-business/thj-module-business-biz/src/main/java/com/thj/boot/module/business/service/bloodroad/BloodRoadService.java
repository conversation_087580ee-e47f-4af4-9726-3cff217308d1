package com.thj.boot.module.business.service.bloodroad;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadCreateReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadPageReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadRespVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 血管通路 Service 接口
 *
 * <AUTHOR>
 */
public interface BloodRoadService {

    /**
     * 创建血管通路
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBloodRoad( BloodRoadCreateReqVO createReqVO);

    /**
     * 更新血管通路
     *
     * @param updateReqVO 更新信息
     */
    void updateBloodRoad( BloodRoadUpdateReqVO updateReqVO);

    /**
     * 删除血管通路
     *
     * @param id 编号
     */
    void deleteBloodRoad(Long id);

    /**
     * 获得血管通路
     *
     * @param id 编号
     * @return 血管通路
     */
    BloodRoadRespVO getBloodRoad(Long id);

    /**
     * 获得血管通路列表
     *
     * @param ids 编号
     * @return 血管通路列表
     */
    List<BloodRoadDO> getBloodRoadList(Collection<Long> ids);

    /**
     * 获得血管通路分页
     *
     * @param pageReqVO 分页查询
     * @return 血管通路分页
     */
    PageResult<BloodRoadDO> getBloodRoadPage(BloodRoadPageReqVO pageReqVO);

    /**
     * 获得血管通路列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 血管通路列表
     */
    List<BloodRoadRespVO> getBloodRoadList(BloodRoadCreateReqVO exportReqVO);

}
