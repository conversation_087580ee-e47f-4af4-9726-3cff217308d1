package com.thj.boot.module.business.convert.adlevaluate;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.adlevaluate.AdlEvaluateDO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluateCreateReqVO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluateRespVO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluateUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 日常生活能力评定(ADL评估) Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AdlEvaluateConvert {

    AdlEvaluateConvert INSTANCE = Mappers.getMapper(AdlEvaluateConvert.class);

    AdlEvaluateDO convert(AdlEvaluateCreateReqVO bean);

    AdlEvaluateDO convert(AdlEvaluateUpdateReqVO bean);

    AdlEvaluateRespVO convert(AdlEvaluateDO bean);

    List<AdlEvaluateRespVO> convertList(List<AdlEvaluateDO> list);

    PageResult<AdlEvaluateRespVO> convertPage(PageResult<AdlEvaluateDO> page);


}
