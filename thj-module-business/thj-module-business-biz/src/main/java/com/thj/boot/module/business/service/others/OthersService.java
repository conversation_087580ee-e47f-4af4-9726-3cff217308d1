package com.thj.boot.module.business.service.others;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.others.OthersDO;
import com.thj.boot.module.business.pojo.others.vo.OthersCreateReqVO;
import com.thj.boot.module.business.pojo.others.vo.OthersPageReqVO;
import com.thj.boot.module.business.pojo.others.vo.OthersRespVO;
import com.thj.boot.module.business.pojo.others.vo.OthersUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 其他-维修登记 Service 接口
 *
 * <AUTHOR>
 */
public interface OthersService {

    /**
     * 创建其他-维修登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOthers(OthersCreateReqVO createReqVO);

    /**
     * 更新其他-维修登记
     *
     * @param updateReqVO 更新信息
     */
    void updateOthers(OthersUpdateReqVO updateReqVO);

    /**
     * 删除其他-维修登记
     *
     * @param id 编号
     */
    void deleteOthers(Long id);

    /**
     * 获得其他-维修登记
     *
     * @param id 编号
     * @return 其他-维修登记
     */
    OthersDO getOthers(Long id);

    /**
     * 获得其他-维修登记列表
     *
     * @param ids 编号
     * @return 其他-维修登记列表
     */
    List<OthersDO> getOthersList(Collection<Long> ids);

    /**
     * 获得其他-维修登记分页
     *
     * @param pageReqVO 分页查询
     * @return 其他-维修登记分页
     */
    PageResult<OthersDO> getOthersPage(OthersPageReqVO pageReqVO);

    /**
     * 获得其他-维修登记列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 其他-维修登记列表
     */
    List<OthersDO> getOthersList(OthersCreateReqVO createReqVO);

    /****
     * <AUTHOR>
     * @date 2024/1/31 20:25
     * @Description 获取最新一条
     **/
    OthersRespVO getNewMaintenanceRegister(OthersCreateReqVO createReqVO);
}
