package com.thj.boot.module.business.convert.dialysismanager;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysismanager.vo.*;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerRespVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析室手卫生检查表（新） Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisManagerConvert {

    DialysisManagerConvert INSTANCE = Mappers.getMapper(DialysisManagerConvert.class);

    DialysisManagerDO convert(DialysisManagerCreateReqVO bean);

    DialysisManagerDO convert(DialysisManagerUpdateReqVO bean);

    DialysisManagerRespVO convert(DialysisManagerDO bean);

    List<DialysisManagerRespVO> convertList(List<DialysisManagerDO> list);

    PageResult<DialysisManagerRespVO> convertPage(PageResult<DialysisManagerDO> page);

    PageResult<DialysisConsumablesRespVO> convertPage2(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

    PageResult<DialysisEmbarkationRespVO> convertPage3(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

    PageResult<DialysisDrugRespVO> convertPage4(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

    PageResult<DialysisCollectRespVO> convertPage5(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

    PageResult<DialysisBloodRespVO> convertPage6(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

    PageResult<DialysisTestRespVO> convertPage7(PageResult<DialysisManagerDO> dialysisManagerDOPageResult);

}
