package com.thj.boot.module.business.pojo.facilitymanager.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

import java.util.Date;

/**
 * 设备日常管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FacilityManagerBaseVO extends BaseDO {

    /**
     * 设备管理id
     */
    private Long id;
    /**
     * 序列号
     */
    private String code;
    /**
     * 设备类型（字典获取）
     */
    private String type;
    /**
     * 机号
     */
    private String facilityCode;
    /**
     * 设备名称id
     */
    private Long facilityNameId;
    /**
     * 设备型号id
     */
    private String facilityTypeId;
    /**
     * 设备品牌
     */
    private Long facilityBrandId;
    /**
     * 厂家
     */
    private String vender;
    /**
     * 维修厂家
     */
    private String repairVender;
    /**
     * 使用科室
     */
    private String officeName;
    /**
     * 科室编号
     */
    private String officeCode;
    /**
     * 购买日期
     */
    private Date buyTime;
    /**
     * 启用日期
     */
    private Date useTime;
    /**
     * 安装日期
     */
    private Date installTime;
    /**
     * 维修工程师
     */
    private String repairName;
    /**
     * 联系电话
     */
    private String phoneNumber;
    /**
     * 保修时间
     */
    private Date keepTime;
    /**
     * 机器状态（字典获取）
     */
    private String state;
    /**
     * 初次使用次数
     */
    private String initUse;
    /**
     * 备注
     */
    private String remark;
    /**
     * 报废日期
     */
    private Date scrapTime;
    /**
     * 报废原因（字典获取）
     */
    private String scrapReason;
    /**
     * 使用年限
     */
    private String useYear;
    /**
     * 使用时长
     */
    private String useDay;
    /**
     * 治疗模式（字典获取）
     */
    private String healMode;
    /**
     * 消毒方式（字典获取）
     */
    private String disinfectType;
    /**
     * 反渗模式（字典获取）
     */
    private String reverseType;
    /**
     * 前处理系统（字典获取多个）
     */
    private String pretreatment;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 班次，1-上午，2-下午，3-晚上
     */
    private String weekDay;
}
