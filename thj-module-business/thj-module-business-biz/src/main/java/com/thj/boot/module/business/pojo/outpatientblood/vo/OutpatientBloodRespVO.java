package com.thj.boot.module.business.pojo.outpatientblood.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutpatientBloodRespVO extends OutpatientBloodBaseVO {
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者类型
     */
    private String patientType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 婚姻状况
     */
    private String marriage;
    /**
     * 民族
     */
    private String nation;
    /**
     * 文化程度
     */
    private String culture;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 家庭住址
     */
    private String address;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 电话(本人)
     */
    private String mobile;
    /**
     * 体格检查
     */
    private String bodyInspect;


}
