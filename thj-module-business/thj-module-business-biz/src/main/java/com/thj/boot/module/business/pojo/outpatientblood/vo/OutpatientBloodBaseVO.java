package com.thj.boot.module.business.pojo.outpatientblood.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
 * 门诊血液透析简历 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class OutpatientBloodBaseVO extends PatientBaseDO {

    /**
     * 门诊血液透析简历主键id
     */
    private Long id;
    /**
     * 内容
     */
    private String content;
    /**
     * 备注
     */
    private String remark;
    /**
     * 主诉
     */
    private String mainSuit;
    /**
     * 现病史
     */
    private String presentDisease;
    /**
     * 初步诊断
     */
    private String preliminary;

}
