package com.thj.boot.module.business.convert.consumout;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.consumout.ConsumOutDO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutCreateReqVO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutExcelVO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutRespVO;
import com.thj.boot.module.business.pojo.consumout.vo.ConsumOutUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 耗材出库记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ConsumOutConvert {

    ConsumOutConvert INSTANCE = Mappers.getMapper(ConsumOutConvert.class);

    ConsumOutDO convert(ConsumOutCreateReqVO bean);

    ConsumOutDO convert(ConsumOutUpdateReqVO bean);

    ConsumOutRespVO convert(ConsumOutDO bean);

    List<ConsumOutRespVO> convertList(List<ConsumOutDO> list);

    PageResult<ConsumOutRespVO> convertPage(PageResult<ConsumOutDO> page);

    List<ConsumOutExcelVO> convertList02(List<ConsumOutDO> list);

}
