package com.thj.boot.module.business.convert.facilitysubarea;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaCreateReqVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaRespVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 设备管理-分区设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilitySubareaConvert {

    FacilitySubareaConvert INSTANCE = Mappers.getMapper(FacilitySubareaConvert.class);

    FacilitySubareaDO convert(FacilitySubareaCreateReqVO bean);

    FacilitySubareaDO convert(FacilitySubareaUpdateReqVO bean);

    FacilitySubareaRespVO convert(FacilitySubareaDO bean);

    List<FacilitySubareaRespVO> convertList(List<FacilitySubareaDO> list);

    PageResult<FacilitySubareaRespVO> convertPage(PageResult<FacilitySubareaDO> page);


}
