package com.thj.boot.module.business.pojo.hemodialysis.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

/**
 * 血液透析患者评估 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class HemodialysisBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 一般护理评估
     */
    private String nursing;
    /**
     * 前次治疗后专科评估
     */
    private String previous;
    /**
     * 新患者情况
     */
    private String newPatient;
    /**
     * 单针双腔导管置管术后
     */
    private String catheter;
    /**
     * 动静脉内瘘吻合术后
     */
    private String afterFistula;
    /**
     * 备注
     */
    private String remark;
    /**
     * 大json
     */
    private String evaluateContent;
    /**
     * 健康教育方式
     */
    private String healthEducation;
}
