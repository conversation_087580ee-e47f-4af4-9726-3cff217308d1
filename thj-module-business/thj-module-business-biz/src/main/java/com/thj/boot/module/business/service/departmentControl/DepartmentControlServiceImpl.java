package com.thj.boot.module.business.service.departmentControl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.utils.CollectionUtils;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.dal.datado.renalprojectitem.RenalProjectItemDO;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper;
import com.thj.boot.module.business.dal.mapper.renalprojectitem.RenalProjectItemMapper;
import com.thj.boot.module.business.pojo.departmentControl.PatientDetailResp;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordCreateReqVO;
import com.thj.boot.module.business.service.dialysisadvice.DialysisAdviceService;
import com.thj.boot.module.business.service.dialysisrecord.DialysisRecordService;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
@RequiredArgsConstructor
public class DepartmentControlServiceImpl implements DepartmentControlService {

    private final PatientMapper patientMapper;

    private final RenalProjectMapper renalProjectMapper;

    private final RenalProjectItemMapper renalProjectItemMapper;

    private final DialysisRecordService dialysisRecordService;

    private final DialysisAdviceService dialogueAdviceService;

    private final DialysisManagerMapper dialogManagerMapper;

    private final BloodRoadMapper bloodRoadMapper;

    private final DictDataApi dictDataApi;

    @Override
    public PatientDetailResp getInfo(Long patientId) {
        Map<String, Object> objectMap = patientMapper.getPatientInfoJoinReason(patientId);
        PatientDetailResp.PatientInfo patientInfo = new PatientDetailResp.PatientInfo(
                Convert.toLong(objectMap.get("id")),
                Convert.toStr(objectMap.get("patientName")),
                Convert.toStr(objectMap.get("sex")),
                Convert.toInt(objectMap.get("age")),
                Convert.toStr(objectMap.get("dialysisAge")),
                Convert.toStr(objectMap.get("dialyzeNo")),
                Convert.toStr(objectMap.get("describes")),
                Convert.toStr(objectMap.get("infect")),
                null
        );


        if (StrUtil.isNotBlank(patientInfo.getInfect())) {
            List<DictDataRespDTO> infect = dictDataApi.getDictListData("infect");
            StringBuilder builder = new StringBuilder();
            Arrays.stream(patientInfo.getInfect().split(StrUtil.COMMA)).forEach(item -> {
                DictDataRespDTO dataRespDTO = CollectionUtils.findFirst(infect, i -> i.getValue().equals(item));
                if (dataRespDTO != null) builder.append(dataRespDTO.getLabel()).append(StrUtil.COMMA);
            });
            patientInfo.setInfect(builder.substring(0, builder.length() - 1));
        }

        BloodRoadDO bloodRoadDO = bloodRoadMapper.selectOne(new LambdaQueryWrapper<BloodRoadDO>().eq(BloodRoadDO::getPatientId, patientId).orderByDesc(BloodRoadDO::getCreateTime).last("limit 1"));
        if (bloodRoadDO != null) {
            Optional.ofNullable(dictDataApi.getDictData("blood_passage_two", Convert.toStr(bloodRoadDO.getType()))).ifPresent(dataRespDTO -> patientInfo.setBloodType(dataRespDTO.getLabel()));
        }


        DialysisRecordCreateReqVO createReqVO = new DialysisRecordCreateReqVO();
        createReqVO.setPatientId(patientId);

        DialysisAdviceCreateReqVO adviceCreateReqVO = new DialysisAdviceCreateReqVO();
        adviceCreateReqVO.setPatientId(patientId);


        return new PatientDetailResp(patientInfo,
                getDialysisPlan(patientId),
                dialysisRecordService.getDialysisRecordJoinList(createReqVO),
                dialogueAdviceService.getDialysisAdviceList(adviceCreateReqVO),
                getCheckInfoMap(patientId));
    }

    /**
     * 获取透析方案
     *
     * @param patientId
     * @return
     */
    private List<Map<String, Object>> getDialysisPlan(Long patientId) {
        List<DialysisManagerDO> dialysisManagerDOS = dialogManagerMapper.selectList(new LambdaQueryWrapper<DialysisManagerDO>().eq(DialysisManagerDO::getPatientId, patientId)
                .select(DialysisManagerDO::getPatientId, DialysisManagerDO::getPrescription, DialysisManagerDO::getUpdateTime));
        return dialysisManagerDOS.stream().map(item -> {
            JSONObject jsonObject = JSON.parseObject(item.getPrescription());
            HashMap<String, Object> map = new HashMap<>(jsonObject);
            map.put("updateTime", item.getUpdateTime());
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 获取检验检查信息
     *
     * @param patientId
     * @return
     */
    private List<Map<String, Object>> getCheckInfoMap(Long patientId) {
        List<Map<String, Object>> renalProjectList = renalProjectMapper.getRenalProjectList(patientId);
        // 先根据dictId分组
//        Map<String, List<Map<String, Object>>> dictIdGroupMap = CollectionUtils.convertMultiMap(renalProjectList, item -> (String) item.get("dictId"));
        Map<String, List<Map<String, Object>>> dictIdGroupMap = renalProjectList.stream()
                .filter(item -> item.get("project_type") != null)
                .collect(Collectors.groupingBy(item -> (String) item.get("dictId")));
        // 再将已分组的dictIdGroupMap 再根据project_type分组
//        Map<String, Map<Integer, List<Map<String, Object>>>> project = dictIdGroupMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, item -> {
//            List<Map<String, Object>> value = item.getValue();
//            Map<Integer, List<Map<String, Object>>> multiMap = CollectionUtils.convertMultiMap(value, childItem -> (Integer) childItem.get("project_type"));
//            return multiMap.entrySet().stream()
//                    .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
//                    .collect(Collectors.toMap(
//                            Map.Entry::getKey,
//                            Map.Entry::getValue,
//                            (e1, e2) -> e1,
//                            LinkedHashMap::new
//                    ));
//        }));

        // 再将已分组的dictIdGroupMap 再根据project_type分组，并使用checkTime作为key
        Map<String, Map<LocalDateTime, List<Map<String, Object>>>> projectTypeGroup = dictIdGroupMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, item -> {
            List<Map<String, Object>> value = item.getValue();
            Map<LocalDateTime, List<Map<String, Object>>> multiMap = new LinkedHashMap<>();
            value.stream().collect(Collectors.groupingBy(childItem -> (Integer) childItem.get("project_type")))
                    .forEach((projectType, projectTypeList) -> {
                        if (!projectTypeList.isEmpty()) {
                            LocalDateTime checkTime = (LocalDateTime) projectTypeList.get(0).get("checkTime");
                            multiMap.put(checkTime, projectTypeList);
                        }
                    });
            return multiMap;
        }));
        // 对每个 dictId 对应的 Map<LocalDateTime, List<Map<String, Object>>> 进行排序（按检查时间从大到小）
        Map<String, Map<LocalDateTime, List<Map<String, Object>>>> project = projectTypeGroup.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().entrySet().stream()
                        .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (e1, e2) -> e1,
                                LinkedHashMap::new
                        )),
                (e1, e2) -> e1,
                LinkedHashMap::new
        ));



        List<String> itemLableList = Arrays.asList("血红蛋白", "白蛋白", "透析前磷", "透析前钙", "透析前钾", "KT/V",
                "URR", "透析前肌酐", "透析后肌酐", "透析前尿素氮", "透析后尿素氮", "血清铁", "血清铁蛋白", "总铁结合力",
                "转铁蛋白饱和度(计算)", "转铁蛋白", "平均红细胞体积(MCV)");
        List<RenalProjectItemDO> projectItemDOList = renalProjectItemMapper.selectList(new LambdaQueryWrapper<RenalProjectItemDO>().in(RenalProjectItemDO::getLabel, itemLableList));
        List<Map<String, Object>> resultList = new ArrayList<>();
        projectItemDOList.forEach(item -> {
            Long dictId = item.getDictId();
            Long key = item.getId();
//            List<Map<String, Object>> mapList = dictIdGroupMap.get(Convert.toStr(dictId));
//            if (CollUtil.isNotEmpty(mapList) && mapList.size() >= 3) {
//                mapList = mapList.subList(0, 3);
//            }
            // 获取检查项目
            Map<LocalDateTime, List<Map<String, Object>>> dictIdGroupMap1 = project.get(Convert.toStr(dictId));
            if (CollUtil.isNotEmpty(dictIdGroupMap1)) {
                Map<String, Object> hashMap = new HashMap<>();
                // 最近一次检查
                if (dictIdGroupMap1.size() >= 1) {
                    List<Map<String, Object>> current = dictIdGroupMap1.values().iterator().next();
                    if (CollUtil.isNotEmpty(current)) {
                        for (Map<String, Object> item1 : current) {
                            if (key.equals(Convert.toLong(item1.get("jsonKey")))) {
                                hashMap.put("current", item1.get("jsonValue"));
                                break;
                            }
                        }
                    }
                }
                // 上次一次检查
                if (dictIdGroupMap1.size() >= 2) {
                    List<Map<String, Object>> before = dictIdGroupMap1.values().stream().skip(1).findFirst().orElse(null);
                    if (CollUtil.isNotEmpty(before)) {
                        for (Map<String, Object> item1 : before) {
                            if (key.equals(Convert.toLong(item1.get("jsonKey")))) {
                                hashMap.put("before", item1.get("jsonValue"));
                                break;
                            }
                        }
                    }
                }
                // 上上一次检查
                if (dictIdGroupMap1.size() >= 3) {
                    List<Map<String, Object>> beforeAfter = dictIdGroupMap1.values().stream().skip(2).findFirst().orElse(null);
                    if (CollUtil.isNotEmpty(beforeAfter)) {
                        for (Map<String, Object> item1 : beforeAfter) {
                            if (key.equals(Convert.toLong(item1.get("jsonKey")))) {
                                hashMap.put("beforeAfter", item1.get("jsonValue"));
                                break;
                            }
                        }
                    }
                }
//            hashMap.put("current", getValueFromMapList(mapList, 0));
//            hashMap.put("before", getValueFromMapList(mapList, 1));
//            hashMap.put("beforeAfter", getValueFromMapList(mapList, 2));
                hashMap.put("projectName", item.getLabel());
                resultList.add(hashMap);
            }
        });
        return resultList;
    }

    private Object getValueFromMapList(List<Map<String, Object>> mapList, int index) {
        if (CollUtil.isEmpty(mapList) || index >= mapList.size()) return null;
        Map<String, Object> map = mapList.get(index);
        if (map == null) return null;
        return map.get("jsonValue");
    }

}

