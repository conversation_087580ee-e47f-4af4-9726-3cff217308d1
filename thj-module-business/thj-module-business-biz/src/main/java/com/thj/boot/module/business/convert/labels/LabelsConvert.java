package com.thj.boot.module.business.convert.labels;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.labels.LabelsDO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsCreateReqVO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsRespVO;
import com.thj.boot.module.business.pojo.labels.vo.LabelsUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 标签管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LabelsConvert {

    LabelsConvert INSTANCE = Mappers.getMapper(LabelsConvert.class);

    LabelsDO convert(LabelsCreateReqVO bean);

    LabelsDO convert(LabelsUpdateReqVO bean);

    LabelsRespVO convert(LabelsDO bean);

    List<LabelsRespVO> convertList(List<LabelsDO> list);

    PageResult<LabelsRespVO> convertPage(PageResult<LabelsDO> page);


}
