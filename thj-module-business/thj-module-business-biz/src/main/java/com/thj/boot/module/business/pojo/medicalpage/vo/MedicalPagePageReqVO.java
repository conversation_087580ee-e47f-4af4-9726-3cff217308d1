package com.thj.boot.module.business.pojo.medicalpage.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicalPagePageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 户口地址
     */
    private String accountAddress;
    /**
     * 邮编
     */
    private String postcode;
    /**
     * 电话
     */
    private String mobile;
    /**
     * 亲属电话
     */
    private String kinsmanMobile;
    /**
     * 诊断
     */
    private String diagnose;
    /**
     * 合并症和并发症
     */
    private String complication;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;

}
