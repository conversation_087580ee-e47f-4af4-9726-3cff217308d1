package com.thj.boot.module.business.pojo.arrangeclasses.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArrangeClassesCreateReqVO extends ArrangeClassesBaseVO {
    /**
     * 月份
     */
    private String month;

    /**
     * 分区
     */
    private List<Long> facilitySubareaIds;

    /**
     * 更多搜索项
     */
    private String more;

    /**
     * 开始日期
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 按照某一天搜索
     */
    private Date dayTime;

    /**
     * 多个医嘱
     */
    private String adviceIds;

    /**
     * 更新类型  1直接更新 2对换排班 3替换排班
     */
    private String updateType;

    /**
     * 透析名称
     */
    private String dialysisName;

    /**
     * 透析名称
     */
    private Long optionId;

    /**
     * 排序（1-机号排序 2-签到排序）
     */
    private String sortType;

    /**
     * 分区
     */
    private List<String> subareaId;

    /**
     * 班次
     */
    private List<String> classes;

    /**
     * excel标题
     */
    private String excelTitle = "sheet1";

}
