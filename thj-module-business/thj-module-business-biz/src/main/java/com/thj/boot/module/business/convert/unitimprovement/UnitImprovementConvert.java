package com.thj.boot.module.business.convert.unitimprovement;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.unitimprovement.UnitImprovementDO;
import com.thj.boot.module.business.pojo.unitimprovement.vo.UnitImprovementCreateReqVO;
import com.thj.boot.module.business.pojo.unitimprovement.vo.UnitImprovementRespVO;
import com.thj.boot.module.business.pojo.unitimprovement.vo.UnitImprovementUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血液透析室持续质量改进书 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UnitImprovementConvert {

    UnitImprovementConvert INSTANCE = Mappers.getMapper(UnitImprovementConvert.class);


    UnitImprovementDO convert(UnitImprovementCreateReqVO bean);

    UnitImprovementDO convert(UnitImprovementUpdateReqVO bean);

    UnitImprovementRespVO convert(UnitImprovementDO bean);

    List<UnitImprovementRespVO> convertList(List<UnitImprovementDO> list);

    PageResult<UnitImprovementRespVO> convertPage(PageResult<UnitImprovementDO> page);


}
