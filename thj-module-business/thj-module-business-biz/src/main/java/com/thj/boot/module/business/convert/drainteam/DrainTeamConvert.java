package com.thj.boot.module.business.convert.drainteam;


import com.thj.boot.module.business.controller.admin.drainteam.vo.DialyziRespVO;
import com.thj.boot.module.business.dal.datado.drainteam.DrainTeamDO;
import com.thj.boot.module.business.pojo.drainteam.DrainTeamCreateReqVO;
import com.thj.boot.module.business.pojo.drainteam.DrainTeamRespVO;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医嘱模版 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DrainTeamConvert {

    DrainTeamConvert INSTANCE = Mappers.getMapper(DrainTeamConvert.class);


    List<DialyziRespVO> convertList(List<DictDataRespDTO> dialyzeWayList);

    List<DrainTeamDO> convertList2(List<DrainTeamCreateReqVO> drainTeamCreateReqVOS);

    List<DrainTeamRespVO> convertList3(List<DrainTeamDO> drainTeamDOS);
}
