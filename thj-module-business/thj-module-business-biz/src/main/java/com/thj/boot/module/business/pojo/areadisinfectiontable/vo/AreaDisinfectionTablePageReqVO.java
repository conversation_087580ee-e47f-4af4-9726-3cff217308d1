package com.thj.boot.module.business.pojo.areadisinfectiontable.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AreaDisinfectionTablePageReqVO extends PageParam {

    private Long id;
    /**
     * 消毒时间日期
     */
    private Date disinfectionTime;
    /**
     * 病区
     */
    private String ward;
    /**
     * 班次
     */
    private String shift;
    /**
     * 类型(上机/下机)
     */
    private String type;
    /**
     * 含氯制剂500mg/L
     */
    private String chlorineContaining;
    /**
     * 防水围裙
     */
    private String waterproofApron;
    /**
     * （护目镜/防护面屏）
     */
    private String eyeFaceShield;
    /**
     * （治疗车/椅子）
     */
    private String treatmentCartChair;
    /**
     * 红外体温计
     */
    private String infraredThermometer;
    /**
     * 移动端
     */
    private String mobileDevice;
    /**
     * （更换床单/通风时间）
     */
    private String ventilationTime;
    /**
     * 污染物品更换（物品/时间）
     */
    private String contaminatedItemsChange;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 签名
     */
    private String signature;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 负责人id
     */
    private Long responsiblePersonId;

}
