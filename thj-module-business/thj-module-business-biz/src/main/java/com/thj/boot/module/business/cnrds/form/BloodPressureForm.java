package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 血压表单
 */
@Data
public class BloodPressureForm {

    /**
     * 年份
     */
    @NotNull
    @JsonProperty("BP_YEAR")
    private String year;

    /**
     * 时间维度
     */
    @NotNull
    @JsonProperty("BP_DIMENSION")
    private String dimension;

    /**
     * 测量部位
     */
    @JsonProperty("BP_POSITION")
    private String position;

    /**
     * 透析相关高血压
     */
    @JsonProperty("BP_HYPERTENSION")
    private String hypertension;

    /**
     * 透析相关低血压
     */
    @JsonProperty("BP_HYPOTENSION")
    private String hypotension;

    /**
     * 透析前_收缩压（mmHg）
     */
    @NotNull
    @JsonProperty("BP_PRESBP")
    private String presbp;

    /**
     * 透析前_舒张压（mmHg）
     */
    @NotNull
    @JsonProperty("BP_PREDBP")
    private String predbp;

    /**
     * 透析后_收缩压（mmHg）
     */
    @JsonProperty("BP_POSSBP")
    private String possbp;

    /**
     * 透析后_舒张压（mmHg）
     */
    @JsonProperty("BP_POSDBP")
    private String posdbp;

    /**
     * 非透析日_收缩压（mmHg）
     */
    @JsonProperty("BP_NDSBP")
    private String ndsbp;

    /**
     * 非透析日_舒张压（mmHg）
     */
    @JsonProperty("BP_NDDBP")
    private String nddbp;


}
