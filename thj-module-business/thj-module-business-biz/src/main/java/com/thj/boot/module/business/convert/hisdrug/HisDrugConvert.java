package com.thj.boot.module.business.convert.hisdrug;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugCreateReqVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugRespVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * his药品 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HisDrugConvert {

    HisDrugConvert INSTANCE = Mappers.getMapper(HisDrugConvert.class);

    HisDrugDO convert(HisDrugCreateReqVO bean);

    HisDrugDO convert(HisDrugUpdateReqVO bean);

    HisDrugRespVO convert(HisDrugDO bean);

    List<HisDrugRespVO> convertList(List<HisDrugDO> list);

    PageResult<HisDrugRespVO> convertPage(PageResult<HisDrugDO> page);


}
