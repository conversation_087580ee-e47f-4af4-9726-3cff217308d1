package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * HIF-PHI 表单
 */
@Data
public class HifPhiForm {

    // 年
    @JsonProperty("HIF_YEAR")
    @NotNull
    private String hifYear;

    // 时间维度
    @JsonProperty("HIF_DIMENSION")
    @NotNull
    private String hifDimension;

    // 有无变化
    @JsonProperty("HIF_CHANGE_YN")
    @NotNull
    private String hifChangeYN;

    // 是否使用
    @JsonProperty("HIF_TREATMENT")
    @NotNull
    private String hifTreatment;

    // 是否为首次
    @JsonProperty("HIF_FIRST_YN")
    private Boolean hifFirstYN;

}
