package com.thj.boot.module.business.convert.jkdivision;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkdivision.JkDivisionDO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionCreateReqVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionRespVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 健康教育-护士患者分组 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JkDivisionConvert {

    JkDivisionConvert INSTANCE = Mappers.getMapper(JkDivisionConvert.class);

    JkDivisionDO convert(JkDivisionCreateReqVO bean);

    JkDivisionDO convert(JkDivisionUpdateReqVO bean);

    JkDivisionRespVO convert(JkDivisionDO bean);

    List<JkDivisionRespVO> convertList(List<JkDivisionDO> list);

    PageResult<JkDivisionRespVO> convertPage(PageResult<JkDivisionDO> page);


}
