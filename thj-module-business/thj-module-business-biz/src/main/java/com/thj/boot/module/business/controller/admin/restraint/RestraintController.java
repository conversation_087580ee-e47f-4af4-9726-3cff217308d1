package com.thj.boot.module.business.controller.admin.restraint;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.restraint.RestraintConvert;
import com.thj.boot.module.business.dal.datado.restraint.RestraintDO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintCreateReqVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintPageReqVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintRespVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintUpdateReqVO;
import com.thj.boot.module.business.service.restraint.RestraintService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/restraint")
@Validated
public class RestraintController {

    @Resource
    private RestraintService restraintService;

    @PostMapping("/create")
    public CommonResult<Long> createRestraint(@RequestBody RestraintCreateReqVO createReqVO) {
        return success(restraintService.createRestraint(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateRestraint(@RequestBody RestraintUpdateReqVO updateReqVO) {
        restraintService.updateRestraint(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteRestraint(@RequestParam("id") Long id) {
        restraintService.deleteRestraint(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<RestraintRespVO> getRestraint(@RequestParam("id") Long id) {
        RestraintDO restraint = restraintService.getRestraint(id);
        return success(RestraintConvert.INSTANCE.convert(restraint));
    }

    @GetMapping("/list")
    public CommonResult<List<RestraintRespVO>> getRestraintList(RestraintCreateReqVO createReqVO) {
        List<RestraintDO> list = restraintService.getRestraintList(createReqVO);
        return success(RestraintConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<RestraintRespVO>> getRestraintPage(@RequestBody RestraintPageReqVO pageVO) {
        PageResult<RestraintDO> pageResult = restraintService.getRestraintPage(pageVO);
        return success(RestraintConvert.INSTANCE.convertPage(pageResult));
    }


}
