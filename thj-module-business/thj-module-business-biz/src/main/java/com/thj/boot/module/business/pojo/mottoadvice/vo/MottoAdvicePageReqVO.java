package com.thj.boot.module.business.pojo.mottoadvice.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MottoAdvicePageReqVO extends PageParam {

    private Long id;
    /**
     * 医嘱类型
     */
    private String adviceType;
    /**
     * 医生名称
     */
    private String doctorName;
    /**
     * 医生ID
     */
    private Long doctorId;
    /**
     * 医嘱描述
     */
    private String describes;
    /**
     * 单次剂量
     */
    private String dose;
    /**
     * 单位
     */
    private String doseUnit;
    /**
     * 开药数量
     */
    private Integer number;
    /**
     * 单位
     */
    private String numberUnit;
    /**
     * 给药途径
     */
    private String route;
    /**
     * 执行频率
     */
    private String rate;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 模板ID
     */
    private Long templateId;
    /**
     * 药品ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer drugId;

    private Long pid;
}
