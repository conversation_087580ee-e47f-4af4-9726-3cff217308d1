package com.thj.boot.module.business.pojo.renalprojectinfo.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static com.thj.boot.common.utils.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 肾科检查项目信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class RenalProjectInfoBaseVO extends BaseDO {

    @NotNull(message = "wl_renal_project主键不能为空")
    private Long projectId;

    private Long jsonKey;

    private String jsonValue;

    private Integer projectType;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date checkTime;

    private String customUnit;

    private String list;

}
