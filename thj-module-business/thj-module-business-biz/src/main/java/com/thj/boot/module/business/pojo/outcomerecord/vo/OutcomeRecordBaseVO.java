package com.thj.boot.module.business.pojo.outcomerecord.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
* 转归记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class OutcomeRecordBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 转归类型（个性化获取）
     */
    private String type;
    /**
     * 转归分类（个性化获取）
     */
    private String classify;
    /**
     * 转归原因（个性化获取）
     */
    private String reason;
    /**
     * 说明
     */
    private String describes;
    /**
     * 转归日期
     */
    private Date prognosisTime;
    /**
     * 住院/门诊（字典获取）
     */
    private String hospitalized;
    /**
     * 病区（字典获取）
     */
    private String endemic;
    /**
     * 床号
     */
    private String bedNo;
    /**
     * 操作人
     */
    private Long userId;
    /**
     * 备注
     */
    private String remark;

}
