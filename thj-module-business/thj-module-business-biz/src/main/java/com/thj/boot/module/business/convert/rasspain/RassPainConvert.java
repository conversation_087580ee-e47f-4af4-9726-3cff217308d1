package com.thj.boot.module.business.convert.rasspain;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.rasspain.RassPainDO;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainCreateReqVO;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainRespVO;
import com.thj.boot.module.business.pojo.rasspain.vo.RassPainUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * RASS评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RassPainConvert {

    RassPainConvert INSTANCE = Mappers.getMapper(RassPainConvert.class);

    RassPainDO convert(RassPainCreateReqVO bean);

    RassPainDO convert(RassPainUpdateReqVO bean);

    RassPainRespVO convert(RassPainDO bean);

    List<RassPainRespVO> convertList(List<RassPainDO> list);

    PageResult<RassPainRespVO> convertPage(PageResult<RassPainDO> page);


}
