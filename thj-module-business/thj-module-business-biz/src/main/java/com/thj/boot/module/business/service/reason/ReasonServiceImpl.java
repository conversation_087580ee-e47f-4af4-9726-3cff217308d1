package com.thj.boot.module.business.service.reason;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.reason.ReasonConvert;
import com.thj.boot.module.business.dal.datado.reason.ReasonDO;
import com.thj.boot.module.business.dal.mapper.reason.ReasonMapper;
import com.thj.boot.module.business.pojo.reason.vo.ReasonCreateReqVO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonPageReqVO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonRespVO;
import com.thj.boot.module.business.pojo.reason.vo.ReasonUpdateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 诊断类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReasonServiceImpl implements ReasonService {

    @Resource
    private ReasonMapper reasonMapper;

    @Override
    public Long createReason(ReasonCreateReqVO createReqVO) {
        checkPidCreate(createReqVO);
        // 插入
        ReasonDO reason = ReasonConvert.INSTANCE.convert(createReqVO);
        ReasonDO reasonDO = reasonMapper.selectById(createReqVO.getPid());
        if (reasonDO != null) {
            reason.setAncestors(reasonDO.getAncestors() + "," + createReqVO.getPid());
        }
        reasonMapper.insert(reason);
        // 返回
        return reason.getId();
    }

    private void checkPidCreate(ReasonCreateReqVO createReqVO) {
        //根据父级id和诊断名称查询是否存在
        Long count = reasonMapper.selectCount(ReasonDO::getPid, createReqVO.getPid(), ReasonDO::getName, createReqVO.getName());
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.DEPT_NAME_DUPLICATE);
        }
    }

    @Override
    public void updateReason(ReasonUpdateReqVO updateReqVO) {
        checkPidUpdate(updateReqVO);
        ReasonDO newParentReason = reasonMapper.selectById(updateReqVO.getPid());
        ReasonDO oldReason = reasonMapper.selectById(updateReqVO.getId());
        if (newParentReason != null && oldReason != null) {
            String newAncestors = newParentReason.getAncestors() + "," + newParentReason.getId();
            String oldAncestors = oldReason.getAncestors();
            updateReqVO.setAncestors(newAncestors);
            updateReasonChildren(updateReqVO.getId(), newAncestors, oldAncestors);
        }
        // 更新
        ReasonDO updateObj = ReasonConvert.INSTANCE.convert(updateReqVO);
        reasonMapper.updateById(updateObj);
    }

    private void checkPidUpdate(ReasonUpdateReqVO updateReqVO) {
        ReasonDO reasonDO = reasonMapper.selectOne(ReasonDO::getPid, updateReqVO.getPid(), ReasonDO::getName, updateReqVO.getName());
        if (reasonDO != null && reasonDO.getId() != updateReqVO.getId()) {
            throw new ServiceException(GlobalErrorCodeConstants.NAME_EXITS);
        }
    }

    private void updateReasonChildren(Long id, String newAncestors, String oldAncestors) {
        //查询自己id和层级包含自己id
        List<ReasonDO> children = reasonMapper.selectList(new LambdaQueryWrapperX<ReasonDO>()
                .eq(ReasonDO::getId, id)
                .or()
                .like(ReasonDO::getAncestors, id));
        for (ReasonDO child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        //修改层级
        /*if (children.size() > 0) {
            reasonMapper.updateDeptChildren(children);
        }*/
    }

    @Override
    public void deleteReason(Long id) {
        //存在自己不可删除
        Long count = reasonMapper.selectCount(ReasonDO::getPid, id);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.MOTTO_JOIN);
        }
        // 删除
        reasonMapper.deleteById(id);
    }


    @Override
    public ReasonDO getReason(Long id) {
        return reasonMapper.selectById(id);
    }

    @Override
    public List<ReasonDO> getReasonList(Collection<Long> ids) {
        return reasonMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ReasonDO> getReasonPage(ReasonPageReqVO pageReqVO) {
        return reasonMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ReasonRespVO> getReasonList(ReasonCreateReqVO createReqVO) {
        List<ReasonDO> reasonDOS = reasonMapper.selectList(createReqVO);
        List<ReasonRespVO> reasonRespVOS = ReasonConvert.INSTANCE.convertList(reasonDOS);
        List<ReasonRespVO> rootList = null;
        if (CollectionUtil.isNotEmpty(reasonRespVOS)) {
            List<ReasonRespVO> finalReasonRespVOS = reasonRespVOS;
            reasonRespVOS = reasonRespVOS.stream().peek(reasonRespVO -> {
                //显示层级名称
                if ("0".equals(reasonRespVO.getAncestors())) {
                    reasonRespVO.setLevel(reasonRespVO.getName());
                } else {
                    List<Long> pidList = Arrays.stream(reasonRespVO.getAncestors().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    List<ReasonRespVO> reasonDOList = finalReasonRespVOS.stream().filter(item -> pidList.contains(item.getId())).collect(Collectors.toList());
                    reasonRespVO.setLevel(reasonDOList.stream().map(ReasonRespVO::getName).collect(Collectors.joining("/ ")) + "/" + reasonRespVO.getName());
                }
            }).collect(Collectors.toList());

            rootList = reasonRespVOS.stream().filter(reasonRespVO -> 0 == reasonRespVO.getPid()).collect(Collectors.toList());
            List<ReasonRespVO> subList = reasonRespVOS.stream().filter(reasonRespVO -> 0 != reasonRespVO.getPid()).collect(Collectors.toList());
            reasonRespVOS.forEach(root -> busort(root, subList));
            return rootList;
        }
        return reasonRespVOS;
    }

    @Override
    public List<ReasonRespVO> getReasonListEnd(ReasonCreateReqVO createReqVO) {
        createReqVO.setPid(0L);
        List<ReasonDO> reasonDOS = reasonMapper.selectList(createReqVO);
        List<ReasonRespVO> respVOList = ReasonConvert.INSTANCE.convertList(reasonDOS);
        if (CollectionUtil.isNotEmpty(respVOList)) {
            respVOList = respVOList.stream().peek(reasonRespVO -> {
                List<ReasonDO> reasonDOList = reasonMapper.selectList(ReasonDO::getId, Arrays.stream(reasonRespVO.getAncestors().split(",")).collect(Collectors.toList()));
                if (CollectionUtil.isNotEmpty(reasonDOList)) {
                    reasonRespVO.setLevel(reasonDOList.stream().map(ReasonDO::getName).collect(Collectors.joining("/")));
                }
            }).collect(Collectors.toList());
        }
        return respVOList;
    }


    private void busort(ReasonRespVO root, List<ReasonRespVO> subList) {
        List<ReasonRespVO> childrenList = subList.stream().filter(reasonRespVO -> reasonRespVO.getPid().equals(Long.valueOf(root.getId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(childrenList)) {
            root.setChildren(childrenList);
            childrenList.forEach(sysMenu -> busort(sysMenu, subList));
        } else {
            root.setChildren(null);
        }
    }

}
