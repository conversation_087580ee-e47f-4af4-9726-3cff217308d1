package com.thj.boot.module.business.convert.childrentumble;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.childrentumble.ChildrenTumbleDO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleCreateReqVO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleRespVO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 小儿高危跌倒坠床评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ChildrenTumbleConvert {

    ChildrenTumbleConvert INSTANCE = Mappers.getMapper(ChildrenTumbleConvert.class);

    ChildrenTumbleDO convert(ChildrenTumbleCreateReqVO bean);

    ChildrenTumbleDO convert(ChildrenTumbleUpdateReqVO bean);

    ChildrenTumbleRespVO convert(ChildrenTumbleDO bean);

    List<ChildrenTumbleRespVO> convertList(List<ChildrenTumbleDO> list);

    PageResult<ChildrenTumbleRespVO> convertPage(PageResult<ChildrenTumbleDO> page);


}
