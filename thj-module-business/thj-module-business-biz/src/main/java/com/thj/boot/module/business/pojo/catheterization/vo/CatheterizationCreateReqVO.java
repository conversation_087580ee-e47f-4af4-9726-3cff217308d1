package com.thj.boot.module.business.pojo.catheterization.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatheterizationCreateReqVO extends CatheterizationBaseVO {

    private Long id;
    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 戴口罩（对/错）
     */
    private String wearingMask;
    /**
     * 正确行手卫生（对/错）
     */
    private String handHygiene;
    /**
     * 戴新无菌手套（对/错）
     */
    private String sterileGloves;
    /**
     * 确认导管夹闭（对/错）
     */
    private String confirmClampClosure;
    /**
     * 移开导管端帽（对/错）
     */
    private String removeCatheterCap;
    /**
     * 消毒管口（对/错）
     */
    private String disinfectTubePort;
    /**
     * 消毒之后待干（对/错）
     */
    private String waitingDryingAfter;
    /**
     * 无菌连接导管与管路（对/错）
     */
    private String sterileLine;
    /**
     * 脱手套（对/错）
     */
    private String gloveRemoval;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTime;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;


}
