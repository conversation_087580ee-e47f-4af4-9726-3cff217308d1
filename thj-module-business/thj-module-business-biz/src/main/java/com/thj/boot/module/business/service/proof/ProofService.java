package com.thj.boot.module.business.service.proof;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.proof.ProofDO;
import com.thj.boot.module.business.pojo.proof.vo.ProofCreateReqVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofPageReqVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofRespVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 疾病诊断-诊断证明 Service 接口
 *
 * <AUTHOR>
 */
public interface ProofService {

    /**
     * 创建疾病诊断-诊断证明
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProof(ProofCreateReqVO createReqVO);

    /**
     * 更新疾病诊断-诊断证明
     *
     * @param updateReqVO 更新信息
     */
    void updateProof(ProofUpdateReqVO updateReqVO);

    /**
     * 删除疾病诊断-诊断证明
     *
     * @param id 编号
     */
    void deleteProof(Long id);

    /**
     * 获得疾病诊断-诊断证明
     *
     * @param id 编号
     * @return 疾病诊断-诊断证明
     */
    ProofRespVO getProof(Long patientId);

    /**
     * 获得疾病诊断-诊断证明列表
     *
     * @param ids 编号
     * @return 疾病诊断-诊断证明列表
     */
    List<ProofDO> getProofList(Collection<Long> ids);

    /**
     * 获得疾病诊断-诊断证明分页
     *
     * @param pageReqVO 分页查询
     * @return 疾病诊断-诊断证明分页
     */
    PageResult<ProofDO> getProofPage(ProofPageReqVO pageReqVO);

    /**
     * 获得疾病诊断-诊断证明列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 疾病诊断-诊断证明列表
     */
    List<ProofDO> getProofList(ProofCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2024/2/23 10:24
     * @Description 新增修改
     **/
    void createOrUpdateProof(ProofUpdateReqVO updateReqVO);
}
