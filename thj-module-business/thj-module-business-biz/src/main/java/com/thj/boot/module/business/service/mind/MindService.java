package com.thj.boot.module.business.service.mind;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.mind.MindDO;
import com.thj.boot.module.business.pojo.mind.vo.MindCreateReqVO;
import com.thj.boot.module.business.pojo.mind.vo.MindPageReqVO;
import com.thj.boot.module.business.pojo.mind.vo.MindUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 心理评估 Service 接口
 *
 * <AUTHOR>
 */
public interface MindService {

    /**
     * 创建心理评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMind( MindCreateReqVO createReqVO);

    /**
     * 更新心理评估
     *
     * @param updateReqVO 更新信息
     */
    void updateMind( MindUpdateReqVO updateReqVO);

    /**
     * 删除心理评估
     *
     * @param id 编号
     */
    void deleteMind(Long id);

    /**
     * 获得心理评估
     *
     * @param id 编号
     * @return 心理评估
     */
    MindDO getMind(Long id);

    /**
     * 获得心理评估列表
     *
     * @param ids 编号
     * @return 心理评估列表
     */
    List<MindDO> getMindList(Collection<Long> ids);

    /**
     * 获得心理评估分页
     *
     * @param pageReqVO 分页查询
     * @return 心理评估分页
     */
    PageResult<MindDO> getMindPage(MindPageReqVO pageReqVO);

    /**
     * 获得心理评估列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 心理评估列表
     */
    List<MindDO> getMindList(MindCreateReqVO createReqVO);

}
