package com.thj.boot.module.business.pojo.puncture.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PuncturePageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 血管通路id
     */
    private Long bloodId;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 日期
     */
    private Date startTime;
    /**
     * A端穿刺方法（字典获取）
     */
    private String aMethod;
    /**
     * A端穿刺类型（字典获取）
     */
    private String aType;
    /**
     * A端穿刺型号（字典获取）
     */
    private String aModel;
    /**
     * A端扣眼方法（字典获取）
     */
    private String aButtonhole;
    /**
     * A端更换理由（字典获取）
     */
    private String aReason;
    /**
     * V端穿刺方法（字典获取）
     */
    private String vMethod;
    /**
     * V端穿刺类型（字典类型）
     */
    private String vType;
    /**
     * V端穿刺型号（字典类型）
     */
    private String vModel;
    /**
     * V端扣眼方法（字典获取）
     */
    private String vButtonhole;
    /**
     * V端更换理由（字典类型）
     */
    private String vReason;
    /**
     * A端穿刺点距吻合口距离
     */
    private String aAnastomose;
    /**
     * ATV端两穿刺点间距离
     */
    private String atvAnastomose;
    /**
     * V端位于分支血管0-是，1-否（字典获取）
     */
    private String vStatus;
    /**
     * 一次成功0-是，1-否（字典获取）
     */
    private String onceSuccess;
    /**
     * 静脉压
     */
    private String venousPressure;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 备注
     */
    private String remark;
    /**
     * 穿刺护士
     */
    private Long userId;
    /**
     * 穿刺护士职称（字典获取）
     */
    private String professional;
    /**
     * 压迫止血时间
     */
    private Date oppressTime;
    /**
     * 红色a端绑带长度
     */
    private String redALength;
    /**
     * 蓝色v端绑带长度
     */
    private String blueVLength;
    /**
     * 手腕周长
     */
    private String wrist;
    /**
     * 红色棉球起点（字典获取）
     */
    private String redBall;
    /**
     * 蓝色棉球起点（字典获取）
     */
    private String blueBall;
    /**
     * 红色止血时间
     */
    private String redStanch;
    /**
     * 蓝色止血时间
     */
    private String blueStanch;
    /**
     * 红色止血刻度
     */
    private String redScale;
    /**
     * 蓝色止血刻度
     */
    private String blueScale;
    /**
     * 红色调整刻度
     */
    private String redAdjust;
    /**
     * 蓝色调整刻度
     */
    private String blueAdjust;

}
