package com.thj.boot.module.business.service.project;

import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.project.ProjectConvert;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.dal.mapper.project.ProjectMapper;
import com.thj.boot.module.business.pojo.project.vo.ProjectCreateReqVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectPageReqVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private ProjectMapper projectMapper;

    @Override
    public Long createProject(ProjectCreateReqVO createReqVO) {
        //检测项目编码唯一性
        checkCode(createReqVO.getCode(), 0);
        // 插入
        ProjectDO project = ProjectConvert.INSTANCE.convert(createReqVO);
        projectMapper.insert(project);
        // 返回
        return project.getId();
    }

    private void checkCode(String code, Integer i) {
        Long count = projectMapper.selectCount(ProjectDO::getCode, code);
        if (count > 0 && 0 == i) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        } else if (count > 1) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        }
    }

    @Override
    public void updateProject(ProjectUpdateReqVO updateReqVO) {
        //检测项目编码唯一性
        checkCode(updateReqVO.getCode(), 1);
        // 更新
        ProjectDO updateObj = ProjectConvert.INSTANCE.convert(updateReqVO);
        projectMapper.updateById(updateObj);
    }

    @Override
    public void deleteProject(Long id) {
        // 删除
        projectMapper.deleteById(id);
    }


    @Override
    public ProjectDO getProject(Long id) {
        return projectMapper.selectById(id);
    }


    @Override
    public PageResult<ProjectDO> getProjectPage(ProjectPageReqVO pageReqVO) {
        return projectMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProjectDO> getProjectList(ProjectCreateReqVO createReqVO) {
        return projectMapper.selectList(createReqVO);
    }

}
