package com.thj.boot.module.business.convert.facility;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityCreateReqVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityRespVO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 设备-机号设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FacilityConvert {

    FacilityConvert INSTANCE = Mappers.getMapper(FacilityConvert.class);

    FacilityDO convert(FacilityCreateReqVO bean);

    FacilityDO convert(FacilityUpdateReqVO bean);

    FacilityRespVO convert(FacilityDO bean);

    List<FacilityRespVO> convertList(List<FacilityDO> list);

    PageResult<FacilityRespVO> convertPage(PageResult<FacilityDO> page);


}
