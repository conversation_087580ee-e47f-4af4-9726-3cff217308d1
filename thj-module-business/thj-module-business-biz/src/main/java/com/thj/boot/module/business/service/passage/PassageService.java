package com.thj.boot.module.business.service.passage;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.passage.PassageDO;
import com.thj.boot.module.business.pojo.passage.vo.PassageCreateReqVO;
import com.thj.boot.module.business.pojo.passage.vo.PassagePageReqVO;
import com.thj.boot.module.business.pojo.passage.vo.PassageUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 通路评估 Service 接口
 *
 * <AUTHOR>
 */
public interface PassageService {

    /**
     * 创建通路评估
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPassage( PassageCreateReqVO createReqVO);

    /**
     * 更新通路评估
     *
     * @param updateReqVO 更新信息
     */
    void updatePassage( PassageUpdateReqVO updateReqVO);

    /**
     * 删除通路评估
     *
     * @param id 编号
     */
    void deletePassage(Long id);

    /**
     * 获得通路评估
     *
     * @param id 编号
     * @return 通路评估
     */
    PassageDO getPassage(Long id);

    /**
     * 获得通路评估列表
     *
     * @param ids 编号
     * @return 通路评估列表
     */
    List<PassageDO> getPassageList(Collection<Long> ids);

    /**
     * 获得通路评估分页
     *
     * @param pageReqVO 分页查询
     * @return 通路评估分页
     */
    PageResult<PassageDO> getPassagePage(PassagePageReqVO pageReqVO);

    /**
     * 获得通路评估列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 通路评估列表
     */
    List<PassageDO> getPassageList(PassageCreateReqVO createReqVO);

}
