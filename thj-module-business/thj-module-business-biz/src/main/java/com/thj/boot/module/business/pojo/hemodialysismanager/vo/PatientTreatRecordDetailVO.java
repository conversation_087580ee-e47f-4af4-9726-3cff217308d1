package com.thj.boot.module.business.pojo.hemodialysismanager.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.common.pojo.PageParam;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PatientTreatRecordDetailVO implements Serializable {

    /**
     * 序号
     */
    @ExcelProperty("序号")
    private int index;

    /**
     * 中心名称
     */
    @ExcelProperty("中心名称")
    private String deptName;

    /**
     * 患者姓名
     */
    @ExcelProperty("患者姓名")
    private String patientName;

    /**
     * 透析时间
     */
    @ExcelProperty("透析时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date hemodialysisTime;

    /**
     * 上机时间
     */
    @ExcelProperty("上机时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    @ColumnWidth(15)
    private Date startDialyzeTime;

    /**
     * 下机时间
     */
    @ExcelProperty("下机时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    @ColumnWidth(15)
    private Date endDialyzeTime;

}
