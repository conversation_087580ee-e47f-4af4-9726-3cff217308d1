package com.thj.boot.module.business.convert.autologousartificial;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.autologousartificial.AutologousArtificialDO;
import com.thj.boot.module.business.pojo.autologousartificial.vo.AutologousArtificialCreateReqVO;
import com.thj.boot.module.business.pojo.autologousartificial.vo.AutologousArtificialRespVO;
import com.thj.boot.module.business.pojo.autologousartificial.vo.AutologousArtificialUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 自体内瘥或人工血管穿刺 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AutologousArtificialConvert {

    AutologousArtificialConvert INSTANCE = Mappers.getMapper(AutologousArtificialConvert.class);

    AutologousArtificialDO convert(AutologousArtificialCreateReqVO bean);

    AutologousArtificialDO convert(AutologousArtificialUpdateReqVO bean);

    AutologousArtificialRespVO convert(AutologousArtificialDO bean);

    List<AutologousArtificialRespVO> convertList(List<AutologousArtificialDO> list);

    PageResult<AutologousArtificialRespVO> convertPage(PageResult<AutologousArtificialDO> page);


}
