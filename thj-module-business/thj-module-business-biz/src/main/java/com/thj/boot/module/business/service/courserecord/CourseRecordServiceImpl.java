package com.thj.boot.module.business.service.courserecord;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.DateUtils;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.convert.courserecord.CourseRecordConvert;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.courserecord.CourseRecordMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordUpdateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 病程记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CourseRecordServiceImpl implements CourseRecordService {

    @Resource
    private CourseRecordMapper courseRecordMapper;

    @Resource
    private PatientMapper patientMapper;

    @Autowired
    private SpringTemplateEngine springTemplateEngine;

    @Autowired
    private BloodRoadMapper bloodRoadMapper;

    @Autowired
    private VascularAccessMapper vascularAccessMapper;

    @Autowired
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Override
    public Long createCourseRecord(CourseRecordCreateReqVO createReqVO) {
        // 插入
        CourseRecordDO courseRecord = CourseRecordConvert.INSTANCE.convert(createReqVO);
        setPatientInfo(courseRecord);
        courseRecordMapper.insert(courseRecord);
        // 返回
        return courseRecord.getId();
    }



    @Override
    public void updateCourseRecord(CourseRecordUpdateReqVO updateReqVO) {
        // 更新
        CourseRecordDO updateObj = CourseRecordConvert.INSTANCE.convert(updateReqVO);
        if (CollUtil.isNotEmpty(updateReqVO.getInfraResultList())) {
            //附件ID集合
            updateObj.setInfraIds(StringUtils.join(updateReqVO.getInfraResultList().stream().map(InfraResult::getId).collect(Collectors.toList()), StrUtil.COMMA));
        }
        setPatientInfo(updateObj);
        courseRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteCourseRecord(Long id) {
        // 删除
        courseRecordMapper.deleteById(id);
    }


    @Override
    public CourseRecordDO getCourseRecord(Long id) {
        return courseRecordMapper.selectById(id);
    }

    @Override
    public List<CourseRecordDO> getCourseRecordList(Collection<Long> ids) {
        return courseRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<CourseRecordDO> getCourseRecordPage(CourseRecordPageReqVO pageReqVO) {
        return courseRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CourseRecordDO> getCourseRecordList(CourseRecordCreateReqVO createReqVO) {
        return courseRecordMapper.selectList(createReqVO);
    }

    @Override
    public void generateSummary(CourseRecordCreateReqVO createReqVO, HttpServletResponse response) {
        String fileName = "quarterly_summary";

        Map<String,Object> map = new HashMap<>();
        // 查询基本信息
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        if (StringUtils.isNotNull(patientDO)) {
            Date receiveTime = patientDO.getReceiveTime();
            if (StringUtils.isNotNull(receiveTime)) {
                Calendar instance = Calendar.getInstance();
                instance.setTime(receiveTime);
                int year = instance.get(Calendar.YEAR);
                int month = instance.get(Calendar.MONTH) + 1;
                int day = instance.get(Calendar.DAY_OF_MONTH);
                map.put("firstYear",year);
                map.put("firstMonth",month);
                map.put("firstDay",day);
                map.put("dialysisAge",patientDO.getDialyzeAge());
            }
            BloodRoadDO bloodRoadDO = bloodRoadMapper.selectOne(new LambdaQueryWrapperX<BloodRoadDO>()
                    .eq(BloodRoadDO::getPatientId, createReqVO.getPatientId())
                    .orderByDesc(BloodRoadDO::getCreateTime)
                    .last("limit 1"));
            if (StringUtils.isNotNull(bloodRoadDO)) {
                Long part = bloodRoadDO.getPart();
                VascularAccessDO vascularAccessDO = vascularAccessMapper.selectById(part);
                Long accessRoad = bloodRoadDO.getAccessRoad();
                VascularAccessDO vascularAccessDO1 = vascularAccessMapper.selectById(accessRoad);
                if (StringUtils.isNotNull(vascularAccessDO) && StringUtils.isNotNull(vascularAccessDO1)) {
                    map.put("blood",vascularAccessDO.getName() + "-" + vascularAccessDO1.getName());
                }
            }

            // 获取hd数量
            LocalDate now = LocalDate.now();
            int month = now.getMonthValue();
            Date startTime = null;
            Date endTime = null;

            if (month >= 1 && month <= 3) {
                startTime = DateUtils.buildTime(2025,1,1);
                endTime = DateUtils.buildTime(2025,3,31);
            } else if (month >= 4 && month <= 6) {
                startTime = DateUtils.buildTime(2025,4,1);
                endTime = DateUtils.buildTime(2025,6,30);
            } else if (month >= 7 && month <= 9) {
                startTime = DateUtils.buildTime(2025,7,1);
                endTime = DateUtils.buildTime(2025,9,30);
            } else {
                startTime = DateUtils.buildTime(2025,10,1);
                endTime = DateUtils.buildTime(2025,12,31);
            }
            List<HemodialysisManagerDO> hemodialysisManagerDOS = hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                    .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                    .between(HemodialysisManagerDO::getHemodialysisTime, startTime, endTime));
            if (!CollectionUtils.isEmpty(hemodialysisManagerDOS)) {
                List<HemodialysisManagerDO> collect = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "1".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    map.put("hdNum",collect.size());
                }

                List<HemodialysisManagerDO> collect1 = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "2".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect1)) {
                    map.put("hdfNum",collect1.size());
                }

                List<HemodialysisManagerDO> collect2 = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "3".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect2)) {
                    map.put("hdpNum",collect2.size());
                }
            }

        }
        map.put("name","小红");
        map.put("season","4");
        map.put("firstYear","2024");
        map.put("firstMonth","5");
        map.put("firstDay","31");

        // 设置数据
        Context context = new Context();
        context.setVariables(map);
        String process = springTemplateEngine.process(fileName, context);

        // 下载word
        try {
            byte[] bytes = process.getBytes(StandardCharsets.UTF_8);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/msword");
            response.setHeader("Access-Control-Expose-Headers","Content-disposition");
            response.setHeader("Content-disposition","attachment; filename=" +
                    URLEncoder.encode(fileName.concat(".doc"), "UTF-8"));
            ServletOutputStream out = response.getOutputStream();

            out.write(bytes);
            out.flush();
            out.close();
        }catch(Exception e){
            e.printStackTrace();
        }

    }

    private void setPatientInfo(CourseRecordDO courseRecord) {
        if(StringUtils.isEmpty(courseRecord.getPatientName())){
            PatientDO patientDO = patientMapper.selectById(courseRecord.getPatientId());
            if(patientDO != null){
                courseRecord.setPatientName(patientDO.getName());
                courseRecord.setPatientNickName(patientDO.getSpellName());
            }
        }
    }

}
