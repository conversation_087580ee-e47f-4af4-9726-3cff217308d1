package com.thj.boot.module.business.pojo.healthcareregistration.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HealthcareRegistrationPageReqVO extends PageParam {

    /**
     * 序号
     */
    private Long id;
    /**
     * 日期
     */
    private LocalDateTime date;
    /**
     * 医护姓名
     */
    private String healthcareName;
    /**
     * 健康码
     */
    private String healthCode;
    /**
     * 行程码
     */
    private String travelCode;
    /**
     * 核酸检测
     */
    private String nucleicAcidTest;
    /**
     * 体温(第1次)
     */
    private String temperature1st;
    /**
     * 体温(第2次)
     */
    private String temperature2nd;
    /**
     * 症状
     */
    private String symptoms;
    /**
     * 流行病学史
     */
    private String epidemiologicalHistory;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 医护人员id
     */
    private Long healthcareId;

}
