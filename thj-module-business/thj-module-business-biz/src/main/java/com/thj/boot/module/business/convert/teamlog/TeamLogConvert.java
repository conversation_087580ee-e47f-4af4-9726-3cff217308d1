package com.thj.boot.module.business.convert.teamlog;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.teamlog.TeamLogDO;
import com.thj.boot.module.business.pojo.teamlog.TeamLogCreateReqVO;
import com.thj.boot.module.business.pojo.teamlog.TeamLogRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 位点标记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TeamLogConvert {

    TeamLogConvert INSTANCE = Mappers.getMapper(TeamLogConvert.class);


    List<TeamLogDO> convertList(List<TeamLogCreateReqVO> createReqVOS);

    PageResult<TeamLogRespVO> convertPage(PageResult<TeamLogDO> teamLogDOPageResult);
}
