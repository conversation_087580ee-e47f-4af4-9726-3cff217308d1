package com.thj.boot.module.business.service.mind;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.mind.MindConvert;
import com.thj.boot.module.business.dal.datado.mind.MindDO;
import com.thj.boot.module.business.dal.mapper.mind.MindMapper;
import com.thj.boot.module.business.pojo.mind.vo.MindCreateReqVO;
import com.thj.boot.module.business.pojo.mind.vo.MindPageReqVO;
import com.thj.boot.module.business.pojo.mind.vo.MindUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 心理评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MindServiceImpl implements MindService {

    @Resource
    private MindMapper mindMapper;

    @Override
    public Long createMind(MindCreateReqVO createReqVO) {
        // 插入
        MindDO mind = MindConvert.INSTANCE.convert(createReqVO);
        mindMapper.insert(mind);
        // 返回
        return mind.getId();
    }

    @Override
    public void updateMind(MindUpdateReqVO updateReqVO) {
        // 更新
        MindDO updateObj = MindConvert.INSTANCE.convert(updateReqVO);
        mindMapper.updateById(updateObj);
    }

    @Override
    public void deleteMind(Long id) {
        // 删除
        mindMapper.deleteById(id);
    }


    @Override
    public MindDO getMind(Long id) {
        return mindMapper.selectById(id);
    }

    @Override
    public List<MindDO> getMindList(Collection<Long> ids) {
        return mindMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MindDO> getMindPage(MindPageReqVO pageReqVO) {
        return mindMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MindDO> getMindList(MindCreateReqVO createReqVO) {
        return mindMapper.selectList(createReqVO);
    }

}
