package com.thj.boot.module.business.pojo.hemodialysismanager.vo;

import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HemodialysisManagerRespVO extends HemodialysisManagerBaseVO {

    /**
     * 类型名称
     */
    private String instrumentName;
    /**
     * 规格名称
     */
    private String specification;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 批号
     */
    private String lotNumber;

    /**
     * 是否血滤机，0-否，1-是
     */
    private Integer state;

    /**
     * 实际置换量（L）
     */
    private String actualExchangeAmount;
    /**
     * 抗凝剂名称
     */
    private String drugNames;
    /**
     * 穿刺针
     */
    private List<String> punctureNeedles;
    /**
     * 人工肾
     */
    private String artificialKidney;
    /**
     * 执行医嘱
     */
    private String adviceState;
    /**
     * 抗凝剂名称
     */
    private List<ContradictRespVO> contradictRespVOS = Lists.newArrayList();

    private String dryWeight;

}
