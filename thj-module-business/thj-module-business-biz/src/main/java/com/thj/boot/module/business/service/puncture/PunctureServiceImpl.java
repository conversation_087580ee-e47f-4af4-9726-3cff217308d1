package com.thj.boot.module.business.service.puncture;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.puncture.PunctureConvert;
import com.thj.boot.module.business.dal.datado.puncture.PunctureDO;
import com.thj.boot.module.business.dal.mapper.puncture.PunctureMapper;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureCreateReqVO;
import com.thj.boot.module.business.pojo.puncture.vo.PuncturePageReqVO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 穿刺信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PunctureServiceImpl implements PunctureService {

    @Resource
    private PunctureMapper punctureMapper;

    @Override
    public Long createPuncture(PunctureCreateReqVO createReqVO) {
        // 插入
        PunctureDO puncture = PunctureConvert.INSTANCE.convert(createReqVO);
        punctureMapper.insert(puncture);
        // 返回
        return puncture.getId();
    }

    @Override
    public void updatePuncture(PunctureUpdateReqVO updateReqVO) {
        // 更新
        PunctureDO updateObj = PunctureConvert.INSTANCE.convert(updateReqVO);
        punctureMapper.updateById(updateObj);
    }

    @Override
    public void deletePuncture(Long id) {
        // 删除
        punctureMapper.deleteById(id);
    }


    @Override
    public PunctureDO getPuncture(Long id) {
        return punctureMapper.selectById(id);
    }

    @Override
    public List<PunctureDO> getPunctureList(Collection<Long> ids) {
        return punctureMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PunctureDO> getPuncturePage(PuncturePageReqVO pageReqVO) {
        return punctureMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PunctureDO> getPunctureList(PunctureCreateReqVO createReqVO) {
        return punctureMapper.selectList(createReqVO);
    }

}
