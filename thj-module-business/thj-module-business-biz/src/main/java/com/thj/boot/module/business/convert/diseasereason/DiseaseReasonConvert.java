package com.thj.boot.module.business.convert.diseasereason;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonCreateReqVO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonRespVO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 疾病诊断 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DiseaseReasonConvert {

    DiseaseReasonConvert INSTANCE = Mappers.getMapper(DiseaseReasonConvert.class);

    DiseaseReasonDO convert(DiseaseReasonCreateReqVO bean);

    DiseaseReasonDO convert(DiseaseReasonUpdateReqVO bean);

    DiseaseReasonRespVO convert(DiseaseReasonDO bean);

    List<DiseaseReasonRespVO> convertList(List<DiseaseReasonDO> list);

    PageResult<DiseaseReasonRespVO> convertPage(PageResult<DiseaseReasonDO> page);


    List<DiseaseReasonDO> convertList2(List<DiseaseReasonCreateReqVO> collect);
}
