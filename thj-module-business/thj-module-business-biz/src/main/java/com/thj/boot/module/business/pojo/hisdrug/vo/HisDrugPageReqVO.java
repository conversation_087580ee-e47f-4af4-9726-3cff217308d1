package com.thj.boot.module.business.pojo.hisdrug.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HisDrugPageReqVO extends PageParam {


    /**
     * 药品名称
     */
    private String fDrugName;
    /**
     * 药品编号
     */
    private String fDrugNumber;
    /**
     * 药品规格
     */
    private String fDrugSpec;
    /**
     * 制剂单位
     */
    private String fPreparaUnit;
    /**
     * 剂型
     */
    private String fDrugForm;
    /**
     * 包装单位
     */
    private String fPackUnit;
    /**
     * 包装数量
     */
    private String fDrugFormCount;
    /**
     * 药品类别
     */
    private String fDrugCategory;
    /**
     * 医保收费等级
     */
    private String fDrugDirectoryType;
    /**
     * 医保编码
     */
    private String fDrugDirectoryCode;
    /**
     * 零售价
     */
    private String fDrugPrice;
    /**
     * 零售单位
     */
    private String fRetailUnit;
    /**
     * 最小剂量
     */
    private String fDmin;
    /**
     * 一次用量
     */
    private String fOnceUsing;
    /**
     * 剂量单位
     */
    private String fSpecUnit;
    /**
     * 生产厂家
     */
    private String fDrugChangjia;
    /**
     * 批准文号
     */
    private String fApprovalNumber;
    /**
     * 限制使用标志
     */
    private String fLimitUseSign;
    /**
     * 限制使用范围
     */
    private String fLimitUseScope;
    /**
     * 透析中心ID
     */
    private String fHosipital;
    /**
     * 有效标志
     */
    private String fEnabledMark;
    /**
     * 有效名称
     */
    private String fEnabledName;
    /**
     * 审核标志
     */
    private String fIsAudit;
    /**
     * 政府指导价/采购价
     */
    private String fPurchasePrice;
    /**
     * 审核时间
     */
    private Long fAuditTime;
    /**
     * 审核零售价
     */
    private String fAuditPrice;
    /**
     * 审核医保编码
     */
    private String fAuditMedinsCode;
    /**
     * 库存单位
     */
    private String fStockUnit;
    /**
     * 库存单位转换
     */
    private String fStockDmin;
    /**
     * 备注
     */
    private String remark;

    private String keyword;

    private Long deptId;

}
