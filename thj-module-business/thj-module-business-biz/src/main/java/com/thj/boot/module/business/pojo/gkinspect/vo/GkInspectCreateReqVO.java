package com.thj.boot.module.business.pojo.gkinspect.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GkInspectCreateReqVO extends GkInspectBaseVO {

    /**
     *
     */
    private Long id;
    /**
     * 被检查者id
     */
    private Long inspecteeId;
    /**
     * 被检查者名称
     */
    private String inspecteeName;
    /**
     * 检查人ID
     */
    private Long inspectId;

    private Long patientId;

    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 检查表类型
     */
    private String inspectType;
    /**
     * 检查用时
     */
    private Integer inspectTime;

    /**
     * 检查总次数
     */
    private int checkTotal;
    /**
     * 内容json
     */
    private String content;
    /**
     * 正确次数
     */
    private Integer correct;
    /**
     * 错误次数
     */
    private Integer errCount;
    /**
     * 检查区域
     */
    private String inspectionArea;

    private String inspectionDate;

    private Long evaluatorId;

    private String remark;

    private Long operatorId;



}
