package com.thj.boot.module.business.convert.infra;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.infra.InfraDO;
import com.thj.boot.module.business.pojo.infra.vo.InfraCreateReqVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraRespVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/10/6 13:53
 * @description
 */
@Mapper
public interface InfraConvert {

    InfraConvert INSTANCE = Mappers.getMapper(InfraConvert.class);

    InfraDO convert(InfraCreateReqVO bean);

    InfraDO convert(InfraUpdateReqVO bean);

    InfraRespVO convert(InfraDO bean);

    List<InfraRespVO> convertList(List<InfraDO> list);

    PageResult<InfraRespVO> convertPage(PageResult<InfraDO> page);


}
