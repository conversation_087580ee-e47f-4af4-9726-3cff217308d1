package com.thj.boot.module.business.pojo.departmentControl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreChartVo<T> implements Serializable {
    private String name;

    private T value;

    private Double rate;

    public PreChartVo(String name, T value) {
        this.name = name;
        this.value = value;
    }
}
