package com.thj.boot.module.business.pojo.dialysisprotocol.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 个性化-透析方案 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DialysisProtocolBaseVO implements Serializable {

    /**
     *
     */
    private Long id;
    /**
     * 透析方案id
     */
    private Long dialyzeId;
    /**
     * 透析时长小时
     */
    private String duration;
    /**
     * 透析时长分钟
     */
    private String durationMin;
    /**
     * 置换方式
     */
    private String substituteMode;

    /**
     * 置换量
     */
    private String substituteTotal;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfume;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 透析液流量(ml/min)
     */
    private String dialysateFlowRate;
    /**
     * 钾(mmol/L)
     */
    private String potassium;
    /**
     * 配方钠(mmol/L)
     */
    private String formulaSodium;
    /**
     * 处方钠
     */
    private String prescriptionSodium;
    /**
     * 钙(mmol/L)
     */
    private String calcium;
    /**
     * 碳酸氢根(mmol/L)
     */
    private String bicarbonate;
    /**
     * 葡萄糖(mmol/L)
     */
    private String glucose;
    /**
     * 起始钠
     */
    private String initialSodium;
    /**
     * 钠曲线
     */
    private String sodiumCurve;
    /**
     * 超滤曲线
     */
    private String ultrafiltrationCurve;
    /**
     * 抗凝剂公式
     */
    private String anticFormula;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者透析方案主键id
     */
    private Long patientDialyzeId;
    /**
     * 0-个性化透析处方，1-患者透析处方,2-血液透析
     */
    private Integer protocolType;
    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 透析时间
     */
    private Date hemodialysisTime;

    /**
     * 更新者
     */
    private String updater;

    private Long protocolId;

}
