package com.thj.boot.module.business.pojo.morse.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MorsePageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 危险评估
     */
    private String danger;
    /**
     * 预防措施
     */
    private String measure;
    /**
     * 预防效果
     */
    private String effect;
    /**
     * 透析间期跌倒事件
     */
    private String dialysis;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者拼音
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;

}
