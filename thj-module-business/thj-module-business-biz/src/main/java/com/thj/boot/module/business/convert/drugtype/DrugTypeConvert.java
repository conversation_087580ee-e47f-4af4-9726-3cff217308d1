package com.thj.boot.module.business.convert.drugtype;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.drugtype.DrugTypeDO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeCreateReqVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 药品字典分类 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DrugTypeConvert {

    DrugTypeConvert INSTANCE = Mappers.getMapper(DrugTypeConvert.class);

    DrugTypeDO convert(DrugTypeCreateReqVO bean);

    DrugTypeDO convert(DrugTypeUpdateReqVO bean);

    DrugTypeRespVO convert(DrugTypeDO bean);

    List<DrugTypeRespVO> convertList(List<DrugTypeDO> list);

    PageResult<DrugTypeRespVO> convertPage(PageResult<DrugTypeDO> page);


}
