package com.thj.boot.module.business.pojo.repairregister.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RepairRegisterPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 报修日期
     */
    private Date repairTime;
    /**
     * 出发时间
     */
    private Date startTime;
    /**
     * 到达时间
     */
    private Date endTime;
    /**
     * 完成时间
     */
    private Date successTime;
    /**
     * 总里程
     */
    private String totalMileage;
    /**
     * 故障发生阶段
     */
    private String phase;
    /**
     * 故障代码
     */
    private String faultCode;
    /**
     * 故障描述
     */
    private String faultDescription;
    /**
     * 原因分析
     */
    private String cause;
    /**
     * 处理过程
     */
    private String processing;
    /**
     * 故障排除
     */
    private String troubleshooting;
    /**
     * 图片地址
     */
    private String imgUrl;
    /**
     * 设备id
     */
    private Long managerId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 门店
     */
    private Long deptId;
    /**
     * 1-透析机,2-水处理机,3-CCDS,4-CDDS,5-其他
     */
    private String type;

}
