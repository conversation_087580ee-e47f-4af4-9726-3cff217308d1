package com.thj.boot.module.business.convert.project;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.pojo.project.vo.ProjectCreateReqVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectRespVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 项目 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectConvert {

    ProjectConvert INSTANCE = Mappers.getMapper(ProjectConvert.class);

    ProjectDO convert(ProjectCreateReqVO bean);

    ProjectDO convert(ProjectUpdateReqVO bean);

    ProjectRespVO convert(ProjectDO bean);

    List<ProjectRespVO> convertList(List<ProjectDO> list);

    PageResult<ProjectRespVO> convertPage(PageResult<ProjectDO> page);

    List<ProjectCreateReqVO> convertList02(List<ProjectDO> list);

}
