package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 透析充分性表单
 */
@Data
public class DialysisAdequacyForm {

    /**
     * 年份
     */
    @NotNull
    @JsonProperty("ADEQ_YEAR")
    private String year;

    /**
     * 时间维度
     */
    @NotNull
    @JsonProperty("ADEQ_DIMENSION")
    private String dimension;

    /**
     * 患者情况
     */
    @JsonProperty("ADEQ_WEIGHT_CONDITION")
    private String weightCondition;

    /**
     * 干体重 (10-200kg)
     */
    @NotNull
    @JsonProperty("ADEQ_WEIGHT_VALUE")
    private String weightValue;

    /**
     * 干体重是否达标
     */
    @NotNull
    @JsonProperty("ADEQ_WEIGHT_ASSESSMENT")
    private String weightAssessment;

    /**
     * 是否手工录入 spKt/V
     */
    @JsonProperty("ADEQ_NONSYS_FLAG")
    private String nonsysFlag;

    /**
     * 透前尿素 (mmol/L)
     */
    @NotNull
    @JsonProperty("ADEQ_PREUREA")
    private String preurea;

    /**
     * 透后尿素 (mmol/L)
     */
    @NotNull
    @JsonProperty("ADEQ_POSUREA")
    private String posurea;

    /**
     * 透析时间（h/次）
     */
    @NotNull
    @JsonProperty("ADEQ_DURATION")
    private String duration;

    /**
     * 超滤量 (L)
     */
    @NotNull
    @JsonProperty("ADEQ_UF")
    private String uf;

    /**
     * URR (%)
     */
    @NotNull
    @JsonProperty("ADEQ_URR")
    private Double urr;

    /**
     * spKt/V
     */
    @NotNull
    @JsonProperty("ADEQ_KTV")
    private Double ktv;

}
