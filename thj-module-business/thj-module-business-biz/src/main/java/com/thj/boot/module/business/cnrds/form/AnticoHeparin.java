package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 普通肝素对象
 */
@Data
public class AnticoHeparin {

    /**
     * 首剂量
     */
    @JsonProperty("ANTICO_HEPARIN_INITIAL")
    private String heparinInitial;

    /**
     * 首剂量单位
     */
    @JsonProperty("ANTICO_HEPARIN_INITIAL_UNIT")
    private String heparinInitialUnit;

    /**
     * 追加速率
     */
    @JsonProperty("ANTICO_HEPARIN_INFUSION_RATE")
    private String heparinInfusionRate;

    /**
     * 追加时间（小时）
     */
    @JsonProperty("ANTICO_HEPARIN_INFUSION_HR")
    private String heparinInfusionHr;

    /**
     * 追加时间（分钟）
     */
    @JsonProperty("ANTICO_HEPARIN_INFUSION_MIN")
    private String heparinInfusionMin;

}
