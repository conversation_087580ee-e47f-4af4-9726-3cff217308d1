package com.thj.boot.module.business.pojo.facilitymanager.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FacilityManagerRespVO extends FacilityManagerBaseVO {
    /**
     * 使用次数
     */
    private Long useNumber;
    /**
     * 故障次数
     */
    private Long runFaultNumber;
    /**
     * 设备名称
     */
    private String facilityName;
    /**
     * 治疗模式
     */
    private String healMode;

    /**
     * 设备型号名称
     */
    private String facilityTypeName;

}
