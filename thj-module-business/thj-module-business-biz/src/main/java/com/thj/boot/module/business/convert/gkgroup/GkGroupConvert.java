package com.thj.boot.module.business.convert.gkgroup;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkgroup.GkGroupDO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupCreateReqVO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupRespVO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 分组 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkGroupConvert {

    GkGroupConvert INSTANCE = Mappers.getMapper(GkGroupConvert.class);

    GkGroupDO convert(GkGroupCreateReqVO bean);

    GkGroupDO convert(GkGroupUpdateReqVO bean);

    GkGroupRespVO convert(GkGroupDO bean);

    List<GkGroupRespVO> convertList(List<GkGroupDO> list);

    PageResult<GkGroupRespVO> convertPage(PageResult<GkGroupDO> page);


}
