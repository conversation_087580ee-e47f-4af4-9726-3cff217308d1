package com.thj.boot.module.business.convert.dialysisdetection;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.BloodPressureExcelVO;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.BloodPressureRecordExcelVO;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.BloodPressureVO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析检测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisDetectionConvert {

    DialysisDetectionConvert INSTANCE = Mappers.getMapper(DialysisDetectionConvert.class);

    DialysisDetectionDO convert(DialysisDetectionCreateReqVO bean);

    DialysisDetectionDO convert(DialysisDetectionUpdateReqVO bean);

    DialysisDetectionRespVO convert(DialysisDetectionDO bean);

    List<DialysisDetectionRespVO> convertList(List<DialysisDetectionDO> list);

    PageResult<DialysisDetectionRespVO> convertPage(PageResult<DialysisDetectionDO> page);


    List<BloodPressureExcelVO> convertExecl(List<BloodPressureVO> list);

    List<BloodPressureRecordExcelVO> convertExecl2(List<BloodPressureVO> list);
}
