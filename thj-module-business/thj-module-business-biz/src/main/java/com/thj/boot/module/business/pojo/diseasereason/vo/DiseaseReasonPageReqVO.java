package com.thj.boot.module.business.pojo.diseasereason.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DiseaseReasonPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 诊断日期
     */
    private Date reasonTime;
    /**
     * 主诉
     */
    private String mainSuit;
    /**
     * 诊断分类id
     */
    private Long reasonId;
    /**
     * 诊断名称
     */
    private String name;
    /**
     * 属性
     */
    private String stats;
    /**
     * 诊断意见
     */
    private String opinion;
    /**
     * 医生
     */
    private Long userId;
    /**
     * 康复日期
     */
    private Date recoveryTime;
    /**
     * 是否同步到透析单
     */
    private String sync;
    /**
     * 患者id
     */
    private Long patientId;

}
