package com.thj.boot.module.business.convert.areadisinfectiontable;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.areadisinfectiontable.AreaDisinfectionTableDO;
import com.thj.boot.module.business.pojo.areadisinfectiontable.vo.AreaDisinfectionTableCreateReqVO;
import com.thj.boot.module.business.pojo.areadisinfectiontable.vo.AreaDisinfectionTableRespVO;
import com.thj.boot.module.business.pojo.areadisinfectiontable.vo.AreaDisinfectionTableUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 区域消毒 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AreaDisinfectionTableConvert {

    AreaDisinfectionTableConvert INSTANCE = Mappers.getMapper(AreaDisinfectionTableConvert.class);

    AreaDisinfectionTableDO convert(AreaDisinfectionTableCreateReqVO bean);

    AreaDisinfectionTableDO convert(AreaDisinfectionTableUpdateReqVO bean);

    AreaDisinfectionTableRespVO convert(AreaDisinfectionTableDO bean);

    List<AreaDisinfectionTableRespVO> convertList(List<AreaDisinfectionTableDO> list);

    PageResult<AreaDisinfectionTableRespVO> convertPage(PageResult<AreaDisinfectionTableDO> page);


}
