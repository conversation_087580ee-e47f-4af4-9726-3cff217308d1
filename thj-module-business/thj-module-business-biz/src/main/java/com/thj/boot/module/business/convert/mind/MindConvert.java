package com.thj.boot.module.business.convert.mind;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.mind.MindDO;
import com.thj.boot.module.business.pojo.mind.vo.MindCreateReqVO;
import com.thj.boot.module.business.pojo.mind.vo.MindRespVO;
import com.thj.boot.module.business.pojo.mind.vo.MindUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 心理评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MindConvert {

    MindConvert INSTANCE = Mappers.getMapper(MindConvert.class);

    MindDO convert(MindCreateReqVO bean);

    MindDO convert(MindUpdateReqVO bean);

    MindRespVO convert(MindDO bean);

    List<MindRespVO> convertList(List<MindDO> list);

    PageResult<MindRespVO> convertPage(PageResult<MindDO> page);


}
