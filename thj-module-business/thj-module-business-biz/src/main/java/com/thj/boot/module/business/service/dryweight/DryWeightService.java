package com.thj.boot.module.business.service.dryweight;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightCreateReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightPageReqVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightRespVO;
import com.thj.boot.module.business.pojo.dryweight.vo.DryWeightUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 干体重 Service 接口
 *
 * <AUTHOR>
 */
public interface DryWeightService {

    /**
     * 创建干体重
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDryWeight( DryWeightCreateReqVO createReqVO);

    /**
     * 更新干体重
     *
     * @param updateReqVO 更新信息
     */
    void updateDryWeight( DryWeightUpdateReqVO updateReqVO);

    /**
     * 删除干体重
     *
     * @param id 编号
     */
    void deleteDryWeight(Long id);

    /**
     * 获得干体重
     *
     * @param id 编号
     * @return 干体重
     */
    DryWeightDO getDryWeight(Long id);

    /**
     * 获得干体重列表
     *
     * @param ids 编号
     * @return 干体重列表
     */
    List<DryWeightDO> getDryWeightList(Collection<Long> ids);

    /**
     * 获得干体重分页
     *
     * @param pageReqVO 分页查询
     * @return 干体重分页
     */
    PageResult<DryWeightRespVO> getDryWeightPage(DryWeightPageReqVO pageReqVO);

    /**
     * 获得干体重列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 干体重列表
     */
    List<DryWeightDO> getDryWeightList(DryWeightCreateReqVO createReqVO);

}
