package com.thj.boot.module.business.service.mottohard;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.mottohard.MottoHardDO;
import com.thj.boot.module.business.pojo.drug.vo.DrugRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardCreateReqVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardPageReqVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardRespVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 透析方案 Service 接口
 *
 * <AUTHOR>
 */
public interface MottoHardService {

    /**
     * 创建透析方案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMottoHard(MottoHardCreateReqVO createReqVO);

    /**
     * 更新透析方案
     *
     * @param updateReqVO 更新信息
     */
    void updateMottoHard(MottoHardUpdateReqVO updateReqVO);

    /**
     * 删除透析方案
     *
     * @param id 编号
     */
    void deleteMottoHard(Long id);

    /**
     * 获得透析方案
     *
     * @param id 编号
     * @return 透析方案
     */
    MottoHardRespVO getMottoHard(String dialyzeId);

    /**
     * 获得透析方案列表
     *
     * @param ids 编号
     * @return 透析方案列表
     */
    List<MottoHardDO> getMottoHardList(Collection<Long> ids);

    /**
     * 获得透析方案分页
     *
     * @param pageReqVO 分页查询
     * @return 透析方案分页
     */
    PageResult<MottoHardDO> getMottoHardPage(MottoHardPageReqVO pageReqVO);

    /**
     * 获得透析方案列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 透析方案列表
     */
    List<MottoHardDO> getMottoHardList(MottoHardCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date 2023/12/13 14:10
     * @Description 根据药品多个id获取对应的药品信息（只针对抗凝剂）
     **/
    List<DrugRespVO> getDrugList(String drugTypeIds);

    /***
     * <AUTHOR>
     * @date 2023/12/13 16:26
     * @Description 获取西药下的抗凝剂id
     **/
    List<DrugTypeRespVO> getDrugTypeList(Long id);

    /**
     * 查询药品字典his和手动新增
     */
    List<HisDrugDO> getDrugDictionary(String name);
}
