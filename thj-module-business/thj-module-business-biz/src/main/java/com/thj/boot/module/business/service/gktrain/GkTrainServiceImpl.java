package com.thj.boot.module.business.service.gktrain;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.gktrain.GkTrainConvert;
import com.thj.boot.module.business.dal.datado.gktrain.GkTrainDO;
import com.thj.boot.module.business.dal.mapper.gkinroles.GkInRolesMapper;
import com.thj.boot.module.business.dal.mapper.gktrain.GkTrainMapper;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainCreateReqVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainPageReqVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainRespVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Collection;
import java.util.List;

/**
 * 感控培训 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GkTrainServiceImpl implements GkTrainService {

    @Resource
    private GkTrainMapper gkTrainMapper;

    @Resource
    private GkInRolesMapper gkInRolesMapper;

    @Override
    public Long createGkTrain(GkTrainCreateReqVO createReqVO) {
        // 插入
        GkTrainDO gkTrain = GkTrainConvert.INSTANCE.convert(createReqVO);
        gkTrain.setPeopleNo(0);
        Calendar c = Calendar.getInstance();
        c.setTime(createReqVO.getPlanTime());
        gkTrain.setYear(String.valueOf(c.get(Calendar.YEAR)));
        gkTrain.setMouth(String.valueOf(c.get(Calendar.MONTH) + 1));
        gkTrainMapper.insert(gkTrain);
        gkInRolesMapper.insertBatch(createReqVO.getList());
        return gkTrain.getId();
    }


    @Override
    public void updateGkTrain(GkTrainDO gkTrainDO) {
        // 更新
        gkTrainMapper.updateById(gkTrainDO);
    }

    @Override
    public void deleteGkTrain(Long id) {
        // 校验存在
        // 删除
        gkTrainMapper.deleteById(id);
    }


    @Override
    public GkTrainDO getGkTrain(Long id) {
        return gkTrainMapper.selectById(id);
    }

    @Override
    public List<GkTrainDO> getGkTrainList(Collection<Long> ids) {
        return gkTrainMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GkTrainDO> getGkTrainPage(GkTrainPageReqVO pageReqVO) {
        return gkTrainMapper.selectPage(pageReqVO);
    }

    @Override
    public List<GkTrainRespVO> getMouthList(String year){


        return gkTrainMapper.queryYearList(year);
    }

    @Override
    public List<GkTrainDO> queryListOnMouth(GkTrainUpdateReqVO vo){
        return gkTrainMapper.queryListOnMouth(vo);
    }

    @Override
    public void updateSort(GkTrainUpdateReqVO updateReqVO){
        gkTrainMapper.updateBatch(updateReqVO.getList());
    }


}
