package com.thj.boot.module.business.pojo.consumablesReplacement.vo;

import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class ConsumablesReplacementReqVO {

    /**
     * 设备管理id
     */
    private Long id;

    /**
     * 序列号
     */
    private String code;

    /**
     * 设备名称id
     */
    private Long facilityNameId;

    /**
     * 设备型号id
     */
    private String facilityTypeId;

    /**
     * 透析机消毒液更换（有几个月没有更换）   -1：未清理过
     */
    private double liquidReplaceDiffMonths = -1;


    /**
     * 细菌过滤器更换（有几个月没有更换）   -1：未清理过
     */
    private double bacterialFilterDiffMonths = -1;

    /**
     * 空气滤网清洁（有几个月没有更换）   -1：未清理过
     */
    private double airFilterDiffMonths = -1;

}
