package com.thj.boot.module.business.pojo.anticoagulant.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

/**
* 抗凝剂类型 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AnticoagulantBaseVO extends BaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 透析方案id
     */
    private Long mottoHardId;
    /**
     * 抗凝剂类型(存入字典)
     */
    private String dictValue;
    /**
     * 首剂
     */
    private String firstDose;
    /**
     * 维持
     */
    private String maintain;
    /**
     * 追加1
     */
    private String addOne;
    /**
     * 追加2
     */
    private String addTwo;
    /**
     * 总量
     */
    private String total;
    /**
     * 备注
     */
    private String remark;
    /**
     * 冲洗量
     */
    private String rinse;
    /**
     * 时长
     */
    private String duration;

    private Boolean deleted;
}
