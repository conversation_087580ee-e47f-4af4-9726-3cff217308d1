package com.thj.boot.module.business.task;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.async.threads.ThreadPoolManager;
import com.thj.boot.module.business.async.his.HisContentAsync;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 11:15
 * @description
 */
@Component
@Slf4j
public class HisTask {

    @Resource
    private HisContentAsync hisContentAsync;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PatientMapper patientMapper;

    //每天凌晨1点执行一次
    @Scheduled(cron = "0 0 1 * * ?")
    public void sendTask() {
        List<DeptRespDTO> deptRespDTOS = deptApi.selectDeptList();
        if (CollectionUtil.isNotEmpty(deptRespDTOS)) {
            for (DeptRespDTO deptRespDTO : deptRespDTOS) {
                Long count = patientMapper.selectCount(PatientDO::getDeptId, deptRespDTO.getId());
                if (count == 0) {
                    continue;
                }
                log.info("同步{}门店", deptRespDTO.getId());
                //药品
                ThreadPoolManager.getInstance().execute(() -> {
                    hisContentAsync.asyncHisDrug(deptRespDTO.getHospitalId(), 1L, deptRespDTO.getId());
                });
                // 耗材
                ThreadPoolManager.getInstance().execute(() -> {
                    hisContentAsync.asyncHisConsumables(deptRespDTO.getHospitalId(), 1L, deptRespDTO.getId());
                });
                // 中心项目
                ThreadPoolManager.getInstance().execute(() -> {
                    hisContentAsync.asyncHisPrescriptionitem(deptRespDTO.getHospitalId(), 1L, deptRespDTO.getId());
                });
                // 套餐
                ThreadPoolManager.getInstance().execute(() -> {
                    hisContentAsync.asyncHisCombo(deptRespDTO.getHospitalId(), 1L, deptRespDTO.getId());
                });
            }
        }
    }


}
