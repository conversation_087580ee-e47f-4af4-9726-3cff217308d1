package com.thj.boot.module.business.service.jkmeans;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.jkmeans.JkMeansDO;
import com.thj.boot.module.business.pojo.jkmeans.vo.JkMeansPageReqVO;

import java.util.List;

/**
 * 健康教育-患者教育资料 Service 接口
 *
 * <AUTHOR>
 */
public interface JkMeansService {

    /**
     * 创建健康教育-患者教育资料
     *
     * @param jkMeansDO 创建信息
     * @return 编号
     */
    void createJkMeans(JkMeansDO jkMeansDO);

    /**
     * 更新健康教育-患者教育资料
     *
     * @param  更新信息
     */
    void updateJkMeans(JkMeansDO updateObj);

    /**
     * 删除健康教育-患者教育资料
     *
     * @param id 编号
     */
    void deleteJkMeans(Long id);

    /**
     * 获得健康教育-患者教育资料
     *
     * @param id 编号
     * @return 健康教育-患者教育资料
     */
    JkMeansDO getJkMeans(Long id);

    /**
     * 获得健康教育-患者教育资料列表
     *
     * @param resourceType 编号
     * @return 健康教育-患者教育资料列表
     */
    List<JkMeansDO> getJkMeansList(Long resourceType,String year);

    /**
     * 获得健康教育-患者教育资料分页
     *
     * @param pageReqVO 分页查询
     * @return 健康教育-患者教育资料分页
     */
    PageResult<JkMeansDO> getJkMeansPage(JkMeansPageReqVO pageReqVO);

    List<JkMeansDO> getMeansDOList(Long resourceType,String year);
}
