package com.thj.boot.module.business.convert.arteriovenous;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.arteriovenous.ArteriovenousDO;
import com.thj.boot.module.business.pojo.arteriovenous.vo.ArteriovenousCreateReqVO;
import com.thj.boot.module.business.pojo.arteriovenous.vo.ArteriovenousRespVO;
import com.thj.boot.module.business.pojo.arteriovenous.vo.ArteriovenousUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 内瘘拔针 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ArteriovenousConvert {

    ArteriovenousConvert INSTANCE = Mappers.getMapper(ArteriovenousConvert.class);

    ArteriovenousDO convert(ArteriovenousCreateReqVO bean);

    ArteriovenousDO convert(ArteriovenousUpdateReqVO bean);

    ArteriovenousRespVO convert(ArteriovenousDO bean);

    List<ArteriovenousRespVO> convertList(List<ArteriovenousDO> list);

    PageResult<ArteriovenousRespVO> convertPage(PageResult<ArteriovenousDO> page);


}
