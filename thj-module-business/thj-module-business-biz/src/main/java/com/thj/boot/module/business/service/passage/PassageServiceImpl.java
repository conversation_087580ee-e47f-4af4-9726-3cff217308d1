package com.thj.boot.module.business.service.passage;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.passage.PassageConvert;
import com.thj.boot.module.business.dal.datado.passage.PassageDO;
import com.thj.boot.module.business.dal.mapper.passage.PassageMapper;
import com.thj.boot.module.business.pojo.passage.vo.PassageCreateReqVO;
import com.thj.boot.module.business.pojo.passage.vo.PassagePageReqVO;
import com.thj.boot.module.business.pojo.passage.vo.PassageUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 通路评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PassageServiceImpl implements PassageService {

    @Resource
    private PassageMapper passageMapper;

    @Override
    public Long createPassage(PassageCreateReqVO createReqVO) {
        // 插入
        PassageDO passage = PassageConvert.INSTANCE.convert(createReqVO);
        passageMapper.insert(passage);
        // 返回
        return passage.getId();
    }

    @Override
    public void updatePassage(PassageUpdateReqVO updateReqVO) {
        // 更新
        PassageDO updateObj = PassageConvert.INSTANCE.convert(updateReqVO);
        passageMapper.updateById(updateObj);
    }

    @Override
    public void deletePassage(Long id) {
        // 删除
        passageMapper.deleteById(id);
    }


    @Override
    public PassageDO getPassage(Long id) {
        return passageMapper.selectById(id);
    }

    @Override
    public List<PassageDO> getPassageList(Collection<Long> ids) {
        return passageMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PassageDO> getPassagePage(PassagePageReqVO pageReqVO) {
        return passageMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PassageDO> getPassageList(PassageCreateReqVO createReqVO) {
        return passageMapper.selectList(createReqVO);
    }

}
