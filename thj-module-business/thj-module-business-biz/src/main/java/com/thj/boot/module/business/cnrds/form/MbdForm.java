package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * MBD干预药 表单
 */
@Data
public class MbdForm {

    // 年
    @JsonProperty("MBD_YEAR")
    @NotNull
    private String mbdYear;

    // 时间维度
    @JsonProperty("MBD_DIMENSION")
    @NotNull
    private String mbdDimension;

    // 有无变化
    @JsonProperty("MBD_CHANGE_YN")
    @NotNull
    private String mbdChangeYN;

    // 是否使用
    @JsonProperty("MBD_TREATMENT")
    @NotNull
    private String mbdTreatment;

    // 维生素D及衍生物_是否使用
    @JsonProperty("MBD_VITD_TREATMENT")
    @NotNull
    private String mbdVitdTreatment;

    // 维生素D及衍生物
    @JsonProperty("MBD_VITD_TYPE")
    private List<String> mbdVitdType;

    // 含钙磷结合剂_是否使用
    @JsonProperty("MBD_CA_BASED_P_BINDER_TR")
    @NotNull
    private String mbdCaBasedPBinderTr;

    // 含钙磷结合剂
    @JsonProperty("MBD_CA_BASED_P_BINDER")
    private List<String> mbdCaBasedPBinder;

    // 非含钙磷结合剂_是否使用
    @JsonProperty("MBD_NON_CA_BASED_P_BINDER_TR")
    @NotNull
    private String mbdNonCaBasedPBinderTr;

    // 不含钙铝磷结合剂
    @JsonProperty("MBD_NON_CA_BASED_P_BINDER")
    private List<String> mbdNonCaBasedPBinder;

    // 拟钙剂
    @JsonProperty("MBD_CASR")
    private List<String> mbdCasr;

    // 其它药物
    @JsonProperty("MBD_OTHERS")
    private List<String> mbdOthers;

    // 是否为首次
    @JsonProperty("MBD_FIRST_YN")
    private Boolean mbdFirstYN;
}
