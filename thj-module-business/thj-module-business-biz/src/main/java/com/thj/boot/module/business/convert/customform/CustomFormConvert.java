package com.thj.boot.module.business.convert.customform;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.customform.CustomFormDO;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormCreateReqVO;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormRespVO;
import com.thj.boot.module.business.pojo.customform.vo.CustomFormUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 自定义表单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CustomFormConvert {

    CustomFormConvert INSTANCE = Mappers.getMapper(CustomFormConvert.class);

    CustomFormDO convert(CustomFormCreateReqVO bean);

    CustomFormDO convert(CustomFormUpdateReqVO bean);

    CustomFormRespVO convert(CustomFormDO bean);

    List<CustomFormRespVO> convertList(List<CustomFormDO> list);

    PageResult<CustomFormRespVO> convertPage(PageResult<CustomFormDO> page);


}
