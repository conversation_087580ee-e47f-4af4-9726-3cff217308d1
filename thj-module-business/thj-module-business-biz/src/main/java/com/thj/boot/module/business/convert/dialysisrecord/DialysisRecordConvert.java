package com.thj.boot.module.business.convert.dialysisrecord;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysisrecord.vo.DialysisRecordExcelVO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordRespVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 透析记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisRecordConvert {

    DialysisRecordConvert INSTANCE = Mappers.getMapper(DialysisRecordConvert.class);

    DialysisRecordDO convert(DialysisRecordCreateReqVO bean);

    DialysisRecordDO convert(DialysisRecordUpdateReqVO bean);

    DialysisRecordRespVO convert(DialysisRecordDO bean);

    List<DialysisRecordRespVO> convertList(List<DialysisRecordDO> list);

    PageResult<DialysisRecordRespVO> convertPage(PageResult<DialysisRecordDO> page);


    PageResult<DialysisRecordExcelVO> convertPage2(PageResult<DialysisRecordDO> pageResult);
}
