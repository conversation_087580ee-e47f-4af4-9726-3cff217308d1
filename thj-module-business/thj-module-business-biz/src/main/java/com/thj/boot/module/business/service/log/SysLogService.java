package com.thj.boot.module.business.service.log;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.log.SysLogDO;

import java.util.List;

/**
 * 系统日志 Service 接口
 *
 * <AUTHOR>
 */
public interface SysLogService {

    /**
     * 创建系统日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createSysLog(SysLogDO createReqVO);

    /**
     * 更新系统日志
     *
     * @param updateReqVO 更新信息
     */
    void updateSysLog(SysLogDO updateReqVO);

    /**
     * 删除系统日志
     *
     * @param id 编号
     */
    void deleteSysLog(Long id);

    /**
     * 获得系统日志
     *
     * @param id 编号
     * @return 系统日志
     */
    SysLogDO getSysLog(Long id);

    /**
     * 获得系统日志列表
     *
     * @param ids 编号
     * @return 系统日志列表
     */
    List<SysLogDO> getSysLogList(SysLogDO sysLogDO);

    /**
     * 获得系统日志分页
     *
     * @param pageReqVO 分页查询
     * @return 系统日志分页
     */
    PageResult<SysLogDO> getSysLogPage(SysLogDO pageReqVO);


}
