package com.thj.boot.module.business.pojo.dialysisdetection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisDetectionRespVO extends DialysisDetectionBaseVO {
    /**
     * 处理
     */
    private String dispose;


    /**
     * 上次监测时间
     */
    @JsonFormat(pattern = "HH:mm")
//    @DateTimeFormat(pattern = "HH:mm") //入参
//    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm") //出参
    private Date detectionMinLast;

}
