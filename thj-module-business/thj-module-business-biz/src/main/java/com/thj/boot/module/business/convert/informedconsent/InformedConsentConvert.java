package com.thj.boot.module.business.convert.informedconsent;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.informedconsent.InformedConsentDO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentCreateReqVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentRespVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 知情同意书 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InformedConsentConvert {

    InformedConsentConvert INSTANCE = Mappers.getMapper(InformedConsentConvert.class);

    InformedConsentDO convert(InformedConsentCreateReqVO bean);

    InformedConsentDO convert(InformedConsentUpdateReqVO bean);

    InformedConsentRespVO convert(InformedConsentDO bean);

    List<InformedConsentRespVO> convertList(List<InformedConsentDO> list);

    List<InformedConsentDO> convertList2(List<InformedConsentCreateReqVO> list);

    PageResult<InformedConsentRespVO> convertPage(PageResult<InformedConsentDO> page);


}
