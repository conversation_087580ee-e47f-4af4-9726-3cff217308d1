package com.thj.boot.module.business.service.adlevaluate;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.adlevaluate.AdlEvaluateDO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluateCreateReqVO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluatePageReqVO;
import com.thj.boot.module.business.pojo.adlevaluate.vo.AdlEvaluateUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 日常生活能力评定(ADL评估) Service 接口
 *
 * <AUTHOR>
 */
public interface AdlEvaluateService {

    /**
     * 创建日常生活能力评定(ADL评估)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAdlEvaluate( AdlEvaluateCreateReqVO createReqVO);

    /**
     * 更新日常生活能力评定(ADL评估)
     *
     * @param updateReqVO 更新信息
     */
    void updateAdlEvaluate( AdlEvaluateUpdateReqVO updateReqVO);

    /**
     * 删除日常生活能力评定(ADL评估)
     *
     * @param id 编号
     */
    void deleteAdlEvaluate(Long id);

    /**
     * 获得日常生活能力评定(ADL评估)
     *
     * @param id 编号
     * @return 日常生活能力评定(ADL评估)
     */
    AdlEvaluateDO getAdlEvaluate(Long id);

    /**
     * 获得日常生活能力评定(ADL评估)列表
     *
     * @param ids 编号
     * @return 日常生活能力评定(ADL评估)列表
     */
    List<AdlEvaluateDO> getAdlEvaluateList(Collection<Long> ids);

    /**
     * 获得日常生活能力评定(ADL评估)分页
     *
     * @param pageReqVO 分页查询
     * @return 日常生活能力评定(ADL评估)分页
     */
    PageResult<AdlEvaluateDO> getAdlEvaluatePage(AdlEvaluatePageReqVO pageReqVO);

    /**
     * 获得日常生活能力评定(ADL评估)列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 日常生活能力评定(ADL评估)列表
     */
    List<AdlEvaluateDO> getAdlEvaluateList(AdlEvaluateCreateReqVO createReqVO);

}
