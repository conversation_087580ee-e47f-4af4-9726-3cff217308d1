package com.thj.boot.module.business.service.drugtype;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.drugtype.DrugTypeDO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeCreateReqVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypePageReqVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 药品字典分类 Service 接口
 *
 * <AUTHOR>
 */
public interface DrugTypeService {

    /**
     * 创建药品字典分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDrugType( DrugTypeCreateReqVO createReqVO);

    /**
     * 更新药品字典分类
     *
     * @param updateReqVO 更新信息
     */
    void updateDrugType( DrugTypeUpdateReqVO updateReqVO);

    /**
     * 删除药品字典分类
     *
     * @param id 编号
     */
    void deleteDrugType(Long id);

    /**
     * 获得药品字典分类
     *
     * @param id 编号
     * @return 药品字典分类
     */
    DrugTypeDO getDrugType(Long id);

    /**
     * 获得药品字典分类列表
     *
     * @param ids 编号
     * @return 药品字典分类列表
     */
    List<DrugTypeDO> getDrugTypeList(Collection<Long> ids);

    /**
     * 获得药品字典分类分页
     *
     * @param pageReqVO 分页查询
     * @return 药品字典分类分页
     */
    PageResult<DrugTypeDO> getDrugTypePage(DrugTypePageReqVO pageReqVO);

    /**
     * 获得药品字典分类列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 药品字典分类列表
     */
    List<DrugTypeRespVO> getDrugTypeList(DrugTypeCreateReqVO createReqVO);

}
