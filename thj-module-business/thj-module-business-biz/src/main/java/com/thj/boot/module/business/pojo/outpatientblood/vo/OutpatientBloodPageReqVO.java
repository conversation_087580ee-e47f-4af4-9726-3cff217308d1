package com.thj.boot.module.business.pojo.outpatientblood.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutpatientBloodPageReqVO extends PageParam {
    /**
     * 门诊血液透析简历主键id
     */
    private Long id;
    /**
     * 内容
     */
    private String content;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 患者昵称
     */
    private String patientNickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 拼音
     */
    private String spellName;
    /**
     * 备注
     */
    private String remark;
}
