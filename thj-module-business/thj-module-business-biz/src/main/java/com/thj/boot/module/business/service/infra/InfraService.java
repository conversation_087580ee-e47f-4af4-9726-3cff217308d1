package com.thj.boot.module.business.service.infra;

import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.dal.datado.infra.InfraDO;
import com.thj.boot.module.business.pojo.infra.vo.InfraCreateReqVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraRespVO;
import com.thj.boot.module.business.pojo.infra.vo.InfraUpdateReqVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/24 16:39
 * @description
 */
public interface InfraService {

    /****
     * <AUTHOR>
     * @date 2023/10/24 16:44
     * @Description 上传文件
     **/
    InfraResult uploadFile(InfraCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2023/10/24 17:37
     * @Description 文件内容
     **/
    byte[] getFileContent(Long configId);

    List<InfraRespVO> getInfraListByIds(List<Long> ids);

    List<InfraRespVO> getInfraList(InfraDO infraDO);

    void updateInfra(InfraUpdateReqVO updateReqVO);

    /**
     * 保存多个
     * @param reqVOS
     * @return
     */
    boolean save(List<InfraUpdateReqVO> reqVOS);

    Boolean remove(List<Long> infraIds);
}
