package com.thj.boot.module.business.convert.hiscombo;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.pojo.hiscombo.vo.HisComboRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 血透事件监测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HisComboConvert {

    HisComboConvert INSTANCE = Mappers.getMapper(HisComboConvert.class);

    PageResult<HisComboRespVO> convertPage(PageResult<HisComboDO> hisComboDOPageResult);
}
