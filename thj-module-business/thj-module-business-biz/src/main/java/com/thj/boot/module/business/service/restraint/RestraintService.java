package com.thj.boot.module.business.service.restraint;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.restraint.RestraintDO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintCreateReqVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintPageReqVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintUpdateReqVO;

import java.util.Collection;
import java.util.List;

/**
 * 约束告知单 Service 接口
 *
 * <AUTHOR>
 */
public interface RestraintService {

    /**
     * 创建约束告知单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRestraint( RestraintCreateReqVO createReqVO);

    /**
     * 更新约束告知单
     *
     * @param updateReqVO 更新信息
     */
    void updateRestraint( RestraintUpdateReqVO updateReqVO);

    /**
     * 删除约束告知单
     *
     * @param id 编号
     */
    void deleteRestraint(Long id);

    /**
     * 获得约束告知单
     *
     * @param id 编号
     * @return 约束告知单
     */
    RestraintDO getRestraint(Long id);

    /**
     * 获得约束告知单列表
     *
     * @param ids 编号
     * @return 约束告知单列表
     */
    List<RestraintDO> getRestraintList(Collection<Long> ids);

    /**
     * 获得约束告知单分页
     *
     * @param pageReqVO 分页查询
     * @return 约束告知单分页
     */
    PageResult<RestraintDO> getRestraintPage(RestraintPageReqVO pageReqVO);

    /**
     * 获得约束告知单列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 约束告知单列表
     */
    List<RestraintDO> getRestraintList(RestraintCreateReqVO exportReqVO);

}
