package com.thj.boot.module.business.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.thj.boot.common.annotation.SysLog;
import com.thj.boot.module.business.dal.datado.log.SysLogDO;
import com.thj.boot.module.business.service.log.SysLogService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * 深圳万狼科技有限公司
 * E-MAIL：<EMAIL>
 * 地址：深圳市宝安区西乡街道铁仔路40号九方广场2栋901
 * 网址：http://www.wanlang.cn/
 * 我们的宗旨是：让每一个网站都产生价值！
 *
 * @Classname SysLogAspect
 * @Description 万狼科技
 * @Version 1.0.0
 * @Date 2024/2/22 17:48
 * @<NAME_EMAIL>
 */
@Aspect
@Component
public class SysLogAspect {
    @Autowired
    private SysLogService sysLogService;

    @Pointcut("@annotation(com.thj.boot.common.annotation.SysLog)")
    public void logPointCut() {

    }

    /**
     *
     * @param point
     * @return
     * @throws Throwable
     */

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
//        System.out.println("执行方法前置操作");
//        Class<?> aClass = point.getTarget().getClass();
//
//        String simpleName = aClass.getSimpleName();
//        simpleName = simpleName.replace("ServiceImpl", "Mapper");
//        String beanname = simpleName.substring(0, 1).toLowerCase() + simpleName.substring(1);
//        Object bean = SpringContextUtils.getBean(beanname);
//        String replace = aClass.getName().replace(".service.", ".dal.mapper.").replace("ServiceImpl", "Mapper");
//        Class<?> aClass1 = Class.forName(replace);
//        String name = aClass1.getName();
//
//        Method method = aClass1.getMethod("selectById", Serializable.class);
//        String params = "";
//        Object[] args = point.getArgs();
//        Object arg = args[0];
//        JSONObject from = JSONObject.from(args[0]);
//        JSONObject jsonObject = JSONObject.from(method.invoke(bean, from.getLong("id")));
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        //保存日志
        saveSysLog(point, time);
        return result;
    }

    private void saveSysLog(ProceedingJoinPoint joinPoint, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        SysLogDO sysLog = new SysLogDO();
        SysLog syslog = method.getAnnotation(SysLog.class);
        if (syslog != null) {
            //注解上的描述
            sysLog.setOperation(syslog.value());
        }

        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        sysLog.setMethod(className + "." + methodName + "()");

        //请求的参数
        Object[] args = joinPoint.getArgs();
        JSONObject after = JSONObject.from(args[0]);

//        Map<String, String> map = ContrastObjUtil.getObjectComparison(before, after);
//        for(Map.Entry<String, String> entry : map.entrySet()){
//            System.out.println("修改前 :" + entry.getKey());
//            System.out.println("修改后 :" + entry.getValue());
//        }
        //操作人ID
        sysLog.setOperateId(StpUtil.getLoginId().toString());
//        sysLog.setBeforParams(before.toString());
//        sysLog.setAfterParams(after.toString());
        sysLog.setParams(after.toString());
//        sysLog.setRemakes(objectComparison.toString());
        sysLog.setTime(time);
        sysLog.setCreateTime(new Date());
        //保存系统日志
        sysLogService.createSysLog(sysLog);
    }
}
