package com.thj.boot.module.business.service.teamlog;

import cn.hutool.core.collection.CollectionUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.teamlog.TeamLogConvert;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.teamlog.TeamLogDO;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.teamlog.TeamLogMapper;
import com.thj.boot.module.business.pojo.teamlog.TeamLogCreateReqVO;
import com.thj.boot.module.business.pojo.teamlog.TeamLogPageReqVO;
import com.thj.boot.module.business.pojo.teamlog.TeamLogRespVO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:58
 * @description
 */
@Service
@Slf4j
public class TeamLogServiceImpl implements TeamLogService {

    @Resource
    private TeamLogMapper teamLogMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PatientMapper patientMapper;

    @Override
    public void create(List<TeamLogCreateReqVO> createReqVOS) {
        if (CollectionUtil.isNotEmpty(createReqVOS)) {
            createReqVOS = createReqVOS.stream().peek(teamLogCreateReqVO -> {
                PatientDO patientDO = patientMapper.selectById(teamLogCreateReqVO.getPatientId());
                teamLogCreateReqVO.setPatientName(patientDO == null ? null : patientDO.getName());
            }).collect(Collectors.toList());
        }
        List<TeamLogDO> teamLogDOS = TeamLogConvert.INSTANCE.convertList(createReqVOS);
        teamLogMapper.insertBatch(teamLogDOS);
    }

    @Override
    public PageResult<TeamLogRespVO> teamLogPage(TeamLogPageReqVO pageReqVO) {
        PageResult<TeamLogDO> teamLogDOPageResult = teamLogMapper.selectPage(pageReqVO);
        PageResult<TeamLogRespVO> teamLogRespVOPageResult = TeamLogConvert.INSTANCE.convertPage(teamLogDOPageResult);
        if (CollectionUtil.isNotEmpty(teamLogRespVOPageResult.getList())) {
            List<TeamLogRespVO> collect = teamLogRespVOPageResult.getList().stream().peek(teamLogRespVO -> {
                UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
                createReqDTO.setId(Long.valueOf(teamLogRespVO.getCreator()));
                UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
                teamLogRespVO.setCreator(adminUser == null ? null : adminUser.getNickname());
                PatientDO patientDO = patientMapper.selectById(teamLogRespVO.getPatientId());
                teamLogRespVO.setPatientName(patientDO == null ? null : patientDO.getName());
            }).collect(Collectors.toList());
            teamLogRespVOPageResult.setList(collect);
        }
        return teamLogRespVOPageResult;
    }
}
