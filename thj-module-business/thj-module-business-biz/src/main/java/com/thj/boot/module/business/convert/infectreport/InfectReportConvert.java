package com.thj.boot.module.business.convert.infectreport;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.infectreport.InfectReportDO;
import com.thj.boot.module.business.pojo.infectreport.vo.InfectReportCreateReqVO;
import com.thj.boot.module.business.pojo.infectreport.vo.InfectReportRespVO;
import com.thj.boot.module.business.pojo.infectreport.vo.InfectReportUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 传染病报告单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InfectReportConvert {

    InfectReportConvert INSTANCE = Mappers.getMapper(InfectReportConvert.class);

    InfectReportDO convert(InfectReportCreateReqVO bean);

    InfectReportDO convert(InfectReportUpdateReqVO bean);

    InfectReportRespVO convert(InfectReportDO bean);

    List<InfectReportRespVO> convertList(List<InfectReportDO> list);

    PageResult<InfectReportRespVO> convertPage(PageResult<InfectReportDO> page);


}
