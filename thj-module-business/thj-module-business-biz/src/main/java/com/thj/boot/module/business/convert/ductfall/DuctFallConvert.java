package com.thj.boot.module.business.convert.ductfall;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.ductfall.DuctFallDO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallCreateReqVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallRespVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 导管脱落风险评估 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DuctFallConvert {

    DuctFallConvert INSTANCE = Mappers.getMapper(DuctFallConvert.class);

    DuctFallDO convert(DuctFallCreateReqVO bean);

    DuctFallDO convert(DuctFallUpdateReqVO bean);

    DuctFallRespVO convert(DuctFallDO bean);

    List<DuctFallRespVO> convertList(List<DuctFallDO> list);

    PageResult<DuctFallRespVO> convertPage(PageResult<DuctFallDO> page);


}
