package com.thj.boot.module.business.pojo.gktrain.vo;

import com.thj.boot.module.business.dal.datado.gktrain.GkTrainDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GkTrainRespVO extends GkTrainBaseVO {

    private String mouthTime;

    private int cunt;

    private int peopleCunt;

    private List<GkTrainDO> list;


}
