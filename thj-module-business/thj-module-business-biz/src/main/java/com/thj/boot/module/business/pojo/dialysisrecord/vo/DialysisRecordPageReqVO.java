package com.thj.boot.module.business.pojo.dialysisrecord.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisRecordPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 透析日期
     */
    private Date dateWeek;
    /**
     * 班次
     */
    private String weekDay;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 机号名称
     */
    private String facilityName;
    /**
     * 分区名称
     */
    private String facilitySubareaName;
    /**
     * 分区+机号
     */
    private String areaCode;
    /**
     * 透析模式名称
     */
    private String dialyzeWayValue;
    /**
     * 透析模式id
     */
    private String dialyzeDictValue;
    /**
     * 透析时长
     */
    private String duration;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeigh;
    /**
     * 超滤总量
     */
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 结束透析时间
     */
    private Date endDialyzeTime;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 患者拼音
     */
    private String spellName;
    /**
     * 实际治疗时长
     */
    private String treatment;
    /**
     * 目标脱水量
     */
    private String targetDehydratedLevel;
    /**
     * 脉搏透前
     */
    private String beforePNo;
    /**
     * 脉搏透后
     */
    private String afterPNo;
    /**
     * 透前血压
     */
    private String beforeBpNoOne;
    /**
     * 透前血压
     */
    private String beforeBpNoTwo;
    /**
     * 透后血压
     */
    private String afterBpNoOne;
    /**
     * 透后血压
     */
    private String afterBpNoTwo;
    /**
     * 透中最低压
     */
    private String lowestPressure;
    /**
     * 透中最高压
     */
    private String highestPressure;
    /**
     * 症状
     */
    private String symptom;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 透析模式
     */
    private String dialysisMode;


}
