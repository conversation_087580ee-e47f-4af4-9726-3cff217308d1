package com.thj.boot.module.business.convert.qualityinspection;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.qualityinspection.QualityInspectionDO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionCreateReqVO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionRespVO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 质量检测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface QualityInspectionConvert {

    QualityInspectionConvert INSTANCE = Mappers.getMapper(QualityInspectionConvert.class);

    QualityInspectionDO convert(QualityInspectionCreateReqVO bean);

    QualityInspectionDO convert(QualityInspectionUpdateReqVO bean);

    QualityInspectionRespVO convert(QualityInspectionDO bean);

    List<QualityInspectionRespVO> convertList(List<QualityInspectionDO> list);

    PageResult<QualityInspectionRespVO> convertPage(PageResult<QualityInspectionDO> page);


}
