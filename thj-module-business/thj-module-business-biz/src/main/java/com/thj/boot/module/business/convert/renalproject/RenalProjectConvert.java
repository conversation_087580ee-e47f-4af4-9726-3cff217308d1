package com.thj.boot.module.business.convert.renalproject;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.renalproject.RenalProjectDO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectCreateReqVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectExcelVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectRespVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 肾科检查 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RenalProjectConvert {

    RenalProjectConvert INSTANCE = Mappers.getMapper(RenalProjectConvert.class);

    RenalProjectDO convert(RenalProjectCreateReqVO bean);

    RenalProjectDO convert(RenalProjectUpdateReqVO bean);

    RenalProjectRespVO convert(RenalProjectDO bean);

    List<RenalProjectRespVO> convertList(List<RenalProjectDO> list);

    PageResult<RenalProjectRespVO> convertPage(PageResult<RenalProjectDO> page);

    List<RenalProjectExcelVO> convertList02(List<RenalProjectDO> list);

}
