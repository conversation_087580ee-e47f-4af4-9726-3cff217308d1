package com.thj.boot.module.business.convert.outpatientblood;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.outpatientblood.OutpatientBloodDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodCreateReqVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodRespVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 门诊血液透析简历 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OutpatientBloodConvert {

    OutpatientBloodConvert INSTANCE = Mappers.getMapper(OutpatientBloodConvert.class);

    OutpatientBloodDO convert(OutpatientBloodCreateReqVO bean);

    OutpatientBloodDO convert(OutpatientBloodUpdateReqVO bean);

    OutpatientBloodRespVO convert(OutpatientBloodDO bean);

    OutpatientBloodRespVO convert(PatientDO patientDO);

    List<OutpatientBloodRespVO> convertList(List<OutpatientBloodDO> list);

    PageResult<OutpatientBloodRespVO> convertPage(PageResult<OutpatientBloodDO> page);


}
