package com.thj.boot.module.business.pojo.dryweight.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DryWeightRespVO extends DryWeightBaseVO {
    private Integer sort;

    private Integer age;

    private Integer sex;
    private Double increase;
    private Double increaseRate;

    private Double recentWeight;

    private Double lasWeight;

    private String checkTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") //出参
    private Date createTime;
}
