package com.thj.boot.module.business.convert.disinfectionrecords;



import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.disinfectionrecords.DisinfectionRecordsDO;
import com.thj.boot.module.business.pojo.disinfectionrecords.vo.DisinfectionRecordsCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionrecords.vo.DisinfectionRecordsRespVO;
import com.thj.boot.module.business.pojo.disinfectionrecords.vo.DisinfectionRecordsUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 消毒记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DisinfectionRecordsConvert {

    DisinfectionRecordsConvert INSTANCE = Mappers.getMapper(DisinfectionRecordsConvert.class);

    DisinfectionRecordsDO convert(DisinfectionRecordsCreateReqVO bean);

    DisinfectionRecordsDO convert(DisinfectionRecordsUpdateReqVO bean);

    DisinfectionRecordsRespVO convert(DisinfectionRecordsDO bean);

    List<DisinfectionRecordsRespVO> convertList(List<DisinfectionRecordsDO> list);

    PageResult<DisinfectionRecordsRespVO> convertPage(PageResult<DisinfectionRecordsDO> page);


}
