package com.thj.boot.module.business.convert.sitemarkers;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.sitemarkers.SiteMarkersDO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersCreateReqVO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersRespVO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 位点标记 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SiteMarkersConvert {

    SiteMarkersConvert INSTANCE = Mappers.getMapper(SiteMarkersConvert.class);

    SiteMarkersDO convert(SiteMarkersCreateReqVO bean);

    SiteMarkersDO convert(SiteMarkersUpdateReqVO bean);

    SiteMarkersRespVO convert(SiteMarkersDO bean);

    List<SiteMarkersRespVO> convertList(List<SiteMarkersDO> list);

    PageResult<SiteMarkersRespVO> convertPage(PageResult<SiteMarkersDO> page);


}
