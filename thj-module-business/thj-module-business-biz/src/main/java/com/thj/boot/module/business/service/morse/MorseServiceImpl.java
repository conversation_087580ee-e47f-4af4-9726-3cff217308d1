package com.thj.boot.module.business.service.morse;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.morse.MorseConvert;
import com.thj.boot.module.business.dal.datado.morse.MorseDO;
import com.thj.boot.module.business.dal.mapper.morse.MorseMapper;
import com.thj.boot.module.business.pojo.morse.vo.MorseCreateReqVO;
import com.thj.boot.module.business.pojo.morse.vo.MorsePageReqVO;
import com.thj.boot.module.business.pojo.morse.vo.MorseUpdateReqVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * morse跌倒评估量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class MorseServiceImpl implements MorseService {

    @Resource
    private MorseMapper morseMapper;

    @Override
    public Long createMorse(MorseCreateReqVO createReqVO) {
        // 插入
        MorseDO morse = MorseConvert.INSTANCE.convert(createReqVO);
        morseMapper.insert(morse);
        // 返回
        return morse.getId();
    }

    @Override
    public void updateMorse(MorseUpdateReqVO updateReqVO) {
        // 更新
        MorseDO updateObj = MorseConvert.INSTANCE.convert(updateReqVO);
        morseMapper.updateById(updateObj);
    }

    @Override
    public void deleteMorse(Long id) {
        // 删除
        morseMapper.deleteById(id);
    }


    @Override
    public MorseDO getMorse(Long id) {
        return morseMapper.selectById(id);
    }

    @Override
    public List<MorseDO> getMorseList(Collection<Long> ids) {
        return morseMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MorseDO> getMorsePage(MorsePageReqVO pageReqVO) {
        return morseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MorseDO> getMorseList(MorseCreateReqVO exportReqVO) {
        return morseMapper.selectList(exportReqVO);
    }

}
