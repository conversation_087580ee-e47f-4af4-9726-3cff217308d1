package com.thj.boot.module.business.convert.restraint;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.restraint.RestraintDO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintCreateReqVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintRespVO;
import com.thj.boot.module.business.pojo.restraint.vo.RestraintUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 约束告知单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RestraintConvert {

    RestraintConvert INSTANCE = Mappers.getMapper(RestraintConvert.class);

    RestraintDO convert(RestraintCreateReqVO bean);

    RestraintDO convert(RestraintUpdateReqVO bean);

    RestraintRespVO convert(RestraintDO bean);

    List<RestraintRespVO> convertList(List<RestraintDO> list);

    PageResult<RestraintRespVO> convertPage(PageResult<RestraintDO> page);


}
