package com.thj.boot.module.business.service.schedpush;

import com.thj.boot.module.business.pojo.schedpush.vo.SchedPushReqVO;
import com.thj.boot.module.business.pojo.schedpush.vo.SchedPushRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/2 11:15
 * @description
 */
public interface SchedPushService {

    /***
     * <AUTHOR>
     * @date 2024/4/2 11:24
     * @Description 查询已绑定的患者信息透析方案排班信息
     **/
    List<SchedPushRespVO> getPatientList(SchedPushReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date 2024/4/2 13:44
     * @Description 批量推送
     **/
    void batchPush(SchedPushReqVO createReqVO);
}
