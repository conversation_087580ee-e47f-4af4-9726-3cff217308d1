package com.thj.boot.module.business.convert.drug;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.pojo.drug.vo.DrugCreateReqVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugRespVO;
import com.thj.boot.module.business.pojo.drug.vo.DrugUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 药品 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DrugConvert {

    DrugConvert INSTANCE = Mappers.getMapper(DrugConvert.class);

    DrugDO convert(DrugCreateReqVO bean);

    DrugDO convert(DrugUpdateReqVO bean);

    DrugRespVO convert(DrugDO bean);

    List<DrugRespVO> convertList(List<DrugDO> list);

    PageResult<DrugRespVO> convertPage(PageResult<DrugDO> page);


}
