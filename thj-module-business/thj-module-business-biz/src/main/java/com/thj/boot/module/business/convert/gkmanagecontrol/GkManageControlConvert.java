package com.thj.boot.module.business.convert.gkmanagecontrol;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkmanagecontrol.GkManageControlDO;
import com.thj.boot.module.business.pojo.gkmanagecontrol.vo.GkManageControlCreateReqVO;
import com.thj.boot.module.business.pojo.gkmanagecontrol.vo.GkManageControlExcelVO;
import com.thj.boot.module.business.pojo.gkmanagecontrol.vo.GkManageControlRespVO;
import com.thj.boot.module.business.pojo.gkmanagecontrol.vo.GkManageControlUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 院感管理质控 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkManageControlConvert {

    GkManageControlConvert INSTANCE = Mappers.getMapper(GkManageControlConvert.class);

    GkManageControlDO convert(GkManageControlCreateReqVO bean);

    GkManageControlDO convert(GkManageControlUpdateReqVO bean);

    GkManageControlRespVO convert(GkManageControlDO bean);

    List<GkManageControlRespVO> convertList(List<GkManageControlDO> list);

    PageResult<GkManageControlRespVO> convertPage(PageResult<GkManageControlDO> page);

    List<GkManageControlExcelVO> convertList02(List<GkManageControlDO> list);

}
