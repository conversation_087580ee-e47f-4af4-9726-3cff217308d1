package com.thj.boot.module.business.pojo.anticoagulant.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AnticoagulantPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 个性化id
     */
    private Long mottoHardId;
    /**
     * 抗凝剂类型(存入字典)
     */
    private String dictValue;
    /**
     * 首剂
     */
    private String firstDose;
    /**
     * 维持
     */
    private String maintain;
    /**
     * 追加1
     */
    private String addOne;
    /**
     * 追加2
     */
    private String addTwo;
    /**
     * 总量
     */
    private String total;
    /**
     * 医嘱名称
     */
    private String doctorAdvice;
    /**
     * 医嘱值
     */
    private String doctorValue;
    /**
     * 备注
     */
    private String remark;

}
