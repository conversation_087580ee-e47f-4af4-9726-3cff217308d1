package com.thj.boot.module.business.convert.proof;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.proof.ProofDO;
import com.thj.boot.module.business.pojo.proof.vo.ProofCreateReqVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofRespVO;
import com.thj.boot.module.business.pojo.proof.vo.ProofUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 疾病诊断-诊断证明 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProofConvert {

    ProofConvert INSTANCE = Mappers.getMapper(ProofConvert.class);

    ProofDO convert(ProofCreateReqVO bean);

    ProofDO convert(ProofUpdateReqVO bean);

    ProofRespVO convert(ProofDO bean);

    List<ProofRespVO> convertList(List<ProofDO> list);

    PageResult<ProofRespVO> convertPage(PageResult<ProofDO> page);


}
