package com.thj.boot.module.business.pojo.renalcheckinfo.vo;


import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
* 肾科检查项目信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RenalCheckInfoBaseVO extends BaseDO{

    @NotNull(message = "wl_renal_project主键不能为空")
    private Long projectId;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;

    private String checkpoint;

    private String checkOpinion;

    private String checkConclusion;

    private String remark;

}
