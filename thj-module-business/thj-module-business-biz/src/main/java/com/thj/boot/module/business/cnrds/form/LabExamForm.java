package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 实验室检查 表单
 */
@Data
public class LabExamForm {

    // 年
    @JsonProperty("LAB_YEAR")
    @NotNull
    private String labYear;

    // 时间维度
    @JsonProperty("LAB_DIMENSION")
    @NotNull
    private String labDimension;

    // 血常规_是否检查
    @JsonProperty("LAB_BLOOD_RT_YN")
    private String labBloodRtYN;

    // 血红蛋白（10-200）
    @JsonProperty("LAB_BLOOD_RT_HB")
    @NotNull
    private String labBloodRtHb;

    // 红细胞计数(0-10)
    @JsonProperty("LAB_BLOOD_RT_RBC")
    private String labBloodRtRbc;

    // 红细胞比容(0-100)
    @JsonProperty("LAB_BLOOD_RT_HCT")
    private String labBloodRtHct;

    // 血小板(0-1000)
    @JsonProperty("LAB_BLOOD_RT_PLT")
    private String labBloodRtPlt;

    // 转铁饱和度_是否检查
    @JsonProperty("LAB_IRON_TEST_TS_YN")
    private String labIronTestTsYn;

    // 转铁饱和度(0-100)
    @JsonProperty("LAB_IRON_TEST_TS")
    @NotNull
    private String labIronTestTs;

    // 铁蛋白_是否检查
    @JsonProperty("LAB_IRON_TEST_SF_YN")
    private String labIronTestSfYn;

    // 铁蛋白(0-5000)
    @JsonProperty("LAB_IRON_TEST_SF")
    @NotNull
    private String labIronTestSf;

    // 血钙血磷_是否检查
    @JsonProperty("LAB_ROD_CAP_YN")
    private String labRodCapYn;

    // 血钙(0-10)
    @JsonProperty("LAB_ROD_CA")
    @NotNull
    private String labRodCa;

    // 血磷(0-10)
    @JsonProperty("LAB_ROD_P")
    @NotNull
    private String labRodP;

    // 甲状旁腺激素(PTH)_是否检查
    @JsonProperty("LAB_ROD_PTH_YN")
    private String labRodPthYn;

    // 甲状旁腺激素(PTH)(0-5000)
    @JsonProperty("LAB_ROD_PTH")
    @NotNull
    private String labRodPth;

    // 生化检查_是否检查
    @JsonProperty("LAB_BIOCHEMICAL_YN")
    private String labBiochemicalYn;

    // 尿素
    @JsonProperty("LAB_BIOCHEMICAL_UREA")
    private String labBiochemicalUrea;

    // 尿素单位
    @JsonProperty("LAB_BIOCHEMICAL_UREA_UNIT")
    private String labBiochemicalUreaUnit;

    // 肌酐
    @JsonProperty("LAB_BIOCHEMICAL_SCR")
    private String labBiochemicalScr;

    // 肌酐单位
    @JsonProperty("LAB_BIOCHEMICAL_SCR_UNIT")
    private String labBiochemicalScrUnit;

    // 尿酸
    @JsonProperty("LAB_BIOCHEMICAL_UA")
    private String labBiochemicalUa;

    // 血白蛋白(10-70)
    @JsonProperty("LAB_BIOCHEMICAL_ALB")
    private String labBiochemicalAlb;

    // 天冬氨酸氨基转移酶(AST)
    @JsonProperty("LAB_BIOCHEMICAL_AST")
    private String labBiochemicalAst;

    // 丙氨酸氨基转移酶(ALT)
    @JsonProperty("LAB_BIOCHEMICAL_ALT")
    private String labBiochemicalAlt;

    // 总胆红素
    @JsonProperty("LAB_BIOCHEMICAL_STB")
    private String labBiochemicalStb;

    // 甘油三酯(0-50)
    @JsonProperty("LAB_BIOCHEMICAL_TG")
    private String labBiochemicalTg;

    // 总胆固醇(0-50)
    @JsonProperty("LAB_BIOCHEMICAL_TC")
    private String labBiochemicalTc;

    // 低密度脂蛋白(0-50)
    @JsonProperty("LAB_BIOCHEMICAL_LDL")
    private String labBiochemicalLdl;

    // 高密度脂蛋白(0-50)
    @JsonProperty("LAB_BIOCHEMICAL_HDL")
    private String labBiochemicalHdl;

    // 血糖(0-80)
    @JsonProperty("LAB_BIOCHEMICAL_GLU")
    private String labBiochemicalGlu;

    // 血钾(0-20)
    @JsonProperty("LAB_BIOCHEMICAL_POTASSIUM")
    private String labBiochemicalPotassium;

    // 血钠(50-200)
    @JsonProperty("LAB_BIOCHEMICAL_SODIUM")
    private String labBiochemicalSodium;

    // C反应蛋白_是否检查
    @JsonProperty("LAB_NUTR_INFLAM_CRP_YN")
    private String labNutrInflamCrpYn;

    // C反应蛋白(0-5000)
    @JsonProperty("LAB_NUTR_INFLAM_CRP")
    @NotNull
    private String labNutrInflamCrp;

    // 前白蛋白_是否检查
    @JsonProperty("LAB_NUTR_INFLAM_PAB_YN")
    private String labNutrInflamPabYn;

    // 前白蛋白(0-5000)
    @JsonProperty("LAB_NUTR_INFLAM_PAB")
    @NotNull
    private String labNutrInflamPab;

    // β2微球蛋白_是否检查
    @JsonProperty("LAB_NUTR_INFLAM_BETA2MG_YN")
    private String labNutrInflamBeta2mgYn;

    // β2微球蛋白
    @JsonProperty("LAB_NUTR_INFLAM_BETA2MG")
    @NotNull
    private String labNutrInflamBeta2mg;

    // HBsAg_是否检查
    @JsonProperty("LAB_INFECT_HBSAG_YN")
    private String labInfectHbsagYn;

    // HBsAg
    @JsonProperty("LAB_INFECT_HBSAG")
    @NotNull
    private String labInfectHbsag;

    // AntiHCV_是否检查
    @JsonProperty("LAB_INFECT_ANTIHCV_YN")
    private String labInfectAntihcvYn;

    // AntiHCV
    @JsonProperty("LAB_INFECT_ANTIHCV")
    @NotNull
    private String labInfectAntihcv;

    // HIV抗体_是否检查
    @JsonProperty("LAB_INFECT_HIV_YN")
    private String labInfectHivYn;

    // HIV抗体
    @JsonProperty("LAB_INFECT_HIV")
    @NotNull
    private String labInfectHiv;

    // 梅毒_是否检查
    @JsonProperty("LAB_INFECT_SYPHILIS_YN")
    private String labInfectSyphilisYn;

    // 梅毒
    @JsonProperty("LAB_INFECT_SYPHILIS")
    @NotNull
    private String labInfectSyphilis;

}
