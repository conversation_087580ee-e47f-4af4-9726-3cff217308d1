package com.thj.boot.module.business.service.motto;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.motto.MottoDO;
import com.thj.boot.module.business.pojo.motto.vo.MottoCreateReqVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoPageReqVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoRespVO;
import com.thj.boot.module.business.pojo.motto.vo.MottoUpdateReqVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleRespVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 个性化 Service 接口
 *
 * <AUTHOR>
 */
public interface MottoService {

    /**
     * 创建个性化
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMotto( MottoCreateReqVO createReqVO);

    /**
     * 更新个性化
     *
     * @param updateReqVO 更新信息
     */
    void updateMotto( MottoUpdateReqVO updateReqVO);

    /**
     * 删除个性化
     *
     * @param id 编号
     */
    void deleteMotto(Long id);

    /**
     * 获得个性化
     *
     * @param id 编号
     * @return 个性化
     */
    List<MottoSimpleRespVO> getMotto(MottoCreateReqVO createReqVO, HttpServletRequest request);


    /**
     * 获得个性化分页
     *
     * @param pageReqVO 分页查询
     * @return 个性化分页
     */
    PageResult<MottoDO> getMottoPage(MottoPageReqVO pageReqVO);

    /**
     * 获得个性化列表, 用于 Excel 导出
     * @return 个性化列表
     */
    List<MottoRespVO> getMottoList(MottoCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2023/11/27 14:43
     * @Description 个性化详情
     **/
    MottoRespVO getMottoInfo(MottoCreateReqVO createReqVO);


    Map<String, String> getMottoSimpleMap();
}
