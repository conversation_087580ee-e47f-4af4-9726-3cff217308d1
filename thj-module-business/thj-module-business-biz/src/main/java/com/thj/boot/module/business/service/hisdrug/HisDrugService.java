package com.thj.boot.module.business.service.hisdrug;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugCreateReqVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugPageReqVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugRespVO;
import com.thj.boot.module.business.pojo.hisdrug.vo.HisDrugUpdateReqVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * his药品 Service 接口
 *
 * <AUTHOR>
 */
public interface HisDrugService {

    /**
     * 创建his药品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHisDrug(HisDrugCreateReqVO createReqVO);

    /**
     * 更新his药品
     *
     * @param updateReqVO 更新信息
     */
    void updateHisDrug(HisDrugUpdateReqVO updateReqVO);

    /**
     * 删除his药品
     *
     * @param id 编号
     */
    void deleteHisDrug(Long id);

    /**
     * 获得his药品
     *
     * @param id 编号
     * @return his药品
     */
    HisDrugDO getHisDrug(Long id);

    /**
     * 获得his药品列表
     *
     * @param ids 编号
     * @return his药品列表
     */
    List<HisDrugDO> getHisDrugList(HisDrugCreateReqVO createReqVO);

    /**
     * 获得his药品分页
     *
     * @param pageReqVO 分页查询
     * @return his药品分页
     */
    PageResult<HisDrugDO> getHisDrugPage(HisDrugPageReqVO pageReqVO);

    void save(String hosipital, String systemdeptid);

    /****
     * <AUTHOR>
     * @date 2024/3/19 9:29
     * @Description 同步his药品
     **/
    Boolean asyncHisDrug(HttpServletRequest request);

    /****
     * <AUTHOR>
     * @date 2024/3/20 9:05
     * @Description 药品和项目名称和规格,新增套餐
     **/
    List<HisDrugRespVO> getHisDrugInformation(HisDrugCreateReqVO hisDrugCreateReqVO);
}
