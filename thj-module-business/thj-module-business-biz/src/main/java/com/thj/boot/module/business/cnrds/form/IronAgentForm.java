package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 铁剂 表单
 */
@Data
public class IronAgentForm {

    // 年
    @JsonProperty("FE_YEAR")
    @NotNull
    private String feYear;

    // 时间维度
    @JsonProperty("FE_DIMENSION")
    @NotNull
    private String feDimension;

    // 有无变化
    @JsonProperty("FE_CHANGE_YN")
    @NotNull
    private String feChangeYN;

    // 是否使用
    @JsonProperty("FE_TREATMENT")
    @NotNull
    private String feTreatment;

    // 方式
    @JsonProperty("FE_ADMIN")
    @NotNull
    private List<String> feAdmin;

    // 口服种类
    @JsonProperty("FE_TYPE_ORAL")
    private String feTypeOral;

    // 静脉种类
    @JsonProperty("FE_TYPE_INTRAVENOUS")
    private String feTypeIntravenous;

    // 是否为首次
    @JsonProperty("FE_FIRST_YN")
    private Boolean feFirstYN;
}
