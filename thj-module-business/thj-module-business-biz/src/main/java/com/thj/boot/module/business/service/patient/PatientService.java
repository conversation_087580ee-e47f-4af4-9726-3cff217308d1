package com.thj.boot.module.business.service.patient;

import com.baomidou.mybatisplus.extension.service.IService;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo;
import com.thj.boot.module.business.controller.admin.gk.vo.MaintainDialysisVo;
import com.thj.boot.module.business.controller.admin.gk.vo.PatientImplementationVo;
import com.thj.boot.module.business.controller.admin.patient.vo.DiseaseHistoryRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientInspectionRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientLabelReqVO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyDO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyReq;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyVo;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientUpdateReqVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 患者管理 Service 接口
 *
 * <AUTHOR>
 */
public interface PatientService extends IService<PatientDO> {

    /**
     * 创建患者管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatient(PatientCreateReqVO createReqVO);

    /**
     * 更新患者管理
     *
     * @param updateReqVO 更新信息
     */
    void updatePatient(PatientUpdateReqVO updateReqVO);

    /**
     * 删除患者管理
     *
     * @param id 编号
     */
    void deletePatient(String ids);

    /**
     * 获得患者管理
     *
     * @param id 编号
     * @return 患者管理
     */
    PatientRespVO getPatient(Long id);

    /**
     * 获得患者管理列表
     *
     * @param ids 编号
     * @return 患者管理列表
     */
    List<PatientDO> getPatientList(Collection<Long> ids);

    /**
     * 获得患者管理分页
     *
     * @param pageReqVO 分页查询
     * @return 患者管理分页
     */
    PageResult<PatientRespVO> getPatientPage(PatientPageReqVO pageReqVO);

    /**
     * 获得患者管理列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 患者管理列表
     */
    List<PatientRespVO> getPatientList(PatientCreateReqVO createReqVO,Long SystemDeptId);

    /***
     * <AUTHOR>
     * @date 2023/11/16 17:04
     * @Description 打印患者基本信息pdf
     **/
    String exportPatientPDF(PatientCreateReqVO createReqVO, HttpServletResponse response);

    /***
     * <AUTHOR>
     * @date 2023/12/16 15:18
     * @Description 根据患者id获取患者详情和二维码
     **/
    String getPcQrCode(HttpServletResponse response, Long patientId);

    /***
     * <AUTHOR>
     * @date 2024/1/8 15:34
     * @Description 根据患者id获取所有已排班信息
     **/
    List<ArrangeClassesRespVO> getTeamPatientList(Long patientId);


    /***
     * <AUTHOR>
     * @date 2024/1/12 15:14
     * @Description app不分页
     **/
    List<PatientRespVO> getPatientListApp(PatientCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date 2024/1/12 15:15
     * @Description app分页
     **/
    PageResult<PatientRespVO> getPatientPageApp(PatientPageReqVO pageVO);

    /***
     * <AUTHOR>
     * @date 2024/1/12 15:32
     * @Description app详情
     **/
    PatientRespVO getPatientApp(Long id);

    /****
     * <AUTHOR>
     * @date 2024/1/15 19:06
     * @Description 病历摘要
     **/
    DiseaseHistoryRespVO diseaseHistory(Long patientId);


    List<PatientRespVO> getPatientWithDialysisList(Date startTime, Date endTime);


    /**
     * 查询所有患者信息
     *
     * @return
     */
    List<MaintainDialysisVo> queryAllList(MaintainDialysisVo vo);

    List<CollectiveVo> getCollective(String keyWord);

    List<PatientImplementationVo> getMouth(PatientImplementationVo vo);

    /**
     * 检验提醒
     */
    List<PatientInspectionRespVO> inspectionList(PatientCreateReqVO createReqVO);

    /****
     * <AUTHOR>
     * @date 2024/2/26 20:08
     * @Description 批量修改标签值
     **/
    void batchLabels(PatientLabelReqVO patientLabelReqVO);

    /****
     * <AUTHOR>
     * @date 2024/4/1 20:26
     * @Description 排班后面显示数量
     **/
    Map<String, Integer> getArrangeClassesNumber(PatientPageReqVO pageVO);

    Map<String, Integer> getArrangeClassesNumber();

    /***
     * <AUTHOR>
     * @date  2024/5/7 16:51
     * @Description 患者导出
     **/
    void exportPatientInfo(PatientPageReqVO patientPageReqVO, HttpServletResponse response) throws IOException;

    Boolean synPatInfoToHis(PatientRespVO patientRespVO);

    List<PatientRespVO> getPatientListByDate(ExamApplyVo date);

    List<ExamApplyDO> getExamList(ExamApplyVo examApplyVo);

    Boolean updateExamList(List<ExamApplyDO> examApplyDOList);

    PageResult<HemodialysisManagerRespVO> getPatientTreatRecordDetail(HemodialysisManagerPageReqVO pageReqVO);
}
