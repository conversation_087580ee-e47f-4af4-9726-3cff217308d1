package com.thj.boot.module.business.convert.disinfectiontable;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.disinfectiontable.DisinfectionTableDO;
import com.thj.boot.module.business.pojo.disinfectiontable.vo.DisinfectionTableCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectiontable.vo.DisinfectionTableRespVO;
import com.thj.boot.module.business.pojo.disinfectiontable.vo.DisinfectionTableUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机器消毒 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DisinfectionTableConvert {

    DisinfectionTableConvert INSTANCE = Mappers.getMapper(DisinfectionTableConvert.class);

    DisinfectionTableDO convert(DisinfectionTableCreateReqVO bean);

    DisinfectionTableDO convert(DisinfectionTableUpdateReqVO bean);

    DisinfectionTableRespVO convert(DisinfectionTableDO bean);

    List<DisinfectionTableRespVO> convertList(List<DisinfectionTableDO> list);

    PageResult<DisinfectionTableRespVO> convertPage(PageResult<DisinfectionTableDO> page);


}
