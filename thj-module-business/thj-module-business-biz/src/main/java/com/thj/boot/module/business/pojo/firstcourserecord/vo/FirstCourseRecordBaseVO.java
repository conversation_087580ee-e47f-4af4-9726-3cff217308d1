package com.thj.boot.module.business.pojo.firstcourserecord.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

import java.util.Date;

/**
 * 首次病程记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FirstCourseRecordBaseVO extends PatientBaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;
    /**
     * 主诉
     */
    private String mainSuit;
    /**
     * 现病史
     */
    private String nowDiseaseHistory;
    /**
     * 既往史
     */
    private String oldDiseaseHistory;
    /**
     * 体格检查
     */
    private String bodyInspect;
    /**
     * 鉴别诊断
     */
    private String identifyDiagnosis;
    /**
     * 诊疗计划
     */
    private String healingProject;
    /**
     * 初步诊断
     */
    private String initDiagnosis;
    /**
     * 医师签名
     */
    private Long signature;
    /**
     * 备注
     */
    private String remark;
    /**
     * 化验检查
     */
    private String assayInspect;
}
