package com.thj.boot.module.business.pojo.arteriovenous.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArteriovenousPageReqVO extends PageParam {

    /**
     * 序号
     */
    private Long id;
    /**
     * 检查日期
     */
    private Date inspectionDate;
    /**
     * 被检查者
     */
    private String inspectedPerson;
    /**
     * 戴口罩（对/错）
     */
    private String wearingMask;
    /**
     * 正确行手卫生前（对/错）
     */
    private String handHygiene;
    /**
     * 戴新洁净手套（对/错）
     */
    private String newGloves;
    /**
     * 无菌断开穿刺针与血管路（对/错）
     */
    private String vascularAccess;
    /**
     * 无菌拔针（对/错）
     */
    private String sterileNeedleRemoval;
    /**
     * 无菌棉球或纱布按压（对/错）
     */
    private String gauzePressing;
    /**
     * 一次性处置穿刺针（对/错）
     */
    private String needle;
    /**
     * 固定压迫棉球（对/错）
     */
    private String cottonBall;
    /**
     * 脱手套（对/错）
     */
    private String gloveRemoval;
    /**
     * 正确行手卫生后（对/错）
     */
    private String handHygieneAfter;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 检查区域
     */
    private String inspectionArea;
    /**
     * 检查时间（min）
     */
    private Integer inspectionTimeInMinutes;
    /**
     * 评语
     */
    private String comments;
    /**
     * 检查者
     */
    private String inspector;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 检查人ID
     */
    private Long inspectorId;
    /**
     * 被检查人ID
     */
    private Long inspectedPersonId;

}
