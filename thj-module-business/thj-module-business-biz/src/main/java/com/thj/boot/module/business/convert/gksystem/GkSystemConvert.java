package com.thj.boot.module.business.convert.gksystem;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gksystem.GkSystemDO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemCreateReqVO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemRespVO;
import com.thj.boot.module.business.pojo.gksystem.vo.GkSystemUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 感控制度 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkSystemConvert {

    GkSystemConvert INSTANCE = Mappers.getMapper(GkSystemConvert.class);

    GkSystemDO convert(GkSystemCreateReqVO bean);

    GkSystemDO convert(GkSystemUpdateReqVO bean);

    GkSystemRespVO convert(GkSystemDO bean);

    List<GkSystemRespVO> convertList(List<GkSystemDO> list);

    PageResult<GkSystemRespVO> convertPage(PageResult<GkSystemDO> page);


}
