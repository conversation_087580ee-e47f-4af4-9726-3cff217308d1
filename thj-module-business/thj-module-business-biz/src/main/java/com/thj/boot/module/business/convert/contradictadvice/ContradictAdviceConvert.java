package com.thj.boot.module.business.convert.contradictadvice;

import com.thj.boot.module.business.dal.datado.contradictadvice.ContradictAdviceDO;
import com.thj.boot.module.business.pojo.contradictAdvice.vo.ContradictAdviceCreateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 抗凝剂 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ContradictAdviceConvert {

    ContradictAdviceConvert INSTANCE = Mappers.getMapper(ContradictAdviceConvert.class);

    List<ContradictAdviceDO> convertList(List<ContradictAdviceCreateReqVO> collect);
}
