package com.thj.boot.module.business.pojo.contradictAdvice.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/25 11:32
 * @description
 */
@Data
public class ContradictAdviceBaseVO implements Serializable {
    /**
     *
     */
    private Long id;
    /**
     * 透析方案id
     */
    private Long dialyzeId;
    /**
     * 药品id
     */
    private String drugId;
    /**
     * 单次用量
     */
    private String singleDose;
    /**
     * 开药数量
     */
    private String prescriptionQuantity;
    /**
     * 给药途径
     */
    private String medicineWayValue;
    /**
     * 执行频率
     */
    private String frequencyValue;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者透析方案主键id
     */
    private Long patientDialyzeId;
    /**
     * 抗凝剂类型
     */
    private String drugTypeId;
    /**
     * 0-个性化透析处方，1-患者透析处方,2-血液透析
     */
    private Integer protocolType;
    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 透析时间
     */
    private Date hemodialysisTime;

    private Boolean deleted;
}
