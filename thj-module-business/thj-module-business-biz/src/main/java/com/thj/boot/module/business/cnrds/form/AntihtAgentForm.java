package com.thj.boot.module.business.cnrds.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 抗高血压药 表单
 */
@Data
public class AntihtAgentForm {

    // 年
    @JsonProperty("ANTIHT_AGENT_YEAR")
    @NotNull
    private String antihtAgentYear;

    // 时间维度
    @JsonProperty("ANTIHT_AGENT_DIMENSION")
    @NotNull
    private String antihtAgentDimension;

    // 有无变化
    @JsonProperty("ANTIHT_AGENT_CHANGE_YN")
    @NotNull
    private String antihtAgentChangeYN;

    // 是否使用
    @JsonProperty("ANTIHT_AGENT_TREATMENT")
    @NotNull
    private String antihtAgentTreatment;

    // 处方
    @JsonProperty("ANTIHT_AGENT_TYPE")
    private List<String> antihtAgentType;

    // 是否为首次
    @JsonProperty("ANTIHT_AGENT_FIRST_YN")
    private String antihtAgentFirstYN;


}
