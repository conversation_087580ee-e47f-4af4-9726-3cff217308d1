package com.thj.boot.module.business.convert.vascularaccess;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessCreateReqVO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessRespVO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 通路 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VascularAccessConvert {

    VascularAccessConvert INSTANCE = Mappers.getMapper(VascularAccessConvert.class);

    VascularAccessDO convert(VascularAccessCreateReqVO bean);

    VascularAccessDO convert(VascularAccessUpdateReqVO bean);

    VascularAccessRespVO convert(VascularAccessDO bean);

    List<VascularAccessRespVO> convertList(List<VascularAccessDO> list);

    PageResult<VascularAccessRespVO> convertPage(PageResult<VascularAccessDO> page);


}
