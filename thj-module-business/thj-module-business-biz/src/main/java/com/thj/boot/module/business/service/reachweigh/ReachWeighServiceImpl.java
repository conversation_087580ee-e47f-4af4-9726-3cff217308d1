package com.thj.boot.module.business.service.reachweigh;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.esotericsoftware.minlog.Log;
import com.google.common.collect.Lists;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.controller.admin.reachweigh.vo.ReachWeighWeekRespVO;
import com.thj.boot.module.business.convert.arrangeclasses.ArrangeClassesConvert;
import com.thj.boot.module.business.convert.reachweigh.ReachWeighConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.dryweight.DryWeightDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.reachweigh.ReachWeighDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.reachweigh.ReachWeighMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighCreateReqVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighPageReqVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighUpdateReqVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 签到称重 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ReachWeighServiceImpl implements ReachWeighService {

    @Resource
    private ReachWeighMapper reachWeighMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private DryWeightMapper dryWeightMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createReachWeigh(ReachWeighCreateReqVO createReqVO) {
        // 返回
        return null;
    }

    @Override
    public void updateReachWeigh(ReachWeighUpdateReqVO updateReqVO) {
        // 更新
        ReachWeighDO updateObj = ReachWeighConvert.INSTANCE.convert(updateReqVO);
        reachWeighMapper.updateById(updateObj);
    }

    @Override
    public void deleteReachWeigh(Long id) {
        // 删除
        reachWeighMapper.deleteById(id);
    }


    @Override
    public ReachWeighRespVO getReachWeigh(ReachWeighCreateReqVO reachWeighCreateReqVO) {
        //签到称重信息
        ReachWeighDO reachWeighDO = reachWeighMapper.selectOne(new LambdaQueryWrapperX<ReachWeighDO>()
                .eqIfPresent(ReachWeighDO::getPatientId, reachWeighCreateReqVO.getPatientId())
                .betweenIfPresent(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(reachWeighCreateReqVO.getClassesTime()), DateUtil.endOfDay(reachWeighCreateReqVO.getClassesTime())));
        ReachWeighRespVO reachWeighRespVO = ReachWeighConvert.INSTANCE.convert(reachWeighDO);

        if (!StringUtils.isEmpty(reachWeighCreateReqVO.getPatientName())) {
            String patientName = reachWeighCreateReqVO.getPatientName();
            String[] split = patientName.split("");
            String join = String.join("%", split);
            reachWeighCreateReqVO.setPatientName(join);

        }
        //排班信息
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, reachWeighCreateReqVO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getClassesTime, reachWeighCreateReqVO.getClassesTime())
                .eq(ArrangeClassesDO::getTempType, 0)
                .likeIfPresent(ArrangeClassesDO::getPatientName,reachWeighCreateReqVO.getPatientName())
                .select(ArrangeClassesDO::getState));

        //干体重
        DryWeightDO dryWeightDO1 = dryWeightMapper.selectOne(new LambdaQueryWrapperX<DryWeightDO>()
                .eqIfPresent(DryWeightDO::getPatientId, reachWeighCreateReqVO.getPatientId())
                .select(DryWeightDO::getDryWeight)
                .orderByDesc(DryWeightDO::getId)
                .last("limit 1"));

        //透前评估已确认,获取透前评估值
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, reachWeighCreateReqVO.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(new Date()))
                .select(HemodialysisManagerDO::getDryWeight
                        , HemodialysisManagerDO::getClothing
                        , HemodialysisManagerDO::getDialyzeBeforeWeigh
                        , HemodialysisManagerDO::getDehydration
                        , HemodialysisManagerDO::getDialyzeBeforeWeight
                        , HemodialysisManagerDO::getPno
                        , HemodialysisManagerDO::getRno
                        , HemodialysisManagerDO::getDegreeCelsius
                        , HemodialysisManagerDO::getBpNoOne
                        , HemodialysisManagerDO::getBpNoTwo
                        , HemodialysisManagerDO::getDialyzeAfterWeigh
                        , HemodialysisManagerDO::getWeights
                        , HemodialysisManagerDO::getDialyzeAfterWeight
                        , HemodialysisManagerDO::getAfterDegreeCelsius
                        , HemodialysisManagerDO::getAfterPNo
                        , HemodialysisManagerDO::getAfterRNo
                        , HemodialysisManagerDO::getAfterBpOne
                        , HemodialysisManagerDO::getAfterBpTwo));

        //干体重
        if (dryWeightDO1 != null) {
            if ("-1".equals(dryWeightDO1.getDryWeight())) {
                dryWeightDO1.setDryWeight("待定");
            } else if ("-2".equals(dryWeightDO1.getDryWeight())) {
                dryWeightDO1.setDryWeight("卧床");
            }
        }

        //默认上次透前衣物重
        HemodialysisManagerDO managerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, reachWeighCreateReqVO.getPatientId())
                .orderByDesc(HemodialysisManagerDO::getId)
                .last("limit 1")
                .select(HemodialysisManagerDO::getClothing,HemodialysisManagerDO::getAfterClothing));

        if (reachWeighRespVO != null) {
            //透前透后测量
            if (arrangeClassesDO != null) {
                if (arrangeClassesDO.getState() < 3) {
                    reachWeighRespVO.setDialyzeState(0);
                } else if (arrangeClassesDO.getState() > 2) {
                    reachWeighRespVO.setDialyzeState(1);
                }
            }
            //干体重
            reachWeighRespVO.setDryWeight(dryWeightDO1 != null ? dryWeightDO1.getDryWeight() : "0");

            //血液透析透前评估
            if (hemodialysisManagerDO != null && hemodialysisManagerDO.getBeforeState() != null) {
                BeanUtil.copyProperties(hemodialysisManagerDO, reachWeighRespVO);
            }
            return reachWeighRespVO;
        }
        ReachWeighRespVO weighRespVO = new ReachWeighRespVO();
        //干体重
        weighRespVO.setDryWeight(dryWeightDO1 != null ? dryWeightDO1.getDryWeight() : "0");
        //透前透后测量
        if (arrangeClassesDO != null) {
            if (arrangeClassesDO.getState() < 3) {
                weighRespVO.setDialyzeState(0);
            } else if (arrangeClassesDO.getState() > 2) {
                weighRespVO.setDialyzeState(1);
            }
        }
        //默认上次透前衣物重
        weighRespVO.setClothing(managerDO != null ? managerDO.getClothing() : "0");
        return weighRespVO;
    }


    @Override
    public List<ReachWeighDO> getReachWeighList(Collection<Long> ids) {
        return reachWeighMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ReachWeighRespVO> getReachWeighPage(ReachWeighPageReqVO pageReqVO) {
        PageResult<ReachWeighDO> reachWeighDOPageResult = reachWeighMapper.selectPage(pageReqVO);
        return ReachWeighConvert.INSTANCE.convertPage(reachWeighDOPageResult);
    }

    @Override
    public List<ReachWeighRespVO> getReachWeighList(ReachWeighCreateReqVO createReqVO) {
        List<ReachWeighDO> reachWeighDOS = reachWeighMapper.selectList(createReqVO);
        return ReachWeighConvert.INSTANCE.convertList(reachWeighDOS);
    }

    @Override
    public List<ArrangeClassesRespVO> teamPatientList(ReachWeighCreateReqVO createReqVO) {
        List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        List<String> dictValues = null;
        if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
            dictValues = arrangeClassesPeriodTime.stream().map(DictDataRespDTO::getValue).collect(Collectors.toList());
        }
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date()))
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(StrUtil.isNotEmpty(createReqVO.getDayState()), ArrangeClassesDO::getDayState, createReqVO.getDayState())
                .inIfPresent(ArrangeClassesDO::getDayState, dictValues)
                .likeIfPresent(ArrangeClassesDO::getPatientName, createReqVO.getPatientName())
                .eqIfPresent(ArrangeClassesDO::getDayState, createReqVO.getWeekDay()));
        List<ArrangeClassesRespVO> respVOS = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        if (CollectionUtil.isNotEmpty(respVOS)) {
            respVOS = respVOS.stream().peek(teamPatientRespVO -> {
                StringBuilder sb = new StringBuilder();
                PatientDO patientDO = patientMapper.selectById(teamPatientRespVO.getPatientId());
                sb.append(teamPatientRespVO.getPatientName()).append(patientDO != null ? "(" + patientDO.getDialyzeNo() + ")" : "");
                teamPatientRespVO.setRegisterName(teamPatientRespVO.getPatientName());
                teamPatientRespVO.setPatientName(sb.toString());
                ReachWeighDO reachWeighDO = reachWeighMapper.selectOne(new LambdaQueryWrapperX<ReachWeighDO>()
                        .eqIfPresent(ReachWeighDO::getPatientId, teamPatientRespVO.getPatientId())
                        .betweenIfPresent(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                if (reachWeighDO != null) {
                    teamPatientRespVO.setState(Integer.valueOf(reachWeighDO.getRegisterState()));
                    teamPatientRespVO.setUpdateTime(reachWeighDO.getUpdateTime());
                }
            }).sorted(Comparator.comparing(ArrangeClassesRespVO::getUpdateTime).reversed()).collect(Collectors.toList());
        }
        return respVOS;
    }

    @Override
    public List<ReachWeighWeekRespVO> weekFlagList() {
        List<ReachWeighWeekRespVO> list = Lists.newArrayList();
        //查询当前的班次
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                .eqIfPresent(ArrangeClassesDO::getTempType, 0));
        if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
            List<DictDataRespDTO> arrangeClassesPeriodTime = dictDataApi.getDictListData("arrangeClassesPeriodTime");
            if (CollectionUtil.isNotEmpty(arrangeClassesPeriodTime)) {
                for (DictDataRespDTO dictDataRespDTO : arrangeClassesPeriodTime) {
                    ReachWeighWeekRespVO reachWeighWeekRespVO = new ReachWeighWeekRespVO();
                    reachWeighWeekRespVO.setDayState(dictDataRespDTO.getLabel());
                    List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesDOS.stream().filter(arrangeClassesDO -> dictDataRespDTO.getValue().equals(arrangeClassesDO.getDayState())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(arrangeClassesDOList)) {
                        List<Long> patientIds = arrangeClassesDOList.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toList());
                        List<ReachWeighDO> reachWeighDOS = reachWeighMapper.selectList(new LambdaQueryWrapperX<ReachWeighDO>()
                                .inIfPresent(ReachWeighDO::getPatientId, patientIds)
                                .eqIfPresent(ReachWeighDO::getRegisterState, 1)
                                .eqIfPresent(ReachWeighDO::getDayState, dictDataRespDTO.getValue())
                                .betweenIfPresent(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                        reachWeighWeekRespVO.setTeamTotal(arrangeClassesDOList.size() + "");
                        reachWeighWeekRespVO.setRegisterTotal(reachWeighDOS.size() + "");
                        reachWeighWeekRespVO.setWeighTotal("0");
                    }
                    list.add(reachWeighWeekRespVO);
                }
            }
        }
        return list;
    }

    @Override
    public void saveOrUpdateReachWeigh(ReachWeighUpdateReqVO updateReqVO, HttpServletRequest request) {
        if (updateReqVO.getPatientId() == null) {
            throw new ServiceException(GlobalErrorCodeConstants.NO_BUTTON);
        }
        //synchronized (ReachWeighServiceImpl.class) {
        //    saveOrUpdateWeight(updateReqVO, 0);
        //}
        String SystemDeptId = request.getHeader("SystemDeptId");
        saveOrUpdateWeight(updateReqVO, 0, SystemDeptId);
    }

    @Override
    public void getFaceWeight(ReachWeighUpdateReqVO updateReqVO, HttpServletRequest request) {
        //synchronized (ReachWeighServiceImpl.class) {
        //    saveOrUpdateWeight(updateReqVO, 1);
        //}
        String SystemDeptId = request.getHeader("SystemDeptId");
        saveOrUpdateWeight(updateReqVO, 1, SystemDeptId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateWeight(ReachWeighUpdateReqVO updateReqVO, int type, String SystemDeptId) {
        String key = "patient:reach:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        //DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        //def.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
        //TransactionStatus status = platformTransactionManager.getTransaction(def);
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                if (1 == type) {
                    //根据透析号获取患者信息
                    PatientDO patientDO = patientMapper.selectOne(new LambdaQueryWrapperX<PatientDO>()
                            .eq(PatientDO::getDialyzeNo, updateReqVO.getDialyzeNo())
                            .select(PatientDO::getId, PatientDO::getName));
                    if (patientDO != null) {
                        updateReqVO.setPatientId(patientDO.getId());
                        updateReqVO.setName(patientDO.getName());
                        DryWeightDO dryWeightDO = dryWeightMapper.selectOne(new LambdaQueryWrapperX<DryWeightDO>()
                                .eq(DryWeightDO::getPatientId, patientDO.getId())
                                .select(DryWeightDO::getDryWeight, DryWeightDO::getState)
                                .last("limit 1")
                                .orderByDesc(DryWeightDO::getId));
                        if (dryWeightDO != null) {
                            if (StrUtil.isNotEmpty(dryWeightDO.getState())) {
                                updateReqVO.setDryWeight("1".equals(dryWeightDO.getState()) ? "卧床" : "待定");
                            } else {
                                updateReqVO.setDryWeight(dryWeightDO.getDryWeight());
                            }
                        }
                        //获取上次的衣物重信息
                        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                                .eq(HemodialysisManagerDO::getPatientId, patientDO.getId())
                                .select(HemodialysisManagerDO::getClothing,HemodialysisManagerDO::getAfterClothing)
                                .last("limit 1")
                                .orderByDesc(HemodialysisManagerDO::getId));
                        if (hemodialysisManagerDO != null) {
                            updateReqVO.setClothing(hemodialysisManagerDO.getClothing());
                            if (StringUtils.isEmpty(hemodialysisManagerDO.getAfterClothing()) || hemodialysisManagerDO.getAfterClothing().equals("0")) {
                                updateReqVO.setAfterClothing(hemodialysisManagerDO.getClothing());
                            }else {
                                updateReqVO.setAfterClothing(hemodialysisManagerDO.getAfterClothing());
                            }
                        }

                        ReachWeighDO reachWeighDO = reachWeighMapper.selectOne(new LambdaQueryWrapperX<ReachWeighDO>()
                                .eq(ReachWeighDO::getPatientId, patientDO.getId())
                                .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                        if (reachWeighDO != null){
                            // 透前数据
                            if (StringUtils.isEmpty(reachWeighDO.getDialyzeBeforeWeigh()) && StringUtils.isEmpty(reachWeighDO.getRno()) &&StringUtils.isEmpty(updateReqVO.getRno())&& StringUtils.isNotEmpty(updateReqVO.getDialyzeBeforeWeigh())) {
                                String rno = getRandomValue("breath");
                                if (StringUtils.isNotEmpty(rno)) {
                                    updateReqVO.setRno(rno);
                                }
                                String temperature = getRandomValue("temperature");
                                if (StringUtils.isNotEmpty(temperature)){
                                    updateReqVO.setDegreeCelsius(temperature);
                                }

                            }
                            // 透后数据
                            if (StringUtils.isEmpty(reachWeighDO.getAfterRNo()) && StringUtils.isEmpty(updateReqVO.getAfterRNo()) && StringUtils.isNotEmpty(updateReqVO.getDialyzeAfterWeigh())) {
                                String rno = getRandomValue("breath");
                                if (StringUtils.isNotEmpty(rno)) {
                                    updateReqVO.setAfterRNo(rno);
                                }
                                String temperature = getRandomValue("temperature");
                                if (StringUtils.isNotEmpty(temperature)){
                                    updateReqVO.setAfterDegreeCelsius(temperature);
                                }
                            }
                        }else{
                            if (StringUtils.isNotEmpty(updateReqVO.getDialyzeBeforeWeigh()) && StringUtils.isEmpty(updateReqVO.getRno())) {
                                String rno = getRandomValue("breath");
                                if (StringUtils.isNotEmpty(rno)) {
                                    updateReqVO.setRno(rno);
                                }
                                String temperature = getRandomValue("temperature");
                                if (StringUtils.isNotEmpty(temperature)){
                                    updateReqVO.setDegreeCelsius(temperature);
                                }
                            }
                        }
                        updateReqVO.setId(reachWeighDO != null ? reachWeighDO.getId() : null);
                    }
                    Log.info("入参人脸称重签到信息:{}", JSONUtil.toJsonStr(updateReqVO));
                }
                ReachWeighDO reachWeighDO1 = ReachWeighConvert.INSTANCE.convert(updateReqVO);
                DateTime registerTime = DateUtil.date();
                reachWeighDO1.setRegisterTime(registerTime);
                reachWeighDO1.setRegisterState("1");
                reachWeighDO1.setDialyzeBeforeWeight(getBeforeWeight(reachWeighDO1));
                reachWeighDO1.setDehydration(getTargetWater(reachWeighDO1));
                reachWeighDO1.setDehydrationPercent(getTargetScale(reachWeighDO1));
                reachWeighDO1.setDialyzeAfterWeight(getAfterWeight(reachWeighDO1));
                reachWeighDO1.setWeights(getWeights(reachWeighDO1));

                ArrangeClassesDO arrangeClassesDO = getArrangeClassesOne(reachWeighDO1);

                if (reachWeighDO1.getId() != null) {
                    //如果透析后点击保存是已下机状态
                    if (arrangeClassesDO != null && arrangeClassesDO.getState() > 2) {
                        reachWeighDO1.setRegisterState("5");
                    }
                    updateReachWeight(reachWeighDO1);
                } else {
                    Long count = reachWeighMapper.selectCount(new LambdaQueryWrapperX<ReachWeighDO>()
                            .eq(ReachWeighDO::getPatientId, reachWeighDO1.getPatientId())
                            .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                    if (count > 0) {
                        updateReachWeight(reachWeighDO1);
                    } else {
                        reachWeighMapper.insert(reachWeighDO1);
                    }
                }
                //修改已签到状态
                if (arrangeClassesDO != null && arrangeClassesDO.getState() == 0) {
                    ArrangeClassesDO arrangeClassesDO1 = new ArrangeClassesDO();
                    arrangeClassesDO1.setState(1);
                    arrangeClassesDO1.setRegisterTime(registerTime);
                    updateArrangeClasses(arrangeClassesDO1, reachWeighDO1);
                }
                //修改透析处方透前和透后评估
                HemodialysisManagerDO hemodialysisManagerOne = getHemodialysisManagerOne(reachWeighDO1);
                if (hemodialysisManagerOne != null) {
                    reachWeighDO1.setId(null);
                    HemodialysisManagerDO hemodialysisManagerDO = new HemodialysisManagerDO();
                    BeanUtil.copyProperties(reachWeighDO1, hemodialysisManagerDO);
                    updateHemodialysisManager(hemodialysisManagerDO);
                }
                //platformTransactionManager.commit(status);
            } else {
                log.info("没有拿到，离开了.......");
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("签到称重异常信息:{}", e.getMessage());
            //platformTransactionManager.rollback(status);
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    private String getRandomValue(String key) {
        String values = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(values)) {
            String[] split = values.split(",");
            Random rand = new Random();
            int min = 0;
            int max =split.length -1;
            int randomIntRange = min + rand.nextInt(max - min + 1);
            return split[randomIntRange];
        }
        return null;
    }


    //体重减少=透前体重-透后体重
    private String getWeights(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeight()) && StrUtil.isNotEmpty(reachWeighDO1.getDialyzeAfterWeight())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeight()).subtract(Convert.toBigDecimal(reachWeighDO1.getDialyzeAfterWeight()));
            return subtract.toString();
        }
        return null;
    }

    //透后体重=透后称重-衣物重
    private String getAfterWeight(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeAfterWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getAfterClothing())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeAfterWeigh()).subtract(Convert.toBigDecimal(reachWeighDO1.getAfterClothing()));
            return subtract.toString();
        }
        return null;
    }

    private void updateHemodialysisManager(HemodialysisManagerDO hemodialysisManagerDO) {
        hemodialysisManagerMapper.update(hemodialysisManagerDO, new LambdaUpdateWrapper<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, hemodialysisManagerDO.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(new Date())));
    }

    private HemodialysisManagerDO getHemodialysisManagerOne(ReachWeighDO reachWeighDO1) {
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, reachWeighDO1.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(new Date()))
                .select(HemodialysisManagerDO::getId));
        return hemodialysisManagerDO;
    }


    private void updateReachWeight(ReachWeighDO reachWeighDO1) {
        reachWeighMapper.update(reachWeighDO1, new LambdaUpdateWrapper<ReachWeighDO>()
                .eq(ReachWeighDO::getPatientId, reachWeighDO1.getPatientId())
                .between(ReachWeighDO::getRegisterTime, DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date())));
    }

    private void updateArrangeClasses(ArrangeClassesDO arrangeClassesDO1, ReachWeighDO reachWeighDO1) {
        arrangeClassesMapper.update(arrangeClassesDO1, new LambdaUpdateWrapper<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, reachWeighDO1.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()))
                .eq(ArrangeClassesDO::getTempType, 0));
    }

    private ArrangeClassesDO getArrangeClassesOne(ReachWeighDO reachWeighDO1) {
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getPatientId, reachWeighDO1.getPatientId())
                .eq(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()))
                .eq(ArrangeClassesDO::getTempType, 0)
                .select(ArrangeClassesDO::getState));
        return arrangeClassesDO;
    }


    //目标脱水百分比 = 目标脱水量/干体重 *100
    private String getTargetScale(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDehydration()) && StrUtil.isNotEmpty(reachWeighDO1.getDryWeight())) {
            //干体重不能为0
            if (!"0".equals(reachWeighDO1.getDryWeight())) {
                BigDecimal multiply = Convert.toBigDecimal(reachWeighDO1.getDehydration())
                        .divide(Convert.toBigDecimal(reachWeighDO1.getDryWeight()), 4, RoundingMode.DOWN)
                        .multiply(Convert.toBigDecimal("100").stripTrailingZeros());
                return multiply.toString();
            }
        }
        return null;
    }

    //目标脱水=透前称重-透前衣物重-干体重
    private String getTargetWater(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getClothing()) && StrUtil.isNotEmpty(reachWeighDO1.getDryWeight())) {
            BigDecimal subtract = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeigh())
                    .subtract(Convert.toBigDecimal(reachWeighDO1.getClothing()))
                    .subtract(Convert.toBigDecimal(reachWeighDO1.getDryWeight()));
            return subtract.toString();
        }
        return null;
    }

    //透前体重 = 透前称重 - 衣物重
    private String getBeforeWeight(ReachWeighDO reachWeighDO1) {
        if (StrUtil.isNotEmpty(reachWeighDO1.getDialyzeBeforeWeigh()) && StrUtil.isNotEmpty(reachWeighDO1.getClothing())) {
            BigDecimal multiply1 = Convert.toBigDecimal(reachWeighDO1.getDialyzeBeforeWeigh()).subtract(Convert.toBigDecimal(reachWeighDO1.getClothing()));
            return multiply1.toString();
        }
        return null;
    }
}
