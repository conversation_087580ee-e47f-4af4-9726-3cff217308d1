package com.thj.boot.module.business.convert.hisinformation;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationCreateReqVO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationRespVO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * his 中心服务项目信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HisInformationConvert {

    HisInformationConvert INSTANCE = Mappers.getMapper(HisInformationConvert.class);

    HisInformationDO convert(HisInformationCreateReqVO bean);

    HisInformationDO convert(HisInformationUpdateReqVO bean);

    HisInformationRespVO convert(HisInformationDO bean);

    List<HisInformationRespVO> convertList(List<HisInformationDO> list);

    PageResult<HisInformationRespVO> convertPage(PageResult<HisInformationDO> page);


}
