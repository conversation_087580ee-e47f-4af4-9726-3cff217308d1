package com.thj.boot.module.business.convert.gksign;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gksign.GkSignDO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignCreateReqVO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignRespVO;
import com.thj.boot.module.business.pojo.gksign.vo.GkSignUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 感控培训签到 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GkSignConvert {

    GkSignConvert INSTANCE = Mappers.getMapper(GkSignConvert.class);

    GkSignDO convert(GkSignCreateReqVO bean);

    GkSignDO convert(GkSignUpdateReqVO bean);

    GkSignRespVO convert(GkSignDO bean);

    List<GkSignRespVO> convertList(List<GkSignDO> list);

    PageResult<GkSignRespVO> convertPage(PageResult<GkSignDO> page);


}
