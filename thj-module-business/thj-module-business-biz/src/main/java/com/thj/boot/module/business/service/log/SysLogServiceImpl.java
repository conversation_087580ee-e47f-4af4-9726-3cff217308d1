package com.thj.boot.module.business.service.log;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.dal.datado.log.SysLogDO;
import com.thj.boot.module.business.dal.mapper.jkdivision.JkDivisionMapper;
import com.thj.boot.module.business.dal.mapper.log.SysLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 系统日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SysLogServiceImpl implements SysLogService {

    @Resource
    private SysLogMapper sysLogMapper;

    @Resource
    JkDivisionMapper jkDivisionMapper;

    @Override
    public void createSysLog(SysLogDO sysLogDO) {

        Map<String, Object> map = jkDivisionMapper.queryUserInfo(Long.valueOf(sysLogDO.getOperateId()));
        sysLogDO.setOperateId(String.valueOf(map.get("id")));
        sysLogDO.setOperateName(String.valueOf(map.get("nickname")));
        // 插入
         sysLogMapper.insert(sysLogDO);
    }

    @Override
    public void updateSysLog(SysLogDO updateReqVO) {
        // 更新
        sysLogMapper.updateById(updateReqVO);
    }

    @Override
    public void deleteSysLog(Long id) {
        // 删除
        sysLogMapper.deleteById(id);
    }


    @Override
    public SysLogDO getSysLog(Long id) {
        return sysLogMapper.selectById(id);
    }

    @Override
    public List<SysLogDO> getSysLogList(SysLogDO sysLogDO) {
        MPJLambdaWrapper<SysLogDO> wrapper = new MPJLambdaWrapper<SysLogDO>(SysLogDO.class);
        wrapper.eq(StringUtils.isNotNull(sysLogDO.getOperation()),SysLogDO::getOperation,sysLogDO.getOperation());
        wrapper.ge(StringUtils.isNotNull(sysLogDO.getBeginTime()),SysLogDO::getCreateTime,sysLogDO.getBeginTime());
        wrapper.le(StringUtils.isNotNull(sysLogDO.getEndTime()),SysLogDO::getCreateTime,sysLogDO.getEndTime());
        return sysLogMapper.selectList(wrapper);
    }

    @Override
    public PageResult<SysLogDO> getSysLogPage(SysLogDO pageReqVO) {
//        return sysLogMapper.selectPage(pageReqVO);
        return null;
    }


}
