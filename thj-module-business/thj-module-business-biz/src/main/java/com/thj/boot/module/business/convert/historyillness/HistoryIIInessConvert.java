package com.thj.boot.module.business.convert.historyillness;

import com.thj.boot.module.business.dal.datado.historyillness.HistoryIllnessDO;
import com.thj.boot.module.business.pojo.historyillness.HistoryIllnessCreateReqVO;
import com.thj.boot.module.business.pojo.historyillness.HistoryIllnessRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 医院感染管理质控 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HistoryIIInessConvert {

    HistoryIIInessConvert INSTANCE = Mappers.getMapper(HistoryIIInessConvert.class);

    HistoryIllnessDO convert(HistoryIllnessCreateReqVO createReqVO);

    List<HistoryIllnessRespVO> convertList(List<HistoryIllnessDO> historyIllnessDOS);
}
