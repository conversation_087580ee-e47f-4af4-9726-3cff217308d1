package com.thj.boot.module.business.convert.puncture;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.puncture.PunctureDO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureCreateReqVO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureRespVO;
import com.thj.boot.module.business.pojo.puncture.vo.PunctureUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 穿刺信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PunctureConvert {

    PunctureConvert INSTANCE = Mappers.getMapper(PunctureConvert.class);

    PunctureDO convert(PunctureCreateReqVO bean);

    PunctureDO convert(PunctureUpdateReqVO bean);

    PunctureRespVO convert(PunctureDO bean);

    List<PunctureRespVO> convertList(List<PunctureDO> list);

    PageResult<PunctureRespVO> convertPage(PageResult<PunctureDO> page);


}
