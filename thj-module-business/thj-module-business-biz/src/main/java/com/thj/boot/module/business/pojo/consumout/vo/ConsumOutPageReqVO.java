package com.thj.boot.module.business.pojo.consumout.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConsumOutPageReqVO extends PageParam {

    private LocalDateTime[] createTime;

    private Long creator;

    private String consumName;

    private String consumNumber;

    private String consumId;

    private String consumType;

    private Long patientId;

    private Integer cunt;

}
