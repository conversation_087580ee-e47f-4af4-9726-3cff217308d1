package com.thj.boot.module.business.service.project;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.pojo.project.vo.ProjectCreateReqVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectPageReqVO;
import com.thj.boot.module.business.pojo.project.vo.ProjectUpdateReqVO;

import java.util.List;

/**
 * 项目 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectService {

    /**
     * 创建项目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProject(ProjectCreateReqVO createReqVO);

    /**
     * 更新项目
     *
     * @param updateReqVO 更新信息
     */
    void updateProject(ProjectUpdateReqVO updateReqVO);

    /**
     * 删除项目
     *
     * @param id 编号
     */
    void deleteProject(Long id);

    /**
     * 获得项目
     *
     * @param id 编号
     * @return 项目
     */
    ProjectDO getProject(Long id);


    /**
     * 获得项目分页
     *
     * @param pageReqVO 分页查询
     * @return 项目分页
     */
    PageResult<ProjectDO> getProjectPage(ProjectPageReqVO pageReqVO);

    /**
     * 获得项目列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 项目列表
     */
    List<ProjectDO> getProjectList(ProjectCreateReqVO createReqVO);

}
