package com.thj.boot.module.business.convert.hemodialysismonitoring;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.hemodialysismonitoring.HemodialysisMonitoringDO;
import com.thj.boot.module.business.pojo.hemodialysismonitoring.vo.HemodialysisMonitoringCreateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismonitoring.vo.HemodialysisMonitoringRespVO;
import com.thj.boot.module.business.pojo.hemodialysismonitoring.vo.HemodialysisMonitoringUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 血透事件监测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HemodialysisMonitoringConvert {

    HemodialysisMonitoringConvert INSTANCE = Mappers.getMapper(HemodialysisMonitoringConvert.class);

    HemodialysisMonitoringDO convert(HemodialysisMonitoringCreateReqVO bean);

    HemodialysisMonitoringDO convert(HemodialysisMonitoringUpdateReqVO bean);

    HemodialysisMonitoringRespVO convert(HemodialysisMonitoringDO bean);

    List<HemodialysisMonitoringRespVO> convertList(List<HemodialysisMonitoringDO> list);

    PageResult<HemodialysisMonitoringRespVO> convertPage(PageResult<HemodialysisMonitoringDO> page);


}
