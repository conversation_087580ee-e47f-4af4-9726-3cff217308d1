package com.thj.boot.module.business.service.ductfall;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.ductfall.DuctFallConvert;
import com.thj.boot.module.business.dal.datado.ductfall.DuctFallDO;
import com.thj.boot.module.business.dal.mapper.ductfall.DuctFallMapper;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallCreateReqVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallPageReqVO;
import com.thj.boot.module.business.pojo.ductfall.vo.DuctFallUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 导管脱落风险评估 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DuctFallServiceImpl implements DuctFallService {

    @Resource
    private DuctFallMapper ductFallMapper;

    @Override
    public Long createDuctFall(DuctFallCreateReqVO createReqVO) {
        // 插入
        DuctFallDO ductFall = DuctFallConvert.INSTANCE.convert(createReqVO);
        ductFallMapper.insert(ductFall);
        // 返回
        return ductFall.getId();
    }

    @Override
    public void updateDuctFall(DuctFallUpdateReqVO updateReqVO) {
        // 更新
        DuctFallDO updateObj = DuctFallConvert.INSTANCE.convert(updateReqVO);
        ductFallMapper.updateById(updateObj);
    }

    @Override
    public void deleteDuctFall(Long id) {
        // 删除
        ductFallMapper.deleteById(id);
    }


    @Override
    public DuctFallDO getDuctFall(Long id) {
        return ductFallMapper.selectById(id);
    }

    @Override
    public List<DuctFallDO> getDuctFallList(Collection<Long> ids) {
        return ductFallMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DuctFallDO> getDuctFallPage(DuctFallPageReqVO pageReqVO) {
        return ductFallMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DuctFallDO> getDuctFallList(DuctFallCreateReqVO createReqVO) {
        return ductFallMapper.selectList(createReqVO);
    }

}
