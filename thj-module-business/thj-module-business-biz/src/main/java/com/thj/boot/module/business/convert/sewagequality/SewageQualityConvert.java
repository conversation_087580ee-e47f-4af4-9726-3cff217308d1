package com.thj.boot.module.business.convert.sewagequality;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.sewagequality.SewageQualityDO;
import com.thj.boot.module.business.pojo.sewagequality.vo.SewageQualityCreateReqVO;
import com.thj.boot.module.business.pojo.sewagequality.vo.SewageQualityRespVO;
import com.thj.boot.module.business.pojo.sewagequality.vo.SewageQualityUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 污水质量检测 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SewageQualityConvert {

    SewageQualityConvert INSTANCE = Mappers.getMapper(SewageQualityConvert.class);

    SewageQualityDO convert(SewageQualityCreateReqVO bean);

    SewageQualityDO convert(SewageQualityUpdateReqVO bean);

    SewageQualityRespVO convert(SewageQualityDO bean);

    List<SewageQualityRespVO> convertList(List<SewageQualityDO> list);

    PageResult<SewageQualityRespVO> convertPage(PageResult<SewageQualityDO> page);


}
