package com.thj.boot.module.business.pojo.inspectsettings.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectSettingsPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 检验项目
     */
    private String checkProject;
    /**
     * 检验频率
     */
    private String frequency;
    /**
     * 提前预警
     */
    private String beforeWarn;
    /**
     * 备注
     */
    private String remark;

}
