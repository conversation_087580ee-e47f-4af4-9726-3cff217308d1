package com.thj.boot.module.business.convert.weightsurine;


import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.weightsurine.WeightsUrineDO;
import com.thj.boot.module.business.pojo.weightsurine.vo.WeightsUrineCreateReqVO;
import com.thj.boot.module.business.pojo.weightsurine.vo.WeightsUrineRespVO;
import com.thj.boot.module.business.pojo.weightsurine.vo.WeightsUrineUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 体重 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WeightsUrineConvert {

    WeightsUrineConvert INSTANCE = Mappers.getMapper(WeightsUrineConvert.class);

    WeightsUrineDO convert(WeightsUrineCreateReqVO bean);

    WeightsUrineDO convert(WeightsUrineUpdateReqVO bean);

    WeightsUrineRespVO convert(WeightsUrineDO bean);

    List<WeightsUrineRespVO> convertList(List<WeightsUrineDO> list);

    PageResult<WeightsUrineRespVO> convertPage(PageResult<WeightsUrineDO> page);


}
