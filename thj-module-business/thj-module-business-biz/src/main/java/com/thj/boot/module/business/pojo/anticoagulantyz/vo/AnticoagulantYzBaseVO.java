package com.thj.boot.module.business.pojo.anticoagulantyz.vo;

import com.thj.boot.common.dataobject.BaseDO;
import lombok.Data;

/**
* 医嘱 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AnticoagulantYzBaseVO extends BaseDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 透析方案id
     */
    private Long mottoHardId;
    /**
     * 抗凝剂类型(存入字典)
     */
    private String dictValue;
    /**
     * 医嘱id
     */
    private Long yzId;
    /**
     * 医嘱名称
     */
    private String yzName;
    /**
     * 单次用量
     */
    private String once;
    /**
     * 开药数量
     */
    private String num;
    /**
     * 给药途径
     */
    private String way;
    /**
     * 执行频率
     */
    private String frequency;
    /**
     * 备注
     */
    private String remark;

    private Boolean deleted;
}
