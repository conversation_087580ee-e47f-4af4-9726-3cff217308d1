package com.thj.boot.module.business.service.gkgroup;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.gkgroup.GkGroupDO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupCreateReqVO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupPageReqVO;
import com.thj.boot.module.business.pojo.gkgroup.vo.GkGroupUpdateReqVO;

import java.util.List;

/**
 * 分组 Service 接口
 *
 * <AUTHOR>
 */
public interface GkGroupService {

    /**
     * 创建分组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGkGroup(GkGroupCreateReqVO createReqVO);

    /**
     * 更新分组
     *
     * @param updateReqVO 更新信息
     */
    void updateGkGroup(GkGroupUpdateReqVO updateReqVO);

    /**
     * 删除分组
     *
     * @param id 编号
     */
    void deleteGkGroup(Long id);

    /**
     * 获得分组
     *
     * @param id 编号
     * @return 分组
     */
    GkGroupDO getGkGroup(Long id);

    /**
     * 获得分组列表
     *
     * @param ids 编号
     * @return 分组列表
     */
    List<GkGroupDO> getGkGroupList(String groupType);

    /**
     * 获得分组分页
     *
     * @param pageReqVO 分页查询
     * @return 分组分页
     */
    PageResult<GkGroupDO> getGkGroupPage(GkGroupPageReqVO pageReqVO);


}
