package com.thj.boot.module.business.pojo.reason.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReasonPageReqVO extends PageParam {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 层级
     */
    private String ancestors;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;

}
