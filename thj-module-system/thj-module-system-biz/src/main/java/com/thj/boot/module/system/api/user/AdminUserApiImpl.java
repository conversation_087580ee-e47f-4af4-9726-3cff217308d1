package com.thj.boot.module.system.api.user;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.boot.module.system.convert.user.AdminUserConvert;
import com.thj.boot.module.system.convert.user.UserConvert;
import com.thj.boot.module.system.dal.datado.permission.UserRoleDO;
import com.thj.boot.module.system.dal.datado.user.AdminUserDO;
import com.thj.boot.module.system.dal.mapper.user.AdminUserMapper;
import com.thj.boot.module.system.pojo.user.vo.UserCreateReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserPageReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserRespVO;
import com.thj.boot.module.system.service.user.AdminUserService;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 9:13
 * @description
 */
@Service
@Slf4j
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private AdminUserService userService;

    @Override
    public AdminUserRespDTO getUser(Long id) {
        AdminUserDO user = userService.getUser(id);
        return UserConvert.INSTANCE.convert4(user);
    }


    @Override
    public UserRespDTO getAdminUser(UserCreateReqDTO createReqDTO) {
        AdminUserDO userDO = adminUserMapper.selectOne(new LambdaQueryWrapperX<AdminUserDO>()
                .eqIfPresent(AdminUserDO::getId, createReqDTO.getId())
                .eqIfPresent(AdminUserDO::getPassword, createReqDTO.getPassword())
                .inIfPresent(AdminUserDO::getRoleIdStr, createReqDTO.getRoleId()));
        return AdminUserConvert.INSTANCE.convert5(userDO);
    }

    @Override
    @Cacheable(cacheNames = "adminList", key = "#userRespDTO.deptId", condition = " #userRespDTO != null && #userRespDTO.deptId != null")
    public List<UserRespDTO> adminUserList(UserRespDTO userRespDTO) {
        UserCreateReqVO createReqVO = AdminUserConvert.INSTANCE.convert6(userRespDTO);
        List<AdminUserDO> adminUserDOS = adminUserMapper.selectList(createReqVO,0L);
        return AdminUserConvert.INSTANCE.convert7(adminUserDOS);
    }

    @Override
    public void updateAdminUser(UserCreateReqDTO createReqDTO) {
        AdminUserDO adminUserDO = AdminUserConvert.INSTANCE.convert(createReqDTO);
        adminUserMapper.update(adminUserDO, new LambdaUpdateWrapper<AdminUserDO>().eq(AdminUserDO::getId, createReqDTO.getId()));
    }

    @Override
    public List<UserRespDTO> selectUserJoinRole() {
        MPJLambdaWrapper<AdminUserDO> wrapper = new MPJLambdaWrapper<>(AdminUserDO.class);
        wrapper.leftJoin(UserRoleDO.class, UserRoleDO::getUserId, AdminUserDO::getId)
                .select(AdminUserDO::getId, AdminUserDO::getNickname)
                .select(UserRoleDO::getRoleId);
        return adminUserMapper.selectJoinList(UserRespDTO.class, wrapper);
    }

    @Override
    public UserRespDTO getAdminUserInfo(Long id) {
        AdminUserDO adminUserDO = adminUserMapper.selectById(id);
        return AdminUserConvert.INSTANCE.convert5(adminUserDO);
    }

    @Override
    public List<UserRespDTO> getUserByRole(String roleCode, String deptId) {
        List<UserRespVO> userByRole = userService.getUserByRole(roleCode, Long.valueOf(deptId));
        return AdminUserConvert.INSTANCE.convertList1(userByRole);
    }

    @Override
    public PageResult<UserRespDTO> getUserByRolePage(String roleCode, Long userId, String deptId, Integer pageNo, Integer size) {
        UserPageReqVO userPageReqVO = new UserPageReqVO();
        userPageReqVO.setRoleCode(roleCode);
        userPageReqVO.setDeptId(Long.valueOf(deptId));
        userPageReqVO.setPageNo(pageNo);
        userPageReqVO.setPageSize(size);
        userPageReqVO.setUserId(userId);
        PageResult<AdminUserDO> userByRolePage = userService.getUserByRolePage(userPageReqVO);
        List<UserRespDTO> userRespDTOS = AdminUserConvert.INSTANCE.convert7(userByRolePage.getList());
        return new PageResult<>(userRespDTOS, userByRolePage.getTotal());
    }

}
