package com.thj.boot.module.system.convert.dict;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.dal.datado.dict.DictTypeDO;
import com.thj.boot.module.system.pojo.dict.vo.type.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface DictTypeConvert {

    DictTypeConvert INSTANCE = Mappers.getMapper(DictTypeConvert.class);

    PageResult<DictTypeRespVO> convertPage(PageResult<DictTypeDO> bean);

    DictTypeRespVO convert(DictTypeDO bean);

    DictTypeDO convert(DictTypeCreateReqVO bean);

    DictTypeDO convert(DictTypeUpdateReqVO bean);

    List<DictTypeSimpleRespVO> convertList(List<DictTypeDO> list);

    List<DictTypeExcelVO> convertList02(List<DictTypeDO> list);

    List<DictTypeRespVO> convertList2(List<DictTypeDO> dictTypeDOS);

    // Add explicit mapping methods to fix MapStruct compilation errors
    default DictTypeSimpleRespVO map(DictTypeDO dictTypeDO) {
        if (dictTypeDO == null) {
            return null;
        }
        DictTypeSimpleRespVO vo = new DictTypeSimpleRespVO();
        vo.setId(dictTypeDO.getId());
        vo.setName(dictTypeDO.getName());
        vo.setType(dictTypeDO.getType());
        return vo;
    }

    default DictTypeExcelVO mapToExcel(DictTypeDO dictTypeDO) {
        if (dictTypeDO == null) {
            return null;
        }
        DictTypeExcelVO vo = new DictTypeExcelVO();
        vo.setId(dictTypeDO.getId());
        vo.setName(dictTypeDO.getName());
        vo.setType(dictTypeDO.getType());
        vo.setStatus(dictTypeDO.getStatus());
        return vo;
    }
}
