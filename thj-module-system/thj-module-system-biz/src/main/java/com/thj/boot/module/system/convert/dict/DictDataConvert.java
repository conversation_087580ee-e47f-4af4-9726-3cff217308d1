package com.thj.boot.module.system.convert.dict;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.dal.datado.dict.DictDataDO;
import com.thj.boot.module.system.pojo.dict.vo.data.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DictDataConvert {

    DictDataConvert INSTANCE = Mappers.getMapper(DictDataConvert.class);

    List<DictDataSimpleRespVO> convertList(List<DictDataDO> list);

    DictDataRespVO convert(DictDataDO bean);

    PageResult<DictDataRespVO> convertPage(PageResult<DictDataDO> page);

    DictDataDO convert(DictDataUpdateReqVO bean);

    DictDataDO convert(DictDataCreateReqVO bean);

    List<DictDataExcelVO> convertList02(List<DictDataDO> bean);

    DictDataRespDTO convert02(DictDataDO bean);

    List<DictDataRespDTO> convert03(List<DictDataDO> dictDataList);

    // Add explicit mapping methods to fix MapStruct compilation errors
    default DictDataSimpleRespVO map(DictDataDO dictDataDO) {
        if (dictDataDO == null) {
            return null;
        }
        DictDataSimpleRespVO vo = new DictDataSimpleRespVO();
        vo.setId(dictDataDO.getId());
        vo.setDictType(dictDataDO.getDictType());
        vo.setValue(dictDataDO.getValue());
        vo.setLabel(dictDataDO.getLabel());
        vo.setColorType(dictDataDO.getColorType());
        vo.setCssClass(dictDataDO.getCssClass());
        vo.setSort(dictDataDO.getSort());
        vo.setRemark(dictDataDO.getRemark());
        return vo;
    }

    default DictDataExcelVO mapToExcel(DictDataDO dictDataDO) {
        if (dictDataDO == null) {
            return null;
        }
        DictDataExcelVO vo = new DictDataExcelVO();
        vo.setId(dictDataDO.getId());
        vo.setSort(dictDataDO.getSort());
        vo.setLabel(dictDataDO.getLabel());
        vo.setValue(dictDataDO.getValue());
        vo.setDictType(dictDataDO.getDictType());
        vo.setStatus(dictDataDO.getStatus());
        return vo;
    }

}
