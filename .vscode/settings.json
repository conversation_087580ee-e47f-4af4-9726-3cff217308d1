{"mcp.servers": {"mysql": {"command": "node", "args": ["mysql_mcp_server/simple-mcp-server.js"], "cwd": "${workspaceFolder}", "env": {"MYSQL_HOST": "***********", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "44033@lzd", "MYSQL_DATABASE": "bailun"}}}, "mcp.client.autoStart": true, "mcp.client.logLevel": "debug", "files.associations": {"*.sql": "sql"}, "sql.connections": [{"name": "MySQL Bailun", "server": "***********", "port": 3306, "database": "bailun", "user": "root", "password": "44033@lzd", "authenticationType": "SqlLogin"}]}