{"version": "0.2.0", "configurations": [{"name": "Start MySQL MCP Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/mysql_mcp_server/index.js", "console": "integratedTerminal", "env": {"MYSQL_HOST": "***********", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "44033@lzd", "MYSQL_DATABASE": "bailun", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false", "NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"]}, {"name": "Start MySQL MCP Server (Production)", "type": "node", "request": "launch", "program": "${workspaceFolder}/mysql_mcp_server/index.js", "console": "integratedTerminal", "env": {"MYSQL_HOST": "***********", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "44033@lzd", "MYSQL_DATABASE": "bailun", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false", "NODE_ENV": "production"}, "skipFiles": ["<node_internals>/**"]}]}