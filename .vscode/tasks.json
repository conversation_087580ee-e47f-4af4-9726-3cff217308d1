{"version": "2.0.0", "tasks": [{"label": "Start MySQL MCP Server", "type": "shell", "command": "node", "args": ["mysql_mcp_server/index.js"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"env": {"MYSQL_HOST": "***********", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "44033@lzd", "MYSQL_DATABASE": "bailun", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false"}}, "problemMatcher": []}, {"label": "Install MySQL MCP Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/mysql_mcp_server"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Test MySQL Connection", "type": "shell", "command": "node", "args": ["-e", "const mysql = require('mysql2/promise'); mysql.createConnection({host: '***********', port: 3306, user: 'root', password: '44033@lzd', database: 'bailun'}).then(conn => {console.log('✅ MySQL connection successful!'); conn.end();}).catch(err => {console.error('❌ MySQL connection failed:', err.message); process.exit(1);});"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}