!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?s(r.parts[t][0])+"-"+s(r.parts[t][1]):s(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function s(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,s=new Array(r.length);for(t=0;t<r.length;t++)s[t]=(o=r[t],e[o.type](o));if(s.sort(),s.length>0){for(t=1,n=1;t<s.length;t++)s[t-1]!==s[t]&&(s[n]=s[t],n++);s.length=n}switch(s.length){case 1:return s[0];case 2:return s[0]+" or "+s[1];default:return s.slice(0,-1).join(", ")+", or "+s[s.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,s={},u={start:Pc},a=Pc,i=Nc("IF",!0),l=Nc("if",!0),f=Nc("exists",!0),p=Nc("EXTENSION",!0),b=Nc("SCHEMA",!0),v=Nc("VERSION",!0),y=Nc("CASCADED",!0),d=Nc("LOCAL",!0),w=Nc("CHECK",!0),L=Nc("OPTION",!1),h=Nc("check_option",!0),C=Nc("security_barrier",!0),m=Nc("security_invoker",!0),E=Nc("SFUNC",!0),A=Nc("STYPE",!0),g=Nc("AGGREGATE",!0),T=Nc("RETURNS",!0),_=Nc("SETOF",!0),S=Nc("CONSTANT",!0),x=Nc(":=",!1),j=Nc("BEGIN",!0),I=Nc("DECLARE",!0),R=Nc("LANGUAGE",!1),N=Nc("TRANSORM",!0),O=Nc("FOR",!1),k=Nc("TYPE",!1),U=Nc("WINDOW",!0),M=Nc("IMMUTABLE",!0),D=Nc("STABLE",!0),P=Nc("VOLATILE",!0),G=Nc("STRICT",!0),$=Nc("NOT",!0),F=Nc("LEAKPROOF",!0),B=Nc("CALLED",!0),H=Nc("NULL",!0),q=Nc("ON",!0),Y=Nc("INPUT",!0),W=Nc("EXTERNAL",!0),V=Nc("SECURITY",!0),X=Nc("INVOKER",!0),Q=Nc("DEFINER",!0),K=Nc("PARALLEL",!0),z=Nc("UNSAFE",!0),Z=Nc("RESTRICTED",!0),J=Nc("SAFE",!0),rr=/^[^ s\t\n\r]/,tr=Oc([" ","s","\t","\n","\r"],!0,!1),er=/^[^ s\t\n\r;]/,nr=Oc([" ","s","\t","\n","\r",";"],!0,!1),or=Nc("COST",!0),sr=Nc("ROWS",!0),ur=Nc("SUPPORT",!0),ar=Nc("TO",!0),ir=Nc("=",!1),cr=Nc("CURRENT",!0),lr=Nc("FUNCTION",!0),fr=Nc("RANGE",!0),pr=Nc("TYPE",!0),br=Nc("DOMAIN",!0),vr=Nc("INCREMENT",!0),yr=Nc("MINVALUE",!0),dr=function(r,t){return{resource:"sequence",prefix:r.toLowerCase(),value:t}},wr=Nc("NO",!0),Lr=Nc("MAXVALUE",!0),hr=Nc("START",!0),Cr=Nc("CACHE",!0),mr=Nc("CYCLE",!0),Er=Nc("OWNED",!0),Ar=Nc("NONE",!0),gr=Nc("INCLUDE",!0),Tr=Nc("NULLS",!0),_r=Nc("FIRST",!0),Sr=Nc("LAST",!0),xr=Nc("MODULUS",!0),jr=Nc("REMAINDER",!0),Ir=Nc("FOR",!0),Rr=Nc("OF",!0),Nr=Nc("AUTO_INCREMENT",!0),Or=Nc("UNIQUE",!0),kr=Nc("KEY",!0),Ur=Nc("PRIMARY",!0),Mr=Nc("COLUMN_FORMAT",!0),Dr=Nc("FIXED",!0),Pr=Nc("DYNAMIC",!0),Gr=Nc("DEFAULT",!0),$r=Nc("STORAGE",!0),Fr=Nc("DISK",!0),Br=Nc("MEMORY",!0),Hr=Nc("CASCADE",!0),qr=Nc("RESTRICT",!0),Yr=Nc("ONLY",!0),Wr=Nc("RESTART",!0),Vr=Nc("CONTINUE",!0),Xr=Nc("IDENTITY",!0),Qr=Nc("OUT",!0),Kr=Nc("VARIADIC",!0),zr=Nc("only",!0),Zr=Nc("OWNER",!0),Jr=Nc("CURRENT_ROLE",!0),rt=Nc("CURRENT_USER",!0),tt=Nc("SESSION_USER",!0),et=Nc("ALGORITHM",!0),nt=Nc("INSTANT",!0),ot=Nc("INPLACE",!0),st=Nc("COPY",!0),ut=Nc("LOCK",!0),at=Nc("SHARED",!0),it=Nc("EXCLUSIVE",!0),ct=Nc("data",!0),lt=Nc("type",!0),ft=Nc("PRIMARY KEY",!0),pt=Nc("FOREIGN KEY",!0),bt=Nc("ENFORCED",!0),vt=Nc("MATCH FULL",!0),yt=Nc("MATCH PARTIAL",!0),dt=Nc("MATCH SIMPLE",!0),wt=Nc("SET NULL",!0),Lt=Nc("NO ACTION",!0),ht=Nc("SET DEFAULT",!0),Ct=Nc("TRIGGER",!0),mt=Nc("BEFORE",!0),Et=Nc("AFTER",!0),At=Nc("INSTEAD OF",!0),gt=Nc("EXECUTE",!0),Tt=Nc("PROCEDURE",!0),_t=Nc("DEFERRABLE",!0),St=Nc("INITIALLY IMMEDIATE",!0),xt=Nc("INITIALLY DEFERRED",!0),jt=Nc("EACH",!0),It=Nc("ROW",!0),Rt=Nc("STATEMENT",!0),Nt=Nc("CHARACTER",!0),Ot=Nc("SET",!0),kt=Nc("CHARSET",!0),Ut=Nc("COLLATE",!0),Mt=Nc("AVG_ROW_LENGTH",!0),Dt=Nc("KEY_BLOCK_SIZE",!0),Pt=Nc("MAX_ROWS",!0),Gt=Nc("MIN_ROWS",!0),$t=Nc("STATS_SAMPLE_PAGES",!0),Ft=Nc("CONNECTION",!0),Bt=Nc("COMPRESSION",!0),Ht=Nc("'",!1),qt=Nc("ZLIB",!0),Yt=Nc("LZ4",!0),Wt=Nc("ENGINE",!0),Vt=Nc("IN",!0),Xt=Nc("ACCESS SHARE",!0),Qt=Nc("ROW SHARE",!0),Kt=Nc("ROW EXCLUSIVE",!0),zt=Nc("SHARE UPDATE EXCLUSIVE",!0),Zt=Nc("SHARE ROW EXCLUSIVE",!0),Jt=Nc("ACCESS EXCLUSIVE",!0),re=Nc("SHARE",!0),te=Nc("MODE",!0),ee=Nc("NOWAIT",!0),ne=Nc("TABLES",!0),oe=Nc("PREPARE",!0),se=Nc("USAGE",!0),ue=function(r){return{type:"origin",value:Array.isArray(r)?r[0]:r}},ae=Nc("CONNECT",!0),ie=Nc("PRIVILEGES",!0),ce=function(r){return{type:"origin",value:r}},le=Nc("SEQUENCE",!0),fe=Nc("DATABASE",!0),pe=Nc("DOMAIN",!1),be=Nc("FUNCTION",!1),ve=Nc("ROUTINE",!0),ye=Nc("LANGUAGE",!0),de=Nc("LARGE",!0),we=Nc("SCHEMA",!1),Le=Nc("FUNCTIONS",!0),he=Nc("PROCEDURES",!0),Ce=Nc("ROUTINES",!0),me=Nc("PUBLIC",!0),Ee=Nc("GRANT",!0),Ae=Nc("OPTION",!0),ge=Nc("ADMIN",!0),Te=Nc("REVOKE",!0),_e=Nc("ELSEIF",!0),Se=Nc("THEN",!0),xe=Nc("END",!0),je=Nc("DEBUG",!0),Ie=Nc("LOG",!0),Re=Nc("INFO",!0),Ne=Nc("NOTICE",!0),Oe=Nc("WARNING",!0),ke=Nc("EXCEPTION",!0),Ue=Nc("MESSAGE",!0),Me=Nc("DETAIL",!0),De=Nc("HINT",!0),Pe=Nc("ERRCODE",!0),Ge=Nc("COLUMN",!0),$e=Nc("CONSTRAINT",!0),Fe=Nc("DATATYPE",!0),Be=Nc("TABLE",!0),He=Nc("SQLSTATE",!0),qe=Nc("RAISE",!0),Ye=Nc("LOOP",!0),We=Nc("SERIALIZABLE",!0),Ve=Nc("REPEATABLE",!0),Xe=Nc("READ",!0),Qe=Nc("COMMITTED",!0),Ke=Nc("UNCOMMITTED",!0),ze=function(r){return{type:"origin",value:"read "+r.toLowerCase()}},Ze=Nc("ISOLATION",!0),Je=Nc("LEVEL",!0),rn=Nc("WRITE",!0),tn=Nc("commit",!0),en=Nc("rollback",!0),nn=Nc("begin",!0),on=Nc("WORK",!0),sn=Nc("TRANSACTION",!0),un=Nc("start",!0),an=Nc("transaction",!0),cn=Nc("ROLE",!0),ln=Nc("SERVER",!0),fn=Nc("SUBSCRIPTION",!0),pn=Nc("IS",!0),bn=Nc("COMMENT",!0),vn=Nc("(",!1),yn=Nc(")",!1),dn=Nc(";",!1),wn=Nc("AT",!0),Ln=Nc("ZONE",!0),hn=Nc("OUTFILE",!0),Cn=Nc("DUMPFILE",!0),mn=Nc("BTREE",!0),En=Nc("HASH",!0),An=Nc("GIST",!0),gn=Nc("GIN",!0),Tn=Nc("WITH",!0),_n=Nc("PARSER",!0),Sn=Nc("VISIBLE",!0),xn=Nc("INVISIBLE",!0),jn=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Ky[t]=t,e&&(Ky[e]=t),function(r){const t=qy(r);r.clear(),t.forEach(t=>r.add(t))}(Xy)}),t},In=Nc("LATERAL",!0),Rn=Nc("TABLESAMPLE",!0),Nn=Nc("CROSS",!0),On=Nc("FOLLOWING",!0),kn=Nc("PRECEDING",!0),Un=Nc("UNBOUNDED",!0),Mn=Nc("DO",!0),Dn=Nc("NOTHING",!0),Pn=Nc("CONFLICT",!0),Gn=function(r,t){return By(r,t)},$n=Nc("!",!1),Fn=Nc(">=",!1),Bn=Nc(">",!1),Hn=Nc("<=",!1),qn=Nc("<>",!1),Yn=Nc("<",!1),Wn=Nc("!=",!1),Vn=Nc("SIMILAR",!0),Xn=Nc("!~*",!1),Qn=Nc("~*",!1),Kn=Nc("~",!1),zn=Nc("!~",!1),Zn=Nc("ESCAPE",!0),Jn=Nc("+",!1),ro=Nc("-",!1),to=Nc("*",!1),eo=Nc("/",!1),no=Nc("%",!1),oo=Nc("||",!1),so=Nc("$",!1),uo=Nc("?|",!1),ao=Nc("?&",!1),io=Nc("?",!1),co=Nc("#-",!1),lo=Nc("#>>",!1),fo=Nc("#>",!1),po=Nc("@>",!1),bo=Nc("<@",!1),vo=Nc("E",!0),yo=function(r){return{type:"default",value:r}},wo=function(r){return!0===My[r.toUpperCase()]},Lo=Nc('"',!1),ho=/^[^"]/,Co=Oc(['"'],!0,!1),mo=/^[^']/,Eo=Oc(["'"],!0,!1),Ao=Nc("`",!1),go=/^[^`]/,To=Oc(["`"],!0,!1),_o=/^[A-Za-z_\u4E00-\u9FA5]/,So=Oc([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),xo=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,jo=Oc([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),Io=/^[A-Za-z0-9_\u4E00-\u9FA5\xC0-\u017F]/,Ro=Oc([["A","Z"],["a","z"],["0","9"],"_",["一","龥"],["À","ſ"]],!1,!1),No=Nc(":",!1),Oo=Nc("OVER",!0),ko=Nc("FILTER",!0),Uo=Nc("FIRST_VALUE",!0),Mo=Nc("LAST_VALUE",!0),Do=Nc("ROW_NUMBER",!0),Po=Nc("DENSE_RANK",!0),Go=Nc("RANK",!0),$o=Nc("LAG",!0),Fo=Nc("LEAD",!0),Bo=Nc("NTH_VALUE",!0),Ho=Nc("IGNORE",!0),qo=Nc("RESPECT",!0),Yo=Nc("percentile_cont",!0),Wo=Nc("percentile_disc",!0),Vo=Nc("within",!0),Xo=Nc("mode",!0),Qo=Nc("BOTH",!0),Ko=Nc("LEADING",!0),zo=Nc("TRAILING",!0),Zo=Nc("trim",!0),Jo=Nc("crosstab",!0),rs=Nc("jsonb_to_recordset",!0),ts=Nc("jsonb_to_record",!0),es=Nc("json_to_recordset",!0),ns=Nc("json_to_record",!0),os=Nc("substring",!0),ss=Nc("years",!0),us=Nc("months",!0),as=Nc("weeks",!0),is=Nc("days",!0),cs=Nc("hours",!0),ls=Nc("mins",!0),fs=Nc("=>",!1),ps=Nc("secs",!0),bs=Nc("make_interval",!0),vs=Nc("now",!0),ys=Nc("at",!0),ds=Nc("zone",!0),ws=Nc("CENTURY",!0),Ls=Nc("DAY",!0),hs=Nc("DATE",!0),Cs=Nc("DECADE",!0),ms=Nc("DOW",!0),Es=Nc("DOY",!0),As=Nc("EPOCH",!0),gs=Nc("HOUR",!0),Ts=Nc("ISODOW",!0),_s=Nc("ISOYEAR",!0),Ss=Nc("MICROSECONDS",!0),xs=Nc("MILLENNIUM",!0),js=Nc("MILLISECONDS",!0),Is=Nc("MINUTE",!0),Rs=Nc("MONTH",!0),Ns=Nc("QUARTER",!0),Os=Nc("SECOND",!0),ks=Nc("TIMEZONE",!0),Us=Nc("TIMEZONE_HOUR",!0),Ms=Nc("TIMEZONE_MINUTE",!0),Ds=Nc("WEEK",!0),Ps=Nc("YEAR",!0),Gs=Nc("NTILE",!0),$s=/^[\n]/,Fs=Oc(["\n"],!1,!1),Bs=/^[^"\\\0-\x1F\x7F]/,Hs=Oc(['"',"\\",["\0",""],""],!0,!1),qs=/^[^'\\]/,Ys=Oc(["'","\\"],!0,!1),Ws=Nc("\\'",!1),Vs=Nc('\\"',!1),Xs=Nc("\\\\",!1),Qs=Nc("\\/",!1),Ks=Nc("\\b",!1),zs=Nc("\\f",!1),Zs=Nc("\\n",!1),Js=Nc("\\r",!1),ru=Nc("\\t",!1),tu=Nc("\\u",!1),eu=Nc("\\",!1),nu=Nc("''",!1),ou=/^[\n\r]/,su=Oc(["\n","\r"],!1,!1),uu=Nc(".",!1),au=/^[0-9]/,iu=Oc([["0","9"]],!1,!1),cu=/^[0-9a-fA-F]/,lu=Oc([["0","9"],["a","f"],["A","F"]],!1,!1),fu=/^[eE]/,pu=Oc(["e","E"],!1,!1),bu=/^[+\-]/,vu=Oc(["+","-"],!1,!1),yu=Nc("NOT NULL",!0),du=Nc("TRUE",!0),wu=Nc("FALSE",!0),Lu=Nc("SHOW",!0),hu=Nc("DROP",!0),Cu=Nc("USE",!0),mu=Nc("ALTER",!0),Eu=Nc("SELECT",!0),Au=Nc("UPDATE",!0),gu=Nc("CREATE",!0),Tu=Nc("TEMPORARY",!0),_u=Nc("UNLOGGED",!1),Su=Nc("TEMP",!0),xu=Nc("DELETE",!0),ju=Nc("INSERT",!0),Iu=Nc("RECURSIVE",!0),Ru=Nc("REPLACE",!0),Nu=Nc("RETURN",!0),Ou=Nc("RETURNING",!0),ku=Nc("RENAME",!0),Uu=(Nc("EXPLAIN",!0),Nc("PARTITION",!0)),Mu=Nc("INTO",!0),Du=Nc("FROM",!0),Pu=Nc("AS",!0),Gu=Nc("TABLESPACE",!0),$u=Nc("COLLATION",!0),Fu=Nc("DEALLOCATE",!0),Bu=Nc("LEFT",!0),Hu=Nc("RIGHT",!0),qu=Nc("FULL",!0),Yu=Nc("INNER",!0),Wu=Nc("JOIN",!0),Vu=Nc("OUTER",!0),Xu=Nc("UNION",!0),Qu=Nc("INTERSECT",!0),Ku=Nc("EXCEPT",!0),zu=Nc("VALUES",!0),Zu=Nc("USING",!0),Ju=Nc("WHERE",!0),ra=Nc("GROUP",!0),ta=Nc("BY",!0),ea=Nc("ORDER",!0),na=Nc("HAVING",!0),oa=Nc("LIMIT",!0),sa=Nc("OFFSET",!0),ua=Nc("ASC",!0),aa=Nc("DESC",!0),ia=Nc("ALL",!0),ca=Nc("DISTINCT",!0),la=Nc("BETWEEN",!0),fa=Nc("LIKE",!0),pa=Nc("ILIKE",!0),ba=Nc("EXISTS",!0),va=Nc("AND",!0),ya=Nc("OR",!0),da=Nc("ARRAY",!0),wa=Nc("ARRAY_AGG",!0),La=Nc("STRING_AGG",!0),ha=Nc("COUNT",!0),Ca=Nc("GROUP_CONCAT",!0),ma=Nc("MAX",!0),Ea=Nc("MIN",!0),Aa=Nc("SUM",!0),ga=Nc("AVG",!0),Ta=Nc("EXTRACT",!0),_a=Nc("CALL",!0),Sa=Nc("CASE",!0),xa=Nc("WHEN",!0),ja=Nc("ELSE",!0),Ia=Nc("CAST",!0),Ra=Nc("BOOL",!0),Na=Nc("BOOLEAN",!0),Oa=Nc("CHAR",!0),ka=Nc("VARCHAR",!0),Ua=Nc("NUMERIC",!0),Ma=Nc("DECIMAL",!0),Da=Nc("SIGNED",!0),Pa=Nc("UNSIGNED",!0),Ga=Nc("INT",!0),$a=Nc("ZEROFILL",!0),Fa=Nc("INTEGER",!0),Ba=Nc("JSON",!0),Ha=Nc("JSONB",!0),qa=Nc("GEOMETRY",!0),Ya=Nc("SMALLINT",!0),Wa=Nc("SERIAL",!0),Va=Nc("TINYINT",!0),Xa=Nc("TINYTEXT",!0),Qa=Nc("TEXT",!0),Ka=Nc("MEDIUMTEXT",!0),za=Nc("LONGTEXT",!0),Za=Nc("MEDIUMINT",!0),Ja=Nc("BIGINT",!0),ri=Nc("ENUM",!0),ti=Nc("FLOAT",!0),ei=Nc("DOUBLE",!0),ni=Nc("BIGSERIAL",!0),oi=Nc("REAL",!0),si=Nc("DATETIME",!0),ui=Nc("TIME",!0),ai=Nc("TIMESTAMP",!0),ii=Nc("TIMESTAMPTZ",!0),ci=Nc("TRUNCATE",!0),li=Nc("USER",!0),fi=Nc("UUID",!0),pi=Nc("OID",!0),bi=Nc("REGCLASS",!0),vi=Nc("REGCOLLATION",!0),yi=Nc("REGCONFIG",!0),di=Nc("REGDICTIONARY",!0),wi=Nc("REGNAMESPACE",!0),Li=Nc("REGOPER",!0),hi=Nc("REGOPERATOR",!0),Ci=Nc("REGPROC",!0),mi=Nc("REGPROCEDURE",!0),Ei=Nc("REGROLE",!0),Ai=Nc("REGTYPE",!0),gi=Nc("CURRENT_DATE",!0),Ti=(Nc("ADDDATE",!0),Nc("INTERVAL",!0)),_i=Nc("CURRENT_TIME",!0),Si=Nc("CURRENT_TIMESTAMP",!0),xi=Nc("SYSTEM_USER",!0),ji=Nc("GLOBAL",!0),Ii=Nc("SESSION",!0),Ri=Nc("PERSIST",!0),Ni=Nc("PERSIST_ONLY",!0),Oi=Nc("VIEW",!0),ki=Nc("@",!1),Ui=Nc("@@",!1),Mi=Nc("$$",!1),Di=Nc("::",!1),Pi=Nc("DUAL",!0),Gi=Nc("ADD",!0),$i=Nc("INDEX",!0),Fi=Nc("FULLTEXT",!0),Bi=Nc("SPATIAL",!0),Hi=Nc("CONCURRENTLY",!0),qi=Nc("REFERENCES",!0),Yi=Nc("SQL_CALC_FOUND_ROWS",!0),Wi=Nc("SQL_CACHE",!0),Vi=Nc("SQL_NO_CACHE",!0),Xi=Nc("SQL_SMALL_RESULT",!0),Qi=Nc("SQL_BIG_RESULT",!0),Ki=Nc("SQL_BUFFER_RESULT",!0),zi=Nc(",",!1),Zi=Nc("[",!1),Ji=Nc("]",!1),rc=Nc("->",!1),tc=Nc("->>",!1),ec=Nc("&&",!1),nc=Nc("/*",!1),oc=Nc("*/",!1),sc=Nc("--",!1),uc=(Nc("#",!1),{type:"any"}),ac=/^[ \t\n\r]/,ic=Oc([" ","\t","\n","\r"],!1,!1),cc=Nc("default",!0),lc=/^[^$]/,fc=Oc(["$"],!0,!1),pc=function(r){return{dataType:r}},bc=Nc("bytea",!0),vc=Nc("varying",!0),yc=Nc("PRECISION",!0),dc=Nc("WITHOUT",!0),wc=function(r){return{dataType:r}},Lc=Nc("POINT",!0),hc=Nc("LINESTRING",!0),Cc=Nc("POLYGON",!0),mc=Nc("MULTIPOINT",!0),Ec=Nc("MULTILINESTRING",!0),Ac=Nc("MULTIPOLYGON",!0),gc=Nc("GEOMETRYCOLLECTION",!0),Tc=Nc("RECORD",!0),_c=0,Sc=0,xc=[{line:1,column:1}],jc=0,Ic=[],Rc=0;if("startRule"in t){if(!(t.startRule in u))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=u[t.startRule]}function Nc(r,t){return{type:"literal",text:r,ignoreCase:t}}function Oc(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function kc(t){var e,n=xc[t];if(n)return n;for(e=t-1;!xc[e];)e--;for(n={line:(n=xc[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return xc[t]=n,n}function Uc(r,t){var e=kc(r),n=kc(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Mc(r){_c<jc||(_c>jc&&(jc=_c,Ic=[]),Ic.push(r))}function Dc(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Pc(){var r,t;return r=_c,fy()!==s?((t=rl())===s&&(t=Fc()),t!==s?(Sc=r,r=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=rl())===s&&(r=Fc()),r}function Gc(){var t;return(t=function(){var t,e,n,o,u,a,i;t=_c,(e=yb())!==s&&fy()!==s&&(n=kb())!==s&&fy()!==s?((o=Yc())===s&&(o=null),o!==s&&fy()!==s&&(u=pf())!==s?(Sc=t,c=e,l=n,f=o,(p=u)&&p.forEach(r=>Vy.add(`${c}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:c.toLowerCase(),keyword:l.toLowerCase(),prefix:f,name:p}},t=e):(_c=t,t=s)):(_c=t,t=s);var c,l,f,p;t===s&&(t=_c,(e=yb())!==s&&fy()!==s&&(n=Vv())!==s&&fy()!==s?((o=Zv())===s&&(o=null),o!==s&&fy()!==s?((u=Yc())===s&&(u=null),u!==s&&fy()!==s&&(a=ip())!==s&&fy()!==s?("cascade"===r.substr(_c,7).toLowerCase()?(i=r.substr(_c,7),_c+=7):(i=s,0===Rc&&Mc(Hr)),i===s&&("restrict"===r.substr(_c,8).toLowerCase()?(i=r.substr(_c,8),_c+=8):(i=s,0===Rc&&Mc(qr))),i===s&&(i=null),i!==s?(Sc=t,e=function(r,t,e,n,o,s){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:[e,n].filter(r=>r).join(" "),name:o,options:s&&[{type:"origin",value:s}]}}}(e,n,o,u,a,i),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=yb())!==s&&fy()!==s&&(n=function(){var t,e,n,o;t=_c,"type"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(pr));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TYPE"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?((o=Yc())===s&&(o=null),o!==s&&fy()!==s&&(u=Cf())!==s&&fy()!==s?("cascade"===r.substr(_c,7).toLowerCase()?(a=r.substr(_c,7),_c+=7):(a=s,0===Rc&&Mc(Hr)),a===s&&("restrict"===r.substr(_c,8).toLowerCase()?(a=r.substr(_c,8),_c+=8):(a=s,0===Rc&&Mc(qr))),a===s&&(a=null),a!==s?(Sc=t,e=function(r,t,e,n,o){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:[e].filter(r=>r).join(" "),name:n,options:o&&[{type:"origin",value:o}]}}}(e,n,o,u,a),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=yb())!==s&&fy()!==s&&(n=Gv())!==s&&fy()!==s?((o=Yc())===s&&(o=null),o!==s&&fy()!==s&&(u=pf())!==s&&fy()!==s?((a=function(){var t,e;t=_c,"restrict"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(qr));e===s&&("cascade"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Hr)));e!==s&&(Sc=t,e=e.toLowerCase());return t=e}())===s&&(a=null),a!==s?(Sc=t,e=function(r,t,e,n,o){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:n,options:o&&[{type:"origin",value:o}]}}}(e,n,o,u,a),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s))));return t}())===s&&(t=function(){var t;(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=_c,(e=hb())!==s&&fy()!==s?((n=Cb())===s&&(n=null),n!==s&&fy()!==s?((o=mb())===s&&(o=null),o!==s&&fy()!==s&&kb()!==s&&fy()!==s?((u=qc())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=jb())!==s&&fy()!==s?("of"===r.substr(_c,2).toLowerCase()?(n=r.substr(_c,2),_c+=2):(n=s,0===Rc&&Mc(Rr)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s&&(u=function(){var t,e,n;t=_c,"for"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ir));e!==s&&fy()!==s&&Fb()!==s&&fy()!==s&&(n=function(){var t,e,n,o,u;t=_c,Rb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=zp())!==s&&fy()!==s&&(n=oy())!==s&&fy()!==s&&bb()!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s&&(u=zp())!==s&&fy()!==s&&oy()!==s?(Sc=t,t={type:"for_values_item",keyword:"from",from:e,to:u}):(_c=t,t=s);t===s&&(t=_c,Zb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=$f())!==s&&fy()!==s&&(n=oy())!==s?(Sc=t,t={type:"for_values_item",keyword:"in",in:e}):(_c=t,t=s),t===s&&(t=_c,Hb()!==s&&fy()!==s&&ny()!==s&&fy()!==s?("modulus"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(xr)),e!==s&&fy()!==s&&(n=eb())!==s&&fy()!==s&&ty()!==s&&fy()!==s?("remainder"===r.substr(_c,9).toLowerCase()?(o=r.substr(_c,9),_c+=9):(o=s,0===Rc&&Mc(jr)),o!==s&&fy()!==s&&(u=eb())!==s&&fy()!==s&&oy()!==s?(Sc=t,t={type:"for_values_item",keyword:"with",modulus:n,remainder:u}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)));return t}())!==s?(Sc=t,t=e={type:"for_values",keyword:"for values",expr:n}):(_c=t,t=s);return t}())!==s&&fy()!==s?(a=_c,(i=Db())!==s&&(c=fy())!==s&&(l=fp())!==s?a=i=[i,c,l]:(_c=a,a=s),a===s&&(a=null),a!==s?(Sc=t,e={type:"partition_of",keyword:"partition of",table:o,for_values:u,tablespace:(f=a)&&f[2]},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var f;return t}())!==s?(Sc=t,b=e,v=n,y=o,d=u,L=i,(w=a)&&w.forEach(r=>Vy.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:b[0].toLowerCase(),keyword:"table",temporary:v&&v[0].toLowerCase(),unlogged:y,if_not_exists:d,table:w,partition_of:L}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var b,v,y,d,w,L;t===s&&(t=_c,(e=hb())!==s&&fy()!==s?((n=Cb())===s&&(n=null),n!==s&&fy()!==s?((o=mb())===s&&(o=null),o!==s&&fy()!==s&&kb()!==s&&fy()!==s?((u=qc())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s?((i=function(){var r,t,e,n,o,u,a,i,c;if(r=_c,(t=ny())!==s)if(fy()!==s)if((e=nl())!==s){for(n=[],o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=nl())!==s?o=u=[u,a,i,c]:(_c=o,o=s);o!==s;)n.push(o),o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=nl())!==s?o=u=[u,a,i,c]:(_c=o,o=s);n!==s&&(o=fy())!==s&&(u=oy())!==s?(Sc=r,t=Fy(e,n),r=t):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;return r}())===s&&(i=null),i!==s&&fy()!==s?((c=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Il())!==s){for(e=[],n=_c,(o=fy())!==s?((u=ty())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=Il())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s?((u=ty())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=Il())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(c=null),c!==s&&fy()!==s?((l=xb())===s&&(l=_b()),l===s&&(l=null),l!==s&&fy()!==s?((f=Ob())===s&&(f=null),f!==s&&fy()!==s?((p=Hc())===s&&(p=null),p!==s?(Sc=t,e=function(r,t,e,n,o,s,u,a,i,c){return o&&o.forEach(r=>Vy.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),unlogged:e,if_not_exists:n,table:o,ignore_replace:a&&a[0].toLowerCase(),as:i&&i[0].toLowerCase(),query_expr:c&&c.ast,create_definitions:s,table_options:u}}}(e,n,o,u,a,i,c,l,f,p),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=hb())!==s&&fy()!==s?((n=Cb())===s&&(n=null),n!==s&&fy()!==s?((o=mb())===s&&(o=null),o!==s&&fy()!==s&&kb()!==s&&fy()!==s?((u=qc())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function r(){var t,e;(t=function(){var r,t;r=_c,rv()!==s&&fy()!==s&&(t=pf())!==s?(Sc=r,r={type:"like",table:t}):(_c=r,r=s);return r}())===s&&(t=_c,ny()!==s&&fy()!==s&&(e=r())!==s&&fy()!==s&&oy()!==s?(Sc=t,(n=e).parentheses=!0,t=n):(_c=t,t=s));var n;return t}())!==s?(Sc=t,e=function(r,t,e,n,o,s){return o&&o.forEach(r=>Vy.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),unlogged:e,if_not_exists:n,table:o,like:s}}}(e,n,o,u,a,i),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)));return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,L,h,C,m,E;t=_c,(e=hb())!==s&&fy()!==s?(n=_c,(o=sv())!==s&&(u=fy())!==s&&(a=_b())!==s?n=o=[o,u,a]:(_c=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?((u=zv())===s&&(u=null),u!==s&&(a=fy())!==s?("trigger"===r.substr(_c,7).toLowerCase()?(i=r.substr(_c,7),_c+=7):(i=s,0===Rc&&Mc(Ct)),i!==s&&fy()!==s&&(c=Tp())!==s&&fy()!==s?("before"===r.substr(_c,6).toLowerCase()?(l=r.substr(_c,6),_c+=6):(l=s,0===Rc&&Mc(mt)),l===s&&("after"===r.substr(_c,5).toLowerCase()?(l=r.substr(_c,5),_c+=5):(l=s,0===Rc&&Mc(Et)),l===s&&("instead of"===r.substr(_c,10).toLowerCase()?(l=r.substr(_c,10),_c+=10):(l=s,0===Rc&&Mc(At)))),l!==s&&fy()!==s&&(f=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Sl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Sl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Sl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&fy()!==s?("on"===r.substr(_c,2).toLowerCase()?(p=r.substr(_c,2),_c+=2):(p=s,0===Rc&&Mc(q)),p!==s&&fy()!==s&&(b=df())!==s&&fy()!==s?(v=_c,(y=Rb())!==s&&(d=fy())!==s&&(w=df())!==s?v=y=[y,d,w]:(_c=v,v=s),v===s&&(v=null),v!==s&&(y=fy())!==s?((d=function(){var t,e,n,o,u;t=_c,e=_c,"not"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc($));n===s&&(n=null);n!==s&&(o=fy())!==s?("deferrable"===r.substr(_c,10).toLowerCase()?(u=r.substr(_c,10),_c+=10):(u=s,0===Rc&&Mc(_t)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s);e!==s&&(n=fy())!==s?("initially immediate"===r.substr(_c,19).toLowerCase()?(o=r.substr(_c,19),_c+=19):(o=s,0===Rc&&Mc(St)),o===s&&("initially deferred"===r.substr(_c,18).toLowerCase()?(o=r.substr(_c,18),_c+=18):(o=s,0===Rc&&Mc(xt))),o!==s?(Sc=t,i=o,e={keyword:(a=e)&&a[0]?a[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):(_c=t,t=s)):(_c=t,t=s);var a,i;return t}())===s&&(d=null),d!==s&&(w=fy())!==s?((L=function(){var t,e,n,o;t=_c,"for"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ir));e!==s&&fy()!==s?("each"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(jt)),n===s&&(n=null),n!==s&&fy()!==s?("row"===r.substr(_c,3).toLowerCase()?(o=r.substr(_c,3),_c+=3):(o=s,0===Rc&&Mc(It)),o===s&&("statement"===r.substr(_c,9).toLowerCase()?(o=r.substr(_c,9),_c+=9):(o=s,0===Rc&&Mc(Rt))),o!==s?(Sc=t,u=e,i=o,e={keyword:(a=n)?`${u.toLowerCase()} ${a.toLowerCase()}`:u.toLowerCase(),args:i.toLowerCase()},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var u,a,i;return t}())===s&&(L=null),L!==s&&fy()!==s?((h=function(){var r,t;r=_c,cv()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(t=Wf())!==s&&fy()!==s&&oy()!==s?(Sc=r,r={type:"when",cond:t,parentheses:!0}):(_c=r,r=s);return r}())===s&&(h=null),h!==s&&fy()!==s?("execute"===r.substr(_c,7).toLowerCase()?(C=r.substr(_c,7),_c+=7):(C=s,0===Rc&&Mc(gt)),C!==s&&fy()!==s?("procedure"===r.substr(_c,9).toLowerCase()?(m=r.substr(_c,9),_c+=9):(m=s,0===Rc&&Mc(Tt)),m===s&&("function"===r.substr(_c,8).toLowerCase()?(m=r.substr(_c,8),_c+=8):(m=s,0===Rc&&Mc(lr))),m!==s&&fy()!==s&&(E=Sy())!==s?(Sc=t,e=function(r,t,e,n,o,s,u,a,i,c,l,f,p,b,v,y){return{type:"create",replace:t&&"or replace",constraint:o,location:s&&s.toLowerCase(),events:u,table:i,from:c&&c[2],deferrable:l,for_each:f,when:p,execute:{keyword:"execute "+v.toLowerCase(),expr:y},constraint_type:n&&n.toLowerCase(),keyword:n&&n.toLowerCase(),constraint_kw:e&&e.toLowerCase(),resource:"constraint"}}(0,n,u,i,c,l,f,0,b,v,d,L,h,0,m,E),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,y,d,w,L;t=_c,(e=hb())!==s&&fy()!==s?("extension"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(p)),n!==s&&fy()!==s?((o=qc())===s&&(o=null),o!==s&&fy()!==s?((u=Tp())===s&&(u=zp()),u!==s&&fy()!==s?((a=Hb())===s&&(a=null),a!==s&&fy()!==s?(i=_c,"schema"===r.substr(_c,6).toLowerCase()?(c=r.substr(_c,6),_c+=6):(c=s,0===Rc&&Mc(b)),c!==s&&(l=fy())!==s&&(f=Tp())!==s?i=c=[c,l,f]:(_c=i,i=s),i===s&&(i=zp()),i===s&&(i=null),i!==s&&(c=fy())!==s?(l=_c,"version"===r.substr(_c,7).toLowerCase()?(f=r.substr(_c,7),_c+=7):(f=s,0===Rc&&Mc(v)),f!==s&&(y=fy())!==s?((d=Tp())===s&&(d=zp()),d!==s?l=f=[f,y,d]:(_c=l,l=s)):(_c=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?(y=_c,(d=Rb())!==s&&(w=fy())!==s?((L=Tp())===s&&(L=zp()),L!==s?y=d=[d,w,L]:(_c=y,y=s)):(_c=y,y=s),y===s&&(y=null),y!==s?(Sc=t,h=o,C=u,m=a,E=i,A=l,g=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:h,extension:Yy(C),with:m&&m[0].toLowerCase(),schema:Yy(E&&E[2].toLowerCase()),version:Yy(A&&A[2]),from:Yy(g&&g[2])},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var h,C,m,E,A,g;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,L,h,C,m;t=_c,(e=hb())!==s&&fy()!==s?((n=Qv())===s&&(n=null),n!==s&&fy()!==s&&(o=Vv())!==s&&fy()!==s?((u=qc())===s&&(u=null),u!==s?((a=Zv())===s&&(a=null),a!==s&&fy()!==s?((i=bp())===s&&(i=null),i!==s&&fy()!==s&&(c=Pb())!==s&&fy()!==s&&(l=df())!==s&&fy()!==s?((f=cf())===s&&(f=null),f!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(p=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=el())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=el())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=el())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((b=function(){var t,e,n;t=_c,"include"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(gr));e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=lp())!==s&&fy()!==s&&oy()!==s?(Sc=t,e=function(r,t){return{type:r.toLowerCase(),keyword:r.toLowerCase(),columns:t}}(e,n),t=e):(_c=t,t=s);return t}())===s&&(b=null),b!==s&&fy()!==s?(v=_c,(y=Hb())!==s&&(d=fy())!==s&&(w=ny())!==s&&(L=fy())!==s&&(h=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=ff())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ff())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ff())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&(C=fy())!==s&&(m=oy())!==s?v=y=[y,d,w,L,h,C,m]:(_c=v,v=s),v===s&&(v=null),v!==s&&(y=fy())!==s?(d=_c,(w=Db())!==s&&(L=fy())!==s&&(h=Tp())!==s?d=w=[w,L,h]:(_c=d,d=s),d===s&&(d=null),d!==s&&(w=fy())!==s?((L=hf())===s&&(L=null),L!==s&&(h=fy())!==s?(Sc=t,E=e,A=n,g=o,T=u,_=a,S=i,x=c,j=l,I=f,R=p,N=b,O=v,k=d,U=L,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:E[0].toLowerCase(),index_type:A&&A.toLowerCase(),keyword:g.toLowerCase(),concurrently:_&&_.toLowerCase(),index:S,if_not_exists:T,on_kw:x[0].toLowerCase(),table:j,index_using:I,index_columns:R,include:N,with:O&&O[4],with_before_where:!0,tablespace:k&&{type:"origin",value:k[2]},where:U}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var E,A,g,T,_,S,x,j,I,R,N,O,k,U;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=hb())!==s&&fy()!==s?((n=Cb())===s&&(n=Eb()),n===s&&(n=null),n!==s&&fy()!==s&&function(){var t,e,n,o;t=_c,"sequence"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(le));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SEQUENCE"):(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s?((o=qc())===s&&(o=null),o!==s&&fy()!==s&&(u=df())!==s&&fy()!==s?(a=_c,(i=Ob())!==s&&(c=fy())!==s&&(l=yp())!==s?a=i=[i,c,l]:(_c=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?((c=function(){var r,t,e,n,o,u;if(r=_c,(t=tl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=tl())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=tl())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e,1),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(c=null),c!==s?(Sc=t,e=function(r,t,e,n,o,s){return n.as=o&&o[2],{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:"sequence",temporary:t&&t[0].toLowerCase(),if_not_exists:e,sequence:[n],create_definitions:s}}}(e,n,o,u,a,c),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var r,t,e,n,o,u;r=_c,(t=hb())!==s&&fy()!==s?((e=Ub())===s&&(e=Mb()),e!==s&&fy()!==s?((n=qc())===s&&(n=null),n!==s&&fy()!==s&&(o=_y())!==s&&fy()!==s?((u=function(){var r,t,e,n,o,u;if(r=_c,(t=jl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=jl())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=jl())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e,1),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(u=null),u!==s?(Sc=r,t=function(r,t,e,n,o){const s=t.toLowerCase();return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:s,if_not_exists:e,[s]:{db:n.schema,schema:n.name},create_definitions:o}}}(t,e,n,o,u),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=hb())!==s&&fy()!==s?("domain"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(br)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Ob())===s&&(u=null),u!==s&&fy()!==s&&(a=Ry())!==s&&fy()!==s?((i=ul())===s&&(i=null),i!==s&&fy()!==s?((c=il())===s&&(c=null),c!==s&&fy()!==s?((l=gl())===s&&(l=null),l!==s?(Sc=t,e=function(r,t,e,n,o,s,u,a){a&&(a.type="constraint");const i=[s,u,a].filter(r=>r);return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i}}}(e,n,o,u,a,i,c,l),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u;t=_c,(e=hb())!==s&&fy()!==s?("type"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(pr)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=function(){var t,e,n,o,u;t=_c,(e=Ob())!==s&&fy()!==s?((n=Av())===s&&("range"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(fr))),n!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s?((u=$f())===s&&(u=null),u!==s&&fy()!==s&&oy()!==s?(Sc=t,a=n,(i=u).parentheses=!0,e={as:"as",resource:a.toLowerCase(),create_definitions:i},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var a,i;t===s&&(t=_c,(e=Ob())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=sl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(o=null),o!==s&&fy()!==s&&(u=oy())!==s?(Sc=t,e=function(r){return{as:"as",create_definitions:r}}(o),t=e):(_c=t,t=s)):(_c=t,t=s));return t}())===s&&(u=null),u!==s?(Sc=t,a=e,i=n,c=o,l=u,Qy.add([c.db,c.table].filter(r=>r).join(".")),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:a[0].toLowerCase(),keyword:i.toLowerCase(),name:{schema:c.db,name:c.table},...l}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var a,i,c,l;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,h,C,m,E,A;t=_c,(e=hb())!==s&&fy()!==s?(n=_c,(o=sv())!==s&&(u=fy())!==s&&(a=_b())!==s?n=o=[o,u,a]:(_c=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?((u=Eb())===s&&(u=Cb()),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=Tb())===s&&(i=null),i!==s&&fy()!==s&&Gv()!==s&&fy()!==s&&(c=df())!==s&&fy()!==s?(l=_c,(f=ny())!==s&&(p=fy())!==s&&(b=lp())!==s&&(v=fy())!==s&&(h=oy())!==s?l=f=[f,p,b,v,h]:(_c=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?(p=_c,(b=Hb())!==s&&(v=fy())!==s&&(h=ny())!==s&&(C=fy())!==s&&(m=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Wc())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Wc())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Wc())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&(E=fy())!==s&&(A=oy())!==s?p=b=[b,v,h,C,m,E,A]:(_c=p,p=s),p===s&&(p=null),p!==s&&(b=fy())!==s&&(v=Ob())!==s&&(h=fy())!==s&&(C=Hl())!==s&&(m=fy())!==s?((E=function(){var t,e,n,o,u;t=_c,(e=Hb())!==s&&fy()!==s?("cascaded"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(y)),n===s&&("local"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(d))),n!==s&&fy()!==s?("check"===r.substr(_c,5).toLowerCase()?(o=r.substr(_c,5),_c+=5):(o=s,0===Rc&&Mc(w)),o!==s&&fy()!==s?("OPTION"===r.substr(_c,6)?(u="OPTION",_c+=6):(u=s,0===Rc&&Mc(L)),u!==s?(Sc=t,e=function(r){return`with ${r.toLowerCase()} check option`}(n),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);t===s&&(t=_c,(e=Hb())!==s&&fy()!==s?("check"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(w)),n!==s&&fy()!==s?("OPTION"===r.substr(_c,6)?(o="OPTION",_c+=6):(o=s,0===Rc&&Mc(L)),o!==s?(Sc=t,t=e="with check option"):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s));return t}())===s&&(E=null),E!==s?(Sc=t,e=function(r,t,e,n,o,s,u,a,i){return o.view=o.table,delete o.table,{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r[0].toLowerCase(),keyword:"view",replace:t&&"or replace",temporary:e&&e[0].toLowerCase(),recursive:n&&n.toLowerCase(),columns:s&&s[2],select:a,view:o,with_options:u&&u[4],with:i}}}(e,n,u,i,c,l,p,C,E),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=hb())!==s&&fy()!==s?(n=_c,(o=sv())!==s&&(u=fy())!==s&&(a=_b())!==s?n=o=[o,u,a]:(_c=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?("aggregate"===r.substr(_c,9).toLowerCase()?(u=r.substr(_c,9),_c+=9):(u=s,0===Rc&&Mc(g)),u!==s&&(a=fy())!==s&&(i=df())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(c=ll())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(l=function(){var t,e,n,o,u,a,i,c;if(t=_c,(e=function(){var t,e,n,o,u;t=_c,"sfunc"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(E));e!==s&&fy()!==s&&qv()!==s&&fy()!==s&&(n=df())!==s&&fy()!==s&&ty()!==s&&fy()!==s?("stype"===r.substr(_c,5).toLowerCase()?(o=r.substr(_c,5),_c+=5):(o=s,0===Rc&&Mc(A)),o!==s&&fy()!==s&&qv()!==s&&fy()!==s&&(u=Ry())!==s?(Sc=t,i=u,e=[{type:"sfunc",symbol:"=",value:{schema:(a=n).db,name:a.table}},{type:"stype",symbol:"=",value:i}],t=e):(_c=t,t=s)):(_c=t,t=s);var a,i;return t}())!==s){for(n=[],o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Vc())!==s?o=u=[u,a,i,c]:(_c=o,o=s);o!==s;)n.push(o),o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Vc())!==s?o=u=[u,a,i,c]:(_c=o,o=s);n!==s?(Sc=t,e=Fy(e,n),t=e):(_c=t,t=s)}else _c=t,t=s;return t}())!==s&&fy()!==s&&oy()!==s?(Sc=t,f=i,p=c,b=l,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"create",keyword:"aggregate",name:{schema:f.db,name:f.table},args:{parentheses:!0,expr:p,orderby:p.orderby},options:b}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var f,p,b;return t}());return t}())===s&&(t=Zc())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=Ov())!==s&&fy()!==s?((n=kb())===s&&(n=null),n!==s&&fy()!==s?("only"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(Yr)),o===s&&(o=null),o!==s&&fy()!==s&&(u=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=cl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=cl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=cl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&fy()!==s?(a=_c,"restart"===r.substr(_c,7).toLowerCase()?(i=r.substr(_c,7),_c+=7):(i=s,0===Rc&&Mc(Wr)),i===s&&("continue"===r.substr(_c,8).toLowerCase()?(i=r.substr(_c,8),_c+=8):(i=s,0===Rc&&Mc(Vr))),i!==s&&(c=fy())!==s?("identity"===r.substr(_c,8).toLowerCase()?(l=r.substr(_c,8),_c+=8):(l=s,0===Rc&&Mc(Xr)),l!==s?a=i=[i,c,l]:(_c=a,a=s)):(_c=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?("cascade"===r.substr(_c,7).toLowerCase()?(c=r.substr(_c,7),_c+=7):(c=s,0===Rc&&Mc(Hr)),c===s&&("restrict"===r.substr(_c,8).toLowerCase()?(c=r.substr(_c,8),_c+=8):(c=s,0===Rc&&Mc(qr))),c===s&&(c=null),c!==s?(Sc=t,f=e,p=n,b=o,v=u,y=a,d=c,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:f.toLowerCase(),keyword:p&&p.toLowerCase()||"table",prefix:b,name:v,suffix:[y&&[y[0],y[2]].join(" "),d].filter(r=>r).map(r=>({type:"origin",value:r}))}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var f,p,b,v,y,d;return t}())===s&&(t=function(){var r,t,e;r=_c,(t=Sb())!==s&&fy()!==s&&kb()!==s&&fy()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=af())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Vy.add(`rename::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`))),t={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"rename",table:n}},r=t):(_c=r,r=s);var n;return r}())===s&&(t=function(){var t,e,n;t=_c,(e=function(){var t,e,n,o;t=_c,"call"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(_a));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CALL"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s&&(n=Sy())!==s?(Sc=t,o=n,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"call",expr:o}},t=e):(_c=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n;t=_c,(e=function(){var t,e,n,o;t=_c,"use"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Cu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s&&(n=bp())!==s?(Sc=t,o=n,Vy.add(`use::${o}::null`),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"use",db:o}},t=e):(_c=t,t=s);var o;return t}())===s&&(t=function(){var t;(t=function(){var t,e,n,o,u,a,i;t=_c,(e=db())!==s&&fy()!==s?((n=kb())===s&&(n=null),n!==s&&fy()!==s?((o=Yc())===s&&(o=null),o!==s&&fy()!==s?("only"===r.substr(_c,4).toLowerCase()?(u=r.substr(_c,4),_c+=4):(u=s,0===Rc&&Mc(zr)),u===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=vl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=t,c=o,l=u,p=i,(f=a)&&f.length>0&&f.forEach(r=>Vy.add(`alter::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"alter",keyword:"table",if_exists:c,prefix:l&&{type:"origin",value:l},table:f,expr:p}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var c,l,f,p;return t}())===s&&(t=function(){var r,t,e,n,o;r=_c,(t=db())!==s&&fy()!==s&&(e=Mb())!==s&&fy()!==s&&(n=Tp())!==s&&fy()!==s?((o=yl())===s&&(o=dl())===s&&(o=wl()),o!==s?(Sc=r,t=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"alter",keyword:n,schema:t,expr:e}}}(e,n,o),r=t):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u;t=_c,(e=db())!==s&&fy()!==s?("domain"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(br)),n===s&&("type"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(pr))),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=yl())===s&&(u=dl())===s&&(u=wl()),u!==s?(Sc=t,e=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"alter",keyword:n,name:{schema:t.db,name:t.table},expr:e}}}(n,o,u),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f;t=_c,(e=db())!==s&&fy()!==s?("function"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(lr)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?(u=_c,(a=ny())!==s&&(i=fy())!==s?((c=bl())===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s?u=a=[a,i,c,l,f]:(_c=u,u=s)):(_c=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=yl())===s&&(i=dl())===s&&(i=wl()),i!==s?(Sc=t,e=function(r,t,e,n){const o=r.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const s={};return e&&e[0]&&(s.parentheses=!0),s.expr=e&&e[2],{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:s,expr:n}}}(n,o,u,i),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a;t=_c,(e=db())!==s&&fy()!==s?("aggregate"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(g)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(u=ll())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((a=yl())===s&&(a=dl())===s&&(a=wl()),a!==s?(Sc=t,e=function(r,t,e,n){const o=r.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n}}}(n,o,u,a),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}());return t}())===s&&(t=function(){var t,e,n,o;t=_c,(e=Nb())!==s&&fy()!==s?((n=function(){var t,e,n,o;t=_c,"global"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ji));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="GLOBAL"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=_c,"session"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ii));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SESSION"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(n=Pv())===s&&(n=function(){var t,e,n,o;t=_c,"persist"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ri));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="PERSIST"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=_c,"persist_only"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Ni));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="PERSIST_ONLY"):(_c=t,t=s)):(_c=t,t=s);return t}()),n===s&&(n=null),n!==s&&fy()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Cy())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Cy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Cy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=t,u=n,a=o,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"set",keyword:u,expr:a}},t=e):(_c=t,t=s)):(_c=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=_c,(e=function(){var t,e,n,o;t=_c,"lock"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ut));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?((n=kb())===s&&(n=null),n!==s&&fy()!==s&&(o=pf())!==s&&fy()!==s?((u=function(){var t,e,n,o;t=_c,"in"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(Vt));e!==s&&fy()!==s?("access share"===r.substr(_c,12).toLowerCase()?(n=r.substr(_c,12),_c+=12):(n=s,0===Rc&&Mc(Xt)),n===s&&("row share"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(Qt)),n===s&&("row exclusive"===r.substr(_c,13).toLowerCase()?(n=r.substr(_c,13),_c+=13):(n=s,0===Rc&&Mc(Kt)),n===s&&("share update exclusive"===r.substr(_c,22).toLowerCase()?(n=r.substr(_c,22),_c+=22):(n=s,0===Rc&&Mc(zt)),n===s&&("share row exclusive"===r.substr(_c,19).toLowerCase()?(n=r.substr(_c,19),_c+=19):(n=s,0===Rc&&Mc(Zt)),n===s&&("exclusive"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(it)),n===s&&("access exclusive"===r.substr(_c,16).toLowerCase()?(n=r.substr(_c,16),_c+=16):(n=s,0===Rc&&Mc(Jt)),n===s&&("share"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(re))))))))),n!==s&&fy()!==s?("mode"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(te)),o!==s?(Sc=t,e={mode:`in ${n.toLowerCase()} mode`},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(u=null),u!==s&&fy()!==s?("nowait"===r.substr(_c,6).toLowerCase()?(a=r.substr(_c,6),_c+=6):(a=s,0===Rc&&Mc(ee)),a===s&&(a=null),a!==s?(Sc=t,i=n,l=u,f=a,(c=o)&&c.forEach(r=>Vy.add(`lock::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var i,c,l,f;return t}())===s&&(t=function(){var t,e,n;t=_c,(e=vb())!==s&&fy()!==s?("tables"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(ne)),n!==s?(Sc=t,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"show",keyword:"tables"}},t=e):(_c=t,t=s)):(_c=t,t=s);t===s&&(t=_c,(e=vb())!==s&&fy()!==s&&(n=Iy())!==s?(Sc=t,e=function(r){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"show",keyword:"var",var:r}}}(n),t=e):(_c=t,t=s));return t}())===s&&(t=function(){var t,e,n,o;t=_c,(e=function(){var t,e,n,o;t=_c,"deallocate"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(Fu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DEALLOCATE"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?("prepare"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(oe)),n===s&&(n=null),n!==s&&fy()!==s?((o=Tp())===s&&(o=Qb()),o!==s?(Sc=t,u=n,a=o,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"deallocate",keyword:u,expr:{type:"default",value:a}}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=_c,(e=Pl())!==s&&fy()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=kl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=kl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=kl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&fy()!==s&&(o=Pb())!==s&&fy()!==s?((u=function(){var t,e,n;t=_c,(e=kb())===s&&("sequence"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(le)),e===s&&("database"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(fe)),e===s&&("DOMAIN"===r.substr(_c,6)?(e="DOMAIN",_c+=6):(e=s,0===Rc&&Mc(pe)),e===s&&("FUNCTION"===r.substr(_c,8)?(e="FUNCTION",_c+=8):(e=s,0===Rc&&Mc(be)),e===s&&("procedure"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Tt)),e===s&&("routine"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(ve)),e===s&&("language"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(ye)),e===s&&("large"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(de)),e===s&&("SCHEMA"===r.substr(_c,6)?(e="SCHEMA",_c+=6):(e=s,0===Rc&&Mc(we)))))))))));e!==s&&(Sc=t,e={type:"origin",value:e.toUpperCase()});(t=e)===s&&(t=_c,(e=Qb())!==s&&fy()!==s?("tables"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(ne)),n===s&&("sequence"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(le)),n===s&&("functions"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(Le)),n===s&&("procedures"===r.substr(_c,10).toLowerCase()?(n=r.substr(_c,10),_c+=10):(n=s,0===Rc&&Mc(he)),n===s&&("routines"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(Ce)))))),n!==s&&fy()!==s&&Zb()!==s&&fy()!==s&&Mb()!==s?(Sc=t,t=e={type:"origin",value:`all ${n} in schema`}):(_c=t,t=s)):(_c=t,t=s));return t}())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Ul())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ul())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ul())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&(c=fy())!==s?((l=bb())===s&&(l=Rb()),l!==s?(Sc=_c,b=l,({revoke:"from",grant:"to"}[e.type].toLowerCase()===b[0].toLowerCase()?void 0:s)!==s&&fy()!==s&&(f=Dl())!==s&&fy()!==s?((p=function(){var t,e,n;t=_c,Hb()!==s&&fy()!==s?("grant"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Ee)),e!==s&&fy()!==s?("option"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(Ae)),n!==s?(Sc=t,t={type:"origin",value:"with grant option"}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(p=null),p!==s?(Sc=t,e=function(r,t,e,n,o,s,u){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{...r,keyword:"priv",objects:t,on:{object_type:e,priv_level:n},to_from:o[0],user_or_roles:s,with:u}}}(e,n,u,i,l,f,p),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var b;t===s&&(t=_c,(e=Pl())!==s&&fy()!==s&&(n=vp())!==s&&fy()!==s?((o=bb())===s&&(o=Rb()),o!==s?(Sc=_c,(function(r,t,e){return{revoke:"from",grant:"to"}[r.type].toLowerCase()===e[0].toLowerCase()}(e,0,o)?void 0:s)!==s&&(u=fy())!==s&&(a=Dl())!==s&&(i=fy())!==s?((c=function(){var t,e,n;t=_c,Hb()!==s&&fy()!==s?("admin"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ge)),e!==s&&fy()!==s?("option"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(Ae)),n!==s?(Sc=t,t={type:"origin",value:"with admin option"}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(c=null),c!==s?(Sc=t,e=function(r,t,e,n,o){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{...r,keyword:"role",objects:t.map(r=>({priv:{type:"string",value:r}})),to_from:e[0],user_or_roles:n,with:o}}}(e,n,o,a,c),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s));return t}())===s&&(t=function(){var t,e,n,o,u,a,c,l,f,p,b,v,y;t=_c,"if"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(i));e!==s&&fy()!==s&&(n=Wf())!==s&&fy()!==s?("then"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(Se)),o!==s&&fy()!==s&&(u=$c())!==s&&fy()!==s?((a=ay())===s&&(a=null),a!==s&&fy()!==s?((c=function(){var r,t,e,n,o,u;if(r=_c,(t=Gl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Gl())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Gl())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e,1),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(c=null),c!==s&&fy()!==s?(l=_c,(f=lv())!==s&&(p=fy())!==s&&(b=$c())!==s?l=f=[f,p,b]:(_c=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?((p=ay())===s&&(p=null),p!==s&&(b=fy())!==s?("end"===r.substr(_c,3).toLowerCase()?(v=r.substr(_c,3),_c+=3):(v=s,0===Rc&&Mc(xe)),v!==s&&fy()!==s?("if"===r.substr(_c,2).toLowerCase()?(y=r.substr(_c,2),_c+=2):(y=s,0===Rc&&Mc(i)),y!==s?(Sc=t,d=n,w=u,L=a,h=c,C=l,m=p,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"if",keyword:"if",boolean_expr:d,semicolons:[L||"",m||""],prefix:{type:"origin",value:"then"},if_expr:w,elseif_expr:h,else_expr:C&&C[2],suffix:{type:"origin",value:"end if"}}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var d,w,L,h,C,m;return t}())===s&&(t=function(){var t,e,n,o,u;t=_c,"raise"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(qe));e!==s&&fy()!==s?((n=function(){var t;"debug"===r.substr(_c,5).toLowerCase()?(t=r.substr(_c,5),_c+=5):(t=s,0===Rc&&Mc(je));t===s&&("log"===r.substr(_c,3).toLowerCase()?(t=r.substr(_c,3),_c+=3):(t=s,0===Rc&&Mc(Ie)),t===s&&("info"===r.substr(_c,4).toLowerCase()?(t=r.substr(_c,4),_c+=4):(t=s,0===Rc&&Mc(Re)),t===s&&("notice"===r.substr(_c,6).toLowerCase()?(t=r.substr(_c,6),_c+=6):(t=s,0===Rc&&Mc(Ne)),t===s&&("warning"===r.substr(_c,7).toLowerCase()?(t=r.substr(_c,7),_c+=7):(t=s,0===Rc&&Mc(Oe)),t===s&&("exception"===r.substr(_c,9).toLowerCase()?(t=r.substr(_c,9),_c+=9):(t=s,0===Rc&&Mc(ke)))))));return t}())===s&&(n=null),n!==s&&fy()!==s?((o=function(){var t,e,n,o,u,a,i,c;if(t=_c,(e=zp())!==s){for(n=[],o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Ty())!==s?o=u=[u,a,i,c]:(_c=o,o=s);o!==s;)n.push(o),o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Ty())!==s?o=u=[u,a,i,c]:(_c=o,o=s);n!==s?(Sc=t,e={type:"format",keyword:e,expr:(l=n)&&l.map(r=>r[3])},t=e):(_c=t,t=s)}else _c=t,t=s;var l;t===s&&(t=_c,"sqlstate"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(He)),e!==s&&(n=fy())!==s&&(o=zp())!==s?(Sc=t,t=e={type:"sqlstate",keyword:{type:"origin",value:"SQLSTATE"},expr:[o]}):(_c=t,t=s),t===s&&(t=_c,(e=bp())!==s&&(Sc=t,e={type:"condition",expr:[{type:"default",value:e}]}),t=e));return t}())===s&&(o=null),o!==s&&fy()!==s?((u=function(){var t,e,n,o,u,a,i,c,l,f;if(t=_c,(e=Bb())!==s)if(fy()!==s)if("message"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Ue)),n===s&&("detail"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(Me)),n===s&&("hint"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(De)),n===s&&("errcode"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Pe)),n===s&&("column"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(Ge)),n===s&&("constraint"===r.substr(_c,10).toLowerCase()?(n=r.substr(_c,10),_c+=10):(n=s,0===Rc&&Mc($e)),n===s&&("datatype"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(Fe)),n===s&&("table"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(Be)),n===s&&("schema"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(b)))))))))),n!==s)if(fy()!==s)if(qv()!==s)if(fy()!==s)if((o=Wf())!==s){for(u=[],a=_c,(i=fy())!==s&&(c=ty())!==s&&(l=fy())!==s&&(f=Wf())!==s?a=i=[i,c,l,f]:(_c=a,a=s);a!==s;)u.push(a),a=_c,(i=fy())!==s&&(c=ty())!==s&&(l=fy())!==s&&(f=Wf())!==s?a=i=[i,c,l,f]:(_c=a,a=s);u!==s?(Sc=t,e=function(r,t,e){const n=[t];return e&&e.forEach(r=>n.push(r[3])),{type:"using",option:r,symbol:"=",expr:n}}(n,o,u),t=e):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;return t}())===s&&(u=null),u!==s?(Sc=t,a=n,i=o,c=u,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"raise",level:a,using:c,raise:i}},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var a,i,c;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,"execute"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(gt));e!==s&&fy()!==s&&(n=bp())!==s&&fy()!==s?(o=_c,(u=ny())!==s&&(a=fy())!==s&&(i=xy())!==s&&(c=fy())!==s&&(l=oy())!==s?o=u=[u,a,i,c,l]:(_c=o,o=s),o===s&&(o=null),o!==s?(Sc=t,f=n,p=o,e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"execute",name:f,args:p&&{type:"expr_list",value:p[2]}}},t=e):(_c=t,t=s)):(_c=t,t=s);var f,p;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c;t=_c,(e=function(){var t,e,n;t=_c,"for"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ir));e!==s&&(Sc=t,e={label:null,keyword:"for"});(t=e)===s&&(t=_c,(e=bp())!==s&&fy()!==s?("for"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc(Ir)),n!==s?(Sc=t,t=e={label:e,keyword:"for"}):(_c=t,t=s)):(_c=t,t=s));return t}())!==s&&fy()!==s&&(n=bp())!==s&&fy()!==s&&Zb()!==s&&fy()!==s&&(o=Hl())!==s&&fy()!==s?("loop"===r.substr(_c,4).toLowerCase()?(u=r.substr(_c,4),_c+=4):(u=s,0===Rc&&Mc(Ye)),u!==s&&fy()!==s&&(a=Fc())!==s&&fy()!==s&&fv()!==s&&fy()!==s?("loop"===r.substr(_c,4).toLowerCase()?(i=r.substr(_c,4),_c+=4):(i=s,0===Rc&&Mc(Ye)),i!==s&&fy()!==s?((c=bp())===s&&(c=null),c!==s?(Sc=_c,f=c,(!(!(l=e).label||!f||l.label!==f)||!l.label&&!f?void 0:s)!==s?(Sc=t,e=function(r,t,e,n,o){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"for",label:o,target:t,query:e,stmts:n.ast}}}(0,n,o,a,c),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var l,f;return t}())===s&&(t=function(){var t,e,n,o;t=_c,"commit"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(tn));e===s&&("rollback"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(en)));e!==s&&(Sc=t,e={type:"transaction",expr:{action:{type:"origin",value:e}}});(t=e)===s&&(t=_c,"begin"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(nn)),e!==s&&fy()!==s?("work"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(on)),n===s&&("transaction"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(sn))),n===s&&(n=null),n!==s&&fy()!==s?((o=Fl())===s&&(o=null),o!==s?(Sc=t,e=function(r,t){return{type:"transaction",expr:{action:{type:"origin",value:"begin"},keyword:r,modes:t}}}(n,o),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"start"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(un)),e!==s&&fy()!==s?("transaction"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(an)),n!==s&&fy()!==s?((o=Fl())===s&&(o=null),o!==s?(Sc=t,e=function(r,t){return{type:"transaction",expr:{action:{type:"origin",value:"start"},keyword:r,modes:t}}}(n,o),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)));return t}())===s&&(t=function(){var t,e,n,o,u;t=_c,"comment"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(bn));e!==s&&fy()!==s?("on"===r.substr(_c,2).toLowerCase()?(n=r.substr(_c,2),_c+=2):(n=s,0===Rc&&Mc(q)),n!==s&&fy()!==s&&(o=function(){var t,e,n;t=_c,(e=kb())===s&&(e=Gv())===s&&(e=Db());e!==s&&fy()!==s&&(n=df())!==s?(Sc=t,o=n,e={type:e.toLowerCase(),name:o},t=e):(_c=t,t=s);var o;t===s&&(t=_c,(e=Wv())!==s&&fy()!==s&&(n=ip())!==s?(Sc=t,e=function(r,t){return{type:r.toLowerCase(),name:t}}(e,n),t=e):(_c=t,t=s),t===s&&(t=_c,(e=Vv())===s&&(e=function(){var t,e,n,o;t=_c,"collation"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc($u));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="COLLATION"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(e=Db())===s&&(e=Mb())===s&&("domain"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(br)),e===s&&(e=Ub())===s&&("role"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(cn)),e===s&&("sequence"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(le)),e===s&&("server"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ln)),e===s&&("subscription"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(fn))))))),e!==s&&fy()!==s&&(n=pp())!==s?(Sc=t,e=function(r,t){return{type:r.toLowerCase(),name:t}}(e,n),t=e):(_c=t,t=s)));return t}())!==s&&fy()!==s&&(u=function(){var t,e,n;t=_c,"is"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(pn));e!==s&&fy()!==s?((n=zp())===s&&(n=Xp()),n!==s?(Sc=t,t=e={keyword:"is",expr:n}):(_c=t,t=s)):(_c=t,t=s);return t}())!==s?(Sc=t,t=e={type:"comment",keyword:"on",target:o,expr:u}):(_c=t,t=s)):(_c=t,t=s);return t}()),t}function $c(){var t;return(t=Hc())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=_c,(t=fy())!==s?((e=ql())===s&&(e=null),e!==s&&fy()!==s&&Lb()!==s&&fy()!==s&&(n=pf())!==s&&fy()!==s&&Nb()!==s&&fy()!==s&&(o=Nf())!==s&&fy()!==s?((u=uf())===s&&(u=null),u!==s&&fy()!==s?((a=hf())===s&&(a=null),a!==s&&fy()!==s?((i=kf())===s&&(i=null),i!==s?(Sc=r,t=function(r,t,e,n,o,s){const u={},a=r=>{const{server:t,db:e,schema:n,as:o,table:s,join:a}=r,i=a?"select":"update",c=[t,e,n].filter(Boolean).join(".")||null;e&&(u[s]=c),s&&Vy.add(`${i}::${c}::${s}`)};return t&&t.forEach(a),n&&n.forEach(a),e&&e.forEach(r=>{if(r.table){const t=Hy(r.table);Vy.add(`update::${u[t]||null}::${t}`)}Xy.add(`update::${r.table}::${r.column.expr.value}`)}),{tableList:Array.from(Vy),columnList:qy(Xy),ast:{with:r,type:"update",table:t,set:e,from:n,where:o,returning:s}}}(e,n,o,u,a,i),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=Df())!==s&&fy()!==s?((n=Ib())===s&&(n=null),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Mf())===s&&(u=null),u!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(a=lp())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(i=Uf())!==s&&fy()!==s?((c=function(){var t,e,n,o;t=_c,Pb()!==s&&fy()!==s?("conflict"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Pn)),e!==s&&fy()!==s?((n=function(){var r,t,e;r=_c,(t=ny())!==s&&fy()!==s&&(e=Cf())!==s&&fy()!==s&&oy()!==s?(Sc=r,t=function(r){return{type:"column",expr:r,parentheses:!0}}(e),r=t):(_c=r,r=s);return r}())===s&&(n=null),n!==s&&fy()!==s&&(o=function(){var t,e,n,o,u;t=_c,"do"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(Mn));e!==s&&fy()!==s?("nothing"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Dn)),n!==s?(Sc=t,t=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(_c=t,t=s)):(_c=t,t=s);t===s&&(t=_c,"do"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(Mn)),e!==s&&fy()!==s&&(n=Lb())!==s&&fy()!==s&&Nb()!==s&&fy()!==s&&(o=Nf())!==s&&fy()!==s?((u=hf())===s&&(u=null),u!==s?(Sc=t,t=e={keyword:"do",expr:{type:"update",set:o,where:u}}):(_c=t,t=s)):(_c=t,t=s));return t}())!==s?(Sc=t,t={type:"conflict",keyword:"on",target:n,action:o}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(c=null),c!==s&&fy()!==s?((l=kf())===s&&(l=null),l!==s?(Sc=t,e=function(r,t,e,n,o,s,u){if(t&&(Vy.add(`insert::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>Xy.add(`insert::${r}::${t.value}`))}return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r,table:[t],columns:n,values:o,partition:e,conflict:s,returning:u}}}(e,o,u,a,i,c,l),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=_c,(t=Df())!==s&&fy()!==s?((e=xb())===s&&(e=null),e!==s&&fy()!==s?((n=Ib())===s&&(n=null),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Mf())===s&&(u=null),u!==s&&fy()!==s&&(a=Uf())!==s&&fy()!==s?((i=kf())===s&&(i=null),i!==s?(Sc=r,t=function(r,t,e,n,o,s,u){n&&(Vy.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),Xy.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:r,table:[n],columns:null,values:s,partition:o,prefix:a,returning:u}}}(t,e,n,o,u,a,i),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o;r=_c,(t=Ab())!==s&&fy()!==s?((e=pf())===s&&(e=null),e!==s&&fy()!==s&&(n=uf())!==s&&fy()!==s?((o=hf())===s&&(o=null),o!==s?(Sc=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,schema:n,table:o,join:s}=r,u=s?"select":"delete",a=[t,n].filter(Boolean).join(".")||null;o&&Vy.add(`${u}::${a}::${o}`),s||Xy.add(`delete::${o}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=Gc())===s&&(t=function(){var r,t;r=[],t=hy();for(;t!==s;)r.push(t),t=hy();return r}()),t}function Fc(){var r,t,e,n,o,u,a,i;if(r=_c,(t=$c())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ay())!==s&&(a=fy())!==s&&(i=$c())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ay())!==s&&(a=fy())!==s&&(i=$c())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Vy),columnList:qy(Xy),ast:n}}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Bc(){var t,e,n,o;return t=_c,(e=function(){var t,e,n,o;t=_c,"union"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Xu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?((n=Qb())===s&&(n=Kb()),n===s&&(n=null),n!==s?(Sc=t,t=e=(o=n)?"union "+o.toLowerCase():"union"):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=function(){var t,e,n,o;t=_c,"intersect"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Qu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e="intersect"),(t=e)===s&&(t=_c,(e=function(){var t,e,n,o;t=_c,"except"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ku));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e="except"),t=e)),t}function Hc(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Hl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Bc())!==s&&(a=fy())!==s&&(i=Hl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Bc())!==s&&(a=fy())!==s&&(i=Hl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s&&(n=fy())!==s?((o=xf())===s&&(o=null),o!==s&&(u=fy())!==s?((a=Rf())===s&&(a=null),a!==s?(Sc=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&n.value&&n.value.length>0&&(r._limit=n),{tableList:Array.from(Vy),columnList:qy(Xy),ast:r}}(t,e,o,a)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)}else _c=r,r=s;return r}function qc(){var t,e;return t=_c,"if"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(i)),e!==s&&fy()!==s&&nv()!==s&&fy()!==s&&ev()!==s?(Sc=t,t=e="IF NOT EXISTS"):(_c=t,t=s),t}function Yc(){var t,e,n;return t=_c,"if"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(l)),e!==s&&fy()!==s?("exists"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(f)),n!==s?(Sc=t,t=e="IF EXISTS"):(_c=t,t=s)):(_c=t,t=s),t}function Wc(){var t,e,n;return t=_c,"check_option"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(h)),e!==s&&fy()!==s&&qv()!==s&&fy()!==s?("cascaded"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(y)),n===s&&("local"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(d))),n!==s?(Sc=t,t=e={type:"check_option",value:n,symbol:"="}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"security_barrier"===r.substr(_c,16).toLowerCase()?(e=r.substr(_c,16),_c+=16):(e=s,0===Rc&&Mc(C)),e===s&&("security_invoker"===r.substr(_c,16).toLowerCase()?(e=r.substr(_c,16),_c+=16):(e=s,0===Rc&&Mc(m))),e!==s&&fy()!==s&&qv()!==s&&fy()!==s&&(n=Kp())!==s?(Sc=t,t=e=function(r,t){return{type:r.toLowerCase(),value:t.value?"true":"false",symbol:"="}}(e,n)):(_c=t,t=s)),t}function Vc(){var r,t,e,n;return r=_c,(t=bp())!==s&&fy()!==s&&qv()!==s&&fy()!==s?((e=bp())===s&&(e=Wf()),e!==s?(Sc=r,r=t={type:t,symbol:"=",value:"string"==typeof(n=e)?{type:"default",value:n}:n}):(_c=r,r=s)):(_c=r,r=s),r}function Xc(){var r,t,e;return r=_c,(t=ip())!==s&&fy()!==s&&(e=Ry())!==s?(Sc=r,r=t=function(r,t){return{column:r,definition:t}}(t,e)):(_c=r,r=s),r}function Qc(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Xc())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Kc(){var t,e,n,o,u,a,i,c,l,f,p,b;return t=_c,(e=Tp())!==s?(Sc=_c,("begin"!==e.toLowerCase()?void 0:s)!==s&&fy()!==s?("constant"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(S)),n===s&&(n=null),n!==s&&fy()!==s&&(o=Ry())!==s&&fy()!==s?((u=ul())===s&&(u=null),u!==s&&fy()!==s?(a=_c,(i=nv())!==s&&(c=fy())!==s&&(l=fb())!==s?a=i=[i,c,l]:(_c=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?(c=_c,(l=pb())===s&&(":="===r.substr(_c,2)?(l=":=",_c+=2):(l=s,0===Rc&&Mc(x))),l===s&&(l=null),l!==s&&(f=fy())!==s?(p=_c,Rc++,"begin"===r.substr(_c,5).toLowerCase()?(b=r.substr(_c,5),_c+=5):(b=s,0===Rc&&Mc(j)),Rc--,b!==s?(_c=p,p=void 0):p=s,p===s&&(p=Wp())===s&&(p=Wf()),p!==s?c=l=[l,f,p]:(_c=c,c=s)):(_c=c,c=s),c===s&&(c=null),c!==s&&(l=fy())!==s?((f=ay())===s&&(f=null),f!==s?(Sc=t,t=e=function(r,t,e,n,o,s,u){return{keyword:"variable",name:r,constant:t,datatype:e,collate:n,not_null:o&&"not null",definition:s&&s[0]&&{type:"default",keyword:s[0],value:s[2]}}}(e,n,o,u,a,c)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function zc(){var r,t,e,n,o,u;if(r=_c,(t=Kc())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Kc())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Kc())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e,1)):(_c=r,r=s)}else _c=r,r=s;return r}function Zc(){var t,e,n,o;return t=_c,"declare"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(I)),e!==s&&fy()!==s&&(n=zc())!==s?(Sc=t,o=n,t=e={tableList:Array.from(Vy),columnList:qy(Xy),ast:{type:"declare",declare:o,symbol:";"}}):(_c=t,t=s),t}function Jc(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,L;if(t=_c,"LANGUAGE"===r.substr(_c,8)?(e="LANGUAGE",_c+=8):(e=s,0===Rc&&Mc(R)),e!==s&&(n=fy())!==s&&(o=Tp())!==s&&(u=fy())!==s?(Sc=t,t=e={prefix:"LANGUAGE",type:"default",value:o}):(_c=t,t=s),t===s&&(t=_c,"transorm"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(N)),e!==s&&(n=fy())!==s?(o=_c,"FOR"===r.substr(_c,3)?(u="FOR",_c+=3):(u=s,0===Rc&&Mc(O)),u!==s&&(a=fy())!==s?("TYPE"===r.substr(_c,4)?(i="TYPE",_c+=4):(i=s,0===Rc&&Mc(k)),i!==s&&(c=fy())!==s&&(l=Tp())!==s?o=u=[u,a,i,c,l]:(_c=o,o=s)):(_c=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s?(Sc=t,t=e=(L=o)?{prefix:["TRANSORM",L[0].toUpperCase(),L[2].toUpperCase()].join(" "),type:"default",value:L[4]}:{type:"origin",value:"TRANSORM"}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"window"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(U)),e===s&&("immutable"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(M)),e===s&&("stable"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(D)),e===s&&("volatile"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(P)),e===s&&("strict"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(G)))))),e!==s&&(n=fy())!==s?(Sc=t,t=e={type:"origin",value:e}):(_c=t,t=s),t===s&&(t=_c,"not"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc($)),e===s&&(e=null),e!==s&&(n=fy())!==s?("leakproof"===r.substr(_c,9).toLowerCase()?(o=r.substr(_c,9),_c+=9):(o=s,0===Rc&&Mc(F)),o!==s&&(u=fy())!==s?(Sc=t,t=e={type:"origin",value:[e,"LEAKPROOF"].filter(r=>r).join(" ")}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"called"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(B)),e===s&&(e=_c,"returns"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(T)),n!==s&&(o=fy())!==s?("null"===r.substr(_c,4).toLowerCase()?(u=r.substr(_c,4),_c+=4):(u=s,0===Rc&&Mc(H)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s)),e===s&&(e=null),e!==s&&(n=fy())!==s?("on"===r.substr(_c,2).toLowerCase()?(o=r.substr(_c,2),_c+=2):(o=s,0===Rc&&Mc(q)),o!==s&&(u=fy())!==s?("null"===r.substr(_c,4).toLowerCase()?(a=r.substr(_c,4),_c+=4):(a=s,0===Rc&&Mc(H)),a!==s&&(i=fy())!==s?("input"===r.substr(_c,5).toLowerCase()?(c=r.substr(_c,5),_c+=5):(c=s,0===Rc&&Mc(Y)),c!==s&&(l=fy())!==s?(Sc=t,t=e=function(r){return Array.isArray(r)&&(r=[r[0],r[2]].join(" ")),{type:"origin",value:r+" ON NULL INPUT"}}(e)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"external"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(W)),e===s&&(e=null),e!==s&&(n=fy())!==s?("security"===r.substr(_c,8).toLowerCase()?(o=r.substr(_c,8),_c+=8):(o=s,0===Rc&&Mc(V)),o!==s&&(u=fy())!==s?("invoker"===r.substr(_c,7).toLowerCase()?(a=r.substr(_c,7),_c+=7):(a=s,0===Rc&&Mc(X)),a===s&&("definer"===r.substr(_c,7).toLowerCase()?(a=r.substr(_c,7),_c+=7):(a=s,0===Rc&&Mc(Q))),a!==s&&(i=fy())!==s?(Sc=t,t=e=function(r,t){return{type:"origin",value:[r,"SECURITY",t].filter(r=>r).join(" ")}}(e,a)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"parallel"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(K)),e!==s&&(n=fy())!==s?("unsafe"===r.substr(_c,6).toLowerCase()?(o=r.substr(_c,6),_c+=6):(o=s,0===Rc&&Mc(z)),o===s&&("restricted"===r.substr(_c,10).toLowerCase()?(o=r.substr(_c,10),_c+=10):(o=s,0===Rc&&Mc(Z)),o===s&&("safe"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(J)))),o!==s&&(u=fy())!==s?(Sc=t,t=e=function(r){return{type:"origin",value:["PARALLEL",r].join(" ")}}(o)):(_c=t,t=s)):(_c=t,t=s),t===s))))))){if(t=_c,(e=Ob())!==s)if((n=fy())!==s){if(o=[],rr.test(r.charAt(_c))?(u=r.charAt(_c),_c++):(u=s,0===Rc&&Mc(tr)),u!==s)for(;u!==s;)o.push(u),rr.test(r.charAt(_c))?(u=r.charAt(_c),_c++):(u=s,0===Rc&&Mc(tr));else o=s;if(o!==s)if((u=fy())!==s)if((a=Zc())===s&&(a=null),a!==s)if((i=fy())!==s)if("begin"===r.substr(_c,5).toLowerCase()?(c=r.substr(_c,5),_c+=5):(c=s,0===Rc&&Mc(j)),c===s&&(c=null),c!==s)if((l=fy())!==s)if((f=Fc())!==s)if(fy()!==s)if((p=fv())===s&&(p=null),p!==s)if(Sc=_c,w=p,((d=c)&&w||!d&&!w?void 0:s)!==s)if(fy()!==s)if((b=ay())===s&&(b=null),b!==s)if(fy()!==s){if(v=[],er.test(r.charAt(_c))?(y=r.charAt(_c),_c++):(y=s,0===Rc&&Mc(nr)),y!==s)for(;y!==s;)v.push(y),er.test(r.charAt(_c))?(y=r.charAt(_c),_c++):(y=s,0===Rc&&Mc(nr));else v=s;v!==s&&(y=fy())!==s?(Sc=t,t=e=function(r,t,e,n,o,s){const u=r.join(""),a=s.join("");if(u!==a)throw new Error(`start symbol '${u}'is not same with end symbol '${a}'`);return{type:"as",declare:t&&t.ast,begin:e,expr:Array.isArray(n.ast)?n.ast.flat():[n.ast],end:o&&o[0],symbol:u}}(o,a,c,f,p,v)):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s}else _c=t,t=s;else _c=t,t=s;t===s&&(t=_c,"cost"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(or)),e===s&&("rows"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(sr))),e!==s&&(n=fy())!==s&&(o=eb())!==s&&(u=fy())!==s?(Sc=t,t=e=function(r,t){return t.prefix=r,t}(e,o)):(_c=t,t=s),t===s&&(t=_c,"support"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(ur)),e!==s&&(n=fy())!==s&&(o=_y())!==s&&(u=fy())!==s?(Sc=t,t=e=function(r){return{prefix:"support",type:"default",value:[r.schema&&r.schema.value,r.name.value].filter(r=>r).join(".")}}(o)):(_c=t,t=s),t===s&&(t=_c,(e=Nb())!==s&&(n=fy())!==s&&(o=Tp())!==s&&(u=fy())!==s?(a=_c,"to"===r.substr(_c,2).toLowerCase()?(i=r.substr(_c,2),_c+=2):(i=s,0===Rc&&Mc(ar)),i===s&&(61===r.charCodeAt(_c)?(i="=",_c++):(i=s,0===Rc&&Mc(ir))),i!==s&&(c=fy())!==s&&(l=vp())!==s?a=i=[i,c,l]:(_c=a,a=s),a===s&&(a=_c,(i=Rb())!==s&&(c=fy())!==s?("current"===r.substr(_c,7).toLowerCase()?(l=r.substr(_c,7),_c+=7):(l=s,0===Rc&&Mc(cr)),l!==s?a=i=[i,c,l]:(_c=a,a=s)):(_c=a,a=s)),a===s&&(a=null),a!==s&&(i=fy())!==s?(Sc=t,t=e=function(r,t){let e;if(t){const r=Array.isArray(t[2])?t[2]:[t[2]];e={prefix:t[0],expr:r.map(r=>({type:"default",value:r}))}}return{type:"set",parameter:r,value:e}}(o,a)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=my()))))}return t}function rl(){var t,e,n,o,u,a,i,c,l,f,p;if(t=_c,hb()!==s)if(fy()!==s)if(e=_c,(n=sv())!==s&&(o=fy())!==s&&(u=_b())!==s?e=n=[n,o,u]:(_c=e,e=s),e===s&&(e=null),e!==s)if((n=fy())!==s)if("function"===r.substr(_c,8).toLowerCase()?(o=r.substr(_c,8),_c+=8):(o=s,0===Rc&&Mc(lr)),o!==s)if((u=fy())!==s)if((a=_y())!==s)if(fy()!==s)if(ny()!==s)if(fy()!==s)if((i=bl())===s&&(i=null),i!==s)if(fy()!==s)if(oy()!==s)if(fy()!==s)if((c=function(){var t,e,n,o,u;return t=_c,"returns"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(T)),e!==s&&fy()!==s?("setof"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(_)),n===s&&(n=null),n!==s&&fy()!==s?((o=Ry())===s&&(o=df()),o!==s?(Sc=t,t=e={type:"returns",keyword:n,expr:o}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"returns"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(T)),e!==s&&fy()!==s&&(n=kb())!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s&&(u=Qc())!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={type:"returns",keyword:"table",expr:u}):(_c=t,t=s)),t}())===s&&(c=null),c!==s)if(fy()!==s){for(l=[],f=Jc();f!==s;)l.push(f),f=Jc();l!==s&&(f=fy())!==s?((p=ay())===s&&(p=null),p!==s&&fy()!==s?(Sc=t,t=function(r,t,e,n,o,s,u){return{tableList:Array.from(Vy),columnList:qy(Xy),ast:{args:o||[],type:"create",replace:t&&"or replace",name:n,returns:s,keyword:e&&e.toLowerCase(),options:u||[]}}}(0,e,o,a,i,c,l)):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;return t}function tl(){var t;return(t=function(){var t,e,n,o,u,a;return t=_c,"increment"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(vr)),e!==s&&fy()!==s?((n=Yb())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Sc=t,u=e,a=o,t=e={resource:"sequence",prefix:n?u.toLowerCase()+" by":u.toLowerCase(),value:a}):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=_c,"minvalue"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(yr)),e!==s&&fy()!==s&&(n=eb())!==s?(Sc=t,t=e=dr(e,n)):(_c=t,t=s),t===s&&(t=_c,"no"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(wr)),e!==s&&fy()!==s?("minvalue"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(yr)),n!==s?(Sc=t,t=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(_c=t,t=s)):(_c=t,t=s)),t}())===s&&(t=function(){var t,e,n;return t=_c,"maxvalue"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Lr)),e!==s&&fy()!==s&&(n=eb())!==s?(Sc=t,t=e=dr(e,n)):(_c=t,t=s),t===s&&(t=_c,"no"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(wr)),e!==s&&fy()!==s?("maxvalue"===r.substr(_c,8).toLowerCase()?(n=r.substr(_c,8),_c+=8):(n=s,0===Rc&&Mc(Lr)),n!==s?(Sc=t,t=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(_c=t,t=s)):(_c=t,t=s)),t}())===s&&(t=function(){var t,e,n,o,u,a;return t=_c,"start"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(hr)),e!==s&&fy()!==s?((n=Hb())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Sc=t,u=e,a=o,t=e={resource:"sequence",prefix:n?u.toLowerCase()+" with":u.toLowerCase(),value:a}):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=_c,"cache"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Cr)),e!==s&&fy()!==s&&(n=eb())!==s?(Sc=t,t=e=dr(e,n)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=_c,"no"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(wr)),e===s&&(e=null),e!==s&&fy()!==s?("cycle"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(mr)),n!==s?(Sc=t,t=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=_c,"owned"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Er)),e!==s&&fy()!==s&&Yb()!==s&&fy()!==s?("none"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Ar)),n!==s?(Sc=t,t=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"owned"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Er)),e!==s&&fy()!==s&&Yb()!==s&&fy()!==s&&(n=ip())!==s?(Sc=t,t=e={resource:"sequence",prefix:"owned by",value:n}):(_c=t,t=s)),t}()),t}function el(){var t,e,n,o,u,a,i,c,l;return t=_c,(e=Wf())!==s&&fy()!==s?((n=ul())===s&&(n=null),n!==s&&fy()!==s?((o=bp())===s&&(o=null),o!==s&&fy()!==s?((u=Vb())===s&&(u=Xb()),u===s&&(u=null),u!==s&&fy()!==s?(a=_c,"nulls"===r.substr(_c,5).toLowerCase()?(i=r.substr(_c,5),_c+=5):(i=s,0===Rc&&Mc(Tr)),i!==s&&(c=fy())!==s?("first"===r.substr(_c,5).toLowerCase()?(l=r.substr(_c,5),_c+=5):(l=s,0===Rc&&Mc(_r)),l===s&&("last"===r.substr(_c,4).toLowerCase()?(l=r.substr(_c,4),_c+=4):(l=s,0===Rc&&Mc(Sr))),l!==s?a=i=[i,c,l]:(_c=a,a=s)):(_c=a,a=s),a===s&&(a=null),a!==s?(Sc=t,t=e=function(r,t,e,n,o){return{collate:t,...r,opclass:e,order_by:n&&n.toLowerCase(),nulls:o&&`${o[0].toLowerCase()} ${o[2].toLowerCase()}`}}(e,n,o,u,a)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function nl(){var r;return(r=sl())===s&&(r=Cl())===s&&(r=ml())===s&&(r=El()),r}function ol(){var t,e,n,o;return(t=function(){var r,t,e;r=_c,(t=Al())!==s&&(Sc=r,t={constraint:t});(r=t)===s&&(r=_c,(t=Qp())===s&&(t=Xp()),t!==s&&fy()!==s?((e=il())===s&&(e=null),e!==s?(Sc=r,t=function(r,t){return r&&!r.value&&(r.value="null"),{default_val:t,nullable:r}}(t,e),r=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=il())!==s&&fy()!==s?((e=Qp())===s&&(e=Xp()),e===s&&(e=null),e!==s?(Sc=r,t=function(r,t){return t&&!t.value&&(t.value="null"),{default_val:r,nullable:t}}(t,e),r=t):(_c=r,r=s)):(_c=r,r=s)));return r}())===s&&(t=_c,"auto_increment"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(Nr)),e!==s&&(Sc=t,e={auto_increment:e.toLowerCase()}),(t=e)===s&&(t=_c,"unique"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Or)),e!==s&&fy()!==s?("key"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc(kr)),n===s&&(n=null),n!==s?(Sc=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"primary"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ur)),e===s&&(e=null),e!==s&&fy()!==s?("key"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc(kr)),n!==s?(Sc=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=vy())!==s&&(Sc=t,e={comment:e}),(t=e)===s&&(t=_c,(e=ul())!==s&&(Sc=t,e={collate:e}),(t=e)===s&&(t=_c,(e=function(){var t,e,n;t=_c,"column_format"===r.substr(_c,13).toLowerCase()?(e=r.substr(_c,13),_c+=13):(e=s,0===Rc&&Mc(Mr));e!==s&&fy()!==s?("fixed"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(Dr)),n===s&&("dynamic"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Pr)),n===s&&("default"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Gr)))),n!==s?(Sc=t,e={type:"column_format",value:n.toLowerCase()},t=e):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={column_format:e}),(t=e)===s&&(t=_c,(e=function(){var t,e,n;t=_c,"storage"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc($r));e!==s&&fy()!==s?("disk"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Fr)),n===s&&("memory"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(Br))),n!==s?(Sc=t,e={type:"storage",value:n.toLowerCase()},t=e):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={storage:e}),(t=e)===s&&(t=_c,(e=Tl())!==s&&(Sc=t,e={reference_definition:e}),(t=e)===s&&(t=_c,(e=function(){var t,e,n,o,u,a,i,c;t=_c,(e=Al())===s&&(e=null);e!==s&&fy()!==s?("check"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(w)),n!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(o=wf())!==s&&fy()!==s&&oy()!==s&&fy()!==s?(u=_c,(a=nv())===s&&(a=null),a!==s&&(i=fy())!==s?("enforced"===r.substr(_c,8).toLowerCase()?(c=r.substr(_c,8),_c+=8):(c=s,0===Rc&&Mc(bt)),c!==s?u=a=[a,i,c]:(_c=u,u=s)):(_c=u,u=s),u===s&&(u=null),u!==s?(Sc=t,e=function(r,t,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:t.toLowerCase(),keyword:r&&r.keyword,constraint:r&&r.constraint,definition:[e],enforced:o.filter(r=>r).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,u),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={check:e}),(t=e)===s&&(t=_c,(e=xl())!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s&&(o=fp())!==s?(Sc=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s))))))))))),t}function sl(){var r,t,e,n;return r=_c,(t=ip())!==s&&fy()!==s?((e=Ry())===s&&(e=Lp()),e!==s&&fy()!==s?((n=function(){var r,t,e,n,o,u;if(r=_c,(t=ol())!==s)if(fy()!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ol())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ol())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;return r}())===s&&(n=null),n!==s?(Sc=r,r=t=function(r,t,e){return Xy.add(`create::${r.table}::${r.column.expr.value}`),"double_quote_string"===t.type&&(t={dataType:`"${t.value}"`}),{column:r,definition:t,resource:"column",...e||{}}}(t,e,n)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r}function ul(){var t,e,n,o,u,a,i,c,l,f;return t=_c,function(){var t,e,n,o;t=_c,"collate"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ut));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="COLLATE"):(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s?((e=qv())===s&&(e=null),e!==s&&fy()!==s?(n=_c,(o=pp())!==s&&(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s?n=o=[o,u,a,i]:(_c=n,n=s),n===s&&(n=null),n!==s&&(o=pp())!==s?(Sc=t,c=e,f=o,t={type:"collate",keyword:"collate",collate:{name:(l=n)?[l[0],f]:f,symbol:c}}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function al(){var r,t,e,n,o;return r=_c,(t=pb())===s&&(t=qv()),t===s&&(t=null),t!==s&&fy()!==s&&(e=Wf())!==s?(Sc=r,o=e,r=t={type:"default",keyword:(n=t)&&n[0],value:o}):(_c=r,r=s),r}function il(){var r,t;return r=_c,pb()!==s&&fy()!==s&&(t=Wf())!==s?(Sc=r,r={type:"default",value:t}):(_c=r,r=s),r}function cl(){var r,t,e,n,o;return r=_c,(t=df())!==s&&fy()!==s?((e=ey())===s&&(e=null),e!==s?(Sc=r,n=t,o=e,Vy.add(`truncate::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),o&&(n.suffix=o),r=t=n):(_c=r,r=s)):(_c=r,r=s),r}function ll(){var r,t,e;return r=_c,(t=ey())!==s&&(Sc=r,t=[{name:"*"}]),(r=t)===s&&(r=_c,(t=bl())===s&&(t=null),t!==s&&fy()!==s&&Wb()!==s&&fy()!==s&&Yb()!==s&&fy()!==s&&(e=bl())!==s?(Sc=r,r=t=function(r,t){const e=r||[];return e.orderby=t,e}(t,e)):(_c=r,r=s),r===s&&(r=bl())),r}function fl(){var t,e;return t=_c,(e=Zb())===s&&("out"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Qr)),e===s&&("variadic"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Kr)))),e!==s&&(Sc=t,e=e.toUpperCase()),t=e}function pl(){var r,t,e,n,o;return r=_c,(t=fl())===s&&(t=null),t!==s&&fy()!==s&&(e=Ry())!==s&&fy()!==s?((n=al())===s&&(n=null),n!==s?(Sc=r,r=t={mode:t,type:e,default:n}):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=fl())===s&&(t=null),t!==s&&fy()!==s&&(e=Tp())!==s&&fy()!==s&&(n=Ry())!==s&&fy()!==s?((o=al())===s&&(o=null),o!==s?(Sc=r,r=t=function(r,t,e,n){return{mode:r,name:t,type:e,default:n}}(t,e,n,o)):(_c=r,r=s)):(_c=r,r=s)),r}function bl(){var r,t,e,n,o,u,a,i;if(r=_c,(t=pl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=pl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=pl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function vl(){var t;return(t=function(){var r,t,e,n,o;r=_c,(t=Yv())!==s&&fy()!==s?((e=Wv())===s&&(e=null),e!==s&&fy()!==s?((n=qc())===s&&(n=null),n!==s&&fy()!==s&&(o=sl())!==s?(Sc=r,u=e,a=o,t={action:"add",if_not_exists:n,...a,keyword:u,resource:"column",type:"alter"},r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);var u,a;return r}())===s&&(t=function(){var r,t,e;r=_c,(t=Yv())!==s&&fy()!==s&&(e=El())!==s?(Sc=r,t=function(r){return{action:"add",create_definitions:r,resource:"constraint",type:"alter"}}(e),r=t):(_c=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o;r=_c,(t=yb())!==s&&fy()!==s?((e=Wv())===s&&(e=null),e!==s&&fy()!==s?((n=Yc())===s&&(n=null),n!==s&&fy()!==s&&(o=ip())!==s?(Sc=r,t=function(r,t,e){return{action:"drop",column:e,if_exists:t,keyword:r,resource:"column",type:"alter"}}(e,n,o),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=function(){var r,t,e;r=_c,(t=Yv())!==s&&fy()!==s&&(e=Cl())!==s?(Sc=r,n=e,t={action:"add",type:"alter",...n},r=t):(_c=r,r=s);var n;return r}())===s&&(t=function(){var r,t,e;r=_c,(t=Yv())!==s&&fy()!==s&&(e=ml())!==s?(Sc=r,n=e,t={action:"add",type:"alter",...n},r=t):(_c=r,r=s);var n;return r}())===s&&(t=yl())===s&&(t=Ll())===s&&(t=hl())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y;t=_c,(e=db())!==s&&fy()!==s?((n=Wv())===s&&(n=null),n!==s&&fy()!==s&&(o=ip())!==s&&fy()!==s?(u=_c,(a=Nb())!==s&&(i=fy())!==s?("data"===r.substr(_c,4).toLowerCase()?(c=r.substr(_c,4),_c+=4):(c=s,0===Rc&&Mc(ct)),c!==s?u=a=[a,i,c]:(_c=u,u=s)):(_c=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?("type"===r.substr(_c,4).toLowerCase()?(i=r.substr(_c,4),_c+=4):(i=s,0===Rc&&Mc(lt)),i!==s&&(c=fy())!==s&&(l=Ry())!==s&&fy()!==s?((f=ul())===s&&(f=null),f!==s&&fy()!==s?(p=_c,(b=Bb())!==s&&(v=fy())!==s&&(y=Wf())!==s?p=b=[b,v,y]:(_c=p,p=s),p===s&&(p=null),p!==s?(Sc=t,e=function(r,t,e,n,o,s){return t.suffix=e?"set data type":"type",{action:"alter",column:t,keyword:r,resource:"column",definition:n,collate:o,using:s&&s[2],type:"alter"}}(n,o,u,l,f,p),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var r,t,e,n,o;r=_c,(t=db())!==s&&fy()!==s?((e=Wv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s&&Nb()!==s&&fy()!==s&&pb()!==s&&fy()!==s&&(o=Wf())!==s?(Sc=r,t=function(r,t,e){return{action:"alter",column:t,keyword:r,resource:"column",default_val:{type:"set default",value:e},type:"alter"}}(e,n,o),r=t):(_c=r,r=s)):(_c=r,r=s);r===s&&(r=_c,(t=db())!==s&&fy()!==s?((e=Wv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s&&yb()!==s&&fy()!==s&&pb()!==s?(Sc=r,t=function(r,t){return{action:"alter",column:t,keyword:r,resource:"column",default_val:{type:"drop default"},type:"alter"}}(e,n),r=t):(_c=r,r=s)):(_c=r,r=s));return r}())===s&&(t=function(){var r,t,e,n,o,u;r=_c,(t=db())!==s&&fy()!==s?((e=Wv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s?((o=Nb())===s&&(o=yb()),o!==s&&fy()!==s&&(u=Qp())!==s?(Sc=r,t=function(r,t,e,n){return n.action=e.toLowerCase(),{action:"alter",column:t,keyword:r,resource:"column",nullable:n,type:"alter"}}(e,n,o,u),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}()),t}function yl(){var r,t,e,n,o;return r=_c,Sb()!==s&&fy()!==s?((t=bb())===s&&(t=Ob()),t===s&&(t=null),t!==s&&fy()!==s&&(e=bp())!==s?(Sc=r,o=e,r={action:"rename",type:"alter",resource:"table",keyword:(n=t)&&n[0].toLowerCase(),table:o}):(_c=r,r=s)):(_c=r,r=s),r}function dl(){var t,e,n;return t=_c,"owner"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Zr)),e!==s&&fy()!==s&&bb()!==s&&fy()!==s?((n=bp())===s&&("current_role"===r.substr(_c,12).toLowerCase()?(n=r.substr(_c,12),_c+=12):(n=s,0===Rc&&Mc(Jr)),n===s&&("current_user"===r.substr(_c,12).toLowerCase()?(n=r.substr(_c,12),_c+=12):(n=s,0===Rc&&Mc(rt)),n===s&&("session_user"===r.substr(_c,12).toLowerCase()?(n=r.substr(_c,12),_c+=12):(n=s,0===Rc&&Mc(tt))))),n!==s?(Sc=t,t=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(_c=t,t=s)):(_c=t,t=s),t}function wl(){var r,t;return r=_c,Nb()!==s&&fy()!==s&&Mb()!==s&&fy()!==s&&(t=bp())!==s?(Sc=r,r={action:"set",type:"alter",resource:"table",keyword:"schema",table:t}):(_c=r,r=s),r}function Ll(){var t,e,n,o;return t=_c,"algorithm"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(et)),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s?("default"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(Gr)),o===s&&("instant"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(nt)),o===s&&("inplace"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(ot)),o===s&&("copy"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(st))))),o!==s?(Sc=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function hl(){var t,e,n,o;return t=_c,"lock"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ut)),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s?("default"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(Gr)),o===s&&("none"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(Ar)),o===s&&("shared"===r.substr(_c,6).toLowerCase()?(o=r.substr(_c,6),_c+=6):(o=s,0===Rc&&Mc(at)),o===s&&("exclusive"===r.substr(_c,9).toLowerCase()?(o=r.substr(_c,9),_c+=9):(o=s,0===Rc&&Mc(it))))),o!==s?(Sc=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function Cl(){var r,t,e,n,o,u;return r=_c,(t=Vv())===s&&(t=Xv()),t!==s&&fy()!==s?((e=Ap())===s&&(e=null),e!==s&&fy()!==s?((n=cf())===s&&(n=null),n!==s&&fy()!==s&&(o=Wl())!==s&&fy()!==s?((u=lf())===s&&(u=null),u!==s&&fy()!==s?(Sc=r,r=t=function(r,t,e,n,o){return{index:t,definition:n,keyword:r.toLowerCase(),index_type:e,resource:"index",index_options:o}}(t,e,n,o,u)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r}function ml(){var t,e,n,o,u,a;return t=_c,(e=function(){var t,e,n,o;t=_c,"fulltext"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Fi));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="FULLTEXT"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=_c,"spatial"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Bi));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SPATIAL"):(_c=t,t=s)):(_c=t,t=s);return t}()),e!==s&&fy()!==s?((n=Vv())===s&&(n=Xv()),n===s&&(n=null),n!==s&&fy()!==s?((o=Ap())===s&&(o=null),o!==s&&fy()!==s&&(u=Wl())!==s&&fy()!==s?((a=lf())===s&&(a=null),a!==s&&fy()!==s?(Sc=t,t=e=function(r,t,e,n,o){return{index:e,definition:n,keyword:t&&`${r.toLowerCase()} ${t.toLowerCase()}`||r.toLowerCase(),index_options:o,resource:"index"}}(e,n,o,u,a)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function El(){var t;return(t=function(){var t,e,n,o,u,a;t=_c,(e=Al())===s&&(e=null);e!==s&&fy()!==s?("primary key"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(ft)),n!==s&&fy()!==s?((o=cf())===s&&(o=null),o!==s&&fy()!==s&&(u=Wl())!==s&&fy()!==s?((a=lf())===s&&(a=null),a!==s?(Sc=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var i,c,l,f,p;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=_c,(t=Al())===s&&(t=null);t!==s&&fy()!==s&&(e=Qv())!==s&&fy()!==s?((n=Vv())===s&&(n=Xv()),n===s&&(n=null),n!==s&&fy()!==s?((o=Ap())===s&&(o=null),o!==s&&fy()!==s?((u=cf())===s&&(u=null),u!==s&&fy()!==s&&(a=Wl())!==s&&fy()!==s?((i=lf())===s&&(i=null),i!==s?(Sc=r,l=e,f=n,p=o,b=u,v=a,y=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);var c,l,f,p,b,v,y;return r}())===s&&(t=function(){var t,e,n,o,u,a;t=_c,(e=Al())===s&&(e=null);e!==s&&fy()!==s?("foreign key"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(pt)),n!==s&&fy()!==s?((o=Ap())===s&&(o=null),o!==s&&fy()!==s&&(u=Wl())!==s&&fy()!==s?((a=Tl())===s&&(a=null),a!==s?(Sc=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var i,c,l,f,p;return t}())===s&&(t=gl()),t}function Al(){var r,t,e;return r=_c,(t=zv())!==s&&fy()!==s?((e=bp())===s&&(e=null),e!==s?(Sc=r,r=t=function(r,t){return{keyword:r.toLowerCase(),constraint:t}}(t,e)):(_c=r,r=s)):(_c=r,r=s),r}function gl(){var t,e,n,o,u,a,i;return t=_c,(e=Al())===s&&(e=null),e!==s&&fy()!==s?("check"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(w)),n!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(o=Vf())!==s&&fy()!==s&&oy()!==s?(Sc=t,a=n,i=o,t=e={constraint:(u=e)&&u.constraint,definition:[i],constraint_type:a.toLowerCase(),keyword:u&&u.keyword,resource:"constraint"}):(_c=t,t=s)):(_c=t,t=s),t}function Tl(){var t,e,n,o,u,a,i,c,l,f;return t=_c,(e=Jv())!==s&&fy()!==s&&(n=df())!==s&&fy()!==s&&(o=Wl())!==s&&fy()!==s?("match full"===r.substr(_c,10).toLowerCase()?(u=r.substr(_c,10),_c+=10):(u=s,0===Rc&&Mc(vt)),u===s&&("match partial"===r.substr(_c,13).toLowerCase()?(u=r.substr(_c,13),_c+=13):(u=s,0===Rc&&Mc(yt)),u===s&&("match simple"===r.substr(_c,12).toLowerCase()?(u=r.substr(_c,12),_c+=12):(u=s,0===Rc&&Mc(dt)))),u===s&&(u=null),u!==s&&fy()!==s?((a=_l())===s&&(a=null),a!==s&&fy()!==s?((i=_l())===s&&(i=null),i!==s?(Sc=t,c=u,l=a,f=i,t=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=_l())!==s&&(Sc=t,e={on_action:[e]}),t=e),t}function _l(){var t,e,n,o;return t=_c,Pb()!==s&&fy()!==s?((e=Ab())===s&&(e=Lb()),e!==s&&fy()!==s&&(n=function(){var t,e,n;t=_c,(e=Uv())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=$f())===s&&(n=null),n!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(_c=t,t=s)):(_c=t,t=s);t===s&&(t=_c,"restrict"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(qr)),e===s&&("cascade"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Hr)),e===s&&("set null"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(wt)),e===s&&("no action"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Lt)),e===s&&("set default"===r.substr(_c,11).toLowerCase()?(e=r.substr(_c,11),_c+=11):(e=s,0===Rc&&Mc(ht)),e===s&&(e=Uv()))))),e!==s&&(Sc=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==s?(Sc=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(_c=t,t=s)):(_c=t,t=s),t}function Sl(){var t,e,n,o,u,a,i;return t=_c,(e=gb())===s&&(e=Ab())===s&&(e=Ov()),e!==s&&(Sc=t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===s&&(t=_c,(e=Lb())!==s&&fy()!==s?(n=_c,"of"===r.substr(_c,2).toLowerCase()?(o=r.substr(_c,2),_c+=2):(o=s,0===Rc&&Mc(Rr)),o!==s&&(u=fy())!==s&&(a=Cf())!==s?n=o=[o,u,a]:(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):(_c=t,t=s)):(_c=t,t=s)),t}function xl(){var t,e,n;return t=_c,"character"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Nt)),e!==s&&fy()!==s?("set"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc(Ot)),n!==s?(Sc=t,t=e="CHARACTER SET"):(_c=t,t=s)):(_c=t,t=s),t}function jl(){var t,e,n,o,u,a,i,c,l;return t=_c,(e=pb())===s&&(e=null),e!==s&&fy()!==s?((n=xl())===s&&("charset"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(kt)),n===s&&("collate"===r.substr(_c,7).toLowerCase()?(n=r.substr(_c,7),_c+=7):(n=s,0===Rc&&Mc(Ut)))),n!==s&&fy()!==s?((o=qv())===s&&(o=null),o!==s&&fy()!==s&&(u=fp())!==s?(Sc=t,i=n,c=o,l=u,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function Il(){var t,e,n,o,u,a,i,c,l;return t=_c,"auto_increment"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(Nr)),e===s&&("avg_row_length"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(Mt)),e===s&&("key_block_size"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(Dt)),e===s&&("max_rows"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Pt)),e===s&&("min_rows"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Gt)),e===s&&("stats_sample_pages"===r.substr(_c,18).toLowerCase()?(e=r.substr(_c,18),_c+=18):(e=s,0===Rc&&Mc($t))))))),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Sc=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=jl())===s&&(t=_c,(e=Kv())===s&&("connection"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(Ft))),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s&&(o=zp())!==s?(Sc=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"compression"===r.substr(_c,11).toLowerCase()?(e=r.substr(_c,11),_c+=11):(e=s,0===Rc&&Mc(Bt)),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s?(o=_c,39===r.charCodeAt(_c)?(u="'",_c++):(u=s,0===Rc&&Mc(Ht)),u!==s?("zlib"===r.substr(_c,4).toLowerCase()?(a=r.substr(_c,4),_c+=4):(a=s,0===Rc&&Mc(qt)),a===s&&("lz4"===r.substr(_c,3).toLowerCase()?(a=r.substr(_c,3),_c+=3):(a=s,0===Rc&&Mc(Yt)),a===s&&("none"===r.substr(_c,4).toLowerCase()?(a=r.substr(_c,4),_c+=4):(a=s,0===Rc&&Mc(Ar)))),a!==s?(39===r.charCodeAt(_c)?(i="'",_c++):(i=s,0===Rc&&Mc(Ht)),i!==s?o=u=[u,a,i]:(_c=o,o=s)):(_c=o,o=s)):(_c=o,o=s),o!==s?(Sc=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"engine"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Wt)),e!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s&&(o=Tp())!==s?(Sc=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=jb())!==s&&fy()!==s&&(n=Yb())!==s&&fy()!==s&&(o=Wf())!==s?(Sc=t,t=e=function(r){return{keyword:"partition by",value:r}}(o)):(_c=t,t=s))))),t}function Rl(){var t,e,n;return t=_c,(e=wb())===s&&(e=gb())===s&&(e=Lb())===s&&(e=Ab())===s&&(e=Ov())===s&&(e=Jv())===s&&("trigger"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ct))),e!==s&&(Sc=t,n=e,e={type:"origin",value:Array.isArray(n)?n[0]:n}),t=e}function Nl(){var t,e,n,o;return t=_c,Qb()!==s?(e=_c,(n=fy())!==s?("privileges"===r.substr(_c,10).toLowerCase()?(o=r.substr(_c,10),_c+=10):(o=s,0===Rc&&Mc(ie)),o!==s?e=n=[n,o]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=null),e!==s?(Sc=t,t={type:"origin",value:e?"all privileges":"all"}):(_c=t,t=s)):(_c=t,t=s),t}function Ol(){var t;return(t=Rl())===s&&(t=function(){var t,e;return t=_c,"usage"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(se)),e===s&&(e=wb())===s&&(e=Lb()),e!==s&&(Sc=t,e=ue(e)),t=e}())===s&&(t=function(){var t,e;return t=_c,(e=hb())===s&&("connect"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(ae)),e===s&&(e=Cb())===s&&(e=Eb())),e!==s&&(Sc=t,e=ue(e)),t=e}())===s&&(t=function(){var t,e;return t=_c,"usage"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(se)),e!==s&&(Sc=t,e=ce(e)),(t=e)===s&&(t=Nl()),t}())===s&&(t=function(){var t,e;return t=_c,"execute"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(gt)),e!==s&&(Sc=t,e=ce(e)),(t=e)===s&&(t=Nl()),t}()),t}function kl(){var r,t,e,n,o,u,a,i;return r=_c,(t=Ol())!==s&&fy()!==s?(e=_c,(n=ny())!==s&&(o=fy())!==s&&(u=Cf())!==s&&(a=fy())!==s&&(i=oy())!==s?e=n=[n,o,u,a,i]:(_c=e,e=s),e===s&&(e=null),e!==s?(Sc=r,r=t=function(r,t){return{priv:r,columns:t&&t[2]}}(t,e)):(_c=r,r=s)):(_c=r,r=s),r}function Ul(){var r,t,e,n,o,u,a;return r=_c,t=_c,(e=bp())!==s&&(n=fy())!==s&&(o=ry())!==s?t=e=[e,n,o]:(_c=t,t=s),t===s&&(t=null),t!==s&&(e=fy())!==s?((n=bp())===s&&(n=ey()),n!==s?(Sc=r,a=n,r=t={prefix:(u=t)&&u[0],name:a}):(_c=r,r=s)):(_c=r,r=s),r}function Ml(){var t,e,n,o;return t=_c,(e=qb())===s&&(e=null),e!==s&&fy()!==s&&(n=bp())!==s?(Sc=t,o=n,t=e={name:{type:"origin",value:e?`${group} ${o}`:o}}):(_c=t,t=s),t===s&&(t=_c,"public"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(me)),e===s&&(e=function(){var t,e,n,o;t=_c,"current_role"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Jr));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CURRENT_ROLE"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(e=Mv())===s&&(e=Dv()),e!==s&&(Sc=t,e=function(r){return{name:{type:"origin",value:r}}}(e)),t=e),t}function Dl(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Ml())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Pl(){var t,e,n,o,u,a,i,c;return t=_c,"grant"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Ee)),e!==s&&(Sc=t,e={type:"grant"}),(t=e)===s&&(t=_c,"revoke"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Te)),e!==s&&fy()!==s?(n=_c,"grant"===r.substr(_c,5).toLowerCase()?(o=r.substr(_c,5),_c+=5):(o=s,0===Rc&&Mc(Ee)),o!==s&&(u=fy())!==s?("option"===r.substr(_c,6).toLowerCase()?(a=r.substr(_c,6),_c+=6):(a=s,0===Rc&&Mc(Ae)),a!==s&&(i=fy())!==s?("for"===r.substr(_c,3).toLowerCase()?(c=r.substr(_c,3),_c+=3):(c=s,0===Rc&&Mc(Ir)),c!==s?n=o=[o,u,a,i,c]:(_c=n,n=s)):(_c=n,n=s)):(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=t,t=e={type:"revoke",grant_option_for:n&&{type:"origin",value:"grant option for"}}):(_c=t,t=s)):(_c=t,t=s)),t}function Gl(){var t,e,n,o,u,a;return t=_c,"elseif"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(_e)),e!==s&&fy()!==s&&(n=Wf())!==s&&fy()!==s?("then"===r.substr(_c,4).toLowerCase()?(o=r.substr(_c,4),_c+=4):(o=s,0===Rc&&Mc(Se)),o!==s&&fy()!==s&&(u=$c())!==s&&fy()!==s?((a=ay())===s&&(a=null),a!==s?(Sc=t,t=e={type:"elseif",boolean_expr:n,then:u,semicolon:a}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function $l(){var t,e,n,o;return t=_c,"isolation"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Ze)),e!==s&&fy()!==s?("level"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(Je)),n!==s&&fy()!==s&&(o=function(){var t,e,n;return t=_c,"serializable"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(We)),e!==s&&(Sc=t,e={type:"origin",value:"serializable"}),(t=e)===s&&(t=_c,"repeatable"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(Ve)),e!==s&&fy()!==s?("read"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Xe)),n!==s?(Sc=t,t=e={type:"origin",value:"repeatable read"}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"read"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Xe)),e!==s&&fy()!==s?("committed"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(Qe)),n===s&&("uncommitted"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(Ke))),n!==s?(Sc=t,t=e=ze(n)):(_c=t,t=s)):(_c=t,t=s))),t}())!==s?(Sc=t,t=e={type:"origin",value:"isolation level "+o.value}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"read"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Xe)),e!==s&&fy()!==s?("write"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(rn)),n===s&&("only"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Yr))),n!==s?(Sc=t,t=e=ze(n)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=nv())===s&&(e=null),e!==s&&fy()!==s?("deferrable"===r.substr(_c,10).toLowerCase()?(n=r.substr(_c,10),_c+=10):(n=s,0===Rc&&Mc(_t)),n!==s?(Sc=t,t=e={type:"origin",value:e?"not deferrable":"deferrable"}):(_c=t,t=s)):(_c=t,t=s))),t}function Fl(){var r,t,e,n,o,u,a,i;if(r=_c,(t=$l())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=$l())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=$l())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Bl(){var t,e,n,o,u,a,i;return t=_c,e=_c,40===r.charCodeAt(_c)?(n="(",_c++):(n=s,0===Rc&&Mc(vn)),n!==s&&(o=fy())!==s&&(u=Hl())!==s&&(a=fy())!==s?(41===r.charCodeAt(_c)?(i=")",_c++):(i=s,0===Rc&&Mc(yn)),i!==s?e=n=[n,o,u,a,i]:(_c=e,e=s)):(_c=e,e=s),e!==s&&(Sc=t,e={...e[2],parentheses_symbol:!0}),t=e}function Hl(){var t,e;return t=_c,wb()!==s&&fy()!==s?(59===r.charCodeAt(_c)?(e=";",_c++):(e=s,0===Rc&&Mc(dn)),e!==s?(Sc=t,t={type:"select"}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=Xl())===s&&(t=Bl()),t}function ql(){var r,t,e,n,o,u,a,i,c;if(r=_c,Hb()!==s)if(fy()!==s)if((t=Yl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Yl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Yl())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;return r===s&&(r=_c,fy()!==s&&Hb()!==s&&(t=fy())!==s&&(e=Tb())!==s&&(n=fy())!==s&&(o=Yl())!==s?(Sc=r,(c=o).recursive=!0,r=[c]):(_c=r,r=s)),r}function Yl(){var r,t,e,n,o,u;return r=_c,(t=zp())===s&&(t=Tp()),t!==s&&fy()!==s?((e=Wl())===s&&(e=null),e!==s&&fy()!==s&&Ob()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=$c())!==s&&fy()!==s&&oy()!==s?(Sc=r,u=e,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n.ast,columns:u}):(_c=r,r=s)):(_c=r,r=s),r}function Wl(){var r,t;return r=_c,ny()!==s&&fy()!==s&&(t=Cf())!==s&&fy()!==s&&oy()!==s?(Sc=r,r=t):(_c=r,r=s),r}function Vl(){var r,t,e;return r=_c,(t=Kb())!==s&&fy()!==s&&Pb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Kl())!==s&&fy()!==s&&oy()!==s?(Sc=r,r=t=function(r,t,e){return console.lo,{type:r+" ON",columns:e}}(t,0,e)):(_c=r,r=s),r===s&&(r=_c,(t=Kb())===s&&(t=null),t!==s&&(Sc=r,t={type:t}),r=t),r}function Xl(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d;return t=_c,fy()!==s?((e=ql())===s&&(e=null),e!==s&&fy()!==s&&wb()!==s&&py()!==s?((n=function(){var r,t,e,n,o,u;if(r=_c,(t=Ql())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Ql())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Ql())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())===s&&(n=null),n!==s&&fy()!==s?((o=Vl())===s&&(o=null),o!==s&&fy()!==s&&(u=zl())!==s&&fy()!==s?((a=sf())===s&&(a=null),a!==s&&fy()!==s?((i=uf())===s&&(i=null),i!==s&&fy()!==s?((c=sf())===s&&(c=null),c!==s&&fy()!==s?((l=hf())===s&&(l=null),l!==s&&fy()!==s?((f=function(){var r,t,e;r=_c,(t=qb())!==s&&fy()!==s&&Yb()!==s&&fy()!==s&&(e=$f())!==s?(Sc=r,t={columns:e.value},r=t):(_c=r,r=s);return r}())===s&&(f=null),f!==s&&fy()!==s?((p=function(){var t,e;t=_c,function(){var t,e,n,o;t=_c,"having"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(na));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s&&(e=Vf())!==s?(Sc=t,t=e):(_c=t,t=s);return t}())===s&&(p=null),p!==s&&fy()!==s?((b=xf())===s&&(b=null),b!==s&&fy()!==s?((v=Rf())===s&&(v=null),v!==s&&fy()!==s?((y=function(){var t,e;t=_c,function(){var t,e,n,o;t=_c,"window"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(U));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=mf())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=mf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=mf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=t,t={keyword:"window",type:"window",expr:e}):(_c=t,t=s);return t}())===s&&(y=null),y!==s&&fy()!==s?((d=sf())===s&&(d=null),d!==s?(Sc=t,t=function(r,t,e,n,o,s,u,a,i,c,l,f,p,b){if(o&&u||o&&b||u&&b||o&&u&&b)throw new Error("A given SQL statement can contain at most one INTO clause");return s&&s.forEach(r=>r.table&&Vy.add(`select::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{with:r,type:"select",options:t,distinct:e,columns:n,into:{...o||u||b||{},position:(o?"column":u&&"from")||b&&"end"},from:s,where:a,groupby:i,having:c,orderby:l,limit:f,window:p}}(e,n,o,u,a,i,c,l,f,p,b,v,y,d)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function Ql(){var t,e;return t=_c,(e=function(){var t;"sql_calc_found_rows"===r.substr(_c,19).toLowerCase()?(t=r.substr(_c,19),_c+=19):(t=s,0===Rc&&Mc(Yi));return t}())===s&&((e=function(){var t;"sql_cache"===r.substr(_c,9).toLowerCase()?(t=r.substr(_c,9),_c+=9):(t=s,0===Rc&&Mc(Wi));return t}())===s&&(e=function(){var t;"sql_no_cache"===r.substr(_c,12).toLowerCase()?(t=r.substr(_c,12),_c+=12):(t=s,0===Rc&&Mc(Vi));return t}()),e===s&&(e=function(){var t;"sql_big_result"===r.substr(_c,14).toLowerCase()?(t=r.substr(_c,14),_c+=14):(t=s,0===Rc&&Mc(Qi));return t}())===s&&(e=function(){var t;"sql_small_result"===r.substr(_c,16).toLowerCase()?(t=r.substr(_c,16),_c+=16):(t=s,0===Rc&&Mc(Xi));return t}())===s&&(e=function(){var t;"sql_buffer_result"===r.substr(_c,17).toLowerCase()?(t=r.substr(_c,17),_c+=17):(t=s,0===Rc&&Mc(Ki));return t}())),e!==s&&(Sc=t,e=e),t=e}function Kl(){var r,t,e,n,o,u,a,i;if(r=_c,(t=ef())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function zl(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Qb())===s&&(t=_c,(e=ey())!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t===s&&(t=ey())),t!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){Xy.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?Fy(e,t):[e]}(0,e)):(_c=r,r=s)}else _c=r,r=s;return r===s&&(r=Kl()),r}function Zl(){var r,t;return r=_c,sy()!==s&&fy()!==s?((t=eb())===s&&(t=zp()),t!==s&&fy()!==s&&uy()!==s?(Sc=r,r={brackets:!0,index:t}):(_c=r,r=s)):(_c=r,r=s),r}function Jl(){var r,t,e,n,o,u;if(r=_c,(t=Zl())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Zl())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Zl())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e,1)):(_c=r,r=s)}else _c=r,r=s;return r}function rf(){var r,t,e,n,o;return r=_c,(t=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Wf())!==s){for(e=[],n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);e!==s?(Sc=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=Gy(t[e][1],n,o)}return o}(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s&&fy()!==s?((e=Jl())===s&&(e=null),e!==s?(Sc=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(_c=r,r=s)):(_c=r,r=s),r}function tf(){var t,e,n,o;return t=_c,"at"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(wn)),e!==s&&fy()!==s&&Rv()!==s&&fy()!==s?("zone"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Ln)),n!==s&&fy()!==s?((o=dp())===s&&(o=ip()),o!==s?(Sc=t,t=e=[{type:"origin",value:"at time zone"},o]):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function ef(){var r,t,e,n,o,u,a,i,c,l,f;if(r=_c,(t=ap())!==s&&(Sc=r,t=function(r){return{expr:r,as:null}}(t)),(r=t)===s){if(r=_c,(t=ip())!==s)if((e=fy())!==s)if((n=Yp())!==s)if((o=fy())!==s){if(u=[],(a=tf())!==s)for(;a!==s;)u.push(a),a=tf();else u=s;u!==s&&(a=fy())!==s?((i=of())===s&&(i=null),i!==s?(Sc=r,r=t=function(r,t,e,n){return t.target[t.target.length-1].suffix=e.flat(),{...t,as:n,type:"cast",expr:r,suffix:e.flat()}}(t,n,u,i)):(_c=r,r=s)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;if(r===s){if(r=_c,(t=cp())===s&&(t=rf()),t!==s)if((e=fy())!==s)if((n=Yp())!==s)if((o=fy())!==s){for(u=[],a=_c,(i=fy())!==s?((c=tp())===s&&(c=np()),c!==s&&(l=fy())!==s&&(f=rf())!==s?a=i=[i,c,l,f]:(_c=a,a=s)):(_c=a,a=s);a!==s;)u.push(a),a=_c,(i=fy())!==s?((c=tp())===s&&(c=np()),c!==s&&(l=fy())!==s&&(f=rf())!==s?a=i=[i,c,l,f]:(_c=a,a=s)):(_c=a,a=s);if(u!==s)if((a=fy())!==s){for(i=[],c=tf();c!==s;)i.push(c),c=tf();i!==s&&(c=fy())!==s?((l=of())===s&&(l=null),l!==s?(Sc=r,r=t=function(r,t,e,n,o){return"column_ref"===r.type&&n.length&&(r.column.options={type:"expr_list",value:n.flat(),separator:" "}),{...t,as:o,type:"cast",expr:r,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]}}}(t,n,u,i,l)):(_c=r,r=s)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;r===s&&(r=_c,(t=pp())!==s&&(e=fy())!==s&&(n=ry())!==s?(o=_c,(u=fp())!==s&&(a=fy())!==s&&(i=ry())!==s?o=u=[u,a,i]:(_c=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s&&(a=ey())!==s?(Sc=r,r=t=function(r,t){const e=t&&t[0];let n;e&&(n=r,r=e),Xy.add(`select::${r?r.value:null}::(.*)`);return{expr:{type:"column_ref",table:r,schema:n,column:"*"},as:null}}(t,o)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,t=_c,(e=pp())!==s&&(n=fy())!==s&&(o=ry())!==s?t=e=[e,n,o]:(_c=t,t=s),t===s&&(t=null),t!==s&&(e=fy())!==s&&(n=ey())!==s?(Sc=r,r=t=function(r){const t=r&&r[0]||null;return Xy.add(`select::${t?t.value:null}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(_c=r,r=s),r===s&&(r=_c,(t=rf())!==s&&(e=fy())!==s?((n=of())===s&&(n=null),n!==s?(Sc=r,r=t={type:"expr",expr:t,as:n}):(_c=r,r=s)):(_c=r,r=s))))}}return r}function nf(){var r,t,e;return r=_c,(t=Ob())===s&&(t=null),t!==s&&fy()!==s&&(e=yp())!==s?(Sc=r,r=t=e):(_c=r,r=s),r}function of(){var r,t,e;return r=_c,(t=Ob())!==s&&fy()!==s&&(e=yp())!==s?(Sc=r,r=t=e):(_c=r,r=s),r===s&&(r=_c,(t=Ob())===s&&(t=null),t!==s&&fy()!==s&&(e=yp())!==s?(Sc=r,r=t=e):(_c=r,r=s)),r}function sf(){var t,e,n;return t=_c,Ib()!==s&&fy()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=jy())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=jy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=jy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=t,t={keyword:"var",type:"into",expr:e}):(_c=t,t=s),t===s&&(t=_c,Ib()!==s&&fy()!==s?("outfile"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(hn)),e===s&&("dumpfile"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Cn))),e===s&&(e=null),e!==s&&fy()!==s?((n=zp())===s&&(n=bp()),n!==s?(Sc=t,t={keyword:e,type:"into",expr:n}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)),t}function uf(){var r,t;return r=_c,Rb()!==s&&fy()!==s&&(t=pf())!==s?(Sc=r,r=t):(_c=r,r=s),r}function af(){var r,t,e;return r=_c,(t=df())!==s&&fy()!==s&&bb()!==s&&fy()!==s&&(e=df())!==s?(Sc=r,r=t=[t,e]):(_c=r,r=s),r}function cf(){var t,e;return t=_c,Bb()!==s&&fy()!==s?("btree"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(mn)),e===s&&("hash"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(En)),e===s&&("gist"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(An)),e===s&&("gin"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(gn))))),e!==s?(Sc=t,t={keyword:"using",type:e.toLowerCase()}):(_c=t,t=s)):(_c=t,t=s),t}function lf(){var r,t,e,n,o,u;if(r=_c,(t=ff())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ff())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ff())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function ff(){var t,e,n,o,u,a;return t=_c,(e=function(){var t,e,n,o;t=_c,"key_block_size"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(Dt));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="KEY_BLOCK_SIZE"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?((n=qv())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Sc=t,u=n,a=o,t=e={type:e.toLowerCase(),symbol:u,expr:a}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=Tp())!==s&&fy()!==s&&(n=qv())!==s&&fy()!==s?((o=eb())===s&&(o=bp()),o!==s?(Sc=t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=cf())===s&&(t=_c,"with"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Tn)),e!==s&&fy()!==s?("parser"===r.substr(_c,6).toLowerCase()?(n=r.substr(_c,6),_c+=6):(n=s,0===Rc&&Mc(_n)),n!==s&&fy()!==s&&(o=Tp())!==s?(Sc=t,t=e={type:"with parser",expr:o}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"visible"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Sn)),e===s&&("invisible"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(xn))),e!==s&&(Sc=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===s&&(t=vy())))),t}function pf(){var r,t,e,n;if(r=_c,(t=vf())!==s){for(e=[],n=bf();n!==s;)e.push(n),n=bf();e!==s?(Sc=r,r=t=jn(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function bf(){var r,t,e;return r=_c,fy()!==s&&(t=ty())!==s&&fy()!==s&&(e=vf())!==s?(Sc=r,r=e):(_c=r,r=s),r===s&&(r=_c,fy()!==s&&(t=function(){var r,t,e,n,o,u,a,i,c,l,f;if(r=_c,(t=yf())!==s)if(fy()!==s)if((e=vf())!==s)if(fy()!==s)if((n=Bb())!==s)if(fy()!==s)if(ny()!==s)if(fy()!==s)if((o=fp())!==s){for(u=[],a=_c,(i=fy())!==s&&(c=ty())!==s&&(l=fy())!==s&&(f=fp())!==s?a=i=[i,c,l,f]:(_c=a,a=s);a!==s;)u.push(a),a=_c,(i=fy())!==s&&(c=ty())!==s&&(l=fy())!==s&&(f=fp())!==s?a=i=[i,c,l,f]:(_c=a,a=s);u!==s&&(a=fy())!==s&&(i=oy())!==s?(Sc=r,p=t,v=o,y=u,(b=e).join=p,b.using=Fy(v,y),r=t=b):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;var p,b,v,y;r===s&&(r=_c,(t=yf())!==s&&fy()!==s&&(e=vf())!==s&&fy()!==s?((n=Lf())===s&&(n=null),n!==s?(Sc=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=yf())===s&&(t=Bc()),t!==s&&fy()!==s&&(e=ny())!==s&&fy()!==s?((n=Hc())===s&&(n=pf()),n!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=of())===s&&(o=null),o!==s&&(u=fy())!==s?((a=Lf())===s&&(a=null),a!==s?(Sc=r,t=function(r,t,e,n){return Array.isArray(t)&&(t={type:"tables",expr:t}),t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,a),r=t):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)));return r}())!==s?(Sc=r,r=t):(_c=r,r=s)),r}function vf(){var t,e,n,o,u,a,i,c,l,f,p,b;return t=_c,(e=function(){var t;"dual"===r.substr(_c,4).toLowerCase()?(t=r.substr(_c,4),_c+=4):(t=s,0===Rc&&Mc(Pi));return t}())!==s&&(Sc=t,e={type:"dual"}),(t=e)===s&&(t=_c,(e=Pf())!==s&&fy()!==s?((n=nf())===s&&(n=null),n!==s?(Sc=t,t=e={expr:{type:"values",values:e},as:n}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"lateral"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(In)),e===s&&(e=null),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Hc())===s&&(o=Pf()),o!==s&&fy()!==s&&(u=oy())!==s&&(a=fy())!==s?((i=nf())===s&&(i=null),i!==s?(Sc=t,t=e=function(r,t,e){return Array.isArray(t)&&(t={type:"values",values:t}),t.parentheses=!0,{prefix:r,expr:t,as:e}}(e,o,i)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"lateral"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(In)),e===s&&(e=null),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=pf())!==s&&fy()!==s&&(u=oy())!==s&&(a=fy())!==s?((i=nf())===s&&(i=null),i!==s?(Sc=t,t=e=function(r,t,e){return{prefix:r,expr:t={type:"tables",expr:t,parentheses:!0},as:e}}(e,o,i)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"lateral"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(In)),e===s&&(e=null),e!==s&&fy()!==s&&(n=Fp())!==s&&fy()!==s?((o=of())===s&&(o=null),o!==s?(Sc=t,t=e=function(r,t,e){return{prefix:r,type:"expr",expr:t,as:e}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=df())!==s&&fy()!==s?("tablesample"===r.substr(_c,11).toLowerCase()?(n=r.substr(_c,11),_c+=11):(n=s,0===Rc&&Mc(Rn)),n!==s&&fy()!==s&&(o=Fp())!==s&&fy()!==s?(u=_c,"repeatable"===r.substr(_c,10).toLowerCase()?(a=r.substr(_c,10),_c+=10):(a=s,0===Rc&&Mc(Ve)),a!==s&&(i=fy())!==s&&(c=ny())!==s&&(l=fy())!==s&&(f=eb())!==s&&(p=fy())!==s&&(b=oy())!==s?u=a=[a,i,c,l,f,p,b]:(_c=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=of())===s&&(i=null),i!==s?(Sc=t,t=e=function(r,t,e,n){return{...r,as:n,tablesample:{expr:t,repeatable:e&&e[4]}}}(e,o,u,i)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=df())!==s&&fy()!==s?((n=of())===s&&(n=null),n!==s?(Sc=t,t=e=function(r,t){return"var"===r.type?(r.as=t,r):{...r,as:t}}(e,n)):(_c=t,t=s)):(_c=t,t=s))))))),t}function yf(){var t,e,n,o;return t=_c,(e=function(){var t,e,n,o;t=_c,"left"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Bu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(n=fy())!==s?((o=$b())===s&&(o=null),o!==s&&fy()!==s&&Gb()!==s?(Sc=t,t=e="LEFT JOIN"):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=function(){var t,e,n,o;t=_c,"right"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Hu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(n=fy())!==s?((o=$b())===s&&(o=null),o!==s&&fy()!==s&&Gb()!==s?(Sc=t,t=e="RIGHT JOIN"):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=function(){var t,e,n,o;t=_c,"full"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(qu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(n=fy())!==s?((o=$b())===s&&(o=null),o!==s&&fy()!==s&&Gb()!==s?(Sc=t,t=e="FULL JOIN"):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"cross"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Nn)),e!==s&&(n=fy())!==s&&(o=Gb())!==s?(Sc=t,t=e="CROSS JOIN"):(_c=t,t=s),t===s&&(t=_c,e=_c,(n=function(){var t,e,n,o;t=_c,"inner"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Yu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(o=fy())!==s?e=n=[n,o]:(_c=e,e=s),e===s&&(e=null),e!==s&&(n=Gb())!==s?(Sc=t,t=e="INNER JOIN"):(_c=t,t=s))))),t}function df(){var r,t,e,n,o,u,a,i,c;return r=_c,(t=bp())!==s?(e=_c,(n=fy())!==s&&(o=ry())!==s&&(u=fy())!==s?((a=bp())===s&&(a=ey()),a!==s?e=n=[n,o,u,a]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=null),e!==s?(n=_c,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s?((i=bp())===s&&(i=ey()),i!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=r,r=t=function(r,t,e){const n={db:null,table:r};return null!==e?(n.db=r,n.schema=t[3],n.table=e[3],n):(null!==t&&(n.db=r,n.table=t[3]),n)}(t,e,n)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=jy())!==s&&(Sc=r,(c=t).db=null,c.table=c.name,t=c),r=t),r}function wf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Wf())!==s){for(e=[],n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=Gy(t[r][1],n,t[r][3]);return n}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Lf(){var r,t;return r=_c,Pb()!==s&&fy()!==s&&(t=Vf())!==s?(Sc=r,r=t):(_c=r,r=s),r}function hf(){var t,e;return t=_c,function(){var t,e,n,o;t=_c,"where"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Ju));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s&&(e=Vf())!==s?(Sc=t,t=e):(_c=t,t=s),t}function Cf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=ip())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ip())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=ip())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function mf(){var r,t,e;return r=_c,(t=Tp())!==s&&fy()!==s&&Ob()!==s&&fy()!==s&&(e=Ef())!==s?(Sc=r,r=t={name:t,as_window_specification:e}):(_c=r,r=s),r}function Ef(){var r,t;return(r=Tp())===s&&(r=_c,ny()!==s&&fy()!==s?((t=function(){var r,t,e,n;r=_c,(t=Sf())===s&&(t=null);t!==s&&fy()!==s?((e=xf())===s&&(e=null),e!==s&&fy()!==s?((n=function(){var r,t,e,n,o;r=_c,(t=Iv())!==s&&fy()!==s?((e=Af())===s&&(e=gf()),e!==s?(Sc=r,r=t={type:"rows",expr:e}):(_c=r,r=s)):(_c=r,r=s);r===s&&(r=_c,(t=Iv())!==s&&fy()!==s&&(e=zb())!==s&&fy()!==s&&(n=gf())!==s&&fy()!==s&&ov()!==s&&fy()!==s&&(o=Af())!==s?(Sc=r,t=Gy(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),r=t):(_c=r,r=s));return r}())===s&&(n=null),n!==s?(Sc=r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s);return r}())===s&&(t=null),t!==s&&fy()!==s&&oy()!==s?(Sc=r,r={window_specification:t||{},parentheses:!0}):(_c=r,r=s)):(_c=r,r=s)),r}function Af(){var t,e,n,o;return t=_c,(e=_f())!==s&&fy()!==s?("following"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(On)),n!==s?(Sc=t,(o=e).value+=" FOLLOWING",t=e=o):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=Tf()),t}function gf(){var t,e,n,o,u;return t=_c,(e=_f())!==s&&fy()!==s?("preceding"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(kn)),n===s&&("following"===r.substr(_c,9).toLowerCase()?(n=r.substr(_c,9),_c+=9):(n=s,0===Rc&&Mc(On))),n!==s?(Sc=t,u=n,(o=e).value+=" "+u.toUpperCase(),t=e=o):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=Tf()),t}function Tf(){var t,e,n;return t=_c,"current"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(cr)),e!==s&&fy()!==s?("row"===r.substr(_c,3).toLowerCase()?(n=r.substr(_c,3),_c+=3):(n=s,0===Rc&&Mc(It)),n!==s?(Sc=t,t=e={type:"origin",value:"current row"}):(_c=t,t=s)):(_c=t,t=s),t}function _f(){var t,e;return t=_c,"unbounded"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Un)),e!==s&&(Sc=t,e={type:"origin",value:e.toUpperCase()}),(t=e)===s&&(t=eb()),t}function Sf(){var r,t,e;return r=_c,jb()!==s&&fy()!==s&&Yb()!==s&&fy()!==s?((t=Cf())===s&&(t=Fp()),t!==s?(Sc=r,e=t,r=Array.isArray(e)?e.map(r=>({type:"expr",expr:r})):[{type:"expr",expr:e}]):(_c=r,r=s)):(_c=r,r=s),r}function xf(){var r,t;return r=_c,Wb()!==s&&fy()!==s&&Yb()!==s&&fy()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=jf())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=jf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=jf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=r,r=t):(_c=r,r=s),r}function jf(){var t,e,n,o,u,a,i;return t=_c,(e=Wf())!==s&&fy()!==s?((n=Xb())===s&&(n=Vb()),n===s&&(n=null),n!==s&&fy()!==s?(o=_c,"nulls"===r.substr(_c,5).toLowerCase()?(u=r.substr(_c,5),_c+=5):(u=s,0===Rc&&Mc(Tr)),u!==s&&(a=fy())!==s?("first"===r.substr(_c,5).toLowerCase()?(i=r.substr(_c,5),_c+=5):(i=s,0===Rc&&Mc(_r)),i===s&&("last"===r.substr(_c,4).toLowerCase()?(i=r.substr(_c,4),_c+=4):(i=s,0===Rc&&Mc(Sr))),i===s&&(i=null),i!==s?o=u=[u,a,i]:(_c=o,o=s)):(_c=o,o=s),o===s&&(o=null),o!==s?(Sc=t,t=e=function(r,t,e){const n={expr:r,type:t};return n.nulls=e&&[e[0],e[2]].filter(r=>r).join(" "),n}(e,n,o)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t}function If(){var r;return(r=eb())===s&&(r=jy())===s&&(r=jp()),r}function Rf(){var t,e,n,o,u,a,i;return t=_c,e=_c,(n=function(){var t,e,n,o;t=_c,"limit"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(oa));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(o=fy())!==s?((u=If())===s&&(u=Qb())===s&&(u=Bl()),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=null),e!==s&&(n=fy())!==s?(o=_c,(u=function(){var t,e,n,o;t=_c,"offset"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(sa));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="OFFSET"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(a=fy())!==s&&(i=If())!==s?o=u=[u,a,i]:(_c=o,o=s),o===s&&(o=null),o!==s?(Sc=t,t=e=function(r,t){const e=[];return r&&e.push("string"==typeof r[2]?{type:"origin",value:"all"}:r[2]),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,o)):(_c=t,t=s)):(_c=t,t=s),t}function Nf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Of())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Of())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Of())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Of(){var t,e,n,o,u;return t=_c,(e=op())!==s&&fy()!==s?(61===r.charCodeAt(_c)?(n="=",_c++):(n=s,0===Rc&&Mc(ir)),n!==s&&fy()!==s&&(o=rp())!==s?(Sc=t,t=e=function(r,t){return{...r,value:t}}(e,o)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=op())!==s&&fy()!==s?(61===r.charCodeAt(_c)?(n="=",_c++):(n=s,0===Rc&&Mc(ir)),n!==s&&fy()!==s&&(o=Fb())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(u=ip())!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={...c,value:u,keyword:"values"}):(_c=t,t=s)):(_c=t,t=s)),t}function kf(){var t,e,n;return t=_c,(e=function(){var t,e,n,o;t=_c,"returning"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Ou));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="RETURNING"):(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&fy()!==s?((n=zl())===s&&(n=Hl()),n!==s?(Sc=t,t=e=function(r,t){return{type:r&&r.toLowerCase()||"returning",columns:"*"===t&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||t}}(e,n)):(_c=t,t=s)):(_c=t,t=s),t}function Uf(){var r;return(r=Pf())===s&&(r=Xl()),r}function Mf(){var r,t,e,n,o,u,a,i,c;if(r=_c,jb()!==s)if(fy()!==s)if((t=ny())!==s)if(fy()!==s)if((e=Tp())!==s){for(n=[],o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Tp())!==s?o=u=[u,a,i,c]:(_c=o,o=s);o!==s;)n.push(o),o=_c,(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s&&(c=Tp())!==s?o=u=[u,a,i,c]:(_c=o,o=s);n!==s&&(o=fy())!==s&&(u=oy())!==s?(Sc=r,r=Fy(e,n)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;return r===s&&(r=_c,jb()!==s&&fy()!==s&&(t=Gf())!==s?(Sc=r,r=t):(_c=r,r=s)),r}function Df(){var r,t;return r=_c,(t=gb())!==s&&(Sc=r,t="insert"),(r=t)===s&&(r=_c,(t=_b())!==s&&(Sc=r,t="replace"),r=t),r}function Pf(){var r,t;return r=_c,Fb()!==s&&fy()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Gf())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Gf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Gf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,t=Fy(t,e),r=t):(_c=r,r=s)}else _c=r,r=s;return r}())!==s?(Sc=r,r=t):(_c=r,r=s),r}function Gf(){var r,t;return r=_c,ny()!==s&&fy()!==s&&(t=$f())!==s&&fy()!==s&&oy()!==s?(Sc=r,r=t):(_c=r,r=s),r}function $f(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Wf())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Fy(r,t),e}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Ff(){var r,t,e;return r=_c,kv()!==s&&fy()!==s&&(t=Wf())!==s&&fy()!==s&&(e=dy())!==s?(Sc=r,r={type:"interval",expr:t,unit:e.toLowerCase()}):(_c=r,r=s),r===s&&(r=_c,kv()!==s&&fy()!==s&&(t=zp())!==s?(Sc=r,r=function(r){return{type:"interval",expr:r,unit:""}}(t)):(_c=r,r=s)),r}function Bf(){var r,t,e,n,o,u;if(r=_c,(t=Hf())!==s)if(fy()!==s){for(e=[],n=_c,(o=fy())!==s&&(u=Hf())!==s?n=o=[o,u]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=Hf())!==s?n=o=[o,u]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e,1)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;return r}function Hf(){var t,e,n;return t=_c,cv()!==s&&fy()!==s&&(e=Vf())!==s&&fy()!==s&&function(){var t,e,n,o;t=_c,"then"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Se));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}()!==s&&fy()!==s&&(n=Wf())!==s?(Sc=t,t={type:"when",cond:e,result:n}):(_c=t,t=s),t}function qf(){var r,t;return r=_c,lv()!==s&&fy()!==s&&(t=Wf())!==s?(Sc=r,r={type:"else",result:t}):(_c=r,r=s),r}function Yf(){var r;return(r=Xf())===s&&(r=function(){var r,t,e,n,o,u;if(r=_c,(t=tp())!==s){if(e=[],n=_c,(o=fy())!==s&&(u=sp())!==s?n=o=[o,u]:(_c=n,n=s),n!==s)for(;n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=sp())!==s?n=o=[o,u]:(_c=n,n=s);else e=s;e!==s?(Sc=r,t=Py(t,e[0][1]),r=t):(_c=r,r=s)}else _c=r,r=s;return r}()),r}function Wf(){var r;return(r=Yf())===s&&(r=Hc()),r}function Vf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Wf())!==s){for(e=[],n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ty()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ty()),u!==s&&(a=fy())!==s&&(i=Wf())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=Gy(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Xf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Qf())!==s){for(e=[],n=_c,(o=py())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Qf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=py())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Qf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Gn(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Qf(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Kf())!==s){for(e=[],n=_c,(o=py())!==s&&(u=ov())!==s&&(a=fy())!==s&&(i=Kf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=py())!==s&&(u=ov())!==s&&(a=fy())!==s&&(i=Kf())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Gn(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Kf(){var t,e,n,o,u;return(t=zf())===s&&(t=function(){var r,t,e;r=_c,(t=function(){var r,t,e,n,o;r=_c,t=_c,(e=nv())!==s&&(n=fy())!==s&&(o=ev())!==s?t=e=[e,n,o]:(_c=t,t=s);t!==s&&(Sc=r,t=(u=t)[0]+" "+u[2]);var u;(r=t)===s&&(r=ev());return r}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Hc())!==s&&fy()!==s&&oy()!==s?(Sc=r,n=t,(o=e).parentheses=!0,t=Py(n,o),r=t):(_c=r,r=s);var n,o;return r}())===s&&(t=_c,(e=nv())===s&&(e=_c,33===r.charCodeAt(_c)?(n="!",_c++):(n=s,0===Rc&&Mc($n)),n!==s?(o=_c,Rc++,61===r.charCodeAt(_c)?(u="=",_c++):(u=s,0===Rc&&Mc(ir)),Rc--,u===s?o=void 0:(_c=o,o=s),o!==s?e=n=[n,o]:(_c=e,e=s)):(_c=e,e=s)),e!==s&&(n=fy())!==s&&(o=Kf())!==s?(Sc=t,t=e=Py("NOT",o)):(_c=t,t=s)),t}function zf(){var t,e,n,o,u;return t=_c,(e=rp())!==s&&fy()!==s?((n=function(){var t;(t=function(){var r,t,e,n,o,u,a;r=_c,t=[],e=_c,(n=fy())!==s&&(o=Zf())!==s&&(u=fy())!==s&&(a=rp())!==s?e=n=[n,o,u,a]:(_c=e,e=s);if(e!==s)for(;e!==s;)t.push(e),e=_c,(n=fy())!==s&&(o=Zf())!==s&&(u=fy())!==s&&(a=rp())!==s?e=n=[n,o,u,a]:(_c=e,e=s);else t=s;t!==s&&(Sc=r,t={type:"arithmetic",tail:t});return r=t}())===s&&(t=function(){var r,t,e,n;r=_c,(t=Jf())!==s&&fy()!==s&&(e=ny())!==s&&fy()!==s&&(n=$f())!==s&&fy()!==s&&oy()!==s?(Sc=r,r=t={op:t,right:n}):(_c=r,r=s);r===s&&(r=_c,(t=Jf())!==s&&fy()!==s?((e=jy())===s&&(e=zp())===s&&(e=Fp()),e!==s?(Sc=r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(_c=r,r=s)):(_c=r,r=s));return r}())===s&&(t=function(){var r,t,e,n;r=_c,(t=function(){var r,t,e,n,o;r=_c,t=_c,(e=nv())!==s&&(n=fy())!==s&&(o=zb())!==s?t=e=[e,n,o]:(_c=t,t=s);t!==s&&(Sc=r,t=(u=t)[0]+" "+u[2]);var u;(r=t)===s&&(r=zb());return r}())!==s&&fy()!==s&&(e=rp())!==s&&fy()!==s&&ov()!==s&&fy()!==s&&(n=rp())!==s?(Sc=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(_c=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o,u,a,i,c;r=_c,(t=Jb())!==s&&(e=fy())!==s&&(n=rp())!==s?(Sc=r,r=t={op:"IS",right:n}):(_c=r,r=s);r===s&&(r=_c,(t=Jb())!==s&&(e=fy())!==s?(n=_c,(o=Kb())!==s&&(u=fy())!==s&&(a=Rb())!==s&&(i=fy())!==s&&(c=df())!==s?n=o=[o,u,a,i,c]:(_c=n,n=s),n!==s?(Sc=r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,t=_c,(e=Jb())!==s&&(n=fy())!==s&&(o=nv())!==s?t=e=[e,n,o]:(_c=t,t=s),t!==s&&(e=fy())!==s&&(n=rp())!==s?(Sc=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(_c=r,r=s)));return r}())===s&&(t=function(){var t,e,n,o;t=_c,(e=function(){var t,e,n,o,u;t=_c,e=_c,(n=nv())!==s&&(o=fy())!==s?((u=rv())===s&&(u=tv()),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s);e!==s&&(Sc=t,e=(a=e)[0]+" "+a[2]);var a;(t=e)===s&&(t=rv())===s&&(t=tv())===s&&(t=_c,"similar"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Vn)),e!==s&&(n=fy())!==s&&(o=bb())!==s?(Sc=t,t=e="SIMILAR TO"):(_c=t,t=s),t===s&&(t=_c,(e=nv())!==s&&(n=fy())!==s?("similar"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(Vn)),o!==s&&(u=fy())!==s&&bb()!==s?(Sc=t,t=e="NOT SIMILAR TO"):(_c=t,t=s)):(_c=t,t=s)));return t}())!==s&&fy()!==s?((n=Wp())===s&&(n=zf()),n!==s&&fy()!==s?((o=function(){var t,e,n;t=_c,"escape"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Zn));e!==s&&fy()!==s&&(n=zp())!==s?(Sc=t,e=function(r,t){return{type:"ESCAPE",value:t}}(0,n),t=e):(_c=t,t=s);return t}())===s&&(o=null),o!==s?(Sc=t,u=e,a=n,(i=o)&&(a.escape=i),t=e={op:u,right:a}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s);var u,a,i;return t}())===s&&(t=function(){var t,e,n;t=_c,(e=function(){var t;"!~*"===r.substr(_c,3)?(t="!~*",_c+=3):(t=s,0===Rc&&Mc(Xn));t===s&&("~*"===r.substr(_c,2)?(t="~*",_c+=2):(t=s,0===Rc&&Mc(Qn)),t===s&&(126===r.charCodeAt(_c)?(t="~",_c++):(t=s,0===Rc&&Mc(Kn)),t===s&&("!~"===r.substr(_c,2)?(t="!~",_c+=2):(t=s,0===Rc&&Mc(zn)))));return t}())!==s&&fy()!==s?((n=Wp())===s&&(n=zf()),n!==s?(Sc=t,t=e={op:e,right:n}):(_c=t,t=s)):(_c=t,t=s);return t}());return t}())===s&&(n=null),n!==s?(Sc=t,o=e,t=e=null===(u=n)?o:"arithmetic"===u.type?By(o,u.tail):Gy(u.op,o,u.right)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=zp())===s&&(t=ip()),t}function Zf(){var t;return">="===r.substr(_c,2)?(t=">=",_c+=2):(t=s,0===Rc&&Mc(Fn)),t===s&&(62===r.charCodeAt(_c)?(t=">",_c++):(t=s,0===Rc&&Mc(Bn)),t===s&&("<="===r.substr(_c,2)?(t="<=",_c+=2):(t=s,0===Rc&&Mc(Hn)),t===s&&("<>"===r.substr(_c,2)?(t="<>",_c+=2):(t=s,0===Rc&&Mc(qn)),t===s&&(60===r.charCodeAt(_c)?(t="<",_c++):(t=s,0===Rc&&Mc(Yn)),t===s&&(61===r.charCodeAt(_c)?(t="=",_c++):(t=s,0===Rc&&Mc(ir)),t===s&&("!="===r.substr(_c,2)?(t="!=",_c+=2):(t=s,0===Rc&&Mc(Wn)))))))),t}function Jf(){var r,t,e,n,o,u;return r=_c,t=_c,(e=nv())!==s&&(n=fy())!==s&&(o=Zb())!==s?t=e=[e,n,o]:(_c=t,t=s),t!==s&&(Sc=r,t=(u=t)[0]+" "+u[2]),(r=t)===s&&(r=Zb()),r}function rp(){var r,t,e,n,o,u,a,i;if(r=_c,(t=ep())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=tp())!==s&&(a=fy())!==s&&(i=ep())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=tp())!==s&&(a=fy())!==s&&(i=ep())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Dy()}));return By(r,t)}(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function tp(){var t;return 43===r.charCodeAt(_c)?(t="+",_c++):(t=s,0===Rc&&Mc(Jn)),t===s&&(45===r.charCodeAt(_c)?(t="-",_c++):(t=s,0===Rc&&Mc(ro))),t}function ep(){var r,t,e,n,o,u,a,i;if(r=_c,(t=up())!==s){for(e=[],n=_c,(o=fy())!==s?((u=np())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=up())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s?((u=np())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=up())!==s?n=o=[o,u,a,i]:(_c=n,n=s)):(_c=n,n=s);e!==s?(Sc=r,r=t=By(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function np(){var t;return 42===r.charCodeAt(_c)?(t="*",_c++):(t=s,0===Rc&&Mc(to)),t===s&&(47===r.charCodeAt(_c)?(t="/",_c++):(t=s,0===Rc&&Mc(eo)),t===s&&(37===r.charCodeAt(_c)?(t="%",_c++):(t=s,0===Rc&&Mc(no)),t===s&&("||"===r.substr(_c,2)?(t="||",_c+=2):(t=s,0===Rc&&Mc(oo))))),t}function op(){var r,t,e,n,o;if(r=_c,(t=ip())!==s)if(fy()!==s)if((e=Jl())===s&&(e=null),e!==s)if(fy()!==s){for(n=[],o=tf();o!==s;)n.push(o),o=tf();n!==s?(Sc=r,r=t=function(r,t,e){return t&&(r.array_index=t),e.length&&(r.options={type:"expr_list",value:e.flat(),separator:" "}),r}(t,e,n)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;return r}function sp(){var t,e,n,o,u,a;return(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&(u=Ob())!==s&&fy()!==s&&(a=Ry())!==s&&fy()!==s&&(i=oy())!==s?(Sc=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[e]}}(e,o,a),t=e):(_c=t,t=s);t===s&&(t=_c,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&(u=Ob())!==s&&fy()!==s&&(a=vv())!==s&&fy()!==s&&(i=ny())!==s&&fy()!==s&&(c=sb())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(l=oy())!==s?(Sc=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),t=e):(_c=t,t=s),t===s&&(t=_c,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&(u=Ob())!==s&&fy()!==s&&(a=vv())!==s&&fy()!==s&&(i=ny())!==s&&fy()!==s&&(c=sb())!==s&&fy()!==s&&ty()!==s&&fy()!==s&&(l=sb())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&oy()!==s?(Sc=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),t=e):(_c=t,t=s),t===s&&(t=_c,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&(u=Ob())!==s&&fy()!==s&&(a=function(){var t;(t=function(){var t,e,n,o;t=_c,"signed"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Da));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SIGNED"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=yv());return t}())!==s&&fy()!==s?((i=wv())===s&&(i=null),i!==s&&fy()!==s&&(c=oy())!==s?(Sc=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,i),t=e):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=ny())!==s&&fy()!==s?((n=Xf())===s&&(n=op())===s&&(n=jp()),n!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?((u=Yp())===s&&(u=null),u!==s?(Sc=t,e=function(r,t){return r.parentheses=!0,t?{...t,type:"cast",keyword:"cast",expr:r}:r}(n,u),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=cp())===s&&(e=Wp())===s&&(e=function(){var t,e,n;t=_c,(e=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=function(){var t,e,n,o;t=_c,"count"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ha));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="COUNT"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=_c,"group_concat"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Ca));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="GROUP_CONCAT"):(_c=t,t=s)):(_c=t,t=s);return t}());e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=function(){var t,e;t=_c,(e=function(){var t,e;t=_c,42===r.charCodeAt(_c)?(e="*",_c++):(e=s,0===Rc&&Mc(to));e!==s&&(Sc=t,e={type:"star",value:"*"});return t=e}())!==s&&(Sc=t,e={expr:e});(t=e)===s&&(t=kp());return t}())!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?((u=Rp())===s&&(u=null),u!==s?(Sc=t,t=e={type:"aggr_func",name:e,args:n,over:u}):(_c=t,t=s)):(_c=t,t=s);t===s&&(t=_c,"percentile_cont"===r.substr(_c,15).toLowerCase()?(e=r.substr(_c,15),_c+=15):(e=s,0===Rc&&Mc(Yo)),e===s&&("percentile_disc"===r.substr(_c,15).toLowerCase()?(e=r.substr(_c,15),_c+=15):(e=s,0===Rc&&Mc(Wo))),e!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=eb())===s&&(n=Vp()),n!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?("within"===r.substr(_c,6).toLowerCase()?(u=r.substr(_c,6),_c+=6):(u=s,0===Rc&&Mc(Vo)),u!==s&&fy()!==s&&qb()!==s&&fy()!==s&&(a=ny())!==s&&fy()!==s&&(i=xf())!==s&&fy()!==s&&(c=oy())!==s&&fy()!==s?((l=Rp())===s&&(l=null),l!==s?(Sc=t,e=function(r,t,e,n){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:t},within_group_orderby:e,over:n}}(e,n,i,l),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"mode"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Xo)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=oy())!==s&&fy()!==s?("within"===r.substr(_c,6).toLowerCase()?(o=r.substr(_c,6),_c+=6):(o=s,0===Rc&&Mc(Vo)),o!==s&&fy()!==s&&(u=qb())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(a=xf())!==s&&fy()!==s&&(i=oy())!==s&&fy()!==s?((c=Rp())===s&&(c=null),c!==s?(Sc=t,e=function(r,t,e){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:{}},within_group_orderby:t,over:e}}(e,a,c),t=e):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)));return t}())===s&&(e=function(){var t,e,n,o;t=_c,(e=function(){var t;(t=function(){var t,e,n,o;t=_c,"sum"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Aa));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SUM"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=_c,"max"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(ma));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MAX"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=_c,"min"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ea));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MIN"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=_c,"avg"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(ga));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="AVG"):(_c=t,t=s)):(_c=t,t=s);return t}());return t}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=rp())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=Rp())===s&&(o=null),o!==s?(Sc=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...Dy()},t=e):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(e=function(){var t,e,n,o,u,a;t=_c,e=_c,(n=bp())!==s&&(o=fy())!==s&&(u=ry())!==s?e=n=[n,o,u]:(_c=e,e=s);e===s&&(e=null);e!==s&&(n=fy())!==s?((o=function(){var t,e,n,o;t=_c,"array_agg"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(wa));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ARRAY_AGG"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(o=function(){var t,e,n,o;t=_c,"string_agg"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(La));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="STRING_AGG"):(_c=t,t=s)):(_c=t,t=s);return t}()),o!==s&&(u=fy())!==s&&ny()!==s&&fy()!==s&&(a=kp())!==s&&fy()!==s&&oy()!==s?(Sc=t,c=o,l=a,e={type:"aggr_func",name:(i=e)?`${i[0]}.${c}`:c,args:l},t=e):(_c=t,t=s)):(_c=t,t=s);var i,c,l;return t}());e!==s&&fy()!==s?((n=function(){var t,e,n;t=_c,"filter"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ko));e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=hf())!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={keyword:"filter",parentheses:!0,where:n}):(_c=t,t=s);return t}())===s&&(n=null),n!==s?(Sc=t,o=e,(u=n)&&(o.filter=u),t=e=o):(_c=t,t=s)):(_c=t,t=s);var o,u;return t}())===s&&(e=function(){var t;(t=function(){var t,e,n;t=_c,(e=function(){var t;"row_number"===r.substr(_c,10).toLowerCase()?(t=r.substr(_c,10),_c+=10):(t=s,0===Rc&&Mc(Do));t===s&&("dense_rank"===r.substr(_c,10).toLowerCase()?(t=r.substr(_c,10),_c+=10):(t=s,0===Rc&&Mc(Po)),t===s&&("rank"===r.substr(_c,4).toLowerCase()?(t=r.substr(_c,4),_c+=4):(t=s,0===Rc&&Mc(Go))));return t}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(n=Rp())!==s?(Sc=t,t=e={type:"window_func",name:e,over:n}):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u;t=_c,(e=function(){var t;"lag"===r.substr(_c,3).toLowerCase()?(t=r.substr(_c,3),_c+=3):(t=s,0===Rc&&Mc($o));t===s&&("lead"===r.substr(_c,4).toLowerCase()?(t=r.substr(_c,4),_c+=4):(t=s,0===Rc&&Mc(Fo)),t===s&&("nth_value"===r.substr(_c,9).toLowerCase()?(t=r.substr(_c,9),_c+=9):(t=s,0===Rc&&Mc(Bo))));return t}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=$f())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=Np())===s&&(o=null),o!==s&&fy()!==s&&(u=Rp())!==s?(Sc=t,t=e={type:"window_func",name:e,args:n,over:u,consider_nulls:o}):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u;t=_c,(e=function(){var t;"first_value"===r.substr(_c,11).toLowerCase()?(t=r.substr(_c,11),_c+=11):(t=s,0===Rc&&Mc(Uo));t===s&&("last_value"===r.substr(_c,10).toLowerCase()?(t=r.substr(_c,10),_c+=10):(t=s,0===Rc&&Mc(Mo)));return t}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Wf())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=Np())===s&&(o=null),o!==s&&fy()!==s&&(u=Rp())!==s?(Sc=t,t=e={type:"window_func",name:e,args:{type:"expr_list",value:[n]},over:u,consider_nulls:o}):(_c=t,t=s)):(_c=t,t=s);return t}());return t}())===s&&(e=Fp())===s&&(e=function(){var r,t,e,n,o,u,a,i;return r=_c,iv()!==s&&fy()!==s&&(t=Bf())!==s&&fy()!==s?((e=qf())===s&&(e=null),e!==s&&fy()!==s&&(n=fv())!==s&&fy()!==s?((o=iv())===s&&(o=null),o!==s?(Sc=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,iv()!==s&&fy()!==s&&(t=Wf())!==s&&fy()!==s&&(e=Bf())!==s&&fy()!==s?((n=qf())===s&&(n=null),n!==s&&fy()!==s&&(o=fv())!==s&&fy()!==s?((u=iv())===s&&(u=null),u!==s?(Sc=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)),r}())===s&&(e=Ff())===s&&(e=op())===s&&(e=jp()),e!==s&&fy()!==s?((n=Yp())===s&&(n=null),n!==s?(Sc=t,e=function(r,t){return t?{...t,type:"cast",keyword:"cast",expr:r}:r}(e,n),t=e):(_c=t,t=s)):(_c=t,t=s))))));return t}())===s&&(t=_c,ny()!==s&&(e=fy())!==s&&(n=Vf())!==s&&(o=fy())!==s&&(u=oy())!==s?(Sc=t,(a=n).parentheses=!0,t=a):(_c=t,t=s),t===s&&(t=jy())===s&&(t=_c,fy()!==s?(36===r.charCodeAt(_c)?(e="$",_c++):(e=s,0===Rc&&Mc(so)),e!==s?(60===r.charCodeAt(_c)?(n="<",_c++):(n=s,0===Rc&&Mc(Yn)),n!==s&&(o=eb())!==s?(62===r.charCodeAt(_c)?(u=">",_c++):(u=s,0===Rc&&Mc(Bn)),u!==s?(Sc=t,t={type:"origin",value:`$<${o.value}>`}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s))),t}function up(){var t,e,n,o,u;return(t=function(){var t,e,n,o,u,a,i,c;if(t=_c,(e=sp())!==s)if(fy()!==s){for(n=[],o=_c,(u=fy())!==s?("?|"===r.substr(_c,2)?(a="?|",_c+=2):(a=s,0===Rc&&Mc(uo)),a===s&&("?&"===r.substr(_c,2)?(a="?&",_c+=2):(a=s,0===Rc&&Mc(ao)),a===s&&(63===r.charCodeAt(_c)?(a="?",_c++):(a=s,0===Rc&&Mc(io)),a===s&&("#-"===r.substr(_c,2)?(a="#-",_c+=2):(a=s,0===Rc&&Mc(co)),a===s&&("#>>"===r.substr(_c,3)?(a="#>>",_c+=3):(a=s,0===Rc&&Mc(lo)),a===s&&("#>"===r.substr(_c,2)?(a="#>",_c+=2):(a=s,0===Rc&&Mc(fo)),a===s&&(a=cy())===s&&(a=iy())===s&&("@>"===r.substr(_c,2)?(a="@>",_c+=2):(a=s,0===Rc&&Mc(po)),a===s&&("<@"===r.substr(_c,2)?(a="<@",_c+=2):(a=s,0===Rc&&Mc(bo))))))))),a!==s&&(i=fy())!==s&&(c=sp())!==s?o=u=[u,a,i,c]:(_c=o,o=s)):(_c=o,o=s);o!==s;)n.push(o),o=_c,(u=fy())!==s?("?|"===r.substr(_c,2)?(a="?|",_c+=2):(a=s,0===Rc&&Mc(uo)),a===s&&("?&"===r.substr(_c,2)?(a="?&",_c+=2):(a=s,0===Rc&&Mc(ao)),a===s&&(63===r.charCodeAt(_c)?(a="?",_c++):(a=s,0===Rc&&Mc(io)),a===s&&("#-"===r.substr(_c,2)?(a="#-",_c+=2):(a=s,0===Rc&&Mc(co)),a===s&&("#>>"===r.substr(_c,3)?(a="#>>",_c+=3):(a=s,0===Rc&&Mc(lo)),a===s&&("#>"===r.substr(_c,2)?(a="#>",_c+=2):(a=s,0===Rc&&Mc(fo)),a===s&&(a=cy())===s&&(a=iy())===s&&("@>"===r.substr(_c,2)?(a="@>",_c+=2):(a=s,0===Rc&&Mc(po)),a===s&&("<@"===r.substr(_c,2)?(a="<@",_c+=2):(a=s,0===Rc&&Mc(bo))))))))),a!==s&&(i=fy())!==s&&(c=sp())!==s?o=u=[u,a,i,c]:(_c=o,o=s)):(_c=o,o=s);n!==s?(Sc=t,l=e,e=(f=n)&&0!==f.length?By(l,f):l,t=e):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;var l,f;return t}())===s&&(t=_c,(e=function(){var t;33===r.charCodeAt(_c)?(t="!",_c++):(t=s,0===Rc&&Mc($n));t===s&&(45===r.charCodeAt(_c)?(t="-",_c++):(t=s,0===Rc&&Mc(ro)),t===s&&(43===r.charCodeAt(_c)?(t="+",_c++):(t=s,0===Rc&&Mc(Jn)),t===s&&(126===r.charCodeAt(_c)?(t="~",_c++):(t=s,0===Rc&&Mc(Kn)))));return t}())!==s?(n=_c,(o=fy())!==s&&(u=up())!==s?n=o=[o,u]:(_c=n,n=s),n!==s?(Sc=t,t=e=Py(e,n[1])):(_c=t,t=s)):(_c=t,t=s)),t}function ap(){var t,e,n,o,u,a;if(t=_c,"e"===r.substr(_c,1).toLowerCase()?(e=r.charAt(_c),_c++):(e=s,0===Rc&&Mc(vo)),e!==s)if(39===r.charCodeAt(_c)?(n="'",_c++):(n=s,0===Rc&&Mc(Ht)),n!==s)if(fy()!==s){for(o=[],u=rb();u!==s;)o.push(u),u=rb();o!==s&&(u=fy())!==s?(39===r.charCodeAt(_c)?(a="'",_c++):(a=s,0===Rc&&Mc(Ht)),a!==s?(Sc=t,t=e={type:"origin",value:`E'${o.join("")}'`}):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;return t}function ip(){var r,t,e,n,o,u,a,i,c,l,f,p,b;return(r=ap())===s&&(r=_c,t=_c,(e=bp())!==s&&(n=fy())!==s&&(o=ry())!==s?t=e=[e,n,o]:(_c=t,t=s),t===s&&(t=null),t!==s&&(e=fy())!==s&&(n=ey())!==s?(Sc=r,r=t=function(r){const t=r&&r[0]||null;return Xy.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*"}}(t)):(_c=r,r=s),r===s&&(r=_c,(t=bp())!==s?(e=_c,(n=fy())!==s&&(o=ry())!==s&&(u=fy())!==s&&(a=bp())!==s?e=n=[n,o,u,a]:(_c=e,e=s),e!==s?(n=_c,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=mp())!==s?n=o=[o,u,a,i]:(_c=n,n=s),n!==s?(o=_c,(u=fy())!==s&&(a=ul())!==s?o=u=[u,a]:(_c=o,o=s),o===s&&(o=null),o!==s?(Sc=r,l=t,f=e,p=n,b=o,Xy.add(`select::${l}.${f[3]}::${p[3].value}`),r=t={type:"column_ref",schema:l,table:f[3],column:{expr:p[3]},collate:b&&b[1]}):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=bp())!==s&&(e=fy())!==s&&(n=ry())!==s&&(o=fy())!==s&&(u=mp())!==s?(a=_c,(i=fy())!==s&&(c=ul())!==s?a=i=[i,c]:(_c=a,a=s),a===s&&(a=null),a!==s?(Sc=r,r=t=function(r,t,e){return Xy.add(`select::${r}::${t.value}`),{type:"column_ref",table:r,column:{expr:t},collate:e&&e[1]}}(t,u,a)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=Ep())!==s?(e=_c,Rc++,n=ny(),Rc--,n===s?e=void 0:(_c=e,e=s),e!==s?(n=_c,(o=fy())!==s&&(u=ul())!==s?n=o=[o,u]:(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=r,r=t=function(r,t){return Xy.add("select::null::"+r.value),{type:"column_ref",table:null,column:{expr:r},collate:t&&t[1]}}(t,n)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s))))),r}function cp(){var r,t,e;return r=_c,(t=Zp())!==s&&(Sc=r,e=t,Xy.add("select::null::"+e.value),t={type:"column_ref",table:null,column:{expr:e}}),r=t}function lp(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Ep())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ep())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ep())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function fp(){var r,t;return r=_c,(t=Tp())!==s&&(Sc=r,t=yo(t)),(r=t)===s&&(r=dp()),r}function pp(){var r,t;return r=_c,(t=Tp())!==s?(Sc=_c,(wo(t)?s:void 0)!==s?(Sc=r,r=t={type:"default",value:t}):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=dp()),r}function bp(){var r,t;return r=_c,(t=Tp())!==s?(Sc=_c,(wo(t)?s:void 0)!==s?(Sc=r,r=t=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=wp()),r}function vp(){var r,t,e,n,o,u,a,i;if(r=_c,(t=bp())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=bp())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=bp())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function yp(){var r,t,e,n,o,u,a,i,c;return r=_c,(t=gp())!==s?(Sc=_c,(!0===My[t.toUpperCase()]?s:void 0)!==s?(e=_c,(n=fy())!==s&&(o=ny())!==s&&(u=fy())!==s&&(a=lp())!==s&&(i=fy())!==s&&(c=oy())!==s?e=n=[n,o,u,a,i,c]:(_c=e,e=s),e===s&&(e=null),e!==s?(Sc=r,r=t=function(r,t){return t?`${r}(${t[3].map(r=>r.value).join(", ")})`:r}(t,e)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=Lp())!==s&&(Sc=r,t=function(r){return r.value}(t)),r=t),r}function dp(){var r;return(r=Lp())===s&&(r=hp())===s&&(r=Cp()),r}function wp(){var r,t;return r=_c,(t=Lp())===s&&(t=hp())===s&&(t=Cp()),t!==s&&(Sc=r,t=t.value),r=t}function Lp(){var t,e,n,o;if(t=_c,34===r.charCodeAt(_c)?(e='"',_c++):(e=s,0===Rc&&Mc(Lo)),e!==s){if(n=[],ho.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Co)),o!==s)for(;o!==s;)n.push(o),ho.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Co));else n=s;n!==s?(34===r.charCodeAt(_c)?(o='"',_c++):(o=s,0===Rc&&Mc(Lo)),o!==s?(Sc=t,t=e={type:"double_quote_string",value:n.join("")}):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;return t}function hp(){var t,e,n,o;if(t=_c,39===r.charCodeAt(_c)?(e="'",_c++):(e=s,0===Rc&&Mc(Ht)),e!==s){if(n=[],mo.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Eo)),o!==s)for(;o!==s;)n.push(o),mo.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Eo));else n=s;n!==s?(39===r.charCodeAt(_c)?(o="'",_c++):(o=s,0===Rc&&Mc(Ht)),o!==s?(Sc=t,t=e={type:"single_quote_string",value:n.join("")}):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;return t}function Cp(){var t,e,n,o;if(t=_c,96===r.charCodeAt(_c)?(e="`",_c++):(e=s,0===Rc&&Mc(Ao)),e!==s){if(n=[],go.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(To)),o!==s)for(;o!==s;)n.push(o),go.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(To));else n=s;n!==s?(96===r.charCodeAt(_c)?(o="`",_c++):(o=s,0===Rc&&Mc(Ao)),o!==s?(Sc=t,t=e={type:"backticks_quote_string",value:n.join("")}):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;return t}function mp(){var r,t;return r=_c,(t=gp())!==s&&(Sc=r,t=yo(t)),(r=t)===s&&(r=dp()),r}function Ep(){var r,t;return r=_c,(t=gp())!==s?(Sc=_c,(wo(t)?s:void 0)!==s?(Sc=r,r=t={type:"default",value:t}):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=dp()),r}function Ap(){var r,t;return r=_c,(t=gp())!==s?(Sc=_c,(wo(t)?s:void 0)!==s?(Sc=r,r=t=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=wp()),r}function gp(){var r,t,e,n;if(r=_c,(t=_p())!==s){for(e=[],n=xp();n!==s;)e.push(n),n=xp();e!==s?(Sc=r,r=t=t+e.join("")):(_c=r,r=s)}else _c=r,r=s;return r}function Tp(){var r,t,e,n;if(r=_c,(t=_p())!==s){for(e=[],n=Sp();n!==s;)e.push(n),n=Sp();e!==s?(Sc=r,r=t=t+e.join("")):(_c=r,r=s)}else _c=r,r=s;return r}function _p(){var t;return _o.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(So)),t}function Sp(){var t;return xo.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(jo)),t}function xp(){var t;return Io.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(Ro)),t}function jp(){var t,e,n,o;return t=_c,e=_c,58===r.charCodeAt(_c)?(n=":",_c++):(n=s,0===Rc&&Mc(No)),n!==s&&(o=Tp())!==s?e=n=[n,o]:(_c=e,e=s),e!==s&&(Sc=t,e={type:"param",value:e[1]}),t=e}function Ip(){var r,t,e;return r=_c,Pb()!==s&&fy()!==s&&Lb()!==s&&fy()!==s&&(t=Uv())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((e=$f())===s&&(e=null),e!==s&&fy()!==s&&oy()!==s?(Sc=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,Pb()!==s&&fy()!==s&&Lb()!==s&&fy()!==s&&(t=Uv())!==s?(Sc=r,r=function(r){return{type:"on update",keyword:r}}(t)):(_c=r,r=s)),r}function Rp(){var t,e,n,o,u;return t=_c,"over"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Oo)),e!==s&&fy()!==s&&(n=Ef())!==s?(Sc=t,t=e={type:"window",as_window_specification:n}):(_c=t,t=s),t===s&&(t=_c,"over"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Oo)),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Sf())===s&&(o=null),o!==s&&fy()!==s?((u=xf())===s&&(u=null),u!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={partitionby:o,orderby:u}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=Ip())),t}function Np(){var t,e,n;return t=_c,"ignore"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ho)),e===s&&("respect"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(qo))),e!==s&&fy()!==s?("nulls"===r.substr(_c,5).toLowerCase()?(n=r.substr(_c,5),_c+=5):(n=s,0===Rc&&Mc(Tr)),n!==s?(Sc=t,t=e=e.toUpperCase()+" NULLS"):(_c=t,t=s)):(_c=t,t=s),t}function Op(){var r,t,e;return r=_c,(t=ty())!==s&&fy()!==s&&(e=zp())!==s?(Sc=r,r=t={symbol:t,delimiter:e}):(_c=r,r=s),r}function kp(){var r,t,e,n,o,u,a,i,c,l,f;if(r=_c,(t=Kb())===s&&(t=null),t!==s)if(fy()!==s)if((e=ny())!==s)if(fy()!==s)if((n=Wf())!==s)if(fy()!==s)if((o=oy())!==s)if(fy()!==s){for(u=[],a=_c,(i=fy())!==s?((c=ov())===s&&(c=sv()),c!==s&&(l=fy())!==s&&(f=Wf())!==s?a=i=[i,c,l,f]:(_c=a,a=s)):(_c=a,a=s);a!==s;)u.push(a),a=_c,(i=fy())!==s?((c=ov())===s&&(c=sv()),c!==s&&(l=fy())!==s&&(f=Wf())!==s?a=i=[i,c,l,f]:(_c=a,a=s)):(_c=a,a=s);u!==s&&(a=fy())!==s?((i=Op())===s&&(i=null),i!==s&&(c=fy())!==s?((l=xf())===s&&(l=null),l!==s?(Sc=r,r=t=function(r,t,e,n,o){const s=e.length;let u=t;u.parentheses=!0;for(let r=0;r<s;++r)u=Gy(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:o,separator:n}}(t,n,u,i,l)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)}else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;else _c=r,r=s;return r===s&&(r=_c,(t=Kb())===s&&(t=null),t!==s&&fy()!==s&&(e=wf())!==s&&fy()!==s?((n=Op())===s&&(n=null),n!==s&&fy()!==s?((o=xf())===s&&(o=null),o!==s?(Sc=r,r=t=function(r,t,e,n){return{distinct:r,expr:t,orderby:n,separator:e}}(t,e,n,o)):(_c=r,r=s)):(_c=r,r=s)):(_c=r,r=s)),r}function Up(){var t,e,n;return t=_c,(e=function(){var t;return"both"===r.substr(_c,4).toLowerCase()?(t=r.substr(_c,4),_c+=4):(t=s,0===Rc&&Mc(Qo)),t===s&&("leading"===r.substr(_c,7).toLowerCase()?(t=r.substr(_c,7),_c+=7):(t=s,0===Rc&&Mc(Ko)),t===s&&("trailing"===r.substr(_c,8).toLowerCase()?(t=r.substr(_c,8),_c+=8):(t=s,0===Rc&&Mc(zo)))),t}())===s&&(e=null),e!==s&&fy()!==s?((n=Wf())===s&&(n=null),n!==s&&fy()!==s&&Rb()!==s?(Sc=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(_c=t,t=s)):(_c=t,t=s),t}function Mp(){var t,e,n,o;return t=_c,"trim"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Zo)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=Up())===s&&(n=null),n!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...Dy()}}(n,o)):(_c=t,t=s)):(_c=t,t=s),t}function Dp(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y;return t=_c,"crosstab"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Jo)),e===s&&("jsonb_to_recordset"===r.substr(_c,18).toLowerCase()?(e=r.substr(_c,18),_c+=18):(e=s,0===Rc&&Mc(rs)),e===s&&("jsonb_to_record"===r.substr(_c,15).toLowerCase()?(e=r.substr(_c,15),_c+=15):(e=s,0===Rc&&Mc(ts)),e===s&&("json_to_recordset"===r.substr(_c,17).toLowerCase()?(e=r.substr(_c,17),_c+=17):(e=s,0===Rc&&Mc(es)),e===s&&("json_to_record"===r.substr(_c,14).toLowerCase()?(e=r.substr(_c,14),_c+=14):(e=s,0===Rc&&Mc(ns)))))),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=$f())!==s&&fy()!==s&&oy()!==s&&fy()!==s?(o=_c,(u=Ob())!==s&&(a=fy())!==s&&(i=Tp())!==s&&(c=fy())!==s&&(l=ny())!==s&&(f=fy())!==s&&(p=Qc())!==s&&(b=fy())!==s&&(v=oy())!==s?o=u=[u,a,i,c,l,f,p,b,v]:(_c=o,o=s),o===s&&(o=null),o!==s?(Sc=t,t=e={type:"tablefunc",name:{name:[{type:"default",value:e}]},args:n,as:(y=o)&&{type:"function",name:{name:[{type:"default",value:y[2]}]},args:{type:"expr_list",value:y[6].map(r=>({...r,type:"column_definition"}))},...Dy()},...Dy()}):(_c=t,t=s)):(_c=t,t=s),t}function Pp(){var t,e,n,o;return t=_c,"years"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ss)),e===s&&("months"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(us)),e===s&&("weeks"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(as)),e===s&&("days"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(is)),e===s&&("hours"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(cs)),e===s&&("mins"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ls))))))),e!==s&&fy()!==s?("=>"===r.substr(_c,2)?(n="=>",_c+=2):(n=s,0===Rc&&Mc(fs)),n!==s&&fy()!==s?((o=nb())===s&&(o=Wf()),o!==s?(Sc=t,t=e={type:"func_arg",value:{name:e,symbol:"=>",expr:o}}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"secs"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ps)),e!==s&&fy()!==s?("=>"===r.substr(_c,2)?(n="=>",_c+=2):(n=s,0===Rc&&Mc(fs)),n!==s&&fy()!==s?((o=ob())===s&&(o=Wf()),o!==s?(Sc=t,t=e=function(r,t){return{type:"func_arg",value:{name:r,symbol:"=>",expr:t}}}(e,o)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)),t}function Gp(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Pp())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Pp())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Pp())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t={type:"expr_list",value:Fy(t,e)}):(_c=r,r=s)}else _c=r,r=s;return r===s&&(r=$f()),r}function $p(){var t,e,n;return t=_c,"make_interval"===r.substr(_c,13).toLowerCase()?(e=r.substr(_c,13),_c+=13):(e=s,0===Rc&&Mc(bs)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Gp())!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n,...Dy()}):(_c=t,t=s),t}function Fp(){var t,e,n,o,u,a,i,c,l,f;return(t=Mp())===s&&(t=Dp())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;return t=_c,"substring"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s&&(o=ty())!==s&&(u=fy())!==s&&(a=eb())!==s&&(i=fy())!==s&&(c=ty())!==s&&(l=fy())!==s&&(f=eb())!==s&&(p=fy())!==s&&oy()!==s?(Sc=t,t=e={type:"function",name:{name:[{type:"origin",value:"substring"}]},args:{type:"expr_list",value:[n,a,f]}}):(_c=t,t=s),t===s&&(t=_c,"substring"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s&&(o=Rb())!==s&&(u=fy())!==s&&(a=dp())!==s&&(i=fy())!==s?(c=_c,"for"===r.substr(_c,3).toLowerCase()?(l=r.substr(_c,3),_c+=3):(l=s,0===Rc&&Mc(Ir)),l!==s&&(f=fy())!==s&&(p=dp())!==s?c=l=[l,f,p]:(_c=c,c=s),c===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s?(Sc=t,t=e=function(r,t,e){const n=[{type:"origin",value:"from"}],o={type:"expr_list",value:[r,t]};return e&&(n.push({type:"origin",value:"for"}),o.value.push(e[2])),{type:"function",name:{name:[{type:"origin",value:"substring"}]},args:o,separator:n}}(n,a,c)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,"substring"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s?(o=_c,(u=Rb())!==s&&(a=fy())!==s&&(i=eb())!==s?o=u=[u,a,i]:(_c=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s?(a=_c,"for"===r.substr(_c,3).toLowerCase()?(i=r.substr(_c,3),_c+=3):(i=s,0===Rc&&Mc(Ir)),i!==s&&(c=fy())!==s&&(l=eb())!==s?a=i=[i,c,l]:(_c=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s&&(c=oy())!==s?(Sc=t,t=e=function(r,t,e){const n=[],o={type:"expr_list",value:[r]};return t&&(n.push({type:"origin",value:"from"}),o.value.push(t[2])),e&&(n.push({type:"origin",value:"for"}),o.value.push(e[2])),{type:"function",name:{name:[{type:"origin",value:"substring"}]},args:o,separator:n}}(n,o,a)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s))),t}())===s&&(t=$p())===s&&(t=_c,"now"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(vs)),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=$f())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s&&fy()!==s?("at"===r.substr(_c,2).toLowerCase()?(u=r.substr(_c,2),_c+=2):(u=s,0===Rc&&Mc(ys)),u!==s&&fy()!==s&&Rv()!==s&&fy()!==s?("zone"===r.substr(_c,4).toLowerCase()?(a=r.substr(_c,4),_c+=4):(a=s,0===Rc&&Mc(ds)),a!==s&&fy()!==s&&(i=zp())!==s?(Sc=t,c=e,l=o,(f=i).prefix="at time zone",t=e={type:"function",name:{name:[{type:"default",value:c}]},args:l||{type:"expr_list",value:[]},suffix:f,...Dy()}):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=function(){var t;(t=Hp())===s&&(t=Mv())===s&&(t=function(){var t,e,n,o;t=_c,"user"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(li));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="USER"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=Dv())===s&&(t=function(){var t,e,n,o;t=_c,"system_user"===r.substr(_c,11).toLowerCase()?(e=r.substr(_c,11),_c+=11):(e=s,0===Rc&&Mc(xi));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SYSTEM_USER"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&("ntile"===r.substr(_c,5).toLowerCase()?(t=r.substr(_c,5),_c+=5):(t=s,0===Rc&&Mc(Gs)));return t}())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=$f())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s&&fy()!==s?((u=Rp())===s&&(u=null),u!==s?(Sc=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"origin",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...Dy()}}(e,o,u)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=function(){var r,t,e,n,o;r=_c,(t=av())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Bp())!==s&&fy()!==s&&Rb()!==s&&fy()!==s?((n=Nv())===s&&(n=kv())===s&&(n=Rv())===s&&(n=xv()),n===s&&(n=null),n!==s&&fy()!==s&&(o=Wf())!==s&&fy()!==s&&oy()!==s?(Sc=r,u=e,a=n,i=o,t={type:t.toLowerCase(),args:{field:u,cast_type:a,source:i},...Dy()},r=t):(_c=r,r=s)):(_c=r,r=s);var u,a,i;r===s&&(r=_c,(t=av())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Bp())!==s&&fy()!==s&&Rb()!==s&&fy()!==s&&(n=Wf())!==s&&fy()!==s&&(o=oy())!==s?(Sc=r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...Dy()}}(t,e,n),r=t):(_c=r,r=s));return r}())===s&&(t=_c,(e=Hp())!==s&&fy()!==s?((n=tf())===s&&(n=null),n!==s&&fy()!==s?((o=Ip())===s&&(o=null),o!==s?(Sc=t,t=e=function(r,t,e){const n={};t&&(n.args={type:"expr_list",value:t},n.args_parentheses=!1,n.separator=" ");return{type:"function",name:{name:[{type:"origin",value:r}]},over:e,...n,...Dy()}}(e,n,o)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=_y())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Vf())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s?(Sc=t,t=e=function(r,t){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},...Dy()}}(e,o)):(_c=t,t=s)):(_c=t,t=s))))),t}function Bp(){var t,e;return t=_c,"century"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(ws)),e===s&&("day"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ls)),e===s&&("date"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(hs)),e===s&&("decade"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Cs)),e===s&&("dow"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(ms)),e===s&&("doy"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Es)),e===s&&("epoch"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(As)),e===s&&("hour"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(gs)),e===s&&("isodow"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ts)),e===s&&("isoyear"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(_s)),e===s&&("microseconds"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Ss)),e===s&&("millennium"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(xs)),e===s&&("milliseconds"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(js)),e===s&&("minute"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Is)),e===s&&("month"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Rs)),e===s&&("quarter"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ns)),e===s&&("second"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Os)),e===s&&("timezone"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(ks)),e===s&&("timezone_hour"===r.substr(_c,13).toLowerCase()?(e=r.substr(_c,13),_c+=13):(e=s,0===Rc&&Mc(Us)),e===s&&("timezone_minute"===r.substr(_c,15).toLowerCase()?(e=r.substr(_c,15),_c+=15):(e=s,0===Rc&&Mc(Ms)),e===s&&("week"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ds)),e===s&&("year"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ps))))))))))))))))))))))),e!==s&&(Sc=t,e=e),t=e}function Hp(){var t;return(t=function(){var t,e,n,o;t=_c,"current_date"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(gi));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CURRENT_DATE"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=_c,"current_time"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(_i));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CURRENT_TIME"):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=Uv()),t}function qp(){var t,e,n,o;return t=_c,34===r.charCodeAt(_c)?(e='"',_c++):(e=s,0===Rc&&Mc(Lo)),e===s&&(e=null),e!==s&&(n=Ry())!==s?(34===r.charCodeAt(_c)?(o='"',_c++):(o=s,0===Rc&&Mc(Lo)),o===s&&(o=null),o!==s?(Sc=t,t=e=function(r,t,e){if(r&&!e||!r&&e)throw new Error("double quoted not match");return r&&e&&(t.quoted='"'),t}(e,n,o)):(_c=t,t=s)):(_c=t,t=s),t}function Yp(){var r,t,e,n,o,u;if(r=_c,t=[],e=_c,(n=Hv())!==s&&(o=fy())!==s&&(u=qp())!==s?e=n=[n,o,u]:(_c=e,e=s),e!==s)for(;e!==s;)t.push(e),e=_c,(n=Hv())!==s&&(o=fy())!==s&&(u=qp())!==s?e=n=[n,o,u]:(_c=e,e=s);else t=s;return t!==s&&(e=fy())!==s?((n=of())===s&&(n=null),n!==s?(Sc=r,r=t={as:n,symbol:"::",target:t.map(r=>r[2])}):(_c=r,r=s)):(_c=r,r=s),r}function Wp(){var t;return(t=zp())===s&&(t=eb())===s&&(t=Kp())===s&&(t=Xp())===s&&(t=function(){var t,e,n,o,u,a;t=_c,(e=Rv())===s&&(e=xv())===s&&(e=Nv())===s&&(e=jv());if(e!==s)if(fy()!==s){if(n=_c,39===r.charCodeAt(_c)?(o="'",_c++):(o=s,0===Rc&&Mc(Ht)),o!==s){for(u=[],a=rb();a!==s;)u.push(a),a=rb();u!==s?(39===r.charCodeAt(_c)?(a="'",_c++):(a=s,0===Rc&&Mc(Ht)),a!==s?n=o=[o,u,a]:(_c=n,n=s)):(_c=n,n=s)}else _c=n,n=s;n!==s?(Sc=t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;var i;if(t===s)if(t=_c,(e=Rv())===s&&(e=xv())===s&&(e=Nv())===s&&(e=jv()),e!==s)if(fy()!==s){if(n=_c,34===r.charCodeAt(_c)?(o='"',_c++):(o=s,0===Rc&&Mc(Lo)),o!==s){for(u=[],a=Jp();a!==s;)u.push(a),a=Jp();u!==s?(34===r.charCodeAt(_c)?(a='"',_c++):(a=s,0===Rc&&Mc(Lo)),a!==s?n=o=[o,u,a]:(_c=n,n=s)):(_c=n,n=s)}else _c=n,n=s;n!==s?(Sc=t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;return t}())===s&&(t=Vp()),t}function Vp(){var r,t;return r=_c,uv()!==s&&fy()!==s&&sy()!==s&&fy()!==s?((t=$f())===s&&(t=null),t!==s&&fy()!==s&&uy()!==s?(Sc=r,r=function(r,t){return{expr_list:t||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}}(0,t)):(_c=r,r=s)):(_c=r,r=s),r}function Xp(){var r,t;return r=_c,(t=fb())!==s&&(Sc=r,t={type:"null",value:null}),r=t}function Qp(){var t,e;return t=_c,(e=function(){var t,e,n,o;t=_c,"not null"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(yu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={type:"not null",value:"not null"}),t=e}function Kp(){var t,e;return t=_c,(e=function(){var t,e,n,o;t=_c,"true"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(du));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={type:"bool",value:!0}),(t=e)===s&&(t=_c,(e=function(){var t,e,n,o;t=_c,"false"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(wu));e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s);return t}())!==s&&(Sc=t,e={type:"bool",value:!1}),t=e),t}function zp(){var t,e,n,o,u,a,i,c,l;if(t=_c,e=_c,39===r.charCodeAt(_c)?(n="'",_c++):(n=s,0===Rc&&Mc(Ht)),n!==s){for(o=[],u=rb();u!==s;)o.push(u),u=rb();o!==s?(39===r.charCodeAt(_c)?(u="'",_c++):(u=s,0===Rc&&Mc(Ht)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s)}else _c=e,e=s;if(e!==s){if(n=[],$s.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Fs)),o!==s)for(;o!==s;)n.push(o),$s.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(Fs));else n=s;if(n!==s)if((o=fy())!==s){if(u=_c,39===r.charCodeAt(_c)?(a="'",_c++):(a=s,0===Rc&&Mc(Ht)),a!==s){for(i=[],c=rb();c!==s;)i.push(c),c=rb();i!==s?(39===r.charCodeAt(_c)?(c="'",_c++):(c=s,0===Rc&&Mc(Ht)),c!==s?u=a=[a,i,c]:(_c=u,u=s)):(_c=u,u=s)}else _c=u,u=s;u!==s?(Sc=t,l=u,t=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`}):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s}else _c=t,t=s;if(t===s){if(t=_c,e=_c,39===r.charCodeAt(_c)?(n="'",_c++):(n=s,0===Rc&&Mc(Ht)),n!==s){for(o=[],u=rb();u!==s;)o.push(u),u=rb();o!==s?(39===r.charCodeAt(_c)?(u="'",_c++):(u=s,0===Rc&&Mc(Ht)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s)}else _c=e,e=s;e!==s&&(Sc=t,e=function(r){return{type:"single_quote_string",value:r[1].join("")}}(e)),(t=e)===s&&(t=Zp())}return t}function Zp(){var t,e,n,o,u;if(t=_c,e=_c,34===r.charCodeAt(_c)?(n='"',_c++):(n=s,0===Rc&&Mc(Lo)),n!==s){for(o=[],u=Jp();u!==s;)o.push(u),u=Jp();o!==s?(34===r.charCodeAt(_c)?(u='"',_c++):(u=s,0===Rc&&Mc(Lo)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s)}else _c=e,e=s;return e!==s?(n=_c,Rc++,o=ry(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e={type:"double_quote_string",value:e[1].join("")}):(_c=t,t=s)):(_c=t,t=s),t}function Jp(){var t;return Bs.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(Hs)),t===s&&(t=tb()),t}function rb(){var t;return qs.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(Ys)),t===s&&(t=tb()),t}function tb(){var t,e,n,o,u,a,i,c,l,f;return t=_c,"\\'"===r.substr(_c,2)?(e="\\'",_c+=2):(e=s,0===Rc&&Mc(Ws)),e!==s&&(Sc=t,e="\\'"),(t=e)===s&&(t=_c,'\\"'===r.substr(_c,2)?(e='\\"',_c+=2):(e=s,0===Rc&&Mc(Vs)),e!==s&&(Sc=t,e='\\"'),(t=e)===s&&(t=_c,"\\\\"===r.substr(_c,2)?(e="\\\\",_c+=2):(e=s,0===Rc&&Mc(Xs)),e!==s&&(Sc=t,e="\\\\"),(t=e)===s&&(t=_c,"\\/"===r.substr(_c,2)?(e="\\/",_c+=2):(e=s,0===Rc&&Mc(Qs)),e!==s&&(Sc=t,e="\\/"),(t=e)===s&&(t=_c,"\\b"===r.substr(_c,2)?(e="\\b",_c+=2):(e=s,0===Rc&&Mc(Ks)),e!==s&&(Sc=t,e="\b"),(t=e)===s&&(t=_c,"\\f"===r.substr(_c,2)?(e="\\f",_c+=2):(e=s,0===Rc&&Mc(zs)),e!==s&&(Sc=t,e="\f"),(t=e)===s&&(t=_c,"\\n"===r.substr(_c,2)?(e="\\n",_c+=2):(e=s,0===Rc&&Mc(Zs)),e!==s&&(Sc=t,e="\n"),(t=e)===s&&(t=_c,"\\r"===r.substr(_c,2)?(e="\\r",_c+=2):(e=s,0===Rc&&Mc(Js)),e!==s&&(Sc=t,e="\r"),(t=e)===s&&(t=_c,"\\t"===r.substr(_c,2)?(e="\\t",_c+=2):(e=s,0===Rc&&Mc(ru)),e!==s&&(Sc=t,e="\t"),(t=e)===s&&(t=_c,"\\u"===r.substr(_c,2)?(e="\\u",_c+=2):(e=s,0===Rc&&Mc(tu)),e!==s&&(n=lb())!==s&&(o=lb())!==s&&(u=lb())!==s&&(a=lb())!==s?(Sc=t,i=n,c=o,l=u,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(_c=t,t=s),t===s&&(t=_c,92===r.charCodeAt(_c)?(e="\\",_c++):(e=s,0===Rc&&Mc(eu)),e!==s&&(Sc=t,e="\\"),(t=e)===s&&(t=_c,"''"===r.substr(_c,2)?(e="''",_c+=2):(e=s,0===Rc&&Mc(nu)),e!==s&&(Sc=t,e="''"),t=e))))))))))),t}function eb(){var r,t,e;return r=_c,(t=function(){var r;(r=ob())===s&&(r=nb());return r}())!==s&&(Sc=r,t=(e=t)&&"object"==typeof e?e:{type:"number",value:e}),r=t}function nb(){var r,t,e;return r=_c,(t=sb())!==s&&(e=ab())!==s?(Sc=r,r=t={type:"bigint",value:t+e}):(_c=r,r=s),r===s&&(r=_c,(t=sb())!==s&&(Sc=r,t=function(r){return $y(r)?{type:"bigint",value:r}:{type:"number",value:parseFloat(r)}}(t)),r=t),r}function ob(){var r,t,e,n;return r=_c,(t=sb())===s&&(t=null),t!==s&&(e=ub())!==s&&(n=ab())!==s?(Sc=r,r=t={type:"bigint",value:(t||"")+e+n}):(_c=r,r=s),r===s&&(r=_c,(t=sb())===s&&(t=null),t!==s&&(e=ub())!==s?(Sc=r,r=t=function(r,t){const e=(r||"")+t;return r&&$y(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e)):(_c=r,r=s)),r}function sb(){var t,e,n;return(t=ib())===s&&(t=cb())===s&&(t=_c,45===r.charCodeAt(_c)?(e="-",_c++):(e=s,0===Rc&&Mc(ro)),e===s&&(43===r.charCodeAt(_c)?(e="+",_c++):(e=s,0===Rc&&Mc(Jn))),e!==s&&(n=ib())!==s?(Sc=t,t=e=e+n):(_c=t,t=s),t===s&&(t=_c,45===r.charCodeAt(_c)?(e="-",_c++):(e=s,0===Rc&&Mc(ro)),e===s&&(43===r.charCodeAt(_c)?(e="+",_c++):(e=s,0===Rc&&Mc(Jn))),e!==s&&(n=cb())!==s?(Sc=t,t=e=function(r,t){return r+t}(e,n)):(_c=t,t=s))),t}function ub(){var t,e,n;return t=_c,46===r.charCodeAt(_c)?(e=".",_c++):(e=s,0===Rc&&Mc(uu)),e!==s&&(n=ib())!==s?(Sc=t,t=e="."+n):(_c=t,t=s),t}function ab(){var t,e,n;return t=_c,(e=function(){var t,e,n;t=_c,fu.test(r.charAt(_c))?(e=r.charAt(_c),_c++):(e=s,0===Rc&&Mc(pu));e!==s?(bu.test(r.charAt(_c))?(n=r.charAt(_c),_c++):(n=s,0===Rc&&Mc(vu)),n===s&&(n=null),n!==s?(Sc=t,t=e=e+(null!==(o=n)?o:"")):(_c=t,t=s)):(_c=t,t=s);var o;return t}())!==s&&(n=ib())!==s?(Sc=t,t=e=e+n):(_c=t,t=s),t}function ib(){var r,t,e;if(r=_c,t=[],(e=cb())!==s)for(;e!==s;)t.push(e),e=cb();else t=s;return t!==s&&(Sc=r,t=t.join("")),r=t}function cb(){var t;return au.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(iu)),t}function lb(){var t;return cu.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(lu)),t}function fb(){var t,e,n,o;return t=_c,"null"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(H)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function pb(){var t,e,n,o;return t=_c,"default"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Gr)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function bb(){var t,e,n,o;return t=_c,"to"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(ar)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function vb(){var t,e,n,o;return t=_c,"show"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Lu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function yb(){var t,e,n,o;return t=_c,"drop"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(hu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DROP"):(_c=t,t=s)):(_c=t,t=s),t}function db(){var t,e,n,o;return t=_c,"alter"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(mu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function wb(){var t,e,n,o;return t=_c,"select"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Eu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Lb(){var t,e,n,o;return t=_c,"update"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Au)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function hb(){var t,e,n,o;return t=_c,"create"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(gu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Cb(){var t,e,n,o;return t=_c,"temporary"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Tu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function mb(){var t,e,n,o;return t=_c,"UNLOGGED"===r.substr(_c,8)?(e="UNLOGGED",_c+=8):(e=s,0===Rc&&Mc(_u)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="UNLOGGED"):(_c=t,t=s)):(_c=t,t=s),t}function Eb(){var t,e,n,o;return t=_c,"temp"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Su)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Ab(){var t,e,n,o;return t=_c,"delete"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(xu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function gb(){var t,e,n,o;return t=_c,"insert"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ju)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Tb(){var t,e,n,o;return t=_c,"recursive"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Iu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="RECURSIVE"):(_c=t,t=s)):(_c=t,t=s),t}function _b(){var t,e,n,o;return t=_c,"replace"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ru)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Sb(){var t,e,n,o;return t=_c,"rename"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ku)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function xb(){var t,e,n,o;return t=_c,"ignore"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ho)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function jb(){var t,e,n,o;return t=_c,"partition"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Uu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="PARTITION"):(_c=t,t=s)):(_c=t,t=s),t}function Ib(){var t,e,n,o;return t=_c,"into"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Mu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Rb(){var t,e,n,o;return t=_c,"from"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Du)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Nb(){var t,e,n,o;return t=_c,"set"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ot)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SET"):(_c=t,t=s)):(_c=t,t=s),t}function Ob(){var t,e,n,o;return t=_c,"as"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(Pu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function kb(){var t,e,n,o;return t=_c,"table"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Be)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TABLE"):(_c=t,t=s)):(_c=t,t=s),t}function Ub(){var t,e,n,o;return t=_c,"database"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(fe)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DATABASE"):(_c=t,t=s)):(_c=t,t=s),t}function Mb(){var t,e,n,o;return t=_c,"schema"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(b)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SCHEMA"):(_c=t,t=s)):(_c=t,t=s),t}function Db(){var t,e,n,o;return t=_c,"tablespace"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(Gu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TABLESPACE"):(_c=t,t=s)):(_c=t,t=s),t}function Pb(){var t,e,n,o;return t=_c,"on"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(q)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Gb(){var t,e,n,o;return t=_c,"join"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Wu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function $b(){var t,e,n,o;return t=_c,"outer"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Vu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Fb(){var t,e,n,o;return t=_c,"values"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(zu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Bb(){var t,e,n,o;return t=_c,"using"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Zu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Hb(){var t,e,n,o;return t=_c,"with"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Tn)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function qb(){var t,e,n,o;return t=_c,"group"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ra)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Yb(){var t,e,n,o;return t=_c,"by"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(ta)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Wb(){var t,e,n,o;return t=_c,"order"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ea)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function Vb(){var t,e,n,o;return t=_c,"asc"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(ua)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ASC"):(_c=t,t=s)):(_c=t,t=s),t}function Xb(){var t,e,n,o;return t=_c,"desc"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(aa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DESC"):(_c=t,t=s)):(_c=t,t=s),t}function Qb(){var t,e,n,o;return t=_c,"all"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(ia)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ALL"):(_c=t,t=s)):(_c=t,t=s),t}function Kb(){var t,e,n,o;return t=_c,"distinct"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(ca)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DISTINCT"):(_c=t,t=s)):(_c=t,t=s),t}function zb(){var t,e,n,o;return t=_c,"between"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(la)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="BETWEEN"):(_c=t,t=s)):(_c=t,t=s),t}function Zb(){var t,e,n,o;return t=_c,"in"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(Vt)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="IN"):(_c=t,t=s)):(_c=t,t=s),t}function Jb(){var t,e,n,o;return t=_c,"is"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(pn)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="IS"):(_c=t,t=s)):(_c=t,t=s),t}function rv(){var t,e,n,o;return t=_c,"like"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(fa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="LIKE"):(_c=t,t=s)):(_c=t,t=s),t}function tv(){var t,e,n,o;return t=_c,"ilike"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(pa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ILIKE"):(_c=t,t=s)):(_c=t,t=s),t}function ev(){var t,e,n,o;return t=_c,"exists"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ba)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="EXISTS"):(_c=t,t=s)):(_c=t,t=s),t}function nv(){var t,e,n,o;return t=_c,"not"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc($)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="NOT"):(_c=t,t=s)):(_c=t,t=s),t}function ov(){var t,e,n,o;return t=_c,"and"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(va)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="AND"):(_c=t,t=s)):(_c=t,t=s),t}function sv(){var t,e,n,o;return t=_c,"or"===r.substr(_c,2).toLowerCase()?(e=r.substr(_c,2),_c+=2):(e=s,0===Rc&&Mc(ya)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="OR"):(_c=t,t=s)):(_c=t,t=s),t}function uv(){var t,e,n,o;return t=_c,"array"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(da)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ARRAY"):(_c=t,t=s)):(_c=t,t=s),t}function av(){var t,e,n,o;return t=_c,"extract"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ta)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="EXTRACT"):(_c=t,t=s)):(_c=t,t=s),t}function iv(){var t,e,n,o;return t=_c,"case"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Sa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function cv(){var t,e,n,o;return t=_c,"when"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(xa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function lv(){var t,e,n,o;return t=_c,"else"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ja)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function fv(){var t,e,n,o;return t=_c,"end"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(xe)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?t=e=[e,n]:(_c=t,t=s)):(_c=t,t=s),t}function pv(){var t,e,n,o;return t=_c,"cast"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ia)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CAST"):(_c=t,t=s)):(_c=t,t=s),t}function bv(){var t,e,n,o;return t=_c,"numeric"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ua)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="NUMERIC"):(_c=t,t=s)):(_c=t,t=s),t}function vv(){var t,e,n,o;return t=_c,"decimal"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ma)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DECIMAL"):(_c=t,t=s)):(_c=t,t=s),t}function yv(){var t,e,n,o;return t=_c,"unsigned"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Pa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="UNSIGNED"):(_c=t,t=s)):(_c=t,t=s),t}function dv(){var t,e,n,o;return t=_c,"int"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ga)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="INT"):(_c=t,t=s)):(_c=t,t=s),t}function wv(){var t,e,n,o;return t=_c,"integer"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Fa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="INTEGER"):(_c=t,t=s)):(_c=t,t=s),t}function Lv(){var t,e,n,o;return t=_c,"smallint"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Ya)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SMALLINT"):(_c=t,t=s)):(_c=t,t=s),t}function hv(){var t,e,n,o;return t=_c,"serial"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Wa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SERIAL"):(_c=t,t=s)):(_c=t,t=s),t}function Cv(){var t,e,n,o;return t=_c,"tinyint"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Va)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TINYINT"):(_c=t,t=s)):(_c=t,t=s),t}function mv(){var t,e,n,o;return t=_c,"mediumint"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Za)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MEDIUMINT"):(_c=t,t=s)):(_c=t,t=s),t}function Ev(){var t,e,n,o;return t=_c,"bigint"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ja)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="BIGINT"):(_c=t,t=s)):(_c=t,t=s),t}function Av(){var t,e,n,o;return t=_c,"enum"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ri)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ENUM"):(_c=t,t=s)):(_c=t,t=s),t}function gv(){var t,e,n,o;return t=_c,"float"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(ti)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="FLOAT"):(_c=t,t=s)):(_c=t,t=s),t}function Tv(){var t,e,n,o;return t=_c,"double"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(ei)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DOUBLE"):(_c=t,t=s)):(_c=t,t=s),t}function _v(){var t,e,n,o;return t=_c,"bigserial"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(ni)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="BIGSERIAL"):(_c=t,t=s)):(_c=t,t=s),t}function Sv(){var t,e,n,o;return t=_c,"real"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(oi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REAL"):(_c=t,t=s)):(_c=t,t=s),t}function xv(){var t,e,n,o;return t=_c,"date"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(hs)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DATE"):(_c=t,t=s)):(_c=t,t=s),t}function jv(){var t,e,n,o;return t=_c,"datetime"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(si)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DATETIME"):(_c=t,t=s)):(_c=t,t=s),t}function Iv(){var t,e,n,o;return t=_c,"rows"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(sr)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ROWS"):(_c=t,t=s)):(_c=t,t=s),t}function Rv(){var t,e,n,o;return t=_c,"time"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(ui)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TIME"):(_c=t,t=s)):(_c=t,t=s),t}function Nv(){var t,e,n,o;return t=_c,"timestamp"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(ai)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TIMESTAMP"):(_c=t,t=s)):(_c=t,t=s),t}function Ov(){var t,e,n,o;return t=_c,"truncate"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(ci)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TRUNCATE"):(_c=t,t=s)):(_c=t,t=s),t}function kv(){var t,e,n,o;return t=_c,"interval"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Ti)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="INTERVAL"):(_c=t,t=s)):(_c=t,t=s),t}function Uv(){var t,e,n,o;return t=_c,"current_timestamp"===r.substr(_c,17).toLowerCase()?(e=r.substr(_c,17),_c+=17):(e=s,0===Rc&&Mc(Si)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CURRENT_TIMESTAMP"):(_c=t,t=s)):(_c=t,t=s),t}function Mv(){var t,e,n,o;return t=_c,"current_user"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(rt)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CURRENT_USER"):(_c=t,t=s)):(_c=t,t=s),t}function Dv(){var t,e,n,o;return t=_c,"session_user"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(tt)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SESSION_USER"):(_c=t,t=s)):(_c=t,t=s),t}function Pv(){var t,e,n,o;return t=_c,"local"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(d)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="LOCAL"):(_c=t,t=s)):(_c=t,t=s),t}function Gv(){var t,e,n,o;return t=_c,"view"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Oi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="VIEW"):(_c=t,t=s)):(_c=t,t=s),t}function $v(){var t;return 36===r.charCodeAt(_c)?(t="$",_c++):(t=s,0===Rc&&Mc(so)),t}function Fv(){var t;return"$$"===r.substr(_c,2)?(t="$$",_c+=2):(t=s,0===Rc&&Mc(Mi)),t}function Bv(){var t;return(t=function(){var t;return"@@"===r.substr(_c,2)?(t="@@",_c+=2):(t=s,0===Rc&&Mc(Ui)),t}())===s&&(t=function(){var t;return 64===r.charCodeAt(_c)?(t="@",_c++):(t=s,0===Rc&&Mc(ki)),t}())===s&&(t=$v())===s&&(t=$v()),t}function Hv(){var t;return"::"===r.substr(_c,2)?(t="::",_c+=2):(t=s,0===Rc&&Mc(Di)),t}function qv(){var t;return 61===r.charCodeAt(_c)?(t="=",_c++):(t=s,0===Rc&&Mc(ir)),t}function Yv(){var t,e,n,o;return t=_c,"add"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Gi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ADD"):(_c=t,t=s)):(_c=t,t=s),t}function Wv(){var t,e,n,o;return t=_c,"column"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Ge)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="COLUMN"):(_c=t,t=s)):(_c=t,t=s),t}function Vv(){var t,e,n,o;return t=_c,"index"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc($i)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="INDEX"):(_c=t,t=s)):(_c=t,t=s),t}function Xv(){var t,e,n,o;return t=_c,"key"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(kr)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="KEY"):(_c=t,t=s)):(_c=t,t=s),t}function Qv(){var t,e,n,o;return t=_c,"unique"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Or)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="UNIQUE"):(_c=t,t=s)):(_c=t,t=s),t}function Kv(){var t,e,n,o;return t=_c,"comment"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(bn)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="COMMENT"):(_c=t,t=s)):(_c=t,t=s),t}function zv(){var t,e,n,o;return t=_c,"constraint"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc($e)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CONSTRAINT"):(_c=t,t=s)):(_c=t,t=s),t}function Zv(){var t,e,n,o;return t=_c,"concurrently"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Hi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CONCURRENTLY"):(_c=t,t=s)):(_c=t,t=s),t}function Jv(){var t,e,n,o;return t=_c,"references"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(qi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REFERENCES"):(_c=t,t=s)):(_c=t,t=s),t}function ry(){var t;return 46===r.charCodeAt(_c)?(t=".",_c++):(t=s,0===Rc&&Mc(uu)),t}function ty(){var t;return 44===r.charCodeAt(_c)?(t=",",_c++):(t=s,0===Rc&&Mc(zi)),t}function ey(){var t;return 42===r.charCodeAt(_c)?(t="*",_c++):(t=s,0===Rc&&Mc(to)),t}function ny(){var t;return 40===r.charCodeAt(_c)?(t="(",_c++):(t=s,0===Rc&&Mc(vn)),t}function oy(){var t;return 41===r.charCodeAt(_c)?(t=")",_c++):(t=s,0===Rc&&Mc(yn)),t}function sy(){var t;return 91===r.charCodeAt(_c)?(t="[",_c++):(t=s,0===Rc&&Mc(Zi)),t}function uy(){var t;return 93===r.charCodeAt(_c)?(t="]",_c++):(t=s,0===Rc&&Mc(Ji)),t}function ay(){var t;return 59===r.charCodeAt(_c)?(t=";",_c++):(t=s,0===Rc&&Mc(dn)),t}function iy(){var t;return"->"===r.substr(_c,2)?(t="->",_c+=2):(t=s,0===Rc&&Mc(rc)),t}function cy(){var t;return"->>"===r.substr(_c,3)?(t="->>",_c+=3):(t=s,0===Rc&&Mc(tc)),t}function ly(){var t;return(t=function(){var t;return"||"===r.substr(_c,2)?(t="||",_c+=2):(t=s,0===Rc&&Mc(oo)),t}())===s&&(t=function(){var t;return"&&"===r.substr(_c,2)?(t="&&",_c+=2):(t=s,0===Rc&&Mc(ec)),t}()),t}function fy(){var r,t;for(r=[],(t=wy())===s&&(t=by());t!==s;)r.push(t),(t=wy())===s&&(t=by());return r}function py(){var r,t;if(r=[],(t=wy())===s&&(t=by()),t!==s)for(;t!==s;)r.push(t),(t=wy())===s&&(t=by());else r=s;return r}function by(){var t;return(t=function t(){var e,n,o,u,a,i,c;e=_c,"/*"===r.substr(_c,2)?(n="/*",_c+=2):(n=s,0===Rc&&Mc(nc));if(n!==s){for(o=[],u=_c,a=_c,Rc++,"*/"===r.substr(_c,2)?(i="*/",_c+=2):(i=s,0===Rc&&Mc(oc)),Rc--,i===s?a=void 0:(_c=a,a=s),a!==s?(i=_c,Rc++,"/*"===r.substr(_c,2)?(c="/*",_c+=2):(c=s,0===Rc&&Mc(nc)),Rc--,c===s?i=void 0:(_c=i,i=s),i!==s&&(c=yy())!==s?u=a=[a,i,c]:(_c=u,u=s)):(_c=u,u=s),u===s&&(u=t());u!==s;)o.push(u),u=_c,a=_c,Rc++,"*/"===r.substr(_c,2)?(i="*/",_c+=2):(i=s,0===Rc&&Mc(oc)),Rc--,i===s?a=void 0:(_c=a,a=s),a!==s?(i=_c,Rc++,"/*"===r.substr(_c,2)?(c="/*",_c+=2):(c=s,0===Rc&&Mc(nc)),Rc--,c===s?i=void 0:(_c=i,i=s),i!==s&&(c=yy())!==s?u=a=[a,i,c]:(_c=u,u=s)):(_c=u,u=s),u===s&&(u=t());o!==s?("*/"===r.substr(_c,2)?(u="*/",_c+=2):(u=s,0===Rc&&Mc(oc)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s)}else _c=e,e=s;return e}())===s&&(t=function(){var t,e,n,o,u,a;t=_c,"--"===r.substr(_c,2)?(e="--",_c+=2):(e=s,0===Rc&&Mc(sc));if(e!==s){for(n=[],o=_c,u=_c,Rc++,a=Ly(),Rc--,a===s?u=void 0:(_c=u,u=s),u!==s&&(a=yy())!==s?o=u=[u,a]:(_c=o,o=s);o!==s;)n.push(o),o=_c,u=_c,Rc++,a=Ly(),Rc--,a===s?u=void 0:(_c=u,u=s),u!==s&&(a=yy())!==s?o=u=[u,a]:(_c=o,o=s);n!==s?t=e=[e,n]:(_c=t,t=s)}else _c=t,t=s;return t}()),t}function vy(){var r,t,e,n;return r=_c,(t=Kv())!==s&&fy()!==s?((e=qv())===s&&(e=null),e!==s&&fy()!==s&&(n=zp())!==s?(Sc=r,r=t=function(r,t,e){return{type:r.toLowerCase(),keyword:r.toLowerCase(),symbol:t,value:e}}(t,e,n)):(_c=r,r=s)):(_c=r,r=s),r}function yy(){var t;return r.length>_c?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(uc)),t}function dy(){var t;return(t=function(){var t,e,n,o;return t=_c,"year"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ps)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="YEAR"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n,o;return t=_c,"month"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Rs)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MONTH"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n,o;return t=_c,"day"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(Ls)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="DAY"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n,o;return t=_c,"hour"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(gs)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="HOUR"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n,o;return t=_c,"minute"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Is)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MINUTE"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(t=function(){var t,e,n,o;return t=_c,"second"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Os)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="SECOND"):(_c=t,t=s)):(_c=t,t=s),t}()),t}function wy(){var t;return ac.test(r.charAt(_c))?(t=r.charAt(_c),_c++):(t=s,0===Rc&&Mc(ic)),t}function Ly(){var t,e;if((t=function(){var t,e;t=_c,Rc++,r.length>_c?(e=r.charAt(_c),_c++):(e=s,0===Rc&&Mc(uc));Rc--,e===s?t=void 0:(_c=t,t=s);return t}())===s)if(t=[],ou.test(r.charAt(_c))?(e=r.charAt(_c),_c++):(e=s,0===Rc&&Mc(su)),e!==s)for(;e!==s;)t.push(e),ou.test(r.charAt(_c))?(e=r.charAt(_c),_c++):(e=s,0===Rc&&Mc(su));else t=s;return t}function hy(){var r,t;return r=_c,Sc=_c,Wy=[],(!0?void 0:s)!==s&&fy()!==s?((t=Cy())===s&&(t=my()),t!==s?(Sc=r,r={type:"proc",stmt:t,vars:Wy}):(_c=r,r=s)):(_c=r,r=s),r}function Cy(){var t,e,n,o,u,a;return(t=function(){var t,e,n,o,u;return t=_c,Rv()!==s&&fy()!==s?("zone"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ln)),e!==s&&fy()!==s&&(n=Ff())!==s&&fy()!==s&&(o=bb())!==s&&fy()!==s&&(u=dy())!==s?(Sc=t,t={type:"assign",left:{type:"expr_list",value:[{type:"origin",value:"time zone"},n],separator:" "},symbol:"to",right:{type:"origin",value:u}}):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,Rv()!==s&&fy()!==s?("zone"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ln)),e!==s&&fy()!==s?((n=bb())===s&&(n=null),n!==s&&fy()!==s?((o=eb())===s&&(o=zp())===s&&(o=Pv())===s&&("default"===r.substr(_c,7).toLowerCase()?(o=r.substr(_c,7),_c+=7):(o=s,0===Rc&&Mc(cc))),o!==s?(Sc=t,t=function(r,t){return{type:"assign",left:{type:"origin",value:"time zone"},symbol:r?"to":null,right:"string"==typeof t?{type:"origin",value:t}:t}}(n,o)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)):(_c=t,t=s)),t}())===s&&(t=_c,(e=jy())===s&&(e=Iy()),e!==s&&fy()!==s?((n=function(){var t;return":="===r.substr(_c,2)?(t=":=",_c+=2):(t=s,0===Rc&&Mc(x)),t}())===s&&(n=qv())===s&&(n=bb()),n!==s&&fy()!==s&&(o=Ey())!==s?(Sc=t,u=n,a=o,t=e={type:"assign",left:e,symbol:Array.isArray(u)?u[0]:u,right:a}):(_c=t,t=s)):(_c=t,t=s)),t}function my(){var t,e;return t=_c,function(){var t,e,n,o;return t=_c,"return"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Nu)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="RETURN"):(_c=t,t=s)):(_c=t,t=s),t}()!==s&&fy()!==s&&(e=Ey())!==s?(Sc=t,t={type:"return",expr:e}):(_c=t,t=s),t}function Ey(){var r;return(r=Hl())===s&&(r=function(){var r,t,e,n,o;r=_c,(t=jy())!==s&&fy()!==s&&(e=yf())!==s&&fy()!==s&&(n=jy())!==s&&fy()!==s&&(o=Lf())!==s?(Sc=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(_c=r,r=s);return r}())===s&&(r=Ay())===s&&(r=function(){var r,t;r=_c,sy()!==s&&fy()!==s&&(t=xy())!==s&&fy()!==s&&uy()!==s?(Sc=r,r={type:"array",value:t}):(_c=r,r=s);return r}()),r}function Ay(){var r,t,e,n,o,u,a,i;if(r=_c,(t=gy())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=tp())!==s&&(a=fy())!==s&&(i=gy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=tp())!==s&&(a=fy())!==s&&(i=gy())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Gn(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function gy(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Ty())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=np())!==s&&(a=fy())!==s&&(i=Ty())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=np())!==s&&(a=fy())!==s&&(i=Ty())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Gn(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function Ty(){var r,t,e,n,o,u,a,i,c;return(r=Wp())===s&&(r=jy())===s&&(r=Sy())===s&&(r=jp())===s&&(r=_c,(t=ny())!==s&&(e=fy())!==s&&(n=Ay())!==s&&(o=fy())!==s&&(u=oy())!==s?(Sc=r,(c=n).parentheses=!0,r=t=c):(_c=r,r=s),r===s&&(r=_c,(t=Tp())!==s?(e=_c,(n=ry())!==s&&(o=fy())!==s&&(u=Tp())!==s?e=n=[n,o,u]:(_c=e,e=s),e===s&&(e=null),e!==s?(Sc=r,a=t,r=t=(i=e)?{type:"column_ref",table:a,column:i[2]}:{type:"var",name:a,prefix:null}):(_c=r,r=s)):(_c=r,r=s))),r}function _y(){var r,t,e,n,o,u,a;return r=_c,(t=fp())!==s?(e=_c,(n=fy())!==s&&(o=ry())!==s&&(u=fy())!==s&&(a=fp())!==s?e=n=[n,o,u,a]:(_c=e,e=s),e===s&&(e=null),e!==s?(Sc=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(_c=r,r=s)):(_c=r,r=s),r}function Sy(){var r,t,e;return r=_c,(t=_y())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((e=xy())===s&&(e=null),e!==s&&fy()!==s&&oy()!==s?(Sc=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...Dy()}):(_c=r,r=s)):(_c=r,r=s),r}function xy(){var r,t,e,n,o,u,a,i;if(r=_c,(t=Ty())!==s){for(e=[],n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ty())!==s?n=o=[o,u,a,i]:(_c=n,n=s);n!==s;)e.push(n),n=_c,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Ty())!==s?n=o=[o,u,a,i]:(_c=n,n=s);e!==s?(Sc=r,r=t=Fy(t,e)):(_c=r,r=s)}else _c=r,r=s;return r}function jy(){var t,e,n,o,u,a,i;if(t=_c,(e=Fv())!==s){for(n=[],lc.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(fc));o!==s;)n.push(o),lc.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(fc));n!==s&&(o=Fv())!==s?(Sc=t,t=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(_c=t,t=s)}else _c=t,t=s;if(t===s){if(t=_c,(e=$v())!==s)if((n=Ap())!==s)if((o=$v())!==s){for(u=[],lc.test(r.charAt(_c))?(a=r.charAt(_c),_c++):(a=s,0===Rc&&Mc(fc));a!==s;)u.push(a),lc.test(r.charAt(_c))?(a=r.charAt(_c),_c++):(a=s,0===Rc&&Mc(fc));u!==s&&(a=$v())!==s&&(i=Ap())!==s?(Sc=_c,(function(r,t,e){if(r!==e)return!0}(n,0,i)?s:void 0)!==s&&$v()!==s?(Sc=t,t=e=function(r,t,e){return{type:"var",name:t.join(""),prefix:`$${r}$`,suffix:`$${e}$`}}(n,u,i)):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;t===s&&(t=_c,(e=Bv())!==s&&(n=Iy())!==s?(Sc=t,t=e=function(r,t){return{type:"var",...t,prefix:r}}(e,n)):(_c=t,t=s))}return t}function Iy(){var t,e,n,o,u;return t=_c,34===r.charCodeAt(_c)?(e='"',_c++):(e=s,0===Rc&&Mc(Lo)),e===s&&(e=null),e!==s&&(n=Tp())!==s&&(o=function(){var t,e,n,o,u;t=_c,e=[],n=_c,46===r.charCodeAt(_c)?(o=".",_c++):(o=s,0===Rc&&Mc(uu));o!==s&&(u=Tp())!==s?n=o=[o,u]:(_c=n,n=s);for(;n!==s;)e.push(n),n=_c,46===r.charCodeAt(_c)?(o=".",_c++):(o=s,0===Rc&&Mc(uu)),o!==s&&(u=Tp())!==s?n=o=[o,u]:(_c=n,n=s);e!==s&&(Sc=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==s?(34===r.charCodeAt(_c)?(u='"',_c++):(u=s,0===Rc&&Mc(Lo)),u===s&&(u=null),u!==s?(Sc=t,t=e=function(r,t,e,n){if(r&&!n||!r&&n)throw new Error("double quoted not match");return Wy.push(t),{type:"var",name:t,members:e,quoted:r&&n?'"':null,prefix:null}}(e,n,o,u)):(_c=t,t=s)):(_c=t,t=s),t===s&&(t=_c,(e=eb())!==s&&(Sc=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Ry(){var t;return(t=function(){var r,t,e;r=_c,(t=Uy())===s&&(t=Oy());t!==s&&fy()!==s&&sy()!==s&&fy()!==s&&(e=uy())!==s&&fy()!==s&&sy()!==s&&fy()!==s&&uy()!==s?(Sc=r,n=t,t={...n,array:{dimension:2}},r=t):(_c=r,r=s);var n;r===s&&(r=_c,(t=Uy())===s&&(t=Oy()),t!==s&&fy()!==s&&sy()!==s&&fy()!==s?((e=eb())===s&&(e=null),e!==s&&fy()!==s&&uy()!==s?(Sc=r,t=function(r,t){return{...r,array:{dimension:1,length:[t]}}}(t,e),r=t):(_c=r,r=s)):(_c=r,r=s),r===s&&(r=_c,(t=Uy())===s&&(t=Oy()),t!==s&&fy()!==s&&uv()!==s?(Sc=r,t=function(r){return{...r,array:{keyword:"array"}}}(t),r=t):(_c=r,r=s)));return r}())===s&&(t=Oy())===s&&(t=Uy())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=xv())===s&&(e=jv());if(e!==s){if(n=_c,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(_c=n,n=s)}else _c=n,n=s;else _c=n,n=s;else _c=n,n=s;n===s&&(n=null),n!==s?(Sc=t,e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3].join(""),10),e.parentheses=!0),e}(e,n),t=e):(_c=t,t=s)}else _c=t,t=s;t===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=Rv())===s&&(e=Nv())===s&&(e=function(){var t,e,n,o;return t=_c,"timestamptz"===r.substr(_c,11).toLowerCase()?(e=r.substr(_c,11),_c+=11):(e=s,0===Rc&&Mc(ii)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TIMESTAMPTZ"):(_c=t,t=s)):(_c=t,t=s),t}());if(e!==s){if(n=_c,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(_c=n,n=s)}else _c=n,n=s;else _c=n,n=s;else _c=n,n=s;n===s&&(n=null),n!==s&&(o=fy())!==s?((u=function(){var t,e,n;t=_c,"without"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(dc));e===s&&("with"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Tn)));e!==s&&fy()!==s&&Rv()!==s&&fy()!==s?("zone"===r.substr(_c,4).toLowerCase()?(n=r.substr(_c,4),_c+=4):(n=s,0===Rc&&Mc(Ln)),n!==s?(Sc=t,e=[e.toUpperCase(),"TIME","ZONE"],t=e):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(u=null),u!==s?(Sc=t,e=function(r,t,e){const n={dataType:r};return t&&(n.length=parseInt(t[3].join(""),10),n.parentheses=!0),e&&(n.suffix=e),n}(e,n,u),t=e):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;return t}());return t}())===s&&(t=function(){var t,e;t=_c,(e=function(){var t,e,n,o;return t=_c,"json"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ba)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="JSON"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"jsonb"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Ha)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="JSONB"):(_c=t,t=s)):(_c=t,t=s),t}());e!==s&&(Sc=t,e=wc(e));return t=e}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=_c,(e=function(){var t,e,n,o;return t=_c,"geometry"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(qa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="GEOMETRY"):(_c=t,t=s)):(_c=t,t=s),t}())!==s?(n=_c,(o=fy())!==s&&(u=ny())!==s&&(a=fy())!==s&&(i=function(){var t,e,n,o,u,a,i;t=_c,"point"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(Lc));e===s&&("linestring"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(hc)),e===s&&("polygon"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Cc)),e===s&&("multipoint"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(mc)),e===s&&("multilinestring"===r.substr(_c,15).toLowerCase()?(e=r.substr(_c,15),_c+=15):(e=s,0===Rc&&Mc(Ec)),e===s&&("multipolygon"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(Ac)),e===s&&("geometrycollection"===r.substr(_c,18).toLowerCase()?(e=r.substr(_c,18),_c+=18):(e=s,0===Rc&&Mc(gc))))))));if(e!==s)if(fy()!==s){if(n=_c,(o=ty())!==s)if((u=fy())!==s){if(a=[],au.test(r.charAt(_c))?(i=r.charAt(_c),_c++):(i=s,0===Rc&&Mc(iu)),i!==s)for(;i!==s;)a.push(i),au.test(r.charAt(_c))?(i=r.charAt(_c),_c++):(i=s,0===Rc&&Mc(iu));else a=s;a!==s?n=o=[o,u,a]:(_c=n,n=s)}else _c=n,n=s;else _c=n,n=s;n===s&&(n=null),n!==s?(Sc=t,e={length:e,scale:(c=n)&&c[2]&&parseInt(c[2].join(""),10)},t=e):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s;var c;return t}())!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=t,e={dataType:e,...(f=n)&&f[3]||{},parentheses:!!f},t=e):(_c=t,t=s)):(_c=t,t=s);var f;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=_c,(e=function(){var t,e,n,o;return t=_c,"tinytext"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(Xa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TINYTEXT"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"text"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Qa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="TEXT"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"mediumtext"===r.substr(_c,10).toLowerCase()?(e=r.substr(_c,10),_c+=10):(e=s,0===Rc&&Mc(Ka)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="MEDIUMTEXT"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"longtext"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(za)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="LONGTEXT"):(_c=t,t=s)):(_c=t,t=s),t}());e!==s?(n=_c,(o=sy())!==s&&(u=fy())!==s&&(a=uy())!==s?n=o=[o,u,a]:(_c=n,n=s),n===s&&(n=null),n!==s?(Sc=t,t=e={dataType:`${e}${n?"[]":""}`}):(_c=t,t=s)):(_c=t,t=s);return t}())===s&&(t=function(){var t,e;t=_c,(e=function(){var t,e,n,o;return t=_c,"uuid"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(fi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="UUID"):(_c=t,t=s)):(_c=t,t=s),t}())!==s&&(Sc=t,e={dataType:e});return t=e}())===s&&(t=function(){var t,e;t=_c,(e=function(){var t,e,n,o;return t=_c,"bool"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Ra)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="BOOL"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"boolean"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Na)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="BOOLEAN"):(_c=t,t=s)):(_c=t,t=s),t}());e!==s&&(Sc=t,e=pc(e));return t=e}())===s&&(t=function(){var r,t,e;r=_c,(t=Av())!==s&&fy()!==s&&(e=Gf())!==s?(Sc=r,n=t,(o=e).parentheses=!0,r=t={dataType:n,expr:o}):(_c=r,r=s);var n,o;return r}())===s&&(t=function(){var r,t;r=_c,(t=hv())===s&&(t=kv());t!==s&&(Sc=r,t=wc(t));return r=t}())===s&&(t=function(){var t,e;t=_c,"bytea"===r.substr(_c,5).toLowerCase()?(e=r.substr(_c,5),_c+=5):(e=s,0===Rc&&Mc(bc));e!==s&&(Sc=t,e={dataType:"BYTEA"});return t=e}())===s&&(t=function(){var t,e;t=_c,(e=function(){var t,e,n,o;return t=_c,"oid"===r.substr(_c,3).toLowerCase()?(e=r.substr(_c,3),_c+=3):(e=s,0===Rc&&Mc(pi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="OID"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regclass"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc(bi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGCLASS"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regcollation"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(vi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGCOLLATION"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regconfig"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(yi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGCONFIG"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regdictionary"===r.substr(_c,13).toLowerCase()?(e=r.substr(_c,13),_c+=13):(e=s,0===Rc&&Mc(di)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGDICTIONARY"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regnamespace"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(wi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGNAMESPACE"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regoper"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Li)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGOPER"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regoperator"===r.substr(_c,11).toLowerCase()?(e=r.substr(_c,11),_c+=11):(e=s,0===Rc&&Mc(hi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGOPERATOR"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regproc"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ci)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGPROC"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regprocedure"===r.substr(_c,12).toLowerCase()?(e=r.substr(_c,12),_c+=12):(e=s,0===Rc&&Mc(mi)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGPROCEDURE"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regrole"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ei)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGROLE"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"regtype"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(Ai)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="REGTYPE"):(_c=t,t=s)):(_c=t,t=s),t}());e!==s&&(Sc=t,e=pc(e));return t=e}())===s&&(t=function(){var t,e;t=_c,"record"===r.substr(_c,6).toLowerCase()?(e=r.substr(_c,6),_c+=6):(e=s,0===Rc&&Mc(Tc));e!==s&&(Sc=t,e={dataType:"RECORD"});return t=e}())===s&&(t=function(){var r,t;r=_c,(t=Tp())!==s?(Sc=_c,e=t,(Qy.has(e)?void 0:s)!==s?(Sc=r,t=function(r){return{dataType:r}}(t),r=t):(_c=r,r=s)):(_c=r,r=s);var e;return r}()),t}function Ny(){var t,e;return t=_c,function(){var t,e,n,o;return t=_c,"character"===r.substr(_c,9).toLowerCase()?(e=r.substr(_c,9),_c+=9):(e=s,0===Rc&&Mc(Nt)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CHARACTER"):(_c=t,t=s)):(_c=t,t=s),t}()!==s&&fy()!==s?("varying"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(vc)),e===s&&(e=null),e!==s?(Sc=t,t="CHARACTER VARYING"):(_c=t,t=s)):(_c=t,t=s),t}function Oy(){var t,e,n,o,u,a,i,c,l;if(t=_c,(e=function(){var t,e,n,o;return t=_c,"char"===r.substr(_c,4).toLowerCase()?(e=r.substr(_c,4),_c+=4):(e=s,0===Rc&&Mc(Oa)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="CHAR"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=_c,"varchar"===r.substr(_c,7).toLowerCase()?(e=r.substr(_c,7),_c+=7):(e=s,0===Rc&&Mc(ka)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="VARCHAR"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(e=Ny()),e!==s){if(n=_c,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(r.charAt(_c))?(c=r.charAt(_c),_c++):(c=s,0===Rc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(_c=n,n=s)}else _c=n,n=s;else _c=n,n=s;else _c=n,n=s;n===s&&(n=null),n!==s?(Sc=t,t=e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3].join(""),10),e.parentheses=!0),e}(e,n)):(_c=t,t=s)}else _c=t,t=s;return t}function ky(){var t,e,n;return t=_c,(e=yv())===s&&(e=null),e!==s&&fy()!==s?((n=function(){var t,e,n,o;return t=_c,"zerofill"===r.substr(_c,8).toLowerCase()?(e=r.substr(_c,8),_c+=8):(e=s,0===Rc&&Mc($a)),e!==s?(n=_c,Rc++,o=_p(),Rc--,o===s?n=void 0:(_c=n,n=s),n!==s?(Sc=t,t=e="ZEROFILL"):(_c=t,t=s)):(_c=t,t=s),t}())===s&&(n=null),n!==s?(Sc=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(_c=t,t=s)):(_c=t,t=s),t}function Uy(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w;if(t=_c,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=mv())===s&&(e=Ev())===s&&(e=gv())===s&&(e=_c,(n=Tv())!==s&&(o=fy())!==s?("precision"===r.substr(_c,9).toLowerCase()?(u=r.substr(_c,9),_c+=9):(u=s,0===Rc&&Mc(yc)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=Tv())===s&&(e=hv())===s&&(e=_v())===s&&(e=Sv())),e!==s)if((n=fy())!==s)if((o=ny())!==s)if((u=fy())!==s){if(a=[],au.test(r.charAt(_c))?(i=r.charAt(_c),_c++):(i=s,0===Rc&&Mc(iu)),i!==s)for(;i!==s;)a.push(i),au.test(r.charAt(_c))?(i=r.charAt(_c),_c++):(i=s,0===Rc&&Mc(iu));else a=s;if(a!==s)if((i=fy())!==s){if(c=_c,(l=ty())!==s)if((f=fy())!==s){if(p=[],au.test(r.charAt(_c))?(b=r.charAt(_c),_c++):(b=s,0===Rc&&Mc(iu)),b!==s)for(;b!==s;)p.push(b),au.test(r.charAt(_c))?(b=r.charAt(_c),_c++):(b=s,0===Rc&&Mc(iu));else p=s;p!==s?c=l=[l,f,p]:(_c=c,c=s)}else _c=c,c=s;else _c=c,c=s;c===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s&&(p=fy())!==s?((b=ky())===s&&(b=null),b!==s?(Sc=t,v=e,y=a,d=c,w=b,t=e={dataType:Array.isArray(v)?`${v[0].toUpperCase()} ${v[2].toUpperCase()}`:v,length:parseInt(y.join(""),10),scale:d&&parseInt(d[2].join(""),10),parentheses:!0,suffix:w}):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;else _c=t,t=s}else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;else _c=t,t=s;if(t===s){if(t=_c,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=mv())===s&&(e=Ev())===s&&(e=gv())===s&&(e=_c,(n=Tv())!==s&&(o=fy())!==s?("precision"===r.substr(_c,9).toLowerCase()?(u=r.substr(_c,9),_c+=9):(u=s,0===Rc&&Mc(yc)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=Tv())===s&&(e=hv())===s&&(e=_v())===s&&(e=Sv())),e!==s){if(n=[],au.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(iu)),o!==s)for(;o!==s;)n.push(o),au.test(r.charAt(_c))?(o=r.charAt(_c),_c++):(o=s,0===Rc&&Mc(iu));else n=s;n!==s&&(o=fy())!==s?((u=ky())===s&&(u=null),u!==s?(Sc=t,t=e=function(r,t,e){return{dataType:Array.isArray(r)?`${r[0].toUpperCase()} ${r[2].toUpperCase()}`:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,u)):(_c=t,t=s)):(_c=t,t=s)}else _c=t,t=s;t===s&&(t=_c,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=mv())===s&&(e=Ev())===s&&(e=gv())===s&&(e=_c,(n=Tv())!==s&&(o=fy())!==s?("precision"===r.substr(_c,9).toLowerCase()?(u=r.substr(_c,9),_c+=9):(u=s,0===Rc&&Mc(yc)),u!==s?e=n=[n,o,u]:(_c=e,e=s)):(_c=e,e=s),e===s&&(e=Tv())===s&&(e=hv())===s&&(e=_v())===s&&(e=Sv())),e!==s&&(n=fy())!==s?((o=ky())===s&&(o=null),o!==s&&(u=fy())!==s?(Sc=t,t=e=function(r,t){return{dataType:Array.isArray(r)?`${r[0].toUpperCase()} ${r[2].toUpperCase()}`:r,suffix:t}}(e,o)):(_c=t,t=s)):(_c=t,t=s))}return t}const My={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,AT:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CONSTRAINT:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,ILIKE:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,PARTITION:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function Dy(){return t.includeLocations?{loc:Uc(Sc,_c)}:{}}function Py(r,t){return{type:"unary_expr",operator:r,expr:t}}function Gy(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e,...Dy()}}function $y(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Fy(r,t,e=3){const n=Array.isArray(r)?r:[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function By(r,t){let e=r;for(let r=0;r<t.length;r++)e=Gy(t[r][1],e,t[r][3]);return e}function Hy(r){const t=Ky[r];return t||(r||null)}function qy(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Hy(r[1])),t.add(r.join("::"))}return Array.from(t)}function Yy(r){return"string"==typeof r?{type:"same",value:r}:r}let Wy=[];const Vy=new Set,Xy=new Set,Qy=new Set,Ky={};if((e=a())!==s&&_c===r.length)return e;throw e!==s&&_c<r.length&&Mc({type:"end"}),Dc(Ic,jc<r.length?r.charAt(jc):null,jc<r.length?Uc(jc,jc+1):Uc(jc,jc))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return j})),e.d(n,"columnOrderListToSQL",(function(){return I})),e.d(n,"commonKeywordArgsToSQL",(function(){return x})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return C})),e.d(n,"commentToSQL",(function(){return T})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return s})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return h})),e.d(n,"columnIdentifierToSql",(function(){return y})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return d})),e.d(n,"onPartitionsToSQL",(function(){return E})),e.d(n,"replaceParams",(function(){return m})),e.d(n,"returningToSQL",(function(){return S})),e.d(n,"hasVal",(function(){return L})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return w})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return _}));var s={database:"postgresql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return u}function b(r){u=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function d(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function w(r){if(r)return r.toUpperCase()}function L(r){return r}function h(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,s=r.suffix,u=r.value,a="object"===o(r)?u:r;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'");break;case"unicode_string":a="U&'".concat(u,"'")}var i=[];return t&&i.push(w(t)),i.push(a),s&&("string"==typeof s&&i.push(s),"object"===o(s)&&(s.collate?i.push(it(s.collate)):i.push(h(s)))),a=i.join(" "),n?"(".concat(a,")"):a}}function C(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():h(n)].filter(L)}function m(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var s=t[n];if("object"!==o(s)||"param"!==s.type)return r(s,e);if(void 0===e[s.value])throw new Error("no value for parameter :".concat(s.value," found"));return t[n]=c(e[s.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function E(r){var t=r.type,e=r.partitions;return[w(t),"(".concat(e.map((function(r){if("range"!==r.type)return h(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(h(t)," ").concat(w(n)," ").concat(h(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,s=r.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),s&&s.length&&(u+=" ".concat(s.join(" "))),"".concat(t).concat(u)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=w(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var s=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(L).join(" ")})).join(", ");return n?"".concat(o,"<").concat(s,">"):"".concat(o," ").concat(s)}}function T(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(h(o)),t.join(" ")}}function _(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[w(t)];if(e){var o=e.keyword,s=e.columns;n.push(w(o),s.map(yt).join(", "))}return n.join(" ")})).join(" OR ")}function S(r){return r?["RETURNING",r.columns.map(Et).filter(L).join(", ")].join(" "):""}function x(r){return r?[w(r.keyword),w(r.args)]:[]}function j(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,s=r.parentheses,u=w(e);return s&&(u+="(".concat(h(n),", ").concat(h(o),")")),u}}function I(r){if(r)return r.map(ht).filter(L).join(", ")}function R(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function O(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),w(e)]}function k(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),s=[];switch(s.push(o),o){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(h(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,R(O(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(T(r));break;case"DATA_COMPRESSION":s.push(n,w(e.value),E(e.on));break;default:s.push(n,h(e))}return s.filter(L).join(" ")}}function U(r){return r?r.map(k):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,s=r.definition,u=r.on,a=r.with,i=[];if(i.push.apply(i,R(O(e))),s&&s.length){var c="CHECK"===w(t)?"(".concat(st(s[0]),")"):"(".concat(s.map((function(r){return st(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),u&&i.push("ON [".concat(u,"]")),i}function D(r){var t=r.operator||r.op,e=st(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},s=[Array.isArray(r.left)?r.left.map(st).join(", "):st(r.left),t,e,w(o.type),st(o.value)].filter(L).join(" ");return[r.parentheses?"(".concat(s,")"):s].join(" ")}function P(r){return function(r){if(Array.isArray(r))return G(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function $(r){return r?[r.prefix.map(h).join(" "),st(r.value),r.suffix.map(h).join(" ")]:[]}function F(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P($(n)),P($(e))).filter(L).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",st(e[0])):i("LIMIT",e.map(st).join("".concat("offset"===t?" ":"").concat(w(t)," ")))}(r):"";var t,e,n}function B(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(yt).join(", "),")"):"";return"".concat("default"===t.type?d(t.value):h(t)).concat(o," AS (").concat(st(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function H(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=w(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?d(e):st(e))}return n.filter(L).join(" ")}}function q(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,s=r.for,u=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,y=r.into,d=void 0===y?{}:y,C=r.isolation,m=r.limit,E=r.options,A=r.orderby,g=r.parentheses_symbol,T=r.qualify,_=r.top,S=r.window,x=r.with,j=r.where,I=[B(x),"SELECT",w(t)];Array.isArray(E)&&I.push(E.join(" ")),I.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[w(t)];return e&&n.push("(".concat(e.map(st).join(", "),")")),n.filter(L).join(" ")}}(o),v(_),gt(e,u));var R=d.position,N="";R&&(N=a("INTO",H,d)),"column"===R&&I.push(N),I.push(a("FROM",cr,u)),"from"===R&&I.push(N);var O=l||{},k=O.keyword,U=O.expr;I.push(a(k,st,U)),I.push(a("WHERE",st,j)),p&&(I.push(i("GROUP BY",ut(p.columns).join(", "))),I.push(ut(p.modifiers).join(", "))),I.push(a("HAVING",st,b)),I.push(a("QUALIFY",st,T)),I.push(a("WINDOW",st,S)),I.push(at(A,"order by")),I.push(it(n)),I.push(F(m)),C&&I.push(a(C.keyword,h,C.expr)),I.push(w(f)),"end"===R&&I.push(N),I.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[w(r.type),w(e)];return t?"".concat(n.join(" "),"(").concat(st(t),")"):n.join(" ")}}(s));var M=I.filter(L).join(" ");return g?"(".concat(M,")"):M}function Y(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return W(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?W(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function W(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=Y(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s={},u=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(s[a]=o[a]);var i=[yt(s)],c="";u&&(c=st(u),i.push("=",c)),e.push(i.filter(L).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return q(r);var t=r.map(st);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(d).join(", "));else{var e=r.value;t.push(e.map(st).join(", "))}return t.push(")"),t.filter(L).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(yt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[w(e)];switch(n){case"origin":o.push(h(t));break;case"update":o.push("UPDATE",a("SET",V,t.set),a("WHERE",st,t.where))}return o.filter(L).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(L).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,s=r.columns,u=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},y=v.keyword,d=v.set,C=[w(e),w(o),cr(t),Q(f)];return Array.isArray(s)&&C.push("(".concat(s.map(h).join(", "),")")),C.push(a(Array.isArray(i)?"VALUES":"",X,i)),C.push(a("ON CONFLICT",Z,u)),C.push(a("SET",V,b)),C.push(a("WHERE",st,c)),C.push(a(y,V,d)),C.push(S(p)),C.filter(L).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",st(t),w(e),st(n)].filter(L).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(w(t),"(").concat(n&&st(n)||"",")"),a("AS","string"==typeof e?d:st,e),a(w(o&&o.keyword),d,o&&o.as)].filter(L).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,s=r.type,u=[st(n),"FOR",yt(e),D(o)],a=["".concat(w(s),"(").concat(u.join(" "),")")];return t&&a.push("AS",d(t)),a.join(" ")}(r);default:return""}}function sr(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,s=r.parentheses,u=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(w(t),"(".concat(d(n)),"(".concat(o.map(st).filter(L).join(", "),"))"));break;case"spatial_window_max_cells":a.push(w(t),"=",st(e));break;case"index":a.push(w(u),w(t),s?"(".concat(e.map(d).join(", "),")"):"= ".concat(d(e)));break;default:a.push(st(e))}return a.filter(L).join(" ")}}function ur(r,t){var e=r.name,n=r.symbol;return[w(e),n,t].filter(L).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",st(r.of));break;case"from_to":t.push("FROM",st(r.from),"TO",st(r.to));break;case"between_and":t.push("BETWEEN",st(r.between),"AND",st(r.and));break;case"contained":t.push("CONTAINED","IN",st(r.in))}return t.filter(L).join(" ")}function ir(r){if("UNNEST"===w(r.type))return nr(r);var t,e,n,o,s=r.table,u=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,y=r.tablesample,m=r.temporal_table,E=r.table_hint,A=d(b),g=d(u),T=d(p),_=s&&d(s);if(c)switch(c.type){case"values":var S=c.parentheses,x=c.values,j=c.prefix,I=[S&&"(","",S&&")"],R=X(x);j&&(R=R.split("(").slice(1).map((function(r){return"".concat(w(j),"(").concat(r)})).join("")),I[1]="VALUES ".concat(R),_=I.filter(L).join("");break;case"tumble":_=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,s=[d(t.expr.db),d(t.expr.schema),d(t.expr.table)].filter(L).join("."),u="DESCRIPTOR(".concat(yt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(ur(t,s)),ur(e,u)],i=ur(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(ur(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(L).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return C(r).join(" ")})).join(", "),_="".concat(w(e),"(").concat(w(n),"(").concat(o,"))");break;default:_=st(c)}var N=[[A,g,T,_=[w(f),_,w(v)].filter(L).join(" ")].filter(L).join(".")];if(y){var O=["TABLESAMPLE",st(y.expr),h(y.repeatable)].filter(L).join(" ");N.push(O)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[w(t),ar(e)].filter(L).join(" ")}}(m),a("AS","string"==typeof i?d:st,i),or(l)),E&&N.push(w(E.keyword),"(".concat(E.expr.map(sr).filter(L).join(", "),")"));var k=N.filter(L).join(" ");return r.parentheses?"(".concat(k,")"):k}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var s=[],u=[],i=!0===e?1:e.length,c=0;c++<i;)s.push("("),u.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return s.join("")+o+u.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],y=v.on,d=v.using,C=v.join,m=[];m.push(C?" ".concat(w(C)):","),m.push(ir(v)),m.push(a("ON",st,y)),d&&m.push("USING (".concat(d.map(h).join(", "),")")),p.push(m.filter(L).join(" "))}return p.filter(L).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var s=h(n);switch(t){case"partition by":case"default collate":s=st(n);break;case"options":s="(".concat(n.map((function(r){return[r.keyword,r.symbol,st(r.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(st).join(", ")}return o.push(s),o.filter(L).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[d(t.db),d(t.table)].filter(L).join(".");return"".concat(w(e)," ").concat(n);case"column":return"COLUMN ".concat(yt(t));default:return"".concat(w(e)," ").concat(h(t))}}function pr(r){var t=r.keyword,e=r.expr;return[w(t),h(e)].filter(L).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",st(e)].filter(L).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var s=st(t),u=st(e);return[s,w(n),u].filter(L).join(" ")}function yr(r){var t,e,n,o,s=r.keyword,u=r.suffix,i="";switch(w(s)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",h,e&&e.right),a("FROM",cr,n),F(o)].filter(L).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===w(t.op)?a("LIKE",h,t.right):a("WHERE",st,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,s="'".concat(e,"'");return n&&(s+="@'".concat(n,"'")),["FOR",s,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(L).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[u]);break;case"VAR":i=ot(r.var),s=""}return["SHOW",w(s),w(u),i].filter(L).join(" ")}var dr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,s=r.type,u=t.expr,a=t.orderby;return[w(s),w(n),[[d(o.schema),d(o.name)].filter(L).join("."),"(".concat(u.map(Zr).join(", ")).concat(a?[" ORDER","BY",a.map(Zr).join(", ")].join(" "):"",")")].filter(L).join(""),zr(e)].filter(L).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,s=r.expr,u=void 0===s?[]:s,a=w(t),i=cr(e),c=u.map(st);return[a,"TABLE",w(n),h(o),i,c.join(", ")].filter(L).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[w(r.type),w(e),d(n),zr(t)].filter(L).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[w(r.type),w(e),[d(n.schema),d(n.name)].filter(L).join("."),zr(t)].filter(L).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[w(r.type),w(n),[[d(o.schema),d(o.name)].filter(L).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(L).join(""),zr(e)].filter(L).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,s=r.view,u=r.with,a=w(t),i=ir(s),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(yt).join(", "),")"));n&&c.push("WITH ".concat(n.map(w).join(", ")));c.push("AS",q(o)),u&&c.push(w(u));return c.filter(L).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[w(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,s=r.schema;return[w(t),w(e),st(n),w(o),d(s)].filter(L).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.options,a=[w(t),w(e),w(n)],i=[d(o.schema),o.name].filter(L).join("."),c="".concat(s.expr.map(Zr).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(Zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(u.map(Qr).join(", "),")")),a.filter(L).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,s=r.as,u=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,y=r.unlogged,d=r.with,C=[w(t),w(f),w(u),w(y),w(e),w(a),cr(n)];if(o){var m=o.type,E=cr(o.table);return C.push(w(m),E),C.filter(L).join(" ")}if(b)return C.concat([Wr(b)]).filter(L).join(" ");i&&C.push("(".concat(i.map(qr).join(", "),")"));if(c){var A=p().database,g=A&&"sqlite"===A.toLowerCase()?", ":" ";C.push(c.map(lr).join(g))}if(d){var T=d.map((function(r){return[h(r.keyword),w(r.symbol),h(r.value)].join(" ")})).join(", ");C.push("WITH (".concat(T,")"))}C.push(w(l),w(s)),v&&C.push(wr(v));return C.filter(L).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,s=r.execute,u=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[w(f),w(l),w(e),w(c),d(t),w(i)],y=_(o);v.push(y,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Fr(x(n)).concat(Fr(x(u)))),b&&v.push(w(b.type),st(b.cond));return v.push(w(s.keyword),$r(s.expr)),v.filter(L).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,s=r.type,u=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,y=[w(s),w(c),st(t),w(n),w(i),ir(l),w(b),f.map((function(r){var t=[w(r.keyword)],e=r.args;return e&&t.push(w(e.keyword),e.columns.map(yt).join(", ")),t.join(" ")})),"ON",ir(u),w(e&&e.keyword),w(e&&e.args),p&&"".concat(w(p.keyword)," ").concat(d(p.trigger)),a("WHEN",st,v),w(o.prefix)];switch(o.type){case"set":y.push(a("SET",V,o.expr));break;case"multiple":y.push(Lr(o.expr.ast))}return y.push(w(o.suffix)),y.filter(L).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,s=r.schema,u=r.type,i=r.with,c=r.version;return[w(u),w(o),w(n),h(t),w(i),a("SCHEMA",h,s),a("VERSION",h,c),a("FROM",h,e)].filter(L).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.returns,a=r.options,i=r.last,c=[w(t),w(e),w(n)],l=[h(o.schema),o.name.map(h).join(".")].filter(L).join("."),f=s.map(Zr).filter(L).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),Array.isArray(n)?"(".concat(n.map(Ct).join(", "),")"):Vr(n)].filter(L).join(" ")}(u),a.map(Xr).join(" "),i),c.filter(L).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,s=r.include,u=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,y=r.on_kw,C=r.table,m=r.tablespace,E=r.type,A=r.where,g=r.with,T=r.with_before_where,_=g&&"WITH (".concat(U(g).join(", "),")"),S=s&&"".concat(w(s.keyword)," (").concat(s.columns.map((function(r){return"string"==typeof r?d(r):st(r)})).join(", "),")"),x=l;l&&(x="string"==typeof l?d(l):[d(l.schema),d(l.name)].filter(L).join("."));var j=[w(E),w(i),w(n),w(o),w(t),x,w(y),ir(C)].concat(Fr(O(c)),["(".concat(I(u),")"),S,U(p).join(" "),zr(b),zr(v),a("TABLESPACE",h,m)]);T?j.push(_,a("WHERE",st,A)):j.push(a("WHERE",st,A),_);return j.push(a("ON",st,f),a("FILESTREAM_ON",h,e)),j.filter(L).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,s=r.if_not_exists,u=r.create_definitions,a=[w(t),w(o),w(e),w(s),cr(n)];u&&a.push(u.map(qr).join(" "));return a.filter(L).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,s=r.create_definitions,u=r[e],a=u.db,i=u.schema,c=[h(a),i.map(h).join(".")].filter(L).join("."),l=[w(t),w(n),w(e),w(o),c];s&&l.push(s.map(lr).join(" "));return l.filter(L).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,s=r.keyword,u=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,h=p.db,m=p.schema,E=p.view,A=[d(h),d(m),d(E)].filter(L).join(".");return[w(f),w(a),w(l),w(u),t&&"ALGORITHM = ".concat(w(t)),st(n),c&&"SQL SECURITY ".concat(w(c)),w(s),w(o),A,e&&"(".concat(e.map(y).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return C(r).join(" ")})).join(", "),")")].join(" "),"AS",wr(i),w(b)].filter(L).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,s=r.target,u=r.create_definitions,a=[w(n),w(o),[d(e.schema),d(e.name)].filter(L).join("."),w(t),A(s)];if(u&&u.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Br(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(u);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(st(f));break;case"default":c.push(w(p),st(f.value));break;case"constraint":c.push(Or(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(L).join(" "))}return a.filter(L).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,s=r.resource,u=[w(r.type),w(n),[d(o.schema),d(o.name)].filter(L).join("."),w(t),w(s)];if(e){var a=[];switch(s){case"enum":case"range":a.push(st(e));break;default:a.push("(".concat(e.map(qr).join(", "),")"))}u.push(a.filter(L).join(" "))}return u.filter(L).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,s=r.keyword,u=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[jr(t)];return e&&n.push(w(e.keyword),e.auth_plugin,h(e.value)),n.filter(L).join(" ")})).join(", "),b=[w(f),w(s),w(o),p];n&&b.push(w(n.keyword),n.value.map(jr).join(", "));b.push(a(c&&c.keyword,st,c&&c.value)),l&&b.push(w(l.keyword),l.value.map((function(r){return st(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,st,r.value))}));return b.push(h(u),T(e),h(t)),b.filter(L).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[w(r.type),w(e),fr(n),pr(t)].filter(L).join(" ")},select:q,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),st(n)].filter(L).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning,l=[B(u),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",st,o)),l.push(at(s,"order by")),l.push(F(i)),l.push(S(c)),l.filter(L).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[w(t),ir(e),(n||[]).map(br).filter(L).join(", ")].filter(L).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[w(t)],s=[e];n&&s.push("(".concat(st(n).join(", "),")"));return o.push(s.join("")),o.filter(L).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[w(t),q(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,s=r.stmts;return[e,w(t),n,"IN",Lr([o]),"LOOP",Lr(s),"END LOOP",e].filter(L).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning;return[B(u),"UPDATE",cr(e),a("SET",V,n),a("FROM",cr,t),a("WHERE",st,o),at(s,"order by"),F(i),S(c)].filter(L).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,s=r.prefix,u=r.go,a=r.semicolons,i=r.suffix,c=[w(r.type),st(t),h(s),"".concat(Er(o.ast||o)).concat(a[0]),w(u)];n&&c.push(n.map((function(r){return[w(r.type),st(r.boolean_expr),"THEN",Er(r.then.ast||r.then),r.semicolon].filter(L).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Er(e.ast||e)).concat(a[1]));return c.push(h(i)),c.filter(L).join(" ")},insert:J,drop:Sr,truncate:Sr,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[w(t)],s=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,s=r.datatype,u=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(L).join(""),w(n),w(o)];switch(c){case"variable":l.push(dt(s),st(r.collate),w(u)),i&&l.push(w(i.keyword),st(i.value));break;case"cursor":l.push(w(a));break;case"table":l.push(w(a),"(".concat(i.map(qr).join(", "),")"))}return l.filter(L).join(" ")})).join("".concat(n," "));return o.push(s),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=w(t),o=d(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var s,u=Ar(e);try{for(u.s();!(s=u.n()).done;){var a=s.value.map(ir);n.push(a.join(" TO "))}}catch(r){u.e(r)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=st(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=w(t);return"".concat(n," ").concat(d(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=w(t),s=e.map(st).join(", ");return[o,w(n),s].filter(L).join(" ")},lock:xr,unlock:xr,show:yr,grant:Ir,revoke:Ir,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[w(t),st(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,s=[w(t),w(e)];n&&s.push([h(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(L).join(""),n.expr.map((function(r){return st(r)})).join(", "));o&&s.push(w(o.type),w(o.option),o.symbol,o.expr.map((function(r){return st(r)})).join(", "));return s.filter(L).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,s=[h(e),w(n)];return o&&s.push(o.map(h).join(", ")),s.filter(L).join(" ")}};function wr(r){if(!r)return"";for(var t=dr[r.type],e=r,n=e._parentheses,o=e._orderby,s=e._limit,u=[n&&"(",t(r)];r._next;){var a=dr[r._next.type],i=w(r.set_op);u.push(i,a(r._next)),r=r._next}return u.push(n&&")",at(o,"order by"),F(s)),u.filter(L).join(" ")}function Lr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],s=wr(o);e===n-1&&"transaction"===o.type&&(s="".concat(s," ;")),t.push(s)}return t.join(" ; ")}var hr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Cr(r){var t=r&&r.ast?r.ast:r;if(!hr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function mr(r){return Array.isArray(r)?(r.forEach(Cr),Lr(r)):(Cr(r),wr(r))}function Er(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[mr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):mr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Tr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function gr(r){return function(r){if(Array.isArray(r))return _r(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Tr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tr(r,t){if(r){if("string"==typeof r)return _r(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_r(r,t):void 0}}function _r(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Sr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,s=r.suffix,u=[w(t),w(e),w(o)];switch(e){case"table":u.push(cr(n));break;case"trigger":u.push([n[0].schema?"".concat(d(n[0].schema),"."):"",d(n[0].trigger)].filter(L).join(""));break;case"database":case"schema":case"procedure":u.push(d(n));break;case"view":u.push(cr(n),r.options&&r.options.map(st).filter(L).join(" "));break;case"index":u.push.apply(u,[yt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(st).filter(L).join(" ")]));break;case"type":u.push(n.map(yt).join(", "),r.options&&r.options.map(st).filter(L).join(" "))}return s&&u.push(s.map(st).filter(L).join(" ")),u.filter(L).join(" ")}function xr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),w(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var s,u=[],a=Ar(n);try{var i=function(){var r=s.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return w(e[r])})).filter(L).join(" "))}u.push(n.join(" "))};for(a.s();!(s=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[u.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(L).join(" ")}function jr(r){var t=r.name,e=r.host,n=[h(t)];return e&&n.push("@",h(e)),n.join("")}function Ir(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,s=r.on,u=r.to_from,a=r.user_or_roles,i=r.with,c=[w(t),h(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[st(t)];return e&&n.push("(".concat(e.map(yt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),s)switch(c.push("ON"),n){case"priv":c.push(h(s.object_type),s.priv_level.map((function(r){return[d(r.prefix),d(r.name)].filter(L).join(".")})).join(", "));break;case"proxy":c.push(jr(s))}return c.push(w(u),a.map(jr).join(", ")),c.push(h(i)),c.filter(L).join(" ")}function Rr(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Or(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,s=r.keyword,u=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(w(s)),l.push(d(t));var b=w(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&d(o)),l.push.apply(l,Rr(M(r))),l.push.apply(l,Rr(wt(u))),l.push(w(n)),l.push(a("FOR",d,i)),l.push(h(c)),l.filter(L).join(" ")}}function kr(r){if(r){var t=r.type;return"rows"===t?[w(t),st(r.expr)].filter(L).join(" "):st(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),kr(o)].filter(L).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,s=r.parentheses,u=w(o);if("WINDOW"===u)return"OVER ".concat(Ur(t));if("ON UPDATE"===u){var a="".concat(w(o)," ").concat(w(n)),i=st(e)||[];return s&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return w(t);for(var e=r.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(h(o[u])),s.push("]");return s.join("")}function Gr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,s=r.as,u=r.offset,a=r.parentheses,i=bt({expr:e,offset:u}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,y=p.dataType,C=p.parentheses,m=p.quoted,E=p.scale,A=p.suffix,g=p.expr,T=g?st(g):"";null!=v&&(T=E?"".concat(v,", ").concat(E):v),C&&(T="(".concat(T,")")),b&&(T="<".concat(T,">")),A&&A.length&&(T+=" ".concat(A.map(h).join(" ")));var _="::",S="",x=[];"as"===o&&(0===l&&(i="".concat(w(n),"(").concat(i)),S=")",_=" ".concat(o.toUpperCase()," ")),0===l&&x.push(i);var j=Pr(p);x.push(_,m,y,m,j,T,S),c.push(x.filter(L).join(""))}s&&c.push(" AS ".concat(d(s)));var I=c.filter(L).join("");return a?"(".concat(I,")"):I}function $r(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,s=r.parentheses,u=r.within_group,a=r.over,i=r.suffix,c=Dr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[w(t),w(e),"(".concat(at(n,"order by"),")")].filter(L).join(" ")}(u),f=st(i),p=[h(n.schema),n.name.map(h).join(".")].filter(L).join(".");if(!t)return[p,l,c].filter(L).join(" ");var b=r.separator||", ";"TRIM"===w(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var y=st(t);if(Array.isArray(b)){for(var d=y[0],C=1,m=y.length;C<m;++C)d=[d,y[C]].join(" ".concat(st(b[C-1])," "));v.push(d)}else v.push(y.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(L).join(" "),[s?"(".concat(v,")"):v,l,c].filter(L).join(" ")}function Fr(r){return function(r){if(Array.isArray(r))return Hr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Br(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Br(r,t){if(r){if("string"==typeof r)return Hr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Hr(r,t):void 0}}function Hr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function qr(r){if(!r)return[];var t,e,n,o,s=r.resource;switch(s){case"column":return Ct(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(w(n)),e.push(o),e.push.apply(e,R(M(t))),e.filter(L).join(" ");case"constraint":return Or(r);case"sequence":return[w(r.prefix),st(r.value)].filter(L).join(" ");default:throw new Error("unknown resource = ".concat(s," type"))}}function Yr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(h(r.from),")"),"TO","(".concat(h(r.to),")"));break;case"in":t.push("IN","(".concat(st(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(h(r.modulus),", REMAINDER ").concat(h(r.remainder),")"))}return t.filter(L).join(" ")}function Wr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,s=[w(t),ir(e),w(n.keyword),Yr(n.expr)];return o&&s.push("TABLESPACE",h(o)),s.filter(L).join(" ")}function Vr(r){return r.dataType?A(r):[d(r.db),d(r.schema),d(r.table)].filter(L).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[w(t),r.symbol,wr(r.declare),w(r.begin),Lr(r.expr),w(r.end),r.symbol].filter(L).join(" ");case"set":return[w(t),r.parameter,w(r.value&&r.value.prefix),r.value&&r.value.expr.map(st).join(", ")].filter(L).join(" ");case"return":return[w(t),st(r.expr)].filter(L).join(" ");default:return st(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[w(t),e];switch(w(t)){case"SFUNC":o.push([d(n.schema),n.name].filter(L).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(st(n))}return o.filter(L).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",h(t),"VALUES",w(e.type),"(".concat(h(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,s=r.if_exists,u=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Ct(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=d(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=d(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,w(r[i])].filter(L).join(" ");break;case"constraint":f=d(r[i]),p=[qr(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=d(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[w(t),w(o),w(n),w(s),u&&yt(u),w(a),f&&f.trim(),p.filter(L).join(" ")];return l&&b.push(w(l.keyword),l.expr&&yt(l.expr)),b.filter(L).join(" ")}function Zr(r){var t=r.default&&[w(r.default.keyword),st(r.default.value)].join(" ");return[w(r.mode),r.name,A(r.type),t].filter(L).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(w(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var s=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(L).join(", "):st(n);return e?"[".concat(s,"]"):o?"(".concat(s,")"):s}(r);default:return""}}function tt(r){var t=r.definition,e=[w(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(L).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,s=st(t.expr);s=Array.isArray(s)?s.join(", "):s;var u=r.name,a=Dr(n);t.distinct&&(s=["DISTINCT",s].join(" ")),t.separator&&t.separator.delimiter&&(s=[s,h(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(s=[s,st(t.separator.expr)].join(" ")),t.orderby&&(s=[s,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(s=[s,w(t.separator.keyword),h(t.separator.value)].filter(L).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(st(e.where),")"):"";return["".concat(u,"(").concat(s,")"),i,a,c].filter(L).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,s=t.having,u="".concat(w(e),"(").concat(st(o));return s&&(u="".concat(u," HAVING ").concat(w(s.prefix)," ").concat(st(s.expr))),[u="".concat(u,")"),Dr(n)].filter(L).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,s=r.separator,u=void 0===s?", ":s;return[e,"(",t?st(t).join(u):"",")",o&&" ",o].filter(L).join("")}(r),Dr(t)].filter(L).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(st(n));for(var s=0,u=e.length;s<u;++s)t.push(e[s].type.toUpperCase()),e[s].cond&&(t.push(st(e[s].cond)),t.push("THEN")),t.push(st(e[s].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Gr,collate:it,column_ref:yt,column_definition:Ct,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,s=t.source,u=["".concat(w(e),"(").concat(w(n)),"FROM",w(o),st(s)];return"".concat(u.filter(L).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[w(t),e,st(n)].filter(L).join(" ")}(t[r])})).filter(L).join(", ");return"".concat(w(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,s=r.mode,u=[w(o),"(".concat(n.map((function(r){return yt(r)})).join(", "),")")].join(" "),a=[w(t),["(",st(r.expr),s&&" ".concat(h(s)),")"].filter(L).join("")].join(" ");return[u,a,mt(e)].filter(L).join(" ")},function:$r,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,s=n.map(st).join(", ");return[o?"(".concat(s,")"):s,"->",st(e)].join(" ")},insert:wr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[w(t),e.map((function(r){return st(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,s=[st(e),"VALUE",st(n)];return o&&s.push("ON","NULL",st(o)),s.filter(L).join(" ")},json_visitor:function(r){return[r.symbol,st(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,st(t.expr)].filter(L).join(" ")},show:yr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[h(e.schema),e.name.map(h).join(".")].filter(L).join(".");return["".concat(o,"(").concat(st(n).join(", "),")"),"AS",$r(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,s=r.quoted,u=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return u&&(c+=u),a.push(c),[s,a.join(" "),s].filter(L).join("")}function st(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var s=o[n];t[s]=e[s]}}var u=t.type;return"expr"===u?st(t.expr):nt[u]?nt[u](t):h(t)}}function ut(r){return r?(Array.isArray(r)||(r=[r]),r.map(st)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=w(t);switch(n){case"ORDER BY":e=r.map((function(r){return[st(r.expr),r.type||"ASC",w(r.nulls)].filter(L).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return st(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,s=e.value,u=[w(t)];return s||u.push(o),u.push(Array.isArray(n)?n.map(h).join("."):h(n)),s&&u.push(o),u.push(st(s)),u.filter(L).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return d(r,t);var e=r.expr,n=r.offset,o=r.suffix,s=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),h(r.value),"".concat(r.name?")":""),"]"].filter(L).join("")})).join("");return[st(e),s,o].filter(L).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s=o.brackets?"[".concat(h(o.index),"]"):"".concat(o.notation).concat(h(o.index));o.property&&(s="".concat(s,".").concat(h(o.property))),e.push(s)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function yt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,s=r.db,u=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,y=r.order_by,h=r.subFields,C=void 0===h?[]:h,m="*"===n?"*":bt(n,u),E=[s,f,p].filter(L).map((function(r){return"".concat("string"==typeof r?d(r):st(r))})),A=E[0];if(A){for(var g=1;g<E.length;++g)A="".concat(A).concat(c[g]||".").concat(E[g]);m="".concat(A).concat(c[g]||".").concat(m)}var T=[m=["".concat(m).concat(vt(t))].concat(lt(C)).join("."),it(o),st(l),a("AS",st,e)];T.push("string"==typeof v?w(v):st(v)),T.push(w(y));var _=T.filter(L).join(" ");return b?"(".concat(_,")"):_}function dt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,s=r.expr,u=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(s&&(u+=st(s)),r.array){var a=Pr(r);u+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return u}}function wt(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,s=r.table,u=r.on_action;return t.push(w(n)),t.push(cr(s)),t.push(e&&"(".concat(e.map((function(r){return st(r)})).join(", "),")")),t.push(w(o)),u.map((function(r){return t.push(w(r.type),st(r.value))})),t.filter(L)}function Lt(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,s=r.comment,u=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,y=r.unique,d=r.primary_key,m=r.column_format,E=r.reference_definition,A=[w(e&&e.action),w(e&&e.value)].filter(L).join(" ");if(b||t.push(A),f){var g=f.type,_=f.value;t.push(g.toUpperCase(),st(_))}var S=p().database;return u&&t.push(w(u.keyword),h(u.constraint)),t.push(Or(o)),t.push(function(r){if(r)return[w(r.value),"(".concat(st(r.expr),")"),w(r.storage_type)].filter(L).join(" ")}(b)),b&&t.push(A),t.push(j(v),w(d),w(y),T(s)),t.push.apply(t,lt(C(n))),"sqlite"!==S.toLowerCase()&&t.push(st(i)),t.push.apply(t,lt(C(m))),t.push.apply(t,lt(C(c))),t.push.apply(t,lt(wt(E))),t.push(a("USING",st,l)),t.filter(L).join(" ")}function ht(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,s=r.order_by,u="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return u.collate=null,[st(u),st(e),o,w(s),w(n)].filter(L).join(" ")}function Ct(r){var t=[],e=yt(r.column),n=dt(r.definition);return t.push(e),t.push(n),t.push(Lt(r)),t.filter(L).join(" ")}function mt(r){return r?"object"===ct(r)?["AS",st(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?d(r):y(r)].join(" "):""}function Et(r,t){var e=r.expr,n=r.type;if("cast"===n)return Gr(r);t&&(e.isDual=t);var o=st(e),s=r.expr_list;if(s){var u=[o],a=s.map((function(r){return Et(r,t)})).join(", ");return u.push([w(n),n&&"(",a,n&&")"].filter(L).join("")),u.filter(L).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,mt(r.as)].filter(L).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return Et(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=ut(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",s=t.join(o);return e?"(".concat(s,")"):s},nt.select=function(r){var t="object"===et(r._next)?wr(r):q(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",s="".concat(t).concat(o).concat(st(n));return e?"(".concat(s,")"):s},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[h(r.key),h(r.value)].join(", ")})).join(", ");return[w(t),"[".concat(e,"]")].join("")};var Tt=e(0);function _t(r){return(_t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var St,xt,jt,It=(St={},xt="postgresql",jt=Tt.parse,(xt=function(r){var t=function(r,t){if("object"!=_t(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=_t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==_t(t)?t:t+""}(xt))in St?Object.defineProperty(St,xt,{value:jt,enumerable:!0,configurable:!0,writable:!0}):St[xt]=jt,St);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Ot(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ot(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function Ot(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&kt(r.prototype,t),e&&kt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),Er(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),st(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return Et(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=t.database,n=void 0===e?"postgresql":e;b(t);var o=n.toLowerCase();if(It[o])return It[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||s.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=Nt(i);try{for(f.s();!(u=f.n()).done;){var p,b=u.value,v=!1,y=Nt(t);try{for(y.s();!(p=y.n()).done;){var d=p.value,w=new RegExp("^".concat(d,"$"),"i");if(w.test(b)){v=!0;break}}}catch(r){y.e(r)}finally{y.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=postgresql.js.map