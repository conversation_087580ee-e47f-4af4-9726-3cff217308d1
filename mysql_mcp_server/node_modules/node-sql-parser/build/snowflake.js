!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},s={start:Xa},a=Xa,i=Ba("IF",!0),c=Ba("EXTENSION",!0),l=Ba("SCHEMA",!0),f=Ba("VERSION",!0),p=Ba("CASCADED",!0),b=Ba("LOCAL",!0),v=Ba("CHECK",!0),y=Ba("OPTION",!1),d=Ba("check_option",!0),h=Ba("security_barrier",!0),w=Ba("security_invoker",!0),m=Ba("TYPE",!0),L=Ba("DOMAIN",!0),C=Ba("INCREMENT",!0),E=Ba("MINVALUE",!0),A=function(r,t){return{resource:"sequence",prefix:r.toLowerCase(),value:t}},g=Ba("NO",!0),T=Ba("MAXVALUE",!0),_=Ba("START",!0),S=Ba("CACHE",!0),j=Ba("CYCLE",!0),x=Ba("OWNED",!0),I=Ba("NONE",!0),R=Ba("NULLS",!0),N=Ba("FIRST",!0),O=Ba("LAST",!0),k=Ba("AUTO_INCREMENT",!0),U=Ba("UNIQUE",!0),M=Ba("KEY",!0),P=Ba("PRIMARY",!0),D=Ba("COLUMN_FORMAT",!0),G=Ba("FIXED",!0),$=Ba("DYNAMIC",!0),F=Ba("DEFAULT",!0),B=Ba("STORAGE",!0),H=Ba("DISK",!0),W=Ba("MEMORY",!0),Y=Ba("CASCADE",!0),q=Ba("RESTRICT",!0),V=Ba("OUT",!0),X=Ba("VARIADIC",!0),Q=Ba("INOUT",!0),K=Ba("AGGREGATE",!0),z=Ba("FUNCTION",!0),Z=Ba("OWNER",!0),J=Ba("CURRENT_ROLE",!0),rr=Ba("CURRENT_USER",!0),tr=Ba("SESSION_USER",!0),er=Ba("ALGORITHM",!0),nr=Ba("INSTANT",!0),or=Ba("INPLACE",!0),ur=Ba("COPY",!0),sr=Ba("LOCK",!0),ar=Ba("SHARED",!0),ir=Ba("EXCLUSIVE",!0),cr=Ba("PRIMARY KEY",!0),lr=Ba("FOREIGN KEY",!0),fr=Ba("MATCH FULL",!0),pr=Ba("MATCH PARTIAL",!0),br=Ba("MATCH SIMPLE",!0),vr=Ba("SET NULL",!0),yr=Ba("NO ACTION",!0),dr=Ba("SET DEFAULT",!0),hr=Ba("TRIGGER",!0),wr=Ba("BEFORE",!0),mr=Ba("AFTER",!0),Lr=Ba("INSTEAD OF",!0),Cr=Ba("ON",!0),Er=Ba("EXECUTE",!0),Ar=Ba("PROCEDURE",!0),gr=Ba("OF",!0),Tr=Ba("NOT",!0),_r=Ba("DEFERRABLE",!0),Sr=Ba("INITIALLY IMMEDIATE",!0),jr=Ba("INITIALLY DEFERRED",!0),xr=Ba("FOR",!0),Ir=Ba("EACH",!0),Rr=Ba("ROW",!0),Nr=Ba("STATEMENT",!0),Or=Ba("CHARACTER",!0),kr=Ba("SET",!0),Ur=Ba("CHARSET",!0),Mr=Ba("COLLATE",!0),Pr=Ba("AVG_ROW_LENGTH",!0),Dr=Ba("KEY_BLOCK_SIZE",!0),Gr=Ba("MAX_ROWS",!0),$r=Ba("MIN_ROWS",!0),Fr=Ba("STATS_SAMPLE_PAGES",!0),Br=Ba("CONNECTION",!0),Hr=Ba("COMPRESSION",!0),Wr=Ba("'",!1),Yr=Ba("ZLIB",!0),qr=Ba("LZ4",!0),Vr=Ba("ENGINE",!0),Xr=Ba("IN",!0),Qr=Ba("ACCESS SHARE",!0),Kr=Ba("ROW SHARE",!0),zr=Ba("ROW EXCLUSIVE",!0),Zr=Ba("SHARE UPDATE EXCLUSIVE",!0),Jr=Ba("SHARE ROW EXCLUSIVE",!0),rt=Ba("ACCESS EXCLUSIVE",!0),tt=Ba("SHARE",!0),et=Ba("MODE",!0),nt=Ba("NOWAIT",!0),ot=Ba("TABLES",!0),ut=Ba("PREPARE",!0),st=Ba(";",!1),at=Ba("(",!1),it=Ba(")",!1),ct=Ba("PERCENT",!0),lt=Ba("exclude",!0),ft=Ba("OUTFILE",!0),pt=Ba("DUMPFILE",!0),bt=Ba("BTREE",!0),vt=Ba("HASH",!0),yt=Ba("GIST",!0),dt=Ba("GIN",!0),ht=Ba("WITH",!0),wt=Ba("PARSER",!0),mt=Ba("VISIBLE",!0),Lt=Ba("INVISIBLE",!0),Ct=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Fb[t]=t,e&&(Fb[e]=t),function(r){const t=Mb(r);r.clear(),t.forEach(t=>r.add(t))}($b)}),t},Et=Ba("ROWCOUNT",!0),At=Ba("TIMELIMIT",!0),gt=Ba("=>",!1),Tt=Ba("GENERATOR",!0),_t=Ba("LATERAL",!0),St=Ba("TABLESAMPLE",!0),jt=Ba("REPEATABLE",!0),xt=Ba("CROSS",!0),It=Ba("PRECEDING",!0),Rt=Ba("RANGE",!0),Nt=Ba("FOLLOWING",!0),Ot=Ba("CURRENT",!0),kt=Ba("UNBOUNDED",!0),Ut=Ba("=",!1),Mt=Ba("DO",!0),Pt=Ba("NOTHING",!0),Dt=Ba("CONFLICT",!0),Gt=function(r,t){return kb(r,t)},$t=Ba("!",!1),Ft=Ba(">=",!1),Bt=Ba(">",!1),Ht=Ba("<=",!1),Wt=Ba("<>",!1),Yt=Ba("<",!1),qt=Ba("!=",!1),Vt=Ba("SIMILAR",!0),Xt=Ba("ESCAPE",!0),Qt=Ba("+",!1),Kt=Ba("-",!1),zt=Ba("*",!1),Zt=Ba("/",!1),Jt=Ba("%",!1),re=Ba("||",!1),te=Ba("$",!1),ee=Ba("~",!1),ne=Ba("?|",!1),oe=Ba("?&",!1),ue=Ba("?",!1),se=Ba("#-",!1),ae=Ba("#>>",!1),ie=Ba("#>",!1),ce=Ba("@>",!1),le=Ba("<@",!1),fe=Ba("E",!0),pe=function(r){return{type:"default",value:r}},be=function(r){return!0===jb[r.toUpperCase()]},ve=Ba('"',!1),ye=/^[^"]/,de=Ha(['"'],!0,!1),he=/^[^']/,we=Ha(["'"],!0,!1),me=Ba("`",!1),Le=/^[^`]/,Ce=Ha(["`"],!0,!1),Ee=/^[A-Za-z0-9_\u4E00-\u9FA5]/,Ae=Ha([["A","Z"],["a","z"],["0","9"],"_",["一","龥"]],!1,!1),ge=/^[A-Za-z0-9_\-$\u4E00-\u9FA5]/,Te=Ha([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"]],!1,!1),_e=Ba(":",!1),Se=Ba("OVER",!0),je=Ba("FILTER",!0),xe=Ba("FIRST_VALUE",!0),Ie=Ba("LAST_VALUE",!0),Re=Ba("ROW_NUMBER",!0),Ne=Ba("DENSE_RANK",!0),Oe=Ba("RANK",!0),ke=Ba("LAG",!0),Ue=Ba("LEAD",!0),Me=Ba("NTH_VALUE",!0),Pe=Ba("IGNORE",!0),De=Ba("RESPECT",!0),Ge=Ba("LISTAGG",!0),$e=Ba("percentile_cont",!0),Fe=Ba("percentile_disc",!0),Be=Ba("within",!0),He=Ba("mode",!0),We=Ba("BOTH",!0),Ye=Ba("LEADING",!0),qe=Ba("TRAILING",!0),Ve=Ba("trim",!0),Xe=Ba("INPUT",!0),Qe=Ba("PATH",!0),Ke=Ba("OUTER",!0),ze=Ba("RECURSIVE",!0),Ze=Ba("POSITION",!0),Je=Ba("now",!0),rn=Ba("at",!0),tn=Ba("zone",!0),en=Ba("FLATTEN",!0),nn=Ba("parse_json",!0),on=Ba("CENTURY",!0),un=Ba("DAY",!0),sn=Ba("DATE",!0),an=Ba("DECADE",!0),cn=Ba("DOW",!0),ln=Ba("DOY",!0),fn=Ba("EPOCH",!0),pn=Ba("HOUR",!0),bn=Ba("ISODOW",!0),vn=Ba("ISOYEAR",!0),yn=Ba("MICROSECONDS",!0),dn=Ba("MILLENNIUM",!0),hn=Ba("MILLISECONDS",!0),wn=Ba("MINUTE",!0),mn=Ba("MONTH",!0),Ln=Ba("QUARTER",!0),Cn=Ba("SECOND",!0),En=Ba("TIMEZONE",!0),An=Ba("TIMEZONE_HOUR",!0),gn=Ba("TIMEZONE_MINUTE",!0),Tn=Ba("WEEK",!0),_n=Ba("YEAR",!0),Sn=Ba("NTILE",!0),jn=/^[\n]/,xn=Ha(["\n"],!1,!1),In=/^[^"\\\0-\x1F\x7F]/,Rn=Ha(['"',"\\",["\0",""],""],!0,!1),Nn=/^[^'\\]/,On=Ha(["'","\\"],!0,!1),kn=Ba("\\'",!1),Un=Ba('\\"',!1),Mn=Ba("\\\\",!1),Pn=Ba("\\/",!1),Dn=Ba("\\b",!1),Gn=Ba("\\f",!1),$n=Ba("\\n",!1),Fn=Ba("\\r",!1),Bn=Ba("\\t",!1),Hn=Ba("\\u",!1),Wn=Ba("\\",!1),Yn=Ba("''",!1),qn=/^[\n\r]/,Vn=Ha(["\n","\r"],!1,!1),Xn=Ba(".",!1),Qn=/^[0-9]/,Kn=Ha([["0","9"]],!1,!1),zn=/^[0-9a-fA-F]/,Zn=Ha([["0","9"],["a","f"],["A","F"]],!1,!1),Jn=/^[eE]/,ro=Ha(["e","E"],!1,!1),to=/^[+\-]/,eo=Ha(["+","-"],!1,!1),no=Ba("NULL",!0),oo=Ba("NOT NULL",!0),uo=Ba("TRUE",!0),so=Ba("TO",!0),ao=Ba("TOP",!0),io=Ba("FALSE",!0),co=Ba("SHOW",!0),lo=Ba("DROP",!0),fo=Ba("USE",!0),po=Ba("ALTER",!0),bo=Ba("SELECT",!0),vo=Ba("UPDATE",!0),yo=Ba("CREATE",!0),ho=Ba("TEMPORARY",!0),wo=Ba("TEMP",!0),mo=Ba("DELETE",!0),Lo=Ba("INSERT",!0),Co=Ba("REPLACE",!0),Eo=Ba("RETURNING",!0),Ao=Ba("RENAME",!0),go=(Ba("EXPLAIN",!0),Ba("PARTITION",!0)),To=Ba("INTO",!0),_o=Ba("FROM",!0),So=Ba("AS",!0),jo=Ba("TABLE",!0),xo=Ba("DATABASE",!0),Io=Ba("SEQUENCE",!0),Ro=Ba("TABLESPACE",!0),No=Ba("DEALLOCATE",!0),Oo=Ba("LEFT",!0),ko=Ba("RIGHT",!0),Uo=Ba("FULL",!0),Mo=Ba("INNER",!0),Po=Ba("JOIN",!0),Do=Ba("UNION",!0),Go=Ba("VALUES",!0),$o=Ba("USING",!0),Fo=Ba("WHERE",!0),Bo=Ba("GROUP",!0),Ho=Ba("BY",!0),Wo=Ba("ORDER",!0),Yo=Ba("HAVING",!0),qo=Ba("QUALIFY",!0),Vo=Ba("WINDOW",!0),Xo=Ba("LIMIT",!0),Qo=Ba("OFFSET",!0),Ko=Ba("ASC",!0),zo=Ba("DESC",!0),Zo=Ba("ALL",!0),Jo=Ba("DISTINCT",!0),ru=Ba("BETWEEN",!0),tu=Ba("IS",!0),eu=Ba("LIKE",!0),nu=Ba("ILIKE",!0),ou=Ba("EXISTS",!0),uu=Ba("REGEXP",!0),su=Ba("AND",!0),au=Ba("OR",!0),iu=Ba("ARRAY",!0),cu=Ba("ARRAY_AGG",!0),lu=Ba("STRING_AGG",!0),fu=Ba("COUNT",!0),pu=Ba("GROUP_CONCAT",!0),bu=Ba("MAX",!0),vu=Ba("MIN",!0),yu=Ba("SUM",!0),du=Ba("AVG",!0),hu=Ba("EXTRACT",!0),wu=Ba("CALL",!0),mu=Ba("CASE",!0),Lu=Ba("WHEN",!0),Cu=Ba("THEN",!0),Eu=Ba("ELSE",!0),Au=Ba("END",!0),gu=Ba("CAST",!0),Tu=Ba("BINARY",!0),_u=Ba("VARBINARY",!0),Su=Ba("BOOL",!0),ju=Ba("BOOLEAN",!0),xu=Ba("CHAR",!0),Iu=Ba("VARCHAR",!0),Ru=Ba("NUMBER",!0),Nu=Ba("NUMERIC",!0),Ou=Ba("DECIMAL",!0),ku=Ba("STRING",!0),Uu=Ba("SIGNED",!0),Mu=Ba("UNSIGNED",!0),Pu=Ba("INT",!0),Du=Ba("BYTEINT",!0),Gu=Ba("ZEROFILL",!0),$u=Ba("INTEGER",!0),Fu=Ba("JSON",!0),Bu=Ba("JSONB",!0),Hu=Ba("GEOMETRY",!0),Wu=Ba("GEOGRAPHY",!0),Yu=Ba("SMALLINT",!0),qu=Ba("SERIAL",!0),Vu=Ba("TINYINT",!0),Xu=Ba("TINYTEXT",!0),Qu=Ba("TEXT",!0),Ku=Ba("MEDIUMTEXT",!0),zu=Ba("LONGTEXT",!0),Zu=Ba("BIGINT",!0),Ju=Ba("ENUM",!0),rs=Ba("FLOAT",!0),ts=Ba("FLOAT4",!0),es=Ba("FLOAT8",!0),ns=Ba("DOUBLE",!0),os=Ba("BIGSERIAL",!0),us=Ba("REAL",!0),ss=Ba("DATETIME",!0),as=Ba("ROWS",!0),is=Ba("TIME",!0),cs=Ba("TIMESTAMP",!0),ls=Ba("TIMESTAMP_TZ",!0),fs=Ba("TIMESTAMP_NTZ",!0),ps=Ba("TRUNCATE",!0),bs=Ba("USER",!0),vs=Ba("UUID",!0),ys=Ba("OID",!0),ds=Ba("REGCLASS",!0),hs=Ba("REGCOLLATION",!0),ws=Ba("REGCONFIG",!0),ms=Ba("REGDICTIONARY",!0),Ls=Ba("REGNAMESPACE",!0),Cs=Ba("REGOPER",!0),Es=Ba("REGOPERATOR",!0),As=Ba("REGPROC",!0),gs=Ba("REGPROCEDURE",!0),Ts=Ba("REGROLE",!0),_s=Ba("REGTYPE",!0),Ss=Ba("CURRENT_DATE",!0),js=(Ba("ADDDATE",!0),Ba("INTERVAL",!0)),xs=Ba("MM",!0),Is=Ba("MON",!0),Rs=Ba("MONS",!0),Ns=Ba("MONTHS",!0),Os=Ba("W",!0),ks=Ba("WK",!0),Us=Ba("WEEKOFYEAR",!0),Ms=Ba("WOY",!0),Ps=Ba("WY",!0),Ds=Ba("WEEKS",!0),Gs=Ba("CURRENT_TIME",!0),$s=Ba("CURRENT_TIMESTAMP",!0),Fs=Ba("SYSTEM_USER",!0),Bs=Ba("GLOBAL",!0),Hs=Ba("SESSION",!0),Ws=Ba("PERSIST",!0),Ys=Ba("PERSIST_ONLY",!0),qs=Ba("PIVOT",!0),Vs=Ba("UNPIVOT",!0),Xs=Ba("VIEW",!0),Qs=Ba("@",!1),Ks=Ba("@@",!1),zs=Ba("$$",!1),Zs=Ba("return",!0),Js=Ba(":=",!1),ra=Ba("::",!1),ta=Ba("DUAL",!0),ea=Ba("ADD",!0),na=Ba("COLUMN",!0),oa=Ba("INDEX",!0),ua=Ba("FULLTEXT",!0),sa=Ba("SPATIAL",!0),aa=Ba("COMMENT",!0),ia=Ba("CONSTRAINT",!0),ca=Ba("CONCURRENTLY",!0),la=Ba("REFERENCES",!0),fa=Ba("SQL_CALC_FOUND_ROWS",!0),pa=Ba("SQL_CACHE",!0),ba=Ba("SQL_NO_CACHE",!0),va=Ba("SQL_SMALL_RESULT",!0),ya=Ba("SQL_BIG_RESULT",!0),da=Ba("SQL_BUFFER_RESULT",!0),ha=Ba(",",!1),wa=Ba("[",!1),ma=Ba("]",!1),La=Ba("->",!1),Ca=Ba("->>",!1),Ea=Ba("&&",!1),Aa=Ba("/*",!1),ga=Ba("*/",!1),Ta=Ba("--",!1),_a=Ba("//",!1),Sa=(Ba("#",!1),{type:"any"}),ja=/^[ \t\n\r]/,xa=Ha([" ","\t","\n","\r"],!1,!1),Ia=/^[^$]/,Ra=Ha(["$"],!0,!1),Na=function(r){return{dataType:r}},Oa=Ba("WITHOUT",!0),ka=Ba("ZONE",!0),Ua=function(r){return{dataType:r}},Ma=0,Pa=0,Da=[{line:1,column:1}],Ga=0,$a=[],Fa=0;if("startRule"in t){if(!(t.startRule in s))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=s[t.startRule]}function Ba(r,t){return{type:"literal",text:r,ignoreCase:t}}function Ha(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Wa(t){var e,n=Da[t];if(n)return n;for(e=t-1;!Da[e];)e--;for(n={line:(n=Da[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Da[t]=n,n}function Ya(r,t){var e=Wa(r),n=Wa(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function qa(r){Ma<Ga||(Ma>Ga&&(Ga=Ma,$a=[]),$a.push(r))}function Va(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Xa(){var r,t;return r=Ma,ub()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Ka())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=tb())!==u&&(a=ub())!==u&&(i=Ka())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=tb())!==u&&(a=ub())!==u&&(i=Ka())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Gb),columnList:Mb($b),ast:n}}(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=r,r=t):(Ma=r,r=u),r}function Qa(){var t;return(t=function(){var t,e,n,o,s,a,c,l,f;t=Ma,(e=ef())!==u&&ub()!==u&&(n=Cf())!==u&&ub()!==u&&(o=Ki())!==u?(Pa=t,p=e,b=n,(v=o)&&v.forEach(r=>Gb.add(`${p}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:p.toLowerCase(),keyword:b.toLowerCase(),name:v}},t=e):(Ma=t,t=u);var p,b,v;t===u&&(t=Ma,(e=ef())!==u&&ub()!==u&&(n=Bp())!==u&&ub()!==u?((o=Vp())===u&&(o=null),o!==u&&ub()!==u?(s=Ma,"if"===r.substr(Ma,2).toLowerCase()?(a=r.substr(Ma,2),Ma+=2):(a=u,0===Fa&&qa(i)),a!==u&&(c=ub())!==u&&(l=Ff())!==u?s=a=[a,c,l]:(Ma=s,s=u),s===u&&(s=null),s!==u&&(a=ub())!==u&&(c=zc())!==u&&(l=ub())!==u?("cascade"===r.substr(Ma,7).toLowerCase()?(f=r.substr(Ma,7),Ma+=7):(f=u,0===Fa&&qa(Y)),f===u&&("restrict"===r.substr(Ma,8).toLowerCase()?(f=r.substr(Ma,8),Ma+=8):(f=u,0===Fa&&qa(q))),f===u&&(f=null),f!==u?(Pa=t,e=function(r,t,e,n,o,u){return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:o,options:u&&[{type:"origin",value:u}]}}}(e,n,o,0,c,f),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u));return t}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,s,a,i,c,l,f,p,b;r=Ma,(t=sf())!==u&&ub()!==u?(e=Ma,(n=Yf())!==u&&(o=ub())!==u&&(s=bf())!==u?e=n=[n,o,s]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u?((o=af())===u&&(o=null),o!==u&&(s=ub())!==u&&Cf()!==u&&ub()!==u?((a=Ja())===u&&(a=null),a!==u&&ub()!==u&&(i=tc())!==u&&ub()!==u?((c=function(){var r,t,e,n,o,s,a,i,c;if(r=Ma,(t=zp())!==u)if(ub()!==u)if((e=ni())!==u){for(n=[],o=Ma,(s=ub())!==u&&(a=Qp())!==u&&(i=ub())!==u&&(c=ni())!==u?o=s=[s,a,i,c]:(Ma=o,o=u);o!==u;)n.push(o),o=Ma,(s=ub())!==u&&(a=Qp())!==u&&(i=ub())!==u&&(c=ni())!==u?o=s=[s,a,i,c]:(Ma=o,o=u);n!==u&&(o=ub())!==u&&(s=Zp())!==u?(Pa=r,t=Ob(e,n),r=t):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;return r}())===u&&(c=null),c!==u&&ub()!==u?((l=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Si())!==u){for(e=[],n=Ma,(o=ub())!==u?((s=Qp())===u&&(s=null),s!==u&&(a=ub())!==u&&(i=Si())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u?((s=Qp())===u&&(s=null),s!==u&&(a=ub())!==u&&(i=Si())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())===u&&(l=null),l!==u&&ub()!==u?((f=yf())===u&&(f=bf()),f===u&&(f=null),f!==u&&ub()!==u?((p=Lf())===u&&(p=null),p!==u&&ub()!==u?((b=Za())===u&&(b=null),b!==u?(Pa=r,v=t,y=e,d=o,h=a,w=i,m=c,L=l,C=f,E=p,A=b,Gb.add(`create::${[w.db,w.schema].filter(Boolean).join(".")||null}::${w.table}`),t={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:v[0].toLowerCase(),keyword:"table",temporary:d&&d[0].toLowerCase(),if_not_exists:h,table:[w],replace:y&&"or replace",ignore_replace:C&&C[0].toLowerCase(),as:E&&E[0].toLowerCase(),query_expr:A&&A.ast,create_definitions:m,table_options:L},...xb()},r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);var v,y,d,h,w,m,L,C,E,A;r===u&&(r=Ma,(t=sf())!==u&&ub()!==u?(e=Ma,(n=Yf())!==u&&(o=ub())!==u&&(s=bf())!==u?e=n=[n,o,s]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u?((o=af())===u&&(o=null),o!==u&&(s=ub())!==u&&Cf()!==u&&ub()!==u?((a=Ja())===u&&(a=null),a!==u&&ub()!==u&&(i=Ki())!==u&&ub()!==u&&(c=function r(){var t,e;(t=function(){var r,t;r=Ma,Gf()!==u&&ub()!==u&&(t=Ki())!==u?(Pa=r,r={type:"like",table:t}):(Ma=r,r=u);return r}())===u&&(t=Ma,zp()!==u&&ub()!==u&&(e=r())!==u&&ub()!==u&&Zp()!==u?(Pa=t,(n=e).parentheses=!0,t=n):(Ma=t,t=u));var n;return t}())!==u?(Pa=r,t=function(r,t,e,n,o,u){return o&&o.forEach(r=>Gb.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:e&&e[0].toLowerCase(),if_not_exists:n,replace:t&&(t[0]+" "+t[2][0]).toUpperCase(),table:o,like:u}}}(t,e,o,a,i,c),r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,p,b,v,y,d,h,w,m,L,C,E;t=Ma,(e=sf())!==u&&ub()!==u?(n=Ma,(o=Yf())!==u&&(s=ub())!==u&&(a=bf())!==u?n=o=[o,s,a]:(Ma=n,n=u),n===u&&(n=null),n!==u&&(o=ub())!==u?((s=qp())===u&&(s=null),s!==u&&(a=ub())!==u?("trigger"===r.substr(Ma,7).toLowerCase()?(i=r.substr(Ma,7),Ma+=7):(i=u,0===Fa&&qa(hr)),i!==u&&ub()!==u&&(c=fl())!==u&&ub()!==u?("before"===r.substr(Ma,6).toLowerCase()?(l=r.substr(Ma,6),Ma+=6):(l=u,0===Fa&&qa(wr)),l===u&&("after"===r.substr(Ma,5).toLowerCase()?(l=r.substr(Ma,5),Ma+=5):(l=u,0===Fa&&qa(mr)),l===u&&("instead of"===r.substr(Ma,10).toLowerCase()?(l=r.substr(Ma,10),Ma+=10):(l=u,0===Fa&&qa(Lr)))),l!==u&&ub()!==u&&(f=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=gi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Yf())!==u&&(a=ub())!==u&&(i=gi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Yf())!==u&&(a=ub())!==u&&(i=gi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u&&ub()!==u?("on"===r.substr(Ma,2).toLowerCase()?(p=r.substr(Ma,2),Ma+=2):(p=u,0===Fa&&qa(Cr)),p!==u&&ub()!==u&&(b=tc())!==u&&ub()!==u?(v=Ma,(y=wf())!==u&&(d=ub())!==u&&(h=tc())!==u?v=y=[y,d,h]:(Ma=v,v=u),v===u&&(v=null),v!==u&&(y=ub())!==u?((d=function(){var t,e,n,o,s;t=Ma,e=Ma,"not"===r.substr(Ma,3).toLowerCase()?(n=r.substr(Ma,3),Ma+=3):(n=u,0===Fa&&qa(Tr));n===u&&(n=null);n!==u&&(o=ub())!==u?("deferrable"===r.substr(Ma,10).toLowerCase()?(s=r.substr(Ma,10),Ma+=10):(s=u,0===Fa&&qa(_r)),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u);e!==u&&(n=ub())!==u?("initially immediate"===r.substr(Ma,19).toLowerCase()?(o=r.substr(Ma,19),Ma+=19):(o=u,0===Fa&&qa(Sr)),o===u&&("initially deferred"===r.substr(Ma,18).toLowerCase()?(o=r.substr(Ma,18),Ma+=18):(o=u,0===Fa&&qa(jr))),o!==u?(Pa=t,i=o,e={keyword:(a=e)&&a[0]?a[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):(Ma=t,t=u)):(Ma=t,t=u);var a,i;return t}())===u&&(d=null),d!==u&&(h=ub())!==u?((w=function(){var t,e,n,o;t=Ma,"for"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(xr));e!==u&&ub()!==u?("each"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(Ir)),n===u&&(n=null),n!==u&&ub()!==u?("row"===r.substr(Ma,3).toLowerCase()?(o=r.substr(Ma,3),Ma+=3):(o=u,0===Fa&&qa(Rr)),o===u&&("statement"===r.substr(Ma,9).toLowerCase()?(o=r.substr(Ma,9),Ma+=9):(o=u,0===Fa&&qa(Nr))),o!==u?(Pa=t,s=e,i=o,e={keyword:(a=n)?`${s.toLowerCase()} ${a.toLowerCase()}`:s.toLowerCase(),args:i.toLowerCase()},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var s,a,i;return t}())===u&&(w=null),w!==u&&ub()!==u?((m=function(){var r,t;r=Ma,Qf()!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(t=Oc())!==u&&ub()!==u&&Zp()!==u?(Pa=r,r={type:"when",cond:t,parentheses:!0}):(Ma=r,r=u);return r}())===u&&(m=null),m!==u&&ub()!==u?("execute"===r.substr(Ma,7).toLowerCase()?(L=r.substr(Ma,7),Ma+=7):(L=u,0===Fa&&qa(Er)),L!==u&&ub()!==u?("procedure"===r.substr(Ma,9).toLowerCase()?(C=r.substr(Ma,9),Ma+=9):(C=u,0===Fa&&qa(Ar)),C===u&&("function"===r.substr(Ma,8).toLowerCase()?(C=r.substr(Ma,8),Ma+=8):(C=u,0===Fa&&qa(z))),C!==u&&ub()!==u&&(E=mb())!==u?(Pa=t,A=s,g=i,_=f,S=b,j=v,x=d,I=w,R=m,N=C,O=E,e={type:"create",replace:n&&"or replace",constraint:c,location:(T=l)&&T.toLowerCase(),events:_,table:S,from:j&&j[2],deferrable:x,for_each:I,when:R,execute:{keyword:"execute "+N.toLowerCase(),expr:O},constraint_type:g&&g.toLowerCase(),keyword:g&&g.toLowerCase(),constraint_kw:A&&A.toLowerCase(),resource:"constraint"},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var A,g,T,_,S,j,x,I,R,N,O;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,p,b,v,y,d,h,w;t=Ma,(e=sf())!==u&&ub()!==u?("extension"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(c)),n!==u&&ub()!==u?((o=Ja())===u&&(o=null),o!==u&&ub()!==u?((s=fl())===u&&(s=$l()),s!==u&&ub()!==u?((a=jf())===u&&(a=null),a!==u&&ub()!==u?(i=Ma,"schema"===r.substr(Ma,6).toLowerCase()?(p=r.substr(Ma,6),Ma+=6):(p=u,0===Fa&&qa(l)),p!==u&&(b=ub())!==u&&(v=fl())!==u?i=p=[p,b,v]:(Ma=i,i=u),i===u&&(i=$l()),i===u&&(i=null),i!==u&&(p=ub())!==u?(b=Ma,"version"===r.substr(Ma,7).toLowerCase()?(v=r.substr(Ma,7),Ma+=7):(v=u,0===Fa&&qa(f)),v!==u&&(y=ub())!==u?((d=fl())===u&&(d=$l()),d!==u?b=v=[v,y,d]:(Ma=b,b=u)):(Ma=b,b=u),b===u&&(b=null),b!==u&&(v=ub())!==u?(y=Ma,(d=wf())!==u&&(h=ub())!==u?((w=fl())===u&&(w=$l()),w!==u?y=d=[d,h,w]:(Ma=y,y=u)):(Ma=y,y=u),y===u&&(y=null),y!==u?(Pa=t,m=o,L=s,C=a,E=i,A=b,g=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:m,extension:Pb(L),with:C&&C[0].toLowerCase(),schema:Pb(E&&E[2].toLowerCase()),version:Pb(A&&A[2]),from:Pb(g&&g[2])},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var m,L,C,E,A,g;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,p,b,v,y,d,h,w,m;t=Ma,(e=sf())!==u&&ub()!==u?((n=Wp())===u&&(n=null),n!==u&&ub()!==u&&(o=Bp())!==u&&ub()!==u?((s=Vp())===u&&(s=null),s!==u&&ub()!==u?((a=rl())===u&&(a=null),a!==u&&ub()!==u&&(i=Af())!==u&&ub()!==u&&(c=tc())!==u&&ub()!==u?((l=Vi())===u&&(l=null),l!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(f=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=ei())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ei())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ei())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?(p=Ma,(b=jf())!==u&&(v=ub())!==u&&(y=zp())!==u&&(d=ub())!==u&&(h=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Qi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Qi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Qi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u&&(w=ub())!==u&&(m=Zp())!==u?p=b=[b,v,y,d,h,w,m]:(Ma=p,p=u),p===u&&(p=null),p!==u&&(b=ub())!==u?(v=Ma,(y=function(){var t,e,n,o;t=Ma,"tablespace"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(Ro));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TABLESPACE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(d=ub())!==u&&(h=fl())!==u?v=y=[y,d,h]:(Ma=v,v=u),v===u&&(v=null),v!==u&&(y=ub())!==u?((d=oc())===u&&(d=null),d!==u&&(h=ub())!==u?(Pa=t,L=e,C=n,E=o,A=s,g=a,T=i,_=c,S=l,j=f,x=p,I=v,R=d,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:L[0].toLowerCase(),index_type:C&&C.toLowerCase(),keyword:E.toLowerCase(),concurrently:A&&A.toLowerCase(),index:g,on_kw:T[0].toLowerCase(),table:_,index_using:S,index_columns:j,with:x&&x[4],with_before_where:!0,tablespace:I&&{type:"origin",value:I[2]},where:R}},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var L,C,E,A,g,T,_,S,j,x,I,R;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=sf())!==u&&ub()!==u?((n=af())===u&&(n=cf()),n===u&&(n=null),n!==u&&ub()!==u&&function(){var t,e,n,o;t=Ma,"sequence"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Io));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SEQUENCE"):(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u?((o=Ja())===u&&(o=null),o!==u&&ub()!==u&&(s=tc())!==u&&ub()!==u?(a=Ma,(i=Lf())!==u&&(c=ub())!==u&&(l=tl())!==u?a=i=[i,c,l]:(Ma=a,a=u),a===u&&(a=null),a!==u&&(i=ub())!==u?((c=function(){var r,t,e,n,o,s;if(r=Ma,(t=ti())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=ti())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=ti())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e,1),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())===u&&(c=null),c!==u?(Pa=t,f=e,p=n,b=o,y=a,d=c,(v=s).as=y&&y[2],e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:f[0].toLowerCase(),keyword:"sequence",temporary:p&&p[0].toLowerCase(),if_not_exists:b,sequence:[v],create_definitions:d}},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var f,p,b,v,y,d;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=sf())!==u&&ub()!==u?(n=Ma,(o=Yf())!==u&&(s=ub())!==u&&(a=bf())!==u?n=o=[o,s,a]:(Ma=n,n=u),n===u&&(n=null),n!==u&&(o=ub())!==u?((s=function(){var t,e,n,o;t=Ma,"database"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(xo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DATABASE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(s=Ef()),s!==u&&(a=ub())!==u?((i=Ja())===u&&(i=null),i!==u&&ub()!==u&&(c=wb())!==u&&ub()!==u?((l=function(){var r,t,e,n,o,s;if(r=Ma,(t=_i())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=_i())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=_i())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e,1),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())===u&&(l=null),l!==u?(Pa=t,e=function(r,t,e,n,o,u){const s=e.toLowerCase();return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r[0].toLowerCase(),keyword:s,if_not_exists:n,replace:t&&"or replace",[s]:{db:o.schema,schema:o.name},create_definitions:u}}}(e,n,s,i,c,l),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=sf())!==u&&ub()!==u?("domain"===r.substr(Ma,6).toLowerCase()?(n=r.substr(Ma,6),Ma+=6):(n=u,0===Fa&&qa(L)),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u?((s=Lf())===u&&(s=null),s!==u&&ub()!==u&&(a=Ab())!==u&&ub()!==u?((i=si())===u&&(i=null),i!==u&&ub()!==u?((c=ai())===u&&(c=null),c!==u&&ub()!==u?((l=Ci())===u&&(l=null),l!==u?(Pa=t,e=function(r,t,e,n,o,u,s,a){a&&(a.type="constraint");const i=[u,s,a].filter(r=>r);return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i},...xb()}}(e,n,o,s,a,i,c,l),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i;t=Ma,(e=sf())!==u&&ub()!==u?("type"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(m)),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u&&(s=Lf())!==u&&ub()!==u&&(a=hp())!==u&&ub()!==u&&zp()!==u&&ub()!==u?((i=_c())===u&&(i=null),i!==u&&ub()!==u&&Zp()!==u?(Pa=t,c=e,l=n,f=o,p=s,b=a,(v=i).parentheses=!0,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:c[0].toLowerCase(),keyword:l.toLowerCase(),name:{schema:f.db,name:f.table},as:p&&p[0]&&p[0].toLowerCase(),resource:b.toLowerCase(),create_definitions:v},...xb()},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var c,l,f,p,b,v;t===u&&(t=Ma,(e=sf())!==u&&ub()!==u?("type"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(m)),n!==u&&ub()!==u&&(o=tc())!==u?(Pa=t,e=function(r,t,e){return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),name:{schema:e.db,name:e.table}}}}(e,n,o),t=e):(Ma=t,t=u)):(Ma=t,t=u));return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,d,h,w,m,L,C,E,A;t=Ma,(e=sf())!==u&&ub()!==u?(n=Ma,(o=Yf())!==u&&(s=ub())!==u&&(a=bf())!==u?n=o=[o,s,a]:(Ma=n,n=u),n===u&&(n=null),n!==u&&(o=ub())!==u?((s=cf())===u&&(s=af()),s===u&&(s=null),s!==u&&(a=ub())!==u?((i=pf())===u&&(i=null),i!==u&&ub()!==u&&function(){var t,e,n,o;t=Ma,"view"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Xs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="VIEW"):(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(c=tc())!==u&&ub()!==u?(l=Ma,(f=zp())!==u&&(d=ub())!==u&&(h=Zc())!==u&&(w=ub())!==u&&(m=Zp())!==u?l=f=[f,d,h,w,m]:(Ma=l,l=u),l===u&&(l=null),l!==u&&(f=ub())!==u?(d=Ma,(h=jf())!==u&&(w=ub())!==u&&(m=zp())!==u&&(L=ub())!==u&&(C=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=ri())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ri())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ri())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u&&(E=ub())!==u&&(A=Zp())!==u?d=h=[h,w,m,L,C,E,A]:(Ma=d,d=u),d===u&&(d=null),d!==u&&(h=ub())!==u&&(w=Lf())!==u&&(m=ub())!==u&&(L=ji())!==u&&(C=ub())!==u?((E=function(){var t,e,n,o,s;t=Ma,(e=jf())!==u&&ub()!==u?("cascaded"===r.substr(Ma,8).toLowerCase()?(n=r.substr(Ma,8),Ma+=8):(n=u,0===Fa&&qa(p)),n===u&&("local"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(b))),n!==u&&ub()!==u?("check"===r.substr(Ma,5).toLowerCase()?(o=r.substr(Ma,5),Ma+=5):(o=u,0===Fa&&qa(v)),o!==u&&ub()!==u?("OPTION"===r.substr(Ma,6)?(s="OPTION",Ma+=6):(s=u,0===Fa&&qa(y)),s!==u?(Pa=t,e=`with ${n.toLowerCase()} check option`,t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,(e=jf())!==u&&ub()!==u?("check"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(v)),n!==u&&ub()!==u?("OPTION"===r.substr(Ma,6)?(o="OPTION",Ma+=6):(o=u,0===Fa&&qa(y)),o!==u?(Pa=t,t=e="with check option"):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u));return t}())===u&&(E=null),E!==u?(Pa=t,g=e,T=n,_=s,S=i,x=l,I=d,R=L,N=E,(j=c).view=j.table,delete j.table,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:g[0].toLowerCase(),keyword:"view",replace:T&&"or replace",temporary:_&&_[0].toLowerCase(),recursive:S&&S.toLowerCase(),columns:x&&x[2],select:R,view:j,with_options:I&&I[4],with:N}},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var g,T,_,S,j,x,I,R,N;return t}());return t}())===u&&(t=function(){var r,t,e,n;r=Ma,(t=Rp())!==u&&ub()!==u?((e=Cf())===u&&(e=null),e!==u&&ub()!==u&&(n=Ki())!==u?(Pa=r,o=t,s=e,(a=n)&&a.forEach(r=>Gb.add(`${o}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:o.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:a}},r=t):(Ma=r,r=u)):(Ma=r,r=u);var o,s,a;return r}())===u&&(t=function(){var r,t,e;r=Ma,(t=vf())!==u&&ub()!==u&&Cf()!==u&&ub()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=qi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=qi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=qi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Gb.add(`rename::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`))),t={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"rename",table:n}},r=t):(Ma=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=Ma,(e=function(){var t,e,n,o;t=Ma,"call"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(wu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CALL"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u&&(n=mb())!==u?(Pa=t,o=n,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"call",expr:o}},t=e):(Ma=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=Ma,(e=function(){var t,e,n,o;t=Ma,"use"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(fo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u&&(n=rl())!==u?(Pa=t,o=n,Gb.add(`use::${o}::null`),e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"use",db:o,...xb()}},t=e):(Ma=t,t=u);var o;return t}())===u&&(t=function(){var t;(t=function(){var r,t,e,n;r=Ma,(t=nf())!==u&&ub()!==u&&Cf()!==u&&ub()!==u&&(e=Ki())!==u&&ub()!==u&&(n=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=fi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=fi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=fi())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=r,s=n,(o=e)&&o.length>0&&o.forEach(r=>Gb.add(`alter::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"alter",table:o,expr:s}},r=t):(Ma=r,r=u);var o,s;return r}())===u&&(t=function(){var r,t,e,n,o;r=Ma,(t=nf())!==u&&ub()!==u&&(e=Ef())!==u&&ub()!==u&&(n=fl())!==u&&ub()!==u?((o=pi())===u&&(o=bi())===u&&(o=vi()),o!==u?(Pa=r,t=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"alter",keyword:n,schema:t,expr:e}}}(e,n,o),r=t):(Ma=r,r=u)):(Ma=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,s;t=Ma,(e=nf())!==u&&ub()!==u?("domain"===r.substr(Ma,6).toLowerCase()?(n=r.substr(Ma,6),Ma+=6):(n=u,0===Fa&&qa(L)),n===u&&("type"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(m))),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u?((s=pi())===u&&(s=bi())===u&&(s=vi()),s!==u?(Pa=t,e=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"alter",keyword:n,name:{schema:t.db,name:t.table},expr:e}}}(n,o,s),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f;t=Ma,(e=nf())!==u&&ub()!==u?("function"===r.substr(Ma,8).toLowerCase()?(n=r.substr(Ma,8),Ma+=8):(n=u,0===Fa&&qa(z)),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u?(s=Ma,(a=zp())!==u&&(i=ub())!==u?((c=li())===u&&(c=null),c!==u&&(l=ub())!==u&&(f=Zp())!==u?s=a=[a,i,c,l,f]:(Ma=s,s=u)):(Ma=s,s=u),s===u&&(s=null),s!==u&&(a=ub())!==u?((i=pi())===u&&(i=bi())===u&&(i=vi()),i!==u?(Pa=t,e=function(r,t,e,n){const o=r.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const u={};return e&&e[0]&&(u.parentheses=!0),u.expr=e&&e[2],{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:u,expr:n}}}(n,o,s,i),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,(e=nf())!==u&&ub()!==u?("aggregate"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(K)),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(s=function(){var r,t,e;r=Ma,(t=Kp())!==u&&(Pa=r,t=[{name:"*"}]);(r=t)===u&&(r=Ma,(t=li())===u&&(t=null),t!==u&&ub()!==u&&Rf()!==u&&ub()!==u&&If()!==u&&ub()!==u&&(e=li())!==u?(Pa=r,t=function(r,t){const e=r||[];return e.orderby=t,e}(t,e),r=t):(Ma=r,r=u),r===u&&(r=li()));return r}())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((a=pi())===u&&(a=bi())===u&&(a=vi()),a!==u?(Pa=t,e=function(r,t,e,n){const o=r.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n},...xb()}}(n,o,s,a),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}());return t}())===u&&(t=function(){var t,e,n,o;t=Ma,(e=mf())!==u&&ub()!==u?((n=function(){var t,e,n,o;t=Ma,"global"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Bs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="GLOBAL"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ma,"session"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Hs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SESSION"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ma,"local"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(b));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="LOCAL"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ma,"persist"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ws));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="PERSIST"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ma,"persist_only"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(Ys));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="PERSIST_ONLY"):(Ma=t,t=u)):(Ma=t,t=u);return t}()),n===u&&(n=null),n!==u&&ub()!==u&&(o=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=bb())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=bb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=bb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=t,s=n,(a=o).keyword=s,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"set",keyword:s,expr:a}},t=e):(Ma=t,t=u)):(Ma=t,t=u);var s,a;return t}())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,(e=function(){var t,e,n,o;t=Ma,"lock"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(sr));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u?((n=Cf())===u&&(n=null),n!==u&&ub()!==u&&(o=Ki())!==u&&ub()!==u?((s=function(){var t,e,n,o;t=Ma,"in"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Xr));e!==u&&ub()!==u?("access share"===r.substr(Ma,12).toLowerCase()?(n=r.substr(Ma,12),Ma+=12):(n=u,0===Fa&&qa(Qr)),n===u&&("row share"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(Kr)),n===u&&("row exclusive"===r.substr(Ma,13).toLowerCase()?(n=r.substr(Ma,13),Ma+=13):(n=u,0===Fa&&qa(zr)),n===u&&("share update exclusive"===r.substr(Ma,22).toLowerCase()?(n=r.substr(Ma,22),Ma+=22):(n=u,0===Fa&&qa(Zr)),n===u&&("share row exclusive"===r.substr(Ma,19).toLowerCase()?(n=r.substr(Ma,19),Ma+=19):(n=u,0===Fa&&qa(Jr)),n===u&&("exclusive"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(ir)),n===u&&("access exclusive"===r.substr(Ma,16).toLowerCase()?(n=r.substr(Ma,16),Ma+=16):(n=u,0===Fa&&qa(rt)),n===u&&("share"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(tt))))))))),n!==u&&ub()!==u?("mode"===r.substr(Ma,4).toLowerCase()?(o=r.substr(Ma,4),Ma+=4):(o=u,0===Fa&&qa(et)),o!==u?(Pa=t,e={mode:`in ${n.toLowerCase()} mode`},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(s=null),s!==u&&ub()!==u?("nowait"===r.substr(Ma,6).toLowerCase()?(a=r.substr(Ma,6),Ma+=6):(a=u,0===Fa&&qa(nt)),a===u&&(a=null),a!==u?(Pa=t,i=n,l=s,f=a,(c=o)&&c.forEach(r=>Gb.add(`lock::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var i,c,l,f;return t}())===u&&(t=function(){var t,e,n;t=Ma,(e=tf())!==u&&ub()!==u?("tables"===r.substr(Ma,6).toLowerCase()?(n=r.substr(Ma,6),Ma+=6):(n=u,0===Fa&&qa(ot)),n!==u?(Pa=t,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"show",keyword:"tables"}},t=e):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,(e=tf())!==u&&ub()!==u&&(n=Eb())!==u?(Pa=t,o=n,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"show",keyword:"var",var:o}},t=e):(Ma=t,t=u));var o;return t}())===u&&(t=function(){var t,e,n,o;t=Ma,(e=function(){var t,e,n,o;t=Ma,"deallocate"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(No));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DEALLOCATE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u?("prepare"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa(ut)),n===u&&(n=null),n!==u&&ub()!==u?((o=fl())===u&&(o=kf()),o!==u?(Pa=t,s=n,a=o,e={tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"deallocate",keyword:s,expr:{type:"default",value:a}}},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var s,a;return t}()),t}function Ka(){var t;return(t=Za())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=Ma,(t=ub())!==u?((e=xi())===u&&(e=null),e!==u&&ub()!==u&&uf()!==u&&ub()!==u&&(n=Ki())!==u&&ub()!==u&&mf()!==u&&ub()!==u&&(o=wc())!==u&&ub()!==u?((s=Wi())===u&&(s=null),s!==u&&ub()!==u?((a=oc())===u&&(a=null),a!==u&&ub()!==u?((i=Lc())===u&&(i=null),i!==u?(Pa=r,t=function(r,t,e,n,o,u){const s={},a=r=>{const{server:t,db:e,schema:n,as:o,table:u,join:a}=r,i=a?"select":"update",c=[t,e,n].filter(Boolean).join(".")||null;e&&(s[u]=c),u&&Gb.add(`${i}::${c}::${u}`)};return t&&t.forEach(a),n&&n.forEach(a),e&&e.forEach(r=>{if(r.table){const t=Ub(r.table);Gb.add(`update::${s[t]||null}::${t}`)}$b.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Gb),columnList:Mb($b),ast:{with:r,type:"update",table:t,set:e,from:n,where:o,returning:u}}}(e,n,o,s,a,i),r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=Ac())!==u&&ub()!==u?((n=hf())===u&&(n=null),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u?((s=Ec())===u&&(s=null),s!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(a=Zc())!==u&&ub()!==u&&Zp()!==u&&ub()!==u&&(i=Cc())!==u&&ub()!==u?((c=function(){var t,e,n,o;t=Ma,Af()!==u&&ub()!==u?("conflict"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Dt)),e!==u&&ub()!==u?((n=function(){var r,t;r=Ma,zp()!==u&&ub()!==u&&(t=uc())!==u&&ub()!==u&&Zp()!==u?(Pa=r,r={type:"column",expr:t,parentheses:!0}):(Ma=r,r=u);return r}())===u&&(n=null),n!==u&&ub()!==u&&(o=function(){var t,e,n,o,s;t=Ma,"do"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Mt));e!==u&&ub()!==u?("nothing"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa(Pt)),n!==u?(Pa=t,t=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,"do"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Mt)),e!==u&&ub()!==u&&(n=uf())!==u&&ub()!==u&&mf()!==u&&ub()!==u&&(o=wc())!==u&&ub()!==u?((s=oc())===u&&(s=null),s!==u?(Pa=t,t=e={keyword:"do",expr:{type:"update",set:o,where:s}}):(Ma=t,t=u)):(Ma=t,t=u));return t}())!==u?(Pa=t,t={type:"conflict",keyword:"on",target:n,action:o}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(c=null),c!==u&&ub()!==u?((l=Lc())===u&&(l=null),l!==u?(Pa=t,e=function(r,t,e,n,o,u,s){if(t&&(Gb.add(`insert::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>$b.add(`insert::${r}::${t}`))}return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r,table:[t],columns:n,values:o,partition:e,conflict:u,returning:s}}}(e,o,s,a,i,c,l),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=Ma,(t=Ac())!==u&&ub()!==u?((e=yf())===u&&(e=null),e!==u&&ub()!==u?((n=hf())===u&&(n=null),n!==u&&ub()!==u&&(o=tc())!==u&&ub()!==u?((s=Ec())===u&&(s=null),s!==u&&ub()!==u&&(a=Cc())!==u&&ub()!==u?((i=Lc())===u&&(i=null),i!==u?(Pa=r,t=function(r,t,e,n,o,u,s){n&&(Gb.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),$b.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:a,returning:s}}}(t,e,n,o,s,a,i),r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o;r=Ma,(t=lf())!==u&&ub()!==u?((e=Ki())===u&&(e=null),e!==u&&ub()!==u&&(n=Wi())!==u&&ub()!==u?((o=oc())===u&&(o=null),o!==u?(Pa=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,schema:n,table:o,join:u}=r,s=u?"select":"delete",a=[t,n].filter(Boolean).join(".")||null;o&&Gb.add(`${s}::${a}::${o}`),u||$b.add(`delete::${o}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0,...xb()}]}return{tableList:Array.from(Gb),columnList:Mb($b),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);return r}())===u&&(t=Qa())===u&&(t=function(){var r,t;r=[],t=pb();for(;t!==u;)r.push(t),t=pb();return r}()),t}function za(){var t,e;return t=Ma,function(){var t,e,n,o;t=Ma,"union"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Do));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u?((e=kf())===u&&(e=null),e!==u?(Pa=t,t=e?"union all":"union"):(Ma=t,t=u)):(Ma=t,t=u),t}function Za(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=ji())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=za())!==u&&(a=ub())!==u&&(i=ji())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=za())!==u&&(a=ub())!==u&&(i=ji())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u&&(n=ub())!==u?((o=vc())===u&&(o=null),o!==u&&(s=ub())!==u?((a=hc())===u&&(a=null),a!==u?(Pa=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&n.value&&n.value.length>0&&(r._limit=n),{tableList:Array.from(Gb),columnList:Mb($b),ast:r}}(t,e,o,a)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Ja(){var t,e;return t=Ma,"if"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(i)),e!==u&&ub()!==u&&Hf()!==u&&ub()!==u&&Ff()!==u?(Pa=t,t=e="IF NOT EXISTS"):(Ma=t,t=u),t}function ri(){var t,e,n;return t=Ma,"check_option"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(d)),e!==u&&ub()!==u&&Gp()!==u&&ub()!==u?("cascaded"===r.substr(Ma,8).toLowerCase()?(n=r.substr(Ma,8),Ma+=8):(n=u,0===Fa&&qa(p)),n===u&&("local"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(b))),n!==u?(Pa=t,t=e={type:"check_option",value:n,symbol:"="}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"security_barrier"===r.substr(Ma,16).toLowerCase()?(e=r.substr(Ma,16),Ma+=16):(e=u,0===Fa&&qa(h)),e===u&&("security_invoker"===r.substr(Ma,16).toLowerCase()?(e=r.substr(Ma,16),Ma+=16):(e=u,0===Fa&&qa(w))),e!==u&&ub()!==u&&Gp()!==u&&ub()!==u&&(n=Gl())!==u?(Pa=t,t=e=function(r,t){return{type:r.toLowerCase(),value:t.value?"true":"false",symbol:"="}}(e,n)):(Ma=t,t=u)),t}function ti(){var t;return(t=function(){var t,e,n,o,s,a;return t=Ma,"increment"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(C)),e!==u&&ub()!==u?((n=If())===u&&(n=null),n!==u&&ub()!==u&&(o=Wl())!==u?(Pa=t,s=e,a=o,t=e={resource:"sequence",prefix:n?s.toLowerCase()+" by":s.toLowerCase(),value:a}):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=Ma,"minvalue"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(E)),e!==u&&ub()!==u&&(n=Wl())!==u?(Pa=t,t=e=A(e,n)):(Ma=t,t=u),t===u&&(t=Ma,"no"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(g)),e!==u&&ub()!==u?("minvalue"===r.substr(Ma,8).toLowerCase()?(n=r.substr(Ma,8),Ma+=8):(n=u,0===Fa&&qa(E)),n!==u?(Pa=t,t=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(Ma=t,t=u)):(Ma=t,t=u)),t}())===u&&(t=function(){var t,e,n;return t=Ma,"maxvalue"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(T)),e!==u&&ub()!==u&&(n=Wl())!==u?(Pa=t,t=e=A(e,n)):(Ma=t,t=u),t===u&&(t=Ma,"no"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(g)),e!==u&&ub()!==u?("maxvalue"===r.substr(Ma,8).toLowerCase()?(n=r.substr(Ma,8),Ma+=8):(n=u,0===Fa&&qa(T)),n!==u?(Pa=t,t=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(Ma=t,t=u)):(Ma=t,t=u)),t}())===u&&(t=function(){var t,e,n,o,s,a;return t=Ma,"start"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(_)),e!==u&&ub()!==u?((n=jf())===u&&(n=null),n!==u&&ub()!==u&&(o=Wl())!==u?(Pa=t,s=e,a=o,t=e={resource:"sequence",prefix:n?s.toLowerCase()+" with":s.toLowerCase(),value:a}):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=Ma,"cache"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(S)),e!==u&&ub()!==u&&(n=Wl())!==u?(Pa=t,t=e=A(e,n)):(Ma=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=Ma,"no"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(g)),e===u&&(e=null),e!==u&&ub()!==u?("cycle"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(j)),n!==u?(Pa=t,t=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=Ma,"owned"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(x)),e!==u&&ub()!==u&&If()!==u&&ub()!==u?("none"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(I)),n!==u?(Pa=t,t=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"owned"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(x)),e!==u&&ub()!==u&&If()!==u&&ub()!==u&&(n=zc())!==u?(Pa=t,t=e={resource:"sequence",prefix:"owned by",value:n}):(Ma=t,t=u)),t}()),t}function ei(){var t,e,n,o,s,a,i,c,l,f,p,b,v,y;return t=Ma,(e=Oc())!==u&&ub()!==u?((n=si())===u&&(n=null),n!==u&&ub()!==u?((o=rl())===u&&(o=null),o!==u&&ub()!==u?((s=Nf())===u&&(s=Of()),s===u&&(s=null),s!==u&&ub()!==u?(a=Ma,"nulls"===r.substr(Ma,5).toLowerCase()?(i=r.substr(Ma,5),Ma+=5):(i=u,0===Fa&&qa(R)),i!==u&&(c=ub())!==u?("first"===r.substr(Ma,5).toLowerCase()?(l=r.substr(Ma,5),Ma+=5):(l=u,0===Fa&&qa(N)),l===u&&("last"===r.substr(Ma,4).toLowerCase()?(l=r.substr(Ma,4),Ma+=4):(l=u,0===Fa&&qa(O))),l!==u?a=i=[i,c,l]:(Ma=a,a=u)):(Ma=a,a=u),a===u&&(a=null),a!==u?(Pa=t,f=e,p=n,b=o,v=s,y=a,t=e={...f,collate:p,opclass:b,order_by:v&&v.toLowerCase(),nulls:y&&`${y[0].toLowerCase()} ${y[2].toLowerCase()}`}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function ni(){var r;return(r=ui())===u&&(r=hi())===u&&(r=wi())===u&&(r=mi()),r}function oi(){var t,e,n,o;return(t=function(){var r,t,e;r=Ma,(t=Dl())===u&&(t=Pl());t!==u&&ub()!==u?((e=ai())===u&&(e=null),e!==u?(Pa=r,o=e,(n=t)&&!n.value&&(n.value="null"),r=t={default_val:o,nullable:n}):(Ma=r,r=u)):(Ma=r,r=u);var n,o;r===u&&(r=Ma,(t=ai())!==u&&ub()!==u?((e=Dl())===u&&(e=Pl()),e===u&&(e=null),e!==u?(Pa=r,t=function(r,t){return t&&!t.value&&(t.value="null"),{default_val:r,nullable:t}}(t,e),r=t):(Ma=r,r=u)):(Ma=r,r=u));return r}())===u&&(t=Ma,"auto_increment"===r.substr(Ma,14).toLowerCase()?(e=r.substr(Ma,14),Ma+=14):(e=u,0===Fa&&qa(k)),e!==u&&(Pa=t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=Ma,"unique"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(U)),e!==u&&ub()!==u?("key"===r.substr(Ma,3).toLowerCase()?(n=r.substr(Ma,3),Ma+=3):(n=u,0===Fa&&qa(M)),n===u&&(n=null),n!==u?(Pa=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"primary"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(P)),e===u&&(e=null),e!==u&&ub()!==u?("key"===r.substr(Ma,3).toLowerCase()?(n=r.substr(Ma,3),Ma+=3):(n=u,0===Fa&&qa(M)),n!==u?(Pa=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=ib())!==u&&(Pa=t,e={comment:e}),(t=e)===u&&(t=Ma,(e=si())!==u&&(Pa=t,e={collate:e}),(t=e)===u&&(t=Ma,(e=function(){var t,e,n;t=Ma,"column_format"===r.substr(Ma,13).toLowerCase()?(e=r.substr(Ma,13),Ma+=13):(e=u,0===Fa&&qa(D));e!==u&&ub()!==u?("fixed"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(G)),n===u&&("dynamic"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa($)),n===u&&("default"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa(F)))),n!==u?(Pa=t,e={type:"column_format",value:n.toLowerCase()},t=e):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={column_format:e}),(t=e)===u&&(t=Ma,(e=function(){var t,e,n;t=Ma,"storage"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(B));e!==u&&ub()!==u?("disk"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(H)),n===u&&("memory"===r.substr(Ma,6).toLowerCase()?(n=r.substr(Ma,6),Ma+=6):(n=u,0===Fa&&qa(W))),n!==u?(Pa=t,e={type:"storage",value:n.toLowerCase()},t=e):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={storage:e}),(t=e)===u&&(t=Ma,(e=Ei())!==u&&(Pa=t,e={reference_definition:e}),(t=e)===u&&(t=Ma,(e=Ti())!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u&&(o=Jc())!==u?(Pa=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u)))))))))),t}function ui(){var r,t,e,n,o,s,a;return r=Ma,(t=zc())!==u&&ub()!==u&&(e=Ab())!==u&&ub()!==u?((n=function(){var r,t,e,n,o,s;if(r=Ma,(t=oi())!==u)if(ub()!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=oi())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=oi())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;return r}())===u&&(n=null),n!==u?(Pa=r,o=t,s=e,a=n,$b.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:s,resource:"column",...a||{}}):(Ma=r,r=u)):(Ma=r,r=u),r}function si(){var t,e,n;return t=Ma,function(){var t,e,n,o;t=Ma,"collate"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Mr));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="COLLATE"):(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u?((e=Gp())===u&&(e=null),e!==u&&ub()!==u&&(n=rl())!==u?(Pa=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Ma=t,t=u)):(Ma=t,t=u),t}function ai(){var r,t;return r=Ma,Zl()!==u&&ub()!==u&&(t=Oc())!==u?(Pa=r,r={type:"default",value:t}):(Ma=r,r=u),r}function ii(){var t,e;return t=Ma,(e=Pf())===u&&("out"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(V)),e===u&&("variadic"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(X)),e===u&&("inout"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Q))))),e!==u&&(Pa=t,e=e.toUpperCase()),t=e}function ci(){var r,t,e,n;return r=Ma,(t=ii())===u&&(t=null),t!==u&&ub()!==u&&(e=Ab())!==u?(Pa=r,r=t={mode:t,type:e}):(Ma=r,r=u),r===u&&(r=Ma,(t=ii())===u&&(t=null),t!==u&&ub()!==u&&(e=fl())!==u&&ub()!==u&&(n=Ab())!==u?(Pa=r,r=t=function(r,t,e){return{mode:r,name:t,type:e}}(t,e,n)):(Ma=r,r=u)),r}function li(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=ci())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ci())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=ci())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function fi(){var r;return(r=function(){var r,t,e,n;r=Ma,(t=$p())!==u&&ub()!==u?((e=Fp())===u&&(e=null),e!==u&&ub()!==u&&(n=ui())!==u?(Pa=r,o=e,s=n,t={action:"add",...s,keyword:o,resource:"column",type:"alter"},r=t):(Ma=r,r=u)):(Ma=r,r=u);var o,s;return r}())===u&&(r=function(){var r,t;r=Ma,$p()!==u&&ub()!==u&&(t=mi())!==u?(Pa=r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(Ma=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Ma,ef()!==u&&ub()!==u?((t=Fp())===u&&(t=null),t!==u&&ub()!==u&&(e=zc())!==u?(Pa=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(Ma=r,r=u)):(Ma=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Ma,(t=$p())!==u&&ub()!==u&&(e=hi())!==u?(Pa=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ma=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e;r=Ma,(t=$p())!==u&&ub()!==u&&(e=wi())!==u?(Pa=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ma=r,r=u);var n;return r}())===u&&(r=pi())===u&&(r=yi())===u&&(r=di()),r}function pi(){var r,t,e,n,o;return r=Ma,vf()!==u&&ub()!==u?((t=Jl())===u&&(t=Lf()),t===u&&(t=null),t!==u&&ub()!==u&&(e=rl())!==u?(Pa=r,o=e,r={action:"rename",type:"alter",resource:"table",keyword:(n=t)&&n[0].toLowerCase(),table:o}):(Ma=r,r=u)):(Ma=r,r=u),r}function bi(){var t,e,n;return t=Ma,"owner"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Z)),e!==u&&ub()!==u&&Jl()!==u&&ub()!==u?((n=rl())===u&&("current_role"===r.substr(Ma,12).toLowerCase()?(n=r.substr(Ma,12),Ma+=12):(n=u,0===Fa&&qa(J)),n===u&&("current_user"===r.substr(Ma,12).toLowerCase()?(n=r.substr(Ma,12),Ma+=12):(n=u,0===Fa&&qa(rr)),n===u&&("session_user"===r.substr(Ma,12).toLowerCase()?(n=r.substr(Ma,12),Ma+=12):(n=u,0===Fa&&qa(tr))))),n!==u?(Pa=t,t=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(Ma=t,t=u)):(Ma=t,t=u),t}function vi(){var r,t;return r=Ma,mf()!==u&&ub()!==u&&Ef()!==u&&ub()!==u&&(t=rl())!==u?(Pa=r,r={action:"set",type:"alter",resource:"table",keyword:"schema",table:t}):(Ma=r,r=u),r}function yi(){var t,e,n,o;return t=Ma,"algorithm"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(er)),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u?("default"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(F)),o===u&&("instant"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(nr)),o===u&&("inplace"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(or)),o===u&&("copy"===r.substr(Ma,4).toLowerCase()?(o=r.substr(Ma,4),Ma+=4):(o=u,0===Fa&&qa(ur))))),o!==u?(Pa=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function di(){var t,e,n,o;return t=Ma,"lock"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(sr)),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u?("default"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(F)),o===u&&("none"===r.substr(Ma,4).toLowerCase()?(o=r.substr(Ma,4),Ma+=4):(o=u,0===Fa&&qa(I)),o===u&&("shared"===r.substr(Ma,6).toLowerCase()?(o=r.substr(Ma,6),Ma+=6):(o=u,0===Fa&&qa(ar)),o===u&&("exclusive"===r.substr(Ma,9).toLowerCase()?(o=r.substr(Ma,9),Ma+=9):(o=u,0===Fa&&qa(ir))))),o!==u?(Pa=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function hi(){var r,t,e,n,o,s,a,i;return r=Ma,(t=Bp())===u&&(t=Hp()),t!==u&&ub()!==u?((e=cl())===u&&(e=null),e!==u&&ub()!==u?((n=Vi())===u&&(n=null),n!==u&&ub()!==u&&(o=Ri())!==u&&ub()!==u?((s=Xi())===u&&(s=null),s!==u&&ub()!==u?(Pa=r,a=n,i=s,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:a,resource:"index",index_options:i}):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r}function wi(){var t,e,n,o,s,a,i,c,l;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"fulltext"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(ua));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="FULLTEXT"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Ma,"spatial"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(sa));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SPATIAL"):(Ma=t,t=u)):(Ma=t,t=u);return t}()),e!==u&&ub()!==u?((n=Bp())===u&&(n=Hp()),n===u&&(n=null),n!==u&&ub()!==u?((o=cl())===u&&(o=null),o!==u&&ub()!==u&&(s=Ri())!==u&&ub()!==u?((a=Xi())===u&&(a=null),a!==u&&ub()!==u?(Pa=t,i=e,l=a,t=e={index:o,definition:s,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function mi(){var t;return(t=function(){var t,e,n,o,s,a;t=Ma,(e=Li())===u&&(e=null);e!==u&&ub()!==u?("primary key"===r.substr(Ma,11).toLowerCase()?(n=r.substr(Ma,11),Ma+=11):(n=u,0===Fa&&qa(cr)),n!==u&&ub()!==u?((o=Vi())===u&&(o=null),o!==u&&ub()!==u&&(s=Ri())!==u&&ub()!==u?((a=Xi())===u&&(a=null),a!==u?(Pa=t,c=n,l=o,f=s,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=Ma,(t=Li())===u&&(t=null);t!==u&&ub()!==u&&(e=Wp())!==u&&ub()!==u?((n=Bp())===u&&(n=Hp()),n===u&&(n=null),n!==u&&ub()!==u?((o=cl())===u&&(o=null),o!==u&&ub()!==u?((s=Vi())===u&&(s=null),s!==u&&ub()!==u&&(a=Ri())!==u&&ub()!==u?((i=Xi())===u&&(i=null),i!==u?(Pa=r,l=e,f=n,p=o,b=s,v=a,y=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);var c,l,f,p,b,v,y;return r}())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,(e=Li())===u&&(e=null);e!==u&&ub()!==u?("foreign key"===r.substr(Ma,11).toLowerCase()?(n=r.substr(Ma,11),Ma+=11):(n=u,0===Fa&&qa(lr)),n!==u&&ub()!==u?((o=cl())===u&&(o=null),o!==u&&ub()!==u&&(s=Ri())!==u&&ub()!==u?((a=Ei())===u&&(a=null),a!==u?(Pa=t,c=n,l=o,f=s,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var i,c,l,f,p;return t}())===u&&(t=Ci()),t}function Li(){var r,t,e,n;return r=Ma,(t=qp())!==u&&ub()!==u?((e=rl())===u&&(e=null),e!==u?(Pa=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(Ma=r,r=u)):(Ma=r,r=u),r}function Ci(){var t,e,n,o,s,a,i;return t=Ma,(e=Li())===u&&(e=null),e!==u&&ub()!==u?("check"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(v)),n!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(o=kc())!==u&&ub()!==u&&Zp()!==u?(Pa=t,a=n,i=o,t=e={constraint:(s=e)&&s.constraint,definition:[i],constraint_type:a.toLowerCase(),keyword:s&&s.keyword,resource:"constraint"}):(Ma=t,t=u)):(Ma=t,t=u),t}function Ei(){var t,e,n,o,s,a,i,c,l,f;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"references"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(la));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REFERENCES"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u&&(n=tc())!==u&&ub()!==u&&(o=Ri())!==u&&ub()!==u?("match full"===r.substr(Ma,10).toLowerCase()?(s=r.substr(Ma,10),Ma+=10):(s=u,0===Fa&&qa(fr)),s===u&&("match partial"===r.substr(Ma,13).toLowerCase()?(s=r.substr(Ma,13),Ma+=13):(s=u,0===Fa&&qa(pr)),s===u&&("match simple"===r.substr(Ma,12).toLowerCase()?(s=r.substr(Ma,12),Ma+=12):(s=u,0===Fa&&qa(br)))),s===u&&(s=null),s!==u&&ub()!==u?((a=Ai())===u&&(a=null),a!==u&&ub()!==u?((i=Ai())===u&&(i=null),i!==u?(Pa=t,c=s,l=a,f=i,t=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=Ai())!==u&&(Pa=t,e={on_action:[e]}),t=e),t}function Ai(){var t,e,n,o;return t=Ma,Af()!==u&&ub()!==u?((e=lf())===u&&(e=uf()),e!==u&&ub()!==u&&(n=function(){var t,e,n;t=Ma,(e=Op())!==u&&ub()!==u&&zp()!==u&&ub()!==u?((n=_c())===u&&(n=null),n!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,"restrict"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(q)),e===u&&("cascade"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Y)),e===u&&("set null"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(vr)),e===u&&("no action"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(yr)),e===u&&("set default"===r.substr(Ma,11).toLowerCase()?(e=r.substr(Ma,11),Ma+=11):(e=u,0===Fa&&qa(dr)),e===u&&(e=Op()))))),e!==u&&(Pa=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(Pa=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Ma=t,t=u)):(Ma=t,t=u),t}function gi(){var t,e,n,o,s,a,i;return t=Ma,(e=ff())===u&&(e=lf())===u&&(e=Rp()),e!==u&&(Pa=t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===u&&(t=Ma,(e=uf())!==u&&ub()!==u?(n=Ma,"of"===r.substr(Ma,2).toLowerCase()?(o=r.substr(Ma,2),Ma+=2):(o=u,0===Fa&&qa(gr)),o!==u&&(s=ub())!==u&&(a=uc())!==u?n=o=[o,s,a]:(Ma=n,n=u),n===u&&(n=null),n!==u?(Pa=t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):(Ma=t,t=u)):(Ma=t,t=u)),t}function Ti(){var t,e,n;return t=Ma,"character"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(Or)),e!==u&&ub()!==u?("set"===r.substr(Ma,3).toLowerCase()?(n=r.substr(Ma,3),Ma+=3):(n=u,0===Fa&&qa(kr)),n!==u?(Pa=t,t=e="CHARACTER SET"):(Ma=t,t=u)):(Ma=t,t=u),t}function _i(){var t,e,n,o,s,a,i,c,l;return t=Ma,(e=Zl())===u&&(e=null),e!==u&&ub()!==u?((n=Ti())===u&&("charset"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa(Ur)),n===u&&("collate"===r.substr(Ma,7).toLowerCase()?(n=r.substr(Ma,7),Ma+=7):(n=u,0===Fa&&qa(Mr)))),n!==u&&ub()!==u?((o=Gp())===u&&(o=null),o!==u&&ub()!==u&&(s=Jc())!==u?(Pa=t,i=n,c=o,l=s,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function Si(){var t,e,n,o,s,a,i,c,l;return t=Ma,"auto_increment"===r.substr(Ma,14).toLowerCase()?(e=r.substr(Ma,14),Ma+=14):(e=u,0===Fa&&qa(k)),e===u&&("avg_row_length"===r.substr(Ma,14).toLowerCase()?(e=r.substr(Ma,14),Ma+=14):(e=u,0===Fa&&qa(Pr)),e===u&&("key_block_size"===r.substr(Ma,14).toLowerCase()?(e=r.substr(Ma,14),Ma+=14):(e=u,0===Fa&&qa(Dr)),e===u&&("max_rows"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Gr)),e===u&&("min_rows"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa($r)),e===u&&("stats_sample_pages"===r.substr(Ma,18).toLowerCase()?(e=r.substr(Ma,18),Ma+=18):(e=u,0===Fa&&qa(Fr))))))),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u&&(o=Wl())!==u?(Pa=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=_i())===u&&(t=Ma,(e=Yp())===u&&("connection"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(Br))),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u&&(o=$l())!==u?(Pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"compression"===r.substr(Ma,11).toLowerCase()?(e=r.substr(Ma,11),Ma+=11):(e=u,0===Fa&&qa(Hr)),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u?(o=Ma,39===r.charCodeAt(Ma)?(s="'",Ma++):(s=u,0===Fa&&qa(Wr)),s!==u?("zlib"===r.substr(Ma,4).toLowerCase()?(a=r.substr(Ma,4),Ma+=4):(a=u,0===Fa&&qa(Yr)),a===u&&("lz4"===r.substr(Ma,3).toLowerCase()?(a=r.substr(Ma,3),Ma+=3):(a=u,0===Fa&&qa(qr)),a===u&&("none"===r.substr(Ma,4).toLowerCase()?(a=r.substr(Ma,4),Ma+=4):(a=u,0===Fa&&qa(I)))),a!==u?(39===r.charCodeAt(Ma)?(i="'",Ma++):(i=u,0===Fa&&qa(Wr)),i!==u?o=s=[s,a,i]:(Ma=o,o=u)):(Ma=o,o=u)):(Ma=o,o=u),o!==u?(Pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"engine"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Vr)),e!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u&&(o=fl())!==u?(Pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u)))),t}function ji(){var t,e,n,o,s,a,i;return t=Ma,(e=of())!==u&&(n=ub())!==u?(59===r.charCodeAt(Ma)?(o=";",Ma++):(o=u,0===Fa&&qa(st)),o!==u?(Pa=t,t=e={type:"select",...xb()}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Oi())===u&&(t=Ma,e=Ma,40===r.charCodeAt(Ma)?(n="(",Ma++):(n=u,0===Fa&&qa(at)),n!==u&&(o=ub())!==u&&(s=ji())!==u&&(a=ub())!==u?(41===r.charCodeAt(Ma)?(i=")",Ma++):(i=u,0===Fa&&qa(it)),i!==u?e=n=[n,o,s,a,i]:(Ma=e,e=u)):(Ma=e,e=u),e!==u&&(Pa=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function xi(){var r,t,e,n,o,s,a,i,c;if(r=Ma,jf()!==u)if(ub()!==u)if((t=Ii())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Ii())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Ii())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;return r===u&&(r=Ma,ub()!==u&&jf()!==u&&(t=ub())!==u&&(e=pf())!==u&&(n=ub())!==u&&(o=Ii())!==u?(Pa=r,(c=o).recursive=!0,r=[c]):(Ma=r,r=u)),r}function Ii(){var r,t,e,n,o;return r=Ma,(t=$l())===u&&(t=fl()),t!==u&&ub()!==u?((e=Ri())===u&&(e=null),e!==u&&ub()!==u&&Lf()!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=Ka())!==u&&ub()!==u&&Zp()!==u?(Pa=r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e,...xb()}):(Ma=r,r=u)):(Ma=r,r=u),r}function Ri(){var r,t;return r=Ma,zp()!==u&&ub()!==u&&(t=uc())!==u&&ub()!==u&&Zp()!==u?(Pa=r,r=t):(Ma=r,r=u),r}function Ni(){var r,t,e,n,o;return r=Ma,(t=Uf())!==u&&ub()!==u&&Af()!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(e=uc())!==u&&ub()!==u&&Zp()!==u?(Pa=r,n=t,o=e,console.lo,r=t={type:n+" ON",columns:o}):(Ma=r,r=u),r===u&&(r=Ma,(t=Uf())===u&&(t=null),t!==u&&(Pa=r,t=function(r){return{type:r}}(t)),r=t),r}function Oi(){var t,e,n,o,s,a,i,c,l,f,p,b,v,y,d,h,w;return t=Ma,ub()!==u?((e=xi())===u&&(e=null),e!==u&&ub()!==u&&of()!==u&&sb()!==u?((n=function(){var r,t,e,n,o,s;if(r=Ma,(t=ki())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=ki())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=ki())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())===u&&(n=null),n!==u&&ub()!==u?((o=Ni())===u&&(o=null),o!==u&&ub()!==u?((s=function(){var t,e,n,o,s;t=Ma,(e=rf())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Yl())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?("percent"===r.substr(Ma,7).toLowerCase()?(s=r.substr(Ma,7),Ma+=7):(s=u,0===Fa&&qa(ct)),s===u&&(s=null),s!==u?(Pa=t,e={value:o,percent:(a=s)&&a.toLowerCase(),parentheses:!0},t=e):(Ma=t,t=u)):(Ma=t,t=u);var a;t===u&&(t=Ma,(e=rf())!==u&&ub()!==u&&(n=Yl())!==u&&ub()!==u?("percent"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(ct)),o===u&&(o=null),o!==u?(Pa=t,e=function(r,t){return{value:r,percent:t&&t.toLowerCase()}}(n,o),t=e):(Ma=t,t=u)):(Ma=t,t=u));return t}())===u&&(s=null),s!==u&&ub()!==u&&(a=Mi())!==u&&ub()!==u?((i=Hi())===u&&(i=null),i!==u&&ub()!==u?((c=Wi())===u&&(c=null),c!==u&&ub()!==u?((l=Hi())===u&&(l=null),l!==u&&ub()!==u?((f=oc())===u&&(f=null),f!==u&&ub()!==u?((p=function(){var r,t,e;r=Ma,(t=xf())!==u&&ub()!==u&&If()!==u&&ub()!==u?((e=kf())===u&&(e=_c()),e!==u?(Pa=r,t={columns:"ALL"===(n=e)?[{type:"origin",value:"all"}]:n.value},r=t):(Ma=r,r=u)):(Ma=r,r=u);var n;return r}())===u&&(p=null),p!==u&&ub()!==u?((b=function(){var t,e;t=Ma,function(){var t,e,n,o;t=Ma,"having"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Yo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(e=kc())!==u?(Pa=t,t=e):(Ma=t,t=u);return t}())===u&&(b=null),b!==u&&ub()!==u?((v=function(){var t,e;t=Ma,function(){var t,e,n,o;t=Ma,"qualify"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(qo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(e=kc())!==u?(Pa=t,t=e):(Ma=t,t=u);return t}())===u&&(v=null),v!==u&&ub()!==u?((y=vc())===u&&(y=null),y!==u&&ub()!==u?((d=hc())===u&&(d=null),d!==u&&ub()!==u?((h=function(){var t,e;t=Ma,function(){var t,e,n,o;t=Ma,"window"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Vo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=sc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=t,t={keyword:"window",type:"window",expr:e}):(Ma=t,t=u);return t}())===u&&(h=null),h!==u&&ub()!==u?((w=Hi())===u&&(w=null),w!==u?(Pa=t,t=function(r,t,e,n,o,u,s,a,i,c,l,f,p,b,v,y){if(u&&a||u&&y||a&&y||u&&a&&y)throw new Error("A given SQL statement can contain at most one INTO clause");return s&&s.forEach(r=>r.table&&Gb.add(`select::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{with:r,type:"select",options:t,distinct:e,columns:o,into:{...u||a||y||{},position:(u?"column":a&&"from")||y&&"end"},from:s,where:i,groupby:c,having:l,qualify:f,orderby:p,top:n,limit:b,window:v,...xb()}}(e,n,o,s,a,i,c,l,f,p,b,v,y,d,h,w)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function ki(){var t,e;return t=Ma,(e=function(){var t;"sql_calc_found_rows"===r.substr(Ma,19).toLowerCase()?(t=r.substr(Ma,19),Ma+=19):(t=u,0===Fa&&qa(fa));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr(Ma,9).toLowerCase()?(t=r.substr(Ma,9),Ma+=9):(t=u,0===Fa&&qa(pa));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr(Ma,12).toLowerCase()?(t=r.substr(Ma,12),Ma+=12):(t=u,0===Fa&&qa(ba));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr(Ma,14).toLowerCase()?(t=r.substr(Ma,14),Ma+=14):(t=u,0===Fa&&qa(ya));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr(Ma,16).toLowerCase()?(t=r.substr(Ma,16),Ma+=16):(t=u,0===Fa&&qa(va));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr(Ma,17).toLowerCase()?(t=r.substr(Ma,17),Ma+=17):(t=u,0===Fa&&qa(da));return t}())),e!==u&&(Pa=t,e=e),t=e}function Ui(){var t,e,n,o;return t=Ma,"exclude"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(lt)),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=_c())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e={type:"function",name:{name:[{type:"origin",value:"exclude"}]},args:o}):(Ma=t,t=u),t===u&&(t=Ma,"exclude"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(lt)),e!==u&&ub()!==u&&(n=zc())!==u?(Pa=t,t=e={type:"function",name:{name:[{type:"origin",value:"exclude"}]},args:{type:"expr_list",value:[n]},args_parentheses:!1}):(Ma=t,t=u)),t}function Mi(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=$i())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=$i())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=$i())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Pi(){var r,t,e;return r=Ma,(t=Jp())!==u&&ub()!==u?((e=Wl())===u&&(e=$l()),e!==u&&ub()!==u&&rb()!==u?(Pa=r,r=t={brackets:!0,index:e}):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=Xp())!==u&&ub()!==u&&(e=rl())!==u?(Pa=r,r=t=function(r,t){return{notation:r,index:{type:"default",value:t}}}(t,e)):(Ma=r,r=u)),r}function Di(){var r,t,e,n,o,s;if(r=Ma,(t=Pi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Pi())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Pi())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e,1)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Gi(){var r,t,e,n,o;return r=Ma,(t=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Oc())!==u){for(e=[],n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf())===u&&(s=ob()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf())===u&&(s=ob()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);e!==u?(Pa=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=Rb(t[e][1],n,o)}return o}(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u&&ub()!==u?((e=Di())===u&&(e=null),e!==u?(Pa=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(Ma=r,r=u)):(Ma=r,r=u),r}function $i(){var r,t,e,n,o,s,a,i,c,l,f,p;if(r=Ma,(t=kf())===u&&(t=Kp()),t!==u&&(e=ub())!==u?((n=Ui())===u&&(n=null),n!==u?(Pa=r,p=n,$b.add("select::null::(.*)"),r=t={expr:{type:"column_ref",table:null,column:"*",suffix:p},as:null,...xb()}):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=Qc())!==u&&(Pa=r,t=function(r){return{expr:r,as:null,...xb()}}(t)),(r=t)===u)){if(r=Ma,(t=Gi())!==u)if((e=ub())!==u)if((n=kl())!==u)if((o=ub())!==u){for(s=[],a=Ma,(i=ub())!==u?((c=Hc())===u&&(c=Yc()),c!==u&&(l=ub())!==u&&(f=Gi())!==u?a=i=[i,c,l,f]:(Ma=a,a=u)):(Ma=a,a=u);a!==u;)s.push(a),a=Ma,(i=ub())!==u?((c=Hc())===u&&(c=Yc()),c!==u&&(l=ub())!==u&&(f=Gi())!==u?a=i=[i,c,l,f]:(Ma=a,a=u)):(Ma=a,a=u);s!==u&&(a=ub())!==u?((i=Bi())===u&&(i=null),i!==u?(Pa=r,r=t=function(r,t,e,n){return{...t,as:n,type:"cast",expr:r,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]},...xb()}}(t,n,s,i)):(Ma=r,r=u)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;r===u&&(r=Ma,t=Ma,(e=rl())!==u&&(n=ub())!==u&&(o=Xp())!==u?t=e=[e,n,o]:(Ma=t,t=u),t===u&&(t=null),t!==u?(e=Ma,(n=rl())!==u&&(o=ub())!==u&&(s=Xp())!==u?e=n=[n,o,s]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u?((o=kf())===u&&(o=Kp()),o!==u&&(s=ub())!==u?((a=Ui())===u&&(a=null),a!==u?(Pa=r,r=t=function(r,t,e){let n,o;r&&(n=null,o=r[0]),t&&(n=r[0],o=t[0]),$b.add(`select::${o}::(.*)`);return{expr:{type:"column_ref",table:o,schema:n,column:"*",suffix:e},as:null,...xb()}}(t,e,a)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=ol())!==u&&(e=ub())!==u?((n=Xp())===u&&(n=null),n!==u?(Pa=Ma,(o=(o=function(r,t){if(t)return!0}(0,n))?u:void 0)!==u&&(s=ub())!==u?((a=Bi())===u&&(a=null),a!==u?(Pa=r,r=t=function(r,t,e){return $b.add("select::null::"+r.value),{type:"expr",expr:{type:"column_ref",table:null,column:{expr:r}},as:e,...xb()}}(t,0,a)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=Gi())!==u&&(e=ub())!==u?((n=Bi())===u&&(n=null),n!==u?(Pa=r,r=t={type:"expr",expr:t,as:n,...xb()}):(Ma=r,r=u)):(Ma=r,r=u))))}return r}function Fi(){var r,t,e;return r=Ma,(t=Lf())===u&&(t=null),t!==u&&ub()!==u&&(e=tl())!==u?(Pa=r,r=t=e):(Ma=r,r=u),r}function Bi(){var r,t,e;return r=Ma,(t=Lf())!==u&&ub()!==u&&(e=al())!==u?(Pa=r,r=t=e):(Ma=r,r=u),r===u&&(r=Ma,(t=Lf())===u&&(t=null),t!==u&&ub()!==u&&(e=cl())!==u?(Pa=r,r=t=e):(Ma=r,r=u)),r}function Hi(){var t,e,n;return t=Ma,hf()!==u&&ub()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Cb())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Cb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Cb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=t,t={keyword:"var",type:"into",expr:e}):(Ma=t,t=u),t===u&&(t=Ma,hf()!==u&&ub()!==u?("outfile"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(ft)),e===u&&("dumpfile"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(pt))),e===u&&(e=null),e!==u&&ub()!==u?((n=$l())===u&&(n=rl()),n!==u?(Pa=t,t={keyword:e,type:"into",expr:n}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)),t}function Wi(){var t,e,n,o,s;return t=Ma,wf()!==u&&ub()!==u&&(e=Ki())!==u&&ub()!==u?((n=function(){var t,e,n,o,s;t=Ma,(e=function(){var t,e,n,o;t=Ma,"pivot"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(qs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="PIVOT"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=wl())!==u&&ub()!==u&&(o=Yi())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((s=Bi())===u&&(s=null),s!==u?(Pa=t,a=o,i=s,e={type:"pivot",expr:n,...a,as:i},t=e):(Ma=t,t=u)):(Ma=t,t=u);var a,i;t===u&&(t=Ma,(e=function(){var t,e,n,o;t=Ma,"unpivot"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Vs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="UNPIVOT"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=zc())!==u&&ub()!==u&&(o=Yi())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((s=Bi())===u&&(s=null),s!==u?(Pa=t,e=function(r,t,e){return{type:"unpivot",expr:r,...t,as:e}}(n,o,s),t=e):(Ma=t,t=u)):(Ma=t,t=u));return t}())===u&&(n=null),n!==u?(Pa=t,s=n,(o=e)[0]&&(o[0].operator=s),t=o):(Ma=t,t=u)):(Ma=t,t=u),t}function Yi(){var t,e,n,o;return t=Ma,"for"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(xr)),e!==u&&ub()!==u&&(n=zc())!==u&&ub()!==u&&(o=Fc())!==u?(Pa=t,t=e={column:n,in_expr:o}):(Ma=t,t=u),t}function qi(){var r,t,e;return r=Ma,(t=tc())!==u&&ub()!==u&&Jl()!==u&&ub()!==u&&(e=tc())!==u?(Pa=r,r=t=[t,e]):(Ma=r,r=u),r}function Vi(){var t,e;return t=Ma,Sf()!==u&&ub()!==u?("btree"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(bt)),e===u&&("hash"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(vt)),e===u&&("gist"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(yt)),e===u&&("gin"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(dt))))),e!==u?(Pa=t,t={keyword:"using",type:e.toLowerCase()}):(Ma=t,t=u)):(Ma=t,t=u),t}function Xi(){var r,t,e,n,o,s;if(r=Ma,(t=Qi())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qi())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qi())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Qi(){var t,e,n,o,s,a;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"key_block_size"===r.substr(Ma,14).toLowerCase()?(e=r.substr(Ma,14),Ma+=14):(e=u,0===Fa&&qa(Dr));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="KEY_BLOCK_SIZE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u?((n=Gp())===u&&(n=null),n!==u&&ub()!==u&&(o=Wl())!==u?(Pa=t,s=n,a=o,t=e={type:e.toLowerCase(),symbol:s,expr:a}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=fl())!==u&&ub()!==u&&(n=Gp())!==u&&ub()!==u?((o=Wl())===u&&(o=rl()),o!==u?(Pa=t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Vi())===u&&(t=Ma,"with"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(ht)),e!==u&&ub()!==u?("parser"===r.substr(Ma,6).toLowerCase()?(n=r.substr(Ma,6),Ma+=6):(n=u,0===Fa&&qa(wt)),n!==u&&ub()!==u&&(o=fl())!==u?(Pa=t,t=e={type:"with parser",expr:o}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"visible"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(mt)),e===u&&("invisible"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(Lt))),e!==u&&(Pa=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=ib())))),t}function Ki(){var r,t,e,n;if(r=Ma,(t=Ji())!==u){for(e=[],n=zi();n!==u;)e.push(n),n=zi();e!==u?(Pa=r,r=t=Ct(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function zi(){var r,t,e;return r=Ma,ub()!==u&&(t=Qp())!==u&&ub()!==u&&(e=Ji())!==u?(Pa=r,r=e):(Ma=r,r=u),r===u&&(r=Ma,ub()!==u&&(t=function(){var r,t,e,n,o,s,a,i,c,l,f;if(r=Ma,(t=rc())!==u)if(ub()!==u)if((e=Ji())!==u)if(ub()!==u)if((n=Sf())!==u)if(ub()!==u)if(zp()!==u)if(ub()!==u)if((o=Jc())!==u){for(s=[],a=Ma,(i=ub())!==u&&(c=Qp())!==u&&(l=ub())!==u&&(f=Jc())!==u?a=i=[i,c,l,f]:(Ma=a,a=u);a!==u;)s.push(a),a=Ma,(i=ub())!==u&&(c=Qp())!==u&&(l=ub())!==u&&(f=Jc())!==u?a=i=[i,c,l,f]:(Ma=a,a=u);s!==u&&(a=ub())!==u&&(i=Zp())!==u?(Pa=r,p=t,v=o,y=s,(b=e).join=p,b.using=Ob(v,y),r=t=b):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;var p,b,v,y;r===u&&(r=Ma,(t=rc())!==u&&ub()!==u&&(e=Ji())!==u&&ub()!==u?((n=nc())===u&&(n=null),n!==u?(Pa=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=rc())===u&&(t=za()),t!==u&&ub()!==u&&(e=zp())!==u&&ub()!==u?((n=Za())===u&&(n=Ki()),n!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((o=Bi())===u&&(o=null),o!==u&&(s=ub())!==u?((a=nc())===u&&(a=null),a!==u?(Pa=r,t=function(r,t,e,n){return Array.isArray(t)&&(t={type:"tables",expr:t}),t.parentheses=!0,{expr:t,as:e,join:r,on:n,...xb()}}(t,n,o,a),r=t):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)));return r}())!==u?(Pa=r,r=t):(Ma=r,r=u)),r}function Zi(){var t,e,n,o,s;return t=Ma,"rowcount"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Et)),e===u&&("timelimit"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(At))),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=Wl())!==u?(Pa=t,s=o,t=e={type:e.toLowerCase(),symbol:"=>",value:s}):(Ma=t,t=u)):(Ma=t,t=u),t}function Ji(){var t,e,n,o,s,a,i,c,l,f,p,b,v;if(t=Ma,(e=function(){var t;"dual"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(ta));return t}())!==u&&(Pa=t,e={type:"dual"}),(t=e)===u&&(t=Ma,(e=gc())!==u&&ub()!==u?((n=Fi())===u&&(n=null),n!==u?(Pa=t,t=e={expr:{type:"values",values:e},as:n,...xb()}):(Ma=t,t=u)):(Ma=t,t=u),t===u)){if(t=Ma,(e=Cf())!==u)if(ub()!==u)if((n=zp())!==u)if(ub()!==u)if("generator"===r.substr(Ma,9).toLowerCase()?(o=r.substr(Ma,9),Ma+=9):(o=u,0===Fa&&qa(Tt)),o!==u)if(ub()!==u)if((s=zp())!==u)if((a=ub())!==u){for(i=[],c=Zi();c!==u;)i.push(c),c=Zi();i!==u&&(c=ub())!==u&&(l=Zp())!==u&&(f=ub())!==u&&(p=Zp())!==u&&(b=ub())!==u?((v=Fi())===u&&(v=null),v!==u?(Pa=t,t=e=function(r,t){return{expr:{keyword:"table",type:"generator",generators:r},as:t,...xb()}}(i,v)):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;t===u&&(t=Ma,"lateral"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(_t)),e===u&&(e=null),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=Za())===u&&(o=gc()),o!==u&&ub()!==u&&(s=Zp())!==u&&(a=ub())!==u?((i=Fi())===u&&(i=null),i!==u?(Pa=t,t=e=function(r,t,e){return Array.isArray(t)&&(t={type:"values",values:t}),t.parentheses=!0,{prefix:r,expr:t,as:e,...xb()}}(e,o,i)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"lateral"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(_t)),e===u&&(e=null),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Ki())!==u&&ub()!==u&&(s=Zp())!==u&&(a=ub())!==u?((i=Fi())===u&&(i=null),i!==u?(Pa=t,t=e=function(r,t,e){return{prefix:r,expr:t={type:"tables",expr:t,parentheses:!0},as:e,...xb()}}(e,o,i)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"lateral"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(_t)),e===u&&(e=null),e!==u&&ub()!==u&&(n=Il())!==u&&ub()!==u?((o=Bi())===u&&(o=null),o!==u?(Pa=t,t=e=function(r,t,e){return{prefix:r,type:"expr",expr:t,as:e}}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=tc())!==u&&ub()!==u?("tablesample"===r.substr(Ma,11).toLowerCase()?(n=r.substr(Ma,11),Ma+=11):(n=u,0===Fa&&qa(St)),n!==u&&ub()!==u&&(o=Il())!==u&&ub()!==u?(s=Ma,"repeatable"===r.substr(Ma,10).toLowerCase()?(a=r.substr(Ma,10),Ma+=10):(a=u,0===Fa&&qa(jt)),a!==u&&(i=ub())!==u&&(c=zp())!==u&&(l=ub())!==u&&(f=Wl())!==u&&(p=ub())!==u&&(b=Zp())!==u?s=a=[a,i,c,l,f,p,b]:(Ma=s,s=u),s===u&&(s=null),s!==u&&(a=ub())!==u?((i=Bi())===u&&(i=null),i!==u?(Pa=t,t=e=function(r,t,e,n){return{...r,as:n,tablesample:{expr:t,repeatable:e&&e[4]},...xb()}}(e,o,s,i)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=tc())!==u&&ub()!==u?((n=Bi())===u&&(n=null),n!==u?(Pa=t,t=e=function(r,t){return"var"===r.type?(r.as=t,Object.assign(r,{...xb()}),r):{...r,as:t,...xb()}}(e,n)):(Ma=t,t=u)):(Ma=t,t=u))))))}return t}function rc(){var t,e,n,o;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"left"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Oo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(n=ub())!==u?((o=Tf())===u&&(o=null),o!==u&&ub()!==u&&gf()!==u?(Pa=t,t=e="LEFT JOIN"):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=function(){var t,e,n,o;t=Ma,"right"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(ko));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(n=ub())!==u?((o=Tf())===u&&(o=null),o!==u&&ub()!==u&&gf()!==u?(Pa=t,t=e="RIGHT JOIN"):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=function(){var t,e,n,o;t=Ma,"full"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Uo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(n=ub())!==u?((o=Tf())===u&&(o=null),o!==u&&ub()!==u&&gf()!==u?(Pa=t,t=e="FULL JOIN"):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"cross"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(xt)),e!==u&&(n=ub())!==u&&(o=gf())!==u?(Pa=t,t=e="CROSS JOIN"):(Ma=t,t=u),t===u&&(t=Ma,e=Ma,(n=function(){var t,e,n,o;t=Ma,"inner"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Mo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(o=ub())!==u?e=n=[n,o]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=gf())!==u?(Pa=t,t=e="INNER JOIN"):(Ma=t,t=u))))),t}function tc(){var r,t,e,n,o,s,a,i,c;return r=Ma,(t=rl())!==u?(e=Ma,(n=ub())!==u&&(o=Xp())!==u&&(s=ub())!==u&&(a=rl())!==u?e=n=[n,o,s,a]:(Ma=e,e=u),e!==u?(n=Ma,(o=ub())!==u&&(s=Xp())!==u&&(a=ub())!==u&&(i=rl())!==u?n=o=[o,s,a,i]:(Ma=n,n=u),n!==u?(Pa=r,r=t=function(r,t,e){const n={db:null,table:r,...xb()};return null!==e&&(n.db=r,n.schema=t[3],n.table=e[3]),n}(t,e,n)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=rl())!==u&&(e=ub())!==u&&(n=Xp())!==u&&(o=ub())!==u&&(s=Kp())!==u?(Pa=r,r=t={db:t,table:"*",...xb()}):(Ma=r,r=u),r===u&&(r=Ma,(t=rl())!==u?(e=Ma,(n=ub())!==u&&(o=Xp())!==u&&(s=ub())!==u&&(a=rl())!==u?e=n=[n,o,s,a]:(Ma=e,e=u),e===u&&(e=null),e!==u?(Pa=r,r=t=function(r,t){const e={db:null,table:r,...xb()};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=Cb())!==u&&(Pa=r,(c=t).db=null,c.table=c.name,t=c),r=t))),r}function ec(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Oc())!==u){for(e=[],n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=Rb(t[r][1],n,t[r][3]);return n}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function nc(){var r,t;return r=Ma,Af()!==u&&ub()!==u&&(t=kc())!==u?(Pa=r,r=t):(Ma=r,r=u),r}function oc(){var t,e;return t=Ma,function(){var t,e,n,o;t=Ma,"where"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Fo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(e=kc())!==u?(Pa=t,t=e):(Ma=t,t=u),t}function uc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=zc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=zc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=zc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function sc(){var r,t,e;return r=Ma,(t=fl())!==u&&ub()!==u&&Lf()!==u&&ub()!==u&&(e=ac())!==u?(Pa=r,r=t={name:t,as_window_specification:e}):(Ma=r,r=u),r}function ac(){var t,e;return(t=fl())===u&&(t=Ma,zp()!==u&&ub()!==u?((e=function(){var t,e,n,o;t=Ma,(e=bc())===u&&(e=null);e!==u&&ub()!==u?((n=vc())===u&&(n=null),n!==u&&ub()!==u?((o=function(){var t,e,n,o,s;t=Ma,(e=_p())!==u&&ub()!==u?((n=cc())===u&&(n=lc()),n!==u?(Pa=t,t=e={type:"rows",expr:n}):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,(e=_p())!==u&&ub()!==u&&(n=Mf())!==u&&ub()!==u&&(o=lc())!==u&&ub()!==u&&Wf()!==u&&ub()!==u&&(s=cc())!==u?(Pa=t,e=Rb(n,{type:"origin",value:"rows"},{type:"expr_list",value:[o,s]}),t=e):(Ma=t,t=u),t===u&&(t=Ma,"range"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Rt)),e!==u&&ub()!==u&&(n=Mf())!==u&&ub()!==u&&(o=ic())!==u&&ub()!==u&&Wf()!==u&&ub()!==u?((s=ic())===u&&(s=fc()),s!==u?(Pa=t,e=function(r,t,e){return Rb(r,{type:"origin",value:"range"},{type:"expr_list",value:[t,e]})}(n,o,s),t=e):(Ma=t,t=u)):(Ma=t,t=u)));return t}())===u&&(o=null),o!==u?(Pa=t,t=e={name:null,partitionby:e,orderby:n,window_frame_clause:o}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(e=null),e!==u&&ub()!==u&&Zp()!==u?(Pa=t,t={window_specification:e||{},parentheses:!0}):(Ma=t,t=u)):(Ma=t,t=u)),t}function ic(){var t,e,n,o;return t=Ma,(e=Sc())!==u&&ub()!==u?("preceding"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(It)),n!==u?(Pa=t,(o=e).suffix={type:"origin",value:"preceding"},t=e=o):(Ma=t,t=u)):(Ma=t,t=u),t}function cc(){var t,e,n,o;return t=Ma,(e=pc())!==u&&ub()!==u?("following"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(Nt)),n!==u?(Pa=t,(o=e).value+=" FOLLOWING",t=e=o):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=fc()),t}function lc(){var t,e,n,o,s;return t=Ma,(e=pc())!==u&&ub()!==u?("preceding"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(It)),n===u&&("following"===r.substr(Ma,9).toLowerCase()?(n=r.substr(Ma,9),Ma+=9):(n=u,0===Fa&&qa(Nt))),n!==u?(Pa=t,s=n,(o=e).value+=" "+s.toUpperCase(),t=e=o):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=fc()),t}function fc(){var t,e,n;return t=Ma,"current"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ot)),e!==u&&ub()!==u?("row"===r.substr(Ma,3).toLowerCase()?(n=r.substr(Ma,3),Ma+=3):(n=u,0===Fa&&qa(Rr)),n!==u?(Pa=t,t=e={type:"origin",value:"current row",...xb()}):(Ma=t,t=u)):(Ma=t,t=u),t}function pc(){var t,e;return t=Ma,"unbounded"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(kt)),e!==u&&(Pa=t,e={type:"origin",value:e.toUpperCase(),...xb()}),(t=e)===u&&(t=Wl()),t}function bc(){var r,t;return r=Ma,df()!==u&&ub()!==u&&If()!==u&&ub()!==u&&(t=Mi())!==u?(Pa=r,r=t):(Ma=r,r=u),r}function vc(){var r,t;return r=Ma,Rf()!==u&&ub()!==u&&If()!==u&&ub()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=yc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=yc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=yc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=r,r=t):(Ma=r,r=u),r}function yc(){var t,e,n,o,s,a,i;return t=Ma,(e=Oc())!==u&&ub()!==u?((n=Of())===u&&(n=Nf()),n===u&&(n=null),n!==u&&ub()!==u?(o=Ma,"nulls"===r.substr(Ma,5).toLowerCase()?(s=r.substr(Ma,5),Ma+=5):(s=u,0===Fa&&qa(R)),s!==u&&(a=ub())!==u?("first"===r.substr(Ma,5).toLowerCase()?(i=r.substr(Ma,5),Ma+=5):(i=u,0===Fa&&qa(N)),i===u&&("last"===r.substr(Ma,4).toLowerCase()?(i=r.substr(Ma,4),Ma+=4):(i=u,0===Fa&&qa(O))),i===u&&(i=null),i!==u?o=s=[s,a,i]:(Ma=o,o=u)):(Ma=o,o=u),o===u&&(o=null),o!==u?(Pa=t,t=e=function(r,t,e){const n={expr:r,type:t};return n.nulls=e&&[e[0],e[2]].filter(r=>r).join(" "),n}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function dc(){var r;return(r=Wl())===u&&(r=Cb())===u&&(r=yl()),r}function hc(){var t,e,n,o,s,a,i;return t=Ma,e=Ma,(n=function(){var t,e,n,o;t=Ma,"limit"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Xo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(o=ub())!==u?((s=dc())===u&&(s=kf()),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u?(o=Ma,(s=function(){var t,e,n,o;t=Ma,"offset"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Qo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="OFFSET"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(a=ub())!==u&&(i=dc())!==u?o=s=[s,a,i]:(Ma=o,o=u),o===u&&(o=null),o!==u?(Pa=t,t=e=function(r,t){const e=[];return r&&e.push("string"==typeof r[2]?{type:"origin",value:"all"}:r[2]),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e,...xb()}}(e,o)):(Ma=t,t=u)):(Ma=t,t=u),t}function wc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=mc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=mc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=mc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function mc(){var t,e,n,o,s,a,i,c,l;return t=Ma,e=Ma,(n=rl())!==u&&(o=ub())!==u&&(s=Xp())!==u?e=n=[n,o,s]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u&&(o=al())!==u&&(s=ub())!==u?(61===r.charCodeAt(Ma)?(a="=",Ma++):(a=u,0===Fa&&qa(Ut)),a!==u&&ub()!==u&&(i=Bc())!==u?(Pa=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,e=Ma,(n=rl())!==u&&(o=ub())!==u&&(s=Xp())!==u?e=n=[n,o,s]:(Ma=e,e=u),e===u&&(e=null),e!==u&&(n=ub())!==u&&(o=al())!==u&&(s=ub())!==u?(61===r.charCodeAt(Ma)?(a="=",Ma++):(a=u,0===Fa&&qa(Ut)),a!==u&&ub()!==u&&(i=_f())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(c=zc())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Ma=t,t=u)):(Ma=t,t=u)),t}function Lc(){var t,e,n,o,s;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"returning"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(Eo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="RETURNING"):(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&ub()!==u?((n=Mi())===u&&(n=ji()),n!==u?(Pa=t,s=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===s&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null,...xb()}]||s}):(Ma=t,t=u)):(Ma=t,t=u),t}function Cc(){var r;return(r=gc())===u&&(r=Oi()),r}function Ec(){var r,t,e,n,o,s,a,i,c;if(r=Ma,df()!==u)if(ub()!==u)if((t=zp())!==u)if(ub()!==u)if((e=fl())!==u){for(n=[],o=Ma,(s=ub())!==u&&(a=Qp())!==u&&(i=ub())!==u&&(c=fl())!==u?o=s=[s,a,i,c]:(Ma=o,o=u);o!==u;)n.push(o),o=Ma,(s=ub())!==u&&(a=Qp())!==u&&(i=ub())!==u&&(c=fl())!==u?o=s=[s,a,i,c]:(Ma=o,o=u);n!==u&&(o=ub())!==u&&(s=Zp())!==u?(Pa=r,r=Ob(e,n)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;return r===u&&(r=Ma,df()!==u&&ub()!==u&&(t=Tc())!==u?(Pa=r,r=t):(Ma=r,r=u)),r}function Ac(){var r,t;return r=Ma,(t=ff())!==u&&(Pa=r,t="insert"),(r=t)===u&&(r=Ma,(t=bf())!==u&&(Pa=r,t="replace"),r=t),r}function gc(){var r,t;return r=Ma,_f()!==u&&ub()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Tc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Tc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Tc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Ob(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())!==u?(Pa=r,r=t):(Ma=r,r=u),r}function Tc(){var r,t;return r=Ma,zp()!==u&&ub()!==u&&(t=_c())!==u&&ub()!==u&&Zp()!==u?(Pa=r,r=t):(Ma=r,r=u),r}function _c(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Oc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Ob(r,t),e}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Sc(){var t,e,n;return t=Ma,Np()!==u&&ub()!==u&&(e=Oc())!==u&&ub()!==u&&(n=function(){var t;(t=function(){var t,e,n,o;t=Ma,"year"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(_n));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="YEAR"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;"month"===r.substr(Ma,5).toLowerCase()?(t=r.substr(Ma,5),Ma+=5):(t=u,0===Fa&&qa(mn));t===u&&("mm"===r.substr(Ma,2).toLowerCase()?(t=r.substr(Ma,2),Ma+=2):(t=u,0===Fa&&qa(xs)),t===u&&("mon"===r.substr(Ma,3).toLowerCase()?(t=r.substr(Ma,3),Ma+=3):(t=u,0===Fa&&qa(Is)),t===u&&("mons"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(Rs)),t===u&&(t=Ma,"months"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Ns)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="MONTH"):(Ma=t,t=u)):(Ma=t,t=u)))));return t}())===u&&(t=function(){var t,e,n,o;"week"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(Tn));t===u&&("w"===r.substr(Ma,1).toLowerCase()?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Os)),t===u&&("wk"===r.substr(Ma,2).toLowerCase()?(t=r.substr(Ma,2),Ma+=2):(t=u,0===Fa&&qa(ks)),t===u&&("weekofyear"===r.substr(Ma,10).toLowerCase()?(t=r.substr(Ma,10),Ma+=10):(t=u,0===Fa&&qa(Us)),t===u&&("woy"===r.substr(Ma,3).toLowerCase()?(t=r.substr(Ma,3),Ma+=3):(t=u,0===Fa&&qa(Ms)),t===u&&("wy"===r.substr(Ma,2).toLowerCase()?(t=r.substr(Ma,2),Ma+=2):(t=u,0===Fa&&qa(Ps)),t===u&&(t=Ma,"weeks"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Ds)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="WEEK"):(Ma=t,t=u)):(Ma=t,t=u)))))));return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"day"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(un));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DAY"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"hour"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(pn));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="HOUR"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"minute"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(wn));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="MINUTE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"second"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Cn));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SECOND"):(Ma=t,t=u)):(Ma=t,t=u);return t}());return t}())!==u?(Pa=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(Ma=t,t=u),t===u&&(t=Ma,Np()!==u&&ub()!==u&&(e=$l())!==u?(Pa=t,t=function(r){return{type:"interval",expr:r,unit:""}}(e)):(Ma=t,t=u)),t}function jc(){var r,t,e,n,o,s,a,i;return r=Ma,Xf()!==u&&ub()!==u&&(t=xc())!==u&&ub()!==u?((e=Rc())===u&&(e=null),e!==u&&ub()!==u&&(n=Kf())!==u&&ub()!==u?((o=Xf())===u&&(o=null),o!==u?(Pa=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,Xf()!==u&&ub()!==u&&(t=Oc())!==u&&ub()!==u&&(e=xc())!==u&&ub()!==u?((n=Rc())===u&&(n=null),n!==u&&ub()!==u&&(o=Kf())!==u&&ub()!==u?((s=Xf())===u&&(s=null),s!==u?(Pa=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)),r}function xc(){var r,t,e,n,o,s;if(r=Ma,(t=Ic())!==u)if(ub()!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Ic())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Ic())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e,1)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;return r}function Ic(){var t,e,n;return t=Ma,Qf()!==u&&ub()!==u&&(e=kc())!==u&&ub()!==u&&function(){var t,e,n,o;t=Ma,"then"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Cu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(n=kc())!==u?(Pa=t,t={type:"when",cond:e,result:n}):(Ma=t,t=u),t}function Rc(){var t,e;return t=Ma,function(){var t,e,n,o;t=Ma,"else"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Eu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}()!==u&&ub()!==u&&(e=Oc())!==u?(Pa=t,t={type:"else",result:e}):(Ma=t,t=u),t}function Nc(){var r;return(r=function(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Uc())!==u){for(e=[],n=Ma,(o=sb())!==u&&(s=Yf())!==u&&(a=ub())!==u&&(i=Uc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=sb())!==u&&(s=Yf())!==u&&(a=ub())!==u&&(i=Uc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,t=Gt(t,e),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,s;if(r=Ma,(t=Hc())!==u){if(e=[],n=Ma,(o=ub())!==u&&(s=Vc())!==u?n=o=[o,s]:(Ma=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Vc())!==u?n=o=[o,s]:(Ma=n,n=u);else e=u;e!==u?(Pa=r,t=Ib(t,e[0][1]),r=t):(Ma=r,r=u)}else Ma=r,r=u;return r}()),r}function Oc(){var r;return(r=Nc())===u&&(r=Za()),r}function kc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Oc())!==u){for(e=[],n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf())===u&&(s=Qp()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u?((s=Wf())===u&&(s=Yf())===u&&(s=Qp()),s!==u&&(a=ub())!==u&&(i=Oc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=Rb(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Uc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Mc())!==u){for(e=[],n=Ma,(o=sb())!==u&&(s=Wf())!==u&&(a=ub())!==u&&(i=Mc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=sb())!==u&&(s=Wf())!==u&&(a=ub())!==u&&(i=Mc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Gt(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Mc(){var t,e,n,o,s;return(t=Pc())===u&&(t=function(){var r,t,e;r=Ma,(t=function(){var r,t,e,n,o;r=Ma,t=Ma,(e=Hf())!==u&&(n=ub())!==u&&(o=Ff())!==u?t=e=[e,n,o]:(Ma=t,t=u);t!==u&&(Pa=r,t=(s=t)[0]+" "+s[2]);var s;(r=t)===u&&(r=Ff());return r}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(e=Za())!==u&&ub()!==u&&Zp()!==u?(Pa=r,n=t,(o=e).parentheses=!0,t=Ib(n,o),r=t):(Ma=r,r=u);var n,o;return r}())===u&&(t=Ma,(e=Hf())===u&&(e=Ma,33===r.charCodeAt(Ma)?(n="!",Ma++):(n=u,0===Fa&&qa($t)),n!==u?(o=Ma,Fa++,61===r.charCodeAt(Ma)?(s="=",Ma++):(s=u,0===Fa&&qa(Ut)),Fa--,s===u?o=void 0:(Ma=o,o=u),o!==u?e=n=[n,o]:(Ma=e,e=u)):(Ma=e,e=u)),e!==u&&(n=ub())!==u&&(o=Mc())!==u?(Pa=t,t=e=Ib("NOT",o)):(Ma=t,t=u)),t}function Pc(){var t,e,n,o,s;return t=Ma,(e=Bc())!==u&&ub()!==u?((n=function(){var t;(t=function(){var r,t,e,n,o,s,a;r=Ma,t=[],e=Ma,(n=ub())!==u&&(o=Dc())!==u&&(s=ub())!==u?((a=Bc())===u&&(a=il()),a!==u?e=n=[n,o,s,a]:(Ma=e,e=u)):(Ma=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=Ma,(n=ub())!==u&&(o=Dc())!==u&&(s=ub())!==u?((a=Bc())===u&&(a=il()),a!==u?e=n=[n,o,s,a]:(Ma=e,e=u)):(Ma=e,e=u);else t=u;t!==u&&(Pa=r,t={type:"arithmetic",tail:t});return r=t}())===u&&(t=Fc())===u&&(t=function(){var r,t,e,n;r=Ma,(t=function(){var r,t,e,n,o;r=Ma,t=Ma,(e=Hf())!==u&&(n=ub())!==u&&(o=Mf())!==u?t=e=[e,n,o]:(Ma=t,t=u);t!==u&&(Pa=r,t=(s=t)[0]+" "+s[2]);var s;(r=t)===u&&(r=Mf());return r}())!==u&&ub()!==u&&(e=Bc())!==u&&ub()!==u&&Wf()!==u&&ub()!==u&&(n=Bc())!==u?(Pa=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Ma=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o,s,a,i,c;r=Ma,(t=Df())!==u&&(e=ub())!==u&&(n=Bc())!==u?(Pa=r,r=t={op:"IS",right:n}):(Ma=r,r=u);r===u&&(r=Ma,(t=Df())!==u&&(e=ub())!==u?(n=Ma,(o=Uf())!==u&&(s=ub())!==u&&(a=wf())!==u&&(i=ub())!==u&&(c=tc())!==u?n=o=[o,s,a,i,c]:(Ma=n,n=u),n!==u?(Pa=r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,t=Ma,(e=Df())!==u&&(n=ub())!==u&&(o=Hf())!==u?t=e=[e,n,o]:(Ma=t,t=u),t!==u&&(e=ub())!==u&&(n=Bc())!==u?(Pa=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Ma=r,r=u)));return r}())===u&&(t=function(){var t,e,n,o;t=Ma,(e=function(){var t,e,n,o,s;t=Ma,e=Ma,(n=Hf())!==u&&(o=ub())!==u?((s=Gf())===u&&(s=$f()),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u);e!==u&&(Pa=t,e=(a=e)[0]+" "+a[2]);var a;(t=e)===u&&(t=Gf())===u&&(t=$f())===u&&(t=Ma,"similar"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Vt)),e!==u&&(n=ub())!==u&&(o=Jl())!==u?(Pa=t,t=e="SIMILAR TO"):(Ma=t,t=u),t===u&&(t=Ma,(e=Hf())!==u&&(n=ub())!==u?("similar"===r.substr(Ma,7).toLowerCase()?(o=r.substr(Ma,7),Ma+=7):(o=u,0===Fa&&qa(Vt)),o!==u&&(s=ub())!==u&&Jl()!==u?(Pa=t,t=e="NOT SIMILAR TO"):(Ma=t,t=u)):(Ma=t,t=u)));return t}())!==u&&ub()!==u?((n=Ul())===u&&(n=Pc()),n!==u&&ub()!==u?((o=Gc())===u&&(o=null),o!==u?(Pa=t,s=e,a=n,(i=o)&&(a.escape=i),t=e={op:s,right:a}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u);var s,a,i;return t}())===u&&(t=function(){var r,t,e,n;r=Ma,(t=function(){var r,t,e,n,o;r=Ma,t=Ma,(e=Hf())!==u&&(n=ub())!==u&&(o=Bf())!==u?t=e=[e,n,o]:(Ma=t,t=u);t!==u&&(Pa=r,t=(s=t)[0]+" "+s[2]);var s;(r=t)===u&&(r=Bf());return r}())!==u&&ub()!==u?((e=Ul())===u&&(e=Pc()),e!==u&&ub()!==u?((n=Gc())===u&&(n=null),n!==u?(Pa=r,o=t,s=e,(a=n)&&(s.escape=a),r=t={op:o,right:s}):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u);var o,s,a;return r}());return t}())===u&&(n=null),n!==u?(Pa=t,o=e,t=e=null===(s=n)?o:"arithmetic"===s.type?kb(o,s.tail):Rb(s.op,o,s.right)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=$l())===u&&(t=zc()),t}function Dc(){var t;return">="===r.substr(Ma,2)?(t=">=",Ma+=2):(t=u,0===Fa&&qa(Ft)),t===u&&(62===r.charCodeAt(Ma)?(t=">",Ma++):(t=u,0===Fa&&qa(Bt)),t===u&&("<="===r.substr(Ma,2)?(t="<=",Ma+=2):(t=u,0===Fa&&qa(Ht)),t===u&&("<>"===r.substr(Ma,2)?(t="<>",Ma+=2):(t=u,0===Fa&&qa(Wt)),t===u&&(60===r.charCodeAt(Ma)?(t="<",Ma++):(t=u,0===Fa&&qa(Yt)),t===u&&(61===r.charCodeAt(Ma)?(t="=",Ma++):(t=u,0===Fa&&qa(Ut)),t===u&&("!="===r.substr(Ma,2)?(t="!=",Ma+=2):(t=u,0===Fa&&qa(qt)))))))),t}function Gc(){var t,e,n;return t=Ma,"escape"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Xt)),e!==u&&ub()!==u&&(n=$l())!==u?(Pa=t,t=e={type:"ESCAPE",value:n}):(Ma=t,t=u),t}function $c(){var r,t,e,n,o,s;return r=Ma,t=Ma,(e=Hf())!==u&&(n=ub())!==u&&(o=Pf())!==u?t=e=[e,n,o]:(Ma=t,t=u),t!==u&&(Pa=r,t=(s=t)[0]+" "+s[2]),(r=t)===u&&(r=Pf()),r}function Fc(){var r,t,e,n;return r=Ma,(t=$c())!==u&&ub()!==u&&(e=zp())!==u&&ub()!==u&&(n=_c())!==u&&ub()!==u&&Zp()!==u?(Pa=r,r=t={op:t,right:n}):(Ma=r,r=u),r===u&&(r=Ma,(t=$c())!==u&&ub()!==u?((e=Cb())===u&&(e=$l())===u&&(e=Il()),e!==u?(Pa=r,r=t=function(r,t){return{op:r,right:t}}(t,e)):(Ma=r,r=u)):(Ma=r,r=u)),r}function Bc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Wc())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Hc())!==u&&(a=ub())!==u&&(i=Wc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Hc())!==u&&(a=ub())!==u&&(i=Wc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...xb()}));return kb(r,t)}(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Hc(){var t;return 43===r.charCodeAt(Ma)?(t="+",Ma++):(t=u,0===Fa&&qa(Qt)),t===u&&(45===r.charCodeAt(Ma)?(t="-",Ma++):(t=u,0===Fa&&qa(Kt))),t}function Wc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=Xc())!==u){for(e=[],n=Ma,(o=ub())!==u?((s=Yc())===u&&(s=ob()),s!==u&&(a=ub())!==u&&(i=Xc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u?((s=Yc())===u&&(s=ob()),s!==u&&(a=ub())!==u&&(i=Xc())!==u?n=o=[o,s,a,i]:(Ma=n,n=u)):(Ma=n,n=u);e!==u?(Pa=r,r=t=kb(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Yc(){var t;return 42===r.charCodeAt(Ma)?(t="*",Ma++):(t=u,0===Fa&&qa(zt)),t===u&&(47===r.charCodeAt(Ma)?(t="/",Ma++):(t=u,0===Fa&&qa(Zt)),t===u&&(37===r.charCodeAt(Ma)?(t="%",Ma++):(t=u,0===Fa&&qa(Jt)),t===u&&("||"===r.substr(Ma,2)?(t="||",Ma+=2):(t=u,0===Fa&&qa(re))))),t}function qc(){var r,t,e,n,o;return r=Ma,(t=zc())!==u&&ub()!==u?((e=Di())===u&&(e=null),e!==u?(Pa=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(Ma=r,r=u)):(Ma=r,r=u),r}function Vc(){var t,e,n,o,s,a;return(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=zf())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&(s=Lf())!==u&&ub()!==u&&(a=Ab())!==u&&ub()!==u&&(i=Zp())!==u?(Pa=t,f=o,p=a,e={type:"cast",keyword:e.toLowerCase(),expr:f,symbol:"as",target:[p]},t=e):(Ma=t,t=u);var f,p;t===u&&(t=Ma,(e=zf())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&(s=Lf())!==u&&ub()!==u&&(a=op())!==u&&ub()!==u&&(i=zp())!==u&&ub()!==u&&(c=ql())!==u&&ub()!==u&&Zp()!==u&&ub()!==u&&(l=Zp())!==u?(Pa=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),t=e):(Ma=t,t=u),t===u&&(t=Ma,(e=zf())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&(s=Lf())!==u&&ub()!==u&&(a=op())!==u&&ub()!==u&&(i=zp())!==u&&ub()!==u&&(c=ql())!==u&&ub()!==u&&Qp()!==u&&ub()!==u&&(l=ql())!==u&&ub()!==u&&Zp()!==u&&ub()!==u&&Zp()!==u?(Pa=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),t=e):(Ma=t,t=u),t===u&&(t=Ma,(e=zf())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&(s=Lf())!==u&&ub()!==u&&(a=function(){var t;(t=function(){var t,e,n,o;t=Ma,"signed"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Uu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SIGNED"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=up());return t}())!==u&&ub()!==u?((i=ip())===u&&(i=null),i!==u&&ub()!==u&&(c=Zp())!==u?(Pa=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,i),t=e):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=zp())!==u&&ub()!==u?((n=Ul())===u&&(n=wl())===u&&(n=ml())===u&&(n=Il())===u&&(n=jc())===u&&(n=Sc())===u&&(n=qc())===u&&(n=yl()),n!==u&&ub()!==u&&(o=Zp())!==u&&ub()!==u?((s=kl())===u&&(s=null),s!==u?(Pa=t,e=function(r,t){return r.parentheses=!0,t?{type:"cast",keyword:"cast",expr:r,...t}:r}(n,s),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=Ul())===u&&(e=wl())===u&&(e=ml())===u&&(e=Il())===u&&(e=jc())===u&&(e=Sc())===u&&(e=qc())===u&&(e=yl()),e!==u&&ub()!==u?((n=kl())===u&&(n=null),n!==u?(Pa=t,e=function(r,t){return t?{type:"cast",keyword:"cast",expr:r,...t}:r}(e,n),t=e):(Ma=t,t=u)):(Ma=t,t=u))))));return t}())===u&&(t=Ma,zp()!==u&&(e=ub())!==u&&(n=kc())!==u&&(o=ub())!==u&&(s=Zp())!==u?(Pa=t,(a=n).parentheses=!0,t=a):(Ma=t,t=u),t===u&&(t=Cb())===u&&(t=Ma,ub()!==u?(36===r.charCodeAt(Ma)?(e="$",Ma++):(e=u,0===Fa&&qa(te)),e!==u?(60===r.charCodeAt(Ma)?(n="<",Ma++):(n=u,0===Fa&&qa(Yt)),n!==u&&(o=Wl())!==u?(62===r.charCodeAt(Ma)?(s=">",Ma++):(s=u,0===Fa&&qa(Bt)),s!==u?(Pa=t,t={type:"origin",value:`$<${o.value}>`}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u))),t}function Xc(){var t,e,n,o,s;return(t=function(){var t,e,n,o,s,a,i,c;if(t=Ma,(e=Vc())!==u)if(ub()!==u){for(n=[],o=Ma,(s=ub())!==u?("?|"===r.substr(Ma,2)?(a="?|",Ma+=2):(a=u,0===Fa&&qa(ne)),a===u&&("?&"===r.substr(Ma,2)?(a="?&",Ma+=2):(a=u,0===Fa&&qa(oe)),a===u&&(63===r.charCodeAt(Ma)?(a="?",Ma++):(a=u,0===Fa&&qa(ue)),a===u&&("#-"===r.substr(Ma,2)?(a="#-",Ma+=2):(a=u,0===Fa&&qa(se)),a===u&&("#>>"===r.substr(Ma,3)?(a="#>>",Ma+=3):(a=u,0===Fa&&qa(ae)),a===u&&("#>"===r.substr(Ma,2)?(a="#>",Ma+=2):(a=u,0===Fa&&qa(ie)),a===u&&(a=nb())===u&&(a=eb())===u&&("@>"===r.substr(Ma,2)?(a="@>",Ma+=2):(a=u,0===Fa&&qa(ce)),a===u&&("<@"===r.substr(Ma,2)?(a="<@",Ma+=2):(a=u,0===Fa&&qa(le))))))))),a!==u&&(i=ub())!==u&&(c=Vc())!==u?o=s=[s,a,i,c]:(Ma=o,o=u)):(Ma=o,o=u);o!==u;)n.push(o),o=Ma,(s=ub())!==u?("?|"===r.substr(Ma,2)?(a="?|",Ma+=2):(a=u,0===Fa&&qa(ne)),a===u&&("?&"===r.substr(Ma,2)?(a="?&",Ma+=2):(a=u,0===Fa&&qa(oe)),a===u&&(63===r.charCodeAt(Ma)?(a="?",Ma++):(a=u,0===Fa&&qa(ue)),a===u&&("#-"===r.substr(Ma,2)?(a="#-",Ma+=2):(a=u,0===Fa&&qa(se)),a===u&&("#>>"===r.substr(Ma,3)?(a="#>>",Ma+=3):(a=u,0===Fa&&qa(ae)),a===u&&("#>"===r.substr(Ma,2)?(a="#>",Ma+=2):(a=u,0===Fa&&qa(ie)),a===u&&(a=nb())===u&&(a=eb())===u&&("@>"===r.substr(Ma,2)?(a="@>",Ma+=2):(a=u,0===Fa&&qa(ce)),a===u&&("<@"===r.substr(Ma,2)?(a="<@",Ma+=2):(a=u,0===Fa&&qa(le))))))))),a!==u&&(i=ub())!==u&&(c=Vc())!==u?o=s=[s,a,i,c]:(Ma=o,o=u)):(Ma=o,o=u);n!==u?(Pa=t,l=e,e=(f=n)&&0!==f.length?kb(l,f):l,t=e):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;var l,f;return t}())===u&&(t=Ma,(e=function(){var t;33===r.charCodeAt(Ma)?(t="!",Ma++):(t=u,0===Fa&&qa($t));t===u&&(45===r.charCodeAt(Ma)?(t="-",Ma++):(t=u,0===Fa&&qa(Kt)),t===u&&(43===r.charCodeAt(Ma)?(t="+",Ma++):(t=u,0===Fa&&qa(Qt)),t===u&&(126===r.charCodeAt(Ma)?(t="~",Ma++):(t=u,0===Fa&&qa(ee)))));return t}())!==u?(n=Ma,(o=ub())!==u&&(s=Xc())!==u?n=o=[o,s]:(Ma=n,n=u),n!==u?(Pa=t,t=e=Ib(e,n[1])):(Ma=t,t=u)):(Ma=t,t=u)),t}function Qc(){var t,e,n,o,s,a;if(t=Ma,"e"===r.substr(Ma,1).toLowerCase()?(e=r.charAt(Ma),Ma++):(e=u,0===Fa&&qa(fe)),e!==u)if(39===r.charCodeAt(Ma)?(n="'",Ma++):(n=u,0===Fa&&qa(Wr)),n!==u)if(ub()!==u){for(o=[],s=Bl();s!==u;)o.push(s),s=Bl();o!==u&&(s=ub())!==u?(39===r.charCodeAt(Ma)?(a="'",Ma++):(a=u,0===Fa&&qa(Wr)),a!==u?(Pa=t,t=e={type:"origin",value:`E'${o.join("")}'`}):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;return t}function Kc(){var r;return(r=Xp())===u&&(r=Dp()),r}function zc(){var r,t,e,n,o,s,a,i,c,l,f,p,b;return(r=Qc())===u&&(r=Ma,t=Ma,(e=rl())!==u&&(n=ub())!==u&&(o=Xp())!==u?t=e=[e,n,o]:(Ma=t,t=u),t===u&&(t=null),t!==u&&(e=ub())!==u&&(n=Kp())!==u?(Pa=r,r=t=function(r){const t=r&&r[0]||null;return $b.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*",...xb()}}(t)):(Ma=r,r=u),r===u&&(r=Ma,(t=rl())!==u?(e=Ma,(n=ub())!==u&&(o=Kc())!==u&&(s=ub())!==u&&(a=function(){var r;(r=fl())===u&&(r=nl());return r}())!==u?e=n=[n,o,s,a]:(Ma=e,e=u),e!==u?(n=Ma,(o=ub())!==u&&(s=Kc())!==u&&(a=ub())!==u&&(i=al())!==u?n=o=[o,s,a,i]:(Ma=n,n=u),n!==u?(o=Ma,(s=ub())!==u&&(a=si())!==u?o=s=[s,a]:(Ma=o,o=u),o===u&&(o=null),o!==u?(Pa=r,l=t,f=e,p=n,b=o,$b.add(`select::${l}.${f[3]}::${p[3]}`),r=t={type:"column_ref",schema:l,notations:[f[1],p[1]],table:f[3],column:p[3],collate:b&&b[1],...xb()}):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=rl())!==u&&(e=ub())!==u&&(n=Kc())!==u&&(o=ub())!==u&&(s=al())!==u?(a=Ma,(i=ub())!==u&&(c=si())!==u?a=i=[i,c]:(Ma=a,a=u),a===u&&(a=null),a!==u?(Pa=r,r=t=function(r,t,e,n){return $b.add(`select::${r}::${e}`),{type:"column_ref",table:r,notations:[t],column:e,collate:n&&n[1],...xb()}}(t,n,s,a)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=cl())!==u?(e=Ma,(n=ub())!==u&&(o=si())!==u?e=n=[n,o]:(Ma=e,e=u),e===u&&(e=null),e!==u?(Pa=r,r=t=function(r,t){return $b.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1],...xb()}}(t,e)):(Ma=r,r=u)):(Ma=r,r=u))))),r}function Zc(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=cl())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=cl())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=cl())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Jc(){var r,t;return r=Ma,(t=fl())!==u&&(Pa=r,t=pe(t)),(r=t)===u&&(r=el()),r}function rl(){var r,t;return r=Ma,(t=fl())!==u?(Pa=Ma,(be(t)?u:void 0)!==u?(Pa=r,r=t=t):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=nl())!==u&&(Pa=r,t=t),r=t),r}function tl(){var r,t,e,n,o,s,a,i,c;return r=Ma,(t=fl())!==u?(Pa=Ma,(!0===jb[t.toUpperCase()]?u:void 0)!==u?(e=Ma,(n=ub())!==u&&(o=zp())!==u&&(s=ub())!==u&&(a=Zc())!==u&&(i=ub())!==u&&(c=Zp())!==u?e=n=[n,o,s,a,i,c]:(Ma=e,e=u),e===u&&(e=null),e!==u?(Pa=r,r=t=function(r,t){return t?`${r}(${t[3].join(", ")})`:r}(t,e)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=nl())!==u&&(Pa=r,t=t),r=t),r}function el(){var r;return(r=ol())===u&&(r=ul())===u&&(r=sl()),r}function nl(){var r,t;return r=Ma,(t=ol())===u&&(t=ul())===u&&(t=sl()),t!==u&&(Pa=r,t=t.value),r=t}function ol(){var t,e,n,o;if(t=Ma,34===r.charCodeAt(Ma)?(e='"',Ma++):(e=u,0===Fa&&qa(ve)),e!==u){if(n=[],ye.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(de)),o!==u)for(;o!==u;)n.push(o),ye.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(de));else n=u;n!==u?(34===r.charCodeAt(Ma)?(o='"',Ma++):(o=u,0===Fa&&qa(ve)),o!==u?(Pa=t,t=e={type:"double_quote_string",value:n.join("")}):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;return t}function ul(){var t,e,n,o;if(t=Ma,39===r.charCodeAt(Ma)?(e="'",Ma++):(e=u,0===Fa&&qa(Wr)),e!==u){if(n=[],he.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(we)),o!==u)for(;o!==u;)n.push(o),he.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(we));else n=u;n!==u?(39===r.charCodeAt(Ma)?(o="'",Ma++):(o=u,0===Fa&&qa(Wr)),o!==u?(Pa=t,t=e={type:"single_quote_string",value:n.join("")}):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;return t}function sl(){var t,e,n,o;if(t=Ma,96===r.charCodeAt(Ma)?(e="`",Ma++):(e=u,0===Fa&&qa(me)),e!==u){if(n=[],Le.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Ce)),o!==u)for(;o!==u;)n.push(o),Le.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Ce));else n=u;n!==u?(96===r.charCodeAt(Ma)?(o="`",Ma++):(o=u,0===Fa&&qa(me)),o!==u?(Pa=t,t=e={type:"backticks_quote_string",value:n.join("")}):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;return t}function al(){var r,t;return r=Ma,(t=ll())!==u&&(Pa=r,t=t),(r=t)===u&&(r=nl()),r}function il(){var r,t;return r=Ma,(t=ll())!==u&&(Pa=r,t=pe(t)),(r=t)===u&&(r=el()),r}function cl(){var r,t;return r=Ma,(t=ll())!==u?(Pa=Ma,(be(t)?u:void 0)!==u?(Pa=r,r=t=t):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=nl()),r}function ll(){var r,t,e,n;if(r=Ma,(t=pl())!==u){for(e=[],n=vl();n!==u;)e.push(n),n=vl();e!==u?(Pa=r,r=t=t+e.join("")):(Ma=r,r=u)}else Ma=r,r=u;return r}function fl(){var r,t,e,n;if(r=Ma,(t=pl())!==u){for(e=[],n=bl();n!==u;)e.push(n),n=bl();e!==u?(Pa=r,r=t=t+e.join("")):(Ma=r,r=u)}else Ma=r,r=u;return r}function pl(){var t;return Ee.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Ae)),t}function bl(){var t;return ge.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Te)),t}function vl(){var t;return Ee.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Ae)),t}function yl(){var t,e,n,o;return t=Ma,e=Ma,58===r.charCodeAt(Ma)?(n=":",Ma++):(n=u,0===Fa&&qa(_e)),n!==u&&(o=fl())!==u?e=n=[n,o]:(Ma=e,e=u),e!==u&&(Pa=t,e={type:"param",value:e[1]}),t=e}function dl(){var r,t,e;return r=Ma,Af()!==u&&ub()!==u&&uf()!==u&&ub()!==u&&(t=Op())!==u&&ub()!==u&&zp()!==u&&ub()!==u?((e=_c())===u&&(e=null),e!==u&&ub()!==u&&Zp()!==u?(Pa=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,Af()!==u&&ub()!==u&&uf()!==u&&ub()!==u&&(t=Op())!==u?(Pa=r,r=function(r){return{type:"on update",keyword:r}}(t)):(Ma=r,r=u)),r}function hl(){var t,e,n,o,s;return t=Ma,"over"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Se)),e!==u&&ub()!==u&&(n=ac())!==u?(Pa=t,t=e={type:"window",as_window_specification:n}):(Ma=t,t=u),t===u&&(t=Ma,"over"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Se)),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=bc())===u&&(o=null),o!==u&&ub()!==u?((s=vc())===u&&(s=null),s!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e={partitionby:o,orderby:s}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=dl())),t}function wl(){var t,e,n,o,s;return t=Ma,(e=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=function(){var t,e,n,o;t=Ma,"count"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(fu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="COUNT"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Ma,"group_concat"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(pu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="GROUP_CONCAT"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&("listagg"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ge)));e!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=function(){var t,e;t=Ma,(e=function(){var t,e;t=Ma,42===r.charCodeAt(Ma)?(e="*",Ma++):(e=u,0===Fa&&qa(zt));e!==u&&(Pa=t,e={type:"star",value:"*"});return t=e}())!==u&&(Pa=t,e={expr:e});(t=e)===u&&(t=El());return t}())!==u&&ub()!==u&&(o=Zp())!==u&&ub()!==u?((s=hl())===u&&(s=null),s!==u?(Pa=t,t=e={type:"aggr_func",name:e,args:n,over:s}):(Ma=t,t=u)):(Ma=t,t=u);t===u&&(t=Ma,"percentile_cont"===r.substr(Ma,15).toLowerCase()?(e=r.substr(Ma,15),Ma+=15):(e=u,0===Fa&&qa($e)),e===u&&("percentile_disc"===r.substr(Ma,15).toLowerCase()?(e=r.substr(Ma,15),Ma+=15):(e=u,0===Fa&&qa(Fe))),e!==u&&ub()!==u&&zp()!==u&&ub()!==u?((n=Wl())===u&&(n=Ml()),n!==u&&ub()!==u&&(o=Zp())!==u&&ub()!==u?("within"===r.substr(Ma,6).toLowerCase()?(s=r.substr(Ma,6),Ma+=6):(s=u,0===Fa&&qa(Be)),s!==u&&ub()!==u&&xf()!==u&&ub()!==u&&(a=zp())!==u&&ub()!==u&&(i=vc())!==u&&ub()!==u&&(c=Zp())!==u&&ub()!==u?((l=hl())===u&&(l=null),l!==u?(Pa=t,e=function(r,t,e,n){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:t},within_group_orderby:e,over:n}}(e,n,i,l),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"mode"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(He)),e!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=Zp())!==u&&ub()!==u?("within"===r.substr(Ma,6).toLowerCase()?(o=r.substr(Ma,6),Ma+=6):(o=u,0===Fa&&qa(Be)),o!==u&&ub()!==u&&(s=xf())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(a=vc())!==u&&ub()!==u&&(i=Zp())!==u&&ub()!==u?((c=hl())===u&&(c=null),c!==u?(Pa=t,e=function(r,t,e){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:{}},within_group_orderby:t,over:e}}(e,a,c),t=e):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)));return t}())===u&&(e=function(){var t,e,n,o;t=Ma,(e=function(){var t;(t=function(){var t,e,n,o;t=Ma,"sum"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(yu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SUM"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"max"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(bu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="MAX"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"min"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(vu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="MIN"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"avg"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(du));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="AVG"):(Ma=t,t=u)):(Ma=t,t=u);return t}());return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=Bc())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((o=hl())===u&&(o=null),o!==u?(Pa=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...xb()},t=e):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(e=function(){var t,e,n,o,s,a;t=Ma,e=Ma,(n=rl())!==u&&(o=ub())!==u&&(s=Xp())!==u?e=n=[n,o,s]:(Ma=e,e=u);e===u&&(e=null);e!==u&&(n=ub())!==u?((o=function(){var t,e,n,o;t=Ma,"array_agg"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(cu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ARRAY_AGG"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=Ma,"string_agg"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(lu));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="STRING_AGG"):(Ma=t,t=u)):(Ma=t,t=u);return t}()),o!==u&&(s=ub())!==u&&zp()!==u&&ub()!==u&&(a=El())!==u&&ub()!==u&&Zp()!==u?(Pa=t,c=o,l=a,e={type:"aggr_func",name:(i=e)?`${i[0]}.${c}`:c,args:l},t=e):(Ma=t,t=u)):(Ma=t,t=u);var i,c,l;return t}()),e!==u&&ub()!==u?((n=function(){var t,e,n;return t=Ma,"filter"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(je)),e!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=oc())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e={keyword:"filter",parentheses:!0,where:n}):(Ma=t,t=u),t}())===u&&(n=null),n!==u?(Pa=t,o=e,(s=n)&&(o.filter=s),t=e=o):(Ma=t,t=u)):(Ma=t,t=u),t}function ml(){var t;return(t=function(){var t,e,n;t=Ma,(e=function(){var t;"row_number"===r.substr(Ma,10).toLowerCase()?(t=r.substr(Ma,10),Ma+=10):(t=u,0===Fa&&qa(Re));t===u&&("dense_rank"===r.substr(Ma,10).toLowerCase()?(t=r.substr(Ma,10),Ma+=10):(t=u,0===Fa&&qa(Ne)),t===u&&("rank"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(Oe))));return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&Zp()!==u&&ub()!==u&&(n=hl())!==u?(Pa=t,t=e={type:"window_func",name:e,over:n}):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s;t=Ma,(e=function(){var t;"lag"===r.substr(Ma,3).toLowerCase()?(t=r.substr(Ma,3),Ma+=3):(t=u,0===Fa&&qa(ke));t===u&&("lead"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(Ue)),t===u&&("nth_value"===r.substr(Ma,9).toLowerCase()?(t=r.substr(Ma,9),Ma+=9):(t=u,0===Fa&&qa(Me))));return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=_c())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((o=Ll())===u&&(o=null),o!==u&&ub()!==u&&(s=hl())!==u?(Pa=t,t=e={type:"window_func",name:e,args:n,over:s,consider_nulls:o}):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s;t=Ma,(e=function(){var t;"first_value"===r.substr(Ma,11).toLowerCase()?(t=r.substr(Ma,11),Ma+=11):(t=u,0===Fa&&qa(xe));t===u&&("last_value"===r.substr(Ma,10).toLowerCase()?(t=r.substr(Ma,10),Ma+=10):(t=u,0===Fa&&qa(Ie)));return t}())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=Oc())!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((o=Ll())===u&&(o=null),o!==u&&ub()!==u&&(s=hl())!==u?(Pa=t,t=e={type:"window_func",name:e,args:{type:"expr_list",value:[n]},over:s,consider_nulls:o}):(Ma=t,t=u)):(Ma=t,t=u);return t}()),t}function Ll(){var t,e,n;return t=Ma,"ignore"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Pe)),e===u&&("respect"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(De))),e!==u&&ub()!==u?("nulls"===r.substr(Ma,5).toLowerCase()?(n=r.substr(Ma,5),Ma+=5):(n=u,0===Fa&&qa(R)),n!==u?(Pa=t,t=e=e.toUpperCase()+" NULLS"):(Ma=t,t=u)):(Ma=t,t=u),t}function Cl(){var r,t,e;return r=Ma,(t=Qp())!==u&&ub()!==u&&(e=$l())!==u?(Pa=r,r=t={symbol:t,delimiter:e}):(Ma=r,r=u),r}function El(){var r,t,e,n,o,s,a,i,c,l,f;if(r=Ma,(t=Uf())===u&&(t=null),t!==u)if(ub()!==u)if((e=zp())!==u)if(ub()!==u)if((n=Oc())!==u)if(ub()!==u)if((o=Zp())!==u)if(ub()!==u){for(s=[],a=Ma,(i=ub())!==u?((c=Wf())===u&&(c=Yf()),c!==u&&(l=ub())!==u&&(f=Oc())!==u?a=i=[i,c,l,f]:(Ma=a,a=u)):(Ma=a,a=u);a!==u;)s.push(a),a=Ma,(i=ub())!==u?((c=Wf())===u&&(c=Yf()),c!==u&&(l=ub())!==u&&(f=Oc())!==u?a=i=[i,c,l,f]:(Ma=a,a=u)):(Ma=a,a=u);s!==u&&(a=ub())!==u?((i=Cl())===u&&(i=null),i!==u&&(c=ub())!==u?((l=vc())===u&&(l=null),l!==u?(Pa=r,r=t=function(r,t,e,n,o){const u=e.length;let s=t;s.parentheses=!0;for(let r=0;r<u;++r)s=Rb(e[r][1],s,e[r][3]);return{distinct:r,expr:s,orderby:o,separator:n}}(t,n,s,i,l)):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)}else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;else Ma=r,r=u;return r===u&&(r=Ma,(t=Uf())===u&&(t=null),t!==u&&ub()!==u&&(e=ec())!==u&&ub()!==u?((n=Cl())===u&&(n=null),n!==u&&ub()!==u?((o=vc())===u&&(o=null),o!==u?(Pa=r,r=t={distinct:t,expr:e,orderby:o,separator:n}):(Ma=r,r=u)):(Ma=r,r=u)):(Ma=r,r=u)),r}function Al(){var t,e,n;return t=Ma,(e=function(){var t;return"both"===r.substr(Ma,4).toLowerCase()?(t=r.substr(Ma,4),Ma+=4):(t=u,0===Fa&&qa(We)),t===u&&("leading"===r.substr(Ma,7).toLowerCase()?(t=r.substr(Ma,7),Ma+=7):(t=u,0===Fa&&qa(Ye)),t===u&&("trailing"===r.substr(Ma,8).toLowerCase()?(t=r.substr(Ma,8),Ma+=8):(t=u,0===Fa&&qa(qe)))),t}())===u&&(e=null),e!==u&&ub()!==u?((n=Oc())===u&&(n=null),n!==u&&ub()!==u&&wf()!==u?(Pa=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Ma=t,t=u)):(Ma=t,t=u),t}function gl(){var t,e,n,o;return t=Ma,"trim"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Ve)),e!==u&&ub()!==u&&zp()!==u&&ub()!==u?((n=Al())===u&&(n=null),n!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...xb()}}(n,o)):(Ma=t,t=u)):(Ma=t,t=u),t}function Tl(){var t,e,n,o;return t=Ma,"mode"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(et)),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=$l())!==u?(Pa=t,t=e=function(r){const t=new Set(["object","array","both"]);if(!r.value||!t.has(r.value.toLowerCase()))throw new Error((r&&r.value)+" is not valid mode in object, array and both");return r.value=r.value.toUpperCase(),{type:"mode",symbol:"=>",value:r}}(o)):(Ma=t,t=u)):(Ma=t,t=u),t}function _l(){var t,e,n,o,s,a,i,c,l,f;return t=Ma,(e=function(){var t,e,n,o;return t=Ma,"input"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Xe)),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=Oc())!==u?(Pa=t,t=e={type:"input",symbol:"=>",value:o}):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u?(n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=function(){var t,e,n,o;return t=Ma,"path"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Qe)),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=$l())!==u?(Pa=t,t=e={type:"path",symbol:"=>",value:o}):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u?n=o=[o,s,a,i]:(Ma=n,n=u),n===u&&(n=null),n!==u?(o=Ma,(s=ub())!==u&&(a=Qp())!==u&&(i=ub())!==u&&(c=function(){var t,e,n,o;return t=Ma,"outer"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Ke)),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=Gl())!==u?(Pa=t,t=e={type:"outer",symbol:"=>",value:o}):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u?o=s=[s,a,i,c]:(Ma=o,o=u),o===u&&(o=null),o!==u?(s=Ma,(a=ub())!==u&&(i=Qp())!==u&&(c=ub())!==u&&(l=function(){var t,e,n,o;return t=Ma,"recursive"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(ze)),e!==u&&ub()!==u?("=>"===r.substr(Ma,2)?(n="=>",Ma+=2):(n=u,0===Fa&&qa(gt)),n!==u&&ub()!==u&&(o=Gl())!==u?(Pa=t,t=e={type:"recursive",symbol:"=>",value:o}):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u?s=a=[a,i,c,l]:(Ma=s,s=u),s===u&&(s=null),s!==u?(a=Ma,(i=ub())!==u&&(c=Qp())!==u&&(l=ub())!==u&&(f=Tl())!==u?a=i=[i,c,l,f]:(Ma=a,a=u),a===u&&(a=null),a!==u?(Pa=t,t=e=function(r,t,e,n,o){return{type:"flattern",input:r,path:t&&t[3],outer:e&&e[3],recursive:n&&n[3],mode:o&&o[3]}}(e,n,o,s,a)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t}function Sl(){var r,t;return r=Ma,Dp()!==u&&ub()!==u&&(t=Jc())!==u?(Pa=r,r={type:"json_visitor",symbol:":",expr:t}):(Ma=r,r=u),r}function jl(){var r,t,e,n,o,s;if(r=Ma,(t=Sl())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Sl())!==u?n=o=[o,s]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Sl())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u?(Pa=r,r=t={type:"expr_list",value:Ob(t,e,1)}):(Ma=r,r=u)}else Ma=r,r=u;return r}function xl(){var t,e,n;return t=Ma,"position"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Ze)),e!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(n=function(){var r,t,e,n,o,s,a,i;return r=Ma,(t=$l())!==u&&ub()!==u&&Pf()!==u&&ub()!==u&&(e=Oc())!==u?(n=Ma,(o=ub())!==u&&(s=wf())!==u&&(a=ub())!==u&&(i=Wl())!==u?n=o=[o,s,a,i]:(Ma=n,n=u),n===u&&(n=null),n!==u?(Pa=r,r=t=function(r,t,e){let n=[r,{type:"origin",value:"in"},t];return e&&(n.push({type:"origin",value:"from"}),n.push(e[3])),{type:"expr_list",value:n}}(t,e,n)):(Ma=r,r=u)):(Ma=r,r=u),r}())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e={type:"function",name:{name:[{type:"origin",value:"position"}]},separator:" ",args:n,...xb()}):(Ma=t,t=u),t}function Il(){var t,e,n,o,s,a,i,c,l,f;return(t=gl())===u&&(t=xl())===u&&(t=Ma,"now"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Je)),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=_c())===u&&(o=null),o!==u&&ub()!==u&&Zp()!==u&&ub()!==u?("at"===r.substr(Ma,2).toLowerCase()?(s=r.substr(Ma,2),Ma+=2):(s=u,0===Fa&&qa(rn)),s!==u&&ub()!==u&&Sp()!==u&&ub()!==u?("zone"===r.substr(Ma,4).toLowerCase()?(a=r.substr(Ma,4),Ma+=4):(a=u,0===Fa&&qa(tn)),a!==u&&ub()!==u&&(i=$l())!==u?(Pa=t,c=e,l=o,(f=i).prefix="at time zone",t=e={type:"function",name:{name:[{type:"default",value:c}]},args:l||{type:"expr_list",value:[]},suffix:f,...xb()}):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"flatten"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(en)),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u&&(o=_l())!==u&&ub()!==u&&Zp()!==u?(Pa=t,t=e=function(r,t){return{type:"flatten",name:{name:[{type:"default",value:r}]},args:t,...xb()}}(e,o)):(Ma=t,t=u),t===u&&(t=Ma,(e=function(){var t;(t=Nl())===u&&(t=function(){var t,e,n,o;t=Ma,"current_user"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(rr));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CURRENT_USER"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"user"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(bs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="USER"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"session_user"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(tr));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SESSION_USER"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"system_user"===r.substr(Ma,11).toLowerCase()?(e=r.substr(Ma,11),Ma+=11):(e=u,0===Fa&&qa(Fs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SYSTEM_USER"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&("ntile"===r.substr(Ma,5).toLowerCase()?(t=r.substr(Ma,5),Ma+=5):(t=u,0===Fa&&qa(Sn)));return t}())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=_c())===u&&(o=null),o!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((s=hl())===u&&(s=null),s!==u?(Pa=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...xb()}}(e,o,s)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=function(){var r,t,e,n,o;r=Ma,(t=Vf())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(e=Rl())!==u&&ub()!==u&&wf()!==u&&ub()!==u?((n=jp())===u&&(n=Np())===u&&(n=Sp())===u&&(n=gp()),n===u&&(n=null),n!==u&&ub()!==u&&(o=Oc())!==u&&ub()!==u&&Zp()!==u?(Pa=r,s=e,a=n,i=o,t={type:t.toLowerCase(),args:{field:s,cast_type:a,source:i},...xb()},r=t):(Ma=r,r=u)):(Ma=r,r=u);var s,a,i;r===u&&(r=Ma,(t=Vf())!==u&&ub()!==u&&zp()!==u&&ub()!==u&&(e=Rl())!==u&&ub()!==u&&wf()!==u&&ub()!==u&&(n=Oc())!==u&&ub()!==u&&(o=Zp())!==u?(Pa=r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...xb()}}(t,e,n),r=t):(Ma=r,r=u));return r}())===u&&(t=Ma,(e=Nl())!==u&&ub()!==u?((n=dl())===u&&(n=null),n!==u?(Pa=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...xb()}):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,"parse_json"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(nn)),e!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=kc())===u&&(o=null),o!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((s=jl())===u&&(s=null),s!==u?(Pa=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},suffix:e,...xb()}}(e,o,s)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=wb())!==u&&ub()!==u&&(n=zp())!==u&&ub()!==u?((o=kc())===u&&(o=null),o!==u&&ub()!==u&&Zp()!==u&&ub()!==u?((s=hl())===u&&(s=null),s!==u?(Pa=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...xb()}}(e,o,s)):(Ma=t,t=u)):(Ma=t,t=u)):(Ma=t,t=u))))))),t}function Rl(){var t,e;return t=Ma,"century"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(on)),e===u&&("day"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(un)),e===u&&("date"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(sn)),e===u&&("decade"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(an)),e===u&&("dow"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(cn)),e===u&&("doy"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(ln)),e===u&&("epoch"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(fn)),e===u&&("hour"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(pn)),e===u&&("isodow"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(bn)),e===u&&("isoyear"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(vn)),e===u&&("microseconds"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(yn)),e===u&&("millennium"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(dn)),e===u&&("milliseconds"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(hn)),e===u&&("minute"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(wn)),e===u&&("month"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(mn)),e===u&&("quarter"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ln)),e===u&&("second"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Cn)),e===u&&("timezone"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(En)),e===u&&("timezone_hour"===r.substr(Ma,13).toLowerCase()?(e=r.substr(Ma,13),Ma+=13):(e=u,0===Fa&&qa(An)),e===u&&("timezone_minute"===r.substr(Ma,15).toLowerCase()?(e=r.substr(Ma,15),Ma+=15):(e=u,0===Fa&&qa(gn)),e===u&&("week"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Tn)),e===u&&("year"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(_n))))))))))))))))))))))),e!==u&&(Pa=t,e=e),t=e}function Nl(){var t;return(t=function(){var t,e,n,o;t=Ma,"current_date"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(Ss));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CURRENT_DATE"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ma,"current_time"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(Gs));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CURRENT_TIME"):(Ma=t,t=u)):(Ma=t,t=u);return t}())===u&&(t=Op()),t}function Ol(){var t,e,n,o;return t=Ma,34===r.charCodeAt(Ma)?(e='"',Ma++):(e=u,0===Fa&&qa(ve)),e===u&&(e=null),e!==u&&(n=Ab())!==u?(34===r.charCodeAt(Ma)?(o='"',Ma++):(o=u,0===Fa&&qa(ve)),o===u&&(o=null),o!==u?(Pa=t,t=e=function(r,t,e){if(r&&!e||!r&&e)throw new Error("double quoted not match");return r&&e&&(t.quoted='"'),t}(e,n,o)):(Ma=t,t=u)):(Ma=t,t=u),t}function kl(){var r,t,e,n,o,s;if(r=Ma,t=[],e=Ma,(n=Pp())!==u&&(o=ub())!==u&&(s=Ol())!==u?e=n=[n,o,s]:(Ma=e,e=u),e!==u)for(;e!==u;)t.push(e),e=Ma,(n=Pp())!==u&&(o=ub())!==u&&(s=Ol())!==u?e=n=[n,o,s]:(Ma=e,e=u);else t=u;return t!==u&&(e=ub())!==u?((n=Bi())===u&&(n=null),n!==u?(Pa=r,r=t={as:n,symbol:"::",target:t.map(r=>r[2])}):(Ma=r,r=u)):(Ma=r,r=u),r}function Ul(){var t;return(t=$l())===u&&(t=Wl())===u&&(t=Gl())===u&&(t=Pl())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,(e=Sp())===u&&(e=gp())===u&&(e=jp())===u&&(e=Tp());if(e!==u)if(ub()!==u){if(n=Ma,39===r.charCodeAt(Ma)?(o="'",Ma++):(o=u,0===Fa&&qa(Wr)),o!==u){for(s=[],a=Bl();a!==u;)s.push(a),a=Bl();s!==u?(39===r.charCodeAt(Ma)?(a="'",Ma++):(a=u,0===Fa&&qa(Wr)),a!==u?n=o=[o,s,a]:(Ma=n,n=u)):(Ma=n,n=u)}else Ma=n,n=u;n!==u?(Pa=t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;var i;if(t===u)if(t=Ma,(e=Sp())===u&&(e=gp())===u&&(e=jp())===u&&(e=Tp()),e!==u)if(ub()!==u){if(n=Ma,34===r.charCodeAt(Ma)?(o='"',Ma++):(o=u,0===Fa&&qa(ve)),o!==u){for(s=[],a=Fl();a!==u;)s.push(a),a=Fl();s!==u?(34===r.charCodeAt(Ma)?(a='"',Ma++):(a=u,0===Fa&&qa(ve)),a!==u?n=o=[o,s,a]:(Ma=n,n=u)):(Ma=n,n=u)}else Ma=n,n=u;n!==u?(Pa=t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;return t}())===u&&(t=Ml()),t}function Ml(){var r,t;return r=Ma,qf()!==u&&ub()!==u&&Jp()!==u&&ub()!==u?((t=_c())===u&&(t=null),t!==u&&ub()!==u&&rb()!==u?(Pa=r,r={expr_list:t||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}):(Ma=r,r=u)):(Ma=r,r=u),r}function Pl(){var t,e;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"null"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(no));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={type:"null",value:null}),t=e}function Dl(){var t,e;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"not null"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(oo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={type:"not null",value:"not null"}),t=e}function Gl(){var t,e;return t=Ma,(e=function(){var t,e,n,o;t=Ma,"true"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(uo));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={type:"bool",value:!0}),(t=e)===u&&(t=Ma,(e=function(){var t,e,n,o;t=Ma,"false"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(io));e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u);return t}())!==u&&(Pa=t,e={type:"bool",value:!1}),t=e),t}function $l(){var t,e,n,o,s,a,i,c,l;if(t=Ma,e=Ma,39===r.charCodeAt(Ma)?(n="'",Ma++):(n=u,0===Fa&&qa(Wr)),n!==u){for(o=[],s=Bl();s!==u;)o.push(s),s=Bl();o!==u?(39===r.charCodeAt(Ma)?(s="'",Ma++):(s=u,0===Fa&&qa(Wr)),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u)}else Ma=e,e=u;if(e!==u){if(n=[],jn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(xn)),o!==u)for(;o!==u;)n.push(o),jn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(xn));else n=u;if(n!==u)if((o=ub())!==u){if(s=Ma,39===r.charCodeAt(Ma)?(a="'",Ma++):(a=u,0===Fa&&qa(Wr)),a!==u){for(i=[],c=Bl();c!==u;)i.push(c),c=Bl();i!==u?(39===r.charCodeAt(Ma)?(c="'",Ma++):(c=u,0===Fa&&qa(Wr)),c!==u?s=a=[a,i,c]:(Ma=s,s=u)):(Ma=s,s=u)}else Ma=s,s=u;s!==u?(Pa=t,l=s,t=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`,...xb()}):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u}else Ma=t,t=u;if(t===u){if(t=Ma,e=Ma,39===r.charCodeAt(Ma)?(n="'",Ma++):(n=u,0===Fa&&qa(Wr)),n!==u){for(o=[],s=Bl();s!==u;)o.push(s),s=Bl();o!==u?(39===r.charCodeAt(Ma)?(s="'",Ma++):(s=u,0===Fa&&qa(Wr)),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u)}else Ma=e,e=u;if(e!==u&&(Pa=t,e=function(r){return{type:"single_quote_string",value:r[1].join(""),...xb()}}(e)),(t=e)===u){if(t=Ma,e=Ma,34===r.charCodeAt(Ma)?(n='"',Ma++):(n=u,0===Fa&&qa(ve)),n!==u){for(o=[],s=Fl();s!==u;)o.push(s),s=Fl();o!==u?(34===r.charCodeAt(Ma)?(s='"',Ma++):(s=u,0===Fa&&qa(ve)),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u)}else Ma=e,e=u;e!==u?(n=Ma,Fa++,o=Xp(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)):(Ma=t,t=u)):(Ma=t,t=u)}}return t}function Fl(){var t;return In.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Rn)),t===u&&(t=Hl()),t}function Bl(){var t;return Nn.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(On)),t===u&&(t=Hl()),t}function Hl(){var t,e,n,o,s,a,i,c,l,f;return t=Ma,"\\'"===r.substr(Ma,2)?(e="\\'",Ma+=2):(e=u,0===Fa&&qa(kn)),e!==u&&(Pa=t,e="\\'"),(t=e)===u&&(t=Ma,'\\"'===r.substr(Ma,2)?(e='\\"',Ma+=2):(e=u,0===Fa&&qa(Un)),e!==u&&(Pa=t,e='\\"'),(t=e)===u&&(t=Ma,"\\\\"===r.substr(Ma,2)?(e="\\\\",Ma+=2):(e=u,0===Fa&&qa(Mn)),e!==u&&(Pa=t,e="\\\\"),(t=e)===u&&(t=Ma,"\\/"===r.substr(Ma,2)?(e="\\/",Ma+=2):(e=u,0===Fa&&qa(Pn)),e!==u&&(Pa=t,e="\\/"),(t=e)===u&&(t=Ma,"\\b"===r.substr(Ma,2)?(e="\\b",Ma+=2):(e=u,0===Fa&&qa(Dn)),e!==u&&(Pa=t,e="\b"),(t=e)===u&&(t=Ma,"\\f"===r.substr(Ma,2)?(e="\\f",Ma+=2):(e=u,0===Fa&&qa(Gn)),e!==u&&(Pa=t,e="\f"),(t=e)===u&&(t=Ma,"\\n"===r.substr(Ma,2)?(e="\\n",Ma+=2):(e=u,0===Fa&&qa($n)),e!==u&&(Pa=t,e="\n"),(t=e)===u&&(t=Ma,"\\r"===r.substr(Ma,2)?(e="\\r",Ma+=2):(e=u,0===Fa&&qa(Fn)),e!==u&&(Pa=t,e="\r"),(t=e)===u&&(t=Ma,"\\t"===r.substr(Ma,2)?(e="\\t",Ma+=2):(e=u,0===Fa&&qa(Bn)),e!==u&&(Pa=t,e="\t"),(t=e)===u&&(t=Ma,"\\u"===r.substr(Ma,2)?(e="\\u",Ma+=2):(e=u,0===Fa&&qa(Hn)),e!==u&&(n=zl())!==u&&(o=zl())!==u&&(s=zl())!==u&&(a=zl())!==u?(Pa=t,i=n,c=o,l=s,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Ma=t,t=u),t===u&&(t=Ma,92===r.charCodeAt(Ma)?(e="\\",Ma++):(e=u,0===Fa&&qa(Wn)),e!==u&&(Pa=t,e="\\"),(t=e)===u&&(t=Ma,"''"===r.substr(Ma,2)?(e="''",Ma+=2):(e=u,0===Fa&&qa(Yn)),e!==u&&(Pa=t,e="''"),t=e))))))))))),t}function Wl(){var r,t,e;return r=Ma,(t=Yl())!==u&&(Pa=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function Yl(){var r,t,e,n;return r=Ma,(t=ql())===u&&(t=null),t!==u&&(e=Vl())!==u&&(n=Xl())!==u?(Pa=r,r=t={type:"bigint",value:(t||"")+e+n}):(Ma=r,r=u),r===u&&(r=Ma,(t=ql())===u&&(t=null),t!==u&&(e=Vl())!==u?(Pa=r,r=t=function(r,t){const e=(r||"")+t;return r&&Nb(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e)):(Ma=r,r=u),r===u&&(r=Ma,(t=ql())!==u&&(e=Xl())!==u?(Pa=r,r=t=function(r,t){return{type:"bigint",value:r+t}}(t,e)):(Ma=r,r=u),r===u&&(r=Ma,(t=ql())!==u&&(Pa=r,t=function(r){return Nb(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t))),r}function ql(){var t,e,n;return(t=Ql())===u&&(t=Kl())===u&&(t=Ma,45===r.charCodeAt(Ma)?(e="-",Ma++):(e=u,0===Fa&&qa(Kt)),e===u&&(43===r.charCodeAt(Ma)?(e="+",Ma++):(e=u,0===Fa&&qa(Qt))),e!==u&&(n=Ql())!==u?(Pa=t,t=e=e+n):(Ma=t,t=u),t===u&&(t=Ma,45===r.charCodeAt(Ma)?(e="-",Ma++):(e=u,0===Fa&&qa(Kt)),e===u&&(43===r.charCodeAt(Ma)?(e="+",Ma++):(e=u,0===Fa&&qa(Qt))),e!==u&&(n=Kl())!==u?(Pa=t,t=e=function(r,t){return r+t}(e,n)):(Ma=t,t=u))),t}function Vl(){var t,e,n;return t=Ma,46===r.charCodeAt(Ma)?(e=".",Ma++):(e=u,0===Fa&&qa(Xn)),e!==u&&(n=Ql())!==u?(Pa=t,t=e="."+n):(Ma=t,t=u),t}function Xl(){var t,e,n;return t=Ma,(e=function(){var t,e,n;t=Ma,Jn.test(r.charAt(Ma))?(e=r.charAt(Ma),Ma++):(e=u,0===Fa&&qa(ro));e!==u?(to.test(r.charAt(Ma))?(n=r.charAt(Ma),Ma++):(n=u,0===Fa&&qa(eo)),n===u&&(n=null),n!==u?(Pa=t,t=e=e+(null!==(o=n)?o:"")):(Ma=t,t=u)):(Ma=t,t=u);var o;return t}())!==u&&(n=Ql())!==u?(Pa=t,t=e=e+n):(Ma=t,t=u),t}function Ql(){var r,t,e;if(r=Ma,t=[],(e=Kl())!==u)for(;e!==u;)t.push(e),e=Kl();else t=u;return t!==u&&(Pa=r,t=t.join("")),r=t}function Kl(){var t;return Qn.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Kn)),t}function zl(){var t;return zn.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Zn)),t}function Zl(){var t,e,n,o;return t=Ma,"default"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(F)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Jl(){var t,e,n,o;return t=Ma,"to"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(so)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function rf(){var t,e,n,o;return t=Ma,"top"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(ao)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function tf(){var t,e,n,o;return t=Ma,"show"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(co)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function ef(){var t,e,n,o;return t=Ma,"drop"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(lo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DROP"):(Ma=t,t=u)):(Ma=t,t=u),t}function nf(){var t,e,n,o;return t=Ma,"alter"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(po)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function of(){var t,e,n,o;return t=Ma,"select"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(bo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function uf(){var t,e,n,o;return t=Ma,"update"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(vo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function sf(){var t,e,n,o;return t=Ma,"create"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(yo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function af(){var t,e,n,o;return t=Ma,"temporary"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(ho)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function cf(){var t,e,n,o;return t=Ma,"temp"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(wo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function lf(){var t,e,n,o;return t=Ma,"delete"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(mo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function ff(){var t,e,n,o;return t=Ma,"insert"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Lo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function pf(){var t,e,n,o;return t=Ma,"recursive"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(ze)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="RECURSIVE"):(Ma=t,t=u)):(Ma=t,t=u),t}function bf(){var t,e,n,o;return t=Ma,"replace"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Co)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function vf(){var t,e,n,o;return t=Ma,"rename"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Ao)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function yf(){var t,e,n,o;return t=Ma,"ignore"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Pe)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function df(){var t,e,n,o;return t=Ma,"partition"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(go)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="PARTITION"):(Ma=t,t=u)):(Ma=t,t=u),t}function hf(){var t,e,n,o;return t=Ma,"into"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(To)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function wf(){var t,e,n,o;return t=Ma,"from"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(_o)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function mf(){var t,e,n,o;return t=Ma,"set"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(kr)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SET"):(Ma=t,t=u)):(Ma=t,t=u),t}function Lf(){var t,e,n,o;return t=Ma,"as"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(So)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Cf(){var t,e,n,o;return t=Ma,"table"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(jo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TABLE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Ef(){var t,e,n,o;return t=Ma,"schema"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(l)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SCHEMA"):(Ma=t,t=u)):(Ma=t,t=u),t}function Af(){var t,e,n,o;return t=Ma,"on"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Cr)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function gf(){var t,e,n,o;return t=Ma,"join"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Po)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Tf(){var t,e,n,o;return t=Ma,"outer"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Ke)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function _f(){var t,e,n,o;return t=Ma,"values"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Go)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Sf(){var t,e,n,o;return t=Ma,"using"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa($o)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function jf(){var t,e,n,o;return t=Ma,"with"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(ht)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function xf(){var t,e,n,o;return t=Ma,"group"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Bo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function If(){var t,e,n,o;return t=Ma,"by"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Ho)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Rf(){var t,e,n,o;return t=Ma,"order"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Wo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Nf(){var t,e,n,o;return t=Ma,"asc"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Ko)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ASC"):(Ma=t,t=u)):(Ma=t,t=u),t}function Of(){var t,e,n,o;return t=Ma,"desc"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(zo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DESC"):(Ma=t,t=u)):(Ma=t,t=u),t}function kf(){var t,e,n,o;return t=Ma,"all"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Zo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ALL"):(Ma=t,t=u)):(Ma=t,t=u),t}function Uf(){var t,e,n,o;return t=Ma,"distinct"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Jo)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DISTINCT"):(Ma=t,t=u)):(Ma=t,t=u),t}function Mf(){var t,e,n,o;return t=Ma,"between"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(ru)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BETWEEN"):(Ma=t,t=u)):(Ma=t,t=u),t}function Pf(){var t,e,n,o;return t=Ma,"in"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(Xr)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="IN"):(Ma=t,t=u)):(Ma=t,t=u),t}function Df(){var t,e,n,o;return t=Ma,"is"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(tu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="IS"):(Ma=t,t=u)):(Ma=t,t=u),t}function Gf(){var t,e,n,o;return t=Ma,"like"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(eu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="LIKE"):(Ma=t,t=u)):(Ma=t,t=u),t}function $f(){var t,e,n,o;return t=Ma,"ilike"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(nu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ILIKE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Ff(){var t,e,n,o;return t=Ma,"exists"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(ou)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="EXISTS"):(Ma=t,t=u)):(Ma=t,t=u),t}function Bf(){var t,e,n,o;return t=Ma,"regexp"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(uu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGEXP"):(Ma=t,t=u)):(Ma=t,t=u),t}function Hf(){var t,e,n,o;return t=Ma,"not"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Tr)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="NOT"):(Ma=t,t=u)):(Ma=t,t=u),t}function Wf(){var t,e,n,o;return t=Ma,"and"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(su)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="AND"):(Ma=t,t=u)):(Ma=t,t=u),t}function Yf(){var t,e,n,o;return t=Ma,"or"===r.substr(Ma,2).toLowerCase()?(e=r.substr(Ma,2),Ma+=2):(e=u,0===Fa&&qa(au)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="OR"):(Ma=t,t=u)):(Ma=t,t=u),t}function qf(){var t,e,n,o;return t=Ma,"array"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(iu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ARRAY"):(Ma=t,t=u)):(Ma=t,t=u),t}function Vf(){var t,e,n,o;return t=Ma,"extract"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(hu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="EXTRACT"):(Ma=t,t=u)):(Ma=t,t=u),t}function Xf(){var t,e,n,o;return t=Ma,"case"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(mu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Qf(){var t,e,n,o;return t=Ma,"when"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Lu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function Kf(){var t,e,n,o;return t=Ma,"end"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Au)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?t=e=[e,n]:(Ma=t,t=u)):(Ma=t,t=u),t}function zf(){var t,e,n,o;return t=Ma,"cast"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(gu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CAST"):(Ma=t,t=u)):(Ma=t,t=u),t}function Zf(){var t,e,n,o;return t=Ma,"binary"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Tu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BINARY"):(Ma=t,t=u)):(Ma=t,t=u),t}function Jf(){var t,e,n,o;return t=Ma,"varbinary"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(_u)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="VARBINARY"):(Ma=t,t=u)):(Ma=t,t=u),t}function rp(){var t,e,n,o;return t=Ma,"char"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(xu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CHAR"):(Ma=t,t=u)):(Ma=t,t=u),t}function tp(){var t,e,n,o;return t=Ma,"varchar"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Iu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="VARCHAR"):(Ma=t,t=u)):(Ma=t,t=u),t}function ep(){var t,e,n,o;return t=Ma,"number"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Ru)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="NUMBER"):(Ma=t,t=u)):(Ma=t,t=u),t}function np(){var t,e,n,o;return t=Ma,"numeric"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Nu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="NUMERIC"):(Ma=t,t=u)):(Ma=t,t=u),t}function op(){var t,e,n,o;return t=Ma,"decimal"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ou)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DECIMAL"):(Ma=t,t=u)):(Ma=t,t=u),t}function up(){var t,e,n,o;return t=Ma,"unsigned"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Mu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="UNSIGNED"):(Ma=t,t=u)):(Ma=t,t=u),t}function sp(){var t,e,n,o;return t=Ma,"int"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(Pu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="INT"):(Ma=t,t=u)):(Ma=t,t=u),t}function ap(){var t,e,n,o;return t=Ma,"byteint"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Du)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BYTEINT"):(Ma=t,t=u)):(Ma=t,t=u),t}function ip(){var t,e,n,o;return t=Ma,"integer"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa($u)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="INTEGER"):(Ma=t,t=u)):(Ma=t,t=u),t}function cp(){var t,e,n,o;return t=Ma,"smallint"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Yu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SMALLINT"):(Ma=t,t=u)):(Ma=t,t=u),t}function lp(){var t,e,n,o;return t=Ma,"serial"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(qu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="SERIAL"):(Ma=t,t=u)):(Ma=t,t=u),t}function fp(){var t,e,n,o;return t=Ma,"tinyint"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Vu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TINYINT"):(Ma=t,t=u)):(Ma=t,t=u),t}function pp(){var t,e,n,o;return t=Ma,"tinytext"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Xu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TINYTEXT"):(Ma=t,t=u)):(Ma=t,t=u),t}function bp(){var t,e,n,o;return t=Ma,"text"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Qu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TEXT"):(Ma=t,t=u)):(Ma=t,t=u),t}function vp(){var t,e,n,o;return t=Ma,"mediumtext"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(Ku)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="MEDIUMTEXT"):(Ma=t,t=u)):(Ma=t,t=u),t}function yp(){var t,e,n,o;return t=Ma,"longtext"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(zu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="LONGTEXT"):(Ma=t,t=u)):(Ma=t,t=u),t}function dp(){var t,e,n,o;return t=Ma,"bigint"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(Zu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BIGINT"):(Ma=t,t=u)):(Ma=t,t=u),t}function hp(){var t,e,n,o;return t=Ma,"enum"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Ju)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ENUM"):(Ma=t,t=u)):(Ma=t,t=u),t}function wp(){var t,e,n,o;return t=Ma,"float"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(rs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="FLOAT"):(Ma=t,t=u)):(Ma=t,t=u),t}function mp(){var t,e,n,o;return t=Ma,"float4"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(ts)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="FLOAT4"):(Ma=t,t=u)):(Ma=t,t=u),t}function Lp(){var t,e,n,o;return t=Ma,"float8"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(es)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="FLOAT8"):(Ma=t,t=u)):(Ma=t,t=u),t}function Cp(){var t,e,n,o;return t=Ma,"double"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(ns)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DOUBLE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Ep(){var t,e,n,o;return t=Ma,"bigserial"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(os)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BIGSERIAL"):(Ma=t,t=u)):(Ma=t,t=u),t}function Ap(){var t,e,n,o;return t=Ma,"real"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(us)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REAL"):(Ma=t,t=u)):(Ma=t,t=u),t}function gp(){var t,e,n,o;return t=Ma,"date"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(sn)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DATE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Tp(){var t,e,n,o;return t=Ma,"datetime"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(ss)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="DATETIME"):(Ma=t,t=u)):(Ma=t,t=u),t}function _p(){var t,e,n,o;return t=Ma,"rows"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(as)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ROWS"):(Ma=t,t=u)):(Ma=t,t=u),t}function Sp(){var t,e,n,o;return t=Ma,"time"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(is)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TIME"):(Ma=t,t=u)):(Ma=t,t=u),t}function jp(){var t,e,n,o;return t=Ma,"timestamp"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(cs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TIMESTAMP"):(Ma=t,t=u)):(Ma=t,t=u),t}function xp(){var t,e,n,o;return t=Ma,"timestamp_tz"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(ls)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TIMESTAMP_TZ"):(Ma=t,t=u)):(Ma=t,t=u),t}function Ip(){var t,e,n,o;return t=Ma,"timestamp_ntz"===r.substr(Ma,13).toLowerCase()?(e=r.substr(Ma,13),Ma+=13):(e=u,0===Fa&&qa(fs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TIMESTAMP_NTZ"):(Ma=t,t=u)):(Ma=t,t=u),t}function Rp(){var t,e,n,o;return t=Ma,"truncate"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(ps)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="TRUNCATE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Np(){var t,e,n,o;return t=Ma,"interval"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(js)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="INTERVAL"):(Ma=t,t=u)):(Ma=t,t=u),t}function Op(){var t,e,n,o;return t=Ma,"current_timestamp"===r.substr(Ma,17).toLowerCase()?(e=r.substr(Ma,17),Ma+=17):(e=u,0===Fa&&qa($s)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CURRENT_TIMESTAMP"):(Ma=t,t=u)):(Ma=t,t=u),t}function kp(){var t;return 36===r.charCodeAt(Ma)?(t="$",Ma++):(t=u,0===Fa&&qa(te)),t}function Up(){var t;return"$$"===r.substr(Ma,2)?(t="$$",Ma+=2):(t=u,0===Fa&&qa(zs)),t}function Mp(){var t;return(t=function(){var t;return"@@"===r.substr(Ma,2)?(t="@@",Ma+=2):(t=u,0===Fa&&qa(Ks)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt(Ma)?(t="@",Ma++):(t=u,0===Fa&&qa(Qs)),t}())===u&&(t=kp())===u&&(t=kp()),t}function Pp(){var t;return"::"===r.substr(Ma,2)?(t="::",Ma+=2):(t=u,0===Fa&&qa(ra)),t}function Dp(){var t;return 58===r.charCodeAt(Ma)?(t=":",Ma++):(t=u,0===Fa&&qa(_e)),t}function Gp(){var t;return 61===r.charCodeAt(Ma)?(t="=",Ma++):(t=u,0===Fa&&qa(Ut)),t}function $p(){var t,e,n,o;return t=Ma,"add"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(ea)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ADD"):(Ma=t,t=u)):(Ma=t,t=u),t}function Fp(){var t,e,n,o;return t=Ma,"column"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(na)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="COLUMN"):(Ma=t,t=u)):(Ma=t,t=u),t}function Bp(){var t,e,n,o;return t=Ma,"index"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(oa)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="INDEX"):(Ma=t,t=u)):(Ma=t,t=u),t}function Hp(){var t,e,n,o;return t=Ma,"key"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(M)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="KEY"):(Ma=t,t=u)):(Ma=t,t=u),t}function Wp(){var t,e,n,o;return t=Ma,"unique"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(U)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="UNIQUE"):(Ma=t,t=u)):(Ma=t,t=u),t}function Yp(){var t,e,n,o;return t=Ma,"comment"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(aa)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="COMMENT"):(Ma=t,t=u)):(Ma=t,t=u),t}function qp(){var t,e,n,o;return t=Ma,"constraint"===r.substr(Ma,10).toLowerCase()?(e=r.substr(Ma,10),Ma+=10):(e=u,0===Fa&&qa(ia)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CONSTRAINT"):(Ma=t,t=u)):(Ma=t,t=u),t}function Vp(){var t,e,n,o;return t=Ma,"concurrently"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(ca)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CONCURRENTLY"):(Ma=t,t=u)):(Ma=t,t=u),t}function Xp(){var t;return 46===r.charCodeAt(Ma)?(t=".",Ma++):(t=u,0===Fa&&qa(Xn)),t}function Qp(){var t;return 44===r.charCodeAt(Ma)?(t=",",Ma++):(t=u,0===Fa&&qa(ha)),t}function Kp(){var t;return 42===r.charCodeAt(Ma)?(t="*",Ma++):(t=u,0===Fa&&qa(zt)),t}function zp(){var t;return 40===r.charCodeAt(Ma)?(t="(",Ma++):(t=u,0===Fa&&qa(at)),t}function Zp(){var t;return 41===r.charCodeAt(Ma)?(t=")",Ma++):(t=u,0===Fa&&qa(it)),t}function Jp(){var t;return 91===r.charCodeAt(Ma)?(t="[",Ma++):(t=u,0===Fa&&qa(wa)),t}function rb(){var t;return 93===r.charCodeAt(Ma)?(t="]",Ma++):(t=u,0===Fa&&qa(ma)),t}function tb(){var t;return 59===r.charCodeAt(Ma)?(t=";",Ma++):(t=u,0===Fa&&qa(st)),t}function eb(){var t;return"->"===r.substr(Ma,2)?(t="->",Ma+=2):(t=u,0===Fa&&qa(La)),t}function nb(){var t;return"->>"===r.substr(Ma,3)?(t="->>",Ma+=3):(t=u,0===Fa&&qa(Ca)),t}function ob(){var t;return(t=function(){var t;return"||"===r.substr(Ma,2)?(t="||",Ma+=2):(t=u,0===Fa&&qa(re)),t}())===u&&(t=function(){var t;return"&&"===r.substr(Ma,2)?(t="&&",Ma+=2):(t=u,0===Fa&&qa(Ea)),t}()),t}function ub(){var r,t;for(r=[],(t=lb())===u&&(t=ab());t!==u;)r.push(t),(t=lb())===u&&(t=ab());return r}function sb(){var r,t;if(r=[],(t=lb())===u&&(t=ab()),t!==u)for(;t!==u;)r.push(t),(t=lb())===u&&(t=ab());else r=u;return r}function ab(){var t;return(t=function t(){var e,n,o,s,a,i,c;e=Ma,"/*"===r.substr(Ma,2)?(n="/*",Ma+=2):(n=u,0===Fa&&qa(Aa));if(n!==u){for(o=[],s=Ma,a=Ma,Fa++,"*/"===r.substr(Ma,2)?(i="*/",Ma+=2):(i=u,0===Fa&&qa(ga)),Fa--,i===u?a=void 0:(Ma=a,a=u),a!==u?(i=Ma,Fa++,"/*"===r.substr(Ma,2)?(c="/*",Ma+=2):(c=u,0===Fa&&qa(Aa)),Fa--,c===u?i=void 0:(Ma=i,i=u),i!==u&&(c=cb())!==u?s=a=[a,i,c]:(Ma=s,s=u)):(Ma=s,s=u),s===u&&(s=t());s!==u;)o.push(s),s=Ma,a=Ma,Fa++,"*/"===r.substr(Ma,2)?(i="*/",Ma+=2):(i=u,0===Fa&&qa(ga)),Fa--,i===u?a=void 0:(Ma=a,a=u),a!==u?(i=Ma,Fa++,"/*"===r.substr(Ma,2)?(c="/*",Ma+=2):(c=u,0===Fa&&qa(Aa)),Fa--,c===u?i=void 0:(Ma=i,i=u),i!==u&&(c=cb())!==u?s=a=[a,i,c]:(Ma=s,s=u)):(Ma=s,s=u),s===u&&(s=t());o!==u?("*/"===r.substr(Ma,2)?(s="*/",Ma+=2):(s=u,0===Fa&&qa(ga)),s!==u?e=n=[n,o,s]:(Ma=e,e=u)):(Ma=e,e=u)}else Ma=e,e=u;return e}())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,"--"===r.substr(Ma,2)?(e="--",Ma+=2):(e=u,0===Fa&&qa(Ta));if(e!==u){for(n=[],o=Ma,s=Ma,Fa++,a=fb(),Fa--,a===u?s=void 0:(Ma=s,s=u),s!==u&&(a=cb())!==u?o=s=[s,a]:(Ma=o,o=u);o!==u;)n.push(o),o=Ma,s=Ma,Fa++,a=fb(),Fa--,a===u?s=void 0:(Ma=s,s=u),s!==u&&(a=cb())!==u?o=s=[s,a]:(Ma=o,o=u);n!==u?t=e=[e,n]:(Ma=t,t=u)}else Ma=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,s,a;t=Ma,"//"===r.substr(Ma,2)?(e="//",Ma+=2):(e=u,0===Fa&&qa(_a));if(e!==u){for(n=[],o=Ma,s=Ma,Fa++,a=fb(),Fa--,a===u?s=void 0:(Ma=s,s=u),s!==u&&(a=cb())!==u?o=s=[s,a]:(Ma=o,o=u);o!==u;)n.push(o),o=Ma,s=Ma,Fa++,a=fb(),Fa--,a===u?s=void 0:(Ma=s,s=u),s!==u&&(a=cb())!==u?o=s=[s,a]:(Ma=o,o=u);n!==u?t=e=[e,n]:(Ma=t,t=u)}else Ma=t,t=u;return t}()),t}function ib(){var r,t,e,n,o,s,a;return r=Ma,(t=Yp())!==u&&ub()!==u?((e=Gp())===u&&(e=null),e!==u&&ub()!==u&&(n=$l())!==u?(Pa=r,s=e,a=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:s,value:a}):(Ma=r,r=u)):(Ma=r,r=u),r}function cb(){var t;return r.length>Ma?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(Sa)),t}function lb(){var t;return ja.test(r.charAt(Ma))?(t=r.charAt(Ma),Ma++):(t=u,0===Fa&&qa(xa)),t}function fb(){var t,e;if((t=function(){var t,e;t=Ma,Fa++,r.length>Ma?(e=r.charAt(Ma),Ma++):(e=u,0===Fa&&qa(Sa));Fa--,e===u?t=void 0:(Ma=t,t=u);return t}())===u)if(t=[],qn.test(r.charAt(Ma))?(e=r.charAt(Ma),Ma++):(e=u,0===Fa&&qa(Vn)),e!==u)for(;e!==u;)t.push(e),qn.test(r.charAt(Ma))?(e=r.charAt(Ma),Ma++):(e=u,0===Fa&&qa(Vn));else t=u;return t}function pb(){var t,e;return t=Ma,Pa=Ma,Db=[],(!0?void 0:u)!==u&&ub()!==u?((e=bb())===u&&(e=function(){var t,e;t=Ma,function(){var t;return"return"===r.substr(Ma,6).toLowerCase()?(t=r.substr(Ma,6),Ma+=6):(t=u,0===Fa&&qa(Zs)),t}()!==u&&ub()!==u&&(e=vb())!==u?(Pa=t,t={type:"return",expr:e}):(Ma=t,t=u);return t}()),e!==u?(Pa=t,t={type:"proc",stmt:e,vars:Db}):(Ma=t,t=u)):(Ma=t,t=u),t}function bb(){var t,e,n,o;return t=Ma,(e=Cb())===u&&(e=Eb()),e!==u&&ub()!==u?((n=function(){var t;return":="===r.substr(Ma,2)?(t=":=",Ma+=2):(t=u,0===Fa&&qa(Js)),t}())===u&&(n=Gp()),n!==u&&ub()!==u&&(o=vb())!==u?(Pa=t,t=e={type:"assign",left:e,symbol:n,right:o}):(Ma=t,t=u)):(Ma=t,t=u),t}function vb(){var r;return(r=ji())===u&&(r=function(){var r,t,e,n,o;r=Ma,(t=Cb())!==u&&ub()!==u&&(e=rc())!==u&&ub()!==u&&(n=Cb())!==u&&ub()!==u&&(o=nc())!==u?(Pa=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Ma=r,r=u);return r}())===u&&(r=yb())===u&&(r=function(){var r,t;r=Ma,Jp()!==u&&ub()!==u&&(t=Lb())!==u&&ub()!==u&&rb()!==u?(Pa=r,r={type:"array",value:t}):(Ma=r,r=u);return r}()),r}function yb(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=db())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Hc())!==u&&(a=ub())!==u&&(i=db())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Hc())!==u&&(a=ub())!==u&&(i=db())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Gt(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function db(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=hb())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Yc())!==u&&(a=ub())!==u&&(i=hb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Yc())!==u&&(a=ub())!==u&&(i=hb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Gt(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function hb(){var r,t,e;return(r=Ul())===u&&(r=Cb())===u&&(r=mb())===u&&(r=yl())===u&&(r=Ma,zp()!==u&&ub()!==u&&(t=yb())!==u&&ub()!==u&&Zp()!==u?(Pa=r,(e=t).parentheses=!0,r=e):(Ma=r,r=u)),r}function wb(){var r,t,e,n,o,s,a;return r=Ma,(t=Jc())!==u?(e=Ma,(n=ub())!==u&&(o=Xp())!==u&&(s=ub())!==u&&(a=Jc())!==u?e=n=[n,o,s,a]:(Ma=e,e=u),e===u&&(e=null),e!==u?(Pa=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(Ma=r,r=u)):(Ma=r,r=u),r}function mb(){var r,t,e;return r=Ma,(t=wb())!==u&&ub()!==u&&zp()!==u&&ub()!==u?((e=Lb())===u&&(e=null),e!==u&&ub()!==u&&Zp()!==u?(Pa=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...xb()}):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=wb())!==u&&(Pa=r,t=function(r){return{type:"function",name:r,args:null,...xb()}}(t)),r=t),r}function Lb(){var r,t,e,n,o,s,a,i;if(r=Ma,(t=hb())!==u){for(e=[],n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=hb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);n!==u;)e.push(n),n=Ma,(o=ub())!==u&&(s=Qp())!==u&&(a=ub())!==u&&(i=hb())!==u?n=o=[o,s,a,i]:(Ma=n,n=u);e!==u?(Pa=r,r=t=Ob(t,e)):(Ma=r,r=u)}else Ma=r,r=u;return r}function Cb(){var t,e,n,o,s,a,i;if(t=Ma,(e=Up())!==u){for(n=[],Ia.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Ra));o!==u;)n.push(o),Ia.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Ra));n!==u&&(o=Up())!==u?(Pa=t,t=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(Ma=t,t=u)}else Ma=t,t=u;if(t===u){if(t=Ma,(e=kp())!==u)if((n=cl())!==u)if((o=kp())!==u){for(s=[],Ia.test(r.charAt(Ma))?(a=r.charAt(Ma),Ma++):(a=u,0===Fa&&qa(Ra));a!==u;)s.push(a),Ia.test(r.charAt(Ma))?(a=r.charAt(Ma),Ma++):(a=u,0===Fa&&qa(Ra));s!==u&&(a=kp())!==u&&(i=cl())!==u?(Pa=Ma,(function(r,t,e){if(r!==e)return!0}(n,0,i)?u:void 0)!==u&&kp()!==u?(Pa=t,t=e=function(r,t,e){return{type:"var",name:t.join(""),prefix:`$${r}$`,suffix:`$${e}$`}}(n,s,i)):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;t===u&&(t=Ma,(e=Mp())!==u&&(n=Eb())!==u?(Pa=t,t=e=function(r,t){return{type:"var",...t,prefix:r}}(e,n)):(Ma=t,t=u))}return t}function Eb(){var t,e,n,o,s;return t=Ma,34===r.charCodeAt(Ma)?(e='"',Ma++):(e=u,0===Fa&&qa(ve)),e===u&&(e=null),e!==u&&(n=fl())!==u&&(o=function(){var t,e,n,o,s;t=Ma,e=[],n=Ma,46===r.charCodeAt(Ma)?(o=".",Ma++):(o=u,0===Fa&&qa(Xn));o!==u&&(s=fl())!==u?n=o=[o,s]:(Ma=n,n=u);for(;n!==u;)e.push(n),n=Ma,46===r.charCodeAt(Ma)?(o=".",Ma++):(o=u,0===Fa&&qa(Xn)),o!==u&&(s=fl())!==u?n=o=[o,s]:(Ma=n,n=u);e!==u&&(Pa=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(34===r.charCodeAt(Ma)?(s='"',Ma++):(s=u,0===Fa&&qa(ve)),s===u&&(s=null),s!==u?(Pa=t,t=e=function(r,t,e,n){if(r&&!n||!r&&n)throw new Error("double quoted not match");return Db.push(t),{type:"var",name:t,members:e,quoted:r&&n?'"':null,prefix:null}}(e,n,o,s)):(Ma=t,t=u)):(Ma=t,t=u),t===u&&(t=Ma,(e=Wl())!==u&&(Pa=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Ab(){var t;return(t=function(){var r,t,e;r=Ma,(t=_b())===u&&(t=gb());t!==u&&ub()!==u&&Jp()!==u&&ub()!==u&&(e=rb())!==u&&ub()!==u&&Jp()!==u&&ub()!==u&&rb()!==u?(Pa=r,n=t,t={...n,array:{dimension:2}},r=t):(Ma=r,r=u);var n;r===u&&(r=Ma,(t=_b())===u&&(t=gb()),t!==u&&ub()!==u&&Jp()!==u&&ub()!==u?((e=Wl())===u&&(e=null),e!==u&&ub()!==u&&rb()!==u?(Pa=r,t=function(r,t){return{...r,array:{dimension:1,length:[t]}}}(t,e),r=t):(Ma=r,r=u)):(Ma=r,r=u),r===u&&(r=Ma,(t=_b())===u&&(t=gb()),t!==u&&ub()!==u&&qf()!==u?(Pa=r,t=function(r){return{...r,array:{keyword:"array"}}}(t),r=t):(Ma=r,r=u)));return r}())===u&&(t=gb())===u&&(t=_b())===u&&(t=function(){var t,e,n,o;t=Ma,(e=gp())===u&&(e=Tp())===u&&(e=xp())===u&&(e=Ip());if(e!==u)if(ub()!==u)if(zp()!==u)if(ub()!==u){if(n=[],Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn)),o!==u)for(;o!==u;)n.push(o),Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn));else n=u;n!==u&&(o=ub())!==u&&Zp()!==u?(Pa=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;t===u&&(t=Ma,(e=gp())===u&&(e=Tp())===u&&(e=xp())===u&&(e=Ip()),e!==u&&(Pa=t,e=Ua(e)),(t=e)===u&&(t=function(){var t,e,n,o,s,a;t=Ma,(e=Sp())===u&&(e=jp());if(e!==u)if(ub()!==u)if((n=zp())!==u)if(ub()!==u){if(o=[],Qn.test(r.charAt(Ma))?(s=r.charAt(Ma),Ma++):(s=u,0===Fa&&qa(Kn)),s!==u)for(;s!==u;)o.push(s),Qn.test(r.charAt(Ma))?(s=r.charAt(Ma),Ma++):(s=u,0===Fa&&qa(Kn));else o=u;o!==u&&(s=ub())!==u&&Zp()!==u&&ub()!==u?((a=Sb())===u&&(a=null),a!==u?(Pa=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),parentheses:!0,suffix:e}}(e,o,a),t=e):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;t===u&&(t=Ma,(e=Sp())===u&&(e=jp()),e!==u&&ub()!==u?((n=Sb())===u&&(n=null),n!==u?(Pa=t,e=function(r,t){return{dataType:r,suffix:t}}(e,n),t=e):(Ma=t,t=u)):(Ma=t,t=u));return t}()));return t}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"json"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Fu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="JSON"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"jsonb"===r.substr(Ma,5).toLowerCase()?(e=r.substr(Ma,5),Ma+=5):(e=u,0===Fa&&qa(Bu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="JSONB"):(Ma=t,t=u)):(Ma=t,t=u),t}());e!==u&&(Pa=t,e=Ua(e));return t=e}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"geometry"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Hu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="GEOMETRY"):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u&&(Pa=t,e={dataType:e});return t=e}())===u&&(t=function(){var r,t;r=Ma,(t=pp())===u&&(t=bp())===u&&(t=vp())===u&&(t=yp());t!==u&&Jp()!==u&&ub()!==u&&rb()!==u?(Pa=r,r=t={dataType:t+"[]"}):(Ma=r,r=u);r===u&&(r=Ma,(t=pp())===u&&(t=bp())===u&&(t=vp())===u&&(t=yp()),t!==u&&(Pa=r,t=function(r){return{dataType:r}}(t)),r=t);return r}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"uuid"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(vs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="UUID"):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u&&(Pa=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"bool"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(Su)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BOOL"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"boolean"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(ju)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="BOOLEAN"):(Ma=t,t=u)):(Ma=t,t=u),t}());e!==u&&(Pa=t,e=Na(e));return t=e}())===u&&(t=function(){var r,t,e;r=Ma,(t=hp())!==u&&ub()!==u&&(e=Tc())!==u?(Pa=r,n=t,(o=e).parentheses=!0,r=t={dataType:n,expr:o}):(Ma=r,r=u);var n,o;return r}())===u&&(t=function(){var r,t;r=Ma,(t=lp())===u&&(t=Np());t!==u&&(Pa=r,t=Ua(t));return r=t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=Ma,(e=Zf())===u&&(e=Jf());if(e!==u)if(ub()!==u)if(zp()!==u)if(ub()!==u){if(n=[],Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn)),o!==u)for(;o!==u;)n.push(o),Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn));else n=u;if(n!==u)if((o=ub())!==u){if(s=Ma,(a=Qp())!==u)if((i=ub())!==u){if(c=[],Qn.test(r.charAt(Ma))?(l=r.charAt(Ma),Ma++):(l=u,0===Fa&&qa(Kn)),l!==u)for(;l!==u;)c.push(l),Qn.test(r.charAt(Ma))?(l=r.charAt(Ma),Ma++):(l=u,0===Fa&&qa(Kn));else c=u;c!==u?s=a=[a,i,c]:(Ma=s,s=u)}else Ma=s,s=u;else Ma=s,s=u;s===u&&(s=null),s!==u&&(a=ub())!==u&&(i=Zp())!==u&&(c=ub())!==u?((l=Tb())===u&&(l=null),l!==u?(Pa=t,f=s,p=l,e={dataType:e,length:parseInt(n.join(""),10),scale:f&&parseInt(f[2].join(""),10),parentheses:!0,suffix:p},t=e):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;var f,p;t===u&&(t=Ma,(e=Zf())===u&&(e=Jf()),e!==u&&(Pa=t,e=function(r){return{dataType:r}}(e)),t=e);return t}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"geography"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(Wu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="GEOGRAPHY"):(Ma=t,t=u)):(Ma=t,t=u),t}())!==u&&(Pa=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=Ma,(e=function(){var t,e,n,o;return t=Ma,"oid"===r.substr(Ma,3).toLowerCase()?(e=r.substr(Ma,3),Ma+=3):(e=u,0===Fa&&qa(ys)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="OID"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regclass"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(ds)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGCLASS"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regcollation"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(hs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGCOLLATION"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regconfig"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(ws)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGCONFIG"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regdictionary"===r.substr(Ma,13).toLowerCase()?(e=r.substr(Ma,13),Ma+=13):(e=u,0===Fa&&qa(ms)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGDICTIONARY"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regnamespace"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(Ls)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGNAMESPACE"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regoper"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Cs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGOPER"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regoperator"===r.substr(Ma,11).toLowerCase()?(e=r.substr(Ma,11),Ma+=11):(e=u,0===Fa&&qa(Es)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGOPERATOR"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regproc"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(As)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGPROC"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regprocedure"===r.substr(Ma,12).toLowerCase()?(e=r.substr(Ma,12),Ma+=12):(e=u,0===Fa&&qa(gs)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGPROCEDURE"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regrole"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Ts)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGROLE"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ma,"regtype"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(_s)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="REGTYPE"):(Ma=t,t=u)):(Ma=t,t=u),t}());e!==u&&(Pa=t,e=Na(e));return t=e}()),t}function gb(){var t,e,n,o;if(t=Ma,(e=rp())===u&&(e=tp()),e!==u)if(ub()!==u)if(zp()!==u)if(ub()!==u){if(n=[],Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn)),o!==u)for(;o!==u;)n.push(o),Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn));else n=u;n!==u&&(o=ub())!==u&&Zp()!==u?(Pa=t,t=e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0}):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;return t===u&&(t=Ma,(e=rp())===u&&(e=function(){var t,e,n,o;return t=Ma,"character"===r.substr(Ma,9).toLowerCase()?(e=r.substr(Ma,9),Ma+=9):(e=u,0===Fa&&qa(Or)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="CHARACTER"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(e=tp())===u&&(e=function(){var t,e,n,o;return t=Ma,"string"===r.substr(Ma,6).toLowerCase()?(e=r.substr(Ma,6),Ma+=6):(e=u,0===Fa&&qa(ku)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="STRING"):(Ma=t,t=u)):(Ma=t,t=u),t}()),e!==u&&(Pa=t,e=function(r){return{dataType:r}}(e)),t=e),t}function Tb(){var t,e,n;return t=Ma,(e=up())===u&&(e=null),e!==u&&ub()!==u?((n=function(){var t,e,n,o;return t=Ma,"zerofill"===r.substr(Ma,8).toLowerCase()?(e=r.substr(Ma,8),Ma+=8):(e=u,0===Fa&&qa(Gu)),e!==u?(n=Ma,Fa++,o=pl(),Fa--,o===u?n=void 0:(Ma=n,n=u),n!==u?(Pa=t,t=e="ZEROFILL"):(Ma=t,t=u)):(Ma=t,t=u),t}())===u&&(n=null),n!==u?(Pa=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(Ma=t,t=u)):(Ma=t,t=u),t}function _b(){var t,e,n,o,s,a,i,c,l,f,p,b,v,y;if(t=Ma,(e=ep())===u&&(e=op())===u&&(e=sp())===u&&(e=ap())===u&&(e=ip())===u&&(e=np())===u&&(e=cp())===u&&(e=fp())===u&&(e=dp())===u&&(e=wp())===u&&(e=mp())===u&&(e=Lp())===u&&(e=Cp())===u&&(e=lp())===u&&(e=Ep())===u&&(e=Ap()),e!==u)if((n=ub())!==u)if((o=zp())!==u)if((s=ub())!==u){if(a=[],Qn.test(r.charAt(Ma))?(i=r.charAt(Ma),Ma++):(i=u,0===Fa&&qa(Kn)),i!==u)for(;i!==u;)a.push(i),Qn.test(r.charAt(Ma))?(i=r.charAt(Ma),Ma++):(i=u,0===Fa&&qa(Kn));else a=u;if(a!==u)if((i=ub())!==u){if(c=Ma,(l=Qp())!==u)if((f=ub())!==u){if(p=[],Qn.test(r.charAt(Ma))?(b=r.charAt(Ma),Ma++):(b=u,0===Fa&&qa(Kn)),b!==u)for(;b!==u;)p.push(b),Qn.test(r.charAt(Ma))?(b=r.charAt(Ma),Ma++):(b=u,0===Fa&&qa(Kn));else p=u;p!==u?c=l=[l,f,p]:(Ma=c,c=u)}else Ma=c,c=u;else Ma=c,c=u;c===u&&(c=null),c!==u&&(l=ub())!==u&&(f=Zp())!==u&&(p=ub())!==u?((b=Tb())===u&&(b=null),b!==u?(Pa=t,v=c,y=b,t=e={dataType:e,length:parseInt(a.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:y}):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;else Ma=t,t=u}else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;else Ma=t,t=u;if(t===u){if(t=Ma,(e=ep())===u&&(e=op())===u&&(e=sp())===u&&(e=ap())===u&&(e=ip())===u&&(e=np())===u&&(e=cp())===u&&(e=fp())===u&&(e=dp())===u&&(e=wp())===u&&(e=mp())===u&&(e=Lp())===u&&(e=Cp())===u&&(e=lp())===u&&(e=Ep())===u&&(e=Ap()),e!==u){if(n=[],Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn)),o!==u)for(;o!==u;)n.push(o),Qn.test(r.charAt(Ma))?(o=r.charAt(Ma),Ma++):(o=u,0===Fa&&qa(Kn));else n=u;n!==u&&(o=ub())!==u?((s=Tb())===u&&(s=null),s!==u?(Pa=t,t=e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,s)):(Ma=t,t=u)):(Ma=t,t=u)}else Ma=t,t=u;t===u&&(t=Ma,(e=ep())===u&&(e=op())===u&&(e=sp())===u&&(e=ap())===u&&(e=ip())===u&&(e=np())===u&&(e=cp())===u&&(e=fp())===u&&(e=dp())===u&&(e=wp())===u&&(e=mp())===u&&(e=Lp())===u&&(e=Cp())===u&&(e=lp())===u&&(e=Ep())===u&&(e=Ap()),e!==u&&(n=ub())!==u?((o=Tb())===u&&(o=null),o!==u&&(s=ub())!==u?(Pa=t,t=e=function(r,t){return{dataType:r,suffix:t}}(e,o)):(Ma=t,t=u)):(Ma=t,t=u))}return t}function Sb(){var t,e,n;return t=Ma,"without"===r.substr(Ma,7).toLowerCase()?(e=r.substr(Ma,7),Ma+=7):(e=u,0===Fa&&qa(Oa)),e===u&&("with"===r.substr(Ma,4).toLowerCase()?(e=r.substr(Ma,4),Ma+=4):(e=u,0===Fa&&qa(ht))),e!==u&&ub()!==u&&Sp()!==u&&ub()!==u?("zone"===r.substr(Ma,4).toLowerCase()?(n=r.substr(Ma,4),Ma+=4):(n=u,0===Fa&&qa(ka)),n!==u?(Pa=t,t=e=[e.toUpperCase(),"TIME","ZONE"]):(Ma=t,t=u)):(Ma=t,t=u),t}const jb={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,QUALIFY:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0,PIVOT:!0,UNPIVOT:!0};function xb(){return t.includeLocations?{loc:Ya(Pa,Ma)}:{}}function Ib(r,t){return{type:"unary_expr",operator:r,expr:t}}function Rb(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e,...xb()}}function Nb(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Ob(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function kb(r,t){let e=r;for(let r=0;r<t.length;r++)e=Rb(t[r][1],e,t[r][3]);return e}function Ub(r){const t=Fb[r];return t||(r||null)}function Mb(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Ub(r[1])),t.add(r.join("::"))}return Array.from(t)}function Pb(r){return"string"==typeof r?{type:"same",value:r}:r}let Db=[];const Gb=new Set,$b=new Set,Fb={};if((e=a())!==u&&Ma===r.length)return e;throw e!==u&&Ma<r.length&&qa({type:"end"}),Va($a,Ga<r.length?r.charAt(Ga):null,Ga<r.length?Ya(Ga,Ga+1):Ya(Ga,Ga))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return x})),e.d(n,"columnOrderListToSQL",(function(){return I})),e.d(n,"commonKeywordArgsToSQL",(function(){return j})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return L})),e.d(n,"commentToSQL",(function(){return T})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return u})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return m})),e.d(n,"columnIdentifierToSql",(function(){return y})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return d})),e.d(n,"onPartitionsToSQL",(function(){return E})),e.d(n,"replaceParams",(function(){return C})),e.d(n,"returningToSQL",(function(){return S})),e.d(n,"hasVal",(function(){return w})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return h})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return _}));var u={database:"snowflake",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},s=u;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return s}function b(r){s=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function d(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function h(r){if(r)return r.toUpperCase()}function w(r){return r}function m(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,u=r.suffix,s=r.value,a="object"===o(r)?s:r;switch(e){case"backticks_quote_string":a="`".concat(s,"`");break;case"string":a="'".concat(s,"'");break;case"regex_string":a='r"'.concat(s,'"');break;case"hex_string":a="X'".concat(s,"'");break;case"full_hex_string":a="0x".concat(s);break;case"natural_string":a="N'".concat(s,"'");break;case"bit_string":a="b'".concat(s,"'");break;case"double_quote_string":a='"'.concat(s,'"');break;case"single_quote_string":a="'".concat(s,"'");break;case"boolean":case"bool":a=s?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(s),t=null;break;case"origin":a=s.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(s,"'");break;case"var_string":a="N'".concat(s,"'");break;case"unicode_string":a="U&'".concat(s,"'")}var i=[];return t&&i.push(h(t)),i.push(a),u&&("string"==typeof u&&i.push(u),"object"===o(u)&&(u.collate?i.push(it(u.collate)):i.push(m(u)))),a=i.join(" "),n?"(".concat(a,")"):a}}function L(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():m(n)].filter(w)}function C(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var u=t[n];if("object"!==o(u)||"param"!==u.type)return r(u,e);if(void 0===e[u.value])throw new Error("no value for parameter :".concat(u.value," found"));return t[n]=c(e[u.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function E(r){var t=r.type,e=r.partitions;return[h(t),"(".concat(e.map((function(r){if("range"!==r.type)return m(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(m(t)," ").concat(h(n)," ").concat(m(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,s="";return null!=e&&(s=o?"".concat(e,", ").concat(o):e),n&&(s="(".concat(s,")")),u&&u.length&&(s+=" ".concat(u.join(" "))),"".concat(t).concat(s)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=h(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(w).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function T(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(m(o)),t.join(" ")}}function _(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[h(t)];if(e){var o=e.keyword,u=e.columns;n.push(h(o),u.map(yt).join(", "))}return n.join(" ")})).join(" OR ")}function S(r){return r?["RETURNING",r.columns.map(Et).filter(w).join(", ")].join(" "):""}function j(r){return r?[h(r.keyword),h(r.args)]:[]}function x(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,s=h(e);return u&&(s+="(".concat(m(n),", ").concat(m(o),")")),s}}function I(r){if(r)return r.map(mt).filter(w).join(", ")}function R(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function O(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),h(e)]}function k(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),u=[];switch(u.push(o),o){case"KEY_BLOCK_SIZE":n&&u.push(n),u.push(m(e));break;case"BTREE":case"HASH":u.length=0,u.push.apply(u,R(O(r)));break;case"WITH PARSER":u.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":u.shift(),u.push(T(r));break;case"DATA_COMPRESSION":u.push(n,h(e.value),E(e.on));break;default:u.push(n,m(e))}return u.filter(w).join(" ")}}function U(r){return r?r.map(k):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,u=r.definition,s=r.on,a=r.with,i=[];if(i.push.apply(i,R(O(e))),u&&u.length){var c="CHECK"===h(t)?"(".concat(ut(u[0]),")"):"(".concat(u.map((function(r){return ut(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),s&&i.push("ON [".concat(s,"]")),i}function P(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[Array.isArray(r.left)?r.left.map(ut).join(", "):ut(r.left),t,e,h(o.type),ut(o.value)].filter(w).join(" ");return[r.parentheses?"(".concat(u,")"):u].join(" ")}function D(r){return function(r){if(Array.isArray(r))return G(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function $(r){return r?[r.prefix.map(m).join(" "),ut(r.value),r.suffix.map(m).join(" ")]:[]}function F(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(D($(n)),D($(e))).filter(w).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",ut(e[0])):i("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(h(t)," ")))}(r):"";var t,e,n}function B(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(yt).join(", "),")"):"";return"".concat("default"===t.type?d(t.value):m(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function H(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=h(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?d(e):ut(e))}return n.filter(w).join(" ")}}function W(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,u=r.for,s=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,y=r.into,d=void 0===y?{}:y,L=r.isolation,C=r.limit,E=r.options,A=r.orderby,g=r.parentheses_symbol,T=r.qualify,_=r.top,S=r.window,j=r.with,x=r.where,I=[B(j),"SELECT",h(t)];Array.isArray(E)&&I.push(E.join(" ")),I.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[h(t)];return e&&n.push("(".concat(e.map(ut).join(", "),")")),n.filter(w).join(" ")}}(o),v(_),gt(e,s));var R=d.position,N="";R&&(N=a("INTO",H,d)),"column"===R&&I.push(N),I.push(a("FROM",cr,s)),"from"===R&&I.push(N);var O=l||{},k=O.keyword,U=O.expr;I.push(a(k,ut,U)),I.push(a("WHERE",ut,x)),p&&(I.push(i("GROUP BY",st(p.columns).join(", "))),I.push(st(p.modifiers).join(", "))),I.push(a("HAVING",ut,b)),I.push(a("QUALIFY",ut,T)),I.push(a("WINDOW",ut,S)),I.push(at(A,"order by")),I.push(it(n)),I.push(F(C)),L&&I.push(a(L.keyword,m,L.expr)),I.push(h(f)),"end"===R&&I.push(N),I.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[h(r.type),h(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(u));var M=I.filter(w).join(" ");return g?"(".concat(M,")"):M}function Y(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return q(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?q(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function q(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=Y(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u={},s=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(u[a]=o[a]);var i=[yt(u)],c="";s&&(c=ut(s),i.push("=",c)),e.push(i.filter(w).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return W(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(d).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(w).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(yt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[h(e)];switch(n){case"origin":o.push(m(t));break;case"update":o.push("UPDATE",a("SET",V,t.set),a("WHERE",ut,t.where))}return o.filter(w).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(w).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,s=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},y=v.keyword,d=v.set,L=[h(e),h(o),cr(t),Q(f)];return Array.isArray(u)&&L.push("(".concat(u.map(m).join(", "),")")),L.push(a(Array.isArray(i)?"VALUES":"",X,i)),L.push(a("ON CONFLICT",Z,s)),L.push(a("SET",V,b)),L.push(a("WHERE",ut,c)),L.push(a(y,V,d)),L.push(S(p)),L.filter(w).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",ut(t),h(e),ut(n)].filter(w).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(h(t),"(").concat(n&&ut(n)||"",")"),a("AS","string"==typeof e?d:ut,e),a(h(o&&o.keyword),d,o&&o.as)].filter(w).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,s=[ut(n),"FOR",yt(e),P(o)],a=["".concat(h(u),"(").concat(s.join(" "),")")];return t&&a.push("AS",d(t)),a.join(" ")}(r);default:return""}}function ur(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,s=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(h(t),"(".concat(d(n)),"(".concat(o.map(ut).filter(w).join(", "),"))"));break;case"spatial_window_max_cells":a.push(h(t),"=",ut(e));break;case"index":a.push(h(s),h(t),u?"(".concat(e.map(d).join(", "),")"):"= ".concat(d(e)));break;default:a.push(ut(e))}return a.filter(w).join(" ")}}function sr(r,t){var e=r.name,n=r.symbol;return[h(e),n,t].filter(w).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",ut(r.of));break;case"from_to":t.push("FROM",ut(r.from),"TO",ut(r.to));break;case"between_and":t.push("BETWEEN",ut(r.between),"AND",ut(r.and));break;case"contained":t.push("CONTAINED","IN",ut(r.in))}return t.filter(w).join(" ")}function ir(r){if("UNNEST"===h(r.type))return nr(r);var t,e,n,o,u=r.table,s=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,y=r.tablesample,C=r.temporal_table,E=r.table_hint,A=d(b),g=d(s),T=d(p),_=u&&d(u);if(c)switch(c.type){case"values":var S=c.parentheses,j=c.values,x=c.prefix,I=[S&&"(","",S&&")"],R=X(j);x&&(R=R.split("(").slice(1).map((function(r){return"".concat(h(x),"(").concat(r)})).join("")),I[1]="VALUES ".concat(R),_=I.filter(w).join("");break;case"tumble":_=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,u=[d(t.expr.db),d(t.expr.schema),d(t.expr.table)].filter(w).join("."),s="DESCRIPTOR(".concat(yt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(sr(t,u)),sr(e,s)],i=sr(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(sr(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(w).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return L(r).join(" ")})).join(", "),_="".concat(h(e),"(").concat(h(n),"(").concat(o,"))");break;default:_=ut(c)}var N=[[A,g,T,_=[h(f),_,h(v)].filter(w).join(" ")].filter(w).join(".")];if(y){var O=["TABLESAMPLE",ut(y.expr),m(y.repeatable)].filter(w).join(" ");N.push(O)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[h(t),ar(e)].filter(w).join(" ")}}(C),a("AS","string"==typeof i?d:ut,i),or(l)),E&&N.push(h(E.keyword),"(".concat(E.expr.map(ur).filter(w).join(", "),")"));var k=N.filter(w).join(" ");return r.parentheses?"(".concat(k,")"):k}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var u=[],s=[],i=!0===e?1:e.length,c=0;c++<i;)u.push("("),s.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return u.join("")+o+s.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],y=v.on,d=v.using,L=v.join,C=[];C.push(L?" ".concat(h(L)):","),C.push(ir(v)),C.push(a("ON",ut,y)),d&&C.push("USING (".concat(d.map(m).join(", "),")")),p.push(C.filter(w).join(" "))}return p.filter(w).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=m(n);switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.filter(w).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[d(t.db),d(t.table)].filter(w).join(".");return"".concat(h(e)," ").concat(n);case"column":return"COLUMN ".concat(yt(t));default:return"".concat(h(e)," ").concat(m(t))}}function pr(r){var t=r.keyword,e=r.expr;return[h(t),m(e)].filter(w).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(w).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),s=ut(e);return[u,h(n),s].filter(w).join(" ")}function yr(r){var t,e,n,o,u=r.keyword,s=r.suffix,i="";switch(h(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",m,e&&e.right),a("FROM",cr,n),F(o)].filter(w).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===h(t.op)?a("LIKE",m,t.right):a("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(w).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[s]);break;case"VAR":i=ot(r.var),u=""}return["SHOW",h(u),h(s),i].filter(w).join(" ")}var dr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,s=t.expr,a=t.orderby;return[h(u),h(n),[[d(o.schema),d(o.name)].filter(w).join("."),"(".concat(s.map(Zr).join(", ")).concat(a?[" ORDER","BY",a.map(Zr).join(", ")].join(" "):"",")")].filter(w).join(""),zr(e)].filter(w).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,u=r.expr,s=void 0===u?[]:u,a=h(t),i=cr(e),c=s.map(ut);return[a,"TABLE",h(n),m(o),i,c.join(", ")].filter(w).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[h(r.type),h(e),d(n),zr(t)].filter(w).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[h(r.type),h(e),[d(n.schema),d(n.name)].filter(w).join("."),zr(t)].filter(w).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[h(r.type),h(n),[[d(o.schema),d(o.name)].filter(w).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(w).join(""),zr(e)].filter(w).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,s=r.with,a=h(t),i=ir(u),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(yt).join(", "),")"));n&&c.push("WITH ".concat(n.map(h).join(", ")));c.push("AS",W(o)),s&&c.push(h(s));return c.filter(w).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[h(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[h(t),h(e),ut(n),h(o),d(u)].filter(w).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,s=r.options,a=[h(t),h(e),h(n)],i=[d(o.schema),o.name].filter(w).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(s.map(Qr).join(", "),")")),a.filter(w).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,s=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,y=r.unlogged,d=r.with,L=[h(t),h(f),h(s),h(y),h(e),h(a),cr(n)];if(o){var C=o.type,E=cr(o.table);return L.push(h(C),E),L.filter(w).join(" ")}if(b)return L.concat([qr(b)]).filter(w).join(" ");i&&L.push("(".concat(i.map(Wr).join(", "),")"));if(c){var A=p().database,g=A&&"sqlite"===A.toLowerCase()?", ":" ";L.push(c.map(lr).join(g))}if(d){var T=d.map((function(r){return[m(r.keyword),h(r.symbol),m(r.value)].join(" ")})).join(", ");L.push("WITH (".concat(T,")"))}L.push(h(l),h(u)),v&&L.push(hr(v));return L.filter(w).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,s=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[h(f),h(l),h(e),h(c),d(t),h(i)],y=_(o);v.push(y,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Fr(j(n)).concat(Fr(j(s)))),b&&v.push(h(b.type),ut(b.cond));return v.push(h(u.keyword),$r(u.expr)),v.filter(w).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,s=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,y=[h(u),h(c),ut(t),h(n),h(i),ir(l),h(b),f.map((function(r){var t=[h(r.keyword)],e=r.args;return e&&t.push(h(e.keyword),e.columns.map(yt).join(", ")),t.join(" ")})),"ON",ir(s),h(e&&e.keyword),h(e&&e.args),p&&"".concat(h(p.keyword)," ").concat(d(p.trigger)),a("WHEN",ut,v),h(o.prefix)];switch(o.type){case"set":y.push(a("SET",V,o.expr));break;case"multiple":y.push(wr(o.expr.ast))}return y.push(h(o.suffix)),y.filter(w).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,s=r.type,i=r.with,c=r.version;return[h(s),h(o),h(n),m(t),h(i),a("SCHEMA",m,u),a("VERSION",m,c),a("FROM",m,e)].filter(w).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,s=r.returns,a=r.options,i=r.last,c=[h(t),h(e),h(n)],l=[m(o.schema),o.name.map(m).join(".")].filter(w).join("."),f=u.map(Zr).filter(w).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),Array.isArray(n)?"(".concat(n.map(Lt).join(", "),")"):Vr(n)].filter(w).join(" ")}(s),a.map(Xr).join(" "),i),c.filter(w).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,u=r.include,s=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,y=r.on_kw,L=r.table,C=r.tablespace,E=r.type,A=r.where,g=r.with,T=r.with_before_where,_=g&&"WITH (".concat(U(g).join(", "),")"),S=u&&"".concat(h(u.keyword)," (").concat(u.columns.map((function(r){return"string"==typeof r?d(r):ut(r)})).join(", "),")"),j=l;l&&(j="string"==typeof l?d(l):[d(l.schema),d(l.name)].filter(w).join("."));var x=[h(E),h(i),h(n),h(o),h(t),j,h(y),ir(L)].concat(Fr(O(c)),["(".concat(I(s),")"),S,U(p).join(" "),zr(b),zr(v),a("TABLESPACE",m,C)]);T?x.push(_,a("WHERE",ut,A)):x.push(a("WHERE",ut,A),_);return x.push(a("ON",ut,f),a("FILESTREAM_ON",m,e)),x.filter(w).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,s=r.create_definitions,a=[h(t),h(o),h(e),h(u),cr(n)];s&&a.push(s.map(Wr).join(" "));return a.filter(w).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,u=r.create_definitions,s=r[e],a=s.db,i=s.schema,c=[m(a),i.map(m).join(".")].filter(w).join("."),l=[h(t),h(n),h(e),h(o),c];u&&l.push(u.map(lr).join(" "));return l.filter(w).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,s=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,m=p.db,C=p.schema,E=p.view,A=[d(m),d(C),d(E)].filter(w).join(".");return[h(f),h(a),h(l),h(s),t&&"ALGORITHM = ".concat(h(t)),ut(n),c&&"SQL SECURITY ".concat(h(c)),h(u),h(o),A,e&&"(".concat(e.map(y).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return L(r).join(" ")})).join(", "),")")].join(" "),"AS",hr(i),h(b)].filter(w).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,s=r.create_definitions,a=[h(n),h(o),[d(e.schema),d(e.name)].filter(w).join("."),h(t),A(u)];if(s&&s.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Br(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}(s);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(ut(f));break;case"default":c.push(h(p),ut(f.value));break;case"constraint":c.push(Or(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(w).join(" "))}return a.filter(w).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,s=[h(r.type),h(n),[d(o.schema),d(o.name)].filter(w).join("."),h(t),h(u)];if(e){var a=[];switch(u){case"enum":case"range":a.push(ut(e));break;default:a.push("(".concat(e.map(Wr).join(", "),")"))}s.push(a.filter(w).join(" "))}return s.filter(w).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,s=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[xr(t)];return e&&n.push(h(e.keyword),e.auth_plugin,m(e.value)),n.filter(w).join(" ")})).join(", "),b=[h(f),h(u),h(o),p];n&&b.push(h(n.keyword),n.value.map(xr).join(", "));b.push(a(c&&c.keyword,ut,c&&c.value)),l&&b.push(h(l.keyword),l.value.map((function(r){return ut(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,ut,r.value))}));return b.push(m(s),T(e),m(t)),b.filter(w).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[h(r.type),h(e),fr(n),pr(t)].filter(w).join(" ")},select:W,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),ut(n)].filter(w).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,s=r.with,i=r.limit,c=r.returning,l=[B(s),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",ut,o)),l.push(at(u,"order by")),l.push(F(i)),l.push(S(c)),l.filter(w).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[h(t),ir(e),(n||[]).map(br).filter(w).join(", ")].filter(w).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[h(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(w).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[h(t),W(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,h(t),n,"IN",wr([o]),"LOOP",wr(u),"END LOOP",e].filter(w).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,s=r.with,i=r.limit,c=r.returning;return[B(s),"UPDATE",cr(e),a("SET",V,n),a("FROM",cr,t),a("WHERE",ut,o),at(u,"order by"),F(i),S(c)].filter(w).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,s=r.go,a=r.semicolons,i=r.suffix,c=[h(r.type),ut(t),m(u),"".concat(Er(o.ast||o)).concat(a[0]),h(s)];n&&c.push(n.map((function(r){return[h(r.type),ut(r.boolean_expr),"THEN",Er(r.then.ast||r.then),r.semicolon].filter(w).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Er(e.ast||e)).concat(a[1]));return c.push(m(i)),c.filter(w).join(" ")},insert:J,drop:Sr,truncate:Sr,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[h(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,s=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(w).join(""),h(n),h(o)];switch(c){case"variable":l.push(dt(u),ut(r.collate),h(s)),i&&l.push(h(i.keyword),ut(i.value));break;case"cursor":l.push(h(a));break;case"table":l.push(h(a),"(".concat(i.map(Wr).join(", "),")"))}return l.filter(w).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=h(t),o=d(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,s=Ar(e);try{for(s.s();!(u=s.n()).done;){var a=u.value.map(ir);n.push(a.join(" TO "))}}catch(r){s.e(r)}finally{s.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=h(t);return"".concat(n," ").concat(d(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=h(t),u=e.map(ut).join(", ");return[o,h(n),u].filter(w).join(" ")},lock:jr,unlock:jr,show:yr,grant:Ir,revoke:Ir,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[h(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[h(t),h(e)];n&&u.push([m(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(w).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(h(o.type),h(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(w).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,u=[m(e),h(n)];return o&&u.push(o.map(m).join(", ")),u.filter(w).join(" ")}};function hr(r){if(!r)return"";for(var t=dr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,s=[n&&"(",t(r)];r._next;){var a=dr[r._next.type],i=h(r.set_op);s.push(i,a(r._next)),r=r._next}return s.push(n&&")",at(o,"order by"),F(u)),s.filter(w).join(" ")}function wr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=hr(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var mr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Lr(r){var t=r&&r.ast?r.ast:r;if(!mr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Cr(r){return Array.isArray(r)?(r.forEach(Lr),wr(r)):(Lr(r),hr(r))}function Er(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Cr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Cr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Tr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function gr(r){return function(r){if(Array.isArray(r))return _r(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Tr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tr(r,t){if(r){if("string"==typeof r)return _r(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_r(r,t):void 0}}function _r(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Sr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=r.suffix,s=[h(t),h(e),h(o)];switch(e){case"table":s.push(cr(n));break;case"trigger":s.push([n[0].schema?"".concat(d(n[0].schema),"."):"",d(n[0].trigger)].filter(w).join(""));break;case"database":case"schema":case"procedure":s.push(d(n));break;case"view":s.push(cr(n),r.options&&r.options.map(ut).filter(w).join(" "));break;case"index":s.push.apply(s,[yt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(ut).filter(w).join(" ")]));break;case"type":s.push(n.map(yt).join(", "),r.options&&r.options.map(ut).filter(w).join(" "))}return u&&s.push(u.map(ut).filter(w).join(" ")),s.filter(w).join(" ")}function jr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),h(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,s=[],a=Ar(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return h(e[r])})).filter(w).join(" "))}s.push(n.join(" "))};for(a.s();!(u=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[s.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(w).join(" ")}function xr(r){var t=r.name,e=r.host,n=[m(t)];return e&&n.push("@",m(e)),n.join("")}function Ir(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,s=r.to_from,a=r.user_or_roles,i=r.with,c=[h(t),m(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(yt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(m(u.object_type),u.priv_level.map((function(r){return[d(r.prefix),d(r.name)].filter(w).join(".")})).join(", "));break;case"proxy":c.push(xr(u))}return c.push(h(s),a.map(xr).join(", ")),c.push(m(i)),c.filter(w).join(" ")}function Rr(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Or(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,s=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(h(u)),l.push(d(t));var b=h(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&d(o)),l.push.apply(l,Rr(M(r))),l.push.apply(l,Rr(ht(s))),l.push(h(n)),l.push(a("FOR",d,i)),l.push(m(c)),l.filter(w).join(" ")}}function kr(r){if(r){var t=r.type;return"rows"===t?[h(t),ut(r.expr)].filter(w).join(" "):ut(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),kr(o)].filter(w).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Pr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,s=h(o);if("WINDOW"===s)return"OVER ".concat(Ur(t));if("ON UPDATE"===s){var a="".concat(h(o)," ").concat(h(n)),i=ut(e)||[];return u&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Dr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return h(t);for(var e=r.array,n=e.dimension,o=e.length,u=[],s=0;s<n;s++)u.push("["),o&&o[s]&&u.push(m(o[s])),u.push("]");return u.join("")}function Gr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,u=r.as,s=r.offset,a=r.parentheses,i=bt({expr:e,offset:s}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,y=p.dataType,L=p.parentheses,C=p.quoted,E=p.scale,A=p.suffix,g=p.expr,T=g?ut(g):"";null!=v&&(T=E?"".concat(v,", ").concat(E):v),L&&(T="(".concat(T,")")),b&&(T="<".concat(T,">")),A&&A.length&&(T+=" ".concat(A.map(m).join(" ")));var _="::",S="",j=[];"as"===o&&(0===l&&(i="".concat(h(n),"(").concat(i)),S=")",_=" ".concat(o.toUpperCase()," ")),0===l&&j.push(i);var x=Dr(p);j.push(_,C,y,C,x,T,S),c.push(j.filter(w).join(""))}u&&c.push(" AS ".concat(d(u)));var I=c.filter(w).join("");return a?"(".concat(I,")"):I}function $r(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,u=r.parentheses,s=r.within_group,a=r.over,i=r.suffix,c=Pr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[h(t),h(e),"(".concat(at(n,"order by"),")")].filter(w).join(" ")}(s),f=ut(i),p=[m(n.schema),n.name.map(m).join(".")].filter(w).join(".");if(!t)return[p,l,c].filter(w).join(" ");var b=r.separator||", ";"TRIM"===h(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var y=ut(t);if(Array.isArray(b)){for(var d=y[0],L=1,C=y.length;L<C;++L)d=[d,y[L]].join(" ".concat(ut(b[L-1])," "));v.push(d)}else v.push(y.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(w).join(" "),[u?"(".concat(v,")"):v,l,c].filter(w).join(" ")}function Fr(r){return function(r){if(Array.isArray(r))return Hr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Br(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Br(r,t){if(r){if("string"==typeof r)return Hr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Hr(r,t):void 0}}function Hr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Wr(r){if(!r)return[];var t,e,n,o,u=r.resource;switch(u){case"column":return Lt(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(h(n)),e.push(o),e.push.apply(e,R(M(t))),e.filter(w).join(" ");case"constraint":return Or(r);case"sequence":return[h(r.prefix),ut(r.value)].filter(w).join(" ");default:throw new Error("unknown resource = ".concat(u," type"))}}function Yr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(m(r.from),")"),"TO","(".concat(m(r.to),")"));break;case"in":t.push("IN","(".concat(ut(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(m(r.modulus),", REMAINDER ").concat(m(r.remainder),")"))}return t.filter(w).join(" ")}function qr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,u=[h(t),ir(e),h(n.keyword),Yr(n.expr)];return o&&u.push("TABLESPACE",m(o)),u.filter(w).join(" ")}function Vr(r){return r.dataType?A(r):[d(r.db),d(r.schema),d(r.table)].filter(w).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[h(t),r.symbol,hr(r.declare),h(r.begin),wr(r.expr),h(r.end),r.symbol].filter(w).join(" ");case"set":return[h(t),r.parameter,h(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(w).join(" ");case"return":return[h(t),ut(r.expr)].filter(w).join(" ");default:return ut(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[h(t),e];switch(h(t)){case"SFUNC":o.push([d(n.schema),n.name].filter(w).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(ut(n))}return o.filter(w).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",m(t),"VALUES",h(e.type),"(".concat(m(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,u=r.if_exists,s=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Lt(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=d(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=d(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,h(r[i])].filter(w).join(" ");break;case"constraint":f=d(r[i]),p=[Wr(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=d(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[h(t),h(o),h(n),h(u),s&&yt(s),h(a),f&&f.trim(),p.filter(w).join(" ")];return l&&b.push(h(l.keyword),l.expr&&yt(l.expr)),b.filter(w).join(" ")}function Zr(r){var t=r.default&&[h(r.default.keyword),ut(r.default.value)].join(" ");return[h(r.mode),r.name,A(r.type),t].filter(w).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(h(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(w).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[h(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(w).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr);u=Array.isArray(u)?u.join(", "):u;var s=r.name,a=Pr(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.separator&&t.separator.delimiter&&(u=[u,m(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(u=[u,ut(t.separator.expr)].join(" ")),t.orderby&&(u=[u,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(u=[u,h(t.separator.keyword),m(t.separator.value)].filter(w).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(s,"(").concat(u,")"),i,a,c].filter(w).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,s="".concat(h(e),"(").concat(ut(o));return u&&(s="".concat(s," HAVING ").concat(h(u.prefix)," ").concat(ut(u.expr))),[s="".concat(s,")"),Pr(n)].filter(w).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=r.separator,s=void 0===u?", ":u;return[e,"(",t?ut(t).join(s):"",")",o&&" ",o].filter(w).join("")}(r),Pr(t)].filter(w).join(" ")},array:tt,assign:vr,binary_expr:P,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,s=e.length;u<s;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Gr,collate:it,column_ref:yt,column_definition:Lt,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,s=["".concat(h(e),"(").concat(h(n)),"FROM",h(o),ut(u)];return"".concat(s.filter(w).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[h(t),e,ut(n)].filter(w).join(" ")}(t[r])})).filter(w).join(", ");return"".concat(h(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode,s=[h(o),"(".concat(n.map((function(r){return yt(r)})).join(", "),")")].join(" "),a=[h(t),["(",ut(r.expr),u&&" ".concat(m(u)),")"].filter(w).join("")].join(" ");return[s,a,Ct(e)].filter(w).join(" ")},function:$r,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,u=n.map(ut).join(", ");return[o?"(".concat(u,")"):u,"->",ut(e)].join(" ")},insert:hr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[h(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,u=[ut(e),"VALUE",ut(n)];return o&&u.push("ON","NULL",ut(o)),u.filter(w).join(" ")},json_visitor:function(r){return[r.symbol,ut(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,ut(t.expr)].filter(w).join(" ")},show:yr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[m(e.schema),e.name.map(m).join(".")].filter(w).join(".");return["".concat(o,"(").concat(ut(n).join(", "),")"),"AS",$r(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.quoted,s=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return s&&(c+=s),a.push(c),[u,a.join(" "),u].filter(w).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}var s=t.type;return"expr"===s?ut(t.expr):nt[s]?nt[s](t):m(t)}}function st(r){return r?(Array.isArray(r)||(r=[r]),r.map(ut)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=h(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",h(r.nulls)].filter(w).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,u=e.value,s=[h(t)];return u||s.push(o),s.push(Array.isArray(n)?n.map(m).join("."):m(n)),u&&s.push(o),s.push(ut(u)),s.filter(w).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return d(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),m(r.value),"".concat(r.name?")":""),"]"].filter(w).join("")})).join("");return[ut(e),u,o].filter(w).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.brackets?"[".concat(m(o.index),"]"):"".concat(o.notation).concat(m(o.index));o.property&&(u="".concat(u,".").concat(m(o.property))),e.push(u)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function yt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,u=r.db,s=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,y=r.order_by,m=r.subFields,L=void 0===m?[]:m,C="*"===n?"*":bt(n,s),E=[u,f,p].filter(w).map((function(r){return"".concat("string"==typeof r?d(r):ut(r))})),A=E[0];if(A){for(var g=1;g<E.length;++g)A="".concat(A).concat(c[g]||".").concat(E[g]);C="".concat(A).concat(c[g]||".").concat(C)}var T=[C=["".concat(C).concat(vt(t))].concat(lt(L)).join("."),it(o),ut(l),a("AS",ut,e)];T.push("string"==typeof v?h(v):ut(v)),T.push(h(y));var _=T.filter(w).join(" ");return b?"(".concat(_,")"):_}function dt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,u=r.expr,s=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(u&&(s+=ut(u)),r.array){var a=Dr(r);s+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return s}}function ht(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,s=r.on_action;return t.push(h(n)),t.push(cr(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(h(o)),s.map((function(r){return t.push(h(r.type),ut(r.value))})),t.filter(w)}function wt(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,s=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,y=r.unique,d=r.primary_key,C=r.column_format,E=r.reference_definition,A=[h(e&&e.action),h(e&&e.value)].filter(w).join(" ");if(b||t.push(A),f){var g=f.type,_=f.value;t.push(g.toUpperCase(),ut(_))}var S=p().database;return s&&t.push(h(s.keyword),m(s.constraint)),t.push(Or(o)),t.push(function(r){if(r)return[h(r.value),"(".concat(ut(r.expr),")"),h(r.storage_type)].filter(w).join(" ")}(b)),b&&t.push(A),t.push(x(v),h(d),h(y),T(u)),t.push.apply(t,lt(L(n))),"sqlite"!==S.toLowerCase()&&t.push(ut(i)),t.push.apply(t,lt(L(C))),t.push.apply(t,lt(L(c))),t.push.apply(t,lt(ht(E))),t.push(a("USING",ut,l)),t.filter(w).join(" ")}function mt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by,s="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return s.collate=null,[ut(s),ut(e),o,h(u),h(n)].filter(w).join(" ")}function Lt(r){var t=[],e=yt(r.column),n=dt(r.definition);return t.push(e),t.push(n),t.push(wt(r)),t.filter(w).join(" ")}function Ct(r){return r?"object"===ct(r)?["AS",ut(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?d(r):y(r)].join(" "):""}function Et(r,t){var e=r.expr,n=r.type;if("cast"===n)return Gr(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var s=[o],a=u.map((function(r){return Et(r,t)})).join(", ");return s.push([h(n),n&&"(",a,n&&")"].filter(w).join("")),s.filter(w).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,Ct(r.as)].filter(w).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return Et(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=st(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",u=t.join(o);return e?"(".concat(u,")"):u},nt.select=function(r){var t="object"===et(r._next)?hr(r):W(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[m(r.key),m(r.value)].join(", ")})).join(", ");return[h(t),"[".concat(e,"]")].join("")};var Tt=e(0);function _t(r){return(_t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var St,jt,xt,It=(St={},jt="snowflake",xt=Tt.parse,(jt=function(r){var t=function(r,t){if("object"!=_t(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=_t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==_t(t)?t:t+""}(jt))in St?Object.defineProperty(St,jt,{value:xt,enumerable:!0,configurable:!0,writable:!0}):St[jt]=xt,St);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Ot(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ot(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function Ot(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&kt(r.prototype,t),e&&kt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),Er(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),ut(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return Et(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=t.database,n=void 0===e?"snowflake":e;b(t);var o=n.toLowerCase();if(It[o])return It[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||u.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var s,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=Nt(i);try{for(f.s();!(s=f.n()).done;){var p,b=s.value,v=!1,y=Nt(t);try{for(y.s();!(p=y.n()).done;){var d=p.value,h=new RegExp("^".concat(d,"$"),"i");if(h.test(b)){v=!0;break}}}catch(r){y.e(r)}finally{y.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Pt(r){return(Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Pt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Pt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Pt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=snowflake.js.map