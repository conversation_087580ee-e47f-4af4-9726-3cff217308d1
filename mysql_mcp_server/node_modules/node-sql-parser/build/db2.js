!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?a(r.parts[t][0])+"-"+a(r.parts[t][1]):a(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function a(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,a=new Array(r.length);for(t=0;t<r.length;t++)a[t]=(o=r[t],e[o.type](o));if(a.sort(),a.length>0){for(t=1,n=1;t<a.length;t++)a[t-1]!==a[t]&&(a[n]=a[t],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,a={},u={start:Qo},s=Qo,i=function(r,t){return Mi(r,t,1)},c=qo("IF",!0),f=function(r,t){return Mi(r,t)},l=qo("AUTO_INCREMENT",!0),p=qo("UNIQUE",!0),v=qo("KEY",!0),b=qo("PRIMARY",!0),d=qo("COLUMN_FORMAT",!0),y=qo("FIXED",!0),h=qo("DYNAMIC",!0),m=qo("DEFAULT",!0),w=qo("STORAGE",!0),L=qo("DISK",!0),C=qo("MEMORY",!0),A=qo("ALGORITHM",!0),E=qo("INSTANT",!0),g=qo("INPLACE",!0),j=qo("COPY",!0),S=qo("LOCK",!0),T=qo("NONE",!0),_=qo("SHARED",!0),x=qo("EXCLUSIVE",!0),k=qo("CHECK",!0),I=qo("NOCHECK",!0),N=qo("PRIMARY KEY",!0),R=qo("NOT",!0),O=qo("FOR",!0),U=qo("REPLICATION",!0),M=qo("FOREIGN KEY",!0),D=qo("MATCH FULL",!0),P=qo("MATCH PARTIAL",!0),F=qo("MATCH SIMPLE",!0),H=qo("RESTRICT",!0),$=qo("CASCADE",!0),q=qo("SET NULL",!0),B=qo("NO ACTION",!0),W=qo("SET DEFAULT",!0),Y=qo("CHARACTER",!0),G=qo("SET",!0),V=qo("CHARSET",!0),Q=qo("COLLATE",!0),X=qo("AVG_ROW_LENGTH",!0),K=qo("KEY_BLOCK_SIZE",!0),z=qo("MAX_ROWS",!0),J=qo("MIN_ROWS",!0),Z=qo("STATS_SAMPLE_PAGES",!0),rr=qo("CONNECTION",!0),tr=qo("COMPRESSION",!0),er=qo("'",!1),nr=qo("ZLIB",!0),or=qo("LZ4",!0),ar=qo("ENGINE",!0),ur=qo("READ",!0),sr=qo("LOCAL",!0),ir=qo("LOW_PRIORITY",!0),cr=qo("WRITE",!0),fr=function(r,t){return Mi(r,t)},lr=qo("(",!1),pr=qo(")",!1),vr=qo("BTREE",!0),br=qo("HASH",!0),dr=qo("WITH",!0),yr=qo("PARSER",!0),hr=qo("VISIBLE",!0),mr=qo("INVISIBLE",!0),wr=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Bi[t]=t,e&&(Bi[e]=t),function(r){const t=Fi(r);r.clear(),t.forEach(t=>r.add(t))}(qi)}),t},Lr=qo("FIRST",!0),Cr=qo("ROWS",!0),Ar=qo("ROW",!0),Er=qo("ONLY",!0),gr=qo("NEXT",!0),jr=qo("CS",!0),Sr=qo("UR",!0),Tr=qo("RS",!0),_r=qo("RR",!0),xr=qo("=",!1),kr=qo("DUPLICATE",!0),Ir=function(r,t){return Di(r,t)},Nr=qo("!",!1),Rr=function(r){return r[0]+" "+r[2]},Or=qo(">=",!1),Ur=qo(">",!1),Mr=qo("<=",!1),Dr=qo("<>",!1),Pr=qo("<",!1),Fr=qo("!=",!1),Hr=qo("+",!1),$r=qo("-",!1),qr=qo("*",!1),Br=qo("/",!1),Wr=qo("%",!1),Yr=qo("~",!1),Gr=qo("?|",!1),Vr=qo("?&",!1),Qr=qo("?",!1),Xr=qo("#-",!1),Kr=qo("#>>",!1),zr=qo("#>",!1),Jr=qo("@>",!1),Zr=qo("<@",!1),rt=function(r){return!0===Ii[r.toUpperCase()]},tt=qo('"',!1),et=/^[^"]/,nt=Bo(['"'],!0,!1),ot=/^[^']/,at=Bo(["'"],!0,!1),ut=qo("`",!1),st=/^[^`]/,it=Bo(["`"],!0,!1),ct=function(r,t){return r+t.join("")},ft=/^[A-Za-z_\u4E00-\u9FA5]/,lt=Bo([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),pt=/^[A-Za-z0-9_$\u4E00-\u9FA5\xC0-\u017F]/,vt=Bo([["A","Z"],["a","z"],["0","9"],"_","$",["一","龥"],["À","ſ"]],!1,!1),bt=/^[A-Za-z0-9_:]/,dt=Bo([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),yt=qo(":",!1),ht=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},mt=/^[^"\\\0-\x1F\x7F]/,wt=Bo(['"',"\\",["\0",""],""],!0,!1),Lt=/^[^'\\]/,Ct=Bo(["'","\\"],!0,!1),At=qo("\\'",!1),Et=qo('\\"',!1),gt=qo("\\\\",!1),jt=qo("\\/",!1),St=qo("\\b",!1),Tt=qo("\\f",!1),_t=qo("\\n",!1),xt=qo("\\r",!1),kt=qo("\\t",!1),It=qo("\\u",!1),Nt=qo("\\",!1),Rt=qo("''",!1),Ot=qo('""',!1),Ut=qo("``",!1),Mt=/^[\n\r]/,Dt=Bo(["\n","\r"],!1,!1),Pt=qo(".",!1),Ft=/^[0-9]/,Ht=Bo([["0","9"]],!1,!1),$t=/^[0-9a-fA-F]/,qt=Bo([["0","9"],["a","f"],["A","F"]],!1,!1),Bt=/^[eE]/,Wt=Bo(["e","E"],!1,!1),Yt=/^[+\-]/,Gt=Bo(["+","-"],!1,!1),Vt=qo("NULL",!0),Qt=qo("NOT NULL",!0),Xt=qo("TRUE",!0),Kt=qo("TO",!0),zt=qo("FALSE",!0),Jt=(qo("SHOW",!0),qo("DROP",!0)),Zt=qo("USE",!0),re=qo("ALTER",!0),te=qo("SELECT",!0),ee=qo("UPDATE",!0),ne=qo("CREATE",!0),oe=qo("TEMPORARY",!0),ae=qo("DELETE",!0),ue=qo("INSERT",!0),se=qo("RECURSIVE",!0),ie=qo("REPLACE",!0),ce=qo("RENAME",!0),fe=qo("IGNORE",!0),le=(qo("EXPLAIN",!0),qo("PARTITION",!0)),pe=qo("INTO",!0),ve=qo("FROM",!0),be=qo("UNLOCK",!0),de=qo("AS",!0),ye=qo("TABLE",!0),he=qo("TABLES",!0),me=qo("DATABASE",!0),we=qo("SCHEMA",!0),Le=qo("ON",!0),Ce=qo("LEFT",!0),Ae=qo("RIGHT",!0),Ee=qo("FULL",!0),ge=qo("INNER",!0),je=qo("JOIN",!0),Se=qo("OUTER",!0),Te=qo("OVER",!0),_e=qo("UNION",!0),xe=qo("MINUS",!0),ke=qo("INTERSECT",!0),Ie=qo("EXCEPT",!0),Ne=qo("VALUES",!0),Re=qo("USING",!0),Oe=qo("WHERE",!0),Ue=qo("GROUP",!0),Me=qo("BY",!0),De=qo("ORDER",!0),Pe=qo("HAVING",!0),Fe=qo("FETCH",!0),He=qo("OFFSET",!0),$e=qo("ASC",!0),qe=qo("DESC",!0),Be=qo("ALL",!0),We=qo("DISTINCT",!0),Ye=qo("BETWEEN",!0),Ge=qo("IN",!0),Ve=qo("IS",!0),Qe=qo("LIKE",!0),Xe=qo("EXISTS",!0),Ke=qo("AND",!0),ze=qo("OR",!0),Je=qo("COUNT",!0),Ze=qo("MAX",!0),rn=qo("MIN",!0),tn=qo("SUM",!0),en=qo("AVG",!0),nn=qo("CALL",!0),on=qo("CASE",!0),an=qo("WHEN",!0),un=qo("THEN",!0),sn=qo("ELSE",!0),cn=qo("END",!0),fn=qo("CAST",!0),ln=qo("CHAR",!0),pn=qo("VARCHAR",!0),vn=qo("NUMERIC",!0),bn=qo("DECIMAL",!0),dn=qo("SIGNED",!0),yn=qo("UNSIGNED",!0),hn=qo("INT",!0),mn=qo("ZEROFILL",!0),wn=qo("INTEGER",!0),Ln=qo("JSON",!0),Cn=qo("SMALLINT",!0),An=qo("TINYINT",!0),En=qo("TINYTEXT",!0),gn=qo("TEXT",!0),jn=qo("MEDIUMTEXT",!0),Sn=qo("LONGTEXT",!0),Tn=qo("BIGINT",!0),_n=qo("FLOAT",!0),xn=qo("DOUBLE",!0),kn=qo("DATE",!0),In=qo("DATETIME",!0),Nn=qo("TIME",!0),Rn=qo("TIMESTAMP",!0),On=qo("TRUNCATE",!0),Un=qo("USER",!0),Mn=qo("CURRENT_DATE",!0),Dn=(qo("ADDDATE",!0),qo("INTERVAL",!0)),Pn=qo("YEAR",!0),Fn=qo("MONTH",!0),Hn=qo("DAY",!0),$n=qo("HOUR",!0),qn=qo("MINUTE",!0),Bn=qo("SECOND",!0),Wn=qo("CURRENT_TIME",!0),Yn=qo("CURRENT_TIMESTAMP",!0),Gn=qo("CURRENT_USER",!0),Vn=qo("SESSION_USER",!0),Qn=qo("SYSTEM_USER",!0),Xn=qo("GLOBAL",!0),Kn=qo("SESSION",!0),zn=qo("PERSIST",!0),Jn=qo("PERSIST_ONLY",!0),Zn=qo("@",!1),ro=qo("@@",!1),to=qo("$",!1),eo=qo("return",!0),no=qo(":=",!1),oo=qo("DUAL",!0),ao=qo("ADD",!0),uo=qo("COLUMN",!0),so=qo("INDEX",!0),io=qo("FULLTEXT",!0),co=qo("SPATIAL",!0),fo=qo("COMMENT",!0),lo=qo("CONSTRAINT",!0),po=qo("REFERENCES",!0),vo=qo("SQL_CALC_FOUND_ROWS",!0),bo=qo("SQL_CACHE",!0),yo=qo("SQL_NO_CACHE",!0),ho=qo("SQL_SMALL_RESULT",!0),mo=qo("SQL_BIG_RESULT",!0),wo=qo("SQL_BUFFER_RESULT",!0),Lo=qo(",",!1),Co=qo("[",!1),Ao=qo("]",!1),Eo=qo(";",!1),go=qo("->",!1),jo=qo("->>",!1),So=qo("||",!1),To=qo("&&",!1),_o=qo("/*",!1),xo=qo("*/",!1),ko=qo("--",!1),Io=qo("#",!1),No={type:"any"},Ro=/^[ \t\n\r]/,Oo=Bo([" ","\t","\n","\r"],!1,!1),Uo=function(r){return{dataType:r}},Mo=0,Do=0,Po=[{line:1,column:1}],Fo=0,Ho=[],$o=0;if("startRule"in t){if(!(t.startRule in u))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=u[t.startRule]}function qo(r,t){return{type:"literal",text:r,ignoreCase:t}}function Bo(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Wo(t){var e,n=Po[t];if(n)return n;for(e=t-1;!Po[e];)e--;for(n={line:(n=Po[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Po[t]=n,n}function Yo(r,t){var e=Wo(r),n=Wo(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Go(r){Mo<Fo||(Mo>Fo&&(Fo=Mo,Ho=[]),Ho.push(r))}function Vo(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Qo(){var r,t;return r=Mo,li()!==a&&(t=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Ko())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=si())!==a&&(s=li())!==a&&(i=Ko())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=si())!==a&&(s=li())!==a&&(i=Ko())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from($i),columnList:Fi(qi),ast:n}}(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=r,r=t):(Mo=r,r=a),r}function Xo(){var t;return(t=function(){var r,t,e,n,o,u;r=Mo,(t=Ku())!==a&&li()!==a&&(e=is())!==a&&li()!==a&&(n=ka())!==a?(Do=r,s=t,c=e,(f=n)&&f.forEach(r=>$i.add(`${s}::${r.db}::${r.table}`)),t={tableList:Array.from($i),columnList:Fi(qi),ast:{type:s.toLowerCase(),keyword:c.toLowerCase(),name:f}},r=t):(Mo=r,r=a);var s,c,f;r===a&&(r=Mo,(t=Ku())!==a&&li()!==a&&(e=Js())!==a&&li()!==a&&(n=du())!==a&&li()!==a&&fs()!==a&&li()!==a&&(o=Oa())!==a&&li()!==a?((u=function(){var r,t,e,n,o,u;r=Mo,(t=aa())===a&&(t=ua());if(t!==a){for(e=[],n=Mo,(o=li())!==a?((u=aa())===a&&(u=ua()),u!==a?n=o=[o,u]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=aa())===a&&(u=ua()),u!==a?n=o=[o,u]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,t=i(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())===a&&(u=null),u!==a&&li()!==a?(Do=r,t=function(r,t,e,n,o){return{tableList:Array.from($i),columnList:Fi(qi),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,u),r=t):(Mo=r,r=a)):(Mo=r,r=a));return r}())===a&&(t=function(){var t;(t=function(){var r,t,e,n,o,u,s,i,c,l;r=Mo,(t=Ju())!==a&&li()!==a?((e=Zu())===a&&(e=null),e!==a&&li()!==a&&is()!==a&&li()!==a?((n=Zo())===a&&(n=null),n!==a&&li()!==a&&(o=ka())!==a&&li()!==a&&(u=function(){var r,t,e,n,o,u,s,i,c;if(r=Mo,(t=ai())!==a)if(li()!==a)if((e=ra())!==a){for(n=[],o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=ra())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=ra())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);n!==a&&(o=li())!==a&&(u=ui())!==a?(Do=r,t=f(e,n),r=t):(Mo=r,r=a)}else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;return r}())!==a&&li()!==a?((s=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=ba())!==a){for(e=[],n=Mo,(o=li())!==a?((u=ni())===a&&(u=null),u!==a&&(s=li())!==a&&(i=ba())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=ni())===a&&(u=null),u!==a&&(s=li())!==a&&(i=ba())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,t=Mi(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())===a&&(s=null),s!==a&&li()!==a?((i=ns())===a&&(i=ts()),i===a&&(i=null),i!==a&&li()!==a?((c=ss())===a&&(c=null),c!==a&&li()!==a?((l=Jo())===a&&(l=null),l!==a?(Do=r,p=t,v=e,b=n,y=u,h=s,m=i,w=c,L=l,(d=o)&&d.forEach(r=>$i.add(`create::${r.db}::${r.table}`)),t={tableList:Array.from($i),columnList:Fi(qi),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:v&&v[0].toLowerCase(),if_not_exists:b,table:d,ignore_replace:m&&m[0].toLowerCase(),as:w&&w[0].toLowerCase(),query_expr:L&&L.ast,create_definitions:y,table_options:h}},r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a);var p,v,b,d,y,h,m,w,L;r===a&&(r=Mo,(t=Ju())!==a&&li()!==a?((e=Zu())===a&&(e=null),e!==a&&li()!==a&&is()!==a&&li()!==a?((n=Zo())===a&&(n=null),n!==a&&li()!==a&&(o=ka())!==a&&li()!==a&&(u=function r(){var t,e;(t=function(){var r,t;r=Mo,Es()!==a&&li()!==a&&(t=ka())!==a?(Do=r,r={type:"like",table:t}):(Mo=r,r=a);return r}())===a&&(t=Mo,ai()!==a&&li()!==a&&(e=r())!==a&&li()!==a&&ui()!==a?(Do=t,(n=e).parentheses=!0,t=n):(Mo=t,t=a));var n;return t}())!==a?(Do=r,t=function(r,t,e,n,o){return n&&n.forEach(r=>$i.add(`create::${r.db}::${r.table}`)),{tableList:Array.from($i),columnList:Fi(qi),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,u),r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a));return r}())===a&&(t=function(){var t,e,n,o,u,s;t=Mo,(e=Ju())!==a&&li()!==a?((n=function(){var t,e,n,o;t=Mo,"database"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(me));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DATABASE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"schema"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(we));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SCHEMA"):(Mo=t,t=a)):(Mo=t,t=a);return t}()),n!==a&&li()!==a?((o=Zo())===a&&(o=null),o!==a&&li()!==a&&(u=gi())!==a&&li()!==a?((s=function(){var r,t,e,n,o,u;if(r=Mo,(t=va())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=va())!==a?n=o=[o,u]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=va())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a?(Do=r,t=i(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())===a&&(s=null),s!==a?(Do=t,e=function(r,t,e,n,o){const a=t.toLowerCase();return{tableList:Array.from($i),columnList:Fi(qi),ast:{type:r[0].toLowerCase(),keyword:a,if_not_exists:e,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,u,s),t=e):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);return t}());return t}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=function(){var t,e,n,o;t=Mo,"truncate"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(On));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TRUNCATE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a?((n=is())===a&&(n=null),n!==a&&li()!==a&&(o=ka())!==a?(Do=t,u=e,s=n,(i=o)&&i.forEach(r=>$i.add(`${u}::${r.db}::${r.table}`)),e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:u.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:i}},t=e):(Mo=t,t=a)):(Mo=t,t=a);var u,s,i;return t}())===a&&(t=function(){var r,t,e;r=Mo,(t=es())!==a&&li()!==a&&is()!==a&&li()!==a&&(e=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Sa())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Sa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Sa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=f(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=r,(n=e).forEach(r=>r.forEach(r=>r.table&&$i.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"rename",table:n}},r=t):(Mo=r,r=a);var n;return r}())===a&&(t=function(){var t,e,n;t=Mo,(e=function(){var t,e,n,o;t=Mo,"call"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(nn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CALL"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&(n=ji())!==a?(Do=t,o=n,e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"call",expr:o}},t=e):(Mo=t,t=a);var o;return t}())===a&&(t=function(){var t,e,n;t=Mo,(e=function(){var t,e,n,o;t=Mo,"use"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(Zt));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&(n=hu())!==a?(Do=t,o=n,$i.add(`use::${o}::null`),e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"use",db:o}},t=e):(Mo=t,t=a);var o;return t}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=function(){var t,e,n,o;t=Mo,"alter"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(re));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&is()!==a&&li()!==a&&(n=ka())!==a&&li()!==a&&(o=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=oa())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=oa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=oa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=f(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=t,s=o,(u=n)&&u.length>0&&u.forEach(r=>$i.add(`alter::${r.db}::${r.table}`)),e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"alter",table:u,expr:s}},t=e):(Mo=t,t=a);var u,s;return t}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=us())!==a&&li()!==a?((n=function(){var t,e,n,o;t=Mo,"global"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Xn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="GLOBAL"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"session"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(Kn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SESSION"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"local"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(sr));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="LOCAL"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"persist"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(zn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="PERSIST"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"persist_only"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(Jn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="PERSIST_ONLY"):(Mo=t,t=a)):(Mo=t,t=a);return t}()),n===a&&(n=null),n!==a&&li()!==a&&(o=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=wi())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=wi())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=wi())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=fr(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=t,u=n,(s=o).keyword=u,e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"set",keyword:u,expr:s}},t=e):(Mo=t,t=a)):(Mo=t,t=a);var u,s;return t}())===a&&(t=function(){var t,e,n;t=Mo,(e=function(){var t,e,n,o;t=Mo,"lock"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(S));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&cs()!==a&&li()!==a&&(n=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=da())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=da())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=da())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=fr(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=t,o=n,e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(Mo=t,t=a);var o;return t}())===a&&(t=function(){var t,e;t=Mo,(e=function(){var t,e,n,o;t=Mo,"unlock"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(be));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&cs()!==a?(Do=t,e={tableList:Array.from($i),columnList:Fi(qi),ast:{type:"unlock",keyword:"tables"}},t=e):(Mo=t,t=a);return t}()),t}function Ko(){var r;return(r=Jo())===a&&(r=function(){var r,t,e,n,o;r=Mo,(t=zu())!==a&&li()!==a&&(e=ka())!==a&&li()!==a&&us()!==a&&li()!==a&&(n=Ba())!==a&&li()!==a?((o=Da())===a&&(o=null),o!==a?(Do=r,t=function(r,t,e){const n={};return r&&r.forEach(r=>{const{server:t,db:e,schema:o,as:a,table:u,join:s}=r,i=s?"select":"update",c=[t,e,o].filter(Boolean).join(".")||null;e&&(n[u]=c),u&&$i.add(`${i}::${c}::${u}`)}),t&&t.forEach(r=>{if(r.table){const t=Pi(r.table);$i.add(`update::${n[t]||null}::${t}`)}qi.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from($i),columnList:Fi(qi),ast:{type:"update",table:r,set:t,where:e}}}(e,n,o),r=t):(Mo=r,r=a)):(Mo=r,r=a);return r}())===a&&(r=function(){var r,t,e,n,o,u,s,i;r=Mo,(t=Qa())!==a&&li()!==a?((e=as())===a&&(e=null),e!==a&&li()!==a&&(n=Oa())!==a&&li()!==a?((o=Ga())===a&&(o=null),o!==a&&li()!==a&&ai()!==a&&li()!==a&&(u=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=gu())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=gu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=gu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=f(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a&&li()!==a&&ui()!==a&&li()!==a&&(s=Ya())!==a&&li()!==a?((i=Va())===a&&(i=null),i!==a?(Do=r,t=function(r,t,e,n,o,a){if(t&&($i.add(`insert::${t.db}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>qi.add(`insert::${r}::${t}`))}return{tableList:Array.from($i),columnList:Fi(qi),ast:{type:r,table:[t],columns:n,values:o,partition:e,on_duplicate_update:a}}}(t,n,o,u,s,i),r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a);return r}())===a&&(r=function(){var r,t,e,n,o,u,s,i;r=Mo,(t=Qa())!==a&&li()!==a?((e=ns())===a&&(e=null),e!==a&&li()!==a?((n=as())===a&&(n=null),n!==a&&li()!==a&&(o=Oa())!==a&&li()!==a?((u=Ga())===a&&(u=null),u!==a&&li()!==a&&(s=Ya())!==a&&li()!==a?((i=Va())===a&&(i=null),i!==a?(Do=r,t=function(r,t,e,n,o,a,u){n&&($i.add(`insert::${n.db}::${n.table}`),qi.add(`insert::${n.table}::(.*)`),n.as=null);const s=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from($i),columnList:Fi(qi),ast:{type:r,table:[n],columns:null,values:a,partition:o,prefix:s,on_duplicate_update:u}}}(t,e,n,o,u,s,i),r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a);return r}())===a&&(r=function(){var r,t,e,n,o,u;r=Mo,(t=Qa())!==a&&li()!==a&&as()!==a&&li()!==a&&(e=Oa())!==a&&li()!==a?((n=Ga())===a&&(n=null),n!==a&&li()!==a&&us()!==a&&li()!==a&&(o=Ba())!==a&&li()!==a?((u=Va())===a&&(u=null),u!==a?(Do=r,s=t,c=n,f=o,l=u,(i=e)&&($i.add(`insert::${i.db}::${i.table}`),qi.add(`insert::${i.table}::(.*)`),i.as=null),t={tableList:Array.from($i),columnList:Fi(qi),ast:{type:s,table:[i],columns:null,partition:c,set:f,on_duplicate_update:l}},r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a);var s,i,c,f,l;return r}())===a&&(r=function(){var r,t,e,n,o;r=Mo,(t=rs())!==a&&li()!==a?((e=ka())===a&&(e=null),e!==a&&li()!==a&&(n=ja())!==a&&li()!==a?((o=Da())===a&&(o=null),o!==a?(Do=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,a=o?"select":"delete";n&&$i.add(`${a}::${t}::${n}`),o||qi.add(`delete::${n}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from($i),columnList:Fi(qi),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a);return r}())===a&&(r=Xo())===a&&(r=function(){var r,t;r=[],t=mi();for(;t!==a;)r.push(t),t=mi();return r}()),r}function zo(){var t,e,n,o,u;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"union"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(_e));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="UNION"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(e=function(){var t,e,n,o;t=Mo,"intersect"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(ke));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="INTERSECT"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(e=function(){var t,e,n,o;t=Mo,"except"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Ie));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="EXCEPT"):(Mo=t,t=a)):(Mo=t,t=a);return t}()),e!==a&&li()!==a?((n=ms())===a&&(n=ws()),n===a&&(n=null),n!==a?(Do=t,o=e,t=e=(u=n)?`${o.toLowerCase()} ${u.toLowerCase()}`:""+o.toLowerCase()):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=function(){var t,e,n,o;t=Mo,"minus"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(xe));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MINUS"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e="minus"),t=e),t}function Jo(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=ya())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=zo())!==a&&(s=li())!==a&&(i=ya())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=zo())!==a&&(s=li())!==a&&(i=ya())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a&&(n=li())!==a?((o=Fa())===a&&(o=null),o!==a&&(u=li())!==a?((s=qa())===a&&(s=null),s!==a?(Do=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from($i),columnList:Fi(qi),ast:r}}(t,e,o,s)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Zo(){var t,e;return t=Mo,"if"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(c)),e!==a&&li()!==a&&js()!==a&&li()!==a&&gs()!==a?(Do=t,t=e="IF NOT EXISTS"):(Mo=t,t=a),t}function ra(){var r;return(r=ca())===a&&(r=ea())===a&&(r=sa())===a&&(r=ia()),r}function ta(){var t,e,n,o;return t=Mo,(e=function(){var t,e;t=Mo,(e=function(){var t,e,n,o;t=Mo,"not null"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(Qt));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={type:"not null",value:"not null"});return t=e}())===a&&(e=Mu()),e!==a&&(Do=t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===a&&(t=Mo,(e=function(){var r,t;r=Mo,Qu()!==a&&li()!==a&&(t=eu())!==a?(Do=r,r={type:"default",value:t}):(Mo=r,r=a);return r}())!==a&&(Do=t,e={default_val:e}),(t=e)===a&&(t=Mo,"auto_increment"===r.substr(Mo,14).toLowerCase()?(e=r.substr(Mo,14),Mo+=14):(e=a,0===$o&&Go(l)),e!==a&&(Do=t,e={auto_increment:e.toLowerCase()}),(t=e)===a&&(t=Mo,"unique"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(p)),e!==a&&li()!==a?("key"===r.substr(Mo,3).toLowerCase()?(n=r.substr(Mo,3),Mo+=3):(n=a,0===$o&&Go(v)),n===a&&(n=null),n!==a?(Do=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,"primary"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(b)),e===a&&(e=null),e!==a&&li()!==a?("key"===r.substr(Mo,3).toLowerCase()?(n=r.substr(Mo,3),Mo+=3):(n=a,0===$o&&Go(v)),n!==a?(Do=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=bi())!==a&&(Do=t,e={comment:e}),(t=e)===a&&(t=Mo,(e=na())!==a&&(Do=t,e={collate:e}),(t=e)===a&&(t=Mo,(e=function(){var t,e,n;t=Mo,"column_format"===r.substr(Mo,13).toLowerCase()?(e=r.substr(Mo,13),Mo+=13):(e=a,0===$o&&Go(d));e!==a&&li()!==a?("fixed"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(y)),n===a&&("dynamic"===r.substr(Mo,7).toLowerCase()?(n=r.substr(Mo,7),Mo+=7):(n=a,0===$o&&Go(h)),n===a&&("default"===r.substr(Mo,7).toLowerCase()?(n=r.substr(Mo,7),Mo+=7):(n=a,0===$o&&Go(m)))),n!==a?(Do=t,e={type:"column_format",value:n.toLowerCase()},t=e):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={column_format:e}),(t=e)===a&&(t=Mo,(e=function(){var t,e,n;t=Mo,"storage"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(w));e!==a&&li()!==a?("disk"===r.substr(Mo,4).toLowerCase()?(n=r.substr(Mo,4),Mo+=4):(n=a,0===$o&&Go(L)),n===a&&("memory"===r.substr(Mo,6).toLowerCase()?(n=r.substr(Mo,6),Mo+=6):(n=a,0===$o&&Go(C))),n!==a?(Do=t,e={type:"storage",value:n.toLowerCase()},t=e):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={storage:e}),(t=e)===a&&(t=Mo,(e=la())!==a&&(Do=t,e={reference_definition:e}),t=e))))))))),t}function ea(){var r,t,e,n,o,u,s;return r=Mo,(t=du())!==a&&li()!==a&&(e=xi())!==a&&li()!==a?((n=function(){var r,t,e,n,o,u;if(r=Mo,(t=ta())!==a)if(li()!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ta())!==a?n=o=[o,u]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ta())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;else Mo=r,r=a;return r}())===a&&(n=null),n!==a?(Do=r,o=t,u=e,s=n,qi.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:u,resource:"column",...s||{}}):(Mo=r,r=a)):(Mo=r,r=a),r}function na(){var t,e,n;return t=Mo,function(){var t,e,n,o;t=Mo,"collate"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(Q));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="COLLATE"):(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a?((e=Xs())===a&&(e=null),e!==a&&li()!==a&&(n=hu())!==a?(Do=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Mo=t,t=a)):(Mo=t,t=a),t}function oa(){var t;return(t=function(){var r,t;r=Mo,Ks()!==a&&li()!==a&&(t=ca())!==a?(Do=r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(Mo=r,r=a);return r}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=Ku())!==a&&li()!==a?("check"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(k)),n!==a&&li()!==a&&(o=Su())!==a?(Do=t,e={action:"drop",constraint:o,keyword:n.toLowerCase(),resource:"constraint",type:"alter"},t=e):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,ds()!==a&&li()!==a?("check"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(k)),e!==a&&li()!==a?("check"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(k)),n!==a&&li()!==a&&ti()!==a&&li()!==a&&(o=Su())!==a?(Do=t,t={action:"with",constraint:o,keyword:"check check",resource:"constraint",type:"alter"}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n;t=Mo,"nocheck"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(I));e!==a&&li()!==a&&ti()!==a&&li()!==a&&(n=Su())!==a?(Do=t,t=e={action:"nocheck",constraint:n,resource:"constraint",type:"alter"}):(Mo=t,t=a);return t}())===a&&(t=function(){var r,t,e,n;r=Mo,(t=Ks())!==a&&li()!==a?((e=zs())===a&&(e=null),e!==a&&li()!==a&&(n=ea())!==a?(Do=r,o=e,u=n,t={action:"add",...u,keyword:o,resource:"column",type:"alter"},r=t):(Mo=r,r=a)):(Mo=r,r=a);var o,u;return r}())===a&&(t=function(){var r,t,e;r=Mo,Ku()!==a&&li()!==a?((t=zs())===a&&(t=null),t!==a&&li()!==a&&(e=du())!==a?(Do=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(Mo=r,r=a)):(Mo=r,r=a);return r}())===a&&(t=function(){var r,t,e;r=Mo,(t=Ks())!==a&&li()!==a&&(e=sa())!==a?(Do=r,n=e,t={action:"add",type:"alter",...n},r=t):(Mo=r,r=a);var n;return r}())===a&&(t=function(){var r,t,e;r=Mo,(t=Ks())!==a&&li()!==a&&(e=ia())!==a?(Do=r,n=e,t={action:"add",type:"alter",...n},r=t):(Mo=r,r=a);var n;return r}())===a&&(t=function(){var r,t,e,n;r=Mo,(t=es())!==a&&li()!==a?((e=Xu())===a&&(e=ss()),e===a&&(e=null),e!==a&&li()!==a&&(n=hu())!==a?(Do=r,u=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},r=t):(Mo=r,r=a)):(Mo=r,r=a);var o,u;return r}())===a&&(t=aa())===a&&(t=ua()),t}function aa(){var t,e,n,o;return t=Mo,"algorithm"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(A)),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a?("default"===r.substr(Mo,7).toLowerCase()?(o=r.substr(Mo,7),Mo+=7):(o=a,0===$o&&Go(m)),o===a&&("instant"===r.substr(Mo,7).toLowerCase()?(o=r.substr(Mo,7),Mo+=7):(o=a,0===$o&&Go(E)),o===a&&("inplace"===r.substr(Mo,7).toLowerCase()?(o=r.substr(Mo,7),Mo+=7):(o=a,0===$o&&Go(g)),o===a&&("copy"===r.substr(Mo,4).toLowerCase()?(o=r.substr(Mo,4),Mo+=4):(o=a,0===$o&&Go(j))))),o!==a?(Do=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t}function ua(){var t,e,n,o;return t=Mo,"lock"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(S)),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a?("default"===r.substr(Mo,7).toLowerCase()?(o=r.substr(Mo,7),Mo+=7):(o=a,0===$o&&Go(m)),o===a&&("none"===r.substr(Mo,4).toLowerCase()?(o=r.substr(Mo,4),Mo+=4):(o=a,0===$o&&Go(T)),o===a&&("shared"===r.substr(Mo,6).toLowerCase()?(o=r.substr(Mo,6),Mo+=6):(o=a,0===$o&&Go(_)),o===a&&("exclusive"===r.substr(Mo,9).toLowerCase()?(o=r.substr(Mo,9),Mo+=9):(o=a,0===$o&&Go(x))))),o!==a?(Do=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t}function sa(){var r,t,e,n,o,u,s,i;return r=Mo,(t=Js())===a&&(t=Zs()),t!==a&&li()!==a?((e=gu())===a&&(e=null),e!==a&&li()!==a?((n=Ta())===a&&(n=null),n!==a&&li()!==a&&(o=wa())!==a&&li()!==a?((u=_a())===a&&(u=null),u!==a&&li()!==a?(Do=r,s=n,i=u,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i}):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a),r}function ia(){var t,e,n,o,u,s,i,c,f;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"fulltext"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(io));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="FULLTEXT"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(e=function(){var t,e,n,o;t=Mo,"spatial"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(co));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SPATIAL"):(Mo=t,t=a)):(Mo=t,t=a);return t}()),e!==a&&li()!==a?((n=Js())===a&&(n=Zs()),n===a&&(n=null),n!==a&&li()!==a?((o=gu())===a&&(o=null),o!==a&&li()!==a&&(u=wa())!==a&&li()!==a?((s=_a())===a&&(s=null),s!==a&&li()!==a?(Do=t,i=e,f=s,t=e={index:o,definition:u,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:f,resource:"index"}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t}function ca(){var t;return(t=function(){var t,e,n,o,u,s;t=Mo,(e=fa())===a&&(e=null);e!==a&&li()!==a?("primary key"===r.substr(Mo,11).toLowerCase()?(n=r.substr(Mo,11),Mo+=11):(n=a,0===$o&&Go(N)),n!==a&&li()!==a?((o=Ta())===a&&(o=null),o!==a&&li()!==a&&(u=wa())!==a&&li()!==a?((s=_a())===a&&(s=null),s!==a?(Do=t,c=n,f=o,l=u,p=s,e={constraint:(i=e)&&i.constraint,definition:l,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:f,resource:"constraint",index_options:p},t=e):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);var i,c,f,l,p;return t}())===a&&(t=function(){var t,e,n,o,u,s,i,c;t=Mo,(e=fa())===a&&(e=null);e!==a&&li()!==a&&(n=function(){var t,e,n,o;t=Mo,"unique"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(p));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="UNIQUE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a?((o=Js())===a&&(o=Zs()),o===a&&(o=null),o!==a&&li()!==a?((u=gu())===a&&(u=null),u!==a&&li()!==a?((s=Ta())===a&&(s=null),s!==a&&li()!==a&&(i=wa())!==a&&li()!==a?((c=_a())===a&&(c=null),c!==a?(Do=t,l=n,v=o,b=u,d=s,y=i,h=c,e={constraint:(f=e)&&f.constraint,definition:y,constraint_type:v&&`${l.toLowerCase()} ${v.toLowerCase()}`||l.toLowerCase(),keyword:f&&f.keyword,index_type:d,index:b,resource:"constraint",index_options:h},t=e):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);var f,l,v,b,d,y,h;return t}())===a&&(t=function(){var t,e,n,o,u,s;t=Mo,(e=fa())===a&&(e=null);e!==a&&li()!==a?("foreign key"===r.substr(Mo,11).toLowerCase()?(n=r.substr(Mo,11),Mo+=11):(n=a,0===$o&&Go(M)),n!==a&&li()!==a?((o=gu())===a&&(o=null),o!==a&&li()!==a&&(u=wa())!==a&&li()!==a?((s=la())===a&&(s=null),s!==a?(Do=t,c=n,f=o,l=u,p=s,e={constraint:(i=e)&&i.constraint,definition:l,constraint_type:c,keyword:i&&i.keyword,index:f,resource:"constraint",reference_definition:p},t=e):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);var i,c,f,l,p;return t}())===a&&(t=function(){var t,e,n,o,u,s,i,c,f,l;t=Mo,(e=fa())===a&&(e=null);e!==a&&li()!==a?("check"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(k)),n!==a&&li()!==a?(o=Mo,"not"===r.substr(Mo,3).toLowerCase()?(u=r.substr(Mo,3),Mo+=3):(u=a,0===$o&&Go(R)),u!==a&&(s=li())!==a?("for"===r.substr(Mo,3).toLowerCase()?(i=r.substr(Mo,3),Mo+=3):(i=a,0===$o&&Go(O)),i!==a&&(c=li())!==a?("replication"===r.substr(Mo,11).toLowerCase()?(f=r.substr(Mo,11),Mo+=11):(f=a,0===$o&&Go(U)),f!==a&&(l=li())!==a?o=u=[u,s,i,c,f,l]:(Mo=o,o=a)):(Mo=o,o=a)):(Mo=o,o=a),o===a&&(o=null),o!==a&&(u=ai())!==a&&(s=li())!==a&&(i=eu())!==a&&(c=li())!==a&&(f=ui())!==a?(Do=t,p=e,v=o,b=i,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:v&&{keyword:"not for replication"},definition:[b],resource:"constraint"},t=e):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a);var p,v,b;return t}()),t}function fa(){var r,t,e,n;return r=Mo,(t=ti())!==a&&li()!==a?((e=hu())===a&&(e=null),e!==a?(Do=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(Mo=r,r=a)):(Mo=r,r=a),r}function la(){var t,e,n,o,u,s,i,c,f,l;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"references"===r.substr(Mo,10).toLowerCase()?(e=r.substr(Mo,10),Mo+=10):(e=a,0===$o&&Go(po));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="REFERENCES"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&(n=ka())!==a&&li()!==a&&(o=wa())!==a&&li()!==a?("match full"===r.substr(Mo,10).toLowerCase()?(u=r.substr(Mo,10),Mo+=10):(u=a,0===$o&&Go(D)),u===a&&("match partial"===r.substr(Mo,13).toLowerCase()?(u=r.substr(Mo,13),Mo+=13):(u=a,0===$o&&Go(P)),u===a&&("match simple"===r.substr(Mo,12).toLowerCase()?(u=r.substr(Mo,12),Mo+=12):(u=a,0===$o&&Go(F)))),u===a&&(u=null),u!==a&&li()!==a?((s=pa())===a&&(s=null),s!==a&&li()!==a?((i=pa())===a&&(i=null),i!==a?(Do=t,c=u,f=s,l=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[f,l].filter(r=>r)}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=pa())!==a&&(Do=t,e={on_action:[e]}),t=e),t}function pa(){var t,e,n,o;return t=Mo,fs()!==a&&li()!==a?((e=rs())===a&&(e=zu()),e!==a&&li()!==a&&(n=function(){var t,e,n;t=Mo,(e=Vs())!==a&&li()!==a&&ai()!==a&&li()!==a?((n=Ka())===a&&(n=null),n!==a&&li()!==a&&ui()!==a?(Do=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Mo=t,t=a)):(Mo=t,t=a);t===a&&(t=Mo,"restrict"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(H)),e===a&&("cascade"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go($)),e===a&&("set null"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(q)),e===a&&("no action"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(B)),e===a&&("set default"===r.substr(Mo,11).toLowerCase()?(e=r.substr(Mo,11),Mo+=11):(e=a,0===$o&&Go(W)),e===a&&(e=Vs()))))),e!==a&&(Do=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==a?(Do=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Mo=t,t=a)):(Mo=t,t=a),t}function va(){var t,e,n,o,u,s,i,c,f;return t=Mo,(e=Qu())===a&&(e=null),e!==a&&li()!==a?((n=function(){var t,e,n;return t=Mo,"character"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(Y)),e!==a&&li()!==a?("set"===r.substr(Mo,3).toLowerCase()?(n=r.substr(Mo,3),Mo+=3):(n=a,0===$o&&Go(G)),n!==a?(Do=t,t=e="CHARACTER SET"):(Mo=t,t=a)):(Mo=t,t=a),t}())===a&&("charset"===r.substr(Mo,7).toLowerCase()?(n=r.substr(Mo,7),Mo+=7):(n=a,0===$o&&Go(V)),n===a&&("collate"===r.substr(Mo,7).toLowerCase()?(n=r.substr(Mo,7),Mo+=7):(n=a,0===$o&&Go(Q)))),n!==a&&li()!==a?((o=Xs())===a&&(o=null),o!==a&&li()!==a&&(u=yu())!==a?(Do=t,i=n,c=o,f=u,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:f}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t}function ba(){var t,e,n,o,u,s,i,c,f;return t=Mo,"auto_increment"===r.substr(Mo,14).toLowerCase()?(e=r.substr(Mo,14),Mo+=14):(e=a,0===$o&&Go(l)),e===a&&("avg_row_length"===r.substr(Mo,14).toLowerCase()?(e=r.substr(Mo,14),Mo+=14):(e=a,0===$o&&Go(X)),e===a&&("key_block_size"===r.substr(Mo,14).toLowerCase()?(e=r.substr(Mo,14),Mo+=14):(e=a,0===$o&&Go(K)),e===a&&("max_rows"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(z)),e===a&&("min_rows"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(J)),e===a&&("stats_sample_pages"===r.substr(Mo,18).toLowerCase()?(e=r.substr(Mo,18),Mo+=18):(e=a,0===$o&&Go(Z))))))),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a&&(o=$u())!==a?(Do=t,c=n,f=o,t=e={keyword:e.toLowerCase(),symbol:c,value:f.value}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=va())===a&&(t=Mo,(e=ri())===a&&("connection"===r.substr(Mo,10).toLowerCase()?(e=r.substr(Mo,10),Mo+=10):(e=a,0===$o&&Go(rr))),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a&&(o=Du())!==a?(Do=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,"compression"===r.substr(Mo,11).toLowerCase()?(e=r.substr(Mo,11),Mo+=11):(e=a,0===$o&&Go(tr)),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a?(o=Mo,39===r.charCodeAt(Mo)?(u="'",Mo++):(u=a,0===$o&&Go(er)),u!==a?("zlib"===r.substr(Mo,4).toLowerCase()?(s=r.substr(Mo,4),Mo+=4):(s=a,0===$o&&Go(nr)),s===a&&("lz4"===r.substr(Mo,3).toLowerCase()?(s=r.substr(Mo,3),Mo+=3):(s=a,0===$o&&Go(or)),s===a&&("none"===r.substr(Mo,4).toLowerCase()?(s=r.substr(Mo,4),Mo+=4):(s=a,0===$o&&Go(T)))),s!==a?(39===r.charCodeAt(Mo)?(i="'",Mo++):(i=a,0===$o&&Go(er)),i!==a?o=u=[u,s,i]:(Mo=o,o=a)):(Mo=o,o=a)):(Mo=o,o=a),o!==a?(Do=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,"engine"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ar)),e!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a&&(o=Su())!==a?(Do=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(Mo=t,t=a)):(Mo=t,t=a)))),t}function da(){var t,e,n,o,u;return t=Mo,(e=Na())!==a&&li()!==a&&(n=function(){var t,e,n;return t=Mo,"read"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(ur)),e!==a&&li()!==a?("local"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(sr)),n===a&&(n=null),n!==a?(Do=t,t=e={type:"read",suffix:n&&"local"}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,"low_priority"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(ir)),e===a&&(e=null),e!==a&&li()!==a?("write"===r.substr(Mo,5).toLowerCase()?(n=r.substr(Mo,5),Mo+=5):(n=a,0===$o&&Go(cr)),n!==a?(Do=t,t=e={type:"write",prefix:e&&"low_priority"}):(Mo=t,t=a)):(Mo=t,t=a)),t}())!==a?(Do=t,o=e,u=n,$i.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:u}):(Mo=t,t=a),t}function ya(){var t,e,n,o,u,s,i;return(t=La())===a&&(t=Mo,e=Mo,40===r.charCodeAt(Mo)?(n="(",Mo++):(n=a,0===$o&&Go(lr)),n!==a&&(o=li())!==a&&(u=ya())!==a&&(s=li())!==a?(41===r.charCodeAt(Mo)?(i=")",Mo++):(i=a,0===$o&&Go(pr)),i!==a?e=n=[n,o,u,s,i]:(Mo=e,e=a)):(Mo=e,e=a),e!==a&&(Do=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function ha(){var t,e,n,o,u,s,i,c,l;if(t=Mo,ds()!==a)if(li()!==a)if((e=ma())!==a){for(n=[],o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=ma())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=ma())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);n!==a?(Do=t,t=f(e,n)):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;return t===a&&(t=Mo,li()!==a&&ds()!==a&&(e=li())!==a&&(n=function(){var t,e,n,o;t=Mo,"recursive"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(se));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(o=li())!==a&&(u=ma())!==a?(Do=t,(l=u).recursive=!0,t=[l]):(Mo=t,t=a)),t}function ma(){var r,t,e,n,o;return r=Mo,(t=Du())===a&&(t=Su()),t!==a&&li()!==a?((e=wa())===a&&(e=null),e!==a&&li()!==a&&ss()!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=Jo())!==a&&li()!==a&&ui()!==a?(Do=r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e}):(Mo=r,r=a)):(Mo=r,r=a),r}function wa(){var r,t;return r=Mo,ai()!==a&&li()!==a&&(t=Pa())!==a&&li()!==a&&ui()!==a?(Do=r,r=t):(Mo=r,r=a),r}function La(){var t,e,n,o,u,s,i,c,f,l,p,v,b,d,y,h,m,w,L,C,A,E,g;return t=Mo,li()!==a?((e=ha())===a&&(e=null),e!==a&&li()!==a&&function(){var t,e,n,o;t=Mo,"select"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(te));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&pi()!==a?((n=function(){var r,t,e,n,o,u;if(r=Mo,(t=Ca())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=Ca())!==a?n=o=[o,u]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=Ca())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a?(Do=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())===a&&(n=null),n!==a&&li()!==a?((o=ws())===a&&(o=null),o!==a&&li()!==a&&(u=Aa())!==a&&li()!==a?((s=ja())===a&&(s=null),s!==a&&li()!==a?((i=Da())===a&&(i=null),i!==a&&li()!==a?((c=function(){var t,e,n;t=Mo,(e=function(){var t,e,n,o;t=Mo,"group"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Ue));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&ys()!==a&&li()!==a&&(n=Ka())!==a?(Do=t,e={columns:n.value},t=e):(Mo=t,t=a);return t}())===a&&(c=null),c!==a&&li()!==a?((f=function(){var t,e;t=Mo,function(){var t,e,n,o;t=Mo,"having"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Pe));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=nu())!==a?(Do=t,t=e):(Mo=t,t=a);return t}())===a&&(f=null),f!==a&&li()!==a?((l=Fa())===a&&(l=null),l!==a&&li()!==a?((p=qa())===a&&(p=null),p!==a?((v=function(){var t,e;t=Mo,ds()!==a&&li()!==a?("cs"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(jr)),e===a&&("ur"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Sr)),e===a&&("rs"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Tr)),e===a&&("rr"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(_r))))),e!==a?(Do=t,t={type:"isolation",keyword:"with",expr:{type:"origin",value:e}}):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(v=null),v!==a&&li()!==a?(Do=t,b=e,d=n,y=o,h=u,w=i,L=c,C=f,A=l,E=p,g=v,(m=s)&&m.forEach(r=>r.table&&$i.add(`select::${r.db}::${r.table}`)),t={with:b,type:"select",options:d,distinct:y,columns:h,from:m,where:w,groupby:L,having:C,orderby:A,limit:E,isolation:g}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t}function Ca(){var t,e;return t=Mo,(e=function(){var t;"sql_calc_found_rows"===r.substr(Mo,19).toLowerCase()?(t=r.substr(Mo,19),Mo+=19):(t=a,0===$o&&Go(vo));return t}())===a&&((e=function(){var t;"sql_cache"===r.substr(Mo,9).toLowerCase()?(t=r.substr(Mo,9),Mo+=9):(t=a,0===$o&&Go(bo));return t}())===a&&(e=function(){var t;"sql_no_cache"===r.substr(Mo,12).toLowerCase()?(t=r.substr(Mo,12),Mo+=12):(t=a,0===$o&&Go(yo));return t}()),e===a&&(e=function(){var t;"sql_big_result"===r.substr(Mo,14).toLowerCase()?(t=r.substr(Mo,14),Mo+=14):(t=a,0===$o&&Go(mo));return t}())===a&&(e=function(){var t;"sql_small_result"===r.substr(Mo,16).toLowerCase()?(t=r.substr(Mo,16),Mo+=16):(t=a,0===$o&&Go(ho));return t}())===a&&(e=function(){var t;"sql_buffer_result"===r.substr(Mo,17).toLowerCase()?(t=r.substr(Mo,17),Mo+=17):(t=a,0===$o&&Go(wo));return t}())),e!==a&&(Do=t,e=e),t=e}function Aa(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=ms())===a&&(t=Mo,(e=oi())!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=oi())),t!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ea())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ea())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){qi.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?Mi(e,t):[e]}(0,e)):(Mo=r,r=a)}else Mo=r,r=a;if(r===a)if(r=Mo,(t=Ea())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ea())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ea())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=f(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ea(){var r,t,e,n,o;return r=Mo,t=Mo,(e=hu())!==a&&(n=li())!==a&&(o=ei())!==a?t=e=[e,n,o]:(Mo=t,t=a),t===a&&(t=null),t!==a&&(e=li())!==a&&(n=oi())!==a?(Do=r,r=t=function(r){const t=r&&r[0]||null;return qi.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(Mo=r,r=a),r===a&&(r=Mo,(t=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=eu())!==a){for(e=[],n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts())===a&&(u=fi()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts())===a&&(u=fi()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=Oi(t[e][1],n,o)}return o}(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a&&(e=li())!==a?((n=ga())===a&&(n=null),n!==a?(Do=r,r=t={expr:t,as:n}):(Mo=r,r=a)):(Mo=r,r=a)),r}function ga(){var r,t,e;return r=Mo,(t=ss())!==a&&li()!==a&&(e=function(){var r,t;r=Mo,(t=Su())!==a?(Do=Mo,(function(r){if(!0===Ii[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?a:void 0)!==a?(Do=r,r=t=t):(Mo=r,r=a)):(Mo=r,r=a);r===a&&(r=Mo,(t=wu())!==a&&(Do=r,t=t),r=t);return r}())!==a?(Do=r,r=t=e):(Mo=r,r=a),r===a&&(r=Mo,(t=ss())===a&&(t=null),t!==a&&li()!==a&&(e=hu())!==a?(Do=r,r=t=e):(Mo=r,r=a)),r}function ja(){var t,e;return t=Mo,function(){var t,e,n,o;t=Mo,"from"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(ve));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=ka())!==a?(Do=t,t=e):(Mo=t,t=a),t}function Sa(){var r,t,e;return r=Mo,(t=Oa())!==a&&li()!==a&&Xu()!==a&&li()!==a&&(e=Oa())!==a?(Do=r,r=t=[t,e]):(Mo=r,r=a),r}function Ta(){var t,e;return t=Mo,bs()!==a&&li()!==a?("btree"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(vr)),e===a&&("hash"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(br))),e!==a?(Do=t,t={keyword:"using",type:e.toLowerCase()}):(Mo=t,t=a)):(Mo=t,t=a),t}function _a(){var r,t,e,n,o,u;if(r=Mo,(t=xa())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=xa())!==a?n=o=[o,u]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=xa())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function xa(){var t,e,n,o,u,s;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"key_block_size"===r.substr(Mo,14).toLowerCase()?(e=r.substr(Mo,14),Mo+=14):(e=a,0===$o&&Go(K));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="KEY_BLOCK_SIZE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a?((n=Xs())===a&&(n=null),n!==a&&li()!==a&&(o=$u())!==a?(Do=t,u=n,s=o,t=e={type:e.toLowerCase(),symbol:u,expr:s}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Ta())===a&&(t=Mo,"with"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(dr)),e!==a&&li()!==a?("parser"===r.substr(Mo,6).toLowerCase()?(n=r.substr(Mo,6),Mo+=6):(n=a,0===$o&&Go(yr)),n!==a&&li()!==a&&(o=Su())!==a?(Do=t,t=e={type:"with parser",expr:o}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,"visible"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(hr)),e===a&&("invisible"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(mr))),e!==a&&(Do=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===a&&(t=bi()))),t}function ka(){var r,t,e,n;if(r=Mo,(t=Na())!==a){for(e=[],n=Ia();n!==a;)e.push(n),n=Ia();e!==a?(Do=r,r=t=wr(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ia(){var r,t,e;return r=Mo,li()!==a&&(t=ni())!==a&&li()!==a&&(e=Na())!==a?(Do=r,r=e):(Mo=r,r=a),r===a&&(r=Mo,li()!==a&&(t=function(){var r,t,e,n,o,u,s,i,c,f,l;if(r=Mo,(t=Ra())!==a)if(li()!==a)if((e=Na())!==a)if(li()!==a)if((n=bs())!==a)if(li()!==a)if(ai()!==a)if(li()!==a)if((o=yu())!==a){for(u=[],s=Mo,(i=li())!==a&&(c=ni())!==a&&(f=li())!==a&&(l=yu())!==a?s=i=[i,c,f,l]:(Mo=s,s=a);s!==a;)u.push(s),s=Mo,(i=li())!==a&&(c=ni())!==a&&(f=li())!==a&&(l=yu())!==a?s=i=[i,c,f,l]:(Mo=s,s=a);u!==a&&(s=li())!==a&&(i=ui())!==a?(Do=r,p=t,b=o,d=u,(v=e).join=p,v.using=Mi(b,d),r=t=v):(Mo=r,r=a)}else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;var p,v,b,d;r===a&&(r=Mo,(t=Ra())!==a&&li()!==a&&(e=Na())!==a&&li()!==a?((n=Ma())===a&&(n=null),n!==a?(Do=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,(t=Ra())===a&&(t=zo()),t!==a&&li()!==a&&(e=ai())!==a&&li()!==a&&(n=Jo())!==a&&li()!==a&&ui()!==a&&li()!==a?((o=ga())===a&&(o=null),o!==a&&(u=li())!==a?((s=Ma())===a&&(s=null),s!==a?(Do=r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)));return r}())!==a?(Do=r,r=t):(Mo=r,r=a)),r}function Na(){var t,e,n,o,u,s;return t=Mo,(e=function(){var t;"dual"===r.substr(Mo,4).toLowerCase()?(t=r.substr(Mo,4),Mo+=4):(t=a,0===$o&&Go(oo));return t}())!==a&&(Do=t,e={type:"dual"}),(t=e)===a&&(t=Mo,(e=Oa())!==a&&li()!==a?((n=ga())===a&&(n=null),n!==a?(Do=t,s=n,t=e="var"===(u=e).type?(u.as=s,u):{db:u.db,table:u.table,as:s}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=ai())!==a&&li()!==a&&(n=Jo())!==a&&li()!==a&&ui()!==a&&li()!==a?((o=ga())===a&&(o=null),o!==a?(Do=t,t=e=function(r,t){return r.parentheses=!0,{expr:r,as:t}}(n,o)):(Mo=t,t=a)):(Mo=t,t=a))),t}function Ra(){var t,e,n,o;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"left"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Ce));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(n=li())!==a?((o=ps())===a&&(o=null),o!==a&&li()!==a&&ls()!==a?(Do=t,t=e="LEFT JOIN"):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=function(){var t,e,n,o;t=Mo,"right"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Ae));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(n=li())!==a?((o=ps())===a&&(o=null),o!==a&&li()!==a&&ls()!==a?(Do=t,t=e="RIGHT JOIN"):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=function(){var t,e,n,o;t=Mo,"full"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Ee));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(n=li())!==a?((o=ps())===a&&(o=null),o!==a&&li()!==a&&ls()!==a?(Do=t,t=e="FULL JOIN"):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,e=Mo,(n=function(){var t,e,n,o;t=Mo,"inner"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(ge));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(o=li())!==a?e=n=[n,o]:(Mo=e,e=a),e===a&&(e=null),e!==a&&(n=ls())!==a?(Do=t,t=e="INNER JOIN"):(Mo=t,t=a)))),t}function Oa(){var r,t,e,n,o,u,s,i;return r=Mo,(t=hu())!==a?(e=Mo,(n=li())!==a&&(o=ei())!==a&&(u=li())!==a&&(s=hu())!==a?e=n=[n,o,u,s]:(Mo=e,e=a),e===a&&(e=null),e!==a?(Do=r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,(t=Ti())!==a&&(Do=r,(i=t).db=null,i.table=i.name,t=i),r=t),r}function Ua(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=eu())!==a){for(e=[],n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=Oi(t[r][1],n,t[r][3]);return n}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ma(){var r,t;return r=Mo,fs()!==a&&li()!==a&&(t=nu())!==a?(Do=r,r=t):(Mo=r,r=a),r}function Da(){var t,e;return t=Mo,function(){var t,e,n,o;t=Mo,"where"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Oe));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=nu())!==a?(Do=t,t=e):(Mo=t,t=a),t}function Pa(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=du())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=du())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=du())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=f(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Fa(){var t,e;return t=Mo,function(){var t,e,n,o;t=Mo,"order"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(De));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&ys()!==a&&li()!==a&&(e=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Ha())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ha())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ha())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=f(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=t,t=e):(Mo=t,t=a),t}function Ha(){var t,e,n;return t=Mo,(e=eu())!==a&&li()!==a?((n=function(){var t,e,n,o;t=Mo,"desc"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(qe));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DESC"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(n=function(){var t,e,n,o;t=Mo,"asc"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go($e));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="ASC"):(Mo=t,t=a)):(Mo=t,t=a);return t}()),n===a&&(n=null),n!==a?(Do=t,t=e={expr:e,type:n}):(Mo=t,t=a)):(Mo=t,t=a),t}function $a(){var r;return(r=$u())===a&&(r=ku()),r}function qa(){var t,e,n,o,u,s,i,c;return t=Mo,hs()!==a&&li()!==a?("first"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Lr)),e!==a&&li()!==a&&(n=$a())!==a&&li()!==a?("rows"===r.substr(Mo,4).toLowerCase()?(o=r.substr(Mo,4),Mo+=4):(o=a,0===$o&&Go(Cr)),o===a&&("row"===r.substr(Mo,3).toLowerCase()?(o=r.substr(Mo,3),Mo+=3):(o=a,0===$o&&Go(Ar))),o!==a&&li()!==a?("only"===r.substr(Mo,4).toLowerCase()?(u=r.substr(Mo,4),Mo+=4):(u=a,0===$o&&Go(Er)),u!==a?(Do=t,t={fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"first"}],value:n,suffix:[{type:"origin",value:o},{type:"origin",value:"only"}]}}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,function(){var t,e,n,o;t=Mo,"offset"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(He));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="OFFSET"):(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=$a())!==a&&li()!==a?("rows"===r.substr(Mo,4).toLowerCase()?(n=r.substr(Mo,4),Mo+=4):(n=a,0===$o&&Go(Cr)),n!==a&&li()!==a&&(o=hs())!==a&&li()!==a?("next"===r.substr(Mo,4).toLowerCase()?(u=r.substr(Mo,4),Mo+=4):(u=a,0===$o&&Go(gr)),u!==a&&li()!==a&&(s=$a())!==a&&li()!==a?("rows"===r.substr(Mo,4).toLowerCase()?(i=r.substr(Mo,4),Mo+=4):(i=a,0===$o&&Go(Cr)),i===a&&("row"===r.substr(Mo,3).toLowerCase()?(i=r.substr(Mo,3),Mo+=3):(i=a,0===$o&&Go(Ar))),i!==a&&li()!==a?("only"===r.substr(Mo,4).toLowerCase()?(c=r.substr(Mo,4),Mo+=4):(c=a,0===$o&&Go(Er)),c!==a?(Do=t,t=function(r,t,e){return{offset:{prefix:[{type:"origin",value:"offset"}],value:r,suffix:[{type:"origin",value:"rows"}]},fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"next"}],value:t,suffix:[{type:"origin",value:e},{type:"origin",value:"only"}]}}}(e,s,i)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a)),t}function Ba(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Wa())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Wa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Wa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=f(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Wa(){var t,e,n,o,u,s,i,c,f;return t=Mo,e=Mo,(n=hu())!==a&&(o=li())!==a&&(u=ei())!==a?e=n=[n,o,u]:(Mo=e,e=a),e===a&&(e=null),e!==a&&(n=li())!==a&&(o=Eu())!==a&&(u=li())!==a?(61===r.charCodeAt(Mo)?(s="=",Mo++):(s=a,0===$o&&Go(xr)),s!==a&&li()!==a&&(i=cu())!==a?(Do=t,t=e={column:o,value:i,table:(f=e)&&f[0]}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,e=Mo,(n=hu())!==a&&(o=li())!==a&&(u=ei())!==a?e=n=[n,o,u]:(Mo=e,e=a),e===a&&(e=null),e!==a&&(n=li())!==a&&(o=Eu())!==a&&(u=li())!==a?(61===r.charCodeAt(Mo)?(s="=",Mo++):(s=a,0===$o&&Go(xr)),s!==a&&li()!==a&&(i=vs())!==a&&li()!==a&&ai()!==a&&li()!==a&&(c=du())!==a&&li()!==a&&ui()!==a?(Do=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Mo=t,t=a)):(Mo=t,t=a)),t}function Ya(){var r;return(r=function(){var r,t;r=Mo,vs()!==a&&li()!==a&&(t=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Xa())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Xa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Xa())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=f(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())!==a?(Do=r,r=t):(Mo=r,r=a);return r}())===a&&(r=La()),r}function Ga(){var r,t,e,n,o,u,s,i,c;if(r=Mo,os()!==a)if(li()!==a)if((t=ai())!==a)if(li()!==a)if((e=Su())!==a){for(n=[],o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=Su())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,(u=li())!==a&&(s=ni())!==a&&(i=li())!==a&&(c=Su())!==a?o=u=[u,s,i,c]:(Mo=o,o=a);n!==a&&(o=li())!==a&&(u=ui())!==a?(Do=r,r=Mi(e,n)):(Mo=r,r=a)}else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;else Mo=r,r=a;return r===a&&(r=Mo,os()!==a&&li()!==a&&(t=Xa())!==a?(Do=r,r=t):(Mo=r,r=a)),r}function Va(){var t,e,n;return t=Mo,fs()!==a&&li()!==a?("duplicate"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(kr)),e!==a&&li()!==a&&Zs()!==a&&li()!==a&&zu()!==a&&li()!==a&&(n=Ba())!==a?(Do=t,t={keyword:"on duplicate key update",set:n}):(Mo=t,t=a)):(Mo=t,t=a),t}function Qa(){var t,e;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"insert"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ue));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e="insert"),(t=e)===a&&(t=Mo,(e=ts())!==a&&(Do=t,e="replace"),t=e),t}function Xa(){var r,t;return r=Mo,ai()!==a&&li()!==a&&(t=Ka())!==a&&li()!==a&&ui()!==a?(Do=r,r=t):(Mo=r,r=a),r}function Ka(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=eu())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Mi(r,t),e}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function za(){var t,e,n;return t=Mo,function(){var t,e,n,o;t=Mo,"interval"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(Dn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="INTERVAL"):(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=eu())!==a&&li()!==a&&(n=function(){var t;(t=function(){var t,e,n,o;t=Mo,"year"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Pn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="YEAR"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"month"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Fn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MONTH"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"day"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(Hn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DAY"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"hour"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go($n));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="HOUR"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"minute"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(qn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MINUTE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"second"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Bn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SECOND"):(Mo=t,t=a)):(Mo=t,t=a);return t}());return t}())!==a?(Do=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(Mo=t,t=a),t}function Ja(){var r,t,e,n,o,u;if(r=Mo,(t=Za())!==a)if(li()!==a){for(e=[],n=Mo,(o=li())!==a&&(u=Za())!==a?n=o=[o,u]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=Za())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a?(Do=r,r=t=i(t,e)):(Mo=r,r=a)}else Mo=r,r=a;else Mo=r,r=a;return r}function Za(){var t,e,n;return t=Mo,function(){var t,e,n,o;t=Mo,"when"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(an));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=nu())!==a&&li()!==a&&function(){var t,e,n,o;t=Mo,"then"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(un));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(n=eu())!==a?(Do=t,t={type:"when",cond:e,result:n}):(Mo=t,t=a),t}function ru(){var t,e;return t=Mo,function(){var t,e,n,o;t=Mo,"else"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(sn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&(e=eu())!==a?(Do=t,t={type:"else",result:e}):(Mo=t,t=a),t}function tu(){var r;return(r=function(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=ou())!==a){for(e=[],n=Mo,(o=pi())!==a&&(u=Ts())!==a&&(s=li())!==a&&(i=ou())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=pi())!==a&&(u=Ts())!==a&&(s=li())!==a&&(i=ou())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,t=Ir(t,e),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}())===a&&(r=function(){var r,t,e,n,o,u;if(r=Mo,(t=fu())!==a){if(e=[],n=Mo,(o=li())!==a&&(u=vu())!==a?n=o=[o,u]:(Mo=n,n=a),n!==a)for(;n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=vu())!==a?n=o=[o,u]:(Mo=n,n=a);else e=a;e!==a?(Do=r,t=Ri(t,e[0][1]),r=t):(Mo=r,r=a)}else Mo=r,r=a;return r}()),r}function eu(){var r;return(r=tu())===a&&(r=Jo()),r}function nu(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=eu())!==a){for(e=[],n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts())===a&&(u=ni()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=Ss())===a&&(u=Ts())===a&&(u=ni()),u!==a&&(s=li())!==a&&(i=eu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=Oi(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function ou(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=au())!==a){for(e=[],n=Mo,(o=pi())!==a&&(u=Ss())!==a&&(s=li())!==a&&(i=au())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=pi())!==a&&(u=Ss())!==a&&(s=li())!==a&&(i=au())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=Ir(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function au(){var t,e,n,o,u;return(t=uu())===a&&(t=function(){var r,t,e;r=Mo,(t=function(){var r,t,e,n,o;r=Mo,t=Mo,(e=js())!==a&&(n=li())!==a&&(o=gs())!==a?t=e=[e,n,o]:(Mo=t,t=a);t!==a&&(Do=r,t=Rr(t));(r=t)===a&&(r=gs());return r}())!==a&&li()!==a&&ai()!==a&&li()!==a&&(e=Jo())!==a&&li()!==a&&ui()!==a?(Do=r,n=t,(o=e).parentheses=!0,t=Ri(n,o),r=t):(Mo=r,r=a);var n,o;return r}())===a&&(t=Mo,(e=js())===a&&(e=Mo,33===r.charCodeAt(Mo)?(n="!",Mo++):(n=a,0===$o&&Go(Nr)),n!==a?(o=Mo,$o++,61===r.charCodeAt(Mo)?(u="=",Mo++):(u=a,0===$o&&Go(xr)),$o--,u===a?o=void 0:(Mo=o,o=a),o!==a?e=n=[n,o]:(Mo=e,e=a)):(Mo=e,e=a)),e!==a&&(n=li())!==a&&(o=au())!==a?(Do=t,t=e=Ri("NOT",o)):(Mo=t,t=a)),t}function uu(){var r,t,e,n,o;return r=Mo,(t=cu())!==a&&li()!==a?((e=function(){var r;(r=function(){var r,t,e,n,o,u,s;r=Mo,t=[],e=Mo,(n=li())!==a&&(o=su())!==a&&(u=li())!==a&&(s=cu())!==a?e=n=[n,o,u,s]:(Mo=e,e=a);if(e!==a)for(;e!==a;)t.push(e),e=Mo,(n=li())!==a&&(o=su())!==a&&(u=li())!==a&&(s=cu())!==a?e=n=[n,o,u,s]:(Mo=e,e=a);else t=a;t!==a&&(Do=r,t={type:"arithmetic",tail:t});return r=t}())===a&&(r=function(){var r,t,e,n;r=Mo,(t=iu())!==a&&li()!==a&&(e=ai())!==a&&li()!==a&&(n=Ka())!==a&&li()!==a&&ui()!==a?(Do=r,r=t={op:t,right:n}):(Mo=r,r=a);r===a&&(r=Mo,(t=iu())!==a&&li()!==a?((e=Ti())===a&&(e=Du())===a&&(e=Ru()),e!==a?(Do=r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(Mo=r,r=a)):(Mo=r,r=a));return r}())===a&&(r=function(){var r,t,e,n;r=Mo,(t=function(){var r,t,e,n,o;r=Mo,t=Mo,(e=js())!==a&&(n=li())!==a&&(o=Ls())!==a?t=e=[e,n,o]:(Mo=t,t=a);t!==a&&(Do=r,t=Rr(t));(r=t)===a&&(r=Ls());return r}())!==a&&li()!==a&&(e=cu())!==a&&li()!==a&&Ss()!==a&&li()!==a&&(n=cu())!==a?(Do=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Mo=r,r=a);return r}())===a&&(r=function(){var r,t,e,n,o;r=Mo,(t=As())!==a&&(e=li())!==a&&(n=cu())!==a?(Do=r,r=t={op:"IS",right:n}):(Mo=r,r=a);r===a&&(r=Mo,t=Mo,(e=As())!==a&&(n=li())!==a&&(o=js())!==a?t=e=[e,n,o]:(Mo=t,t=a),t!==a&&(e=li())!==a&&(n=cu())!==a?(Do=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Mo=r,r=a));return r}())===a&&(r=function(){var r,t,e;r=Mo,(t=function(){var r,t,e,n,o;r=Mo,t=Mo,(e=js())!==a&&(n=li())!==a&&(o=Es())!==a?t=e=[e,n,o]:(Mo=t,t=a);t!==a&&(Do=r,t=Rr(t));(r=t)===a&&(r=Es());return r}())!==a&&li()!==a?((e=Uu())===a&&(e=uu()),e!==a?(Do=r,r=t={op:t,right:e}):(Mo=r,r=a)):(Mo=r,r=a);return r}());return r}())===a&&(e=null),e!==a?(Do=r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?Di(n,o.tail):Oi(o.op,n,o.right)):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Du())===a&&(r=du()),r}function su(){var t;return">="===r.substr(Mo,2)?(t=">=",Mo+=2):(t=a,0===$o&&Go(Or)),t===a&&(62===r.charCodeAt(Mo)?(t=">",Mo++):(t=a,0===$o&&Go(Ur)),t===a&&("<="===r.substr(Mo,2)?(t="<=",Mo+=2):(t=a,0===$o&&Go(Mr)),t===a&&("<>"===r.substr(Mo,2)?(t="<>",Mo+=2):(t=a,0===$o&&Go(Dr)),t===a&&(60===r.charCodeAt(Mo)?(t="<",Mo++):(t=a,0===$o&&Go(Pr)),t===a&&(61===r.charCodeAt(Mo)?(t="=",Mo++):(t=a,0===$o&&Go(xr)),t===a&&("!="===r.substr(Mo,2)?(t="!=",Mo+=2):(t=a,0===$o&&Go(Fr)))))))),t}function iu(){var r,t,e,n,o;return r=Mo,t=Mo,(e=js())!==a&&(n=li())!==a&&(o=Cs())!==a?t=e=[e,n,o]:(Mo=t,t=a),t!==a&&(Do=r,t=Rr(t)),(r=t)===a&&(r=Cs()),r}function cu(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=lu())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=fu())!==a&&(s=li())!==a&&(i=lu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=fu())!==a&&(s=li())!==a&&(i=lu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Ni()}));return Di(r,t)}(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function fu(){var t;return 43===r.charCodeAt(Mo)?(t="+",Mo++):(t=a,0===$o&&Go(Hr)),t===a&&(45===r.charCodeAt(Mo)?(t="-",Mo++):(t=a,0===$o&&Go($r))),t}function lu(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=bu())!==a){for(e=[],n=Mo,(o=li())!==a?((u=pu())===a&&(u=fi()),u!==a&&(s=li())!==a&&(i=bu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a?((u=pu())===a&&(u=fi()),u!==a&&(s=li())!==a&&(i=bu())!==a?n=o=[o,u,s,i]:(Mo=n,n=a)):(Mo=n,n=a);e!==a?(Do=r,r=t=Di(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function pu(){var t;return 42===r.charCodeAt(Mo)?(t="*",Mo++):(t=a,0===$o&&Go(qr)),t===a&&(47===r.charCodeAt(Mo)?(t="/",Mo++):(t=a,0===$o&&Go(Br)),t===a&&(37===r.charCodeAt(Mo)?(t="%",Mo++):(t=a,0===$o&&Go(Wr)))),t}function vu(){var t,e,n;return(t=function(){var t,e,n,o,u,s,i;t=Mo,(e=ks())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=eu())!==a&&li()!==a&&ss()!==a&&li()!==a&&(o=xi())!==a&&li()!==a&&(u=ui())!==a?(Do=t,c=n,f=o,e={type:"cast",keyword:e.toLowerCase(),expr:c,symbol:"as",target:[f]},t=e):(Mo=t,t=a);var c,f;t===a&&(t=Mo,(e=ks())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=eu())!==a&&li()!==a&&ss()!==a&&li()!==a&&(o=Os())!==a&&li()!==a&&(u=ai())!==a&&li()!==a&&(s=qu())!==a&&li()!==a&&ui()!==a&&li()!==a&&(i=ui())!==a?(Do=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,n,s),t=e):(Mo=t,t=a),t===a&&(t=Mo,(e=ks())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=eu())!==a&&li()!==a&&ss()!==a&&li()!==a&&(o=Os())!==a&&li()!==a&&(u=ai())!==a&&li()!==a&&(s=qu())!==a&&li()!==a&&ni()!==a&&li()!==a&&(i=qu())!==a&&li()!==a&&ui()!==a&&li()!==a&&ui()!==a?(Do=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,n,s,i),t=e):(Mo=t,t=a),t===a&&(t=Mo,(e=ks())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=eu())!==a&&li()!==a&&ss()!==a&&li()!==a&&(o=function(){var t;(t=function(){var t,e,n,o;t=Mo,"signed"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(dn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SIGNED"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=Us());return t}())!==a&&li()!==a?((u=Ds())===a&&(u=null),u!==a&&li()!==a&&(s=ui())!==a?(Do=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,n,o,u),t=e):(Mo=t,t=a)):(Mo=t,t=a))));return t}())===a&&(t=Uu())===a&&(t=function(){var t;(t=function(){var t,e,n,o;t=Mo,(e=function(){var t,e,n,o;t=Mo,"count"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Je));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="COUNT"):(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=function(){var t,e,n,o,u,s,i,c,f,l;t=Mo,(e=function(){var t,e;t=Mo,42===r.charCodeAt(Mo)?(e="*",Mo++):(e=a,0===$o&&Go(qr));e!==a&&(Do=t,e={type:"star",value:"*"});return t=e}())!==a&&(Do=t,e={expr:e});if((t=e)===a){if(t=Mo,(e=ws())===a&&(e=null),e!==a)if(li()!==a)if((n=ai())!==a)if(li()!==a)if((o=eu())!==a)if(li()!==a)if(ui()!==a){for(u=[],s=Mo,(i=li())!==a?((c=Ss())===a&&(c=Ts()),c!==a&&(f=li())!==a&&(l=eu())!==a?s=i=[i,c,f,l]:(Mo=s,s=a)):(Mo=s,s=a);s!==a;)u.push(s),s=Mo,(i=li())!==a?((c=Ss())===a&&(c=Ts()),c!==a&&(f=li())!==a&&(l=eu())!==a?s=i=[i,c,f,l]:(Mo=s,s=a)):(Mo=s,s=a);u!==a&&(s=li())!==a?((i=Fa())===a&&(i=null),i!==a?(Do=t,e=function(r,t,e,n){const o=e.length;let a=t;a.parentheses=!0;for(let r=0;r<o;++r)a=Oi(e[r][1],a,e[r][3]);return{distinct:r,expr:a,orderby:n}}(e,o,u,i),t=e):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;t===a&&(t=Mo,(e=ws())===a&&(e=null),e!==a&&li()!==a&&(n=Ua())!==a&&li()!==a?((o=Fa())===a&&(o=null),o!==a?(Do=t,t=e={distinct:e,expr:n,orderby:o}):(Mo=t,t=a)):(Mo=t,t=a))}return t}())!==a&&li()!==a&&ui()!==a&&li()!==a?((o=Nu())===a&&(o=null),o!==a?(Do=t,t=e={type:"aggr_func",name:e,args:n,over:o}):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=function(){var t;(t=function(){var t,e,n,o;t=Mo,"sum"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(tn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SUM"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"max"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(Ze));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MAX"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"min"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(rn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MIN"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"avg"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(en));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="AVG"):(Mo=t,t=a)):(Mo=t,t=a);return t}());return t}())!==a&&li()!==a&&ai()!==a&&li()!==a&&(n=cu())!==a&&li()!==a&&ui()!==a&&li()!==a?((o=Nu())===a&&(o=null),o!==a?(Do=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...Ni()},t=e):(Mo=t,t=a)):(Mo=t,t=a);return t}());return t}())===a&&(t=Ru())===a&&(t=function(){var r,t,e,n,o,u,s,i;return r=Mo,_s()!==a&&li()!==a&&(t=Ja())!==a&&li()!==a?((e=ru())===a&&(e=null),e!==a&&li()!==a&&(n=xs())!==a&&li()!==a?((o=_s())===a&&(o=null),o!==a?(Do=r,s=t,(i=e)&&s.push(i),r={type:"case",expr:null,args:s}):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,_s()!==a&&li()!==a&&(t=eu())!==a&&li()!==a&&(e=Ja())!==a&&li()!==a?((n=ru())===a&&(n=null),n!==a&&li()!==a&&(o=xs())!==a&&li()!==a?((u=_s())===a&&(u=null),u!==a?(Do=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(Mo=r,r=a)):(Mo=r,r=a)):(Mo=r,r=a)),r}())===a&&(t=za())===a&&(t=du())===a&&(t=ku())===a&&(t=Mo,ai()!==a&&li()!==a&&(e=nu())!==a&&li()!==a&&ui()!==a?(Do=t,(n=e).parentheses=!0,t=n):(Mo=t,t=a),t===a&&(t=Ti())),t}function bu(){var t,e,n,o,u;return(t=function(){var t,e,n,o,u,s,i,c;if(t=Mo,(e=vu())!==a)if(li()!==a){for(n=[],o=Mo,(u=li())!==a?("?|"===r.substr(Mo,2)?(s="?|",Mo+=2):(s=a,0===$o&&Go(Gr)),s===a&&("?&"===r.substr(Mo,2)?(s="?&",Mo+=2):(s=a,0===$o&&Go(Vr)),s===a&&(63===r.charCodeAt(Mo)?(s="?",Mo++):(s=a,0===$o&&Go(Qr)),s===a&&("#-"===r.substr(Mo,2)?(s="#-",Mo+=2):(s=a,0===$o&&Go(Xr)),s===a&&("#>>"===r.substr(Mo,3)?(s="#>>",Mo+=3):(s=a,0===$o&&Go(Kr)),s===a&&("#>"===r.substr(Mo,2)?(s="#>",Mo+=2):(s=a,0===$o&&Go(zr)),s===a&&(s=ci())===a&&(s=ii())===a&&("@>"===r.substr(Mo,2)?(s="@>",Mo+=2):(s=a,0===$o&&Go(Jr)),s===a&&("<@"===r.substr(Mo,2)?(s="<@",Mo+=2):(s=a,0===$o&&Go(Zr))))))))),s!==a&&(i=li())!==a&&(c=vu())!==a?o=u=[u,s,i,c]:(Mo=o,o=a)):(Mo=o,o=a);o!==a;)n.push(o),o=Mo,(u=li())!==a?("?|"===r.substr(Mo,2)?(s="?|",Mo+=2):(s=a,0===$o&&Go(Gr)),s===a&&("?&"===r.substr(Mo,2)?(s="?&",Mo+=2):(s=a,0===$o&&Go(Vr)),s===a&&(63===r.charCodeAt(Mo)?(s="?",Mo++):(s=a,0===$o&&Go(Qr)),s===a&&("#-"===r.substr(Mo,2)?(s="#-",Mo+=2):(s=a,0===$o&&Go(Xr)),s===a&&("#>>"===r.substr(Mo,3)?(s="#>>",Mo+=3):(s=a,0===$o&&Go(Kr)),s===a&&("#>"===r.substr(Mo,2)?(s="#>",Mo+=2):(s=a,0===$o&&Go(zr)),s===a&&(s=ci())===a&&(s=ii())===a&&("@>"===r.substr(Mo,2)?(s="@>",Mo+=2):(s=a,0===$o&&Go(Jr)),s===a&&("<@"===r.substr(Mo,2)?(s="<@",Mo+=2):(s=a,0===$o&&Go(Zr))))))))),s!==a&&(i=li())!==a&&(c=vu())!==a?o=u=[u,s,i,c]:(Mo=o,o=a)):(Mo=o,o=a);n!==a?(Do=t,f=e,e=(l=n)&&0!==l.length?Di(f,l):f,t=e):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;var f,l;return t}())===a&&(t=Mo,(e=function(){var t;33===r.charCodeAt(Mo)?(t="!",Mo++):(t=a,0===$o&&Go(Nr));t===a&&(45===r.charCodeAt(Mo)?(t="-",Mo++):(t=a,0===$o&&Go($r)),t===a&&(43===r.charCodeAt(Mo)?(t="+",Mo++):(t=a,0===$o&&Go(Hr)),t===a&&(126===r.charCodeAt(Mo)?(t="~",Mo++):(t=a,0===$o&&Go(Yr)))));return t}())!==a?(n=Mo,(o=li())!==a&&(u=bu())!==a?n=o=[o,u]:(Mo=n,n=a),n!==a?(Do=t,t=e=Ri(e,n[1])):(Mo=t,t=a)):(Mo=t,t=a)),t}function du(){var r,t,e,n,o,u,s,i,c,f,l,p;return r=Mo,(t=hu())!==a&&(e=li())!==a&&(n=ei())!==a&&(o=li())!==a&&(u=Eu())!==a?(s=Mo,(i=li())!==a&&(c=na())!==a?s=i=[i,c]:(Mo=s,s=a),s===a&&(s=null),s!==a?(Do=r,f=t,l=u,p=s,qi.add(`select::${f}::${l}`),r=t={type:"column_ref",table:f,column:l,collate:p&&p[1]}):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,(t=gu())!==a?(e=Mo,(n=li())!==a&&(o=na())!==a?e=n=[n,o]:(Mo=e,e=a),e===a&&(e=null),e!==a?(Do=r,r=t=function(r,t){return qi.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1]}}(t,e)):(Mo=r,r=a)):(Mo=r,r=a)),r}function yu(){var r,t;return r=Mo,(t=Su())!==a&&(Do=r,t={type:"default",value:t}),(r=t)===a&&(r=mu()),r}function hu(){var r,t;return r=Mo,(t=Su())!==a?(Do=Mo,(rt(t)?a:void 0)!==a?(Do=r,r=t=t):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,(t=wu())!==a&&(Do=r,t=t),r=t),r}function mu(){var r;return(r=Lu())===a&&(r=Cu())===a&&(r=Au()),r}function wu(){var r,t;return r=Mo,(t=Lu())===a&&(t=Cu())===a&&(t=Au()),t!==a&&(Do=r,t=t.value),r=t}function Lu(){var t,e,n,o;if(t=Mo,34===r.charCodeAt(Mo)?(e='"',Mo++):(e=a,0===$o&&Go(tt)),e!==a){if(n=[],et.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(nt)),o!==a)for(;o!==a;)n.push(o),et.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(nt));else n=a;n!==a?(34===r.charCodeAt(Mo)?(o='"',Mo++):(o=a,0===$o&&Go(tt)),o!==a?(Do=t,t=e={type:"double_quote_string",value:n.join("")}):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;return t}function Cu(){var t,e,n,o;if(t=Mo,39===r.charCodeAt(Mo)?(e="'",Mo++):(e=a,0===$o&&Go(er)),e!==a){if(n=[],ot.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(at)),o!==a)for(;o!==a;)n.push(o),ot.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(at));else n=a;n!==a?(39===r.charCodeAt(Mo)?(o="'",Mo++):(o=a,0===$o&&Go(er)),o!==a?(Do=t,t=e={type:"single_quote_string",value:n.join("")}):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;return t}function Au(){var t,e,n,o;if(t=Mo,96===r.charCodeAt(Mo)?(e="`",Mo++):(e=a,0===$o&&Go(ut)),e!==a){if(n=[],st.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(it)),o!==a)for(;o!==a;)n.push(o),st.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(it));else n=a;n!==a?(96===r.charCodeAt(Mo)?(o="`",Mo++):(o=a,0===$o&&Go(ut)),o!==a?(Do=t,t=e={type:"backticks_quote_string",value:n.join("")}):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;return t}function Eu(){var r,t;return r=Mo,(t=ju())!==a&&(Do=r,t=t),(r=t)===a&&(r=wu()),r}function gu(){var r,t;return r=Mo,(t=ju())!==a?(Do=Mo,(rt(t)?a:void 0)!==a?(Do=r,r=t=t):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=wu()),r}function ju(){var r,t,e,n;if(r=Mo,(t=Tu())!==a){for(e=[],n=xu();n!==a;)e.push(n),n=xu();e!==a?(Do=r,r=t=ct(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Su(){var r,t,e,n;if(r=Mo,(t=Tu())!==a){for(e=[],n=_u();n!==a;)e.push(n),n=_u();e!==a?(Do=r,r=t=ct(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Tu(){var t;return ft.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(lt)),t}function _u(){var t;return pt.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(vt)),t}function xu(){var t;return bt.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(dt)),t}function ku(){var t,e,n,o;return t=Mo,e=Mo,58===r.charCodeAt(Mo)?(n=":",Mo++):(n=a,0===$o&&Go(yt)),n!==a&&(o=Su())!==a?e=n=[n,o]:(Mo=e,e=a),e!==a&&(Do=t,e={type:"param",value:e[1]}),t=e}function Iu(){var r,t,e;return r=Mo,fs()!==a&&li()!==a&&zu()!==a&&li()!==a&&(t=Vs())!==a&&li()!==a&&ai()!==a&&li()!==a?((e=Ka())===a&&(e=null),e!==a&&li()!==a&&ui()!==a?(Do=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,fs()!==a&&li()!==a&&zu()!==a&&li()!==a&&(t=Vs())!==a?(Do=r,r=function(r){return{type:"on update",keyword:r}}(t)):(Mo=r,r=a)),r}function Nu(){var t,e,n;return t=Mo,function(){var t,e,n,o;t=Mo,"over"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Te));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}()!==a&&li()!==a&&ai()!==a&&li()!==a&&os()!==a&&li()!==a&&ys()!==a&&li()!==a&&(e=Aa())!==a&&li()!==a?((n=Fa())===a&&(n=null),n!==a&&li()!==a&&ui()!==a?(Do=t,t={partitionby:e,orderby:n}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Iu()),t}function Ru(){var t,e,n,o,u;return t=Mo,(e=function(){var t;(t=Ou())===a&&(t=function(){var t,e,n,o;t=Mo,"current_user"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(Gn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CURRENT_USER"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"user"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Un));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="USER"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"session_user"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(Vn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SESSION_USER"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"system_user"===r.substr(Mo,11).toLowerCase()?(e=r.substr(Mo,11),Mo+=11):(e=a,0===$o&&Go(Qn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SYSTEM_USER"):(Mo=t,t=a)):(Mo=t,t=a);return t}());return t}())!==a&&li()!==a&&(n=ai())!==a&&li()!==a?((o=Ka())===a&&(o=null),o!==a&&li()!==a&&ui()!==a&&li()!==a?((u=Nu())===a&&(u=null),u!==a?(Do=t,t=e={type:"function",name:{name:[{type:"default",value:e}]},args:o||{type:"expr_list",value:[]},over:u,...Ni()}):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=Ou())!==a&&li()!==a?((n=Iu())===a&&(n=null),n!==a?(Do=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...Ni()}):(Mo=t,t=a)):(Mo=t,t=a),t===a&&(t=Mo,(e=gi())!==a&&li()!==a&&(n=ai())!==a&&li()!==a?((o=nu())===a&&(o=null),o!==a&&li()!==a&&ui()!==a&&li()!==a?((u=Nu())===a&&(u=null),u!==a?(Do=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...Ni()}}(e,o,u)):(Mo=t,t=a)):(Mo=t,t=a)):(Mo=t,t=a))),t}function Ou(){var t;return(t=function(){var t,e,n,o;t=Mo,"current_date"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(Mn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CURRENT_DATE"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=function(){var t,e,n,o;t=Mo,"current_time"===r.substr(Mo,12).toLowerCase()?(e=r.substr(Mo,12),Mo+=12):(e=a,0===$o&&Go(Wn));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CURRENT_TIME"):(Mo=t,t=a)):(Mo=t,t=a);return t}())===a&&(t=Vs()),t}function Uu(){var t;return(t=Du())===a&&(t=$u())===a&&(t=function(){var t,e;t=Mo,(e=function(){var t,e,n,o;t=Mo,"true"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Xt));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={type:"bool",value:!0});(t=e)===a&&(t=Mo,(e=function(){var t,e,n,o;t=Mo,"false"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(zt));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={type:"bool",value:!1}),t=e);return t}())===a&&(t=Mu())===a&&(t=function(){var t,e,n,o,u,s;t=Mo,(e=Ys())===a&&(e=Bs())===a&&(e=Gs())===a&&(e=Ws());if(e!==a)if(li()!==a){if(n=Mo,39===r.charCodeAt(Mo)?(o="'",Mo++):(o=a,0===$o&&Go(er)),o!==a){for(u=[],s=Fu();s!==a;)u.push(s),s=Fu();u!==a?(39===r.charCodeAt(Mo)?(s="'",Mo++):(s=a,0===$o&&Go(er)),s!==a?n=o=[o,u,s]:(Mo=n,n=a)):(Mo=n,n=a)}else Mo=n,n=a;n!==a?(Do=t,e=ht(e,n),t=e):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;if(t===a)if(t=Mo,(e=Ys())===a&&(e=Bs())===a&&(e=Gs())===a&&(e=Ws()),e!==a)if(li()!==a){if(n=Mo,34===r.charCodeAt(Mo)?(o='"',Mo++):(o=a,0===$o&&Go(tt)),o!==a){for(u=[],s=Pu();s!==a;)u.push(s),s=Pu();u!==a?(34===r.charCodeAt(Mo)?(s='"',Mo++):(s=a,0===$o&&Go(tt)),s!==a?n=o=[o,u,s]:(Mo=n,n=a)):(Mo=n,n=a)}else Mo=n,n=a;n!==a?(Do=t,e=ht(e,n),t=e):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;return t}()),t}function Mu(){var t,e;return t=Mo,(e=function(){var t,e,n,o;t=Mo,"null"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Vt));e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a);return t}())!==a&&(Do=t,e={type:"null",value:null}),t=e}function Du(){var t,e,n,o,u;if(t=Mo,e=Mo,39===r.charCodeAt(Mo)?(n="'",Mo++):(n=a,0===$o&&Go(er)),n!==a){for(o=[],u=Fu();u!==a;)o.push(u),u=Fu();o!==a?(39===r.charCodeAt(Mo)?(u="'",Mo++):(u=a,0===$o&&Go(er)),u!==a?e=n=[n,o,u]:(Mo=e,e=a)):(Mo=e,e=a)}else Mo=e,e=a;if(e!==a&&(Do=t,e={type:"single_quote_string",value:e[1].join("")}),(t=e)===a){if(t=Mo,e=Mo,34===r.charCodeAt(Mo)?(n='"',Mo++):(n=a,0===$o&&Go(tt)),n!==a){for(o=[],u=Pu();u!==a;)o.push(u),u=Pu();o!==a?(34===r.charCodeAt(Mo)?(u='"',Mo++):(u=a,0===$o&&Go(tt)),u!==a?e=n=[n,o,u]:(Mo=e,e=a)):(Mo=e,e=a)}else Mo=e,e=a;e!==a&&(Do=t,e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)),t=e}return t}function Pu(){var t;return mt.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(wt)),t===a&&(t=Hu()),t}function Fu(){var t;return Lt.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(Ct)),t===a&&(t=Hu()),t}function Hu(){var t,e,n,o,u,s,i,c,f,l;return t=Mo,"\\'"===r.substr(Mo,2)?(e="\\'",Mo+=2):(e=a,0===$o&&Go(At)),e!==a&&(Do=t,e="\\'"),(t=e)===a&&(t=Mo,'\\"'===r.substr(Mo,2)?(e='\\"',Mo+=2):(e=a,0===$o&&Go(Et)),e!==a&&(Do=t,e='\\"'),(t=e)===a&&(t=Mo,"\\\\"===r.substr(Mo,2)?(e="\\\\",Mo+=2):(e=a,0===$o&&Go(gt)),e!==a&&(Do=t,e="\\\\"),(t=e)===a&&(t=Mo,"\\/"===r.substr(Mo,2)?(e="\\/",Mo+=2):(e=a,0===$o&&Go(jt)),e!==a&&(Do=t,e="\\/"),(t=e)===a&&(t=Mo,"\\b"===r.substr(Mo,2)?(e="\\b",Mo+=2):(e=a,0===$o&&Go(St)),e!==a&&(Do=t,e="\b"),(t=e)===a&&(t=Mo,"\\f"===r.substr(Mo,2)?(e="\\f",Mo+=2):(e=a,0===$o&&Go(Tt)),e!==a&&(Do=t,e="\f"),(t=e)===a&&(t=Mo,"\\n"===r.substr(Mo,2)?(e="\\n",Mo+=2):(e=a,0===$o&&Go(_t)),e!==a&&(Do=t,e="\n"),(t=e)===a&&(t=Mo,"\\r"===r.substr(Mo,2)?(e="\\r",Mo+=2):(e=a,0===$o&&Go(xt)),e!==a&&(Do=t,e="\r"),(t=e)===a&&(t=Mo,"\\t"===r.substr(Mo,2)?(e="\\t",Mo+=2):(e=a,0===$o&&Go(kt)),e!==a&&(Do=t,e="\t"),(t=e)===a&&(t=Mo,"\\u"===r.substr(Mo,2)?(e="\\u",Mo+=2):(e=a,0===$o&&Go(It)),e!==a&&(n=Vu())!==a&&(o=Vu())!==a&&(u=Vu())!==a&&(s=Vu())!==a?(Do=t,i=n,c=o,f=u,l=s,t=e=String.fromCharCode(parseInt("0x"+i+c+f+l))):(Mo=t,t=a),t===a&&(t=Mo,92===r.charCodeAt(Mo)?(e="\\",Mo++):(e=a,0===$o&&Go(Nt)),e!==a&&(Do=t,e="\\"),(t=e)===a&&(t=Mo,"''"===r.substr(Mo,2)?(e="''",Mo+=2):(e=a,0===$o&&Go(Rt)),e!==a&&(Do=t,e="''"),(t=e)===a&&(t=Mo,'""'===r.substr(Mo,2)?(e='""',Mo+=2):(e=a,0===$o&&Go(Ot)),e!==a&&(Do=t,e='""'),(t=e)===a&&(t=Mo,"``"===r.substr(Mo,2)?(e="``",Mo+=2):(e=a,0===$o&&Go(Ut)),e!==a&&(Do=t,e="``"),t=e))))))))))))),t}function $u(){var r,t,e;return r=Mo,(t=function(){var r,t,e,n;r=Mo,(t=qu())!==a&&(e=Bu())!==a&&(n=Wu())!==a?(Do=r,r=t={type:"bigint",value:t+e+n}):(Mo=r,r=a);r===a&&(r=Mo,(t=qu())!==a&&(e=Bu())!==a?(Do=r,t=function(r,t){const e=r+t;return Ui(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(Mo=r,r=a),r===a&&(r=Mo,(t=qu())!==a&&(e=Wu())!==a?(Do=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(Mo=r,r=a),r===a&&(r=Mo,(t=qu())!==a&&(Do=r,t=function(r){return Ui(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==a&&(Do=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function qu(){var t,e,n;return(t=Yu())===a&&(t=Gu())===a&&(t=Mo,45===r.charCodeAt(Mo)?(e="-",Mo++):(e=a,0===$o&&Go($r)),e===a&&(43===r.charCodeAt(Mo)?(e="+",Mo++):(e=a,0===$o&&Go(Hr))),e!==a&&(n=Yu())!==a?(Do=t,t=e=e+n):(Mo=t,t=a),t===a&&(t=Mo,45===r.charCodeAt(Mo)?(e="-",Mo++):(e=a,0===$o&&Go($r)),e===a&&(43===r.charCodeAt(Mo)?(e="+",Mo++):(e=a,0===$o&&Go(Hr))),e!==a&&(n=Gu())!==a?(Do=t,t=e=function(r,t){return r+t}(e,n)):(Mo=t,t=a))),t}function Bu(){var t,e,n;return t=Mo,46===r.charCodeAt(Mo)?(e=".",Mo++):(e=a,0===$o&&Go(Pt)),e!==a&&(n=Yu())!==a?(Do=t,t=e="."+n):(Mo=t,t=a),t}function Wu(){var t,e,n;return t=Mo,(e=function(){var t,e,n;t=Mo,Bt.test(r.charAt(Mo))?(e=r.charAt(Mo),Mo++):(e=a,0===$o&&Go(Wt));e!==a?(Yt.test(r.charAt(Mo))?(n=r.charAt(Mo),Mo++):(n=a,0===$o&&Go(Gt)),n===a&&(n=null),n!==a?(Do=t,t=e=e+(null!==(o=n)?o:"")):(Mo=t,t=a)):(Mo=t,t=a);var o;return t}())!==a&&(n=Yu())!==a?(Do=t,t=e=e+n):(Mo=t,t=a),t}function Yu(){var r,t,e;if(r=Mo,t=[],(e=Gu())!==a)for(;e!==a;)t.push(e),e=Gu();else t=a;return t!==a&&(Do=r,t=t.join("")),r=t}function Gu(){var t;return Ft.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(Ht)),t}function Vu(){var t;return $t.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(qt)),t}function Qu(){var t,e,n,o;return t=Mo,"default"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(m)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function Xu(){var t,e,n,o;return t=Mo,"to"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Kt)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function Ku(){var t,e,n,o;return t=Mo,"drop"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Jt)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DROP"):(Mo=t,t=a)):(Mo=t,t=a),t}function zu(){var t,e,n,o;return t=Mo,"update"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ee)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function Ju(){var t,e,n,o;return t=Mo,"create"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ne)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function Zu(){var t,e,n,o;return t=Mo,"temporary"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(oe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function rs(){var t,e,n,o;return t=Mo,"delete"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ae)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ts(){var t,e,n,o;return t=Mo,"replace"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(ie)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function es(){var t,e,n,o;return t=Mo,"rename"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(ce)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ns(){var t,e,n,o;return t=Mo,"ignore"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(fe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function os(){var t,e,n,o;return t=Mo,"partition"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(le)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="PARTITION"):(Mo=t,t=a)):(Mo=t,t=a),t}function as(){var t,e,n,o;return t=Mo,"into"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(pe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function us(){var t,e,n,o;return t=Mo,"set"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(G)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SET"):(Mo=t,t=a)):(Mo=t,t=a),t}function ss(){var t,e,n,o;return t=Mo,"as"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(de)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function is(){var t,e,n,o;return t=Mo,"table"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(ye)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TABLE"):(Mo=t,t=a)):(Mo=t,t=a),t}function cs(){var t,e,n,o;return t=Mo,"tables"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(he)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TABLES"):(Mo=t,t=a)):(Mo=t,t=a),t}function fs(){var t,e,n,o;return t=Mo,"on"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Le)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ls(){var t,e,n,o;return t=Mo,"join"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(je)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ps(){var t,e,n,o;return t=Mo,"outer"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Se)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function vs(){var t,e,n,o;return t=Mo,"values"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Ne)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function bs(){var t,e,n,o;return t=Mo,"using"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Re)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ds(){var t,e,n,o;return t=Mo,"with"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(dr)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ys(){var t,e,n,o;return t=Mo,"by"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Me)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function hs(){var t,e,n,o;return t=Mo,"fetch"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(Fe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="FETCH"):(Mo=t,t=a)):(Mo=t,t=a),t}function ms(){var t,e,n,o;return t=Mo,"all"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(Be)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="ALL"):(Mo=t,t=a)):(Mo=t,t=a),t}function ws(){var t,e,n,o;return t=Mo,"distinct"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(We)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DISTINCT"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ls(){var t,e,n,o;return t=Mo,"between"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(Ye)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="BETWEEN"):(Mo=t,t=a)):(Mo=t,t=a),t}function Cs(){var t,e,n,o;return t=Mo,"in"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Ge)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="IN"):(Mo=t,t=a)):(Mo=t,t=a),t}function As(){var t,e,n,o;return t=Mo,"is"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(Ve)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="IS"):(Mo=t,t=a)):(Mo=t,t=a),t}function Es(){var t,e,n,o;return t=Mo,"like"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Qe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="LIKE"):(Mo=t,t=a)):(Mo=t,t=a),t}function gs(){var t,e,n,o;return t=Mo,"exists"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Xe)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="EXISTS"):(Mo=t,t=a)):(Mo=t,t=a),t}function js(){var t,e,n,o;return t=Mo,"not"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(R)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="NOT"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ss(){var t,e,n,o;return t=Mo,"and"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(Ke)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="AND"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ts(){var t,e,n,o;return t=Mo,"or"===r.substr(Mo,2).toLowerCase()?(e=r.substr(Mo,2),Mo+=2):(e=a,0===$o&&Go(ze)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="OR"):(Mo=t,t=a)):(Mo=t,t=a),t}function _s(){var t,e,n,o;return t=Mo,"case"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(on)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function xs(){var t,e,n,o;return t=Mo,"end"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(cn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?t=e=[e,n]:(Mo=t,t=a)):(Mo=t,t=a),t}function ks(){var t,e,n,o;return t=Mo,"cast"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(fn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CAST"):(Mo=t,t=a)):(Mo=t,t=a),t}function Is(){var t,e,n,o;return t=Mo,"char"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(ln)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CHAR"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ns(){var t,e,n,o;return t=Mo,"varchar"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(pn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="VARCHAR"):(Mo=t,t=a)):(Mo=t,t=a),t}function Rs(){var t,e,n,o;return t=Mo,"numeric"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(vn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="NUMERIC"):(Mo=t,t=a)):(Mo=t,t=a),t}function Os(){var t,e,n,o;return t=Mo,"decimal"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(bn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DECIMAL"):(Mo=t,t=a)):(Mo=t,t=a),t}function Us(){var t,e,n,o;return t=Mo,"unsigned"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(yn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="UNSIGNED"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ms(){var t,e,n,o;return t=Mo,"int"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(hn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="INT"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ds(){var t,e,n,o;return t=Mo,"integer"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(wn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="INTEGER"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ps(){var t,e,n,o;return t=Mo,"smallint"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(Cn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="SMALLINT"):(Mo=t,t=a)):(Mo=t,t=a),t}function Fs(){var t,e,n,o;return t=Mo,"tinyint"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(An)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TINYINT"):(Mo=t,t=a)):(Mo=t,t=a),t}function Hs(){var t,e,n,o;return t=Mo,"bigint"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(Tn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="BIGINT"):(Mo=t,t=a)):(Mo=t,t=a),t}function $s(){var t,e,n,o;return t=Mo,"float"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(_n)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="FLOAT"):(Mo=t,t=a)):(Mo=t,t=a),t}function qs(){var t,e,n,o;return t=Mo,"double"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(xn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DOUBLE"):(Mo=t,t=a)):(Mo=t,t=a),t}function Bs(){var t,e,n,o;return t=Mo,"date"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(kn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DATE"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ws(){var t,e,n,o;return t=Mo,"datetime"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(In)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="DATETIME"):(Mo=t,t=a)):(Mo=t,t=a),t}function Ys(){var t,e,n,o;return t=Mo,"time"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Nn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TIME"):(Mo=t,t=a)):(Mo=t,t=a),t}function Gs(){var t,e,n,o;return t=Mo,"timestamp"===r.substr(Mo,9).toLowerCase()?(e=r.substr(Mo,9),Mo+=9):(e=a,0===$o&&Go(Rn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TIMESTAMP"):(Mo=t,t=a)):(Mo=t,t=a),t}function Vs(){var t,e,n,o;return t=Mo,"current_timestamp"===r.substr(Mo,17).toLowerCase()?(e=r.substr(Mo,17),Mo+=17):(e=a,0===$o&&Go(Yn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CURRENT_TIMESTAMP"):(Mo=t,t=a)):(Mo=t,t=a),t}function Qs(){var t;return(t=function(){var t;return"@@"===r.substr(Mo,2)?(t="@@",Mo+=2):(t=a,0===$o&&Go(ro)),t}())===a&&(t=function(){var t;return 64===r.charCodeAt(Mo)?(t="@",Mo++):(t=a,0===$o&&Go(Zn)),t}())===a&&(t=function(){var t;return 36===r.charCodeAt(Mo)?(t="$",Mo++):(t=a,0===$o&&Go(to)),t}()),t}function Xs(){var t;return 61===r.charCodeAt(Mo)?(t="=",Mo++):(t=a,0===$o&&Go(xr)),t}function Ks(){var t,e,n,o;return t=Mo,"add"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(ao)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="ADD"):(Mo=t,t=a)):(Mo=t,t=a),t}function zs(){var t,e,n,o;return t=Mo,"column"===r.substr(Mo,6).toLowerCase()?(e=r.substr(Mo,6),Mo+=6):(e=a,0===$o&&Go(uo)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="COLUMN"):(Mo=t,t=a)):(Mo=t,t=a),t}function Js(){var t,e,n,o;return t=Mo,"index"===r.substr(Mo,5).toLowerCase()?(e=r.substr(Mo,5),Mo+=5):(e=a,0===$o&&Go(so)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="INDEX"):(Mo=t,t=a)):(Mo=t,t=a),t}function Zs(){var t,e,n,o;return t=Mo,"key"===r.substr(Mo,3).toLowerCase()?(e=r.substr(Mo,3),Mo+=3):(e=a,0===$o&&Go(v)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="KEY"):(Mo=t,t=a)):(Mo=t,t=a),t}function ri(){var t,e,n,o;return t=Mo,"comment"===r.substr(Mo,7).toLowerCase()?(e=r.substr(Mo,7),Mo+=7):(e=a,0===$o&&Go(fo)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="COMMENT"):(Mo=t,t=a)):(Mo=t,t=a),t}function ti(){var t,e,n,o;return t=Mo,"constraint"===r.substr(Mo,10).toLowerCase()?(e=r.substr(Mo,10),Mo+=10):(e=a,0===$o&&Go(lo)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="CONSTRAINT"):(Mo=t,t=a)):(Mo=t,t=a),t}function ei(){var t;return 46===r.charCodeAt(Mo)?(t=".",Mo++):(t=a,0===$o&&Go(Pt)),t}function ni(){var t;return 44===r.charCodeAt(Mo)?(t=",",Mo++):(t=a,0===$o&&Go(Lo)),t}function oi(){var t;return 42===r.charCodeAt(Mo)?(t="*",Mo++):(t=a,0===$o&&Go(qr)),t}function ai(){var t;return 40===r.charCodeAt(Mo)?(t="(",Mo++):(t=a,0===$o&&Go(lr)),t}function ui(){var t;return 41===r.charCodeAt(Mo)?(t=")",Mo++):(t=a,0===$o&&Go(pr)),t}function si(){var t;return 59===r.charCodeAt(Mo)?(t=";",Mo++):(t=a,0===$o&&Go(Eo)),t}function ii(){var t;return"->"===r.substr(Mo,2)?(t="->",Mo+=2):(t=a,0===$o&&Go(go)),t}function ci(){var t;return"->>"===r.substr(Mo,3)?(t="->>",Mo+=3):(t=a,0===$o&&Go(jo)),t}function fi(){var t;return(t=function(){var t;return"||"===r.substr(Mo,2)?(t="||",Mo+=2):(t=a,0===$o&&Go(So)),t}())===a&&(t=function(){var t;return"&&"===r.substr(Mo,2)?(t="&&",Mo+=2):(t=a,0===$o&&Go(To)),t}()),t}function li(){var r,t;for(r=[],(t=yi())===a&&(t=vi());t!==a;)r.push(t),(t=yi())===a&&(t=vi());return r}function pi(){var r,t;if(r=[],(t=yi())===a&&(t=vi()),t!==a)for(;t!==a;)r.push(t),(t=yi())===a&&(t=vi());else r=a;return r}function vi(){var t;return(t=function(){var t,e,n,o,u,s;t=Mo,"/*"===r.substr(Mo,2)?(e="/*",Mo+=2):(e=a,0===$o&&Go(_o));if(e!==a){for(n=[],o=Mo,u=Mo,$o++,"*/"===r.substr(Mo,2)?(s="*/",Mo+=2):(s=a,0===$o&&Go(xo)),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,u=Mo,$o++,"*/"===r.substr(Mo,2)?(s="*/",Mo+=2):(s=a,0===$o&&Go(xo)),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);n!==a?("*/"===r.substr(Mo,2)?(o="*/",Mo+=2):(o=a,0===$o&&Go(xo)),o!==a?t=e=[e,n,o]:(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;return t}())===a&&(t=function(){var t,e,n,o,u,s;t=Mo,"--"===r.substr(Mo,2)?(e="--",Mo+=2):(e=a,0===$o&&Go(ko));if(e!==a){for(n=[],o=Mo,u=Mo,$o++,s=hi(),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,u=Mo,$o++,s=hi(),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);n!==a?t=e=[e,n]:(Mo=t,t=a)}else Mo=t,t=a;return t}())===a&&(t=function(){var t,e,n,o,u,s;t=Mo,35===r.charCodeAt(Mo)?(e="#",Mo++):(e=a,0===$o&&Go(Io));if(e!==a){for(n=[],o=Mo,u=Mo,$o++,s=hi(),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);o!==a;)n.push(o),o=Mo,u=Mo,$o++,s=hi(),$o--,s===a?u=void 0:(Mo=u,u=a),u!==a&&(s=di())!==a?o=u=[u,s]:(Mo=o,o=a);n!==a?t=e=[e,n]:(Mo=t,t=a)}else Mo=t,t=a;return t}()),t}function bi(){var r,t,e,n,o,u,s;return r=Mo,(t=ri())!==a&&li()!==a?((e=Xs())===a&&(e=null),e!==a&&li()!==a&&(n=Du())!==a?(Do=r,u=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:s}):(Mo=r,r=a)):(Mo=r,r=a),r}function di(){var t;return r.length>Mo?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(No)),t}function yi(){var t;return Ro.test(r.charAt(Mo))?(t=r.charAt(Mo),Mo++):(t=a,0===$o&&Go(Oo)),t}function hi(){var t,e;if((t=function(){var t,e;t=Mo,$o++,r.length>Mo?(e=r.charAt(Mo),Mo++):(e=a,0===$o&&Go(No));$o--,e===a?t=void 0:(Mo=t,t=a);return t}())===a)if(t=[],Mt.test(r.charAt(Mo))?(e=r.charAt(Mo),Mo++):(e=a,0===$o&&Go(Dt)),e!==a)for(;e!==a;)t.push(e),Mt.test(r.charAt(Mo))?(e=r.charAt(Mo),Mo++):(e=a,0===$o&&Go(Dt));else t=a;return t}function mi(){var t,e;return t=Mo,Do=Mo,Hi=[],(!0?void 0:a)!==a&&li()!==a?((e=wi())===a&&(e=function(){var t,e;t=Mo,function(){var t;return"return"===r.substr(Mo,6).toLowerCase()?(t=r.substr(Mo,6),Mo+=6):(t=a,0===$o&&Go(eo)),t}()!==a&&li()!==a&&(e=Li())!==a?(Do=t,t={type:"return",expr:e}):(Mo=t,t=a);return t}()),e!==a?(Do=t,t={stmt:e,vars:Hi}):(Mo=t,t=a)):(Mo=t,t=a),t}function wi(){var t,e,n,o;return t=Mo,(e=Ti())===a&&(e=_i()),e!==a&&li()!==a?((n=function(){var t;return":="===r.substr(Mo,2)?(t=":=",Mo+=2):(t=a,0===$o&&Go(no)),t}())===a&&(n=Xs()),n!==a&&li()!==a&&(o=Li())!==a?(Do=t,t=e={type:"assign",left:e,symbol:n,right:o}):(Mo=t,t=a)):(Mo=t,t=a),t}function Li(){var t;return(t=ya())===a&&(t=function(){var r,t,e,n,o;r=Mo,(t=Ti())!==a&&li()!==a&&(e=Ra())!==a&&li()!==a&&(n=Ti())!==a&&li()!==a&&(o=Ma())!==a?(Do=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Mo=r,r=a);return r}())===a&&(t=Ci())===a&&(t=function(){var t,e;t=Mo,function(){var t;return 91===r.charCodeAt(Mo)?(t="[",Mo++):(t=a,0===$o&&Go(Co)),t}()!==a&&li()!==a&&(e=Si())!==a&&li()!==a&&function(){var t;return 93===r.charCodeAt(Mo)?(t="]",Mo++):(t=a,0===$o&&Go(Ao)),t}()!==a?(Do=t,t={type:"array",value:e}):(Mo=t,t=a);return t}()),t}function Ci(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Ai())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=fu())!==a&&(s=li())!==a&&(i=Ai())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=fu())!==a&&(s=li())!==a&&(i=Ai())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=Ir(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ai(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Ei())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=pu())!==a&&(s=li())!==a&&(i=Ei())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=pu())!==a&&(s=li())!==a&&(i=Ei())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=Ir(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ei(){var r,t,e;return(r=Uu())===a&&(r=Ti())===a&&(r=ji())===a&&(r=ku())===a&&(r=Mo,ai()!==a&&li()!==a&&(t=Ci())!==a&&li()!==a&&ui()!==a?(Do=r,(e=t).parentheses=!0,r=e):(Mo=r,r=a)),r}function gi(){var r,t,e,n,o,u,s;return r=Mo,(t=yu())!==a?(e=Mo,(n=li())!==a&&(o=ei())!==a&&(u=li())!==a&&(s=yu())!==a?e=n=[n,o,u,s]:(Mo=e,e=a),e===a&&(e=null),e!==a?(Do=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(Mo=r,r=a)):(Mo=r,r=a),r}function ji(){var r,t,e;return r=Mo,(t=gi())!==a&&li()!==a&&ai()!==a&&li()!==a?((e=Si())===a&&(e=null),e!==a&&li()!==a&&ui()!==a?(Do=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...Ni()}):(Mo=r,r=a)):(Mo=r,r=a),r===a&&(r=Mo,(t=gi())!==a&&(Do=r,t=function(r){return{type:"function",name:r,args:null,...Ni()}}(t)),r=t),r}function Si(){var r,t,e,n,o,u,s,i;if(r=Mo,(t=Ei())!==a){for(e=[],n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ei())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);n!==a;)e.push(n),n=Mo,(o=li())!==a&&(u=ni())!==a&&(s=li())!==a&&(i=Ei())!==a?n=o=[o,u,s,i]:(Mo=n,n=a);e!==a?(Do=r,r=t=f(t,e)):(Mo=r,r=a)}else Mo=r,r=a;return r}function Ti(){var r,t,e,n,o;return r=Mo,(t=Qs())!==a&&(e=_i())!==a?(Do=r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(Mo=r,r=a),r}function _i(){var t,e,n,o,u;return t=Mo,(e=Su())!==a&&(n=function(){var t,e,n,o,u;t=Mo,e=[],n=Mo,46===r.charCodeAt(Mo)?(o=".",Mo++):(o=a,0===$o&&Go(Pt));o!==a&&(u=Su())!==a?n=o=[o,u]:(Mo=n,n=a);for(;n!==a;)e.push(n),n=Mo,46===r.charCodeAt(Mo)?(o=".",Mo++):(o=a,0===$o&&Go(Pt)),o!==a&&(u=Su())!==a?n=o=[o,u]:(Mo=n,n=a);e!==a&&(Do=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==a?(Do=t,o=e,u=n,Hi.push(o),t=e={type:"var",name:o,members:u,prefix:null}):(Mo=t,t=a),t===a&&(t=Mo,(e=$u())!==a&&(Do=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function xi(){var t;return(t=function(){var t,e,n,o;t=Mo,(e=Is())===a&&(e=Ns());if(e!==a)if(li()!==a)if(ai()!==a)if(li()!==a){if(n=[],Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht)),o!==a)for(;o!==a;)n.push(o),Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht));else n=a;n!==a&&(o=li())!==a&&ui()!==a?(Do=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;t===a&&(t=Mo,(e=Is())!==a&&(Do=t,e=Uo(e)),(t=e)===a&&(t=Mo,(e=Ns())!==a&&(Do=t,e=Uo(e)),t=e));return t}())===a&&(t=function(){var t,e,n,o,u,s,i,c,f,l,p,v;t=Mo,(e=Rs())===a&&(e=Os())===a&&(e=Ms())===a&&(e=Ds())===a&&(e=Ps())===a&&(e=Fs())===a&&(e=Hs())===a&&(e=$s())===a&&(e=qs());if(e!==a)if((n=li())!==a)if((o=ai())!==a)if((u=li())!==a){if(s=[],Ft.test(r.charAt(Mo))?(i=r.charAt(Mo),Mo++):(i=a,0===$o&&Go(Ht)),i!==a)for(;i!==a;)s.push(i),Ft.test(r.charAt(Mo))?(i=r.charAt(Mo),Mo++):(i=a,0===$o&&Go(Ht));else s=a;if(s!==a)if((i=li())!==a){if(c=Mo,(f=ni())!==a)if((l=li())!==a){if(p=[],Ft.test(r.charAt(Mo))?(v=r.charAt(Mo),Mo++):(v=a,0===$o&&Go(Ht)),v!==a)for(;v!==a;)p.push(v),Ft.test(r.charAt(Mo))?(v=r.charAt(Mo),Mo++):(v=a,0===$o&&Go(Ht));else p=a;p!==a?c=f=[f,l,p]:(Mo=c,c=a)}else Mo=c,c=a;else Mo=c,c=a;c===a&&(c=null),c!==a&&(f=li())!==a&&(l=ui())!==a&&(p=li())!==a?((v=ki())===a&&(v=null),v!==a?(Do=t,b=c,d=v,e={dataType:e,length:parseInt(s.join(""),10),scale:b&&parseInt(b[2].join(""),10),parentheses:!0,suffix:d},t=e):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a}else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;var b,d;if(t===a){if(t=Mo,(e=Rs())===a&&(e=Os())===a&&(e=Ms())===a&&(e=Ds())===a&&(e=Ps())===a&&(e=Fs())===a&&(e=Hs())===a&&(e=$s())===a&&(e=qs()),e!==a){if(n=[],Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht)),o!==a)for(;o!==a;)n.push(o),Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht));else n=a;n!==a&&(o=li())!==a?((u=ki())===a&&(u=null),u!==a?(Do=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,u),t=e):(Mo=t,t=a)):(Mo=t,t=a)}else Mo=t,t=a;t===a&&(t=Mo,(e=Rs())===a&&(e=Os())===a&&(e=Ms())===a&&(e=Ds())===a&&(e=Ps())===a&&(e=Fs())===a&&(e=Hs())===a&&(e=$s())===a&&(e=qs()),e!==a&&(n=li())!==a?((o=ki())===a&&(o=null),o!==a&&(u=li())!==a?(Do=t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(Mo=t,t=a)):(Mo=t,t=a))}return t}())===a&&(t=function(){var t,e,n,o;t=Mo,(e=Bs())===a&&(e=Ws())===a&&(e=Ys())===a&&(e=Gs());if(e!==a)if(li()!==a)if(ai()!==a)if(li()!==a){if(n=[],Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht)),o!==a)for(;o!==a;)n.push(o),Ft.test(r.charAt(Mo))?(o=r.charAt(Mo),Mo++):(o=a,0===$o&&Go(Ht));else n=a;n!==a&&(o=li())!==a&&ui()!==a?(Do=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Mo=t,t=a)}else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;else Mo=t,t=a;t===a&&(t=Mo,(e=Bs())===a&&(e=Ws())===a&&(e=Ys())===a&&(e=Gs()),e!==a&&(Do=t,e=Uo(e)),t=e);return t}())===a&&(t=function(){var t,e;t=Mo,(e=function(){var t,e,n,o;return t=Mo,"json"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(Ln)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="JSON"):(Mo=t,t=a)):(Mo=t,t=a),t}())!==a&&(Do=t,e=Uo(e));return t=e}())===a&&(t=function(){var t,e;t=Mo,(e=function(){var t,e,n,o;return t=Mo,"tinytext"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(En)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TINYTEXT"):(Mo=t,t=a)):(Mo=t,t=a),t}())===a&&(e=function(){var t,e,n,o;return t=Mo,"text"===r.substr(Mo,4).toLowerCase()?(e=r.substr(Mo,4),Mo+=4):(e=a,0===$o&&Go(gn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="TEXT"):(Mo=t,t=a)):(Mo=t,t=a),t}())===a&&(e=function(){var t,e,n,o;return t=Mo,"mediumtext"===r.substr(Mo,10).toLowerCase()?(e=r.substr(Mo,10),Mo+=10):(e=a,0===$o&&Go(jn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="MEDIUMTEXT"):(Mo=t,t=a)):(Mo=t,t=a),t}())===a&&(e=function(){var t,e,n,o;return t=Mo,"longtext"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(Sn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="LONGTEXT"):(Mo=t,t=a)):(Mo=t,t=a),t}());e!==a&&(Do=t,e={dataType:e});return t=e}()),t}function ki(){var t,e,n;return t=Mo,(e=Us())===a&&(e=null),e!==a&&li()!==a?((n=function(){var t,e,n,o;return t=Mo,"zerofill"===r.substr(Mo,8).toLowerCase()?(e=r.substr(Mo,8),Mo+=8):(e=a,0===$o&&Go(mn)),e!==a?(n=Mo,$o++,o=Tu(),$o--,o===a?n=void 0:(Mo=n,n=a),n!==a?(Do=t,t=e="ZEROFILL"):(Mo=t,t=a)):(Mo=t,t=a),t}())===a&&(n=null),n!==a?(Do=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(Mo=t,t=a)):(Mo=t,t=a),t}const Ii={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXCEPT:!0,EXPLAIN:!0,FALSE:!0,FETCH:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,MINUS:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,TYPE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function Ni(){return t.includeLocations?{loc:Yo(Do,Mo)}:{}}function Ri(r,t){return{type:"unary_expr",operator:r,expr:t}}function Oi(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function Ui(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Mi(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function Di(r,t){let e=r;for(let r=0;r<t.length;r++)e=Oi(t[r][1],e,t[r][3]);return e}function Pi(r){const t=Bi[r];return t||(r||null)}function Fi(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Pi(r[1])),t.add(r.join("::"))}return Array.from(t)}let Hi=[];const $i=new Set,qi=new Set,Bi={};if((e=s())!==a&&Mo===r.length)return e;throw e!==a&&Mo<r.length&&Go({type:"end"}),Vo(Ho,Fo<r.length?r.charAt(Fo):null,Fo<r.length?Yo(Fo,Fo+1):Yo(Fo,Fo))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return x})),e.d(n,"columnOrderListToSQL",(function(){return k})),e.d(n,"commonKeywordArgsToSQL",(function(){return _})),e.d(n,"commonOptionConnector",(function(){return s})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return L})),e.d(n,"commentToSQL",(function(){return j})),e.d(n,"createBinaryExpr",(function(){return f})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return E})),e.d(n,"DEFAULT_OPT",(function(){return a})),e.d(n,"escape",(function(){return l})),e.d(n,"literalToSQL",(function(){return w})),e.d(n,"columnIdentifierToSql",(function(){return d})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return y})),e.d(n,"onPartitionsToSQL",(function(){return A})),e.d(n,"replaceParams",(function(){return C})),e.d(n,"returningToSQL",(function(){return T})),e.d(n,"hasVal",(function(){return m})),e.d(n,"setParserOpt",(function(){return v})),e.d(n,"toUpper",(function(){return h})),e.d(n,"topToSQL",(function(){return b})),e.d(n,"triggerEventToSQL",(function(){return S}));var a={database:"db2",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=a;function s(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function f(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function l(r){return r}function p(){return u}function v(r){u=r}function b(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function y(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function h(r){if(r)return r.toUpperCase()}function m(r){return r}function w(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,a=r.suffix,u=r.value,s="object"===o(r)?u:r;switch(e){case"backticks_quote_string":s="`".concat(u,"`");break;case"string":s="'".concat(u,"'");break;case"regex_string":s='r"'.concat(u,'"');break;case"hex_string":s="X'".concat(u,"'");break;case"full_hex_string":s="0x".concat(u);break;case"natural_string":s="N'".concat(u,"'");break;case"bit_string":s="b'".concat(u,"'");break;case"double_quote_string":s='"'.concat(u,'"');break;case"single_quote_string":s="'".concat(u,"'");break;case"boolean":case"bool":s=u?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(t||":").concat(u),t=null;break;case"origin":s=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":s="N'".concat(u,"'");break;case"unicode_string":s="U&'".concat(u,"'")}var i=[];return t&&i.push(h(t)),i.push(s),a&&("string"==typeof a&&i.push(a),"object"===o(a)&&(a.collate?i.push(it(a.collate)):i.push(w(a)))),s=i.join(" "),n?"(".concat(s,")"):s}}function L(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():w(n)].filter(m)}function C(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var a=t[n];if("object"!==o(a)||"param"!==a.type)return r(a,e);if(void 0===e[a.value])throw new Error("no value for parameter :".concat(a.value," found"));return t[n]=c(e[a.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function A(r){var t=r.type,e=r.partitions;return[h(t),"(".concat(e.map((function(r){if("range"!==r.type)return w(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(w(t)," ").concat(h(n)," ").concat(w(e))})).join(", "),")")].join(" ")}function E(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,a=r.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),a&&a.length&&(u+=" ".concat(a.join(" "))),"".concat(t).concat(u)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=h(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(m).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function j(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(w(o)),t.join(" ")}}function S(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[h(t)];if(e){var o=e.keyword,a=e.columns;n.push(h(o),a.map(dt).join(", "))}return n.join(" ")})).join(" OR ")}function T(r){return r?["RETURNING",r.columns.map(At).filter(m).join(", ")].join(" "):""}function _(r){return r?[h(r.keyword),h(r.args)]:[]}function x(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,a=r.parentheses,u=h(e);return a&&(u+="(".concat(w(n),", ").concat(w(o),")")),u}}function k(r){if(r)return r.map(wt).filter(m).join(", ")}function I(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function R(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),h(e)]}function O(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),a=[];switch(a.push(o),o){case"KEY_BLOCK_SIZE":n&&a.push(n),a.push(w(e));break;case"BTREE":case"HASH":a.length=0,a.push.apply(a,I(R(r)));break;case"WITH PARSER":a.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":a.shift(),a.push(j(r));break;case"DATA_COMPRESSION":a.push(n,h(e.value),A(e.on));break;default:a.push(n,w(e))}return a.filter(m).join(" ")}}function U(r){return r?r.map(O):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,a=r.definition,u=r.on,s=r.with,i=[];if(i.push.apply(i,I(R(e))),a&&a.length){var c="CHECK"===h(t)?"(".concat(at(a[0]),")"):"(".concat(a.map((function(r){return at(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),s&&i.push("WITH (".concat(U(s).join(", "),")")),u&&i.push("ON [".concat(u,"]")),i}function D(r){var t=r.operator||r.op,e=at(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},a=[Array.isArray(r.left)?r.left.map(at).join(", "):at(r.left),t,e,h(o.type),at(o.value)].filter(m).join(" ");return[r.parentheses?"(".concat(a,")"):a].join(" ")}function P(r){return function(r){if(Array.isArray(r))return F(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return F(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?F(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function H(r){return r?[r.prefix.map(w).join(" "),at(r.value),r.suffix.map(w).join(" ")]:[]}function $(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(H(n)),P(H(e))).filter(m).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",at(e[0])):i("LIMIT",e.map(at).join("".concat("offset"===t?" ":"").concat(h(t)," ")))}(r):"";var t,e,n}function q(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(dt).join(", "),")"):"";return"".concat("default"===t.type?y(t.value):w(t)).concat(o," AS (").concat(at(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function B(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=h(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?y(e):at(e))}return n.filter(m).join(" ")}}function W(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,a=r.for,u=r.from,c=r.for_sys_time_as_of,f=void 0===c?{}:c,l=r.locking_read,p=r.groupby,v=r.having,d=r.into,y=void 0===d?{}:d,L=r.isolation,C=r.limit,A=r.options,E=r.orderby,g=r.parentheses_symbol,j=r.qualify,S=r.top,T=r.window,_=r.with,x=r.where,k=[q(_),"SELECT",h(t)];Array.isArray(A)&&k.push(A.join(" ")),k.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[h(t)];return e&&n.push("(".concat(e.map(at).join(", "),")")),n.filter(m).join(" ")}}(o),b(S),gt(e,u));var I=y.position,N="";I&&(N=s("INTO",B,y)),"column"===I&&k.push(N),k.push(s("FROM",cr,u)),"from"===I&&k.push(N);var R=f||{},O=R.keyword,U=R.expr;k.push(s(O,at,U)),k.push(s("WHERE",at,x)),p&&(k.push(i("GROUP BY",ut(p.columns).join(", "))),k.push(ut(p.modifiers).join(", "))),k.push(s("HAVING",at,v)),k.push(s("QUALIFY",at,j)),k.push(s("WINDOW",at,T)),k.push(st(E,"order by")),k.push(it(n)),k.push($(C)),L&&k.push(s(L.keyword,w,L.expr)),k.push(h(l)),"end"===I&&k.push(N),k.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[h(r.type),h(e)];return t?"".concat(n.join(" "),"(").concat(at(t),")"):n.join(" ")}}(a));var M=k.filter(m).join(" ");return g?"(".concat(M,")"):M}function Y(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){s=!0,a=r},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=Y(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,a={},u=o.value;for(var s in o)"value"!==s&&"keyword"!==s&&(a[s]=o[s]);var i=[dt(a)],c="";u&&(c=at(u),i.push("=",c)),e.push(i.filter(m).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function Q(r){if("select"===r.type)return W(r);var t=r.map(at);return"(".concat(t.join("), ("),")")}function X(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(y).join(", "));else{var e=r.value;t.push(e.map(at).join(", "))}return t.push(")"),t.filter(m).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(dt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[h(e)];switch(n){case"origin":o.push(w(t));break;case"update":o.push("UPDATE",s("SET",V,t.set),s("WHERE",at,t.where))}return o.filter(m).join(" ")}function J(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(m).join(" ")}function Z(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,a=r.columns,u=r.conflict,i=r.values,c=r.where,f=r.on_duplicate_update,l=r.partition,p=r.returning,v=r.set,b=f||{},d=b.keyword,y=b.set,L=[h(e),h(o),cr(t),X(l)];return Array.isArray(a)&&L.push("(".concat(a.map(w).join(", "),")")),L.push(s(Array.isArray(i)?"VALUES":"",Q,i)),L.push(s("ON CONFLICT",J,u)),L.push(s("SET",V,v)),L.push(s("WHERE",at,c)),L.push(s(d,V,y)),L.push(T(p)),L.filter(m).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",at(t),h(e),at(n)].filter(m).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(h(t),"(").concat(n&&at(n)||"",")"),s("AS","string"==typeof e?y:at,e),s(h(o&&o.keyword),y,o&&o.as)].filter(m).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,a=r.type,u=[at(n),"FOR",dt(e),D(o)],s=["".concat(h(a),"(").concat(u.join(" "),")")];return t&&s.push("AS",y(t)),s.join(" ")}(r);default:return""}}function ar(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,a=r.parentheses,u=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(h(t),"(".concat(y(n)),"(".concat(o.map(at).filter(m).join(", "),"))"));break;case"spatial_window_max_cells":s.push(h(t),"=",at(e));break;case"index":s.push(h(u),h(t),a?"(".concat(e.map(y).join(", "),")"):"= ".concat(y(e)));break;default:s.push(at(e))}return s.filter(m).join(" ")}}function ur(r,t){var e=r.name,n=r.symbol;return[h(e),n,t].filter(m).join(" ")}function sr(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",at(r.of));break;case"from_to":t.push("FROM",at(r.from),"TO",at(r.to));break;case"between_and":t.push("BETWEEN",at(r.between),"AND",at(r.and));break;case"contained":t.push("CONTAINED","IN",at(r.in))}return t.filter(m).join(" ")}function ir(r){if("UNNEST"===h(r.type))return nr(r);var t,e,n,o,a=r.table,u=r.db,i=r.as,c=r.expr,f=r.operator,l=r.prefix,p=r.schema,v=r.server,b=r.suffix,d=r.tablesample,C=r.temporal_table,A=r.table_hint,E=y(v),g=y(u),j=y(p),S=a&&y(a);if(c)switch(c.type){case"values":var T=c.parentheses,_=c.values,x=c.prefix,k=[T&&"(","",T&&")"],I=Q(_);x&&(I=I.split("(").slice(1).map((function(r){return"".concat(h(x),"(").concat(r)})).join("")),k[1]="VALUES ".concat(I),S=k.filter(m).join("");break;case"tumble":S=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,a=[y(t.expr.db),y(t.expr.schema),y(t.expr.table)].filter(m).join("."),u="DESCRIPTOR(".concat(dt(e.expr),")"),s=["TABLE(TUMBLE(TABLE ".concat(ur(t,a)),ur(e,u)],i=ur(o,rr(o.expr));return n&&n.expr?s.push(i,"".concat(ur(n,rr(n.expr)),"))")):s.push("".concat(i,"))")),s.filter(m).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return L(r).join(" ")})).join(", "),S="".concat(h(e),"(").concat(h(n),"(").concat(o,"))");break;default:S=at(c)}var N=[[E,g,j,S=[h(l),S,h(b)].filter(m).join(" ")].filter(m).join(".")];if(d){var R=["TABLESAMPLE",at(d.expr),w(d.repeatable)].filter(m).join(" ");N.push(R)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[h(t),sr(e)].filter(m).join(" ")}}(C),s("AS","string"==typeof i?y:at,i),or(f)),A&&N.push(h(A.keyword),"(".concat(A.expr.map(ar).filter(m).join(", "),")"));var O=N.filter(m).join(" ");return r.parentheses?"(".concat(O,")"):O}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var a=[],u=[],i=!0===e?1:e.length,c=0;c++<i;)a.push("("),u.push(")");var f=n&&n.length>0?cr([""].concat(tr(n))):"";return a.join("")+o+u.join("")+f}return o}var l=r[0],p=[];if("dual"===l.type)return"DUAL";p.push(ir(l));for(var v=1;v<r.length;++v){var b=r[v],d=b.on,y=b.using,L=b.join,C=[];C.push(L?" ".concat(h(L)):","),C.push(ir(b)),C.push(s("ON",at,d)),y&&C.push("USING (".concat(y.map(w).join(", "),")")),p.push(C.filter(m).join(" "))}return p.filter(m).join("")}function fr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var a=w(n);switch(t){case"partition by":case"default collate":a=at(n);break;case"options":a="(".concat(n.map((function(r){return[r.keyword,r.symbol,at(r.value)].join(" ")})).join(", "),")");break;case"cluster by":a=n.map(at).join(", ")}return o.push(a),o.filter(m).join(" ")}function lr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[y(t.db),y(t.table)].filter(m).join(".");return"".concat(h(e)," ").concat(n);case"column":return"COLUMN ".concat(dt(t));default:return"".concat(h(e)," ").concat(w(t))}}function pr(r){var t=r.keyword,e=r.expr;return[h(t),w(e)].filter(m).join(" ")}function vr(r){var t=r.name,e=r.value;return["@".concat(t),"=",at(e)].filter(m).join(" ")}function br(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var a=at(t),u=at(e);return[a,h(n),u].filter(m).join(" ")}function dr(r){var t,e,n,o,a=r.keyword,u=r.suffix,i="";switch(h(a)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[s("IN",w,e&&e.right),s("FROM",cr,n),$(o)].filter(m).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===h(t.op)?s("LIKE",w,t.right):s("WHERE",at,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=s("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(m).join(" ")}}(r);break;case"CREATE":i=s("",ir,r[u]);break;case"VAR":i=ot(r.var),a=""}return["SHOW",h(a),h(u),i].filter(m).join(" ")}var yr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,a=r.type,u=t.expr,s=t.orderby;return[h(a),h(n),[[y(o.schema),y(o.name)].filter(m).join("."),"(".concat(u.map(Jr).join(", ")).concat(s?[" ORDER","BY",s.map(Jr).join(", ")].join(" "):"",")")].filter(m).join(""),zr(e)].filter(m).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,a=r.expr,u=void 0===a?[]:a,s=h(t),i=cr(e),c=u.map(at);return[s,"TABLE",h(n),w(o),i,c.join(", ")].filter(m).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[h(r.type),h(e),y(n),zr(t)].filter(m).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[h(r.type),h(e),[y(n.schema),y(n.name)].filter(m).join("."),zr(t)].filter(m).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[h(r.type),h(n),[[y(o.schema),y(o.name)].filter(m).join("."),t&&"(".concat(t.expr?t.expr.map(Jr).join(", "):"",")")].filter(m).join(""),zr(e)].filter(m).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,a=r.view,u=r.with,s=h(t),i=ir(a),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(dt).join(", "),")"));n&&c.push("WITH ".concat(n.map(h).join(", ")));c.push("AS",W(o)),u&&c.push(h(u));return c.filter(m).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[h(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,a=r.schema;return[h(t),h(e),at(n),h(o),y(a)].filter(m).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,a=r.args,u=r.options,s=[h(t),h(e),h(n)],i=[y(o.schema),o.name].filter(m).join("."),c="".concat(a.expr.map(Jr).join(", ")).concat(a.orderby?[" ORDER","BY",a.orderby.map(Jr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(u.map(Xr).join(", "),")")),s.filter(m).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,a=r.as,u=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,f=r.ignore_replace,l=r.replace,v=r.partition_of,b=r.query_expr,d=r.unlogged,y=r.with,L=[h(t),h(l),h(u),h(d),h(e),h(s),cr(n)];if(o){var C=o.type,A=cr(o.table);return L.push(h(C),A),L.filter(m).join(" ")}if(v)return L.concat([Gr(v)]).filter(m).join(" ");i&&L.push("(".concat(i.map(Wr).join(", "),")"));if(c){var E=p().database,g=E&&"sqlite"===E.toLowerCase()?", ":" ";L.push(c.map(fr).join(g))}if(y){var j=y.map((function(r){return[w(r.keyword),h(r.symbol),w(r.value)].join(" ")})).join(", ");L.push("WITH (".concat(j,")"))}L.push(h(f),h(a)),b&&L.push(hr(b));return L.filter(m).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,a=r.execute,u=r.for_each,s=r.from,i=r.location,c=r.keyword,f=r.or,l=r.type,p=r.table,v=r.when,b=[h(l),h(f),h(e),h(c),y(t),h(i)],d=S(o);b.push(d,"ON",ir(p)),s&&b.push("FROM",ir(s));b.push.apply(b,$r(_(n)).concat($r(_(u)))),v&&b.push(h(v.type),at(v.cond));return b.push(h(a.keyword),Hr(a.expr)),b.filter(m).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,a=r.type,u=r.table,i=r.if_not_exists,c=r.temporary,f=r.trigger,l=r.events,p=r.order,v=r.time,b=r.when,d=[h(a),h(c),at(t),h(n),h(i),ir(f),h(v),l.map((function(r){var t=[h(r.keyword)],e=r.args;return e&&t.push(h(e.keyword),e.columns.map(dt).join(", ")),t.join(" ")})),"ON",ir(u),h(e&&e.keyword),h(e&&e.args),p&&"".concat(h(p.keyword)," ").concat(y(p.trigger)),s("WHEN",at,b),h(o.prefix)];switch(o.type){case"set":d.push(s("SET",V,o.expr));break;case"multiple":d.push(mr(o.expr.ast))}return d.push(h(o.suffix)),d.filter(m).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,a=r.schema,u=r.type,i=r.with,c=r.version;return[h(u),h(o),h(n),w(t),h(i),s("SCHEMA",w,a),s("VERSION",w,c),s("FROM",w,e)].filter(m).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,a=r.args,u=r.returns,s=r.options,i=r.last,c=[h(t),h(e),h(n)],f=[w(o.schema),o.name.map(w).join(".")].filter(m).join("."),l=a.map(Jr).filter(m).join(", ");return c.push("".concat(f,"(").concat(l,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),Array.isArray(n)?"(".concat(n.map(Lt).join(", "),")"):Vr(n)].filter(m).join(" ")}(u),s.map(Qr).join(" "),i),c.filter(m).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,a=r.include,u=r.index_columns,i=r.index_type,c=r.index_using,f=r.index,l=r.on,p=r.index_options,v=r.algorithm_option,b=r.lock_option,d=r.on_kw,L=r.table,C=r.tablespace,A=r.type,E=r.where,g=r.with,j=r.with_before_where,S=g&&"WITH (".concat(U(g).join(", "),")"),T=a&&"".concat(h(a.keyword)," (").concat(a.columns.map((function(r){return"string"==typeof r?y(r):at(r)})).join(", "),")"),_=f;f&&(_="string"==typeof f?y(f):[y(f.schema),y(f.name)].filter(m).join("."));var x=[h(A),h(i),h(n),h(o),h(t),_,h(d),ir(L)].concat($r(R(c)),["(".concat(k(u),")"),T,U(p).join(" "),zr(v),zr(b),s("TABLESPACE",w,C)]);j?x.push(S,s("WHERE",at,E)):x.push(s("WHERE",at,E),S);return x.push(s("ON",at,l),s("FILESTREAM_ON",w,e)),x.filter(m).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,a=r.if_not_exists,u=r.create_definitions,s=[h(t),h(o),h(e),h(a),cr(n)];u&&s.push(u.map(Wr).join(" "));return s.filter(m).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,a=r.create_definitions,u=r[e],s=u.db,i=u.schema,c=[w(s),i.map(w).join(".")].filter(m).join("."),f=[h(t),h(n),h(e),h(o),c];a&&f.push(a.map(fr).join(" "));return f.filter(m).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,a=r.keyword,u=r.recursive,s=r.replace,i=r.select,c=r.sql_security,f=r.temporary,l=r.type,p=r.view,v=r.with,b=r.with_options,w=p.db,C=p.schema,A=p.view,E=[y(w),y(C),y(A)].filter(m).join(".");return[h(l),h(s),h(f),h(u),t&&"ALGORITHM = ".concat(h(t)),at(n),c&&"SQL SECURITY ".concat(h(c)),h(a),h(o),E,e&&"(".concat(e.map(d).join(", "),")"),b&&["WITH","(".concat(b.map((function(r){return L(r).join(" ")})).join(", "),")")].join(" "),"AS",hr(i),h(v)].filter(m).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,a=r.target,u=r.create_definitions,s=[h(n),h(o),[y(e.schema),y(e.name)].filter(m).join("."),h(t),E(a)];if(u&&u.length>0){var i,c=[],f=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=qr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){s=!0,a=r},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}(u);try{for(f.s();!(i=f.n()).done;){var l=i.value,p=l.type;switch(p){case"collate":c.push(at(l));break;case"default":c.push(h(p),at(l.value));break;case"constraint":c.push(Rr(l))}}}catch(r){f.e(r)}finally{f.f()}s.push(c.filter(m).join(" "))}return s.filter(m).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,a=r.resource,u=[h(r.type),h(n),[y(o.schema),y(o.name)].filter(m).join("."),h(t),h(a)];if(e){var s=[];switch(a){case"enum":case"range":s.push(at(e));break;default:s.push("(".concat(e.map(Wr).join(", "),")"))}u.push(s.filter(m).join(" "))}return u.filter(m).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,a=r.keyword,u=r.lock_option,i=r.password_options,c=r.require,f=r.resource_options,l=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[xr(t)];return e&&n.push(h(e.keyword),e.auth_plugin,w(e.value)),n.filter(m).join(" ")})).join(", "),v=[h(l),h(a),h(o),p];n&&v.push(h(n.keyword),n.value.map(xr).join(", "));v.push(s(c&&c.keyword,at,c&&c.value)),f&&v.push(h(f.keyword),f.value.map((function(r){return at(r)})).join(" "));i&&i.forEach((function(r){return v.push(s(r.keyword,at,r.value))}));return v.push(w(u),j(e),w(t)),v.filter(m).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[h(r.type),h(e),lr(n),pr(t)].filter(m).join(" ")},select:W,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),at(n)].filter(m).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,a=r.orderby,u=r.with,i=r.limit,c=r.returning,f=[q(u),"DELETE"],l=gt(t,e);return f.push(l),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||f.push(cr(n))),f.push(s("FROM",cr,e)),f.push(s("WHERE",at,o)),f.push(st(a,"order by")),f.push($(i)),f.push(T(c)),f.filter(m).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[h(t),ir(e),(n||[]).map(vr).filter(m).join(", ")].filter(m).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[h(t)],a=[e];n&&a.push("(".concat(at(n).join(", "),")"));return o.push(a.join("")),o.filter(m).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[h(t),W(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,a=r.stmts;return[e,h(t),n,"IN",mr([o]),"LOOP",mr(a),"END LOOP",e].filter(m).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,a=r.orderby,u=r.with,i=r.limit,c=r.returning;return[q(u),"UPDATE",cr(e),s("SET",V,n),s("FROM",cr,t),s("WHERE",at,o),st(a,"order by"),$(i),T(c)].filter(m).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,a=r.prefix,u=r.go,s=r.semicolons,i=r.suffix,c=[h(r.type),at(t),w(a),"".concat(Ar(o.ast||o)).concat(s[0]),h(u)];n&&c.push(n.map((function(r){return[h(r.type),at(r.boolean_expr),"THEN",Ar(r.then.ast||r.then),r.semicolon].filter(m).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Ar(e.ast||e)).concat(s[1]));return c.push(w(i)),c.filter(m).join(" ")},insert:Z,drop:Tr,truncate:Tr,replace:Z,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[h(t)],a=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,a=r.datatype,u=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,f=[[t,e].filter(m).join(""),h(n),h(o)];switch(c){case"variable":f.push(yt(a),at(r.collate),h(u)),i&&f.push(h(i.keyword),at(i.value));break;case"cursor":f.push(h(s));break;case"table":f.push(h(s),"(".concat(i.map(Wr).join(", "),")"))}return f.filter(m).join(" ")})).join("".concat(n," "));return o.push(a),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=h(t),o=y(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var a,u=Er(e);try{for(u.s();!(a=u.n()).done;){var s=a.value.map(ir);n.push(s.join(" TO "))}}catch(r){u.e(r)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=at(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=h(t);return"".concat(n," ").concat(y(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=h(t),a=e.map(at).join(", ");return[o,h(n),a].filter(m).join(" ")},lock:_r,unlock:_r,show:dr,grant:kr,revoke:kr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return br(t);case"return":return function(r){var t=r.type,e=r.expr;return[h(t),at(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,a=[h(t),h(e)];n&&a.push([w(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(m).join(""),n.expr.map((function(r){return at(r)})).join(", "));o&&a.push(h(o.type),h(o.option),o.symbol,o.expr.map((function(r){return at(r)})).join(", "));return a.filter(m).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,a=[w(e),h(n)];return o&&a.push(o.map(w).join(", ")),a.filter(m).join(" ")}};function hr(r){if(!r)return"";for(var t=yr[r.type],e=r,n=e._parentheses,o=e._orderby,a=e._limit,u=[n&&"(",t(r)];r._next;){var s=yr[r._next.type],i=h(r.set_op);u.push(i,s(r._next)),r=r._next}return u.push(n&&")",st(o,"order by"),$(a)),u.filter(m).join(" ")}function mr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],a=hr(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),t.push(a)}return t.join(" ; ")}var wr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Lr(r){var t=r&&r.ast?r.ast:r;if(!wr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Cr(r){return Array.isArray(r)?(r.forEach(Lr),mr(r)):(Lr(r),hr(r))}function Ar(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Cr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Cr(r)}function Er(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=jr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){s=!0,a=r},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function gr(r){return function(r){if(Array.isArray(r))return Sr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||jr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jr(r,t){if(r){if("string"==typeof r)return Sr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Sr(r,t):void 0}}function Sr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Tr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,a=r.suffix,u=[h(t),h(e),h(o)];switch(e){case"table":u.push(cr(n));break;case"trigger":u.push([n[0].schema?"".concat(y(n[0].schema),"."):"",y(n[0].trigger)].filter(m).join(""));break;case"database":case"schema":case"procedure":u.push(y(n));break;case"view":u.push(cr(n),r.options&&r.options.map(at).filter(m).join(" "));break;case"index":u.push.apply(u,[dt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(at).filter(m).join(" ")]));break;case"type":u.push(n.map(dt).join(", "),r.options&&r.options.map(at).filter(m).join(" "))}return a&&u.push(a.map(at).filter(m).join(" ")),u.filter(m).join(" ")}function _r(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),h(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var a,u=[],s=Er(n);try{var i=function(){var r=a.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return h(e[r])})).filter(m).join(" "))}u.push(n.join(" "))};for(s.s();!(a=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[u.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(m).join(" ")}function xr(r){var t=r.name,e=r.host,n=[w(t)];return e&&n.push("@",w(e)),n.join("")}function kr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,a=r.on,u=r.to_from,s=r.user_or_roles,i=r.with,c=[h(t),w(e)],f=o.map((function(r){var t=r.priv,e=r.columns,n=[at(t)];return e&&n.push("(".concat(e.map(dt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(f),a)switch(c.push("ON"),n){case"priv":c.push(w(a.object_type),a.priv_level.map((function(r){return[y(r.prefix),y(r.name)].filter(m).join(".")})).join(", "));break;case"proxy":c.push(xr(a))}return c.push(h(u),s.map(xr).join(", ")),c.push(w(i)),c.filter(m).join(" ")}function Ir(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Rr(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,a=r.keyword,u=r.reference_definition,i=r.for,c=r.with_values,f=[],l=p().database;f.push(h(a)),f.push(y(t));var v=h(e);return"sqlite"===l.toLowerCase()&&"UNIQUE KEY"===v&&(v="UNIQUE"),f.push(v),f.push("sqlite"!==l.toLowerCase()&&y(o)),f.push.apply(f,Ir(M(r))),f.push.apply(f,Ir(ht(u))),f.push(h(n)),f.push(s("FOR",y,i)),f.push(w(c)),f.filter(m).join(" ")}}function Or(r){if(r){var t=r.type;return"rows"===t?[h(t),at(r.expr)].filter(m).join(" "):at(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),Or(o)].filter(m).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,a=r.parentheses,u=h(o);if("WINDOW"===u)return"OVER ".concat(Ur(t));if("ON UPDATE"===u){var s="".concat(h(o)," ").concat(h(n)),i=at(e)||[];return a&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return h(t);for(var e=r.array,n=e.dimension,o=e.length,a=[],u=0;u<n;u++)a.push("["),o&&o[u]&&a.push(w(o[u])),a.push("]");return a.join("")}function Fr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,a=r.as,u=r.offset,s=r.parentheses,i=vt({expr:e,offset:u}),c=[],f=0,l=t.length;f<l;++f){var p=t[f],v=p.angle_brackets,b=p.length,d=p.dataType,L=p.parentheses,C=p.quoted,A=p.scale,E=p.suffix,g=p.expr,j=g?at(g):"";null!=b&&(j=A?"".concat(b,", ").concat(A):b),L&&(j="(".concat(j,")")),v&&(j="<".concat(j,">")),E&&E.length&&(j+=" ".concat(E.map(w).join(" ")));var S="::",T="",_=[];"as"===o&&(0===f&&(i="".concat(h(n),"(").concat(i)),T=")",S=" ".concat(o.toUpperCase()," ")),0===f&&_.push(i);var x=Pr(p);_.push(S,C,d,C,x,j,T),c.push(_.filter(m).join(""))}a&&c.push(" AS ".concat(y(a)));var k=c.filter(m).join("");return s?"(".concat(k,")"):k}function Hr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,a=r.parentheses,u=r.within_group,s=r.over,i=r.suffix,c=Dr(s),f=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[h(t),h(e),"(".concat(st(n,"order by"),")")].filter(m).join(" ")}(u),l=at(i),p=[w(n.schema),n.name.map(w).join(".")].filter(m).join(".");if(!t)return[p,f,c].filter(m).join(" ");var v=r.separator||", ";"TRIM"===h(p)&&(v=" ");var b=[p];b.push(!1===o?" ":"(");var d=at(t);if(Array.isArray(v)){for(var y=d[0],L=1,C=d.length;L<C;++L)y=[y,d[L]].join(" ".concat(at(v[L-1])," "));b.push(y)}else b.push(d.join(v));return!1!==o&&b.push(")"),b.push(bt(e)),b=[b.join(""),l].filter(m).join(" "),[a?"(".concat(b,")"):b,f,c].filter(m).join(" ")}function $r(r){return function(r){if(Array.isArray(r))return Br(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||qr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(r,t){if(r){if("string"==typeof r)return Br(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Br(r,t):void 0}}function Br(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Wr(r){if(!r)return[];var t,e,n,o,a=r.resource;switch(a){case"column":return Lt(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(h(n)),e.push(o),e.push.apply(e,I(M(t))),e.filter(m).join(" ");case"constraint":return Rr(r);case"sequence":return[h(r.prefix),at(r.value)].filter(m).join(" ");default:throw new Error("unknown resource = ".concat(a," type"))}}function Yr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(w(r.from),")"),"TO","(".concat(w(r.to),")"));break;case"in":t.push("IN","(".concat(at(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(w(r.modulus),", REMAINDER ").concat(w(r.remainder),")"))}return t.filter(m).join(" ")}function Gr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,a=[h(t),ir(e),h(n.keyword),Yr(n.expr)];return o&&a.push("TABLESPACE",w(o)),a.filter(m).join(" ")}function Vr(r){return r.dataType?E(r):[y(r.db),y(r.schema),y(r.table)].filter(m).join(".")}function Qr(r){var t=r.type;switch(t){case"as":return[h(t),r.symbol,hr(r.declare),h(r.begin),mr(r.expr),h(r.end),r.symbol].filter(m).join(" ");case"set":return[h(t),r.parameter,h(r.value&&r.value.prefix),r.value&&r.value.expr.map(at).join(", ")].filter(m).join(" ");case"return":return[h(t),at(r.expr)].filter(m).join(" ");default:return at(r)}}function Xr(r){var t=r.type,e=r.symbol,n=r.value,o=[h(t),e];switch(h(t)){case"SFUNC":o.push([y(n.schema),n.name].filter(m).join("."));break;case"STYPE":case"MSTYPE":o.push(E(n));break;default:o.push(at(n))}return o.filter(m).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",w(t),"VALUES",h(e.type),"(".concat(w(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,a=r.if_exists,u=r.old_column,s=r.prefix,i=r.resource,c=r.symbol,f=r.suffix,l="",p=[];switch(i){case"column":p=[Lt(r)];break;case"index":p=M(r),l=r[i];break;case"table":case"schema":l=y(r[i]);break;case"aggregate":case"function":case"domain":case"type":l=y(r[i]);break;case"algorithm":case"lock":case"table-option":l=[c,h(r[i])].filter(m).join(" ");break;case"constraint":l=y(r[i]),p=[Wr(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":l=y(r[i]);break;default:l=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var v=[h(t),h(o),h(n),h(a),u&&dt(u),h(s),l&&l.trim(),p.filter(m).join(" ")];return f&&v.push(h(f.keyword),f.expr&&dt(f.expr)),v.filter(m).join(" ")}function Jr(r){var t=r.default&&[h(r.default.keyword),at(r.default.value)].join(" ");return[h(r.mode),r.name,E(r.type),t].filter(m).join(" ")}function Zr(r){return(Zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(h(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var a=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(m).join(", "):at(n);return e?"[".concat(a,"]"):o?"(".concat(a,")"):a}(r);default:return""}}function tt(r){var t=r.definition,e=[h(r.keyword)];return t&&"object"===Zr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(m).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,a=at(t.expr);a=Array.isArray(a)?a.join(", "):a;var u=r.name,s=Dr(n);t.distinct&&(a=["DISTINCT",a].join(" ")),t.separator&&t.separator.delimiter&&(a=[a,w(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(a=[a,at(t.separator.expr)].join(" ")),t.orderby&&(a=[a,st(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(a=[a,h(t.separator.keyword),w(t.separator.value)].filter(m).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(at(e.where),")"):"";return["".concat(u,"(").concat(a,")"),i,s,c].filter(m).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,a=t.having,u="".concat(h(e),"(").concat(at(o));return a&&(u="".concat(u," HAVING ").concat(h(a.prefix)," ").concat(at(a.expr))),[u="".concat(u,")"),Dr(n)].filter(m).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,a=r.separator,u=void 0===a?", ":a;return[e,"(",t?at(t).join(u):"",")",o&&" ",o].filter(m).join("")}(r),Dr(t)].filter(m).join(" ")},array:tt,assign:br,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(at(n));for(var a=0,u=e.length;a<u;++a)t.push(e[a].type.toUpperCase()),e[a].cond&&(t.push(at(e[a].cond)),t.push("THEN")),t.push(at(e[a].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Fr,collate:it,column_ref:dt,column_definition:Lt,datatype:E,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,a=t.source,u=["".concat(h(e),"(").concat(h(n)),"FROM",h(o),at(a)];return"".concat(u.filter(m).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[h(t),e,at(n)].filter(m).join(" ")}(t[r])})).filter(m).join(", ");return"".concat(h(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,a=r.mode,u=[h(o),"(".concat(n.map((function(r){return dt(r)})).join(", "),")")].join(" "),s=[h(t),["(",at(r.expr),a&&" ".concat(w(a)),")"].filter(m).join("")].join(" ");return[u,s,Ct(e)].filter(m).join(" ")},function:Hr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,a=n.map(at).join(", ");return[o?"(".concat(a,")"):a,"->",at(e)].join(" ")},insert:hr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[h(t),e.map((function(r){return at(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,a=[at(e),"VALUE",at(n)];return o&&a.push("ON","NULL",at(o)),a.filter(m).join(" ")},json_visitor:function(r){return[r.symbol,at(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,at(t.expr)].filter(m).join(" ")},show:dr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[w(e.schema),e.name.map(w).join(".")].filter(m).join(".");return["".concat(o,"(").concat(at(n).join(", "),")"),"AS",Hr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,a=r.quoted,u=r.suffix,s=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return u&&(c+=u),s.push(c),[a,s.join(" "),a].filter(m).join("")}function at(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var a=o[n];t[a]=e[a]}}var u=t.type;return"expr"===u?at(t.expr):nt[u]?nt[u](t):w(t)}}function ut(r){return r?(Array.isArray(r)||(r=[r]),r.map(at)):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=h(t);switch(n){case"ORDER BY":e=r.map((function(r){return[at(r.expr),r.type||"ASC",h(r.nulls)].filter(m).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return at(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,a=e.value,u=[h(t)];return a||u.push(o),u.push(Array.isArray(n)?n.map(w).join("."):w(n)),a&&u.push(o),u.push(at(a)),u.filter(m).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function ft(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||lt(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lt(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function vt(r,t){if("string"==typeof r)return y(r,t);var e=r.expr,n=r.offset,o=r.suffix,a=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),w(r.value),"".concat(r.name?")":""),"]"].filter(m).join("")})).join("");return[at(e),a,o].filter(m).join("")}function bt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=lt(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){s=!0,a=r},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,a=o.brackets?"[".concat(w(o.index),"]"):"".concat(o.notation).concat(w(o.index));o.property&&(a="".concat(a,".").concat(w(o.property))),e.push(a)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function dt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,a=r.db,u=r.isDual,i=r.notations,c=void 0===i?[]:i,f=r.options,l=r.schema,p=r.table,v=r.parentheses,b=r.suffix,d=r.order_by,w=r.subFields,L=void 0===w?[]:w,C="*"===n?"*":vt(n,u),A=[a,l,p].filter(m).map((function(r){return"".concat("string"==typeof r?y(r):at(r))})),E=A[0];if(E){for(var g=1;g<A.length;++g)E="".concat(E).concat(c[g]||".").concat(A[g]);C="".concat(E).concat(c[g]||".").concat(C)}var j=[C=["".concat(C).concat(bt(t))].concat(ft(L)).join("."),it(o),at(f),s("AS",at,e)];j.push("string"==typeof b?h(b):at(b)),j.push(h(d));var S=j.filter(m).join(" ");return v?"(".concat(S,")"):S}function yt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,a=r.expr,u=E({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(a&&(u+=at(a)),r.array){var s=Pr(r);u+=[/^\[.*\]$/.test(s)?"":" ",s].join("")}return u}}function ht(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,a=r.table,u=r.on_action;return t.push(h(n)),t.push(cr(a)),t.push(e&&"(".concat(e.map((function(r){return at(r)})).join(", "),")")),t.push(h(o)),u.map((function(r){return t.push(h(r.type),at(r.value))})),t.filter(m)}function mt(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,a=r.comment,u=r.constraint,i=r.collate,c=r.storage,f=r.using,l=r.default_val,v=r.generated,b=r.auto_increment,d=r.unique,y=r.primary_key,C=r.column_format,A=r.reference_definition,E=[h(e&&e.action),h(e&&e.value)].filter(m).join(" ");if(v||t.push(E),l){var g=l.type,S=l.value;t.push(g.toUpperCase(),at(S))}var T=p().database;return u&&t.push(h(u.keyword),w(u.constraint)),t.push(Rr(o)),t.push(function(r){if(r)return[h(r.value),"(".concat(at(r.expr),")"),h(r.storage_type)].filter(m).join(" ")}(v)),v&&t.push(E),t.push(x(b),h(y),h(d),j(a)),t.push.apply(t,ft(L(n))),"sqlite"!==T.toLowerCase()&&t.push(at(i)),t.push.apply(t,ft(L(C))),t.push.apply(t,ft(L(c))),t.push.apply(t,ft(ht(A))),t.push(s("USING",at,f)),t.filter(m).join(" ")}function wt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,a=r.order_by,u="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return u.collate=null,[at(u),at(e),o,h(a),h(n)].filter(m).join(" ")}function Lt(r){var t=[],e=dt(r.column),n=yt(r.definition);return t.push(e),t.push(n),t.push(mt(r)),t.filter(m).join(" ")}function Ct(r){return r?"object"===ct(r)?["AS",at(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?y(r):d(r)].join(" "):""}function At(r,t){var e=r.expr,n=r.type;if("cast"===n)return Fr(r);t&&(e.isDual=t);var o=at(e),a=r.expr_list;if(a){var u=[o],s=a.map((function(r){return At(r,t)})).join(", ");return u.push([h(n),n&&"(",s,n&&")"].filter(m).join("")),u.filter(m).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(bt(e.array_index))),[o,Ct(r.as)].filter(m).join(" ")}function Et(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=Et(t);return r.map((function(r){return At(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=ut(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",a=t.join(o);return e?"(".concat(a,")"):a},nt.select=function(r){var t="object"===et(r._next)?hr(r):W(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",a="".concat(t).concat(o).concat(at(n));return e?"(".concat(a,")"):a},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[w(r.key),w(r.value)].join(", ")})).join(", ");return[h(t),"[".concat(e,"]")].join("")};var jt=e(0);function St(r){return(St="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var Tt,_t,xt,kt=(Tt={},_t="db2",xt=jt.parse,(_t=function(r){var t=function(r,t){if("object"!=St(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=St(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==St(t)?t:t+""}(_t))in Tt?Object.defineProperty(Tt,_t,{value:xt,enumerable:!0,configurable:!0,writable:!0}):Tt[_t]=xt,Tt);function It(r){return(It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Rt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Rt(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){s=!0,a=r},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function Rt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Ot(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=It(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=It(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==It(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&Ot(r.prototype,t),e&&Ot(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return v(t),Ar(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return v(t),at(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a;if(v(e),!r||"*"===r)return[];var n=Et(t);return r.map((function(r){return At(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,e=t.database,n=void 0===e?"db2":e;v(t);var o=n.toLowerCase();if(kt[o])return kt[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||a.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,s=this["".concat(o,"List")].bind(this),i=s(r,e),c=!0,f="",l=Nt(i);try{for(l.s();!(u=l.n()).done;){var p,v=u.value,b=!1,d=Nt(t);try{for(d.s();!(p=d.n()).done;){var y=p.value,h=new RegExp("^".concat(y,"$"),"i");if(h.test(v)){b=!0;break}}}catch(r){d.e(r)}finally{d.f()}if(!b){f=v,c=!1;break}}}catch(r){l.e(r)}finally{l.f()}if(!c)throw new Error("authority = '".concat(f,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=db2.js.map