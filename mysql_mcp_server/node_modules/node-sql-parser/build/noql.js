!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?s(r.parts[t][0])+"-"+s(r.parts[t][1]):s(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function s(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,s=new Array(r.length);for(t=0;t<r.length;t++)s[t]=(o=r[t],e[o.type](o));if(s.sort(),s.length>0){for(t=1,n=1;t<s.length;t++)s[t-1]!==s[t]&&(s[n]=s[t],n++);s.length=n}switch(s.length){case 1:return s[0];case 2:return s[0]+" or "+s[1];default:return s.slice(0,-1).join(", ")+", or "+s[s.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,s={},u={start:Oi},a=Oi,i=_i("IF",!0),c=_i("EXTENSION",!0),l=_i("SCHEMA",!0),f=_i("VERSION",!0),p=_i("CASCADED",!0),b=_i("LOCAL",!0),v=_i("CHECK",!0),y=_i("OPTION",!1),d=_i("check_option",!0),w=_i("security_barrier",!0),h=_i("security_invoker",!0),L=_i("SFUNC",!0),C=_i("STYPE",!0),m=_i("AGGREGATE",!0),E=_i("RETURNS",!0),A=_i("SETOF",!0),g=_i("CONSTANT",!0),T=_i(":=",!1),S=_i("BEGIN",!0),_=_i("DECLARE",!0),x=_i("LANGUAGE",!1),j=_i("TRANSORM",!0),I=_i("FOR",!1),R=_i("TYPE",!1),N=_i("WINDOW",!0),O=_i("IMMUTABLE",!0),k=_i("STABLE",!0),U=_i("VOLATILE",!0),M=_i("STRICT",!0),D=_i("NOT",!0),P=_i("LEAKPROOF",!0),G=_i("CALLED",!0),$=_i("NULL",!0),F=_i("ON",!0),H=_i("INPUT",!0),B=_i("EXTERNAL",!0),q=_i("SECURITY",!0),Y=_i("INVOKER",!0),W=_i("DEFINER",!0),V=_i("PARALLEL",!0),X=_i("UNSAFE",!0),Q=_i("RESTRICTED",!0),K=_i("SAFE",!0),z=/^[^ s\t\n\r]/,Z=xi([" ","s","\t","\n","\r"],!0,!1),J=/^[^ s\t\n\r;]/,rr=xi([" ","s","\t","\n","\r",";"],!0,!1),tr=_i("COST",!0),er=_i("ROWS",!0),nr=_i("SUPPORT",!0),or=_i("TO",!0),sr=_i("=",!1),ur=_i("CURRENT",!0),ar=_i("FUNCTION",!0),ir=_i("TYPE",!0),cr=_i("DOMAIN",!0),lr=_i("INCREMENT",!0),fr=_i("MINVALUE",!0),pr=function(r,t){return{resource:"sequence",prefix:r.toLowerCase(),value:t}},br=_i("NO",!0),vr=_i("MAXVALUE",!0),yr=_i("START",!0),dr=_i("CACHE",!0),wr=_i("CYCLE",!0),hr=_i("OWNED",!0),Lr=_i("NONE",!0),Cr=_i("NULLS",!0),mr=_i("FIRST",!0),Er=_i("LAST",!0),Ar=_i("AUTO_INCREMENT",!0),gr=_i("UNIQUE",!0),Tr=_i("KEY",!0),Sr=_i("PRIMARY",!0),_r=_i("COLUMN_FORMAT",!0),xr=_i("FIXED",!0),jr=_i("DYNAMIC",!0),Ir=_i("DEFAULT",!0),Rr=_i("STORAGE",!0),Nr=_i("DISK",!0),Or=_i("MEMORY",!0),kr=_i("CASCADE",!0),Ur=_i("RESTRICT",!0),Mr=_i("OUT",!0),Dr=_i("VARIADIC",!0),Pr=_i("INOUT",!0),Gr=_i("OWNER",!0),$r=_i("CURRENT_ROLE",!0),Fr=_i("CURRENT_USER",!0),Hr=_i("SESSION_USER",!0),Br=_i("ALGORITHM",!0),qr=_i("INSTANT",!0),Yr=_i("INPLACE",!0),Wr=_i("COPY",!0),Vr=_i("LOCK",!0),Xr=_i("SHARED",!0),Qr=_i("EXCLUSIVE",!0),Kr=_i("PRIMARY KEY",!0),zr=_i("FOREIGN KEY",!0),Zr=_i("MATCH FULL",!0),Jr=_i("MATCH PARTIAL",!0),rt=_i("MATCH SIMPLE",!0),tt=_i("SET NULL",!0),et=_i("NO ACTION",!0),nt=_i("SET DEFAULT",!0),ot=_i("TRIGGER",!0),st=_i("BEFORE",!0),ut=_i("AFTER",!0),at=_i("INSTEAD OF",!0),it=_i("EXECUTE",!0),ct=_i("PROCEDURE",!0),lt=_i("OF",!0),ft=_i("DEFERRABLE",!0),pt=_i("INITIALLY IMMEDIATE",!0),bt=_i("INITIALLY DEFERRED",!0),vt=_i("FOR",!0),yt=_i("EACH",!0),dt=_i("ROW",!0),wt=_i("STATEMENT",!0),ht=_i("CHARACTER",!0),Lt=_i("SET",!0),Ct=_i("CHARSET",!0),mt=_i("COLLATE",!0),Et=_i("AVG_ROW_LENGTH",!0),At=_i("KEY_BLOCK_SIZE",!0),gt=_i("MAX_ROWS",!0),Tt=_i("MIN_ROWS",!0),St=_i("STATS_SAMPLE_PAGES",!0),_t=_i("CONNECTION",!0),xt=_i("COMPRESSION",!0),jt=_i("'",!1),It=_i("ZLIB",!0),Rt=_i("LZ4",!0),Nt=_i("ENGINE",!0),Ot=_i("IN",!0),kt=_i("ACCESS SHARE",!0),Ut=_i("ROW SHARE",!0),Mt=_i("ROW EXCLUSIVE",!0),Dt=_i("SHARE UPDATE EXCLUSIVE",!0),Pt=_i("SHARE ROW EXCLUSIVE",!0),Gt=_i("ACCESS EXCLUSIVE",!0),$t=_i("SHARE",!0),Ft=_i("MODE",!0),Ht=_i("NOWAIT",!0),Bt=_i("TABLES",!0),qt=_i("PREPARE",!0),Yt=_i("USAGE",!0),Wt=function(r){return{type:"origin",value:Array.isArray(r)?r[0]:r}},Vt=_i("CONNECT",!0),Xt=_i("PRIVILEGES",!0),Qt=function(r){return{type:"origin",value:r}},Kt=_i("SEQUENCE",!0),zt=_i("DATABASE",!0),Zt=_i("DOMAIN",!1),Jt=_i("FUNCTION",!1),re=_i("ROUTINE",!0),te=_i("LANGUAGE",!0),ee=_i("LARGE",!0),ne=_i("SCHEMA",!1),oe=_i("FUNCTIONS",!0),se=_i("PROCEDURES",!0),ue=_i("ROUTINES",!0),ae=_i("PUBLIC",!0),ie=_i("GRANT",!0),ce=_i("OPTION",!0),le=_i("ADMIN",!0),fe=_i("REVOKE",!0),pe=_i("ELSEIF",!0),be=_i("THEN",!0),ve=_i("END",!0),ye=_i("DEBUG",!0),de=_i("LOG",!0),we=_i("INFO",!0),he=_i("NOTICE",!0),Le=_i("WARNING",!0),Ce=_i("EXCEPTION",!0),me=_i("MESSAGE",!0),Ee=_i("DETAIL",!0),Ae=_i("HINT",!0),ge=_i("ERRCODE",!0),Te=_i("COLUMN",!0),Se=_i("CONSTRAINT",!0),_e=_i("DATATYPE",!0),xe=_i("TABLE",!0),je=_i("SQLSTATE",!0),Ie=_i("RAISE",!0),Re=_i("LOOP",!0),Ne=_i(";",!1),Oe=_i("(",!1),Ue=_i(")",!1),Me=_i('"',!1),De=_i("OUTFILE",!0),Pe=_i("DUMPFILE",!0),Ge=_i("BTREE",!0),$e=_i("HASH",!0),Fe=_i("GIST",!0),He=_i("GIN",!0),Be=_i("WITH",!0),qe=_i("PARSER",!0),Ye=_i("VISIBLE",!0),We=_i("INVISIBLE",!0),Ve=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Tv[t]=t,e&&(Tv[e]=t),function(r){const t=Cv(r);r.clear(),t.forEach(t=>r.add(t))}(gv)}),t},Xe=_i("LATERAL",!0),Qe=_i("TABLESAMPLE",!0),Ke=_i("REPEATABLE",!0),ze=_i("CROSS",!0),Ze=_i("FOLLOWING",!0),Je=_i("PRECEDING",!0),rn=_i("UNBOUNDED",!0),tn=_i("DO",!0),en=_i("NOTHING",!0),nn=_i("CONFLICT",!0),on=function(r,t){return hv(r,t)},sn=_i("!",!1),un=_i(">=",!1),an=_i(">",!1),cn=_i("<=",!1),ln=_i("<>",!1),fn=_i("<",!1),pn=_i("!=",!1),bn=_i("SIMILAR",!0),vn=_i("!~*",!1),yn=_i("~*",!1),dn=_i("~",!1),wn=_i("!~",!1),hn=_i("ESCAPE",!0),Ln=_i("+",!1),Cn=_i("-",!1),mn=_i("*",!1),En=_i("/",!1),An=_i("%",!1),gn=_i("||",!1),Tn=_i("$",!1),Sn=_i("?|",!1),_n=_i("?&",!1),xn=_i("?",!1),jn=_i("#-",!1),In=_i("#>>",!1),Rn=_i("#>",!1),Nn=_i("@>",!1),On=_i("<@",!1),kn=_i("E",!0),Un=function(r){return!0===pv[r.toUpperCase()]},Mn=/^[^"]/,Dn=xi(['"'],!0,!1),Pn=/^[^']/,Gn=xi(["'"],!0,!1),$n=_i("`",!1),Fn=/^[^`]/,Hn=xi(["`"],!0,!1),Bn=/^[A-Za-z_\u4E00-\u9FA5]/,qn=xi([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),Yn=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,Wn=xi([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),Vn=/^[A-Za-z0-9_\u4E00-\u9FA5\xC0-\u017F]/,Xn=xi([["A","Z"],["a","z"],["0","9"],"_",["一","龥"],["À","ſ"]],!1,!1),Qn=_i(":",!1),Kn=_i("OVER",!0),zn=_i("FILTER",!0),Zn=_i("FIRST_VALUE",!0),Jn=_i("LAST_VALUE",!0),ro=_i("ROW_NUMBER",!0),to=_i("DENSE_RANK",!0),eo=_i("RANK",!0),no=_i("LAG",!0),oo=_i("LEAD",!0),so=_i("NTH_VALUE",!0),uo=_i("IGNORE",!0),ao=_i("RESPECT",!0),io=_i("percentile_cont",!0),co=_i("percentile_disc",!0),lo=_i("within",!0),fo=_i("mode",!0),po=_i("BOTH",!0),bo=_i("LEADING",!0),vo=_i("TRAILING",!0),yo=_i("trim",!0),wo=_i("crosstab",!0),ho=_i("now",!0),Lo=_i("at",!0),Co=_i("zone",!0),mo=_i("CENTURY",!0),Eo=_i("DAY",!0),Ao=_i("DATE",!0),go=_i("DECADE",!0),To=_i("DOW",!0),So=_i("DOY",!0),_o=_i("EPOCH",!0),xo=_i("HOUR",!0),jo=_i("ISODOW",!0),Io=_i("ISOYEAR",!0),Ro=_i("MICROSECONDS",!0),No=_i("MILLENNIUM",!0),Oo=_i("MILLISECONDS",!0),ko=_i("MINUTE",!0),Uo=_i("MONTH",!0),Mo=_i("QUARTER",!0),Do=_i("SECOND",!0),Po=_i("TIMEZONE",!0),Go=_i("TIMEZONE_HOUR",!0),$o=_i("TIMEZONE_MINUTE",!0),Fo=_i("WEEK",!0),Ho=_i("YEAR",!0),Bo=_i("NTILE",!0),qo=/^[\n]/,Yo=xi(["\n"],!1,!1),Wo=/^[^"\\\0-\x1F\x7F]/,Vo=xi(['"',"\\",["\0",""],""],!0,!1),Xo=/^[^'\\]/,Qo=xi(["'","\\"],!0,!1),Ko=_i("\\'",!1),zo=_i('\\"',!1),Zo=_i("\\\\",!1),Jo=_i("\\/",!1),rs=_i("\\b",!1),ts=_i("\\f",!1),es=_i("\\n",!1),ns=_i("\\r",!1),os=_i("\\t",!1),ss=_i("\\u",!1),us=_i("\\",!1),as=_i("''",!1),is=/^[\n\r]/,cs=xi(["\n","\r"],!1,!1),ls=_i(".",!1),fs=/^[0-9]/,ps=xi([["0","9"]],!1,!1),bs=/^[0-9a-fA-F]/,vs=xi([["0","9"],["a","f"],["A","F"]],!1,!1),ys=/^[eE]/,ds=xi(["e","E"],!1,!1),ws=/^[+\-]/,hs=xi(["+","-"],!1,!1),Ls=_i("NOT NULL",!0),Cs=_i("TRUE",!0),ms=_i("FALSE",!0),Es=_i("SHOW",!0),As=_i("DROP",!0),gs=_i("USE",!0),Ts=_i("ALTER",!0),Ss=_i("SELECT",!0),_s=_i("UPDATE",!0),xs=_i("CREATE",!0),js=_i("TEMPORARY",!0),Is=_i("TEMP",!0),Rs=_i("DELETE",!0),Ns=_i("INSERT",!0),Os=_i("RECURSIVE",!0),ks=_i("REPLACE",!0),Us=_i("RETURNING",!0),Ms=_i("RENAME",!0),Ds=(_i("EXPLAIN",!0),_i("PARTITION",!0)),Ps=_i("INTO",!0),Gs=_i("FROM",!0),$s=_i("AS",!0),Fs=_i("TABLESPACE",!0),Hs=_i("DEALLOCATE",!0),Bs=_i("LEFT",!0),qs=_i("RIGHT",!0),Ys=_i("FULL",!0),Ws=_i("INNER",!0),Vs=_i("JOIN",!0),Xs=_i("OUTER",!0),Qs=_i("UNION",!0),Ks=_i("INTERSECT",!0),zs=_i("EXCEPT",!0),Zs=_i("VALUES",!0),Js=_i("USING",!0),ru=_i("WHERE",!0),tu=_i("GROUP",!0),eu=_i("BY",!0),nu=_i("ORDER",!0),ou=_i("HAVING",!0),su=_i("LIMIT",!0),uu=_i("OFFSET",!0),au=_i("ASC",!0),iu=_i("DESC",!0),cu=_i("ALL",!0),lu=_i("DISTINCT",!0),fu=_i("BETWEEN",!0),pu=_i("IS",!0),bu=_i("LIKE",!0),vu=_i("ILIKE",!0),yu=_i("EXISTS",!0),du=_i("AND",!0),wu=_i("OR",!0),hu=_i("ARRAY",!0),Lu=_i("ARRAY_AGG",!0),Cu=_i("STRING_AGG",!0),mu=_i("COUNT",!0),Eu=_i("GROUP_CONCAT",!0),Au=_i("MAX",!0),gu=_i("MIN",!0),Tu=_i("SUM",!0),Su=_i("AVG",!0),_u=_i("EXTRACT",!0),xu=_i("CALL",!0),ju=_i("CASE",!0),Iu=_i("WHEN",!0),Ru=_i("ELSE",!0),Nu=_i("CAST",!0),Ou=_i("BOOL",!0),ku=_i("BOOLEAN",!0),Uu=_i("CHAR",!0),Mu=_i("VARCHAR",!0),Du=_i("NUMERIC",!0),Pu=_i("DECIMAL",!0),Gu=_i("SIGNED",!0),$u=_i("UNSIGNED",!0),Fu=_i("INT",!0),Hu=_i("ZEROFILL",!0),Bu=_i("INTEGER",!0),qu=_i("JSON",!0),Yu=_i("JSONB",!0),Wu=_i("GEOMETRY",!0),Vu=_i("SMALLINT",!0),Xu=_i("SERIAL",!0),Qu=_i("TINYINT",!0),Ku=_i("TINYTEXT",!0),zu=_i("TEXT",!0),Zu=_i("MEDIUMTEXT",!0),Ju=_i("LONGTEXT",!0),ra=_i("BIGINT",!0),ta=_i("ENUM",!0),ea=_i("FLOAT",!0),na=_i("DOUBLE",!0),oa=_i("BIGSERIAL",!0),sa=_i("REAL",!0),ua=_i("DATETIME",!0),aa=_i("TIME",!0),ia=_i("TIMESTAMP",!0),ca=_i("TRUNCATE",!0),la=_i("USER",!0),fa=_i("UUID",!0),pa=_i("OID",!0),ba=_i("REGCLASS",!0),va=_i("REGCOLLATION",!0),ya=_i("REGCONFIG",!0),da=_i("REGDICTIONARY",!0),wa=_i("REGNAMESPACE",!0),ha=_i("REGOPER",!0),La=_i("REGOPERATOR",!0),Ca=_i("REGPROC",!0),ma=_i("REGPROCEDURE",!0),Ea=_i("REGROLE",!0),Aa=_i("REGTYPE",!0),ga=_i("CURRENT_DATE",!0),Ta=(_i("ADDDATE",!0),_i("INTERVAL",!0)),Sa=_i("CURRENT_TIME",!0),_a=_i("CURRENT_TIMESTAMP",!0),xa=_i("SYSTEM_USER",!0),ja=_i("GLOBAL",!0),Ia=_i("SESSION",!0),Ra=_i("PERSIST",!0),Na=_i("PERSIST_ONLY",!0),Oa=_i("VIEW",!0),ka=_i("@",!1),Ua=_i("@@",!1),Ma=_i("$$",!1),Da=_i("return",!0),Pa=_i("::",!1),Ga=_i("DUAL",!0),$a=_i("ADD",!0),Fa=_i("INDEX",!0),Ha=_i("FULLTEXT",!0),Ba=_i("SPATIAL",!0),qa=_i("COMMENT",!0),Ya=_i("CONCURRENTLY",!0),Wa=_i("REFERENCES",!0),Va=_i("SQL_CALC_FOUND_ROWS",!0),Xa=_i("SQL_CACHE",!0),Qa=_i("SQL_NO_CACHE",!0),Ka=_i("SQL_SMALL_RESULT",!0),za=_i("SQL_BIG_RESULT",!0),Za=_i("SQL_BUFFER_RESULT",!0),Ja=_i(",",!1),ri=_i("[",!1),ti=_i("]",!1),ei=_i("->",!1),ni=_i("->>",!1),oi=_i("&&",!1),si=_i("/*",!1),ui=_i("*/",!1),ai=_i("--",!1),ii=(_i("#",!1),{type:"any"}),ci=/^[ \t\n\r]/,li=xi([" ","\t","\n","\r"],!1,!1),fi=/^[^$]/,pi=xi(["$"],!0,!1),bi=function(r){return{dataType:r}},vi=_i("bytea",!0),yi=_i("varying",!0),di=_i("PRECISION",!0),wi=_i("WITHOUT",!0),hi=_i("ZONE",!0),Li=function(r){return{dataType:r}},Ci=_i("RECORD",!0),mi=0,Ei=0,Ai=[{line:1,column:1}],gi=0,Ti=[],Si=0;if("startRule"in t){if(!(t.startRule in u))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=u[t.startRule]}function _i(r,t){return{type:"literal",text:r,ignoreCase:t}}function xi(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function ji(t){var e,n=Ai[t];if(n)return n;for(e=t-1;!Ai[e];)e--;for(n={line:(n=Ai[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Ai[t]=n,n}function Ii(r,t){var e=ji(r),n=ji(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Ri(r){mi<gi||(mi>gi&&(gi=mi,Ti=[]),Ti.push(r))}function Ni(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Oi(){var t,e;return t=mi,Hb()!==s?((e=function(){var t,e,n,o,u,a,i,c,l,f,p,b;if(t=mi,(e=Kf())!==s)if(Hb()!==s)if(n=mi,(o=Op())!==s&&(u=Hb())!==s&&(a=ep())!==s?n=o=[o,u,a]:(mi=n,n=s),n===s&&(n=null),n!==s)if((o=Hb())!==s)if("function"===r.substr(mi,8).toLowerCase()?(u=r.substr(mi,8),mi+=8):(u=s,0===Si&&Ri(ar)),u!==s)if((a=Hb())!==s)if((i=el())!==s)if(Hb()!==s)if(kb()!==s)if(Hb()!==s)if((c=sc())===s&&(c=null),c!==s)if(Hb()!==s)if(Ub()!==s)if(Hb()!==s)if((l=function(){var t,e,n,o,u;t=mi,"returns"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(E));e!==s&&Hb()!==s?("setof"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(A)),n===s&&(n=null),n!==s&&Hb()!==s?((o=uv())===s&&(o=el()),o!==s?(Ei=t,t=e={type:"returns",keyword:n,expr:o}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,"returns"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(E)),e!==s&&Hb()!==s&&(n=lp())!==s&&Hb()!==s&&(o=kb())!==s&&Hb()!==s&&(u=Bi())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e={type:"returns",keyword:"table",expr:u}):(mi=t,t=s));return t}())===s&&(l=null),l!==s)if(Hb()!==s){for(f=[],p=Vi();p!==s;)f.push(p),p=Vi();f!==s&&(p=Hb())!==s?((b=Pb())===s&&(b=null),b!==s&&Hb()!==s?(Ei=t,v=n,y=u,d=i,w=c,h=l,L=f,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{args:w||[],type:"create",replace:v&&"or replace",name:{schema:d.db,name:d.table},returns:h,keyword:y&&y.toLowerCase(),options:L||[]}},t=e):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;var v,y,d,w,h,L;return t}())===s&&(e=Mi()),e!==s?(Ei=t,t=e):(mi=t,t=s)):(mi=t,t=s),t}function ki(){var t;return(t=function(){var t,e,n,o,u,a,c,l,f;t=mi,(e=Wf())!==s&&Hb()!==s&&(n=lp())!==s&&Hb()!==s&&(o=Zc())!==s?(Ei=t,p=e,b=n,(v=o)&&v.forEach(r=>Av.add(`${p}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:p.toLowerCase(),keyword:b.toLowerCase(),name:v}},t=e):(mi=t,t=s);var p,b,v;t===s&&(t=mi,(e=Wf())!==s&&Hb()!==s&&(n=gb())!==s&&Hb()!==s?((o=jb())===s&&(o=null),o!==s&&Hb()!==s?(u=mi,"if"===r.substr(mi,2).toLowerCase()?(a=r.substr(mi,2),mi+=2):(a=s,0===Si&&Ri(i)),a!==s&&(c=Hb())!==s&&(l=Ip())!==s?u=a=[a,c,l]:(mi=u,u=s),u===s&&(u=null),u!==s&&(a=Hb())!==s&&(c=Xl())!==s&&(l=Hb())!==s?("cascade"===r.substr(mi,7).toLowerCase()?(f=r.substr(mi,7),mi+=7):(f=s,0===Si&&Ri(kr)),f===s&&("restrict"===r.substr(mi,8).toLowerCase()?(f=r.substr(mi,8),mi+=8):(f=s,0===Si&&Ri(Ur))),f===s&&(f=null),f!==s?(Ei=t,e=function(r,t,e,n,o,s){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:o,options:s&&[{type:"origin",value:s}]}}}(e,n,o,0,c,f),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s));return t}())===s&&(t=function(){var t;(t=function(){var r,t,e,n,o,u,a,i,c,l;r=mi,(t=Kf())!==s&&Hb()!==s?((e=zf())===s&&(e=null),e!==s&&Hb()!==s&&lp()!==s&&Hb()!==s?((n=Gi())===s&&(n=null),n!==s&&Hb()!==s&&(o=Zc())!==s&&Hb()!==s&&(u=function(){var r,t,e,n,o,u,a,i,c;if(r=mi,(t=kb())!==s)if(Hb()!==s)if((e=Ki())!==s){for(n=[],o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=Ki())!==s?o=u=[u,a,i,c]:(mi=o,o=s);o!==s;)n.push(o),o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=Ki())!==s?o=u=[u,a,i,c]:(mi=o,o=s);n!==s&&(o=Hb())!==s&&(u=Ub())!==s?(Ei=r,t=wv(e,n),r=t):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;return r}())!==s&&Hb()!==s?((a=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ec())!==s){for(e=[],n=mi,(o=Hb())!==s?((u=Nb())===s&&(u=null),u!==s&&(a=Hb())!==s&&(i=Ec())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s?((u=Nb())===s&&(u=null),u!==s&&(a=Hb())!==s&&(i=Ec())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())===s&&(a=null),a!==s&&Hb()!==s?((i=op())===s&&(i=ep()),i===s&&(i=null),i!==s&&Hb()!==s?((c=cp())===s&&(c=null),c!==s&&Hb()!==s?((l=Pi())===s&&(l=null),l!==s?(Ei=r,f=t,p=e,b=n,y=u,d=a,w=i,h=c,L=l,(v=o)&&v.forEach(r=>Av.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:w&&w[0].toLowerCase(),as:h&&h[0].toLowerCase(),query_expr:L&&L.ast,create_definitions:y,table_options:d}},r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);var f,p,b,v,y,d,w,h,L;r===s&&(r=mi,(t=Kf())!==s&&Hb()!==s?((e=zf())===s&&(e=null),e!==s&&Hb()!==s&&lp()!==s&&Hb()!==s?((n=Gi())===s&&(n=null),n!==s&&Hb()!==s&&(o=Zc())!==s&&Hb()!==s&&(u=function r(){var t,e;(t=function(){var r,t;r=mi,xp()!==s&&Hb()!==s&&(t=Zc())!==s?(Ei=r,r={type:"like",table:t}):(mi=r,r=s);return r}())===s&&(t=mi,kb()!==s&&Hb()!==s&&(e=r())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,(n=e).parentheses=!0,t=n):(mi=t,t=s));var n;return t}())!==s?(Ei=r,t=function(r,t,e,n,o){return n&&n.forEach(r=>Av.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,u),r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,h,L,C,m,E;t=mi,(e=Kf())!==s&&Hb()!==s?(n=mi,(o=Op())!==s&&(u=Hb())!==s&&(a=ep())!==s?n=o=[o,u,a]:(mi=n,n=s),n===s&&(n=null),n!==s&&(o=Hb())!==s?((u=xb())===s&&(u=null),u!==s&&(a=Hb())!==s?("trigger"===r.substr(mi,7).toLowerCase()?(i=r.substr(mi,7),mi+=7):(i=s,0===Si&&Ri(ot)),i!==s&&Hb()!==s&&(c=cf())!==s&&Hb()!==s?("before"===r.substr(mi,6).toLowerCase()?(l=r.substr(mi,6),mi+=6):(l=s,0===Si&&Ri(st)),l===s&&("after"===r.substr(mi,5).toLowerCase()?(l=r.substr(mi,5),mi+=5):(l=s,0===Si&&Ri(ut)),l===s&&("instead of"===r.substr(mi,10).toLowerCase()?(l=r.substr(mi,10),mi+=10):(l=s,0===Si&&Ri(at)))),l!==s&&Hb()!==s&&(f=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Lc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Op())!==s&&(a=Hb())!==s&&(i=Lc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Op())!==s&&(a=Hb())!==s&&(i=Lc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&Hb()!==s?("on"===r.substr(mi,2).toLowerCase()?(p=r.substr(mi,2),mi+=2):(p=s,0===Si&&Ri(F)),p!==s&&Hb()!==s&&(b=el())!==s&&Hb()!==s?(v=mi,(y=ap())!==s&&(d=Hb())!==s&&(w=el())!==s?v=y=[y,d,w]:(mi=v,v=s),v===s&&(v=null),v!==s&&(y=Hb())!==s?((d=function(){var t,e,n,o,u;t=mi,e=mi,"not"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(D));n===s&&(n=null);n!==s&&(o=Hb())!==s?("deferrable"===r.substr(mi,10).toLowerCase()?(u=r.substr(mi,10),mi+=10):(u=s,0===Si&&Ri(ft)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s);e!==s&&(n=Hb())!==s?("initially immediate"===r.substr(mi,19).toLowerCase()?(o=r.substr(mi,19),mi+=19):(o=s,0===Si&&Ri(pt)),o===s&&("initially deferred"===r.substr(mi,18).toLowerCase()?(o=r.substr(mi,18),mi+=18):(o=s,0===Si&&Ri(bt))),o!==s?(Ei=t,i=o,e={keyword:(a=e)&&a[0]?a[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):(mi=t,t=s)):(mi=t,t=s);var a,i;return t}())===s&&(d=null),d!==s&&(w=Hb())!==s?((h=function(){var t,e,n,o;t=mi,"for"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(vt));e!==s&&Hb()!==s?("each"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(yt)),n===s&&(n=null),n!==s&&Hb()!==s?("row"===r.substr(mi,3).toLowerCase()?(o=r.substr(mi,3),mi+=3):(o=s,0===Si&&Ri(dt)),o===s&&("statement"===r.substr(mi,9).toLowerCase()?(o=r.substr(mi,9),mi+=9):(o=s,0===Si&&Ri(wt))),o!==s?(Ei=t,u=e,i=o,e={keyword:(a=n)?`${u.toLowerCase()} ${a.toLowerCase()}`:u.toLowerCase(),args:i.toLowerCase()},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var u,a,i;return t}())===s&&(h=null),h!==s&&Hb()!==s?((L=function(){var r,t;r=mi,Dp()!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(t=Nl())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r={type:"when",cond:t,parentheses:!0}):(mi=r,r=s);return r}())===s&&(L=null),L!==s&&Hb()!==s?("execute"===r.substr(mi,7).toLowerCase()?(C=r.substr(mi,7),mi+=7):(C=s,0===Si&&Ri(it)),C!==s&&Hb()!==s?("procedure"===r.substr(mi,9).toLowerCase()?(m=r.substr(mi,9),mi+=9):(m=s,0===Si&&Ri(ct)),m===s&&("function"===r.substr(mi,8).toLowerCase()?(m=r.substr(mi,8),mi+=8):(m=s,0===Si&&Ri(ar))),m!==s&&Hb()!==s&&(E=ev())!==s?(Ei=t,A=u,g=i,S=f,_=b,x=v,j=d,I=h,R=L,N=m,O=E,e={type:"create",replace:n&&"or replace",constraint:c,location:(T=l)&&T.toLowerCase(),events:S,table:_,from:x&&x[2],deferrable:j,for_each:I,when:R,execute:{keyword:"execute "+N.toLowerCase(),expr:O},constraint_type:g&&g.toLowerCase(),keyword:g&&g.toLowerCase(),constraint_kw:A&&A.toLowerCase(),resource:"constraint"},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var A,g,T,S,_,x,j,I,R,N,O;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,p,b,v,y,d,w,h;t=mi,(e=Kf())!==s&&Hb()!==s?("extension"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(c)),n!==s&&Hb()!==s?((o=Gi())===s&&(o=null),o!==s&&Hb()!==s?((u=cf())===s&&(u=Rf()),u!==s&&Hb()!==s?((a=wp())===s&&(a=null),a!==s&&Hb()!==s?(i=mi,"schema"===r.substr(mi,6).toLowerCase()?(p=r.substr(mi,6),mi+=6):(p=s,0===Si&&Ri(l)),p!==s&&(b=Hb())!==s&&(v=cf())!==s?i=p=[p,b,v]:(mi=i,i=s),i===s&&(i=Rf()),i===s&&(i=null),i!==s&&(p=Hb())!==s?(b=mi,"version"===r.substr(mi,7).toLowerCase()?(v=r.substr(mi,7),mi+=7):(v=s,0===Si&&Ri(f)),v!==s&&(y=Hb())!==s?((d=cf())===s&&(d=Rf()),d!==s?b=v=[v,y,d]:(mi=b,b=s)):(mi=b,b=s),b===s&&(b=null),b!==s&&(v=Hb())!==s?(y=mi,(d=ap())!==s&&(w=Hb())!==s?((h=cf())===s&&(h=Rf()),h!==s?y=d=[d,w,h]:(mi=y,y=s)):(mi=y,y=s),y===s&&(y=null),y!==s?(Ei=t,L=o,C=u,m=a,E=i,A=b,g=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:L,extension:mv(C),with:m&&m[0].toLowerCase(),schema:mv(E&&E[2].toLowerCase()),version:mv(A&&A[2]),from:mv(g&&g[2])},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var L,C,m,E,A,g;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,h,L;t=mi,(e=Kf())!==s&&Hb()!==s?((n=Sb())===s&&(n=null),n!==s&&Hb()!==s&&(o=gb())!==s&&Hb()!==s?((u=jb())===s&&(u=null),u!==s&&Hb()!==s?((a=zl())===s&&(a=null),a!==s&&Hb()!==s&&(i=pp())!==s&&Hb()!==s&&(c=el())!==s&&Hb()!==s?((l=Qc())===s&&(l=null),l!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(f=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Qi())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Qi())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Qi())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?(p=mi,(b=wp())!==s&&(v=Hb())!==s&&(y=kb())!==s&&(d=Hb())!==s&&(w=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=zc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=zc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=zc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&(h=Hb())!==s&&(L=Ub())!==s?p=b=[b,v,y,d,w,h,L]:(mi=p,p=s),p===s&&(p=null),p!==s&&(b=Hb())!==s?(v=mi,(y=function(){var t,e,n,o;t=mi,"tablespace"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Fs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TABLESPACE"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(d=Hb())!==s&&(w=cf())!==s?v=y=[y,d,w]:(mi=v,v=s),v===s&&(v=null),v!==s&&(y=Hb())!==s?((d=sl())===s&&(d=null),d!==s&&(w=Hb())!==s?(Ei=t,C=e,m=n,E=o,A=u,g=a,T=i,S=c,_=l,x=f,j=p,I=v,R=d,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:C[0].toLowerCase(),index_type:m&&m.toLowerCase(),keyword:E.toLowerCase(),concurrently:A&&A.toLowerCase(),index:g,on_kw:T[0].toLowerCase(),table:S,index_using:_,index_columns:x,with:j&&j[4],with_before_where:!0,tablespace:I&&{type:"origin",value:I[2]},where:R}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var C,m,E,A,g,T,S,_,x,j,I,R;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=Kf())!==s&&Hb()!==s?((n=zf())===s&&(n=Zf()),n===s&&(n=null),n!==s&&Hb()!==s&&function(){var t,e,n,o;t=mi,"sequence"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Kt));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SEQUENCE"):(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s?((o=Gi())===s&&(o=null),o!==s&&Hb()!==s&&(u=el())!==s&&Hb()!==s?(a=mi,(i=cp())!==s&&(c=Hb())!==s&&(l=Jl())!==s?a=i=[i,c,l]:(mi=a,a=s),a===s&&(a=null),a!==s&&(i=Hb())!==s?((c=function(){var r,t,e,n,o,u;if(r=mi,(t=Xi())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Xi())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Xi())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e,1),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())===s&&(c=null),c!==s?(Ei=t,f=e,p=n,b=o,y=a,d=c,(v=u).as=y&&y[2],e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:f[0].toLowerCase(),keyword:"sequence",temporary:p&&p[0].toLowerCase(),if_not_exists:b,sequence:[v],create_definitions:d}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var f,p,b,v,y,d;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=Kf())!==s&&Hb()!==s?((n=function(){var t,e,n,o;t=mi,"database"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(zt));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DATABASE"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(n=fp()),n!==s&&Hb()!==s?((o=Gi())===s&&(o=null),o!==s&&Hb()!==s&&(u=tv())!==s&&Hb()!==s?((a=function(){var r,t,e,n,o,u;if(r=mi,(t=mc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=mc())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=mc())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e,1),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())===s&&(a=null),a!==s?(Ei=t,e=function(r,t,e,n,o){const s=t.toLowerCase();return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r[0].toLowerCase(),keyword:s,if_not_exists:e,[s]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,u,a),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=Kf())!==s&&Hb()!==s?("domain"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(cr)),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s?((u=cp())===s&&(u=null),u!==s&&Hb()!==s&&(a=uv())!==s&&Hb()!==s?((i=Ji())===s&&(i=null),i!==s&&Hb()!==s?((c=tc())===s&&(c=null),c!==s&&Hb()!==s?((l=dc())===s&&(l=null),l!==s?(Ei=t,e=function(r,t,e,n,o,s,u,a){a&&(a.type="constraint");const i=[s,u,a].filter(r=>r);return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i}}}(e,n,o,u,a,i,c,l),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i;t=mi,(e=Kf())!==s&&Hb()!==s?("type"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(ir)),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s&&(u=cp())!==s&&Hb()!==s&&(a=eb())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((i=Sl())===s&&(i=null),i!==s&&Hb()!==s&&Ub()!==s?(Ei=t,c=e,l=n,f=o,p=u,b=a,(v=i).parentheses=!0,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:c[0].toLowerCase(),keyword:l.toLowerCase(),name:{schema:f.db,name:f.table},as:p&&p[0]&&p[0].toLowerCase(),resource:b.toLowerCase(),create_definitions:v}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var c,l,f,p,b,v;t===s&&(t=mi,(e=Kf())!==s&&Hb()!==s?("type"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(ir)),n!==s&&Hb()!==s&&(o=el())!==s?(Ei=t,e=function(r,t,e){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),name:{schema:e.db,name:e.table}}}}(e,n,o),t=e):(mi=t,t=s)):(mi=t,t=s));return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,d,w,h,L,C,m,E,A;t=mi,(e=Kf())!==s&&Hb()!==s?(n=mi,(o=Op())!==s&&(u=Hb())!==s&&(a=ep())!==s?n=o=[o,u,a]:(mi=n,n=s),n===s&&(n=null),n!==s&&(o=Hb())!==s?((u=Zf())===s&&(u=zf()),u===s&&(u=null),u!==s&&(a=Hb())!==s?((i=tp())===s&&(i=null),i!==s&&Hb()!==s&&function(){var t,e,n,o;t=mi,"view"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Oa));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="VIEW"):(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s&&(c=el())!==s&&Hb()!==s?(l=mi,(f=kb())!==s&&(d=Hb())!==s&&(w=Ql())!==s&&(h=Hb())!==s&&(L=Ub())!==s?l=f=[f,d,w,h,L]:(mi=l,l=s),l===s&&(l=null),l!==s&&(f=Hb())!==s?(d=mi,(w=wp())!==s&&(h=Hb())!==s&&(L=kb())!==s&&(C=Hb())!==s&&(m=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=$i())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=$i())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=$i())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&(E=Hb())!==s&&(A=Ub())!==s?d=w=[w,h,L,C,m,E,A]:(mi=d,d=s),d===s&&(d=null),d!==s&&(w=Hb())!==s&&(h=cp())!==s&&(L=Hb())!==s&&(C=Dc())!==s&&(m=Hb())!==s?((E=function(){var t,e,n,o,u;t=mi,(e=wp())!==s&&Hb()!==s?("cascaded"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(p)),n===s&&("local"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(b))),n!==s&&Hb()!==s?("check"===r.substr(mi,5).toLowerCase()?(o=r.substr(mi,5),mi+=5):(o=s,0===Si&&Ri(v)),o!==s&&Hb()!==s?("OPTION"===r.substr(mi,6)?(u="OPTION",mi+=6):(u=s,0===Si&&Ri(y)),u!==s?(Ei=t,e=`with ${n.toLowerCase()} check option`,t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,(e=wp())!==s&&Hb()!==s?("check"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(v)),n!==s&&Hb()!==s?("OPTION"===r.substr(mi,6)?(o="OPTION",mi+=6):(o=s,0===Si&&Ri(y)),o!==s?(Ei=t,t=e="with check option"):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s));return t}())===s&&(E=null),E!==s?(Ei=t,g=e,T=n,S=u,_=i,j=l,I=d,R=C,N=E,(x=c).view=x.table,delete x.table,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:g[0].toLowerCase(),keyword:"view",replace:T&&"or replace",temporary:S&&S[0].toLowerCase(),recursive:_&&_.toLowerCase(),columns:j&&j[2],select:R,view:x,with_options:I&&I[4],with:N}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var g,T,S,_,x,j,I,R,N;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=Kf())!==s&&Hb()!==s?(n=mi,(o=Op())!==s&&(u=Hb())!==s&&(a=ep())!==s?n=o=[o,u,a]:(mi=n,n=s),n===s&&(n=null),n!==s&&(o=Hb())!==s?("aggregate"===r.substr(mi,9).toLowerCase()?(u=r.substr(mi,9),mi+=9):(u=s,0===Si&&Ri(m)),u!==s&&(a=Hb())!==s&&(i=el())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(c=ec())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(l=function(){var t,e,n,o,u,a,i,c;if(t=mi,(e=function(){var t,e,n,o,u;t=mi,"sfunc"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(L));e!==s&&Hb()!==s&&mb()!==s&&Hb()!==s&&(n=el())!==s&&Hb()!==s&&Nb()!==s&&Hb()!==s?("stype"===r.substr(mi,5).toLowerCase()?(o=r.substr(mi,5),mi+=5):(o=s,0===Si&&Ri(C)),o!==s&&Hb()!==s&&mb()!==s&&Hb()!==s&&(u=uv())!==s?(Ei=t,i=u,e=[{type:"sfunc",symbol:"=",value:{schema:(a=n).db,name:a.table}},{type:"stype",symbol:"=",value:i}],t=e):(mi=t,t=s)):(mi=t,t=s);var a,i;return t}())!==s){for(n=[],o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=Fi())!==s?o=u=[u,a,i,c]:(mi=o,o=s);o!==s;)n.push(o),o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=Fi())!==s?o=u=[u,a,i,c]:(mi=o,o=s);n!==s?(Ei=t,e=wv(e,n),t=e):(mi=t,t=s)}else mi=t,t=s;return t}())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,f=n,p=i,b=c,v=l,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"create",keyword:"aggregate",replace:f&&"or replace",name:{schema:p.db,name:p.table},args:{parentheses:!0,expr:b,orderby:b.orderby},options:v}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var f,p,b,v;return t}());return t}())===s&&(t=Wi())===s&&(t=function(){var r,t,e,n;r=mi,(t=pb())!==s&&Hb()!==s?((e=lp())===s&&(e=null),e!==s&&Hb()!==s&&(n=Zc())!==s?(Ei=r,o=t,u=e,(a=n)&&a.forEach(r=>Av.add(`${o}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:o.toLowerCase(),keyword:u&&u.toLowerCase()||"table",name:a}},r=t):(mi=r,r=s)):(mi=r,r=s);var o,u,a;return r}())===s&&(t=function(){var r,t,e;r=mi,(t=np())!==s&&Hb()!==s&&lp()!==s&&Hb()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Xc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Av.add(`rename::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`))),t={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"rename",table:n}},r=t):(mi=r,r=s);var n;return r}())===s&&(t=function(){var t,e,n;t=mi,(e=function(){var t,e,n,o;t=mi,"call"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(xu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CALL"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s&&(n=ev())!==s?(Ei=t,o=n,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"call",expr:o}},t=e):(mi=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n;t=mi,(e=function(){var t,e,n,o;t=mi,"use"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(gs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s&&(n=zl())!==s?(Ei=t,o=n,Av.add(`use::${o}::null`),e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"use",db:o}},t=e):(mi=t,t=s);var o;return t}())===s&&(t=function(){var t;(t=function(){var r,t,e,n;r=mi,(t=Vf())!==s&&Hb()!==s&&lp()!==s&&Hb()!==s&&(e=Zc())!==s&&Hb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=uc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=uc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=uc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=r,u=n,(o=e)&&o.length>0&&o.forEach(r=>Av.add(`alter::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"alter",table:o,expr:u}},r=t):(mi=r,r=s);var o,u;return r}())===s&&(t=function(){var r,t,e,n,o;r=mi,(t=Vf())!==s&&Hb()!==s&&(e=fp())!==s&&Hb()!==s&&(n=cf())!==s&&Hb()!==s?((o=ac())===s&&(o=ic())===s&&(o=cc()),o!==s?(Ei=r,t=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"alter",keyword:n,schema:t,expr:e}}}(e,n,o),r=t):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u;t=mi,(e=Vf())!==s&&Hb()!==s?("domain"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(cr)),n===s&&("type"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(ir))),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s?((u=ac())===s&&(u=ic())===s&&(u=cc()),u!==s?(Ei=t,e=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"alter",keyword:n,name:{schema:t.db,name:t.table},expr:e}}}(n,o,u),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f;t=mi,(e=Vf())!==s&&Hb()!==s?("function"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(ar)),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s?(u=mi,(a=kb())!==s&&(i=Hb())!==s?((c=sc())===s&&(c=null),c!==s&&(l=Hb())!==s&&(f=Ub())!==s?u=a=[a,i,c,l,f]:(mi=u,u=s)):(mi=u,u=s),u===s&&(u=null),u!==s&&(a=Hb())!==s?((i=ac())===s&&(i=ic())===s&&(i=cc()),i!==s?(Ei=t,e=function(r,t,e,n){const o=r.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const s={};return e&&e[0]&&(s.parentheses=!0),s.expr=e&&e[2],{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:s,expr:n}}}(n,o,u,i),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=Vf())!==s&&Hb()!==s?("aggregate"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(m)),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(u=ec())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((a=ac())===s&&(a=ic())===s&&(a=cc()),a!==s?(Ei=t,e=function(r,t,e,n){const o=r.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n}}}(n,o,u,a),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}());return t}())===s&&(t=function(){var t,e,n,o;t=mi,(e=ip())!==s&&Hb()!==s?((n=function(){var t,e,n,o;t=mi,"global"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ja));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="GLOBAL"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=mi,"session"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ia));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SESSION"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=mi,"local"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(b));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="LOCAL"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=mi,"persist"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ra));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="PERSIST"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=mi,"persist_only"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Na));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="PERSIST_ONLY"):(mi=t,t=s)):(mi=t,t=s);return t}()),n===s&&(n=null),n!==s&&Hb()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Kb())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Kb())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Kb())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=t,u=n,(a=o).keyword=u,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"set",keyword:u,expr:a}},t=e):(mi=t,t=s)):(mi=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=function(){var t,e,n,o;t=mi,"lock"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Vr));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s?((n=lp())===s&&(n=null),n!==s&&Hb()!==s&&(o=Zc())!==s&&Hb()!==s?((u=function(){var t,e,n,o;t=mi,"in"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(Ot));e!==s&&Hb()!==s?("access share"===r.substr(mi,12).toLowerCase()?(n=r.substr(mi,12),mi+=12):(n=s,0===Si&&Ri(kt)),n===s&&("row share"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(Ut)),n===s&&("row exclusive"===r.substr(mi,13).toLowerCase()?(n=r.substr(mi,13),mi+=13):(n=s,0===Si&&Ri(Mt)),n===s&&("share update exclusive"===r.substr(mi,22).toLowerCase()?(n=r.substr(mi,22),mi+=22):(n=s,0===Si&&Ri(Dt)),n===s&&("share row exclusive"===r.substr(mi,19).toLowerCase()?(n=r.substr(mi,19),mi+=19):(n=s,0===Si&&Ri(Pt)),n===s&&("exclusive"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(Qr)),n===s&&("access exclusive"===r.substr(mi,16).toLowerCase()?(n=r.substr(mi,16),mi+=16):(n=s,0===Si&&Ri(Gt)),n===s&&("share"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri($t))))))))),n!==s&&Hb()!==s?("mode"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(Ft)),o!==s?(Ei=t,e={mode:`in ${n.toLowerCase()} mode`},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(u=null),u!==s&&Hb()!==s?("nowait"===r.substr(mi,6).toLowerCase()?(a=r.substr(mi,6),mi+=6):(a=s,0===Si&&Ri(Ht)),a===s&&(a=null),a!==s?(Ei=t,i=n,l=u,f=a,(c=o)&&c.forEach(r=>Av.add(`lock::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var i,c,l,f;return t}())===s&&(t=function(){var t,e,n;t=mi,(e=Yf())!==s&&Hb()!==s?("tables"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(Bt)),n!==s?(Ei=t,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"show",keyword:"tables"}},t=e):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,(e=Yf())!==s&&Hb()!==s&&(n=sv())!==s?(Ei=t,o=n,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"show",keyword:"var",var:o}},t=e):(mi=t,t=s));var o;return t}())===s&&(t=function(){var t,e,n,o;t=mi,(e=function(){var t,e,n,o;t=mi,"deallocate"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Hs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DEALLOCATE"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s?("prepare"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(qt)),n===s&&(n=null),n!==s&&Hb()!==s?((o=cf())===s&&(o=Ap()),o!==s?(Ei=t,u=n,a=o,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"deallocate",keyword:u,expr:{type:"default",value:a}}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=mi,(e=Ic())!==s&&Hb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Sc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Sc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Sc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&Hb()!==s&&(o=pp())!==s&&Hb()!==s?((u=function(){var t,e,n;t=mi,(e=lp())===s&&("sequence"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Kt)),e===s&&("database"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(zt)),e===s&&("DOMAIN"===r.substr(mi,6)?(e="DOMAIN",mi+=6):(e=s,0===Si&&Ri(Zt)),e===s&&("FUNCTION"===r.substr(mi,8)?(e="FUNCTION",mi+=8):(e=s,0===Si&&Ri(Jt)),e===s&&("procedure"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(ct)),e===s&&("routine"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(re)),e===s&&("language"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(te)),e===s&&("large"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ee)),e===s&&("SCHEMA"===r.substr(mi,6)?(e="SCHEMA",mi+=6):(e=s,0===Si&&Ri(ne)))))))))));e!==s&&(Ei=t,e={type:"origin",value:e.toUpperCase()});(t=e)===s&&(t=mi,(e=Ap())!==s&&Hb()!==s?("tables"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(Bt)),n===s&&("sequence"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(Kt)),n===s&&("functions"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(oe)),n===s&&("procedures"===r.substr(mi,10).toLowerCase()?(n=r.substr(mi,10),mi+=10):(n=s,0===Si&&Ri(se)),n===s&&("routines"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(ue)))))),n!==s&&Hb()!==s&&Sp()!==s&&Hb()!==s&&fp()!==s?(Ei=t,t=e={type:"origin",value:`all ${n} in schema`}):(mi=t,t=s)):(mi=t,t=s));return t}())===s&&(u=null),u!==s&&(a=Hb())!==s&&(i=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=_c())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=_c())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=_c())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&(c=Hb())!==s?((l=qf())===s&&(l=ap()),l!==s?(Ei=mi,b=l,({revoke:"from",grant:"to"}[e.type].toLowerCase()===b[0].toLowerCase()?void 0:s)!==s&&Hb()!==s&&(f=jc())!==s&&Hb()!==s?((p=function(){var t,e,n;t=mi,wp()!==s&&Hb()!==s?("grant"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ie)),e!==s&&Hb()!==s?("option"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(ce)),n!==s?(Ei=t,t={type:"origin",value:"with grant option"}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(p=null),p!==s?(Ei=t,e=function(r,t,e,n,o,s,u){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{...r,keyword:"priv",objects:t,on:{object_type:e,priv_level:n},to_from:o[0],user_or_roles:s,with:u}}}(e,n,u,i,l,f,p),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var b;t===s&&(t=mi,(e=Ic())!==s&&Hb()!==s&&(n=Zl())!==s&&Hb()!==s?((o=qf())===s&&(o=ap()),o!==s?(Ei=mi,(function(r,t,e){return{revoke:"from",grant:"to"}[r.type].toLowerCase()===e[0].toLowerCase()}(e,0,o)?void 0:s)!==s&&(u=Hb())!==s&&(a=jc())!==s&&(i=Hb())!==s?((c=function(){var t,e,n;t=mi,wp()!==s&&Hb()!==s?("admin"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(le)),e!==s&&Hb()!==s?("option"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(ce)),n!==s?(Ei=t,t={type:"origin",value:"with admin option"}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(c=null),c!==s?(Ei=t,e=function(r,t,e,n,o){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{...r,keyword:"role",objects:t.map(r=>({priv:{type:"string",value:r}})),to_from:e[0],user_or_roles:n,with:o}}}(e,n,o,a,c),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s));return t}())===s&&(t=function(){var t,e,n,o,u,a,c,l,f,p,b,v,y;t=mi,"if"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(i));e!==s&&Hb()!==s&&(n=Nl())!==s&&Hb()!==s?("then"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(be)),o!==s&&Hb()!==s&&(u=Ui())!==s&&Hb()!==s?((a=Pb())===s&&(a=null),a!==s&&Hb()!==s?((c=function(){var r,t,e,n,o,u;if(r=mi,(t=Rc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Rc())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Rc())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e,1),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())===s&&(c=null),c!==s&&Hb()!==s?(l=mi,(f=Pp())!==s&&(p=Hb())!==s&&(b=Ui())!==s?l=f=[f,p,b]:(mi=l,l=s),l===s&&(l=null),l!==s&&(f=Hb())!==s?((p=Pb())===s&&(p=null),p!==s&&(b=Hb())!==s?("end"===r.substr(mi,3).toLowerCase()?(v=r.substr(mi,3),mi+=3):(v=s,0===Si&&Ri(ve)),v!==s&&Hb()!==s?("if"===r.substr(mi,2).toLowerCase()?(y=r.substr(mi,2),mi+=2):(y=s,0===Si&&Ri(i)),y!==s?(Ei=t,d=n,w=u,h=a,L=c,C=l,m=p,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"if",keyword:"if",boolean_expr:d,semicolons:[h||"",m||""],prefix:{type:"origin",value:"then"},if_expr:w,elseif_expr:L,else_expr:C&&C[2],suffix:{type:"origin",value:"end if"}}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var d,w,h,L,C,m;return t}())===s&&(t=function(){var t,e,n,o,u;t=mi,"raise"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Ie));e!==s&&Hb()!==s?((n=function(){var t;"debug"===r.substr(mi,5).toLowerCase()?(t=r.substr(mi,5),mi+=5):(t=s,0===Si&&Ri(ye));t===s&&("log"===r.substr(mi,3).toLowerCase()?(t=r.substr(mi,3),mi+=3):(t=s,0===Si&&Ri(de)),t===s&&("info"===r.substr(mi,4).toLowerCase()?(t=r.substr(mi,4),mi+=4):(t=s,0===Si&&Ri(we)),t===s&&("notice"===r.substr(mi,6).toLowerCase()?(t=r.substr(mi,6),mi+=6):(t=s,0===Si&&Ri(he)),t===s&&("warning"===r.substr(mi,7).toLowerCase()?(t=r.substr(mi,7),mi+=7):(t=s,0===Si&&Ri(Le)),t===s&&("exception"===r.substr(mi,9).toLowerCase()?(t=r.substr(mi,9),mi+=9):(t=s,0===Si&&Ri(Ce)))))));return t}())===s&&(n=null),n!==s&&Hb()!==s?((o=function(){var t,e,n,o,u,a,i,c;if(t=mi,(e=Rf())!==s){for(n=[],o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=rv())!==s?o=u=[u,a,i,c]:(mi=o,o=s);o!==s;)n.push(o),o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=rv())!==s?o=u=[u,a,i,c]:(mi=o,o=s);n!==s?(Ei=t,e={type:"format",keyword:e,expr:(l=n)&&l.map(r=>r[3])},t=e):(mi=t,t=s)}else mi=t,t=s;var l;t===s&&(t=mi,"sqlstate"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(je)),e!==s&&(n=Hb())!==s&&(o=Rf())!==s?(Ei=t,t=e={type:"sqlstate",keyword:{type:"origin",value:"SQLSTATE"},expr:[o]}):(mi=t,t=s),t===s&&(t=mi,(e=zl())!==s&&(Ei=t,e={type:"condition",expr:[{type:"default",value:e}]}),t=e));return t}())===s&&(o=null),o!==s&&Hb()!==s?((u=function(){var t,e,n,o,u,a,i,c,f,p;if(t=mi,(e=dp())!==s)if(Hb()!==s)if("message"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(me)),n===s&&("detail"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(Ee)),n===s&&("hint"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(Ae)),n===s&&("errcode"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(ge)),n===s&&("column"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(Te)),n===s&&("constraint"===r.substr(mi,10).toLowerCase()?(n=r.substr(mi,10),mi+=10):(n=s,0===Si&&Ri(Se)),n===s&&("datatype"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(_e)),n===s&&("table"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(xe)),n===s&&("schema"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(l)))))))))),n!==s)if(Hb()!==s)if(mb()!==s)if(Hb()!==s)if((o=Nl())!==s){for(u=[],a=mi,(i=Hb())!==s&&(c=Nb())!==s&&(f=Hb())!==s&&(p=Nl())!==s?a=i=[i,c,f,p]:(mi=a,a=s);a!==s;)u.push(a),a=mi,(i=Hb())!==s&&(c=Nb())!==s&&(f=Hb())!==s&&(p=Nl())!==s?a=i=[i,c,f,p]:(mi=a,a=s);u!==s?(Ei=t,e=function(r,t,e){const n=[t];return e&&e.forEach(r=>n.push(r[3])),{type:"using",option:r,symbol:"=",expr:n}}(n,o,u),t=e):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;return t}())===s&&(u=null),u!==s?(Ei=t,a=n,i=o,c=u,e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"raise",level:a,using:c,raise:i}},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var a,i,c;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,"execute"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(it));e!==s&&Hb()!==s&&(n=zl())!==s&&Hb()!==s?(o=mi,(u=kb())!==s&&(a=Hb())!==s&&(i=nv())!==s&&(c=Hb())!==s&&(l=Ub())!==s?o=u=[u,a,i,c,l]:(mi=o,o=s),o===s&&(o=null),o!==s?(Ei=t,e=function(r,t){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"execute",name:r,args:t&&{type:"expr_list",value:t[2]}}}}(n,o),t=e):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c;t=mi,(e=function(){var t,e,n;t=mi,"for"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(vt));e!==s&&(Ei=t,e={label:null,keyword:"for"});(t=e)===s&&(t=mi,(e=zl())!==s&&Hb()!==s?("for"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(vt)),n!==s?(Ei=t,t=e={label:e,keyword:"for"}):(mi=t,t=s)):(mi=t,t=s));return t}())!==s&&Hb()!==s&&(n=zl())!==s&&Hb()!==s&&Sp()!==s&&Hb()!==s&&(o=Nc())!==s&&Hb()!==s?("loop"===r.substr(mi,4).toLowerCase()?(u=r.substr(mi,4),mi+=4):(u=s,0===Si&&Ri(Re)),u!==s&&Hb()!==s&&(a=Mi())!==s&&Hb()!==s&&Gp()!==s&&Hb()!==s?("loop"===r.substr(mi,4).toLowerCase()?(i=r.substr(mi,4),mi+=4):(i=s,0===Si&&Ri(Re)),i!==s&&Hb()!==s?((c=zl())===s&&(c=null),c!==s?(Ei=mi,f=c,(!(!(l=e).label||!f||l.label!==f)||!l.label&&!f?void 0:s)!==s?(Ei=t,e=function(r,t,e,n,o){return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"for",label:o,target:t,query:e,stmts:n.ast}}}(0,n,o,a,c),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var l,f;return t}()),t}function Ui(){var t;return(t=Pi())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=mi,(t=Hb())!==s?((e=Oc())===s&&(e=null),e!==s&&Hb()!==s&&Qf()!==s&&Hb()!==s&&(n=Zc())!==s&&Hb()!==s&&ip()!==s&&Hb()!==s&&(o=hl())!==s&&Hb()!==s?((u=Vc())===s&&(u=null),u!==s&&Hb()!==s?((a=sl())===s&&(a=null),a!==s&&Hb()!==s?((i=Cl())===s&&(i=null),i!==s?(Ei=r,t=function(r,t,e,n,o,s){const u={},a=r=>{const{server:t,db:e,schema:n,as:o,table:s,join:a}=r,i=a?"select":"update",c=[t,e,n].filter(Boolean).join(".")||null;e&&(u[s]=c),s&&Av.add(`${i}::${c}::${s}`)};return t&&t.forEach(a),n&&n.forEach(a),e&&e.forEach(r=>{if(r.table){const t=Lv(r.table);Av.add(`update::${u[t]||null}::${t}`)}gv.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Av),columnList:Cv(gv),ast:{with:r,type:"update",table:t,set:e,from:n,where:o,returning:s}}}(e,n,o,u,a,i),r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=Al())!==s&&Hb()!==s?((n=up())===s&&(n=null),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s?((u=El())===s&&(u=null),u!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(a=Ql())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&(i=ml())!==s&&Hb()!==s?((c=function(){var t,e,n,o;t=mi,pp()!==s&&Hb()!==s?("conflict"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(nn)),e!==s&&Hb()!==s?((n=function(){var r,t;r=mi,kb()!==s&&Hb()!==s&&(t=ul())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r={type:"column",expr:t,parentheses:!0}):(mi=r,r=s);return r}())===s&&(n=null),n!==s&&Hb()!==s&&(o=function(){var t,e,n,o,u;t=mi,"do"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(tn));e!==s&&Hb()!==s?("nothing"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(en)),n!==s?(Ei=t,t=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,"do"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(tn)),e!==s&&Hb()!==s&&(n=Qf())!==s&&Hb()!==s&&ip()!==s&&Hb()!==s&&(o=hl())!==s&&Hb()!==s?((u=sl())===s&&(u=null),u!==s?(Ei=t,t=e={keyword:"do",expr:{type:"update",set:o,where:u}}):(mi=t,t=s)):(mi=t,t=s));return t}())!==s?(Ei=t,t={type:"conflict",keyword:"on",target:n,action:o}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(c=null),c!==s&&Hb()!==s?((l=Cl())===s&&(l=null),l!==s?(Ei=t,e=function(r,t,e,n,o,s,u){if(t&&(Av.add(`insert::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>gv.add(`insert::${r}::${t}`))}return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r,table:[t],columns:n,values:o,partition:e,conflict:s,returning:u}}}(e,o,u,a,i,c,l),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=mi,(t=Al())!==s&&Hb()!==s?((e=op())===s&&(e=null),e!==s&&Hb()!==s?((n=up())===s&&(n=null),n!==s&&Hb()!==s&&(o=el())!==s&&Hb()!==s?((u=El())===s&&(u=null),u!==s&&Hb()!==s&&(a=ml())!==s&&Hb()!==s?((i=Cl())===s&&(i=null),i!==s?(Ei=r,t=function(r,t,e,n,o,s,u){n&&(Av.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),gv.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:r,table:[n],columns:null,values:s,partition:o,prefix:a,returning:u}}}(t,e,n,o,u,a,i),r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o;r=mi,(t=Jf())!==s&&Hb()!==s?((e=Zc())===s&&(e=null),e!==s&&Hb()!==s&&(n=Vc())!==s&&Hb()!==s?((o=sl())===s&&(o=null),o!==s?(Ei=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,schema:n,table:o,join:s}=r,u=s?"select":"delete",a=[t,n].filter(Boolean).join(".")||null;o&&Av.add(`${u}::${a}::${o}`),s||gv.add(`delete::${o}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(t=ki())===s&&(t=function(){var r,t;r=[],t=Qb();for(;t!==s;)r.push(t),t=Qb();return r}()),t}function Mi(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ui())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Pb())!==s&&(a=Hb())!==s&&(i=Ui())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Pb())!==s&&(a=Hb())!==s&&(i=Ui())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Av),columnList:Cv(gv),ast:n}}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Di(){var t,e,n,o;return t=mi,(e=function(){var t,e,n,o;t=mi,"union"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Qs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s?((n=Ap())===s&&(n=gp()),n===s&&(n=null),n!==s?(Ei=t,t=e=(o=n)?"union "+o.toLowerCase():"union"):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=function(){var t,e,n,o;t=mi,"intersect"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Ks));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e="intersect"),(t=e)===s&&(t=mi,(e=function(){var t,e,n,o;t=mi,"except"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(zs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e="except"),t=e)),t}function Pi(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Nc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Di())!==s&&(a=Hb())!==s&&(i=Nc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Di())!==s&&(a=Hb())!==s&&(i=Nc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s&&(n=Hb())!==s?((o=vl())===s&&(o=null),o!==s&&(u=Hb())!==s?((a=wl())===s&&(a=null),a!==s?(Ei=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&n.value&&n.value.length>0&&(r._limit=n),{tableList:Array.from(Av),columnList:Cv(gv),ast:r}}(t,e,o,a)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)}else mi=r,r=s;return r}function Gi(){var t,e;return t=mi,"if"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(i)),e!==s&&Hb()!==s&&Rp()!==s&&Hb()!==s&&Ip()!==s?(Ei=t,t=e="IF NOT EXISTS"):(mi=t,t=s),t}function $i(){var t,e,n;return t=mi,"check_option"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(d)),e!==s&&Hb()!==s&&mb()!==s&&Hb()!==s?("cascaded"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(p)),n===s&&("local"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(b))),n!==s?(Ei=t,t=e={type:"check_option",value:n,symbol:"="}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"security_barrier"===r.substr(mi,16).toLowerCase()?(e=r.substr(mi,16),mi+=16):(e=s,0===Si&&Ri(w)),e===s&&("security_invoker"===r.substr(mi,16).toLowerCase()?(e=r.substr(mi,16),mi+=16):(e=s,0===Si&&Ri(h))),e!==s&&Hb()!==s&&mb()!==s&&Hb()!==s&&(n=If())!==s?(Ei=t,t=e=function(r,t){return{type:r.toLowerCase(),value:t.value?"true":"false",symbol:"="}}(e,n)):(mi=t,t=s)),t}function Fi(){var r,t,e,n;return r=mi,(t=zl())!==s&&Hb()!==s&&mb()!==s&&Hb()!==s?((e=zl())===s&&(e=Nl()),e!==s?(Ei=r,r=t={type:t,symbol:"=",value:"string"==typeof(n=e)?{type:"default",value:n}:n}):(mi=r,r=s)):(mi=r,r=s),r}function Hi(){var r,t,e;return r=mi,(t=Xl())!==s&&Hb()!==s&&(e=uv())!==s?(Ei=r,r=t={column:t,definition:e}):(mi=r,r=s),r}function Bi(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Hi())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Hi())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Hi())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function qi(){var t,e,n,o,u,a,i,c,l,f,p,b;return t=mi,(e=cf())!==s?(Ei=mi,("begin"!==e.toLowerCase()?void 0:s)!==s&&Hb()!==s?("constant"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(g)),n===s&&(n=null),n!==s&&Hb()!==s&&(o=uv())!==s&&Hb()!==s?((u=Ji())===s&&(u=null),u!==s&&Hb()!==s?(a=mi,(i=Rp())!==s&&(c=Hb())!==s&&(l=Hf())!==s?a=i=[i,c,l]:(mi=a,a=s),a===s&&(a=null),a!==s&&(i=Hb())!==s?(c=mi,(l=Bf())===s&&(":="===r.substr(mi,2)?(l=":=",mi+=2):(l=s,0===Si&&Ri(T))),l===s&&(l=null),l!==s&&(f=Hb())!==s?(p=mi,Si++,"begin"===r.substr(mi,5).toLowerCase()?(b=r.substr(mi,5),mi+=5):(b=s,0===Si&&Ri(S)),Si--,b!==s?(mi=p,p=void 0):p=s,p===s&&(p=Sf())===s&&(p=Nl()),p!==s?c=l=[l,f,p]:(mi=c,c=s)):(mi=c,c=s),c===s&&(c=null),c!==s&&(l=Hb())!==s?((f=Pb())===s&&(f=null),f!==s?(Ei=t,t=e=function(r,t,e,n,o,s,u){return{keyword:"variable",name:r,constant:t,datatype:e,collate:n,not_null:o&&"not null",definition:s&&s[0]&&{type:"default",keyword:s[0],value:s[2]}}}(e,n,o,u,a,c)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function Yi(){var r,t,e,n,o,u;if(r=mi,(t=qi())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=qi())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=qi())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e,1)):(mi=r,r=s)}else mi=r,r=s;return r}function Wi(){var t,e,n,o;return t=mi,"declare"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(_)),e!==s&&Hb()!==s&&(n=Yi())!==s?(Ei=t,o=n,t=e={tableList:Array.from(Av),columnList:Cv(gv),ast:{type:"declare",declare:o,symbol:";"}}):(mi=t,t=s),t}function Vi(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w,h;if(t=mi,"LANGUAGE"===r.substr(mi,8)?(e="LANGUAGE",mi+=8):(e=s,0===Si&&Ri(x)),e!==s&&(n=Hb())!==s&&(o=cf())!==s&&(u=Hb())!==s?(Ei=t,t=e={prefix:"LANGUAGE",type:"default",value:o}):(mi=t,t=s),t===s&&(t=mi,"transorm"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(j)),e!==s&&(n=Hb())!==s?(o=mi,"FOR"===r.substr(mi,3)?(u="FOR",mi+=3):(u=s,0===Si&&Ri(I)),u!==s&&(a=Hb())!==s?("TYPE"===r.substr(mi,4)?(i="TYPE",mi+=4):(i=s,0===Si&&Ri(R)),i!==s&&(c=Hb())!==s&&(l=cf())!==s?o=u=[u,a,i,c,l]:(mi=o,o=s)):(mi=o,o=s),o===s&&(o=null),o!==s&&(u=Hb())!==s?(Ei=t,t=e=(h=o)?{prefix:["TRANSORM",h[0].toUpperCase(),h[2].toUpperCase()].join(" "),type:"default",value:h[4]}:{type:"origin",value:"TRANSORM"}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"window"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(N)),e===s&&("immutable"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(O)),e===s&&("stable"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(k)),e===s&&("volatile"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(U)),e===s&&("strict"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(M)))))),e!==s&&(n=Hb())!==s?(Ei=t,t=e={type:"origin",value:e}):(mi=t,t=s),t===s&&(t=mi,"not"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(D)),e===s&&(e=null),e!==s&&(n=Hb())!==s?("leakproof"===r.substr(mi,9).toLowerCase()?(o=r.substr(mi,9),mi+=9):(o=s,0===Si&&Ri(P)),o!==s&&(u=Hb())!==s?(Ei=t,t=e={type:"origin",value:[e,"LEAKPROOF"].filter(r=>r).join(" ")}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"called"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(G)),e===s&&(e=mi,"returns"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(E)),n!==s&&(o=Hb())!==s?("null"===r.substr(mi,4).toLowerCase()?(u=r.substr(mi,4),mi+=4):(u=s,0===Si&&Ri($)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s)),e===s&&(e=null),e!==s&&(n=Hb())!==s?("on"===r.substr(mi,2).toLowerCase()?(o=r.substr(mi,2),mi+=2):(o=s,0===Si&&Ri(F)),o!==s&&(u=Hb())!==s?("null"===r.substr(mi,4).toLowerCase()?(a=r.substr(mi,4),mi+=4):(a=s,0===Si&&Ri($)),a!==s&&(i=Hb())!==s?("input"===r.substr(mi,5).toLowerCase()?(c=r.substr(mi,5),mi+=5):(c=s,0===Si&&Ri(H)),c!==s&&(l=Hb())!==s?(Ei=t,t=e=function(r){return Array.isArray(r)&&(r=[r[0],r[2]].join(" ")),{type:"origin",value:r+" ON NULL INPUT"}}(e)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"external"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(B)),e===s&&(e=null),e!==s&&(n=Hb())!==s?("security"===r.substr(mi,8).toLowerCase()?(o=r.substr(mi,8),mi+=8):(o=s,0===Si&&Ri(q)),o!==s&&(u=Hb())!==s?("invoker"===r.substr(mi,7).toLowerCase()?(a=r.substr(mi,7),mi+=7):(a=s,0===Si&&Ri(Y)),a===s&&("definer"===r.substr(mi,7).toLowerCase()?(a=r.substr(mi,7),mi+=7):(a=s,0===Si&&Ri(W))),a!==s&&(i=Hb())!==s?(Ei=t,t=e=function(r,t){return{type:"origin",value:[r,"SECURITY",t].filter(r=>r).join(" ")}}(e,a)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"parallel"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(V)),e!==s&&(n=Hb())!==s?("unsafe"===r.substr(mi,6).toLowerCase()?(o=r.substr(mi,6),mi+=6):(o=s,0===Si&&Ri(X)),o===s&&("restricted"===r.substr(mi,10).toLowerCase()?(o=r.substr(mi,10),mi+=10):(o=s,0===Si&&Ri(Q)),o===s&&("safe"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(K)))),o!==s&&(u=Hb())!==s?(Ei=t,t=e=function(r){return{type:"origin",value:["PARALLEL",r].join(" ")}}(o)):(mi=t,t=s)):(mi=t,t=s),t===s))))))){if(t=mi,(e=cp())!==s)if((n=Hb())!==s){if(o=[],z.test(r.charAt(mi))?(u=r.charAt(mi),mi++):(u=s,0===Si&&Ri(Z)),u!==s)for(;u!==s;)o.push(u),z.test(r.charAt(mi))?(u=r.charAt(mi),mi++):(u=s,0===Si&&Ri(Z));else o=s;if(o!==s)if((u=Hb())!==s)if((a=Wi())===s&&(a=null),a!==s)if((i=Hb())!==s)if("begin"===r.substr(mi,5).toLowerCase()?(c=r.substr(mi,5),mi+=5):(c=s,0===Si&&Ri(S)),c===s&&(c=null),c!==s)if((l=Hb())!==s)if((f=Mi())!==s)if(Hb()!==s)if((p=Gp())===s&&(p=null),p!==s)if(Ei=mi,w=p,((d=c)&&w||!d&&!w?void 0:s)!==s)if(Hb()!==s)if((b=Pb())===s&&(b=null),b!==s)if(Hb()!==s){if(v=[],J.test(r.charAt(mi))?(y=r.charAt(mi),mi++):(y=s,0===Si&&Ri(rr)),y!==s)for(;y!==s;)v.push(y),J.test(r.charAt(mi))?(y=r.charAt(mi),mi++):(y=s,0===Si&&Ri(rr));else v=s;v!==s&&(y=Hb())!==s?(Ei=t,t=e=function(r,t,e,n,o,s){const u=r.join(""),a=s.join("");if(u!==a)throw new Error(`start symbol '${u}'is not same with end symbol '${a}'`);return{type:"as",declare:t&&t.ast,begin:e,expr:Array.isArray(n.ast)?n.ast.flat():[n.ast],end:o&&o[0],symbol:u}}(o,a,c,f,p,v)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s}else mi=t,t=s;else mi=t,t=s;t===s&&(t=mi,"cost"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(tr)),e===s&&("rows"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(er))),e!==s&&(n=Hb())!==s&&(o=Uf())!==s&&(u=Hb())!==s?(Ei=t,t=e=function(r,t){return t.prefix=r,t}(e,o)):(mi=t,t=s),t===s&&(t=mi,"support"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(nr)),e!==s&&(n=Hb())!==s&&(o=tv())!==s&&(u=Hb())!==s?(Ei=t,t=e=function(r){return{prefix:"support",type:"default",value:r}}(o)):(mi=t,t=s),t===s&&(t=mi,(e=ip())!==s&&(n=Hb())!==s&&(o=cf())!==s&&(u=Hb())!==s?(a=mi,"to"===r.substr(mi,2).toLowerCase()?(i=r.substr(mi,2),mi+=2):(i=s,0===Si&&Ri(or)),i===s&&(61===r.charCodeAt(mi)?(i="=",mi++):(i=s,0===Si&&Ri(sr))),i!==s&&(c=Hb())!==s&&(l=Zl())!==s?a=i=[i,c,l]:(mi=a,a=s),a===s&&(a=mi,(i=ap())!==s&&(c=Hb())!==s?("current"===r.substr(mi,7).toLowerCase()?(l=r.substr(mi,7),mi+=7):(l=s,0===Si&&Ri(ur)),l!==s?a=i=[i,c,l]:(mi=a,a=s)):(mi=a,a=s)),a===s&&(a=null),a!==s&&(i=Hb())!==s?(Ei=t,t=e=function(r,t){let e;if(t){const r=Array.isArray(t[2])?t[2]:[t[2]];e={prefix:t[0],expr:r.map(r=>({type:"default",value:r}))}}return{type:"set",parameter:r,value:e}}(o,a)):(mi=t,t=s)):(mi=t,t=s))))}return t}function Xi(){var t;return(t=function(){var t,e,n,o,u,a;return t=mi,"increment"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(lr)),e!==s&&Hb()!==s?((n=Lp())===s&&(n=null),n!==s&&Hb()!==s&&(o=Uf())!==s?(Ei=t,u=e,a=o,t=e={resource:"sequence",prefix:n?u.toLowerCase()+" by":u.toLowerCase(),value:a}):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=mi,"minvalue"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(fr)),e!==s&&Hb()!==s&&(n=Uf())!==s?(Ei=t,t=e=pr(e,n)):(mi=t,t=s),t===s&&(t=mi,"no"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(br)),e!==s&&Hb()!==s?("minvalue"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(fr)),n!==s?(Ei=t,t=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(mi=t,t=s)):(mi=t,t=s)),t}())===s&&(t=function(){var t,e,n;return t=mi,"maxvalue"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(vr)),e!==s&&Hb()!==s&&(n=Uf())!==s?(Ei=t,t=e=pr(e,n)):(mi=t,t=s),t===s&&(t=mi,"no"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(br)),e!==s&&Hb()!==s?("maxvalue"===r.substr(mi,8).toLowerCase()?(n=r.substr(mi,8),mi+=8):(n=s,0===Si&&Ri(vr)),n!==s?(Ei=t,t=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(mi=t,t=s)):(mi=t,t=s)),t}())===s&&(t=function(){var t,e,n,o,u,a;return t=mi,"start"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(yr)),e!==s&&Hb()!==s?((n=wp())===s&&(n=null),n!==s&&Hb()!==s&&(o=Uf())!==s?(Ei=t,u=e,a=o,t=e={resource:"sequence",prefix:n?u.toLowerCase()+" with":u.toLowerCase(),value:a}):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=mi,"cache"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(dr)),e!==s&&Hb()!==s&&(n=Uf())!==s?(Ei=t,t=e=pr(e,n)):(mi=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=mi,"no"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(br)),e===s&&(e=null),e!==s&&Hb()!==s?("cycle"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(wr)),n!==s?(Ei=t,t=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(t=function(){var t,e,n;return t=mi,"owned"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(hr)),e!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s?("none"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(Lr)),n!==s?(Ei=t,t=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"owned"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(hr)),e!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s&&(n=Xl())!==s?(Ei=t,t=e={resource:"sequence",prefix:"owned by",value:n}):(mi=t,t=s)),t}()),t}function Qi(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y;return t=mi,(e=Nl())!==s&&Hb()!==s?((n=Ji())===s&&(n=null),n!==s&&Hb()!==s?((o=zl())===s&&(o=null),o!==s&&Hb()!==s?((u=mp())===s&&(u=Ep()),u===s&&(u=null),u!==s&&Hb()!==s?(a=mi,"nulls"===r.substr(mi,5).toLowerCase()?(i=r.substr(mi,5),mi+=5):(i=s,0===Si&&Ri(Cr)),i!==s&&(c=Hb())!==s?("first"===r.substr(mi,5).toLowerCase()?(l=r.substr(mi,5),mi+=5):(l=s,0===Si&&Ri(mr)),l===s&&("last"===r.substr(mi,4).toLowerCase()?(l=r.substr(mi,4),mi+=4):(l=s,0===Si&&Ri(Er))),l!==s?a=i=[i,c,l]:(mi=a,a=s)):(mi=a,a=s),a===s&&(a=null),a!==s?(Ei=t,f=e,p=n,b=o,v=u,y=a,t=e={...f,collate:p,opclass:b,order_by:v&&v.toLowerCase(),nulls:y&&`${y[0].toLowerCase()} ${y[2].toLowerCase()}`}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function Ki(){var r;return(r=Zi())===s&&(r=pc())===s&&(r=bc())===s&&(r=vc()),r}function zi(){var t,e,n,o;return(t=function(){var r,t,e;r=mi,(t=jf())===s&&(t=xf());t!==s&&Hb()!==s?((e=tc())===s&&(e=null),e!==s?(Ei=r,o=e,(n=t)&&!n.value&&(n.value="null"),r=t={default_val:o,nullable:n}):(mi=r,r=s)):(mi=r,r=s);var n,o;r===s&&(r=mi,(t=tc())!==s&&Hb()!==s?((e=jf())===s&&(e=xf()),e===s&&(e=null),e!==s?(Ei=r,t=function(r,t){return t&&!t.value&&(t.value="null"),{default_val:r,nullable:t}}(t,e),r=t):(mi=r,r=s)):(mi=r,r=s));return r}())===s&&(t=mi,"auto_increment"===r.substr(mi,14).toLowerCase()?(e=r.substr(mi,14),mi+=14):(e=s,0===Si&&Ri(Ar)),e!==s&&(Ei=t,e={auto_increment:e.toLowerCase()}),(t=e)===s&&(t=mi,"unique"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(gr)),e!==s&&Hb()!==s?("key"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(Tr)),n===s&&(n=null),n!==s?(Ei=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"primary"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Sr)),e===s&&(e=null),e!==s&&Hb()!==s?("key"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(Tr)),n!==s?(Ei=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=Yb())!==s&&(Ei=t,e={comment:e}),(t=e)===s&&(t=mi,(e=Ji())!==s&&(Ei=t,e={collate:e}),(t=e)===s&&(t=mi,(e=function(){var t,e,n;t=mi,"column_format"===r.substr(mi,13).toLowerCase()?(e=r.substr(mi,13),mi+=13):(e=s,0===Si&&Ri(_r));e!==s&&Hb()!==s?("fixed"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(xr)),n===s&&("dynamic"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(jr)),n===s&&("default"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(Ir)))),n!==s?(Ei=t,e={type:"column_format",value:n.toLowerCase()},t=e):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e={column_format:e}),(t=e)===s&&(t=mi,(e=function(){var t,e,n;t=mi,"storage"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Rr));e!==s&&Hb()!==s?("disk"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(Nr)),n===s&&("memory"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(Or))),n!==s?(Ei=t,e={type:"storage",value:n.toLowerCase()},t=e):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e={storage:e}),(t=e)===s&&(t=mi,(e=wc())!==s&&(Ei=t,e={reference_definition:e}),(t=e)===s&&(t=mi,(e=Cc())!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s&&(o=Kl())!==s?(Ei=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s)))))))))),t}function Zi(){var r,t,e,n,o,u,a;return r=mi,(t=Xl())!==s&&Hb()!==s&&(e=uv())!==s&&Hb()!==s?((n=function(){var r,t,e,n,o,u;if(r=mi,(t=zi())!==s)if(Hb()!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=zi())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=zi())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;return r}())===s&&(n=null),n!==s?(Ei=r,o=t,u=e,a=n,gv.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:u,resource:"column",...a||{}}):(mi=r,r=s)):(mi=r,r=s),r}function Ji(){var t,e,n;return t=mi,function(){var t,e,n,o;t=mi,"collate"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(mt));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="COLLATE"):(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s?((e=mb())===s&&(e=null),e!==s&&Hb()!==s&&(n=zl())!==s?(Ei=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(mi=t,t=s)):(mi=t,t=s),t}function rc(){var r,t,e,n,o;return r=mi,(t=Bf())===s&&(t=mb()),t===s&&(t=null),t!==s&&Hb()!==s&&(e=Nl())!==s?(Ei=r,o=e,r=t={type:"default",keyword:(n=t)&&n[0],value:o}):(mi=r,r=s),r}function tc(){var r,t;return r=mi,Bf()!==s&&Hb()!==s&&(t=Nl())!==s?(Ei=r,r={type:"default",value:t}):(mi=r,r=s),r}function ec(){var r,t,e;return r=mi,(t=Ob())!==s&&(Ei=r,t=[{name:"*"}]),(r=t)===s&&(r=mi,(t=sc())===s&&(t=null),t!==s&&Hb()!==s&&Cp()!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s&&(e=sc())!==s?(Ei=r,r=t=function(r,t){const e=r||[];return e.orderby=t,e}(t,e)):(mi=r,r=s),r===s&&(r=sc())),r}function nc(){var t,e;return t=mi,(e=Sp())===s&&("out"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Mr)),e===s&&("variadic"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Dr)),e===s&&("inout"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Pr))))),e!==s&&(Ei=t,e=e.toUpperCase()),t=e}function oc(){var r,t,e,n,o;return r=mi,(t=nc())===s&&(t=null),t!==s&&Hb()!==s&&(e=uv())!==s&&Hb()!==s?((n=rc())===s&&(n=null),n!==s?(Ei=r,r=t={mode:t,type:e,default:n}):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=nc())===s&&(t=null),t!==s&&Hb()!==s&&(e=cf())!==s&&Hb()!==s&&(n=uv())!==s&&Hb()!==s?((o=rc())===s&&(o=null),o!==s?(Ei=r,r=t=function(r,t,e,n){return{mode:r,name:t,type:e,default:n}}(t,e,n,o)):(mi=r,r=s)):(mi=r,r=s)),r}function sc(){var r,t,e,n,o,u,a,i;if(r=mi,(t=oc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=oc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=oc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function uc(){var r;return(r=function(){var r,t,e,n;r=mi,(t=Eb())!==s&&Hb()!==s?((e=Ab())===s&&(e=null),e!==s&&Hb()!==s&&(n=Zi())!==s?(Ei=r,o=e,u=n,t={action:"add",...u,keyword:o,resource:"column",type:"alter"},r=t):(mi=r,r=s)):(mi=r,r=s);var o,u;return r}())===s&&(r=function(){var r,t;r=mi,Eb()!==s&&Hb()!==s&&(t=vc())!==s?(Ei=r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(mi=r,r=s);return r}())===s&&(r=function(){var r,t,e;r=mi,Wf()!==s&&Hb()!==s?((t=Ab())===s&&(t=null),t!==s&&Hb()!==s&&(e=Xl())!==s?(Ei=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(r=function(){var r,t,e;r=mi,(t=Eb())!==s&&Hb()!==s&&(e=pc())!==s?(Ei=r,n=e,t={action:"add",type:"alter",...n},r=t):(mi=r,r=s);var n;return r}())===s&&(r=function(){var r,t,e;r=mi,(t=Eb())!==s&&Hb()!==s&&(e=bc())!==s?(Ei=r,n=e,t={action:"add",type:"alter",...n},r=t):(mi=r,r=s);var n;return r}())===s&&(r=ac())===s&&(r=lc())===s&&(r=fc()),r}function ac(){var r,t,e,n,o;return r=mi,np()!==s&&Hb()!==s?((t=qf())===s&&(t=cp()),t===s&&(t=null),t!==s&&Hb()!==s&&(e=zl())!==s?(Ei=r,o=e,r={action:"rename",type:"alter",resource:"table",keyword:(n=t)&&n[0].toLowerCase(),table:o}):(mi=r,r=s)):(mi=r,r=s),r}function ic(){var t,e,n;return t=mi,"owner"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Gr)),e!==s&&Hb()!==s&&qf()!==s&&Hb()!==s?((n=zl())===s&&("current_role"===r.substr(mi,12).toLowerCase()?(n=r.substr(mi,12),mi+=12):(n=s,0===Si&&Ri($r)),n===s&&("current_user"===r.substr(mi,12).toLowerCase()?(n=r.substr(mi,12),mi+=12):(n=s,0===Si&&Ri(Fr)),n===s&&("session_user"===r.substr(mi,12).toLowerCase()?(n=r.substr(mi,12),mi+=12):(n=s,0===Si&&Ri(Hr))))),n!==s?(Ei=t,t=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(mi=t,t=s)):(mi=t,t=s),t}function cc(){var r,t;return r=mi,ip()!==s&&Hb()!==s&&fp()!==s&&Hb()!==s&&(t=zl())!==s?(Ei=r,r={action:"set",type:"alter",resource:"table",keyword:"schema",table:t}):(mi=r,r=s),r}function lc(){var t,e,n,o;return t=mi,"algorithm"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Br)),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s?("default"===r.substr(mi,7).toLowerCase()?(o=r.substr(mi,7),mi+=7):(o=s,0===Si&&Ri(Ir)),o===s&&("instant"===r.substr(mi,7).toLowerCase()?(o=r.substr(mi,7),mi+=7):(o=s,0===Si&&Ri(qr)),o===s&&("inplace"===r.substr(mi,7).toLowerCase()?(o=r.substr(mi,7),mi+=7):(o=s,0===Si&&Ri(Yr)),o===s&&("copy"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(Wr))))),o!==s?(Ei=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function fc(){var t,e,n,o;return t=mi,"lock"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Vr)),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s?("default"===r.substr(mi,7).toLowerCase()?(o=r.substr(mi,7),mi+=7):(o=s,0===Si&&Ri(Ir)),o===s&&("none"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(Lr)),o===s&&("shared"===r.substr(mi,6).toLowerCase()?(o=r.substr(mi,6),mi+=6):(o=s,0===Si&&Ri(Xr)),o===s&&("exclusive"===r.substr(mi,9).toLowerCase()?(o=r.substr(mi,9),mi+=9):(o=s,0===Si&&Ri(Qr))))),o!==s?(Ei=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function pc(){var r,t,e,n,o,u,a,i;return r=mi,(t=gb())===s&&(t=Tb()),t!==s&&Hb()!==s?((e=uf())===s&&(e=null),e!==s&&Hb()!==s?((n=Qc())===s&&(n=null),n!==s&&Hb()!==s&&(o=Uc())!==s&&Hb()!==s?((u=Kc())===s&&(u=null),u!==s&&Hb()!==s?(Ei=r,a=n,i=u,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:a,resource:"index",index_options:i}):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r}function bc(){var t,e,n,o,u,a,i,c,l;return t=mi,(e=function(){var t,e,n,o;t=mi,"fulltext"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ha));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="FULLTEXT"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=mi,"spatial"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ba));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SPATIAL"):(mi=t,t=s)):(mi=t,t=s);return t}()),e!==s&&Hb()!==s?((n=gb())===s&&(n=Tb()),n===s&&(n=null),n!==s&&Hb()!==s?((o=uf())===s&&(o=null),o!==s&&Hb()!==s&&(u=Uc())!==s&&Hb()!==s?((a=Kc())===s&&(a=null),a!==s&&Hb()!==s?(Ei=t,i=e,l=a,t=e={index:o,definition:u,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function vc(){var t;return(t=function(){var t,e,n,o,u,a;t=mi,(e=yc())===s&&(e=null);e!==s&&Hb()!==s?("primary key"===r.substr(mi,11).toLowerCase()?(n=r.substr(mi,11),mi+=11):(n=s,0===Si&&Ri(Kr)),n!==s&&Hb()!==s?((o=Qc())===s&&(o=null),o!==s&&Hb()!==s&&(u=Uc())!==s&&Hb()!==s?((a=Kc())===s&&(a=null),a!==s?(Ei=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var i,c,l,f,p;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=mi,(t=yc())===s&&(t=null);t!==s&&Hb()!==s&&(e=Sb())!==s&&Hb()!==s?((n=gb())===s&&(n=Tb()),n===s&&(n=null),n!==s&&Hb()!==s?((o=uf())===s&&(o=null),o!==s&&Hb()!==s?((u=Qc())===s&&(u=null),u!==s&&Hb()!==s&&(a=Uc())!==s&&Hb()!==s?((i=Kc())===s&&(i=null),i!==s?(Ei=r,l=e,f=n,p=o,b=u,v=a,y=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);var c,l,f,p,b,v,y;return r}())===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=yc())===s&&(e=null);e!==s&&Hb()!==s?("foreign key"===r.substr(mi,11).toLowerCase()?(n=r.substr(mi,11),mi+=11):(n=s,0===Si&&Ri(zr)),n!==s&&Hb()!==s?((o=uf())===s&&(o=null),o!==s&&Hb()!==s&&(u=Uc())!==s&&Hb()!==s?((a=wc())===s&&(a=null),a!==s?(Ei=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var i,c,l,f,p;return t}())===s&&(t=dc()),t}function yc(){var r,t,e,n;return r=mi,(t=xb())!==s&&Hb()!==s?((e=zl())===s&&(e=null),e!==s?(Ei=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(mi=r,r=s)):(mi=r,r=s),r}function dc(){var t,e,n,o,u,a,i;return t=mi,(e=yc())===s&&(e=null),e!==s&&Hb()!==s?("check"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(v)),n!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(o=Ol())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,a=n,i=o,t=e={constraint:(u=e)&&u.constraint,definition:[i],constraint_type:a.toLowerCase(),keyword:u&&u.keyword,resource:"constraint"}):(mi=t,t=s)):(mi=t,t=s),t}function wc(){var t,e,n,o,u,a,i,c,l,f;return t=mi,(e=Ib())!==s&&Hb()!==s&&(n=el())!==s&&Hb()!==s&&(o=Uc())!==s&&Hb()!==s?("match full"===r.substr(mi,10).toLowerCase()?(u=r.substr(mi,10),mi+=10):(u=s,0===Si&&Ri(Zr)),u===s&&("match partial"===r.substr(mi,13).toLowerCase()?(u=r.substr(mi,13),mi+=13):(u=s,0===Si&&Ri(Jr)),u===s&&("match simple"===r.substr(mi,12).toLowerCase()?(u=r.substr(mi,12),mi+=12):(u=s,0===Si&&Ri(rt)))),u===s&&(u=null),u!==s&&Hb()!==s?((a=hc())===s&&(a=null),a!==s&&Hb()!==s?((i=hc())===s&&(i=null),i!==s?(Ei=t,c=u,l=a,f=i,t=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=hc())!==s&&(Ei=t,e={on_action:[e]}),t=e),t}function hc(){var t,e,n,o;return t=mi,pp()!==s&&Hb()!==s?((e=Jf())===s&&(e=Qf()),e!==s&&Hb()!==s&&(n=function(){var t,e,n;t=mi,(e=vb())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((n=Sl())===s&&(n=null),n!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,"restrict"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ur)),e===s&&("cascade"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(kr)),e===s&&("set null"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(tt)),e===s&&("no action"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(et)),e===s&&("set default"===r.substr(mi,11).toLowerCase()?(e=r.substr(mi,11),mi+=11):(e=s,0===Si&&Ri(nt)),e===s&&(e=vb()))))),e!==s&&(Ei=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==s?(Ei=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(mi=t,t=s)):(mi=t,t=s),t}function Lc(){var t,e,n,o,u,a,i;return t=mi,(e=rp())===s&&(e=Jf())===s&&(e=pb()),e!==s&&(Ei=t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===s&&(t=mi,(e=Qf())!==s&&Hb()!==s?(n=mi,"of"===r.substr(mi,2).toLowerCase()?(o=r.substr(mi,2),mi+=2):(o=s,0===Si&&Ri(lt)),o!==s&&(u=Hb())!==s&&(a=ul())!==s?n=o=[o,u,a]:(mi=n,n=s),n===s&&(n=null),n!==s?(Ei=t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):(mi=t,t=s)):(mi=t,t=s)),t}function Cc(){var t,e,n;return t=mi,"character"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(ht)),e!==s&&Hb()!==s?("set"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(Lt)),n!==s?(Ei=t,t=e="CHARACTER SET"):(mi=t,t=s)):(mi=t,t=s),t}function mc(){var t,e,n,o,u,a,i,c,l;return t=mi,(e=Bf())===s&&(e=null),e!==s&&Hb()!==s?((n=Cc())===s&&("charset"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(Ct)),n===s&&("collate"===r.substr(mi,7).toLowerCase()?(n=r.substr(mi,7),mi+=7):(n=s,0===Si&&Ri(mt)))),n!==s&&Hb()!==s?((o=mb())===s&&(o=null),o!==s&&Hb()!==s&&(u=Kl())!==s?(Ei=t,i=n,c=o,l=u,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function Ec(){var t,e,n,o,u,a,i,c,l;return t=mi,"auto_increment"===r.substr(mi,14).toLowerCase()?(e=r.substr(mi,14),mi+=14):(e=s,0===Si&&Ri(Ar)),e===s&&("avg_row_length"===r.substr(mi,14).toLowerCase()?(e=r.substr(mi,14),mi+=14):(e=s,0===Si&&Ri(Et)),e===s&&("key_block_size"===r.substr(mi,14).toLowerCase()?(e=r.substr(mi,14),mi+=14):(e=s,0===Si&&Ri(At)),e===s&&("max_rows"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(gt)),e===s&&("min_rows"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Tt)),e===s&&("stats_sample_pages"===r.substr(mi,18).toLowerCase()?(e=r.substr(mi,18),mi+=18):(e=s,0===Si&&Ri(St))))))),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s&&(o=Uf())!==s?(Ei=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mc())===s&&(t=mi,(e=_b())===s&&("connection"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(_t))),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s&&(o=Rf())!==s?(Ei=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"compression"===r.substr(mi,11).toLowerCase()?(e=r.substr(mi,11),mi+=11):(e=s,0===Si&&Ri(xt)),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s?(o=mi,39===r.charCodeAt(mi)?(u="'",mi++):(u=s,0===Si&&Ri(jt)),u!==s?("zlib"===r.substr(mi,4).toLowerCase()?(a=r.substr(mi,4),mi+=4):(a=s,0===Si&&Ri(It)),a===s&&("lz4"===r.substr(mi,3).toLowerCase()?(a=r.substr(mi,3),mi+=3):(a=s,0===Si&&Ri(Rt)),a===s&&("none"===r.substr(mi,4).toLowerCase()?(a=r.substr(mi,4),mi+=4):(a=s,0===Si&&Ri(Lr)))),a!==s?(39===r.charCodeAt(mi)?(i="'",mi++):(i=s,0===Si&&Ri(jt)),i!==s?o=u=[u,a,i]:(mi=o,o=s)):(mi=o,o=s)):(mi=o,o=s),o!==s?(Ei=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"engine"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Nt)),e!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s&&(o=cf())!==s?(Ei=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s)))),t}function Ac(){var t,e,n;return t=mi,(e=Xf())===s&&(e=rp())===s&&(e=Qf())===s&&(e=Jf())===s&&(e=pb())===s&&(e=Ib())===s&&("trigger"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ot))),e!==s&&(Ei=t,n=e,e={type:"origin",value:Array.isArray(n)?n[0]:n}),t=e}function gc(){var t,e,n,o;return t=mi,Ap()!==s?(e=mi,(n=Hb())!==s?("privileges"===r.substr(mi,10).toLowerCase()?(o=r.substr(mi,10),mi+=10):(o=s,0===Si&&Ri(Xt)),o!==s?e=n=[n,o]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=t,t={type:"origin",value:e?"all privileges":"all"}):(mi=t,t=s)):(mi=t,t=s),t}function Tc(){var t;return(t=Ac())===s&&(t=function(){var t,e;return t=mi,"usage"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Yt)),e===s&&(e=Xf())===s&&(e=Qf()),e!==s&&(Ei=t,e=Wt(e)),t=e}())===s&&(t=function(){var t,e;return t=mi,(e=Kf())===s&&("connect"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Vt)),e===s&&(e=zf())===s&&(e=Zf())),e!==s&&(Ei=t,e=Wt(e)),t=e}())===s&&(t=function(){var t,e;return t=mi,"usage"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Yt)),e!==s&&(Ei=t,e=Qt(e)),(t=e)===s&&(t=gc()),t}())===s&&(t=function(){var t,e;return t=mi,"execute"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(it)),e!==s&&(Ei=t,e=Qt(e)),(t=e)===s&&(t=gc()),t}()),t}function Sc(){var r,t,e,n,o,u,a,i,c;return r=mi,(t=Tc())!==s&&Hb()!==s?(e=mi,(n=kb())!==s&&(o=Hb())!==s&&(u=ul())!==s&&(a=Hb())!==s&&(i=Ub())!==s?e=n=[n,o,u,a,i]:(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=r,r=t={priv:t,columns:(c=e)&&c[2]}):(mi=r,r=s)):(mi=r,r=s),r}function _c(){var r,t,e,n,o;return r=mi,t=mi,(e=zl())!==s&&(n=Hb())!==s&&(o=Rb())!==s?t=e=[e,n,o]:(mi=t,t=s),t===s&&(t=null),t!==s&&(e=Hb())!==s?((n=zl())===s&&(n=Ob()),n!==s?(Ei=r,r=t=function(r,t){return{prefix:r&&r[0],name:t}}(t,n)):(mi=r,r=s)):(mi=r,r=s),r}function xc(){var t,e,n,o;return t=mi,(e=hp())===s&&(e=null),e!==s&&Hb()!==s&&(n=zl())!==s?(Ei=t,o=n,t=e={name:{type:"origin",value:e?`${group} ${o}`:o}}):(mi=t,t=s),t===s&&(t=mi,"public"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ae)),e===s&&(e=function(){var t,e,n,o;t=mi,"current_role"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri($r));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CURRENT_ROLE"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(e=yb())===s&&(e=db()),e!==s&&(Ei=t,e=function(r){return{name:{type:"origin",value:r}}}(e)),t=e),t}function jc(){var r,t,e,n,o,u,a,i;if(r=mi,(t=xc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=xc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=xc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Ic(){var t,e,n,o,u,a,i,c;return t=mi,"grant"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ie)),e!==s&&(Ei=t,e={type:"grant"}),(t=e)===s&&(t=mi,"revoke"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(fe)),e!==s&&Hb()!==s?(n=mi,"grant"===r.substr(mi,5).toLowerCase()?(o=r.substr(mi,5),mi+=5):(o=s,0===Si&&Ri(ie)),o!==s&&(u=Hb())!==s?("option"===r.substr(mi,6).toLowerCase()?(a=r.substr(mi,6),mi+=6):(a=s,0===Si&&Ri(ce)),a!==s&&(i=Hb())!==s?("for"===r.substr(mi,3).toLowerCase()?(c=r.substr(mi,3),mi+=3):(c=s,0===Si&&Ri(vt)),c!==s?n=o=[o,u,a,i,c]:(mi=n,n=s)):(mi=n,n=s)):(mi=n,n=s),n===s&&(n=null),n!==s?(Ei=t,t=e={type:"revoke",grant_option_for:n&&{type:"origin",value:"grant option for"}}):(mi=t,t=s)):(mi=t,t=s)),t}function Rc(){var t,e,n,o,u,a;return t=mi,"elseif"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(pe)),e!==s&&Hb()!==s&&(n=Nl())!==s&&Hb()!==s?("then"===r.substr(mi,4).toLowerCase()?(o=r.substr(mi,4),mi+=4):(o=s,0===Si&&Ri(be)),o!==s&&Hb()!==s&&(u=Ui())!==s&&Hb()!==s?((a=Pb())===s&&(a=null),a!==s?(Ei=t,t=e={type:"elseif",boolean_expr:n,then:u,semicolon:a}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function Nc(){var t,e,n,o,u,a,i;return t=mi,(e=Xf())!==s&&(n=Hb())!==s?(59===r.charCodeAt(mi)?(o=";",mi++):(o=s,0===Si&&Ri(Ne)),o!==s?(Ei=t,t=e={type:"select"}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=Dc())===s&&(t=mi,e=mi,40===r.charCodeAt(mi)?(n="(",mi++):(n=s,0===Si&&Ri(Oe)),n!==s&&(o=Hb())!==s&&(u=Nc())!==s&&(a=Hb())!==s?(41===r.charCodeAt(mi)?(i=")",mi++):(i=s,0===Si&&Ri(Ue)),i!==s?e=n=[n,o,u,a,i]:(mi=e,e=s)):(mi=e,e=s),e!==s&&(Ei=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Oc(){var r,t,e,n,o,u,a,i,c;if(r=mi,wp()!==s)if(Hb()!==s)if((t=kc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=kc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=kc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;return r===s&&(r=mi,Hb()!==s&&wp()!==s&&(t=Hb())!==s&&(e=tp())!==s&&(n=Hb())!==s&&(o=kc())!==s?(Ei=r,(c=o).recursive=!0,r=[c]):(mi=r,r=s)),r}function kc(){var r,t,e,n;return r=mi,(t=Rf())===s&&(t=cf()),t!==s&&Hb()!==s?((e=Uc())===s&&(e=null),e!==s&&Hb()!==s&&cp()!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=Ui())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r=t=function(r,t,e){return"string"==typeof r&&(r={type:"default",value:r}),{name:r,stmt:e.ast,columns:t}}(t,e,n)):(mi=r,r=s)):(mi=r,r=s),r}function Uc(){var r,t;return r=mi,kb()!==s&&Hb()!==s&&(t=ul())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r=t):(mi=r,r=s),r}function Mc(){var r,t,e,n,o;return r=mi,(t=gp())!==s&&Hb()!==s&&pp()!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(e=ul())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,n=t,o=e,console.lo,r=t={type:n+" ON",columns:o}):(mi=r,r=s),r===s&&(r=mi,(t=gp())===s&&(t=null),t!==s&&(Ei=r,t=function(r){return{type:r}}(t)),r=t),r}function Dc(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d;return t=mi,Hb()!==s?((e=Oc())===s&&(e=null),e!==s&&Hb()!==s&&Xf()!==s&&Bb()!==s?((n=function(){var r,t,e,n,o,u;if(r=mi,(t=Pc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Pc())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Pc())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())===s&&(n=null),n!==s&&Hb()!==s?((o=Mc())===s&&(o=null),o!==s&&Hb()!==s&&(u=Gc())!==s&&Hb()!==s?((a=Wc())===s&&(a=null),a!==s&&Hb()!==s?((i=Vc())===s&&(i=null),i!==s&&Hb()!==s?((c=Wc())===s&&(c=null),c!==s&&Hb()!==s?((l=sl())===s&&(l=null),l!==s&&Hb()!==s?((f=function(){var r,t,e;r=mi,(t=hp())!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s&&(e=Sl())!==s?(Ei=r,t={columns:e.value},r=t):(mi=r,r=s);return r}())===s&&(f=null),f!==s&&Hb()!==s?((p=function(){var t,e;t=mi,function(){var t,e,n,o;t=mi,"having"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ou));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s&&(e=Ol())!==s?(Ei=t,t=e):(mi=t,t=s);return t}())===s&&(p=null),p!==s&&Hb()!==s?((b=vl())===s&&(b=null),b!==s&&Hb()!==s?((v=wl())===s&&(v=null),v!==s&&Hb()!==s?((y=function(){var t,e;t=mi,function(){var t,e,n,o;t=mi,"window"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(N));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=al())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=al())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=al())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=t,t={keyword:"window",type:"window",expr:e}):(mi=t,t=s);return t}())===s&&(y=null),y!==s&&Hb()!==s?((d=Wc())===s&&(d=null),d!==s?(Ei=t,t=function(r,t,e,n,o,s,u,a,i,c,l,f,p,b){if(o&&u||o&&b||u&&b||o&&u&&b)throw new Error("A given SQL statement can contain at most one INTO clause");return s&&s.forEach(r=>r.table&&Av.add(`select::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{with:r,type:"select",options:t,distinct:e,columns:n,into:{...o||u||b||{},position:(o?"column":u&&"from")||b&&"end"},from:s,where:a,groupby:i,having:c,orderby:l,limit:f,window:p}}(e,n,o,u,a,i,c,l,f,p,b,v,y,d)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function Pc(){var t,e;return t=mi,(e=function(){var t;"sql_calc_found_rows"===r.substr(mi,19).toLowerCase()?(t=r.substr(mi,19),mi+=19):(t=s,0===Si&&Ri(Va));return t}())===s&&((e=function(){var t;"sql_cache"===r.substr(mi,9).toLowerCase()?(t=r.substr(mi,9),mi+=9):(t=s,0===Si&&Ri(Xa));return t}())===s&&(e=function(){var t;"sql_no_cache"===r.substr(mi,12).toLowerCase()?(t=r.substr(mi,12),mi+=12):(t=s,0===Si&&Ri(Qa));return t}()),e===s&&(e=function(){var t;"sql_big_result"===r.substr(mi,14).toLowerCase()?(t=r.substr(mi,14),mi+=14):(t=s,0===Si&&Ri(za));return t}())===s&&(e=function(){var t;"sql_small_result"===r.substr(mi,16).toLowerCase()?(t=r.substr(mi,16),mi+=16):(t=s,0===Si&&Ri(Ka));return t}())===s&&(e=function(){var t;"sql_buffer_result"===r.substr(mi,17).toLowerCase()?(t=r.substr(mi,17),mi+=17):(t=s,0===Si&&Ri(Za));return t}())),e!==s&&(Ei=t,e=e),t=e}function Gc(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ap())===s&&(t=mi,(e=Ob())!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t===s&&(t=Ob())),t!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Bc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Bc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){gv.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?wv(e,t):[e]}(0,e)):(mi=r,r=s)}else mi=r,r=s;if(r===s)if(r=mi,(t=Bc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Bc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Bc())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function $c(){var r,t;return r=mi,Mb()!==s&&Hb()!==s?((t=Uf())===s&&(t=Rf()),t!==s&&Hb()!==s&&Db()!==s?(Ei=r,r={brackets:!0,index:t}):(mi=r,r=s)):(mi=r,r=s),r}function Fc(){var r,t,e,n,o,u;if(r=mi,(t=$c())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=$c())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=$c())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e,1)):(mi=r,r=s)}else mi=r,r=s;return r}function Hc(){var r,t,e,n,o;return r=mi,(t=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Nl())!==s){for(e=[],n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op())===s&&(u=Fb()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op())===s&&(u=Fb()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);e!==s?(Ei=r,t=function(r,t){r.ast;if(!t||0===t.length)return r;const e=t.length;let n=t[e-1][3];for(let o=e-1;o>=0;o--){const e=0===o?r:t[o-1][3];n=yv(t[o][1],e,n)}return n}(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s&&Hb()!==s?((e=Fc())===s&&(e=null),e!==s?(Ei=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(mi=r,r=s)):(mi=r,r=s),r}function Bc(){var r,t,e,n,o,u,a,i,c,l,f;if(r=mi,(t=Vl())!==s&&(Ei=r,t={expr:t,as:null}),(r=t)===s){if(r=mi,(t=Hc())!==s)if((e=Hb())!==s)if((n=Tf())!==s)if((o=Hb())!==s){for(u=[],a=mi,(i=Hb())!==s?((c=Fl())===s&&(c=Bl()),c!==s&&(l=Hb())!==s&&(f=Hc())!==s?a=i=[i,c,l,f]:(mi=a,a=s)):(mi=a,a=s);a!==s;)u.push(a),a=mi,(i=Hb())!==s?((c=Fl())===s&&(c=Bl()),c!==s&&(l=Hb())!==s&&(f=Hc())!==s?a=i=[i,c,l,f]:(mi=a,a=s)):(mi=a,a=s);u!==s&&(a=Hb())!==s?((i=Yc())===s&&(i=null),i!==s?(Ei=r,r=t=function(r,t,e,n){return{...t,as:n,type:"cast",expr:r,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]}}}(t,n,u,i)):(mi=r,r=s)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;r===s&&(r=mi,(t=zl())!==s&&(e=Hb())!==s&&(n=Rb())!==s?(o=mi,(u=zl())!==s&&(a=Hb())!==s&&(i=Rb())!==s?o=u=[u,a,i]:(mi=o,o=s),o===s&&(o=null),o!==s&&(u=Hb())!==s&&(a=Ob())!==s?(Ei=r,r=t=function(r,t){const e=t&&t[0];let n;e&&(n=r,r=e),gv.add(`select::${r}::(.*)`);return{expr:{type:"column_ref",table:r,schema:n,column:"*"},as:null}}(t,o)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,t=mi,(e=zl())!==s&&(n=Hb())!==s&&(o=Rb())!==s?t=e=[e,n,o]:(mi=t,t=s),t===s&&(t=null),t!==s&&(e=Hb())!==s&&(n=Ob())!==s?(Ei=r,r=t=function(r){const t=r&&r[0]||null;return gv.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(mi=r,r=s),r===s&&(r=mi,(t=ef())!==s&&(e=Hb())!==s?((n=Rb())===s&&(n=null),n!==s?(Ei=mi,(o=(o=function(r,t){if(t)return!0}(0,n))?s:void 0)!==s&&(u=Hb())!==s?((a=Yc())===s&&(a=null),a!==s?(Ei=r,r=t=function(r,t,e){return gv.add("select::null::"+r.value),{type:"expr",expr:{type:"column_ref",table:null,column:{expr:r}},as:e}}(t,0,a)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=Hc())!==s&&(e=Hb())!==s?((n=Yc())===s&&(n=null),n!==s?(Ei=r,r=t={type:"expr",expr:t,as:n}):(mi=r,r=s)):(mi=r,r=s)))))}return r}function qc(){var r,t,e;return r=mi,(t=cp())===s&&(t=null),t!==s&&Hb()!==s&&(e=Jl())!==s?(Ei=r,r=t=e):(mi=r,r=s),r}function Yc(){var r,t,e;return r=mi,(t=cp())!==s&&Hb()!==s&&(e=Jl())!==s?(Ei=r,r=t=e):(mi=r,r=s),r===s&&(r=mi,(t=cp())===s&&(t=null),t!==s&&Hb()!==s&&(e=Jl())!==s?(Ei=r,r=t=e):(mi=r,r=s)),r}function Wc(){var t,e,n;return t=mi,up()!==s&&Hb()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=ov())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=ov())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=ov())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=t,t={keyword:"var",type:"into",expr:e}):(mi=t,t=s),t===s&&(t=mi,up()!==s&&Hb()!==s?("outfile"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(De)),e===s&&("dumpfile"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Pe))),e===s&&(e=null),e!==s&&Hb()!==s?((n=Rf())===s&&(n=zl()),n!==s?(Ei=t,t={keyword:e,type:"into",expr:n}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)),t}function Vc(){var r,t;return r=mi,ap()!==s&&Hb()!==s&&(t=Zc())!==s?(Ei=r,r=t):(mi=r,r=s),r}function Xc(){var r,t,e;return r=mi,(t=el())!==s&&Hb()!==s&&qf()!==s&&Hb()!==s&&(e=el())!==s?(Ei=r,r=t=[t,e]):(mi=r,r=s),r}function Qc(){var t,e;return t=mi,dp()!==s&&Hb()!==s?("btree"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Ge)),e===s&&("hash"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri($e)),e===s&&("gist"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Fe)),e===s&&("gin"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(He))))),e!==s?(Ei=t,t={keyword:"using",type:e.toLowerCase()}):(mi=t,t=s)):(mi=t,t=s),t}function Kc(){var r,t,e,n,o,u;if(r=mi,(t=zc())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=zc())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=zc())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function zc(){var t,e,n,o,u,a;return t=mi,(e=function(){var t,e,n,o;t=mi,"key_block_size"===r.substr(mi,14).toLowerCase()?(e=r.substr(mi,14),mi+=14):(e=s,0===Si&&Ri(At));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="KEY_BLOCK_SIZE"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s?((n=mb())===s&&(n=null),n!==s&&Hb()!==s&&(o=Uf())!==s?(Ei=t,u=n,a=o,t=e={type:e.toLowerCase(),symbol:u,expr:a}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=cf())!==s&&Hb()!==s&&(n=mb())!==s&&Hb()!==s?((o=Uf())===s&&(o=zl()),o!==s?(Ei=t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=Qc())===s&&(t=mi,"with"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Be)),e!==s&&Hb()!==s?("parser"===r.substr(mi,6).toLowerCase()?(n=r.substr(mi,6),mi+=6):(n=s,0===Si&&Ri(qe)),n!==s&&Hb()!==s&&(o=cf())!==s?(Ei=t,t=e={type:"with parser",expr:o}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"visible"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ye)),e===s&&("invisible"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(We))),e!==s&&(Ei=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===s&&(t=Yb())))),t}function Zc(){var r,t,e,n;if(r=mi,(t=rl())!==s){for(e=[],n=Jc();n!==s;)e.push(n),n=Jc();e!==s?(Ei=r,r=t=Ve(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Jc(){var r,t,e;return r=mi,Hb()!==s&&(t=Nb())!==s&&Hb()!==s&&(e=rl())!==s?(Ei=r,r=e):(mi=r,r=s),r===s&&(r=mi,Hb()!==s&&(t=function(){var r,t,e,n,o,u,a,i,c,l,f;if(r=mi,(t=tl())!==s)if(Hb()!==s)if((e=rl())!==s)if(Hb()!==s)if((n=dp())!==s)if(Hb()!==s)if(kb()!==s)if(Hb()!==s)if((o=Kl())!==s){for(u=[],a=mi,(i=Hb())!==s&&(c=Nb())!==s&&(l=Hb())!==s&&(f=Kl())!==s?a=i=[i,c,l,f]:(mi=a,a=s);a!==s;)u.push(a),a=mi,(i=Hb())!==s&&(c=Nb())!==s&&(l=Hb())!==s&&(f=Kl())!==s?a=i=[i,c,l,f]:(mi=a,a=s);u!==s&&(a=Hb())!==s&&(i=Ub())!==s?(Ei=r,p=t,v=o,y=u,(b=e).join=p,b.using=wv(v,y),r=t=b):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;var p,b,v,y;r===s&&(r=mi,(t=tl())!==s&&Hb()!==s&&(e=rl())!==s&&Hb()!==s?((n=ol())===s&&(n=null),n!==s?(Ei=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=tl())===s&&(t=Di()),t!==s&&Hb()!==s&&(e=kb())!==s&&Hb()!==s?((n=Pi())===s&&(n=Zc()),n!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((o=Yc())===s&&(o=null),o!==s&&(u=Hb())!==s?((a=ol())===s&&(a=null),a!==s?(Ei=r,t=function(r,t,e,n){return Array.isArray(t)&&(t={type:"tables",expr:t}),t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,a),r=t):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)));return r}())!==s?(Ei=r,r=t):(mi=r,r=s)),r}function rl(){var t,e,n,o,u,a,i,c,l,f,p,b;return t=mi,(e=function(){var t;"dual"===r.substr(mi,4).toLowerCase()?(t=r.substr(mi,4),mi+=4):(t=s,0===Si&&Ri(Ga));return t}())!==s&&(Ei=t,e={type:"dual"}),(t=e)===s&&(t=mi,(e=gl())!==s&&Hb()!==s?((n=qc())===s&&(n=null),n!==s?(Ei=t,t=e={expr:{type:"values",values:e},as:n}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"lateral"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Xe)),e===s&&(e=null),e!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s?((o=Pi())===s&&(o=gl()),o!==s&&Hb()!==s&&(u=Ub())!==s&&(a=Hb())!==s?((i=qc())===s&&(i=null),i!==s?(Ei=t,t=e=function(r,t,e){return Array.isArray(t)&&(t={type:"values",values:t}),t.parentheses=!0,{prefix:r,expr:t,as:e}}(e,o,i)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"lateral"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Xe)),e===s&&(e=null),e!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s&&(o=Zc())!==s&&Hb()!==s&&(u=Ub())!==s&&(a=Hb())!==s?((i=qc())===s&&(i=null),i!==s?(Ei=t,t=e=function(r,t,e){return{prefix:r,expr:t={type:"tables",expr:t,parentheses:!0},as:e}}(e,o,i)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"lateral"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Xe)),e===s&&(e=null),e!==s&&Hb()!==s&&(n=Ef())!==s&&Hb()!==s?((o=Yc())===s&&(o=null),o!==s?(Ei=t,t=e=function(r,t,e){return{prefix:r,type:"expr",expr:t,as:e}}(e,n,o)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=el())!==s&&Hb()!==s?("tablesample"===r.substr(mi,11).toLowerCase()?(n=r.substr(mi,11),mi+=11):(n=s,0===Si&&Ri(Qe)),n!==s&&Hb()!==s&&(o=Ef())!==s&&Hb()!==s?(u=mi,"repeatable"===r.substr(mi,10).toLowerCase()?(a=r.substr(mi,10),mi+=10):(a=s,0===Si&&Ri(Ke)),a!==s&&(i=Hb())!==s&&(c=kb())!==s&&(l=Hb())!==s&&(f=Uf())!==s&&(p=Hb())!==s&&(b=Ub())!==s?u=a=[a,i,c,l,f,p,b]:(mi=u,u=s),u===s&&(u=null),u!==s&&(a=Hb())!==s?((i=Yc())===s&&(i=null),i!==s?(Ei=t,t=e=function(r,t,e,n){return{...r,as:n,tablesample:{expr:t,repeatable:e&&e[4]}}}(e,o,u,i)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=el())!==s&&Hb()!==s?((n=Yc())===s&&(n=null),n!==s?(Ei=t,t=e=function(r,t){return"var"===r.type?(r.as=t,r):{...r,as:t}}(e,n)):(mi=t,t=s)):(mi=t,t=s))))))),t}function tl(){var t,e,n,o;return t=mi,(e=function(){var t,e,n,o;t=mi,"left"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Bs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(n=Hb())!==s?((o=vp())===s&&(o=null),o!==s&&Hb()!==s&&bp()!==s?(Ei=t,t=e="LEFT JOIN"):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=function(){var t,e,n,o;t=mi,"right"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(qs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(n=Hb())!==s?((o=vp())===s&&(o=null),o!==s&&Hb()!==s&&bp()!==s?(Ei=t,t=e="RIGHT JOIN"):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=function(){var t,e,n,o;t=mi,"full"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ys));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(n=Hb())!==s?((o=vp())===s&&(o=null),o!==s&&Hb()!==s&&bp()!==s?(Ei=t,t=e="FULL JOIN"):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"cross"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ze)),e!==s&&(n=Hb())!==s&&(o=bp())!==s?(Ei=t,t=e="CROSS JOIN"):(mi=t,t=s),t===s&&(t=mi,e=mi,(n=function(){var t,e,n,o;t=mi,"inner"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Ws));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(o=Hb())!==s?e=n=[n,o]:(mi=e,e=s),e===s&&(e=null),e!==s&&(n=bp())!==s?(Ei=t,t=e="INNER JOIN"):(mi=t,t=s))))),t}function el(){var r,t,e,n,o,u,a,i,c;return r=mi,(t=zl())!==s?(e=mi,(n=Hb())!==s&&(o=Rb())!==s&&(u=Hb())!==s?((a=zl())===s&&(a=Ob()),a!==s?e=n=[n,o,u,a]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=null),e!==s?(n=mi,(o=Hb())!==s&&(u=Rb())!==s&&(a=Hb())!==s?((i=zl())===s&&(i=Ob()),i!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s),n===s&&(n=null),n!==s?(Ei=r,r=t=function(r,t,e){const n={db:null,table:r};return null!==e?(n.db=r,n.schema=t[3],n.table=e[3],n):(null!==t&&(n.db=r,n.table=t[3]),n)}(t,e,n)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=ov())!==s&&(Ei=r,(c=t).db=null,c.table=c.name,t=c),r=t),r}function nl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Nl())!==s){for(e=[],n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=yv(t[r][1],n,t[r][3]);return n}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function ol(){var r,t;return r=mi,pp()!==s&&Hb()!==s&&(t=Ol())!==s?(Ei=r,r=t):(mi=r,r=s),r}function sl(){var t,e;return t=mi,function(){var t,e,n,o;t=mi,"where"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ru));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s&&(e=Ol())!==s?(Ei=t,t=e):(mi=t,t=s),t}function ul(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Xl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Xl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Xl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function al(){var r,t,e;return r=mi,(t=cf())!==s&&Hb()!==s&&cp()!==s&&Hb()!==s&&(e=il())!==s?(Ei=r,r=t={name:t,as_window_specification:e}):(mi=r,r=s),r}function il(){var r,t;return(r=cf())===s&&(r=mi,kb()!==s&&Hb()!==s?((t=function(){var r,t,e,n;r=mi,(t=bl())===s&&(t=null);t!==s&&Hb()!==s?((e=vl())===s&&(e=null),e!==s&&Hb()!==s?((n=function(){var r,t,e,n,o;r=mi,(t=cb())!==s&&Hb()!==s?((e=cl())===s&&(e=ll()),e!==s?(Ei=r,r=t={type:"rows",expr:e}):(mi=r,r=s)):(mi=r,r=s);r===s&&(r=mi,(t=cb())!==s&&Hb()!==s&&(e=Tp())!==s&&Hb()!==s&&(n=ll())!==s&&Hb()!==s&&Np()!==s&&Hb()!==s&&(o=cl())!==s?(Ei=r,t=yv(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),r=t):(mi=r,r=s));return r}())===s&&(n=null),n!==s?(Ei=r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s);return r}())===s&&(t=null),t!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r={window_specification:t||{},parentheses:!0}):(mi=r,r=s)):(mi=r,r=s)),r}function cl(){var t,e,n,o;return t=mi,(e=pl())!==s&&Hb()!==s?("following"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(Ze)),n!==s?(Ei=t,(o=e).value+=" FOLLOWING",t=e=o):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=fl()),t}function ll(){var t,e,n,o,u;return t=mi,(e=pl())!==s&&Hb()!==s?("preceding"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(Je)),n===s&&("following"===r.substr(mi,9).toLowerCase()?(n=r.substr(mi,9),mi+=9):(n=s,0===Si&&Ri(Ze))),n!==s?(Ei=t,u=n,(o=e).value+=" "+u.toUpperCase(),t=e=o):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=fl()),t}function fl(){var t,e,n;return t=mi,"current"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ur)),e!==s&&Hb()!==s?("row"===r.substr(mi,3).toLowerCase()?(n=r.substr(mi,3),mi+=3):(n=s,0===Si&&Ri(dt)),n!==s?(Ei=t,t=e={type:"origin",value:"current row"}):(mi=t,t=s)):(mi=t,t=s),t}function pl(){var t,e;return t=mi,"unbounded"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(rn)),e!==s&&(Ei=t,e={type:"origin",value:e.toUpperCase()}),(t=e)===s&&(t=Uf()),t}function bl(){var r,t;return r=mi,sp()!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s&&(t=Gc())!==s?(Ei=r,r=t):(mi=r,r=s),r}function vl(){var r,t;return r=mi,Cp()!==s&&Hb()!==s&&Lp()!==s&&Hb()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=yl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=yl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=yl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=r,r=t):(mi=r,r=s),r}function yl(){var t,e,n,o,u,a,i;return t=mi,(e=Nl())!==s&&Hb()!==s?((n=Ep())===s&&(n=mp()),n===s&&(n=null),n!==s&&Hb()!==s?(o=mi,"nulls"===r.substr(mi,5).toLowerCase()?(u=r.substr(mi,5),mi+=5):(u=s,0===Si&&Ri(Cr)),u!==s&&(a=Hb())!==s?("first"===r.substr(mi,5).toLowerCase()?(i=r.substr(mi,5),mi+=5):(i=s,0===Si&&Ri(mr)),i===s&&("last"===r.substr(mi,4).toLowerCase()?(i=r.substr(mi,4),mi+=4):(i=s,0===Si&&Ri(Er))),i===s&&(i=null),i!==s?o=u=[u,a,i]:(mi=o,o=s)):(mi=o,o=s),o===s&&(o=null),o!==s?(Ei=t,t=e=function(r,t,e){const n={expr:r,type:t};return n.nulls=e&&[e[0],e[2]].filter(r=>r).join(" "),n}(e,n,o)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t}function dl(){var r;return(r=Uf())===s&&(r=ov())===s&&(r=bf()),r}function wl(){var t,e,n,o,u,a,i;return t=mi,e=mi,(n=function(){var t,e,n,o;t=mi,"limit"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(su));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(o=Hb())!==s?((u=dl())===s&&(u=Ap()),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=null),e!==s&&(n=Hb())!==s?(o=mi,(u=function(){var t,e,n,o;t=mi,"offset"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(uu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="OFFSET"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(a=Hb())!==s&&(i=dl())!==s?o=u=[u,a,i]:(mi=o,o=s),o===s&&(o=null),o!==s?(Ei=t,t=e=function(r,t){const e=[];return r&&e.push("string"==typeof r[2]?{type:"origin",value:"all"}:r[2]),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,o)):(mi=t,t=s)):(mi=t,t=s),t}function hl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ll())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Ll())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Ll())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Ll(){var t,e,n,o,u,a,i,c,l;return t=mi,e=mi,(n=zl())!==s&&(o=Hb())!==s&&(u=Rb())!==s?e=n=[n,o,u]:(mi=e,e=s),e===s&&(e=null),e!==s&&(n=Hb())!==s&&(o=sf())!==s&&(u=Hb())!==s?(61===r.charCodeAt(mi)?(a="=",mi++):(a=s,0===Si&&Ri(sr)),a!==s&&Hb()!==s&&(i=$l())!==s?(Ei=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,e=mi,(n=zl())!==s&&(o=Hb())!==s&&(u=Rb())!==s?e=n=[n,o,u]:(mi=e,e=s),e===s&&(e=null),e!==s&&(n=Hb())!==s&&(o=sf())!==s&&(u=Hb())!==s?(61===r.charCodeAt(mi)?(a="=",mi++):(a=s,0===Si&&Ri(sr)),a!==s&&Hb()!==s&&(i=yp())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(c=Xl())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(mi=t,t=s)):(mi=t,t=s)),t}function Cl(){var t,e,n,o,u;return t=mi,(e=function(){var t,e,n,o;t=mi,"returning"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Us));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="RETURNING"):(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&Hb()!==s?((n=Gc())===s&&(n=Nc()),n!==s?(Ei=t,u=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===u&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||u}):(mi=t,t=s)):(mi=t,t=s),t}function ml(){var r;return(r=gl())===s&&(r=Dc()),r}function El(){var r,t,e,n,o,u,a,i,c;if(r=mi,sp()!==s)if(Hb()!==s)if((t=kb())!==s)if(Hb()!==s)if((e=cf())!==s){for(n=[],o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=cf())!==s?o=u=[u,a,i,c]:(mi=o,o=s);o!==s;)n.push(o),o=mi,(u=Hb())!==s&&(a=Nb())!==s&&(i=Hb())!==s&&(c=cf())!==s?o=u=[u,a,i,c]:(mi=o,o=s);n!==s&&(o=Hb())!==s&&(u=Ub())!==s?(Ei=r,r=wv(e,n)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;return r===s&&(r=mi,sp()!==s&&Hb()!==s&&(t=Tl())!==s?(Ei=r,r=t):(mi=r,r=s)),r}function Al(){var r,t;return r=mi,(t=rp())!==s&&(Ei=r,t="insert"),(r=t)===s&&(r=mi,(t=ep())!==s&&(Ei=r,t="replace"),r=t),r}function gl(){var r,t;return r=mi,yp()!==s&&Hb()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Tl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,t=wv(t,e),r=t):(mi=r,r=s)}else mi=r,r=s;return r}())!==s?(Ei=r,r=t):(mi=r,r=s),r}function Tl(){var r,t;return r=mi,kb()!==s&&Hb()!==s&&(t=Sl())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r=t):(mi=r,r=s),r}function Sl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Nl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=wv(r,t),e}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function _l(){var t,e,n;return t=mi,bb()!==s&&Hb()!==s&&(e=Nl())!==s&&Hb()!==s&&(n=function(){var t;(t=function(){var t,e,n,o;t=mi,"year"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ho));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="YEAR"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"month"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Uo));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="MONTH"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"day"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Eo));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DAY"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"hour"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(xo));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="HOUR"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"minute"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ko));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="MINUTE"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"second"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Do));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SECOND"):(mi=t,t=s)):(mi=t,t=s);return t}());return t}())!==s?(Ei=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(mi=t,t=s),t===s&&(t=mi,bb()!==s&&Hb()!==s&&(e=Rf())!==s?(Ei=t,t=function(r){return{type:"interval",expr:r,unit:""}}(e)):(mi=t,t=s)),t}function xl(){var r,t,e,n,o,u;if(r=mi,(t=jl())!==s)if(Hb()!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=jl())!==s?n=o=[o,u]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=jl())!==s?n=o=[o,u]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e,1)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;return r}function jl(){var t,e,n;return t=mi,Dp()!==s&&Hb()!==s&&(e=Ol())!==s&&Hb()!==s&&function(){var t,e,n,o;t=mi,"then"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(be));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}()!==s&&Hb()!==s&&(n=Nl())!==s?(Ei=t,t={type:"when",cond:e,result:n}):(mi=t,t=s),t}function Il(){var r,t;return r=mi,Pp()!==s&&Hb()!==s&&(t=Nl())!==s?(Ei=r,r={type:"else",result:t}):(mi=r,r=s),r}function Rl(){var r;return(r=kl())===s&&(r=function(){var r,t,e,n,o,u;if(r=mi,(t=Fl())!==s){if(e=[],n=mi,(o=Hb())!==s&&(u=Yl())!==s?n=o=[o,u]:(mi=n,n=s),n!==s)for(;n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Yl())!==s?n=o=[o,u]:(mi=n,n=s);else e=s;e!==s?(Ei=r,t=vv(t,e[0][1]),r=t):(mi=r,r=s)}else mi=r,r=s;return r}()),r}function Nl(){var r;return(r=Rl())===s&&(r=Pi()),r}function Ol(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Nl())!==s){for(e=[],n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op())===s&&(u=Nb()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s?((u=Np())===s&&(u=Op())===s&&(u=Nb()),u!==s&&(a=Hb())!==s&&(i=Nl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=yv(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function kl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ul())!==s){for(e=[],n=mi,(o=Bb())!==s&&(u=Op())!==s&&(a=Hb())!==s&&(i=Ul())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Bb())!==s&&(u=Op())!==s&&(a=Hb())!==s&&(i=Ul())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=on(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Ul(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Ml())!==s){for(e=[],n=mi,(o=Bb())!==s&&(u=Np())!==s&&(a=Hb())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Bb())!==s&&(u=Np())!==s&&(a=Hb())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=on(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Ml(){var t,e,n,o,u;return(t=Dl())===s&&(t=function(){var r,t,e;r=mi,(t=function(){var r,t,e,n,o;r=mi,t=mi,(e=Rp())!==s&&(n=Hb())!==s&&(o=Ip())!==s?t=e=[e,n,o]:(mi=t,t=s);t!==s&&(Ei=r,t=(u=t)[0]+" "+u[2]);var u;(r=t)===s&&(r=Ip());return r}())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(e=Pi())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,n=t,(o=e).parentheses=!0,t=vv(n,o),r=t):(mi=r,r=s);var n,o;return r}())===s&&(t=mi,(e=Rp())===s&&(e=mi,33===r.charCodeAt(mi)?(n="!",mi++):(n=s,0===Si&&Ri(sn)),n!==s?(o=mi,Si++,61===r.charCodeAt(mi)?(u="=",mi++):(u=s,0===Si&&Ri(sr)),Si--,u===s?o=void 0:(mi=o,o=s),o!==s?e=n=[n,o]:(mi=e,e=s)):(mi=e,e=s)),e!==s&&(n=Hb())!==s&&(o=Ml())!==s?(Ei=t,t=e=vv("NOT",o)):(mi=t,t=s)),t}function Dl(){var t,e,n,o,u;return t=mi,(e=$l())!==s&&Hb()!==s?((n=function(){var t;(t=function(){var r,t,e,n,o,u,a;r=mi,t=[],e=mi,(n=Hb())!==s&&(o=Pl())!==s&&(u=Hb())!==s&&(a=$l())!==s?e=n=[n,o,u,a]:(mi=e,e=s);if(e!==s)for(;e!==s;)t.push(e),e=mi,(n=Hb())!==s&&(o=Pl())!==s&&(u=Hb())!==s&&(a=$l())!==s?e=n=[n,o,u,a]:(mi=e,e=s);else t=s;t!==s&&(Ei=r,t={type:"arithmetic",tail:t});return r=t}())===s&&(t=function(){var r,t,e,n;r=mi,(t=Gl())!==s&&Hb()!==s&&(e=kb())!==s&&Hb()!==s&&(n=Sl())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r=t={op:t,right:n}):(mi=r,r=s);r===s&&(r=mi,(t=Gl())!==s&&Hb()!==s?((e=ov())===s&&(e=Rf())===s&&(e=Ef()),e!==s?(Ei=r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(mi=r,r=s)):(mi=r,r=s));return r}())===s&&(t=function(){var r,t,e,n;r=mi,(t=function(){var r,t,e,n,o;r=mi,t=mi,(e=Rp())!==s&&(n=Hb())!==s&&(o=Tp())!==s?t=e=[e,n,o]:(mi=t,t=s);t!==s&&(Ei=r,t=(u=t)[0]+" "+u[2]);var u;(r=t)===s&&(r=Tp());return r}())!==s&&Hb()!==s&&(e=$l())!==s&&Hb()!==s&&Np()!==s&&Hb()!==s&&(n=$l())!==s?(Ei=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(mi=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o,u,a,i,c;r=mi,(t=_p())!==s&&(e=Hb())!==s&&(n=$l())!==s?(Ei=r,r=t={op:"IS",right:n}):(mi=r,r=s);r===s&&(r=mi,(t=_p())!==s&&(e=Hb())!==s?(n=mi,(o=gp())!==s&&(u=Hb())!==s&&(a=ap())!==s&&(i=Hb())!==s&&(c=el())!==s?n=o=[o,u,a,i,c]:(mi=n,n=s),n!==s?(Ei=r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,t=mi,(e=_p())!==s&&(n=Hb())!==s&&(o=Rp())!==s?t=e=[e,n,o]:(mi=t,t=s),t!==s&&(e=Hb())!==s&&(n=$l())!==s?(Ei=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(mi=r,r=s)));return r}())===s&&(t=function(){var t,e,n,o;t=mi,(e=function(){var t,e,n,o,u;t=mi,e=mi,(n=Rp())!==s&&(o=Hb())!==s?((u=xp())===s&&(u=jp()),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s);e!==s&&(Ei=t,e=(a=e)[0]+" "+a[2]);var a;(t=e)===s&&(t=xp())===s&&(t=jp())===s&&(t=mi,"similar"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(bn)),e!==s&&(n=Hb())!==s&&(o=qf())!==s?(Ei=t,t=e="SIMILAR TO"):(mi=t,t=s),t===s&&(t=mi,(e=Rp())!==s&&(n=Hb())!==s?("similar"===r.substr(mi,7).toLowerCase()?(o=r.substr(mi,7),mi+=7):(o=s,0===Si&&Ri(bn)),o!==s&&(u=Hb())!==s&&qf()!==s?(Ei=t,t=e="NOT SIMILAR TO"):(mi=t,t=s)):(mi=t,t=s)));return t}())!==s&&Hb()!==s?((n=Sf())===s&&(n=Dl()),n!==s&&Hb()!==s?((o=function(){var t,e,n;t=mi,"escape"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(hn));e!==s&&Hb()!==s&&(n=Rf())!==s?(Ei=t,t=e={type:"ESCAPE",value:n}):(mi=t,t=s);return t}())===s&&(o=null),o!==s?(Ei=t,u=e,a=n,(i=o)&&(a.escape=i),t=e={op:u,right:a}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s);var u,a,i;return t}())===s&&(t=function(){var t,e,n;t=mi,(e=function(){var t;"!~*"===r.substr(mi,3)?(t="!~*",mi+=3):(t=s,0===Si&&Ri(vn));t===s&&("~*"===r.substr(mi,2)?(t="~*",mi+=2):(t=s,0===Si&&Ri(yn)),t===s&&(126===r.charCodeAt(mi)?(t="~",mi++):(t=s,0===Si&&Ri(dn)),t===s&&("!~"===r.substr(mi,2)?(t="!~",mi+=2):(t=s,0===Si&&Ri(wn)))));return t}())!==s&&Hb()!==s?((n=Sf())===s&&(n=Dl()),n!==s?(Ei=t,t=e={op:e,right:n}):(mi=t,t=s)):(mi=t,t=s);return t}());return t}())===s&&(n=null),n!==s?(Ei=t,o=e,t=e=null===(u=n)?o:"arithmetic"===u.type?hv(o,u.tail):yv(u.op,o,u.right)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=Rf())===s&&(t=Xl()),t}function Pl(){var t;return">="===r.substr(mi,2)?(t=">=",mi+=2):(t=s,0===Si&&Ri(un)),t===s&&(62===r.charCodeAt(mi)?(t=">",mi++):(t=s,0===Si&&Ri(an)),t===s&&("<="===r.substr(mi,2)?(t="<=",mi+=2):(t=s,0===Si&&Ri(cn)),t===s&&("<>"===r.substr(mi,2)?(t="<>",mi+=2):(t=s,0===Si&&Ri(ln)),t===s&&(60===r.charCodeAt(mi)?(t="<",mi++):(t=s,0===Si&&Ri(fn)),t===s&&(61===r.charCodeAt(mi)?(t="=",mi++):(t=s,0===Si&&Ri(sr)),t===s&&("!="===r.substr(mi,2)?(t="!=",mi+=2):(t=s,0===Si&&Ri(pn)))))))),t}function Gl(){var r,t,e,n,o,u;return r=mi,t=mi,(e=Rp())!==s&&(n=Hb())!==s&&(o=Sp())!==s?t=e=[e,n,o]:(mi=t,t=s),t!==s&&(Ei=r,t=(u=t)[0]+" "+u[2]),(r=t)===s&&(r=Sp()),r}function $l(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Hl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Fl())!==s&&(a=Hb())!==s&&(i=Hl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Fl())!==s&&(a=Hb())!==s&&(i=Hl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...bv()}));return hv(r,t)}(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Fl(){var t;return 43===r.charCodeAt(mi)?(t="+",mi++):(t=s,0===Si&&Ri(Ln)),t===s&&(45===r.charCodeAt(mi)?(t="-",mi++):(t=s,0===Si&&Ri(Cn))),t}function Hl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Wl())!==s){for(e=[],n=mi,(o=Hb())!==s?((u=Bl())===s&&(u=Fb()),u!==s&&(a=Hb())!==s&&(i=Wl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s?((u=Bl())===s&&(u=Fb()),u!==s&&(a=Hb())!==s&&(i=Wl())!==s?n=o=[o,u,a,i]:(mi=n,n=s)):(mi=n,n=s);e!==s?(Ei=r,r=t=hv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Bl(){var t;return 42===r.charCodeAt(mi)?(t="*",mi++):(t=s,0===Si&&Ri(mn)),t===s&&(47===r.charCodeAt(mi)?(t="/",mi++):(t=s,0===Si&&Ri(En)),t===s&&(37===r.charCodeAt(mi)?(t="%",mi++):(t=s,0===Si&&Ri(An)),t===s&&("||"===r.substr(mi,2)?(t="||",mi+=2):(t=s,0===Si&&Ri(gn))))),t}function ql(){var r,t,e,n,o;return r=mi,(t=Xl())!==s&&Hb()!==s?((e=Fc())===s&&(e=null),e!==s?(Ei=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(mi=r,r=s)):(mi=r,r=s),r}function Yl(){var t,e,n,o,u,a;return(t=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=$p())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&(u=cp())!==s&&Hb()!==s&&(a=uv())!==s&&Hb()!==s&&(i=Ub())!==s?(Ei=t,f=o,p=a,e={type:"cast",keyword:e.toLowerCase(),expr:f,symbol:"as",target:[p]},t=e):(mi=t,t=s);var f,p;t===s&&(t=mi,(e=$p())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&(u=cp())!==s&&Hb()!==s&&(a=qp())!==s&&Hb()!==s&&(i=kb())!==s&&Hb()!==s&&(c=Mf())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&(l=Ub())!==s?(Ei=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),t=e):(mi=t,t=s),t===s&&(t=mi,(e=$p())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&(u=cp())!==s&&Hb()!==s&&(a=qp())!==s&&Hb()!==s&&(i=kb())!==s&&Hb()!==s&&(c=Mf())!==s&&Hb()!==s&&Nb()!==s&&Hb()!==s&&(l=Mf())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&Ub()!==s?(Ei=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),t=e):(mi=t,t=s),t===s&&(t=mi,(e=$p())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&(u=cp())!==s&&Hb()!==s&&(a=function(){var t;(t=function(){var t,e,n,o;t=mi,"signed"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Gu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SIGNED"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=Yp());return t}())!==s&&Hb()!==s?((i=Vp())===s&&(i=null),i!==s&&Hb()!==s&&(c=Ub())!==s?(Ei=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,i),t=e):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=kb())!==s&&Hb()!==s?((n=kl())===s&&(n=ql())===s&&(n=bf()),n!==s&&Hb()!==s&&(o=Ub())!==s&&Hb()!==s?((u=Tf())===s&&(u=null),u!==s?(Ei=t,e=function(r,t){return r.parentheses=!0,t?{type:"cast",keyword:"cast",expr:r,...t}:r}(n,u),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=Sf())===s&&(e=function(){var t,e,n;t=mi,(e=function(){var t,e,n,o,u,a,i,c,l;t=mi,(e=function(){var t,e,n,o;t=mi,"count"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(mu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="COUNT"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=mi,"group_concat"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Eu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="GROUP_CONCAT"):(mi=t,t=s)):(mi=t,t=s);return t}());e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=function(){var t,e;t=mi,(e=function(){var t,e;t=mi,42===r.charCodeAt(mi)?(e="*",mi++):(e=s,0===Si&&Ri(mn));e!==s&&(Ei=t,e={type:"star",value:"*"});return t=e}())!==s&&(Ei=t,e={expr:e});(t=e)===s&&(t=hf());return t}())!==s&&Hb()!==s&&(o=Ub())!==s&&Hb()!==s?((u=yf())===s&&(u=null),u!==s?(Ei=t,e=function(r,t,e){return{type:"aggr_func",name:r,args:t,over:e}}(e,n,u),t=e):(mi=t,t=s)):(mi=t,t=s);t===s&&(t=mi,"percentile_cont"===r.substr(mi,15).toLowerCase()?(e=r.substr(mi,15),mi+=15):(e=s,0===Si&&Ri(io)),e===s&&("percentile_disc"===r.substr(mi,15).toLowerCase()?(e=r.substr(mi,15),mi+=15):(e=s,0===Si&&Ri(co))),e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((n=Uf())===s&&(n=_f()),n!==s&&Hb()!==s&&(o=Ub())!==s&&Hb()!==s?("within"===r.substr(mi,6).toLowerCase()?(u=r.substr(mi,6),mi+=6):(u=s,0===Si&&Ri(lo)),u!==s&&Hb()!==s&&hp()!==s&&Hb()!==s&&(a=kb())!==s&&Hb()!==s&&(i=vl())!==s&&Hb()!==s&&(c=Ub())!==s&&Hb()!==s?((l=yf())===s&&(l=null),l!==s?(Ei=t,e=function(r,t,e,n){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:t},within_group_orderby:e,over:n}}(e,n,i,l),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,"mode"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(fo)),e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=Ub())!==s&&Hb()!==s?("within"===r.substr(mi,6).toLowerCase()?(o=r.substr(mi,6),mi+=6):(o=s,0===Si&&Ri(lo)),o!==s&&Hb()!==s&&(u=hp())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(a=vl())!==s&&Hb()!==s&&(i=Ub())!==s&&Hb()!==s?((c=yf())===s&&(c=null),c!==s?(Ei=t,e=function(r,t,e){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:{}},within_group_orderby:t,over:e}}(e,a,c),t=e):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)));return t}())===s&&(e=function(){var t,e,n,o;t=mi,(e=function(){var t;(t=function(){var t,e,n,o;t=mi,"sum"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Tu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SUM"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"max"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Au));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="MAX"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"min"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(gu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="MIN"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"avg"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Su));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="AVG"):(mi=t,t=s)):(mi=t,t=s);return t}());return t}())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=$l())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((o=yf())===s&&(o=null),o!==s?(Ei=t,e=function(r,t,e){return{type:"aggr_func",name:r,args:{expr:t},over:e,...bv()}}(e,n,o),t=e):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(e=function(){var t,e,n,o,u,a;t=mi,e=mi,(n=zl())!==s&&(o=Hb())!==s&&(u=Rb())!==s?e=n=[n,o,u]:(mi=e,e=s);e===s&&(e=null);e!==s&&(n=Hb())!==s?((o=function(){var t,e,n,o;t=mi,"array_agg"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Lu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ARRAY_AGG"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(o=function(){var t,e,n,o;t=mi,"string_agg"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Cu));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="STRING_AGG"):(mi=t,t=s)):(mi=t,t=s);return t}()),o!==s&&(u=Hb())!==s&&kb()!==s&&Hb()!==s&&(a=hf())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,e=function(r,t,e){return{type:"aggr_func",name:r?`${r[0]}.${t}`:t,args:e}}(e,o,a),t=e):(mi=t,t=s)):(mi=t,t=s);return t}());e!==s&&Hb()!==s?((n=function(){var t,e,n;t=mi,"filter"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(zn));e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=sl())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e={keyword:"filter",parentheses:!0,where:n}):(mi=t,t=s);return t}())===s&&(n=null),n!==s?(Ei=t,e=function(r,t){return t&&(r.filter=t),r}(e,n),t=e):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(e=function(){var t;(t=function(){var t,e,n;t=mi,(e=function(){var t;"row_number"===r.substr(mi,10).toLowerCase()?(t=r.substr(mi,10),mi+=10):(t=s,0===Si&&Ri(ro));t===s&&("dense_rank"===r.substr(mi,10).toLowerCase()?(t=r.substr(mi,10),mi+=10):(t=s,0===Si&&Ri(to)),t===s&&("rank"===r.substr(mi,4).toLowerCase()?(t=r.substr(mi,4),mi+=4):(t=s,0===Si&&Ri(eo))));return t}())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&(n=yf())!==s?(Ei=t,e=function(r,t){return{type:"window_func",name:r,over:t}}(e,n),t=e):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u;t=mi,(e=function(){var t;"lag"===r.substr(mi,3).toLowerCase()?(t=r.substr(mi,3),mi+=3):(t=s,0===Si&&Ri(no));t===s&&("lead"===r.substr(mi,4).toLowerCase()?(t=r.substr(mi,4),mi+=4):(t=s,0===Si&&Ri(oo)),t===s&&("nth_value"===r.substr(mi,9).toLowerCase()?(t=r.substr(mi,9),mi+=9):(t=s,0===Si&&Ri(so))));return t}())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=Sl())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((o=df())===s&&(o=null),o!==s&&Hb()!==s&&(u=yf())!==s?(Ei=t,e=function(r,t,e,n){return{type:"window_func",name:r,args:t,over:n,consider_nulls:e}}(e,n,o,u),t=e):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u;t=mi,(e=function(){var t;"first_value"===r.substr(mi,11).toLowerCase()?(t=r.substr(mi,11),mi+=11):(t=s,0===Si&&Ri(Zn));t===s&&("last_value"===r.substr(mi,10).toLowerCase()?(t=r.substr(mi,10),mi+=10):(t=s,0===Si&&Ri(Jn)));return t}())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=Nl())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((o=df())===s&&(o=null),o!==s&&Hb()!==s&&(u=yf())!==s?(Ei=t,e=function(r,t,e,n){return{type:"window_func",name:r,args:{type:"expr_list",value:[t]},over:n,consider_nulls:e}}(e,n,o,u),t=e):(mi=t,t=s)):(mi=t,t=s);return t}());return t}())===s&&(e=Ef())===s&&(e=function(){var r,t,e,n,o,u,a,i;return r=mi,Mp()!==s&&Hb()!==s&&(t=xl())!==s&&Hb()!==s?((e=Il())===s&&(e=null),e!==s&&Hb()!==s&&(n=Gp())!==s&&Hb()!==s?((o=Mp())===s&&(o=null),o!==s?(Ei=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,Mp()!==s&&Hb()!==s&&(t=Nl())!==s&&Hb()!==s&&(e=xl())!==s&&Hb()!==s?((n=Il())===s&&(n=null),n!==s&&Hb()!==s&&(o=Gp())!==s&&Hb()!==s?((u=Mp())===s&&(u=null),u!==s?(Ei=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)),r}())===s&&(e=_l())===s&&(e=ql())===s&&(e=bf()),e!==s&&Hb()!==s?((n=Tf())===s&&(n=null),n!==s?(Ei=t,e=function(r,t){return t?{type:"cast",keyword:"cast",expr:r,...t}:r}(e,n),t=e):(mi=t,t=s)):(mi=t,t=s))))));return t}())===s&&(t=mi,kb()!==s&&(e=Hb())!==s&&(n=Ol())!==s&&(o=Hb())!==s&&(u=Ub())!==s?(Ei=t,(a=n).parentheses=!0,t=a):(mi=t,t=s),t===s&&(t=ov())===s&&(t=mi,Hb()!==s?(36===r.charCodeAt(mi)?(e="$",mi++):(e=s,0===Si&&Ri(Tn)),e!==s?(60===r.charCodeAt(mi)?(n="<",mi++):(n=s,0===Si&&Ri(fn)),n!==s&&(o=Uf())!==s?(62===r.charCodeAt(mi)?(u=">",mi++):(u=s,0===Si&&Ri(an)),u!==s?(Ei=t,t={type:"origin",value:`$<${o.value}>`}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s))),t}function Wl(){var t,e,n,o,u;return(t=function(){var t,e,n,o,u,a,i,c;if(t=mi,(e=Yl())!==s)if(Hb()!==s){for(n=[],o=mi,(u=Hb())!==s?("?|"===r.substr(mi,2)?(a="?|",mi+=2):(a=s,0===Si&&Ri(Sn)),a===s&&("?&"===r.substr(mi,2)?(a="?&",mi+=2):(a=s,0===Si&&Ri(_n)),a===s&&(63===r.charCodeAt(mi)?(a="?",mi++):(a=s,0===Si&&Ri(xn)),a===s&&("#-"===r.substr(mi,2)?(a="#-",mi+=2):(a=s,0===Si&&Ri(jn)),a===s&&("#>>"===r.substr(mi,3)?(a="#>>",mi+=3):(a=s,0===Si&&Ri(In)),a===s&&("#>"===r.substr(mi,2)?(a="#>",mi+=2):(a=s,0===Si&&Ri(Rn)),a===s&&(a=$b())===s&&(a=Gb())===s&&("@>"===r.substr(mi,2)?(a="@>",mi+=2):(a=s,0===Si&&Ri(Nn)),a===s&&("<@"===r.substr(mi,2)?(a="<@",mi+=2):(a=s,0===Si&&Ri(On))))))))),a!==s&&(i=Hb())!==s&&(c=Yl())!==s?o=u=[u,a,i,c]:(mi=o,o=s)):(mi=o,o=s);o!==s;)n.push(o),o=mi,(u=Hb())!==s?("?|"===r.substr(mi,2)?(a="?|",mi+=2):(a=s,0===Si&&Ri(Sn)),a===s&&("?&"===r.substr(mi,2)?(a="?&",mi+=2):(a=s,0===Si&&Ri(_n)),a===s&&(63===r.charCodeAt(mi)?(a="?",mi++):(a=s,0===Si&&Ri(xn)),a===s&&("#-"===r.substr(mi,2)?(a="#-",mi+=2):(a=s,0===Si&&Ri(jn)),a===s&&("#>>"===r.substr(mi,3)?(a="#>>",mi+=3):(a=s,0===Si&&Ri(In)),a===s&&("#>"===r.substr(mi,2)?(a="#>",mi+=2):(a=s,0===Si&&Ri(Rn)),a===s&&(a=$b())===s&&(a=Gb())===s&&("@>"===r.substr(mi,2)?(a="@>",mi+=2):(a=s,0===Si&&Ri(Nn)),a===s&&("<@"===r.substr(mi,2)?(a="<@",mi+=2):(a=s,0===Si&&Ri(On))))))))),a!==s&&(i=Hb())!==s&&(c=Yl())!==s?o=u=[u,a,i,c]:(mi=o,o=s)):(mi=o,o=s);n!==s?(Ei=t,l=e,e=(f=n)&&0!==f.length?hv(l,f):l,t=e):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;var l,f;return t}())===s&&(t=mi,(e=function(){var t;33===r.charCodeAt(mi)?(t="!",mi++):(t=s,0===Si&&Ri(sn));t===s&&(45===r.charCodeAt(mi)?(t="-",mi++):(t=s,0===Si&&Ri(Cn)),t===s&&(43===r.charCodeAt(mi)?(t="+",mi++):(t=s,0===Si&&Ri(Ln)),t===s&&(126===r.charCodeAt(mi)?(t="~",mi++):(t=s,0===Si&&Ri(dn)))));return t}())!==s?(n=mi,(o=Hb())!==s&&(u=Wl())!==s?n=o=[o,u]:(mi=n,n=s),n!==s?(Ei=t,t=e=vv(e,n[1])):(mi=t,t=s)):(mi=t,t=s)),t}function Vl(){var t,e,n,o,u,a;if(t=mi,"e"===r.substr(mi,1).toLowerCase()?(e=r.charAt(mi),mi++):(e=s,0===Si&&Ri(kn)),e!==s)if(39===r.charCodeAt(mi)?(n="'",mi++):(n=s,0===Si&&Ri(jt)),n!==s)if(Hb()!==s){for(o=[],u=Of();u!==s;)o.push(u),u=Of();o!==s&&(u=Hb())!==s?(39===r.charCodeAt(mi)?(a="'",mi++):(a=s,0===Si&&Ri(jt)),a!==s?(Ei=t,t=e={type:"origin",value:`E'${o.join("")}'`}):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;return t}function Xl(){var r,t,e,n,o,u,a,i,c,l,f,p,b;return(r=Vl())===s&&(r=mi,t=mi,(e=zl())!==s&&(n=Hb())!==s&&(o=Rb())!==s?t=e=[e,n,o]:(mi=t,t=s),t===s&&(t=null),t!==s&&(e=Hb())!==s&&(n=Ob())!==s?(Ei=r,r=t=function(r){const t=r&&r[0]||null;return gv.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*"}}(t)):(mi=r,r=s),r===s&&(r=mi,(t=zl())!==s?(e=mi,(n=Hb())!==s&&(o=Rb())!==s&&(u=Hb())!==s&&(a=zl())!==s?e=n=[n,o,u,a]:(mi=e,e=s),e!==s?(n=mi,(o=Hb())!==s&&(u=Rb())!==s&&(a=Hb())!==s&&(i=uf())!==s?n=o=[o,u,a,i]:(mi=n,n=s),n!==s?(o=mi,(u=Hb())!==s&&(a=Ji())!==s?o=u=[u,a]:(mi=o,o=s),o===s&&(o=null),o!==s?(Ei=r,l=t,f=e,p=n,b=o,gv.add(`select::${l}.${f[3]}::${p[3]}`),r=t={type:"column_ref",schema:l,table:f[3],column:p[3],collate:b&&b[1]}):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=zl())!==s&&(e=Hb())!==s&&(n=Rb())!==s&&(o=Hb())!==s&&(u=uf())!==s?(a=mi,(i=Hb())!==s&&(c=Ji())!==s?a=i=[i,c]:(mi=a,a=s),a===s&&(a=null),a!==s?(Ei=r,r=t=function(r,t,e){return gv.add(`select::${r}::${t}`),{type:"column_ref",table:r,column:t,collate:e&&e[1]}}(t,u,a)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=uf())!==s?(e=mi,(n=Hb())!==s&&(o=Ji())!==s?e=n=[n,o]:(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=r,r=t=function(r,t){return gv.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1]}}(t,e)):(mi=r,r=s)):(mi=r,r=s))))),r}function Ql(){var r,t,e,n,o,u,a,i;if(r=mi,(t=uf())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=uf())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=uf())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Kl(){var r,t;return r=mi,(t=cf())!==s&&(Ei=r,t={type:"default",value:t}),(r=t)===s&&(r=rf()),r}function zl(){var r,t;return r=mi,(t=cf())!==s?(Ei=mi,(Un(t)?s:void 0)!==s?(Ei=r,r=t=t):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=tf())!==s&&(Ei=r,t=t),r=t),r}function Zl(){var r,t,e,n,o,u,a,i;if(r=mi,(t=zl())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=zl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=zl())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Jl(){var r,t,e,n,o,u,a,i,c;return r=mi,(t=cf())!==s?(Ei=mi,(function(r){return!0===pv[r.toUpperCase()]}(t)?s:void 0)!==s?(e=mi,(n=Hb())!==s&&(o=kb())!==s&&(u=Hb())!==s&&(a=Ql())!==s&&(i=Hb())!==s&&(c=Ub())!==s?e=n=[n,o,u,a,i,c]:(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=r,r=t=function(r,t){return t?`${r}(${t[3].join(", ")})`:r}(t,e)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=tf())!==s&&(Ei=r,t=t),r=t),r}function rf(){var r;return(r=ef())===s&&(r=nf())===s&&(r=of()),r}function tf(){var r,t;return r=mi,(t=ef())===s&&(t=nf())===s&&(t=of()),t!==s&&(Ei=r,t=t.value),r=t}function ef(){var t,e,n,o;if(t=mi,34===r.charCodeAt(mi)?(e='"',mi++):(e=s,0===Si&&Ri(Me)),e!==s){if(n=[],Mn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Dn)),o!==s)for(;o!==s;)n.push(o),Mn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Dn));else n=s;n!==s?(34===r.charCodeAt(mi)?(o='"',mi++):(o=s,0===Si&&Ri(Me)),o!==s?(Ei=t,t=e={type:"double_quote_string",value:n.join("")}):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;return t}function nf(){var t,e,n,o;if(t=mi,39===r.charCodeAt(mi)?(e="'",mi++):(e=s,0===Si&&Ri(jt)),e!==s){if(n=[],Pn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Gn)),o!==s)for(;o!==s;)n.push(o),Pn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Gn));else n=s;n!==s?(39===r.charCodeAt(mi)?(o="'",mi++):(o=s,0===Si&&Ri(jt)),o!==s?(Ei=t,t=e={type:"single_quote_string",value:n.join("")}):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;return t}function of(){var t,e,n,o;if(t=mi,96===r.charCodeAt(mi)?(e="`",mi++):(e=s,0===Si&&Ri($n)),e!==s){if(n=[],Fn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Hn)),o!==s)for(;o!==s;)n.push(o),Fn.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Hn));else n=s;n!==s?(96===r.charCodeAt(mi)?(o="`",mi++):(o=s,0===Si&&Ri($n)),o!==s?(Ei=t,t=e={type:"backticks_quote_string",value:n.join("")}):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;return t}function sf(){var r;return(r=af())===s&&(r=tf()),r}function uf(){var r,t;return r=mi,(t=af())!==s?(Ei=mi,(Un(t)?s:void 0)!==s?(Ei=r,r=t=t):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=tf()),r}function af(){var r,t,e,n;if(r=mi,(t=lf())!==s){for(e=[],n=pf();n!==s;)e.push(n),n=pf();e!==s?(Ei=r,r=t=t+e.join("")):(mi=r,r=s)}else mi=r,r=s;return r}function cf(){var r,t,e,n;if(r=mi,(t=lf())!==s){for(e=[],n=ff();n!==s;)e.push(n),n=ff();e!==s?(Ei=r,r=t=t+e.join("")):(mi=r,r=s)}else mi=r,r=s;return r}function lf(){var t;return Bn.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(qn)),t}function ff(){var t;return Yn.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(Wn)),t}function pf(){var t;return Vn.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(Xn)),t}function bf(){var t,e,n,o;return t=mi,e=mi,58===r.charCodeAt(mi)?(n=":",mi++):(n=s,0===Si&&Ri(Qn)),n!==s&&(o=cf())!==s?e=n=[n,o]:(mi=e,e=s),e!==s&&(Ei=t,e={type:"param",value:e[1]}),t=e}function vf(){var r,t,e;return r=mi,pp()!==s&&Hb()!==s&&Qf()!==s&&Hb()!==s&&(t=vb())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((e=Sl())===s&&(e=null),e!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,pp()!==s&&Hb()!==s&&Qf()!==s&&Hb()!==s&&(t=vb())!==s?(Ei=r,r=function(r){return{type:"on update",keyword:r}}(t)):(mi=r,r=s)),r}function yf(){var t,e,n,o,u;return t=mi,"over"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Kn)),e!==s&&Hb()!==s&&(n=il())!==s?(Ei=t,t=e={type:"window",as_window_specification:n}):(mi=t,t=s),t===s&&(t=mi,"over"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Kn)),e!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s?((o=bl())===s&&(o=null),o!==s&&Hb()!==s?((u=vl())===s&&(u=null),u!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e={partitionby:o,orderby:u}):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=vf())),t}function df(){var t,e,n;return t=mi,"ignore"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(uo)),e===s&&("respect"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ao))),e!==s&&Hb()!==s?("nulls"===r.substr(mi,5).toLowerCase()?(n=r.substr(mi,5),mi+=5):(n=s,0===Si&&Ri(Cr)),n!==s?(Ei=t,t=e=e.toUpperCase()+" NULLS"):(mi=t,t=s)):(mi=t,t=s),t}function wf(){var r,t;return r=mi,Nb()!==s&&Hb()!==s&&(t=Rf())!==s?(Ei=r,r={symbol:ke,delimiter:t}):(mi=r,r=s),r}function hf(){var r,t,e,n,o,u,a,i,c,l,f;if(r=mi,(t=gp())===s&&(t=null),t!==s)if(Hb()!==s)if((e=kb())!==s)if(Hb()!==s)if((n=Nl())!==s)if(Hb()!==s)if((o=Ub())!==s)if(Hb()!==s){for(u=[],a=mi,(i=Hb())!==s?((c=Np())===s&&(c=Op()),c!==s&&(l=Hb())!==s&&(f=Nl())!==s?a=i=[i,c,l,f]:(mi=a,a=s)):(mi=a,a=s);a!==s;)u.push(a),a=mi,(i=Hb())!==s?((c=Np())===s&&(c=Op()),c!==s&&(l=Hb())!==s&&(f=Nl())!==s?a=i=[i,c,l,f]:(mi=a,a=s)):(mi=a,a=s);u!==s&&(a=Hb())!==s?((i=wf())===s&&(i=null),i!==s&&(c=Hb())!==s?((l=vl())===s&&(l=null),l!==s?(Ei=r,r=t=function(r,t,e,n,o){const s=e.length;let u=t;u.parentheses=!0;for(let r=0;r<s;++r)u=yv(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:o,separator:n}}(t,n,u,i,l)):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)}else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;else mi=r,r=s;return r===s&&(r=mi,(t=gp())===s&&(t=null),t!==s&&Hb()!==s&&(e=nl())!==s&&Hb()!==s?((n=wf())===s&&(n=null),n!==s&&Hb()!==s?((o=vl())===s&&(o=null),o!==s?(Ei=r,r=t={distinct:t,expr:e,orderby:o,separator:n}):(mi=r,r=s)):(mi=r,r=s)):(mi=r,r=s)),r}function Lf(){var t,e,n;return t=mi,(e=function(){var t;return"both"===r.substr(mi,4).toLowerCase()?(t=r.substr(mi,4),mi+=4):(t=s,0===Si&&Ri(po)),t===s&&("leading"===r.substr(mi,7).toLowerCase()?(t=r.substr(mi,7),mi+=7):(t=s,0===Si&&Ri(bo)),t===s&&("trailing"===r.substr(mi,8).toLowerCase()?(t=r.substr(mi,8),mi+=8):(t=s,0===Si&&Ri(vo)))),t}())===s&&(e=null),e!==s&&Hb()!==s?((n=Nl())===s&&(n=null),n!==s&&Hb()!==s&&ap()!==s?(Ei=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(mi=t,t=s)):(mi=t,t=s),t}function Cf(){var t,e,n,o;return t=mi,"trim"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(yo)),e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((n=Lf())===s&&(n=null),n!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...bv()}}(n,o)):(mi=t,t=s)):(mi=t,t=s),t}function mf(){var t,e,n,o;return t=mi,"crosstab"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(wo)),e!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(n=Sl())!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s&&cp()!==s&&Hb()!==s&&cf()!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(o=Bi())!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e={type:"tablefunc",name:{name:[{type:"origin",value:"crosstab"}]},args:n,as:{type:"function",name:{name:[{type:"default",value:name}]},args:{type:"expr_list",value:o.map(r=>({...r,type:"column_definition"}))},...bv()},...bv()}):(mi=t,t=s),t}function Ef(){var t,e,n,o,u,a,i;return(t=Cf())===s&&(t=mf())===s&&(t=mi,"now"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(ho)),e!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s?((o=Sl())===s&&(o=null),o!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?("at"===r.substr(mi,2).toLowerCase()?(u=r.substr(mi,2),mi+=2):(u=s,0===Si&&Ri(Lo)),u!==s&&Hb()!==s&&lb()!==s&&Hb()!==s?("zone"===r.substr(mi,4).toLowerCase()?(a=r.substr(mi,4),mi+=4):(a=s,0===Si&&Ri(Co)),a!==s&&Hb()!==s&&(i=Rf())!==s?(Ei=t,t=e=function(r,t,e){return e.prefix="at time zone",{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},suffix:e,...bv()}}(e,o,i)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=function(){var t;(t=gf())===s&&(t=yb())===s&&(t=function(){var t,e,n,o;t=mi,"user"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(la));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="USER"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=db())===s&&(t=function(){var t,e,n,o;t=mi,"system_user"===r.substr(mi,11).toLowerCase()?(e=r.substr(mi,11),mi+=11):(e=s,0===Si&&Ri(xa));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SYSTEM_USER"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&("ntile"===r.substr(mi,5).toLowerCase()?(t=r.substr(mi,5),mi+=5):(t=s,0===Si&&Ri(Bo)));return t}())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s?((o=Sl())===s&&(o=null),o!==s&&Hb()!==s&&Ub()!==s&&Hb()!==s?((u=yf())===s&&(u=null),u!==s?(Ei=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...bv()}}(e,o,u)):(mi=t,t=s)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=function(){var r,t,e,n,o;r=mi,(t=Up())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(e=Af())!==s&&Hb()!==s&&ap()!==s&&Hb()!==s?((n=fb())===s&&(n=bb())===s&&(n=lb())===s&&(n=ab()),n===s&&(n=null),n!==s&&Hb()!==s&&(o=Nl())!==s&&Hb()!==s&&Ub()!==s?(Ei=r,u=e,a=n,i=o,t={type:t.toLowerCase(),args:{field:u,cast_type:a,source:i},...bv()},r=t):(mi=r,r=s)):(mi=r,r=s);var u,a,i;r===s&&(r=mi,(t=Up())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s&&(e=Af())!==s&&Hb()!==s&&ap()!==s&&Hb()!==s&&(n=Nl())!==s&&Hb()!==s&&(o=Ub())!==s?(Ei=r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...bv()}}(t,e,n),r=t):(mi=r,r=s));return r}())===s&&(t=mi,(e=gf())!==s&&Hb()!==s?((n=vf())===s&&(n=null),n!==s?(Ei=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...bv()}):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=tv())!==s&&Hb()!==s&&(n=kb())!==s&&Hb()!==s?((o=Ol())===s&&(o=null),o!==s&&Hb()!==s&&Ub()!==s?(Ei=t,t=e=function(r,t){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},...bv()}}(e,o)):(mi=t,t=s)):(mi=t,t=s))))),t}function Af(){var t,e;return t=mi,"century"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(mo)),e===s&&("day"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Eo)),e===s&&("date"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ao)),e===s&&("decade"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(go)),e===s&&("dow"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(To)),e===s&&("doy"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(So)),e===s&&("epoch"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(_o)),e===s&&("hour"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(xo)),e===s&&("isodow"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(jo)),e===s&&("isoyear"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Io)),e===s&&("microseconds"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Ro)),e===s&&("millennium"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(No)),e===s&&("milliseconds"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Oo)),e===s&&("minute"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ko)),e===s&&("month"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Uo)),e===s&&("quarter"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Mo)),e===s&&("second"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Do)),e===s&&("timezone"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Po)),e===s&&("timezone_hour"===r.substr(mi,13).toLowerCase()?(e=r.substr(mi,13),mi+=13):(e=s,0===Si&&Ri(Go)),e===s&&("timezone_minute"===r.substr(mi,15).toLowerCase()?(e=r.substr(mi,15),mi+=15):(e=s,0===Si&&Ri($o)),e===s&&("week"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Fo)),e===s&&("year"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ho))))))))))))))))))))))),e!==s&&(Ei=t,e=e),t=e}function gf(){var t;return(t=function(){var t,e,n,o;t=mi,"current_date"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(ga));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CURRENT_DATE"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=mi,"current_time"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Sa));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CURRENT_TIME"):(mi=t,t=s)):(mi=t,t=s);return t}())===s&&(t=vb()),t}function Tf(){var r,t,e,n,o,u;if(r=mi,t=[],e=mi,(n=Cb())!==s&&(o=Hb())!==s&&(u=uv())!==s?e=n=[n,o,u]:(mi=e,e=s),e!==s)for(;e!==s;)t.push(e),e=mi,(n=Cb())!==s&&(o=Hb())!==s&&(u=uv())!==s?e=n=[n,o,u]:(mi=e,e=s);else t=s;return t!==s&&(e=Hb())!==s?((n=Yc())===s&&(n=null),n!==s?(Ei=r,r=t={as:n,symbol:"::",target:t.map(r=>r[2])}):(mi=r,r=s)):(mi=r,r=s),r}function Sf(){var t;return(t=Rf())===s&&(t=Uf())===s&&(t=If())===s&&(t=xf())===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=lb())===s&&(e=ab())===s&&(e=fb())===s&&(e=ib());if(e!==s)if(Hb()!==s){if(n=mi,39===r.charCodeAt(mi)?(o="'",mi++):(o=s,0===Si&&Ri(jt)),o!==s){for(u=[],a=Of();a!==s;)u.push(a),a=Of();u!==s?(39===r.charCodeAt(mi)?(a="'",mi++):(a=s,0===Si&&Ri(jt)),a!==s?n=o=[o,u,a]:(mi=n,n=s)):(mi=n,n=s)}else mi=n,n=s;n!==s?(Ei=t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;var i;if(t===s)if(t=mi,(e=lb())===s&&(e=ab())===s&&(e=fb())===s&&(e=ib()),e!==s)if(Hb()!==s){if(n=mi,34===r.charCodeAt(mi)?(o='"',mi++):(o=s,0===Si&&Ri(Me)),o!==s){for(u=[],a=Nf();a!==s;)u.push(a),a=Nf();u!==s?(34===r.charCodeAt(mi)?(a='"',mi++):(a=s,0===Si&&Ri(Me)),a!==s?n=o=[o,u,a]:(mi=n,n=s)):(mi=n,n=s)}else mi=n,n=s;n!==s?(Ei=t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;return t}())===s&&(t=_f()),t}function _f(){var r,t;return r=mi,kp()!==s&&Hb()!==s&&Mb()!==s&&Hb()!==s?((t=Sl())===s&&(t=null),t!==s&&Hb()!==s&&Db()!==s?(Ei=r,r={expr_list:t||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}):(mi=r,r=s)):(mi=r,r=s),r}function xf(){var r,t;return r=mi,(t=Hf())!==s&&(Ei=r,t={type:"null",value:null}),r=t}function jf(){var t,e;return t=mi,(e=function(){var t,e,n,o;t=mi,"not null"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ls));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e={type:"not null",value:"not null"}),t=e}function If(){var t,e;return t=mi,(e=function(){var t,e,n,o;t=mi,"true"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Cs));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e={type:"bool",value:!0}),(t=e)===s&&(t=mi,(e=function(){var t,e,n,o;t=mi,"false"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ms));e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s);return t}())!==s&&(Ei=t,e={type:"bool",value:!1}),t=e),t}function Rf(){var t,e,n,o,u,a,i,c,l;if(t=mi,e=mi,39===r.charCodeAt(mi)?(n="'",mi++):(n=s,0===Si&&Ri(jt)),n!==s){for(o=[],u=Of();u!==s;)o.push(u),u=Of();o!==s?(39===r.charCodeAt(mi)?(u="'",mi++):(u=s,0===Si&&Ri(jt)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s)}else mi=e,e=s;if(e!==s){if(n=[],qo.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Yo)),o!==s)for(;o!==s;)n.push(o),qo.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(Yo));else n=s;if(n!==s)if((o=Hb())!==s){if(u=mi,39===r.charCodeAt(mi)?(a="'",mi++):(a=s,0===Si&&Ri(jt)),a!==s){for(i=[],c=Of();c!==s;)i.push(c),c=Of();i!==s?(39===r.charCodeAt(mi)?(c="'",mi++):(c=s,0===Si&&Ri(jt)),c!==s?u=a=[a,i,c]:(mi=u,u=s)):(mi=u,u=s)}else mi=u,u=s;u!==s?(Ei=t,l=u,t=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`}):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s}else mi=t,t=s;if(t===s){if(t=mi,e=mi,39===r.charCodeAt(mi)?(n="'",mi++):(n=s,0===Si&&Ri(jt)),n!==s){for(o=[],u=Of();u!==s;)o.push(u),u=Of();o!==s?(39===r.charCodeAt(mi)?(u="'",mi++):(u=s,0===Si&&Ri(jt)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s)}else mi=e,e=s;if(e!==s&&(Ei=t,e=function(r){return{type:"single_quote_string",value:r[1].join("")}}(e)),(t=e)===s){if(t=mi,e=mi,34===r.charCodeAt(mi)?(n='"',mi++):(n=s,0===Si&&Ri(Me)),n!==s){for(o=[],u=Nf();u!==s;)o.push(u),u=Nf();o!==s?(34===r.charCodeAt(mi)?(u='"',mi++):(u=s,0===Si&&Ri(Me)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s)}else mi=e,e=s;e!==s?(n=mi,Si++,o=Rb(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)):(mi=t,t=s)):(mi=t,t=s)}}return t}function Nf(){var t;return Wo.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(Vo)),t===s&&(t=kf()),t}function Of(){var t;return Xo.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(Qo)),t===s&&(t=kf()),t}function kf(){var t,e,n,o,u,a,i,c,l,f;return t=mi,"\\'"===r.substr(mi,2)?(e="\\'",mi+=2):(e=s,0===Si&&Ri(Ko)),e!==s&&(Ei=t,e="\\'"),(t=e)===s&&(t=mi,'\\"'===r.substr(mi,2)?(e='\\"',mi+=2):(e=s,0===Si&&Ri(zo)),e!==s&&(Ei=t,e='\\"'),(t=e)===s&&(t=mi,"\\\\"===r.substr(mi,2)?(e="\\\\",mi+=2):(e=s,0===Si&&Ri(Zo)),e!==s&&(Ei=t,e="\\\\"),(t=e)===s&&(t=mi,"\\/"===r.substr(mi,2)?(e="\\/",mi+=2):(e=s,0===Si&&Ri(Jo)),e!==s&&(Ei=t,e="\\/"),(t=e)===s&&(t=mi,"\\b"===r.substr(mi,2)?(e="\\b",mi+=2):(e=s,0===Si&&Ri(rs)),e!==s&&(Ei=t,e="\b"),(t=e)===s&&(t=mi,"\\f"===r.substr(mi,2)?(e="\\f",mi+=2):(e=s,0===Si&&Ri(ts)),e!==s&&(Ei=t,e="\f"),(t=e)===s&&(t=mi,"\\n"===r.substr(mi,2)?(e="\\n",mi+=2):(e=s,0===Si&&Ri(es)),e!==s&&(Ei=t,e="\n"),(t=e)===s&&(t=mi,"\\r"===r.substr(mi,2)?(e="\\r",mi+=2):(e=s,0===Si&&Ri(ns)),e!==s&&(Ei=t,e="\r"),(t=e)===s&&(t=mi,"\\t"===r.substr(mi,2)?(e="\\t",mi+=2):(e=s,0===Si&&Ri(os)),e!==s&&(Ei=t,e="\t"),(t=e)===s&&(t=mi,"\\u"===r.substr(mi,2)?(e="\\u",mi+=2):(e=s,0===Si&&Ri(ss)),e!==s&&(n=Ff())!==s&&(o=Ff())!==s&&(u=Ff())!==s&&(a=Ff())!==s?(Ei=t,i=n,c=o,l=u,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(mi=t,t=s),t===s&&(t=mi,92===r.charCodeAt(mi)?(e="\\",mi++):(e=s,0===Si&&Ri(us)),e!==s&&(Ei=t,e="\\"),(t=e)===s&&(t=mi,"''"===r.substr(mi,2)?(e="''",mi+=2):(e=s,0===Si&&Ri(as)),e!==s&&(Ei=t,e="''"),t=e))))))))))),t}function Uf(){var r,t,e;return r=mi,(t=function(){var r,t,e,n;r=mi,(t=Mf())===s&&(t=null);t!==s&&(e=Df())!==s&&(n=Pf())!==s?(Ei=r,r=t={type:"bigint",value:(t||"")+e+n}):(mi=r,r=s);r===s&&(r=mi,(t=Mf())===s&&(t=null),t!==s&&(e=Df())!==s?(Ei=r,t=function(r,t){const e=(r||"")+t;return r&&dv(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(mi=r,r=s),r===s&&(r=mi,(t=Mf())!==s&&(e=Pf())!==s?(Ei=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(mi=r,r=s),r===s&&(r=mi,(t=Mf())!==s&&(Ei=r,t=function(r){return dv(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==s&&(Ei=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function Mf(){var t,e,n;return(t=Gf())===s&&(t=$f())===s&&(t=mi,45===r.charCodeAt(mi)?(e="-",mi++):(e=s,0===Si&&Ri(Cn)),e===s&&(43===r.charCodeAt(mi)?(e="+",mi++):(e=s,0===Si&&Ri(Ln))),e!==s&&(n=Gf())!==s?(Ei=t,t=e=e+n):(mi=t,t=s),t===s&&(t=mi,45===r.charCodeAt(mi)?(e="-",mi++):(e=s,0===Si&&Ri(Cn)),e===s&&(43===r.charCodeAt(mi)?(e="+",mi++):(e=s,0===Si&&Ri(Ln))),e!==s&&(n=$f())!==s?(Ei=t,t=e=function(r,t){return r+t}(e,n)):(mi=t,t=s))),t}function Df(){var t,e,n;return t=mi,46===r.charCodeAt(mi)?(e=".",mi++):(e=s,0===Si&&Ri(ls)),e!==s&&(n=Gf())!==s?(Ei=t,t=e="."+n):(mi=t,t=s),t}function Pf(){var t,e,n;return t=mi,(e=function(){var t,e,n;t=mi,ys.test(r.charAt(mi))?(e=r.charAt(mi),mi++):(e=s,0===Si&&Ri(ds));e!==s?(ws.test(r.charAt(mi))?(n=r.charAt(mi),mi++):(n=s,0===Si&&Ri(hs)),n===s&&(n=null),n!==s?(Ei=t,t=e=e+(null!==(o=n)?o:"")):(mi=t,t=s)):(mi=t,t=s);var o;return t}())!==s&&(n=Gf())!==s?(Ei=t,t=e=e+n):(mi=t,t=s),t}function Gf(){var r,t,e;if(r=mi,t=[],(e=$f())!==s)for(;e!==s;)t.push(e),e=$f();else t=s;return t!==s&&(Ei=r,t=t.join("")),r=t}function $f(){var t;return fs.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(ps)),t}function Ff(){var t;return bs.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(vs)),t}function Hf(){var t,e,n,o;return t=mi,"null"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri($)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Bf(){var t,e,n,o;return t=mi,"default"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ir)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function qf(){var t,e,n,o;return t=mi,"to"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(or)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Yf(){var t,e,n,o;return t=mi,"show"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Es)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Wf(){var t,e,n,o;return t=mi,"drop"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(As)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DROP"):(mi=t,t=s)):(mi=t,t=s),t}function Vf(){var t,e,n,o;return t=mi,"alter"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Ts)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Xf(){var t,e,n,o;return t=mi,"select"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Ss)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Qf(){var t,e,n,o;return t=mi,"update"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(_s)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Kf(){var t,e,n,o;return t=mi,"create"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(xs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function zf(){var t,e,n,o;return t=mi,"temporary"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(js)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Zf(){var t,e,n,o;return t=mi,"temp"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Is)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Jf(){var t,e,n,o;return t=mi,"delete"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Rs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function rp(){var t,e,n,o;return t=mi,"insert"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Ns)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function tp(){var t,e,n,o;return t=mi,"recursive"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Os)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="RECURSIVE"):(mi=t,t=s)):(mi=t,t=s),t}function ep(){var t,e,n,o;return t=mi,"replace"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ks)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function np(){var t,e,n,o;return t=mi,"rename"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Ms)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function op(){var t,e,n,o;return t=mi,"ignore"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(uo)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function sp(){var t,e,n,o;return t=mi,"partition"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(Ds)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="PARTITION"):(mi=t,t=s)):(mi=t,t=s),t}function up(){var t,e,n,o;return t=mi,"into"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ps)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function ap(){var t,e,n,o;return t=mi,"from"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Gs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function ip(){var t,e,n,o;return t=mi,"set"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Lt)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SET"):(mi=t,t=s)):(mi=t,t=s),t}function cp(){var t,e,n,o;return t=mi,"as"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri($s)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function lp(){var t,e,n,o;return t=mi,"table"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(xe)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TABLE"):(mi=t,t=s)):(mi=t,t=s),t}function fp(){var t,e,n,o;return t=mi,"schema"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(l)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SCHEMA"):(mi=t,t=s)):(mi=t,t=s),t}function pp(){var t,e,n,o;return t=mi,"on"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(F)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function bp(){var t,e,n,o;return t=mi,"join"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Vs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function vp(){var t,e,n,o;return t=mi,"outer"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Xs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function yp(){var t,e,n,o;return t=mi,"values"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Zs)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function dp(){var t,e,n,o;return t=mi,"using"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Js)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function wp(){var t,e,n,o;return t=mi,"with"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Be)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function hp(){var t,e,n,o;return t=mi,"group"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(tu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Lp(){var t,e,n,o;return t=mi,"by"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(eu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Cp(){var t,e,n,o;return t=mi,"order"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(nu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function mp(){var t,e,n,o;return t=mi,"asc"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(au)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ASC"):(mi=t,t=s)):(mi=t,t=s),t}function Ep(){var t,e,n,o;return t=mi,"desc"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(iu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DESC"):(mi=t,t=s)):(mi=t,t=s),t}function Ap(){var t,e,n,o;return t=mi,"all"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(cu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ALL"):(mi=t,t=s)):(mi=t,t=s),t}function gp(){var t,e,n,o;return t=mi,"distinct"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(lu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DISTINCT"):(mi=t,t=s)):(mi=t,t=s),t}function Tp(){var t,e,n,o;return t=mi,"between"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(fu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="BETWEEN"):(mi=t,t=s)):(mi=t,t=s),t}function Sp(){var t,e,n,o;return t=mi,"in"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(Ot)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="IN"):(mi=t,t=s)):(mi=t,t=s),t}function _p(){var t,e,n,o;return t=mi,"is"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(pu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="IS"):(mi=t,t=s)):(mi=t,t=s),t}function xp(){var t,e,n,o;return t=mi,"like"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(bu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="LIKE"):(mi=t,t=s)):(mi=t,t=s),t}function jp(){var t,e,n,o;return t=mi,"ilike"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(vu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ILIKE"):(mi=t,t=s)):(mi=t,t=s),t}function Ip(){var t,e,n,o;return t=mi,"exists"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(yu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="EXISTS"):(mi=t,t=s)):(mi=t,t=s),t}function Rp(){var t,e,n,o;return t=mi,"not"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(D)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="NOT"):(mi=t,t=s)):(mi=t,t=s),t}function Np(){var t,e,n,o;return t=mi,"and"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(du)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="AND"):(mi=t,t=s)):(mi=t,t=s),t}function Op(){var t,e,n,o;return t=mi,"or"===r.substr(mi,2).toLowerCase()?(e=r.substr(mi,2),mi+=2):(e=s,0===Si&&Ri(wu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="OR"):(mi=t,t=s)):(mi=t,t=s),t}function kp(){var t,e,n,o;return t=mi,"array"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(hu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ARRAY"):(mi=t,t=s)):(mi=t,t=s),t}function Up(){var t,e,n,o;return t=mi,"extract"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(_u)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="EXTRACT"):(mi=t,t=s)):(mi=t,t=s),t}function Mp(){var t,e,n,o;return t=mi,"case"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(ju)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Dp(){var t,e,n,o;return t=mi,"when"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Iu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Pp(){var t,e,n,o;return t=mi,"else"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ru)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function Gp(){var t,e,n,o;return t=mi,"end"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(ve)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?t=e=[e,n]:(mi=t,t=s)):(mi=t,t=s),t}function $p(){var t,e,n,o;return t=mi,"cast"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Nu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CAST"):(mi=t,t=s)):(mi=t,t=s),t}function Fp(){var t,e,n,o;return t=mi,"char"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Uu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CHAR"):(mi=t,t=s)):(mi=t,t=s),t}function Hp(){var t,e,n,o;return t=mi,"varchar"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Mu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="VARCHAR"):(mi=t,t=s)):(mi=t,t=s),t}function Bp(){var t,e,n,o;return t=mi,"numeric"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Du)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="NUMERIC"):(mi=t,t=s)):(mi=t,t=s),t}function qp(){var t,e,n,o;return t=mi,"decimal"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Pu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DECIMAL"):(mi=t,t=s)):(mi=t,t=s),t}function Yp(){var t,e,n,o;return t=mi,"unsigned"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri($u)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="UNSIGNED"):(mi=t,t=s)):(mi=t,t=s),t}function Wp(){var t,e,n,o;return t=mi,"int"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Fu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="INT"):(mi=t,t=s)):(mi=t,t=s),t}function Vp(){var t,e,n,o;return t=mi,"integer"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Bu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="INTEGER"):(mi=t,t=s)):(mi=t,t=s),t}function Xp(){var t,e,n,o;return t=mi,"smallint"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Vu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SMALLINT"):(mi=t,t=s)):(mi=t,t=s),t}function Qp(){var t,e,n,o;return t=mi,"serial"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Xu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SERIAL"):(mi=t,t=s)):(mi=t,t=s),t}function Kp(){var t,e,n,o;return t=mi,"tinyint"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Qu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TINYINT"):(mi=t,t=s)):(mi=t,t=s),t}function zp(){var t,e,n,o;return t=mi,"tinytext"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ku)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TINYTEXT"):(mi=t,t=s)):(mi=t,t=s),t}function Zp(){var t,e,n,o;return t=mi,"text"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(zu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TEXT"):(mi=t,t=s)):(mi=t,t=s),t}function Jp(){var t,e,n,o;return t=mi,"mediumtext"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Zu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="MEDIUMTEXT"):(mi=t,t=s)):(mi=t,t=s),t}function rb(){var t,e,n,o;return t=mi,"longtext"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ju)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="LONGTEXT"):(mi=t,t=s)):(mi=t,t=s),t}function tb(){var t,e,n,o;return t=mi,"bigint"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(ra)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="BIGINT"):(mi=t,t=s)):(mi=t,t=s),t}function eb(){var t,e,n,o;return t=mi,"enum"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(ta)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ENUM"):(mi=t,t=s)):(mi=t,t=s),t}function nb(){var t,e,n,o;return t=mi,"float"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(ea)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="FLOAT"):(mi=t,t=s)):(mi=t,t=s),t}function ob(){var t,e,n,o;return t=mi,"double"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(na)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DOUBLE"):(mi=t,t=s)):(mi=t,t=s),t}function sb(){var t,e,n,o;return t=mi,"bigserial"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(oa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="BIGSERIAL"):(mi=t,t=s)):(mi=t,t=s),t}function ub(){var t,e,n,o;return t=mi,"real"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(sa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REAL"):(mi=t,t=s)):(mi=t,t=s),t}function ab(){var t,e,n,o;return t=mi,"date"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ao)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DATE"):(mi=t,t=s)):(mi=t,t=s),t}function ib(){var t,e,n,o;return t=mi,"datetime"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(ua)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="DATETIME"):(mi=t,t=s)):(mi=t,t=s),t}function cb(){var t,e,n,o;return t=mi,"rows"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(er)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ROWS"):(mi=t,t=s)):(mi=t,t=s),t}function lb(){var t,e,n,o;return t=mi,"time"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(aa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TIME"):(mi=t,t=s)):(mi=t,t=s),t}function fb(){var t,e,n,o;return t=mi,"timestamp"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(ia)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TIMESTAMP"):(mi=t,t=s)):(mi=t,t=s),t}function pb(){var t,e,n,o;return t=mi,"truncate"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(ca)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="TRUNCATE"):(mi=t,t=s)):(mi=t,t=s),t}function bb(){var t,e,n,o;return t=mi,"interval"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Ta)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="INTERVAL"):(mi=t,t=s)):(mi=t,t=s),t}function vb(){var t,e,n,o;return t=mi,"current_timestamp"===r.substr(mi,17).toLowerCase()?(e=r.substr(mi,17),mi+=17):(e=s,0===Si&&Ri(_a)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CURRENT_TIMESTAMP"):(mi=t,t=s)):(mi=t,t=s),t}function yb(){var t,e,n,o;return t=mi,"current_user"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Fr)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CURRENT_USER"):(mi=t,t=s)):(mi=t,t=s),t}function db(){var t,e,n,o;return t=mi,"session_user"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Hr)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="SESSION_USER"):(mi=t,t=s)):(mi=t,t=s),t}function wb(){var t;return 36===r.charCodeAt(mi)?(t="$",mi++):(t=s,0===Si&&Ri(Tn)),t}function hb(){var t;return"$$"===r.substr(mi,2)?(t="$$",mi+=2):(t=s,0===Si&&Ri(Ma)),t}function Lb(){var t;return(t=function(){var t;return"@@"===r.substr(mi,2)?(t="@@",mi+=2):(t=s,0===Si&&Ri(Ua)),t}())===s&&(t=function(){var t;return 64===r.charCodeAt(mi)?(t="@",mi++):(t=s,0===Si&&Ri(ka)),t}())===s&&(t=wb())===s&&(t=wb()),t}function Cb(){var t;return"::"===r.substr(mi,2)?(t="::",mi+=2):(t=s,0===Si&&Ri(Pa)),t}function mb(){var t;return 61===r.charCodeAt(mi)?(t="=",mi++):(t=s,0===Si&&Ri(sr)),t}function Eb(){var t,e,n,o;return t=mi,"add"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri($a)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ADD"):(mi=t,t=s)):(mi=t,t=s),t}function Ab(){var t,e,n,o;return t=mi,"column"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Te)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="COLUMN"):(mi=t,t=s)):(mi=t,t=s),t}function gb(){var t,e,n,o;return t=mi,"index"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Fa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="INDEX"):(mi=t,t=s)):(mi=t,t=s),t}function Tb(){var t,e,n,o;return t=mi,"key"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(Tr)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="KEY"):(mi=t,t=s)):(mi=t,t=s),t}function Sb(){var t,e,n,o;return t=mi,"unique"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(gr)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="UNIQUE"):(mi=t,t=s)):(mi=t,t=s),t}function _b(){var t,e,n,o;return t=mi,"comment"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(qa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="COMMENT"):(mi=t,t=s)):(mi=t,t=s),t}function xb(){var t,e,n,o;return t=mi,"constraint"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Se)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CONSTRAINT"):(mi=t,t=s)):(mi=t,t=s),t}function jb(){var t,e,n,o;return t=mi,"concurrently"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(Ya)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CONCURRENTLY"):(mi=t,t=s)):(mi=t,t=s),t}function Ib(){var t,e,n,o;return t=mi,"references"===r.substr(mi,10).toLowerCase()?(e=r.substr(mi,10),mi+=10):(e=s,0===Si&&Ri(Wa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REFERENCES"):(mi=t,t=s)):(mi=t,t=s),t}function Rb(){var t;return 46===r.charCodeAt(mi)?(t=".",mi++):(t=s,0===Si&&Ri(ls)),t}function Nb(){var t;return 44===r.charCodeAt(mi)?(t=",",mi++):(t=s,0===Si&&Ri(Ja)),t}function Ob(){var t;return 42===r.charCodeAt(mi)?(t="*",mi++):(t=s,0===Si&&Ri(mn)),t}function kb(){var t;return 40===r.charCodeAt(mi)?(t="(",mi++):(t=s,0===Si&&Ri(Oe)),t}function Ub(){var t;return 41===r.charCodeAt(mi)?(t=")",mi++):(t=s,0===Si&&Ri(Ue)),t}function Mb(){var t;return 91===r.charCodeAt(mi)?(t="[",mi++):(t=s,0===Si&&Ri(ri)),t}function Db(){var t;return 93===r.charCodeAt(mi)?(t="]",mi++):(t=s,0===Si&&Ri(ti)),t}function Pb(){var t;return 59===r.charCodeAt(mi)?(t=";",mi++):(t=s,0===Si&&Ri(Ne)),t}function Gb(){var t;return"->"===r.substr(mi,2)?(t="->",mi+=2):(t=s,0===Si&&Ri(ei)),t}function $b(){var t;return"->>"===r.substr(mi,3)?(t="->>",mi+=3):(t=s,0===Si&&Ri(ni)),t}function Fb(){var t;return(t=function(){var t;return"||"===r.substr(mi,2)?(t="||",mi+=2):(t=s,0===Si&&Ri(gn)),t}())===s&&(t=function(){var t;return"&&"===r.substr(mi,2)?(t="&&",mi+=2):(t=s,0===Si&&Ri(oi)),t}()),t}function Hb(){var r,t;for(r=[],(t=Vb())===s&&(t=qb());t!==s;)r.push(t),(t=Vb())===s&&(t=qb());return r}function Bb(){var r,t;if(r=[],(t=Vb())===s&&(t=qb()),t!==s)for(;t!==s;)r.push(t),(t=Vb())===s&&(t=qb());else r=s;return r}function qb(){var t;return(t=function t(){var e,n,o,u,a,i,c;e=mi,"/*"===r.substr(mi,2)?(n="/*",mi+=2):(n=s,0===Si&&Ri(si));if(n!==s){for(o=[],u=mi,a=mi,Si++,"*/"===r.substr(mi,2)?(i="*/",mi+=2):(i=s,0===Si&&Ri(ui)),Si--,i===s?a=void 0:(mi=a,a=s),a!==s?(i=mi,Si++,"/*"===r.substr(mi,2)?(c="/*",mi+=2):(c=s,0===Si&&Ri(si)),Si--,c===s?i=void 0:(mi=i,i=s),i!==s&&(c=Wb())!==s?u=a=[a,i,c]:(mi=u,u=s)):(mi=u,u=s),u===s&&(u=t());u!==s;)o.push(u),u=mi,a=mi,Si++,"*/"===r.substr(mi,2)?(i="*/",mi+=2):(i=s,0===Si&&Ri(ui)),Si--,i===s?a=void 0:(mi=a,a=s),a!==s?(i=mi,Si++,"/*"===r.substr(mi,2)?(c="/*",mi+=2):(c=s,0===Si&&Ri(si)),Si--,c===s?i=void 0:(mi=i,i=s),i!==s&&(c=Wb())!==s?u=a=[a,i,c]:(mi=u,u=s)):(mi=u,u=s),u===s&&(u=t());o!==s?("*/"===r.substr(mi,2)?(u="*/",mi+=2):(u=s,0===Si&&Ri(ui)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s)}else mi=e,e=s;return e}())===s&&(t=function(){var t,e,n,o,u,a;t=mi,"--"===r.substr(mi,2)?(e="--",mi+=2):(e=s,0===Si&&Ri(ai));if(e!==s){for(n=[],o=mi,u=mi,Si++,a=Xb(),Si--,a===s?u=void 0:(mi=u,u=s),u!==s&&(a=Wb())!==s?o=u=[u,a]:(mi=o,o=s);o!==s;)n.push(o),o=mi,u=mi,Si++,a=Xb(),Si--,a===s?u=void 0:(mi=u,u=s),u!==s&&(a=Wb())!==s?o=u=[u,a]:(mi=o,o=s);n!==s?t=e=[e,n]:(mi=t,t=s)}else mi=t,t=s;return t}()),t}function Yb(){var r,t,e,n,o,u,a;return r=mi,(t=_b())!==s&&Hb()!==s?((e=mb())===s&&(e=null),e!==s&&Hb()!==s&&(n=Rf())!==s?(Ei=r,u=e,a=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:a}):(mi=r,r=s)):(mi=r,r=s),r}function Wb(){var t;return r.length>mi?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(ii)),t}function Vb(){var t;return ci.test(r.charAt(mi))?(t=r.charAt(mi),mi++):(t=s,0===Si&&Ri(li)),t}function Xb(){var t,e;if((t=function(){var t,e;t=mi,Si++,r.length>mi?(e=r.charAt(mi),mi++):(e=s,0===Si&&Ri(ii));Si--,e===s?t=void 0:(mi=t,t=s);return t}())===s)if(t=[],is.test(r.charAt(mi))?(e=r.charAt(mi),mi++):(e=s,0===Si&&Ri(cs)),e!==s)for(;e!==s;)t.push(e),is.test(r.charAt(mi))?(e=r.charAt(mi),mi++):(e=s,0===Si&&Ri(cs));else t=s;return t}function Qb(){var t,e;return t=mi,Ei=mi,Ev=[],(!0?void 0:s)!==s&&Hb()!==s?((e=Kb())===s&&(e=function(){var t,e;t=mi,function(){var t;return"return"===r.substr(mi,6).toLowerCase()?(t=r.substr(mi,6),mi+=6):(t=s,0===Si&&Ri(Da)),t}()!==s&&Hb()!==s&&(e=zb())!==s?(Ei=t,t={type:"return",expr:e}):(mi=t,t=s);return t}()),e!==s?(Ei=t,t={type:"proc",stmt:e,vars:Ev}):(mi=t,t=s)):(mi=t,t=s),t}function Kb(){var t,e,n,o;return t=mi,(e=ov())===s&&(e=sv()),e!==s&&Hb()!==s?((n=function(){var t;return":="===r.substr(mi,2)?(t=":=",mi+=2):(t=s,0===Si&&Ri(T)),t}())===s&&(n=mb()),n!==s&&Hb()!==s&&(o=zb())!==s?(Ei=t,t=e={type:"assign",left:e,symbol:n,right:o}):(mi=t,t=s)):(mi=t,t=s),t}function zb(){var r;return(r=Nc())===s&&(r=function(){var r,t,e,n,o;r=mi,(t=ov())!==s&&Hb()!==s&&(e=tl())!==s&&Hb()!==s&&(n=ov())!==s&&Hb()!==s&&(o=ol())!==s?(Ei=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(mi=r,r=s);return r}())===s&&(r=Zb())===s&&(r=function(){var r,t;r=mi,Mb()!==s&&Hb()!==s&&(t=nv())!==s&&Hb()!==s&&Db()!==s?(Ei=r,r={type:"array",value:t}):(mi=r,r=s);return r}()),r}function Zb(){var r,t,e,n,o,u,a,i;if(r=mi,(t=Jb())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Fl())!==s&&(a=Hb())!==s&&(i=Jb())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Fl())!==s&&(a=Hb())!==s&&(i=Jb())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=on(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function Jb(){var r,t,e,n,o,u,a,i;if(r=mi,(t=rv())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Bl())!==s&&(a=Hb())!==s&&(i=rv())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Bl())!==s&&(a=Hb())!==s&&(i=rv())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=on(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function rv(){var r,t,e,n,o,u,a,i,c;return(r=Sf())===s&&(r=ov())===s&&(r=ev())===s&&(r=bf())===s&&(r=mi,(t=kb())!==s&&(e=Hb())!==s&&(n=Zb())!==s&&(o=Hb())!==s&&(u=Ub())!==s?(Ei=r,(c=n).parentheses=!0,r=t=c):(mi=r,r=s),r===s&&(r=mi,(t=cf())!==s?(e=mi,(n=Rb())!==s&&(o=Hb())!==s&&(u=cf())!==s?e=n=[n,o,u]:(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=r,a=t,r=t=(i=e)?{type:"column_ref",table:a,column:i[2]}:{type:"var",name:a,prefix:null}):(mi=r,r=s)):(mi=r,r=s))),r}function tv(){var r,t,e,n,o,u,a;return r=mi,(t=Kl())!==s?(e=mi,(n=Hb())!==s&&(o=Rb())!==s&&(u=Hb())!==s&&(a=Kl())!==s?e=n=[n,o,u,a]:(mi=e,e=s),e===s&&(e=null),e!==s?(Ei=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(mi=r,r=s)):(mi=r,r=s),r}function ev(){var r,t,e;return r=mi,(t=tv())!==s&&Hb()!==s&&kb()!==s&&Hb()!==s?((e=nv())===s&&(e=null),e!==s&&Hb()!==s&&Ub()!==s?(Ei=r,r=t=function(r,t){return{type:"function",name:r,args:{type:"expr_list",value:t},...bv()}}(t,e)):(mi=r,r=s)):(mi=r,r=s),r}function nv(){var r,t,e,n,o,u,a,i;if(r=mi,(t=rv())!==s){for(e=[],n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=rv())!==s?n=o=[o,u,a,i]:(mi=n,n=s);n!==s;)e.push(n),n=mi,(o=Hb())!==s&&(u=Nb())!==s&&(a=Hb())!==s&&(i=rv())!==s?n=o=[o,u,a,i]:(mi=n,n=s);e!==s?(Ei=r,r=t=wv(t,e)):(mi=r,r=s)}else mi=r,r=s;return r}function ov(){var t,e,n,o,u,a,i;if(t=mi,(e=hb())!==s){for(n=[],fi.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(pi));o!==s;)n.push(o),fi.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(pi));n!==s&&(o=hb())!==s?(Ei=t,t=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(mi=t,t=s)}else mi=t,t=s;if(t===s){if(t=mi,(e=wb())!==s)if((n=uf())!==s)if((o=wb())!==s){for(u=[],fi.test(r.charAt(mi))?(a=r.charAt(mi),mi++):(a=s,0===Si&&Ri(pi));a!==s;)u.push(a),fi.test(r.charAt(mi))?(a=r.charAt(mi),mi++):(a=s,0===Si&&Ri(pi));u!==s&&(a=wb())!==s&&(i=uf())!==s?(Ei=mi,(function(r,t,e){if(r!==e)return!0}(n,0,i)?s:void 0)!==s&&wb()!==s?(Ei=t,t=e=function(r,t,e){return{type:"var",name:t.join(""),prefix:`$${r}$`,suffix:`$${e}$`}}(n,u,i)):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;t===s&&(t=mi,(e=Lb())!==s&&(n=sv())!==s?(Ei=t,t=e=function(r,t){return{type:"var",...t,prefix:r}}(e,n)):(mi=t,t=s))}return t}function sv(){var t,e,n,o,u;return t=mi,34===r.charCodeAt(mi)?(e='"',mi++):(e=s,0===Si&&Ri(Me)),e===s&&(e=null),e!==s&&(n=cf())!==s&&(o=function(){var t,e,n,o,u;t=mi,e=[],n=mi,46===r.charCodeAt(mi)?(o=".",mi++):(o=s,0===Si&&Ri(ls));o!==s&&(u=cf())!==s?n=o=[o,u]:(mi=n,n=s);for(;n!==s;)e.push(n),n=mi,46===r.charCodeAt(mi)?(o=".",mi++):(o=s,0===Si&&Ri(ls)),o!==s&&(u=cf())!==s?n=o=[o,u]:(mi=n,n=s);e!==s&&(Ei=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==s?(34===r.charCodeAt(mi)?(u='"',mi++):(u=s,0===Si&&Ri(Me)),u===s&&(u=null),u!==s?(Ei=t,t=e=function(r,t,e,n){if(r&&!n||!r&&n)throw new Error("double quoted not match");return Ev.push(t),{type:"var",name:t,members:e,quoted:r&&n?'"':null,prefix:null}}(e,n,o,u)):(mi=t,t=s)):(mi=t,t=s),t===s&&(t=mi,(e=Uf())!==s&&(Ei=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function uv(){var t;return(t=function(){var r,t,e;r=mi,(t=lv())===s&&(t=iv());t!==s&&Hb()!==s&&Mb()!==s&&Hb()!==s&&(e=Db())!==s&&Hb()!==s&&Mb()!==s&&Hb()!==s&&Db()!==s?(Ei=r,n=t,t={...n,array:{dimension:2}},r=t):(mi=r,r=s);var n;r===s&&(r=mi,(t=lv())===s&&(t=iv()),t!==s&&Hb()!==s&&Mb()!==s&&Hb()!==s?((e=Uf())===s&&(e=null),e!==s&&Hb()!==s&&Db()!==s?(Ei=r,t=function(r,t){return{...r,array:{dimension:1,length:[t]}}}(t,e),r=t):(mi=r,r=s)):(mi=r,r=s),r===s&&(r=mi,(t=lv())===s&&(t=iv()),t!==s&&Hb()!==s&&kp()!==s?(Ei=r,t=function(r){return{...r,array:{keyword:"array"}}}(t),r=t):(mi=r,r=s)));return r}())===s&&(t=iv())===s&&(t=lv())===s&&(t=function(){var t,e,n,o;t=mi,(e=ab())===s&&(e=ib());if(e!==s)if(Hb()!==s)if(kb()!==s)if(Hb()!==s){if(n=[],fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps)),o!==s)for(;o!==s;)n.push(o),fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps));else n=s;n!==s&&(o=Hb())!==s&&Ub()!==s?(Ei=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;t===s&&(t=mi,(e=ab())===s&&(e=ib()),e!==s&&(Ei=t,e=Li(e)),(t=e)===s&&(t=function(){var t,e,n,o,u,a;t=mi,(e=lb())===s&&(e=fb());if(e!==s)if(Hb()!==s)if((n=kb())!==s)if(Hb()!==s){if(o=[],fs.test(r.charAt(mi))?(u=r.charAt(mi),mi++):(u=s,0===Si&&Ri(ps)),u!==s)for(;u!==s;)o.push(u),fs.test(r.charAt(mi))?(u=r.charAt(mi),mi++):(u=s,0===Si&&Ri(ps));else o=s;o!==s&&(u=Hb())!==s&&Ub()!==s&&Hb()!==s?((a=fv())===s&&(a=null),a!==s?(Ei=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),parentheses:!0,suffix:e}}(e,o,a),t=e):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;t===s&&(t=mi,(e=lb())===s&&(e=fb()),e!==s&&Hb()!==s?((n=fv())===s&&(n=null),n!==s?(Ei=t,e=function(r,t){return{dataType:r,suffix:t}}(e,n),t=e):(mi=t,t=s)):(mi=t,t=s));return t}()));return t}())===s&&(t=function(){var t,e;t=mi,(e=function(){var t,e,n,o;return t=mi,"json"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(qu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="JSON"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"jsonb"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(Yu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="JSONB"):(mi=t,t=s)):(mi=t,t=s),t}());e!==s&&(Ei=t,e=Li(e));return t=e}())===s&&(t=function(){var t,e;t=mi,(e=function(){var t,e,n,o;return t=mi,"geometry"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Wu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="GEOMETRY"):(mi=t,t=s)):(mi=t,t=s),t}())!==s&&(Ei=t,e={dataType:e});return t=e}())===s&&(t=function(){var r,t;r=mi,(t=zp())===s&&(t=Zp())===s&&(t=Jp())===s&&(t=rb());t!==s&&Mb()!==s&&Hb()!==s&&Db()!==s?(Ei=r,r=t={dataType:t+"[]"}):(mi=r,r=s);r===s&&(r=mi,(t=zp())===s&&(t=Zp())===s&&(t=Jp())===s&&(t=rb()),t!==s&&(Ei=r,t=function(r){return{dataType:r}}(t)),r=t);return r}())===s&&(t=function(){var t,e;t=mi,(e=function(){var t,e,n,o;return t=mi,"uuid"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(fa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="UUID"):(mi=t,t=s)):(mi=t,t=s),t}())!==s&&(Ei=t,e={dataType:e});return t=e}())===s&&(t=function(){var t,e;t=mi,(e=function(){var t,e,n,o;return t=mi,"bool"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Ou)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="BOOL"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"boolean"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ku)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="BOOLEAN"):(mi=t,t=s)):(mi=t,t=s),t}());e!==s&&(Ei=t,e=bi(e));return t=e}())===s&&(t=function(){var r,t,e;r=mi,(t=eb())!==s&&Hb()!==s&&(e=Tl())!==s?(Ei=r,n=t,(o=e).parentheses=!0,r=t={dataType:n,expr:o}):(mi=r,r=s);var n,o;return r}())===s&&(t=function(){var r,t;r=mi,(t=Qp())===s&&(t=bb());t!==s&&(Ei=r,t=Li(t));return r=t}())===s&&(t=function(){var t,e;t=mi,"bytea"===r.substr(mi,5).toLowerCase()?(e=r.substr(mi,5),mi+=5):(e=s,0===Si&&Ri(vi));e!==s&&(Ei=t,e={dataType:"BYTEA"});return t=e}())===s&&(t=function(){var t,e;t=mi,(e=function(){var t,e,n,o;return t=mi,"oid"===r.substr(mi,3).toLowerCase()?(e=r.substr(mi,3),mi+=3):(e=s,0===Si&&Ri(pa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="OID"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regclass"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(ba)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGCLASS"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regcollation"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(va)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGCOLLATION"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regconfig"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(ya)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGCONFIG"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regdictionary"===r.substr(mi,13).toLowerCase()?(e=r.substr(mi,13),mi+=13):(e=s,0===Si&&Ri(da)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGDICTIONARY"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regnamespace"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(wa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGNAMESPACE"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regoper"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(ha)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGOPER"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regoperator"===r.substr(mi,11).toLowerCase()?(e=r.substr(mi,11),mi+=11):(e=s,0===Si&&Ri(La)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGOPERATOR"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regproc"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ca)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGPROC"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regprocedure"===r.substr(mi,12).toLowerCase()?(e=r.substr(mi,12),mi+=12):(e=s,0===Si&&Ri(ma)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGPROCEDURE"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regrole"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Ea)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGROLE"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=mi,"regtype"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(Aa)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="REGTYPE"):(mi=t,t=s)):(mi=t,t=s),t}());e!==s&&(Ei=t,e=bi(e));return t=e}())===s&&(t=function(){var t,e;t=mi,"record"===r.substr(mi,6).toLowerCase()?(e=r.substr(mi,6),mi+=6):(e=s,0===Si&&Ri(Ci));e!==s&&(Ei=t,e={dataType:"RECORD"});return t=e}()),t}function av(){var t,e;return t=mi,function(){var t,e,n,o;return t=mi,"character"===r.substr(mi,9).toLowerCase()?(e=r.substr(mi,9),mi+=9):(e=s,0===Si&&Ri(ht)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="CHARACTER"):(mi=t,t=s)):(mi=t,t=s),t}()!==s&&Hb()!==s?("varying"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(yi)),e===s&&(e=null),e!==s?(Ei=t,t="CHARACTER VARYING"):(mi=t,t=s)):(mi=t,t=s),t}function iv(){var t,e,n,o;if(t=mi,(e=Fp())===s&&(e=Hp())===s&&(e=av()),e!==s)if(Hb()!==s)if(kb()!==s)if(Hb()!==s){if(n=[],fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps)),o!==s)for(;o!==s;)n.push(o),fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps));else n=s;n!==s&&(o=Hb())!==s&&Ub()!==s?(Ei=t,t=e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0}):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;return t===s&&(t=mi,(e=Fp())===s&&(e=av())===s&&(e=Hp()),e!==s&&(Ei=t,e=function(r){return{dataType:r}}(e)),t=e),t}function cv(){var t,e,n;return t=mi,(e=Yp())===s&&(e=null),e!==s&&Hb()!==s?((n=function(){var t,e,n,o;return t=mi,"zerofill"===r.substr(mi,8).toLowerCase()?(e=r.substr(mi,8),mi+=8):(e=s,0===Si&&Ri(Hu)),e!==s?(n=mi,Si++,o=lf(),Si--,o===s?n=void 0:(mi=n,n=s),n!==s?(Ei=t,t=e="ZEROFILL"):(mi=t,t=s)):(mi=t,t=s),t}())===s&&(n=null),n!==s?(Ei=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(mi=t,t=s)):(mi=t,t=s),t}function lv(){var t,e,n,o,u,a,i,c,l,f,p,b,v,y,d,w;if(t=mi,(e=Bp())===s&&(e=qp())===s&&(e=Wp())===s&&(e=Vp())===s&&(e=Xp())===s&&(e=Kp())===s&&(e=tb())===s&&(e=nb())===s&&(e=mi,(n=ob())!==s&&(o=Hb())!==s?("precision"===r.substr(mi,9).toLowerCase()?(u=r.substr(mi,9),mi+=9):(u=s,0===Si&&Ri(di)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=ob())===s&&(e=Qp())===s&&(e=sb())===s&&(e=ub())),e!==s)if((n=Hb())!==s)if((o=kb())!==s)if((u=Hb())!==s){if(a=[],fs.test(r.charAt(mi))?(i=r.charAt(mi),mi++):(i=s,0===Si&&Ri(ps)),i!==s)for(;i!==s;)a.push(i),fs.test(r.charAt(mi))?(i=r.charAt(mi),mi++):(i=s,0===Si&&Ri(ps));else a=s;if(a!==s)if((i=Hb())!==s){if(c=mi,(l=Nb())!==s)if((f=Hb())!==s){if(p=[],fs.test(r.charAt(mi))?(b=r.charAt(mi),mi++):(b=s,0===Si&&Ri(ps)),b!==s)for(;b!==s;)p.push(b),fs.test(r.charAt(mi))?(b=r.charAt(mi),mi++):(b=s,0===Si&&Ri(ps));else p=s;p!==s?c=l=[l,f,p]:(mi=c,c=s)}else mi=c,c=s;else mi=c,c=s;c===s&&(c=null),c!==s&&(l=Hb())!==s&&(f=Ub())!==s&&(p=Hb())!==s?((b=cv())===s&&(b=null),b!==s?(Ei=t,v=e,y=a,d=c,w=b,t=e={dataType:Array.isArray(v)?`${v[0].toUpperCase()} ${v[2].toUpperCase()}`:v,length:parseInt(y.join(""),10),scale:d&&parseInt(d[2].join(""),10),parentheses:!0,suffix:w}):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;else mi=t,t=s}else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;else mi=t,t=s;if(t===s){if(t=mi,(e=Bp())===s&&(e=qp())===s&&(e=Wp())===s&&(e=Vp())===s&&(e=Xp())===s&&(e=Kp())===s&&(e=tb())===s&&(e=nb())===s&&(e=mi,(n=ob())!==s&&(o=Hb())!==s?("precision"===r.substr(mi,9).toLowerCase()?(u=r.substr(mi,9),mi+=9):(u=s,0===Si&&Ri(di)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=ob())===s&&(e=Qp())===s&&(e=sb())===s&&(e=ub())),e!==s){if(n=[],fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps)),o!==s)for(;o!==s;)n.push(o),fs.test(r.charAt(mi))?(o=r.charAt(mi),mi++):(o=s,0===Si&&Ri(ps));else n=s;n!==s&&(o=Hb())!==s?((u=cv())===s&&(u=null),u!==s?(Ei=t,t=e=function(r,t,e){return{dataType:Array.isArray(r)?`${r[0].toUpperCase()} ${r[2].toUpperCase()}`:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,u)):(mi=t,t=s)):(mi=t,t=s)}else mi=t,t=s;t===s&&(t=mi,(e=Bp())===s&&(e=qp())===s&&(e=Wp())===s&&(e=Vp())===s&&(e=Xp())===s&&(e=Kp())===s&&(e=tb())===s&&(e=nb())===s&&(e=mi,(n=ob())!==s&&(o=Hb())!==s?("precision"===r.substr(mi,9).toLowerCase()?(u=r.substr(mi,9),mi+=9):(u=s,0===Si&&Ri(di)),u!==s?e=n=[n,o,u]:(mi=e,e=s)):(mi=e,e=s),e===s&&(e=ob())===s&&(e=Qp())===s&&(e=sb())===s&&(e=ub())),e!==s&&(n=Hb())!==s?((o=cv())===s&&(o=null),o!==s&&(u=Hb())!==s?(Ei=t,t=e=function(r,t){return{dataType:Array.isArray(r)?`${r[0].toUpperCase()} ${r[2].toUpperCase()}`:r,suffix:t}}(e,o)):(mi=t,t=s)):(mi=t,t=s))}return t}function fv(){var t,e,n;return t=mi,"without"===r.substr(mi,7).toLowerCase()?(e=r.substr(mi,7),mi+=7):(e=s,0===Si&&Ri(wi)),e===s&&("with"===r.substr(mi,4).toLowerCase()?(e=r.substr(mi,4),mi+=4):(e=s,0===Si&&Ri(Be))),e!==s&&Hb()!==s&&lb()!==s&&Hb()!==s?("zone"===r.substr(mi,4).toLowerCase()?(n=r.substr(mi,4),mi+=4):(n=s,0===Si&&Ri(hi)),n!==s?(Ei=t,t=e=[e.toUpperCase(),"TIME","ZONE"]):(mi=t,t=s)):(mi=t,t=s),t}const pv={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function bv(){return t.includeLocations?{loc:Ii(Ei,mi)}:{}}function vv(r,t){return{type:"unary_expr",operator:r,expr:t}}function yv(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function dv(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function wv(r,t,e=3){const n=Array.isArray(r)?r:[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function hv(r,t){let e=r;for(let r=0;r<t.length;r++)e=yv(t[r][1],e,t[r][3]);return e}function Lv(r){const t=Tv[r];return t||(r||null)}function Cv(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Lv(r[1])),t.add(r.join("::"))}return Array.from(t)}function mv(r){return"string"==typeof r?{type:"same",value:r}:r}let Ev=[];const Av=new Set,gv=new Set,Tv={};if((e=a())!==s&&mi===r.length)return e;throw e!==s&&mi<r.length&&Ri({type:"end"}),Ni(Ti,gi<r.length?r.charAt(gi):null,gi<r.length?Ii(gi,gi+1):Ii(gi,gi))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return j})),e.d(n,"columnOrderListToSQL",(function(){return I})),e.d(n,"commonKeywordArgsToSQL",(function(){return x})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return C})),e.d(n,"commentToSQL",(function(){return T})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return s})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return L})),e.d(n,"columnIdentifierToSql",(function(){return y})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return d})),e.d(n,"onPartitionsToSQL",(function(){return E})),e.d(n,"replaceParams",(function(){return m})),e.d(n,"returningToSQL",(function(){return _})),e.d(n,"hasVal",(function(){return h})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return w})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return S}));var s={database:"noql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return u}function b(r){u=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function d(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function w(r){if(r)return r.toUpperCase()}function h(r){return r}function L(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,s=r.suffix,u=r.value,a="object"===o(r)?u:r;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'");break;case"unicode_string":a="U&'".concat(u,"'")}var i=[];return t&&i.push(w(t)),i.push(a),s&&("string"==typeof s&&i.push(s),"object"===o(s)&&(s.collate?i.push(it(s.collate)):i.push(L(s)))),a=i.join(" "),n?"(".concat(a,")"):a}}function C(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():L(n)].filter(h)}function m(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var s=t[n];if("object"!==o(s)||"param"!==s.type)return r(s,e);if(void 0===e[s.value])throw new Error("no value for parameter :".concat(s.value," found"));return t[n]=c(e[s.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function E(r){var t=r.type,e=r.partitions;return[w(t),"(".concat(e.map((function(r){if("range"!==r.type)return L(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(L(t)," ").concat(w(n)," ").concat(L(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,s=r.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),s&&s.length&&(u+=" ".concat(s.join(" "))),"".concat(t).concat(u)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=w(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var s=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(h).join(" ")})).join(", ");return n?"".concat(o,"<").concat(s,">"):"".concat(o," ").concat(s)}}function T(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(L(o)),t.join(" ")}}function S(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[w(t)];if(e){var o=e.keyword,s=e.columns;n.push(w(o),s.map(yt).join(", "))}return n.join(" ")})).join(" OR ")}function _(r){return r?["RETURNING",r.columns.map(Et).filter(h).join(", ")].join(" "):""}function x(r){return r?[w(r.keyword),w(r.args)]:[]}function j(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,s=r.parentheses,u=w(e);return s&&(u+="(".concat(L(n),", ").concat(L(o),")")),u}}function I(r){if(r)return r.map(Lt).filter(h).join(", ")}function R(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function O(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),w(e)]}function k(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),s=[];switch(s.push(o),o){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(L(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,R(O(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(T(r));break;case"DATA_COMPRESSION":s.push(n,w(e.value),E(e.on));break;default:s.push(n,L(e))}return s.filter(h).join(" ")}}function U(r){return r?r.map(k):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,s=r.definition,u=r.on,a=r.with,i=[];if(i.push.apply(i,R(O(e))),s&&s.length){var c="CHECK"===w(t)?"(".concat(st(s[0]),")"):"(".concat(s.map((function(r){return st(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),u&&i.push("ON [".concat(u,"]")),i}function D(r){var t=r.operator||r.op,e=st(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},s=[Array.isArray(r.left)?r.left.map(st).join(", "):st(r.left),t,e,w(o.type),st(o.value)].filter(h).join(" ");return[r.parentheses?"(".concat(s,")"):s].join(" ")}function P(r){return function(r){if(Array.isArray(r))return G(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function $(r){return r?[r.prefix.map(L).join(" "),st(r.value),r.suffix.map(L).join(" ")]:[]}function F(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P($(n)),P($(e))).filter(h).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",st(e[0])):i("LIMIT",e.map(st).join("".concat("offset"===t?" ":"").concat(w(t)," ")))}(r):"";var t,e,n}function H(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(yt).join(", "),")"):"";return"".concat("default"===t.type?d(t.value):L(t)).concat(o," AS (").concat(st(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function B(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=w(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?d(e):st(e))}return n.filter(h).join(" ")}}function q(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,s=r.for,u=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,y=r.into,d=void 0===y?{}:y,C=r.isolation,m=r.limit,E=r.options,A=r.orderby,g=r.parentheses_symbol,T=r.qualify,S=r.top,_=r.window,x=r.with,j=r.where,I=[H(x),"SELECT",w(t)];Array.isArray(E)&&I.push(E.join(" ")),I.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[w(t)];return e&&n.push("(".concat(e.map(st).join(", "),")")),n.filter(h).join(" ")}}(o),v(S),gt(e,u));var R=d.position,N="";R&&(N=a("INTO",B,d)),"column"===R&&I.push(N),I.push(a("FROM",cr,u)),"from"===R&&I.push(N);var O=l||{},k=O.keyword,U=O.expr;I.push(a(k,st,U)),I.push(a("WHERE",st,j)),p&&(I.push(i("GROUP BY",ut(p.columns).join(", "))),I.push(ut(p.modifiers).join(", "))),I.push(a("HAVING",st,b)),I.push(a("QUALIFY",st,T)),I.push(a("WINDOW",st,_)),I.push(at(A,"order by")),I.push(it(n)),I.push(F(m)),C&&I.push(a(C.keyword,L,C.expr)),I.push(w(f)),"end"===R&&I.push(N),I.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[w(r.type),w(e)];return t?"".concat(n.join(" "),"(").concat(st(t),")"):n.join(" ")}}(s));var M=I.filter(h).join(" ");return g?"(".concat(M,")"):M}function Y(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return W(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?W(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function W(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=Y(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s={},u=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(s[a]=o[a]);var i=[yt(s)],c="";u&&(c=st(u),i.push("=",c)),e.push(i.filter(h).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return q(r);var t=r.map(st);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(d).join(", "));else{var e=r.value;t.push(e.map(st).join(", "))}return t.push(")"),t.filter(h).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(yt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[w(e)];switch(n){case"origin":o.push(L(t));break;case"update":o.push("UPDATE",a("SET",V,t.set),a("WHERE",st,t.where))}return o.filter(h).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(h).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,s=r.columns,u=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},y=v.keyword,d=v.set,C=[w(e),w(o),cr(t),Q(f)];return Array.isArray(s)&&C.push("(".concat(s.map(L).join(", "),")")),C.push(a(Array.isArray(i)?"VALUES":"",X,i)),C.push(a("ON CONFLICT",Z,u)),C.push(a("SET",V,b)),C.push(a("WHERE",st,c)),C.push(a(y,V,d)),C.push(_(p)),C.filter(h).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",st(t),w(e),st(n)].filter(h).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(w(t),"(").concat(n&&st(n)||"",")"),a("AS","string"==typeof e?d:st,e),a(w(o&&o.keyword),d,o&&o.as)].filter(h).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,s=r.type,u=[st(n),"FOR",yt(e),D(o)],a=["".concat(w(s),"(").concat(u.join(" "),")")];return t&&a.push("AS",d(t)),a.join(" ")}(r);default:return""}}function sr(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,s=r.parentheses,u=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(w(t),"(".concat(d(n)),"(".concat(o.map(st).filter(h).join(", "),"))"));break;case"spatial_window_max_cells":a.push(w(t),"=",st(e));break;case"index":a.push(w(u),w(t),s?"(".concat(e.map(d).join(", "),")"):"= ".concat(d(e)));break;default:a.push(st(e))}return a.filter(h).join(" ")}}function ur(r,t){var e=r.name,n=r.symbol;return[w(e),n,t].filter(h).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",st(r.of));break;case"from_to":t.push("FROM",st(r.from),"TO",st(r.to));break;case"between_and":t.push("BETWEEN",st(r.between),"AND",st(r.and));break;case"contained":t.push("CONTAINED","IN",st(r.in))}return t.filter(h).join(" ")}function ir(r){if("UNNEST"===w(r.type))return nr(r);var t,e,n,o,s=r.table,u=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,y=r.tablesample,m=r.temporal_table,E=r.table_hint,A=d(b),g=d(u),T=d(p),S=s&&d(s);if(c)switch(c.type){case"values":var _=c.parentheses,x=c.values,j=c.prefix,I=[_&&"(","",_&&")"],R=X(x);j&&(R=R.split("(").slice(1).map((function(r){return"".concat(w(j),"(").concat(r)})).join("")),I[1]="VALUES ".concat(R),S=I.filter(h).join("");break;case"tumble":S=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,s=[d(t.expr.db),d(t.expr.schema),d(t.expr.table)].filter(h).join("."),u="DESCRIPTOR(".concat(yt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(ur(t,s)),ur(e,u)],i=ur(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(ur(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(h).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return C(r).join(" ")})).join(", "),S="".concat(w(e),"(").concat(w(n),"(").concat(o,"))");break;default:S=st(c)}var N=[[A,g,T,S=[w(f),S,w(v)].filter(h).join(" ")].filter(h).join(".")];if(y){var O=["TABLESAMPLE",st(y.expr),L(y.repeatable)].filter(h).join(" ");N.push(O)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[w(t),ar(e)].filter(h).join(" ")}}(m),a("AS","string"==typeof i?d:st,i),or(l)),E&&N.push(w(E.keyword),"(".concat(E.expr.map(sr).filter(h).join(", "),")"));var k=N.filter(h).join(" ");return r.parentheses?"(".concat(k,")"):k}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var s=[],u=[],i=!0===e?1:e.length,c=0;c++<i;)s.push("("),u.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return s.join("")+o+u.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],y=v.on,d=v.using,C=v.join,m=[];m.push(C?" ".concat(w(C)):","),m.push(ir(v)),m.push(a("ON",st,y)),d&&m.push("USING (".concat(d.map(L).join(", "),")")),p.push(m.filter(h).join(" "))}return p.filter(h).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var s=L(n);switch(t){case"partition by":case"default collate":s=st(n);break;case"options":s="(".concat(n.map((function(r){return[r.keyword,r.symbol,st(r.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(st).join(", ")}return o.push(s),o.filter(h).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[d(t.db),d(t.table)].filter(h).join(".");return"".concat(w(e)," ").concat(n);case"column":return"COLUMN ".concat(yt(t));default:return"".concat(w(e)," ").concat(L(t))}}function pr(r){var t=r.keyword,e=r.expr;return[w(t),L(e)].filter(h).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",st(e)].filter(h).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var s=st(t),u=st(e);return[s,w(n),u].filter(h).join(" ")}function yr(r){var t,e,n,o,s=r.keyword,u=r.suffix,i="";switch(w(s)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",L,e&&e.right),a("FROM",cr,n),F(o)].filter(h).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===w(t.op)?a("LIKE",L,t.right):a("WHERE",st,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,s="'".concat(e,"'");return n&&(s+="@'".concat(n,"'")),["FOR",s,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(h).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[u]);break;case"VAR":i=ot(r.var),s=""}return["SHOW",w(s),w(u),i].filter(h).join(" ")}var dr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,s=r.type,u=t.expr,a=t.orderby;return[w(s),w(n),[[d(o.schema),d(o.name)].filter(h).join("."),"(".concat(u.map(Zr).join(", ")).concat(a?[" ORDER","BY",a.map(Zr).join(", ")].join(" "):"",")")].filter(h).join(""),zr(e)].filter(h).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,s=r.expr,u=void 0===s?[]:s,a=w(t),i=cr(e),c=u.map(st);return[a,"TABLE",w(n),L(o),i,c.join(", ")].filter(h).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[w(r.type),w(e),d(n),zr(t)].filter(h).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[w(r.type),w(e),[d(n.schema),d(n.name)].filter(h).join("."),zr(t)].filter(h).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[w(r.type),w(n),[[d(o.schema),d(o.name)].filter(h).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(h).join(""),zr(e)].filter(h).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,s=r.view,u=r.with,a=w(t),i=ir(s),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(yt).join(", "),")"));n&&c.push("WITH ".concat(n.map(w).join(", ")));c.push("AS",q(o)),u&&c.push(w(u));return c.filter(h).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[w(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,s=r.schema;return[w(t),w(e),st(n),w(o),d(s)].filter(h).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.options,a=[w(t),w(e),w(n)],i=[d(o.schema),o.name].filter(h).join("."),c="".concat(s.expr.map(Zr).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(Zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(u.map(Qr).join(", "),")")),a.filter(h).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,s=r.as,u=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,y=r.unlogged,d=r.with,C=[w(t),w(f),w(u),w(y),w(e),w(a),cr(n)];if(o){var m=o.type,E=cr(o.table);return C.push(w(m),E),C.filter(h).join(" ")}if(b)return C.concat([Wr(b)]).filter(h).join(" ");i&&C.push("(".concat(i.map(qr).join(", "),")"));if(c){var A=p().database,g=A&&"sqlite"===A.toLowerCase()?", ":" ";C.push(c.map(lr).join(g))}if(d){var T=d.map((function(r){return[L(r.keyword),w(r.symbol),L(r.value)].join(" ")})).join(", ");C.push("WITH (".concat(T,")"))}C.push(w(l),w(s)),v&&C.push(wr(v));return C.filter(h).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,s=r.execute,u=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[w(f),w(l),w(e),w(c),d(t),w(i)],y=S(o);v.push(y,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Fr(x(n)).concat(Fr(x(u)))),b&&v.push(w(b.type),st(b.cond));return v.push(w(s.keyword),$r(s.expr)),v.filter(h).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,s=r.type,u=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,y=[w(s),w(c),st(t),w(n),w(i),ir(l),w(b),f.map((function(r){var t=[w(r.keyword)],e=r.args;return e&&t.push(w(e.keyword),e.columns.map(yt).join(", ")),t.join(" ")})),"ON",ir(u),w(e&&e.keyword),w(e&&e.args),p&&"".concat(w(p.keyword)," ").concat(d(p.trigger)),a("WHEN",st,v),w(o.prefix)];switch(o.type){case"set":y.push(a("SET",V,o.expr));break;case"multiple":y.push(hr(o.expr.ast))}return y.push(w(o.suffix)),y.filter(h).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,s=r.schema,u=r.type,i=r.with,c=r.version;return[w(u),w(o),w(n),L(t),w(i),a("SCHEMA",L,s),a("VERSION",L,c),a("FROM",L,e)].filter(h).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.returns,a=r.options,i=r.last,c=[w(t),w(e),w(n)],l=[L(o.schema),o.name.map(L).join(".")].filter(h).join("."),f=s.map(Zr).filter(h).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),Array.isArray(n)?"(".concat(n.map(Ct).join(", "),")"):Vr(n)].filter(h).join(" ")}(u),a.map(Xr).join(" "),i),c.filter(h).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,s=r.include,u=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,y=r.on_kw,C=r.table,m=r.tablespace,E=r.type,A=r.where,g=r.with,T=r.with_before_where,S=g&&"WITH (".concat(U(g).join(", "),")"),_=s&&"".concat(w(s.keyword)," (").concat(s.columns.map((function(r){return"string"==typeof r?d(r):st(r)})).join(", "),")"),x=l;l&&(x="string"==typeof l?d(l):[d(l.schema),d(l.name)].filter(h).join("."));var j=[w(E),w(i),w(n),w(o),w(t),x,w(y),ir(C)].concat(Fr(O(c)),["(".concat(I(u),")"),_,U(p).join(" "),zr(b),zr(v),a("TABLESPACE",L,m)]);T?j.push(S,a("WHERE",st,A)):j.push(a("WHERE",st,A),S);return j.push(a("ON",st,f),a("FILESTREAM_ON",L,e)),j.filter(h).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,s=r.if_not_exists,u=r.create_definitions,a=[w(t),w(o),w(e),w(s),cr(n)];u&&a.push(u.map(qr).join(" "));return a.filter(h).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,s=r.create_definitions,u=r[e],a=u.db,i=u.schema,c=[L(a),i.map(L).join(".")].filter(h).join("."),l=[w(t),w(n),w(e),w(o),c];s&&l.push(s.map(lr).join(" "));return l.filter(h).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,s=r.keyword,u=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,L=p.db,m=p.schema,E=p.view,A=[d(L),d(m),d(E)].filter(h).join(".");return[w(f),w(a),w(l),w(u),t&&"ALGORITHM = ".concat(w(t)),st(n),c&&"SQL SECURITY ".concat(w(c)),w(s),w(o),A,e&&"(".concat(e.map(y).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return C(r).join(" ")})).join(", "),")")].join(" "),"AS",wr(i),w(b)].filter(h).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,s=r.target,u=r.create_definitions,a=[w(n),w(o),[d(e.schema),d(e.name)].filter(h).join("."),w(t),A(s)];if(u&&u.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Hr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(u);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(st(f));break;case"default":c.push(w(p),st(f.value));break;case"constraint":c.push(Or(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(h).join(" "))}return a.filter(h).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,s=r.resource,u=[w(r.type),w(n),[d(o.schema),d(o.name)].filter(h).join("."),w(t),w(s)];if(e){var a=[];switch(s){case"enum":case"range":a.push(st(e));break;default:a.push("(".concat(e.map(qr).join(", "),")"))}u.push(a.filter(h).join(" "))}return u.filter(h).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,s=r.keyword,u=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[jr(t)];return e&&n.push(w(e.keyword),e.auth_plugin,L(e.value)),n.filter(h).join(" ")})).join(", "),b=[w(f),w(s),w(o),p];n&&b.push(w(n.keyword),n.value.map(jr).join(", "));b.push(a(c&&c.keyword,st,c&&c.value)),l&&b.push(w(l.keyword),l.value.map((function(r){return st(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,st,r.value))}));return b.push(L(u),T(e),L(t)),b.filter(h).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[w(r.type),w(e),fr(n),pr(t)].filter(h).join(" ")},select:q,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),st(n)].filter(h).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning,l=[H(u),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",st,o)),l.push(at(s,"order by")),l.push(F(i)),l.push(_(c)),l.filter(h).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[w(t),ir(e),(n||[]).map(br).filter(h).join(", ")].filter(h).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[w(t)],s=[e];n&&s.push("(".concat(st(n).join(", "),")"));return o.push(s.join("")),o.filter(h).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[w(t),q(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,s=r.stmts;return[e,w(t),n,"IN",hr([o]),"LOOP",hr(s),"END LOOP",e].filter(h).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning;return[H(u),"UPDATE",cr(e),a("SET",V,n),a("FROM",cr,t),a("WHERE",st,o),at(s,"order by"),F(i),_(c)].filter(h).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,s=r.prefix,u=r.go,a=r.semicolons,i=r.suffix,c=[w(r.type),st(t),L(s),"".concat(Er(o.ast||o)).concat(a[0]),w(u)];n&&c.push(n.map((function(r){return[w(r.type),st(r.boolean_expr),"THEN",Er(r.then.ast||r.then),r.semicolon].filter(h).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Er(e.ast||e)).concat(a[1]));return c.push(L(i)),c.filter(h).join(" ")},insert:J,drop:_r,truncate:_r,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[w(t)],s=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,s=r.datatype,u=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(h).join(""),w(n),w(o)];switch(c){case"variable":l.push(dt(s),st(r.collate),w(u)),i&&l.push(w(i.keyword),st(i.value));break;case"cursor":l.push(w(a));break;case"table":l.push(w(a),"(".concat(i.map(qr).join(", "),")"))}return l.filter(h).join(" ")})).join("".concat(n," "));return o.push(s),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=w(t),o=d(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var s,u=Ar(e);try{for(u.s();!(s=u.n()).done;){var a=s.value.map(ir);n.push(a.join(" TO "))}}catch(r){u.e(r)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=st(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=w(t);return"".concat(n," ").concat(d(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=w(t),s=e.map(st).join(", ");return[o,w(n),s].filter(h).join(" ")},lock:xr,unlock:xr,show:yr,grant:Ir,revoke:Ir,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[w(t),st(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,s=[w(t),w(e)];n&&s.push([L(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(h).join(""),n.expr.map((function(r){return st(r)})).join(", "));o&&s.push(w(o.type),w(o.option),o.symbol,o.expr.map((function(r){return st(r)})).join(", "));return s.filter(h).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,s=[L(e),w(n)];return o&&s.push(o.map(L).join(", ")),s.filter(h).join(" ")}};function wr(r){if(!r)return"";for(var t=dr[r.type],e=r,n=e._parentheses,o=e._orderby,s=e._limit,u=[n&&"(",t(r)];r._next;){var a=dr[r._next.type],i=w(r.set_op);u.push(i,a(r._next)),r=r._next}return u.push(n&&")",at(o,"order by"),F(s)),u.filter(h).join(" ")}function hr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],s=wr(o);e===n-1&&"transaction"===o.type&&(s="".concat(s," ;")),t.push(s)}return t.join(" ; ")}var Lr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Cr(r){var t=r&&r.ast?r.ast:r;if(!Lr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function mr(r){return Array.isArray(r)?(r.forEach(Cr),hr(r)):(Cr(r),wr(r))}function Er(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[mr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):mr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Tr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function gr(r){return function(r){if(Array.isArray(r))return Sr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Tr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tr(r,t){if(r){if("string"==typeof r)return Sr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Sr(r,t):void 0}}function Sr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function _r(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,s=r.suffix,u=[w(t),w(e),w(o)];switch(e){case"table":u.push(cr(n));break;case"trigger":u.push([n[0].schema?"".concat(d(n[0].schema),"."):"",d(n[0].trigger)].filter(h).join(""));break;case"database":case"schema":case"procedure":u.push(d(n));break;case"view":u.push(cr(n),r.options&&r.options.map(st).filter(h).join(" "));break;case"index":u.push.apply(u,[yt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(st).filter(h).join(" ")]));break;case"type":u.push(n.map(yt).join(", "),r.options&&r.options.map(st).filter(h).join(" "))}return s&&u.push(s.map(st).filter(h).join(" ")),u.filter(h).join(" ")}function xr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),w(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var s,u=[],a=Ar(n);try{var i=function(){var r=s.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return w(e[r])})).filter(h).join(" "))}u.push(n.join(" "))};for(a.s();!(s=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[u.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(h).join(" ")}function jr(r){var t=r.name,e=r.host,n=[L(t)];return e&&n.push("@",L(e)),n.join("")}function Ir(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,s=r.on,u=r.to_from,a=r.user_or_roles,i=r.with,c=[w(t),L(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[st(t)];return e&&n.push("(".concat(e.map(yt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),s)switch(c.push("ON"),n){case"priv":c.push(L(s.object_type),s.priv_level.map((function(r){return[d(r.prefix),d(r.name)].filter(h).join(".")})).join(", "));break;case"proxy":c.push(jr(s))}return c.push(w(u),a.map(jr).join(", ")),c.push(L(i)),c.filter(h).join(" ")}function Rr(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Or(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,s=r.keyword,u=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(w(s)),l.push(d(t));var b=w(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&d(o)),l.push.apply(l,Rr(M(r))),l.push.apply(l,Rr(wt(u))),l.push(w(n)),l.push(a("FOR",d,i)),l.push(L(c)),l.filter(h).join(" ")}}function kr(r){if(r){var t=r.type;return"rows"===t?[w(t),st(r.expr)].filter(h).join(" "):st(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),kr(o)].filter(h).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,s=r.parentheses,u=w(o);if("WINDOW"===u)return"OVER ".concat(Ur(t));if("ON UPDATE"===u){var a="".concat(w(o)," ").concat(w(n)),i=st(e)||[];return s&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return w(t);for(var e=r.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(L(o[u])),s.push("]");return s.join("")}function Gr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,s=r.as,u=r.offset,a=r.parentheses,i=bt({expr:e,offset:u}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,y=p.dataType,C=p.parentheses,m=p.quoted,E=p.scale,A=p.suffix,g=p.expr,T=g?st(g):"";null!=v&&(T=E?"".concat(v,", ").concat(E):v),C&&(T="(".concat(T,")")),b&&(T="<".concat(T,">")),A&&A.length&&(T+=" ".concat(A.map(L).join(" ")));var S="::",_="",x=[];"as"===o&&(0===l&&(i="".concat(w(n),"(").concat(i)),_=")",S=" ".concat(o.toUpperCase()," ")),0===l&&x.push(i);var j=Pr(p);x.push(S,m,y,m,j,T,_),c.push(x.filter(h).join(""))}s&&c.push(" AS ".concat(d(s)));var I=c.filter(h).join("");return a?"(".concat(I,")"):I}function $r(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,s=r.parentheses,u=r.within_group,a=r.over,i=r.suffix,c=Dr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[w(t),w(e),"(".concat(at(n,"order by"),")")].filter(h).join(" ")}(u),f=st(i),p=[L(n.schema),n.name.map(L).join(".")].filter(h).join(".");if(!t)return[p,l,c].filter(h).join(" ");var b=r.separator||", ";"TRIM"===w(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var y=st(t);if(Array.isArray(b)){for(var d=y[0],C=1,m=y.length;C<m;++C)d=[d,y[C]].join(" ".concat(st(b[C-1])," "));v.push(d)}else v.push(y.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(h).join(" "),[s?"(".concat(v,")"):v,l,c].filter(h).join(" ")}function Fr(r){return function(r){if(Array.isArray(r))return Br(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Hr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(r,t){if(r){if("string"==typeof r)return Br(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Br(r,t):void 0}}function Br(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function qr(r){if(!r)return[];var t,e,n,o,s=r.resource;switch(s){case"column":return Ct(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(w(n)),e.push(o),e.push.apply(e,R(M(t))),e.filter(h).join(" ");case"constraint":return Or(r);case"sequence":return[w(r.prefix),st(r.value)].filter(h).join(" ");default:throw new Error("unknown resource = ".concat(s," type"))}}function Yr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(L(r.from),")"),"TO","(".concat(L(r.to),")"));break;case"in":t.push("IN","(".concat(st(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(L(r.modulus),", REMAINDER ").concat(L(r.remainder),")"))}return t.filter(h).join(" ")}function Wr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,s=[w(t),ir(e),w(n.keyword),Yr(n.expr)];return o&&s.push("TABLESPACE",L(o)),s.filter(h).join(" ")}function Vr(r){return r.dataType?A(r):[d(r.db),d(r.schema),d(r.table)].filter(h).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[w(t),r.symbol,wr(r.declare),w(r.begin),hr(r.expr),w(r.end),r.symbol].filter(h).join(" ");case"set":return[w(t),r.parameter,w(r.value&&r.value.prefix),r.value&&r.value.expr.map(st).join(", ")].filter(h).join(" ");case"return":return[w(t),st(r.expr)].filter(h).join(" ");default:return st(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[w(t),e];switch(w(t)){case"SFUNC":o.push([d(n.schema),n.name].filter(h).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(st(n))}return o.filter(h).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",L(t),"VALUES",w(e.type),"(".concat(L(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,s=r.if_exists,u=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Ct(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=d(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=d(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,w(r[i])].filter(h).join(" ");break;case"constraint":f=d(r[i]),p=[qr(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=d(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[w(t),w(o),w(n),w(s),u&&yt(u),w(a),f&&f.trim(),p.filter(h).join(" ")];return l&&b.push(w(l.keyword),l.expr&&yt(l.expr)),b.filter(h).join(" ")}function Zr(r){var t=r.default&&[w(r.default.keyword),st(r.default.value)].join(" ");return[w(r.mode),r.name,A(r.type),t].filter(h).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(w(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var s=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(h).join(", "):st(n);return e?"[".concat(s,"]"):o?"(".concat(s,")"):s}(r);default:return""}}function tt(r){var t=r.definition,e=[w(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(h).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,s=st(t.expr);s=Array.isArray(s)?s.join(", "):s;var u=r.name,a=Dr(n);t.distinct&&(s=["DISTINCT",s].join(" ")),t.separator&&t.separator.delimiter&&(s=[s,L(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(s=[s,st(t.separator.expr)].join(" ")),t.orderby&&(s=[s,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(s=[s,w(t.separator.keyword),L(t.separator.value)].filter(h).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(st(e.where),")"):"";return["".concat(u,"(").concat(s,")"),i,a,c].filter(h).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,s=t.having,u="".concat(w(e),"(").concat(st(o));return s&&(u="".concat(u," HAVING ").concat(w(s.prefix)," ").concat(st(s.expr))),[u="".concat(u,")"),Dr(n)].filter(h).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,s=r.separator,u=void 0===s?", ":s;return[e,"(",t?st(t).join(u):"",")",o&&" ",o].filter(h).join("")}(r),Dr(t)].filter(h).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(st(n));for(var s=0,u=e.length;s<u;++s)t.push(e[s].type.toUpperCase()),e[s].cond&&(t.push(st(e[s].cond)),t.push("THEN")),t.push(st(e[s].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Gr,collate:it,column_ref:yt,column_definition:Ct,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,s=t.source,u=["".concat(w(e),"(").concat(w(n)),"FROM",w(o),st(s)];return"".concat(u.filter(h).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[w(t),e,st(n)].filter(h).join(" ")}(t[r])})).filter(h).join(", ");return"".concat(w(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,s=r.mode,u=[w(o),"(".concat(n.map((function(r){return yt(r)})).join(", "),")")].join(" "),a=[w(t),["(",st(r.expr),s&&" ".concat(L(s)),")"].filter(h).join("")].join(" ");return[u,a,mt(e)].filter(h).join(" ")},function:$r,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,s=n.map(st).join(", ");return[o?"(".concat(s,")"):s,"->",st(e)].join(" ")},insert:wr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[w(t),e.map((function(r){return st(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,s=[st(e),"VALUE",st(n)];return o&&s.push("ON","NULL",st(o)),s.filter(h).join(" ")},json_visitor:function(r){return[r.symbol,st(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,st(t.expr)].filter(h).join(" ")},show:yr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[L(e.schema),e.name.map(L).join(".")].filter(h).join(".");return["".concat(o,"(").concat(st(n).join(", "),")"),"AS",$r(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,s=r.quoted,u=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return u&&(c+=u),a.push(c),[s,a.join(" "),s].filter(h).join("")}function st(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var s=o[n];t[s]=e[s]}}var u=t.type;return"expr"===u?st(t.expr):nt[u]?nt[u](t):L(t)}}function ut(r){return r?(Array.isArray(r)||(r=[r]),r.map(st)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=w(t);switch(n){case"ORDER BY":e=r.map((function(r){return[st(r.expr),r.type||"ASC",w(r.nulls)].filter(h).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return st(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,s=e.value,u=[w(t)];return s||u.push(o),u.push(Array.isArray(n)?n.map(L).join("."):L(n)),s&&u.push(o),u.push(st(s)),u.filter(h).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return d(r,t);var e=r.expr,n=r.offset,o=r.suffix,s=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),L(r.value),"".concat(r.name?")":""),"]"].filter(h).join("")})).join("");return[st(e),s,o].filter(h).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s=o.brackets?"[".concat(L(o.index),"]"):"".concat(o.notation).concat(L(o.index));o.property&&(s="".concat(s,".").concat(L(o.property))),e.push(s)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function yt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,s=r.db,u=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,y=r.order_by,L=r.subFields,C=void 0===L?[]:L,m="*"===n?"*":bt(n,u),E=[s,f,p].filter(h).map((function(r){return"".concat("string"==typeof r?d(r):st(r))})),A=E[0];if(A){for(var g=1;g<E.length;++g)A="".concat(A).concat(c[g]||".").concat(E[g]);m="".concat(A).concat(c[g]||".").concat(m)}var T=[m=["".concat(m).concat(vt(t))].concat(lt(C)).join("."),it(o),st(l),a("AS",st,e)];T.push("string"==typeof v?w(v):st(v)),T.push(w(y));var S=T.filter(h).join(" ");return b?"(".concat(S,")"):S}function dt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,s=r.expr,u=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(s&&(u+=st(s)),r.array){var a=Pr(r);u+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return u}}function wt(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,s=r.table,u=r.on_action;return t.push(w(n)),t.push(cr(s)),t.push(e&&"(".concat(e.map((function(r){return st(r)})).join(", "),")")),t.push(w(o)),u.map((function(r){return t.push(w(r.type),st(r.value))})),t.filter(h)}function ht(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,s=r.comment,u=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,y=r.unique,d=r.primary_key,m=r.column_format,E=r.reference_definition,A=[w(e&&e.action),w(e&&e.value)].filter(h).join(" ");if(b||t.push(A),f){var g=f.type,S=f.value;t.push(g.toUpperCase(),st(S))}var _=p().database;return u&&t.push(w(u.keyword),L(u.constraint)),t.push(Or(o)),t.push(function(r){if(r)return[w(r.value),"(".concat(st(r.expr),")"),w(r.storage_type)].filter(h).join(" ")}(b)),b&&t.push(A),t.push(j(v),w(d),w(y),T(s)),t.push.apply(t,lt(C(n))),"sqlite"!==_.toLowerCase()&&t.push(st(i)),t.push.apply(t,lt(C(m))),t.push.apply(t,lt(C(c))),t.push.apply(t,lt(wt(E))),t.push(a("USING",st,l)),t.filter(h).join(" ")}function Lt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,s=r.order_by,u="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return u.collate=null,[st(u),st(e),o,w(s),w(n)].filter(h).join(" ")}function Ct(r){var t=[],e=yt(r.column),n=dt(r.definition);return t.push(e),t.push(n),t.push(ht(r)),t.filter(h).join(" ")}function mt(r){return r?"object"===ct(r)?["AS",st(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?d(r):y(r)].join(" "):""}function Et(r,t){var e=r.expr,n=r.type;if("cast"===n)return Gr(r);t&&(e.isDual=t);var o=st(e),s=r.expr_list;if(s){var u=[o],a=s.map((function(r){return Et(r,t)})).join(", ");return u.push([w(n),n&&"(",a,n&&")"].filter(h).join("")),u.filter(h).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,mt(r.as)].filter(h).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return Et(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=ut(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",s=t.join(o);return e?"(".concat(s,")"):s},nt.select=function(r){var t="object"===et(r._next)?wr(r):q(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",s="".concat(t).concat(o).concat(st(n));return e?"(".concat(s,")"):s},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[L(r.key),L(r.value)].join(", ")})).join(", ");return[w(t),"[".concat(e,"]")].join("")};var Tt=e(0);function St(r){return(St="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var _t,xt,jt,It=(_t={},xt="noql",jt=Tt.parse,(xt=function(r){var t=function(r,t){if("object"!=St(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=St(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==St(t)?t:t+""}(xt))in _t?Object.defineProperty(_t,xt,{value:jt,enumerable:!0,configurable:!0,writable:!0}):_t[xt]=jt,_t);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Ot(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ot(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function Ot(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&kt(r.prototype,t),e&&kt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),Er(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),st(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return Et(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=t.database,n=void 0===e?"noql":e;b(t);var o=n.toLowerCase();if(It[o])return It[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||s.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=Nt(i);try{for(f.s();!(u=f.n()).done;){var p,b=u.value,v=!1,y=Nt(t);try{for(y.s();!(p=y.n()).done;){var d=p.value,w=new RegExp("^".concat(d,"$"),"i");if(w.test(b)){v=!0;break}}}catch(r){y.e(r)}finally{y.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=noql.js.map