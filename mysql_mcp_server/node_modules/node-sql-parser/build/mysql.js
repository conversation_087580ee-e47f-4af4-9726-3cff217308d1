!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?s(r.parts[t][0])+"-"+s(r.parts[t][1]):s(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function s(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,s=new Array(r.length);for(t=0;t<r.length;t++)s[t]=(o=r[t],e[o.type](o));if(s.sort(),s.length>0){for(t=1,n=1;t<s.length;t++)s[t-1]!==s[t]&&(s[n]=s[t],n++);s.length=n}switch(s.length){case 1:return s[0];case 2:return s[0]+" or "+s[1];default:return s.slice(0,-1).join(", ")+", or "+s[s.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,s={},u={start:ki},a=ki,i=function(r,t){return Ub(r,t)},c=function(r,t){return{...r,order_by:t&&t.toLowerCase()}},l=function(r,t){return Ub(r,t,1)},f=Si("IF",!0),p="IDENTIFIED",b=Si("IDENTIFIED",!1),v=Si("WITH",!0),d=Si("BY",!0),y=Si("RANDOM",!0),w=Si("PASSWORD",!0),h=Si("AS",!0),L=function(r,t){return Ub(r,t)},C=Si("role",!0),m=Si("NONE",!0),E=Si("SSL",!0),A=Si("X509",!0),T=Si("CIPHER",!0),_=Si("ISSUER",!0),I=Si("SUBJECT",!0),g=function(r,t){return t.prefix=r.toLowerCase(),t},S=Si("REQUIRE",!0),N=Si("MAX_QUERIES_PER_HOUR",!0),O=Si("MAX_UPDATES_PER_HOUR",!0),R=Si("MAX_CONNECTIONS_PER_HOUR",!0),x=Si("MAX_USER_CONNECTIONS",!0),j=Si("EXPIRE",!0),k=Si("DEFAULT",!0),U=Si("NEVER",!0),M=Si("HISTORY",!0),D=Si("REUSE",!1),P=Si("CURRENT",!0),G=Si("OPTIONAL",!0),F=Si("FAILED_LOGIN_ATTEMPTS",!0),H=Si("PASSWORD_LOCK_TIME",!0),Y=Si("UNBOUNDED",!0),$=Si("ACCOUNT",!0),B=Si("LOCK",!0),W=Si("UNLOCK",!0),q=Si("ATTRIBUTE",!0),V=Si("CASCADED",!0),X=Si("LOCAL",!0),K=Si("CHECK",!0),Q=Si("OPTION",!1),Z=Si("ALGORITHM",!0),z=Si("UNDEFINED",!0),J=Si("MERGE",!0),rr=Si("TEMPTABLE",!0),tr=Si("SQL",!0),er=Si("SECURITY",!0),nr=Si("DEFINER",!0),or=Si("INVOKER",!0),sr=function(r,t){return Ub(r,t)},ur=Si("AUTO_INCREMENT",!0),ar=Si("UNIQUE",!0),ir=Si("KEY",!0),cr=Si("PRIMARY",!0),lr=Si("@",!1),fr=function(){return jb("=",{type:"origin",value:"definer"},{type:"function",name:{name:[{type:"default",value:"current_user"}]},args:{type:"expr_list",value:[]}})},pr=Si("BEFORE",!0),br=Si("AFTER",!0),vr=Si("FOR",!0),dr=Si("EACH",!0),yr=Si("ROW",!0),wr=Si("STATEMENT",!0),hr=Si("FOLLOWS",!0),Lr=Si("PRECEDES",!0),Cr=Si("COLUMN_FORMAT",!0),mr=Si("FIXED",!0),Er=Si("DYNAMIC",!0),Ar=Si("STORAGE",!0),Tr=Si("DISK",!0),_r=Si("MEMORY",!0),Ir=Si("GENERATED",!0),gr=Si("ALWAYS",!0),Sr=Si("STORED",!0),Nr=Si("VIRTUAL",!0),Or=Si("if",!0),Rr=Si("exists",!0),xr=Si("first",!0),jr=Si("after",!0),kr=Si("LESS",!0),Ur=Si("THAN",!0),Mr=Si("DROP",!0),Dr=Si("TRUNCATE",!0),Pr=Si("DISCARD",!0),Gr=Si("IMPORT",!0),Fr=Si("COALESCE",!0),Hr=Si("ANALYZE",!0),Yr=Si("TABLESPACE",!0),$r=Si("INSTANT",!0),Br=Si("INPLACE",!0),Wr=Si("COPY",!0),qr=Si("SHARED",!0),Vr=Si("EXCLUSIVE",!0),Xr=Si("CHANGE",!0),Kr=Si("FOREIGN",!0),Qr=Si("CONSTRAINT",!0),Zr=Si("NOCHECK",!0),zr=Si("NOT",!0),Jr=Si("REPLICATION",!0),rt=Si("FOREIGN KEY",!0),tt=Si("ENFORCED",!0),et=Si("MATCH FULL",!0),nt=Si("MATCH PARTIAL",!0),ot=Si("MATCH SIMPLE",!0),st=Si("RESTRICT",!0),ut=Si("CASCADE",!0),at=Si("SET NULL",!0),it=Si("NO ACTION",!0),ct=Si("SET DEFAULT",!0),lt=Si("CHARACTER",!0),ft=Si("SET",!0),pt=Si("CHARSET",!0),bt=Si("COLLATE",!0),vt=Si("AVG_ROW_LENGTH",!0),dt=Si("KEY_BLOCK_SIZE",!0),yt=Si("MAX_ROWS",!0),wt=Si("MIN_ROWS",!0),ht=Si("STATS_SAMPLE_PAGES",!0),Lt=Si("CHECKSUM",!1),Ct=Si("DELAY_KEY_WRITE",!1),mt=/^[01]/,Et=Ni(["0","1"],!1,!1),At=Si("CONNECTION",!0),Tt=Si("ENGINE_ATTRIBUTE",!0),_t=Si("SECONDARY_ENGINE_ATTRIBUTE",!0),It=Si("DATA",!0),gt=Si("INDEX",!0),St=Si("DIRECTORY",!0),Nt=Si("COMPRESSION",!0),Ot=Si("'",!1),Rt=Si("ZLIB",!0),xt=Si("LZ4",!0),jt=Si("ENGINE",!0),kt=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}},Ut=Si("ROW_FORMAT",!0),Mt=Si("COMPRESSED",!0),Dt=Si("REDUNDANT",!0),Pt=Si("COMPACT",!0),Gt=Si("READ",!0),Ft=Si("LOW_PRIORITY",!0),Ht=Si("WRITE",!0),Yt=function(r,t){return Ub(r,t)},$t=Si("BINARY",!0),Bt=Si("MASTER",!0),Wt=Si("LOGS",!0),qt=Si("TRIGGERS",!0),Vt=Si("STATUS",!0),Xt=Si("PROCESSLIST",!0),Kt=Si("PROCEDURE",!0),Qt=Si("FUNCTION",!0),Zt=Si("BINLOG",!0),zt=Si("EVENTS",!0),Jt=Si("COLLATION",!0),re=Si("DATABASES",!0),te=Si("COLUMNS",!0),ee=Si("INDEXES",!0),ne=Si("EVENT",!0),oe=Si("GRANTS",!0),se=Si("VIEW",!0),ue=Si("GRANT",!0),ae=Si("OPTION",!0),ie=function(r){return{type:"origin",value:Array.isArray(r)?r[0]:r}},ce=Si("ROUTINE",!0),le=Si("EXECUTE",!0),fe=Si("ADMIN",!0),pe=Si("GRANT",!1),be=Si("PROXY",!1),ve=Si("(",!1),de=Si(")",!1),ye=/^[0-9]/,we=Ni([["0","9"]],!1,!1),he=Si("IN",!0),Le=Si("SHARE",!0),Ce=Si("MODE",!0),me=Si("WAIT",!0),Ee=Si("NOWAIT",!0),Ae=Si("SKIP",!0),Te=Si("LOCKED",!0),_e=Si("NATURAL",!0),Ie=Si("LANGUAGE",!0),ge=Si("QUERY",!0),Se=Si("EXPANSION",!0),Ne=Si("BOOLEAN",!0),Oe=Si("MATCH",!0),Re=Si("AGAINST",!1),xe=Si("OUTFILE",!0),je=Si("DUMPFILE",!0),ke=Si("BTREE",!0),Ue=Si("HASH",!0),Me=Si("PARSER",!0),De=Si("VISIBLE",!0),Pe=Si("INVISIBLE",!0),Ge=Si("LATERAL",!0),Fe=/^[_0-9]/,He=Ni(["_",["0","9"]],!1,!1),Ye=Si("ROLLUP",!0),$e=Si("?",!1),Be=Si("=",!1),We=Si("DUPLICATE",!0),qe=function(r,t){return Mb(r,t)},Ve=function(r){return r[0]+" "+r[2]},Xe=Si(">=",!1),Ke=Si(">",!1),Qe=Si("<=",!1),Ze=Si("<>",!1),ze=Si("<",!1),Je=Si("!=",!1),rn=Si("ESCAPE",!0),tn=Si("+",!1),en=Si("-",!1),nn=Si("*",!1),on=Si("/",!1),sn=Si("%",!1),un=Si("||",!1),an=Si("div",!0),cn=Si("mod",!0),ln=Si("&",!1),fn=Si(">>",!1),pn=Si("<<",!1),bn=Si("^",!1),vn=Si("|",!1),dn=Si("!",!1),yn=Si("~",!1),wn=Si("?|",!1),hn=Si("?&",!1),Ln=Si("#-",!1),Cn=Si("#>>",!1),mn=Si("#>",!1),En=Si("@>",!1),An=Si("<@",!1),Tn=function(r){return!0===Nb[r.toUpperCase()]},_n=Si('"',!1),In=/^[^"]/,gn=Ni(['"'],!0,!1),Sn=/^[^']/,Nn=Ni(["'"],!0,!1),On=Si("`",!1),Rn=/^[^`\\]/,xn=Ni(["`","\\"],!0,!1),jn=function(r,t){return r+t.join("")},kn=/^[A-Za-z_\u4E00-\u9FA5]/,Un=Ni([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),Mn=/^[A-Za-z0-9_$\x80-\uFFFF]/,Dn=Ni([["A","Z"],["a","z"],["0","9"],"_","$",["","￿"]],!1,!1),Pn=/^[A-Za-z0-9_:]/,Gn=Ni([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),Fn=Si(":",!1),Hn=Si("NOW",!0),Yn=Si("OVER",!0),$n=Si("WINDOW",!0),Bn=Si("FOLLOWING",!0),Wn=Si("PRECEDING",!0),qn=Si("SEPARATOR",!0),Vn=Si("YEAR_MONTH",!0),Xn=Si("DAY_HOUR",!0),Kn=Si("DAY_MINUTE",!0),Qn=Si("DAY_SECOND",!0),Zn=Si("DAY_MICROSECOND",!0),zn=Si("HOUR_MINUTE",!0),Jn=Si("HOUR_SECOND",!0),ro=Si("HOUR_MICROSECOND",!0),to=Si("MINUTE_SECOND",!0),eo=Si("MINUTE_MICROSECOND",!0),no=Si("SECOND_MICROSECOND",!0),oo=Si("TIMEZONE_HOUR",!0),so=Si("TIMEZONE_MINUTE",!0),uo=Si("CENTURY",!0),ao=Si("DAY",!0),io=Si("DATE",!0),co=Si("DECADE",!0),lo=Si("DOW",!0),fo=Si("DOY",!0),po=Si("EPOCH",!0),bo=Si("HOUR",!0),vo=Si("ISODOW",!0),yo=Si("ISOWEEK",!0),wo=Si("ISOYEAR",!0),ho=Si("MICROSECONDS",!0),Lo=Si("MILLENNIUM",!0),Co=Si("MILLISECONDS",!0),mo=Si("MINUTE",!0),Eo=Si("MONTH",!0),Ao=Si("QUARTER",!0),To=Si("SECOND",!0),_o=Si("TIME",!0),Io=Si("TIMEZONE",!0),go=Si("WEEK",!0),So=Si("YEAR",!0),No=Si("DATE_TRUNC",!0),Oo=Si("BOTH",!0),Ro=Si("LEADING",!0),xo=Si("TRAILING",!0),jo=Si("trim",!0),ko=Si("convert",!0),Uo=Si("binary",!0),Mo=Si("_binary",!0),Do=Si("_latin1",!0),Po=Si("X",!0),Go=/^[0-9A-Fa-f]/,Fo=Ni([["0","9"],["A","F"],["a","f"]],!1,!1),Ho=Si("b",!0),Yo=Si("0x",!0),$o=Si("N",!0),Bo=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},Wo=/^[^"\\\0-\x1F\x7F]/,qo=Ni(['"',"\\",["\0",""],""],!0,!1),Vo=/^[\n]/,Xo=Ni(["\n"],!1,!1),Ko=/^[^'\\]/,Qo=Ni(["'","\\"],!0,!1),Zo=Si("\\'",!1),zo=Si('\\"',!1),Jo=Si("\\\\",!1),rs=Si("\\/",!1),ts=Si("\\b",!1),es=Si("\\f",!1),ns=Si("\\n",!1),os=Si("\\r",!1),ss=Si("\\t",!1),us=Si("\\u",!1),as=Si("\\",!1),is=Si("''",!1),cs=Si('""',!1),ls=Si("``",!1),fs=/^[\n\r]/,ps=Ni(["\n","\r"],!1,!1),bs=Si(".",!1),vs=/^[0-9a-fA-F]/,ds=Ni([["0","9"],["a","f"],["A","F"]],!1,!1),ys=/^[eE]/,ws=Ni(["e","E"],!1,!1),hs=/^[+\-]/,Ls=Ni(["+","-"],!1,!1),Cs=Si("NULL",!0),ms=Si("NOT NULL",!0),Es=Si("TRUE",!0),As=Si("TO",!0),Ts=Si("FALSE",!0),_s=Si("SHOW",!0),Is=Si("USE",!0),gs=Si("ALTER",!0),Ss=Si("SELECT",!0),Ns=Si("UPDATE",!0),Os=Si("CREATE",!0),Rs=Si("TEMPORARY",!0),xs=Si("DELETE",!0),js=Si("INSERT",!0),ks=Si("RECURSIVE",!0),Us=Si("REPLACE",!0),Ms=Si("RENAME",!0),Ds=Si("IGNORE",!0),Ps=Si("EXPLAIN",!0),Gs=Si("PARTITION",!0),Fs=Si("INTO",!0),Hs=Si("FROM",!0),Ys=Si("TABLE",!0),$s=Si("TRIGGER",!0),Bs=Si("TABLES",!0),Ws=Si("DATABASE",!0),qs=Si("SCHEMA",!0),Vs=Si("ON",!0),Xs=Si("LEFT",!0),Ks=Si("RIGHT",!0),Qs=Si("FULL",!0),Zs=Si("INNER",!0),zs=Si("CROSS",!0),Js=Si("JOIN",!0),ru=Si("OUTER",!0),tu=Si("UNION",!0),eu=Si("MINUS",!0),nu=Si("INTERSECT",!0),ou=Si("VALUES",!0),su=Si("USING",!0),uu=Si("WHERE",!0),au=Si("GO",!0),iu=Si("GROUP",!0),cu=Si("ORDER",!0),lu=Si("HAVING",!0),fu=Si("LIMIT",!0),pu=Si("OFFSET",!0),bu=Si("ASC",!0),vu=Si("DESC",!0),du=Si("DESCRIBE",!0),yu=Si("ALL",!0),wu=Si("DISTINCT",!0),hu=Si("BETWEEN",!0),Lu=Si("IS",!0),Cu=Si("LIKE",!0),mu=Si("RLIKE",!0),Eu=Si("REGEXP",!0),Au=Si("EXISTS",!0),Tu=Si("AND",!0),_u=Si("OR",!0),Iu=Si("COUNT",!0),gu=Si("GROUP_CONCAT",!0),Su=Si("MAX",!0),Nu=Si("MIN",!0),Ou=Si("SUM",!0),Ru=Si("AVG",!0),xu=Si("EXTRACT",!0),ju=Si("CALL",!0),ku=Si("CASE",!0),Uu=Si("WHEN",!0),Mu=Si("THEN",!0),Du=Si("ELSE",!0),Pu=Si("END",!0),Gu=Si("CAST",!0),Fu=Si("VARBINARY",!0),Hu=Si("BIT",!0),Yu=Si("CHAR",!0),$u=Si("VARCHAR",!0),Bu=Si("NUMERIC",!0),Wu=Si("DECIMAL",!0),qu=Si("SIGNED",!0),Vu=Si("UNSIGNED",!0),Xu=Si("INT",!0),Ku=Si("ZEROFILL",!0),Qu=Si("INTEGER",!0),Zu=Si("JSON",!0),zu=Si("SMALLINT",!0),Ju=Si("MEDIUMINT",!0),ra=Si("TINYINT",!0),ta=Si("TINYTEXT",!0),ea=Si("TEXT",!0),na=Si("MEDIUMTEXT",!0),oa=Si("LONGTEXT",!0),sa=Si("BIGINT",!0),ua=Si("ENUM",!0),aa=Si("FLOAT",!0),ia=Si("DOUBLE",!0),ca=Si("DATETIME",!0),la=Si("ROWS",!0),fa=Si("TIMESTAMP",!0),pa=Si("USER",!0),ba=Si("CURRENT_DATE",!0),va=(Si("ADDDATE",!0),Si("INTERVAL",!0)),da=Si("MICROSECOND",!0),ya=Si("CURRENT_TIME",!0),wa=Si("CURRENT_TIMESTAMP",!0),ha=Si("CURRENT_USER",!0),La=Si("SESSION_USER",!0),Ca=Si("SYSTEM_USER",!0),ma=Si("GLOBAL",!0),Ea=Si("SESSION",!0),Aa=Si("PERSIST",!0),Ta=Si("PERSIST_ONLY",!0),_a=Si("GEOMETRY",!0),Ia=Si("POINT",!0),ga=Si("LINESTRING",!0),Sa=Si("POLYGON",!0),Na=Si("MULTIPOINT",!0),Oa=Si("MULTILINESTRING",!0),Ra=Si("MULTIPOLYGON",!0),xa=Si("GEOMETRYCOLLECTION",!0),ja=Si("@@",!1),ka=Si("$",!1),Ua=Si("return",!0),Ma=Si(":=",!1),Da=Si("DUAL",!0),Pa=Si("ADD",!0),Ga=Si("COLUMN",!0),Fa=Si("MODIFY",!0),Ha=Si("FULLTEXT",!0),Ya=Si("SPATIAL",!0),$a=Si("COMMENT",!0),Ba=Si("REFERENCES",!0),Wa=Si("SQL_CALC_FOUND_ROWS",!0),qa=Si("SQL_CACHE",!0),Va=Si("SQL_NO_CACHE",!0),Xa=Si("SQL_SMALL_RESULT",!0),Ka=Si("SQL_BIG_RESULT",!0),Qa=Si("SQL_BUFFER_RESULT",!0),Za=Si(",",!1),za=Si("[",!1),Ja=Si("]",!1),ri=Si(";",!1),ti=Si("->",!1),ei=Si("->>",!1),ni=Si("&&",!1),oi=Si("XOR",!0),si=Si("/*",!1),ui=Si("*/",!1),ai=Si("--",!1),ii=Si("#",!1),ci={type:"any"},li=/^[ \t\n\r]/,fi=Ni([" ","\t","\n","\r"],!1,!1),pi=function(r,t,e){return{type:"assign",left:r,symbol:t,right:e}},bi=Si("boolean",!0),vi=Si("blob",!0),di=Si("tinyblob",!0),yi=Si("mediumblob",!0),wi=Si("longblob",!0),hi=function(r,t){return{dataType:r,...t||{}}},Li=Si("ARRAY",!0),Ci=/^[0-6]/,mi=Ni([["0","6"]],!1,!1),Ei=0,Ai=0,Ti=[{line:1,column:1}],_i=0,Ii=[],gi=0;if("startRule"in t){if(!(t.startRule in u))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=u[t.startRule]}function Si(r,t){return{type:"literal",text:r,ignoreCase:t}}function Ni(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Oi(t){var e,n=Ti[t];if(n)return n;for(e=t-1;!Ti[e];)e--;for(n={line:(n=Ti[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Ti[t]=n,n}function Ri(r,t){var e=Oi(r),n=Oi(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function xi(r){Ei<_i||(Ei>_i&&(_i=Ei,Ii=[]),Ii.push(r))}function ji(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function ki(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Ui())!==s)if(sb()!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zf())!==s&&(a=sb())!==s&&(i=Ui())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zf())!==s&&(a=sb())!==s&&(i=Ui())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){if(!t||0===t.length)return r;delete r.tableList,delete r.columnList;let e=r;for(let r=0;r<t.length;r++)delete t[r][3].tableList,delete t[r][3].columnList,e.go_next=t[r][3],e.go="go",e=e.go_next;return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:r}}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;return r}function Ui(){var r,t;return r=Ei,sb()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Di())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=tb())!==s&&(a=sb())!==s&&(i=Di())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=tb())!==s&&(a=sb())!==s&&(i=Di())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:n}}(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=r,r=t):(Ei=r,r=s),r}function Mi(){var t;return(t=function(){var r,t,e,n,o,u,a;r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Ff())!==s&&sb()!==s?((n=Zi())===s&&(n=null),n!==s&&sb()!==s&&(o=Pc())!==s?(Ai=r,i=t,c=e,f=n,(p=o)&&p.forEach(r=>Hb.add(`${i}::${r.db}::${r.table}`)),t={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:i.toLowerCase(),keyword:c.toLowerCase(),prefix:f,name:p}},r=t):(Ei=r,r=s)):(Ei=r,r=s);var i,c,f,p;r===s&&(r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Up())!==s&&sb()!==s?((n=Zi())===s&&(n=null),n!==s&&sb()!==s&&(o=Pc())!==s&&sb()!==s?((u=cc())===s&&(u=null),u!==s?(Ai=r,t=function(r,t,e,n,o){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:n,options:o&&[{type:"origin",value:o}]}}}(t,e,n,o,u),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Yp())!==s&&sb()!==s&&(n=Il())!==s&&sb()!==s&&(o=Wf())!==s&&sb()!==s&&(u=Yc())!==s&&sb()!==s?((a=function(){var r,t,e,n,o,u;r=Ei,(t=tc())===s&&(t=ec());if(t!==s){for(e=[],n=Ei,(o=sb())!==s?((u=tc())===s&&(u=ec()),u!==s?n=o=[o,u]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=tc())===s&&(u=ec()),u!==s?n=o=[o,u]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,t=l(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(a=null),a!==s&&sb()!==s?(Ai=r,t=function(r,t,e,n,o){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,u,a),r=t):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Tf())!==s&&sb()!==s?((e=$f())===s&&(e=Bf()),e!==s&&sb()!==s?((n=Zi())===s&&(n=null),n!==s&&sb()!==s&&(o=Fl())!==s?(Ai=r,t=function(r,t,e,n){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:n}}}(t,e,n,o),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Hf())!==s&&sb()!==s?((n=Zi())===s&&(n=null),n!==s&&sb()!==s&&(o=Fc())!==s?(Ai=r,t=function(r,t,e,n){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:[{schema:n.db,trigger:n.table}]}}}(t,e,n,o),r=t):(Ei=r,r=s)):(Ei=r,r=s)))));return r}())===s&&(t=function(){var t;(t=function(){var r,t,e,n,o,u,a,c,l,f;r=Ei,(t=Sf())!==s&&sb()!==s?((e=Nf())===s&&(e=null),e!==s&&sb()!==s&&Ff()!==s&&sb()!==s?((n=Hi())===s&&(n=null),n!==s&&sb()!==s&&(o=Yc())!==s&&sb()!==s&&(u=function r(){var t,e;(t=function(){var r,t;r=Ei,up()!==s&&sb()!==s&&(t=Pc())!==s?(Ai=r,r={type:"like",table:t}):(Ei=r,r=s);return r}())===s&&(t=Ei,Jp()!==s&&sb()!==s&&(e=r())!==s&&sb()!==s&&rb()!==s?(Ai=t,(n=e).parentheses=!0,t=n):(Ei=t,t=s));var n;return t}())!==s?(Ai=r,p=t,b=e,v=n,y=u,(d=o)&&Hb.add(`create::${d.db}::${d.table}`),t={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:v,table:[d],like:y}},r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);var p,b,v,d,y;r===s&&(r=Ei,(t=Sf())!==s&&sb()!==s?((e=Nf())===s&&(e=null),e!==s&&sb()!==s&&Ff()!==s&&sb()!==s?((n=Hi())===s&&(n=null),n!==s&&sb()!==s&&(o=Yc())!==s&&sb()!==s?((u=function(){var r,t,e,n,o,u,a,i,c;if(r=Ei,(t=Jp())!==s)if(sb()!==s)if((e=qi())!==s){for(n=[],o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=qi())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=qi())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);n!==s&&(o=sb())!==s&&(u=rb())!==s?(Ai=r,t=sr(e,n),r=t):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;return r}())===s&&(u=null),u!==s&&sb()!==s?((a=function(){var r,t,e,n,o,u,a,c;if(r=Ei,(t=pc())!==s){for(e=[],n=Ei,(o=sb())!==s?((u=Zp())===s&&(u=null),u!==s&&(a=sb())!==s&&(c=pc())!==s?n=o=[o,u,a,c]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=Zp())===s&&(u=null),u!==s&&(a=sb())!==s&&(c=pc())!==s?n=o=[o,u,a,c]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,t=i(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(a=null),a!==s&&sb()!==s?((c=kf())===s&&(c=xf()),c===s&&(c=null),c!==s&&sb()!==s?((l=Gf())===s&&(l=null),l!==s&&sb()!==s?((f=Gi())===s&&(f=null),f!==s?(Ai=r,t=function(r,t,e,n,o,s,u,a,i){return n&&Hb.add(`create::${n.db}::${n.table}`),{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:[n],ignore_replace:u&&u[0].toLowerCase(),as:a&&a[0].toLowerCase(),query_expr:i&&i.ast,create_definitions:o,table_options:s}}}(t,e,n,o,u,a,c,l,f),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=Ei,(e=Sf())!==s&&sb()!==s?((n=Ki())===s&&(n=null),n!==s&&sb()!==s&&Hf()!==s&&sb()!==s?((o=Hi())===s&&(o=null),o!==s&&sb()!==s&&(u=Yc())!==s&&sb()!==s&&(a=function(){var t;"before"===r.substr(Ei,6).toLowerCase()?(t=r.substr(Ei,6),Ei+=6):(t=s,0===gi&&xi(pr));t===s&&("after"===r.substr(Ei,5).toLowerCase()?(t=r.substr(Ei,5),Ei+=5):(t=s,0===gi&&xi(br)));return t}())!==s&&sb()!==s&&(i=function(){var r,t;r=Ei,(t=Rf())===s&&(t=gf())===s&&(t=Of());t!==s&&(Ai=r,t={keyword:t[0].toLowerCase()});return r=t}())!==s&&sb()!==s&&Wf()!==s&&sb()!==s&&(c=Yc())!==s&&sb()!==s&&(l=function(){var t,e,n,o;t=Ei,"for"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(vr));e!==s&&sb()!==s?("each"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(dr)),n===s&&(n=null),n!==s&&sb()!==s?("row"===r.substr(Ei,3).toLowerCase()?(o=r.substr(Ei,3),Ei+=3):(o=s,0===gi&&xi(yr)),o===s&&("statement"===r.substr(Ei,9).toLowerCase()?(o=r.substr(Ei,9),Ei+=9):(o=s,0===gi&&xi(wr))),o!==s?(Ai=t,u=e,i=o,e={keyword:(a=n)?`${u.toLowerCase()} ${a.toLowerCase()}`:u.toLowerCase(),args:i.toLowerCase()},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var u,a,i;return t}())!==s&&sb()!==s?((f=function(){var t,e,n;t=Ei,"follows"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(hr));e===s&&("precedes"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Lr)));e!==s&&sb()!==s&&(n=Rl())!==s?(Ai=t,t=e={keyword:e,trigger:n}):(Ei=t,t=s);return t}())===s&&(f=null),f!==s&&sb()!==s&&(p=function(){var r,t;r=Ei,Pf()!==s&&sb()!==s&&(t=Zc())!==s?(Ai=r,r={type:"set",expr:t}):(Ei=r,r=s);return r}())!==s?(Ai=t,b=e,v=n,d=o,y=u,w=a,h=i,L=c,C=l,m=f,E=p,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:b[0].toLowerCase(),definer:v,keyword:"trigger",for_each:C,if_not_exists:d,trigger:y,time:w,events:[h],order:m,table:L,execute:E}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var b,v,d,y,w,h,L,C,m,E;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,c,l,f,p,b;r=Ei,(t=Sf())!==s&&sb()!==s?((e=qp())===s&&(e=Bp())===s&&(e=Wp()),e===s&&(e=null),e!==s&&sb()!==s&&(n=Yp())!==s&&sb()!==s&&(o=Rl())!==s&&sb()!==s?((u=Uc())===s&&(u=null),u!==s&&sb()!==s&&(a=Wf())!==s&&sb()!==s&&(c=Yc())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(l=function(){var r,t,e,n,o,u,a,c;if(r=Ei,(t=Fi())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=Fi())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=Fi())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);e!==s?(Ai=r,t=i(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&sb()!==s&&rb()!==s&&sb()!==s?((f=Mc())===s&&(f=null),f!==s&&sb()!==s?((p=tc())===s&&(p=null),p!==s&&sb()!==s?((b=ec())===s&&(b=null),b!==s&&sb()!==s?(Ai=r,v=t,d=e,y=n,w=o,h=u,L=a,C=c,m=l,E=f,A=p,T=b,t={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:v[0].toLowerCase(),index_type:d&&d.toLowerCase(),keyword:y.toLowerCase(),index:w,on_kw:L[0].toLowerCase(),table:C,index_columns:m,index_using:h,index_options:E,algorithm_option:A,lock_option:T}},r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);var v,d,y,w,h,L,C,m,E,A,T;return r}())===s&&(t=function(){var r,t,e,n,o,u;r=Ei,(t=Sf())!==s&&sb()!==s?((e=$f())===s&&(e=Bf()),e!==s&&sb()!==s?((n=Hi())===s&&(n=null),n!==s&&sb()!==s&&(o=hb())!==s&&sb()!==s?((u=function(){var r,t,e,n,o,u;if(r=Ei,(t=fc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=fc())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=fc())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,t=l(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(u=null),u!==s?(Ai=r,t=function(r,t,e,n,o){const s=t.toLowerCase();return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r[0].toLowerCase(),keyword:s,if_not_exists:e,[s]:{db:n.schema,schema:n.name},create_definitions:o}}}(t,e,n,o,u),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h,L,C,m,E;t=Ei,(e=Sf())!==s&&sb()!==s?(n=Ei,(o=lp())!==s&&(u=sb())!==s&&(a=xf())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=null),n!==s&&(o=sb())!==s?(u=Ei,"algorithm"===r.substr(Ei,9).toLowerCase()?(a=r.substr(Ei,9),Ei+=9):(a=s,0===gi&&xi(Z)),a!==s&&(i=sb())!==s&&(c=Gp())!==s&&(l=sb())!==s?("undefined"===r.substr(Ei,9).toLowerCase()?(f=r.substr(Ei,9),Ei+=9):(f=s,0===gi&&xi(z)),f===s&&("merge"===r.substr(Ei,5).toLowerCase()?(f=r.substr(Ei,5),Ei+=5):(f=s,0===gi&&xi(J)),f===s&&("temptable"===r.substr(Ei,9).toLowerCase()?(f=r.substr(Ei,9),Ei+=9):(f=s,0===gi&&xi(rr)))),f!==s?u=a=[a,i,c,l,f]:(Ei=u,u=s)):(Ei=u,u=s),u===s&&(u=null),u!==s&&(a=sb())!==s?((i=Ki())===s&&(i=null),i!==s&&(c=sb())!==s?(l=Ei,"sql"===r.substr(Ei,3).toLowerCase()?(f=r.substr(Ei,3),Ei+=3):(f=s,0===gi&&xi(tr)),f!==s&&(p=sb())!==s?("security"===r.substr(Ei,8).toLowerCase()?(b=r.substr(Ei,8),Ei+=8):(b=s,0===gi&&xi(er)),b!==s&&(v=sb())!==s?("definer"===r.substr(Ei,7).toLowerCase()?(d=r.substr(Ei,7),Ei+=7):(d=s,0===gi&&xi(nr)),d===s&&("invoker"===r.substr(Ei,7).toLowerCase()?(d=r.substr(Ei,7),Ei+=7):(d=s,0===gi&&xi(or))),d!==s?l=f=[f,p,b,v,d]:(Ei=l,l=s)):(Ei=l,l=s)):(Ei=l,l=s),l===s&&(l=null),l!==s&&(f=sb())!==s&&(p=Up())!==s&&(b=sb())!==s&&(v=Yc())!==s&&(d=sb())!==s?(y=Ei,(w=Jp())!==s&&(h=sb())!==s&&(L=gl())!==s&&(C=sb())!==s&&(m=rb())!==s?y=w=[w,h,L,C,m]:(Ei=y,y=s),y===s&&(y=null),y!==s&&(w=sb())!==s&&(h=Gf())!==s&&(L=sb())!==s&&(C=Ic())!==s&&(m=sb())!==s?((E=function(){var t,e,n,o,u;t=Ei,(e=Qf())!==s&&sb()!==s?("cascaded"===r.substr(Ei,8).toLowerCase()?(n=r.substr(Ei,8),Ei+=8):(n=s,0===gi&&xi(V)),n===s&&("local"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(X))),n!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(o=r.substr(Ei,5),Ei+=5):(o=s,0===gi&&xi(K)),o!==s&&sb()!==s?("OPTION"===r.substr(Ei,6)?(u="OPTION",Ei+=6):(u=s,0===gi&&xi(Q)),u!==s?(Ai=t,e=`with ${n.toLowerCase()} check option`,t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);t===s&&(t=Ei,(e=Qf())!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(K)),n!==s&&sb()!==s?("OPTION"===r.substr(Ei,6)?(o="OPTION",Ei+=6):(o=s,0===gi&&xi(Q)),o!==s?(Ai=t,t=e="with check option"):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s));return t}())===s&&(E=null),E!==s?(Ai=t,A=e,T=n,_=u,I=i,g=l,N=y,O=C,R=E,(S=v).view=S.table,delete S.table,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:A[0].toLowerCase(),keyword:"view",replace:T&&"or replace",algorithm:_&&_[4],definer:I,sql_security:g&&g[4],columns:N&&N[2],select:O,view:S,with:R}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var A,T,_,I,g,S,N,O,R;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=Ei,(e=Sf())!==s&&sb()!==s&&Rp()!==s&&sb()!==s?((n=Hi())===s&&(n=null),n!==s&&sb()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Yi())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Yi())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Yi())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=L(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&sb()!==s?((u=function(){var t,e,n;t=Ei,mf()!==s&&sb()!==s?("role"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(C)),e!==s&&sb()!==s&&(n=wc())!==s?(Ai=t,t={keyword:"default role",value:n}):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(u=null),u!==s&&sb()!==s?((a=function(){var t,e,n;t=Ei,"require"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(S));e!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=$i())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=cp())!==s&&(a=sb())!==s&&(i=$i())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=cp())!==s&&(a=sb())!==s&&(i=$i())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=Mb(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,t=e={keyword:"require",value:n}):(Ei=t,t=s);return t}())===s&&(a=null),a!==s&&sb()!==s?((i=function(){var r,t,e,n,o,u,a;if(r=Ei,(t=Qf())!==s)if(sb()!==s)if((e=Bi())!==s){for(n=[],o=Ei,(u=sb())!==s&&(a=Bi())!==s?o=u=[u,a]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,(u=sb())!==s&&(a=Bi())!==s?o=u=[u,a]:(Ei=o,o=s);n!==s?(Ai=r,t=function(r,t){const e=[r];if(t)for(const r of t)e.push(r[1]);return{keyword:"with",value:e}}(e,n),r=t):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;return r}())===s&&(i=null),i!==s&&sb()!==s?((c=function(){var r,t,e,n,o,u;if(r=Ei,(t=Wi())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Wi())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Wi())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,t=Ub(t,e,1),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(c=null),c!==s&&sb()!==s?((l=function(){var t,e,n;t=Ei,"account"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi($));e!==s&&sb()!==s?("lock"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(B)),n===s&&("unlock"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(W))),n!==s?(Ai=t,e=function(r){const t={type:"origin",value:r.toLowerCase(),prefix:"account"};return t}(n),t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(l=null),l!==s&&sb()!==s?((f=ib())===s&&(f=null),f!==s&&sb()!==s?((p=function(){var t,e,n;t=Ei,"attribute"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(q));e!==s&&sb()!==s&&(n=lf())!==s?(Ai=t,(o=n).prefix="attribute",t=e=o):(Ei=t,t=s);var o;return t}())===s&&(p=null),p!==s?(Ai=t,b=e,v=n,d=o,y=u,w=a,h=i,m=c,E=l,A=f,T=p,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:b[0].toLowerCase(),keyword:"user",if_not_exists:v,user:d,default_role:y,require:w,resource_options:h,password_options:m,lock_option:E,comment:A,attribute:T}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var b,v,d,y,w,h,m,E,A,T;return t}());return t}())===s&&(t=function(){var t,e,n,o;t=Ei,(e=function(){var t,e,n,o;t=Ei,"truncate"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Dr));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TRUNCATE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s?((n=Ff())===s&&(n=null),n!==s&&sb()!==s&&(o=Pc())!==s?(Ai=t,u=e,a=n,(i=o)&&i.forEach(r=>Hb.add(`${u}::${r.db}::${r.table}`)),e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:u.toLowerCase(),keyword:a&&a.toLowerCase()||"table",name:i}},t=e):(Ei=t,t=s)):(Ei=t,t=s);var u,a,i;return t}())===s&&(t=function(){var r,t,e;r=Ei,(t=jf())!==s&&sb()!==s&&Ff()!==s&&sb()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=kc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=kc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=kc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=sr(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Hb.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"rename",table:n}},r=t):(Ei=r,r=s);var n;return r}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;t=Ei,"call"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(ju));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CALL"):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&(n=function(){var r;(r=Lb())===s&&(r=Cb());return r}())!==s?(Ai=t,o=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"call",expr:o}},t=e):(Ei=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;t=Ei,"use"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Is));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&(n=Rl())!==s?(Ai=t,o=n,Hb.add(`use::${o}::null`),e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"use",db:o}},t=e):(Ei=t,t=s);var o;return t}())===s&&(t=function(){var r,t,e,n;r=Ei,(t=_f())!==s&&sb()!==s&&Ff()!==s&&sb()!==s&&(e=Yc())!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Ji())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Ji())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Ji())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=sr(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=r,o=e,u=n,Hb.add(`alter::${o.db}::${o.table}`),t={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"alter",table:[o],expr:u}},r=t):(Ei=r,r=s);var o,u;return r}())===s&&(t=function(){var t,e,n,o;t=Ei,(e=Pf())!==s&&sb()!==s?((n=function(){var t,e,n,o;t=Ei,"global"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(ma));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="GLOBAL"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=Ei,"session"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ea));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SESSION"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=Ei,"local"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(X));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="LOCAL"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=Ei,"persist"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Aa));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="PERSIST"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=Ei,"persist_only"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(Ta));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="PERSIST_ONLY"):(Ei=t,t=s)):(Ei=t,t=s);return t}()),n===s&&(n=null),n!==s&&sb()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=bb())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=bb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=bb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=Yt(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,u=n,(a=o).keyword=u,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"set",keyword:u,expr:a}},t=e):(Ei=t,t=s)):(Ei=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;t=Ei,"lock"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(B));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&Yf()!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=bc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=bc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=bc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=Yt(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,o=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(Ei=t,t=s);var o;return t}())===s&&(t=function(){var t,e;t=Ei,(e=function(){var t,e,n,o;t=Ei,"unlock"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(W));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&Yf()!==s?(Ai=t,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"unlock",keyword:"tables"}},t=e):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=Ei,(e=Af())!==s&&sb()!==s?("binary"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi($t)),n===s&&("master"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(Bt))),n!==s&&(o=sb())!==s?("logs"===r.substr(Ei,4).toLowerCase()?(u=r.substr(Ei,4),Ei+=4):(u=s,0===gi&&xi(Wt)),u!==s?(Ai=t,f=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",suffix:"logs",keyword:f.toLowerCase()}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var f;t===s&&(t=Ei,(e=Af())!==s&&sb()!==s&&(n=Yf())!==s?(Ai=t,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:"tables"}},t=e):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s?("triggers"===r.substr(Ei,8).toLowerCase()?(n=r.substr(Ei,8),Ei+=8):(n=s,0===gi&&xi(qt)),n===s&&("status"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(Vt)),n===s&&("processlist"===r.substr(Ei,11).toLowerCase()?(n=r.substr(Ei,11),Ei+=11):(n=s,0===gi&&xi(Xt)))),n!==s?(Ai=t,d=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:d.toLowerCase()}},t=e):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s?("procedure"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Kt)),n===s&&("function"===r.substr(Ei,8).toLowerCase()?(n=r.substr(Ei,8),Ei+=8):(n=s,0===gi&&xi(Qt))),n!==s&&(o=sb())!==s?("status"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(Vt)),u!==s?(Ai=t,e=function(r){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:r.toLowerCase(),suffix:"status"}}}(n),t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s?("binlog"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(Zt)),n!==s&&(o=sb())!==s?("events"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(zt)),u!==s&&(a=sb())!==s?((i=Ll())===s&&(i=null),i!==s&&sb()!==s?((c=jc())===s&&(c=null),c!==s&&sb()!==s?((l=Qc())===s&&(l=null),l!==s?(Ai=t,p=i,b=c,v=l,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",suffix:"events",keyword:"binlog",in:p,from:b,limit:v}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s?(n=Ei,"character"===r.substr(Ei,9).toLowerCase()?(o=r.substr(Ei,9),Ei+=9):(o=s,0===gi&&xi(lt)),o!==s&&(u=sb())!==s?("set"===r.substr(Ei,3).toLowerCase()?(a=r.substr(Ei,3),Ei+=3):(a=s,0===gi&&xi(ft)),a!==s?n=o=[o,u,a]:(Ei=n,n=s)):(Ei=n,n=s),n===s&&("collation"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Jt)),n===s&&("databases"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(re)))),n!==s&&(o=sb())!==s?((u=hl())===s&&(u=Bc()),u===s&&(u=null),u!==s?(Ai=t,e=function(r,t){let e=Array.isArray(r)&&r||[r];return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",suffix:e[2]&&e[2].toLowerCase(),keyword:e[0].toLowerCase(),expr:t}}}(n,u),t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s?("columns"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(te)),n===s&&("indexes"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(ee)),n===s&&("index"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(gt)))),n!==s&&(o=sb())!==s&&(u=jc())!==s?(Ai=t,e=function(r,t){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:r.toLowerCase(),from:t}}}(n,u),t=e):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Af())!==s&&sb()!==s&&(n=Sf())!==s&&(o=sb())!==s?((u=Up())===s&&(u=Ff())===s&&("event"===r.substr(Ei,5).toLowerCase()?(u=r.substr(Ei,5),Ei+=5):(u=s,0===gi&&xi(ne)),u===s&&(u=Hf())===s&&("procedure"===r.substr(Ei,9).toLowerCase()?(u=r.substr(Ei,9),Ei+=9):(u=s,0===gi&&xi(Kt)))),u!==s&&(a=sb())!==s&&(i=Yc())!==s?(Ai=t,e=function(r,t){const e=r.toLowerCase();return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:"create",suffix:e,[e]:t}}}(u,i),t=e):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=function(){var t,e,n,o;t=Ei,(e=Af())!==s&&sb()!==s?("grants"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(oe)),n!==s&&sb()!==s?((o=function(){var t,e,n,o,u,a,i;t=Ei,"for"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(vr));e!==s&&sb()!==s&&(n=Rl())!==s&&sb()!==s?(o=Ei,(u=Mp())!==s&&(a=sb())!==s&&(i=Rl())!==s?o=u=[u,a,i]:(Ei=o,o=s),o===s&&(o=null),o!==s&&(u=sb())!==s?((a=function(){var r,t;r=Ei,Kf()!==s&&sb()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Rl())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=Yt(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=r,r=t):(Ei=r,r=s);return r}())===s&&(a=null),a!==s?(Ai=t,l=a,e={user:n,host:(c=o)&&c[2],role_list:l},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var c,l;return t}())===s&&(o=null),o!==s?(Ai=t,u=o,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"show",keyword:"grants",for:u}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var u;return t}()))))))));var p,b,v;var d;return t}())===s&&(t=function(){var t,e,n;t=Ei,(e=rp())===s&&(e=function(){var t,e,n,o;t=Ei,"describe"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(du));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DESCRIBE"):(Ei=t,t=s)):(Ei=t,t=s);return t}());e!==s&&sb()!==s&&(n=Rl())!==s?(Ai=t,o=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"desc",table:o}},t=e):(Ei=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=Ei,"grant"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(ue));e!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=dc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=dc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=dc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=L(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&sb()!==s&&(o=Wf())!==s&&sb()!==s?((u=function(){var t,e;t=Ei,(e=Ff())===s&&("function"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Qt)),e===s&&("procedure"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Kt))));e!==s&&(Ai=t,e={type:"origin",value:e.toUpperCase()});return t=e}())===s&&(u=null),u!==s&&sb()!==s&&(a=function(){var r,t,e,n,o;r=Ei,t=Ei,(e=Rl())===s&&(e=zp());e!==s&&(n=sb())!==s&&(o=Qp())!==s?t=e=[e,n,o]:(Ei=t,t=s);t===s&&(t=null);t!==s&&(e=sb())!==s?((n=Rl())===s&&(n=zp()),n!==s?(Ai=r,a=n,t={prefix:(u=t)&&u[0],name:a},r=t):(Ei=r,r=s)):(Ei=r,r=s);var u,a;return r}())!==s&&sb()!==s&&(i=Ef())!==s&&sb()!==s&&(c=wc())!==s&&sb()!==s?((l=function(){var t,e,n;t=Ei,Qf()!==s&&sb()!==s?("grant"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(ue)),e!==s&&sb()!==s?("option"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(ae)),n!==s?(Ai=t,t={type:"origin",value:"with grant option"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(l=null),l!==s?(Ai=t,f=n,p=u,b=a,v=i,d=c,y=l,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"grant",keyword:"priv",objects:f,on:{object_type:p,priv_level:[b]},to_from:v[0],user_or_roles:d,with:y}},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var f,p,b,v,d,y;t===s&&(t=Ei,"GRANT"===r.substr(Ei,5)?(e="GRANT",Ei+=5):(e=s,0===gi&&xi(pe)),e!==s&&sb()!==s?("PROXY"===r.substr(Ei,5)?(n="PROXY",Ei+=5):(n=s,0===gi&&xi(be)),n!==s&&sb()!==s&&(o=Wf())!==s&&sb()!==s&&(u=yc())!==s&&sb()!==s&&(a=Ef())!==s&&sb()!==s&&(i=wc())!==s&&sb()!==s?((c=hc())===s&&(c=null),c!==s?(Ai=t,e=function(r,t,e,n){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"grant",keyword:"proxy",objects:[{priv:{type:"origin",value:"proxy"}}],on:r,to_from:t[0],user_or_roles:e,with:n}}}(u,a,i,c),t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"GRANT"===r.substr(Ei,5)?(e="GRANT",Ei+=5):(e=s,0===gi&&xi(pe)),e!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Rl())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=L(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&sb()!==s&&(o=Ef())!==s&&sb()!==s&&(u=wc())!==s&&sb()!==s?((a=hc())===s&&(a=null),a!==s?(Ai=t,e=function(r,t,e,n){return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"grant",keyword:"role",objects:r.map(r=>({priv:{type:"string",value:r}})),to_from:t[0],user_or_roles:e,with:n}}}(n,o,u,a),t=e):(Ei=t,t=s)):(Ei=t,t=s)));return t}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;t=Ei,"explain"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ps));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&(n=Ic())!==s?(Ai=t,o=n,e={tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:"explain",expr:o}},t=e):(Ei=t,t=s);var o;return t}()),t}function Di(){var r;return(r=Gi())===s&&(r=function(){var r,t,e,n,o,u,a,i;r=Ei,(t=sb())!==s?((e=Cc())===s&&(e=null),e!==s&&sb()!==s&&gf()!==s&&sb()!==s&&(n=Pc())!==s&&sb()!==s&&Pf()!==s&&sb()!==s&&(o=Zc())!==s&&sb()!==s?((u=Bc())===s&&(u=null),u!==s&&sb()!==s?((a=Vc())===s&&(a=null),a!==s&&sb()!==s?((i=Qc())===s&&(i=null),i!==s?(Ai=r,t=function(r,t,e,n,o,s){const u={};return t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,s=o?"select":"update";t&&(u[n]=t),n&&Hb.add(`${s}::${t}::${n}`)}),e&&e.forEach(r=>{if(r.table){const t=Db(r.table);Hb.add(`update::${u[t]||null}::${t}`)}Yb.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{with:r,type:"update",table:t,set:e,where:n,orderby:o,limit:s}}}(e,n,o,u,a,i),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i,c;r=Ei,(t=el())!==s&&sb()!==s?((e=kf())===s&&(e=null),e!==s&&sb()!==s?((n=Mf())===s&&(n=null),n!==s&&sb()!==s&&(o=Yc())!==s&&sb()!==s?((u=rl())===s&&(u=null),u!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(a=gl())!==s&&sb()!==s&&rb()!==s&&sb()!==s&&(i=Jc())!==s&&sb()!==s?((c=tl())===s&&(c=null),c!==s?(Ai=r,t=function(r,t,e,n,o,s,u,a){if(n&&(Hb.add(`insert::${n.db}::${n.table}`),n.as=null),s){let r=n&&n.table||null;Array.isArray(u)&&u.forEach((r,t)=>{if(r.value.length!=s.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),s.forEach(t=>Yb.add(`insert::${r}::${t}`))}const i=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r,table:[n],columns:s,values:u,partition:o,prefix:i,on_duplicate_update:a}}}(t,e,n,o,u,a,i,c),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i;r=Ei,(t=el())!==s&&sb()!==s?((e=kf())===s&&(e=null),e!==s&&sb()!==s?((n=Mf())===s&&(n=null),n!==s&&sb()!==s&&(o=Yc())!==s&&sb()!==s?((u=rl())===s&&(u=null),u!==s&&sb()!==s&&(a=Jc())!==s&&sb()!==s?((i=tl())===s&&(i=null),i!==s?(Ai=r,t=function(r,t,e,n,o,s,u){n&&(Hb.add(`insert::${n.db}::${n.table}`),Yb.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r,table:[n],columns:null,values:s,partition:o,prefix:a,on_duplicate_update:u}}}(t,e,n,o,u,a,i),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i;r=Ei,(t=el())!==s&&sb()!==s?((e=kf())===s&&(e=null),e!==s&&sb()!==s?((n=Mf())===s&&(n=null),n!==s&&sb()!==s&&(o=Yc())!==s&&sb()!==s?((u=rl())===s&&(u=null),u!==s&&sb()!==s&&Pf()!==s&&sb()!==s&&(a=Zc())!==s&&sb()!==s?((i=tl())===s&&(i=null),i!==s?(Ai=r,t=function(r,t,e,n,o,s,u){n&&(Hb.add(`insert::${n.db}::${n.table}`),Yb.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{type:r,table:[n],columns:null,partition:o,prefix:a,set:s,on_duplicate_update:u}}}(t,e,n,o,u,a,i),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i;r=Ei,(t=sb())!==s?((e=Cc())===s&&(e=null),e!==s&&sb()!==s&&Of()!==s&&sb()!==s?((n=Pc())===s&&(n=null),n!==s&&sb()!==s&&(o=jc())!==s&&sb()!==s?((u=Bc())===s&&(u=null),u!==s&&sb()!==s?((a=Vc())===s&&(a=null),a!==s&&sb()!==s?((i=Qc())===s&&(i=null),i!==s?(Ai=r,t=function(r,t,e,n,o,s){if(e){(Array.isArray(e)?e:e.expr).forEach(r=>{const{db:t,as:e,table:n,join:o}=r,s=o?"select":"delete";n&&Hb.add(`${s}::${t}::${n}`),o||Yb.add(`delete::${n}::(.*)`)})}if(null===t&&1===e.length){const r=e[0];t=[{db:r.db,table:r.table,as:r.as,addition:!0}]}return{tableList:Array.from(Hb),columnList:Pb(Yb),ast:{with:r,type:"delete",table:t,from:e,where:n,orderby:o,limit:s}}}(e,n,o,u,a,i),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(r=Mi())===s&&(r=function(){var r,t;r=[],t=pb();for(;t!==s;)r.push(t),t=pb();return r}()),r}function Pi(){var t,e,n,o;return t=Ei,(e=function(){var t,e,n,o;t=Ei,"union"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(tu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s?((n=tp())===s&&(n=ep()),n===s&&(n=null),n!==s?(Ai=t,t=e=(o=n)?"union "+o.toLowerCase():"union"):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"minus"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(eu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e="minus"),(t=e)===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"intersect"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(nu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e="intersect"),t=e)),t}function Gi(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Lc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Pi())!==s&&(a=sb())!==s&&(i=Lc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Pi())!==s&&(a=sb())!==s&&(i=Lc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s&&(n=sb())!==s?((o=Vc())===s&&(o=null),o!==s&&(u=sb())!==s?((a=Qc())===s&&(a=null),a!==s?(Ai=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(Hb),columnList:Pb(Yb),ast:r}}(t,e,o,a)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Fi(){var r,t,e;return r=Ei,(t=ll())!==s&&sb()!==s?((e=Jf())===s&&(e=rp()),e===s&&(e=null),e!==s?(Ai=r,r=t=c(t,e)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=function(){var r,t,e;r=Ei,(t=Il())!==s&&sb()!==s?((e=Jf())===s&&(e=rp()),e===s&&(e=null),e!==s?(Ai=r,t=c(t,e),r=t):(Ei=r,r=s)):(Ei=r,r=s);return r}()),r}function Hi(){var t,e;return t=Ei,"if"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(f)),e!==s&&sb()!==s&&ip()!==s&&sb()!==s&&ap()!==s?(Ai=t,t=e="IF NOT EXISTS"):(Ei=t,t=s),t}function Yi(){var t,e,n;return t=Ei,(e=yc())!==s&&sb()!==s?((n=function(){var t,e,n,o,u,a,i,c,l;return t=Ei,r.substr(Ei,10)===p?(e=p,Ei+=10):(e=s,0===gi&&xi(b)),e!==s&&sb()!==s?(n=Ei,"with"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(v)),o!==s&&(u=sb())!==s&&(a=Rl())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=null),n!==s&&(o=sb())!==s?("by"===r.substr(Ei,2).toLowerCase()?(u=r.substr(Ei,2),Ei+=2):(u=s,0===gi&&xi(d)),u!==s&&(a=sb())!==s?("random"===r.substr(Ei,6).toLowerCase()?(i=r.substr(Ei,6),Ei+=6):(i=s,0===gi&&xi(y)),i!==s&&sb()!==s?("password"===r.substr(Ei,8).toLowerCase()?(c=r.substr(Ei,8),Ei+=8):(c=s,0===gi&&xi(w)),c!==s?(Ai=t,t=e={keyword:["identified",(l=n)&&l[0].toLowerCase()].filter(r=>r).join(" "),auth_plugin:l&&l[2],value:{prefix:"by",type:"origin",value:"random password"}}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,r.substr(Ei,10)===p?(e=p,Ei+=10):(e=s,0===gi&&xi(b)),e!==s&&sb()!==s?(n=Ei,"with"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(v)),o!==s&&(u=sb())!==s&&(a=Rl())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=null),n!==s&&(o=sb())!==s?("by"===r.substr(Ei,2).toLowerCase()?(u=r.substr(Ei,2),Ei+=2):(u=s,0===gi&&xi(d)),u!==s&&(a=sb())!==s&&(i=lf())!==s?(Ai=t,t=e=function(r,t){return t.prefix="by",{keyword:["identified",r&&r[0].toLowerCase()].filter(r=>r).join(" "),auth_plugin:r&&r[2],value:t}}(n,i)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,r.substr(Ei,10)===p?(e=p,Ei+=10):(e=s,0===gi&&xi(b)),e!==s&&sb()!==s?("with"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(v)),n!==s&&(o=sb())!==s&&(u=Rl())!==s&&(a=sb())!==s?("as"===r.substr(Ei,2).toLowerCase()?(i=r.substr(Ei,2),Ei+=2):(i=s,0===gi&&xi(h)),i!==s&&sb()!==s&&(c=lf())!==s?(Ai=t,t=e=function(r,t){return t.prefix="as",{keyword:"identified with",auth_plugin:r&&r[2],value:t}}(u,c)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s))),t}())===s&&(n=null),n!==s?(Ai=t,t=e={user:e,auth_option:n}):(Ei=t,t=s)):(Ei=t,t=s),t}function $i(){var t,e,n;return t=Ei,"none"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(m)),e===s&&("ssl"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(E)),e===s&&("x509"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(A)))),e!==s&&(Ai=t,e={type:"origin",value:e}),(t=e)===s&&(t=Ei,"cipher"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(T)),e===s&&("issuer"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(_)),e===s&&("subject"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(I)))),e!==s&&sb()!==s&&(n=lf())!==s?(Ai=t,t=e=g(e,n)):(Ei=t,t=s)),t}function Bi(){var t,e,n;return t=Ei,"max_queries_per_hour"===r.substr(Ei,20).toLowerCase()?(e=r.substr(Ei,20),Ei+=20):(e=s,0===gi&&xi(N)),e===s&&("max_updates_per_hour"===r.substr(Ei,20).toLowerCase()?(e=r.substr(Ei,20),Ei+=20):(e=s,0===gi&&xi(O)),e===s&&("max_connections_per_hour"===r.substr(Ei,24).toLowerCase()?(e=r.substr(Ei,24),Ei+=24):(e=s,0===gi&&xi(R)),e===s&&("max_user_connections"===r.substr(Ei,20).toLowerCase()?(e=r.substr(Ei,20),Ei+=20):(e=s,0===gi&&xi(x))))),e!==s&&sb()!==s&&(n=vf())!==s?(Ai=t,t=e=g(e,n)):(Ei=t,t=s),t}function Wi(){var t,e,n,o,u,a;return t=Ei,"password"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(w)),e!==s&&sb()!==s?("expire"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(j)),n!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(k)),o===s&&("never"===r.substr(Ei,5).toLowerCase()?(o=r.substr(Ei,5),Ei+=5):(o=s,0===gi&&xi(U)),o===s&&(o=ul())),o!==s?(Ai=t,t=e={keyword:"password expire",value:"string"==typeof(a=o)?{type:"origin",value:a}:a}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"password"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(w)),e!==s&&sb()!==s?("history"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(M)),n!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(k)),o===s&&(o=vf()),o!==s?(Ai=t,t=e=function(r){return{keyword:"password history",value:"string"==typeof r?{type:"origin",value:r}:r}}(o)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"password"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(w)),e!==s&&sb()!==s?("REUSE"===r.substr(Ei,5)?(n="REUSE",Ei+=5):(n=s,0===gi&&xi(D)),n!==s&&sb()!==s&&(o=ul())!==s?(Ai=t,t=e=function(r){return{keyword:"password reuse",value:r}}(o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"password"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(w)),e!==s&&sb()!==s?("require"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(S)),n!==s&&sb()!==s?("current"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(P)),o!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(u=r.substr(Ei,7),Ei+=7):(u=s,0===gi&&xi(k)),u===s&&("optional"===r.substr(Ei,8).toLowerCase()?(u=r.substr(Ei,8),Ei+=8):(u=s,0===gi&&xi(G))),u!==s?(Ai=t,t=e=function(r){return{keyword:"password require current",value:{type:"origin",value:r}}}(u)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"failed_login_attempts"===r.substr(Ei,21).toLowerCase()?(e=r.substr(Ei,21),Ei+=21):(e=s,0===gi&&xi(F)),e!==s&&sb()!==s&&(n=vf())!==s?(Ai=t,t=e=function(r){return{keyword:"failed_login_attempts",value:r}}(n)):(Ei=t,t=s),t===s&&(t=Ei,"password_lock_time"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(H)),e!==s&&sb()!==s?((n=vf())===s&&("unbounded"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Y))),n!==s?(Ai=t,t=e=function(r){return{keyword:"password_lock_time",value:"string"==typeof r?{type:"origin",value:r}:r}}(n)):(Ei=t,t=s)):(Ei=t,t=s)))))),t}function qi(){var r;return(r=sc())===s&&(r=Xi())===s&&(r=nc())===s&&(r=oc()),r}function Vi(){var t,e,n,o,u;return t=Ei,(e=function(){var t,e;t=Ei,(e=function(){var t,e,n,o;t=Ei,"not null"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(ms));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={type:"not null",value:"not null"});return t=e}())===s&&(e=cf()),e!==s&&(Ai=t,(u=e)&&!u.value&&(u.value="null"),e={nullable:u}),(t=e)===s&&(t=Ei,(e=function(){var r,t;r=Ei,mf()!==s&&sb()!==s&&(t=ll())!==s?(Ai=r,r={type:"default",value:t}):(Ei=r,r=s);return r}())!==s&&(Ai=t,e={default_val:e}),(t=e)===s&&(t=Ei,"auto_increment"===r.substr(Ei,14).toLowerCase()?(e=r.substr(Ei,14),Ei+=14):(e=s,0===gi&&xi(ur)),e!==s&&(Ai=t,e={auto_increment:e.toLowerCase()}),(t=e)===s&&(t=Ei,"unique"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(ar)),e!==s&&sb()!==s?("key"===r.substr(Ei,3).toLowerCase()?(n=r.substr(Ei,3),Ei+=3):(n=s,0===gi&&xi(ir)),n===s&&(n=null),n!==s?(Ai=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"primary"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(cr)),e===s&&(e=null),e!==s&&sb()!==s?("key"===r.substr(Ei,3).toLowerCase()?(n=r.substr(Ei,3),Ei+=3):(n=s,0===gi&&xi(ir)),n!==s?(Ai=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=ib())!==s&&(Ai=t,e={comment:e}),(t=e)===s&&(t=Ei,(e=Qi())!==s&&(Ai=t,e={collate:e}),(t=e)===s&&(t=Ei,(e=function(){var t,e,n;t=Ei,"column_format"===r.substr(Ei,13).toLowerCase()?(e=r.substr(Ei,13),Ei+=13):(e=s,0===gi&&xi(Cr));e!==s&&sb()!==s?("fixed"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(mr)),n===s&&("dynamic"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(Er)),n===s&&("default"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(k)))),n!==s?(Ai=t,e={type:"column_format",value:n.toLowerCase()},t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={column_format:e}),(t=e)===s&&(t=Ei,(e=function(){var t,e,n;t=Ei,"storage"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ar));e!==s&&sb()!==s?("disk"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(Tr)),n===s&&("memory"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(_r))),n!==s?(Ai=t,e={type:"storage",value:n.toLowerCase()},t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={storage:e}),(t=e)===s&&(t=Ei,(e=ac())!==s&&(Ai=t,e={reference_definition:e}),(t=e)===s&&(t=Ei,(e=function(){var t,e,n,o,u,a,i,c;t=Ei,(e=uc())===s&&(e=null);e!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(K)),n!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(o=fl())!==s&&sb()!==s&&rb()!==s&&sb()!==s?(u=Ei,(a=ip())===s&&(a=null),a!==s&&(i=sb())!==s?("enforced"===r.substr(Ei,8).toLowerCase()?(c=r.substr(Ei,8),Ei+=8):(c=s,0===gi&&xi(tt)),c!==s?u=a=[a,i,c]:(Ei=u,u=s)):(Ei=u,u=s),u===s&&(u=null),u!==s?(Ai=t,e=function(r,t,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:t.toLowerCase(),keyword:r&&r.keyword,constraint:r&&r.constraint,definition:[e],enforced:o.filter(r=>r).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,u),t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={check:e}),(t=e)===s&&(t=Ei,(e=lc())!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s&&(o=Nl())!==s?(Ai=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t,e,n,o,u,a,i,c;t=Ei,e=Ei,(n=function(){var t,e,n,o,u;t=Ei,e=Ei,"generated"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Ir));n!==s&&(o=sb())!==s?("always"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(gr)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s);e!==s&&(Ai=t,e=e.join("").toLowerCase());return t=e}())===s&&(n=null);n!==s&&(o=sb())!==s?("as"===r.substr(Ei,2).toLowerCase()?(u=r.substr(Ei,2),Ei+=2):(u=s,0===gi&&xi(h)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s);if(e!==s)if((n=sb())!==s)if((o=Jp())!==s)if((u=sb())!==s)if((a=af())===s&&(a=ll()),a!==s)if(sb()!==s)if(rb()!==s)if(sb()!==s){for(i=[],"stored"===r.substr(Ei,6).toLowerCase()?(c=r.substr(Ei,6),Ei+=6):(c=s,0===gi&&xi(Sr)),c===s&&("virtual"===r.substr(Ei,7).toLowerCase()?(c=r.substr(Ei,7),Ei+=7):(c=s,0===gi&&xi(Nr)));c!==s;)i.push(c),"stored"===r.substr(Ei,6).toLowerCase()?(c=r.substr(Ei,6),Ei+=6):(c=s,0===gi&&xi(Sr)),c===s&&("virtual"===r.substr(Ei,7).toLowerCase()?(c=r.substr(Ei,7),Ei+=7):(c=s,0===gi&&xi(Nr)));i!==s?(Ai=t,l=i,e={type:"generated",expr:a,value:e.filter(r=>"string"==typeof r).join(" ").toLowerCase(),storage_type:l&&l[0]&&l[0].toLowerCase()},t=e):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;var l;return t}())!==s&&(Ai=t,e={generated:e}),t=e)))))))))))),t}function Xi(){var r,t,e,n,o,u,a;return r=Ei,(t=Il())!==s&&sb()!==s&&(e=Tb())!==s&&sb()!==s?((n=function(){var r,t,e,n,o,u;if(r=Ei,(t=Vi())!==s)if(sb()!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Vi())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Vi())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;return r}())===s&&(n=null),n!==s?(Ai=r,o=t,u=e,a=n,Yb.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:u,resource:"column",...a||{}}):(Ei=r,r=s)):(Ei=r,r=s),r}function Ki(){var t,e,n,o,u;return t=Ei,"definer"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(nr)),e!==s&&sb()!==s&&Gp()!==s&&sb()!==s?((n=Ml())===s&&(n=lf()),n!==s&&sb()!==s?(64===r.charCodeAt(Ei)?(o="@",Ei++):(o=s,0===gi&&xi(lr)),o!==s&&sb()!==s?((u=Ml())===s&&(u=lf()),u!==s?(Ai=t,t=e=function(r,t){const e=jb("@",r,t);return jb("=",{type:"origin",value:"definer"},e)}(n,u)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"definer"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(nr)),e!==s&&sb()!==s&&Gp()!==s&&sb()!==s&&(n=kp())!==s&&sb()!==s&&(o=Jp())!==s&&sb()!==s&&(u=rb())!==s?(Ai=t,t=e=fr()):(Ei=t,t=s),t===s&&(t=Ei,"definer"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(nr)),e!==s&&sb()!==s&&Gp()!==s&&sb()!==s&&(n=kp())!==s?(Ai=t,t=e=fr()):(Ei=t,t=s))),t}function Qi(){var t,e,n;return t=Ei,function(){var t,e,n,o;t=Ei,"collate"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(bt));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="COLLATE"):(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s?((e=Gp())===s&&(e=null),e!==s&&sb()!==s&&(n=Rl())!==s?(Ai=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Ei=t,t=s)):(Ei=t,t=s),t}function Zi(){var t,e,n;return t=Ei,"if"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(Or)),e!==s&&sb()!==s?("exists"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(Rr)),n!==s?(Ai=t,t=e="if exists"):(Ei=t,t=s)):(Ei=t,t=s),t}function zi(){var t,e,n;return t=Ei,"first"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(xr)),e!==s&&(Ai=t,e={keyword:e}),(t=e)===s&&(t=Ei,"after"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(jr)),e!==s&&sb()!==s&&(n=Il())!==s?(Ai=t,t=e=function(r,t){return{keyword:r,expr:t}}(e,n)):(Ei=t,t=s)),t}function Ji(){var t,e,n;return(t=function(){var r,t;r=Ei,Fp()!==s&&sb()!==s&&(t=sc())!==s?(Ai=r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(Ei=r,r=s);return r}())===s&&(t=function(){var t,e,n,o;t=Ei,(e=Tf())!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(K)),n!==s&&sb()!==s&&(o=Fl())!==s?(Ai=t,e={action:"drop",constraint:o,keyword:n.toLowerCase(),resource:"constraint",type:"alter"},t=e):(Ei=t,t=s)):(Ei=t,t=s);t===s&&(t=Ei,(e=Tf())!==s&&sb()!==s?("constraint"===r.substr(Ei,10).toLowerCase()?(n=r.substr(Ei,10),Ei+=10):(n=s,0===gi&&xi(Qr)),n!==s&&sb()!==s&&(o=Fl())!==s?(Ai=t,e=function(r,t){return{action:"drop",constraint:t,keyword:r.toLowerCase(),resource:"constraint",type:"alter"}}(n,o),t=e):(Ei=t,t=s)):(Ei=t,t=s));return t}())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,(e=Tf())!==s&&sb()!==s?("primary"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(cr)),n!==s&&(o=sb())!==s&&(u=$p())!==s?(Ai=t,t=e={action:"drop",key:"",keyword:"primary key",resource:"key",type:"alter"}):(Ei=t,t=s)):(Ei=t,t=s);t===s&&(t=Ei,(e=Tf())!==s&&sb()!==s?(n=Ei,"foreign"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(Kr)),o===s&&(o=null),o!==s&&(u=sb())!==s&&(a=$p())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=Yp()),n!==s&&(o=sb())!==s&&(u=Rl())!==s?(Ai=t,e=function(r,t){const e=Array.isArray(r)?"key":"index";return{action:"drop",[e]:t,keyword:Array.isArray(r)?""+[r[0],r[2]].filter(r=>r).join(" ").toLowerCase():r.toLowerCase(),resource:e,type:"alter"}}(n,u),t=e):(Ei=t,t=s)):(Ei=t,t=s));return t}())===s&&(t=function(){var t,e,n,o;t=Ei,Qf()!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(K)),e!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(K)),n!==s&&sb()!==s&&Xp()!==s&&sb()!==s&&(o=Fl())!==s?(Ai=t,t={action:"with",constraint:o,keyword:"check check",resource:"constraint",type:"alter"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n;t=Ei,"nocheck"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Zr));e!==s&&sb()!==s&&Xp()!==s&&sb()!==s&&(n=Fl())!==s?(Ai=t,t=e={action:"nocheck",constraint:n,resource:"constraint",type:"alter"}):(Ei=t,t=s);return t}())===s&&(t=function(){var r,t,e,n,o;r=Ei,(t=Fp())!==s&&sb()!==s&&(e=Hp())!==s&&sb()!==s&&(n=Xi())!==s&&sb()!==s?((o=zi())===s&&(o=null),o!==s?(Ai=r,u=e,a=n,i=o,t={action:"add",...a,keyword:u,suffix:i,resource:"column",type:"alter"},r=t):(Ei=r,r=s)):(Ei=r,r=s);var u,a,i;r===s&&(r=Ei,(t=Fp())!==s&&sb()!==s&&(e=Xi())!==s&&sb()!==s?((n=zi())===s&&(n=null),n!==s?(Ai=r,t=function(r,t){return{action:"add",...r,suffix:t,resource:"column",type:"alter"}}(e,n),r=t):(Ei=r,r=s)):(Ei=r,r=s));return r}())===s&&(t=function(){var r,t,e,n;r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Hp())!==s&&sb()!==s&&(n=Il())!==s?(Ai=r,r=t={action:"drop",column:n,keyword:e,resource:"column",type:"alter"}):(Ei=r,r=s);r===s&&(r=Ei,(t=Tf())!==s&&sb()!==s&&(e=Il())!==s?(Ai=r,t=function(r){return{action:"drop",column:r,resource:"column",type:"alter"}}(e),r=t):(Ei=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u;t=Ei,(e=function(){var t,e,n,o;t=Ei,"modify"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Fa));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MODIFY"):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s?((n=Hp())===s&&(n=null),n!==s&&sb()!==s&&(o=Xi())!==s&&sb()!==s?((u=zi())===s&&(u=null),u!==s?(Ai=t,a=o,i=u,e={action:"modify",keyword:n,...a,suffix:i,resource:"column",type:"alter"},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var a,i;return t}())===s&&(t=function(){var r,t,e;r=Ei,(t=Fp())!==s&&sb()!==s&&(e=nc())!==s?(Ai=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ei=r,r=s);var n;return r}())===s&&(t=function(){var r,t,e;r=Ei,(t=Fp())!==s&&sb()!==s&&(e=oc())!==s?(Ai=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ei=r,r=s);var n;return r}())===s&&(t=function(){var r,t,e,n,o;r=Ei,(t=jf())!==s&&sb()!==s&&Hp()!==s&&sb()!==s&&(e=Il())!==s&&sb()!==s?((n=Ef())===s&&(n=Gf()),n===s&&(n=null),n!==s&&sb()!==s&&(o=Il())!==s?(Ai=r,a=o,t={action:"rename",type:"alter",resource:"column",keyword:"column",old_column:e,prefix:(u=n)&&u[0].toLowerCase(),column:a},r=t):(Ei=r,r=s)):(Ei=r,r=s);var u,a;return r}())===s&&(t=function(){var r,t,e,n;r=Ei,(t=jf())!==s&&sb()!==s?((e=Ef())===s&&(e=Gf()),e===s&&(e=null),e!==s&&sb()!==s&&(n=Rl())!==s?(Ai=r,u=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},r=t):(Ei=r,r=s)):(Ei=r,r=s);var o,u;return r}())===s&&(t=tc())===s&&(t=ec())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,"change"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Xr));e!==s&&sb()!==s?((n=Hp())===s&&(n=null),n!==s&&sb()!==s&&(o=Il())!==s&&sb()!==s&&(u=Xi())!==s&&sb()!==s?((a=zi())===s&&(a=null),a!==s?(Ai=t,i=n,c=u,l=a,e={action:"change",old_column:o,...c,keyword:i,resource:"column",type:"alter",suffix:l},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var i,c,l;return t}())===s&&(t=function(){var t,e,n,o,u;t=Ei,"drop"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Mr));e===s&&("truncate"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Dr)),e===s&&("discard"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Pr)),e===s&&("import"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Gr)),e===s&&("coalesce"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Fr)),e===s&&("analyze"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Hr)),e===s&&("check"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(K))))))));e!==s&&sb()!==s&&(n=Uf())!==s&&sb()!==s&&(o=Sc())!==s&&sb()!==s?("tablespace"===r.substr(Ei,10).toLowerCase()?(u=r.substr(Ei,10),Ei+=10):(u=s,0===gi&&xi(Yr)),u===s&&(u=null),u!==s?(Ai=t,e=function(r,t,e,n){const o={action:r.toLowerCase(),keyword:t,resource:"partition",type:"alter",partitions:e};return n&&(o.suffix={keyword:n}),o}(e,n,o,u),t=e):(Ei=t,t=s)):(Ei=t,t=s);t===s&&(t=Ei,(e=Fp())!==s&&sb()!==s&&(n=Uf())!==s&&sb()!==s&&(o=Jp())!==s&&sb()!==s&&(u=function(){var r,t,e,n,o,u,a,c;if(r=Ei,(t=rc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=rc())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=rc())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);e!==s?(Ai=r,t=i(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e={action:"add",keyword:n,resource:"partition",type:"alter",partitions:u}):(Ei=t,t=s));return t}())===s&&(t=Ei,(e=pc())!==s&&(Ai=t,(n=e).resource=n.keyword,n[n.keyword]=n.value,delete n.value,e={type:"alter",...n}),t=e),t}function rc(){var t,e,n,o,u;return t=Ei,Uf()!==s&&sb()!==s&&(e=Nl())!==s&&sb()!==s&&Xf()!==s&&sb()!==s?("less"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(kr)),n!==s&&sb()!==s?("than"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(Ur)),o!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(u=vf())!==s&&sb()!==s&&rb()!==s?(Ai=t,t={name:e,value:{type:"less than",expr:u,parentheses:!0}}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function tc(){var t,e,n,o;return t=Ei,"algorithm"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Z)),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(k)),o===s&&("instant"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi($r)),o===s&&("inplace"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(Br)),o===s&&("copy"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(Wr))))),o!==s?(Ai=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function ec(){var t,e,n,o;return t=Ei,"lock"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(B)),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(k)),o===s&&("none"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(m)),o===s&&("shared"===r.substr(Ei,6).toLowerCase()?(o=r.substr(Ei,6),Ei+=6):(o=s,0===gi&&xi(qr)),o===s&&("exclusive"===r.substr(Ei,9).toLowerCase()?(o=r.substr(Ei,9),Ei+=9):(o=s,0===gi&&xi(Vr))))),o!==s?(Ai=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function nc(){var r,t,e,n,o,u,a,i;return r=Ei,(t=Yp())===s&&(t=$p()),t!==s&&sb()!==s?((e=Pl())===s&&(e=null),e!==s&&sb()!==s?((n=Uc())===s&&(n=null),n!==s&&sb()!==s&&(o=Tc())!==s&&sb()!==s?((u=Mc())===s&&(u=null),u!==s&&sb()!==s?(Ai=r,a=n,i=u,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:a,resource:"index",index_options:i}):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r}function oc(){var r,t,e,n,o,u,a,i,c;return r=Ei,(t=Bp())===s&&(t=Wp()),t!==s&&sb()!==s?((e=Yp())===s&&(e=$p()),e===s&&(e=null),e!==s&&sb()!==s?((n=Pl())===s&&(n=null),n!==s&&sb()!==s&&(o=Ec())!==s&&sb()!==s?((u=Mc())===s&&(u=null),u!==s?(Ai=r,a=t,c=u,r=t={index:n,definition:o,keyword:(i=e)&&`${a.toLowerCase()} ${i.toLowerCase()}`||a.toLowerCase(),index_options:c,resource:"index"}):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r}function sc(){var t;return(t=function(){var t,e,n,o,u,a,i,c;t=Ei,(e=uc())===s&&(e=null);e!==s&&sb()!==s?(n=Ei,"primary"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(cr)),o!==s&&(u=sb())!==s?("key"===r.substr(Ei,3).toLowerCase()?(a=r.substr(Ei,3),Ei+=3):(a=s,0===gi&&xi(ir)),a!==s?n=o=[o,u,a]:(Ei=n,n=s)):(Ei=n,n=s),n!==s&&(o=sb())!==s?((u=Uc())===s&&(u=null),u!==s&&(a=sb())!==s&&(i=Tc())!==s&&sb()!==s?((c=Mc())===s&&(c=null),c!==s?(Ai=t,f=n,p=u,b=i,v=c,e={constraint:(l=e)&&l.constraint,definition:b,constraint_type:`${f[0].toLowerCase()} ${f[2].toLowerCase()}`,keyword:l&&l.keyword,index_type:p,resource:"constraint",index_options:v},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var l,f,p,b,v;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=Ei,(t=uc())===s&&(t=null);t!==s&&sb()!==s&&(e=qp())!==s&&sb()!==s?((n=Yp())===s&&(n=$p()),n===s&&(n=null),n!==s&&sb()!==s?((o=Pl())===s&&(o=null),o!==s&&sb()!==s?((u=Uc())===s&&(u=null),u!==s&&sb()!==s&&(a=Tc())!==s&&sb()!==s?((i=Mc())===s&&(i=null),i!==s?(Ai=r,l=e,f=n,p=o,b=u,v=a,d=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:d},r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);var c,l,f,p,b,v,d;return r}())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,(e=uc())===s&&(e=null);e!==s&&sb()!==s?("foreign key"===r.substr(Ei,11).toLowerCase()?(n=r.substr(Ei,11),Ei+=11):(n=s,0===gi&&xi(rt)),n!==s&&sb()!==s?((o=Pl())===s&&(o=null),o!==s&&sb()!==s&&(u=Ec())!==s&&sb()!==s?((a=ac())===s&&(a=null),a!==s?(Ai=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var i,c,l,f,p;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f;t=Ei,(e=uc())===s&&(e=null);e!==s&&sb()!==s?("check"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(K)),n!==s&&sb()!==s?(o=Ei,"not"===r.substr(Ei,3).toLowerCase()?(u=r.substr(Ei,3),Ei+=3):(u=s,0===gi&&xi(zr)),u!==s&&(a=sb())!==s?("for"===r.substr(Ei,3).toLowerCase()?(i=r.substr(Ei,3),Ei+=3):(i=s,0===gi&&xi(vr)),i!==s&&(c=sb())!==s?("replication"===r.substr(Ei,11).toLowerCase()?(l=r.substr(Ei,11),Ei+=11):(l=s,0===gi&&xi(Jr)),l!==s&&(f=sb())!==s?o=u=[u,a,i,c,l,f]:(Ei=o,o=s)):(Ei=o,o=s)):(Ei=o,o=s),o===s&&(o=null),o!==s&&(u=Jp())!==s&&(a=sb())!==s&&(i=fl())!==s&&(c=sb())!==s&&(l=rb())!==s?(Ai=t,p=e,b=o,v=i,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication"},definition:[v],resource:"constraint"},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var p,b,v;return t}()),t}function uc(){var r,t,e,n;return r=Ei,(t=Xp())!==s&&sb()!==s?((e=Rl())===s&&(e=null),e!==s?(Ai=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(Ei=r,r=s)):(Ei=r,r=s),r}function ac(){var t,e,n,o,u,a,i,c,l,f;return t=Ei,(e=Kp())!==s&&sb()!==s&&(n=Pc())!==s&&sb()!==s&&(o=Ec())!==s&&sb()!==s?("match full"===r.substr(Ei,10).toLowerCase()?(u=r.substr(Ei,10),Ei+=10):(u=s,0===gi&&xi(et)),u===s&&("match partial"===r.substr(Ei,13).toLowerCase()?(u=r.substr(Ei,13),Ei+=13):(u=s,0===gi&&xi(nt)),u===s&&("match simple"===r.substr(Ei,12).toLowerCase()?(u=r.substr(Ei,12),Ei+=12):(u=s,0===gi&&xi(ot)))),u===s&&(u=null),u!==s&&sb()!==s?((a=ic())===s&&(a=null),a!==s&&sb()!==s?((i=ic())===s&&(i=null),i!==s?(Ai=t,c=u,l=a,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=ic())!==s&&(Ai=t,e={on_action:[e]}),t=e),t}function ic(){var t,e,n,o;return t=Ei,Wf()!==s&&sb()!==s?((e=Of())===s&&(e=gf()),e!==s&&sb()!==s&&(n=function(){var t,e,n;t=Ei,(e=jp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s?((n=sl())===s&&(n=null),n!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Ei=t,t=s)):(Ei=t,t=s);t===s&&(t=Ei,(e=cc())===s&&("set null"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(at)),e===s&&("no action"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(it)),e===s&&("set default"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(ct)),e===s&&(e=jp())))),e!==s&&(Ai=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==s?(Ai=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Ei=t,t=s)):(Ei=t,t=s),t}function cc(){var t,e;return t=Ei,"restrict"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(st)),e===s&&("cascade"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(ut))),e!==s&&(Ai=t,e=e.toLowerCase()),t=e}function lc(){var t,e,n;return t=Ei,"character"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(lt)),e!==s&&sb()!==s?("set"===r.substr(Ei,3).toLowerCase()?(n=r.substr(Ei,3),Ei+=3):(n=s,0===gi&&xi(ft)),n!==s?(Ai=t,t=e="CHARACTER SET"):(Ei=t,t=s)):(Ei=t,t=s),t}function fc(){var t,e,n,o,u,a,i,c,l;return t=Ei,(e=mf())===s&&(e=null),e!==s&&sb()!==s?((n=lc())===s&&("charset"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(pt)),n===s&&("collate"===r.substr(Ei,7).toLowerCase()?(n=r.substr(Ei,7),Ei+=7):(n=s,0===gi&&xi(bt)))),n!==s&&sb()!==s?((o=Gp())===s&&(o=null),o!==s&&sb()!==s&&(u=Nl())!==s?(Ai=t,i=n,c=o,l=u,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function pc(){var t,e,n,o,u,a,i,c,l;return t=Ei,"auto_increment"===r.substr(Ei,14).toLowerCase()?(e=r.substr(Ei,14),Ei+=14):(e=s,0===gi&&xi(ur)),e===s&&("avg_row_length"===r.substr(Ei,14).toLowerCase()?(e=r.substr(Ei,14),Ei+=14):(e=s,0===gi&&xi(vt)),e===s&&("key_block_size"===r.substr(Ei,14).toLowerCase()?(e=r.substr(Ei,14),Ei+=14):(e=s,0===gi&&xi(dt)),e===s&&("max_rows"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(yt)),e===s&&("min_rows"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(wt)),e===s&&("stats_sample_pages"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(ht))))))),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s&&(o=vf())!==s?(Ai=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"CHECKSUM"===r.substr(Ei,8)?(e="CHECKSUM",Ei+=8):(e=s,0===gi&&xi(Lt)),e===s&&("DELAY_KEY_WRITE"===r.substr(Ei,15)?(e="DELAY_KEY_WRITE",Ei+=15):(e=s,0===gi&&xi(Ct))),e!==s&&sb()!==s&&(n=Gp())!==s&&sb()!==s?(mt.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(Et)),o!==s?(Ai=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e}}(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=fc())===s&&(t=Ei,(e=Vp())===s&&("connection"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(At)),e===s&&("engine_attribute"===r.substr(Ei,16).toLowerCase()?(e=r.substr(Ei,16),Ei+=16):(e=s,0===gi&&xi(Tt)),e===s&&("secondary_engine_attribute"===r.substr(Ei,26).toLowerCase()?(e=r.substr(Ei,26),Ei+=26):(e=s,0===gi&&xi(_t))))),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s&&(o=lf())!==s?(Ai=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"data"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(It)),e===s&&("index"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(gt))),e!==s&&sb()!==s?("directory"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(St)),n!==s&&sb()!==s?((o=Gp())===s&&(o=null),o!==s&&(u=sb())!==s&&(a=lf())!==s?(Ai=t,t=e=function(r,t,e){return{keyword:r.toLowerCase()+" directory",symbol:t,value:`'${e.value}'`}}(e,o,a)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"compression"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(Nt)),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s?(o=Ei,39===r.charCodeAt(Ei)?(u="'",Ei++):(u=s,0===gi&&xi(Ot)),u!==s?("zlib"===r.substr(Ei,4).toLowerCase()?(a=r.substr(Ei,4),Ei+=4):(a=s,0===gi&&xi(Rt)),a===s&&("lz4"===r.substr(Ei,3).toLowerCase()?(a=r.substr(Ei,3),Ei+=3):(a=s,0===gi&&xi(xt)),a===s&&("none"===r.substr(Ei,4).toLowerCase()?(a=r.substr(Ei,4),Ei+=4):(a=s,0===gi&&xi(m)))),a!==s?(39===r.charCodeAt(Ei)?(i="'",Ei++):(i=s,0===gi&&xi(Ot)),i!==s?o=u=[u,a,i]:(Ei=o,o=s)):(Ei=o,o=s)):(Ei=o,o=s),o!==s?(Ai=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"engine"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(jt)),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s&&(o=Fl())!==s?(Ai=t,t=e=kt(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"row_format"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Ut)),e!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s?("default"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(k)),o===s&&("dynamic"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(Er)),o===s&&("fixed"===r.substr(Ei,5).toLowerCase()?(o=r.substr(Ei,5),Ei+=5):(o=s,0===gi&&xi(mr)),o===s&&("compressed"===r.substr(Ei,10).toLowerCase()?(o=r.substr(Ei,10),Ei+=10):(o=s,0===gi&&xi(Mt)),o===s&&("redundant"===r.substr(Ei,9).toLowerCase()?(o=r.substr(Ei,9),Ei+=9):(o=s,0===gi&&xi(Dt)),o===s&&("compact"===r.substr(Ei,7).toLowerCase()?(o=r.substr(Ei,7),Ei+=7):(o=s,0===gi&&xi(Pt))))))),o!==s?(Ai=t,t=e=kt(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s))))))),t}function bc(){var t,e,n,o,u;return t=Ei,(e=Fc())!==s&&sb()!==s&&(n=function(){var t,e,n;return t=Ei,"read"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Gt)),e!==s&&sb()!==s?("local"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(X)),n===s&&(n=null),n!==s?(Ai=t,t=e={type:"read",suffix:n&&"local"}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"low_priority"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(Ft)),e===s&&(e=null),e!==s&&sb()!==s?("write"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(Ht)),n!==s?(Ai=t,t=e={type:"write",prefix:e&&"low_priority"}):(Ei=t,t=s)):(Ei=t,t=s)),t}())!==s?(Ai=t,o=e,u=n,Hb.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:u}):(Ei=t,t=s),t}function vc(){var t;return(t=function(){var t,e,n,o,u;return t=Ei,(e=tp())===s&&(e=_f())===s&&(e=Ei,(n=Sf())!==s&&(o=sb())!==s?("view"===r.substr(Ei,4).toLowerCase()?(u=r.substr(Ei,4),Ei+=4):(u=s,0===gi&&xi(se)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s),e===s&&(e=Sf())===s&&(e=Of())===s&&(e=Tf())===s&&(e=Ei,"grant"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(ue)),n!==s&&(o=sb())!==s?("option"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(ae)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s),e===s&&(e=Yp())===s&&(e=Rf())===s&&(e=Kp())===s&&(e=If())===s&&(e=Ei,(n=Af())!==s&&(o=sb())!==s&&(u=Up())!==s?e=n=[n,o,u]:(Ei=e,e=s),e===s&&(e=Hf())===s&&(e=gf())))),e!==s&&(Ai=t,e=ie(e)),t=e}())===s&&(t=function(){var t,e,n,o,u;return t=Ei,e=Ei,(n=_f())!==s&&(o=sb())!==s?("routine"===r.substr(Ei,7).toLowerCase()?(u=r.substr(Ei,7),Ei+=7):(u=s,0===gi&&xi(ce)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s),e===s&&("execute"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(le)),e===s&&(e=Ei,"grant"===r.substr(Ei,5).toLowerCase()?(n=r.substr(Ei,5),Ei+=5):(n=s,0===gi&&xi(ue)),n!==s&&(o=sb())!==s?("option"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(ae)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s),e===s&&(e=Ei,(n=Sf())!==s&&(o=sb())!==s?("routine"===r.substr(Ei,7).toLowerCase()?(u=r.substr(Ei,7),Ei+=7):(u=s,0===gi&&xi(ce)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s)))),e!==s&&(Ai=t,e=ie(e)),t=e}()),t}function dc(){var r,t,e,n,o,u,a,i,c;return r=Ei,(t=vc())!==s&&sb()!==s?(e=Ei,(n=Jp())!==s&&(o=sb())!==s&&(u=Wc())!==s&&(a=sb())!==s&&(i=rb())!==s?e=n=[n,o,u,a,i]:(Ei=e,e=s),e===s&&(e=null),e!==s?(Ai=r,r=t={priv:t,columns:(c=e)&&c[2]}):(Ei=r,r=s)):(Ei=r,r=s),r}function yc(){var t,e,n,o,u,a,i;return t=Ei,(e=Rl())!==s&&sb()!==s?(n=Ei,64===r.charCodeAt(Ei)?(o="@",Ei++):(o=s,0===gi&&xi(lr)),o!==s&&(u=sb())!==s&&(a=Rl())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=null),n!==s?(Ai=t,t=e={name:{type:"single_quote_string",value:e},host:(i=n)?{type:"single_quote_string",value:i[2]}:null}):(Ei=t,t=s)):(Ei=t,t=s),t}function wc(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=yc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=yc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=yc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=L(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function hc(){var t,e,n;return t=Ei,Qf()!==s&&sb()!==s?("admin"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(fe)),e!==s&&sb()!==s?("option"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(ae)),n!==s?(Ai=t,t={type:"origin",value:"with admin option"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function Lc(){var t,e,n,o,u,a,i;return(t=Ic())===s&&(t=Ei,e=Ei,40===r.charCodeAt(Ei)?(n="(",Ei++):(n=s,0===gi&&xi(ve)),n!==s&&(o=sb())!==s&&(u=Lc())!==s&&(a=sb())!==s?(41===r.charCodeAt(Ei)?(i=")",Ei++):(i=s,0===gi&&xi(de)),i!==s?e=n=[n,o,u,a,i]:(Ei=e,e=s)):(Ei=e,e=s),e!==s&&(Ai=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Cc(){var t,e,n,o,u,a,i,c,l;if(t=Ei,Qf()!==s)if(sb()!==s)if((e=mc())!==s){for(n=[],o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=mc())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=mc())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);n!==s?(Ai=t,t=sr(e,n)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;return t===s&&(t=Ei,sb()!==s&&Qf()!==s&&(e=sb())!==s&&(n=function(){var t,e,n,o;t=Ei,"recursive"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(ks));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(o=sb())!==s&&(u=mc())!==s?(Ai=t,(l=u).recursive=!0,t=[l]):(Ei=t,t=s)),t}function mc(){var r,t,e,n,o,u,a;return r=Ei,(t=lf())===s&&(t=Fl())===s&&(t=Yc()),t!==s&&sb()!==s?((e=Ec())===s&&(e=null),e!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=Gi())!==s&&sb()!==s&&rb()!==s?(Ai=r,u=e,a=n,"string"==typeof(o=t)&&(o={type:"default",value:o}),o.table&&(o={type:"default",value:o.table}),r=t={name:o,stmt:a,columns:u}):(Ei=r,r=s)):(Ei=r,r=s),r}function Ec(){var r,t;return r=Ei,Jp()!==s&&sb()!==s&&(t=function(){var r;(r=Wc())===s&&(r=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=af())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=sr(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}());return r}())!==s&&sb()!==s&&rb()!==s?(Ai=r,r=t):(Ei=r,r=s),r}function Ac(){var t,e,n,o,u,a,i;if(t=Ei,(e=Dl())!==s)if(sb()!==s)if((n=Jp())!==s)if(sb()!==s){if(o=[],ye.test(r.charAt(Ei))?(u=r.charAt(Ei),Ei++):(u=s,0===gi&&xi(we)),u!==s)for(;u!==s;)o.push(u),ye.test(r.charAt(Ei))?(u=r.charAt(Ei),Ei++):(u=s,0===gi&&xi(we));else o=s;o!==s&&(u=sb())!==s&&rb()!==s&&sb()!==s?((a=Jf())===s&&(a=rp()),a===s&&(a=null),a!==s?(Ai=t,i=a,t=e={type:"column_ref",column:e,suffix:`(${parseInt(o.join(""),10)})`,order_by:i,...Rb()}):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;return t===s&&(t=Ei,(e=Dl())!==s&&sb()!==s?((n=Jf())===s&&(n=rp()),n===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){return{type:"column_ref",column:r,order_by:t,...Rb()}}(e,n)):(Ei=t,t=s)):(Ei=t,t=s)),t}function Tc(){var r,t,e;return r=Ei,Jp()!==s&&sb()!==s?((t=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Ac())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Ac())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Ac())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(t=sl()),t!==s&&sb()!==s&&rb()!==s?(Ai=r,r=(e=t).type?e.value:e):(Ei=r,r=s)):(Ei=r,r=s),r}function _c(){var t,e,n,o;return t=Ei,(e=function(){var t,e,n,o,u,a;return t=Ei,e=Ei,"for"===r.substr(Ei,3).toLowerCase()?(n=r.substr(Ei,3),Ei+=3):(n=s,0===gi&&xi(vr)),n!==s&&(o=sb())!==s&&(u=gf())!==s?e=n=[n,o,u]:(Ei=e,e=s),e!==s&&(Ai=t,e=`${(a=e)[0]} ${a[2][0]}`),t=e}())===s&&(e=function(){var t,e,n,o,u,a,i,c,l,f;return t=Ei,e=Ei,"lock"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(B)),n!==s&&(o=sb())!==s?("in"===r.substr(Ei,2).toLowerCase()?(u=r.substr(Ei,2),Ei+=2):(u=s,0===gi&&xi(he)),u!==s&&(a=sb())!==s?("share"===r.substr(Ei,5).toLowerCase()?(i=r.substr(Ei,5),Ei+=5):(i=s,0===gi&&xi(Le)),i!==s&&(c=sb())!==s?("mode"===r.substr(Ei,4).toLowerCase()?(l=r.substr(Ei,4),Ei+=4):(l=s,0===gi&&xi(Ce)),l!==s?e=n=[n,o,u,a,i,c,l]:(Ei=e,e=s)):(Ei=e,e=s)):(Ei=e,e=s)):(Ei=e,e=s),e!==s&&(Ai=t,e=`${(f=e)[0]} ${f[2]} ${f[4]} ${f[6]}`),t=e}()),e!==s&&sb()!==s?((n=function(){var t,e,n,o,u,a,i;return t=Ei,e=Ei,"wait"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(me)),n!==s&&(o=sb())!==s&&(u=vf())!==s?e=n=[n,o,u]:(Ei=e,e=s),e!==s&&(Ai=t,e=`${(a=e)[0]} ${a[2].value}`),(t=e)===s&&("nowait"===r.substr(Ei,6).toLowerCase()?(t=r.substr(Ei,6),Ei+=6):(t=s,0===gi&&xi(Ee)),t===s&&(t=Ei,e=Ei,"skip"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(Ae)),n!==s&&(o=sb())!==s?("locked"===r.substr(Ei,6).toLowerCase()?(u=r.substr(Ei,6),Ei+=6):(u=s,0===gi&&xi(Te)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s),e!==s&&(Ai=t,e=`${(i=e)[0]} ${i[2]}`),t=e)),t}())===s&&(n=null),n!==s?(Ai=t,t=e=e+((o=n)?" "+o:"")):(Ei=t,t=s)):(Ei=t,t=s),t}function Ic(){var t,e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h;return t=Ei,sb()!==s?((e=Cc())===s&&(e=null),e!==s&&sb()!==s&&If()!==s&&ub()!==s?((n=function(){var r,t,e,n,o,u;if(r=Ei,(t=gc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=gc())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=gc())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(n=null),n!==s&&sb()!==s?((o=ep())===s&&(o=null),o!==s&&sb()!==s&&(u=Sc())!==s&&sb()!==s?((a=xc())===s&&(a=null),a!==s&&sb()!==s?((i=jc())===s&&(i=null),i!==s&&sb()!==s?((c=xc())===s&&(c=null),c!==s&&sb()!==s?((l=Bc())===s&&(l=null),l!==s&&sb()!==s?((f=function(){var t,e,n,o;t=Ei,(e=function(){var t,e,n,o;t=Ei,"group"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(iu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s&&zf()!==s&&sb()!==s&&(n=sl())!==s&&sb()!==s?((o=function(){var t,e;t=Ei,Qf()!==s&&sb()!==s?("rollup"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ye)),e!==s?(Ai=t,t={type:"origin",value:"with rollup"}):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(o=null),o!==s?(Ai=t,u=o,e={columns:n.value,modifiers:[u]},t=e):(Ei=t,t=s)):(Ei=t,t=s);var u;return t}())===s&&(f=null),f!==s&&sb()!==s?((p=function(){var t,e;t=Ei,function(){var t,e,n,o;t=Ei,"having"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(lu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(e=pl())!==s?(Ai=t,t=e):(Ei=t,t=s);return t}())===s&&(p=null),p!==s&&sb()!==s?((b=Vc())===s&&(b=null),b!==s&&sb()!==s?((v=Qi())===s&&(v=null),v!==s&&sb()!==s?((d=Qc())===s&&(d=null),d!==s&&sb()!==s?((y=_c())===s&&(y=null),y!==s&&sb()!==s?((w=function(){var t,e,n;t=Ei,"window"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi($n));e!==s&&sb()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Vl())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Vl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Vl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=Ub(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,t=e={keyword:"window",type:"window",expr:n}):(Ei=t,t=s);return t}())===s&&(w=null),w!==s&&sb()!==s?((h=xc())===s&&(h=null),h!==s?(Ai=t,t=function(r,t,e,n,o,s,u,a,i,c,l,f,p,b,v,d){if(o&&u||o&&d||u&&d||o&&u&&d)throw new Error("A given SQL statement can contain at most one INTO clause");if(s){(Array.isArray(s)?s:s.expr).forEach(r=>r.table&&Hb.add(`select::${r.db}::${r.table}`))}return{with:r,type:"select",options:t,distinct:e,columns:n,into:{...o||u||d||{},position:(o?"column":u&&"from")||d&&"end"},from:s,where:a,groupby:i,having:c,orderby:l,limit:p,locking_read:b&&b,window:v,collate:f,...Rb()}}(e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function gc(){var t,e;return t=Ei,(e=function(){var t;"sql_calc_found_rows"===r.substr(Ei,19).toLowerCase()?(t=r.substr(Ei,19),Ei+=19):(t=s,0===gi&&xi(Wa));return t}())===s&&((e=function(){var t;"sql_cache"===r.substr(Ei,9).toLowerCase()?(t=r.substr(Ei,9),Ei+=9):(t=s,0===gi&&xi(qa));return t}())===s&&(e=function(){var t;"sql_no_cache"===r.substr(Ei,12).toLowerCase()?(t=r.substr(Ei,12),Ei+=12):(t=s,0===gi&&xi(Va));return t}()),e===s&&(e=function(){var t;"sql_big_result"===r.substr(Ei,14).toLowerCase()?(t=r.substr(Ei,14),Ei+=14):(t=s,0===gi&&xi(Ka));return t}())===s&&(e=function(){var t;"sql_small_result"===r.substr(Ei,16).toLowerCase()?(t=r.substr(Ei,16),Ei+=16):(t=s,0===gi&&xi(Xa));return t}())===s&&(e=function(){var t;"sql_buffer_result"===r.substr(Ei,17).toLowerCase()?(t=r.substr(Ei,17),Ei+=17):(t=s,0===gi&&xi(Qa));return t}())),e!==s&&(Ai=t,e=e),t=e}function Sc(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=tp())===s&&(t=Ei,(e=zp())!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=zp())),t!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Oc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Oc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){Yb.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null,...Rb()};return t&&t.length>0?Ub(e,t):[e]}(0,e)):(Ei=r,r=s)}else Ei=r,r=s;if(r===s)if(r=Ei,(t=Oc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Oc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Oc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Nc(){var t,e,n,o,u,a,i;return t=Ei,"match"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Oe)),e!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=Wc())!==s&&sb()!==s&&rb()!==s&&sb()!==s?("AGAINST"===r.substr(Ei,7)?(o="AGAINST",Ei+=7):(o=s,0===gi&&xi(Re)),o!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(u=ll())!==s&&sb()!==s?((a=function(){var t,e,n,o,u,a,i;return t=Ei,op()!==s&&sb()!==s?("natural"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(_e)),e!==s&&sb()!==s?("language"===r.substr(Ei,8).toLowerCase()?(n=r.substr(Ei,8),Ei+=8):(n=s,0===gi&&xi(Ie)),n!==s&&sb()!==s?("mode"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(Ce)),o!==s&&sb()!==s?("with"===r.substr(Ei,4).toLowerCase()?(u=r.substr(Ei,4),Ei+=4):(u=s,0===gi&&xi(v)),u!==s&&sb()!==s?("query"===r.substr(Ei,5).toLowerCase()?(a=r.substr(Ei,5),Ei+=5):(a=s,0===gi&&xi(ge)),a!==s&&sb()!==s?("expansion"===r.substr(Ei,9).toLowerCase()?(i=r.substr(Ei,9),Ei+=9):(i=s,0===gi&&xi(Se)),i!==s?(Ai=t,t={type:"origin",value:"IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,op()!==s&&sb()!==s?("natural"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(_e)),e!==s&&sb()!==s?("language"===r.substr(Ei,8).toLowerCase()?(n=r.substr(Ei,8),Ei+=8):(n=s,0===gi&&xi(Ie)),n!==s&&sb()!==s?("mode"===r.substr(Ei,4).toLowerCase()?(o=r.substr(Ei,4),Ei+=4):(o=s,0===gi&&xi(Ce)),o!==s?(Ai=t,t={type:"origin",value:"IN NATURAL LANGUAGE MODE"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,op()!==s&&sb()!==s?("boolean"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ne)),e!==s&&sb()!==s?("mode"===r.substr(Ei,4).toLowerCase()?(n=r.substr(Ei,4),Ei+=4):(n=s,0===gi&&xi(Ce)),n!==s?(Ai=t,t={type:"origin",value:"IN BOOLEAN MODE"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,Qf()!==s&&sb()!==s?("query"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(ge)),e!==s&&sb()!==s?("expansion"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Se)),n!==s?(Ai=t,t={type:"origin",value:"WITH QUERY EXPANSION"}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)))),t}())===s&&(a=null),a!==s&&sb()!==s&&rb()!==s&&sb()!==s?((i=Rc())===s&&(i=null),i!==s?(Ai=t,t=e={against:"against",columns:n,expr:u,match:"match",mode:a,type:"fulltext_search",as:i}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function Oc(){var r,t,e,n,o,u,a,i;return r=Ei,(t=Nc())!==s&&(Ai=r,t=function(r){const{as:t,...e}=r;return{expr:e,as:t}}(t)),(r=t)===s&&(r=Ei,(t=Rl())!==s&&(e=sb())!==s&&(n=Qp())!==s&&(o=sb())!==s&&(u=Rl())!==s&&sb()!==s&&Qp()!==s&&sb()!==s&&zp()!==s?(Ai=r,a=t,i=u,Yb.add(`select::${a}::${i}::(.*)`),r=t={expr:{type:"column_ref",db:a,table:i,column:"*"},as:null,...Rb()}):(Ei=r,r=s),r===s&&(r=Ei,t=Ei,(e=Rl())!==s&&(n=sb())!==s&&(o=Qp())!==s?t=e=[e,n,o]:(Ei=t,t=s),t===s&&(t=null),t!==s&&(e=sb())!==s&&(n=zp())!==s?(Ai=r,r=t=function(r){const t=r&&r[0]||null;return Yb.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null,...Rb()}}(t)):(Ei=r,r=s),r===s&&(r=Ei,(t=function(){var r,t,e,n;r=Ei,(t=Eb())===s&&(t=Ab());t!==s&&sb()!==s&&(e=Pp())!==s&&sb()!==s&&(n=vb())!==s?(Ai=r,t=pi(t,e,n),r=t):(Ei=r,r=s);return r}())!==s&&(e=sb())!==s?((n=Rc())===s&&(n=null),n!==s?(Ai=r,r=t={expr:t,as:n}):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=ll())!==s){for(e=[],n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp())===s&&(u=ob()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp())===s&&(u=ob()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=jb(t[e][1],n,o)}return o}(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s&&(e=sb())!==s?((n=Rc())===s&&(n=null),n!==s?(Ai=r,r=t=function(r,t){return{expr:r,as:t}}(t,n)):(Ei=r,r=s)):(Ei=r,r=s))))),r}function Rc(){var r,t,e;return r=Ei,(t=Gf())!==s&&sb()!==s&&(e=function(){var r,t;r=Ei,(t=Fl())!==s?(Ai=Ei,(function(r){if(!0===Nb[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?s:void 0)!==s?(Ai=r,r=t=t):(Ei=r,r=s)):(Ei=r,r=s);r===s&&(r=Ei,(t=jl())!==s&&(Ai=r,t=t),r=t);return r}())!==s?(Ai=r,r=t=e):(Ei=r,r=s),r===s&&(r=Ei,(t=Gf())===s&&(t=null),t!==s&&sb()!==s&&(e=Rl())!==s?(Ai=r,r=t=e):(Ei=r,r=s)),r}function xc(){var t,e,n;return t=Ei,Mf()!==s&&sb()!==s&&(e=function(){var r,t,e,n,o,u,a,c;if(r=Ei,(t=Eb())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=Eb())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(c=Eb())!==s?n=o=[o,u,a,c]:(Ei=n,n=s);e!==s?(Ai=r,t=i(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,t={keyword:"var",type:"into",expr:e}):(Ei=t,t=s),t===s&&(t=Ei,Mf()!==s&&sb()!==s?("outfile"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(xe)),e===s&&("dumpfile"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(je))),e===s&&(e=null),e!==s&&sb()!==s?((n=lf())===s&&(n=Rl()),n!==s?(Ai=t,t={keyword:e,type:"into",expr:n}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)),t}function jc(){var r,t;return r=Ei,Df()!==s&&sb()!==s&&(t=Pc())!==s?(Ai=r,r=t):(Ei=r,r=s),r}function kc(){var r,t,e;return r=Ei,(t=Yc())!==s&&sb()!==s&&Ef()!==s&&sb()!==s&&(e=Yc())!==s?(Ai=r,r=t=[t,e]):(Ei=r,r=s),r}function Uc(){var t,e;return t=Ei,Kf()!==s&&sb()!==s?("btree"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(ke)),e===s&&("hash"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Ue))),e!==s?(Ai=t,t={keyword:"using",type:e.toLowerCase()}):(Ei=t,t=s)):(Ei=t,t=s),t}function Mc(){var r,t,e,n,o,u;if(r=Ei,(t=Dc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Dc())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Dc())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Dc(){var t,e,n,o,u,a;return t=Ei,(e=function(){var t,e,n,o;t=Ei,"key_block_size"===r.substr(Ei,14).toLowerCase()?(e=r.substr(Ei,14),Ei+=14):(e=s,0===gi&&xi(dt));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="KEY_BLOCK_SIZE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&sb()!==s?((n=Gp())===s&&(n=null),n!==s&&sb()!==s&&(o=vf())!==s?(Ai=t,u=n,a=o,t=e={type:e.toLowerCase(),symbol:u,expr:a}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Uc())===s&&(t=Ei,"with"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(v)),e!==s&&sb()!==s?("parser"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi(Me)),n!==s&&sb()!==s&&(o=Fl())!==s?(Ai=t,t=e={type:"with parser",expr:o}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"visible"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(De)),e===s&&("invisible"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Pe))),e!==s&&(Ai=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===s&&(t=ib()))),t}function Pc(){var r,t,e,n,o,u,a,i,c,l,f,p;if(r=Ei,(t=Fc())!==s){for(e=[],n=Gc();n!==s;)e.push(n),n=Gc();e!==s?(Ai=r,f=t,(p=e).unshift(f),p.forEach(r=>{const{table:t,as:e}=r;$b[t]=t,e&&($b[e]=t),Gb(Yb)}),r=t=p):(Ei=r,r=s)}else Ei=r,r=s;if(r===s){if(r=Ei,t=[],(e=Jp())!==s)for(;e!==s;)t.push(e),e=Jp();else t=s;if(t!==s)if((e=sb())!==s)if((n=Fc())!==s){for(o=[],u=Gc();u!==s;)o.push(u),u=Gc();if(o!==s)if((u=sb())!==s){if(a=[],(i=rb())!==s)for(;i!==s;)a.push(i),i=rb();else a=s;if(a!==s)if((i=sb())!==s){for(c=[],l=Gc();l!==s;)c.push(l),l=Gc();c!==s?(Ai=r,r=t=function(r,t,e,n,o){if(r.length!==n.length)throw new Error(`parentheses not match in from clause: ${r.length} != ${n.length}`);return e.unshift(t),e.forEach(r=>{const{table:t,as:e}=r;$b[t]=t,e&&($b[e]=t),Gb(Yb)}),o.forEach(r=>{const{table:t,as:e}=r;$b[t]=t,e&&($b[e]=t),Gb(Yb)}),{expr:e,parentheses:{length:n.length},joins:o}}(t,n,o,a,c)):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s}else Ei=r,r=s;else Ei=r,r=s}else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s}return r}function Gc(){var r,t,e;return r=Ei,sb()!==s&&(t=Zp())!==s&&sb()!==s&&(e=Fc())!==s?(Ai=r,r=e):(Ei=r,r=s),r===s&&(r=Ei,sb()!==s&&(t=function(){var r,t,e,n,o,u,a,i,c,l,f;if(r=Ei,(t=Hc())!==s)if(sb()!==s)if((e=Fc())!==s)if(sb()!==s)if((n=Kf())!==s)if(sb()!==s)if(Jp()!==s)if(sb()!==s)if((o=Nl())!==s){for(u=[],a=Ei,(i=sb())!==s&&(c=Zp())!==s&&(l=sb())!==s&&(f=Nl())!==s?a=i=[i,c,l,f]:(Ei=a,a=s);a!==s;)u.push(a),a=Ei,(i=sb())!==s&&(c=Zp())!==s&&(l=sb())!==s&&(f=Nl())!==s?a=i=[i,c,l,f]:(Ei=a,a=s);u!==s&&(a=sb())!==s&&(i=rb())!==s?(Ai=r,p=t,v=o,d=u,(b=e).join=p,b.using=Ub(v,d),r=t=b):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;var p,b,v,d;r===s&&(r=Ei,(t=Hc())!==s&&sb()!==s&&(e=Fc())!==s&&sb()!==s?((n=$c())===s&&(n=null),n!==s?(Ai=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Hc())===s&&(t=Pi()),t!==s&&sb()!==s&&(e=Jp())!==s&&sb()!==s&&(n=Gi())!==s&&sb()!==s&&rb()!==s&&sb()!==s?((o=Rc())===s&&(o=null),o!==s&&(u=sb())!==s?((a=$c())===s&&(a=null),a!==s?(Ai=r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,a),r=t):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)));return r}())!==s?(Ai=r,r=t):(Ei=r,r=s)),r}function Fc(){var t,e,n,o,u,a,i;return t=Ei,(e=function(){var t;"dual"===r.substr(Ei,4).toLowerCase()?(t=r.substr(Ei,4),Ei+=4):(t=s,0===gi&&xi(Da));return t}())!==s&&(Ai=t,e={type:"dual"}),(t=e)===s&&(t=Ei,(e=Yc())!==s&&sb()!==s?((n=Rc())===s&&(n=null),n!==s?(Ai=t,i=n,t=e="var"===(a=e).type?(a.as=i,a):{db:a.db,table:a.table,as:i,...Rb()}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Jp())!==s&&sb()!==s&&(n=Yc())!==s&&sb()!==s?((o=Rc())===s&&(o=null),o!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e=function(r,t,e){return"var"===r.type?(r.as=t,r.parentheses=!0,r):{db:r.db,table:r.table,as:t,parentheses:!0}}(n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=nl())!==s&&sb()!==s?((n=Rc())===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){return{expr:{type:"values",values:r,prefix:"row"},as:t}}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,"lateral"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ge)),e===s&&(e=null),e!==s&&sb()!==s&&(n=Jp())!==s&&sb()!==s?((o=Gi())===s&&(o=nl()),o!==s&&sb()!==s&&rb()!==s&&sb()!==s?((u=Rc())===s&&(u=null),u!==s?(Ai=t,t=e=function(r,t,e){Array.isArray(t)&&(t={type:"values",values:t,prefix:"row"}),t.parentheses=!0;const n={expr:t,as:e};return r&&(n.prefix=r),n}(e,o,u)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s))))),t}function Hc(){var t,e,n,o;return t=Ei,(e=function(){var t,e,n,o;t=Ei,"left"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Xs));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(n=sb())!==s?((o=Vf())===s&&(o=null),o!==s&&sb()!==s&&qf()!==s?(Ai=t,t=e="LEFT JOIN"):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"right"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Ks));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(n=sb())!==s?((o=Vf())===s&&(o=null),o!==s&&sb()!==s&&qf()!==s?(Ai=t,t=e="RIGHT JOIN"):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"full"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Qs));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(n=sb())!==s?((o=Vf())===s&&(o=null),o!==s&&sb()!==s&&qf()!==s?(Ai=t,t=e="FULL JOIN"):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"cross"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(zs));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(n=sb())!==s&&(o=qf())!==s?(Ai=t,t=e="CROSS JOIN"):(Ei=t,t=s),t===s&&(t=Ei,e=Ei,(n=function(){var t,e,n,o;t=Ei,"inner"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Zs));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(o=sb())!==s?e=n=[n,o]:(Ei=e,e=s),e===s&&(e=null),e!==s&&(n=qf())!==s?(Ai=t,t=e="INNER JOIN"):(Ei=t,t=s))))),t}function Yc(){var t,e,n,o,u,a,i,c,l;if(t=Ei,e=[],Fe.test(r.charAt(Ei))?(n=r.charAt(Ei),Ei++):(n=s,0===gi&&xi(He)),n!==s)for(;n!==s;)e.push(n),Fe.test(r.charAt(Ei))?(n=r.charAt(Ei),Ei++):(n=s,0===gi&&xi(He));else e=s;return e!==s&&(n=Ol())!==s?(o=Ei,(u=sb())!==s&&(a=Qp())!==s&&(i=sb())!==s&&(c=Ol())!==s?o=u=[u,a,i,c]:(Ei=o,o=s),o===s&&(o=null),o!==s?(Ai=t,t=e=function(r,t,e){const n=`${r.join("")}${t}`,o={db:null,table:n};return null!==e&&(o.db=n,o.table=e[3]),o}(e,n,o)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Rl())!==s?(n=Ei,(o=sb())!==s&&(u=Qp())!==s&&(a=sb())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s),n===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=Eb())!==s&&(Ai=t,(l=e).db=null,l.table=l.name,e=l),t=e)),t}function $c(){var r,t;return r=Ei,Wf()!==s&&sb()!==s&&(t=fl())!==s?(Ai=r,r=t):(Ei=r,r=s),r}function Bc(){var t,e;return t=Ei,function(){var t,e,n,o;t=Ei,"where"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(uu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(e=pl())!==s?(Ai=t,t=e):(Ei=t,t=s),t}function Wc(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Il())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Il())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Il())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function qc(){var r,t;return r=Ei,Uf()!==s&&sb()!==s&&zf()!==s&&sb()!==s&&(t=Sc())!==s?(Ai=r,r=t):(Ei=r,r=s),r}function Vc(){var t,e;return t=Ei,function(){var t,e,n,o;t=Ei,"order"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(cu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&zf()!==s&&sb()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Xc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Xc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=sr(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=t,t=e):(Ei=t,t=s),t}function Xc(){var r,t,e;return r=Ei,(t=ll())!==s&&sb()!==s?((e=rp())===s&&(e=Jf()),e===s&&(e=null),e!==s?(Ai=r,r=t={expr:t,type:e}):(Ei=r,r=s)):(Ei=r,r=s),r}function Kc(){var t,e;return(t=vf())===s&&(t=Bl())===s&&(t=Ei,63===r.charCodeAt(Ei)?(e="?",Ei++):(e=s,0===gi&&xi($e)),e!==s&&(Ai=t,e={type:"origin",value:"?"}),t=e),t}function Qc(){var t,e,n,o,u,a;return t=Ei,function(){var t,e,n,o;t=Ei,"limit"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(fu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(e=Kc())!==s&&sb()!==s?(n=Ei,(o=Zp())===s&&(o=function(){var t,e,n,o;t=Ei,"offset"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(pu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="OFFSET"):(Ei=t,t=s)):(Ei=t,t=s);return t}()),o!==s&&(u=sb())!==s&&(a=Kc())!==s?n=o=[o,u,a]:(Ei=n,n=s),n===s&&(n=null),n!==s?(Ai=t,t=function(r,t){const e=[r];return t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e,...Rb()}}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t}function Zc(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=zc())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=zc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=zc())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function zc(){var t,e,n,o,u,a,i,c,l;return t=Ei,e=Ei,(n=Rl())!==s&&(o=sb())!==s&&(u=Qp())!==s?e=n=[n,o,u]:(Ei=e,e=s),e===s&&(e=null),e!==s&&(n=sb())!==s&&(o=Dl())!==s&&(u=sb())!==s?(61===r.charCodeAt(Ei)?(a="=",Ei++):(a=s,0===gi&&xi(Be)),a!==s&&sb()!==s&&(i=Cl())!==s?(Ai=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,e=Ei,(n=Rl())!==s&&(o=sb())!==s&&(u=Qp())!==s?e=n=[n,o,u]:(Ei=e,e=s),e===s&&(e=null),e!==s&&(n=sb())!==s&&(o=Dl())!==s&&(u=sb())!==s?(61===r.charCodeAt(Ei)?(a="=",Ei++):(a=s,0===gi&&xi(Be)),a!==s&&sb()!==s&&(i=Xf())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(c=Il())!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Ei=t,t=s)):(Ei=t,t=s)),t}function Jc(){var r;return(r=nl())===s&&(r=Ic()),r}function rl(){var r,t,e,n,o,u,a,i,c;if(r=Ei,Uf()!==s)if(sb()!==s)if((t=Jp())!==s)if(sb()!==s)if((e=Fl())!==s){for(n=[],o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=Fl())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,(u=sb())!==s&&(a=Zp())!==s&&(i=sb())!==s&&(c=Fl())!==s?o=u=[u,a,i,c]:(Ei=o,o=s);n!==s&&(o=sb())!==s&&(u=rb())!==s?(Ai=r,r=L(e,n)):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;else Ei=r,r=s;return r===s&&(r=Ei,Uf()!==s&&sb()!==s&&(t=ol())!==s?(Ai=r,r=t):(Ei=r,r=s)),r}function tl(){var t,e,n;return t=Ei,Wf()!==s&&sb()!==s?("duplicate"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(We)),e!==s&&sb()!==s&&$p()!==s&&sb()!==s&&gf()!==s&&sb()!==s&&(n=Zc())!==s?(Ai=t,t={keyword:"on duplicate key update",set:n}):(Ei=t,t=s)):(Ei=t,t=s),t}function el(){var r,t;return r=Ei,(t=Rf())!==s&&(Ai=r,t="insert"),(r=t)===s&&(r=Ei,(t=xf())!==s&&(Ai=r,t="replace"),r=t),r}function nl(){var r,t;return r=Ei,Xf()!==s&&sb()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=ol())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=ol())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=ol())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=sr(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())!==s?(Ai=r,r=t):(Ei=r,r=s),r}function ol(){var t,e,n;return t=Ei,"row"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(yr)),e===s&&(e=null),e!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=sl())!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e=n):(Ei=t,t=s),t}function sl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=ll())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Ub(r,t),e}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function ul(){var t,e,n;return t=Ei,xp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&(n=function(){var t;(t=function(){var t,e,n,o;t=Ei,"year"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(So));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="YEAR"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"quarter"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ao));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="QUARTER"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"month"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Eo));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MONTH"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"week"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(go));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="WEEK"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"day"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(ao));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DAY"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"hour"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(bo));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="HOUR"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"minute"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(mo));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MINUTE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"second"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(To));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"microsecond"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(da));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MICROSECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"second_microsecond"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(no));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SECOND_MICROSECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"minute_microsecond"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(eo));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MINUTE_MICROSECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"minute_second"===r.substr(Ei,13).toLowerCase()?(e=r.substr(Ei,13),Ei+=13):(e=s,0===gi&&xi(to));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MINUTE_SECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"hour_microsecond"===r.substr(Ei,16).toLowerCase()?(e=r.substr(Ei,16),Ei+=16):(e=s,0===gi&&xi(ro));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="HOUR_MICROSECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"hour_second"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(Jn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="HOUR_SECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"hour_minute"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(zn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="HOUR_MINUTE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"day_microsecond"===r.substr(Ei,15).toLowerCase()?(e=r.substr(Ei,15),Ei+=15):(e=s,0===gi&&xi(Zn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DAY_MICROSECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"day_second"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Qn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DAY_SECOND"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"day_minute"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Kn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DAY_MINUTE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"day_hour"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Xn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DAY_HOUR"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"year_month"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Vn));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="YEAR_MONTH"):(Ei=t,t=s)):(Ei=t,t=s);return t}());return t}())!==s?(Ai=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(Ei=t,t=s),t}function al(){var r,t,e,n,o,u;if(r=Ei,(t=il())!==s)if(sb()!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=il())!==s?n=o=[o,u]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=il())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s?(Ai=r,r=t=l(t,e)):(Ei=r,r=s)}else Ei=r,r=s;else Ei=r,r=s;return r}function il(){var t,e,n;return t=Ei,function(){var t,e,n,o;t=Ei,"when"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Uu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(e=pl())!==s&&sb()!==s&&function(){var t,e,n,o;t=Ei,"then"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Mu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(n=ll())!==s?(Ai=t,t={type:"when",cond:e,result:n}):(Ei=t,t=s),t}function cl(){var t,e;return t=Ei,function(){var t,e,n,o;t=Ei,"else"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Du));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}()!==s&&sb()!==s&&(e=ll())!==s?(Ai=t,t={type:"else",result:e}):(Ei=t,t=s),t}function ll(){var r;return(r=function(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=bl())!==s){for(e=[],n=Ei,(o=ub())!==s&&(u=lp())!==s&&(a=sb())!==s&&(i=bl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=ub())!==s&&(u=lp())!==s&&(a=sb())!==s&&(i=bl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,t=qe(t,e),r=t):(Ei=r,r=s)}else Ei=r,r=s;return r}())===s&&(r=Gi()),r}function fl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=ll())!==s){for(e=[],n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=jb(t[r][1],n,t[r][3]);return n}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function pl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=ll())!==s){for(e=[],n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp())===s&&(u=Zp()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=cp())===s&&(u=lp())===s&&(u=Zp()),u!==s&&(a=sb())!==s&&(i=ll())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=jb(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=Array.isArray(n)?n:[n],r}return n}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function bl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=vl())!==s){for(e=[],n=Ei,(o=ub())!==s&&(u=cp())!==s&&(a=sb())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=ub())!==s&&(u=cp())!==s&&(a=sb())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=Mb(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function vl(){var r,t;return(r=dl())===s&&(r=function(){var r,t,e;r=Ei,(t=function(){var r,t,e,n,o;r=Ei,t=Ei,(e=ip())!==s&&(n=sb())!==s&&(o=ap())!==s?t=e=[e,n,o]:(Ei=t,t=s);t!==s&&(Ai=r,t=Ve(t));(r=t)===s&&(r=ap());return r}())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=Gi())!==s&&sb()!==s&&rb()!==s?(Ai=r,n=t,(o=e).parentheses=!0,t=xb(n,o),r=t):(Ei=r,r=s);var n,o;return r}())===s&&(r=Ei,ip()!==s&&sb()!==s&&(t=vl())!==s?(Ai=r,r=xb("NOT",t)):(Ei=r,r=s)),r}function dl(){var t,e,n,o,u;return t=Ei,(e=Cl())!==s&&sb()!==s?((n=function(){var t;(t=function(){var r,t,e,n,o,u,a;r=Ei,t=[],e=Ei,(n=sb())!==s&&(o=yl())!==s&&(u=sb())!==s&&(a=Cl())!==s?e=n=[n,o,u,a]:(Ei=e,e=s);if(e!==s)for(;e!==s;)t.push(e),e=Ei,(n=sb())!==s&&(o=yl())!==s&&(u=sb())!==s&&(a=Cl())!==s?e=n=[n,o,u,a]:(Ei=e,e=s);else t=s;t!==s&&(Ai=r,t={type:"arithmetic",tail:t});return r=t}())===s&&(t=Ll())===s&&(t=function(){var r,t,e,n;r=Ei,(t=function(){var r,t,e,n,o;r=Ei,t=Ei,(e=ip())!==s&&(n=sb())!==s&&(o=np())!==s?t=e=[e,n,o]:(Ei=t,t=s);t!==s&&(Ai=r,t=Ve(t));(r=t)===s&&(r=np());return r}())!==s&&sb()!==s&&(e=Cl())!==s&&sb()!==s&&cp()!==s&&sb()!==s&&(n=Cl())!==s?(Ai=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Ei=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o;r=Ei,(t=sp())!==s&&(e=sb())!==s&&(n=Cl())!==s?(Ai=r,r=t={op:"IS",right:n}):(Ei=r,r=s);r===s&&(r=Ei,t=Ei,(e=sp())!==s&&(n=sb())!==s&&(o=ip())!==s?t=e=[e,n,o]:(Ei=t,t=s),t!==s&&(e=sb())!==s&&(n=Cl())!==s?(Ai=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Ei=r,r=s));return r}())===s&&(t=hl())===s&&(t=function(){var t,e,n,o;t=Ei,(e=function(){var t,e,n;t=Ei,(e=ip())===s&&(e=null);e!==s&&sb()!==s?((n=function(){var t,e,n,o;t=Ei,"regexp"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Eu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="REGEXP"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=Ei,"rlike"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(mu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="RLIKE"):(Ei=t,t=s)):(Ei=t,t=s);return t}()),n!==s?(Ai=t,u=n,t=e=(o=e)?`${o} ${u}`:u):(Ei=t,t=s)):(Ei=t,t=s);var o,u;return t}())!==s&&sb()!==s?("binary"===r.substr(Ei,6).toLowerCase()?(n=r.substr(Ei,6),Ei+=6):(n=s,0===gi&&xi($t)),n===s&&(n=null),n!==s&&sb()!==s?((o=nf())===s&&(o=lf())===s&&(o=Il()),o!==s?(Ai=t,u=e,t=e={op:(a=n)?`${u} ${a}`:u,right:o}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s);var u,a;return t}());return t}())===s&&(n=null),n!==s?(Ai=t,o=e,t=e=null===(u=n)?o:"arithmetic"===u.type?Mb(o,u.tail):jb(u.op,o,u.right)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=lf())===s&&(t=Il()),t}function yl(){var t;return">="===r.substr(Ei,2)?(t=">=",Ei+=2):(t=s,0===gi&&xi(Xe)),t===s&&(62===r.charCodeAt(Ei)?(t=">",Ei++):(t=s,0===gi&&xi(Ke)),t===s&&("<="===r.substr(Ei,2)?(t="<=",Ei+=2):(t=s,0===gi&&xi(Qe)),t===s&&("<>"===r.substr(Ei,2)?(t="<>",Ei+=2):(t=s,0===gi&&xi(Ze)),t===s&&(60===r.charCodeAt(Ei)?(t="<",Ei++):(t=s,0===gi&&xi(ze)),t===s&&(61===r.charCodeAt(Ei)?(t="=",Ei++):(t=s,0===gi&&xi(Be)),t===s&&("!="===r.substr(Ei,2)?(t="!=",Ei+=2):(t=s,0===gi&&xi(Je)))))))),t}function wl(){var r,t,e,n,o;return r=Ei,t=Ei,(e=ip())!==s&&(n=sb())!==s&&(o=op())!==s?t=e=[e,n,o]:(Ei=t,t=s),t!==s&&(Ai=r,t=Ve(t)),(r=t)===s&&(r=op()),r}function hl(){var t,e,n,o,u,a,i;return t=Ei,(e=function(){var r,t,e,n,o;return r=Ei,t=Ei,(e=ip())!==s&&(n=sb())!==s&&(o=up())!==s?t=e=[e,n,o]:(Ei=t,t=s),t!==s&&(Ai=r,t=Ve(t)),(r=t)===s&&(r=up()),r}())!==s&&sb()!==s?((n=af())===s&&(n=Bl())===s&&(n=dl()),n!==s&&sb()!==s?((o=function(){var t,e,n;return t=Ei,"escape"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(rn)),e!==s&&sb()!==s&&(n=lf())!==s?(Ai=t,t=e={type:"ESCAPE",value:n}):(Ei=t,t=s),t}())===s&&(o=null),o!==s?(Ai=t,u=e,a=n,(i=o)&&(a.escape=i),t=e={op:u,right:a}):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t}function Ll(){var r,t,e,n;return r=Ei,(t=wl())!==s&&sb()!==s&&(e=Jp())!==s&&sb()!==s&&(n=sl())!==s&&sb()!==s&&rb()!==s?(Ai=r,r=t={op:t,right:n}):(Ei=r,r=s),r===s&&(r=Ei,(t=wl())!==s&&sb()!==s?((e=Eb())===s&&(e=Il())===s&&(e=lf()),e!==s?(Ai=r,r=t=function(r,t){return{op:r,right:t}}(t,e)):(Ei=r,r=s)):(Ei=r,r=s)),r}function Cl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=El())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=ml())!==s&&(a=sb())!==s&&(i=El())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=ml())!==s&&(a=sb())!==s&&(i=El())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Rb()}));return Mb(r,t)}(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function ml(){var t;return 43===r.charCodeAt(Ei)?(t="+",Ei++):(t=s,0===gi&&xi(tn)),t===s&&(45===r.charCodeAt(Ei)?(t="-",Ei++):(t=s,0===gi&&xi(en))),t}function El(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Tl())!==s){for(e=[],n=Ei,(o=sb())!==s?((u=Al())===s&&(u=ob()),u!==s&&(a=sb())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s?((u=Al())===s&&(u=ob()),u!==s&&(a=sb())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s)):(Ei=n,n=s);e!==s?(Ai=r,r=t=Mb(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Al(){var t,e;return 42===r.charCodeAt(Ei)?(t="*",Ei++):(t=s,0===gi&&xi(nn)),t===s&&(47===r.charCodeAt(Ei)?(t="/",Ei++):(t=s,0===gi&&xi(on)),t===s&&(37===r.charCodeAt(Ei)?(t="%",Ei++):(t=s,0===gi&&xi(sn)),t===s&&("||"===r.substr(Ei,2)?(t="||",Ei+=2):(t=s,0===gi&&xi(un)),t===s&&(t=Ei,"div"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(an)),e===s&&("mod"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(cn))),e!==s&&(Ai=t,e=e.toUpperCase()),(t=e)===s&&(38===r.charCodeAt(Ei)?(t="&",Ei++):(t=s,0===gi&&xi(ln)),t===s&&(">>"===r.substr(Ei,2)?(t=">>",Ei+=2):(t=s,0===gi&&xi(fn)),t===s&&("<<"===r.substr(Ei,2)?(t="<<",Ei+=2):(t=s,0===gi&&xi(pn)),t===s&&(94===r.charCodeAt(Ei)?(t="^",Ei++):(t=s,0===gi&&xi(bn)),t===s&&(124===r.charCodeAt(Ei)?(t="|",Ei++):(t=s,0===gi&&xi(vn))))))))))),t}function Tl(){var t,e,n,o,u;return(t=function(){var t,e,n,o,u,a,i,c;if(t=Ei,(e=_l())!==s)if(sb()!==s){for(n=[],o=Ei,(u=sb())!==s?("?|"===r.substr(Ei,2)?(a="?|",Ei+=2):(a=s,0===gi&&xi(wn)),a===s&&("?&"===r.substr(Ei,2)?(a="?&",Ei+=2):(a=s,0===gi&&xi(hn)),a===s&&(63===r.charCodeAt(Ei)?(a="?",Ei++):(a=s,0===gi&&xi($e)),a===s&&("#-"===r.substr(Ei,2)?(a="#-",Ei+=2):(a=s,0===gi&&xi(Ln)),a===s&&("#>>"===r.substr(Ei,3)?(a="#>>",Ei+=3):(a=s,0===gi&&xi(Cn)),a===s&&("#>"===r.substr(Ei,2)?(a="#>",Ei+=2):(a=s,0===gi&&xi(mn)),a===s&&(a=nb())===s&&(a=eb())===s&&("@>"===r.substr(Ei,2)?(a="@>",Ei+=2):(a=s,0===gi&&xi(En)),a===s&&("<@"===r.substr(Ei,2)?(a="<@",Ei+=2):(a=s,0===gi&&xi(An))))))))),a!==s&&(i=sb())!==s&&(c=_l())!==s?o=u=[u,a,i,c]:(Ei=o,o=s)):(Ei=o,o=s);o!==s;)n.push(o),o=Ei,(u=sb())!==s?("?|"===r.substr(Ei,2)?(a="?|",Ei+=2):(a=s,0===gi&&xi(wn)),a===s&&("?&"===r.substr(Ei,2)?(a="?&",Ei+=2):(a=s,0===gi&&xi(hn)),a===s&&(63===r.charCodeAt(Ei)?(a="?",Ei++):(a=s,0===gi&&xi($e)),a===s&&("#-"===r.substr(Ei,2)?(a="#-",Ei+=2):(a=s,0===gi&&xi(Ln)),a===s&&("#>>"===r.substr(Ei,3)?(a="#>>",Ei+=3):(a=s,0===gi&&xi(Cn)),a===s&&("#>"===r.substr(Ei,2)?(a="#>",Ei+=2):(a=s,0===gi&&xi(mn)),a===s&&(a=nb())===s&&(a=eb())===s&&("@>"===r.substr(Ei,2)?(a="@>",Ei+=2):(a=s,0===gi&&xi(En)),a===s&&("<@"===r.substr(Ei,2)?(a="<@",Ei+=2):(a=s,0===gi&&xi(An))))))))),a!==s&&(i=sb())!==s&&(c=_l())!==s?o=u=[u,a,i,c]:(Ei=o,o=s)):(Ei=o,o=s);n!==s?(Ai=t,l=e,e=(f=n)&&0!==f.length?Mb(l,f):l,t=e):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;var l,f;return t}())===s&&(t=Ei,(e=function(){var t;33===r.charCodeAt(Ei)?(t="!",Ei++):(t=s,0===gi&&xi(dn));t===s&&(45===r.charCodeAt(Ei)?(t="-",Ei++):(t=s,0===gi&&xi(en)),t===s&&(43===r.charCodeAt(Ei)?(t="+",Ei++):(t=s,0===gi&&xi(tn)),t===s&&(126===r.charCodeAt(Ei)?(t="~",Ei++):(t=s,0===gi&&xi(yn)))));return t}())!==s?(n=Ei,(o=sb())!==s&&(u=Tl())!==s?n=o=[o,u]:(Ei=n,n=s),n!==s?(Ai=t,t=e=xb(e,n[1])):(Ei=t,t=s)):(Ei=t,t=s)),t}function _l(){var t,e,n,o;return(t=function(){var t;(t=function(){var t,e,n,o;t=Ei,(e=function(){var t,e,n,o;t=Ei,"count"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Iu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="COUNT"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=Ei,"group_concat"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(gu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="GROUP_CONCAT"):(Ei=t,t=s)):(Ei=t,t=s);return t}());e!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=function(){var t,e,n,o,u;t=Ei,(e=function(){var t,e;t=Ei,42===r.charCodeAt(Ei)?(e="*",Ei++):(e=s,0===gi&&xi(nn));e!==s&&(Ai=t,e={type:"star",value:"*"});return t=e}())!==s&&(Ai=t,e={expr:e,...Rb()});(t=e)===s&&(t=Ei,(e=ep())===s&&(e=null),e!==s&&sb()!==s&&(n=pl())!==s&&sb()!==s?((o=Vc())===s&&(o=null),o!==s&&sb()!==s?((u=function(){var t,e,n;t=Ei,"separator"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(qn));e===s&&(e=null);e!==s&&sb()!==s&&(n=lf())!==s?(Ai=t,t=e={keyword:e,value:n}):(Ei=t,t=s);return t}())===s&&(u=null),u!==s?(Ai=t,e={distinct:e,expr:n,orderby:o,separator:u,...Rb()},t=e):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s));return t}())!==s&&sb()!==s&&rb()!==s&&sb()!==s?((o=ql())===s&&(o=null),o!==s?(Ai=t,e={type:"aggr_func",name:e,args:n,over:o,...Rb()},t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,(e=function(){var t;(t=function(){var t,e,n,o;t=Ei,"sum"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Ou));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SUM"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"max"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Su));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MAX"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"min"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Nu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MIN"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"avg"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Ru));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="AVG"):(Ei=t,t=s)):(Ei=t,t=s);return t}());return t}())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=ll())!==s&&sb()!==s&&rb()!==s&&sb()!==s?((o=ql())===s&&(o=null),o!==s?(Ai=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...Rb()},t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}());return t}())===s&&(t=Nc())===s&&(t=nf())===s&&(t=function(){var r,t,e,n,o,u,a;r=Ei,(t=vp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(n=Ib())!==s&&sb()!==s&&(o=lc())!==s&&sb()!==s&&(u=Nl())!==s&&sb()!==s&&rb()!==s?(Ai=r,t=function(r,t,e,n,o){const{dataType:s,length:u}=e;let a=s;return void 0!==u&&(a=`${a}(${u})`),{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:a,suffix:[{type:"origin",value:n},o]}]}}(t,e,n,o,u),r=t):(Ei=r,r=s);r===s&&(r=Ei,(t=vp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(n=Tb())!==s&&sb()!==s&&(o=rb())!==s?(Ai=r,i=e,c=n,t={type:"cast",keyword:t.toLowerCase(),expr:i,symbol:"as",target:[c]},r=t):(Ei=r,r=s),r===s&&(r=Ei,(t=vp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(n=wp())!==s&&sb()!==s&&(o=Jp())!==s&&sb()!==s&&(u=df())!==s&&sb()!==s&&rb()!==s&&sb()!==s&&(a=rb())!==s?(Ai=r,t=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(t,e,u),r=t):(Ei=r,r=s),r===s&&(r=Ei,(t=vp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(n=wp())!==s&&sb()!==s&&(o=Jp())!==s&&sb()!==s&&(u=df())!==s&&sb()!==s&&Zp()!==s&&sb()!==s&&(a=df())!==s&&sb()!==s&&rb()!==s&&sb()!==s&&rb()!==s?(Ai=r,t=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(t,e,u,a),r=t):(Ei=r,r=s),r===s&&(r=Ei,(t=vp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(e=ll())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(n=sf())!==s&&sb()!==s?((o=Lp())===s&&(o=null),o!==s&&sb()!==s&&(u=rb())!==s?(Ai=r,t=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:[e,n].filter(Boolean).join(" ")}]}}(t,e,n,o),r=t):(Ei=r,r=s)):(Ei=r,r=s)))));var i,c;return r}())===s&&(t=function(){var r,t,e,n,o,u,a,i;return r=Ei,pp()!==s&&sb()!==s&&(t=al())!==s&&sb()!==s?((e=cl())===s&&(e=null),e!==s&&sb()!==s&&(n=bp())!==s&&sb()!==s?((o=pp())===s&&(o=null),o!==s?(Ai=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,pp()!==s&&sb()!==s&&(t=ll())!==s&&sb()!==s&&(e=al())!==s&&sb()!==s?((n=cl())===s&&(n=null),n!==s&&sb()!==s&&(o=bp())!==s&&sb()!==s?((u=pp())===s&&(u=null),u!==s?(Ai=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s)),r}())===s&&(t=ul())===s&&(t=uf())===s&&(t=Il())===s&&(t=vf())===s&&(t=Bl())===s&&(t=Ei,Jp()!==s&&(e=sb())!==s&&(n=pl())!==s&&sb()!==s&&rb()!==s?(Ai=t,(o=n).parentheses=!0,t=o):(Ei=t,t=s),t===s&&(t=Eb())===s&&(t=Ei,sb()!==s?(63===r.charCodeAt(Ei)?(e="?",Ei++):(e=s,0===gi&&xi($e)),e!==s?(Ai=t,t={type:"origin",value:e}):(Ei=t,t=s)):(Ei=t,t=s))),t}function Il(){var r,t,e,n,o,u,a,i,c,l,f,p,b,v,d,y,w;return r=Ei,(t=Fl())===s&&(t=Ml()),t!==s&&(e=sb())!==s&&(n=Qp())!==s&&(o=sb())!==s?((u=Fl())===s&&(u=Ml()),u!==s&&(a=sb())!==s&&(i=Qp())!==s&&(c=sb())!==s&&(l=Dl())!==s?(f=Ei,(p=sb())!==s&&(b=Qi())!==s?f=p=[p,b]:(Ei=f,f=s),f===s&&(f=null),f!==s?(Ai=r,v=t,d=u,y=l,w=f,Yb.add(`select::${"object"==typeof v?v.value:v}::${"object"==typeof d?d.value:d}::${y}`),r=t={type:"column_ref",db:v,table:d,column:y,collate:w&&w[1],...Rb()}):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Fl())===s&&(t=Ml()),t!==s&&(e=sb())!==s&&(n=Qp())!==s&&(o=sb())!==s&&(u=Dl())!==s?(a=Ei,(i=sb())!==s&&(c=Qi())!==s?a=i=[i,c]:(Ei=a,a=s),a===s&&(a=null),a!==s?(Ai=r,r=t=function(r,t,e){return Yb.add(`select::${"object"==typeof r?r.value:r}::${t}`),{type:"column_ref",table:r,column:t,collate:e&&e[1],...Rb()}}(t,u,a)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,t=Ei,(e=Rl())!==s&&(n=sb())!==s&&(o=Qp())!==s?t=e=[e,n,o]:(Ei=t,t=s),t===s&&(t=null),t!==s&&(e=sb())!==s&&(n=zp())!==s?(Ai=r,r=t=function(r){const t=r&&r[0]||null;return Yb.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null,...Rb()}}(t)):(Ei=r,r=s),r===s&&(r=Ei,(t=Pl())!==s?(e=Ei,(n=sb())!==s&&(o=Qi())!==s?e=n=[n,o]:(Ei=e,e=s),e===s&&(e=null),e!==s?(Ai=r,r=t=function(r,t){return Yb.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1],...Rb()}}(t,e)):(Ei=r,r=s)):(Ei=r,r=s)))),r}function gl(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=Pl())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Pl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=Pl())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Sl(){var r,t;return r=Ei,(t=Fl())!==s&&(Ai=r,t={type:"default",value:t}),r=t}function Nl(){var r;return(r=Sl())===s&&(r=xl()),r}function Ol(){var r;return(r=Fl())===s&&(r=jl()),r}function Rl(){var r,t;return r=Ei,(t=Fl())!==s?(Ai=Ei,(Tn(t)?s:void 0)!==s?(Ai=r,r=t=t):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=jl()),r}function xl(){var r;return(r=kl())===s&&(r=Ul())===s&&(r=Ml()),r}function jl(){var r,t;return r=Ei,(t=kl())===s&&(t=Ul())===s&&(t=Ml()),t!==s&&(Ai=r,t=t.value),r=t}function kl(){var t,e,n,o;if(t=Ei,34===r.charCodeAt(Ei)?(e='"',Ei++):(e=s,0===gi&&xi(_n)),e!==s){if(n=[],In.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(gn)),o!==s)for(;o!==s;)n.push(o),In.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(gn));else n=s;n!==s?(34===r.charCodeAt(Ei)?(o='"',Ei++):(o=s,0===gi&&xi(_n)),o!==s?(Ai=t,t=e={type:"double_quote_string",value:n.join("")}):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;return t}function Ul(){var t,e,n,o;if(t=Ei,39===r.charCodeAt(Ei)?(e="'",Ei++):(e=s,0===gi&&xi(Ot)),e!==s){if(n=[],Sn.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(Nn)),o!==s)for(;o!==s;)n.push(o),Sn.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(Nn));else n=s;n!==s?(39===r.charCodeAt(Ei)?(o="'",Ei++):(o=s,0===gi&&xi(Ot)),o!==s?(Ai=t,t=e={type:"single_quote_string",value:n.join("")}):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;return t}function Ml(){var t,e,n,o;if(t=Ei,96===r.charCodeAt(Ei)?(e="`",Ei++):(e=s,0===gi&&xi(On)),e!==s){if(n=[],Rn.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(xn)),o===s&&(o=bf()),o!==s)for(;o!==s;)n.push(o),Rn.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(xn)),o===s&&(o=bf());else n=s;n!==s?(96===r.charCodeAt(Ei)?(o="`",Ei++):(o=s,0===gi&&xi(On)),o!==s?(Ai=t,t=e={type:"backticks_quote_string",value:n.join("")}):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;return t}function Dl(){var r,t;return r=Ei,(t=Gl())!==s&&(Ai=r,t=t),(r=t)===s&&(r=jl()),r}function Pl(){var r,t;return r=Ei,(t=Gl())!==s?(Ai=Ei,(Tn(t)?s:void 0)!==s?(Ai=r,r=t=t):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=Ml())!==s&&(Ai=r,t=t.value),r=t),r}function Gl(){var r,t,e,n;if(r=Ei,(t=Hl())!==s){for(e=[],n=$l();n!==s;)e.push(n),n=$l();e!==s?(Ai=r,r=t=jn(t,e)):(Ei=r,r=s)}else Ei=r,r=s;if(r===s)if(r=Ei,(t=hf())!==s){if(e=[],(n=$l())!==s)for(;n!==s;)e.push(n),n=$l();else e=s;e!==s?(Ai=r,r=t=jn(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Fl(){var r,t,e,n;if(r=Ei,(t=Hl())!==s){for(e=[],n=Yl();n!==s;)e.push(n),n=Yl();e!==s?(Ai=r,r=t=jn(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Hl(){var t;return kn.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(Un)),t}function Yl(){var t;return Mn.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(Dn)),t}function $l(){var t;return Pn.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(Gn)),t}function Bl(){var t,e,n,o;return t=Ei,e=Ei,58===r.charCodeAt(Ei)?(n=":",Ei++):(n=s,0===gi&&xi(Fn)),n!==s&&(o=Fl())!==s?e=n=[n,o]:(Ei=e,e=s),e!==s&&(Ai=t,e={type:"param",value:e[1]}),t=e}function Wl(){var t,e,n,o,u,a,i,c,l;return t=Ei,Wf()!==s&&sb()!==s&&gf()!==s&&sb()!==s&&(e=jp())!==s&&sb()!==s?(n=Ei,(o=Jp())!==s&&(u=sb())!==s?((a=sl())===s&&(a=null),a!==s&&(i=sb())!==s&&(c=rb())!==s?n=o=[o,u,a,i,c]:(Ei=n,n=s)):(Ei=n,n=s),n===s&&(n=null),n!==s?(Ai=t,t={type:"on update",keyword:e,parentheses:!!(l=n),expr:l?l[2]:null}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,Wf()!==s&&sb()!==s&&gf()!==s&&sb()!==s?("now"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Hn)),e!==s&&sb()!==s&&(n=Jp())!==s&&(o=sb())!==s&&(u=rb())!==s?(Ai=t,t=function(r){return{type:"on update",keyword:r,parentheses:!0}}(e)):(Ei=t,t=s)):(Ei=t,t=s)),t}function ql(){var t,e,n;return t=Ei,"over"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Yn)),e!==s&&sb()!==s&&(n=Xl())!==s?(Ai=t,t=e={type:"window",as_window_specification:n}):(Ei=t,t=s),t===s&&(t=Wl()),t}function Vl(){var r,t,e;return r=Ei,(t=Fl())!==s&&sb()!==s&&Gf()!==s&&sb()!==s&&(e=Xl())!==s?(Ai=r,r=t={name:t,as_window_specification:e}):(Ei=r,r=s),r}function Xl(){var r,t;return(r=Fl())===s&&(r=Ei,Jp()!==s&&sb()!==s?((t=function(){var r,t,e,n;r=Ei,(t=qc())===s&&(t=null);t!==s&&sb()!==s?((e=Vc())===s&&(e=null),e!==s&&sb()!==s?((n=function(){var r,t,e,n,o;r=Ei,(t=Sp())!==s&&sb()!==s?((e=Kl())===s&&(e=Ql()),e!==s?(Ai=r,r=t={type:"rows",expr:e}):(Ei=r,r=s)):(Ei=r,r=s);r===s&&(r=Ei,(t=Sp())!==s&&sb()!==s&&(e=np())!==s&&sb()!==s&&(n=Ql())!==s&&sb()!==s&&cp()!==s&&sb()!==s&&(o=Kl())!==s?(Ai=r,t=jb(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),r=t):(Ei=r,r=s));return r}())===s&&(n=null),n!==s?(Ai=r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(Ei=r,r=s)):(Ei=r,r=s)):(Ei=r,r=s);return r}())===s&&(t=null),t!==s&&sb()!==s&&rb()!==s?(Ai=r,r={window_specification:t||{},parentheses:!0}):(Ei=r,r=s)):(Ei=r,r=s)),r}function Kl(){var t,e,n,o;return t=Ei,(e=zl())!==s&&sb()!==s?("following"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Bn)),n!==s?(Ai=t,(o=e).value+=" FOLLOWING",t=e=o):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Zl()),t}function Ql(){var t,e,n,o,u;return t=Ei,(e=zl())!==s&&sb()!==s?("preceding"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Wn)),n===s&&("following"===r.substr(Ei,9).toLowerCase()?(n=r.substr(Ei,9),Ei+=9):(n=s,0===gi&&xi(Bn))),n!==s?(Ai=t,u=n,(o=e).value+=" "+u.toUpperCase(),t=e=o):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Zl()),t}function Zl(){var t,e,n;return t=Ei,"current"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(P)),e!==s&&sb()!==s?("row"===r.substr(Ei,3).toLowerCase()?(n=r.substr(Ei,3),Ei+=3):(n=s,0===gi&&xi(yr)),n!==s?(Ai=t,t=e={type:"origin",value:"current row",...Rb()}):(Ei=t,t=s)):(Ei=t,t=s),t}function zl(){var t,e;return t=Ei,"unbounded"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Y)),e!==s&&(Ai=t,e={type:"origin",value:e.toUpperCase(),...Rb()}),(t=e)===s&&(t=vf()),t}function Jl(){var t,e;return t=Ei,"year_month"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Vn)),e===s&&("day_hour"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Xn)),e===s&&("day_minute"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Kn)),e===s&&("day_second"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Qn)),e===s&&("day_microsecond"===r.substr(Ei,15).toLowerCase()?(e=r.substr(Ei,15),Ei+=15):(e=s,0===gi&&xi(Zn)),e===s&&("hour_minute"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(zn)),e===s&&("hour_second"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(Jn)),e===s&&("hour_microsecond"===r.substr(Ei,16).toLowerCase()?(e=r.substr(Ei,16),Ei+=16):(e=s,0===gi&&xi(ro)),e===s&&("minute_second"===r.substr(Ei,13).toLowerCase()?(e=r.substr(Ei,13),Ei+=13):(e=s,0===gi&&xi(to)),e===s&&("minute_microsecond"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(eo)),e===s&&("second_microsecond"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(no)),e===s&&("timezone_hour"===r.substr(Ei,13).toLowerCase()?(e=r.substr(Ei,13),Ei+=13):(e=s,0===gi&&xi(oo)),e===s&&("timezone_minute"===r.substr(Ei,15).toLowerCase()?(e=r.substr(Ei,15),Ei+=15):(e=s,0===gi&&xi(so)),e===s&&("century"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(uo)),e===s&&("day"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(ao)),e===s&&("date"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(io)),e===s&&("decade"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(co)),e===s&&("dow"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(lo)),e===s&&("doy"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(fo)),e===s&&("epoch"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(po)),e===s&&("hour"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(bo)),e===s&&("isodow"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(vo)),e===s&&("isoweek"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(yo)),e===s&&("isoyear"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(wo)),e===s&&("microseconds"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(ho)),e===s&&("millennium"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Lo)),e===s&&("milliseconds"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(Co)),e===s&&("minute"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(mo)),e===s&&("month"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Eo)),e===s&&("quarter"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ao)),e===s&&("second"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(To)),e===s&&("time"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(_o)),e===s&&("timezone"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Io)),e===s&&("week"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(go)),e===s&&("year"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(So)))))))))))))))))))))))))))))))))))),e!==s&&(Ai=t,e=e),t=e}function rf(){var t,e,n,o,u,a,i,c;return t=Ei,(e=fp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=Jl())!==s&&sb()!==s&&Df()!==s&&sb()!==s?((o=Op())===s&&(o=xp())===s&&(o=Np())===s&&(o=Ip()),o!==s&&sb()!==s&&(u=ll())!==s&&sb()!==s&&rb()!==s?(Ai=t,a=n,i=o,c=u,t=e={type:e.toLowerCase(),args:{field:a,cast_type:i,source:c},...Rb()}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=fp())!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=Jl())!==s&&sb()!==s&&Df()!==s&&sb()!==s&&(o=ll())!==s&&sb()!==s&&(u=rb())!==s?(Ai=t,t=e=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...Rb()}}(e,n,o)):(Ei=t,t=s),t===s&&(t=Ei,"date_trunc"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(No)),e!==s&&sb()!==s&&Jp()!==s&&sb()!==s&&(n=ll())!==s&&sb()!==s&&Zp()!==s&&sb()!==s&&(o=Jl())!==s&&sb()!==s&&(u=rb())!==s?(Ai=t,t=e=function(r,t){return{type:"function",name:{name:[{type:"origin",value:"date_trunc"}]},args:{type:"expr_list",value:[r,{type:"origin",value:t}]},over:null,...Rb()}}(n,o)):(Ei=t,t=s))),t}function tf(){var t,e,n;return t=Ei,(e=function(){var t;return"both"===r.substr(Ei,4).toLowerCase()?(t=r.substr(Ei,4),Ei+=4):(t=s,0===gi&&xi(Oo)),t===s&&("leading"===r.substr(Ei,7).toLowerCase()?(t=r.substr(Ei,7),Ei+=7):(t=s,0===gi&&xi(Ro)),t===s&&("trailing"===r.substr(Ei,8).toLowerCase()?(t=r.substr(Ei,8),Ei+=8):(t=s,0===gi&&xi(xo)))),t}())===s&&(e=null),e!==s&&sb()!==s?((n=ll())===s&&(n=null),n!==s&&sb()!==s&&Df()!==s?(Ai=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t}function ef(){var t,e,n,o;return t=Ei,"trim"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(jo)),e!==s&&sb()!==s&&Jp()!==s&&sb()!==s?((n=tf())===s&&(n=null),n!==s&&sb()!==s&&(o=ll())!==s&&sb()!==s&&rb()!==s?(Ai=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...Rb()}}(n,o)):(Ei=t,t=s)):(Ei=t,t=s),t}function nf(){var t,e,n,o,u,a,i,c;return(t=rf())===s&&(t=ef())===s&&(t=Ei,"convert"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(ko)),e!==s&&sb()!==s&&(n=Jp())!==s&&sb()!==s&&(o=function(){var r,t,e,n,o,u;return r=Ei,(t=db())!==s&&sb()!==s&&Zp()!==s&&sb()!==s?((e=Ib())===s&&(e=Sb()),e!==s&&sb()!==s&&(n=lc())!==s&&sb()!==s&&(o=Nl())!==s?(Ai=r,r=t=function(r,t,e,n){const{dataType:o,length:s}=t;let u=o;return void 0!==s&&(u=`${u}(${s})`),{type:"expr_list",value:[r,{type:"origin",value:u,suffix:{prefix:e,...n}}]}}(t,e,n,o)):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=db())!==s&&sb()!==s&&Zp()!==s&&sb()!==s?((e=sf())===s&&(e=Tb()),e!==s?(Ai=r,r=t={type:"expr_list",value:[t,{type:"datatype",..."string"==typeof(u=e)?{dataType:u}:u}]}):(Ei=r,r=s)):(Ei=r,r=s),r===s&&(r=Ei,(t=pl())!==s&&sb()!==s&&Kf()!==s&&sb()!==s&&(e=Fl())!==s?(Ai=r,r=t=function(r,t){return r.suffix="USING "+t.toUpperCase(),{type:"expr_list",value:[r]}}(t,e)):(Ei=r,r=s))),r}())!==s&&(u=sb())!==s&&rb()!==s?(Ai=t,t=e={type:"function",name:{name:[{type:"origin",value:"convert"}]},args:o,...Rb()}):(Ei=t,t=s),t===s&&(t=Ei,(e=function(){var t;(t=of())===s&&(t=kp())===s&&(t=Rp())===s&&(t=function(){var t,e,n,o;t=Ei,"session_user"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(La));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SESSION_USER"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"system_user"===r.substr(Ei,11).toLowerCase()?(e=r.substr(Ei,11),Ei+=11):(e=s,0===gi&&xi(Ca));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SYSTEM_USER"):(Ei=t,t=s)):(Ei=t,t=s);return t}());return t}())!==s&&sb()!==s&&(n=Jp())!==s&&sb()!==s?((o=sl())===s&&(o=null),o!==s&&(u=sb())!==s&&rb()!==s&&sb()!==s?((a=ql())===s&&(a=null),a!==s?(Ai=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...Rb()}}(e,o,a)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=of())!==s&&sb()!==s?((n=Wl())===s&&(n=null),n!==s?(Ai=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...Rb()}):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=Ei,(e=hb())!==s?(Ai=Ei,(!Ob[(c=e).name[0]&&c.name[0].value.toLowerCase()]?void 0:s)!==s&&(n=sb())!==s&&Jp()!==s&&(o=sb())!==s?((u=pl())===s&&(u=null),u!==s&&sb()!==s&&rb()!==s&&(a=sb())!==s?((i=ql())===s&&(i=null),i!==s?(Ai=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),(r.name[0]&&"TIMESTAMPDIFF"===r.name[0].value.toUpperCase()||r.name[0]&&"TIMESTAMPADD"===r.name[0].value.toUpperCase())&&t.value&&t.value[0]&&(t.value[0]={type:"origin",value:t.value[0].column}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...Rb()}}(e,u,i)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s)):(Ei=t,t=s))))),t}function of(){var t;return(t=function(){var t,e,n,o;t=Ei,"current_date"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(ba));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CURRENT_DATE"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"current_time"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(ya));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CURRENT_TIME"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=jp()),t}function sf(){var t;return(t=function(){var t,e,n,o;t=Ei,"signed"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(qu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SIGNED"):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=Ei,"unsigned"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Vu));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="UNSIGNED"):(Ei=t,t=s)):(Ei=t,t=s);return t}()),t}function uf(){var t,e,n,o,u,a,i,c,l;return t=Ei,"binary"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Uo)),e===s&&("_binary"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Mo))),e===s&&(e=null),e!==s&&sb()!==s&&(n=lf())!==s?(o=Ei,(u=sb())!==s&&(a=Qi())!==s?o=u=[u,a]:(Ei=o,o=s),o===s&&(o=null),o!==s?(Ai=t,c=n,l=o,(i=e)&&(c.prefix=i.toLowerCase()),l&&(c.suffix={collate:l[1]}),t=e=c):(Ei=t,t=s)):(Ei=t,t=s),t===s&&(t=function(){var t,e;t=Ei,(e=function(){var t,e,n,o;t=Ei,"true"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Es));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={type:"bool",value:!0});(t=e)===s&&(t=Ei,(e=function(){var t,e,n,o;t=Ei,"false"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Ts));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={type:"bool",value:!1}),t=e);return t}())===s&&(t=cf())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,(e=Np())===s&&(e=Ip())===s&&(e=Op())===s&&(e=gp());if(e!==s)if(sb()!==s){if(n=Ei,39===r.charCodeAt(Ei)?(o="'",Ei++):(o=s,0===gi&&xi(Ot)),o!==s){for(u=[],a=pf();a!==s;)u.push(a),a=pf();u!==s?(39===r.charCodeAt(Ei)?(a="'",Ei++):(a=s,0===gi&&xi(Ot)),a!==s?n=o=[o,u,a]:(Ei=n,n=s)):(Ei=n,n=s)}else Ei=n,n=s;n!==s?(Ai=t,e=Bo(e,n),t=e):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;if(t===s)if(t=Ei,(e=Np())===s&&(e=Ip())===s&&(e=Op())===s&&(e=gp()),e!==s)if(sb()!==s){if(n=Ei,34===r.charCodeAt(Ei)?(o='"',Ei++):(o=s,0===gi&&xi(_n)),o!==s){for(u=[],a=ff();a!==s;)u.push(a),a=ff();u!==s?(34===r.charCodeAt(Ei)?(a='"',Ei++):(a=s,0===gi&&xi(_n)),a!==s?n=o=[o,u,a]:(Ei=n,n=s)):(Ei=n,n=s)}else Ei=n,n=s;n!==s?(Ai=t,e=Bo(e,n),t=e):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;return t}()),t}function af(){var r;return(r=uf())===s&&(r=vf()),r}function cf(){var t,e;return t=Ei,(e=function(){var t,e,n,o;t=Ei,"null"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Cs));e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s);return t}())!==s&&(Ai=t,e={type:"null",value:null}),t=e}function lf(){var t,e,n,o,u,a,i,c;if(t=Ei,"_binary"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Mo)),e===s&&("_latin1"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Do))),e===s&&(e=null),e!==s)if((n=sb())!==s)if("x"===r.substr(Ei,1).toLowerCase()?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(Po)),o!==s){if(u=Ei,39===r.charCodeAt(Ei)?(a="'",Ei++):(a=s,0===gi&&xi(Ot)),a!==s){for(i=[],Go.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(Fo));c!==s;)i.push(c),Go.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(Fo));i!==s?(39===r.charCodeAt(Ei)?(c="'",Ei++):(c=s,0===gi&&xi(Ot)),c!==s?u=a=[a,i,c]:(Ei=u,u=s)):(Ei=u,u=s)}else Ei=u,u=s;u!==s?(Ai=t,t=e={type:"hex_string",prefix:e,value:u[1].join("")}):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;if(t===s){if(t=Ei,"_binary"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Mo)),e===s&&("_latin1"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Do))),e===s&&(e=null),e!==s)if((n=sb())!==s)if("b"===r.substr(Ei,1).toLowerCase()?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(Ho)),o!==s){if(u=Ei,39===r.charCodeAt(Ei)?(a="'",Ei++):(a=s,0===gi&&xi(Ot)),a!==s){for(i=[],Go.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(Fo));c!==s;)i.push(c),Go.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(Fo));i!==s?(39===r.charCodeAt(Ei)?(c="'",Ei++):(c=s,0===gi&&xi(Ot)),c!==s?u=a=[a,i,c]:(Ei=u,u=s)):(Ei=u,u=s)}else Ei=u,u=s;u!==s?(Ai=t,t=e=function(r,t,e){return{type:"bit_string",prefix:r,value:e[1].join("")}}(e,0,u)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;if(t===s){if(t=Ei,"_binary"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Mo)),e===s&&("_latin1"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Do))),e===s&&(e=null),e!==s)if((n=sb())!==s)if("0x"===r.substr(Ei,2).toLowerCase()?(o=r.substr(Ei,2),Ei+=2):(o=s,0===gi&&xi(Yo)),o!==s){for(u=[],Go.test(r.charAt(Ei))?(a=r.charAt(Ei),Ei++):(a=s,0===gi&&xi(Fo));a!==s;)u.push(a),Go.test(r.charAt(Ei))?(a=r.charAt(Ei),Ei++):(a=s,0===gi&&xi(Fo));u!==s?(Ai=t,t=e=function(r,t,e){return{type:"full_hex_string",prefix:r,value:e.join("")}}(e,0,u)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;if(t===s){if(t=Ei,"n"===r.substr(Ei,1).toLowerCase()?(e=r.charAt(Ei),Ei++):(e=s,0===gi&&xi($o)),e!==s){if(n=Ei,39===r.charCodeAt(Ei)?(o="'",Ei++):(o=s,0===gi&&xi(Ot)),o!==s){for(u=[],a=pf();a!==s;)u.push(a),a=pf();u!==s?(39===r.charCodeAt(Ei)?(a="'",Ei++):(a=s,0===gi&&xi(Ot)),a!==s?n=o=[o,u,a]:(Ei=n,n=s)):(Ei=n,n=s)}else Ei=n,n=s;n!==s?(Ai=t,t=e=function(r,t){return{type:"natural_string",value:t[1].join("")}}(0,n)):(Ei=t,t=s)}else Ei=t,t=s;if(t===s){if(t=Ei,e=Ei,39===r.charCodeAt(Ei)?(n="'",Ei++):(n=s,0===gi&&xi(Ot)),n!==s){for(o=[],u=pf();u!==s;)o.push(u),u=pf();o!==s?(39===r.charCodeAt(Ei)?(u="'",Ei++):(u=s,0===gi&&xi(Ot)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s)}else Ei=e,e=s;if(e!==s&&(Ai=t,e=function(r){return{type:"single_quote_string",value:r[1].join("")}}(e)),(t=e)===s){if(t=Ei,e=Ei,34===r.charCodeAt(Ei)?(n='"',Ei++):(n=s,0===gi&&xi(_n)),n!==s){for(o=[],u=ff();u!==s;)o.push(u),u=ff();o!==s?(34===r.charCodeAt(Ei)?(u='"',Ei++):(u=s,0===gi&&xi(_n)),u!==s?e=n=[n,o,u]:(Ei=e,e=s)):(Ei=e,e=s)}else Ei=e,e=s;e!==s&&(Ai=t,e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)),t=e}}}}}return t}function ff(){var t;return Wo.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(qo)),t===s&&(t=bf())===s&&(Vo.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(Xo))),t}function pf(){var t;return Ko.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(Qo)),t===s&&(t=bf()),t}function bf(){var t,e,n,o,u,a,i,c,l,f;return t=Ei,"\\'"===r.substr(Ei,2)?(e="\\'",Ei+=2):(e=s,0===gi&&xi(Zo)),e!==s&&(Ai=t,e="\\'"),(t=e)===s&&(t=Ei,'\\"'===r.substr(Ei,2)?(e='\\"',Ei+=2):(e=s,0===gi&&xi(zo)),e!==s&&(Ai=t,e='\\"'),(t=e)===s&&(t=Ei,"\\\\"===r.substr(Ei,2)?(e="\\\\",Ei+=2):(e=s,0===gi&&xi(Jo)),e!==s&&(Ai=t,e="\\\\"),(t=e)===s&&(t=Ei,"\\/"===r.substr(Ei,2)?(e="\\/",Ei+=2):(e=s,0===gi&&xi(rs)),e!==s&&(Ai=t,e="\\/"),(t=e)===s&&(t=Ei,"\\b"===r.substr(Ei,2)?(e="\\b",Ei+=2):(e=s,0===gi&&xi(ts)),e!==s&&(Ai=t,e="\b"),(t=e)===s&&(t=Ei,"\\f"===r.substr(Ei,2)?(e="\\f",Ei+=2):(e=s,0===gi&&xi(es)),e!==s&&(Ai=t,e="\f"),(t=e)===s&&(t=Ei,"\\n"===r.substr(Ei,2)?(e="\\n",Ei+=2):(e=s,0===gi&&xi(ns)),e!==s&&(Ai=t,e="\n"),(t=e)===s&&(t=Ei,"\\r"===r.substr(Ei,2)?(e="\\r",Ei+=2):(e=s,0===gi&&xi(os)),e!==s&&(Ai=t,e="\r"),(t=e)===s&&(t=Ei,"\\t"===r.substr(Ei,2)?(e="\\t",Ei+=2):(e=s,0===gi&&xi(ss)),e!==s&&(Ai=t,e="\t"),(t=e)===s&&(t=Ei,"\\u"===r.substr(Ei,2)?(e="\\u",Ei+=2):(e=s,0===gi&&xi(us)),e!==s&&(n=Cf())!==s&&(o=Cf())!==s&&(u=Cf())!==s&&(a=Cf())!==s?(Ai=t,i=n,c=o,l=u,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Ei=t,t=s),t===s&&(t=Ei,92===r.charCodeAt(Ei)?(e="\\",Ei++):(e=s,0===gi&&xi(as)),e!==s&&(Ai=t,e="\\"),(t=e)===s&&(t=Ei,"''"===r.substr(Ei,2)?(e="''",Ei+=2):(e=s,0===gi&&xi(is)),e!==s&&(Ai=t,e="''"),(t=e)===s&&(t=Ei,'""'===r.substr(Ei,2)?(e='""',Ei+=2):(e=s,0===gi&&xi(cs)),e!==s&&(Ai=t,e='""'),(t=e)===s&&(t=Ei,"``"===r.substr(Ei,2)?(e="``",Ei+=2):(e=s,0===gi&&xi(ls)),e!==s&&(Ai=t,e="``"),t=e))))))))))))),t}function vf(){var r,t,e;return r=Ei,(t=function(){var r,t,e,n;r=Ei,(t=df())!==s&&(e=yf())!==s&&(n=wf())!==s?(Ai=r,r=t={type:"bigint",value:t+e+n}):(Ei=r,r=s);r===s&&(r=Ei,(t=df())!==s&&(e=yf())!==s?(Ai=r,t=function(r,t){const e=r+t;return kb(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(Ei=r,r=s),r===s&&(r=Ei,(t=df())!==s&&(e=wf())!==s?(Ai=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(Ei=r,r=s),r===s&&(r=Ei,(t=df())!==s&&(Ai=r,t=function(r){return kb(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==s&&(Ai=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function df(){var t,e,n;return(t=hf())===s&&(t=Lf())===s&&(t=Ei,45===r.charCodeAt(Ei)?(e="-",Ei++):(e=s,0===gi&&xi(en)),e===s&&(43===r.charCodeAt(Ei)?(e="+",Ei++):(e=s,0===gi&&xi(tn))),e!==s&&(n=hf())!==s?(Ai=t,t=e=e+n):(Ei=t,t=s),t===s&&(t=Ei,45===r.charCodeAt(Ei)?(e="-",Ei++):(e=s,0===gi&&xi(en)),e===s&&(43===r.charCodeAt(Ei)?(e="+",Ei++):(e=s,0===gi&&xi(tn))),e!==s&&(n=Lf())!==s?(Ai=t,t=e=function(r,t){return r+t}(e,n)):(Ei=t,t=s))),t}function yf(){var t,e,n,o;return t=Ei,46===r.charCodeAt(Ei)?(e=".",Ei++):(e=s,0===gi&&xi(bs)),e!==s?((n=hf())===s&&(n=null),n!==s?(Ai=t,t=e=(o=n)?"."+o:""):(Ei=t,t=s)):(Ei=t,t=s),t}function wf(){var t,e,n;return t=Ei,(e=function(){var t,e,n;t=Ei,ys.test(r.charAt(Ei))?(e=r.charAt(Ei),Ei++):(e=s,0===gi&&xi(ws));e!==s?(hs.test(r.charAt(Ei))?(n=r.charAt(Ei),Ei++):(n=s,0===gi&&xi(Ls)),n===s&&(n=null),n!==s?(Ai=t,t=e=e+(null!==(o=n)?o:"")):(Ei=t,t=s)):(Ei=t,t=s);var o;return t}())!==s&&(n=hf())!==s?(Ai=t,t=e=e+n):(Ei=t,t=s),t}function hf(){var r,t,e;if(r=Ei,t=[],(e=Lf())!==s)for(;e!==s;)t.push(e),e=Lf();else t=s;return t!==s&&(Ai=r,t=t.join("")),r=t}function Lf(){var t;return ye.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(we)),t}function Cf(){var t;return vs.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(ds)),t}function mf(){var t,e,n,o;return t=Ei,"default"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(k)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Ef(){var t,e,n,o;return t=Ei,"to"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(As)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Af(){var t,e,n,o;return t=Ei,"show"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(_s)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Tf(){var t,e,n,o;return t=Ei,"drop"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Mr)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DROP"):(Ei=t,t=s)):(Ei=t,t=s),t}function _f(){var t,e,n,o;return t=Ei,"alter"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(gs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function If(){var t,e,n,o;return t=Ei,"select"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ss)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function gf(){var t,e,n,o;return t=Ei,"update"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ns)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Sf(){var t,e,n,o;return t=Ei,"create"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Os)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Nf(){var t,e,n,o;return t=Ei,"temporary"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Rs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Of(){var t,e,n,o;return t=Ei,"delete"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(xs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Rf(){var t,e,n,o;return t=Ei,"insert"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(js)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function xf(){var t,e,n,o;return t=Ei,"replace"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Us)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function jf(){var t,e,n,o;return t=Ei,"rename"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ms)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function kf(){var t,e,n,o;return t=Ei,"ignore"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ds)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Uf(){var t,e,n,o;return t=Ei,"partition"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Gs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="PARTITION"):(Ei=t,t=s)):(Ei=t,t=s),t}function Mf(){var t,e,n,o;return t=Ei,"into"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Fs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Df(){var t,e,n,o;return t=Ei,"from"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Hs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Pf(){var t,e,n,o;return t=Ei,"set"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(ft)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SET"):(Ei=t,t=s)):(Ei=t,t=s),t}function Gf(){var t,e,n,o;return t=Ei,"as"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(h)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Ff(){var t,e,n,o;return t=Ei,"table"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Ys)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TABLE"):(Ei=t,t=s)):(Ei=t,t=s),t}function Hf(){var t,e,n,o;return t=Ei,"trigger"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi($s)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TRIGGER"):(Ei=t,t=s)):(Ei=t,t=s),t}function Yf(){var t,e,n,o;return t=Ei,"tables"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Bs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TABLES"):(Ei=t,t=s)):(Ei=t,t=s),t}function $f(){var t,e,n,o;return t=Ei,"database"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Ws)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DATABASE"):(Ei=t,t=s)):(Ei=t,t=s),t}function Bf(){var t,e,n,o;return t=Ei,"schema"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(qs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SCHEMA"):(Ei=t,t=s)):(Ei=t,t=s),t}function Wf(){var t,e,n,o;return t=Ei,"on"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(Vs)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function qf(){var t,e,n,o;return t=Ei,"join"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Js)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Vf(){var t,e,n,o;return t=Ei,"outer"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(ru)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Xf(){var t,e,n,o;return t=Ei,"values"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(ou)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Kf(){var t,e,n,o;return t=Ei,"using"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(su)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Qf(){var t,e,n,o;return t=Ei,"with"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(v)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Zf(){var t,e,n,o;return t=Ei,"go"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(au)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="GO"):(Ei=t,t=s)):(Ei=t,t=s),t}function zf(){var t,e,n,o;return t=Ei,"by"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(d)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function Jf(){var t,e,n,o;return t=Ei,"asc"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(bu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ASC"):(Ei=t,t=s)):(Ei=t,t=s),t}function rp(){var t,e,n,o;return t=Ei,"desc"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(vu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DESC"):(Ei=t,t=s)):(Ei=t,t=s),t}function tp(){var t,e,n,o;return t=Ei,"all"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(yu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ALL"):(Ei=t,t=s)):(Ei=t,t=s),t}function ep(){var t,e,n,o;return t=Ei,"distinct"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(wu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DISTINCT"):(Ei=t,t=s)):(Ei=t,t=s),t}function np(){var t,e,n,o;return t=Ei,"between"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(hu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="BETWEEN"):(Ei=t,t=s)):(Ei=t,t=s),t}function op(){var t,e,n,o;return t=Ei,"in"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(he)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="IN"):(Ei=t,t=s)):(Ei=t,t=s),t}function sp(){var t,e,n,o;return t=Ei,"is"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(Lu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="IS"):(Ei=t,t=s)):(Ei=t,t=s),t}function up(){var t,e,n,o;return t=Ei,"like"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Cu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="LIKE"):(Ei=t,t=s)):(Ei=t,t=s),t}function ap(){var t,e,n,o;return t=Ei,"exists"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Au)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="EXISTS"):(Ei=t,t=s)):(Ei=t,t=s),t}function ip(){var t,e,n,o;return t=Ei,"not"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(zr)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="NOT"):(Ei=t,t=s)):(Ei=t,t=s),t}function cp(){var t,e,n,o;return t=Ei,"and"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Tu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="AND"):(Ei=t,t=s)):(Ei=t,t=s),t}function lp(){var t,e,n,o;return t=Ei,"or"===r.substr(Ei,2).toLowerCase()?(e=r.substr(Ei,2),Ei+=2):(e=s,0===gi&&xi(_u)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="OR"):(Ei=t,t=s)):(Ei=t,t=s),t}function fp(){var t,e,n,o;return t=Ei,"extract"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(xu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="EXTRACT"):(Ei=t,t=s)):(Ei=t,t=s),t}function pp(){var t,e,n,o;return t=Ei,"case"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(ku)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function bp(){var t,e,n,o;return t=Ei,"end"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Pu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?t=e=[e,n]:(Ei=t,t=s)):(Ei=t,t=s),t}function vp(){var t,e,n,o;return t=Ei,"cast"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Gu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CAST"):(Ei=t,t=s)):(Ei=t,t=s),t}function dp(){var t,e,n,o;return t=Ei,"bit"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Hu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="BIT"):(Ei=t,t=s)):(Ei=t,t=s),t}function yp(){var t,e,n,o;return t=Ei,"numeric"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Bu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="NUMERIC"):(Ei=t,t=s)):(Ei=t,t=s),t}function wp(){var t,e,n,o;return t=Ei,"decimal"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Wu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DECIMAL"):(Ei=t,t=s)):(Ei=t,t=s),t}function hp(){var t,e,n,o;return t=Ei,"int"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Xu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="INT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Lp(){var t,e,n,o;return t=Ei,"integer"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Qu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="INTEGER"):(Ei=t,t=s)):(Ei=t,t=s),t}function Cp(){var t,e,n,o;return t=Ei,"smallint"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(zu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SMALLINT"):(Ei=t,t=s)):(Ei=t,t=s),t}function mp(){var t,e,n,o;return t=Ei,"mediumint"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Ju)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MEDIUMINT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Ep(){var t,e,n,o;return t=Ei,"tinyint"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(ra)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TINYINT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Ap(){var t,e,n,o;return t=Ei,"bigint"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(sa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="BIGINT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Tp(){var t,e,n,o;return t=Ei,"float"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(aa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="FLOAT"):(Ei=t,t=s)):(Ei=t,t=s),t}function _p(){var t,e,n,o;return t=Ei,"double"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(ia)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DOUBLE"):(Ei=t,t=s)):(Ei=t,t=s),t}function Ip(){var t,e,n,o;return t=Ei,"date"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(io)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DATE"):(Ei=t,t=s)):(Ei=t,t=s),t}function gp(){var t,e,n,o;return t=Ei,"datetime"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(ca)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="DATETIME"):(Ei=t,t=s)):(Ei=t,t=s),t}function Sp(){var t,e,n,o;return t=Ei,"rows"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(la)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ROWS"):(Ei=t,t=s)):(Ei=t,t=s),t}function Np(){var t,e,n,o;return t=Ei,"time"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(_o)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TIME"):(Ei=t,t=s)):(Ei=t,t=s),t}function Op(){var t,e,n,o;return t=Ei,"timestamp"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(fa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TIMESTAMP"):(Ei=t,t=s)):(Ei=t,t=s),t}function Rp(){var t,e,n,o;return t=Ei,"user"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(pa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="USER"):(Ei=t,t=s)):(Ei=t,t=s),t}function xp(){var t,e,n,o;return t=Ei,"interval"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(va)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="INTERVAL"):(Ei=t,t=s)):(Ei=t,t=s),t}function jp(){var t,e,n,o;return t=Ei,"current_timestamp"===r.substr(Ei,17).toLowerCase()?(e=r.substr(Ei,17),Ei+=17):(e=s,0===gi&&xi(wa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CURRENT_TIMESTAMP"):(Ei=t,t=s)):(Ei=t,t=s),t}function kp(){var t,e,n,o;return t=Ei,"current_user"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(ha)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CURRENT_USER"):(Ei=t,t=s)):(Ei=t,t=s),t}function Up(){var t,e,n,o;return t=Ei,"view"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(se)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="VIEW"):(Ei=t,t=s)):(Ei=t,t=s),t}function Mp(){var t;return 64===r.charCodeAt(Ei)?(t="@",Ei++):(t=s,0===gi&&xi(lr)),t}function Dp(){var t;return(t=function(){var t;return"@@"===r.substr(Ei,2)?(t="@@",Ei+=2):(t=s,0===gi&&xi(ja)),t}())===s&&(t=Mp())===s&&(t=function(){var t;return 36===r.charCodeAt(Ei)?(t="$",Ei++):(t=s,0===gi&&xi(ka)),t}()),t}function Pp(){var t;return":="===r.substr(Ei,2)?(t=":=",Ei+=2):(t=s,0===gi&&xi(Ma)),t}function Gp(){var t;return 61===r.charCodeAt(Ei)?(t="=",Ei++):(t=s,0===gi&&xi(Be)),t}function Fp(){var t,e,n,o;return t=Ei,"add"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(Pa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ADD"):(Ei=t,t=s)):(Ei=t,t=s),t}function Hp(){var t,e,n,o;return t=Ei,"column"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(Ga)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="COLUMN"):(Ei=t,t=s)):(Ei=t,t=s),t}function Yp(){var t,e,n,o;return t=Ei,"index"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(gt)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="INDEX"):(Ei=t,t=s)):(Ei=t,t=s),t}function $p(){var t,e,n,o;return t=Ei,"key"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(ir)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="KEY"):(Ei=t,t=s)):(Ei=t,t=s),t}function Bp(){var t,e,n,o;return t=Ei,"fulltext"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Ha)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="FULLTEXT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Wp(){var t,e,n,o;return t=Ei,"spatial"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Ya)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="SPATIAL"):(Ei=t,t=s)):(Ei=t,t=s),t}function qp(){var t,e,n,o;return t=Ei,"unique"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi(ar)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="UNIQUE"):(Ei=t,t=s)):(Ei=t,t=s),t}function Vp(){var t,e,n,o;return t=Ei,"comment"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi($a)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="COMMENT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Xp(){var t,e,n,o;return t=Ei,"constraint"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Qr)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CONSTRAINT"):(Ei=t,t=s)):(Ei=t,t=s),t}function Kp(){var t,e,n,o;return t=Ei,"references"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Ba)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="REFERENCES"):(Ei=t,t=s)):(Ei=t,t=s),t}function Qp(){var t;return 46===r.charCodeAt(Ei)?(t=".",Ei++):(t=s,0===gi&&xi(bs)),t}function Zp(){var t;return 44===r.charCodeAt(Ei)?(t=",",Ei++):(t=s,0===gi&&xi(Za)),t}function zp(){var t;return 42===r.charCodeAt(Ei)?(t="*",Ei++):(t=s,0===gi&&xi(nn)),t}function Jp(){var t;return 40===r.charCodeAt(Ei)?(t="(",Ei++):(t=s,0===gi&&xi(ve)),t}function rb(){var t;return 41===r.charCodeAt(Ei)?(t=")",Ei++):(t=s,0===gi&&xi(de)),t}function tb(){var t;return 59===r.charCodeAt(Ei)?(t=";",Ei++):(t=s,0===gi&&xi(ri)),t}function eb(){var t;return"->"===r.substr(Ei,2)?(t="->",Ei+=2):(t=s,0===gi&&xi(ti)),t}function nb(){var t;return"->>"===r.substr(Ei,3)?(t="->>",Ei+=3):(t=s,0===gi&&xi(ei)),t}function ob(){var t;return(t=function(){var t;return"||"===r.substr(Ei,2)?(t="||",Ei+=2):(t=s,0===gi&&xi(un)),t}())===s&&(t=function(){var t;return"&&"===r.substr(Ei,2)?(t="&&",Ei+=2):(t=s,0===gi&&xi(ni)),t}())===s&&(t=function(){var t,e,n,o;return t=Ei,"xor"===r.substr(Ei,3).toLowerCase()?(e=r.substr(Ei,3),Ei+=3):(e=s,0===gi&&xi(oi)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="XOR"):(Ei=t,t=s)):(Ei=t,t=s),t}()),t}function sb(){var r,t;for(r=[],(t=lb())===s&&(t=ab());t!==s;)r.push(t),(t=lb())===s&&(t=ab());return r}function ub(){var r,t;if(r=[],(t=lb())===s&&(t=ab()),t!==s)for(;t!==s;)r.push(t),(t=lb())===s&&(t=ab());else r=s;return r}function ab(){var t;return(t=function(){var t,e,n,o,u,a;t=Ei,"/*"===r.substr(Ei,2)?(e="/*",Ei+=2):(e=s,0===gi&&xi(si));if(e!==s){for(n=[],o=Ei,u=Ei,gi++,"*/"===r.substr(Ei,2)?(a="*/",Ei+=2):(a=s,0===gi&&xi(ui)),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,u=Ei,gi++,"*/"===r.substr(Ei,2)?(a="*/",Ei+=2):(a=s,0===gi&&xi(ui)),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);n!==s?("*/"===r.substr(Ei,2)?(o="*/",Ei+=2):(o=s,0===gi&&xi(ui)),o!==s?t=e=[e,n,o]:(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,"--"===r.substr(Ei,2)?(e="--",Ei+=2):(e=s,0===gi&&xi(ai));if(e!==s){for(n=[],o=Ei,u=Ei,gi++,a=fb(),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,u=Ei,gi++,a=fb(),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);n!==s?t=e=[e,n]:(Ei=t,t=s)}else Ei=t,t=s;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=Ei,35===r.charCodeAt(Ei)?(e="#",Ei++):(e=s,0===gi&&xi(ii));if(e!==s){for(n=[],o=Ei,u=Ei,gi++,a=fb(),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);o!==s;)n.push(o),o=Ei,u=Ei,gi++,a=fb(),gi--,a===s?u=void 0:(Ei=u,u=s),u!==s&&(a=cb())!==s?o=u=[u,a]:(Ei=o,o=s);n!==s?t=e=[e,n]:(Ei=t,t=s)}else Ei=t,t=s;return t}()),t}function ib(){var r,t,e,n,o,u,a;return r=Ei,(t=Vp())!==s&&sb()!==s?((e=Gp())===s&&(e=null),e!==s&&sb()!==s&&(n=lf())!==s?(Ai=r,u=e,a=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:a}):(Ei=r,r=s)):(Ei=r,r=s),r}function cb(){var t;return r.length>Ei?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(ci)),t}function lb(){var t;return li.test(r.charAt(Ei))?(t=r.charAt(Ei),Ei++):(t=s,0===gi&&xi(fi)),t}function fb(){var t,e;if((t=function(){var t,e;t=Ei,gi++,r.length>Ei?(e=r.charAt(Ei),Ei++):(e=s,0===gi&&xi(ci));gi--,e===s?t=void 0:(Ei=t,t=s);return t}())===s)if(t=[],fs.test(r.charAt(Ei))?(e=r.charAt(Ei),Ei++):(e=s,0===gi&&xi(ps)),e!==s)for(;e!==s;)t.push(e),fs.test(r.charAt(Ei))?(e=r.charAt(Ei),Ei++):(e=s,0===gi&&xi(ps));else t=s;return t}function pb(){var t,e;return t=Ei,Ai=Ei,Fb=[],(!0?void 0:s)!==s&&sb()!==s?((e=bb())===s&&(e=function(){var t,e;t=Ei,function(){var t;return"return"===r.substr(Ei,6).toLowerCase()?(t=r.substr(Ei,6),Ei+=6):(t=s,0===gi&&xi(Ua)),t}()!==s&&sb()!==s&&(e=vb())!==s?(Ai=t,t={type:"return",expr:e}):(Ei=t,t=s);return t}()),e!==s?(Ai=t,t={stmt:e,vars:Fb}):(Ei=t,t=s)):(Ei=t,t=s),t}function bb(){var r,t,e,n;return r=Ei,(t=Eb())===s&&(t=Ab()),t!==s&&sb()!==s?((e=Pp())===s&&(e=Gp()),e!==s&&sb()!==s&&(n=vb())!==s?(Ai=r,r=t=pi(t,e,n)):(Ei=r,r=s)):(Ei=r,r=s),r}function vb(){var t;return(t=Lc())===s&&(t=function(){var r,t,e,n,o;r=Ei,(t=Eb())!==s&&sb()!==s&&(e=Hc())!==s&&sb()!==s&&(n=Eb())!==s&&sb()!==s&&(o=$c())!==s?(Ai=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Ei=r,r=s);return r}())===s&&(t=db())===s&&(t=function(){var t,e;t=Ei,function(){var t;return 91===r.charCodeAt(Ei)?(t="[",Ei++):(t=s,0===gi&&xi(za)),t}()!==s&&sb()!==s&&(e=mb())!==s&&sb()!==s&&function(){var t;return 93===r.charCodeAt(Ei)?(t="]",Ei++):(t=s,0===gi&&xi(Ja)),t}()!==s?(Ai=t,t={type:"array",value:e}):(Ei=t,t=s);return t}()),t}function db(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=yb())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=ml())!==s&&(a=sb())!==s&&(i=yb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=ml())!==s&&(a=sb())!==s&&(i=yb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=qe(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function yb(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=wb())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Al())!==s&&(a=sb())!==s&&(i=wb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Al())!==s&&(a=sb())!==s&&(i=wb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=qe(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function wb(){var r,t,e;return(r=Lb())===s&&(r=af())===s&&(r=Eb())===s&&(r=Il())===s&&(r=Cb())===s&&(r=Bl())===s&&(r=Ei,Jp()!==s&&sb()!==s&&(t=db())!==s&&sb()!==s&&rb()!==s?(Ai=r,(e=t).parentheses=!0,r=e):(Ei=r,r=s)),r}function hb(){var r,t,e,n,o,u,a;return r=Ei,(t=Sl())===s&&(t=Ml()),t!==s?(e=Ei,(n=sb())!==s&&(o=Qp())!==s&&(u=sb())!==s?((a=Sl())===s&&(a=Ml()),a!==s?e=n=[n,o,u,a]:(Ei=e,e=s)):(Ei=e,e=s),e===s&&(e=null),e!==s?(Ai=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(Ei=r,r=s)):(Ei=r,r=s),r}function Lb(){var r,t,e;return r=Ei,(t=hb())!==s&&sb()!==s&&Jp()!==s&&sb()!==s?((e=mb())===s&&(e=null),e!==s&&sb()!==s&&rb()!==s?(Ai=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...Rb()}):(Ei=r,r=s)):(Ei=r,r=s),r}function Cb(){var r,t;return r=Ei,(t=hb())!==s&&(Ai=r,t={type:"function",name:t,args:null,...Rb()}),r=t}function mb(){var r,t,e,n,o,u,a,i;if(r=Ei,(t=wb())!==s){for(e=[],n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=wb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);n!==s;)e.push(n),n=Ei,(o=sb())!==s&&(u=Zp())!==s&&(a=sb())!==s&&(i=wb())!==s?n=o=[o,u,a,i]:(Ei=n,n=s);e!==s?(Ai=r,r=t=sr(t,e)):(Ei=r,r=s)}else Ei=r,r=s;return r}function Eb(){var r,t,e,n,o;return r=Ei,(t=Dp())!==s&&(e=Ab())!==s?(Ai=r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(Ei=r,r=s),r}function Ab(){var t,e,n,o,u;return t=Ei,(e=Fl())!==s&&(n=function(){var t,e,n,o,u;t=Ei,e=[],n=Ei,46===r.charCodeAt(Ei)?(o=".",Ei++):(o=s,0===gi&&xi(bs));o!==s&&(u=Fl())!==s?n=o=[o,u]:(Ei=n,n=s);for(;n!==s;)e.push(n),n=Ei,46===r.charCodeAt(Ei)?(o=".",Ei++):(o=s,0===gi&&xi(bs)),o!==s&&(u=Fl())!==s?n=o=[o,u]:(Ei=n,n=s);e!==s&&(Ai=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==s?(Ai=t,o=e,u=n,Fb.push(o),t=e={type:"var",name:o,members:u,prefix:null}):(Ei=t,t=s),t===s&&(t=Ei,(e=vf())!==s&&(Ai=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Tb(){var t;return(t=Ib())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b;t=Ei,(e=yp())===s&&(e=wp())===s&&(e=hp())===s&&(e=Lp())===s&&(e=Cp())===s&&(e=mp())===s&&(e=Ep())===s&&(e=Ap())===s&&(e=Tp())===s&&(e=_p())===s&&(e=dp());if(e!==s)if((n=sb())!==s)if((o=Jp())!==s)if((u=sb())!==s){if(a=[],ye.test(r.charAt(Ei))?(i=r.charAt(Ei),Ei++):(i=s,0===gi&&xi(we)),i!==s)for(;i!==s;)a.push(i),ye.test(r.charAt(Ei))?(i=r.charAt(Ei),Ei++):(i=s,0===gi&&xi(we));else a=s;if(a!==s)if((i=sb())!==s){if(c=Ei,(l=Zp())!==s)if((f=sb())!==s){if(p=[],ye.test(r.charAt(Ei))?(b=r.charAt(Ei),Ei++):(b=s,0===gi&&xi(we)),b!==s)for(;b!==s;)p.push(b),ye.test(r.charAt(Ei))?(b=r.charAt(Ei),Ei++):(b=s,0===gi&&xi(we));else p=s;p!==s?c=l=[l,f,p]:(Ei=c,c=s)}else Ei=c,c=s;else Ei=c,c=s;c===s&&(c=null),c!==s&&(l=sb())!==s&&(f=rb())!==s&&(p=sb())!==s?((b=gb())===s&&(b=null),b!==s?(Ai=t,v=c,d=b,e={dataType:e,length:parseInt(a.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:d},t=e):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s}else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;else Ei=t,t=s;var v,d;if(t===s){if(t=Ei,(e=yp())===s&&(e=wp())===s&&(e=hp())===s&&(e=Lp())===s&&(e=Cp())===s&&(e=mp())===s&&(e=Ep())===s&&(e=Ap())===s&&(e=Tp())===s&&(e=_p())===s&&(e=dp()),e!==s){if(n=[],ye.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(we)),o!==s)for(;o!==s;)n.push(o),ye.test(r.charAt(Ei))?(o=r.charAt(Ei),Ei++):(o=s,0===gi&&xi(we));else n=s;n!==s&&(o=sb())!==s?((u=gb())===s&&(u=null),u!==s?(Ai=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,u),t=e):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;t===s&&(t=Ei,(e=yp())===s&&(e=wp())===s&&(e=hp())===s&&(e=Lp())===s&&(e=Cp())===s&&(e=mp())===s&&(e=Ep())===s&&(e=Ap())===s&&(e=Tp())===s&&(e=_p())===s&&(e=dp()),e!==s&&(n=sb())!==s?((o=gb())===s&&(o=null),o!==s&&(u=sb())!==s?(Ai=t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(Ei=t,t=s)):(Ei=t,t=s))}return t}())===s&&(t=Sb())===s&&(t=function(){var t,e;t=Ei,(e=function(){var t,e,n,o;return t=Ei,"json"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Zu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="JSON"):(Ei=t,t=s)):(Ei=t,t=s),t}())!==s&&(Ai=t,e={dataType:e});return t=e}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;return t=Ei,"tinytext"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(ta)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TINYTEXT"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"text"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(ea)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="TEXT"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"mediumtext"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(na)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MEDIUMTEXT"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"longtext"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(oa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="LONGTEXT"):(Ei=t,t=s)):(Ei=t,t=s),t}());e!==s?((n=_b())===s&&(n=null),n!==s?(Ai=t,e=hi(e,n),t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;return t=Ei,"enum"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(ua)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ENUM"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=Pf());e!==s&&sb()!==s&&(n=ol())!==s?(Ai=t,o=e,(u=n).parentheses=!0,t=e={dataType:o,expr:u}):(Ei=t,t=s);var o,u;return t}())===s&&(t=function(){var t,e;t=Ei,"boolean"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(bi));e!==s&&(Ai=t,e={dataType:"BOOLEAN"});return t=e}())===s&&(t=function(){var t,e,n;t=Ei,(e=function(){var t,e,n,o;return t=Ei,"binary"===r.substr(Ei,6).toLowerCase()?(e=r.substr(Ei,6),Ei+=6):(e=s,0===gi&&xi($t)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="BINARY"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"varbinary"===r.substr(Ei,9).toLowerCase()?(e=r.substr(Ei,9),Ei+=9):(e=s,0===gi&&xi(Fu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="VARBINARY"):(Ei=t,t=s)):(Ei=t,t=s),t}());e!==s&&sb()!==s?((n=_b())===s&&(n=null),n!==s?(Ai=t,e=hi(e,n),t=e):(Ei=t,t=s)):(Ei=t,t=s);return t}())===s&&(t=function(){var t,e;t=Ei,"blob"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(vi));e===s&&("tinyblob"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(di)),e===s&&("mediumblob"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(yi)),e===s&&("longblob"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(wi)))));e!==s&&(Ai=t,e={dataType:e.toUpperCase()});return t=e}())===s&&(t=function(){var t,e;t=Ei,(e=function(){var t,e,n,o;return t=Ei,"geometry"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(_a)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="GEOMETRY"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"point"===r.substr(Ei,5).toLowerCase()?(e=r.substr(Ei,5),Ei+=5):(e=s,0===gi&&xi(Ia)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="POINT"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"linestring"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(ga)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="LINESTRING"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"polygon"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi(Sa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="POLYGON"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"multipoint"===r.substr(Ei,10).toLowerCase()?(e=r.substr(Ei,10),Ei+=10):(e=s,0===gi&&xi(Na)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MULTIPOINT"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"multilinestring"===r.substr(Ei,15).toLowerCase()?(e=r.substr(Ei,15),Ei+=15):(e=s,0===gi&&xi(Oa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MULTILINESTRING"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"multipolygon"===r.substr(Ei,12).toLowerCase()?(e=r.substr(Ei,12),Ei+=12):(e=s,0===gi&&xi(Ra)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="MULTIPOLYGON"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"geometrycollection"===r.substr(Ei,18).toLowerCase()?(e=r.substr(Ei,18),Ei+=18):(e=s,0===gi&&xi(xa)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="GEOMETRYCOLLECTION"):(Ei=t,t=s)):(Ei=t,t=s),t}());e!==s&&(Ai=t,e={dataType:e});return t=e}()),t}function _b(){var t,e,n,o,u;if(t=Ei,Jp()!==s)if(sb()!==s){if(e=[],ye.test(r.charAt(Ei))?(n=r.charAt(Ei),Ei++):(n=s,0===gi&&xi(we)),n!==s)for(;n!==s;)e.push(n),ye.test(r.charAt(Ei))?(n=r.charAt(Ei),Ei++):(n=s,0===gi&&xi(we));else e=s;e!==s&&(n=sb())!==s&&rb()!==s&&sb()!==s?((o=gb())===s&&(o=null),o!==s?(Ai=t,u=o,t={length:parseInt(e.join(""),10),parentheses:!0,suffix:u}):(Ei=t,t=s)):(Ei=t,t=s)}else Ei=t,t=s;else Ei=t,t=s;return t}function Ib(){var t,e,n,o,u,a,i,c,l,f,p;if(t=Ei,(e=function(){var t,e,n,o;return t=Ei,"char"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(Yu)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="CHAR"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=Ei,"varchar"===r.substr(Ei,7).toLowerCase()?(e=r.substr(Ei,7),Ei+=7):(e=s,0===gi&&xi($u)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="VARCHAR"):(Ei=t,t=s)):(Ei=t,t=s),t}()),e!==s){if(n=Ei,(o=sb())!==s)if((u=Jp())!==s)if((a=sb())!==s){if(i=[],ye.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(we)),c!==s)for(;c!==s;)i.push(c),ye.test(r.charAt(Ei))?(c=r.charAt(Ei),Ei++):(c=s,0===gi&&xi(we));else i=s;i!==s&&(c=sb())!==s&&(l=rb())!==s&&(f=sb())!==s?("array"===r.substr(Ei,5).toLowerCase()?(p=r.substr(Ei,5),Ei+=5):(p=s,0===gi&&xi(Li)),p===s&&(p=null),p!==s?n=o=[o,u,a,i,c,l,f,p]:(Ei=n,n=s)):(Ei=n,n=s)}else Ei=n,n=s;else Ei=n,n=s;else Ei=n,n=s;n===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3].join(""),10),e.parentheses=!0,e.suffix=t[7]&&["ARRAY"]),e}(e,n)):(Ei=t,t=s)}else Ei=t,t=s;return t}function gb(){var t,e,n;return t=Ei,(e=sf())===s&&(e=null),e!==s&&sb()!==s?((n=function(){var t,e,n,o;return t=Ei,"zerofill"===r.substr(Ei,8).toLowerCase()?(e=r.substr(Ei,8),Ei+=8):(e=s,0===gi&&xi(Ku)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="ZEROFILL"):(Ei=t,t=s)):(Ei=t,t=s),t}())===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t}function Sb(){var t,e,n,o,u,a,i,c,l,f,p;return t=Ei,(e=Ip())===s&&(e=gp())===s&&(e=Np())===s&&(e=Op())===s&&(e=function(){var t,e,n,o;return t=Ei,"year"===r.substr(Ei,4).toLowerCase()?(e=r.substr(Ei,4),Ei+=4):(e=s,0===gi&&xi(So)),e!==s?(n=Ei,gi++,o=Hl(),gi--,o===s?n=void 0:(Ei=n,n=s),n!==s?(Ai=t,t=e="YEAR"):(Ei=t,t=s)):(Ei=t,t=s),t}()),e!==s?(n=Ei,(o=sb())!==s&&(u=Jp())!==s&&(a=sb())!==s?(Ci.test(r.charAt(Ei))?(i=r.charAt(Ei),Ei++):(i=s,0===gi&&xi(mi)),i!==s&&(c=sb())!==s&&(l=rb())!==s&&(f=sb())!==s?((p=gb())===s&&(p=null),p!==s?n=o=[o,u,a,i,c,l,f,p]:(Ei=n,n=s)):(Ei=n,n=s)):(Ei=n,n=s),n===s&&(n=null),n!==s?(Ai=t,t=e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3],10),e.parentheses=!0,e.suffix=t[7]),e}(e,n)):(Ei=t,t=s)):(Ei=t,t=s),t}const Nb={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,ANALYZE:!0,ACCESSIBLE:!0,BEFORE:!0,BETWEEN:!0,BIGINT:!0,BLOB:!0,BOTH:!0,BY:!0,BOOLEAN:!0,CALL:!0,CASCADE:!0,CASE:!0,CHAR:!0,CHECK:!0,COLLATE:!0,CONDITION:!0,CONSTRAINT:!0,CONTINUE:!0,CONVERT:!0,CREATE:!0,CROSS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,CURSOR:!0,DATABASE:!0,DATABASES:!0,DAY_HOUR:!0,DAY_MICROSECOND:!0,DAY_MINUTE:!0,DAY_SECOND:!0,DEC:!0,DECIMAL:!0,DECLARE:!0,DEFAULT:!0,DELAYED:!0,DELETE:!0,DESC:!0,DESCRIBE:!0,DETERMINISTIC:!0,DISTINCT:!0,DISTINCTROW:!0,DIV:!0,DROP:!0,DOUBLE:!0,DUAL:!0,ELSE:!0,EACH:!0,ELSEIF:!0,ENCLOSED:!0,ESCAPED:!0,EXCEPT:!0,EXISTS:!0,EXIT:!0,EXPLAIN:!0,FALSE:!0,FULL:!0,FROM:!0,FETCH:!0,FLOAT:!0,FLOAT4:!0,FLOAT8:!0,FOR:!0,FORCE:!0,FOREIGN:!0,FULLTEXT:!0,FUNCTION:!0,GENERATED:!0,GET:!0,GO:!0,GRANT:!0,GROUP:!0,GROUPING:!0,GROUPS:!0,HAVING:!0,HIGH_PRIORITY:!0,HOUR_MICROSECOND:!0,HOUR_MINUTE:!0,HOUR_SECOND:!0,IGNORE:!0,IN:!0,INNER:!0,INFILE:!0,INOUT:!0,INSENSITIVE:!0,INSERT:!0,INTERSECT:!0,INT:!0,INT1:!0,INT2:!0,INT3:!0,INT4:!0,INT8:!0,INTEGER:!0,INTERVAL:!0,INTO:!0,IO_AFTER_GTIDS:!0,IO_BEFORE_GTIDS:!0,IS:!0,ITERATE:!0,JOIN:!0,JSON_TABLE:!0,KEY:!0,KEYS:!0,KILL:!0,LAG:!0,LAST_VALUE:!0,LATERAL:!0,LEAD:!0,LEADING:!0,LEAVE:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LINEAR:!0,LINES:!0,LOAD:!0,LOCALTIME:!0,LOCALTIMESTAMP:!0,LOCK:!0,LONG:!0,LONGBLOB:!0,LONGTEXT:!0,LOOP:!0,LOW_PRIORITY:!0,MASTER_BIND:!0,MATCH:!0,MAXVALUE:!0,MEDIUMBLOB:!0,MEDIUMINT:!0,MEDIUMTEXT:!0,MIDDLEINT:!0,MINUTE_MICROSECOND:!0,MINUTE_SECOND:!0,MINUS:!0,MOD:!0,MODIFIES:!0,NATURAL:!0,NOT:!0,NO_WRITE_TO_BINLOG:!0,NTH_VALUE:!0,NTILE:!0,NULL:!0,NUMERIC:!0,OF:!0,ON:!0,OPTIMIZE:!0,OPTIMIZER_COSTS:!0,OPTION:!0,OPTIONALLY:!0,OR:!0,ORDER:!0,OUT:!0,OUTER:!0,OUTFILE:!0,OVER:!0,PARTITION:!0,PERCENT_RANK:!0,PRECISION:!0,PRIMARY:!0,PROCEDURE:!0,PURGE:!0,RANGE:!0,RANK:!0,READ:!0,READS:!0,READ_WRITE:!0,REAL:!0,RECURSIVE:!0,REFERENCES:!0,REGEXP:!0,RELEASE:!0,RENAME:!0,REPEAT:!0,REPLACE:!0,REQUIRE:!0,RESIGNAL:!0,RESTRICT:!0,RETURN:!0,REVOKE:!0,RIGHT:!0,RLIKE:!0,ROW:!0,ROWS:!0,ROW_NUMBER:!0,SCHEMA:!0,SCHEMAS:!0,SELECT:!0,SENSITIVE:!0,SEPARATOR:!0,SET:!0,SHOW:!0,SIGNAL:!0,SMALLINT:!0,SPATIAL:!0,SPECIFIC:!0,SQL:!0,SQLEXCEPTION:!0,SQLSTATE:!0,SQLWARNING:!0,SQL_BIG_RESULT:!0,SSL:!0,STARTING:!0,STORED:!0,STRAIGHT_JOIN:!0,SYSTEM:!0,TABLE:!0,TERMINATED:!0,THEN:!0,TINYBLOB:!0,TINYINT:!0,TINYTEXT:!0,TO:!0,TRAILING:!0,TRIGGER:!0,TRUE:!0,UNION:!0,UNIQUE:!0,UNLOCK:!0,UNSIGNED:!0,UPDATE:!0,USAGE:!0,USE:!0,USING:!0,UTC_DATE:!0,UTC_TIME:!0,UTC_TIMESTAMP:!0,VALUES:!0,VARBINARY:!0,VARCHAR:!0,VARCHARACTER:!0,VARYING:!0,VIRTUAL:!0,WHEN:!0,WHERE:!0,WHILE:!0,WINDOW:!0,WITH:!0,WRITE:!0,XOR:!0,YEAR_MONTH:!0,ZEROFILL:!0},Ob={avg:!0,sum:!0,count:!0,convert:!0,max:!0,min:!0,group_concat:!0,std:!0,variance:!0,current_date:!0,current_time:!0,current_timestamp:!0,current_user:!0,user:!0,session_user:!0,system_user:!0};function Rb(){return t.includeLocations?{loc:Ri(Ai,Ei)}:{}}function xb(r,t){return{type:"unary_expr",operator:r,expr:t}}function jb(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e,...Rb()}}function kb(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Ub(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function Mb(r,t){let e=r;for(let r=0;r<t.length;r++)e=jb(t[r][1],e,t[r][3]);return e}function Db(r){const t=$b[r];return t||(r||null)}function Pb(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Db(r[1])),t.add(r.join("::"))}return Array.from(t)}function Gb(r){const t=Pb(r);r.clear(),t.forEach(t=>r.add(t))}let Fb=[];const Hb=new Set,Yb=new Set,$b={};if((e=a())!==s&&Ei===r.length)return e;throw e!==s&&Ei<r.length&&xi({type:"end"}),ji(Ii,_i<r.length?r.charAt(_i):null,_i<r.length?Ri(_i,_i+1):Ri(_i,_i))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return T})),e.d(n,"autoIncrementToSQL",(function(){return N})),e.d(n,"columnOrderListToSQL",(function(){return O})),e.d(n,"commonKeywordArgsToSQL",(function(){return S})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return C})),e.d(n,"commentToSQL",(function(){return _})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return s})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return L})),e.d(n,"columnIdentifierToSql",(function(){return d})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return y})),e.d(n,"onPartitionsToSQL",(function(){return E})),e.d(n,"replaceParams",(function(){return m})),e.d(n,"returningToSQL",(function(){return g})),e.d(n,"hasVal",(function(){return h})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return w})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return I}));var s={database:"mysql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return u}function b(r){u=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function y(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function w(r){if(r)return r.toUpperCase()}function h(r){return r}function L(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,s=r.suffix,u=r.value,a="object"===o(r)?u:r;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'");break;case"unicode_string":a="U&'".concat(u,"'")}var i=[];return t&&i.push(w(t)),i.push(a),s&&("string"==typeof s&&i.push(s),"object"===o(s)&&(s.collate?i.push(it(s.collate)):i.push(L(s)))),a=i.join(" "),n?"(".concat(a,")"):a}}function C(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():L(n)].filter(h)}function m(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var s=t[n];if("object"!==o(s)||"param"!==s.type)return r(s,e);if(void 0===e[s.value])throw new Error("no value for parameter :".concat(s.value," found"));return t[n]=c(e[s.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function E(r){var t=r.type,e=r.partitions;return[w(t),"(".concat(e.map((function(r){if("range"!==r.type)return L(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(L(t)," ").concat(w(n)," ").concat(L(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,s=r.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),s&&s.length&&(u+=" ".concat(s.join(" "))),"".concat(t).concat(u)}function T(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=w(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var s=e&&e.map((function(r){return[r.field_name,T(r.field_type)].filter(h).join(" ")})).join(", ");return n?"".concat(o,"<").concat(s,">"):"".concat(o," ").concat(s)}}function _(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(L(o)),t.join(" ")}}function I(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[w(t)];if(e){var o=e.keyword,s=e.columns;n.push(w(o),s.map(dt).join(", "))}return n.join(" ")})).join(" OR ")}function g(r){return r?["RETURNING",r.columns.map(Et).filter(h).join(", ")].join(" "):""}function S(r){return r?[w(r.keyword),w(r.args)]:[]}function N(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,s=r.parentheses,u=w(e);return s&&(u+="(".concat(L(n),", ").concat(L(o),")")),u}}function O(r){if(r)return r.map(Lt).filter(h).join(", ")}function R(r){return function(r){if(Array.isArray(r))return x(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return x(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?x(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function j(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),w(e)]}function k(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),s=[];switch(s.push(o),o){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(L(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,R(j(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(_(r));break;case"DATA_COMPRESSION":s.push(n,w(e.value),E(e.on));break;default:s.push(n,L(e))}return s.filter(h).join(" ")}}function U(r){return r?r.map(k):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,s=r.definition,u=r.on,a=r.with,i=[];if(i.push.apply(i,R(j(e))),s&&s.length){var c="CHECK"===w(t)?"(".concat(st(s[0]),")"):"(".concat(s.map((function(r){return st(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),u&&i.push("ON [".concat(u,"]")),i}function D(r){var t=r.operator||r.op,e=st(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},s=[Array.isArray(r.left)?r.left.map(st).join(", "):st(r.left),t,e,w(o.type),st(o.value)].filter(h).join(" ");return[r.parentheses?"(".concat(s,")"):s].join(" ")}function P(r){return function(r){if(Array.isArray(r))return G(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function F(r){return r?[r.prefix.map(L).join(" "),st(r.value),r.suffix.map(L).join(" ")]:[]}function H(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(F(n)),P(F(e))).filter(h).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",st(e[0])):i("LIMIT",e.map(st).join("".concat("offset"===t?" ":"").concat(w(t)," ")))}(r):"";var t,e,n}function Y(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(dt).join(", "),")"):"";return"".concat("default"===t.type?y(t.value):L(t)).concat(o," AS (").concat(st(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function $(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=w(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?y(e):st(e))}return n.filter(h).join(" ")}}function B(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,s=r.for,u=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,d=r.into,y=void 0===d?{}:d,C=r.isolation,m=r.limit,E=r.options,A=r.orderby,T=r.parentheses_symbol,_=r.qualify,I=r.top,g=r.window,S=r.with,N=r.where,O=[Y(S),"SELECT",w(t)];Array.isArray(E)&&O.push(E.join(" ")),O.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[w(t)];return e&&n.push("(".concat(e.map(st).join(", "),")")),n.filter(h).join(" ")}}(o),v(I),Tt(e,u));var R=y.position,x="";R&&(x=a("INTO",$,y)),"column"===R&&O.push(x),O.push(a("FROM",cr,u)),"from"===R&&O.push(x);var j=l||{},k=j.keyword,U=j.expr;O.push(a(k,st,U)),O.push(a("WHERE",st,N)),p&&(O.push(i("GROUP BY",ut(p.columns).join(", "))),O.push(ut(p.modifiers).join(", "))),O.push(a("HAVING",st,b)),O.push(a("QUALIFY",st,_)),O.push(a("WINDOW",st,g)),O.push(at(A,"order by")),O.push(it(n)),O.push(H(m)),C&&O.push(a(C.keyword,L,C.expr)),O.push(w(f)),"end"===R&&O.push(x),O.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[w(r.type),w(e)];return t?"".concat(n.join(" "),"(").concat(st(t),")"):n.join(" ")}}(s));var M=O.filter(h).join(" ");return T?"(".concat(M,")"):M}function W(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return q(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?q(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function q(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=W(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s={},u=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(s[a]=o[a]);var i=[dt(s)],c="";u&&(c=st(u),i.push("=",c)),e.push(i.filter(h).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return B(r);var t=r.map(st);return"(".concat(t.join("), ("),")")}function K(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(y).join(", "));else{var e=r.value;t.push(e.map(st).join(", "))}return t.push(")"),t.filter(h).join("")}function Q(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(dt).join(", "),")")}}function Z(r){var t=r.expr,e=r.keyword,n=t.type,o=[w(e)];switch(n){case"origin":o.push(L(t));break;case"update":o.push("UPDATE",a("SET",V,t.set),a("WHERE",st,t.where))}return o.filter(h).join(" ")}function z(r){if(!r)return"";var t=r.action;return[Q(r.target),Z(t)].filter(h).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,s=r.columns,u=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},d=v.keyword,y=v.set,C=[w(e),w(o),cr(t),K(f)];return Array.isArray(s)&&C.push("(".concat(s.map(L).join(", "),")")),C.push(a(Array.isArray(i)?"VALUES":"",X,i)),C.push(a("ON CONFLICT",z,u)),C.push(a("SET",V,b)),C.push(a("WHERE",st,c)),C.push(a(d,V,y)),C.push(g(p)),C.filter(h).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",st(t),w(e),st(n)].filter(h).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(w(t),"(").concat(n&&st(n)||"",")"),a("AS","string"==typeof e?y:st,e),a(w(o&&o.keyword),y,o&&o.as)].filter(h).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,s=r.type,u=[st(n),"FOR",dt(e),D(o)],a=["".concat(w(s),"(").concat(u.join(" "),")")];return t&&a.push("AS",y(t)),a.join(" ")}(r);default:return""}}function sr(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,s=r.parentheses,u=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(w(t),"(".concat(y(n)),"(".concat(o.map(st).filter(h).join(", "),"))"));break;case"spatial_window_max_cells":a.push(w(t),"=",st(e));break;case"index":a.push(w(u),w(t),s?"(".concat(e.map(y).join(", "),")"):"= ".concat(y(e)));break;default:a.push(st(e))}return a.filter(h).join(" ")}}function ur(r,t){var e=r.name,n=r.symbol;return[w(e),n,t].filter(h).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",st(r.of));break;case"from_to":t.push("FROM",st(r.from),"TO",st(r.to));break;case"between_and":t.push("BETWEEN",st(r.between),"AND",st(r.and));break;case"contained":t.push("CONTAINED","IN",st(r.in))}return t.filter(h).join(" ")}function ir(r){if("UNNEST"===w(r.type))return nr(r);var t,e,n,o,s=r.table,u=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,d=r.tablesample,m=r.temporal_table,E=r.table_hint,A=y(b),T=y(u),_=y(p),I=s&&y(s);if(c)switch(c.type){case"values":var g=c.parentheses,S=c.values,N=c.prefix,O=[g&&"(","",g&&")"],R=X(S);N&&(R=R.split("(").slice(1).map((function(r){return"".concat(w(N),"(").concat(r)})).join("")),O[1]="VALUES ".concat(R),I=O.filter(h).join("");break;case"tumble":I=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,s=[y(t.expr.db),y(t.expr.schema),y(t.expr.table)].filter(h).join("."),u="DESCRIPTOR(".concat(dt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(ur(t,s)),ur(e,u)],i=ur(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(ur(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(h).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return C(r).join(" ")})).join(", "),I="".concat(w(e),"(").concat(w(n),"(").concat(o,"))");break;default:I=st(c)}var x=[[A,T,_,I=[w(f),I,w(v)].filter(h).join(" ")].filter(h).join(".")];if(d){var j=["TABLESAMPLE",st(d.expr),L(d.repeatable)].filter(h).join(" ");x.push(j)}x.push(function(r){if(r){var t=r.keyword,e=r.expr;return[w(t),ar(e)].filter(h).join(" ")}}(m),a("AS","string"==typeof i?y:st,i),or(l)),E&&x.push(w(E.keyword),"(".concat(E.expr.map(sr).filter(h).join(", "),")"));var k=x.filter(h).join(" ");return r.parentheses?"(".concat(k,")"):k}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var s=[],u=[],i=!0===e?1:e.length,c=0;c++<i;)s.push("("),u.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return s.join("")+o+u.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],d=v.on,y=v.using,C=v.join,m=[];m.push(C?" ".concat(w(C)):","),m.push(ir(v)),m.push(a("ON",st,d)),y&&m.push("USING (".concat(y.map(L).join(", "),")")),p.push(m.filter(h).join(" "))}return p.filter(h).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var s=L(n);switch(t){case"partition by":case"default collate":s=st(n);break;case"options":s="(".concat(n.map((function(r){return[r.keyword,r.symbol,st(r.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(st).join(", ")}return o.push(s),o.filter(h).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[y(t.db),y(t.table)].filter(h).join(".");return"".concat(w(e)," ").concat(n);case"column":return"COLUMN ".concat(dt(t));default:return"".concat(w(e)," ").concat(L(t))}}function pr(r){var t=r.keyword,e=r.expr;return[w(t),L(e)].filter(h).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",st(e)].filter(h).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var s=st(t),u=st(e);return[s,w(n),u].filter(h).join(" ")}function dr(r){var t,e,n,o,s=r.keyword,u=r.suffix,i="";switch(w(s)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",L,e&&e.right),a("FROM",cr,n),H(o)].filter(h).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===w(t.op)?a("LIKE",L,t.right):a("WHERE",st,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,s="'".concat(e,"'");return n&&(s+="@'".concat(n,"'")),["FOR",s,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(h).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[u]);break;case"VAR":i=ot(r.var),s=""}return["SHOW",w(s),w(u),i].filter(h).join(" ")}var yr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,s=r.type,u=t.expr,a=t.orderby;return[w(s),w(n),[[y(o.schema),y(o.name)].filter(h).join("."),"(".concat(u.map(zr).join(", ")).concat(a?[" ORDER","BY",a.map(zr).join(", ")].join(" "):"",")")].filter(h).join(""),Zr(e)].filter(h).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,s=r.expr,u=void 0===s?[]:s,a=w(t),i=cr(e),c=u.map(st);return[a,"TABLE",w(n),L(o),i,c.join(", ")].filter(h).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[w(r.type),w(e),y(n),Zr(t)].filter(h).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[w(r.type),w(e),[y(n.schema),y(n.name)].filter(h).join("."),Zr(t)].filter(h).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[w(r.type),w(n),[[y(o.schema),y(o.name)].filter(h).join("."),t&&"(".concat(t.expr?t.expr.map(zr).join(", "):"",")")].filter(h).join(""),Zr(e)].filter(h).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,s=r.view,u=r.with,a=w(t),i=ir(s),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(dt).join(", "),")"));n&&c.push("WITH ".concat(n.map(w).join(", ")));c.push("AS",B(o)),u&&c.push(w(u));return c.filter(h).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[w(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,s=r.schema;return[w(t),w(e),st(n),w(o),y(s)].filter(h).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.options,a=[w(t),w(e),w(n)],i=[y(o.schema),o.name].filter(h).join("."),c="".concat(s.expr.map(zr).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(u.map(Kr).join(", "),")")),a.filter(h).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,s=r.as,u=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,d=r.unlogged,y=r.with,C=[w(t),w(f),w(u),w(d),w(e),w(a),cr(n)];if(o){var m=o.type,E=cr(o.table);return C.push(w(m),E),C.filter(h).join(" ")}if(b)return C.concat([qr(b)]).filter(h).join(" ");i&&C.push("(".concat(i.map(Br).join(", "),")"));if(c){var A=p().database,T=A&&"sqlite"===A.toLowerCase()?", ":" ";C.push(c.map(lr).join(T))}if(y){var _=y.map((function(r){return[L(r.keyword),w(r.symbol),L(r.value)].join(" ")})).join(", ");C.push("WITH (".concat(_,")"))}C.push(w(l),w(s)),v&&C.push(wr(v));return C.filter(h).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,s=r.execute,u=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[w(f),w(l),w(e),w(c),y(t),w(i)],d=I(o);v.push(d,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Hr(S(n)).concat(Hr(S(u)))),b&&v.push(w(b.type),st(b.cond));return v.push(w(s.keyword),Fr(s.expr)),v.filter(h).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,s=r.type,u=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,d=[w(s),w(c),st(t),w(n),w(i),ir(l),w(b),f.map((function(r){var t=[w(r.keyword)],e=r.args;return e&&t.push(w(e.keyword),e.columns.map(dt).join(", ")),t.join(" ")})),"ON",ir(u),w(e&&e.keyword),w(e&&e.args),p&&"".concat(w(p.keyword)," ").concat(y(p.trigger)),a("WHEN",st,v),w(o.prefix)];switch(o.type){case"set":d.push(a("SET",V,o.expr));break;case"multiple":d.push(hr(o.expr.ast))}return d.push(w(o.suffix)),d.filter(h).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,s=r.schema,u=r.type,i=r.with,c=r.version;return[w(u),w(o),w(n),L(t),w(i),a("SCHEMA",L,s),a("VERSION",L,c),a("FROM",L,e)].filter(h).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.returns,a=r.options,i=r.last,c=[w(t),w(e),w(n)],l=[L(o.schema),o.name.map(L).join(".")].filter(h).join("."),f=s.map(zr).filter(h).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),Array.isArray(n)?"(".concat(n.map(Ct).join(", "),")"):Vr(n)].filter(h).join(" ")}(u),a.map(Xr).join(" "),i),c.filter(h).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,s=r.include,u=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,d=r.on_kw,C=r.table,m=r.tablespace,E=r.type,A=r.where,T=r.with,_=r.with_before_where,I=T&&"WITH (".concat(U(T).join(", "),")"),g=s&&"".concat(w(s.keyword)," (").concat(s.columns.map((function(r){return"string"==typeof r?y(r):st(r)})).join(", "),")"),S=l;l&&(S="string"==typeof l?y(l):[y(l.schema),y(l.name)].filter(h).join("."));var N=[w(E),w(i),w(n),w(o),w(t),S,w(d),ir(C)].concat(Hr(j(c)),["(".concat(O(u),")"),g,U(p).join(" "),Zr(b),Zr(v),a("TABLESPACE",L,m)]);_?N.push(I,a("WHERE",st,A)):N.push(a("WHERE",st,A),I);return N.push(a("ON",st,f),a("FILESTREAM_ON",L,e)),N.filter(h).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,s=r.if_not_exists,u=r.create_definitions,a=[w(t),w(o),w(e),w(s),cr(n)];u&&a.push(u.map(Br).join(" "));return a.filter(h).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,s=r.create_definitions,u=r[e],a=u.db,i=u.schema,c=[L(a),i.map(L).join(".")].filter(h).join("."),l=[w(t),w(n),w(e),w(o),c];s&&l.push(s.map(lr).join(" "));return l.filter(h).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,s=r.keyword,u=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,L=p.db,m=p.schema,E=p.view,A=[y(L),y(m),y(E)].filter(h).join(".");return[w(f),w(a),w(l),w(u),t&&"ALGORITHM = ".concat(w(t)),st(n),c&&"SQL SECURITY ".concat(w(c)),w(s),w(o),A,e&&"(".concat(e.map(d).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return C(r).join(" ")})).join(", "),")")].join(" "),"AS",wr(i),w(b)].filter(h).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,s=r.target,u=r.create_definitions,a=[w(n),w(o),[y(e.schema),y(e.name)].filter(h).join("."),w(t),A(s)];if(u&&u.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Yr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(u);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(st(f));break;case"default":c.push(w(p),st(f.value));break;case"constraint":c.push(jr(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(h).join(" "))}return a.filter(h).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,s=r.resource,u=[w(r.type),w(n),[y(o.schema),y(o.name)].filter(h).join("."),w(t),w(s)];if(e){var a=[];switch(s){case"enum":case"range":a.push(st(e));break;default:a.push("(".concat(e.map(Br).join(", "),")"))}u.push(a.filter(h).join(" "))}return u.filter(h).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,s=r.keyword,u=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[Nr(t)];return e&&n.push(w(e.keyword),e.auth_plugin,L(e.value)),n.filter(h).join(" ")})).join(", "),b=[w(f),w(s),w(o),p];n&&b.push(w(n.keyword),n.value.map(Nr).join(", "));b.push(a(c&&c.keyword,st,c&&c.value)),l&&b.push(w(l.keyword),l.value.map((function(r){return st(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,st,r.value))}));return b.push(L(u),_(e),L(t)),b.filter(h).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[w(r.type),w(e),fr(n),pr(t)].filter(h).join(" ")},select:B,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),st(n)].filter(h).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning,l=[Y(u),"DELETE"],f=Tt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",st,o)),l.push(at(s,"order by")),l.push(H(i)),l.push(g(c)),l.filter(h).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[w(t),ir(e),(n||[]).map(br).filter(h).join(", ")].filter(h).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[w(t)],s=[e];n&&s.push("(".concat(st(n).join(", "),")"));return o.push(s.join("")),o.filter(h).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[w(t),B(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,s=r.stmts;return[e,w(t),n,"IN",hr([o]),"LOOP",hr(s),"END LOOP",e].filter(h).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning;return[Y(u),"UPDATE",cr(e),a("SET",V,n),a("FROM",cr,t),a("WHERE",st,o),at(s,"order by"),H(i),g(c)].filter(h).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,s=r.prefix,u=r.go,a=r.semicolons,i=r.suffix,c=[w(r.type),st(t),L(s),"".concat(Er(o.ast||o)).concat(a[0]),w(u)];n&&c.push(n.map((function(r){return[w(r.type),st(r.boolean_expr),"THEN",Er(r.then.ast||r.then),r.semicolon].filter(h).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Er(e.ast||e)).concat(a[1]));return c.push(L(i)),c.filter(h).join(" ")},insert:J,drop:gr,truncate:gr,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[w(t)],s=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,s=r.datatype,u=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(h).join(""),w(n),w(o)];switch(c){case"variable":l.push(yt(s),st(r.collate),w(u)),i&&l.push(w(i.keyword),st(i.value));break;case"cursor":l.push(w(a));break;case"table":l.push(w(a),"(".concat(i.map(Br).join(", "),")"))}return l.filter(h).join(" ")})).join("".concat(n," "));return o.push(s),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=w(t),o=y(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var s,u=Ar(e);try{for(u.s();!(s=u.n()).done;){var a=s.value.map(ir);n.push(a.join(" TO "))}}catch(r){u.e(r)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=st(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=w(t);return"".concat(n," ").concat(y(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=w(t),s=e.map(st).join(", ");return[o,w(n),s].filter(h).join(" ")},lock:Sr,unlock:Sr,show:dr,grant:Or,revoke:Or,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[w(t),st(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,s=[w(t),w(e)];n&&s.push([L(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(h).join(""),n.expr.map((function(r){return st(r)})).join(", "));o&&s.push(w(o.type),w(o.option),o.symbol,o.expr.map((function(r){return st(r)})).join(", "));return s.filter(h).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,s=[L(e),w(n)];return o&&s.push(o.map(L).join(", ")),s.filter(h).join(" ")}};function wr(r){if(!r)return"";for(var t=yr[r.type],e=r,n=e._parentheses,o=e._orderby,s=e._limit,u=[n&&"(",t(r)];r._next;){var a=yr[r._next.type],i=w(r.set_op);u.push(i,a(r._next)),r=r._next}return u.push(n&&")",at(o,"order by"),H(s)),u.filter(h).join(" ")}function hr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],s=wr(o);e===n-1&&"transaction"===o.type&&(s="".concat(s," ;")),t.push(s)}return t.join(" ; ")}var Lr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Cr(r){var t=r&&r.ast?r.ast:r;if(!Lr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function mr(r){return Array.isArray(r)?(r.forEach(Cr),hr(r)):(Cr(r),wr(r))}function Er(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[mr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):mr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=_r(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function Tr(r){return function(r){if(Array.isArray(r))return Ir(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||_r(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _r(r,t){if(r){if("string"==typeof r)return Ir(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ir(r,t):void 0}}function Ir(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function gr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,s=r.suffix,u=[w(t),w(e),w(o)];switch(e){case"table":u.push(cr(n));break;case"trigger":u.push([n[0].schema?"".concat(y(n[0].schema),"."):"",y(n[0].trigger)].filter(h).join(""));break;case"database":case"schema":case"procedure":u.push(y(n));break;case"view":u.push(cr(n),r.options&&r.options.map(st).filter(h).join(" "));break;case"index":u.push.apply(u,[dt(n)].concat(Tr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(st).filter(h).join(" ")]));break;case"type":u.push(n.map(dt).join(", "),r.options&&r.options.map(st).filter(h).join(" "))}return s&&u.push(s.map(st).filter(h).join(" ")),u.filter(h).join(" ")}function Sr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),w(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var s,u=[],a=Ar(n);try{var i=function(){var r=s.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return w(e[r])})).filter(h).join(" "))}u.push(n.join(" "))};for(a.s();!(s=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[u.join(", ")].concat(Tr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(h).join(" ")}function Nr(r){var t=r.name,e=r.host,n=[L(t)];return e&&n.push("@",L(e)),n.join("")}function Or(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,s=r.on,u=r.to_from,a=r.user_or_roles,i=r.with,c=[w(t),L(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[st(t)];return e&&n.push("(".concat(e.map(dt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),s)switch(c.push("ON"),n){case"priv":c.push(L(s.object_type),s.priv_level.map((function(r){return[y(r.prefix),y(r.name)].filter(h).join(".")})).join(", "));break;case"proxy":c.push(Nr(s))}return c.push(w(u),a.map(Nr).join(", ")),c.push(L(i)),c.filter(h).join(" ")}function Rr(r){return function(r){if(Array.isArray(r))return xr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return xr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?xr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function jr(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,s=r.keyword,u=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(w(s)),l.push(y(t));var b=w(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&y(o)),l.push.apply(l,Rr(M(r))),l.push.apply(l,Rr(wt(u))),l.push(w(n)),l.push(a("FOR",y,i)),l.push(L(c)),l.filter(h).join(" ")}}function kr(r){if(r){var t=r.type;return"rows"===t?[w(t),st(r.expr)].filter(h).join(" "):st(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),kr(o)].filter(h).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,s=r.parentheses,u=w(o);if("WINDOW"===u)return"OVER ".concat(Ur(t));if("ON UPDATE"===u){var a="".concat(w(o)," ").concat(w(n)),i=st(e)||[];return s&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return w(t);for(var e=r.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(L(o[u])),s.push("]");return s.join("")}function Gr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,s=r.as,u=r.offset,a=r.parentheses,i=bt({expr:e,offset:u}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,d=p.dataType,C=p.parentheses,m=p.quoted,E=p.scale,A=p.suffix,T=p.expr,_=T?st(T):"";null!=v&&(_=E?"".concat(v,", ").concat(E):v),C&&(_="(".concat(_,")")),b&&(_="<".concat(_,">")),A&&A.length&&(_+=" ".concat(A.map(L).join(" ")));var I="::",g="",S=[];"as"===o&&(0===l&&(i="".concat(w(n),"(").concat(i)),g=")",I=" ".concat(o.toUpperCase()," ")),0===l&&S.push(i);var N=Pr(p);S.push(I,m,d,m,N,_,g),c.push(S.filter(h).join(""))}s&&c.push(" AS ".concat(y(s)));var O=c.filter(h).join("");return a?"(".concat(O,")"):O}function Fr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,s=r.parentheses,u=r.within_group,a=r.over,i=r.suffix,c=Dr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[w(t),w(e),"(".concat(at(n,"order by"),")")].filter(h).join(" ")}(u),f=st(i),p=[L(n.schema),n.name.map(L).join(".")].filter(h).join(".");if(!t)return[p,l,c].filter(h).join(" ");var b=r.separator||", ";"TRIM"===w(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var d=st(t);if(Array.isArray(b)){for(var y=d[0],C=1,m=d.length;C<m;++C)y=[y,d[C]].join(" ".concat(st(b[C-1])," "));v.push(y)}else v.push(d.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(h).join(" "),[s?"(".concat(v,")"):v,l,c].filter(h).join(" ")}function Hr(r){return function(r){if(Array.isArray(r))return $r(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Yr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yr(r,t){if(r){if("string"==typeof r)return $r(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?$r(r,t):void 0}}function $r(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Br(r){if(!r)return[];var t,e,n,o,s=r.resource;switch(s){case"column":return Ct(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(w(n)),e.push(o),e.push.apply(e,R(M(t))),e.filter(h).join(" ");case"constraint":return jr(r);case"sequence":return[w(r.prefix),st(r.value)].filter(h).join(" ");default:throw new Error("unknown resource = ".concat(s," type"))}}function Wr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(L(r.from),")"),"TO","(".concat(L(r.to),")"));break;case"in":t.push("IN","(".concat(st(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(L(r.modulus),", REMAINDER ").concat(L(r.remainder),")"))}return t.filter(h).join(" ")}function qr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,s=[w(t),ir(e),w(n.keyword),Wr(n.expr)];return o&&s.push("TABLESPACE",L(o)),s.filter(h).join(" ")}function Vr(r){return r.dataType?A(r):[y(r.db),y(r.schema),y(r.table)].filter(h).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[w(t),r.symbol,wr(r.declare),w(r.begin),hr(r.expr),w(r.end),r.symbol].filter(h).join(" ");case"set":return[w(t),r.parameter,w(r.value&&r.value.prefix),r.value&&r.value.expr.map(st).join(", ")].filter(h).join(" ");case"return":return[w(t),st(r.expr)].filter(h).join(" ");default:return st(r)}}function Kr(r){var t=r.type,e=r.symbol,n=r.value,o=[w(t),e];switch(w(t)){case"SFUNC":o.push([y(n.schema),n.name].filter(h).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(st(n))}return o.filter(h).join(" ")}function Qr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",L(t),"VALUES",w(e.type),"(".concat(L(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Tt(t)}}function Zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,s=r.if_exists,u=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Ct(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=y(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=y(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,w(r[i])].filter(h).join(" ");break;case"constraint":f=y(r[i]),p=[Br(e)];break;case"partition":p=[Qr(t,r.partitions)];break;case"key":f=y(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[w(t),w(o),w(n),w(s),u&&dt(u),w(a),f&&f.trim(),p.filter(h).join(" ")];return l&&b.push(w(l.keyword),l.expr&&dt(l.expr)),b.filter(h).join(" ")}function zr(r){var t=r.default&&[w(r.default.keyword),st(r.default.value)].join(" ");return[w(r.mode),r.name,A(r.type),t].filter(h).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(w(r.type)){case"STRUCT":return"(".concat(Tt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(Tt(t),"]");var s=Array.isArray(n)?n.map((function(r){return"(".concat(Tt(r),")")})).filter(h).join(", "):st(n);return e?"[".concat(s,"]"):o?"(".concat(s,")"):s}(r);default:return""}}function tt(r){var t=r.definition,e=[w(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(T(t))),e.push(rt(r)),e.filter(h).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:Zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,s=st(t.expr);s=Array.isArray(s)?s.join(", "):s;var u=r.name,a=Dr(n);t.distinct&&(s=["DISTINCT",s].join(" ")),t.separator&&t.separator.delimiter&&(s=[s,L(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(s=[s,st(t.separator.expr)].join(" ")),t.orderby&&(s=[s,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(s=[s,w(t.separator.keyword),L(t.separator.value)].filter(h).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(st(e.where),")"):"";return["".concat(u,"(").concat(s,")"),i,a,c].filter(h).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,s=t.having,u="".concat(w(e),"(").concat(st(o));return s&&(u="".concat(u," HAVING ").concat(w(s.prefix)," ").concat(st(s.expr))),[u="".concat(u,")"),Dr(n)].filter(h).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,s=r.separator,u=void 0===s?", ":s;return[e,"(",t?st(t).join(u):"",")",o&&" ",o].filter(h).join("")}(r),Dr(t)].filter(h).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(st(n));for(var s=0,u=e.length;s<u;++s)t.push(e[s].type.toUpperCase()),e[s].cond&&(t.push(st(e[s].cond)),t.push("THEN")),t.push(st(e[s].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Gr,collate:it,column_ref:dt,column_definition:Ct,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,s=t.source,u=["".concat(w(e),"(").concat(w(n)),"FROM",w(o),st(s)];return"".concat(u.filter(h).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[w(t),e,st(n)].filter(h).join(" ")}(t[r])})).filter(h).join(", ");return"".concat(w(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,s=r.mode,u=[w(o),"(".concat(n.map((function(r){return dt(r)})).join(", "),")")].join(" "),a=[w(t),["(",st(r.expr),s&&" ".concat(L(s)),")"].filter(h).join("")].join(" ");return[u,a,mt(e)].filter(h).join(" ")},function:Fr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,s=n.map(st).join(", ");return[o?"(".concat(s,")"):s,"->",st(e)].join(" ")},insert:wr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[w(t),e.map((function(r){return st(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,s=[st(e),"VALUE",st(n)];return o&&s.push("ON","NULL",st(o)),s.filter(h).join(" ")},json_visitor:function(r){return[r.symbol,st(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,st(t.expr)].filter(h).join(" ")},show:dr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[L(e.schema),e.name.map(L).join(".")].filter(h).join(".");return["".concat(o,"(").concat(st(n).join(", "),")"),"AS",Fr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,s=r.quoted,u=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return u&&(c+=u),a.push(c),[s,a.join(" "),s].filter(h).join("")}function st(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var s=o[n];t[s]=e[s]}}var u=t.type;return"expr"===u?st(t.expr):nt[u]?nt[u](t):L(t)}}function ut(r){return r?(Array.isArray(r)||(r=[r]),r.map(st)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=w(t);switch(n){case"ORDER BY":e=r.map((function(r){return[st(r.expr),r.type||"ASC",w(r.nulls)].filter(h).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return st(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,s=e.value,u=[w(t)];return s||u.push(o),u.push(Array.isArray(n)?n.map(L).join("."):L(n)),s&&u.push(o),u.push(st(s)),u.filter(h).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return y(r,t);var e=r.expr,n=r.offset,o=r.suffix,s=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),L(r.value),"".concat(r.name?")":""),"]"].filter(h).join("")})).join("");return[st(e),s,o].filter(h).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s=o.brackets?"[".concat(L(o.index),"]"):"".concat(o.notation).concat(L(o.index));o.property&&(s="".concat(s,".").concat(L(o.property))),e.push(s)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function dt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,s=r.db,u=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,d=r.order_by,L=r.subFields,C=void 0===L?[]:L,m="*"===n?"*":bt(n,u),E=[s,f,p].filter(h).map((function(r){return"".concat("string"==typeof r?y(r):st(r))})),A=E[0];if(A){for(var T=1;T<E.length;++T)A="".concat(A).concat(c[T]||".").concat(E[T]);m="".concat(A).concat(c[T]||".").concat(m)}var _=[m=["".concat(m).concat(vt(t))].concat(lt(C)).join("."),it(o),st(l),a("AS",st,e)];_.push("string"==typeof v?w(v):st(v)),_.push(w(d));var I=_.filter(h).join(" ");return b?"(".concat(I,")"):I}function yt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,s=r.expr,u=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(s&&(u+=st(s)),r.array){var a=Pr(r);u+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return u}}function wt(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,s=r.table,u=r.on_action;return t.push(w(n)),t.push(cr(s)),t.push(e&&"(".concat(e.map((function(r){return st(r)})).join(", "),")")),t.push(w(o)),u.map((function(r){return t.push(w(r.type),st(r.value))})),t.filter(h)}function ht(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,s=r.comment,u=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,d=r.unique,y=r.primary_key,m=r.column_format,E=r.reference_definition,A=[w(e&&e.action),w(e&&e.value)].filter(h).join(" ");if(b||t.push(A),f){var T=f.type,I=f.value;t.push(T.toUpperCase(),st(I))}var g=p().database;return u&&t.push(w(u.keyword),L(u.constraint)),t.push(jr(o)),t.push(function(r){if(r)return[w(r.value),"(".concat(st(r.expr),")"),w(r.storage_type)].filter(h).join(" ")}(b)),b&&t.push(A),t.push(N(v),w(y),w(d),_(s)),t.push.apply(t,lt(C(n))),"sqlite"!==g.toLowerCase()&&t.push(st(i)),t.push.apply(t,lt(C(m))),t.push.apply(t,lt(C(c))),t.push.apply(t,lt(wt(E))),t.push(a("USING",st,l)),t.filter(h).join(" ")}function Lt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,s=r.order_by,u="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return u.collate=null,[st(u),st(e),o,w(s),w(n)].filter(h).join(" ")}function Ct(r){var t=[],e=dt(r.column),n=yt(r.definition);return t.push(e),t.push(n),t.push(ht(r)),t.filter(h).join(" ")}function mt(r){return r?"object"===ct(r)?["AS",st(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?y(r):d(r)].join(" "):""}function Et(r,t){var e=r.expr,n=r.type;if("cast"===n)return Gr(r);t&&(e.isDual=t);var o=st(e),s=r.expr_list;if(s){var u=[o],a=s.map((function(r){return Et(r,t)})).join(", ");return u.push([w(n),n&&"(",a,n&&")"].filter(h).join("")),u.filter(h).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,mt(r.as)].filter(h).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function Tt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return Et(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=ut(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",s=t.join(o);return e?"(".concat(s,")"):s},nt.select=function(r){var t="object"===et(r._next)?wr(r):B(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",s="".concat(t).concat(o).concat(st(n));return e?"(".concat(s,")"):s},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[L(r.key),L(r.value)].join(", ")})).join(", ");return[w(t),"[".concat(e,"]")].join("")};var _t=e(0);function It(r){return(It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var gt,St,Nt,Ot=(gt={},St="mysql",Nt=_t.parse,(St=function(r){var t=function(r,t){if("object"!=It(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=It(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==It(t)?t:t+""}(St))in gt?Object.defineProperty(gt,St,{value:Nt,enumerable:!0,configurable:!0,writable:!0}):gt[St]=Nt,gt);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function xt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return jt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?jt(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function jt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&kt(r.prototype,t),e&&kt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),Er(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),st(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return Et(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=t.database,n=void 0===e?"mysql":e;b(t);var o=n.toLowerCase();if(Ot[o])return Ot[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||s.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=xt(i);try{for(f.s();!(u=f.n()).done;){var p,b=u.value,v=!1,d=xt(t);try{for(d.s();!(p=d.n()).done;){var y=p.value,w=new RegExp("^".concat(y,"$"),"i");if(w.test(b)){v=!0;break}}}catch(r){d.e(r)}finally{d.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=mysql.js.map