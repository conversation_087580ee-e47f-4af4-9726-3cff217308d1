!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?s(r.parts[t][0])+"-"+s(r.parts[t][1]):s(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function s(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,s=new Array(r.length);for(t=0;t<r.length;t++)s[t]=(o=r[t],e[o.type](o));if(s.sort(),s.length>0){for(t=1,n=1;t<s.length;t++)s[t-1]!==s[t]&&(s[n]=s[t],n++);s.length=n}switch(s.length){case 1:return s[0];case 2:return s[0]+" or "+s[1];default:return s.slice(0,-1).join(", ")+", or "+s[s.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,s={},u={start:Ti},a=Ti,i=function(r,t){return Cb(r,t)},c=function(r,t){return{...r,order_by:t&&t.toLowerCase()}},l=function(r,t){return Cb(r,t,1)},f=mi("IF",!0),p="IDENTIFIED",b=mi("IDENTIFIED",!1),v=mi("WITH",!0),d=mi("BY",!0),y=mi("RANDOM",!0),w=mi("PASSWORD",!0),h=mi("AS",!0),L=function(r,t){return Cb(r,t)},m=mi("role",!0),C=mi("NONE",!0),A=mi("SSL",!0),E=mi("X509",!0),g=mi("CIPHER",!0),_=mi("ISSUER",!0),T=mi("SUBJECT",!0),S=function(r,t){return t.prefix=r.toLowerCase(),t},x=mi("REQUIRE",!0),I=mi("MAX_QUERIES_PER_HOUR",!0),j=mi("MAX_UPDATES_PER_HOUR",!0),N=mi("MAX_CONNECTIONS_PER_HOUR",!0),R=mi("MAX_USER_CONNECTIONS",!0),k=mi("EXPIRE",!0),O=mi("DEFAULT",!0),U=mi("NEVER",!0),M=mi("HISTORY",!0),D=mi("REUSE",!1),P=mi("CURRENT",!0),$=mi("OPTIONAL",!0),G=mi("FAILED_LOGIN_ATTEMPTS",!0),F=mi("PASSWORD_LOCK_TIME",!0),H=mi("UNBOUNDED",!0),Y=mi("ACCOUNT",!0),W=mi("LOCK",!0),B=mi("UNLOCK",!0),q=mi("ATTRIBUTE",!0),X=mi("CASCADED",!0),V=mi("LOCAL",!0),K=mi("CHECK",!0),Q=mi("OPTION",!1),z=mi("ALGORITHM",!0),Z=mi("UNDEFINED",!0),J=mi("MERGE",!0),rr=mi("TEMPTABLE",!0),tr=mi("SQL",!0),er=mi("SECURITY",!0),nr=mi("DEFINER",!0),or=mi("INVOKER",!0),sr=function(r,t){return Cb(r,t)},ur=mi("AUTO_INCREMENT",!0),ar=mi("UNIQUE",!0),ir=mi("KEY",!0),cr=mi("PRIMARY",!0),lr=mi("@",!1),fr=function(){return Lb("=",{type:"origin",value:"definer"},{type:"function",name:{name:[{type:"default",value:"current_user"}]},args:{type:"expr_list",value:[]}})},pr=mi("BEFORE",!0),br=mi("AFTER",!0),vr=mi("FOR",!0),dr=mi("EACH",!0),yr=mi("ROW",!0),wr=mi("STATEMENT",!0),hr=mi("FOLLOWS",!0),Lr=mi("PRECEDES",!0),mr=mi("COLUMN_FORMAT",!0),Cr=mi("FIXED",!0),Ar=mi("DYNAMIC",!0),Er=mi("STORAGE",!0),gr=mi("DISK",!0),_r=mi("MEMORY",!0),Tr=mi("GENERATED",!0),Sr=mi("ALWAYS",!0),xr=mi("STORED",!0),Ir=mi("VIRTUAL",!0),jr=mi("if",!0),Nr=mi("exists",!0),Rr=mi("first",!0),kr=mi("after",!0),Or=mi("LESS",!0),Ur=mi("THAN",!0),Mr=mi("DROP",!0),Dr=mi("TRUNCATE",!0),Pr=mi("DISCARD",!0),$r=mi("IMPORT",!0),Gr=mi("COALESCE",!0),Fr=mi("ANALYZE",!0),Hr=mi("TABLESPACE",!0),Yr=mi("FOREIGN",!0),Wr=mi("INSTANT",!0),Br=mi("INPLACE",!0),qr=mi("COPY",!0),Xr=mi("SHARED",!0),Vr=mi("EXCLUSIVE",!0),Kr=mi("CHANGE",!0),Qr=/^[0-9]/,zr=Ci([["0","9"]],!1,!1),Zr=mi("PRIMARY KEY",!0),Jr=mi("NOT",!0),rt=mi("REPLICATION",!0),tt=mi("FOREIGN KEY",!0),et=mi("ENFORCED",!0),nt=mi("MATCH FULL",!0),ot=mi("MATCH PARTIAL",!0),st=mi("MATCH SIMPLE",!0),ut=mi("RESTRICT",!0),at=mi("CASCADE",!0),it=mi("SET NULL",!0),ct=mi("NO ACTION",!0),lt=mi("SET DEFAULT",!0),ft=mi("CHARACTER",!0),pt=mi("SET",!0),bt=mi("CHARSET",!0),vt=mi("COLLATE",!0),dt=mi("AVG_ROW_LENGTH",!0),yt=mi("KEY_BLOCK_SIZE",!0),wt=mi("MAX_ROWS",!0),ht=mi("MIN_ROWS",!0),Lt=mi("STATS_SAMPLE_PAGES",!0),mt=mi("CHECKSUM",!1),Ct=mi("DELAY_KEY_WRITE",!1),At=/^[01]/,Et=Ci(["0","1"],!1,!1),gt=mi("CONNECTION",!0),_t=mi("ENGINE_ATTRIBUTE",!0),Tt=mi("SECONDARY_ENGINE_ATTRIBUTE",!0),St=mi("DATA",!0),xt=mi("INDEX",!0),It=mi("DIRECTORY",!0),jt=mi("COMPRESSION",!0),Nt=mi("'",!1),Rt=mi("ZLIB",!0),kt=mi("LZ4",!0),Ot=mi("ENGINE",!0),Ut=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}},Mt=mi("ROW_FORMAT",!0),Dt=mi("COMPRESSED",!0),Pt=mi("REDUNDANT",!0),$t=mi("COMPACT",!0),Gt=mi("BINARY",!0),Ft=mi("MASTER",!0),Ht=mi("LOGS",!0),Yt=mi("TRIGGERS",!0),Wt=mi("STATUS",!0),Bt=mi("PROCESSLIST",!0),qt=mi("PROCEDURE",!0),Xt=mi("FUNCTION",!0),Vt=mi("BINLOG",!0),Kt=mi("EVENTS",!0),Qt=mi("COLLATION",!0),zt=mi("DATABASES",!0),Zt=mi("COLUMNS",!0),Jt=mi("INDEXES",!0),re=mi("EVENT",!0),te=mi("GRANTS",!0),ee=function(r,t){return Cb(r,t)},ne=mi("READ",!0),oe=mi("LOW_PRIORITY",!0),se=mi("WRITE",!0),ue=mi("VIEW",!0),ae=mi("GRANT",!0),ie=mi("OPTION",!0),ce=function(r){return{type:"origin",value:Array.isArray(r)?r[0]:r}},le=mi("ROUTINE",!0),fe=mi("EXECUTE",!0),pe=mi("ADMIN",!0),be=mi("GRANT",!1),ve=mi("PROXY",!1),de=mi("(",!1),ye=mi(")",!1),we=mi("IN",!0),he=mi("SHARE",!0),Le=mi("MODE",!0),me=mi("WAIT",!0),Ce=mi("NOWAIT",!0),Ae=mi("SKIP",!0),Ee=mi("LOCKED",!0),ge=mi("NATURAL",!0),_e=mi("LANGUAGE",!0),Te=mi("QUERY",!0),Se=mi("EXPANSION",!0),xe=mi("BOOLEAN",!0),Ie=mi("MATCH",!0),je=mi("AGAINST",!1),Ne=mi("OUTFILE",!0),Re=mi("DUMPFILE",!0),ke=mi("BTREE",!0),Oe=mi("HASH",!0),Ue=mi("PARSER",!0),Me=mi("VISIBLE",!0),De=mi("INVISIBLE",!0),Pe=mi("LATERAL",!0),$e=/^[_0-9]/,Ge=Ci(["_",["0","9"]],!1,!1),Fe=mi("ROLLUP",!0),He=mi("?",!1),Ye=mi("=",!1),We=mi("DUPLICATE",!0),Be=function(r,t){return Ab(r,t)},qe=function(r){return r[0]+" "+r[2]},Xe=mi(">=",!1),Ve=mi(">",!1),Ke=mi("<=",!1),Qe=mi("<>",!1),ze=mi("<",!1),Ze=mi("!=",!1),Je=mi("ESCAPE",!0),rn=mi("+",!1),tn=mi("-",!1),en=mi("*",!1),nn=mi("/",!1),on=mi("%",!1),sn=mi("||",!1),un=mi("div",!0),an=mi("mod",!0),cn=mi("&",!1),ln=mi(">>",!1),fn=mi("<<",!1),pn=mi("^",!1),bn=mi("|",!1),vn=mi("!",!1),dn=mi("~",!1),yn=function(r){return{type:"default",value:r}},wn=function(r){return!0===db[r.toUpperCase()]},hn=mi('"',!1),Ln=/^[^"]/,mn=Ci(['"'],!0,!1),Cn=/^[^']/,An=Ci(["'"],!0,!1),En=mi("`",!1),gn=/^[^`\\]/,_n=Ci(["`","\\"],!0,!1),Tn=function(r,t){return r+t.join("")},Sn=/^[A-Za-z_\u4E00-\u9FA5]/,xn=Ci([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),In=/^[A-Za-z0-9_$$\u4E00-\u9FA5\xC0-\u017F]/,jn=Ci([["A","Z"],["a","z"],["0","9"],"_","$","$",["一","龥"],["À","ſ"]],!1,!1),Nn=/^[A-Za-z0-9_:]/,Rn=Ci([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),kn=mi(":",!1),On=mi("NOW",!0),Un=mi("OVER",!0),Mn=mi("WINDOW",!0),Dn=mi("FOLLOWING",!0),Pn=mi("PRECEDING",!0),$n=mi("SEPARATOR",!0),Gn=mi("YEAR_MONTH",!0),Fn=mi("DAY_HOUR",!0),Hn=mi("DAY_MINUTE",!0),Yn=mi("DAY_SECOND",!0),Wn=mi("DAY_MICROSECOND",!0),Bn=mi("HOUR_MINUTE",!0),qn=mi("HOUR_SECOND",!0),Xn=mi("HOUR_MICROSECOND",!0),Vn=mi("MINUTE_SECOND",!0),Kn=mi("MINUTE_MICROSECOND",!0),Qn=mi("SECOND_MICROSECOND",!0),zn=mi("TIMEZONE_HOUR",!0),Zn=mi("TIMEZONE_MINUTE",!0),Jn=mi("CENTURY",!0),ro=mi("DAY",!0),to=mi("DATE",!0),eo=mi("DECADE",!0),no=mi("DOW",!0),oo=mi("DOY",!0),so=mi("EPOCH",!0),uo=mi("HOUR",!0),ao=mi("ISODOW",!0),io=mi("ISOWEEK",!0),co=mi("ISOYEAR",!0),lo=mi("MICROSECONDS",!0),fo=mi("MILLENNIUM",!0),po=mi("MILLISECONDS",!0),bo=mi("MINUTE",!0),vo=mi("MONTH",!0),yo=mi("QUARTER",!0),wo=mi("SECOND",!0),ho=mi("TIME",!0),Lo=mi("TIMEZONE",!0),mo=mi("WEEK",!0),Co=mi("YEAR",!0),Ao=mi("DATE_TRUNC",!0),Eo=mi("BOTH",!0),go=mi("LEADING",!0),_o=mi("TRAILING",!0),To=mi("trim",!0),So=mi("convert",!0),xo=mi("binary",!0),Io=mi("_binary",!0),jo=mi("_latin1",!0),No=mi("X",!0),Ro=/^[0-9A-Fa-f]/,ko=Ci([["0","9"],["A","F"],["a","f"]],!1,!1),Oo=mi("b",!0),Uo=mi("0x",!0),Mo=mi("N",!0),Do=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},Po=/^[^"\\\0-\x1F\x7F]/,$o=Ci(['"',"\\",["\0",""],""],!0,!1),Go=/^[\n]/,Fo=Ci(["\n"],!1,!1),Ho=/^[^'\\]/,Yo=Ci(["'","\\"],!0,!1),Wo=mi("\\'",!1),Bo=mi('\\"',!1),qo=mi("\\\\",!1),Xo=mi("\\/",!1),Vo=mi("\\b",!1),Ko=mi("\\f",!1),Qo=mi("\\n",!1),zo=mi("\\r",!1),Zo=mi("\\t",!1),Jo=mi("\\u",!1),rs=mi("\\",!1),ts=mi("''",!1),es=mi('""',!1),ns=mi("``",!1),os=/^[\n\r]/,ss=Ci(["\n","\r"],!1,!1),us=mi(".",!1),as=/^[0-9a-fA-F]/,is=Ci([["0","9"],["a","f"],["A","F"]],!1,!1),cs=/^[eE]/,ls=Ci(["e","E"],!1,!1),fs=/^[+\-]/,ps=Ci(["+","-"],!1,!1),bs=mi("NULL",!0),vs=mi("NOT NULL",!0),ds=mi("TRUE",!0),ys=mi("TO",!0),ws=mi("FALSE",!0),hs=mi("SHOW",!0),Ls=mi("USE",!0),ms=mi("ALTER",!0),Cs=mi("SELECT",!0),As=mi("UPDATE",!0),Es=mi("CREATE",!0),gs=mi("TEMPORARY",!0),_s=mi("DELETE",!0),Ts=mi("INSERT",!0),Ss=mi("RECURSIVE",!0),xs=mi("REPLACE",!0),Is=mi("RETURNING",!0),js=mi("RENAME",!0),Ns=mi("IGNORE",!0),Rs=mi("EXPLAIN",!0),ks=mi("PARTITION",!0),Os=mi("INTO",!0),Us=mi("FROM",!0),Ms=mi("TABLE",!0),Ds=mi("TRIGGER",!0),Ps=mi("TABLES",!0),$s=mi("DATABASE",!0),Gs=mi("SCHEMA",!0),Fs=mi("ON",!0),Hs=mi("LEFT",!0),Ys=mi("RIGHT",!0),Ws=mi("FULL",!0),Bs=mi("INNER",!0),qs=mi("CROSS",!0),Xs=mi("JOIN",!0),Vs=mi("OUTER",!0),Ks=mi("UNION",!0),Qs=mi("MINUS",!0),zs=mi("INTERSECT",!0),Zs=mi("VALUES",!0),Js=mi("USING",!0),ru=mi("WHERE",!0),tu=mi("GROUP",!0),eu=mi("ORDER",!0),nu=mi("HAVING",!0),ou=mi("LIMIT",!0),su=mi("OFFSET",!0),uu=mi("ASC",!0),au=mi("DESC",!0),iu=mi("DESCRIBE",!0),cu=mi("ALL",!0),lu=mi("DISTINCT",!0),fu=mi("BETWEEN",!0),pu=mi("IS",!0),bu=mi("LIKE",!0),vu=mi("RLIKE",!0),du=mi("REGEXP",!0),yu=mi("EXISTS",!0),wu=mi("AND",!0),hu=mi("OR",!0),Lu=mi("COUNT",!0),mu=mi("GROUP_CONCAT",!0),Cu=mi("MAX",!0),Au=mi("MIN",!0),Eu=mi("SUM",!0),gu=mi("AVG",!0),_u=mi("EXTRACT",!0),Tu=mi("CALL",!0),Su=mi("CASE",!0),xu=mi("WHEN",!0),Iu=mi("THEN",!0),ju=mi("ELSE",!0),Nu=mi("END",!0),Ru=mi("CAST",!0),ku=mi("VARBINARY",!0),Ou=mi("BIT",!0),Uu=mi("CHAR",!0),Mu=mi("VARCHAR",!0),Du=mi("NUMERIC",!0),Pu=mi("DECIMAL",!0),$u=mi("SIGNED",!0),Gu=mi("UNSIGNED",!0),Fu=mi("INT",!0),Hu=mi("ZEROFILL",!0),Yu=mi("INTEGER",!0),Wu=mi("JSON",!0),Bu=mi("SMALLINT",!0),qu=mi("MEDIUMINT",!0),Xu=mi("TINYINT",!0),Vu=mi("TINYTEXT",!0),Ku=mi("TEXT",!0),Qu=mi("MEDIUMTEXT",!0),zu=mi("LONGTEXT",!0),Zu=mi("BIGINT",!0),Ju=mi("ENUM",!0),ra=mi("FLOAT",!0),ta=mi("DOUBLE",!0),ea=mi("DATETIME",!0),na=mi("ROWS",!0),oa=mi("TIMESTAMP",!0),sa=mi("USER",!0),ua=mi("UUID",!0),aa=mi("CURRENT_DATE",!0),ia=(mi("ADDDATE",!0),mi("INTERVAL",!0)),ca=mi("CURRENT_TIME",!0),la=mi("CURRENT_TIMESTAMP",!0),fa=mi("CURRENT_USER",!0),pa=mi("SESSION_USER",!0),ba=mi("SYSTEM_USER",!0),va=mi("GLOBAL",!0),da=mi("SESSION",!0),ya=mi("PERSIST",!0),wa=mi("PERSIST_ONLY",!0),ha=mi("GEOMETRY",!0),La=mi("POINT",!0),ma=mi("LINESTRING",!0),Ca=mi("POLYGON",!0),Aa=mi("MULTIPOINT",!0),Ea=mi("MULTILINESTRING",!0),ga=mi("MULTIPOLYGON",!0),_a=mi("GEOMETRYCOLLECTION",!0),Ta=mi("@@",!1),Sa=mi("$",!1),xa=mi("return",!0),Ia=mi(":=",!1),ja=mi("DUAL",!0),Na=mi("ADD",!0),Ra=mi("COLUMN",!0),ka=mi("MODIFY",!0),Oa=mi("FULLTEXT",!0),Ua=mi("SPATIAL",!0),Ma=mi("COMMENT",!0),Da=mi("CONSTRAINT",!0),Pa=mi("REFERENCES",!0),$a=mi("SQL_CALC_FOUND_ROWS",!0),Ga=mi("SQL_CACHE",!0),Fa=mi("SQL_NO_CACHE",!0),Ha=mi("SQL_SMALL_RESULT",!0),Ya=mi("SQL_BIG_RESULT",!0),Wa=mi("SQL_BUFFER_RESULT",!0),Ba=mi(",",!1),qa=mi("[",!1),Xa=mi("]",!1),Va=mi(";",!1),Ka=mi("&&",!1),Qa=mi("XOR",!0),za=mi("/*",!1),Za=mi("*/",!1),Ja=mi("--",!1),ri=mi("#",!1),ti={type:"any"},ei=/^[ \t\n\r]/,ni=Ci([" ","\t","\n","\r"],!1,!1),oi=function(r,t,e){return{type:"assign",left:r,symbol:t,right:e}},si=mi("boolean",!0),ui=mi("blob",!0),ai=mi("tinyblob",!0),ii=mi("mediumblob",!0),ci=mi("longblob",!0),li=function(r,t){return{dataType:r,...t||{}}},fi=mi("ARRAY",!0),pi=/^[0-6]/,bi=Ci([["0","6"]],!1,!1),vi=0,di=0,yi=[{line:1,column:1}],wi=0,hi=[],Li=0;if("startRule"in t){if(!(t.startRule in u))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=u[t.startRule]}function mi(r,t){return{type:"literal",text:r,ignoreCase:t}}function Ci(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Ai(t){var e,n=yi[t];if(n)return n;for(e=t-1;!yi[e];)e--;for(n={line:(n=yi[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return yi[t]=n,n}function Ei(r,t){var e=Ai(r),n=Ai(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function gi(r){vi<wi||(vi>wi&&(wi=vi,hi=[]),hi.push(r))}function _i(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Ti(){var r,t;return r=vi,Bp()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=xi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Yp())!==s&&(a=Bp())!==s&&(i=xi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Yp())!==s&&(a=Bp())!==s&&(i=xi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Sb),columnList:gb(xb),ast:n}}(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=r,r=t):(vi=r,r=s),r}function Si(){var t;return(t=function(){var r,t,e,n,o,u,a;r=vi,(t=vf())!==s&&Bp()!==s&&(e=jf())!==s&&Bp()!==s?((n=Hi())===s&&(n=null),n!==s&&Bp()!==s&&(o=xc())!==s?(di=r,i=t,c=e,f=n,(p=o)&&p.forEach(r=>Sb.add(`${i}::${r.db}::${r.table}`)),t={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:i.toLowerCase(),keyword:c.toLowerCase(),prefix:f,name:p}},r=t):(vi=r,r=s)):(vi=r,r=s);var i,c,f,p;r===s&&(r=vi,(t=vf())!==s&&Bp()!==s&&(e=gp())!==s&&Bp()!==s?((n=Hi())===s&&(n=null),n!==s&&Bp()!==s&&(o=xc())!==s&&Bp()!==s?((u=tc())===s&&(u=null),u!==s?(di=r,t=function(r,t,e,n,o){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:n,options:o&&[{type:"origin",value:o}]}}}(t,e,n,o,u),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=vf())!==s&&Bp()!==s&&(e=Np())!==s&&Bp()!==s&&(n=yl())!==s&&Bp()!==s&&(o=Uf())!==s&&Bp()!==s&&(u=Rc())!==s&&Bp()!==s?((a=function(){var r,t,e,n,o,u;r=vi,(t=qi())===s&&(t=Xi());if(t!==s){for(e=[],n=vi,(o=Bp())!==s?((u=qi())===s&&(u=Xi()),u!==s?n=o=[o,u]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=qi())===s&&(u=Xi()),u!==s?n=o=[o,u]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,t=l(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(a=null),a!==s&&Bp()!==s?(di=r,t=function(r,t,e,n,o){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,u,a),r=t):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=vf())!==s&&Bp()!==s?((e=kf())===s&&(e=Of()),e!==s&&Bp()!==s?((n=Hi())===s&&(n=null),n!==s&&Bp()!==s&&(o=jl())!==s?(di=r,t=function(r,t,e,n){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:n}}}(t,e,n,o),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=vf())!==s&&Bp()!==s&&(e=Nf())!==s&&Bp()!==s?((n=Hi())===s&&(n=null),n!==s&&Bp()!==s&&(o=jc())!==s?(di=r,t=function(r,t,e,n){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:[{schema:n.db,trigger:n.table}]}}}(t,e,n,o),r=t):(vi=r,r=s)):(vi=r,r=s)))));return r}())===s&&(t=function(){var t;(t=function(){var r,t,e,n,o,u,a,c,l,f;r=vi,(t=hf())!==s&&Bp()!==s?((e=Lf())===s&&(e=null),e!==s&&Bp()!==s&&jf()!==s&&Bp()!==s?((n=Ri())===s&&(n=null),n!==s&&Bp()!==s&&(o=xc())!==s&&Bp()!==s&&(u=function(){var r,t,e,n,o,u,a,i,c;if(r=vi,(t=Fp())!==s)if(Bp()!==s)if((e=Di())!==s){for(n=[],o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=Di())!==s?o=u=[u,a,i,c]:(vi=o,o=s);o!==s;)n.push(o),o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=Di())!==s?o=u=[u,a,i,c]:(vi=o,o=s);n!==s&&(o=Bp())!==s&&(u=Hp())!==s?(di=r,t=sr(e,n),r=t):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;return r}())!==s&&Bp()!==s?((a=function(){var r,t,e,n,o,u,a,c;if(r=vi,(t=oc())!==s){for(e=[],n=vi,(o=Bp())!==s?((u=$p())===s&&(u=null),u!==s&&(a=Bp())!==s&&(c=oc())!==s?n=o=[o,u,a,c]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=$p())===s&&(u=null),u!==s&&(a=Bp())!==s&&(c=oc())!==s?n=o=[o,u,a,c]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,t=i(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(a=null),a!==s&&Bp()!==s?((c=gf())===s&&(c=Af()),c===s&&(c=null),c!==s&&Bp()!==s?((l=If())===s&&(l=null),l!==s&&Bp()!==s?((f=ji())===s&&(f=null),f!==s?(di=r,p=t,b=e,v=n,y=u,w=a,h=c,L=l,m=f,(d=o)&&d.forEach(r=>Sb.add(`create::${r.db}::${r.table}`)),t={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:v,table:d,ignore_replace:h&&h[0].toLowerCase(),as:L&&L[0].toLowerCase(),query_expr:m&&m.ast,create_definitions:y,table_options:w}},r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);var p,b,v,d,y,w,h,L,m;r===s&&(r=vi,(t=hf())!==s&&Bp()!==s?((e=Lf())===s&&(e=null),e!==s&&Bp()!==s&&jf()!==s&&Bp()!==s?((n=Ri())===s&&(n=null),n!==s&&Bp()!==s&&(o=xc())!==s&&Bp()!==s&&(u=function r(){var t,e;(t=function(){var r,t,e;r=vi,(t=Kf())!==s&&Bp()!==s&&(e=xc())!==s?(di=r,t=function(r){return{type:"like",table:r}}(e),r=t):(vi=r,r=s);return r}())===s&&(t=vi,Fp()!==s&&Bp()!==s&&(e=r())!==s&&Bp()!==s&&Hp()!==s?(di=t,(n=e).parentheses=!0,t=n):(vi=t,t=s));var n;return t}())!==s?(di=r,t=function(r,t,e,n,o){return n&&n.forEach(r=>Sb.add(`create::${r.db}::${r.table}`)),{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,u),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=vi,(e=hf())!==s&&Bp()!==s?((n=Gi())===s&&(n=null),n!==s&&Bp()!==s&&Nf()!==s&&Bp()!==s?((o=Ri())===s&&(o=null),o!==s&&Bp()!==s&&(u=Rc())!==s&&Bp()!==s&&(a=function(){var t;"before"===r.substr(vi,6).toLowerCase()?(t=r.substr(vi,6),vi+=6):(t=s,0===Li&&gi(pr));t===s&&("after"===r.substr(vi,5).toLowerCase()?(t=r.substr(vi,5),vi+=5):(t=s,0===Li&&gi(br)));return t}())!==s&&Bp()!==s&&(i=function(){var r,t;r=vi,(t=Cf())===s&&(t=wf())===s&&(t=mf());t!==s&&(di=r,t={keyword:t[0].toLowerCase()});return r=t}())!==s&&Bp()!==s&&Uf()!==s&&Bp()!==s&&(c=Rc())!==s&&Bp()!==s&&(l=function(){var t,e,n,o;t=vi,"for"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(vr));e!==s&&Bp()!==s?("each"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(dr)),n===s&&(n=null),n!==s&&Bp()!==s?("row"===r.substr(vi,3).toLowerCase()?(o=r.substr(vi,3),vi+=3):(o=s,0===Li&&gi(yr)),o===s&&("statement"===r.substr(vi,9).toLowerCase()?(o=r.substr(vi,9),vi+=9):(o=s,0===Li&&gi(wr))),o!==s?(di=t,u=e,i=o,e={keyword:(a=n)?`${u.toLowerCase()} ${a.toLowerCase()}`:u.toLowerCase(),args:i.toLowerCase()},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var u,a,i;return t}())!==s&&Bp()!==s?((f=function(){var t,e,n;t=vi,"follows"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(hr));e===s&&("precedes"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Lr)));e!==s&&Bp()!==s&&(n=jl())!==s?(di=t,t=e={keyword:e,trigger:n}):(vi=t,t=s);return t}())===s&&(f=null),f!==s&&Bp()!==s&&(p=function(){var r,t;r=vi,xf()!==s&&Bp()!==s&&(t=Fc())!==s?(di=r,r={type:"set",expr:t}):(vi=r,r=s);return r}())!==s?(di=t,b=e,v=n,d=o,y=u,w=a,h=i,L=c,m=l,C=f,A=p,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:b[0].toLowerCase(),definer:v,keyword:"trigger",for_each:m,if_not_exists:d,trigger:y,time:w,events:[h],order:C,table:L,execute:A}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var b,v,d,y,w,h,L,m,C,A;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,c,l,f,p,b;r=vi,(t=hf())!==s&&Bp()!==s?((e=Up())===s&&(e=kp())===s&&(e=Op()),e===s&&(e=null),e!==s&&Bp()!==s&&(n=Np())!==s&&Bp()!==s&&(o=Cl())!==s&&Bp()!==s?((u=_c())===s&&(u=null),u!==s&&Bp()!==s&&(a=Uf())!==s&&Bp()!==s&&(c=Rc())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(l=function(){var r,t,e,n,o,u,a,c;if(r=vi,(t=Ni())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=Ni())!==s?n=o=[o,u,a,c]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=Ni())!==s?n=o=[o,u,a,c]:(vi=n,n=s);e!==s?(di=r,t=i(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((f=Tc())===s&&(f=null),f!==s&&Bp()!==s?((p=qi())===s&&(p=null),p!==s&&Bp()!==s?((b=Xi())===s&&(b=null),b!==s&&Bp()!==s?(di=r,v=t,d=e,y=n,w=o,h=u,L=a,m=c,C=l,A=f,E=p,g=b,t={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:v[0].toLowerCase(),index_type:d&&d.toLowerCase(),keyword:y.toLowerCase(),index:w,on_kw:L[0].toLowerCase(),table:m,index_columns:C,index_using:h,index_options:A,algorithm_option:E,lock_option:g}},r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);var v,d,y,w,h,L,m,C,A,E,g;return r}())===s&&(t=function(){var r,t,e,n,o,u;r=vi,(t=hf())!==s&&Bp()!==s?((e=kf())===s&&(e=Of()),e!==s&&Bp()!==s?((n=Ri())===s&&(n=null),n!==s&&Bp()!==s&&(o=ob())!==s&&Bp()!==s?((u=function(){var r,t,e,n,o,u;if(r=vi,(t=nc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=nc())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=nc())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,t=l(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(u=null),u!==s?(di=r,t=function(r,t,e,n,o){const s=t.toLowerCase();return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r[0].toLowerCase(),keyword:s,if_not_exists:e,[s]:{db:n.schema,schema:n.name},create_definitions:o}}}(t,e,n,o,u),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h,L,m,C,A;t=vi,(e=hf())!==s&&Bp()!==s?(n=vi,(o=Jf())!==s&&(u=Bp())!==s&&(a=Af())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=null),n!==s&&(o=Bp())!==s?(u=vi,"algorithm"===r.substr(vi,9).toLowerCase()?(a=r.substr(vi,9),vi+=9):(a=s,0===Li&&gi(z)),a!==s&&(i=Bp())!==s&&(c=xp())!==s&&(l=Bp())!==s?("undefined"===r.substr(vi,9).toLowerCase()?(f=r.substr(vi,9),vi+=9):(f=s,0===Li&&gi(Z)),f===s&&("merge"===r.substr(vi,5).toLowerCase()?(f=r.substr(vi,5),vi+=5):(f=s,0===Li&&gi(J)),f===s&&("temptable"===r.substr(vi,9).toLowerCase()?(f=r.substr(vi,9),vi+=9):(f=s,0===Li&&gi(rr)))),f!==s?u=a=[a,i,c,l,f]:(vi=u,u=s)):(vi=u,u=s),u===s&&(u=null),u!==s&&(a=Bp())!==s?((i=Gi())===s&&(i=null),i!==s&&(c=Bp())!==s?(l=vi,"sql"===r.substr(vi,3).toLowerCase()?(f=r.substr(vi,3),vi+=3):(f=s,0===Li&&gi(tr)),f!==s&&(p=Bp())!==s?("security"===r.substr(vi,8).toLowerCase()?(b=r.substr(vi,8),vi+=8):(b=s,0===Li&&gi(er)),b!==s&&(v=Bp())!==s?("definer"===r.substr(vi,7).toLowerCase()?(d=r.substr(vi,7),vi+=7):(d=s,0===Li&&gi(nr)),d===s&&("invoker"===r.substr(vi,7).toLowerCase()?(d=r.substr(vi,7),vi+=7):(d=s,0===Li&&gi(or))),d!==s?l=f=[f,p,b,v,d]:(vi=l,l=s)):(vi=l,l=s)):(vi=l,l=s),l===s&&(l=null),l!==s&&(f=Bp())!==s&&(p=gp())!==s&&(b=Bp())!==s&&(v=Rc())!==s&&(d=Bp())!==s?(y=vi,(w=Fp())!==s&&(h=Bp())!==s&&(L=wl())!==s&&(m=Bp())!==s&&(C=Hp())!==s?y=w=[w,h,L,m,C]:(vi=y,y=s),y===s&&(y=null),y!==s&&(w=Bp())!==s&&(h=If())!==s&&(L=Bp())!==s&&(m=yc())!==s&&(C=Bp())!==s?((A=function(){var t,e,n,o,u;t=vi,(e=Gf())!==s&&Bp()!==s?("cascaded"===r.substr(vi,8).toLowerCase()?(n=r.substr(vi,8),vi+=8):(n=s,0===Li&&gi(X)),n===s&&("local"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(V))),n!==s&&Bp()!==s?("check"===r.substr(vi,5).toLowerCase()?(o=r.substr(vi,5),vi+=5):(o=s,0===Li&&gi(K)),o!==s&&Bp()!==s?("OPTION"===r.substr(vi,6)?(u="OPTION",vi+=6):(u=s,0===Li&&gi(Q)),u!==s?(di=t,e=`with ${n.toLowerCase()} check option`,t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);t===s&&(t=vi,(e=Gf())!==s&&Bp()!==s?("check"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(K)),n!==s&&Bp()!==s?("OPTION"===r.substr(vi,6)?(o="OPTION",vi+=6):(o=s,0===Li&&gi(Q)),o!==s?(di=t,t=e="with check option"):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s));return t}())===s&&(A=null),A!==s?(di=t,E=e,g=n,_=u,T=i,S=l,I=y,j=m,N=A,(x=v).view=x.table,delete x.table,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:E[0].toLowerCase(),keyword:"view",replace:g&&"or replace",algorithm:_&&_[4],definer:T,sql_security:S&&S[4],columns:I&&I[2],select:j,view:x,with:N}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var E,g,_,T,S,x,I,j,N;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p;t=vi,(e=hf())!==s&&Bp()!==s&&mp()!==s&&Bp()!==s?((n=Ri())===s&&(n=null),n!==s&&Bp()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=ki())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ki())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ki())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=L(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&Bp()!==s?((u=function(){var t,e,n;t=vi,ff()!==s&&Bp()!==s?("role"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(m)),e!==s&&Bp()!==s&&(n=cc())!==s?(di=t,t={keyword:"default role",value:n}):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(u=null),u!==s&&Bp()!==s?((a=function(){var t,e,n;t=vi,"require"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(x));e!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Oi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Zf())!==s&&(a=Bp())!==s&&(i=Oi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Zf())!==s&&(a=Bp())!==s&&(i=Oi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=Ab(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,t=e={keyword:"require",value:n}):(vi=t,t=s);return t}())===s&&(a=null),a!==s&&Bp()!==s?((i=function(){var r,t,e,n,o,u,a;if(r=vi,(t=Gf())!==s)if(Bp()!==s)if((e=Ui())!==s){for(n=[],o=vi,(u=Bp())!==s&&(a=Ui())!==s?o=u=[u,a]:(vi=o,o=s);o!==s;)n.push(o),o=vi,(u=Bp())!==s&&(a=Ui())!==s?o=u=[u,a]:(vi=o,o=s);n!==s?(di=r,t=function(r,t){const e=[r];if(t)for(const r of t)e.push(r[1]);return{keyword:"with",value:e}}(e,n),r=t):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;return r}())===s&&(i=null),i!==s&&Bp()!==s?((c=function(){var r,t,e,n,o,u;if(r=vi,(t=Mi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Mi())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Mi())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,t=Cb(t,e,1),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(c=null),c!==s&&Bp()!==s?((l=function(){var t,e,n;t=vi,"account"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Y));e!==s&&Bp()!==s?("lock"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(W)),n===s&&("unlock"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(B))),n!==s?(di=t,e=function(r){const t={type:"origin",value:r.toLowerCase(),prefix:"account"};return t}(n),t=e):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(l=null),l!==s&&Bp()!==s?((f=Vp())===s&&(f=null),f!==s&&Bp()!==s?((p=function(){var t,e,n;t=vi,"attribute"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(q));e!==s&&Bp()!==s&&(n=Jl())!==s?(di=t,(o=n).prefix="attribute",t=e=o):(vi=t,t=s);var o;return t}())===s&&(p=null),p!==s?(di=t,b=e,v=n,d=o,y=u,w=a,h=i,C=c,A=l,E=f,g=p,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:b[0].toLowerCase(),keyword:"user",if_not_exists:v,user:d,default_role:y,require:w,resource_options:h,password_options:C,lock_option:A,comment:E,attribute:g}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var b,v,d,y,w,h,C,A,E,g;return t}());return t}())===s&&(t=function(){var t,e,n,o;t=vi,(e=function(){var t,e,n,o;t=vi,"truncate"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Dr));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TRUNCATE"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=jf())===s&&(n=null),n!==s&&Bp()!==s&&(o=xc())!==s?(di=t,u=e,a=n,(i=o)&&i.forEach(r=>Sb.add(`${u}::${r.db}::${r.table}`)),e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:u.toLowerCase(),keyword:a&&a.toLowerCase()||"table",name:i}},t=e):(vi=t,t=s)):(vi=t,t=s);var u,a,i;return t}())===s&&(t=function(){var r,t,e;r=vi,(t=Ef())!==s&&Bp()!==s&&jf()!==s&&Bp()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=gc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=gc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=gc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=sr(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Sb.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"rename",table:n}},r=t):(vi=r,r=s);var n;return r}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;t=vi,"call"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Tu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CALL"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&(n=function(){var r;(r=sb())===s&&(r=ub());return r}())!==s?(di=t,o=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"call",expr:o}},t=e):(vi=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;t=vi,"use"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Ls));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&(n=Cl())!==s?(di=t,o=n,Sb.add(`use::${o}::null`),e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"use",db:o}},t=e):(vi=t,t=s);var o;return t}())===s&&(t=function(){var r,t,e,n;r=vi,(t=df())!==s&&Bp()!==s&&jf()!==s&&Bp()!==s&&(e=Rc())!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Wi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Wi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Wi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=sr(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=r,o=e,u=n,Sb.add(`alter::${o.db}::${o.table}`),t={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"alter",table:[o],expr:u}},r=t):(vi=r,r=s);var o,u;return r}())===s&&(t=function(){var t,e,n,o;t=vi,(e=xf())!==s&&Bp()!==s?((n=function(){var t,e,n,o;t=vi,"global"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(va));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="GLOBAL"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=vi,"session"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(da));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SESSION"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=vi,"local"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(V));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="LOCAL"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=vi,"persist"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(ya));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="PERSIST"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=vi,"persist_only"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(wa));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="PERSIST_ONLY"):(vi=t,t=s)):(vi=t,t=s);return t}()),n===s&&(n=null),n!==s&&Bp()!==s&&(o=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Jp())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Jp())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Jp())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=ee(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,u=n,(a=o).keyword=u,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"set",keyword:u,expr:a}},t=e):(vi=t,t=s)):(vi=t,t=s);var u,a;return t}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;t=vi,"lock"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(W));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&Rf()!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=sc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=sc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=sc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=ee(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,o=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(vi=t,t=s);var o;return t}())===s&&(t=function(){var t,e;t=vi,(e=function(){var t,e,n,o;t=vi,"unlock"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(B));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&Rf()!==s?(di=t,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"unlock",keyword:"tables"}},t=e):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=vi,(e=bf())!==s&&Bp()!==s?("binary"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Gt)),n===s&&("master"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Ft))),n!==s&&(o=Bp())!==s?("logs"===r.substr(vi,4).toLowerCase()?(u=r.substr(vi,4),vi+=4):(u=s,0===Li&&gi(Ht)),u!==s?(di=t,f=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",suffix:"logs",keyword:f.toLowerCase()}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var f;t===s&&(t=vi,(e=bf())!==s&&Bp()!==s&&(n=Rf())!==s?(di=t,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:"tables"}},t=e):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s?("triggers"===r.substr(vi,8).toLowerCase()?(n=r.substr(vi,8),vi+=8):(n=s,0===Li&&gi(Yt)),n===s&&("status"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Wt)),n===s&&("processlist"===r.substr(vi,11).toLowerCase()?(n=r.substr(vi,11),vi+=11):(n=s,0===Li&&gi(Bt)))),n!==s?(di=t,d=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:d.toLowerCase()}},t=e):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s?("procedure"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(qt)),n===s&&("function"===r.substr(vi,8).toLowerCase()?(n=r.substr(vi,8),vi+=8):(n=s,0===Li&&gi(Xt))),n!==s&&(o=Bp())!==s?("status"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(Wt)),u!==s?(di=t,e=function(r){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:r.toLowerCase(),suffix:"status"}}}(n),t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s?("binlog"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Vt)),n!==s&&(o=Bp())!==s?("events"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(Kt)),u!==s&&(a=Bp())!==s?((i=ll())===s&&(i=null),i!==s&&Bp()!==s?((c=Ec())===s&&(c=null),c!==s&&Bp()!==s?((l=Gc())===s&&(l=null),l!==s?(di=t,p=i,b=c,v=l,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",suffix:"events",keyword:"binlog",in:p,from:b,limit:v}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s?(n=vi,"character"===r.substr(vi,9).toLowerCase()?(o=r.substr(vi,9),vi+=9):(o=s,0===Li&&gi(ft)),o!==s&&(u=Bp())!==s?("set"===r.substr(vi,3).toLowerCase()?(a=r.substr(vi,3),vi+=3):(a=s,0===Li&&gi(pt)),a!==s?n=o=[o,u,a]:(vi=n,n=s)):(vi=n,n=s),n===s&&("collation"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Qt)),n===s&&("databases"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(zt)))),n!==s&&(o=Bp())!==s?((u=cl())===s&&(u=Oc()),u===s&&(u=null),u!==s?(di=t,e=function(r,t){let e=Array.isArray(r)&&r||[r];return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",suffix:e[2]&&e[2].toLowerCase(),keyword:e[0].toLowerCase(),expr:t}}}(n,u),t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s?("columns"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(Zt)),n===s&&("indexes"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(Jt)),n===s&&("index"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(xt)))),n!==s&&(o=Bp())!==s&&(u=Ec())!==s?(di=t,e=function(r,t){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:r.toLowerCase(),from:t}}}(n,u),t=e):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=bf())!==s&&Bp()!==s&&(n=hf())!==s&&(o=Bp())!==s?((u=gp())===s&&(u=jf())===s&&("event"===r.substr(vi,5).toLowerCase()?(u=r.substr(vi,5),vi+=5):(u=s,0===Li&&gi(re)),u===s&&(u=Nf())===s&&("procedure"===r.substr(vi,9).toLowerCase()?(u=r.substr(vi,9),vi+=9):(u=s,0===Li&&gi(qt)))),u!==s&&(a=Bp())!==s&&(i=Rc())!==s?(di=t,e=function(r,t){const e=r.toLowerCase();return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:"create",suffix:e,[e]:t}}}(u,i),t=e):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=function(){var t,e,n,o;t=vi,(e=bf())!==s&&Bp()!==s?("grants"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(te)),n!==s&&Bp()!==s?((o=function(){var t,e,n,o,u,a,i;t=vi,"for"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(vr));e!==s&&Bp()!==s&&(n=Cl())!==s&&Bp()!==s?(o=vi,(u=_p())!==s&&(a=Bp())!==s&&(i=Cl())!==s?o=u=[u,a,i]:(vi=o,o=s),o===s&&(o=null),o!==s&&(u=Bp())!==s?((a=function(){var r,t;r=vi,$f()!==s&&Bp()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Cl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Cl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Cl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=ee(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=r,r=t):(vi=r,r=s);return r}())===s&&(a=null),a!==s?(di=t,l=a,e={user:n,host:(c=o)&&c[2],role_list:l},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var c,l;return t}())===s&&(o=null),o!==s?(di=t,u=o,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"show",keyword:"grants",for:u}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var u;return t}()))))))));var p,b,v;var d;return t}())===s&&(t=function(){var t,e,n;t=vi,(e=Yf())===s&&(e=function(){var t,e,n,o;t=vi,"describe"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(iu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DESCRIBE"):(vi=t,t=s)):(vi=t,t=s);return t}());e!==s&&Bp()!==s&&(n=Cl())!==s?(di=t,o=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"desc",table:o}},t=e):(vi=t,t=s);var o;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l;t=vi,"grant"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ae));e!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=ac())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ac())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ac())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=L(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&Bp()!==s&&(o=Uf())!==s&&Bp()!==s?((u=function(){var t,e;t=vi,(e=jf())===s&&("function"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Xt)),e===s&&("procedure"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(qt))));e!==s&&(di=t,e={type:"origin",value:e.toUpperCase()});return t=e}())===s&&(u=null),u!==s&&Bp()!==s&&(a=function(){var r,t,e,n,o;r=vi,t=vi,(e=Cl())===s&&(e=Gp());e!==s&&(n=Bp())!==s&&(o=Pp())!==s?t=e=[e,n,o]:(vi=t,t=s);t===s&&(t=null);t!==s&&(e=Bp())!==s?((n=Cl())===s&&(n=Gp()),n!==s?(di=r,a=n,t={prefix:(u=t)&&u[0],name:a},r=t):(vi=r,r=s)):(vi=r,r=s);var u,a;return r}())!==s&&Bp()!==s&&(i=pf())!==s&&Bp()!==s&&(c=cc())!==s&&Bp()!==s?((l=function(){var t,e,n;t=vi,Gf()!==s&&Bp()!==s?("grant"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ae)),e!==s&&Bp()!==s?("option"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(ie)),n!==s?(di=t,t={type:"origin",value:"with grant option"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(l=null),l!==s?(di=t,f=n,p=u,b=a,v=i,d=c,y=l,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"grant",keyword:"priv",objects:f,on:{object_type:p,priv_level:[b]},to_from:v[0],user_or_roles:d,with:y}},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var f,p,b,v,d,y;t===s&&(t=vi,"GRANT"===r.substr(vi,5)?(e="GRANT",vi+=5):(e=s,0===Li&&gi(be)),e!==s&&Bp()!==s?("PROXY"===r.substr(vi,5)?(n="PROXY",vi+=5):(n=s,0===Li&&gi(ve)),n!==s&&Bp()!==s&&(o=Uf())!==s&&Bp()!==s&&(u=ic())!==s&&Bp()!==s&&(a=pf())!==s&&Bp()!==s&&(i=cc())!==s&&Bp()!==s?((c=lc())===s&&(c=null),c!==s?(di=t,e=function(r,t,e,n){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"grant",keyword:"proxy",objects:[{priv:{type:"origin",value:"proxy"}}],on:r,to_from:t[0],user_or_roles:e,with:n}}}(u,a,i,c),t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"GRANT"===r.substr(vi,5)?(e="GRANT",vi+=5):(e=s,0===Li&&gi(be)),e!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Cl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Cl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Cl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=L(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&Bp()!==s&&(o=pf())!==s&&Bp()!==s&&(u=cc())!==s&&Bp()!==s?((a=lc())===s&&(a=null),a!==s?(di=t,e=function(r,t,e,n){return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"grant",keyword:"role",objects:r.map(r=>({priv:{type:"string",value:r}})),to_from:t[0],user_or_roles:e,with:n}}}(n,o,u,a),t=e):(vi=t,t=s)):(vi=t,t=s)));return t}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;t=vi,"explain"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Rs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&(n=yc())!==s?(di=t,o=n,e={tableList:Array.from(Sb),columnList:gb(xb),ast:{type:"explain",expr:o}},t=e):(vi=t,t=s);var o;return t}()),t}function xi(){var r;return(r=ji())===s&&(r=function(){var r,t,e,n,o,u;r=vi,(t=Bp())!==s?((e=pc())===s&&(e=null),e!==s&&Bp()!==s&&wf()!==s&&Bp()!==s&&(n=xc())!==s&&Bp()!==s&&xf()!==s&&Bp()!==s&&(o=Fc())!==s&&Bp()!==s?((u=Oc())===s&&(u=null),u!==s?(di=r,t=function(r,t,e,n){const o={};return t&&t.forEach(r=>{const{db:t,as:e,table:n,join:s}=r,u=s?"select":"update";t&&(o[n]=t),n&&Sb.add(`${u}::${t}::${n}`)}),e&&e.forEach(r=>{if(r.table){const t=Eb(r.table);Sb.add(`update::${o[t]||null}::${t}`)}xb.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Sb),columnList:gb(xb),ast:{with:r,type:"update",table:t,set:e,where:n}}}(e,n,o,u),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i,c,l;r=vi,(t=Xc())!==s&&Bp()!==s?((e=gf())===s&&(e=null),e!==s&&Bp()!==s?((n=Tf())===s&&(n=null),n!==s&&Bp()!==s&&(o=Rc())!==s&&Bp()!==s?((u=Bc())===s&&(u=null),u!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(a=wl())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s&&(i=Wc())!==s&&Bp()!==s?((c=qc())===s&&(c=null),c!==s&&Bp()!==s?((l=Yc())===s&&(l=null),l!==s?(di=r,t=function(r,t,e,n,o,s,u,a,i){if(n&&(Sb.add(`insert::${n.db}::${n.table}`),n.as=null),s){let r=n&&n.table||null;Array.isArray(u)&&u.forEach((r,t)=>{if(r.value.length!=s.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),s.forEach(t=>xb.add(`insert::${r}::${t}`))}let c=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r,table:[n],columns:s,values:u,partition:o,prefix:c,on_duplicate_update:a,returning:i}}}(t,e,n,o,u,a,i,c,l),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i,c;r=vi,(t=Xc())!==s&&Bp()!==s?((e=gf())===s&&(e=null),e!==s&&Bp()!==s?((n=Tf())===s&&(n=null),n!==s&&Bp()!==s&&(o=Rc())!==s&&Bp()!==s?((u=Bc())===s&&(u=null),u!==s&&Bp()!==s&&(a=Wc())!==s&&Bp()!==s?((i=qc())===s&&(i=null),i!==s&&Bp()!==s?((c=Yc())===s&&(c=null),c!==s?(di=r,t=function(r,t,e,n,o,s,u,a){n&&(Sb.add(`insert::${n.db}::${n.table}`),xb.add(`insert::${n.table}::(.*)`),n.as=null);const i=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r,table:[n],columns:null,values:s,partition:o,prefix:i,on_duplicate_update:u,returning:a}}}(t,e,n,o,u,a,i,c),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a,i,c;r=vi,(t=Xc())!==s&&Bp()!==s?((e=gf())===s&&(e=null),e!==s&&Bp()!==s?((n=Tf())===s&&(n=null),n!==s&&Bp()!==s&&(o=Rc())!==s&&Bp()!==s?((u=Bc())===s&&(u=null),u!==s&&Bp()!==s&&xf()!==s&&Bp()!==s&&(a=Fc())!==s&&Bp()!==s?((i=qc())===s&&(i=null),i!==s&&Bp()!==s?((c=Yc())===s&&(c=null),c!==s?(di=r,t=function(r,t,e,n,o,s,u,a){n&&(Sb.add(`insert::${n.db}::${n.table}`),xb.add(`insert::${n.table}::(.*)`),n.as=null);const i=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Sb),columnList:gb(xb),ast:{type:r,table:[n],columns:null,partition:o,prefix:i,set:s,on_duplicate_update:u,returning:a}}}(t,e,n,o,u,a,i,c),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(r=function(){var r,t,e,n,o,u,a;r=vi,(t=Bp())!==s?((e=pc())===s&&(e=null),e!==s&&Bp()!==s&&mf()!==s&&Bp()!==s?((n=xc())===s&&(n=null),n!==s&&Bp()!==s&&(o=Ec())!==s&&Bp()!==s?((u=Oc())===s&&(u=null),u!==s&&Bp()!==s?((a=Yc())===s&&(a=null),a!==s?(di=r,t=function(r,t,e,n,o){if(e){(Array.isArray(e)?e:e.expr).forEach(r=>{const{db:t,as:e,table:n,join:o}=r,s=o?"select":"delete";n&&Sb.add(`${s}::${t}::${n}`),o||xb.add(`delete::${n}::(.*)`)})}if(null===t&&1===e.length){const r=e[0];t=[{db:r.db,table:r.table,as:r.as,addition:!0}]}return{tableList:Array.from(Sb),columnList:gb(xb),ast:{with:r,type:"delete",table:t,from:e,where:n,returning:o}}}(e,n,o,u,a),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(r=Si())===s&&(r=function(){var r,t;r=[],t=Zp();for(;t!==s;)r.push(t),t=Zp();return r}()),r}function Ii(){var t,e,n,o;return t=vi,(e=function(){var t,e,n,o;t=vi,"union"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Ks));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=Wf())===s&&(n=Bf()),n===s&&(n=null),n!==s?(di=t,t=e=(o=n)?"union "+o.toLowerCase():"union"):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"minus"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Qs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e="minus"),(t=e)===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"intersect"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(zs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e="intersect"),t=e)),t}function ji(){var r,t,e,n,o,u,a,i;if(r=vi,(t=fc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Ii())!==s&&(a=Bp())!==s&&(i=fc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Ii())!==s&&(a=Bp())!==s&&(i=fc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s&&(n=Bp())!==s?((o=Dc())===s&&(o=null),o!==s&&(u=Bp())!==s?((a=Gc())===s&&(a=null),a!==s?(di=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(Sb),columnList:gb(xb),ast:r}}(t,e,o,a)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)}else vi=r,r=s;return r}function Ni(){var r,t,e;return r=vi,(t=tl())!==s&&Bp()!==s?((e=Hf())===s&&(e=Yf()),e===s&&(e=null),e!==s?(di=r,r=t=c(t,e)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=function(){var r,t,e;r=vi,(t=yl())!==s&&Bp()!==s?((e=Hf())===s&&(e=Yf()),e===s&&(e=null),e!==s?(di=r,t=c(t,e),r=t):(vi=r,r=s)):(vi=r,r=s);return r}()),r}function Ri(){var t,e;return t=vi,"if"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(f)),e!==s&&Bp()!==s&&zf()!==s&&Bp()!==s&&Qf()!==s?(di=t,t=e="IF NOT EXISTS"):(vi=t,t=s),t}function ki(){var t,e,n;return t=vi,(e=ic())!==s&&Bp()!==s?((n=function(){var t,e,n,o,u,a,i,c,l;return t=vi,r.substr(vi,10)===p?(e=p,vi+=10):(e=s,0===Li&&gi(b)),e!==s&&Bp()!==s?(n=vi,"with"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(v)),o!==s&&(u=Bp())!==s&&(a=Cl())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=null),n!==s&&(o=Bp())!==s?("by"===r.substr(vi,2).toLowerCase()?(u=r.substr(vi,2),vi+=2):(u=s,0===Li&&gi(d)),u!==s&&(a=Bp())!==s?("random"===r.substr(vi,6).toLowerCase()?(i=r.substr(vi,6),vi+=6):(i=s,0===Li&&gi(y)),i!==s&&Bp()!==s?("password"===r.substr(vi,8).toLowerCase()?(c=r.substr(vi,8),vi+=8):(c=s,0===Li&&gi(w)),c!==s?(di=t,t=e={keyword:["identified",(l=n)&&l[0].toLowerCase()].filter(r=>r).join(" "),auth_plugin:l&&l[2],value:{prefix:"by",type:"origin",value:"random password"}}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,r.substr(vi,10)===p?(e=p,vi+=10):(e=s,0===Li&&gi(b)),e!==s&&Bp()!==s?(n=vi,"with"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(v)),o!==s&&(u=Bp())!==s&&(a=Cl())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=null),n!==s&&(o=Bp())!==s?("by"===r.substr(vi,2).toLowerCase()?(u=r.substr(vi,2),vi+=2):(u=s,0===Li&&gi(d)),u!==s&&(a=Bp())!==s&&(i=Jl())!==s?(di=t,t=e=function(r,t){return t.prefix="by",{keyword:["identified",r&&r[0].toLowerCase()].filter(r=>r).join(" "),auth_plugin:r&&r[2],value:t}}(n,i)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,r.substr(vi,10)===p?(e=p,vi+=10):(e=s,0===Li&&gi(b)),e!==s&&Bp()!==s?("with"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(v)),n!==s&&(o=Bp())!==s&&(u=Cl())!==s&&(a=Bp())!==s?("as"===r.substr(vi,2).toLowerCase()?(i=r.substr(vi,2),vi+=2):(i=s,0===Li&&gi(h)),i!==s&&Bp()!==s&&(c=Jl())!==s?(di=t,t=e=function(r,t){return t.prefix="as",{keyword:"identified with",auth_plugin:r&&r[2],value:t}}(u,c)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s))),t}())===s&&(n=null),n!==s?(di=t,t=e={user:e,auth_option:n}):(vi=t,t=s)):(vi=t,t=s),t}function Oi(){var t,e,n;return t=vi,"none"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(C)),e===s&&("ssl"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(A)),e===s&&("x509"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(E)))),e!==s&&(di=t,e={type:"origin",value:e}),(t=e)===s&&(t=vi,"cipher"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(g)),e===s&&("issuer"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(_)),e===s&&("subject"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(T)))),e!==s&&Bp()!==s&&(n=Jl())!==s?(di=t,t=e=S(e,n)):(vi=t,t=s)),t}function Ui(){var t,e,n;return t=vi,"max_queries_per_hour"===r.substr(vi,20).toLowerCase()?(e=r.substr(vi,20),vi+=20):(e=s,0===Li&&gi(I)),e===s&&("max_updates_per_hour"===r.substr(vi,20).toLowerCase()?(e=r.substr(vi,20),vi+=20):(e=s,0===Li&&gi(j)),e===s&&("max_connections_per_hour"===r.substr(vi,24).toLowerCase()?(e=r.substr(vi,24),vi+=24):(e=s,0===Li&&gi(N)),e===s&&("max_user_connections"===r.substr(vi,20).toLowerCase()?(e=r.substr(vi,20),vi+=20):(e=s,0===Li&&gi(R))))),e!==s&&Bp()!==s&&(n=nf())!==s?(di=t,t=e=S(e,n)):(vi=t,t=s),t}function Mi(){var t,e,n,o,u,a;return t=vi,"password"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(w)),e!==s&&Bp()!==s?("expire"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(k)),n!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(O)),o===s&&("never"===r.substr(vi,5).toLowerCase()?(o=r.substr(vi,5),vi+=5):(o=s,0===Li&&gi(U)),o===s&&(o=zc())),o!==s?(di=t,t=e={keyword:"password expire",value:"string"==typeof(a=o)?{type:"origin",value:a}:a}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"password"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(w)),e!==s&&Bp()!==s?("history"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(M)),n!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(O)),o===s&&(o=nf()),o!==s?(di=t,t=e=function(r){return{keyword:"password history",value:"string"==typeof r?{type:"origin",value:r}:r}}(o)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"password"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(w)),e!==s&&Bp()!==s?("REUSE"===r.substr(vi,5)?(n="REUSE",vi+=5):(n=s,0===Li&&gi(D)),n!==s&&Bp()!==s&&(o=zc())!==s?(di=t,t=e=function(r){return{keyword:"password reuse",value:r}}(o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"password"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(w)),e!==s&&Bp()!==s?("require"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(x)),n!==s&&Bp()!==s?("current"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(P)),o!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(u=r.substr(vi,7),vi+=7):(u=s,0===Li&&gi(O)),u===s&&("optional"===r.substr(vi,8).toLowerCase()?(u=r.substr(vi,8),vi+=8):(u=s,0===Li&&gi($))),u!==s?(di=t,t=e=function(r){return{keyword:"password require current",value:{type:"origin",value:r}}}(u)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"failed_login_attempts"===r.substr(vi,21).toLowerCase()?(e=r.substr(vi,21),vi+=21):(e=s,0===Li&&gi(G)),e!==s&&Bp()!==s&&(n=nf())!==s?(di=t,t=e=function(r){return{keyword:"failed_login_attempts",value:r}}(n)):(vi=t,t=s),t===s&&(t=vi,"password_lock_time"===r.substr(vi,18).toLowerCase()?(e=r.substr(vi,18),vi+=18):(e=s,0===Li&&gi(F)),e!==s&&Bp()!==s?((n=nf())===s&&("unbounded"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(H))),n!==s?(di=t,t=e=function(r){return{keyword:"password_lock_time",value:"string"==typeof r?{type:"origin",value:r}:r}}(n)):(vi=t,t=s)):(vi=t,t=s)))))),t}function Di(){var t;return(t=$i())===s&&(t=Qi())===s&&(t=zi())===s&&(t=function(){var t;(t=function(){var t,e,n,o,u,a;t=vi,(e=Zi())===s&&(e=null);e!==s&&Bp()!==s?("primary key"===r.substr(vi,11).toLowerCase()?(n=r.substr(vi,11),vi+=11):(n=s,0===Li&&gi(Zr)),n!==s&&Bp()!==s?((o=_c())===s&&(o=null),o!==s&&Bp()!==s&&(u=Ki())!==s&&Bp()!==s?((a=Tc())===s&&(a=null),a!==s?(di=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var i,c,l,f,p;return t}())===s&&(t=function(){var r,t,e,n,o,u,a,i;r=vi,(t=Zi())===s&&(t=null);t!==s&&Bp()!==s&&(e=Up())!==s&&Bp()!==s?((n=Np())===s&&(n=Rp()),n===s&&(n=null),n!==s&&Bp()!==s?((o=xl())===s&&(o=null),o!==s&&Bp()!==s?((u=_c())===s&&(u=null),u!==s&&Bp()!==s&&(a=Ki())!==s&&Bp()!==s?((i=Tc())===s&&(i=null),i!==s?(di=r,l=e,f=n,p=o,b=u,v=a,d=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:d},r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);var c,l,f,p,b,v,d;return r}())===s&&(t=function(){var t,e,n,o,u,a;t=vi,(e=Zi())===s&&(e=null);e!==s&&Bp()!==s?("foreign key"===r.substr(vi,11).toLowerCase()?(n=r.substr(vi,11),vi+=11):(n=s,0===Li&&gi(tt)),n!==s&&Bp()!==s?((o=xl())===s&&(o=null),o!==s&&Bp()!==s&&(u=vc())!==s&&Bp()!==s?((a=Ji())===s&&(a=null),a!==s?(di=t,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var i,c,l,f,p;return t}())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f;t=vi,(e=Zi())===s&&(e=null);e!==s&&Bp()!==s?("check"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(K)),n!==s&&Bp()!==s?(o=vi,"not"===r.substr(vi,3).toLowerCase()?(u=r.substr(vi,3),vi+=3):(u=s,0===Li&&gi(Jr)),u!==s&&(a=Bp())!==s?("for"===r.substr(vi,3).toLowerCase()?(i=r.substr(vi,3),vi+=3):(i=s,0===Li&&gi(vr)),i!==s&&(c=Bp())!==s?("replication"===r.substr(vi,11).toLowerCase()?(l=r.substr(vi,11),vi+=11):(l=s,0===Li&&gi(rt)),l!==s&&(f=Bp())!==s?o=u=[u,a,i,c,l,f]:(vi=o,o=s)):(vi=o,o=s)):(vi=o,o=s),o===s&&(o=null),o!==s&&(u=Fp())!==s&&(a=Bp())!==s&&(i=el())!==s&&(c=Bp())!==s&&(l=Hp())!==s?(di=t,p=e,b=o,v=i,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication"},definition:[v],resource:"constraint"},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var p,b,v;return t}());return t}()),t}function Pi(){var t,e,n,o,u;return t=vi,(e=function(){var t,e;t=vi,(e=function(){var t,e,n,o;t=vi,"not null"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(vs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={type:"not null",value:"not null"});return t=e}())===s&&(e=Zl()),e!==s&&(di=t,(u=e)&&!u.value&&(u.value="null"),e={nullable:u}),(t=e)===s&&(t=vi,(e=function(){var r,t;r=vi,ff()!==s&&Bp()!==s&&(t=tl())!==s?(di=r,r={type:"default",value:t}):(vi=r,r=s);return r}())!==s&&(di=t,e={default_val:e}),(t=e)===s&&(t=vi,"auto_increment"===r.substr(vi,14).toLowerCase()?(e=r.substr(vi,14),vi+=14):(e=s,0===Li&&gi(ur)),e!==s&&(di=t,e={auto_increment:e.toLowerCase()}),(t=e)===s&&(t=vi,"unique"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(ar)),e!==s&&Bp()!==s?("key"===r.substr(vi,3).toLowerCase()?(n=r.substr(vi,3),vi+=3):(n=s,0===Li&&gi(ir)),n===s&&(n=null),n!==s?(di=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"primary"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(cr)),e===s&&(e=null),e!==s&&Bp()!==s?("key"===r.substr(vi,3).toLowerCase()?(n=r.substr(vi,3),vi+=3):(n=s,0===Li&&gi(ir)),n!==s?(di=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Vp())!==s&&(di=t,e={comment:e}),(t=e)===s&&(t=vi,(e=Fi())!==s&&(di=t,e={collate:e}),(t=e)===s&&(t=vi,(e=function(){var t,e,n;t=vi,"column_format"===r.substr(vi,13).toLowerCase()?(e=r.substr(vi,13),vi+=13):(e=s,0===Li&&gi(mr));e!==s&&Bp()!==s?("fixed"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(Cr)),n===s&&("dynamic"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(Ar)),n===s&&("default"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(O)))),n!==s?(di=t,e={type:"column_format",value:n.toLowerCase()},t=e):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={column_format:e}),(t=e)===s&&(t=vi,(e=function(){var t,e,n;t=vi,"storage"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Er));e!==s&&Bp()!==s?("disk"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(gr)),n===s&&("memory"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(_r))),n!==s?(di=t,e={type:"storage",value:n.toLowerCase()},t=e):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={storage:e}),(t=e)===s&&(t=vi,(e=Ji())!==s&&(di=t,e={reference_definition:e}),(t=e)===s&&(t=vi,(e=function(){var t,e,n,o,u,a,i,c;t=vi,(e=Zi())===s&&(e=null);e!==s&&Bp()!==s?("check"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(K)),n!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(o=el())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?(u=vi,(a=zf())===s&&(a=null),a!==s&&(i=Bp())!==s?("enforced"===r.substr(vi,8).toLowerCase()?(c=r.substr(vi,8),vi+=8):(c=s,0===Li&&gi(et)),c!==s?u=a=[a,i,c]:(vi=u,u=s)):(vi=u,u=s),u===s&&(u=null),u!==s?(di=t,e=function(r,t,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:t.toLowerCase(),keyword:r&&r.keyword,constraint:r&&r.constraint,definition:[e],enforced:o.filter(r=>r).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,u),t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={check:e}),(t=e)===s&&(t=vi,(e=ec())!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s&&(o=Ll())!==s?(di=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(vi=t,t=s)):(vi=t,t=s)))))))))))),t}function $i(){var t,e,n,o,u,a,i,c,l;return t=vi,(e=yl())!==s&&Bp()!==s&&(n=lb())!==s&&Bp()!==s?((o=function(){var t,e,n,o,u,a,i,c;t=vi,e=vi,(n=function(){var t,e,n,o,u;t=vi,e=vi,"generated"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Tr));n!==s&&(o=Bp())!==s?("always"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(Sr)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s);e!==s&&(di=t,e=e.join("").toLowerCase());return t=e}())===s&&(n=null);n!==s&&(o=Bp())!==s?("as"===r.substr(vi,2).toLowerCase()?(u=r.substr(vi,2),vi+=2):(u=s,0===Li&&gi(h)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s);if(e!==s)if((n=Bp())!==s)if((o=Fp())!==s)if((u=Bp())!==s)if((a=zl())===s&&(a=tl()),a!==s)if(Bp()!==s)if(Hp()!==s)if(Bp()!==s){for(i=[],"stored"===r.substr(vi,6).toLowerCase()?(c=r.substr(vi,6),vi+=6):(c=s,0===Li&&gi(xr)),c===s&&("virtual"===r.substr(vi,7).toLowerCase()?(c=r.substr(vi,7),vi+=7):(c=s,0===Li&&gi(Ir)));c!==s;)i.push(c),"stored"===r.substr(vi,6).toLowerCase()?(c=r.substr(vi,6),vi+=6):(c=s,0===Li&&gi(xr)),c===s&&("virtual"===r.substr(vi,7).toLowerCase()?(c=r.substr(vi,7),vi+=7):(c=s,0===Li&&gi(Ir)));i!==s?(di=t,l=i,e={type:"generated",expr:a,value:e.filter(r=>"string"==typeof r).join(" ").toLowerCase(),storage_type:l&&l[0]&&l[0].toLowerCase()},t=e):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;var l;return t}())===s&&(o=null),o!==s&&Bp()!==s?((u=function(){var r,t,e,n,o,u;if(r=vi,(t=Pi())!==s)if(Bp()!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Pi())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Pi())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;return r}())===s&&(u=null),u!==s?(di=t,a=e,i=n,c=o,l=u,xb.add(`create::${a.table}::${a.column}`),t=e={column:a,definition:i,generated:c,resource:"column",...l||{}}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function Gi(){var t,e,n,o,u;return t=vi,"definer"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(nr)),e!==s&&Bp()!==s&&xp()!==s&&Bp()!==s?((n=Tl())===s&&(n=Jl()),n!==s&&Bp()!==s?(64===r.charCodeAt(vi)?(o="@",vi++):(o=s,0===Li&&gi(lr)),o!==s&&Bp()!==s?((u=Tl())===s&&(u=Jl()),u!==s?(di=t,t=e=function(r,t){const e=Lb(r,"@",t);return Lb("=",{type:"origin",value:"definer"},e)}(n,u)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"definer"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(nr)),e!==s&&Bp()!==s&&xp()!==s&&Bp()!==s&&(n=Ep())!==s&&Bp()!==s&&(o=Fp())!==s&&Bp()!==s&&(u=Hp())!==s?(di=t,t=e=fr()):(vi=t,t=s),t===s&&(t=vi,"definer"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(nr)),e!==s&&Bp()!==s&&xp()!==s&&Bp()!==s&&(n=Ep())!==s?(di=t,t=e=fr()):(vi=t,t=s))),t}function Fi(){var t,e,n;return t=vi,function(){var t,e,n,o;t=vi,"collate"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(vt));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="COLLATE"):(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s?((e=xp())===s&&(e=null),e!==s&&Bp()!==s&&(n=Cl())!==s?(di=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(vi=t,t=s)):(vi=t,t=s),t}function Hi(){var t,e,n;return t=vi,"if"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(jr)),e!==s&&Bp()!==s?("exists"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Nr)),n!==s?(di=t,t=e="if exists"):(vi=t,t=s)):(vi=t,t=s),t}function Yi(){var t,e,n;return t=vi,"first"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Rr)),e!==s&&(di=t,e={keyword:e}),(t=e)===s&&(t=vi,"after"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(kr)),e!==s&&Bp()!==s&&(n=yl())!==s?(di=t,t=e=function(r,t){return{keyword:r,expr:t}}(e,n)):(vi=t,t=s)),t}function Wi(){var t,e,n;return(t=function(){var r,t,e,n,o,u;r=vi,(t=Ip())!==s&&Bp()!==s?((e=jp())===s&&(e=null),e!==s&&Bp()!==s?((n=Ri())===s&&(n=null),n!==s&&Bp()!==s&&(o=$i())!==s&&Bp()!==s?((u=Yi())===s&&(u=null),u!==s?(di=r,a=e,i=n,c=o,l=u,t={action:"add",...c,suffix:l,keyword:a,if_not_exists:i,resource:"column",type:"alter"},r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);var a,i,c,l;r===s&&(r=vi,(t=Ip())!==s&&Bp()!==s&&(e=$i())!==s&&Bp()!==s?((n=Yi())===s&&(n=null),n!==s?(di=r,t=function(r,t){return{action:"add",...r,suffix:t,resource:"column",type:"alter"}}(e,n),r=t):(vi=r,r=s)):(vi=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u,a;t=vi,(e=vf())!==s&&Bp()!==s?("primary"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(cr)),n!==s&&(o=Bp())!==s&&(u=Rp())!==s?(di=t,t=e={action:"drop",key:"",keyword:"primary key",resource:"key",type:"alter"}):(vi=t,t=s)):(vi=t,t=s);t===s&&(t=vi,(e=vf())!==s&&Bp()!==s?(n=vi,"foreign"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(Yr)),o===s&&(o=null),o!==s&&(u=Bp())!==s&&(a=Rp())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=Np()),n!==s&&(o=Bp())!==s&&(u=Cl())!==s?(di=t,e=function(r,t){const e=Array.isArray(r)?"key":"index";return{action:"drop",[e]:t,keyword:Array.isArray(r)?""+[r[0],r[2]].filter(r=>r).join(" ").toLowerCase():r.toLowerCase(),resource:e,type:"alter"}}(n,u),t=e):(vi=t,t=s)):(vi=t,t=s));return t}())===s&&(t=function(){var r,t,e,n;r=vi,(t=vf())!==s&&Bp()!==s&&(e=jp())!==s&&Bp()!==s&&(n=yl())!==s?(di=r,r=t={action:"drop",column:n,keyword:e,resource:"column",type:"alter"}):(vi=r,r=s);r===s&&(r=vi,(t=vf())!==s&&Bp()!==s&&(e=yl())!==s?(di=r,t=function(r){return{action:"drop",column:r,resource:"column",type:"alter"}}(e),r=t):(vi=r,r=s));return r}())===s&&(t=function(){var t,e,n,o,u;t=vi,(e=function(){var t,e,n,o;t=vi,"modify"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(ka));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MODIFY"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=jp())===s&&(n=null),n!==s&&Bp()!==s&&(o=$i())!==s&&Bp()!==s?((u=Yi())===s&&(u=null),u!==s?(di=t,a=o,i=u,e={action:"modify",keyword:n,...a,suffix:i,resource:"column",type:"alter"},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var a,i;return t}())===s&&(t=function(){var r,t,e;r=vi,(t=Ip())!==s&&Bp()!==s&&(e=Qi())!==s?(di=r,n=e,t={action:"add",type:"alter",...n},r=t):(vi=r,r=s);var n;return r}())===s&&(t=function(){var r,t,e;r=vi,(t=Ip())!==s&&Bp()!==s&&(e=zi())!==s?(di=r,n=e,t={action:"add",type:"alter",...n},r=t):(vi=r,r=s);var n;return r}())===s&&(t=function(){var r,t,e,n,o;r=vi,(t=Ef())!==s&&Bp()!==s&&jp()!==s&&Bp()!==s&&(e=yl())!==s&&Bp()!==s?((n=pf())===s&&(n=If()),n===s&&(n=null),n!==s&&Bp()!==s&&(o=yl())!==s?(di=r,a=o,t={action:"rename",type:"alter",resource:"column",keyword:"column",old_column:e,prefix:(u=n)&&u[0].toLowerCase(),column:a},r=t):(vi=r,r=s)):(vi=r,r=s);var u,a;return r}())===s&&(t=function(){var r,t,e,n;r=vi,(t=Ef())!==s&&Bp()!==s?((e=pf())===s&&(e=If()),e===s&&(e=null),e!==s&&Bp()!==s&&(n=Cl())!==s?(di=r,u=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},r=t):(vi=r,r=s)):(vi=r,r=s);var o,u;return r}())===s&&(t=qi())===s&&(t=Xi())===s&&(t=function(){var t,e,n,o,u,a;t=vi,"change"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Kr));e!==s&&Bp()!==s?((n=jp())===s&&(n=null),n!==s&&Bp()!==s&&(o=yl())!==s&&Bp()!==s&&(u=$i())!==s&&Bp()!==s?((a=Yi())===s&&(a=null),a!==s?(di=t,i=n,c=u,l=a,e={action:"change",old_column:o,...c,keyword:i,resource:"column",type:"alter",suffix:l},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var i,c,l;return t}())===s&&(t=function(){var t,e,n,o,u;t=vi,"drop"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Mr));e===s&&("truncate"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Dr)),e===s&&("discard"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Pr)),e===s&&("import"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi($r)),e===s&&("coalesce"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Gr)),e===s&&("analyze"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Fr)),e===s&&("check"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(K))))))));e!==s&&Bp()!==s&&(n=_f())!==s&&Bp()!==s&&(o=hc())!==s&&Bp()!==s?("tablespace"===r.substr(vi,10).toLowerCase()?(u=r.substr(vi,10),vi+=10):(u=s,0===Li&&gi(Hr)),u===s&&(u=null),u!==s?(di=t,e=function(r,t,e,n){const o={action:r.toLowerCase(),keyword:t,resource:"partition",type:"alter",partitions:e};return n&&(o.suffix={keyword:n}),o}(e,n,o,u),t=e):(vi=t,t=s)):(vi=t,t=s);t===s&&(t=vi,(e=Ip())!==s&&Bp()!==s&&(n=_f())!==s&&Bp()!==s&&(o=Fp())!==s&&Bp()!==s&&(u=function(){var r,t,e,n,o,u,a,c;if(r=vi,(t=Bi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=Bi())!==s?n=o=[o,u,a,c]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=Bi())!==s?n=o=[o,u,a,c]:(vi=n,n=s);e!==s?(di=r,t=i(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e={action:"add",keyword:n,resource:"partition",type:"alter",partitions:u}):(vi=t,t=s));return t}())===s&&(t=vi,(e=oc())!==s&&(di=t,(n=e).resource=n.keyword,n[n.keyword]=n.value,delete n.value,e={type:"alter",...n}),t=e),t}function Bi(){var t,e,n,o,u;return t=vi,_f()!==s&&Bp()!==s&&(e=Ll())!==s&&Bp()!==s&&Pf()!==s&&Bp()!==s?("less"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(Or)),n!==s&&Bp()!==s?("than"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(Ur)),o!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(u=nf())!==s&&Bp()!==s&&Hp()!==s?(di=t,t={name:e,value:{type:"less than",expr:u,parentheses:!0}}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function qi(){var t,e,n,o;return t=vi,"algorithm"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(z)),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(O)),o===s&&("instant"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(Wr)),o===s&&("inplace"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(Br)),o===s&&("copy"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(qr))))),o!==s?(di=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function Xi(){var t,e,n,o;return t=vi,"lock"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(W)),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(O)),o===s&&("none"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(C)),o===s&&("shared"===r.substr(vi,6).toLowerCase()?(o=r.substr(vi,6),vi+=6):(o=s,0===Li&&gi(Xr)),o===s&&("exclusive"===r.substr(vi,9).toLowerCase()?(o=r.substr(vi,9),vi+=9):(o=s,0===Li&&gi(Vr))))),o!==s?(di=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function Vi(){var t,e,n,o,u,a,i;if(t=vi,(e=Sl())!==s)if(Bp()!==s)if((n=Fp())!==s)if(Bp()!==s){if(o=[],Qr.test(r.charAt(vi))?(u=r.charAt(vi),vi++):(u=s,0===Li&&gi(zr)),u!==s)for(;u!==s;)o.push(u),Qr.test(r.charAt(vi))?(u=r.charAt(vi),vi++):(u=s,0===Li&&gi(zr));else o=s;o!==s&&(u=Bp())!==s&&Hp()!==s&&Bp()!==s?((a=Hf())===s&&(a=Yf()),a===s&&(a=null),a!==s?(di=t,i=a,t=e={type:"column_ref",column:e,suffix:`(${parseInt(o.join(""),10)})`,order_by:i}):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;return t===s&&(t=vi,(e=Sl())!==s&&Bp()!==s?((n=Hf())===s&&(n=Yf()),n===s&&(n=null),n!==s?(di=t,t=e=function(r,t){return{type:"column_ref",column:r,order_by:t}}(e,n)):(vi=t,t=s)):(vi=t,t=s)),t}function Ki(){var r,t,e;return r=vi,Fp()!==s&&Bp()!==s?((t=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Vi())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Vi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Vi())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(t=Qc()),t!==s&&Bp()!==s&&Hp()!==s?(di=r,r=(e=t).type?e.value:e):(vi=r,r=s)):(vi=r,r=s),r}function Qi(){var r,t,e,n,o,u,a,i;return r=vi,(t=Np())===s&&(t=Rp()),t!==s&&Bp()!==s?((e=xl())===s&&(e=null),e!==s&&Bp()!==s?((n=_c())===s&&(n=null),n!==s&&Bp()!==s&&(o=Ki())!==s&&Bp()!==s?((u=Tc())===s&&(u=null),u!==s&&Bp()!==s?(di=r,a=n,i=u,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:a,resource:"index",index_options:i}):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r}function zi(){var r,t,e,n,o,u,a,i,c;return r=vi,(t=kp())===s&&(t=Op()),t!==s&&Bp()!==s?((e=Np())===s&&(e=Rp()),e===s&&(e=null),e!==s&&Bp()!==s?((n=xl())===s&&(n=null),n!==s&&Bp()!==s&&(o=vc())!==s&&Bp()!==s?((u=Tc())===s&&(u=null),u!==s&&Bp()!==s?(di=r,a=t,c=u,r=t={index:n,definition:o,keyword:(i=e)&&`${a.toLowerCase()} ${i.toLowerCase()}`||a.toLowerCase(),index_options:c,resource:"index"}):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r}function Zi(){var t,e,n,o;return t=vi,(e=function(){var t,e,n,o;t=vi,"constraint"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Da));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CONSTRAINT"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=Cl())===s&&(n=null),n!==s?(di=t,o=n,t=e={keyword:e.toLowerCase(),constraint:o}):(vi=t,t=s)):(vi=t,t=s),t}function Ji(){var t,e,n,o,u,a,i,c,l,f;return t=vi,(e=Dp())!==s&&Bp()!==s&&(n=xc())!==s&&Bp()!==s&&(o=vc())!==s&&Bp()!==s?("match full"===r.substr(vi,10).toLowerCase()?(u=r.substr(vi,10),vi+=10):(u=s,0===Li&&gi(nt)),u===s&&("match partial"===r.substr(vi,13).toLowerCase()?(u=r.substr(vi,13),vi+=13):(u=s,0===Li&&gi(ot)),u===s&&("match simple"===r.substr(vi,12).toLowerCase()?(u=r.substr(vi,12),vi+=12):(u=s,0===Li&&gi(st)))),u===s&&(u=null),u!==s&&Bp()!==s?((a=rc())===s&&(a=null),a!==s&&Bp()!==s?((i=rc())===s&&(i=null),i!==s?(di=t,c=u,l=a,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=rc())!==s&&(di=t,e={on_action:[e]}),t=e),t}function rc(){var t,e,n,o;return t=vi,Uf()!==s&&Bp()!==s?((e=mf())===s&&(e=wf()),e!==s&&Bp()!==s&&(n=function(){var t,e,n;t=vi,(e=Ap())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s?((n=Qc())===s&&(n=null),n!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(vi=t,t=s)):(vi=t,t=s);t===s&&(t=vi,(e=tc())===s&&("set null"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(it)),e===s&&("no action"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(ct)),e===s&&("set default"===r.substr(vi,11).toLowerCase()?(e=r.substr(vi,11),vi+=11):(e=s,0===Li&&gi(lt)),e===s&&(e=Ap())))),e!==s&&(di=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==s?(di=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(vi=t,t=s)):(vi=t,t=s),t}function tc(){var t,e;return t=vi,"restrict"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ut)),e===s&&("cascade"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(at))),e!==s&&(di=t,e=e.toLowerCase()),t=e}function ec(){var t,e,n;return t=vi,"character"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(ft)),e!==s&&Bp()!==s?("set"===r.substr(vi,3).toLowerCase()?(n=r.substr(vi,3),vi+=3):(n=s,0===Li&&gi(pt)),n!==s?(di=t,t=e="CHARACTER SET"):(vi=t,t=s)):(vi=t,t=s),t}function nc(){var t,e,n,o,u,a,i,c,l;return t=vi,(e=ff())===s&&(e=null),e!==s&&Bp()!==s?((n=ec())===s&&("charset"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(bt)),n===s&&("collate"===r.substr(vi,7).toLowerCase()?(n=r.substr(vi,7),vi+=7):(n=s,0===Li&&gi(vt)))),n!==s&&Bp()!==s?((o=xp())===s&&(o=null),o!==s&&Bp()!==s&&(u=Ll())!==s?(di=t,i=n,c=o,l=u,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function oc(){var t,e,n,o,u,a,i,c,l;return t=vi,"auto_increment"===r.substr(vi,14).toLowerCase()?(e=r.substr(vi,14),vi+=14):(e=s,0===Li&&gi(ur)),e===s&&("avg_row_length"===r.substr(vi,14).toLowerCase()?(e=r.substr(vi,14),vi+=14):(e=s,0===Li&&gi(dt)),e===s&&("key_block_size"===r.substr(vi,14).toLowerCase()?(e=r.substr(vi,14),vi+=14):(e=s,0===Li&&gi(yt)),e===s&&("max_rows"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(wt)),e===s&&("min_rows"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ht)),e===s&&("stats_sample_pages"===r.substr(vi,18).toLowerCase()?(e=r.substr(vi,18),vi+=18):(e=s,0===Li&&gi(Lt))))))),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s&&(o=nf())!==s?(di=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=nc())===s&&(t=vi,"CHECKSUM"===r.substr(vi,8)?(e="CHECKSUM",vi+=8):(e=s,0===Li&&gi(mt)),e===s&&("DELAY_KEY_WRITE"===r.substr(vi,15)?(e="DELAY_KEY_WRITE",vi+=15):(e=s,0===Li&&gi(Ct))),e!==s&&Bp()!==s&&(n=xp())!==s&&Bp()!==s?(At.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(Et)),o!==s?(di=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e}}(e,n,o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Mp())===s&&("connection"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(gt)),e===s&&("engine_attribute"===r.substr(vi,16).toLowerCase()?(e=r.substr(vi,16),vi+=16):(e=s,0===Li&&gi(_t)),e===s&&("secondary_engine_attribute"===r.substr(vi,26).toLowerCase()?(e=r.substr(vi,26),vi+=26):(e=s,0===Li&&gi(Tt))))),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s&&(o=Jl())!==s?(di=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"data"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(St)),e===s&&("index"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(xt))),e!==s&&Bp()!==s?("directory"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(It)),n!==s&&Bp()!==s?((o=xp())===s&&(o=null),o!==s&&(u=Bp())!==s&&(a=Jl())!==s?(di=t,t=e=function(r,t,e){return{keyword:r.toLowerCase()+" directory",symbol:t,value:`'${e.value}'`}}(e,o,a)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"compression"===r.substr(vi,11).toLowerCase()?(e=r.substr(vi,11),vi+=11):(e=s,0===Li&&gi(jt)),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s?(o=vi,39===r.charCodeAt(vi)?(u="'",vi++):(u=s,0===Li&&gi(Nt)),u!==s?("zlib"===r.substr(vi,4).toLowerCase()?(a=r.substr(vi,4),vi+=4):(a=s,0===Li&&gi(Rt)),a===s&&("lz4"===r.substr(vi,3).toLowerCase()?(a=r.substr(vi,3),vi+=3):(a=s,0===Li&&gi(kt)),a===s&&("none"===r.substr(vi,4).toLowerCase()?(a=r.substr(vi,4),vi+=4):(a=s,0===Li&&gi(C)))),a!==s?(39===r.charCodeAt(vi)?(i="'",vi++):(i=s,0===Li&&gi(Nt)),i!==s?o=u=[u,a,i]:(vi=o,o=s)):(vi=o,o=s)):(vi=o,o=s),o!==s?(di=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"engine"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Ot)),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s&&(o=jl())!==s?(di=t,t=e=Ut(e,n,o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"row_format"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Mt)),e!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s?("default"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(O)),o===s&&("dynamic"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi(Ar)),o===s&&("fixed"===r.substr(vi,5).toLowerCase()?(o=r.substr(vi,5),vi+=5):(o=s,0===Li&&gi(Cr)),o===s&&("compressed"===r.substr(vi,10).toLowerCase()?(o=r.substr(vi,10),vi+=10):(o=s,0===Li&&gi(Dt)),o===s&&("redundant"===r.substr(vi,9).toLowerCase()?(o=r.substr(vi,9),vi+=9):(o=s,0===Li&&gi(Pt)),o===s&&("compact"===r.substr(vi,7).toLowerCase()?(o=r.substr(vi,7),vi+=7):(o=s,0===Li&&gi($t))))))),o!==s?(di=t,t=e=Ut(e,n,o)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s))))))),t}function sc(){var t,e,n,o,u;return t=vi,(e=jc())!==s&&Bp()!==s&&(n=function(){var t,e,n;return t=vi,"read"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ne)),e!==s&&Bp()!==s?("local"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(V)),n===s&&(n=null),n!==s?(di=t,t=e={type:"read",suffix:n&&"local"}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"low_priority"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(oe)),e===s&&(e=null),e!==s&&Bp()!==s?("write"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(se)),n!==s?(di=t,t=e={type:"write",prefix:e&&"low_priority"}):(vi=t,t=s)):(vi=t,t=s)),t}())!==s?(di=t,o=e,u=n,Sb.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:u}):(vi=t,t=s),t}function uc(){var t;return(t=function(){var t,e,n,o,u;return t=vi,(e=Wf())===s&&(e=df())===s&&(e=vi,(n=hf())!==s&&(o=Bp())!==s?("view"===r.substr(vi,4).toLowerCase()?(u=r.substr(vi,4),vi+=4):(u=s,0===Li&&gi(ue)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s),e===s&&(e=hf())===s&&(e=mf())===s&&(e=vf())===s&&(e=vi,"grant"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(ae)),n!==s&&(o=Bp())!==s?("option"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(ie)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s),e===s&&(e=Np())===s&&(e=Cf())===s&&(e=Dp())===s&&(e=yf())===s&&(e=vi,(n=bf())!==s&&(o=Bp())!==s&&(u=gp())!==s?e=n=[n,o,u]:(vi=e,e=s),e===s&&(e=Nf())===s&&(e=wf())))),e!==s&&(di=t,e=ce(e)),t=e}())===s&&(t=function(){var t,e,n,o,u;return t=vi,e=vi,(n=df())!==s&&(o=Bp())!==s?("routine"===r.substr(vi,7).toLowerCase()?(u=r.substr(vi,7),vi+=7):(u=s,0===Li&&gi(le)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s),e===s&&("execute"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(fe)),e===s&&(e=vi,"grant"===r.substr(vi,5).toLowerCase()?(n=r.substr(vi,5),vi+=5):(n=s,0===Li&&gi(ae)),n!==s&&(o=Bp())!==s?("option"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(ie)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s),e===s&&(e=vi,(n=hf())!==s&&(o=Bp())!==s?("routine"===r.substr(vi,7).toLowerCase()?(u=r.substr(vi,7),vi+=7):(u=s,0===Li&&gi(le)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s)))),e!==s&&(di=t,e=ce(e)),t=e}()),t}function ac(){var r,t,e,n,o,u,a,i,c;return r=vi,(t=uc())!==s&&Bp()!==s?(e=vi,(n=Fp())!==s&&(o=Bp())!==s&&(u=Uc())!==s&&(a=Bp())!==s&&(i=Hp())!==s?e=n=[n,o,u,a,i]:(vi=e,e=s),e===s&&(e=null),e!==s?(di=r,r=t={priv:t,columns:(c=e)&&c[2]}):(vi=r,r=s)):(vi=r,r=s),r}function ic(){var t,e,n,o,u,a,i;return t=vi,(e=Cl())!==s&&Bp()!==s?(n=vi,64===r.charCodeAt(vi)?(o="@",vi++):(o=s,0===Li&&gi(lr)),o!==s&&(u=Bp())!==s&&(a=Cl())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=null),n!==s?(di=t,t=e={name:{type:"single_quote_string",value:e},host:(i=n)?{type:"single_quote_string",value:i[2]}:null}):(vi=t,t=s)):(vi=t,t=s),t}function cc(){var r,t,e,n,o,u,a,i;if(r=vi,(t=ic())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ic())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=ic())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=L(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function lc(){var t,e,n;return t=vi,Gf()!==s&&Bp()!==s?("admin"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(pe)),e!==s&&Bp()!==s?("option"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(ie)),n!==s?(di=t,t={type:"origin",value:"with admin option"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function fc(){var t,e,n,o,u,a,i;return(t=yc())===s&&(t=vi,e=vi,40===r.charCodeAt(vi)?(n="(",vi++):(n=s,0===Li&&gi(de)),n!==s&&(o=Bp())!==s&&(u=fc())!==s&&(a=Bp())!==s?(41===r.charCodeAt(vi)?(i=")",vi++):(i=s,0===Li&&gi(ye)),i!==s?e=n=[n,o,u,a,i]:(vi=e,e=s)):(vi=e,e=s),e!==s&&(di=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function pc(){var t,e,n,o,u,a,i,c,l;if(t=vi,Gf()!==s)if(Bp()!==s)if((e=bc())!==s){for(n=[],o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=bc())!==s?o=u=[u,a,i,c]:(vi=o,o=s);o!==s;)n.push(o),o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=bc())!==s?o=u=[u,a,i,c]:(vi=o,o=s);n!==s?(di=t,t=sr(e,n)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;return t===s&&(t=vi,Bp()!==s&&Gf()!==s&&(e=Bp())!==s&&(n=function(){var t,e,n,o;t=vi,"recursive"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(Ss));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(o=Bp())!==s&&(u=bc())!==s?(di=t,(l=u).recursive=!0,t=[l]):(vi=t,t=s)),t}function bc(){var r,t,e,n,o,u,a;return r=vi,(t=Jl())===s&&(t=jl())===s&&(t=Rc()),t!==s&&Bp()!==s?((e=vc())===s&&(e=null),e!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=ji())!==s&&Bp()!==s&&Hp()!==s?(di=r,u=e,a=n,"string"==typeof(o=t)&&(o={type:"default",value:o}),o.table&&(o={type:"default",value:o.table}),r=t={name:o,stmt:a,columns:u}):(vi=r,r=s)):(vi=r,r=s),r}function vc(){var r,t;return r=vi,Fp()!==s&&Bp()!==s&&(t=function(){var r;(r=Uc())===s&&(r=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=zl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=zl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=zl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=sr(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}());return r}())!==s&&Bp()!==s&&Hp()!==s?(di=r,r=t):(vi=r,r=s),r}function dc(){var t,e,n,o;return t=vi,(e=function(){var t,e,n,o,u,a;return t=vi,e=vi,"for"===r.substr(vi,3).toLowerCase()?(n=r.substr(vi,3),vi+=3):(n=s,0===Li&&gi(vr)),n!==s&&(o=Bp())!==s&&(u=wf())!==s?e=n=[n,o,u]:(vi=e,e=s),e!==s&&(di=t,e=`${(a=e)[0]} ${a[2][0]}`),t=e}())===s&&(e=function(){var t,e,n,o,u,a,i,c,l,f;return t=vi,e=vi,"lock"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(W)),n!==s&&(o=Bp())!==s?("in"===r.substr(vi,2).toLowerCase()?(u=r.substr(vi,2),vi+=2):(u=s,0===Li&&gi(we)),u!==s&&(a=Bp())!==s?("share"===r.substr(vi,5).toLowerCase()?(i=r.substr(vi,5),vi+=5):(i=s,0===Li&&gi(he)),i!==s&&(c=Bp())!==s?("mode"===r.substr(vi,4).toLowerCase()?(l=r.substr(vi,4),vi+=4):(l=s,0===Li&&gi(Le)),l!==s?e=n=[n,o,u,a,i,c,l]:(vi=e,e=s)):(vi=e,e=s)):(vi=e,e=s)):(vi=e,e=s),e!==s&&(di=t,e=`${(f=e)[0]} ${f[2]} ${f[4]} ${f[6]}`),t=e}()),e!==s&&Bp()!==s?((n=function(){var t,e,n,o,u,a,i;return t=vi,e=vi,"wait"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(me)),n!==s&&(o=Bp())!==s&&(u=nf())!==s?e=n=[n,o,u]:(vi=e,e=s),e!==s&&(di=t,e=`${(a=e)[0]} ${a[2].value}`),(t=e)===s&&("nowait"===r.substr(vi,6).toLowerCase()?(t=r.substr(vi,6),vi+=6):(t=s,0===Li&&gi(Ce)),t===s&&(t=vi,e=vi,"skip"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(Ae)),n!==s&&(o=Bp())!==s?("locked"===r.substr(vi,6).toLowerCase()?(u=r.substr(vi,6),vi+=6):(u=s,0===Li&&gi(Ee)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s),e!==s&&(di=t,e=`${(i=e)[0]} ${i[2]}`),t=e)),t}())===s&&(n=null),n!==s?(di=t,t=e=e+((o=n)?" "+o:"")):(vi=t,t=s)):(vi=t,t=s),t}function yc(){var t,e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h;return t=vi,Bp()!==s?((e=pc())===s&&(e=null),e!==s&&Bp()!==s&&yf()!==s&&qp()!==s?((n=function(){var r,t,e,n,o,u;if(r=vi,(t=wc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=wc())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=wc())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(n=null),n!==s&&Bp()!==s?((o=Bf())===s&&(o=null),o!==s&&Bp()!==s&&(u=hc())!==s&&Bp()!==s?((a=Ac())===s&&(a=null),a!==s&&Bp()!==s?((i=Ec())===s&&(i=null),i!==s&&Bp()!==s?((c=Ac())===s&&(c=null),c!==s&&Bp()!==s?((l=Oc())===s&&(l=null),l!==s&&Bp()!==s?((f=function(){var t,e,n,o;t=vi,(e=function(){var t,e,n,o;t=vi,"group"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(tu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s&&Ff()!==s&&Bp()!==s&&(n=Qc())!==s&&Bp()!==s?((o=function(){var t,e;t=vi,Gf()!==s&&Bp()!==s?("rollup"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Fe)),e!==s?(di=t,t={type:"origin",value:"with rollup"}):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(o=null),o!==s?(di=t,u=o,e={columns:n.value,modifiers:[u]},t=e):(vi=t,t=s)):(vi=t,t=s);var u;return t}())===s&&(f=null),f!==s&&Bp()!==s?((p=function(){var t,e;t=vi,function(){var t,e,n,o;t=vi,"having"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(nu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(e=nl())!==s?(di=t,t=e):(vi=t,t=s);return t}())===s&&(p=null),p!==s&&Bp()!==s?((b=Dc())===s&&(b=null),b!==s&&Bp()!==s?((v=Fi())===s&&(v=null),v!==s&&Bp()!==s?((d=Gc())===s&&(d=null),d!==s&&Bp()!==s?((y=dc())===s&&(y=null),y!==s&&Bp()!==s?((w=function(){var t,e,n;t=vi,"window"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Mn));e!==s&&Bp()!==s&&(n=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Dl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Dl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Dl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=Cb(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,t=e={keyword:"window",type:"window",expr:n}):(vi=t,t=s);return t}())===s&&(w=null),w!==s&&Bp()!==s?((h=Ac())===s&&(h=null),h!==s?(di=t,t=function(r,t,e,n,o,s,u,a,i,c,l,f,p,b,v,d){if(o&&u||o&&d||u&&d||o&&u&&d)throw new Error("A given SQL statement can contain at most one INTO clause");if(s){(Array.isArray(s)?s:s.expr).forEach(r=>r.table&&Sb.add(`select::${r.db}::${r.table}`))}return{with:r,type:"select",options:t,distinct:e,columns:n,into:{...o||u||d||{},position:(o?"column":u&&"from")||d&&"end"},from:s,where:a,groupby:i,having:c,orderby:l,collate:f,limit:p,locking_read:b&&b,window:v}}(e,n,o,u,a,i,c,l,f,p,b,v,d,y,w,h)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function wc(){var t,e;return t=vi,(e=function(){var t;"sql_calc_found_rows"===r.substr(vi,19).toLowerCase()?(t=r.substr(vi,19),vi+=19):(t=s,0===Li&&gi($a));return t}())===s&&((e=function(){var t;"sql_cache"===r.substr(vi,9).toLowerCase()?(t=r.substr(vi,9),vi+=9):(t=s,0===Li&&gi(Ga));return t}())===s&&(e=function(){var t;"sql_no_cache"===r.substr(vi,12).toLowerCase()?(t=r.substr(vi,12),vi+=12):(t=s,0===Li&&gi(Fa));return t}()),e===s&&(e=function(){var t;"sql_big_result"===r.substr(vi,14).toLowerCase()?(t=r.substr(vi,14),vi+=14):(t=s,0===Li&&gi(Ya));return t}())===s&&(e=function(){var t;"sql_small_result"===r.substr(vi,16).toLowerCase()?(t=r.substr(vi,16),vi+=16):(t=s,0===Li&&gi(Ha));return t}())===s&&(e=function(){var t;"sql_buffer_result"===r.substr(vi,17).toLowerCase()?(t=r.substr(vi,17),vi+=17):(t=s,0===Li&&gi(Wa));return t}())),e!==s&&(di=t,e=e),t=e}function hc(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Wf())===s&&(t=vi,(e=Gp())!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t===s&&(t=Gp())),t!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=mc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=mc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){xb.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?Cb(e,t):[e]}(0,e)):(vi=r,r=s)}else vi=r,r=s;if(r===s)if(r=vi,(t=mc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=mc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=mc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function Lc(){var t,e,n,o,u,a,i;return t=vi,"match"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Ie)),e!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=Uc())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?("AGAINST"===r.substr(vi,7)?(o="AGAINST",vi+=7):(o=s,0===Li&&gi(je)),o!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(u=tl())!==s&&Bp()!==s?((a=function(){var t,e,n,o,u,a,i;return t=vi,Xf()!==s&&Bp()!==s?("natural"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(ge)),e!==s&&Bp()!==s?("language"===r.substr(vi,8).toLowerCase()?(n=r.substr(vi,8),vi+=8):(n=s,0===Li&&gi(_e)),n!==s&&Bp()!==s?("mode"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(Le)),o!==s&&Bp()!==s?("with"===r.substr(vi,4).toLowerCase()?(u=r.substr(vi,4),vi+=4):(u=s,0===Li&&gi(v)),u!==s&&Bp()!==s?("query"===r.substr(vi,5).toLowerCase()?(a=r.substr(vi,5),vi+=5):(a=s,0===Li&&gi(Te)),a!==s&&Bp()!==s?("expansion"===r.substr(vi,9).toLowerCase()?(i=r.substr(vi,9),vi+=9):(i=s,0===Li&&gi(Se)),i!==s?(di=t,t={type:"origin",value:"IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,Xf()!==s&&Bp()!==s?("natural"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(ge)),e!==s&&Bp()!==s?("language"===r.substr(vi,8).toLowerCase()?(n=r.substr(vi,8),vi+=8):(n=s,0===Li&&gi(_e)),n!==s&&Bp()!==s?("mode"===r.substr(vi,4).toLowerCase()?(o=r.substr(vi,4),vi+=4):(o=s,0===Li&&gi(Le)),o!==s?(di=t,t={type:"origin",value:"IN NATURAL LANGUAGE MODE"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,Xf()!==s&&Bp()!==s?("boolean"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(xe)),e!==s&&Bp()!==s?("mode"===r.substr(vi,4).toLowerCase()?(n=r.substr(vi,4),vi+=4):(n=s,0===Li&&gi(Le)),n!==s?(di=t,t={type:"origin",value:"IN BOOLEAN MODE"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,Gf()!==s&&Bp()!==s?("query"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Te)),e!==s&&Bp()!==s?("expansion"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Se)),n!==s?(di=t,t={type:"origin",value:"WITH QUERY EXPANSION"}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)))),t}())===s&&(a=null),a!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((i=Cc())===s&&(i=null),i!==s?(di=t,t=e={against:"against",columns:n,expr:u,match:"match",mode:a,type:"fulltext_search",as:i}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function mc(){var r,t,e,n,o,u,a,i;return r=vi,(t=Lc())!==s&&(di=r,t=function(r){const{as:t,...e}=r;return{expr:e,as:t}}(t)),(r=t)===s&&(r=vi,(t=Cl())!==s&&(e=Bp())!==s&&(n=Pp())!==s&&(o=Bp())!==s&&(u=Cl())!==s&&Bp()!==s&&Pp()!==s&&Bp()!==s&&Gp()!==s?(di=r,a=t,i=u,xb.add(`select::${a}::${i}::(.*)`),r=t={expr:{type:"column_ref",db:a,table:i,column:"*"},as:null}):(vi=r,r=s),r===s&&(r=vi,t=vi,(e=Cl())!==s&&(n=Bp())!==s&&(o=Pp())!==s?t=e=[e,n,o]:(vi=t,t=s),t===s&&(t=null),t!==s&&(e=Bp())!==s&&(n=Gp())!==s?(di=r,r=t=function(r){return xb.add(`select::${r}::(.*)`),{expr:{type:"column_ref",table:r&&r[0]||null,column:"*"},as:null}}(t)):(vi=r,r=s),r===s&&(r=vi,(t=function(){var r,t,e,n;r=vi,(t=ib())===s&&(t=cb());t!==s&&Bp()!==s&&(e=Sp())!==s&&Bp()!==s&&(n=rb())!==s?(di=r,t=oi(t,e,n),r=t):(vi=r,r=s);return r}())!==s&&(e=Bp())!==s?((n=Cc())===s&&(n=null),n!==s?(di=r,r=t={expr:t,as:n}):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=tl())!==s){for(e=[],n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf())===s&&(u=Wp()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf())===s&&(u=Wp()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=Lb(t[e][1],n,o)}return o}(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s&&(e=Bp())!==s?((n=Cc())===s&&(n=null),n!==s?(di=r,r=t=function(r,t){return{expr:r,as:t}}(t,n)):(vi=r,r=s)):(vi=r,r=s))))),r}function Cc(){var r,t,e;return r=vi,(t=If())!==s&&Bp()!==s&&(e=function(){var r,t;r=vi,(t=jl())!==s?(di=vi,(function(r){if(!0===db[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?s:void 0)!==s?(di=r,r=t=t):(vi=r,r=s)):(vi=r,r=s);r===s&&(r=El());return r}())!==s?(di=r,r=t=e):(vi=r,r=s),r===s&&(r=vi,(t=If())===s&&(t=null),t!==s&&Bp()!==s&&(e=Cl())!==s?(di=r,r=t=e):(vi=r,r=s)),r}function Ac(){var t,e,n;return t=vi,Tf()!==s&&Bp()!==s&&(e=function(){var r,t,e,n,o,u,a,c;if(r=vi,(t=ib())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=ib())!==s?n=o=[o,u,a,c]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(c=ib())!==s?n=o=[o,u,a,c]:(vi=n,n=s);e!==s?(di=r,t=i(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,t={keyword:"var",type:"into",expr:e}):(vi=t,t=s),t===s&&(t=vi,Tf()!==s&&Bp()!==s?("outfile"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Ne)),e===s&&("dumpfile"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Re))),e===s&&(e=null),e!==s&&Bp()!==s?((n=Jl())===s&&(n=Cl()),n!==s?(di=t,t={keyword:e,type:"into",expr:n}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)),t}function Ec(){var r,t;return r=vi,Sf()!==s&&Bp()!==s&&(t=xc())!==s?(di=r,r=t):(vi=r,r=s),r}function gc(){var r,t,e;return r=vi,(t=Rc())!==s&&Bp()!==s&&pf()!==s&&Bp()!==s&&(e=Rc())!==s?(di=r,r=t=[t,e]):(vi=r,r=s),r}function _c(){var t,e;return t=vi,$f()!==s&&Bp()!==s?("btree"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ke)),e===s&&("hash"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Oe))),e!==s?(di=t,t={keyword:"using",type:e.toLowerCase()}):(vi=t,t=s)):(vi=t,t=s),t}function Tc(){var r,t,e,n,o,u;if(r=vi,(t=Sc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Sc())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Sc())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function Sc(){var t,e,n,o,u,a;return t=vi,(e=function(){var t,e,n,o;t=vi,"key_block_size"===r.substr(vi,14).toLowerCase()?(e=r.substr(vi,14),vi+=14):(e=s,0===Li&&gi(yt));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="KEY_BLOCK_SIZE"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=xp())===s&&(n=null),n!==s&&Bp()!==s&&(o=nf())!==s?(di=t,u=n,a=o,t=e={type:e.toLowerCase(),symbol:u,expr:a}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=_c())===s&&(t=vi,"with"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(v)),e!==s&&Bp()!==s?("parser"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Ue)),n!==s&&Bp()!==s&&(o=jl())!==s?(di=t,t=e={type:"with parser",expr:o}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"visible"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Me)),e===s&&("invisible"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(De))),e!==s&&(di=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===s&&(t=Vp()))),t}function xc(){var r,t,e,n,o,u,a,i,c,l,f,p;if(r=vi,(t=jc())!==s){for(e=[],n=Ic();n!==s;)e.push(n),n=Ic();e!==s?(di=r,f=t,(p=e).unshift(f),p.forEach(r=>{const{table:t,as:e}=r;Ib[t]=t,e&&(Ib[e]=t),_b(xb)}),r=t=p):(vi=r,r=s)}else vi=r,r=s;if(r===s){if(r=vi,t=[],(e=Fp())!==s)for(;e!==s;)t.push(e),e=Fp();else t=s;if(t!==s)if((e=Bp())!==s)if((n=jc())!==s){for(o=[],u=Ic();u!==s;)o.push(u),u=Ic();if(o!==s)if((u=Bp())!==s){if(a=[],(i=Hp())!==s)for(;i!==s;)a.push(i),i=Hp();else a=s;if(a!==s)if((i=Bp())!==s){for(c=[],l=Ic();l!==s;)c.push(l),l=Ic();c!==s?(di=r,r=t=function(r,t,e,n,o){if(r.length!==n.length)throw new Error(`parentheses not match in from clause: ${r.length} != ${n.length}`);return e.unshift(t),e.forEach(r=>{const{table:t,as:e}=r;Ib[t]=t,e&&(Ib[e]=t),_b(xb)}),o.forEach(r=>{const{table:t,as:e}=r;Ib[t]=t,e&&(Ib[e]=t),_b(xb)}),{expr:e,parentheses:{length:n.length},joins:o}}(t,n,o,a,c)):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s}else vi=r,r=s;else vi=r,r=s}else vi=r,r=s;else vi=r,r=s;else vi=r,r=s}return r}function Ic(){var r,t,e;return r=vi,Bp()!==s&&(t=$p())!==s&&Bp()!==s&&(e=jc())!==s?(di=r,r=e):(vi=r,r=s),r===s&&(r=vi,Bp()!==s&&(t=function(){var r,t,e,n,o,u,a,i,c,l,f;if(r=vi,(t=Nc())!==s)if(Bp()!==s)if((e=jc())!==s)if(Bp()!==s)if((n=$f())!==s)if(Bp()!==s)if(Fp()!==s)if(Bp()!==s)if((o=Ll())!==s){for(u=[],a=vi,(i=Bp())!==s&&(c=$p())!==s&&(l=Bp())!==s&&(f=Ll())!==s?a=i=[i,c,l,f]:(vi=a,a=s);a!==s;)u.push(a),a=vi,(i=Bp())!==s&&(c=$p())!==s&&(l=Bp())!==s&&(f=Ll())!==s?a=i=[i,c,l,f]:(vi=a,a=s);u!==s&&(a=Bp())!==s&&(i=Hp())!==s?(di=r,p=t,v=o,d=u,(b=e).join=p,b.using=Cb(v,d),r=t=b):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;var p,b,v,d;r===s&&(r=vi,(t=Nc())!==s&&Bp()!==s&&(e=jc())!==s&&Bp()!==s?((n=kc())===s&&(n=null),n!==s?(di=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=Nc())===s&&(t=Ii()),t!==s&&Bp()!==s&&(e=Fp())!==s&&Bp()!==s&&(n=ji())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((o=Cc())===s&&(o=null),o!==s&&(u=Bp())!==s?((a=kc())===s&&(a=null),a!==s?(di=r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,a),r=t):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)));return r}())!==s?(di=r,r=t):(vi=r,r=s)),r}function jc(){var t,e,n,o,u,a,i;return t=vi,(e=function(){var t;"dual"===r.substr(vi,4).toLowerCase()?(t=r.substr(vi,4),vi+=4):(t=s,0===Li&&gi(ja));return t}())!==s&&(di=t,e={type:"dual"}),(t=e)===s&&(t=vi,(e=Rc())!==s&&Bp()!==s?((n=Cc())===s&&(n=null),n!==s?(di=t,i=n,t=e="var"===(a=e).type?(a.as=i,a):{db:a.db,table:a.table,as:i}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Fp())!==s&&Bp()!==s&&(n=Rc())!==s&&Bp()!==s?((o=Cc())===s&&(o=null),o!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e=function(r,t,e){return"var"===r.type?(r.as=t,r.parentheses=!0,r):{db:r.db,table:r.table,as:t,parentheses:!0}}(n,o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Vc())!==s&&Bp()!==s?((n=Cc())===s&&(n=null),n!==s?(di=t,t=e=function(r,t){return{expr:{type:"values",values:r,prefix:"row"},as:t}}(e,n)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,"lateral"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Pe)),e===s&&(e=null),e!==s&&Bp()!==s&&(n=Fp())!==s&&Bp()!==s?((o=ji())===s&&(o=Vc()),o!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((u=Cc())===s&&(u=null),u!==s?(di=t,t=e=function(r,t,e){Array.isArray(t)&&(t={type:"values",values:t,prefix:"row"}),t.parentheses=!0;const n={expr:t,as:e};return r&&(n.prefix=r),n}(e,o,u)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s))))),t}function Nc(){var t,e,n,o;return t=vi,(e=function(){var t,e,n,o;t=vi,"left"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Hs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(n=Bp())!==s?((o=Df())===s&&(o=null),o!==s&&Bp()!==s&&Mf()!==s?(di=t,t=e="LEFT JOIN"):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"right"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Ys));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(n=Bp())!==s?((o=Df())===s&&(o=null),o!==s&&Bp()!==s&&Mf()!==s?(di=t,t=e="RIGHT JOIN"):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"full"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Ws));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(n=Bp())!==s?((o=Df())===s&&(o=null),o!==s&&Bp()!==s&&Mf()!==s?(di=t,t=e="FULL JOIN"):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"cross"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(qs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(n=Bp())!==s&&(o=Mf())!==s?(di=t,t=e="CROSS JOIN"):(vi=t,t=s),t===s&&(t=vi,e=vi,(n=function(){var t,e,n,o;t=vi,"inner"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Bs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(o=Bp())!==s?e=n=[n,o]:(vi=e,e=s),e===s&&(e=null),e!==s&&(n=Mf())!==s?(di=t,t=e="INNER JOIN"):(vi=t,t=s))))),t}function Rc(){var t,e,n,o,u,a,i,c,l;if(t=vi,e=[],$e.test(r.charAt(vi))?(n=r.charAt(vi),vi++):(n=s,0===Li&&gi(Ge)),n!==s)for(;n!==s;)e.push(n),$e.test(r.charAt(vi))?(n=r.charAt(vi),vi++):(n=s,0===Li&&gi(Ge));else e=s;return e!==s&&(n=ml())!==s?(o=vi,(u=Bp())!==s&&(a=Pp())!==s&&(i=Bp())!==s&&(c=ml())!==s?o=u=[u,a,i,c]:(vi=o,o=s),o===s&&(o=null),o!==s?(di=t,t=e=function(r,t,e){const n=`${r.join("")}${t}`,o={db:null,table:n};return null!==e&&(o.db=n,o.table=e[3]),o}(e,n,o)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Cl())!==s?(n=vi,(o=Bp())!==s&&(u=Pp())!==s&&(a=Bp())!==s&&(i=Cl())!==s?n=o=[o,u,a,i]:(vi=n,n=s),n===s&&(n=null),n!==s?(di=t,t=e=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(e,n)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=ib())!==s&&(di=t,(l=e).db=null,l.table=l.name,e=l),t=e)),t}function kc(){var r,t;return r=vi,Uf()!==s&&Bp()!==s&&(t=el())!==s?(di=r,r=t):(vi=r,r=s),r}function Oc(){var t,e;return t=vi,function(){var t,e,n,o;t=vi,"where"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ru));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(e=nl())!==s?(di=t,t=e):(vi=t,t=s),t}function Uc(){var r,t,e,n,o,u,a,i;if(r=vi,(t=yl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=yl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=yl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function Mc(){var r,t;return r=vi,_f()!==s&&Bp()!==s&&Ff()!==s&&Bp()!==s&&(t=hc())!==s?(di=r,r=t):(vi=r,r=s),r}function Dc(){var t,e;return t=vi,function(){var t,e,n,o;t=vi,"order"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(eu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&Ff()!==s&&Bp()!==s&&(e=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Pc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Pc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Pc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=sr(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=t,t=e):(vi=t,t=s),t}function Pc(){var r,t,e;return r=vi,(t=tl())!==s&&Bp()!==s?((e=Yf())===s&&(e=Hf()),e===s&&(e=null),e!==s?(di=r,r=t={expr:t,type:e}):(vi=r,r=s)):(vi=r,r=s),r}function $c(){var t,e;return(t=nf())===s&&(t=Ol())===s&&(t=vi,63===r.charCodeAt(vi)?(e="?",vi++):(e=s,0===Li&&gi(He)),e!==s&&(di=t,e={type:"origin",value:"?"}),t=e),t}function Gc(){var t,e,n,o,u,a;return t=vi,function(){var t,e,n,o;t=vi,"limit"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ou));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(e=$c())!==s&&Bp()!==s?(n=vi,(o=$p())===s&&(o=function(){var t,e,n,o;t=vi,"offset"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(su));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="OFFSET"):(vi=t,t=s)):(vi=t,t=s);return t}()),o!==s&&(u=Bp())!==s&&(a=$c())!==s?n=o=[o,u,a]:(vi=n,n=s),n===s&&(n=null),n!==s?(di=t,t=function(r,t){const e=[r];return t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,n)):(vi=t,t=s)):(vi=t,t=s),t}function Fc(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Hc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Hc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Hc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function Hc(){var t,e,n,o,u,a,i,c,l;return t=vi,e=vi,(n=Cl())!==s&&(o=Bp())!==s&&(u=Pp())!==s?e=n=[n,o,u]:(vi=e,e=s),e===s&&(e=null),e!==s&&(n=Bp())!==s&&(o=Sl())!==s&&(u=Bp())!==s?(61===r.charCodeAt(vi)?(a="=",vi++):(a=s,0===Li&&gi(Ye)),a!==s&&Bp()!==s&&(i=fl())!==s?(di=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,e=vi,(n=Cl())!==s&&(o=Bp())!==s&&(u=Pp())!==s?e=n=[n,o,u]:(vi=e,e=s),e===s&&(e=null),e!==s&&(n=Bp())!==s&&(o=Sl())!==s&&(u=Bp())!==s?(61===r.charCodeAt(vi)?(a="=",vi++):(a=s,0===Li&&gi(Ye)),a!==s&&Bp()!==s&&(i=Pf())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(c=yl())!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(vi=t,t=s)):(vi=t,t=s)),t}function Yc(){var t,e,n,o,u;return t=vi,(e=function(){var t,e,n,o;t=vi,"returning"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(Is));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="RETURNING"):(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&Bp()!==s?((n=hc())===s&&(n=fc()),n!==s?(di=t,u=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===u&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||u}):(vi=t,t=s)):(vi=t,t=s),t}function Wc(){var r;return(r=Vc())===s&&(r=yc()),r}function Bc(){var r,t,e,n,o,u,a,i,c;if(r=vi,_f()!==s)if(Bp()!==s)if((t=Fp())!==s)if(Bp()!==s)if((e=jl())!==s){for(n=[],o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=jl())!==s?o=u=[u,a,i,c]:(vi=o,o=s);o!==s;)n.push(o),o=vi,(u=Bp())!==s&&(a=$p())!==s&&(i=Bp())!==s&&(c=jl())!==s?o=u=[u,a,i,c]:(vi=o,o=s);n!==s&&(o=Bp())!==s&&(u=Hp())!==s?(di=r,r=L(e,n)):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;else vi=r,r=s;return r===s&&(r=vi,_f()!==s&&Bp()!==s&&(t=Kc())!==s?(di=r,r=t):(vi=r,r=s)),r}function qc(){var t,e,n;return t=vi,Uf()!==s&&Bp()!==s?("duplicate"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(We)),e!==s&&Bp()!==s&&Rp()!==s&&Bp()!==s&&wf()!==s&&Bp()!==s&&(n=Fc())!==s?(di=t,t={keyword:"on duplicate key update",set:n}):(vi=t,t=s)):(vi=t,t=s),t}function Xc(){var r,t;return r=vi,(t=Cf())!==s&&(di=r,t="insert"),(r=t)===s&&(r=vi,(t=Af())!==s&&(di=r,t="replace"),r=t),r}function Vc(){var r,t;return r=vi,Pf()!==s&&Bp()!==s&&(t=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=Kc())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Kc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=Kc())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=sr(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())!==s?(di=r,r=t):(vi=r,r=s),r}function Kc(){var t,e,n;return t=vi,"row"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(yr)),e===s&&(e=null),e!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=Qc())!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e=n):(vi=t,t=s),t}function Qc(){var r,t,e,n,o,u,a,i;if(r=vi,(t=tl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Cb(r,t),e}(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function zc(){var t,e,n;return t=vi,Cp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&(n=function(){var t;(t=function(){var t,e,n,o;t=vi,"year"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Co));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="YEAR"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"month"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(vo));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MONTH"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"week"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(mo));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="WEEK"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"day"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(ro));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DAY"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"hour"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(uo));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="HOUR"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"minute"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(bo));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MINUTE"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"second"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(wo));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SECOND"):(vi=t,t=s)):(vi=t,t=s);return t}());return t}())!==s?(di=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(vi=t,t=s),t}function Zc(){var r,t,e,n,o,u;if(r=vi,(t=Jc())!==s)if(Bp()!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=Jc())!==s?n=o=[o,u]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=Jc())!==s?n=o=[o,u]:(vi=n,n=s);e!==s?(di=r,r=t=l(t,e)):(vi=r,r=s)}else vi=r,r=s;else vi=r,r=s;return r}function Jc(){var t,e,n;return t=vi,function(){var t,e,n,o;t=vi,"when"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(xu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(e=nl())!==s&&Bp()!==s&&function(){var t,e,n,o;t=vi,"then"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Iu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(n=tl())!==s?(di=t,t={type:"when",cond:e,result:n}):(vi=t,t=s),t}function rl(){var t,e;return t=vi,function(){var t,e,n,o;t=vi,"else"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ju));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}()!==s&&Bp()!==s&&(e=tl())!==s?(di=t,t={type:"else",result:e}):(vi=t,t=s),t}function tl(){var r;return(r=function(){var r,t,e,n,o,u,a,i;if(r=vi,(t=ol())!==s){for(e=[],n=vi,(o=qp())!==s&&(u=Jf())!==s&&(a=Bp())!==s&&(i=ol())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=qp())!==s&&(u=Jf())!==s&&(a=Bp())!==s&&(i=ol())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,t=Be(t,e),r=t):(vi=r,r=s)}else vi=r,r=s;return r}())===s&&(r=ji()),r}function el(){var r,t,e,n,o,u,a,i;if(r=vi,(t=tl())!==s){for(e=[],n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=Lb(t[r][1],n,t[r][3]);return n}(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function nl(){var r,t,e,n,o,u,a,i;if(r=vi,(t=tl())!==s){for(e=[],n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf())===s&&(u=$p()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=Zf())===s&&(u=Jf())===s&&(u=$p()),u!==s&&(a=Bp())!==s&&(i=tl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=Lb(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=Array.isArray(n)?n:[n],r}return n}(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function ol(){var r,t,e,n,o,u,a,i;if(r=vi,(t=sl())!==s){for(e=[],n=vi,(o=qp())!==s&&(u=Zf())!==s&&(a=Bp())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=qp())!==s&&(u=Zf())!==s&&(a=Bp())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=Be(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function sl(){var r,t;return(r=ul())===s&&(r=function(){var r,t,e;r=vi,(t=function(){var r,t,e,n,o;r=vi,t=vi,(e=zf())!==s&&(n=Bp())!==s&&(o=Qf())!==s?t=e=[e,n,o]:(vi=t,t=s);t!==s&&(di=r,t=qe(t));(r=t)===s&&(r=Qf());return r}())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=ji())!==s&&Bp()!==s&&Hp()!==s?(di=r,n=t,(o=e).parentheses=!0,t=hb(n,o),r=t):(vi=r,r=s);var n,o;return r}())===s&&(r=vi,zf()!==s&&Bp()!==s&&(t=sl())!==s?(di=r,r=hb("NOT",t)):(vi=r,r=s)),r}function ul(){var t,e,n,o,u;return t=vi,(e=fl())!==s&&Bp()!==s?((n=function(){var t;(t=function(){var r,t,e,n,o,u,a;r=vi,t=[],e=vi,(n=Bp())!==s&&(o=al())!==s&&(u=Bp())!==s&&(a=fl())!==s?e=n=[n,o,u,a]:(vi=e,e=s);if(e!==s)for(;e!==s;)t.push(e),e=vi,(n=Bp())!==s&&(o=al())!==s&&(u=Bp())!==s&&(a=fl())!==s?e=n=[n,o,u,a]:(vi=e,e=s);else t=s;t!==s&&(di=r,t={type:"arithmetic",tail:t});return r=t}())===s&&(t=ll())===s&&(t=function(){var r,t,e,n;r=vi,(t=function(){var r,t,e,n,o;r=vi,t=vi,(e=zf())!==s&&(n=Bp())!==s&&(o=qf())!==s?t=e=[e,n,o]:(vi=t,t=s);t!==s&&(di=r,t=qe(t));(r=t)===s&&(r=qf());return r}())!==s&&Bp()!==s&&(e=fl())!==s&&Bp()!==s&&Zf()!==s&&Bp()!==s&&(n=fl())!==s?(di=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(vi=r,r=s);return r}())===s&&(t=function(){var r,t,e,n,o;r=vi,(t=Vf())!==s&&(e=Bp())!==s&&(n=fl())!==s?(di=r,r=t={op:"IS",right:n}):(vi=r,r=s);r===s&&(r=vi,t=vi,(e=Vf())!==s&&(n=Bp())!==s&&(o=zf())!==s?t=e=[e,n,o]:(vi=t,t=s),t!==s&&(e=Bp())!==s&&(n=fl())!==s?(di=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(vi=r,r=s));return r}())===s&&(t=cl())===s&&(t=function(){var t,e,n,o;t=vi,(e=function(){var t,e,n;t=vi,(e=zf())===s&&(e=null);e!==s&&Bp()!==s?((n=function(){var t,e,n,o;t=vi,"regexp"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(du));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="REGEXP"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(n=function(){var t,e,n,o;t=vi,"rlike"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(vu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="RLIKE"):(vi=t,t=s)):(vi=t,t=s);return t}()),n!==s?(di=t,u=n,t=e=(o=e)?`${o} ${u}`:u):(vi=t,t=s)):(vi=t,t=s);var o,u;return t}())!==s&&Bp()!==s?("binary"===r.substr(vi,6).toLowerCase()?(n=r.substr(vi,6),vi+=6):(n=s,0===Li&&gi(Gt)),n===s&&(n=null),n!==s&&Bp()!==s?((o=Xl())===s&&(o=Jl())===s&&(o=yl()),o!==s?(di=t,u=e,t=e={op:(a=n)?`${u} ${a}`:u,right:o}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s);var u,a;return t}());return t}())===s&&(n=null),n!==s?(di=t,o=e,t=e=null===(u=n)?o:"arithmetic"===u.type?Ab(o,u.tail):Lb(u.op,o,u.right)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=Jl())===s&&(t=yl()),t}function al(){var t;return">="===r.substr(vi,2)?(t=">=",vi+=2):(t=s,0===Li&&gi(Xe)),t===s&&(62===r.charCodeAt(vi)?(t=">",vi++):(t=s,0===Li&&gi(Ve)),t===s&&("<="===r.substr(vi,2)?(t="<=",vi+=2):(t=s,0===Li&&gi(Ke)),t===s&&("<>"===r.substr(vi,2)?(t="<>",vi+=2):(t=s,0===Li&&gi(Qe)),t===s&&(60===r.charCodeAt(vi)?(t="<",vi++):(t=s,0===Li&&gi(ze)),t===s&&(61===r.charCodeAt(vi)?(t="=",vi++):(t=s,0===Li&&gi(Ye)),t===s&&("!="===r.substr(vi,2)?(t="!=",vi+=2):(t=s,0===Li&&gi(Ze)))))))),t}function il(){var r,t,e,n,o;return r=vi,t=vi,(e=zf())!==s&&(n=Bp())!==s&&(o=Xf())!==s?t=e=[e,n,o]:(vi=t,t=s),t!==s&&(di=r,t=qe(t)),(r=t)===s&&(r=Xf()),r}function cl(){var t,e,n,o,u,a,i;return t=vi,(e=function(){var r,t,e,n,o;return r=vi,t=vi,(e=zf())!==s&&(n=Bp())!==s&&(o=Kf())!==s?t=e=[e,n,o]:(vi=t,t=s),t!==s&&(di=r,t=qe(t)),(r=t)===s&&(r=Kf()),r}())!==s&&Bp()!==s?((n=zl())===s&&(n=Ol())===s&&(n=ul()),n!==s&&Bp()!==s?((o=function(){var t,e,n;return t=vi,"escape"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Je)),e!==s&&Bp()!==s&&(n=Jl())!==s?(di=t,t=e={type:"ESCAPE",value:n}):(vi=t,t=s),t}())===s&&(o=null),o!==s?(di=t,u=e,a=n,(i=o)&&(a.escape=i),t=e={op:u,right:a}):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t}function ll(){var r,t,e,n;return r=vi,(t=il())!==s&&Bp()!==s&&(e=Fp())!==s&&Bp()!==s&&(n=Qc())!==s&&Bp()!==s&&Hp()!==s?(di=r,r=t={op:t,right:n}):(vi=r,r=s),r===s&&(r=vi,(t=il())!==s&&Bp()!==s?((e=ib())===s&&(e=yl())===s&&(e=Jl()),e!==s?(di=r,r=t=function(r,t){return{op:r,right:t}}(t,e)):(vi=r,r=s)):(vi=r,r=s)),r}function fl(){var r,t,e,n,o,u,a,i;if(r=vi,(t=bl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=pl())!==s&&(a=Bp())!==s&&(i=bl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=pl())!==s&&(a=Bp())!==s&&(i=bl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...wb()}));return Ab(r,t)}(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function pl(){var t;return 43===r.charCodeAt(vi)?(t="+",vi++):(t=s,0===Li&&gi(rn)),t===s&&(45===r.charCodeAt(vi)?(t="-",vi++):(t=s,0===Li&&gi(tn))),t}function bl(){var r,t,e,n,o,u,a,i;if(r=vi,(t=dl())!==s){for(e=[],n=vi,(o=Bp())!==s?((u=vl())===s&&(u=Wp()),u!==s&&(a=Bp())!==s&&(i=dl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s?((u=vl())===s&&(u=Wp()),u!==s&&(a=Bp())!==s&&(i=dl())!==s?n=o=[o,u,a,i]:(vi=n,n=s)):(vi=n,n=s);e!==s?(di=r,r=t=Ab(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function vl(){var t,e;return 42===r.charCodeAt(vi)?(t="*",vi++):(t=s,0===Li&&gi(en)),t===s&&(47===r.charCodeAt(vi)?(t="/",vi++):(t=s,0===Li&&gi(nn)),t===s&&(37===r.charCodeAt(vi)?(t="%",vi++):(t=s,0===Li&&gi(on)),t===s&&("||"===r.substr(vi,2)?(t="||",vi+=2):(t=s,0===Li&&gi(sn)),t===s&&(t=vi,"div"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(un)),e===s&&("mod"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(an))),e!==s&&(di=t,e=e.toUpperCase()),(t=e)===s&&(38===r.charCodeAt(vi)?(t="&",vi++):(t=s,0===Li&&gi(cn)),t===s&&(">>"===r.substr(vi,2)?(t=">>",vi+=2):(t=s,0===Li&&gi(ln)),t===s&&("<<"===r.substr(vi,2)?(t="<<",vi+=2):(t=s,0===Li&&gi(fn)),t===s&&(94===r.charCodeAt(vi)?(t="^",vi++):(t=s,0===Li&&gi(pn)),t===s&&(124===r.charCodeAt(vi)?(t="|",vi++):(t=s,0===Li&&gi(bn))))))))))),t}function dl(){var t,e,n,o,u;return(t=function(){var t,e,n;(t=function(){var t;(t=function(){var t,e,n,o;t=vi,(e=function(){var t,e,n,o;t=vi,"count"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Lu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="COUNT"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(e=function(){var t,e,n,o;t=vi,"group_concat"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(mu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="GROUP_CONCAT"):(vi=t,t=s)):(vi=t,t=s);return t}());e!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=function(){var t,e,n,o,u;t=vi,(e=function(){var t,e;t=vi,42===r.charCodeAt(vi)?(e="*",vi++):(e=s,0===Li&&gi(en));e!==s&&(di=t,e={type:"star",value:"*"});return t=e}())!==s&&(di=t,e={expr:e,...wb()});(t=e)===s&&(t=vi,(e=Bf())===s&&(e=null),e!==s&&Bp()!==s&&(n=nl())!==s&&Bp()!==s?((o=Dc())===s&&(o=null),o!==s&&Bp()!==s?((u=function(){var t,e,n;t=vi,"separator"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi($n));e===s&&(e=null);e!==s&&Bp()!==s&&(n=Jl())!==s?(di=t,t=e={keyword:e,value:n}):(vi=t,t=s);return t}())===s&&(u=null),u!==s?(di=t,e={distinct:e,expr:n,orderby:o,separator:u,...wb()},t=e):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s));return t}())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((o=Ml())===s&&(o=null),o!==s?(di=t,t=e={type:"aggr_func",name:e,args:n,over:o}):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,(e=function(){var t;(t=function(){var t,e,n,o;t=vi,"sum"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Eu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SUM"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"max"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Cu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MAX"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"min"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Au));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MIN"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"avg"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(gu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="AVG"):(vi=t,t=s)):(vi=t,t=s);return t}());return t}())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=tl())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s?((o=Ml())===s&&(o=null),o!==s?(di=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...wb()},t=e):(vi=t,t=s)):(vi=t,t=s);return t}());return t}())===s&&(t=Lc())===s&&(t=Xl())===s&&(t=function(){var r,t,e,n,o,u,a;r=vi,(t=np())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(n=pb())!==s&&Bp()!==s&&(o=ec())!==s&&Bp()!==s&&(u=Ll())!==s&&Bp()!==s&&Hp()!==s?(di=r,t=function(r,t,e,n,o){const{dataType:s,length:u}=e;let a=s;return void 0!==u&&(a=`${a}(${u})`),{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:a,suffix:[{type:"origin",value:n},o]}]}}(t,e,n,o,u),r=t):(vi=r,r=s);r===s&&(r=vi,(t=np())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(n=lb())!==s&&Bp()!==s&&(o=Hp())!==s?(di=r,i=e,c=n,t={type:"cast",keyword:t.toLowerCase(),expr:i,symbol:"as",target:[c]},r=t):(vi=r,r=s),r===s&&(r=vi,(t=np())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(n=up())!==s&&Bp()!==s&&(o=Fp())!==s&&Bp()!==s&&(u=of())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s&&(a=Hp())!==s?(di=r,t=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(t,e,u),r=t):(vi=r,r=s),r===s&&(r=vi,(t=np())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(n=up())!==s&&Bp()!==s&&(o=Fp())!==s&&Bp()!==s&&(u=of())!==s&&Bp()!==s&&$p()!==s&&Bp()!==s&&(a=of())!==s&&Bp()!==s&&Hp()!==s&&Bp()!==s&&Hp()!==s?(di=r,t=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(t,e,u,a),r=t):(vi=r,r=s),r===s&&(r=vi,(t=np())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(e=tl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(n=Kl())!==s&&Bp()!==s?((o=ip())===s&&(o=null),o!==s&&Bp()!==s&&(u=Hp())!==s?(di=r,t=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:[e,n].filter(Boolean).join(" ")}]}}(t,e,n,o),r=t):(vi=r,r=s)):(vi=r,r=s)))));var i,c;return r}())===s&&(t=function(){var r,t,e,n,o,u,a,i;return r=vi,tp()!==s&&Bp()!==s&&(t=Zc())!==s&&Bp()!==s?((e=rl())===s&&(e=null),e!==s&&Bp()!==s&&(n=ep())!==s&&Bp()!==s?((o=tp())===s&&(o=null),o!==s?(di=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,tp()!==s&&Bp()!==s&&(t=tl())!==s&&Bp()!==s&&(e=Zc())!==s&&Bp()!==s?((n=rl())===s&&(n=null),n!==s&&Bp()!==s&&(o=ep())!==s&&Bp()!==s?((u=tp())===s&&(u=null),u!==s?(di=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s)),r}())===s&&(t=zc())===s&&(t=Ql())===s&&(t=yl())===s&&(t=nf())===s&&(t=Ol())===s&&(t=vi,Fp()!==s&&(e=Bp())!==s&&(n=nl())!==s&&Bp()!==s&&Hp()!==s?(di=t,(o=n).parentheses=!0,t=o):(vi=t,t=s),t===s&&(t=ib())===s&&(t=vi,Bp()!==s?(63===r.charCodeAt(vi)?(e="?",vi++):(e=s,0===Li&&gi(He)),e!==s?(di=t,t={type:"origin",value:e}):(vi=t,t=s)):(vi=t,t=s)));var o;return t}())===s&&(t=vi,(e=function(){var t;33===r.charCodeAt(vi)?(t="!",vi++):(t=s,0===Li&&gi(vn));t===s&&(45===r.charCodeAt(vi)?(t="-",vi++):(t=s,0===Li&&gi(tn)),t===s&&(43===r.charCodeAt(vi)?(t="+",vi++):(t=s,0===Li&&gi(rn)),t===s&&(126===r.charCodeAt(vi)?(t="~",vi++):(t=s,0===Li&&gi(dn)))));return t}())!==s?(n=vi,(o=Bp())!==s&&(u=dl())!==s?n=o=[o,u]:(vi=n,n=s),n!==s?(di=t,t=e=hb(e,n[1])):(vi=t,t=s)):(vi=t,t=s)),t}function yl(){var r,t,e,n,o,u,a,i,c,l,f,p,b,v,d,y;return r=vi,(t=jl())===s&&(t=Tl()),t!==s&&Bp()!==s&&(e=Pp())!==s&&(n=Bp())!==s?((o=jl())===s&&(o=Tl()),o!==s&&(u=Bp())!==s&&(a=Pp())!==s&&(i=Bp())!==s&&(c=Sl())!==s?(l=vi,(f=Bp())!==s&&(p=Fi())!==s?l=f=[f,p]:(vi=l,l=s),l===s&&(l=null),l!==s?(di=r,b=t,v=o,d=c,y=l,xb.add(`select::${b}::${v}::${d}`),r=t={type:"column_ref",db:b,table:v,column:d,collate:y&&y[1],...wb()}):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=jl())===s&&(t=Tl()),t!==s&&Bp()!==s&&(e=Pp())!==s&&(n=Bp())!==s&&(o=Sl())!==s?(u=vi,(a=Bp())!==s&&(i=Fi())!==s?u=a=[a,i]:(vi=u,u=s),u===s&&(u=null),u!==s?(di=r,r=t=function(r,t,e){return xb.add(`select::${r}::${t}`),{type:"column_ref",table:r,column:t,collate:e&&e[1],...wb()}}(t,o,u)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=xl())!==s&&Bp()!==s?(e=vi,(n=Bp())!==s&&(o=Fi())!==s?e=n=[n,o]:(vi=e,e=s),e===s&&(e=null),e!==s?(di=r,r=t=function(r,t){return xb.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1],...wb()}}(t,e)):(vi=r,r=s)):(vi=r,r=s))),r}function wl(){var r,t,e,n,o,u,a,i;if(r=vi,(t=xl())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=xl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=xl())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function hl(){var r,t;return r=vi,(t=jl())!==s&&(di=r,t=yn(t)),r=t}function Ll(){var r,t;return r=vi,(t=jl())!==s&&(di=r,t=yn(t)),(r=t)===s&&(r=Al()),r}function ml(){var r;return(r=jl())===s&&(r=El()),r}function Cl(){var r,t;return r=vi,(t=jl())!==s?(di=vi,(wn(t)?s:void 0)!==s?(di=r,r=t=t):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=El()),r}function Al(){var r;return(r=gl())===s&&(r=_l())===s&&(r=Tl()),r}function El(){var r,t;return r=vi,(t=gl())===s&&(t=_l())===s&&(t=Tl()),t!==s&&(di=r,t=t.value),r=t}function gl(){var t,e,n,o;if(t=vi,34===r.charCodeAt(vi)?(e='"',vi++):(e=s,0===Li&&gi(hn)),e!==s){if(n=[],Ln.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(mn)),o!==s)for(;o!==s;)n.push(o),Ln.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(mn));else n=s;n!==s?(34===r.charCodeAt(vi)?(o='"',vi++):(o=s,0===Li&&gi(hn)),o!==s?(di=t,t=e={type:"double_quote_string",value:n.join("")}):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;return t}function _l(){var t,e,n,o;if(t=vi,39===r.charCodeAt(vi)?(e="'",vi++):(e=s,0===Li&&gi(Nt)),e!==s){if(n=[],Cn.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(An)),o!==s)for(;o!==s;)n.push(o),Cn.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(An));else n=s;n!==s?(39===r.charCodeAt(vi)?(o="'",vi++):(o=s,0===Li&&gi(Nt)),o!==s?(di=t,t=e={type:"single_quote_string",value:n.join("")}):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;return t}function Tl(){var t,e,n,o;if(t=vi,96===r.charCodeAt(vi)?(e="`",vi++):(e=s,0===Li&&gi(En)),e!==s){if(n=[],gn.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(_n)),o===s&&(o=ef()),o!==s)for(;o!==s;)n.push(o),gn.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(_n)),o===s&&(o=ef());else n=s;n!==s?(96===r.charCodeAt(vi)?(o="`",vi++):(o=s,0===Li&&gi(En)),o!==s?(di=t,t=e={type:"backticks_quote_string",value:n.join("")}):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;return t}function Sl(){var r,t;return r=vi,(t=Il())!==s&&(di=r,t=t),(r=t)===s&&(r=El()),r}function xl(){var r,t;return r=vi,(t=Il())!==s?(di=vi,(wn(t)?s:void 0)!==s?(di=r,r=t=t):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=Tl())!==s&&(di=r,t=t.value),r=t),r}function Il(){var r,t,e,n;if(r=vi,(t=Nl())!==s){for(e=[],n=kl();n!==s;)e.push(n),n=kl();e!==s?(di=r,r=t=Tn(t,e)):(vi=r,r=s)}else vi=r,r=s;if(r===s)if(r=vi,(t=af())!==s){if(e=[],(n=kl())!==s)for(;n!==s;)e.push(n),n=kl();else e=s;e!==s?(di=r,r=t=Tn(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function jl(){var r,t,e,n;if(r=vi,(t=Nl())!==s){for(e=[],n=Rl();n!==s;)e.push(n),n=Rl();e!==s?(di=r,r=t=Tn(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function Nl(){var t;return Sn.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(xn)),t}function Rl(){var t;return In.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(jn)),t}function kl(){var t;return Nn.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(Rn)),t}function Ol(){var t,e,n,o;return t=vi,e=vi,58===r.charCodeAt(vi)?(n=":",vi++):(n=s,0===Li&&gi(kn)),n!==s&&(o=jl())!==s?e=n=[n,o]:(vi=e,e=s),e!==s&&(di=t,e={type:"param",value:e[1]}),t=e}function Ul(){var t,e,n,o,u,a,i,c,l;return t=vi,Uf()!==s&&Bp()!==s&&wf()!==s&&Bp()!==s&&(e=Ap())!==s&&Bp()!==s?(n=vi,(o=Fp())!==s&&(u=Bp())!==s?((a=Qc())===s&&(a=null),a!==s&&(i=Bp())!==s&&(c=Hp())!==s?n=o=[o,u,a,i,c]:(vi=n,n=s)):(vi=n,n=s),n===s&&(n=null),n!==s?(di=t,t={type:"on update",keyword:e,parentheses:!!(l=n),expr:l?l[2]:null}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,Uf()!==s&&Bp()!==s&&wf()!==s&&Bp()!==s?("now"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(On)),e!==s&&Bp()!==s&&(n=Fp())!==s&&(o=Bp())!==s&&(u=Hp())!==s?(di=t,t=function(r){return{type:"on update",keyword:r,parentheses:!0}}(e)):(vi=t,t=s)):(vi=t,t=s)),t}function Ml(){var t,e,n;return t=vi,"over"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Un)),e!==s&&Bp()!==s&&(n=Pl())!==s?(di=t,t=e={type:"window",as_window_specification:n}):(vi=t,t=s),t===s&&(t=Ul()),t}function Dl(){var r,t,e;return r=vi,(t=jl())!==s&&Bp()!==s&&If()!==s&&Bp()!==s&&(e=Pl())!==s?(di=r,r=t={name:t,as_window_specification:e}):(vi=r,r=s),r}function Pl(){var r,t;return(r=jl())===s&&(r=vi,Fp()!==s&&Bp()!==s?((t=function(){var r,t,e,n;r=vi,(t=Mc())===s&&(t=null);t!==s&&Bp()!==s?((e=Dc())===s&&(e=null),e!==s&&Bp()!==s?((n=function(){var r,t,e,n,o;r=vi,(t=wp())!==s&&Bp()!==s?((e=$l())===s&&(e=Gl()),e!==s?(di=r,r=t={type:"rows",expr:e}):(vi=r,r=s)):(vi=r,r=s);r===s&&(r=vi,(t=wp())!==s&&Bp()!==s&&(e=qf())!==s&&Bp()!==s&&(n=Gl())!==s&&Bp()!==s&&Zf()!==s&&Bp()!==s&&(o=$l())!==s?(di=r,t=Lb(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),r=t):(vi=r,r=s));return r}())===s&&(n=null),n!==s?(di=r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(vi=r,r=s)):(vi=r,r=s)):(vi=r,r=s);return r}())===s&&(t=null),t!==s&&Bp()!==s&&Hp()!==s?(di=r,r={window_specification:t||{},parentheses:!0}):(vi=r,r=s)):(vi=r,r=s)),r}function $l(){var t,e,n,o;return t=vi,(e=Hl())!==s&&Bp()!==s?("following"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Dn)),n!==s?(di=t,(o=e).value+=" FOLLOWING",t=e=o):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=Fl()),t}function Gl(){var t,e,n,o,u;return t=vi,(e=Hl())!==s&&Bp()!==s?("preceding"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Pn)),n===s&&("following"===r.substr(vi,9).toLowerCase()?(n=r.substr(vi,9),vi+=9):(n=s,0===Li&&gi(Dn))),n!==s?(di=t,u=n,(o=e).value+=" "+u.toUpperCase(),t=e=o):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=Fl()),t}function Fl(){var t,e,n;return t=vi,"current"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(P)),e!==s&&Bp()!==s?("row"===r.substr(vi,3).toLowerCase()?(n=r.substr(vi,3),vi+=3):(n=s,0===Li&&gi(yr)),n!==s?(di=t,t=e={type:"origin",value:"current row"}):(vi=t,t=s)):(vi=t,t=s),t}function Hl(){var t,e;return t=vi,"unbounded"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(H)),e!==s&&(di=t,e={type:"origin",value:e.toUpperCase()}),(t=e)===s&&(t=nf()),t}function Yl(){var t,e;return t=vi,"year_month"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Gn)),e===s&&("day_hour"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Fn)),e===s&&("day_minute"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Hn)),e===s&&("day_second"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Yn)),e===s&&("day_microsecond"===r.substr(vi,15).toLowerCase()?(e=r.substr(vi,15),vi+=15):(e=s,0===Li&&gi(Wn)),e===s&&("hour_minute"===r.substr(vi,11).toLowerCase()?(e=r.substr(vi,11),vi+=11):(e=s,0===Li&&gi(Bn)),e===s&&("hour_second"===r.substr(vi,11).toLowerCase()?(e=r.substr(vi,11),vi+=11):(e=s,0===Li&&gi(qn)),e===s&&("hour_microsecond"===r.substr(vi,16).toLowerCase()?(e=r.substr(vi,16),vi+=16):(e=s,0===Li&&gi(Xn)),e===s&&("minute_second"===r.substr(vi,13).toLowerCase()?(e=r.substr(vi,13),vi+=13):(e=s,0===Li&&gi(Vn)),e===s&&("minute_microsecond"===r.substr(vi,18).toLowerCase()?(e=r.substr(vi,18),vi+=18):(e=s,0===Li&&gi(Kn)),e===s&&("second_microsecond"===r.substr(vi,18).toLowerCase()?(e=r.substr(vi,18),vi+=18):(e=s,0===Li&&gi(Qn)),e===s&&("timezone_hour"===r.substr(vi,13).toLowerCase()?(e=r.substr(vi,13),vi+=13):(e=s,0===Li&&gi(zn)),e===s&&("timezone_minute"===r.substr(vi,15).toLowerCase()?(e=r.substr(vi,15),vi+=15):(e=s,0===Li&&gi(Zn)),e===s&&("century"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Jn)),e===s&&("day"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(ro)),e===s&&("date"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(to)),e===s&&("decade"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(eo)),e===s&&("dow"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(no)),e===s&&("doy"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(oo)),e===s&&("epoch"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(so)),e===s&&("hour"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(uo)),e===s&&("isodow"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(ao)),e===s&&("isoweek"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(io)),e===s&&("isoyear"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(co)),e===s&&("microseconds"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(lo)),e===s&&("millennium"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(fo)),e===s&&("milliseconds"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(po)),e===s&&("minute"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(bo)),e===s&&("month"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(vo)),e===s&&("quarter"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(yo)),e===s&&("second"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(wo)),e===s&&("time"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ho)),e===s&&("timezone"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Lo)),e===s&&("week"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(mo)),e===s&&("year"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Co)))))))))))))))))))))))))))))))))))),e!==s&&(di=t,e=e),t=e}function Wl(){var t,e,n,o,u,a,i,c;return t=vi,(e=rp())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=Yl())!==s&&Bp()!==s&&Sf()!==s&&Bp()!==s?((o=Lp())===s&&(o=Cp())===s&&(o=hp())===s&&(o=dp()),o!==s&&Bp()!==s&&(u=tl())!==s&&Bp()!==s&&Hp()!==s?(di=t,a=n,i=o,c=u,t=e={type:e.toLowerCase(),args:{field:a,cast_type:i,source:c},...wb()}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=rp())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=Yl())!==s&&Bp()!==s&&Sf()!==s&&Bp()!==s&&(o=tl())!==s&&Bp()!==s&&(u=Hp())!==s?(di=t,t=e=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...wb()}}(e,n,o)):(vi=t,t=s),t===s&&(t=vi,"date_trunc"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Ao)),e!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s&&(n=tl())!==s&&Bp()!==s&&$p()!==s&&Bp()!==s&&(o=Yl())!==s&&Bp()!==s&&(u=Hp())!==s?(di=t,t=e=function(r,t){return{type:"function",name:{name:[{type:"origin",value:"date_trunc"}]},args:{type:"expr_list",value:[r,{type:"origin",value:t}]},over:null,...wb()}}(n,o)):(vi=t,t=s))),t}function Bl(){var t,e,n;return t=vi,(e=function(){var t;return"both"===r.substr(vi,4).toLowerCase()?(t=r.substr(vi,4),vi+=4):(t=s,0===Li&&gi(Eo)),t===s&&("leading"===r.substr(vi,7).toLowerCase()?(t=r.substr(vi,7),vi+=7):(t=s,0===Li&&gi(go)),t===s&&("trailing"===r.substr(vi,8).toLowerCase()?(t=r.substr(vi,8),vi+=8):(t=s,0===Li&&gi(_o)))),t}())===s&&(e=null),e!==s&&Bp()!==s?((n=tl())===s&&(n=null),n!==s&&Bp()!==s&&Sf()!==s?(di=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(vi=t,t=s)):(vi=t,t=s),t}function ql(){var t,e,n,o;return t=vi,"trim"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(To)),e!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s?((n=Bl())===s&&(n=null),n!==s&&Bp()!==s&&(o=tl())!==s&&Bp()!==s&&Hp()!==s?(di=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...wb()}}(n,o)):(vi=t,t=s)):(vi=t,t=s),t}function Xl(){var t,e,n,o,u,a,i,c;return(t=Wl())===s&&(t=ql())===s&&(t=vi,"convert"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(So)),e!==s&&Bp()!==s&&(n=Fp())!==s&&Bp()!==s&&(o=function(){var r,t,e,n,o,u;return r=vi,(t=tb())!==s&&Bp()!==s&&$p()!==s&&Bp()!==s?((e=pb())===s&&(e=vb()),e!==s&&Bp()!==s&&(n=ec())!==s&&Bp()!==s&&(o=Ll())!==s?(di=r,r=t=function(r,t,e,n){const{dataType:o,length:s}=t;let u=o;return void 0!==s&&(u=`${u}(${s})`),{type:"expr_list",value:[r,{type:"origin",value:u,suffix:{prefix:e,...n}}]}}(t,e,n,o)):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=tb())!==s&&Bp()!==s&&$p()!==s&&Bp()!==s?((e=Kl())===s&&(e=lb()),e!==s?(di=r,r=t={type:"expr_list",value:[t,{type:"datatype",..."string"==typeof(u=e)?{dataType:u}:u}]}):(vi=r,r=s)):(vi=r,r=s),r===s&&(r=vi,(t=nl())!==s&&Bp()!==s&&$f()!==s&&Bp()!==s&&(e=jl())!==s?(di=r,r=t=function(r,t){return r.suffix="USING "+t.toUpperCase(),{type:"expr_list",value:[r]}}(t,e)):(vi=r,r=s))),r}())!==s&&(u=Bp())!==s&&Hp()!==s?(di=t,t=e={type:"function",name:{name:[{type:"origin",value:"convert"}]},args:o,...wb()}):(vi=t,t=s),t===s&&(t=vi,(e=function(){var t;(t=Vl())===s&&(t=Ep())===s&&(t=mp())===s&&(t=function(){var t,e,n,o;t=vi,"session_user"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(pa));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SESSION_USER"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"system_user"===r.substr(vi,11).toLowerCase()?(e=r.substr(vi,11),vi+=11):(e=s,0===Li&&gi(ba));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SYSTEM_USER"):(vi=t,t=s)):(vi=t,t=s);return t}());return t}())!==s&&Bp()!==s&&(n=Fp())!==s&&Bp()!==s?((o=Qc())===s&&(o=null),o!==s&&(u=Bp())!==s&&Hp()!==s&&Bp()!==s?((a=Ml())===s&&(a=null),a!==s?(di=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...wb()}}(e,o,a)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=Vl())!==s&&Bp()!==s?((n=Ul())===s&&(n=null),n!==s?(di=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...wb()}):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=vi,(e=ob())!==s?(di=vi,(!yb[(c=e).name[0]&&c.name[0].value.toLowerCase()]?void 0:s)!==s&&(n=Bp())!==s&&Fp()!==s&&(o=Bp())!==s?((u=nl())===s&&(u=null),u!==s&&Bp()!==s&&Hp()!==s&&(a=Bp())!==s?((i=Ml())===s&&(i=null),i!==s?(di=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),(r.name[0]&&"TIMESTAMPDIFF"===r.name[0].value.toUpperCase()||r.name[0]&&"TIMESTAMPADD"===r.name[0].value.toUpperCase())&&t.value&&t.value[0]&&(t.value[0]={type:"origin",value:t.value[0].column}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...wb()}}(e,u,i)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s)):(vi=t,t=s))))),t}function Vl(){var t;return(t=function(){var t,e,n,o;t=vi,"current_date"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(aa));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CURRENT_DATE"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"current_time"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(ca));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CURRENT_TIME"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=Ap()),t}function Kl(){var t;return(t=function(){var t,e,n,o;t=vi,"signed"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi($u));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SIGNED"):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n,o;t=vi,"unsigned"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Gu));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="UNSIGNED"):(vi=t,t=s)):(vi=t,t=s);return t}()),t}function Ql(){var t,e,n,o,u,a,i,c,l;return t=vi,"binary"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(xo)),e===s&&("_binary"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Io))),e===s&&(e=null),e!==s&&Bp()!==s&&(n=Jl())!==s?(o=vi,(u=Bp())!==s&&(a=Fi())!==s?o=u=[u,a]:(vi=o,o=s),o===s&&(o=null),o!==s?(di=t,c=n,l=o,(i=e)&&(c.prefix=i.toLowerCase()),l&&(c.suffix={collate:l[1]}),t=e=c):(vi=t,t=s)):(vi=t,t=s),t===s&&(t=function(){var t,e;t=vi,(e=function(){var t,e,n,o;t=vi,"true"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ds));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={type:"bool",value:!0});(t=e)===s&&(t=vi,(e=function(){var t,e,n,o;t=vi,"false"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ws));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={type:"bool",value:!1}),t=e);return t}())===s&&(t=Zl())===s&&(t=function(){var t,e,n,o,u,a;t=vi,(e=hp())===s&&(e=dp())===s&&(e=Lp())===s&&(e=yp());if(e!==s)if(Bp()!==s){if(n=vi,39===r.charCodeAt(vi)?(o="'",vi++):(o=s,0===Li&&gi(Nt)),o!==s){for(u=[],a=tf();a!==s;)u.push(a),a=tf();u!==s?(39===r.charCodeAt(vi)?(a="'",vi++):(a=s,0===Li&&gi(Nt)),a!==s?n=o=[o,u,a]:(vi=n,n=s)):(vi=n,n=s)}else vi=n,n=s;n!==s?(di=t,e=Do(e,n),t=e):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;if(t===s)if(t=vi,(e=hp())===s&&(e=dp())===s&&(e=Lp())===s&&(e=yp()),e!==s)if(Bp()!==s){if(n=vi,34===r.charCodeAt(vi)?(o='"',vi++):(o=s,0===Li&&gi(hn)),o!==s){for(u=[],a=rf();a!==s;)u.push(a),a=rf();u!==s?(34===r.charCodeAt(vi)?(a='"',vi++):(a=s,0===Li&&gi(hn)),a!==s?n=o=[o,u,a]:(vi=n,n=s)):(vi=n,n=s)}else vi=n,n=s;n!==s?(di=t,e=Do(e,n),t=e):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;return t}()),t}function zl(){var r;return(r=Ql())===s&&(r=nf()),r}function Zl(){var t,e;return t=vi,(e=function(){var t,e,n,o;t=vi,"null"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(bs));e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s);return t}())!==s&&(di=t,e={type:"null",value:null}),t=e}function Jl(){var t,e,n,o,u,a,i,c;if(t=vi,"_binary"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Io)),e===s&&("_latin1"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(jo))),e===s&&(e=null),e!==s)if((n=Bp())!==s)if("x"===r.substr(vi,1).toLowerCase()?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(No)),o!==s){if(u=vi,39===r.charCodeAt(vi)?(a="'",vi++):(a=s,0===Li&&gi(Nt)),a!==s){for(i=[],Ro.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(ko));c!==s;)i.push(c),Ro.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(ko));i!==s?(39===r.charCodeAt(vi)?(c="'",vi++):(c=s,0===Li&&gi(Nt)),c!==s?u=a=[a,i,c]:(vi=u,u=s)):(vi=u,u=s)}else vi=u,u=s;u!==s?(di=t,t=e={type:"hex_string",prefix:e,value:u[1].join("")}):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;if(t===s){if(t=vi,"_binary"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Io)),e===s&&("_latin1"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(jo))),e===s&&(e=null),e!==s)if((n=Bp())!==s)if("b"===r.substr(vi,1).toLowerCase()?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(Oo)),o!==s){if(u=vi,39===r.charCodeAt(vi)?(a="'",vi++):(a=s,0===Li&&gi(Nt)),a!==s){for(i=[],Ro.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(ko));c!==s;)i.push(c),Ro.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(ko));i!==s?(39===r.charCodeAt(vi)?(c="'",vi++):(c=s,0===Li&&gi(Nt)),c!==s?u=a=[a,i,c]:(vi=u,u=s)):(vi=u,u=s)}else vi=u,u=s;u!==s?(di=t,t=e=function(r,t,e){return{type:"bit_string",prefix:r,value:e[1].join("")}}(e,0,u)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;if(t===s){if(t=vi,"_binary"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Io)),e===s&&("_latin1"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(jo))),e===s&&(e=null),e!==s)if((n=Bp())!==s)if("0x"===r.substr(vi,2).toLowerCase()?(o=r.substr(vi,2),vi+=2):(o=s,0===Li&&gi(Uo)),o!==s){for(u=[],Ro.test(r.charAt(vi))?(a=r.charAt(vi),vi++):(a=s,0===Li&&gi(ko));a!==s;)u.push(a),Ro.test(r.charAt(vi))?(a=r.charAt(vi),vi++):(a=s,0===Li&&gi(ko));u!==s?(di=t,t=e=function(r,t,e){return{type:"full_hex_string",prefix:r,value:e.join("")}}(e,0,u)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;if(t===s){if(t=vi,"n"===r.substr(vi,1).toLowerCase()?(e=r.charAt(vi),vi++):(e=s,0===Li&&gi(Mo)),e!==s){if(n=vi,39===r.charCodeAt(vi)?(o="'",vi++):(o=s,0===Li&&gi(Nt)),o!==s){for(u=[],a=tf();a!==s;)u.push(a),a=tf();u!==s?(39===r.charCodeAt(vi)?(a="'",vi++):(a=s,0===Li&&gi(Nt)),a!==s?n=o=[o,u,a]:(vi=n,n=s)):(vi=n,n=s)}else vi=n,n=s;n!==s?(di=t,t=e=function(r,t){return{type:"natural_string",value:t[1].join("")}}(0,n)):(vi=t,t=s)}else vi=t,t=s;if(t===s){if(t=vi,e=vi,39===r.charCodeAt(vi)?(n="'",vi++):(n=s,0===Li&&gi(Nt)),n!==s){for(o=[],u=tf();u!==s;)o.push(u),u=tf();o!==s?(39===r.charCodeAt(vi)?(u="'",vi++):(u=s,0===Li&&gi(Nt)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s)}else vi=e,e=s;if(e!==s&&(di=t,e=function(r){return{type:"single_quote_string",value:r[1].join("")}}(e)),(t=e)===s){if(t=vi,e=vi,34===r.charCodeAt(vi)?(n='"',vi++):(n=s,0===Li&&gi(hn)),n!==s){for(o=[],u=rf();u!==s;)o.push(u),u=rf();o!==s?(34===r.charCodeAt(vi)?(u='"',vi++):(u=s,0===Li&&gi(hn)),u!==s?e=n=[n,o,u]:(vi=e,e=s)):(vi=e,e=s)}else vi=e,e=s;e!==s&&(di=t,e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)),t=e}}}}}return t}function rf(){var t;return Po.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi($o)),t===s&&(t=ef())===s&&(Go.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(Fo))),t}function tf(){var t;return Ho.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(Yo)),t===s&&(t=ef()),t}function ef(){var t,e,n,o,u,a,i,c,l,f;return t=vi,"\\'"===r.substr(vi,2)?(e="\\'",vi+=2):(e=s,0===Li&&gi(Wo)),e!==s&&(di=t,e="\\'"),(t=e)===s&&(t=vi,'\\"'===r.substr(vi,2)?(e='\\"',vi+=2):(e=s,0===Li&&gi(Bo)),e!==s&&(di=t,e='\\"'),(t=e)===s&&(t=vi,"\\\\"===r.substr(vi,2)?(e="\\\\",vi+=2):(e=s,0===Li&&gi(qo)),e!==s&&(di=t,e="\\\\"),(t=e)===s&&(t=vi,"\\/"===r.substr(vi,2)?(e="\\/",vi+=2):(e=s,0===Li&&gi(Xo)),e!==s&&(di=t,e="\\/"),(t=e)===s&&(t=vi,"\\b"===r.substr(vi,2)?(e="\\b",vi+=2):(e=s,0===Li&&gi(Vo)),e!==s&&(di=t,e="\b"),(t=e)===s&&(t=vi,"\\f"===r.substr(vi,2)?(e="\\f",vi+=2):(e=s,0===Li&&gi(Ko)),e!==s&&(di=t,e="\f"),(t=e)===s&&(t=vi,"\\n"===r.substr(vi,2)?(e="\\n",vi+=2):(e=s,0===Li&&gi(Qo)),e!==s&&(di=t,e="\n"),(t=e)===s&&(t=vi,"\\r"===r.substr(vi,2)?(e="\\r",vi+=2):(e=s,0===Li&&gi(zo)),e!==s&&(di=t,e="\r"),(t=e)===s&&(t=vi,"\\t"===r.substr(vi,2)?(e="\\t",vi+=2):(e=s,0===Li&&gi(Zo)),e!==s&&(di=t,e="\t"),(t=e)===s&&(t=vi,"\\u"===r.substr(vi,2)?(e="\\u",vi+=2):(e=s,0===Li&&gi(Jo)),e!==s&&(n=lf())!==s&&(o=lf())!==s&&(u=lf())!==s&&(a=lf())!==s?(di=t,i=n,c=o,l=u,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(vi=t,t=s),t===s&&(t=vi,92===r.charCodeAt(vi)?(e="\\",vi++):(e=s,0===Li&&gi(rs)),e!==s&&(di=t,e="\\"),(t=e)===s&&(t=vi,"''"===r.substr(vi,2)?(e="''",vi+=2):(e=s,0===Li&&gi(ts)),e!==s&&(di=t,e="''"),(t=e)===s&&(t=vi,'""'===r.substr(vi,2)?(e='""',vi+=2):(e=s,0===Li&&gi(es)),e!==s&&(di=t,e='""'),(t=e)===s&&(t=vi,"``"===r.substr(vi,2)?(e="``",vi+=2):(e=s,0===Li&&gi(ns)),e!==s&&(di=t,e="``"),t=e))))))))))))),t}function nf(){var r,t,e;return r=vi,(t=function(){var r,t,e,n;r=vi,(t=of())!==s&&(e=sf())!==s&&(n=uf())!==s?(di=r,r=t={type:"bigint",value:t+e+n}):(vi=r,r=s);r===s&&(r=vi,(t=of())!==s&&(e=sf())!==s?(di=r,t=function(r,t){const e=r+t;return mb(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(vi=r,r=s),r===s&&(r=vi,(t=of())!==s&&(e=uf())!==s?(di=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(vi=r,r=s),r===s&&(r=vi,(t=of())!==s&&(di=r,t=function(r){return mb(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==s&&(di=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function of(){var t,e,n;return(t=af())===s&&(t=cf())===s&&(t=vi,45===r.charCodeAt(vi)?(e="-",vi++):(e=s,0===Li&&gi(tn)),e===s&&(43===r.charCodeAt(vi)?(e="+",vi++):(e=s,0===Li&&gi(rn))),e!==s&&(n=af())!==s?(di=t,t=e=e+n):(vi=t,t=s),t===s&&(t=vi,45===r.charCodeAt(vi)?(e="-",vi++):(e=s,0===Li&&gi(tn)),e===s&&(43===r.charCodeAt(vi)?(e="+",vi++):(e=s,0===Li&&gi(rn))),e!==s&&(n=cf())!==s?(di=t,t=e=function(r,t){return r+t}(e,n)):(vi=t,t=s))),t}function sf(){var t,e,n,o;return t=vi,46===r.charCodeAt(vi)?(e=".",vi++):(e=s,0===Li&&gi(us)),e!==s?((n=af())===s&&(n=null),n!==s?(di=t,t=e=(o=n)?"."+o:""):(vi=t,t=s)):(vi=t,t=s),t}function uf(){var t,e,n;return t=vi,(e=function(){var t,e,n;t=vi,cs.test(r.charAt(vi))?(e=r.charAt(vi),vi++):(e=s,0===Li&&gi(ls));e!==s?(fs.test(r.charAt(vi))?(n=r.charAt(vi),vi++):(n=s,0===Li&&gi(ps)),n===s&&(n=null),n!==s?(di=t,t=e=e+(null!==(o=n)?o:"")):(vi=t,t=s)):(vi=t,t=s);var o;return t}())!==s&&(n=af())!==s?(di=t,t=e=e+n):(vi=t,t=s),t}function af(){var r,t,e;if(r=vi,t=[],(e=cf())!==s)for(;e!==s;)t.push(e),e=cf();else t=s;return t!==s&&(di=r,t=t.join("")),r=t}function cf(){var t;return Qr.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(zr)),t}function lf(){var t;return as.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(is)),t}function ff(){var t,e,n,o;return t=vi,"default"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(O)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function pf(){var t,e,n,o;return t=vi,"to"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(ys)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function bf(){var t,e,n,o;return t=vi,"show"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(hs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function vf(){var t,e,n,o;return t=vi,"drop"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Mr)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DROP"):(vi=t,t=s)):(vi=t,t=s),t}function df(){var t,e,n,o;return t=vi,"alter"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ms)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function yf(){var t,e,n,o;return t=vi,"select"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Cs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function wf(){var t,e,n,o;return t=vi,"update"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(As)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function hf(){var t,e,n,o;return t=vi,"create"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Es)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Lf(){var t,e,n,o;return t=vi,"temporary"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(gs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function mf(){var t,e,n,o;return t=vi,"delete"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(_s)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Cf(){var t,e,n,o;return t=vi,"insert"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Ts)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Af(){var t,e,n,o;return t=vi,"replace"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(xs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Ef(){var t,e,n,o;return t=vi,"rename"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(js)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function gf(){var t,e,n,o;return t=vi,"ignore"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Ns)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function _f(){var t,e,n,o;return t=vi,"partition"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(ks)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="PARTITION"):(vi=t,t=s)):(vi=t,t=s),t}function Tf(){var t,e,n,o;return t=vi,"into"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Os)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Sf(){var t,e,n,o;return t=vi,"from"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Us)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function xf(){var t,e,n,o;return t=vi,"set"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(pt)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SET"):(vi=t,t=s)):(vi=t,t=s),t}function If(){var t,e,n,o;return t=vi,"as"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(h)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function jf(){var t,e,n,o;return t=vi,"table"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Ms)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TABLE"):(vi=t,t=s)):(vi=t,t=s),t}function Nf(){var t,e,n,o;return t=vi,"trigger"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Ds)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TRIGGER"):(vi=t,t=s)):(vi=t,t=s),t}function Rf(){var t,e,n,o;return t=vi,"tables"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Ps)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TABLES"):(vi=t,t=s)):(vi=t,t=s),t}function kf(){var t,e,n,o;return t=vi,"database"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi($s)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DATABASE"):(vi=t,t=s)):(vi=t,t=s),t}function Of(){var t,e,n,o;return t=vi,"schema"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Gs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SCHEMA"):(vi=t,t=s)):(vi=t,t=s),t}function Uf(){var t,e,n,o;return t=vi,"on"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(Fs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Mf(){var t,e,n,o;return t=vi,"join"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Xs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Df(){var t,e,n,o;return t=vi,"outer"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Vs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Pf(){var t,e,n,o;return t=vi,"values"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Zs)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function $f(){var t,e,n,o;return t=vi,"using"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(Js)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Gf(){var t,e,n,o;return t=vi,"with"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(v)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Ff(){var t,e,n,o;return t=vi,"by"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(d)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function Hf(){var t,e,n,o;return t=vi,"asc"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(uu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ASC"):(vi=t,t=s)):(vi=t,t=s),t}function Yf(){var t,e,n,o;return t=vi,"desc"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(au)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DESC"):(vi=t,t=s)):(vi=t,t=s),t}function Wf(){var t,e,n,o;return t=vi,"all"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(cu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ALL"):(vi=t,t=s)):(vi=t,t=s),t}function Bf(){var t,e,n,o;return t=vi,"distinct"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(lu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DISTINCT"):(vi=t,t=s)):(vi=t,t=s),t}function qf(){var t,e,n,o;return t=vi,"between"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(fu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="BETWEEN"):(vi=t,t=s)):(vi=t,t=s),t}function Xf(){var t,e,n,o;return t=vi,"in"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(we)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="IN"):(vi=t,t=s)):(vi=t,t=s),t}function Vf(){var t,e,n,o;return t=vi,"is"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(pu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="IS"):(vi=t,t=s)):(vi=t,t=s),t}function Kf(){var t,e,n,o;return t=vi,"like"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(bu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="LIKE"):(vi=t,t=s)):(vi=t,t=s),t}function Qf(){var t,e,n,o;return t=vi,"exists"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(yu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="EXISTS"):(vi=t,t=s)):(vi=t,t=s),t}function zf(){var t,e,n,o;return t=vi,"not"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Jr)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="NOT"):(vi=t,t=s)):(vi=t,t=s),t}function Zf(){var t,e,n,o;return t=vi,"and"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(wu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="AND"):(vi=t,t=s)):(vi=t,t=s),t}function Jf(){var t,e,n,o;return t=vi,"or"===r.substr(vi,2).toLowerCase()?(e=r.substr(vi,2),vi+=2):(e=s,0===Li&&gi(hu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="OR"):(vi=t,t=s)):(vi=t,t=s),t}function rp(){var t,e,n,o;return t=vi,"extract"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(_u)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="EXTRACT"):(vi=t,t=s)):(vi=t,t=s),t}function tp(){var t,e,n,o;return t=vi,"case"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Su)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function ep(){var t,e,n,o;return t=vi,"end"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Nu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?t=e=[e,n]:(vi=t,t=s)):(vi=t,t=s),t}function np(){var t,e,n,o;return t=vi,"cast"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Ru)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CAST"):(vi=t,t=s)):(vi=t,t=s),t}function op(){var t,e,n,o;return t=vi,"bit"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Ou)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="BIT"):(vi=t,t=s)):(vi=t,t=s),t}function sp(){var t,e,n,o;return t=vi,"numeric"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Du)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="NUMERIC"):(vi=t,t=s)):(vi=t,t=s),t}function up(){var t,e,n,o;return t=vi,"decimal"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Pu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DECIMAL"):(vi=t,t=s)):(vi=t,t=s),t}function ap(){var t,e,n,o;return t=vi,"int"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Fu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="INT"):(vi=t,t=s)):(vi=t,t=s),t}function ip(){var t,e,n,o;return t=vi,"integer"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Yu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="INTEGER"):(vi=t,t=s)):(vi=t,t=s),t}function cp(){var t,e,n,o;return t=vi,"smallint"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Bu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SMALLINT"):(vi=t,t=s)):(vi=t,t=s),t}function lp(){var t,e,n,o;return t=vi,"mediumint"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(qu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MEDIUMINT"):(vi=t,t=s)):(vi=t,t=s),t}function fp(){var t,e,n,o;return t=vi,"tinyint"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Xu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TINYINT"):(vi=t,t=s)):(vi=t,t=s),t}function pp(){var t,e,n,o;return t=vi,"bigint"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Zu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="BIGINT"):(vi=t,t=s)):(vi=t,t=s),t}function bp(){var t,e,n,o;return t=vi,"float"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(ra)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="FLOAT"):(vi=t,t=s)):(vi=t,t=s),t}function vp(){var t,e,n,o;return t=vi,"double"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(ta)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DOUBLE"):(vi=t,t=s)):(vi=t,t=s),t}function dp(){var t,e,n,o;return t=vi,"date"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(to)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DATE"):(vi=t,t=s)):(vi=t,t=s),t}function yp(){var t,e,n,o;return t=vi,"datetime"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ea)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="DATETIME"):(vi=t,t=s)):(vi=t,t=s),t}function wp(){var t,e,n,o;return t=vi,"rows"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(na)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ROWS"):(vi=t,t=s)):(vi=t,t=s),t}function hp(){var t,e,n,o;return t=vi,"time"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ho)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TIME"):(vi=t,t=s)):(vi=t,t=s),t}function Lp(){var t,e,n,o;return t=vi,"timestamp"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(oa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TIMESTAMP"):(vi=t,t=s)):(vi=t,t=s),t}function mp(){var t,e,n,o;return t=vi,"user"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(sa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="USER"):(vi=t,t=s)):(vi=t,t=s),t}function Cp(){var t,e,n,o;return t=vi,"interval"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ia)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="INTERVAL"):(vi=t,t=s)):(vi=t,t=s),t}function Ap(){var t,e,n,o;return t=vi,"current_timestamp"===r.substr(vi,17).toLowerCase()?(e=r.substr(vi,17),vi+=17):(e=s,0===Li&&gi(la)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CURRENT_TIMESTAMP"):(vi=t,t=s)):(vi=t,t=s),t}function Ep(){var t,e,n,o;return t=vi,"current_user"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(fa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CURRENT_USER"):(vi=t,t=s)):(vi=t,t=s),t}function gp(){var t,e,n,o;return t=vi,"view"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ue)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="VIEW"):(vi=t,t=s)):(vi=t,t=s),t}function _p(){var t;return 64===r.charCodeAt(vi)?(t="@",vi++):(t=s,0===Li&&gi(lr)),t}function Tp(){var t;return(t=function(){var t;return"@@"===r.substr(vi,2)?(t="@@",vi+=2):(t=s,0===Li&&gi(Ta)),t}())===s&&(t=_p())===s&&(t=function(){var t;return 36===r.charCodeAt(vi)?(t="$",vi++):(t=s,0===Li&&gi(Sa)),t}()),t}function Sp(){var t;return":="===r.substr(vi,2)?(t=":=",vi+=2):(t=s,0===Li&&gi(Ia)),t}function xp(){var t;return 61===r.charCodeAt(vi)?(t="=",vi++):(t=s,0===Li&&gi(Ye)),t}function Ip(){var t,e,n,o;return t=vi,"add"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Na)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ADD"):(vi=t,t=s)):(vi=t,t=s),t}function jp(){var t,e,n,o;return t=vi,"column"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Ra)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="COLUMN"):(vi=t,t=s)):(vi=t,t=s),t}function Np(){var t,e,n,o;return t=vi,"index"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(xt)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="INDEX"):(vi=t,t=s)):(vi=t,t=s),t}function Rp(){var t,e,n,o;return t=vi,"key"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(ir)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="KEY"):(vi=t,t=s)):(vi=t,t=s),t}function kp(){var t,e,n,o;return t=vi,"fulltext"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Oa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="FULLTEXT"):(vi=t,t=s)):(vi=t,t=s),t}function Op(){var t,e,n,o;return t=vi,"spatial"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Ua)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="SPATIAL"):(vi=t,t=s)):(vi=t,t=s),t}function Up(){var t,e,n,o;return t=vi,"unique"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(ar)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="UNIQUE"):(vi=t,t=s)):(vi=t,t=s),t}function Mp(){var t,e,n,o;return t=vi,"comment"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Ma)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="COMMENT"):(vi=t,t=s)):(vi=t,t=s),t}function Dp(){var t,e,n,o;return t=vi,"references"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Pa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="REFERENCES"):(vi=t,t=s)):(vi=t,t=s),t}function Pp(){var t;return 46===r.charCodeAt(vi)?(t=".",vi++):(t=s,0===Li&&gi(us)),t}function $p(){var t;return 44===r.charCodeAt(vi)?(t=",",vi++):(t=s,0===Li&&gi(Ba)),t}function Gp(){var t;return 42===r.charCodeAt(vi)?(t="*",vi++):(t=s,0===Li&&gi(en)),t}function Fp(){var t;return 40===r.charCodeAt(vi)?(t="(",vi++):(t=s,0===Li&&gi(de)),t}function Hp(){var t;return 41===r.charCodeAt(vi)?(t=")",vi++):(t=s,0===Li&&gi(ye)),t}function Yp(){var t;return 59===r.charCodeAt(vi)?(t=";",vi++):(t=s,0===Li&&gi(Va)),t}function Wp(){var t;return(t=function(){var t;return"||"===r.substr(vi,2)?(t="||",vi+=2):(t=s,0===Li&&gi(sn)),t}())===s&&(t=function(){var t;return"&&"===r.substr(vi,2)?(t="&&",vi+=2):(t=s,0===Li&&gi(Ka)),t}())===s&&(t=function(){var t,e,n,o;return t=vi,"xor"===r.substr(vi,3).toLowerCase()?(e=r.substr(vi,3),vi+=3):(e=s,0===Li&&gi(Qa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="XOR"):(vi=t,t=s)):(vi=t,t=s),t}()),t}function Bp(){var r,t;for(r=[],(t=Qp())===s&&(t=Xp());t!==s;)r.push(t),(t=Qp())===s&&(t=Xp());return r}function qp(){var r,t;if(r=[],(t=Qp())===s&&(t=Xp()),t!==s)for(;t!==s;)r.push(t),(t=Qp())===s&&(t=Xp());else r=s;return r}function Xp(){var t;return(t=function(){var t,e,n,o,u,a;t=vi,"/*"===r.substr(vi,2)?(e="/*",vi+=2):(e=s,0===Li&&gi(za));if(e!==s){for(n=[],o=vi,u=vi,Li++,"*/"===r.substr(vi,2)?(a="*/",vi+=2):(a=s,0===Li&&gi(Za)),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);o!==s;)n.push(o),o=vi,u=vi,Li++,"*/"===r.substr(vi,2)?(a="*/",vi+=2):(a=s,0===Li&&gi(Za)),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);n!==s?("*/"===r.substr(vi,2)?(o="*/",vi+=2):(o=s,0===Li&&gi(Za)),o!==s?t=e=[e,n,o]:(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=vi,"--"===r.substr(vi,2)?(e="--",vi+=2):(e=s,0===Li&&gi(Ja));if(e!==s){for(n=[],o=vi,u=vi,Li++,a=zp(),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);o!==s;)n.push(o),o=vi,u=vi,Li++,a=zp(),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);n!==s?t=e=[e,n]:(vi=t,t=s)}else vi=t,t=s;return t}())===s&&(t=function(){var t,e,n,o,u,a;t=vi,35===r.charCodeAt(vi)?(e="#",vi++):(e=s,0===Li&&gi(ri));if(e!==s){for(n=[],o=vi,u=vi,Li++,a=zp(),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);o!==s;)n.push(o),o=vi,u=vi,Li++,a=zp(),Li--,a===s?u=void 0:(vi=u,u=s),u!==s&&(a=Kp())!==s?o=u=[u,a]:(vi=o,o=s);n!==s?t=e=[e,n]:(vi=t,t=s)}else vi=t,t=s;return t}()),t}function Vp(){var r,t,e,n,o,u,a;return r=vi,(t=Mp())!==s&&Bp()!==s?((e=xp())===s&&(e=null),e!==s&&Bp()!==s&&(n=Jl())!==s?(di=r,u=e,a=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:a}):(vi=r,r=s)):(vi=r,r=s),r}function Kp(){var t;return r.length>vi?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(ti)),t}function Qp(){var t;return ei.test(r.charAt(vi))?(t=r.charAt(vi),vi++):(t=s,0===Li&&gi(ni)),t}function zp(){var t,e;if((t=function(){var t,e;t=vi,Li++,r.length>vi?(e=r.charAt(vi),vi++):(e=s,0===Li&&gi(ti));Li--,e===s?t=void 0:(vi=t,t=s);return t}())===s)if(t=[],os.test(r.charAt(vi))?(e=r.charAt(vi),vi++):(e=s,0===Li&&gi(ss)),e!==s)for(;e!==s;)t.push(e),os.test(r.charAt(vi))?(e=r.charAt(vi),vi++):(e=s,0===Li&&gi(ss));else t=s;return t}function Zp(){var t,e;return t=vi,di=vi,Tb=[],(!0?void 0:s)!==s&&Bp()!==s?((e=Jp())===s&&(e=function(){var t,e;t=vi,function(){var t;return"return"===r.substr(vi,6).toLowerCase()?(t=r.substr(vi,6),vi+=6):(t=s,0===Li&&gi(xa)),t}()!==s&&Bp()!==s&&(e=rb())!==s?(di=t,t={type:"return",expr:e}):(vi=t,t=s);return t}()),e!==s?(di=t,t={stmt:e,vars:Tb}):(vi=t,t=s)):(vi=t,t=s),t}function Jp(){var r,t,e,n;return r=vi,(t=ib())===s&&(t=cb()),t!==s&&Bp()!==s?((e=Sp())===s&&(e=xp()),e!==s&&Bp()!==s&&(n=rb())!==s?(di=r,r=t=oi(t,e,n)):(vi=r,r=s)):(vi=r,r=s),r}function rb(){var t;return(t=fc())===s&&(t=function(){var r,t,e,n,o;r=vi,(t=ib())!==s&&Bp()!==s&&(e=Nc())!==s&&Bp()!==s&&(n=ib())!==s&&Bp()!==s&&(o=kc())!==s?(di=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(vi=r,r=s);return r}())===s&&(t=tb())===s&&(t=function(){var t,e;t=vi,function(){var t;return 91===r.charCodeAt(vi)?(t="[",vi++):(t=s,0===Li&&gi(qa)),t}()!==s&&Bp()!==s&&(e=ab())!==s&&Bp()!==s&&function(){var t;return 93===r.charCodeAt(vi)?(t="]",vi++):(t=s,0===Li&&gi(Xa)),t}()!==s?(di=t,t={type:"array",value:e}):(vi=t,t=s);return t}()),t}function tb(){var r,t,e,n,o,u,a,i;if(r=vi,(t=eb())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=pl())!==s&&(a=Bp())!==s&&(i=eb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=pl())!==s&&(a=Bp())!==s&&(i=eb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=Be(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function eb(){var r,t,e,n,o,u,a,i;if(r=vi,(t=nb())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=vl())!==s&&(a=Bp())!==s&&(i=nb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=vl())!==s&&(a=Bp())!==s&&(i=nb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=Be(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function nb(){var r,t,e;return(r=sb())===s&&(r=zl())===s&&(r=ib())===s&&(r=yl())===s&&(r=ub())===s&&(r=Ol())===s&&(r=vi,Fp()!==s&&Bp()!==s&&(t=tb())!==s&&Bp()!==s&&Hp()!==s?(di=r,(e=t).parentheses=!0,r=e):(vi=r,r=s)),r}function ob(){var r,t,e,n,o,u,a;return r=vi,(t=hl())===s&&(t=Tl()),t!==s?(e=vi,(n=Bp())!==s&&(o=Pp())!==s&&(u=Bp())!==s?((a=hl())===s&&(a=Tl()),a!==s?e=n=[n,o,u,a]:(vi=e,e=s)):(vi=e,e=s),e===s&&(e=null),e!==s?(di=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(vi=r,r=s)):(vi=r,r=s),r}function sb(){var r,t,e;return r=vi,(t=ob())!==s&&Bp()!==s&&Fp()!==s&&Bp()!==s?((e=ab())===s&&(e=null),e!==s&&Bp()!==s&&Hp()!==s?(di=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...wb()}):(vi=r,r=s)):(vi=r,r=s),r}function ub(){var r,t;return r=vi,(t=ob())!==s&&(di=r,t={type:"function",name:t,args:null,...wb()}),r=t}function ab(){var r,t,e,n,o,u,a,i;if(r=vi,(t=nb())!==s){for(e=[],n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=nb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);n!==s;)e.push(n),n=vi,(o=Bp())!==s&&(u=$p())!==s&&(a=Bp())!==s&&(i=nb())!==s?n=o=[o,u,a,i]:(vi=n,n=s);e!==s?(di=r,r=t=sr(t,e)):(vi=r,r=s)}else vi=r,r=s;return r}function ib(){var r,t,e,n,o;return r=vi,(t=Tp())!==s&&(e=cb())!==s?(di=r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(vi=r,r=s),r}function cb(){var t,e,n,o,u;return t=vi,(e=jl())!==s&&(n=function(){var t,e,n,o,u;t=vi,e=[],n=vi,46===r.charCodeAt(vi)?(o=".",vi++):(o=s,0===Li&&gi(us));o!==s&&(u=jl())!==s?n=o=[o,u]:(vi=n,n=s);for(;n!==s;)e.push(n),n=vi,46===r.charCodeAt(vi)?(o=".",vi++):(o=s,0===Li&&gi(us)),o!==s&&(u=jl())!==s?n=o=[o,u]:(vi=n,n=s);e!==s&&(di=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==s?(di=t,o=e,u=n,Tb.push(o),t=e={type:"var",name:o,members:u,prefix:null}):(vi=t,t=s),t===s&&(t=vi,(e=nf())!==s&&(di=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function lb(){var t;return(t=pb())===s&&(t=function(){var t,e,n,o,u,a,i,c,l,f,p,b;t=vi,(e=sp())===s&&(e=up())===s&&(e=ap())===s&&(e=ip())===s&&(e=cp())===s&&(e=lp())===s&&(e=fp())===s&&(e=pp())===s&&(e=bp())===s&&(e=vp())===s&&(e=op());if(e!==s)if((n=Bp())!==s)if((o=Fp())!==s)if((u=Bp())!==s){if(a=[],Qr.test(r.charAt(vi))?(i=r.charAt(vi),vi++):(i=s,0===Li&&gi(zr)),i!==s)for(;i!==s;)a.push(i),Qr.test(r.charAt(vi))?(i=r.charAt(vi),vi++):(i=s,0===Li&&gi(zr));else a=s;if(a!==s)if((i=Bp())!==s){if(c=vi,(l=$p())!==s)if((f=Bp())!==s){if(p=[],Qr.test(r.charAt(vi))?(b=r.charAt(vi),vi++):(b=s,0===Li&&gi(zr)),b!==s)for(;b!==s;)p.push(b),Qr.test(r.charAt(vi))?(b=r.charAt(vi),vi++):(b=s,0===Li&&gi(zr));else p=s;p!==s?c=l=[l,f,p]:(vi=c,c=s)}else vi=c,c=s;else vi=c,c=s;c===s&&(c=null),c!==s&&(l=Bp())!==s&&(f=Hp())!==s&&(p=Bp())!==s?((b=bb())===s&&(b=null),b!==s?(di=t,v=c,d=b,e={dataType:e,length:parseInt(a.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:d},t=e):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s}else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;else vi=t,t=s;var v,d;if(t===s){if(t=vi,(e=sp())===s&&(e=up())===s&&(e=ap())===s&&(e=ip())===s&&(e=cp())===s&&(e=lp())===s&&(e=fp())===s&&(e=pp())===s&&(e=bp())===s&&(e=vp())===s&&(e=op()),e!==s){if(n=[],Qr.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(zr)),o!==s)for(;o!==s;)n.push(o),Qr.test(r.charAt(vi))?(o=r.charAt(vi),vi++):(o=s,0===Li&&gi(zr));else n=s;n!==s&&(o=Bp())!==s?((u=bb())===s&&(u=null),u!==s?(di=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,u),t=e):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;t===s&&(t=vi,(e=sp())===s&&(e=up())===s&&(e=ap())===s&&(e=ip())===s&&(e=cp())===s&&(e=lp())===s&&(e=fp())===s&&(e=pp())===s&&(e=bp())===s&&(e=vp())===s&&(e=op()),e!==s&&(n=Bp())!==s?((o=bb())===s&&(o=null),o!==s&&(u=Bp())!==s?(di=t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(vi=t,t=s)):(vi=t,t=s))}return t}())===s&&(t=vb())===s&&(t=function(){var t,e;t=vi,(e=function(){var t,e,n,o;return t=vi,"json"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Wu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="JSON"):(vi=t,t=s)):(vi=t,t=s),t}())!==s&&(di=t,e={dataType:e});return t=e}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;return t=vi,"tinytext"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Vu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TINYTEXT"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"text"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Ku)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="TEXT"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"mediumtext"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Qu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MEDIUMTEXT"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"longtext"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(zu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="LONGTEXT"):(vi=t,t=s)):(vi=t,t=s),t}());e!==s?((n=fb())===s&&(n=null),n!==s?(di=t,e=li(e,n),t=e):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;return t=vi,"enum"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Ju)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ENUM"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=xf());e!==s&&Bp()!==s&&(n=Kc())!==s?(di=t,o=e,(u=n).parentheses=!0,t=e={dataType:o,expr:u}):(vi=t,t=s);var o,u;return t}())===s&&(t=function(){var t,e;t=vi,(e=function(){var t,e,n,o;return t=vi,"uuid"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ua)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="UUID"):(vi=t,t=s)):(vi=t,t=s),t}())!==s&&(di=t,e={dataType:e});return t=e}())===s&&(t=function(){var t,e;t=vi,"boolean"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(si));e!==s&&(di=t,e={dataType:"BOOLEAN"});return t=e}())===s&&(t=function(){var t,e,n;t=vi,(e=function(){var t,e,n,o;return t=vi,"binary"===r.substr(vi,6).toLowerCase()?(e=r.substr(vi,6),vi+=6):(e=s,0===Li&&gi(Gt)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="BINARY"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"varbinary"===r.substr(vi,9).toLowerCase()?(e=r.substr(vi,9),vi+=9):(e=s,0===Li&&gi(ku)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="VARBINARY"):(vi=t,t=s)):(vi=t,t=s),t}());e!==s&&Bp()!==s?((n=fb())===s&&(n=null),n!==s?(di=t,e=li(e,n),t=e):(vi=t,t=s)):(vi=t,t=s);return t}())===s&&(t=function(){var t,e;t=vi,"blob"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(ui));e===s&&("tinyblob"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ai)),e===s&&("mediumblob"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(ii)),e===s&&("longblob"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ci)))));e!==s&&(di=t,e={dataType:e.toUpperCase()});return t=e}())===s&&(t=function(){var t,e;t=vi,(e=function(){var t,e,n,o;return t=vi,"geometry"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(ha)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="GEOMETRY"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"point"===r.substr(vi,5).toLowerCase()?(e=r.substr(vi,5),vi+=5):(e=s,0===Li&&gi(La)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="POINT"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"linestring"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(ma)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="LINESTRING"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"polygon"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Ca)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="POLYGON"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"multipoint"===r.substr(vi,10).toLowerCase()?(e=r.substr(vi,10),vi+=10):(e=s,0===Li&&gi(Aa)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MULTIPOINT"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"multilinestring"===r.substr(vi,15).toLowerCase()?(e=r.substr(vi,15),vi+=15):(e=s,0===Li&&gi(Ea)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MULTILINESTRING"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"multipolygon"===r.substr(vi,12).toLowerCase()?(e=r.substr(vi,12),vi+=12):(e=s,0===Li&&gi(ga)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="MULTIPOLYGON"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"geometrycollection"===r.substr(vi,18).toLowerCase()?(e=r.substr(vi,18),vi+=18):(e=s,0===Li&&gi(_a)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="GEOMETRYCOLLECTION"):(vi=t,t=s)):(vi=t,t=s),t}());e!==s&&(di=t,e={dataType:e});return t=e}()),t}function fb(){var t,e,n,o,u;if(t=vi,Fp()!==s)if(Bp()!==s){if(e=[],Qr.test(r.charAt(vi))?(n=r.charAt(vi),vi++):(n=s,0===Li&&gi(zr)),n!==s)for(;n!==s;)e.push(n),Qr.test(r.charAt(vi))?(n=r.charAt(vi),vi++):(n=s,0===Li&&gi(zr));else e=s;e!==s&&(n=Bp())!==s&&Hp()!==s&&Bp()!==s?((o=bb())===s&&(o=null),o!==s?(di=t,u=o,t={length:parseInt(e.join(""),10),parentheses:!0,suffix:u}):(vi=t,t=s)):(vi=t,t=s)}else vi=t,t=s;else vi=t,t=s;return t}function pb(){var t,e,n,o,u,a,i,c,l,f,p;if(t=vi,(e=function(){var t,e,n,o;return t=vi,"char"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Uu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="CHAR"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(e=function(){var t,e,n,o;return t=vi,"varchar"===r.substr(vi,7).toLowerCase()?(e=r.substr(vi,7),vi+=7):(e=s,0===Li&&gi(Mu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="VARCHAR"):(vi=t,t=s)):(vi=t,t=s),t}()),e!==s){if(n=vi,(o=Bp())!==s)if((u=Fp())!==s)if((a=Bp())!==s){if(i=[],Qr.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(zr)),c!==s)for(;c!==s;)i.push(c),Qr.test(r.charAt(vi))?(c=r.charAt(vi),vi++):(c=s,0===Li&&gi(zr));else i=s;i!==s&&(c=Bp())!==s&&(l=Hp())!==s&&(f=Bp())!==s?("array"===r.substr(vi,5).toLowerCase()?(p=r.substr(vi,5),vi+=5):(p=s,0===Li&&gi(fi)),p===s&&(p=null),p!==s?n=o=[o,u,a,i,c,l,f,p]:(vi=n,n=s)):(vi=n,n=s)}else vi=n,n=s;else vi=n,n=s;else vi=n,n=s;n===s&&(n=null),n!==s?(di=t,t=e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3].join(""),10),e.parentheses=!0,e.suffix=t[7]&&["ARRAY"]),e}(e,n)):(vi=t,t=s)}else vi=t,t=s;return t}function bb(){var t,e,n;return t=vi,(e=Kl())===s&&(e=null),e!==s&&Bp()!==s?((n=function(){var t,e,n,o;return t=vi,"zerofill"===r.substr(vi,8).toLowerCase()?(e=r.substr(vi,8),vi+=8):(e=s,0===Li&&gi(Hu)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="ZEROFILL"):(vi=t,t=s)):(vi=t,t=s),t}())===s&&(n=null),n!==s?(di=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(vi=t,t=s)):(vi=t,t=s),t}function vb(){var t,e,n,o,u,a,i,c,l,f,p;return t=vi,(e=dp())===s&&(e=yp())===s&&(e=hp())===s&&(e=Lp())===s&&(e=function(){var t,e,n,o;return t=vi,"year"===r.substr(vi,4).toLowerCase()?(e=r.substr(vi,4),vi+=4):(e=s,0===Li&&gi(Co)),e!==s?(n=vi,Li++,o=Nl(),Li--,o===s?n=void 0:(vi=n,n=s),n!==s?(di=t,t=e="YEAR"):(vi=t,t=s)):(vi=t,t=s),t}()),e!==s?(n=vi,(o=Bp())!==s&&(u=Fp())!==s&&(a=Bp())!==s?(pi.test(r.charAt(vi))?(i=r.charAt(vi),vi++):(i=s,0===Li&&gi(bi)),i!==s&&(c=Bp())!==s&&(l=Hp())!==s&&(f=Bp())!==s?((p=bb())===s&&(p=null),p!==s?n=o=[o,u,a,i,c,l,f,p]:(vi=n,n=s)):(vi=n,n=s)):(vi=n,n=s),n===s&&(n=null),n!==s?(di=t,t=e=function(r,t){const e={dataType:r};return t&&(e.length=parseInt(t[3],10),e.parentheses=!0,e.suffix=t[7]),e}(e,n)):(vi=t,t=s)):(vi=t,t=s),t}const db={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,BOOLEAN:!0,CALL:!0,CASCADE:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CROSS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FOR:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LATERAL:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NATURAL:!0,MINUS:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,READ:!0,RETURNING:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,STATUS:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0},yb={avg:!0,sum:!0,count:!0,max:!0,min:!0,group_concat:!0,std:!0,variance:!0,current_date:!0,current_time:!0,current_timestamp:!0,current_user:!0,user:!0,session_user:!0,system_user:!0};function wb(){return t.includeLocations?{loc:Ei(di,vi)}:{}}function hb(r,t){return{type:"unary_expr",operator:r,expr:t}}function Lb(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function mb(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Cb(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function Ab(r,t){let e=r;for(let r=0;r<t.length;r++)e=Lb(t[r][1],e,t[r][3]);return e}function Eb(r){const t=Ib[r];return t||(r||null)}function gb(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Eb(r[1])),t.add(r.join("::"))}return Array.from(t)}function _b(r){const t=gb(r);r.clear(),t.forEach(t=>r.add(t))}let Tb=[];const Sb=new Set,xb=new Set,Ib={};if((e=a())!==s&&vi===r.length)return e;throw e!==s&&vi<r.length&&gi({type:"end"}),_i(hi,wi<r.length?r.charAt(wi):null,wi<r.length?Ei(wi,wi+1):Ei(wi,wi))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return I})),e.d(n,"columnOrderListToSQL",(function(){return j})),e.d(n,"commonKeywordArgsToSQL",(function(){return x})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return m})),e.d(n,"commentToSQL",(function(){return _})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return E})),e.d(n,"DEFAULT_OPT",(function(){return s})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return L})),e.d(n,"columnIdentifierToSql",(function(){return d})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return y})),e.d(n,"onPartitionsToSQL",(function(){return A})),e.d(n,"replaceParams",(function(){return C})),e.d(n,"returningToSQL",(function(){return S})),e.d(n,"hasVal",(function(){return h})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return w})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return T}));var s={database:"mariadb",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return u}function b(r){u=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function y(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function w(r){if(r)return r.toUpperCase()}function h(r){return r}function L(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,s=r.suffix,u=r.value,a="object"===o(r)?u:r;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'");break;case"unicode_string":a="U&'".concat(u,"'")}var i=[];return t&&i.push(w(t)),i.push(a),s&&("string"==typeof s&&i.push(s),"object"===o(s)&&(s.collate?i.push(it(s.collate)):i.push(L(s)))),a=i.join(" "),n?"(".concat(a,")"):a}}function m(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():L(n)].filter(h)}function C(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var s=t[n];if("object"!==o(s)||"param"!==s.type)return r(s,e);if(void 0===e[s.value])throw new Error("no value for parameter :".concat(s.value," found"));return t[n]=c(e[s.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function A(r){var t=r.type,e=r.partitions;return[w(t),"(".concat(e.map((function(r){if("range"!==r.type)return L(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(L(t)," ").concat(w(n)," ").concat(L(e))})).join(", "),")")].join(" ")}function E(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,s=r.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),s&&s.length&&(u+=" ".concat(s.join(" "))),"".concat(t).concat(u)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=w(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var s=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(h).join(" ")})).join(", ");return n?"".concat(o,"<").concat(s,">"):"".concat(o," ").concat(s)}}function _(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(L(o)),t.join(" ")}}function T(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[w(t)];if(e){var o=e.keyword,s=e.columns;n.push(w(o),s.map(dt).join(", "))}return n.join(" ")})).join(" OR ")}function S(r){return r?["RETURNING",r.columns.map(At).filter(h).join(", ")].join(" "):""}function x(r){return r?[w(r.keyword),w(r.args)]:[]}function I(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,s=r.parentheses,u=w(e);return s&&(u+="(".concat(L(n),", ").concat(L(o),")")),u}}function j(r){if(r)return r.map(Lt).filter(h).join(", ")}function N(r){return function(r){if(Array.isArray(r))return R(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return R(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?R(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function k(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),w(e)]}function O(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),s=[];switch(s.push(o),o){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(L(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,N(k(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(_(r));break;case"DATA_COMPRESSION":s.push(n,w(e.value),A(e.on));break;default:s.push(n,L(e))}return s.filter(h).join(" ")}}function U(r){return r?r.map(O):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,s=r.definition,u=r.on,a=r.with,i=[];if(i.push.apply(i,N(k(e))),s&&s.length){var c="CHECK"===w(t)?"(".concat(st(s[0]),")"):"(".concat(s.map((function(r){return st(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),u&&i.push("ON [".concat(u,"]")),i}function D(r){var t=r.operator||r.op,e=st(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},s=[Array.isArray(r.left)?r.left.map(st).join(", "):st(r.left),t,e,w(o.type),st(o.value)].filter(h).join(" ");return[r.parentheses?"(".concat(s,")"):s].join(" ")}function P(r){return function(r){if(Array.isArray(r))return $(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return $(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?$(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function G(r){return r?[r.prefix.map(L).join(" "),st(r.value),r.suffix.map(L).join(" ")]:[]}function F(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(G(n)),P(G(e))).filter(h).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",st(e[0])):i("LIMIT",e.map(st).join("".concat("offset"===t?" ":"").concat(w(t)," ")))}(r):"";var t,e,n}function H(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(dt).join(", "),")"):"";return"".concat("default"===t.type?y(t.value):L(t)).concat(o," AS (").concat(st(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function Y(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=w(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?y(e):st(e))}return n.filter(h).join(" ")}}function W(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,s=r.for,u=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,d=r.into,y=void 0===d?{}:d,m=r.isolation,C=r.limit,A=r.options,E=r.orderby,g=r.parentheses_symbol,_=r.qualify,T=r.top,S=r.window,x=r.with,I=r.where,j=[H(x),"SELECT",w(t)];Array.isArray(A)&&j.push(A.join(" ")),j.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[w(t)];return e&&n.push("(".concat(e.map(st).join(", "),")")),n.filter(h).join(" ")}}(o),v(T),gt(e,u));var N=y.position,R="";N&&(R=a("INTO",Y,y)),"column"===N&&j.push(R),j.push(a("FROM",cr,u)),"from"===N&&j.push(R);var k=l||{},O=k.keyword,U=k.expr;j.push(a(O,st,U)),j.push(a("WHERE",st,I)),p&&(j.push(i("GROUP BY",ut(p.columns).join(", "))),j.push(ut(p.modifiers).join(", "))),j.push(a("HAVING",st,b)),j.push(a("QUALIFY",st,_)),j.push(a("WINDOW",st,S)),j.push(at(E,"order by")),j.push(it(n)),j.push(F(C)),m&&j.push(a(m.keyword,L,m.expr)),j.push(w(f)),"end"===N&&j.push(R),j.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[w(r.type),w(e)];return t?"".concat(n.join(" "),"(").concat(st(t),")"):n.join(" ")}}(s));var M=j.filter(h).join(" ");return g?"(".concat(M,")"):M}function B(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return q(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?q(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function q(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function X(r){if(!r||0===r.length)return"";var t,e=[],n=B(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s={},u=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(s[a]=o[a]);var i=[dt(s)],c="";u&&(c=st(u),i.push("=",c)),e.push(i.filter(h).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function V(r){if("select"===r.type)return W(r);var t=r.map(st);return"(".concat(t.join("), ("),")")}function K(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(y).join(", "));else{var e=r.value;t.push(e.map(st).join(", "))}return t.push(")"),t.filter(h).join("")}function Q(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(dt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[w(e)];switch(n){case"origin":o.push(L(t));break;case"update":o.push("UPDATE",a("SET",X,t.set),a("WHERE",st,t.where))}return o.filter(h).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[Q(r.target),z(t)].filter(h).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,s=r.columns,u=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},d=v.keyword,y=v.set,m=[w(e),w(o),cr(t),K(f)];return Array.isArray(s)&&m.push("(".concat(s.map(L).join(", "),")")),m.push(a(Array.isArray(i)?"VALUES":"",V,i)),m.push(a("ON CONFLICT",Z,u)),m.push(a("SET",X,b)),m.push(a("WHERE",st,c)),m.push(a(d,X,y)),m.push(S(p)),m.filter(h).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",st(t),w(e),st(n)].filter(h).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(w(t),"(").concat(n&&st(n)||"",")"),a("AS","string"==typeof e?y:st,e),a(w(o&&o.keyword),y,o&&o.as)].filter(h).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,s=r.type,u=[st(n),"FOR",dt(e),D(o)],a=["".concat(w(s),"(").concat(u.join(" "),")")];return t&&a.push("AS",y(t)),a.join(" ")}(r);default:return""}}function sr(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,s=r.parentheses,u=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(w(t),"(".concat(y(n)),"(".concat(o.map(st).filter(h).join(", "),"))"));break;case"spatial_window_max_cells":a.push(w(t),"=",st(e));break;case"index":a.push(w(u),w(t),s?"(".concat(e.map(y).join(", "),")"):"= ".concat(y(e)));break;default:a.push(st(e))}return a.filter(h).join(" ")}}function ur(r,t){var e=r.name,n=r.symbol;return[w(e),n,t].filter(h).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",st(r.of));break;case"from_to":t.push("FROM",st(r.from),"TO",st(r.to));break;case"between_and":t.push("BETWEEN",st(r.between),"AND",st(r.and));break;case"contained":t.push("CONTAINED","IN",st(r.in))}return t.filter(h).join(" ")}function ir(r){if("UNNEST"===w(r.type))return nr(r);var t,e,n,o,s=r.table,u=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,d=r.tablesample,C=r.temporal_table,A=r.table_hint,E=y(b),g=y(u),_=y(p),T=s&&y(s);if(c)switch(c.type){case"values":var S=c.parentheses,x=c.values,I=c.prefix,j=[S&&"(","",S&&")"],N=V(x);I&&(N=N.split("(").slice(1).map((function(r){return"".concat(w(I),"(").concat(r)})).join("")),j[1]="VALUES ".concat(N),T=j.filter(h).join("");break;case"tumble":T=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,s=[y(t.expr.db),y(t.expr.schema),y(t.expr.table)].filter(h).join("."),u="DESCRIPTOR(".concat(dt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(ur(t,s)),ur(e,u)],i=ur(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(ur(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(h).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return m(r).join(" ")})).join(", "),T="".concat(w(e),"(").concat(w(n),"(").concat(o,"))");break;default:T=st(c)}var R=[[E,g,_,T=[w(f),T,w(v)].filter(h).join(" ")].filter(h).join(".")];if(d){var k=["TABLESAMPLE",st(d.expr),L(d.repeatable)].filter(h).join(" ");R.push(k)}R.push(function(r){if(r){var t=r.keyword,e=r.expr;return[w(t),ar(e)].filter(h).join(" ")}}(C),a("AS","string"==typeof i?y:st,i),or(l)),A&&R.push(w(A.keyword),"(".concat(A.expr.map(sr).filter(h).join(", "),")"));var O=R.filter(h).join(" ");return r.parentheses?"(".concat(O,")"):O}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var s=[],u=[],i=!0===e?1:e.length,c=0;c++<i;)s.push("("),u.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return s.join("")+o+u.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],d=v.on,y=v.using,m=v.join,C=[];C.push(m?" ".concat(w(m)):","),C.push(ir(v)),C.push(a("ON",st,d)),y&&C.push("USING (".concat(y.map(L).join(", "),")")),p.push(C.filter(h).join(" "))}return p.filter(h).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var s=L(n);switch(t){case"partition by":case"default collate":s=st(n);break;case"options":s="(".concat(n.map((function(r){return[r.keyword,r.symbol,st(r.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(st).join(", ")}return o.push(s),o.filter(h).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[y(t.db),y(t.table)].filter(h).join(".");return"".concat(w(e)," ").concat(n);case"column":return"COLUMN ".concat(dt(t));default:return"".concat(w(e)," ").concat(L(t))}}function pr(r){var t=r.keyword,e=r.expr;return[w(t),L(e)].filter(h).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",st(e)].filter(h).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var s=st(t),u=st(e);return[s,w(n),u].filter(h).join(" ")}function dr(r){var t,e,n,o,s=r.keyword,u=r.suffix,i="";switch(w(s)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",L,e&&e.right),a("FROM",cr,n),F(o)].filter(h).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===w(t.op)?a("LIKE",L,t.right):a("WHERE",st,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,s="'".concat(e,"'");return n&&(s+="@'".concat(n,"'")),["FOR",s,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(h).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[u]);break;case"VAR":i=ot(r.var),s=""}return["SHOW",w(s),w(u),i].filter(h).join(" ")}var yr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,s=r.type,u=t.expr,a=t.orderby;return[w(s),w(n),[[y(o.schema),y(o.name)].filter(h).join("."),"(".concat(u.map(Zr).join(", ")).concat(a?[" ORDER","BY",a.map(Zr).join(", ")].join(" "):"",")")].filter(h).join(""),zr(e)].filter(h).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,s=r.expr,u=void 0===s?[]:s,a=w(t),i=cr(e),c=u.map(st);return[a,"TABLE",w(n),L(o),i,c.join(", ")].filter(h).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[w(r.type),w(e),y(n),zr(t)].filter(h).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[w(r.type),w(e),[y(n.schema),y(n.name)].filter(h).join("."),zr(t)].filter(h).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[w(r.type),w(n),[[y(o.schema),y(o.name)].filter(h).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(h).join(""),zr(e)].filter(h).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,s=r.view,u=r.with,a=w(t),i=ir(s),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(dt).join(", "),")"));n&&c.push("WITH ".concat(n.map(w).join(", ")));c.push("AS",W(o)),u&&c.push(w(u));return c.filter(h).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[w(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,s=r.schema;return[w(t),w(e),st(n),w(o),y(s)].filter(h).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.options,a=[w(t),w(e),w(n)],i=[y(o.schema),o.name].filter(h).join("."),c="".concat(s.expr.map(Zr).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(Zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(u.map(Kr).join(", "),")")),a.filter(h).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,s=r.as,u=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,d=r.unlogged,y=r.with,m=[w(t),w(f),w(u),w(d),w(e),w(a),cr(n)];if(o){var C=o.type,A=cr(o.table);return m.push(w(C),A),m.filter(h).join(" ")}if(b)return m.concat([qr(b)]).filter(h).join(" ");i&&m.push("(".concat(i.map(Wr).join(", "),")"));if(c){var E=p().database,g=E&&"sqlite"===E.toLowerCase()?", ":" ";m.push(c.map(lr).join(g))}if(y){var _=y.map((function(r){return[L(r.keyword),w(r.symbol),L(r.value)].join(" ")})).join(", ");m.push("WITH (".concat(_,")"))}m.push(w(l),w(s)),v&&m.push(wr(v));return m.filter(h).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,s=r.execute,u=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[w(f),w(l),w(e),w(c),y(t),w(i)],d=T(o);v.push(d,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Fr(x(n)).concat(Fr(x(u)))),b&&v.push(w(b.type),st(b.cond));return v.push(w(s.keyword),Gr(s.expr)),v.filter(h).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,s=r.type,u=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,d=[w(s),w(c),st(t),w(n),w(i),ir(l),w(b),f.map((function(r){var t=[w(r.keyword)],e=r.args;return e&&t.push(w(e.keyword),e.columns.map(dt).join(", ")),t.join(" ")})),"ON",ir(u),w(e&&e.keyword),w(e&&e.args),p&&"".concat(w(p.keyword)," ").concat(y(p.trigger)),a("WHEN",st,v),w(o.prefix)];switch(o.type){case"set":d.push(a("SET",X,o.expr));break;case"multiple":d.push(hr(o.expr.ast))}return d.push(w(o.suffix)),d.filter(h).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,s=r.schema,u=r.type,i=r.with,c=r.version;return[w(u),w(o),w(n),L(t),w(i),a("SCHEMA",L,s),a("VERSION",L,c),a("FROM",L,e)].filter(h).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,s=r.args,u=r.returns,a=r.options,i=r.last,c=[w(t),w(e),w(n)],l=[L(o.schema),o.name.map(L).join(".")].filter(h).join("."),f=s.map(Zr).filter(h).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),Array.isArray(n)?"(".concat(n.map(mt).join(", "),")"):Xr(n)].filter(h).join(" ")}(u),a.map(Vr).join(" "),i),c.filter(h).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,s=r.include,u=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,d=r.on_kw,m=r.table,C=r.tablespace,A=r.type,E=r.where,g=r.with,_=r.with_before_where,T=g&&"WITH (".concat(U(g).join(", "),")"),S=s&&"".concat(w(s.keyword)," (").concat(s.columns.map((function(r){return"string"==typeof r?y(r):st(r)})).join(", "),")"),x=l;l&&(x="string"==typeof l?y(l):[y(l.schema),y(l.name)].filter(h).join("."));var I=[w(A),w(i),w(n),w(o),w(t),x,w(d),ir(m)].concat(Fr(k(c)),["(".concat(j(u),")"),S,U(p).join(" "),zr(b),zr(v),a("TABLESPACE",L,C)]);_?I.push(T,a("WHERE",st,E)):I.push(a("WHERE",st,E),T);return I.push(a("ON",st,f),a("FILESTREAM_ON",L,e)),I.filter(h).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,s=r.if_not_exists,u=r.create_definitions,a=[w(t),w(o),w(e),w(s),cr(n)];u&&a.push(u.map(Wr).join(" "));return a.filter(h).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,s=r.create_definitions,u=r[e],a=u.db,i=u.schema,c=[L(a),i.map(L).join(".")].filter(h).join("."),l=[w(t),w(n),w(e),w(o),c];s&&l.push(s.map(lr).join(" "));return l.filter(h).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,s=r.keyword,u=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,L=p.db,C=p.schema,A=p.view,E=[y(L),y(C),y(A)].filter(h).join(".");return[w(f),w(a),w(l),w(u),t&&"ALGORITHM = ".concat(w(t)),st(n),c&&"SQL SECURITY ".concat(w(c)),w(s),w(o),E,e&&"(".concat(e.map(d).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return m(r).join(" ")})).join(", "),")")].join(" "),"AS",wr(i),w(b)].filter(h).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,s=r.target,u=r.create_definitions,a=[w(n),w(o),[y(e.schema),y(e.name)].filter(h).join("."),w(t),E(s)];if(u&&u.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Hr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(u);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(st(f));break;case"default":c.push(w(p),st(f.value));break;case"constraint":c.push(kr(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(h).join(" "))}return a.filter(h).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,s=r.resource,u=[w(r.type),w(n),[y(o.schema),y(o.name)].filter(h).join("."),w(t),w(s)];if(e){var a=[];switch(s){case"enum":case"range":a.push(st(e));break;default:a.push("(".concat(e.map(Wr).join(", "),")"))}u.push(a.filter(h).join(" "))}return u.filter(h).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,s=r.keyword,u=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[Ir(t)];return e&&n.push(w(e.keyword),e.auth_plugin,L(e.value)),n.filter(h).join(" ")})).join(", "),b=[w(f),w(s),w(o),p];n&&b.push(w(n.keyword),n.value.map(Ir).join(", "));b.push(a(c&&c.keyword,st,c&&c.value)),l&&b.push(w(l.keyword),l.value.map((function(r){return st(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,st,r.value))}));return b.push(L(u),_(e),L(t)),b.filter(h).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[w(r.type),w(e),fr(n),pr(t)].filter(h).join(" ")},select:W,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[w(t),w(e),st(n)].filter(h).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning,l=[H(u),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",st,o)),l.push(at(s,"order by")),l.push(F(i)),l.push(S(c)),l.filter(h).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[w(t),ir(e),(n||[]).map(br).filter(h).join(", ")].filter(h).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[w(t)],s=[e];n&&s.push("(".concat(st(n).join(", "),")"));return o.push(s.join("")),o.filter(h).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[w(t),W(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,s=r.stmts;return[e,w(t),n,"IN",hr([o]),"LOOP",hr(s),"END LOOP",e].filter(h).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,s=r.orderby,u=r.with,i=r.limit,c=r.returning;return[H(u),"UPDATE",cr(e),a("SET",X,n),a("FROM",cr,t),a("WHERE",st,o),at(s,"order by"),F(i),S(c)].filter(h).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,s=r.prefix,u=r.go,a=r.semicolons,i=r.suffix,c=[w(r.type),st(t),L(s),"".concat(Ar(o.ast||o)).concat(a[0]),w(u)];n&&c.push(n.map((function(r){return[w(r.type),st(r.boolean_expr),"THEN",Ar(r.then.ast||r.then),r.semicolon].filter(h).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Ar(e.ast||e)).concat(a[1]));return c.push(L(i)),c.filter(h).join(" ")},insert:J,drop:Sr,truncate:Sr,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[w(t)],s=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,s=r.datatype,u=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(h).join(""),w(n),w(o)];switch(c){case"variable":l.push(yt(s),st(r.collate),w(u)),i&&l.push(w(i.keyword),st(i.value));break;case"cursor":l.push(w(a));break;case"table":l.push(w(a),"(".concat(i.map(Wr).join(", "),")"))}return l.filter(h).join(" ")})).join("".concat(n," "));return o.push(s),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=w(t),o=y(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var s,u=Er(e);try{for(u.s();!(s=u.n()).done;){var a=s.value.map(ir);n.push(a.join(" TO "))}}catch(r){u.e(r)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=st(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=w(t);return"".concat(n," ").concat(y(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=w(t),s=e.map(st).join(", ");return[o,w(n),s].filter(h).join(" ")},lock:xr,unlock:xr,show:dr,grant:jr,revoke:jr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[w(t),st(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,s=[w(t),w(e)];n&&s.push([L(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(h).join(""),n.expr.map((function(r){return st(r)})).join(", "));o&&s.push(w(o.type),w(o.option),o.symbol,o.expr.map((function(r){return st(r)})).join(", "));return s.filter(h).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,s=[L(e),w(n)];return o&&s.push(o.map(L).join(", ")),s.filter(h).join(" ")}};function wr(r){if(!r)return"";for(var t=yr[r.type],e=r,n=e._parentheses,o=e._orderby,s=e._limit,u=[n&&"(",t(r)];r._next;){var a=yr[r._next.type],i=w(r.set_op);u.push(i,a(r._next)),r=r._next}return u.push(n&&")",at(o,"order by"),F(s)),u.filter(h).join(" ")}function hr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],s=wr(o);e===n-1&&"transaction"===o.type&&(s="".concat(s," ;")),t.push(s)}return t.join(" ; ")}var Lr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function mr(r){var t=r&&r.ast?r.ast:r;if(!Lr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Cr(r){return Array.isArray(r)?(r.forEach(mr),hr(r)):(mr(r),wr(r))}function Ar(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Cr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Cr(r)}function Er(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=_r(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function gr(r){return function(r){if(Array.isArray(r))return Tr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||_r(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _r(r,t){if(r){if("string"==typeof r)return Tr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Tr(r,t):void 0}}function Tr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Sr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,s=r.suffix,u=[w(t),w(e),w(o)];switch(e){case"table":u.push(cr(n));break;case"trigger":u.push([n[0].schema?"".concat(y(n[0].schema),"."):"",y(n[0].trigger)].filter(h).join(""));break;case"database":case"schema":case"procedure":u.push(y(n));break;case"view":u.push(cr(n),r.options&&r.options.map(st).filter(h).join(" "));break;case"index":u.push.apply(u,[dt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(st).filter(h).join(" ")]));break;case"type":u.push(n.map(dt).join(", "),r.options&&r.options.map(st).filter(h).join(" "))}return s&&u.push(s.map(st).filter(h).join(" ")),u.filter(h).join(" ")}function xr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),w(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var s,u=[],a=Er(n);try{var i=function(){var r=s.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return w(e[r])})).filter(h).join(" "))}u.push(n.join(" "))};for(a.s();!(s=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[u.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(h).join(" ")}function Ir(r){var t=r.name,e=r.host,n=[L(t)];return e&&n.push("@",L(e)),n.join("")}function jr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,s=r.on,u=r.to_from,a=r.user_or_roles,i=r.with,c=[w(t),L(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[st(t)];return e&&n.push("(".concat(e.map(dt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),s)switch(c.push("ON"),n){case"priv":c.push(L(s.object_type),s.priv_level.map((function(r){return[y(r.prefix),y(r.name)].filter(h).join(".")})).join(", "));break;case"proxy":c.push(Ir(s))}return c.push(w(u),a.map(Ir).join(", ")),c.push(L(i)),c.filter(h).join(" ")}function Nr(r){return function(r){if(Array.isArray(r))return Rr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Rr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Rr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kr(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,s=r.keyword,u=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(w(s)),l.push(y(t));var b=w(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&y(o)),l.push.apply(l,Nr(M(r))),l.push.apply(l,Nr(wt(u))),l.push(w(n)),l.push(a("FOR",y,i)),l.push(L(c)),l.filter(h).join(" ")}}function Or(r){if(r){var t=r.type;return"rows"===t?[w(t),st(r.expr)].filter(h).join(" "):st(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),Or(o)].filter(h).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,s=r.parentheses,u=w(o);if("WINDOW"===u)return"OVER ".concat(Ur(t));if("ON UPDATE"===u){var a="".concat(w(o)," ").concat(w(n)),i=st(e)||[];return s&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return w(t);for(var e=r.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(L(o[u])),s.push("]");return s.join("")}function $r(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,s=r.as,u=r.offset,a=r.parentheses,i=bt({expr:e,offset:u}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,d=p.dataType,m=p.parentheses,C=p.quoted,A=p.scale,E=p.suffix,g=p.expr,_=g?st(g):"";null!=v&&(_=A?"".concat(v,", ").concat(A):v),m&&(_="(".concat(_,")")),b&&(_="<".concat(_,">")),E&&E.length&&(_+=" ".concat(E.map(L).join(" ")));var T="::",S="",x=[];"as"===o&&(0===l&&(i="".concat(w(n),"(").concat(i)),S=")",T=" ".concat(o.toUpperCase()," ")),0===l&&x.push(i);var I=Pr(p);x.push(T,C,d,C,I,_,S),c.push(x.filter(h).join(""))}s&&c.push(" AS ".concat(y(s)));var j=c.filter(h).join("");return a?"(".concat(j,")"):j}function Gr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,s=r.parentheses,u=r.within_group,a=r.over,i=r.suffix,c=Dr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[w(t),w(e),"(".concat(at(n,"order by"),")")].filter(h).join(" ")}(u),f=st(i),p=[L(n.schema),n.name.map(L).join(".")].filter(h).join(".");if(!t)return[p,l,c].filter(h).join(" ");var b=r.separator||", ";"TRIM"===w(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var d=st(t);if(Array.isArray(b)){for(var y=d[0],m=1,C=d.length;m<C;++m)y=[y,d[m]].join(" ".concat(st(b[m-1])," "));v.push(y)}else v.push(d.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(h).join(" "),[s?"(".concat(v,")"):v,l,c].filter(h).join(" ")}function Fr(r){return function(r){if(Array.isArray(r))return Yr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Hr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(r,t){if(r){if("string"==typeof r)return Yr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Yr(r,t):void 0}}function Yr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Wr(r){if(!r)return[];var t,e,n,o,s=r.resource;switch(s){case"column":return mt(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(w(n)),e.push(o),e.push.apply(e,N(M(t))),e.filter(h).join(" ");case"constraint":return kr(r);case"sequence":return[w(r.prefix),st(r.value)].filter(h).join(" ");default:throw new Error("unknown resource = ".concat(s," type"))}}function Br(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(L(r.from),")"),"TO","(".concat(L(r.to),")"));break;case"in":t.push("IN","(".concat(st(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(L(r.modulus),", REMAINDER ").concat(L(r.remainder),")"))}return t.filter(h).join(" ")}function qr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,s=[w(t),ir(e),w(n.keyword),Br(n.expr)];return o&&s.push("TABLESPACE",L(o)),s.filter(h).join(" ")}function Xr(r){return r.dataType?E(r):[y(r.db),y(r.schema),y(r.table)].filter(h).join(".")}function Vr(r){var t=r.type;switch(t){case"as":return[w(t),r.symbol,wr(r.declare),w(r.begin),hr(r.expr),w(r.end),r.symbol].filter(h).join(" ");case"set":return[w(t),r.parameter,w(r.value&&r.value.prefix),r.value&&r.value.expr.map(st).join(", ")].filter(h).join(" ");case"return":return[w(t),st(r.expr)].filter(h).join(" ");default:return st(r)}}function Kr(r){var t=r.type,e=r.symbol,n=r.value,o=[w(t),e];switch(w(t)){case"SFUNC":o.push([y(n.schema),n.name].filter(h).join("."));break;case"STYPE":case"MSTYPE":o.push(E(n));break;default:o.push(st(n))}return o.filter(h).join(" ")}function Qr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",L(t),"VALUES",w(e.type),"(".concat(L(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,s=r.if_exists,u=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[mt(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=y(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=y(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,w(r[i])].filter(h).join(" ");break;case"constraint":f=y(r[i]),p=[Wr(e)];break;case"partition":p=[Qr(t,r.partitions)];break;case"key":f=y(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[w(t),w(o),w(n),w(s),u&&dt(u),w(a),f&&f.trim(),p.filter(h).join(" ")];return l&&b.push(w(l.keyword),l.expr&&dt(l.expr)),b.filter(h).join(" ")}function Zr(r){var t=r.default&&[w(r.default.keyword),st(r.default.value)].join(" ");return[w(r.mode),r.name,E(r.type),t].filter(h).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(w(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var s=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(h).join(", "):st(n);return e?"[".concat(s,"]"):o?"(".concat(s,")"):s}(r);default:return""}}function tt(r){var t=r.definition,e=[w(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(h).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,s=st(t.expr);s=Array.isArray(s)?s.join(", "):s;var u=r.name,a=Dr(n);t.distinct&&(s=["DISTINCT",s].join(" ")),t.separator&&t.separator.delimiter&&(s=[s,L(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(s=[s,st(t.separator.expr)].join(" ")),t.orderby&&(s=[s,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(s=[s,w(t.separator.keyword),L(t.separator.value)].filter(h).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(st(e.where),")"):"";return["".concat(u,"(").concat(s,")"),i,a,c].filter(h).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,s=t.having,u="".concat(w(e),"(").concat(st(o));return s&&(u="".concat(u," HAVING ").concat(w(s.prefix)," ").concat(st(s.expr))),[u="".concat(u,")"),Dr(n)].filter(h).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,s=r.separator,u=void 0===s?", ":s;return[e,"(",t?st(t).join(u):"",")",o&&" ",o].filter(h).join("")}(r),Dr(t)].filter(h).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(st(n));for(var s=0,u=e.length;s<u;++s)t.push(e[s].type.toUpperCase()),e[s].cond&&(t.push(st(e[s].cond)),t.push("THEN")),t.push(st(e[s].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:$r,collate:it,column_ref:dt,column_definition:mt,datatype:E,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,s=t.source,u=["".concat(w(e),"(").concat(w(n)),"FROM",w(o),st(s)];return"".concat(u.filter(h).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[w(t),e,st(n)].filter(h).join(" ")}(t[r])})).filter(h).join(", ");return"".concat(w(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,s=r.mode,u=[w(o),"(".concat(n.map((function(r){return dt(r)})).join(", "),")")].join(" "),a=[w(t),["(",st(r.expr),s&&" ".concat(L(s)),")"].filter(h).join("")].join(" ");return[u,a,Ct(e)].filter(h).join(" ")},function:Gr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,s=n.map(st).join(", ");return[o?"(".concat(s,")"):s,"->",st(e)].join(" ")},insert:wr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[w(t),e.map((function(r){return st(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,s=[st(e),"VALUE",st(n)];return o&&s.push("ON","NULL",st(o)),s.filter(h).join(" ")},json_visitor:function(r){return[r.symbol,st(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,st(t.expr)].filter(h).join(" ")},show:dr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[L(e.schema),e.name.map(L).join(".")].filter(h).join(".");return["".concat(o,"(").concat(st(n).join(", "),")"),"AS",Gr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,s=r.quoted,u=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return u&&(c+=u),a.push(c),[s,a.join(" "),s].filter(h).join("")}function st(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var s=o[n];t[s]=e[s]}}var u=t.type;return"expr"===u?st(t.expr):nt[u]?nt[u](t):L(t)}}function ut(r){return r?(Array.isArray(r)||(r=[r]),r.map(st)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=w(t);switch(n){case"ORDER BY":e=r.map((function(r){return[st(r.expr),r.type||"ASC",w(r.nulls)].filter(h).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return st(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,s=e.value,u=[w(t)];return s||u.push(o),u.push(Array.isArray(n)?n.map(L).join("."):L(n)),s&&u.push(o),u.push(st(s)),u.filter(h).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return y(r,t);var e=r.expr,n=r.offset,o=r.suffix,s=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),L(r.value),"".concat(r.name?")":""),"]"].filter(h).join("")})).join("");return[st(e),s,o].filter(h).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,s=o.brackets?"[".concat(L(o.index),"]"):"".concat(o.notation).concat(L(o.index));o.property&&(s="".concat(s,".").concat(L(o.property))),e.push(s)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function dt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,s=r.db,u=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,d=r.order_by,L=r.subFields,m=void 0===L?[]:L,C="*"===n?"*":bt(n,u),A=[s,f,p].filter(h).map((function(r){return"".concat("string"==typeof r?y(r):st(r))})),E=A[0];if(E){for(var g=1;g<A.length;++g)E="".concat(E).concat(c[g]||".").concat(A[g]);C="".concat(E).concat(c[g]||".").concat(C)}var _=[C=["".concat(C).concat(vt(t))].concat(lt(m)).join("."),it(o),st(l),a("AS",st,e)];_.push("string"==typeof v?w(v):st(v)),_.push(w(d));var T=_.filter(h).join(" ");return b?"(".concat(T,")"):T}function yt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,s=r.expr,u=E({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(s&&(u+=st(s)),r.array){var a=Pr(r);u+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return u}}function wt(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,s=r.table,u=r.on_action;return t.push(w(n)),t.push(cr(s)),t.push(e&&"(".concat(e.map((function(r){return st(r)})).join(", "),")")),t.push(w(o)),u.map((function(r){return t.push(w(r.type),st(r.value))})),t.filter(h)}function ht(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,s=r.comment,u=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,d=r.unique,y=r.primary_key,C=r.column_format,A=r.reference_definition,E=[w(e&&e.action),w(e&&e.value)].filter(h).join(" ");if(b||t.push(E),f){var g=f.type,T=f.value;t.push(g.toUpperCase(),st(T))}var S=p().database;return u&&t.push(w(u.keyword),L(u.constraint)),t.push(kr(o)),t.push(function(r){if(r)return[w(r.value),"(".concat(st(r.expr),")"),w(r.storage_type)].filter(h).join(" ")}(b)),b&&t.push(E),t.push(I(v),w(y),w(d),_(s)),t.push.apply(t,lt(m(n))),"sqlite"!==S.toLowerCase()&&t.push(st(i)),t.push.apply(t,lt(m(C))),t.push.apply(t,lt(m(c))),t.push.apply(t,lt(wt(A))),t.push(a("USING",st,l)),t.filter(h).join(" ")}function Lt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,s=r.order_by,u="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return u.collate=null,[st(u),st(e),o,w(s),w(n)].filter(h).join(" ")}function mt(r){var t=[],e=dt(r.column),n=yt(r.definition);return t.push(e),t.push(n),t.push(ht(r)),t.filter(h).join(" ")}function Ct(r){return r?"object"===ct(r)?["AS",st(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?y(r):d(r)].join(" "):""}function At(r,t){var e=r.expr,n=r.type;if("cast"===n)return $r(r);t&&(e.isDual=t);var o=st(e),s=r.expr_list;if(s){var u=[o],a=s.map((function(r){return At(r,t)})).join(", ");return u.push([w(n),n&&"(",a,n&&")"].filter(h).join("")),u.filter(h).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,Ct(r.as)].filter(h).join(" ")}function Et(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=Et(t);return r.map((function(r){return At(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=ut(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",s=t.join(o);return e?"(".concat(s,")"):s},nt.select=function(r){var t="object"===et(r._next)?wr(r):W(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",s="".concat(t).concat(o).concat(st(n));return e?"(".concat(s,")"):s},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[L(r.key),L(r.value)].join(", ")})).join(", ");return[w(t),"[".concat(e,"]")].join("")};var _t=e(0);function Tt(r){return(Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var St,xt,It,jt=(St={},xt="mariadb",It=_t.parse,(xt=function(r){var t=function(r,t){if("object"!=Tt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Tt(t)?t:t+""}(xt))in St?Object.defineProperty(St,xt,{value:It,enumerable:!0,configurable:!0,writable:!0}):St[xt]=It,St);function Nt(r){return(Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Rt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return kt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?kt(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function kt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Ot(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Nt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Nt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&Ot(r.prototype,t),e&&Ot(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),Ar(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;return b(t),st(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(b(e),!r||"*"===r)return[];var n=Et(t);return r.map((function(r){return At(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,e=t.database,n=void 0===e?"mariadb":e;b(t);var o=n.toLowerCase();if(jt[o])return jt[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||s.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=Rt(i);try{for(f.s();!(u=f.n()).done;){var p,b=u.value,v=!1,d=Rt(t);try{for(d.s();!(p=d.n()).done;){var y=p.value,w=new RegExp("^".concat(y,"$"),"i");if(w.test(b)){v=!0;break}}}catch(r){d.e(r)}finally{d.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=mariadb.js.map