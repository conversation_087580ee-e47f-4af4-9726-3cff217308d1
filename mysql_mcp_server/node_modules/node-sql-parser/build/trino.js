!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},s={start:Aa},a=Aa,i=ha("IF",!0),c=ha("EXTENSION",!0),l=ha("SCHEMA",!0),f=ha("VERSION",!0),p=ha("CASCADED",!0),b=ha("LOCAL",!0),v=ha("CHECK",!0),d=ha("OPTION",!1),y=ha("check_option",!0),h=ha("security_barrier",!0),w=ha("security_invoker",!0),m=ha("TYPE",!0),L=ha("DOMAIN",!0),C=ha("INCREMENT",!0),E=ha("MINVALUE",!0),A=function(r,t){return{resource:"sequence",prefix:r.toLowerCase(),value:t}},g=ha("NO",!0),T=ha("MAXVALUE",!0),_=ha("START",!0),S=ha("CACHE",!0),j=ha("CYCLE",!0),x=ha("OWNED",!0),I=ha("NONE",!0),R=ha("NULLS",!0),N=ha("FIRST",!0),k=ha("LAST",!0),O=ha("AUTO_INCREMENT",!0),U=ha("UNIQUE",!0),M=ha("KEY",!0),D=ha("PRIMARY",!0),P=ha("COLUMN_FORMAT",!0),$=ha("FIXED",!0),F=ha("DYNAMIC",!0),G=ha("DEFAULT",!0),H=ha("STORAGE",!0),B=ha("DISK",!0),q=ha("MEMORY",!0),W=ha("CASCADE",!0),Y=ha("RESTRICT",!0),V=ha("OUT",!0),X=ha("VARIADIC",!0),Q=ha("INOUT",!0),K=ha("AGGREGATE",!0),z=ha("FUNCTION",!0),Z=ha("OWNER",!0),J=ha("CURRENT_ROLE",!0),rr=ha("CURRENT_USER",!0),tr=ha("SESSION_USER",!0),er=ha("ALGORITHM",!0),nr=ha("INSTANT",!0),or=ha("INPLACE",!0),ur=ha("COPY",!0),sr=ha("LOCK",!0),ar=ha("SHARED",!0),ir=ha("EXCLUSIVE",!0),cr=ha("PRIMARY KEY",!0),lr=ha("FOREIGN KEY",!0),fr=ha("MATCH FULL",!0),pr=ha("MATCH PARTIAL",!0),br=ha("MATCH SIMPLE",!0),vr=ha("SET NULL",!0),dr=ha("NO ACTION",!0),yr=ha("SET DEFAULT",!0),hr=ha("TRIGGER",!0),wr=ha("BEFORE",!0),mr=ha("AFTER",!0),Lr=ha("INSTEAD OF",!0),Cr=ha("ON",!0),Er=ha("EXECUTE",!0),Ar=ha("PROCEDURE",!0),gr=ha("OF",!0),Tr=ha("NOT",!0),_r=ha("DEFERRABLE",!0),Sr=ha("INITIALLY IMMEDIATE",!0),jr=ha("INITIALLY DEFERRED",!0),xr=ha("FOR",!0),Ir=ha("EACH",!0),Rr=ha("ROW",!0),Nr=ha("STATEMENT",!0),kr=ha("CHARACTER",!0),Or=ha("SET",!0),Ur=ha("CHARSET",!0),Mr=ha("COLLATE",!0),Dr=ha("AVG_ROW_LENGTH",!0),Pr=ha("KEY_BLOCK_SIZE",!0),$r=ha("MAX_ROWS",!0),Fr=ha("MIN_ROWS",!0),Gr=ha("STATS_SAMPLE_PAGES",!0),Hr=ha("CONNECTION",!0),Br=ha("COMPRESSION",!0),qr=ha("'",!1),Wr=ha("ZLIB",!0),Yr=ha("LZ4",!0),Vr=ha("ENGINE",!0),Xr=ha("IN",!0),Qr=ha("ACCESS SHARE",!0),Kr=ha("ROW SHARE",!0),zr=ha("ROW EXCLUSIVE",!0),Zr=ha("SHARE UPDATE EXCLUSIVE",!0),Jr=ha("SHARE ROW EXCLUSIVE",!0),rt=ha("ACCESS EXCLUSIVE",!0),tt=ha("SHARE",!0),et=ha("MODE",!0),nt=ha("NOWAIT",!0),ot=ha("TABLES",!0),ut=ha("PREPARE",!0),st=ha(";",!1),at=ha("(",!1),it=ha(")",!1),ct=function(r,t){return{with:r,...t}},lt=ha("OUTFILE",!0),ft=ha("DUMPFILE",!0),pt=ha("BTREE",!0),bt=ha("HASH",!0),vt=ha("GIST",!0),dt=ha("GIN",!0),yt=ha("WITH",!0),ht=ha("PARSER",!0),wt=ha("VISIBLE",!0),mt=ha("INVISIBLE",!0),Lt=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;tb[t]=t,e&&(tb[e]=t),function(r){const t=Qp(r);r.clear(),t.forEach(t=>r.add(t))}(rb)}),t},Ct=ha("LATERAL",!0),Et=ha("TABLESAMPLE",!0),At=ha("REPEATABLE",!0),gt=ha("CROSS",!0),Tt=ha("FOLLOWING",!0),_t=ha("PRECEDING",!0),St=ha("CURRENT",!0),jt=ha("UNBOUNDED",!0),xt=ha("=",!1),It=ha("DO",!0),Rt=ha("NOTHING",!0),Nt=ha("CONFLICT",!0),kt=ha("->",!1),Ot=function(r,t){return Vp(r,t)},Ut=ha("!",!1),Mt=ha(">=",!1),Dt=ha(">",!1),Pt=ha("<=",!1),$t=ha("<>",!1),Ft=ha("<",!1),Gt=ha("!=",!1),Ht=ha("SIMILAR",!0),Bt=ha("!~*",!1),qt=ha("~*",!1),Wt=ha("~",!1),Yt=ha("!~",!1),Vt=ha("ESCAPE",!0),Xt=ha("+",!1),Qt=ha("-",!1),Kt=ha("*",!1),zt=ha("/",!1),Zt=ha("%",!1),Jt=ha("||",!1),re=ha("$",!1),te=ha("?",!1),ee=ha("?|",!1),ne=ha("?&",!1),oe=ha("#-",!1),ue=ha("#>>",!1),se=ha("#>",!1),ae=ha("@>",!1),ie=ha("<@",!1),ce=ha("E",!0),le=function(r){return!0===Fp[r.toUpperCase()]},fe=ha('"',!1),pe=/^[^"]/,be=wa(['"'],!0,!1),ve=/^[^']/,de=wa(["'"],!0,!1),ye=ha("`",!1),he=/^[^`]/,we=wa(["`"],!0,!1),me=function(r){return{type:"default",value:r}},Le=/^[A-Za-z_\u4E00-\u9FA5]/,Ce=wa([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),Ee=/^[A-Za-z0-9_$\x80-\uFFFF]/,Ae=wa([["A","Z"],["a","z"],["0","9"],"_","$",["","￿"]],!1,!1),ge=/^[A-Za-z0-9_\u4E00-\u9FA5]/,Te=wa([["A","Z"],["a","z"],["0","9"],"_",["一","龥"]],!1,!1),_e=ha(":",!1),Se=ha("OVER",!0),je=ha("FILTER",!0),xe=ha("FIRST_VALUE",!0),Ie=ha("LAST_VALUE",!0),Re=ha("ROW_NUMBER",!0),Ne=ha("DENSE_RANK",!0),Oe=ha("RANK",!0),Ue=ha("LAG",!0),Me=ha("LEAD",!0),De=ha("NTH_VALUE",!0),Pe=ha("IGNORE",!0),$e=ha("RESPECT",!0),Fe=ha("percentile_cont",!0),Ge=ha("percentile_disc",!0),He=ha("within",!0),Be=ha("mode",!0),qe=ha("BOTH",!0),We=ha("LEADING",!0),Ye=ha("TRAILING",!0),Ve=ha("trim",!0),Xe=ha("INPUT",!0),Qe=ha("=>",!1),Ke=ha("PATH",!0),ze=ha("OUTER",!0),Ze=ha("RECURSIVE",!0),Je=ha("now",!0),rn=ha("at",!0),tn=ha("zone",!0),en=ha("FLATTEN",!0),nn=ha("CENTURY",!0),on=ha("DAY",!0),un=ha("DATE",!0),sn=ha("DECADE",!0),an=ha("DOW",!0),cn=ha("DOY",!0),ln=ha("EPOCH",!0),fn=ha("HOUR",!0),pn=ha("ISODOW",!0),bn=ha("ISOYEAR",!0),vn=ha("MICROSECONDS",!0),dn=ha("MILLENNIUM",!0),yn=ha("MILLISECONDS",!0),hn=ha("MINUTE",!0),wn=ha("MONTH",!0),mn=ha("QUARTER",!0),Ln=ha("SECOND",!0),Cn=ha("TIMEZONE",!0),En=ha("TIMEZONE_HOUR",!0),An=ha("TIMEZONE_MINUTE",!0),gn=ha("WEEK",!0),Tn=ha("YEAR",!0),_n=ha("NTILE",!0),Sn=/^[\n]/,jn=wa(["\n"],!1,!1),xn=/^[^"\\\0-\x1F\x7F]/,In=wa(['"',"\\",["\0",""],""],!0,!1),Rn=/^[^'\\]/,Nn=wa(["'","\\"],!0,!1),kn=ha("\\'",!1),On=ha('\\"',!1),Un=ha("\\\\",!1),Mn=ha("\\/",!1),Dn=ha("\\b",!1),Pn=ha("\\f",!1),$n=ha("\\n",!1),Fn=ha("\\r",!1),Gn=ha("\\t",!1),Hn=ha("\\u",!1),Bn=ha("\\",!1),qn=ha("''",!1),Wn=/^[\n\r]/,Yn=wa(["\n","\r"],!1,!1),Vn=ha(".",!1),Xn=/^[0-9]/,Qn=wa([["0","9"]],!1,!1),Kn=/^[0-9a-fA-F]/,zn=wa([["0","9"],["a","f"],["A","F"]],!1,!1),Zn=/^[eE]/,Jn=wa(["e","E"],!1,!1),ro=/^[+\-]/,to=wa(["+","-"],!1,!1),eo=ha("NULL",!0),no=ha("NOT NULL",!0),oo=ha("TRUE",!0),uo=ha("TO",!0),so=ha("FALSE",!0),ao=ha("SHOW",!0),io=ha("DROP",!0),co=ha("USE",!0),lo=ha("ALTER",!0),fo=ha("SELECT",!0),po=ha("UPDATE",!0),bo=ha("CREATE",!0),vo=ha("TEMPORARY",!0),yo=ha("TEMP",!0),ho=ha("DELETE",!0),wo=ha("INSERT",!0),mo=ha("REPLACE",!0),Lo=ha("RETURNING",!0),Co=ha("RENAME",!0),Eo=(ha("EXPLAIN",!0),ha("PARTITION",!0)),Ao=ha("INTO",!0),go=ha("FROM",!0),To=ha("AS",!0),_o=ha("TABLE",!0),So=ha("DATABASE",!0),jo=ha("SEQUENCE",!0),xo=ha("TABLESPACE",!0),Io=ha("DEALLOCATE",!0),Ro=ha("LEFT",!0),No=ha("RIGHT",!0),ko=ha("FULL",!0),Oo=ha("INNER",!0),Uo=ha("JOIN",!0),Mo=ha("UNION",!0),Do=ha("VALUES",!0),Po=ha("USING",!0),$o=ha("WHERE",!0),Fo=ha("GROUP",!0),Go=ha("BY",!0),Ho=ha("ORDER",!0),Bo=ha("HAVING",!0),qo=ha("WINDOW",!0),Wo=ha("LIMIT",!0),Yo=ha("OFFSET",!0),Vo=ha("ASC",!0),Xo=ha("DESC",!0),Qo=ha("ALL",!0),Ko=ha("DISTINCT",!0),zo=ha("BETWEEN",!0),Zo=ha("IS",!0),Jo=ha("LIKE",!0),ru=ha("ILIKE",!0),tu=ha("EXISTS",!0),eu=ha("AND",!0),nu=ha("OR",!0),ou=ha("ARRAY",!0),uu=ha("ARRAY_AGG",!0),su=ha("STRING_AGG",!0),au=ha("COUNT",!0),iu=ha("GROUP_CONCAT",!0),cu=ha("MAX",!0),lu=ha("MIN",!0),fu=ha("SUM",!0),pu=ha("AVG",!0),bu=ha("EXTRACT",!0),vu=ha("CALL",!0),du=ha("CASE",!0),yu=ha("WHEN",!0),hu=ha("THEN",!0),wu=ha("ELSE",!0),mu=ha("END",!0),Lu=ha("CAST",!0),Cu=ha("TRY_CAST",!0),Eu=ha("BOOL",!0),Au=ha("BOOLEAN",!0),gu=ha("CHAR",!0),Tu=ha("VARCHAR",!0),_u=ha("NUMBER",!0),Su=ha("DECIMAL",!0),ju=ha("SIGNED",!0),xu=ha("UNSIGNED",!0),Iu=ha("INT",!0),Ru=ha("ZEROFILL",!0),Nu=ha("INTEGER",!0),ku=ha("JSON",!0),Ou=ha("JSONB",!0),Uu=ha("GEOMETRY",!0),Mu=ha("SMALLINT",!0),Du=ha("SERIAL",!0),Pu=ha("TINYINT",!0),$u=ha("TINYTEXT",!0),Fu=ha("TEXT",!0),Gu=ha("MEDIUMTEXT",!0),Hu=ha("LONGTEXT",!0),Bu=ha("BIGINT",!0),qu=ha("ENUM",!0),Wu=ha("FLOAT",!0),Yu=ha("DOUBLE",!0),Vu=ha("BIGSERIAL",!0),Xu=ha("REAL",!0),Qu=ha("DATETIME",!0),Ku=ha("ROWS",!0),zu=ha("TIME",!0),Zu=ha("TIMESTAMP",!0),Ju=ha("TRUNCATE",!0),rs=ha("USER",!0),ts=ha("UUID",!0),es=ha("OID",!0),ns=ha("REGCLASS",!0),os=ha("REGCOLLATION",!0),us=ha("REGCONFIG",!0),ss=ha("REGDICTIONARY",!0),as=ha("REGNAMESPACE",!0),is=ha("REGOPER",!0),cs=ha("REGOPERATOR",!0),ls=ha("REGPROC",!0),fs=ha("REGPROCEDURE",!0),ps=ha("REGROLE",!0),bs=ha("REGTYPE",!0),vs=ha("CURRENT_DATE",!0),ds=(ha("ADDDATE",!0),ha("INTERVAL",!0)),ys=ha("CURRENT_TIME",!0),hs=ha("CURRENT_TIMESTAMP",!0),ws=ha("SYSTEM_USER",!0),ms=ha("GLOBAL",!0),Ls=ha("SESSION",!0),Cs=ha("PERSIST",!0),Es=ha("PERSIST_ONLY",!0),As=ha("VIEW",!0),gs=ha("@",!1),Ts=ha("@@",!1),_s=ha("$$",!1),Ss=ha("return",!0),js=ha(":=",!1),xs=ha("::",!1),Is=ha("DUAL",!0),Rs=ha("ADD",!0),Ns=ha("COLUMN",!0),ks=ha("INDEX",!0),Os=ha("FULLTEXT",!0),Us=ha("SPATIAL",!0),Ms=ha("COMMENT",!0),Ds=ha("CONSTRAINT",!0),Ps=ha("CONCURRENTLY",!0),$s=ha("REFERENCES",!0),Fs=ha("SQL_CALC_FOUND_ROWS",!0),Gs=ha("SQL_CACHE",!0),Hs=ha("SQL_NO_CACHE",!0),Bs=ha("SQL_SMALL_RESULT",!0),qs=ha("SQL_BIG_RESULT",!0),Ws=ha("SQL_BUFFER_RESULT",!0),Ys=ha(",",!1),Vs=ha("[",!1),Xs=ha("]",!1),Qs=ha("->>",!1),Ks=ha("&&",!1),zs=ha("/*",!1),Zs=ha("*/",!1),Js=ha("--",!1),ra=ha("//",!1),ta=(ha("#",!1),{type:"any"}),ea=/^[ \t\n\r]/,na=wa([" ","\t","\n","\r"],!1,!1),oa=/^[^$]/,ua=wa(["$"],!0,!1),sa=function(r){return{dataType:r}},aa=ha("bytea",!0),ia=function(r){return{dataType:r}},ca=ha("WITHOUT",!0),la=ha("ZONE",!0),fa=0,pa=0,ba=[{line:1,column:1}],va=0,da=[],ya=0;if("startRule"in t){if(!(t.startRule in s))throw new Error("Can't start parsing from rule \""+t.startRule+'".');a=s[t.startRule]}function ha(r,t){return{type:"literal",text:r,ignoreCase:t}}function wa(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function ma(t){var e,n=ba[t];if(n)return n;for(e=t-1;!ba[e];)e--;for(n={line:(n=ba[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return ba[t]=n,n}function La(r,t){var e=ma(r),n=ma(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Ca(r){fa<va||(fa>va&&(va=fa,da=[]),da.push(r))}function Ea(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Aa(){var r,t;return r=fa,hp()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Ta())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=bp())!==u&&(a=hp())!==u&&(i=Ta())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=bp())!==u&&(a=hp())!==u&&(i=Ta())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Jp),columnList:Qp(rb),ast:n}}(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=r,r=t):(fa=r,r=u),r}function ga(){var t;return(t=function(){var t,e,n,o,s,a,c,l,f;t=fa,(e=El())!==u&&hp()!==u&&(n=Fl())!==u&&hp()!==u&&(o=gi())!==u?(pa=t,p=e,b=n,(v=o)&&v.forEach(r=>Jp.add(`${p}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:p.toLowerCase(),keyword:b.toLowerCase(),name:v}},t=e):(fa=t,t=u);var p,b,v;t===u&&(t=fa,(e=El())!==u&&hp()!==u&&(n=rp())!==u&&hp()!==u?((o=up())===u&&(o=null),o!==u&&hp()!==u?(s=fa,"if"===r.substr(fa,2).toLowerCase()?(a=r.substr(fa,2),fa+=2):(a=u,0===ya&&Ca(i)),a!==u&&(c=hp())!==u&&(l=sf())!==u?s=a=[a,c,l]:(fa=s,s=u),s===u&&(s=null),s!==u&&(a=hp())!==u&&(c=Cc())!==u&&(l=hp())!==u?("cascade"===r.substr(fa,7).toLowerCase()?(f=r.substr(fa,7),fa+=7):(f=u,0===ya&&Ca(W)),f===u&&("restrict"===r.substr(fa,8).toLowerCase()?(f=r.substr(fa,8),fa+=8):(f=u,0===ya&&Ca(Y))),f===u&&(f=null),f!==u?(pa=t,e=function(r,t,e,n,o,u){return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),prefix:e,name:o,options:u&&[{type:"origin",value:u}]}}}(e,n,o,0,c,f),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u));return t}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,s,a,i,c,l;r=fa,(t=_l())!==u&&hp()!==u?((e=Sl())===u&&(e=null),e!==u&&hp()!==u&&Fl()!==u&&hp()!==u?((n=ja())===u&&(n=null),n!==u&&hp()!==u&&(o=gi())!==u&&hp()!==u&&(s=function(){var r,t,e,n,o,s,a,i,c;if(r=fa,(t=cp())!==u)if(hp()!==u)if((e=Na())!==u){for(n=[],o=fa,(s=hp())!==u&&(a=ap())!==u&&(i=hp())!==u&&(c=Na())!==u?o=s=[s,a,i,c]:(fa=o,o=u);o!==u;)n.push(o),o=fa,(s=hp())!==u&&(a=ap())!==u&&(i=hp())!==u&&(c=Na())!==u?o=s=[s,a,i,c]:(fa=o,o=u);n!==u&&(o=hp())!==u&&(s=lp())!==u?(pa=r,t=Yp(e,n),r=t):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;return r}())!==u&&hp()!==u?((a=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=ei())!==u){for(e=[],n=fa,(o=hp())!==u?((s=ap())===u&&(s=null),s!==u&&(a=hp())!==u&&(i=ei())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u?((s=ap())===u&&(s=null),s!==u&&(a=hp())!==u&&(i=ei())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())===u&&(a=null),a!==u&&hp()!==u?((i=Ol())===u&&(i=Nl()),i===u&&(i=null),i!==u&&hp()!==u?((c=$l())===u&&(c=null),c!==u&&hp()!==u?((l=Sa())===u&&(l=null),l!==u?(pa=r,f=t,p=e,b=n,d=s,y=a,h=i,w=c,m=l,(v=o)&&v.forEach(r=>Jp.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:h&&h[0].toLowerCase(),as:w&&w[0].toLowerCase(),query_expr:m&&m.ast,create_definitions:d,table_options:y},...Hp()},r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);var f,p,b,v,d,y,h,w,m;r===u&&(r=fa,(t=_l())!==u&&hp()!==u?((e=Sl())===u&&(e=null),e!==u&&hp()!==u&&Fl()!==u&&hp()!==u?((n=ja())===u&&(n=null),n!==u&&hp()!==u&&(o=gi())!==u&&hp()!==u&&(s=function r(){var t,e;(t=function(){var r,t;r=fa,of()!==u&&hp()!==u&&(t=gi())!==u?(pa=r,r={type:"like",table:t}):(fa=r,r=u);return r}())===u&&(t=fa,cp()!==u&&hp()!==u&&(e=r())!==u&&hp()!==u&&lp()!==u?(pa=t,(n=e).parentheses=!0,t=n):(fa=t,t=u));var n;return t}())!==u?(pa=r,t=function(r,t,e,n,o){return n&&n.forEach(r=>Jp.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,s),r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,p,b,v,d,y,h,w,m,L,C,E;t=fa,(e=_l())!==u&&hp()!==u?(n=fa,(o=lf())!==u&&(s=hp())!==u&&(a=Nl())!==u?n=o=[o,s,a]:(fa=n,n=u),n===u&&(n=null),n!==u&&(o=hp())!==u?((s=op())===u&&(s=null),s!==u&&(a=hp())!==u?("trigger"===r.substr(fa,7).toLowerCase()?(i=r.substr(fa,7),fa+=7):(i=u,0===ya&&Ca(hr)),i!==u&&hp()!==u&&(c=Mc())!==u&&hp()!==u?("before"===r.substr(fa,6).toLowerCase()?(l=r.substr(fa,6),fa+=6):(l=u,0===ya&&Ca(wr)),l===u&&("after"===r.substr(fa,5).toLowerCase()?(l=r.substr(fa,5),fa+=5):(l=u,0===ya&&Ca(mr)),l===u&&("instead of"===r.substr(fa,10).toLowerCase()?(l=r.substr(fa,10),fa+=10):(l=u,0===ya&&Ca(Lr)))),l!==u&&hp()!==u&&(f=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Ja())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=lf())!==u&&(a=hp())!==u&&(i=Ja())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=lf())!==u&&(a=hp())!==u&&(i=Ja())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&hp()!==u?("on"===r.substr(fa,2).toLowerCase()?(p=r.substr(fa,2),fa+=2):(p=u,0===ya&&Ca(Cr)),p!==u&&hp()!==u&&(b=ji())!==u&&hp()!==u?(v=fa,(d=Dl())!==u&&(y=hp())!==u&&(h=ji())!==u?v=d=[d,y,h]:(fa=v,v=u),v===u&&(v=null),v!==u&&(d=hp())!==u?((y=function(){var t,e,n,o,s;t=fa,e=fa,"not"===r.substr(fa,3).toLowerCase()?(n=r.substr(fa,3),fa+=3):(n=u,0===ya&&Ca(Tr));n===u&&(n=null);n!==u&&(o=hp())!==u?("deferrable"===r.substr(fa,10).toLowerCase()?(s=r.substr(fa,10),fa+=10):(s=u,0===ya&&Ca(_r)),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u);e!==u&&(n=hp())!==u?("initially immediate"===r.substr(fa,19).toLowerCase()?(o=r.substr(fa,19),fa+=19):(o=u,0===ya&&Ca(Sr)),o===u&&("initially deferred"===r.substr(fa,18).toLowerCase()?(o=r.substr(fa,18),fa+=18):(o=u,0===ya&&Ca(jr))),o!==u?(pa=t,i=o,e={keyword:(a=e)&&a[0]?a[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):(fa=t,t=u)):(fa=t,t=u);var a,i;return t}())===u&&(y=null),y!==u&&(h=hp())!==u?((w=function(){var t,e,n,o;t=fa,"for"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(xr));e!==u&&hp()!==u?("each"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(Ir)),n===u&&(n=null),n!==u&&hp()!==u?("row"===r.substr(fa,3).toLowerCase()?(o=r.substr(fa,3),fa+=3):(o=u,0===ya&&Ca(Rr)),o===u&&("statement"===r.substr(fa,9).toLowerCase()?(o=r.substr(fa,9),fa+=9):(o=u,0===ya&&Ca(Nr))),o!==u?(pa=t,s=e,i=o,e={keyword:(a=n)?`${s.toLowerCase()} ${a.toLowerCase()}`:s.toLowerCase(),args:i.toLowerCase()},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var s,a,i;return t}())===u&&(w=null),w!==u&&hp()!==u?((m=function(){var r,t;r=fa,df()!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(t=sc())!==u&&hp()!==u&&lp()!==u?(pa=r,r={type:"when",cond:t,parentheses:!0}):(fa=r,r=u);return r}())===u&&(m=null),m!==u&&hp()!==u?("execute"===r.substr(fa,7).toLowerCase()?(L=r.substr(fa,7),fa+=7):(L=u,0===ya&&Ca(Er)),L!==u&&hp()!==u?("procedure"===r.substr(fa,9).toLowerCase()?(C=r.substr(fa,9),fa+=9):(C=u,0===ya&&Ca(Ar)),C===u&&("function"===r.substr(fa,8).toLowerCase()?(C=r.substr(fa,8),fa+=8):(C=u,0===ya&&Ca(z))),C!==u&&hp()!==u&&(E=Rp())!==u?(pa=t,A=s,g=i,_=f,S=b,j=v,x=y,I=w,R=m,N=C,k=E,e={type:"create",replace:n&&"or replace",constraint:c,location:(T=l)&&T.toLowerCase(),events:_,table:S,from:j&&j[2],deferrable:x,for_each:I,when:R,execute:{keyword:"execute "+N.toLowerCase(),expr:k},constraint_type:g&&g.toLowerCase(),keyword:g&&g.toLowerCase(),constraint_kw:A&&A.toLowerCase(),resource:"constraint"},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var A,g,T,_,S,j,x,I,R,N,k;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,p,b,v,d,y,h,w;t=fa,(e=_l())!==u&&hp()!==u?("extension"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(c)),n!==u&&hp()!==u?((o=ja())===u&&(o=null),o!==u&&hp()!==u?((s=Mc())===u&&(s=il()),s!==u&&hp()!==u?((a=Vl())===u&&(a=null),a!==u&&hp()!==u?(i=fa,"schema"===r.substr(fa,6).toLowerCase()?(p=r.substr(fa,6),fa+=6):(p=u,0===ya&&Ca(l)),p!==u&&(b=hp())!==u&&(v=Mc())!==u?i=p=[p,b,v]:(fa=i,i=u),i===u&&(i=il()),i===u&&(i=null),i!==u&&(p=hp())!==u?(b=fa,"version"===r.substr(fa,7).toLowerCase()?(v=r.substr(fa,7),fa+=7):(v=u,0===ya&&Ca(f)),v!==u&&(d=hp())!==u?((y=Mc())===u&&(y=il()),y!==u?b=v=[v,d,y]:(fa=b,b=u)):(fa=b,b=u),b===u&&(b=null),b!==u&&(v=hp())!==u?(d=fa,(y=Dl())!==u&&(h=hp())!==u?((w=Mc())===u&&(w=il()),w!==u?d=y=[y,h,w]:(fa=d,d=u)):(fa=d,d=u),d===u&&(d=null),d!==u?(pa=t,m=o,L=s,C=a,E=i,A=b,g=d,e={type:"create",keyword:n.toLowerCase(),if_not_exists:m,extension:Kp(L),with:C&&C[0].toLowerCase(),schema:Kp(E&&E[2].toLowerCase()),version:Kp(A&&A[2]),from:Kp(g&&g[2])},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var m,L,C,E,A,g;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,p,b,v,d,y,h,w,m;t=fa,(e=_l())!==u&&hp()!==u?((n=ep())===u&&(n=null),n!==u&&hp()!==u&&(o=rp())!==u&&hp()!==u?((s=up())===u&&(s=null),s!==u&&hp()!==u?((a=Ac())===u&&(a=null),a!==u&&hp()!==u&&(i=Hl())!==u&&hp()!==u&&(c=ji())!==u&&hp()!==u?((l=Ci())===u&&(l=null),l!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(f=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Ra())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ra())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ra())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&hp()!==u&&lp()!==u&&hp()!==u?(p=fa,(b=Vl())!==u&&(v=hp())!==u&&(d=cp())!==u&&(y=hp())!==u&&(h=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Ai())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ai())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ai())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&(w=hp())!==u&&(m=lp())!==u?p=b=[b,v,d,y,h,w,m]:(fa=p,p=u),p===u&&(p=null),p!==u&&(b=hp())!==u?(v=fa,(d=function(){var t,e,n,o;t=fa,"tablespace"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(xo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TABLESPACE"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(y=hp())!==u&&(h=Mc())!==u?v=d=[d,y,h]:(fa=v,v=u),v===u&&(v=null),v!==u&&(d=hp())!==u?((y=Ri())===u&&(y=null),y!==u&&(h=hp())!==u?(pa=t,L=e,C=n,E=o,A=s,g=a,T=i,_=c,S=l,j=f,x=p,I=v,R=y,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:L[0].toLowerCase(),index_type:C&&C.toLowerCase(),keyword:E.toLowerCase(),concurrently:A&&A.toLowerCase(),index:g,on_kw:T[0].toLowerCase(),table:_,index_using:S,index_columns:j,with:x&&x[4],with_before_where:!0,tablespace:I&&{type:"origin",value:I[2]},where:R}},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var L,C,E,A,g,T,_,S,j,x,I,R;return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=fa,(e=_l())!==u&&hp()!==u?((n=Sl())===u&&(n=jl()),n===u&&(n=null),n!==u&&hp()!==u&&function(){var t,e,n,o;t=fa,"sequence"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(jo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SEQUENCE"):(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u?((o=ja())===u&&(o=null),o!==u&&hp()!==u&&(s=ji())!==u&&hp()!==u?(a=fa,(i=$l())!==u&&(c=hp())!==u&&(l=gc())!==u?a=i=[i,c,l]:(fa=a,a=u),a===u&&(a=null),a!==u&&(i=hp())!==u?((c=function(){var r,t,e,n,o,s;if(r=fa,(t=Ia())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=Ia())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=Ia())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e,1),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())===u&&(c=null),c!==u?(pa=t,f=e,p=n,b=o,d=a,y=c,(v=s).as=d&&d[2],e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:f[0].toLowerCase(),keyword:"sequence",temporary:p&&p[0].toLowerCase(),if_not_exists:b,sequence:[v],create_definitions:y}},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var f,p,b,v,d,y;return t}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=_l())!==u&&hp()!==u?((n=function(){var t,e,n,o;t=fa,"database"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(So));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DATABASE"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(n=Gl()),n!==u&&hp()!==u?((o=ja())===u&&(o=null),o!==u&&hp()!==u&&(s=Ip())!==u&&hp()!==u?((a=function(){var r,t,e,n,o,s;if(r=fa,(t=ti())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ti())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ti())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e,1),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())===u&&(a=null),a!==u?(pa=t,e=function(r,t,e,n,o){const u=t.toLowerCase();return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r[0].toLowerCase(),keyword:u,if_not_exists:e,[u]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,s,a),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=fa,(e=_l())!==u&&hp()!==u?("domain"===r.substr(fa,6).toLowerCase()?(n=r.substr(fa,6),fa+=6):(n=u,0===ya&&Ca(L)),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u?((s=$l())===u&&(s=null),s!==u&&hp()!==u&&(a=Up())!==u&&hp()!==u?((i=Ua())===u&&(i=null),i!==u&&hp()!==u?((c=Ma())===u&&(c=null),c!==u&&hp()!==u?((l=Ka())===u&&(l=null),l!==u?(pa=t,e=function(r,t,e,n,o,u,s,a){a&&(a.type="constraint");const i=[u,s,a].filter(r=>r);return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i},...Hp()}}(e,n,o,s,a,i,c,l),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i;t=fa,(e=_l())!==u&&hp()!==u?("type"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(m)),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u&&(s=$l())!==u&&hp()!==u&&(a=Of())!==u&&hp()!==u&&cp()!==u&&hp()!==u?((i=Zi())===u&&(i=null),i!==u&&hp()!==u&&lp()!==u?(pa=t,c=e,l=n,f=o,p=s,b=a,(v=i).parentheses=!0,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:c[0].toLowerCase(),keyword:l.toLowerCase(),name:{schema:f.db,name:f.table},as:p&&p[0]&&p[0].toLowerCase(),resource:b.toLowerCase(),create_definitions:v},...Hp()},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var c,l,f,p,b,v;t===u&&(t=fa,(e=_l())!==u&&hp()!==u?("type"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(m)),n!==u&&hp()!==u&&(o=ji())!==u?(pa=t,e=function(r,t,e){return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r[0].toLowerCase(),keyword:t.toLowerCase(),name:{schema:e.db,name:e.table}}}}(e,n,o),t=e):(fa=t,t=u)):(fa=t,t=u));return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f,y,h,w,m,L,C,E,A;t=fa,(e=_l())!==u&&hp()!==u?(n=fa,(o=lf())!==u&&(s=hp())!==u&&(a=Nl())!==u?n=o=[o,s,a]:(fa=n,n=u),n===u&&(n=null),n!==u&&(o=hp())!==u?((s=jl())===u&&(s=Sl()),s===u&&(s=null),s!==u&&(a=hp())!==u?((i=Rl())===u&&(i=null),i!==u&&hp()!==u&&function(){var t,e,n,o;t=fa,"view"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(As));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="VIEW"):(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(c=ji())!==u&&hp()!==u?(l=fa,(f=cp())!==u&&(y=hp())!==u&&(h=Ec())!==u&&(w=hp())!==u&&(m=lp())!==u?l=f=[f,y,h,w,m]:(fa=l,l=u),l===u&&(l=null),l!==u&&(f=hp())!==u?(y=fa,(h=Vl())!==u&&(w=hp())!==u&&(m=cp())!==u&&(L=hp())!==u&&(C=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=xa())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=xa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=xa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&(E=hp())!==u&&(A=lp())!==u?y=h=[h,w,m,L,C,E,A]:(fa=y,y=u),y===u&&(y=null),y!==u&&(h=hp())!==u&&(w=$l())!==u&&(m=hp())!==u&&(L=ci())!==u&&(C=hp())!==u?((E=function(){var t,e,n,o,s;t=fa,(e=Vl())!==u&&hp()!==u?("cascaded"===r.substr(fa,8).toLowerCase()?(n=r.substr(fa,8),fa+=8):(n=u,0===ya&&Ca(p)),n===u&&("local"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(b))),n!==u&&hp()!==u?("check"===r.substr(fa,5).toLowerCase()?(o=r.substr(fa,5),fa+=5):(o=u,0===ya&&Ca(v)),o!==u&&hp()!==u?("OPTION"===r.substr(fa,6)?(s="OPTION",fa+=6):(s=u,0===ya&&Ca(d)),s!==u?(pa=t,e=`with ${n.toLowerCase()} check option`,t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);t===u&&(t=fa,(e=Vl())!==u&&hp()!==u?("check"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(v)),n!==u&&hp()!==u?("OPTION"===r.substr(fa,6)?(o="OPTION",fa+=6):(o=u,0===ya&&Ca(d)),o!==u?(pa=t,t=e="with check option"):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u));return t}())===u&&(E=null),E!==u?(pa=t,g=e,T=n,_=s,S=i,x=l,I=y,R=L,N=E,(j=c).view=j.table,delete j.table,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:g[0].toLowerCase(),keyword:"view",replace:T&&"or replace",temporary:_&&_[0].toLowerCase(),recursive:S&&S.toLowerCase(),columns:x&&x[2],select:R,view:j,with_options:I&&I[4],with:N}},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var g,T,_,S,j,x,I,R,N;return t}());return t}())===u&&(t=function(){var r,t,e,n;r=fa,(t=qf())!==u&&hp()!==u?((e=Fl())===u&&(e=null),e!==u&&hp()!==u&&(n=gi())!==u?(pa=r,o=t,s=e,(a=n)&&a.forEach(r=>Jp.add(`${o}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:o.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:a}},r=t):(fa=r,r=u)):(fa=r,r=u);var o,s,a;return r}())===u&&(t=function(){var r,t,e;r=fa,(t=kl())!==u&&hp()!==u&&Fl()!==u&&hp()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Li())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Li())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Li())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Jp.add(`rename::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`))),t={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"rename",table:n}},r=t):(fa=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=fa,(e=function(){var t,e,n,o;t=fa,"call"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(vu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CALL"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u&&(n=Rp())!==u?(pa=t,o=n,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"call",expr:o}},t=e):(fa=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=fa,(e=function(){var t,e,n,o;t=fa,"use"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(co));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u&&(n=Ac())!==u?(pa=t,o=n,Jp.add(`use::${o}::null`),e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"use",db:o,...Hp()}},t=e):(fa=t,t=u);var o;return t}())===u&&(t=function(){var t;(t=function(){var r,t,e,n;r=fa,(t=Al())!==u&&hp()!==u&&Fl()!==u&&hp()!==u&&(e=gi())!==u&&hp()!==u&&(n=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Fa())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Fa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Fa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=r,s=n,(o=e)&&o.length>0&&o.forEach(r=>Jp.add(`alter::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"alter",table:o,expr:s}},r=t):(fa=r,r=u);var o,s;return r}())===u&&(t=function(){var r,t,e,n,o;r=fa,(t=Al())!==u&&hp()!==u&&(e=Gl())!==u&&hp()!==u&&(n=Mc())!==u&&hp()!==u?((o=Ga())===u&&(o=Ha())===u&&(o=Ba()),o!==u?(pa=r,t=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"alter",keyword:n,schema:t,expr:e}}}(e,n,o),r=t):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,s;t=fa,(e=Al())!==u&&hp()!==u?("domain"===r.substr(fa,6).toLowerCase()?(n=r.substr(fa,6),fa+=6):(n=u,0===ya&&Ca(L)),n===u&&("type"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(m))),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u?((s=Ga())===u&&(s=Ha())===u&&(s=Ba()),s!==u?(pa=t,e=function(r,t,e){const n=r.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"alter",keyword:n,name:{schema:t.db,name:t.table},expr:e}}}(n,o,s),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l,f;t=fa,(e=Al())!==u&&hp()!==u?("function"===r.substr(fa,8).toLowerCase()?(n=r.substr(fa,8),fa+=8):(n=u,0===ya&&Ca(z)),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u?(s=fa,(a=cp())!==u&&(i=hp())!==u?((c=$a())===u&&(c=null),c!==u&&(l=hp())!==u&&(f=lp())!==u?s=a=[a,i,c,l,f]:(fa=s,s=u)):(fa=s,s=u),s===u&&(s=null),s!==u&&(a=hp())!==u?((i=Ga())===u&&(i=Ha())===u&&(i=Ba()),i!==u?(pa=t,e=function(r,t,e,n){const o=r.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const u={};return e&&e[0]&&(u.parentheses=!0),u.expr=e&&e[2],{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:u,expr:n}}}(n,o,s,i),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=Al())!==u&&hp()!==u?("aggregate"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(K)),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(s=function(){var r,t,e;r=fa,(t=ip())!==u&&(pa=r,t=[{name:"*"}]);(r=t)===u&&(r=fa,(t=$a())===u&&(t=null),t!==u&&hp()!==u&&Kl()!==u&&hp()!==u&&Ql()!==u&&hp()!==u&&(e=$a())!==u?(pa=r,t=function(r,t){const e=r||[];return e.orderby=t,e}(t,e),r=t):(fa=r,r=u),r===u&&(r=$a()));return r}())!==u&&hp()!==u&&lp()!==u&&hp()!==u?((a=Ga())===u&&(a=Ha())===u&&(a=Ba()),a!==u?(pa=t,e=function(r,t,e,n){const o=r.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"alter",keyword:o,name:{schema:t.db,name:t.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n},...Hp()}}(n,o,s,a),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}());return t}())===u&&(t=function(){var t,e,n,o;t=fa,(e=Pl())!==u&&hp()!==u?((n=function(){var t,e,n,o;t=fa,"global"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(ms));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="GLOBAL"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=fa,"session"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ls));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SESSION"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=fa,"local"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(b));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="LOCAL"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=fa,"persist"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Cs));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="PERSIST"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=fa,"persist_only"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(Es));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="PERSIST_ONLY"):(fa=t,t=u)):(fa=t,t=u);return t}()),n===u&&(n=null),n!==u&&hp()!==u&&(o=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Tp())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Tp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Tp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=t,s=n,(a=o).keyword=s,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"set",keyword:s,expr:a}},t=e):(fa=t,t=u)):(fa=t,t=u);var s,a;return t}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=function(){var t,e,n,o;t=fa,"lock"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(sr));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u?((n=Fl())===u&&(n=null),n!==u&&hp()!==u&&(o=gi())!==u&&hp()!==u?((s=function(){var t,e,n,o;t=fa,"in"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(Xr));e!==u&&hp()!==u?("access share"===r.substr(fa,12).toLowerCase()?(n=r.substr(fa,12),fa+=12):(n=u,0===ya&&Ca(Qr)),n===u&&("row share"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(Kr)),n===u&&("row exclusive"===r.substr(fa,13).toLowerCase()?(n=r.substr(fa,13),fa+=13):(n=u,0===ya&&Ca(zr)),n===u&&("share update exclusive"===r.substr(fa,22).toLowerCase()?(n=r.substr(fa,22),fa+=22):(n=u,0===ya&&Ca(Zr)),n===u&&("share row exclusive"===r.substr(fa,19).toLowerCase()?(n=r.substr(fa,19),fa+=19):(n=u,0===ya&&Ca(Jr)),n===u&&("exclusive"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(ir)),n===u&&("access exclusive"===r.substr(fa,16).toLowerCase()?(n=r.substr(fa,16),fa+=16):(n=u,0===ya&&Ca(rt)),n===u&&("share"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(tt))))))))),n!==u&&hp()!==u?("mode"===r.substr(fa,4).toLowerCase()?(o=r.substr(fa,4),fa+=4):(o=u,0===ya&&Ca(et)),o!==u?(pa=t,e={mode:`in ${n.toLowerCase()} mode`},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(s=null),s!==u&&hp()!==u?("nowait"===r.substr(fa,6).toLowerCase()?(a=r.substr(fa,6),fa+=6):(a=u,0===ya&&Ca(nt)),a===u&&(a=null),a!==u?(pa=t,i=n,l=s,f=a,(c=o)&&c.forEach(r=>Jp.add(`lock::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var i,c,l,f;return t}())===u&&(t=function(){var t,e,n;t=fa,(e=Cl())!==u&&hp()!==u?("tables"===r.substr(fa,6).toLowerCase()?(n=r.substr(fa,6),fa+=6):(n=u,0===ya&&Ca(ot)),n!==u?(pa=t,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"show",keyword:"tables"}},t=e):(fa=t,t=u)):(fa=t,t=u);t===u&&(t=fa,(e=Cl())!==u&&hp()!==u&&(n=Op())!==u?(pa=t,o=n,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"show",keyword:"var",var:o}},t=e):(fa=t,t=u));var o;return t}())===u&&(t=function(){var t,e,n,o;t=fa,(e=function(){var t,e,n,o;t=fa,"deallocate"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(Io));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DEALLOCATE"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u?("prepare"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(ut)),n===u&&(n=null),n!==u&&hp()!==u?((o=Mc())===u&&(o=Jl()),o!==u?(pa=t,s=n,a=o,e={tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"deallocate",keyword:s,expr:{type:"default",value:a}}},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var s,a;return t}()),t}function Ta(){var t;return(t=Sa())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=fa,(t=hp())!==u?((e=oi())===u&&(e=null),e!==u&&hp()!==u&&Tl()!==u&&hp()!==u&&(n=gi())!==u&&hp()!==u&&Pl()!==u&&hp()!==u&&(o=qi())!==u&&hp()!==u?((s=mi())===u&&(s=null),s!==u&&hp()!==u?((a=Ri())===u&&(a=null),a!==u&&hp()!==u?((i=Yi())===u&&(i=null),i!==u?(pa=r,t=function(r,t,e,n,o,u){const s={},a=r=>{const{server:t,db:e,schema:n,as:o,table:u,join:a}=r,i=a?"select":"update",c=[t,e,n].filter(Boolean).join(".")||null;e&&(s[u]=c),u&&Jp.add(`${i}::${c}::${u}`)};return t&&t.forEach(a),n&&n.forEach(a),e&&e.forEach(r=>{if(r.table){const t=Xp(r.table);Jp.add(`update::${s[t]||null}::${t}`)}rb.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Jp),columnList:Qp(rb),ast:{with:r,type:"update",table:t,set:e,from:n,where:o,returning:u}}}(e,n,o,s,a,i),r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,s,a,i,c,l;t=fa,(e=Qi())!==u&&hp()!==u?((n=Ml())===u&&(n=null),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u?((s=Xi())===u&&(s=null),s!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(a=Ec())!==u&&hp()!==u&&lp()!==u&&hp()!==u&&(i=Vi())!==u&&hp()!==u?((c=function(){var t,e,n,o;t=fa,Hl()!==u&&hp()!==u?("conflict"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Nt)),e!==u&&hp()!==u?((n=function(){var r,t;r=fa,cp()!==u&&hp()!==u&&(t=Ni())!==u&&hp()!==u&&lp()!==u?(pa=r,r={type:"column",expr:t,parentheses:!0}):(fa=r,r=u);return r}())===u&&(n=null),n!==u&&hp()!==u&&(o=function(){var t,e,n,o,s;t=fa,"do"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(It));e!==u&&hp()!==u?("nothing"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(Rt)),n!==u?(pa=t,t=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(fa=t,t=u)):(fa=t,t=u);t===u&&(t=fa,"do"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(It)),e!==u&&hp()!==u&&(n=Tl())!==u&&hp()!==u&&Pl()!==u&&hp()!==u&&(o=qi())!==u&&hp()!==u?((s=Ri())===u&&(s=null),s!==u?(pa=t,t=e={keyword:"do",expr:{type:"update",set:o,where:s}}):(fa=t,t=u)):(fa=t,t=u));return t}())!==u?(pa=t,t={type:"conflict",keyword:"on",target:n,action:o}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(c=null),c!==u&&hp()!==u?((l=Yi())===u&&(l=null),l!==u?(pa=t,e=function(r,t,e,n,o,u,s){if(t&&(Jp.add(`insert::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>rb.add(`insert::${r}::${t}`))}return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r,table:[t],columns:n,values:o,partition:e,conflict:u,returning:s}}}(e,o,s,a,i,c,l),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=fa,(t=Qi())!==u&&hp()!==u?((e=Ol())===u&&(e=null),e!==u&&hp()!==u?((n=Ml())===u&&(n=null),n!==u&&hp()!==u&&(o=ji())!==u&&hp()!==u?((s=Xi())===u&&(s=null),s!==u&&hp()!==u&&(a=Vi())!==u&&hp()!==u?((i=Yi())===u&&(i=null),i!==u?(pa=r,t=function(r,t,e,n,o,u,s){n&&(Jp.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),rb.add(`insert::${n.table}::(.*)`),n.as=null);const a=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:a,returning:s}}}(t,e,n,o,s,a,i),r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o;r=fa,(t=xl())!==u&&hp()!==u?((e=gi())===u&&(e=null),e!==u&&hp()!==u&&(n=mi())!==u&&hp()!==u?((o=Ri())===u&&(o=null),o!==u?(pa=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,schema:e,as:n,table:o,join:u}=r,s=u?"select":"delete",a=[t,e].filter(Boolean).join(".")||null;o&&Jp.add(`${s}::${a}::${o}`),u||rb.add(`delete::${o}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0,...Hp()}]}return{tableList:Array.from(Jp),columnList:Qp(rb),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(t=ga())===u&&(t=function(){var r,t;r=[],t=gp();for(;t!==u;)r.push(t),t=gp();return r}()),t}function _a(){var t,e;return t=fa,function(){var t,e,n,o;t=fa,"union"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Mo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u?((e=Jl())===u&&(e=null),e!==u?(pa=t,t=e?"union all":"union"):(fa=t,t=u)):(fa=t,t=u),t}function Sa(){var r,t,e,n,o,s,a,i;if(r=fa,(t=ni())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=_a())!==u&&(a=hp())!==u&&(i=ni())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=_a())!==u&&(a=hp())!==u&&(i=ni())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u&&(n=hp())!==u?((o=Fi())===u&&(o=null),o!==u&&(s=hp())!==u?((a=Bi())===u&&(a=null),a!==u?(pa=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&n.value&&n.value.length>0&&(r._limit=n),{tableList:Array.from(Jp),columnList:Qp(rb),ast:r}}(t,e,o,a)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)}else fa=r,r=u;return r}function ja(){var t,e;return t=fa,"if"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(i)),e!==u&&hp()!==u&&af()!==u&&hp()!==u&&sf()!==u?(pa=t,t=e="IF NOT EXISTS"):(fa=t,t=u),t}function xa(){var t,e,n;return t=fa,"check_option"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(y)),e!==u&&hp()!==u&&zf()!==u&&hp()!==u?("cascaded"===r.substr(fa,8).toLowerCase()?(n=r.substr(fa,8),fa+=8):(n=u,0===ya&&Ca(p)),n===u&&("local"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(b))),n!==u?(pa=t,t=e={type:"check_option",value:n,symbol:"="}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"security_barrier"===r.substr(fa,16).toLowerCase()?(e=r.substr(fa,16),fa+=16):(e=u,0===ya&&Ca(h)),e===u&&("security_invoker"===r.substr(fa,16).toLowerCase()?(e=r.substr(fa,16),fa+=16):(e=u,0===ya&&Ca(w))),e!==u&&hp()!==u&&zf()!==u&&hp()!==u&&(n=al())!==u?(pa=t,t=e=function(r,t){return{type:r.toLowerCase(),value:t.value?"true":"false",symbol:"="}}(e,n)):(fa=t,t=u)),t}function Ia(){var t;return(t=function(){var t,e,n,o,s,a;return t=fa,"increment"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(C)),e!==u&&hp()!==u?((n=Ql())===u&&(n=null),n!==u&&hp()!==u&&(o=pl())!==u?(pa=t,s=e,a=o,t=e={resource:"sequence",prefix:n?s.toLowerCase()+" by":s.toLowerCase(),value:a}):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=fa,"minvalue"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(E)),e!==u&&hp()!==u&&(n=pl())!==u?(pa=t,t=e=A(e,n)):(fa=t,t=u),t===u&&(t=fa,"no"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(g)),e!==u&&hp()!==u?("minvalue"===r.substr(fa,8).toLowerCase()?(n=r.substr(fa,8),fa+=8):(n=u,0===ya&&Ca(E)),n!==u?(pa=t,t=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(fa=t,t=u)):(fa=t,t=u)),t}())===u&&(t=function(){var t,e,n;return t=fa,"maxvalue"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(T)),e!==u&&hp()!==u&&(n=pl())!==u?(pa=t,t=e=A(e,n)):(fa=t,t=u),t===u&&(t=fa,"no"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(g)),e!==u&&hp()!==u?("maxvalue"===r.substr(fa,8).toLowerCase()?(n=r.substr(fa,8),fa+=8):(n=u,0===ya&&Ca(T)),n!==u?(pa=t,t=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(fa=t,t=u)):(fa=t,t=u)),t}())===u&&(t=function(){var t,e,n,o,s,a;return t=fa,"start"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(_)),e!==u&&hp()!==u?((n=Vl())===u&&(n=null),n!==u&&hp()!==u&&(o=pl())!==u?(pa=t,s=e,a=o,t=e={resource:"sequence",prefix:n?s.toLowerCase()+" with":s.toLowerCase(),value:a}):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=fa,"cache"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(S)),e!==u&&hp()!==u&&(n=pl())!==u?(pa=t,t=e=A(e,n)):(fa=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=fa,"no"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(g)),e===u&&(e=null),e!==u&&hp()!==u?("cycle"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(j)),n!==u?(pa=t,t=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(t=function(){var t,e,n;return t=fa,"owned"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(x)),e!==u&&hp()!==u&&Ql()!==u&&hp()!==u?("none"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(I)),n!==u?(pa=t,t=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"owned"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(x)),e!==u&&hp()!==u&&Ql()!==u&&hp()!==u&&(n=Cc())!==u?(pa=t,t=e={resource:"sequence",prefix:"owned by",value:n}):(fa=t,t=u)),t}()),t}function Ra(){var t,e,n,o,s,a,i,c,l,f,p,b,v,d;return t=fa,(e=sc())!==u&&hp()!==u?((n=Ua())===u&&(n=null),n!==u&&hp()!==u?((o=Ac())===u&&(o=null),o!==u&&hp()!==u?((s=zl())===u&&(s=Zl()),s===u&&(s=null),s!==u&&hp()!==u?(a=fa,"nulls"===r.substr(fa,5).toLowerCase()?(i=r.substr(fa,5),fa+=5):(i=u,0===ya&&Ca(R)),i!==u&&(c=hp())!==u?("first"===r.substr(fa,5).toLowerCase()?(l=r.substr(fa,5),fa+=5):(l=u,0===ya&&Ca(N)),l===u&&("last"===r.substr(fa,4).toLowerCase()?(l=r.substr(fa,4),fa+=4):(l=u,0===ya&&Ca(k))),l!==u?a=i=[i,c,l]:(fa=a,a=u)):(fa=a,a=u),a===u&&(a=null),a!==u?(pa=t,f=e,p=n,b=o,v=s,d=a,t=e={...f,collate:p,opclass:b,order_by:v&&v.toLowerCase(),nulls:d&&`${d[0].toLowerCase()} ${d[2].toLowerCase()}`}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Na(){var r;return(r=Oa())===u&&(r=Ya())===u&&(r=Va())===u&&(r=Xa()),r}function ka(){var t,e,n,o;return(t=function(){var r,t,e;r=fa,(t=sl())===u&&(t=ul());t!==u&&hp()!==u?((e=Ma())===u&&(e=null),e!==u?(pa=r,o=e,(n=t)&&!n.value&&(n.value="null"),r=t={default_val:o,nullable:n}):(fa=r,r=u)):(fa=r,r=u);var n,o;r===u&&(r=fa,(t=Ma())!==u&&hp()!==u?((e=sl())===u&&(e=ul()),e===u&&(e=null),e!==u?(pa=r,t=function(r,t){return t&&!t.value&&(t.value="null"),{default_val:r,nullable:t}}(t,e),r=t):(fa=r,r=u)):(fa=r,r=u));return r}())===u&&(t=fa,"auto_increment"===r.substr(fa,14).toLowerCase()?(e=r.substr(fa,14),fa+=14):(e=u,0===ya&&Ca(O)),e!==u&&(pa=t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=fa,"unique"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(U)),e!==u&&hp()!==u?("key"===r.substr(fa,3).toLowerCase()?(n=r.substr(fa,3),fa+=3):(n=u,0===ya&&Ca(M)),n===u&&(n=null),n!==u?(pa=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"primary"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(D)),e===u&&(e=null),e!==u&&hp()!==u?("key"===r.substr(fa,3).toLowerCase()?(n=r.substr(fa,3),fa+=3):(n=u,0===ya&&Ca(M)),n!==u?(pa=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=Lp())!==u&&(pa=t,e={comment:e}),(t=e)===u&&(t=fa,(e=Ua())!==u&&(pa=t,e={collate:e}),(t=e)===u&&(t=fa,(e=function(){var t,e,n;t=fa,"column_format"===r.substr(fa,13).toLowerCase()?(e=r.substr(fa,13),fa+=13):(e=u,0===ya&&Ca(P));e!==u&&hp()!==u?("fixed"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca($)),n===u&&("dynamic"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(F)),n===u&&("default"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(G)))),n!==u?(pa=t,e={type:"column_format",value:n.toLowerCase()},t=e):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={column_format:e}),(t=e)===u&&(t=fa,(e=function(){var t,e,n;t=fa,"storage"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(H));e!==u&&hp()!==u?("disk"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(B)),n===u&&("memory"===r.substr(fa,6).toLowerCase()?(n=r.substr(fa,6),fa+=6):(n=u,0===ya&&Ca(q))),n!==u?(pa=t,e={type:"storage",value:n.toLowerCase()},t=e):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={storage:e}),(t=e)===u&&(t=fa,(e=za())!==u&&(pa=t,e={reference_definition:e}),(t=e)===u&&(t=fa,(e=ri())!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u&&(o=Ic())!==u?(pa=t,t=e=function(r,t,e){return{character_set:{type:r,value:e,symbol:t}}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u)))))))))),t}function Oa(){var r,t,e,n,o,s,a;return r=fa,(t=Cc())!==u&&hp()!==u&&(e=Up())!==u&&hp()!==u?((n=function(){var r,t,e,n,o,s;if(r=fa,(t=ka())!==u)if(hp()!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ka())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ka())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;return r}())===u&&(n=null),n!==u?(pa=r,o=t,s=e,a=n,rb.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:s,resource:"column",...a||{}}):(fa=r,r=u)):(fa=r,r=u),r}function Ua(){var t,e,n;return t=fa,function(){var t,e,n,o;t=fa,"collate"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Mr));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="COLLATE"):(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u?((e=zf())===u&&(e=null),e!==u&&hp()!==u&&(n=Ac())!==u?(pa=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(fa=t,t=u)):(fa=t,t=u),t}function Ma(){var r,t;return r=fa,ml()!==u&&hp()!==u&&(t=sc())!==u?(pa=r,r={type:"default",value:t}):(fa=r,r=u),r}function Da(){var t,e;return t=fa,(e=ef())===u&&("out"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(V)),e===u&&("variadic"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(X)),e===u&&("inout"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Q))))),e!==u&&(pa=t,e=e.toUpperCase()),t=e}function Pa(){var r,t,e,n;return r=fa,(t=Da())===u&&(t=null),t!==u&&hp()!==u&&(e=Up())!==u?(pa=r,r=t={mode:t,type:e}):(fa=r,r=u),r===u&&(r=fa,(t=Da())===u&&(t=null),t!==u&&hp()!==u&&(e=Mc())!==u&&hp()!==u&&(n=Up())!==u?(pa=r,r=t=function(r,t,e){return{mode:r,name:t,type:e}}(t,e,n)):(fa=r,r=u)),r}function $a(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Pa())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Pa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Pa())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Fa(){var r;return(r=function(){var r,t,e,n;r=fa,(t=Zf())!==u&&hp()!==u?((e=Jf())===u&&(e=null),e!==u&&hp()!==u&&(n=Oa())!==u?(pa=r,o=e,s=n,t={action:"add",...s,keyword:o,resource:"column",type:"alter"},r=t):(fa=r,r=u)):(fa=r,r=u);var o,s;return r}())===u&&(r=function(){var r,t;r=fa,Zf()!==u&&hp()!==u&&(t=Xa())!==u?(pa=r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(fa=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=fa,El()!==u&&hp()!==u?((t=Jf())===u&&(t=null),t!==u&&hp()!==u&&(e=Cc())!==u?(pa=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=fa,(t=Zf())!==u&&hp()!==u&&(e=Ya())!==u?(pa=r,n=e,t={action:"add",type:"alter",...n},r=t):(fa=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e;r=fa,(t=Zf())!==u&&hp()!==u&&(e=Va())!==u?(pa=r,n=e,t={action:"add",type:"alter",...n},r=t):(fa=r,r=u);var n;return r}())===u&&(r=Ga())===u&&(r=qa())===u&&(r=Wa()),r}function Ga(){var r,t,e,n,o;return r=fa,kl()!==u&&hp()!==u?((t=Ll())===u&&(t=$l()),t===u&&(t=null),t!==u&&hp()!==u&&(e=Ac())!==u?(pa=r,o=e,r={action:"rename",type:"alter",resource:"table",keyword:(n=t)&&n[0].toLowerCase(),table:o}):(fa=r,r=u)):(fa=r,r=u),r}function Ha(){var t,e,n;return t=fa,"owner"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Z)),e!==u&&hp()!==u&&Ll()!==u&&hp()!==u?((n=Ac())===u&&("current_role"===r.substr(fa,12).toLowerCase()?(n=r.substr(fa,12),fa+=12):(n=u,0===ya&&Ca(J)),n===u&&("current_user"===r.substr(fa,12).toLowerCase()?(n=r.substr(fa,12),fa+=12):(n=u,0===ya&&Ca(rr)),n===u&&("session_user"===r.substr(fa,12).toLowerCase()?(n=r.substr(fa,12),fa+=12):(n=u,0===ya&&Ca(tr))))),n!==u?(pa=t,t=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(fa=t,t=u)):(fa=t,t=u),t}function Ba(){var r,t;return r=fa,Pl()!==u&&hp()!==u&&Gl()!==u&&hp()!==u&&(t=Ac())!==u?(pa=r,r={action:"set",type:"alter",resource:"table",keyword:"schema",table:t}):(fa=r,r=u),r}function qa(){var t,e,n,o;return t=fa,"algorithm"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(er)),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u?("default"===r.substr(fa,7).toLowerCase()?(o=r.substr(fa,7),fa+=7):(o=u,0===ya&&Ca(G)),o===u&&("instant"===r.substr(fa,7).toLowerCase()?(o=r.substr(fa,7),fa+=7):(o=u,0===ya&&Ca(nr)),o===u&&("inplace"===r.substr(fa,7).toLowerCase()?(o=r.substr(fa,7),fa+=7):(o=u,0===ya&&Ca(or)),o===u&&("copy"===r.substr(fa,4).toLowerCase()?(o=r.substr(fa,4),fa+=4):(o=u,0===ya&&Ca(ur))))),o!==u?(pa=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Wa(){var t,e,n,o;return t=fa,"lock"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(sr)),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u?("default"===r.substr(fa,7).toLowerCase()?(o=r.substr(fa,7),fa+=7):(o=u,0===ya&&Ca(G)),o===u&&("none"===r.substr(fa,4).toLowerCase()?(o=r.substr(fa,4),fa+=4):(o=u,0===ya&&Ca(I)),o===u&&("shared"===r.substr(fa,6).toLowerCase()?(o=r.substr(fa,6),fa+=6):(o=u,0===ya&&Ca(ar)),o===u&&("exclusive"===r.substr(fa,9).toLowerCase()?(o=r.substr(fa,9),fa+=9):(o=u,0===ya&&Ca(ir))))),o!==u?(pa=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Ya(){var r,t,e,n,o,s,a,i;return r=fa,(t=rp())===u&&(t=tp()),t!==u&&hp()!==u?((e=Oc())===u&&(e=null),e!==u&&hp()!==u?((n=Ci())===u&&(n=null),n!==u&&hp()!==u&&(o=si())!==u&&hp()!==u?((s=Ei())===u&&(s=null),s!==u&&hp()!==u?(pa=r,a=n,i=s,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:a,resource:"index",index_options:i}):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r}function Va(){var t,e,n,o,s,a,i,c,l;return t=fa,(e=function(){var t,e,n,o;t=fa,"fulltext"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Os));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="FULLTEXT"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=fa,"spatial"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Us));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SPATIAL"):(fa=t,t=u)):(fa=t,t=u);return t}()),e!==u&&hp()!==u?((n=rp())===u&&(n=tp()),n===u&&(n=null),n!==u&&hp()!==u?((o=Oc())===u&&(o=null),o!==u&&hp()!==u&&(s=si())!==u&&hp()!==u?((a=Ei())===u&&(a=null),a!==u&&hp()!==u?(pa=t,i=e,l=a,t=e={index:o,definition:s,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Xa(){var t;return(t=function(){var t,e,n,o,s,a;t=fa,(e=Qa())===u&&(e=null);e!==u&&hp()!==u?("primary key"===r.substr(fa,11).toLowerCase()?(n=r.substr(fa,11),fa+=11):(n=u,0===ya&&Ca(cr)),n!==u&&hp()!==u?((o=Ci())===u&&(o=null),o!==u&&hp()!==u&&(s=si())!==u&&hp()!==u?((a=Ei())===u&&(a=null),a!==u?(pa=t,c=n,l=o,f=s,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var r,t,e,n,o,s,a,i;r=fa,(t=Qa())===u&&(t=null);t!==u&&hp()!==u&&(e=ep())!==u&&hp()!==u?((n=rp())===u&&(n=tp()),n===u&&(n=null),n!==u&&hp()!==u?((o=Oc())===u&&(o=null),o!==u&&hp()!==u?((s=Ci())===u&&(s=null),s!==u&&hp()!==u&&(a=si())!==u&&hp()!==u?((i=Ei())===u&&(i=null),i!==u?(pa=r,l=e,f=n,p=o,b=s,v=a,d=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:d},r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);var c,l,f,p,b,v,d;return r}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=Qa())===u&&(e=null);e!==u&&hp()!==u?("foreign key"===r.substr(fa,11).toLowerCase()?(n=r.substr(fa,11),fa+=11):(n=u,0===ya&&Ca(lr)),n!==u&&hp()!==u?((o=Oc())===u&&(o=null),o!==u&&hp()!==u&&(s=si())!==u&&hp()!==u?((a=za())===u&&(a=null),a!==u?(pa=t,c=n,l=o,f=s,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var i,c,l,f,p;return t}())===u&&(t=Ka()),t}function Qa(){var r,t,e,n;return r=fa,(t=op())!==u&&hp()!==u?((e=Ac())===u&&(e=null),e!==u?(pa=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(fa=r,r=u)):(fa=r,r=u),r}function Ka(){var t,e,n,o,s,a,i;return t=fa,(e=Qa())===u&&(e=null),e!==u&&hp()!==u?("check"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(v)),n!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(o=ac())!==u&&hp()!==u&&lp()!==u?(pa=t,a=n,i=o,t=e={constraint:(s=e)&&s.constraint,definition:[i],constraint_type:a.toLowerCase(),keyword:s&&s.keyword,resource:"constraint"}):(fa=t,t=u)):(fa=t,t=u),t}function za(){var t,e,n,o,s,a,i,c,l,f;return t=fa,(e=function(){var t,e,n,o;t=fa,"references"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca($s));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REFERENCES"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u&&(n=ji())!==u&&hp()!==u&&(o=si())!==u&&hp()!==u?("match full"===r.substr(fa,10).toLowerCase()?(s=r.substr(fa,10),fa+=10):(s=u,0===ya&&Ca(fr)),s===u&&("match partial"===r.substr(fa,13).toLowerCase()?(s=r.substr(fa,13),fa+=13):(s=u,0===ya&&Ca(pr)),s===u&&("match simple"===r.substr(fa,12).toLowerCase()?(s=r.substr(fa,12),fa+=12):(s=u,0===ya&&Ca(br)))),s===u&&(s=null),s!==u&&hp()!==u?((a=Za())===u&&(a=null),a!==u&&hp()!==u?((i=Za())===u&&(i=null),i!==u?(pa=t,c=s,l=a,f=i,t=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=Za())!==u&&(pa=t,e={on_action:[e]}),t=e),t}function Za(){var t,e,n,o;return t=fa,Hl()!==u&&hp()!==u?((e=xl())===u&&(e=Tl()),e!==u&&hp()!==u&&(n=function(){var t,e,n;t=fa,(e=Yf())!==u&&hp()!==u&&cp()!==u&&hp()!==u?((n=Zi())===u&&(n=null),n!==u&&hp()!==u&&lp()!==u?(pa=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(fa=t,t=u)):(fa=t,t=u);t===u&&(t=fa,"restrict"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Y)),e===u&&("cascade"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(W)),e===u&&("set null"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(vr)),e===u&&("no action"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(dr)),e===u&&("set default"===r.substr(fa,11).toLowerCase()?(e=r.substr(fa,11),fa+=11):(e=u,0===ya&&Ca(yr)),e===u&&(e=Yf()))))),e!==u&&(pa=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(pa=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(fa=t,t=u)):(fa=t,t=u),t}function Ja(){var t,e,n,o,s,a,i;return t=fa,(e=Il())===u&&(e=xl())===u&&(e=qf()),e!==u&&(pa=t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===u&&(t=fa,(e=Tl())!==u&&hp()!==u?(n=fa,"of"===r.substr(fa,2).toLowerCase()?(o=r.substr(fa,2),fa+=2):(o=u,0===ya&&Ca(gr)),o!==u&&(s=hp())!==u&&(a=Ni())!==u?n=o=[o,s,a]:(fa=n,n=u),n===u&&(n=null),n!==u?(pa=t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):(fa=t,t=u)):(fa=t,t=u)),t}function ri(){var t,e,n;return t=fa,"character"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(kr)),e!==u&&hp()!==u?("set"===r.substr(fa,3).toLowerCase()?(n=r.substr(fa,3),fa+=3):(n=u,0===ya&&Ca(Or)),n!==u?(pa=t,t=e="CHARACTER SET"):(fa=t,t=u)):(fa=t,t=u),t}function ti(){var t,e,n,o,s,a,i,c,l;return t=fa,(e=ml())===u&&(e=null),e!==u&&hp()!==u?((n=ri())===u&&("charset"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(Ur)),n===u&&("collate"===r.substr(fa,7).toLowerCase()?(n=r.substr(fa,7),fa+=7):(n=u,0===ya&&Ca(Mr)))),n!==u&&hp()!==u?((o=zf())===u&&(o=null),o!==u&&hp()!==u&&(s=Ic())!==u?(pa=t,i=n,c=o,l=s,t=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function ei(){var t,e,n,o,s,a,i,c,l;return t=fa,"auto_increment"===r.substr(fa,14).toLowerCase()?(e=r.substr(fa,14),fa+=14):(e=u,0===ya&&Ca(O)),e===u&&("avg_row_length"===r.substr(fa,14).toLowerCase()?(e=r.substr(fa,14),fa+=14):(e=u,0===ya&&Ca(Dr)),e===u&&("key_block_size"===r.substr(fa,14).toLowerCase()?(e=r.substr(fa,14),fa+=14):(e=u,0===ya&&Ca(Pr)),e===u&&("max_rows"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca($r)),e===u&&("min_rows"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Fr)),e===u&&("stats_sample_pages"===r.substr(fa,18).toLowerCase()?(e=r.substr(fa,18),fa+=18):(e=u,0===ya&&Ca(Gr))))))),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u&&(o=pl())!==u?(pa=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=ti())===u&&(t=fa,(e=np())===u&&("connection"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(Hr))),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u&&(o=il())!==u?(pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"compression"===r.substr(fa,11).toLowerCase()?(e=r.substr(fa,11),fa+=11):(e=u,0===ya&&Ca(Br)),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u?(o=fa,39===r.charCodeAt(fa)?(s="'",fa++):(s=u,0===ya&&Ca(qr)),s!==u?("zlib"===r.substr(fa,4).toLowerCase()?(a=r.substr(fa,4),fa+=4):(a=u,0===ya&&Ca(Wr)),a===u&&("lz4"===r.substr(fa,3).toLowerCase()?(a=r.substr(fa,3),fa+=3):(a=u,0===ya&&Ca(Yr)),a===u&&("none"===r.substr(fa,4).toLowerCase()?(a=r.substr(fa,4),fa+=4):(a=u,0===ya&&Ca(I)))),a!==u?(39===r.charCodeAt(fa)?(i="'",fa++):(i=u,0===ya&&Ca(qr)),i!==u?o=s=[s,a,i]:(fa=o,o=u)):(fa=o,o=u)):(fa=o,o=u),o!==u?(pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"engine"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Vr)),e!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u&&(o=Mc())!==u?(pa=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u)))),t}function ni(){var t,e,n,o,s,a,i;return t=fa,(e=gl())!==u&&(n=hp())!==u?(59===r.charCodeAt(fa)?(o=";",fa++):(o=u,0===ya&&Ca(st)),o!==u?(pa=t,t=e={type:"select",...Hp()}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=ci())===u&&(t=fa,e=fa,40===r.charCodeAt(fa)?(n="(",fa++):(n=u,0===ya&&Ca(at)),n!==u&&(o=hp())!==u&&(s=ni())!==u&&(a=hp())!==u?(41===r.charCodeAt(fa)?(i=")",fa++):(i=u,0===ya&&Ca(it)),i!==u?e=n=[n,o,s,a,i]:(fa=e,e=u)):(fa=e,e=u),e!==u&&(pa=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function oi(){var r,t,e,n,o,s,a,i,c;if(r=fa,Vl()!==u)if(hp()!==u)if((t=ui())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=ui())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=ui())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;return r===u&&(r=fa,hp()!==u&&Vl()!==u&&(t=hp())!==u&&(e=Rl())!==u&&(n=hp())!==u&&(o=ui())!==u?(pa=r,(c=o).recursive=!0,r=[c]):(fa=r,r=u)),r}function ui(){var r,t,e,n,o;return r=fa,(t=il())===u&&(t=Mc()),t!==u&&hp()!==u?((e=si())===u&&(e=null),e!==u&&hp()!==u&&$l()!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=Ta())!==u&&hp()!==u&&lp()!==u?(pa=r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e,...Hp()}):(fa=r,r=u)):(fa=r,r=u),r}function si(){var r,t;return r=fa,cp()!==u&&hp()!==u&&(t=Ni())!==u&&hp()!==u&&lp()!==u?(pa=r,r=t):(fa=r,r=u),r}function ai(){var r,t,e,n,o;return r=fa,(t=rf())!==u&&hp()!==u&&Hl()!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(e=Ni())!==u&&hp()!==u&&lp()!==u?(pa=r,n=t,o=e,console.lo,r=t={type:n+" ON",columns:o}):(fa=r,r=u),r===u&&(r=fa,(t=rf())===u&&(t=null),t!==u&&(pa=r,t=function(r){return{type:r}}(t)),r=t),r}function ii(){var t,e,n,o,s,a,i,c,l,f,p,b,v,d;return t=fa,hp()!==u&&gl()!==u&&wp()!==u?((e=function(){var r,t,e,n,o,s;if(r=fa,(t=li())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=li())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=li())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())===u&&(e=null),e!==u&&hp()!==u?((n=ai())===u&&(n=null),n!==u&&hp()!==u&&(o=fi())!==u&&hp()!==u?((s=wi())===u&&(s=null),s!==u&&hp()!==u?((a=mi())===u&&(a=null),a!==u&&hp()!==u?((i=wi())===u&&(i=null),i!==u&&hp()!==u?((c=Ri())===u&&(c=null),c!==u&&hp()!==u?((l=function(){var r,t,e;r=fa,(t=Xl())!==u&&hp()!==u&&Ql()!==u&&hp()!==u&&(e=Zi())!==u?(pa=r,t={columns:e.value},r=t):(fa=r,r=u);return r}())===u&&(l=null),l!==u&&hp()!==u?((f=function(){var t,e;t=fa,function(){var t,e,n,o;t=fa,"having"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Bo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(e=ac())!==u?(pa=t,t=e):(fa=t,t=u);return t}())===u&&(f=null),f!==u&&hp()!==u?((p=Fi())===u&&(p=null),p!==u&&hp()!==u?((b=Bi())===u&&(b=null),b!==u&&hp()!==u?((v=function(){var t,e;t=fa,function(){var t,e,n,o;t=fa,"window"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(qo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=ki())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=ki())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=ki())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=t,t={keyword:"window",type:"window",expr:e}):(fa=t,t=u);return t}())===u&&(v=null),v!==u&&hp()!==u?((d=wi())===u&&(d=null),d!==u?(pa=t,t=function(r,t,e,n,o,u,s,a,i,c,l,f,p){if(n&&u||n&&p||u&&p||n&&u&&p)throw new Error("A given SQL statement can contain at most one INTO clause");return o&&o.forEach(r=>r.table&&Jp.add(`select::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{type:"select",options:r,distinct:t,columns:e,into:{...n||u||p||{},position:(n?"column":u&&"from")||p&&"end"},from:o,where:s,groupby:a,having:i,orderby:c,limit:l,window:f,...Hp()}}(e,n,o,s,a,i,c,l,f,p,b,v,d)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function ci(){var t,e,n,o,s,a;return t=fa,hp()!==u?((e=oi())===u&&(e=null),e!==u&&(n=ii())!==u?(pa=t,t=ct(e,n)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,hp()!==u?((e=oi())===u&&(e=null),e!==u&&(n=hp())!==u?(40===r.charCodeAt(fa)?(o="(",fa++):(o=u,0===ya&&Ca(at)),o===u&&(o=null),o!==u&&(s=ii())!==u&&hp()!==u?(41===r.charCodeAt(fa)?(a=")",fa++):(a=u,0===ya&&Ca(it)),a===u&&(a=null),a!==u?(pa=t,t=ct(e,s)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)),t}function li(){var t,e;return t=fa,(e=function(){var t;"sql_calc_found_rows"===r.substr(fa,19).toLowerCase()?(t=r.substr(fa,19),fa+=19):(t=u,0===ya&&Ca(Fs));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr(fa,9).toLowerCase()?(t=r.substr(fa,9),fa+=9):(t=u,0===ya&&Ca(Gs));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr(fa,12).toLowerCase()?(t=r.substr(fa,12),fa+=12):(t=u,0===ya&&Ca(Hs));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr(fa,14).toLowerCase()?(t=r.substr(fa,14),fa+=14):(t=u,0===ya&&Ca(qs));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr(fa,16).toLowerCase()?(t=r.substr(fa,16),fa+=16):(t=u,0===ya&&Ca(Bs));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr(fa,17).toLowerCase()?(t=r.substr(fa,17),fa+=17):(t=u,0===ya&&Ca(Ws));return t}())),e!==u&&(pa=t,e=e),t=e}function fi(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Jl())===u&&(t=fa,(e=ip())!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t===u&&(t=ip())),t!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=di())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=di())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){rb.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null,...Hp()};return t&&t.length>0?Yp(e,t):[e]}(0,e)):(fa=r,r=u)}else fa=r,r=u;if(r===u)if(r=fa,(t=di())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=di())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=di())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function pi(){var r,t;return r=fa,fp()!==u&&hp()!==u?((t=pl())===u&&(t=il()),t!==u&&hp()!==u&&pp()!==u?(pa=r,r={brackets:!0,index:t}):(fa=r,r=u)):(fa=r,r=u),r}function bi(){var r,t,e,n,o,s;if(r=fa,(t=pi())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=pi())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=pi())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e,1)):(fa=r,r=u)}else fa=r,r=u;return r}function vi(){var r,t,e,n,o;return r=fa,(t=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=sc())!==u){for(e=[],n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf())===u&&(s=yp()),s!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf())===u&&(s=yp()),s!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);e!==u?(pa=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=qp(t[e][1],n,o)}return o}(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&hp()!==u?((e=bi())===u&&(e=null),e!==u?(pa=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(fa=r,r=u)):(fa=r,r=u),r}function di(){var r,t,e,n,o,s,a,i,c,l,f;if(r=fa,(t=Lc())!==u&&(pa=r,t={expr:t,as:null,...Hp()}),(r=t)===u){if(r=fa,(t=vi())!==u)if((e=hp())!==u)if((n=el())!==u)if((o=hp())!==u){for(s=[],a=fa,(i=hp())!==u?((c=vc())===u&&(c=yc()),c!==u&&(l=hp())!==u&&(f=vi())!==u?a=i=[i,c,l,f]:(fa=a,a=u)):(fa=a,a=u);a!==u;)s.push(a),a=fa,(i=hp())!==u?((c=vc())===u&&(c=yc()),c!==u&&(l=hp())!==u&&(f=vi())!==u?a=i=[i,c,l,f]:(fa=a,a=u)):(fa=a,a=u);s!==u&&(a=hp())!==u?((i=hi())===u&&(i=null),i!==u?(pa=r,r=t=function(r,t,e,n){return{...t,as:n,type:"cast",expr:r,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]},...Hp()}}(t,n,s,i)):(fa=r,r=u)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;r===u&&(r=fa,(t=Rc())!==u&&(e=hp())!==u&&(n=sp())!==u?(o=fa,(s=Rc())!==u&&(a=hp())!==u&&(i=sp())!==u?o=s=[s,a,i]:(fa=o,o=u),o===u&&(o=null),o!==u&&(s=hp())!==u&&(a=ip())!==u?(pa=r,r=t=function(r,t){const e=t&&t[0];let n;e&&(n=r,r=e),rb.add(`select::${r?r.value:null}::(.*)`);return{expr:{type:"column_ref",table:r,schema:n,column:"*"},as:null,...Hp()}}(t,o)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,t=fa,(e=Rc())!==u&&(n=hp())!==u&&(o=sp())!==u?t=e=[e,n,o]:(fa=t,t=u),t===u&&(t=null),t!==u&&(e=hp())!==u&&(n=ip())!==u?(pa=r,r=t=function(r){const t=r&&r[0]||null;return rb.add(`select::${t?t.value:null}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null,...Hp()}}(t)):(fa=r,r=u),r===u&&(r=fa,(t=Sc())!==u&&(e=hp())!==u?((n=sp())===u&&(n=null),n!==u?(pa=fa,(o=(o=function(r,t){if(t)return!0}(0,n))?u:void 0)!==u&&(s=hp())!==u?((a=hi())===u&&(a=null),a!==u?(pa=r,r=t=function(r,t,e){return rb.add("select::null::"+r.value),{type:"expr",expr:{type:"column_ref",table:null,column:{expr:r}},as:e,...Hp()}}(t,0,a)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=vi())!==u&&(e=hp())!==u?((n=hi())===u&&(n=null),n!==u?(pa=r,r=t={type:"expr",expr:t,as:n,...Hp()}):(fa=r,r=u)):(fa=r,r=u)))))}return r}function yi(){var r,t,e;return r=fa,(t=$l())===u&&(t=null),t!==u&&hp()!==u&&(e=gc())!==u?(pa=r,r=t=e):(fa=r,r=u),r}function hi(){var r,t,e;return r=fa,(t=$l())!==u&&hp()!==u&&(e=gc())!==u?(pa=r,r=t=e):(fa=r,r=u),r===u&&(r=fa,(t=$l())===u&&(t=null),t!==u&&hp()!==u&&(e=Ac())!==u?(pa=r,r=t=e):(fa=r,r=u)),r}function wi(){var t,e,n;return t=fa,Ml()!==u&&hp()!==u&&(e=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=kp())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=kp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=kp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=t,t={keyword:"var",type:"into",expr:e}):(fa=t,t=u),t===u&&(t=fa,Ml()!==u&&hp()!==u?("outfile"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(lt)),e===u&&("dumpfile"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(ft))),e===u&&(e=null),e!==u&&hp()!==u?((n=il())===u&&(n=Ac()),n!==u?(pa=t,t={keyword:e,type:"into",expr:n}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)),t}function mi(){var r,t;return r=fa,Dl()!==u&&hp()!==u&&(t=gi())!==u?(pa=r,r=t):(fa=r,r=u),r}function Li(){var r,t,e;return r=fa,(t=ji())!==u&&hp()!==u&&Ll()!==u&&hp()!==u&&(e=ji())!==u?(pa=r,r=t=[t,e]):(fa=r,r=u),r}function Ci(){var t,e;return t=fa,Yl()!==u&&hp()!==u?("btree"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(pt)),e===u&&("hash"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(bt)),e===u&&("gist"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(vt)),e===u&&("gin"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(dt))))),e!==u?(pa=t,t={keyword:"using",type:e.toLowerCase()}):(fa=t,t=u)):(fa=t,t=u),t}function Ei(){var r,t,e,n,o,s;if(r=fa,(t=Ai())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=Ai())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=Ai())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Ai(){var t,e,n,o,s,a;return t=fa,(e=function(){var t,e,n,o;t=fa,"key_block_size"===r.substr(fa,14).toLowerCase()?(e=r.substr(fa,14),fa+=14):(e=u,0===ya&&Ca(Pr));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="KEY_BLOCK_SIZE"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u?((n=zf())===u&&(n=null),n!==u&&hp()!==u&&(o=pl())!==u?(pa=t,s=n,a=o,t=e={type:e.toLowerCase(),symbol:s,expr:a}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=Mc())!==u&&hp()!==u&&(n=zf())!==u&&hp()!==u?((o=pl())===u&&(o=Ac()),o!==u?(pa=t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=Ci())===u&&(t=fa,"with"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(yt)),e!==u&&hp()!==u?("parser"===r.substr(fa,6).toLowerCase()?(n=r.substr(fa,6),fa+=6):(n=u,0===ya&&Ca(ht)),n!==u&&hp()!==u&&(o=Mc())!==u?(pa=t,t=e={type:"with parser",expr:o}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"visible"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(wt)),e===u&&("invisible"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(mt))),e!==u&&(pa=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=Lp())))),t}function gi(){var r,t,e,n;if(r=fa,(t=_i())!==u){for(e=[],n=Ti();n!==u;)e.push(n),n=Ti();e!==u?(pa=r,r=t=Lt(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Ti(){var r,t,e;return r=fa,hp()!==u&&(t=ap())!==u&&hp()!==u&&(e=_i())!==u?(pa=r,r=e):(fa=r,r=u),r===u&&(r=fa,hp()!==u&&(t=function(){var r,t,e,n,o,s,a,i,c,l,f;if(r=fa,(t=Si())!==u)if(hp()!==u)if((e=_i())!==u)if(hp()!==u)if((n=Yl())!==u)if(hp()!==u)if(cp()!==u)if(hp()!==u)if((o=Ic())!==u){for(s=[],a=fa,(i=hp())!==u&&(c=ap())!==u&&(l=hp())!==u&&(f=Ic())!==u?a=i=[i,c,l,f]:(fa=a,a=u);a!==u;)s.push(a),a=fa,(i=hp())!==u&&(c=ap())!==u&&(l=hp())!==u&&(f=Ic())!==u?a=i=[i,c,l,f]:(fa=a,a=u);s!==u&&(a=hp())!==u&&(i=lp())!==u?(pa=r,p=t,v=o,d=s,(b=e).join=p,b.using=Yp(v,d),r=t=b):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;var p,b,v,d;r===u&&(r=fa,(t=Si())!==u&&hp()!==u&&(e=_i())!==u&&hp()!==u?((n=Ii())===u&&(n=null),n!==u?(pa=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=Si())===u&&(t=_a()),t!==u&&hp()!==u&&(e=cp())!==u&&hp()!==u?((n=Sa())===u&&(n=gi()),n!==u&&hp()!==u&&lp()!==u&&hp()!==u?((o=hi())===u&&(o=null),o!==u&&(s=hp())!==u?((a=Ii())===u&&(a=null),a!==u?(pa=r,t=function(r,t,e,n){return Array.isArray(t)&&(t={type:"tables",expr:t}),t.parentheses=!0,{expr:t,as:e,join:r,on:n,...Hp()}}(t,n,o,a),r=t):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)));return r}())!==u?(pa=r,r=t):(fa=r,r=u)),r}function _i(){var t,e,n,o,s,a,i,c,l,f,p,b;return t=fa,(e=function(){var t;"dual"===r.substr(fa,4).toLowerCase()?(t=r.substr(fa,4),fa+=4):(t=u,0===ya&&Ca(Is));return t}())!==u&&(pa=t,e={type:"dual"}),(t=e)===u&&(t=fa,(e=Ki())!==u&&hp()!==u?((n=yi())===u&&(n=null),n!==u?(pa=t,t=e={expr:{type:"values",values:e},as:n,...Hp()}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"lateral"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ct)),e===u&&(e=null),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u?((o=Sa())===u&&(o=Ki()),o!==u&&hp()!==u&&(s=lp())!==u&&(a=hp())!==u?((i=yi())===u&&(i=null),i!==u?(pa=t,t=e=function(r,t,e){return Array.isArray(t)&&(t={type:"values",values:t}),t.parentheses=!0,{prefix:r,expr:t,as:e,...Hp()}}(e,o,i)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"lateral"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ct)),e===u&&(e=null),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=gi())!==u&&hp()!==u&&(s=lp())!==u&&(a=hp())!==u?((i=yi())===u&&(i=null),i!==u?(pa=t,t=e=function(r,t,e){return{prefix:r,expr:t={type:"tables",expr:t,parentheses:!0},as:e,...Hp()}}(e,o,i)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"lateral"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ct)),e===u&&(e=null),e!==u&&hp()!==u&&(n=Zc())!==u&&hp()!==u?((o=hi())===u&&(o=null),o!==u?(pa=t,t=e=function(r,t,e){return{prefix:r,type:"expr",expr:t,as:e}}(e,n,o)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=ji())!==u&&hp()!==u?("tablesample"===r.substr(fa,11).toLowerCase()?(n=r.substr(fa,11),fa+=11):(n=u,0===ya&&Ca(Et)),n!==u&&hp()!==u&&(o=Zc())!==u&&hp()!==u?(s=fa,"repeatable"===r.substr(fa,10).toLowerCase()?(a=r.substr(fa,10),fa+=10):(a=u,0===ya&&Ca(At)),a!==u&&(i=hp())!==u&&(c=cp())!==u&&(l=hp())!==u&&(f=pl())!==u&&(p=hp())!==u&&(b=lp())!==u?s=a=[a,i,c,l,f,p,b]:(fa=s,s=u),s===u&&(s=null),s!==u&&(a=hp())!==u?((i=hi())===u&&(i=null),i!==u?(pa=t,t=e=function(r,t,e,n){return{...r,as:n,tablesample:{expr:t,repeatable:e&&e[4]},...Hp()}}(e,o,s,i)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=ji())!==u&&hp()!==u?((n=hi())===u&&(n=null),n!==u?(pa=t,t=e=function(r,t){return"var"===r.type?(r.as=t,Object.assign(r,{...Hp()}),r):{...r,as:t,...Hp()}}(e,n)):(fa=t,t=u)):(fa=t,t=u))))))),t}function Si(){var t,e,n,o;return t=fa,(e=function(){var t,e,n,o;t=fa,"left"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Ro));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(n=hp())!==u?((o=ql())===u&&(o=null),o!==u&&hp()!==u&&Bl()!==u?(pa=t,t=e="LEFT JOIN"):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=function(){var t,e,n,o;t=fa,"right"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(No));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(n=hp())!==u?((o=ql())===u&&(o=null),o!==u&&hp()!==u&&Bl()!==u?(pa=t,t=e="RIGHT JOIN"):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=function(){var t,e,n,o;t=fa,"full"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(ko));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(n=hp())!==u?((o=ql())===u&&(o=null),o!==u&&hp()!==u&&Bl()!==u?(pa=t,t=e="FULL JOIN"):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"cross"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(gt)),e!==u&&(n=hp())!==u&&(o=Bl())!==u?(pa=t,t=e="CROSS JOIN"):(fa=t,t=u),t===u&&(t=fa,e=fa,(n=function(){var t,e,n,o;t=fa,"inner"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Oo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(o=hp())!==u?e=n=[n,o]:(fa=e,e=u),e===u&&(e=null),e!==u&&(n=Bl())!==u?(pa=t,t=e="INNER JOIN"):(fa=t,t=u))))),t}function ji(){var r,t,e,n,o,s,a,i,c;return r=fa,(t=Ac())!==u?(e=fa,(n=hp())!==u&&(o=sp())!==u&&(s=hp())!==u&&(a=Ac())!==u?e=n=[n,o,s,a]:(fa=e,e=u),e!==u?(n=fa,(o=hp())!==u&&(s=sp())!==u&&(a=hp())!==u&&(i=Ac())!==u?n=o=[o,s,a,i]:(fa=n,n=u),n!==u?(pa=r,r=t=function(r,t,e){const n={db:null,table:r,...Hp()};return null!==e&&(n.db=r,n.schema=t[3],n.table=e[3]),n}(t,e,n)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=Ac())!==u&&(e=hp())!==u&&(n=sp())!==u&&(o=hp())!==u&&(s=ip())!==u?(pa=r,r=t={db:t,table:"*",...Hp()}):(fa=r,r=u),r===u&&(r=fa,(t=Ac())!==u?(e=fa,(n=hp())!==u&&(o=sp())!==u&&(s=hp())!==u&&(a=Ac())!==u?e=n=[n,o,s,a]:(fa=e,e=u),e===u&&(e=null),e!==u?(pa=r,r=t=function(r,t){const e={db:null,table:r,...Hp()};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=kp())!==u&&(pa=r,(c=t).db=null,c.table=c.name,t=c),r=t))),r}function xi(){var r,t,e,n,o,s,a,i;if(r=fa,(t=sc())!==u){for(e=[],n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf()),s!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf()),s!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=qp(t[r][1],n,t[r][3]);return n}(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Ii(){var r,t;return r=fa,Hl()!==u&&hp()!==u&&(t=ac())!==u?(pa=r,r=t):(fa=r,r=u),r}function Ri(){var t,e;return t=fa,function(){var t,e,n,o;t=fa,"where"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca($o));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(e=ac())!==u?(pa=t,t=e):(fa=t,t=u),t}function Ni(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Cc())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Cc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Cc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function ki(){var r,t,e;return r=fa,(t=Mc())!==u&&hp()!==u&&$l()!==u&&hp()!==u&&(e=Oi())!==u?(pa=r,r=t={name:t,as_window_specification:e}):(fa=r,r=u),r}function Oi(){var r,t;return(r=Mc())===u&&(r=fa,cp()!==u&&hp()!==u?((t=function(){var r,t,e,n;r=fa,(t=$i())===u&&(t=null);t!==u&&hp()!==u?((e=Fi())===u&&(e=null),e!==u&&hp()!==u?((n=function(){var r,t,e,n,o;r=fa,(t=Gf())!==u&&hp()!==u?((e=Ui())===u&&(e=Mi()),e!==u?(pa=r,r=t={type:"rows",expr:e}):(fa=r,r=u)):(fa=r,r=u);r===u&&(r=fa,(t=Gf())!==u&&hp()!==u&&(e=tf())!==u&&hp()!==u&&(n=Mi())!==u&&hp()!==u&&cf()!==u&&hp()!==u&&(o=Ui())!==u?(pa=r,t=qp(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),r=t):(fa=r,r=u));return r}())===u&&(n=null),n!==u?(pa=r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u);return r}())===u&&(t=null),t!==u&&hp()!==u&&lp()!==u?(pa=r,r={window_specification:t||{},parentheses:!0}):(fa=r,r=u)):(fa=r,r=u)),r}function Ui(){var t,e,n,o;return t=fa,(e=Pi())!==u&&hp()!==u?("following"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(Tt)),n!==u?(pa=t,(o=e).value+=" FOLLOWING",t=e=o):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=Di()),t}function Mi(){var t,e,n,o,s;return t=fa,(e=Pi())!==u&&hp()!==u?("preceding"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(_t)),n===u&&("following"===r.substr(fa,9).toLowerCase()?(n=r.substr(fa,9),fa+=9):(n=u,0===ya&&Ca(Tt))),n!==u?(pa=t,s=n,(o=e).value+=" "+s.toUpperCase(),t=e=o):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=Di()),t}function Di(){var t,e,n;return t=fa,"current"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(St)),e!==u&&hp()!==u?("row"===r.substr(fa,3).toLowerCase()?(n=r.substr(fa,3),fa+=3):(n=u,0===ya&&Ca(Rr)),n!==u?(pa=t,t=e={type:"origin",value:"current row",...Hp()}):(fa=t,t=u)):(fa=t,t=u),t}function Pi(){var t,e;return t=fa,"unbounded"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(jt)),e!==u&&(pa=t,e={type:"origin",value:e.toUpperCase(),...Hp()}),(t=e)===u&&(t=pl()),t}function $i(){var r,t;return r=fa,Ul()!==u&&hp()!==u&&Ql()!==u&&hp()!==u&&(t=Ni())!==u?(pa=r,r=t.map(r=>({type:"expr",expr:r}))):(fa=r,r=u),r}function Fi(){var r,t;return r=fa,Kl()!==u&&hp()!==u&&Ql()!==u&&hp()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Gi())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Gi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Gi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=r,r=t):(fa=r,r=u),r}function Gi(){var t,e,n,o,s,a,i;return t=fa,(e=sc())!==u&&hp()!==u?((n=Zl())===u&&(n=zl()),n===u&&(n=null),n!==u&&hp()!==u?(o=fa,"nulls"===r.substr(fa,5).toLowerCase()?(s=r.substr(fa,5),fa+=5):(s=u,0===ya&&Ca(R)),s!==u&&(a=hp())!==u?("first"===r.substr(fa,5).toLowerCase()?(i=r.substr(fa,5),fa+=5):(i=u,0===ya&&Ca(N)),i===u&&("last"===r.substr(fa,4).toLowerCase()?(i=r.substr(fa,4),fa+=4):(i=u,0===ya&&Ca(k))),i===u&&(i=null),i!==u?o=s=[s,a,i]:(fa=o,o=u)):(fa=o,o=u),o===u&&(o=null),o!==u?(pa=t,t=e=function(r,t,e){const n={expr:r,type:t};return n.nulls=e&&[e[0],e[2]].filter(r=>r).join(" "),n}(e,n,o)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Hi(){var r;return(r=pl())===u&&(r=kp())===u&&(r=Fc()),r}function Bi(){var t,e,n,o,s,a,i;return t=fa,e=fa,(n=function(){var t,e,n,o;t=fa,"limit"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Wo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(o=hp())!==u?((s=Hi())===u&&(s=Jl()),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u),e===u&&(e=null),e!==u&&(n=hp())!==u?(o=fa,(s=function(){var t,e,n,o;t=fa,"offset"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Yo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="OFFSET"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(a=hp())!==u&&(i=Hi())!==u?o=s=[s,a,i]:(fa=o,o=u),o===u&&(o=null),o!==u?(pa=t,t=e=function(r,t){const e=[];return r&&e.push("string"==typeof r[2]?{type:"origin",value:"all"}:r[2]),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e,...Hp()}}(e,o)):(fa=t,t=u)):(fa=t,t=u),t}function qi(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Wi())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Wi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Wi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Wi(){var t,e,n,o,s,a,i,c,l;return t=fa,e=fa,(n=Ac())!==u&&(o=hp())!==u&&(s=sp())!==u?e=n=[n,o,s]:(fa=e,e=u),e===u&&(e=null),e!==u&&(n=hp())!==u&&(o=Nc())!==u&&(s=hp())!==u?(61===r.charCodeAt(fa)?(a="=",fa++):(a=u,0===ya&&Ca(xt)),a!==u&&hp()!==u&&(i=bc())!==u?(pa=t,t=e={column:{expr:o},value:i,table:(l=e)&&l[0]}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,e=fa,(n=Ac())!==u&&(o=hp())!==u&&(s=sp())!==u?e=n=[n,o,s]:(fa=e,e=u),e===u&&(e=null),e!==u&&(n=hp())!==u&&(o=Nc())!==u&&(s=hp())!==u?(61===r.charCodeAt(fa)?(a="=",fa++):(a=u,0===ya&&Ca(xt)),a!==u&&hp()!==u&&(i=Wl())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(c=Cc())!==u&&hp()!==u&&lp()!==u?(pa=t,t=e=function(r,t,e){return{column:{expr:t},value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(fa=t,t=u)):(fa=t,t=u)),t}function Yi(){var t,e,n,o,s;return t=fa,(e=function(){var t,e,n,o;t=fa,"returning"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Lo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="RETURNING"):(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&hp()!==u?((n=fi())===u&&(n=ni()),n!==u?(pa=t,s=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===s&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null,...Hp()}]||s}):(fa=t,t=u)):(fa=t,t=u),t}function Vi(){var r;return(r=Ki())===u&&(r=ci()),r}function Xi(){var r,t,e,n,o,s,a,i,c;if(r=fa,Ul()!==u)if(hp()!==u)if((t=cp())!==u)if(hp()!==u)if((e=Mc())!==u){for(n=[],o=fa,(s=hp())!==u&&(a=ap())!==u&&(i=hp())!==u&&(c=Mc())!==u?o=s=[s,a,i,c]:(fa=o,o=u);o!==u;)n.push(o),o=fa,(s=hp())!==u&&(a=ap())!==u&&(i=hp())!==u&&(c=Mc())!==u?o=s=[s,a,i,c]:(fa=o,o=u);n!==u&&(o=hp())!==u&&(s=lp())!==u?(pa=r,r=Yp(e,n)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;return r===u&&(r=fa,Ul()!==u&&hp()!==u&&(t=zi())!==u?(pa=r,r=t):(fa=r,r=u)),r}function Qi(){var r,t;return r=fa,(t=Il())!==u&&(pa=r,t="insert"),(r=t)===u&&(r=fa,(t=Nl())!==u&&(pa=r,t="replace"),r=t),r}function Ki(){var r,t;return r=fa,Wl()!==u&&hp()!==u&&(t=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=zi())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=zi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=zi())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u?(pa=r,r=t):(fa=r,r=u),r}function zi(){var r,t;return r=fa,cp()!==u&&hp()!==u&&(t=Zi())!==u&&hp()!==u&&lp()!==u?(pa=r,r=t):(fa=r,r=u),r}function Zi(){var r,t,e,n,o,s,a,i;if(r=fa,(t=sc())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=sc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Yp(r,t),e}(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Ji(){var t,e,n;return t=fa,Wf()!==u&&hp()!==u&&(e=sc())!==u&&hp()!==u&&(n=function(){var t;(t=function(){var t,e,n,o;t=fa,"year"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Tn));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="YEAR"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"month"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(wn));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="MONTH"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"week"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(gn));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="WEEK"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"day"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(on));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DAY"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"hour"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(fn));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="HOUR"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"minute"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(hn));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="MINUTE"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"second"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Ln));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SECOND"):(fa=t,t=u)):(fa=t,t=u);return t}());return t}())!==u?(pa=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(fa=t,t=u),t===u&&(t=fa,Wf()!==u&&hp()!==u&&(e=il())!==u?(pa=t,t=function(r){return{type:"interval",expr:r,unit:""}}(e)):(fa=t,t=u)),t}function rc(){var r,t,e,n,o,s,a,i;return r=fa,vf()!==u&&hp()!==u&&(t=tc())!==u&&hp()!==u?((e=nc())===u&&(e=null),e!==u&&hp()!==u&&(n=yf())!==u&&hp()!==u?((o=vf())===u&&(o=null),o!==u?(pa=r,a=t,(i=e)&&a.push(i),r={type:"case",expr:null,args:a}):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,vf()!==u&&hp()!==u&&(t=sc())!==u&&hp()!==u&&(e=tc())!==u&&hp()!==u?((n=nc())===u&&(n=null),n!==u&&hp()!==u&&(o=yf())!==u&&hp()!==u?((s=vf())===u&&(s=null),s!==u?(pa=r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)),r}function tc(){var r,t,e,n,o,s;if(r=fa,(t=ec())!==u)if(hp()!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ec())!==u?n=o=[o,s]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ec())!==u?n=o=[o,s]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e,1)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;return r}function ec(){var t,e,n;return t=fa,df()!==u&&hp()!==u&&(e=ac())!==u&&hp()!==u&&function(){var t,e,n,o;t=fa,"then"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(hu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(n=sc())!==u?(pa=t,t={type:"when",cond:e,result:n}):(fa=t,t=u),t}function nc(){var t,e;return t=fa,function(){var t,e,n,o;t=fa,"else"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(wu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}()!==u&&hp()!==u&&(e=sc())!==u?(pa=t,t={type:"else",result:e}):(fa=t,t=u),t}function oc(){var r;return(r=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=ic())!==u){for(e=[],n=fa,(o=wp())!==u&&(s=lf())!==u&&(a=hp())!==u&&(i=ic())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=wp())!==u&&(s=lf())!==u&&(a=hp())!==u&&(i=ic())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Ot(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,s;if(r=fa,(t=vc())!==u){if(e=[],n=fa,(o=hp())!==u&&(s=wc())!==u?n=o=[o,s]:(fa=n,n=u),n!==u)for(;n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=wc())!==u?n=o=[o,s]:(fa=n,n=u);else e=u;e!==u?(pa=r,t=Bp(t,e[0][1]),r=t):(fa=r,r=u)}else fa=r,r=u;return r}()),r}function uc(){var t,e,n,o,s,a;return t=fa,(e=Rc())!==u&&hp()!==u?("->"===r.substr(fa,2)?(n="->",fa+=2):(n=u,0===ya&&Ca(kt)),n!==u&&hp()!==u&&(o=oc())!==u?(pa=fa,(zp(o)?void 0:u)!==u?(pa=t,t=e=function(r,t){return{type:"lambda",args:{value:[r]},expr:t}}(e,o)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=cp())!==u&&hp()!==u&&(n=function(){var r,t,e,n,o,s,a,i;if(r=fa,(t=Ic())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ic())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=Ic())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,t=Yp(t,e),r=t):(fa=r,r=u)}else fa=r,r=u;return r}())!==u&&hp()!==u&&(o=lp())!==u&&hp()!==u?("->"===r.substr(fa,2)?(s="->",fa+=2):(s=u,0===ya&&Ca(kt)),s!==u&&hp()!==u&&(a=oc())!==u?(pa=fa,(function(r,t){return zp(t)}(0,a)?void 0:u)!==u?(pa=t,t=e=function(r,t){return{type:"lambda",args:{value:r,parentheses:!0},expr:t}}(n,a)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)),t}function sc(){var r;return(r=uc())===u&&(r=oc())===u&&(r=Sa()),r}function ac(){var r,t,e,n,o,s,a,i;if(r=fa,(t=vi())!==u){for(e=[],n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf())===u&&(s=ap()),s!==u&&(a=hp())!==u&&(i=vi())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u?((s=cf())===u&&(s=lf())===u&&(s=ap()),s!==u&&(a=hp())!==u&&(i=vi())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=qp(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function ic(){var r,t,e,n,o,s,a,i;if(r=fa,(t=cc())!==u){for(e=[],n=fa,(o=wp())!==u&&(s=cf())!==u&&(a=hp())!==u&&(i=cc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=wp())!==u&&(s=cf())!==u&&(a=hp())!==u&&(i=cc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Ot(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function cc(){var t,e,n,o,s;return(t=lc())===u&&(t=function(){var r,t,e;r=fa,(t=function(){var r,t,e,n,o;r=fa,t=fa,(e=af())!==u&&(n=hp())!==u&&(o=sf())!==u?t=e=[e,n,o]:(fa=t,t=u);t!==u&&(pa=r,t=(s=t)[0]+" "+s[2]);var s;(r=t)===u&&(r=sf());return r}())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(e=Sa())!==u&&hp()!==u&&lp()!==u?(pa=r,n=t,(o=e).parentheses=!0,t=Bp(n,o),r=t):(fa=r,r=u);var n,o;return r}())===u&&(t=fa,(e=af())===u&&(e=fa,33===r.charCodeAt(fa)?(n="!",fa++):(n=u,0===ya&&Ca(Ut)),n!==u?(o=fa,ya++,61===r.charCodeAt(fa)?(s="=",fa++):(s=u,0===ya&&Ca(xt)),ya--,s===u?o=void 0:(fa=o,o=u),o!==u?e=n=[n,o]:(fa=e,e=u)):(fa=e,e=u)),e!==u&&(n=hp())!==u&&(o=cc())!==u?(pa=t,t=e=Bp("NOT",o)):(fa=t,t=u)),t}function lc(){var t,e,n,o,s;return t=fa,(e=bc())!==u&&hp()!==u?((n=function(){var t;(t=function(){var r,t,e,n,o,s,a;r=fa,t=[],e=fa,(n=hp())!==u&&(o=fc())!==u&&(s=hp())!==u&&(a=bc())!==u?e=n=[n,o,s,a]:(fa=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=fa,(n=hp())!==u&&(o=fc())!==u&&(s=hp())!==u&&(a=bc())!==u?e=n=[n,o,s,a]:(fa=e,e=u);else t=u;t!==u&&(pa=r,t={type:"arithmetic",tail:t});return r=t}())===u&&(t=function(){var r,t,e,n;r=fa,(t=pc())!==u&&hp()!==u&&(e=cp())!==u&&hp()!==u&&(n=Zi())!==u&&hp()!==u&&lp()!==u?(pa=r,r=t={op:t,right:n}):(fa=r,r=u);r===u&&(r=fa,(t=pc())!==u&&hp()!==u?((e=kp())===u&&(e=il())===u&&(e=Zc()),e!==u?(pa=r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(fa=r,r=u)):(fa=r,r=u));return r}())===u&&(t=function(){var r,t,e,n;r=fa,(t=function(){var r,t,e,n,o;r=fa,t=fa,(e=af())!==u&&(n=hp())!==u&&(o=tf())!==u?t=e=[e,n,o]:(fa=t,t=u);t!==u&&(pa=r,t=(s=t)[0]+" "+s[2]);var s;(r=t)===u&&(r=tf());return r}())!==u&&hp()!==u&&(e=bc())!==u&&hp()!==u&&cf()!==u&&hp()!==u&&(n=bc())!==u?(pa=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(fa=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o,s,a,i,c;r=fa,(t=nf())!==u&&(e=hp())!==u&&(n=bc())!==u?(pa=r,r=t={op:"IS",right:n}):(fa=r,r=u);r===u&&(r=fa,(t=nf())!==u&&(e=hp())!==u?(n=fa,(o=rf())!==u&&(s=hp())!==u&&(a=Dl())!==u&&(i=hp())!==u&&(c=ji())!==u?n=o=[o,s,a,i,c]:(fa=n,n=u),n!==u?(pa=r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,t=fa,(e=nf())!==u&&(n=hp())!==u&&(o=af())!==u?t=e=[e,n,o]:(fa=t,t=u),t!==u&&(e=hp())!==u&&(n=bc())!==u?(pa=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(fa=r,r=u)));return r}())===u&&(t=function(){var t,e,n,o;t=fa,(e=function(){var t,e,n,o,s;t=fa,e=fa,(n=af())!==u&&(o=hp())!==u?((s=of())===u&&(s=uf()),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u);e!==u&&(pa=t,e=(a=e)[0]+" "+a[2]);var a;(t=e)===u&&(t=of())===u&&(t=uf())===u&&(t=fa,"similar"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ht)),e!==u&&(n=hp())!==u&&(o=Ll())!==u?(pa=t,t=e="SIMILAR TO"):(fa=t,t=u),t===u&&(t=fa,(e=af())!==u&&(n=hp())!==u?("similar"===r.substr(fa,7).toLowerCase()?(o=r.substr(fa,7),fa+=7):(o=u,0===ya&&Ca(Ht)),o!==u&&(s=hp())!==u&&Ll()!==u?(pa=t,t=e="NOT SIMILAR TO"):(fa=t,t=u)):(fa=t,t=u)));return t}())!==u&&hp()!==u?((n=nl())===u&&(n=lc()),n!==u&&hp()!==u?((o=function(){var t,e,n;t=fa,"escape"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Vt));e!==u&&hp()!==u&&(n=il())!==u?(pa=t,t=e={type:"ESCAPE",value:n}):(fa=t,t=u);return t}())===u&&(o=null),o!==u?(pa=t,s=e,a=n,(i=o)&&(a.escape=i),t=e={op:s,right:a}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u);var s,a,i;return t}())===u&&(t=function(){var t,e,n;t=fa,(e=function(){var t;"!~*"===r.substr(fa,3)?(t="!~*",fa+=3):(t=u,0===ya&&Ca(Bt));t===u&&("~*"===r.substr(fa,2)?(t="~*",fa+=2):(t=u,0===ya&&Ca(qt)),t===u&&(126===r.charCodeAt(fa)?(t="~",fa++):(t=u,0===ya&&Ca(Wt)),t===u&&("!~"===r.substr(fa,2)?(t="!~",fa+=2):(t=u,0===ya&&Ca(Yt)))));return t}())!==u&&hp()!==u?((n=nl())===u&&(n=lc()),n!==u?(pa=t,t=e={op:e,right:n}):(fa=t,t=u)):(fa=t,t=u);return t}());return t}())===u&&(n=null),n!==u?(pa=t,o=e,t=e=null===(s=n)?o:"arithmetic"===s.type?Vp(o,s.tail):qp(s.op,o,s.right)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=il())===u&&(t=Cc()),t}function fc(){var t;return">="===r.substr(fa,2)?(t=">=",fa+=2):(t=u,0===ya&&Ca(Mt)),t===u&&(62===r.charCodeAt(fa)?(t=">",fa++):(t=u,0===ya&&Ca(Dt)),t===u&&("<="===r.substr(fa,2)?(t="<=",fa+=2):(t=u,0===ya&&Ca(Pt)),t===u&&("<>"===r.substr(fa,2)?(t="<>",fa+=2):(t=u,0===ya&&Ca($t)),t===u&&(60===r.charCodeAt(fa)?(t="<",fa++):(t=u,0===ya&&Ca(Ft)),t===u&&(61===r.charCodeAt(fa)?(t="=",fa++):(t=u,0===ya&&Ca(xt)),t===u&&("!="===r.substr(fa,2)?(t="!=",fa+=2):(t=u,0===ya&&Ca(Gt)))))))),t}function pc(){var r,t,e,n,o,s;return r=fa,t=fa,(e=af())!==u&&(n=hp())!==u&&(o=ef())!==u?t=e=[e,n,o]:(fa=t,t=u),t!==u&&(pa=r,t=(s=t)[0]+" "+s[2]),(r=t)===u&&(r=ef()),r}function bc(){var r,t,e,n,o,s,a,i;if(r=fa,(t=dc())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=vc())!==u&&(a=hp())!==u&&(i=dc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=vc())!==u&&(a=hp())!==u&&(i=dc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Hp()}));return Vp(r,t)}(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function vc(){var t;return 43===r.charCodeAt(fa)?(t="+",fa++):(t=u,0===ya&&Ca(Xt)),t===u&&(45===r.charCodeAt(fa)?(t="-",fa++):(t=u,0===ya&&Ca(Qt))),t}function dc(){var r,t,e,n,o,s,a,i;if(r=fa,(t=mc())!==u){for(e=[],n=fa,(o=hp())!==u?((s=yc())===u&&(s=yp()),s!==u&&(a=hp())!==u&&(i=mc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u?((s=yc())===u&&(s=yp()),s!==u&&(a=hp())!==u&&(i=mc())!==u?n=o=[o,s,a,i]:(fa=n,n=u)):(fa=n,n=u);e!==u?(pa=r,r=t=Vp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function yc(){var t;return 42===r.charCodeAt(fa)?(t="*",fa++):(t=u,0===ya&&Ca(Kt)),t===u&&(47===r.charCodeAt(fa)?(t="/",fa++):(t=u,0===ya&&Ca(zt)),t===u&&(37===r.charCodeAt(fa)?(t="%",fa++):(t=u,0===ya&&Ca(Zt)),t===u&&("||"===r.substr(fa,2)?(t="||",fa+=2):(t=u,0===ya&&Ca(Jt))))),t}function hc(){var r,t,e,n,o;return r=fa,(t=Cc())!==u&&hp()!==u?((e=pi())===u&&(e=null),e!==u?(pa=r,n=t,(o=e)&&(n.array_index=o),r=t=n):(fa=r,r=u)):(fa=r,r=u),r}function wc(){var t,e,n,o,s,a;return(t=function(){var t,e,n,o,s,a,i,c,l;t=fa,(e=hf())===u&&(e=wf());e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&(s=$l())!==u&&hp()!==u&&(a=Up())!==u&&hp()!==u&&(i=lp())!==u?(pa=t,f=o,p=a,e={type:"cast",keyword:e.toLowerCase(),expr:f,symbol:"as",target:[p]},t=e):(fa=t,t=u);var f,p;t===u&&(t=fa,(e=hf())===u&&(e=wf()),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&(s=$l())!==u&&hp()!==u&&(a=Ef())!==u&&hp()!==u&&(i=cp())!==u&&hp()!==u&&(c=bl())!==u&&hp()!==u&&lp()!==u&&hp()!==u&&(l=lp())!==u?(pa=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),t=e):(fa=t,t=u),t===u&&(t=fa,(e=hf())===u&&(e=wf()),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&(s=$l())!==u&&hp()!==u&&(a=Ef())!==u&&hp()!==u&&(i=cp())!==u&&hp()!==u&&(c=bl())!==u&&hp()!==u&&ap()!==u&&hp()!==u&&(l=bl())!==u&&hp()!==u&&lp()!==u&&hp()!==u&&lp()!==u?(pa=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),t=e):(fa=t,t=u),t===u&&(t=fa,(e=hf())===u&&(e=wf()),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&(s=$l())!==u&&hp()!==u&&(a=function(){var t;(t=function(){var t,e,n,o;t=fa,"signed"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(ju));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SIGNED"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=Af());return t}())!==u&&hp()!==u?((i=Tf())===u&&(i=null),i!==u&&hp()!==u&&(c=lp())!==u?(pa=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,i),t=e):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=cp())!==u&&hp()!==u?((n=Zc())===u&&(n=Bc())===u&&(n=qc())===u&&(n=rc())===u&&(n=Ji())===u&&(n=nl())===u&&(n=hc())===u&&(n=Fc()),n!==u&&hp()!==u&&(o=lp())!==u&&hp()!==u?((s=el())===u&&(s=null),s!==u?(pa=t,e=function(r,t){return r.parentheses=!0,t?{type:"cast",keyword:"cast",expr:r,...t}:r}(n,s),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=Zc())===u&&(e=Bc())===u&&(e=qc())===u&&(e=rc())===u&&(e=Ji())===u&&(e=nl())===u&&(e=hc())===u&&(e=Fc()),e!==u&&hp()!==u?((n=el())===u&&(n=null),n!==u?(pa=t,e=function(r,t){return t?{type:"cast",keyword:"cast",expr:r,...t}:r}(e,n),t=e):(fa=t,t=u)):(fa=t,t=u))))));return t}())===u&&(t=fa,cp()!==u&&(e=hp())!==u&&(n=ac())!==u&&(o=hp())!==u&&(s=lp())!==u?(pa=t,(a=n).parentheses=!0,t=a):(fa=t,t=u),t===u&&(t=kp())===u&&(t=fa,hp()!==u?(36===r.charCodeAt(fa)?(e="$",fa++):(e=u,0===ya&&Ca(re)),e!==u?(60===r.charCodeAt(fa)?(n="<",fa++):(n=u,0===ya&&Ca(Ft)),n!==u&&(o=pl())!==u?(62===r.charCodeAt(fa)?(s=">",fa++):(s=u,0===ya&&Ca(Dt)),s!==u?(pa=t,t={type:"origin",value:`$<${o.value}>`}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,hp()!==u?(63===r.charCodeAt(fa)?(e="?",fa++):(e=u,0===ya&&Ca(te)),e!==u?(pa=t,t={type:"origin",value:e}):(fa=t,t=u)):(fa=t,t=u)))),t}function mc(){var t,e,n,o,s;return(t=function(){var t,e,n,o,s,a,i,c;if(t=fa,(e=wc())!==u)if(hp()!==u){for(n=[],o=fa,(s=hp())!==u?("?|"===r.substr(fa,2)?(a="?|",fa+=2):(a=u,0===ya&&Ca(ee)),a===u&&("?&"===r.substr(fa,2)?(a="?&",fa+=2):(a=u,0===ya&&Ca(ne)),a===u&&(63===r.charCodeAt(fa)?(a="?",fa++):(a=u,0===ya&&Ca(te)),a===u&&("#-"===r.substr(fa,2)?(a="#-",fa+=2):(a=u,0===ya&&Ca(oe)),a===u&&("#>>"===r.substr(fa,3)?(a="#>>",fa+=3):(a=u,0===ya&&Ca(ue)),a===u&&("#>"===r.substr(fa,2)?(a="#>",fa+=2):(a=u,0===ya&&Ca(se)),a===u&&(a=dp())===u&&(a=vp())===u&&("@>"===r.substr(fa,2)?(a="@>",fa+=2):(a=u,0===ya&&Ca(ae)),a===u&&("<@"===r.substr(fa,2)?(a="<@",fa+=2):(a=u,0===ya&&Ca(ie))))))))),a!==u&&(i=hp())!==u&&(c=wc())!==u?o=s=[s,a,i,c]:(fa=o,o=u)):(fa=o,o=u);o!==u;)n.push(o),o=fa,(s=hp())!==u?("?|"===r.substr(fa,2)?(a="?|",fa+=2):(a=u,0===ya&&Ca(ee)),a===u&&("?&"===r.substr(fa,2)?(a="?&",fa+=2):(a=u,0===ya&&Ca(ne)),a===u&&(63===r.charCodeAt(fa)?(a="?",fa++):(a=u,0===ya&&Ca(te)),a===u&&("#-"===r.substr(fa,2)?(a="#-",fa+=2):(a=u,0===ya&&Ca(oe)),a===u&&("#>>"===r.substr(fa,3)?(a="#>>",fa+=3):(a=u,0===ya&&Ca(ue)),a===u&&("#>"===r.substr(fa,2)?(a="#>",fa+=2):(a=u,0===ya&&Ca(se)),a===u&&(a=dp())===u&&(a=vp())===u&&("@>"===r.substr(fa,2)?(a="@>",fa+=2):(a=u,0===ya&&Ca(ae)),a===u&&("<@"===r.substr(fa,2)?(a="<@",fa+=2):(a=u,0===ya&&Ca(ie))))))))),a!==u&&(i=hp())!==u&&(c=wc())!==u?o=s=[s,a,i,c]:(fa=o,o=u)):(fa=o,o=u);n!==u?(pa=t,l=e,e=(f=n)&&0!==f.length?Vp(l,f):l,t=e):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;var l,f;return t}())===u&&(t=fa,(e=function(){var t;33===r.charCodeAt(fa)?(t="!",fa++):(t=u,0===ya&&Ca(Ut));t===u&&(45===r.charCodeAt(fa)?(t="-",fa++):(t=u,0===ya&&Ca(Qt)),t===u&&(43===r.charCodeAt(fa)?(t="+",fa++):(t=u,0===ya&&Ca(Xt)),t===u&&(126===r.charCodeAt(fa)?(t="~",fa++):(t=u,0===ya&&Ca(Wt)))));return t}())!==u?(n=fa,(o=hp())!==u&&(s=mc())!==u?n=o=[o,s]:(fa=n,n=u),n!==u?(pa=t,t=e=Bp(e,n[1])):(fa=t,t=u)):(fa=t,t=u)),t}function Lc(){var t,e,n,o,s,a;if(t=fa,"e"===r.substr(fa,1).toLowerCase()?(e=r.charAt(fa),fa++):(e=u,0===ya&&Ca(ce)),e!==u)if(39===r.charCodeAt(fa)?(n="'",fa++):(n=u,0===ya&&Ca(qr)),n!==u)if(hp()!==u){for(o=[],s=ll();s!==u;)o.push(s),s=ll();o!==u&&(s=hp())!==u?(39===r.charCodeAt(fa)?(a="'",fa++):(a=u,0===ya&&Ca(qr)),a!==u?(pa=t,t=e={type:"origin",value:`E'${o.join("")}'`}):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;return t}function Cc(){var r,t,e,n,o,s,a,i,c,l,f,p,b;return(r=Lc())===u&&(r=fa,t=fa,(e=Ac())!==u&&(n=hp())!==u&&(o=sp())!==u?t=e=[e,n,o]:(fa=t,t=u),t===u&&(t=null),t!==u&&(e=hp())!==u&&(n=ip())!==u?(pa=r,r=t=function(r){const t=r&&r[0]||null;return rb.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*",...Hp()}}(t)):(fa=r,r=u),r===u&&(r=fa,(t=Ac())!==u?(e=fa,(n=hp())!==u&&(o=sp())!==u&&(s=hp())!==u&&(a=Ac())!==u?e=n=[n,o,s,a]:(fa=e,e=u),e!==u?(n=fa,(o=hp())!==u&&(s=sp())!==u&&(a=hp())!==u&&(i=kc())!==u?n=o=[o,s,a,i]:(fa=n,n=u),n!==u?(o=fa,(s=hp())!==u&&(a=Ua())!==u?o=s=[s,a]:(fa=o,o=u),o===u&&(o=null),o!==u?(pa=r,l=t,f=e,p=n,b=o,rb.add(`select::${l}.${f[3]}::${p[3].value}`),r=t={type:"column_ref",schema:l,table:f[3],column:{expr:p[3]},collate:b&&b[1],...Hp()}):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=Ac())!==u&&(e=hp())!==u&&(n=sp())!==u&&(o=hp())!==u&&(s=kc())!==u?(a=fa,(i=hp())!==u&&(c=Ua())!==u?a=i=[i,c]:(fa=a,a=u),a===u&&(a=null),a!==u?(pa=r,r=t=function(r,t,e){return rb.add(`select::${r}::${t.value}`),{type:"column_ref",table:r,column:{expr:t},collate:e&&e[1],...Hp()}}(t,s,a)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=kc())!==u?(e=fa,ya++,n=cp(),ya--,n===u?e=void 0:(fa=e,e=u),e!==u?(n=fa,(o=hp())!==u&&(s=Ua())!==u?n=o=[o,s]:(fa=n,n=u),n===u&&(n=null),n!==u?(pa=r,r=t=function(r,t){return rb.add("select::null::"+r.value),{type:"column_ref",table:null,column:{expr:r},collate:t&&t[1],...Hp()}}(t,n)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u))))),r}function Ec(){var r,t,e,n,o,s,a,i;if(r=fa,(t=kc())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=kc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=kc())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function Ac(){var r,t;return r=fa,(t=Mc())!==u?(pa=fa,(le(t)?u:void 0)!==u?(pa=r,r=t=t):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=_c())!==u&&(pa=r,t=t),r=t),r}function gc(){var r,t,e,n,o,s,a,i,c;return r=fa,(t=Mc())!==u?(pa=fa,(!0===Fp[t.toUpperCase()]?u:void 0)!==u?(e=fa,(n=hp())!==u&&(o=cp())!==u&&(s=hp())!==u&&(a=Ec())!==u&&(i=hp())!==u&&(c=lp())!==u?e=n=[n,o,s,a,i,c]:(fa=e,e=u),e===u&&(e=null),e!==u?(pa=r,r=t=function(r,t){return t?`${r}(${t[3].map(r=>r.value).join(", ")})`:r}(t,e)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=_c())!==u&&(pa=r,t=t),r=t),r}function Tc(){var r;return(r=Sc())===u&&(r=jc())===u&&(r=xc()),r}function _c(){var r,t;return r=fa,(t=Sc())===u&&(t=jc())===u&&(t=xc()),t!==u&&(pa=r,t=t.value),r=t}function Sc(){var t,e,n,o;if(t=fa,34===r.charCodeAt(fa)?(e='"',fa++):(e=u,0===ya&&Ca(fe)),e!==u){if(n=[],pe.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(be)),o!==u)for(;o!==u;)n.push(o),pe.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(be));else n=u;n!==u?(34===r.charCodeAt(fa)?(o='"',fa++):(o=u,0===ya&&Ca(fe)),o!==u?(pa=t,t=e={type:"double_quote_string",value:n.join("")}):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;return t}function jc(){var t,e,n,o;if(t=fa,39===r.charCodeAt(fa)?(e="'",fa++):(e=u,0===ya&&Ca(qr)),e!==u){if(n=[],ve.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(de)),o!==u)for(;o!==u;)n.push(o),ve.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(de));else n=u;n!==u?(39===r.charCodeAt(fa)?(o="'",fa++):(o=u,0===ya&&Ca(qr)),o!==u?(pa=t,t=e={type:"single_quote_string",value:n.join("")}):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;return t}function xc(){var t,e,n,o;if(t=fa,96===r.charCodeAt(fa)?(e="`",fa++):(e=u,0===ya&&Ca(ye)),e!==u){if(n=[],he.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(we)),o!==u)for(;o!==u;)n.push(o),he.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(we));else n=u;n!==u?(96===r.charCodeAt(fa)?(o="`",fa++):(o=u,0===ya&&Ca(ye)),o!==u?(pa=t,t=e={type:"backticks_quote_string",value:n.join("")}):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;return t}function Ic(){var r,t;return r=fa,(t=Mc())!==u&&(pa=r,t=me(t)),(r=t)===u&&(r=Tc()),r}function Rc(){var r,t;return r=fa,(t=Mc())!==u?(pa=fa,(le(t)?u:void 0)!==u?(pa=r,r=t={type:"default",value:t}):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=Tc()),r}function Nc(){var r,t;return r=fa,(t=Uc())!==u&&(pa=r,t=me(t)),(r=t)===u&&(r=Tc()),r}function kc(){var r,t;return r=fa,(t=Uc())!==u?(pa=fa,(le(t)?u:void 0)!==u?(pa=r,r=t={type:"default",value:t}):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=Tc()),r}function Oc(){var r,t;return r=fa,(t=Uc())!==u?(pa=fa,(le(t)?u:void 0)!==u?(pa=r,r=t=t):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=_c()),r}function Uc(){var r,t,e,n;if(r=fa,(t=Dc())!==u){for(e=[],n=$c();n!==u;)e.push(n),n=$c();e!==u?(pa=r,r=t=t+e.join("")):(fa=r,r=u)}else fa=r,r=u;return r}function Mc(){var r,t,e,n;if(r=fa,(t=Dc())!==u){for(e=[],n=Pc();n!==u;)e.push(n),n=Pc();e!==u?(pa=r,r=t=t+e.join("")):(fa=r,r=u)}else fa=r,r=u;return r}function Dc(){var t;return Le.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(Ce)),t}function Pc(){var t;return Ee.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(Ae)),t}function $c(){var t;return ge.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(Te)),t}function Fc(){var t,e,n,o;return t=fa,e=fa,58===r.charCodeAt(fa)?(n=":",fa++):(n=u,0===ya&&Ca(_e)),n!==u&&(o=Mc())!==u?e=n=[n,o]:(fa=e,e=u),e!==u&&(pa=t,e={type:"param",value:e[1]}),t=e}function Gc(){var r,t,e;return r=fa,Hl()!==u&&hp()!==u&&Tl()!==u&&hp()!==u&&(t=Yf())!==u&&hp()!==u&&cp()!==u&&hp()!==u?((e=Zi())===u&&(e=null),e!==u&&hp()!==u&&lp()!==u?(pa=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,Hl()!==u&&hp()!==u&&Tl()!==u&&hp()!==u&&(t=Yf())!==u?(pa=r,r=function(r){return{type:"on update",keyword:r}}(t)):(fa=r,r=u)),r}function Hc(){var t,e,n,o,s;return t=fa,"over"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Se)),e!==u&&hp()!==u&&(n=Oi())!==u?(pa=t,t=e={type:"window",as_window_specification:n}):(fa=t,t=u),t===u&&(t=fa,"over"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Se)),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u?((o=$i())===u&&(o=null),o!==u&&hp()!==u?((s=Fi())===u&&(s=null),s!==u&&hp()!==u&&lp()!==u?(pa=t,t=e={partitionby:o,orderby:s}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=Gc())),t}function Bc(){var t,e,n,o,s;return t=fa,(e=function(){var t,e,n,o,s,a,i,c,l;t=fa,(e=pf())===u&&(e=function(){var t,e,n,o;t=fa,"group_concat"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(iu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="GROUP_CONCAT"):(fa=t,t=u)):(fa=t,t=u);return t}());e!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=function(){var t,e;t=fa,(e=function(){var t,e;t=fa,42===r.charCodeAt(fa)?(e="*",fa++):(e=u,0===ya&&Ca(Kt));e!==u&&(pa=t,e={type:"star",value:"*"});return t=e}())!==u&&(pa=t,e={expr:e});(t=e)===u&&(t=Vc());return t}())!==u&&hp()!==u&&(o=lp())!==u&&hp()!==u?((s=Hc())===u&&(s=null),s!==u?(pa=t,t=e={type:"aggr_func",name:e,args:n,over:s}):(fa=t,t=u)):(fa=t,t=u);t===u&&(t=fa,(e=pf())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=lp())!==u&&hp()!==u?((o=Hc())===u&&(o=null),o!==u?(pa=t,e=function(r,t){return{type:"aggr_func",name:r,args:{expr:{type:"star",value:""}},over:t}}(e,o),t=e):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"percentile_cont"===r.substr(fa,15).toLowerCase()?(e=r.substr(fa,15),fa+=15):(e=u,0===ya&&Ca(Fe)),e===u&&("percentile_disc"===r.substr(fa,15).toLowerCase()?(e=r.substr(fa,15),fa+=15):(e=u,0===ya&&Ca(Ge))),e!==u&&hp()!==u&&cp()!==u&&hp()!==u?((n=pl())===u&&(n=ol()),n!==u&&hp()!==u&&(o=lp())!==u&&hp()!==u?("within"===r.substr(fa,6).toLowerCase()?(s=r.substr(fa,6),fa+=6):(s=u,0===ya&&Ca(He)),s!==u&&hp()!==u&&Xl()!==u&&hp()!==u&&(a=cp())!==u&&hp()!==u&&(i=Fi())!==u&&hp()!==u&&(c=lp())!==u&&hp()!==u?((l=Hc())===u&&(l=null),l!==u?(pa=t,e=function(r,t,e,n){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:t},within_group_orderby:e,over:n}}(e,n,i,l),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"mode"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Be)),e!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=lp())!==u&&hp()!==u?("within"===r.substr(fa,6).toLowerCase()?(o=r.substr(fa,6),fa+=6):(o=u,0===ya&&Ca(He)),o!==u&&hp()!==u&&(s=Xl())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(a=Fi())!==u&&hp()!==u&&(i=lp())!==u&&hp()!==u?((c=Hc())===u&&(c=null),c!==u?(pa=t,e=function(r,t,e){return{type:"aggr_func",name:r.toUpperCase(),args:{expr:{}},within_group_orderby:t,over:e}}(e,a,c),t=e):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u))));return t}())===u&&(e=function(){var t,e,n,o;t=fa,(e=function(){var t;(t=function(){var t,e,n,o;t=fa,"sum"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(fu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SUM"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"max"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(cu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="MAX"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"min"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(lu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="MIN"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"avg"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(pu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="AVG"):(fa=t,t=u)):(fa=t,t=u);return t}());return t}())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=bc())!==u&&hp()!==u&&lp()!==u&&hp()!==u?((o=Hc())===u&&(o=null),o!==u?(pa=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...Hp()},t=e):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(e=function(){var t,e,n,o,s,a;t=fa,e=fa,(n=Ac())!==u&&(o=hp())!==u&&(s=sp())!==u?e=n=[n,o,s]:(fa=e,e=u);e===u&&(e=null);e!==u&&(n=hp())!==u?((o=function(){var t,e,n,o;t=fa,"array_agg"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(uu));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ARRAY_AGG"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=fa,"string_agg"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(su));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="STRING_AGG"):(fa=t,t=u)):(fa=t,t=u);return t}()),o!==u&&(s=hp())!==u&&cp()!==u&&hp()!==u&&(a=Vc())!==u&&hp()!==u&&lp()!==u?(pa=t,c=o,l=a,e={type:"aggr_func",name:(i=e)?`${i[0]}.${c}`:c,args:l},t=e):(fa=t,t=u)):(fa=t,t=u);var i,c,l;return t}()),e!==u&&hp()!==u?((n=function(){var t,e,n;return t=fa,"filter"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(je)),e!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=Ri())!==u&&hp()!==u&&lp()!==u?(pa=t,t=e={keyword:"filter",parentheses:!0,where:n}):(fa=t,t=u),t}())===u&&(n=null),n!==u?(pa=t,o=e,(s=n)&&(o.filter=s),t=e=o):(fa=t,t=u)):(fa=t,t=u),t}function qc(){var t;return(t=function(){var t,e,n;t=fa,(e=function(){var t;"row_number"===r.substr(fa,10).toLowerCase()?(t=r.substr(fa,10),fa+=10):(t=u,0===ya&&Ca(Re));t===u&&("dense_rank"===r.substr(fa,10).toLowerCase()?(t=r.substr(fa,10),fa+=10):(t=u,0===ya&&Ca(Ne)),t===u&&("rank"===r.substr(fa,4).toLowerCase()?(t=r.substr(fa,4),fa+=4):(t=u,0===ya&&Ca(Oe))));return t}())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&lp()!==u&&hp()!==u&&(n=Hc())!==u?(pa=t,t=e={type:"window_func",name:e,over:n}):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s;t=fa,(e=function(){var t;"lag"===r.substr(fa,3).toLowerCase()?(t=r.substr(fa,3),fa+=3):(t=u,0===ya&&Ca(Ue));t===u&&("lead"===r.substr(fa,4).toLowerCase()?(t=r.substr(fa,4),fa+=4):(t=u,0===ya&&Ca(Me)),t===u&&("nth_value"===r.substr(fa,9).toLowerCase()?(t=r.substr(fa,9),fa+=9):(t=u,0===ya&&Ca(De))));return t}())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=Zi())!==u&&hp()!==u&&lp()!==u&&hp()!==u?((o=Wc())===u&&(o=null),o!==u&&hp()!==u&&(s=Hc())!==u?(pa=t,t=e={type:"window_func",name:e,args:n,over:s,consider_nulls:o}):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,s;t=fa,(e=function(){var t;"first_value"===r.substr(fa,11).toLowerCase()?(t=r.substr(fa,11),fa+=11):(t=u,0===ya&&Ca(xe));t===u&&("last_value"===r.substr(fa,10).toLowerCase()?(t=r.substr(fa,10),fa+=10):(t=u,0===ya&&Ca(Ie)));return t}())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(n=sc())!==u&&hp()!==u&&lp()!==u&&hp()!==u?((o=Wc())===u&&(o=null),o!==u&&hp()!==u&&(s=Hc())!==u?(pa=t,t=e={type:"window_func",name:e,args:{type:"expr_list",value:[n]},over:s,consider_nulls:o}):(fa=t,t=u)):(fa=t,t=u);return t}()),t}function Wc(){var t,e,n;return t=fa,"ignore"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Pe)),e===u&&("respect"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca($e))),e!==u&&hp()!==u?("nulls"===r.substr(fa,5).toLowerCase()?(n=r.substr(fa,5),fa+=5):(n=u,0===ya&&Ca(R)),n!==u?(pa=t,t=e=e.toUpperCase()+" NULLS"):(fa=t,t=u)):(fa=t,t=u),t}function Yc(){var r,t;return r=fa,ap()!==u&&hp()!==u&&(t=il())!==u?(pa=r,r={symbol:ke,delimiter:t}):(fa=r,r=u),r}function Vc(){var r,t,e,n,o,s,a,i,c,l,f;if(r=fa,(t=rf())===u&&(t=null),t!==u)if(hp()!==u)if((e=cp())!==u)if(hp()!==u)if((n=sc())!==u)if(hp()!==u)if((o=lp())!==u)if(hp()!==u){for(s=[],a=fa,(i=hp())!==u?((c=cf())===u&&(c=lf()),c!==u&&(l=hp())!==u&&(f=sc())!==u?a=i=[i,c,l,f]:(fa=a,a=u)):(fa=a,a=u);a!==u;)s.push(a),a=fa,(i=hp())!==u?((c=cf())===u&&(c=lf()),c!==u&&(l=hp())!==u&&(f=sc())!==u?a=i=[i,c,l,f]:(fa=a,a=u)):(fa=a,a=u);s!==u&&(a=hp())!==u?((i=Yc())===u&&(i=null),i!==u&&(c=hp())!==u?((l=Fi())===u&&(l=null),l!==u?(pa=r,r=t=function(r,t,e,n,o){const u=e.length;let s=t;s.parentheses=!0;for(let r=0;r<u;++r)s=qp(e[r][1],s,e[r][3]);return{distinct:r,expr:s,orderby:o,separator:n}}(t,n,s,i,l)):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)}else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;else fa=r,r=u;return r===u&&(r=fa,(t=rf())===u&&(t=null),t!==u&&hp()!==u&&(e=xi())!==u&&hp()!==u?((n=Yc())===u&&(n=null),n!==u&&hp()!==u?((o=Fi())===u&&(o=null),o!==u?(pa=r,r=t={distinct:t,expr:e,orderby:o,separator:n}):(fa=r,r=u)):(fa=r,r=u)):(fa=r,r=u)),r}function Xc(){var t,e,n;return t=fa,(e=function(){var t;return"both"===r.substr(fa,4).toLowerCase()?(t=r.substr(fa,4),fa+=4):(t=u,0===ya&&Ca(qe)),t===u&&("leading"===r.substr(fa,7).toLowerCase()?(t=r.substr(fa,7),fa+=7):(t=u,0===ya&&Ca(We)),t===u&&("trailing"===r.substr(fa,8).toLowerCase()?(t=r.substr(fa,8),fa+=8):(t=u,0===ya&&Ca(Ye)))),t}())===u&&(e=null),e!==u&&hp()!==u?((n=sc())===u&&(n=null),n!==u&&hp()!==u&&Dl()!==u?(pa=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(fa=t,t=u)):(fa=t,t=u),t}function Qc(){var t,e,n,o;return t=fa,"trim"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Ve)),e!==u&&hp()!==u&&cp()!==u&&hp()!==u?((n=Xc())===u&&(n=null),n!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&lp()!==u?(pa=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...Hp()}}(n,o)):(fa=t,t=u)):(fa=t,t=u),t}function Kc(){var t,e,n,o;return t=fa,"mode"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(et)),e!==u&&hp()!==u?("=>"===r.substr(fa,2)?(n="=>",fa+=2):(n=u,0===ya&&Ca(Qe)),n!==u&&hp()!==u&&(o=il())!==u?(pa=t,t=e=function(r){const t=new Set(["object","array","both"]);if(!r.value||!t.has(r.value.toLowerCase()))throw new Error((r&&r.value)+" is not valid mode in object, array and both");return r.value=r.value.toUpperCase(),{type:"mode",symbol:"=>",value:r}}(o)):(fa=t,t=u)):(fa=t,t=u),t}function zc(){var t,e,n,o,s,a,i,c,l,f;return t=fa,(e=function(){var t,e,n,o;return t=fa,"input"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Xe)),e!==u&&hp()!==u?("=>"===r.substr(fa,2)?(n="=>",fa+=2):(n=u,0===ya&&Ca(Qe)),n!==u&&hp()!==u&&(o=sc())!==u?(pa=t,t=e={type:"input",symbol:"=>",value:o}):(fa=t,t=u)):(fa=t,t=u),t}())!==u?(n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=function(){var t,e,n,o;return t=fa,"path"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Ke)),e!==u&&hp()!==u?("=>"===r.substr(fa,2)?(n="=>",fa+=2):(n=u,0===ya&&Ca(Qe)),n!==u&&hp()!==u&&(o=il())!==u?(pa=t,t=e={type:"path",symbol:"=>",value:o}):(fa=t,t=u)):(fa=t,t=u),t}())!==u?n=o=[o,s,a,i]:(fa=n,n=u),n===u&&(n=null),n!==u?(o=fa,(s=hp())!==u&&(a=ap())!==u&&(i=hp())!==u&&(c=function(){var t,e,n,o;return t=fa,"outer"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ze)),e!==u&&hp()!==u?("=>"===r.substr(fa,2)?(n="=>",fa+=2):(n=u,0===ya&&Ca(Qe)),n!==u&&hp()!==u&&(o=al())!==u?(pa=t,t=e={type:"outer",symbol:"=>",value:o}):(fa=t,t=u)):(fa=t,t=u),t}())!==u?o=s=[s,a,i,c]:(fa=o,o=u),o===u&&(o=null),o!==u?(s=fa,(a=hp())!==u&&(i=ap())!==u&&(c=hp())!==u&&(l=function(){var t,e,n,o;return t=fa,"recursive"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Ze)),e!==u&&hp()!==u?("=>"===r.substr(fa,2)?(n="=>",fa+=2):(n=u,0===ya&&Ca(Qe)),n!==u&&hp()!==u&&(o=al())!==u?(pa=t,t=e={type:"recursive",symbol:"=>",value:o}):(fa=t,t=u)):(fa=t,t=u),t}())!==u?s=a=[a,i,c,l]:(fa=s,s=u),s===u&&(s=null),s!==u?(a=fa,(i=hp())!==u&&(c=ap())!==u&&(l=hp())!==u&&(f=Kc())!==u?a=i=[i,c,l,f]:(fa=a,a=u),a===u&&(a=null),a!==u?(pa=t,t=e=function(r,t,e,n,o){return{type:"flattern",input:r,path:t&&t[3],outer:e&&e[3],recursive:n&&n[3],mode:o&&o[3]}}(e,n,o,s,a)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t}function Zc(){var t,e,n,o,s,a,i,c,l,f,p,b;return(t=Qc())===u&&(t=fa,"now"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Je)),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u?((o=Zi())===u&&(o=null),o!==u&&(s=hp())!==u&&lp()!==u&&hp()!==u?("at"===r.substr(fa,2).toLowerCase()?(a=r.substr(fa,2),fa+=2):(a=u,0===ya&&Ca(rn)),a!==u&&(i=hp())!==u&&Hf()!==u&&hp()!==u?("zone"===r.substr(fa,4).toLowerCase()?(c=r.substr(fa,4),fa+=4):(c=u,0===ya&&Ca(tn)),c!==u&&hp()!==u&&(l=il())!==u?(pa=t,f=e,p=o,(b=l).prefix="at time zone",t=e={type:"function",name:{name:[{type:"default",value:f}]},args:p||{type:"expr_list",value:[]},suffix:b,...Hp()}):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,"flatten"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(en)),e!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u&&(o=zc())!==u&&(s=hp())!==u&&lp()!==u?(pa=t,t=e=function(r,t){return{type:"flatten",name:{name:[{type:"default",value:r}]},args:t,...Hp()}}(e,o)):(fa=t,t=u),t===u&&(t=fa,(e=function(){var t;(t=rl())===u&&(t=function(){var t,e,n,o;t=fa,"current_user"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(rr));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CURRENT_USER"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"user"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(rs));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="USER"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"session_user"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(tr));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SESSION_USER"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"system_user"===r.substr(fa,11).toLowerCase()?(e=r.substr(fa,11),fa+=11):(e=u,0===ya&&Ca(ws));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SYSTEM_USER"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&("ntile"===r.substr(fa,5).toLowerCase()?(t=r.substr(fa,5),fa+=5):(t=u,0===ya&&Ca(_n)));return t}())!==u&&hp()!==u&&(n=cp())!==u&&hp()!==u?((o=Zi())===u&&(o=null),o!==u&&(s=hp())!==u&&lp()!==u&&hp()!==u?((a=Hc())===u&&(a=null),a!==u?(pa=t,t=e=function(r,t,e){return{type:"function",name:{name:[{type:"default",value:r}]},args:t||{type:"expr_list",value:[]},over:e,...Hp()}}(e,o,a)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=function(){var r,t,e,n,o;r=fa,(t=bf())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(e=Jc())!==u&&hp()!==u&&Dl()!==u&&hp()!==u?((n=Bf())===u&&(n=Wf())===u&&(n=Hf())===u&&(n=$f()),n===u&&(n=null),n!==u&&hp()!==u&&(o=sc())!==u&&hp()!==u&&lp()!==u?(pa=r,s=e,a=n,i=o,t={type:t.toLowerCase(),args:{field:s,cast_type:a,source:i},...Hp()},r=t):(fa=r,r=u)):(fa=r,r=u);var s,a,i;r===u&&(r=fa,(t=bf())!==u&&hp()!==u&&cp()!==u&&hp()!==u&&(e=Jc())!==u&&hp()!==u&&Dl()!==u&&hp()!==u&&(n=sc())!==u&&hp()!==u&&(o=lp())!==u?(pa=r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...Hp()}}(t,e,n),r=t):(fa=r,r=u));return r}())===u&&(t=fa,(e=rl())!==u&&hp()!==u?((n=Gc())===u&&(n=null),n!==u?(pa=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...Hp()}):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=Ip())!==u?(pa=fa,(function(r){return!Gp[r.name[0]&&r.name[0].value.toLowerCase()]}(e)?void 0:u)!==u&&(n=hp())!==u&&cp()!==u&&(o=hp())!==u?((s=ac())===u&&(s=null),s!==u&&hp()!==u&&lp()!==u&&(a=hp())!==u?((i=Hc())===u&&(i=null),i!==u?(pa=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),(r.name[0]&&"TIMESTAMPDIFF"===r.name[0].value.toUpperCase()||r.name[0]&&"TIMESTAMPADD"===r.name[0].value.toUpperCase())&&t.value&&t.value[0]&&(t.value[0]={type:"origin",value:t.value[0].column}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...Hp()}}(e,s,i)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)):(fa=t,t=u)))))),t}function Jc(){var t,e;return t=fa,"century"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(nn)),e===u&&("day"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(on)),e===u&&("date"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(un)),e===u&&("decade"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(sn)),e===u&&("dow"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(an)),e===u&&("doy"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(cn)),e===u&&("epoch"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ln)),e===u&&("hour"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(fn)),e===u&&("isodow"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(pn)),e===u&&("isoyear"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(bn)),e===u&&("microseconds"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(vn)),e===u&&("millennium"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(dn)),e===u&&("milliseconds"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(yn)),e===u&&("minute"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(hn)),e===u&&("month"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(wn)),e===u&&("quarter"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(mn)),e===u&&("second"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Ln)),e===u&&("timezone"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Cn)),e===u&&("timezone_hour"===r.substr(fa,13).toLowerCase()?(e=r.substr(fa,13),fa+=13):(e=u,0===ya&&Ca(En)),e===u&&("timezone_minute"===r.substr(fa,15).toLowerCase()?(e=r.substr(fa,15),fa+=15):(e=u,0===ya&&Ca(An)),e===u&&("week"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(gn)),e===u&&("year"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Tn))))))))))))))))))))))),e!==u&&(pa=t,e=e),t=e}function rl(){var t;return(t=function(){var t,e,n,o;t=fa,"current_date"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(vs));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CURRENT_DATE"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=fa,"current_time"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(ys));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CURRENT_TIME"):(fa=t,t=u)):(fa=t,t=u);return t}())===u&&(t=Yf()),t}function tl(){var t,e,n,o;return t=fa,34===r.charCodeAt(fa)?(e='"',fa++):(e=u,0===ya&&Ca(fe)),e===u&&(e=null),e!==u&&(n=Up())!==u?(34===r.charCodeAt(fa)?(o='"',fa++):(o=u,0===ya&&Ca(fe)),o===u&&(o=null),o!==u?(pa=t,t=e=function(r,t,e){if(r&&!e||!r&&e)throw new Error("double quoted not match");return r&&e&&(t.quoted='"'),t}(e,n,o)):(fa=t,t=u)):(fa=t,t=u),t}function el(){var r,t,e,n,o,s;if(r=fa,t=[],e=fa,(n=Kf())!==u&&(o=hp())!==u&&(s=tl())!==u?e=n=[n,o,s]:(fa=e,e=u),e!==u)for(;e!==u;)t.push(e),e=fa,(n=Kf())!==u&&(o=hp())!==u&&(s=tl())!==u?e=n=[n,o,s]:(fa=e,e=u);else t=u;return t!==u&&(e=hp())!==u?((n=hi())===u&&(n=null),n!==u?(pa=r,r=t={as:n,symbol:"::",target:t.map(r=>r[2])}):(fa=r,r=u)):(fa=r,r=u),r}function nl(){var t;return(t=il())===u&&(t=pl())===u&&(t=al())===u&&(t=ul())===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=Hf())===u&&(e=$f())===u&&(e=Bf())===u&&(e=Ff());if(e!==u)if(hp()!==u){if(n=fa,39===r.charCodeAt(fa)?(o="'",fa++):(o=u,0===ya&&Ca(qr)),o!==u){for(s=[],a=ll();a!==u;)s.push(a),a=ll();s!==u?(39===r.charCodeAt(fa)?(a="'",fa++):(a=u,0===ya&&Ca(qr)),a!==u?n=o=[o,s,a]:(fa=n,n=u)):(fa=n,n=u)}else fa=n,n=u;n!==u?(pa=t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;var i;if(t===u)if(t=fa,(e=Hf())===u&&(e=$f())===u&&(e=Bf())===u&&(e=Ff()),e!==u)if(hp()!==u){if(n=fa,34===r.charCodeAt(fa)?(o='"',fa++):(o=u,0===ya&&Ca(fe)),o!==u){for(s=[],a=cl();a!==u;)s.push(a),a=cl();s!==u?(34===r.charCodeAt(fa)?(a='"',fa++):(a=u,0===ya&&Ca(fe)),a!==u?n=o=[o,s,a]:(fa=n,n=u)):(fa=n,n=u)}else fa=n,n=u;n!==u?(pa=t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;return t}())===u&&(t=ol()),t}function ol(){var r,t;return r=fa,ff()!==u&&hp()!==u&&fp()!==u&&hp()!==u?((t=Zi())===u&&(t=null),t!==u&&hp()!==u&&pp()!==u?(pa=r,r={expr_list:t||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}):(fa=r,r=u)):(fa=r,r=u),r}function ul(){var t,e;return t=fa,(e=function(){var t,e,n,o;t=fa,"null"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(eo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={type:"null",value:null}),t=e}function sl(){var t,e;return t=fa,(e=function(){var t,e,n,o;t=fa,"not null"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(no));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={type:"not null",value:"not null"}),t=e}function al(){var t,e;return t=fa,(e=function(){var t,e,n,o;t=fa,"true"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(oo));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={type:"bool",value:!0}),(t=e)===u&&(t=fa,(e=function(){var t,e,n,o;t=fa,"false"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(so));e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u);return t}())!==u&&(pa=t,e={type:"bool",value:!1}),t=e),t}function il(){var t,e,n,o,s,a,i,c,l;if(t=fa,e=fa,39===r.charCodeAt(fa)?(n="'",fa++):(n=u,0===ya&&Ca(qr)),n!==u){for(o=[],s=ll();s!==u;)o.push(s),s=ll();o!==u?(39===r.charCodeAt(fa)?(s="'",fa++):(s=u,0===ya&&Ca(qr)),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u)}else fa=e,e=u;if(e!==u){if(n=[],Sn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(jn)),o!==u)for(;o!==u;)n.push(o),Sn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(jn));else n=u;if(n!==u)if((o=hp())!==u){if(s=fa,39===r.charCodeAt(fa)?(a="'",fa++):(a=u,0===ya&&Ca(qr)),a!==u){for(i=[],c=ll();c!==u;)i.push(c),c=ll();i!==u?(39===r.charCodeAt(fa)?(c="'",fa++):(c=u,0===ya&&Ca(qr)),c!==u?s=a=[a,i,c]:(fa=s,s=u)):(fa=s,s=u)}else fa=s,s=u;s!==u?(pa=t,l=s,t=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`,...Hp()}):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u}else fa=t,t=u;if(t===u){if(t=fa,e=fa,39===r.charCodeAt(fa)?(n="'",fa++):(n=u,0===ya&&Ca(qr)),n!==u){for(o=[],s=ll();s!==u;)o.push(s),s=ll();o!==u?(39===r.charCodeAt(fa)?(s="'",fa++):(s=u,0===ya&&Ca(qr)),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u)}else fa=e,e=u;if(e!==u&&(pa=t,e=function(r){return{type:"single_quote_string",value:r[1].join(""),...Hp()}}(e)),(t=e)===u){if(t=fa,e=fa,34===r.charCodeAt(fa)?(n='"',fa++):(n=u,0===ya&&Ca(fe)),n!==u){for(o=[],s=cl();s!==u;)o.push(s),s=cl();o!==u?(34===r.charCodeAt(fa)?(s='"',fa++):(s=u,0===ya&&Ca(fe)),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u)}else fa=e,e=u;e!==u?(n=fa,ya++,o=sp(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)):(fa=t,t=u)):(fa=t,t=u)}}return t}function cl(){var t;return xn.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(In)),t===u&&(t=fl()),t}function ll(){var t;return Rn.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(Nn)),t===u&&(t=fl()),t}function fl(){var t,e,n,o,s,a,i,c,l,f;return t=fa,"\\'"===r.substr(fa,2)?(e="\\'",fa+=2):(e=u,0===ya&&Ca(kn)),e!==u&&(pa=t,e="\\'"),(t=e)===u&&(t=fa,'\\"'===r.substr(fa,2)?(e='\\"',fa+=2):(e=u,0===ya&&Ca(On)),e!==u&&(pa=t,e='\\"'),(t=e)===u&&(t=fa,"\\\\"===r.substr(fa,2)?(e="\\\\",fa+=2):(e=u,0===ya&&Ca(Un)),e!==u&&(pa=t,e="\\\\"),(t=e)===u&&(t=fa,"\\/"===r.substr(fa,2)?(e="\\/",fa+=2):(e=u,0===ya&&Ca(Mn)),e!==u&&(pa=t,e="\\/"),(t=e)===u&&(t=fa,"\\b"===r.substr(fa,2)?(e="\\b",fa+=2):(e=u,0===ya&&Ca(Dn)),e!==u&&(pa=t,e="\b"),(t=e)===u&&(t=fa,"\\f"===r.substr(fa,2)?(e="\\f",fa+=2):(e=u,0===ya&&Ca(Pn)),e!==u&&(pa=t,e="\f"),(t=e)===u&&(t=fa,"\\n"===r.substr(fa,2)?(e="\\n",fa+=2):(e=u,0===ya&&Ca($n)),e!==u&&(pa=t,e="\n"),(t=e)===u&&(t=fa,"\\r"===r.substr(fa,2)?(e="\\r",fa+=2):(e=u,0===ya&&Ca(Fn)),e!==u&&(pa=t,e="\r"),(t=e)===u&&(t=fa,"\\t"===r.substr(fa,2)?(e="\\t",fa+=2):(e=u,0===ya&&Ca(Gn)),e!==u&&(pa=t,e="\t"),(t=e)===u&&(t=fa,"\\u"===r.substr(fa,2)?(e="\\u",fa+=2):(e=u,0===ya&&Ca(Hn)),e!==u&&(n=wl())!==u&&(o=wl())!==u&&(s=wl())!==u&&(a=wl())!==u?(pa=t,i=n,c=o,l=s,f=a,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(fa=t,t=u),t===u&&(t=fa,92===r.charCodeAt(fa)?(e="\\",fa++):(e=u,0===ya&&Ca(Bn)),e!==u&&(pa=t,e="\\"),(t=e)===u&&(t=fa,"''"===r.substr(fa,2)?(e="''",fa+=2):(e=u,0===ya&&Ca(qn)),e!==u&&(pa=t,e="''"),t=e))))))))))),t}function pl(){var r,t,e;return r=fa,(t=function(){var r,t,e,n;r=fa,(t=bl())===u&&(t=null);t!==u&&(e=vl())!==u&&(n=dl())!==u?(pa=r,r=t={type:"bigint",value:(t||"")+e+n}):(fa=r,r=u);r===u&&(r=fa,(t=bl())===u&&(t=null),t!==u&&(e=vl())!==u?(pa=r,t=function(r,t){const e=(r||"")+t;return r&&Wp(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(fa=r,r=u),r===u&&(r=fa,(t=bl())!==u&&(e=dl())!==u?(pa=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(fa=r,r=u),r===u&&(r=fa,(t=bl())!==u&&(pa=r,t=function(r){return Wp(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==u&&(pa=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function bl(){var t,e,n;return(t=yl())===u&&(t=hl())===u&&(t=fa,45===r.charCodeAt(fa)?(e="-",fa++):(e=u,0===ya&&Ca(Qt)),e===u&&(43===r.charCodeAt(fa)?(e="+",fa++):(e=u,0===ya&&Ca(Xt))),e!==u&&(n=yl())!==u?(pa=t,t=e=e+n):(fa=t,t=u),t===u&&(t=fa,45===r.charCodeAt(fa)?(e="-",fa++):(e=u,0===ya&&Ca(Qt)),e===u&&(43===r.charCodeAt(fa)?(e="+",fa++):(e=u,0===ya&&Ca(Xt))),e!==u&&(n=hl())!==u?(pa=t,t=e=function(r,t){return r+t}(e,n)):(fa=t,t=u))),t}function vl(){var t,e,n;return t=fa,46===r.charCodeAt(fa)?(e=".",fa++):(e=u,0===ya&&Ca(Vn)),e!==u&&(n=yl())!==u?(pa=t,t=e="."+n):(fa=t,t=u),t}function dl(){var t,e,n;return t=fa,(e=function(){var t,e,n;t=fa,Zn.test(r.charAt(fa))?(e=r.charAt(fa),fa++):(e=u,0===ya&&Ca(Jn));e!==u?(ro.test(r.charAt(fa))?(n=r.charAt(fa),fa++):(n=u,0===ya&&Ca(to)),n===u&&(n=null),n!==u?(pa=t,t=e=e+(null!==(o=n)?o:"")):(fa=t,t=u)):(fa=t,t=u);var o;return t}())!==u&&(n=yl())!==u?(pa=t,t=e=e+n):(fa=t,t=u),t}function yl(){var r,t,e;if(r=fa,t=[],(e=hl())!==u)for(;e!==u;)t.push(e),e=hl();else t=u;return t!==u&&(pa=r,t=t.join("")),r=t}function hl(){var t;return Xn.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(Qn)),t}function wl(){var t;return Kn.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(zn)),t}function ml(){var t,e,n,o;return t=fa,"default"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(G)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Ll(){var t,e,n,o;return t=fa,"to"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(uo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Cl(){var t,e,n,o;return t=fa,"show"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(ao)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function El(){var t,e,n,o;return t=fa,"drop"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(io)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DROP"):(fa=t,t=u)):(fa=t,t=u),t}function Al(){var t,e,n,o;return t=fa,"alter"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(lo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function gl(){var t,e,n,o;return t=fa,"select"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(fo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Tl(){var t,e,n,o;return t=fa,"update"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(po)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function _l(){var t,e,n,o;return t=fa,"create"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(bo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Sl(){var t,e,n,o;return t=fa,"temporary"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(vo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function jl(){var t,e,n,o;return t=fa,"temp"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(yo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function xl(){var t,e,n,o;return t=fa,"delete"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(ho)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Il(){var t,e,n,o;return t=fa,"insert"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(wo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Rl(){var t,e,n,o;return t=fa,"recursive"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Ze)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="RECURSIVE"):(fa=t,t=u)):(fa=t,t=u),t}function Nl(){var t,e,n,o;return t=fa,"replace"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(mo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function kl(){var t,e,n,o;return t=fa,"rename"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Co)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Ol(){var t,e,n,o;return t=fa,"ignore"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Pe)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Ul(){var t,e,n,o;return t=fa,"partition"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Eo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="PARTITION"):(fa=t,t=u)):(fa=t,t=u),t}function Ml(){var t,e,n,o;return t=fa,"into"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Ao)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Dl(){var t,e,n,o;return t=fa,"from"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(go)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Pl(){var t,e,n,o;return t=fa,"set"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Or)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SET"):(fa=t,t=u)):(fa=t,t=u),t}function $l(){var t,e,n,o;return t=fa,"as"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(To)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Fl(){var t,e,n,o;return t=fa,"table"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(_o)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TABLE"):(fa=t,t=u)):(fa=t,t=u),t}function Gl(){var t,e,n,o;return t=fa,"schema"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(l)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SCHEMA"):(fa=t,t=u)):(fa=t,t=u),t}function Hl(){var t,e,n,o;return t=fa,"on"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(Cr)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Bl(){var t,e,n,o;return t=fa,"join"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Uo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function ql(){var t,e,n,o;return t=fa,"outer"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ze)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Wl(){var t,e,n,o;return t=fa,"values"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Do)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Yl(){var t,e,n,o;return t=fa,"using"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Po)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Vl(){var t,e,n,o;return t=fa,"with"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(yt)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Xl(){var t,e,n,o;return t=fa,"group"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Fo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Ql(){var t,e,n,o;return t=fa,"by"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(Go)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function Kl(){var t,e,n,o;return t=fa,"order"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Ho)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function zl(){var t,e,n,o;return t=fa,"asc"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Vo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ASC"):(fa=t,t=u)):(fa=t,t=u),t}function Zl(){var t,e,n,o;return t=fa,"desc"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Xo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DESC"):(fa=t,t=u)):(fa=t,t=u),t}function Jl(){var t,e,n,o;return t=fa,"all"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Qo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ALL"):(fa=t,t=u)):(fa=t,t=u),t}function rf(){var t,e,n,o;return t=fa,"distinct"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Ko)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DISTINCT"):(fa=t,t=u)):(fa=t,t=u),t}function tf(){var t,e,n,o;return t=fa,"between"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(zo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="BETWEEN"):(fa=t,t=u)):(fa=t,t=u),t}function ef(){var t,e,n,o;return t=fa,"in"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(Xr)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="IN"):(fa=t,t=u)):(fa=t,t=u),t}function nf(){var t,e,n,o;return t=fa,"is"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(Zo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="IS"):(fa=t,t=u)):(fa=t,t=u),t}function of(){var t,e,n,o;return t=fa,"like"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Jo)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="LIKE"):(fa=t,t=u)):(fa=t,t=u),t}function uf(){var t,e,n,o;return t=fa,"ilike"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ru)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ILIKE"):(fa=t,t=u)):(fa=t,t=u),t}function sf(){var t,e,n,o;return t=fa,"exists"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(tu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="EXISTS"):(fa=t,t=u)):(fa=t,t=u),t}function af(){var t,e,n,o;return t=fa,"not"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Tr)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="NOT"):(fa=t,t=u)):(fa=t,t=u),t}function cf(){var t,e,n,o;return t=fa,"and"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(eu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="AND"):(fa=t,t=u)):(fa=t,t=u),t}function lf(){var t,e,n,o;return t=fa,"or"===r.substr(fa,2).toLowerCase()?(e=r.substr(fa,2),fa+=2):(e=u,0===ya&&Ca(nu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="OR"):(fa=t,t=u)):(fa=t,t=u),t}function ff(){var t,e,n,o;return t=fa,"array"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ou)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ARRAY"):(fa=t,t=u)):(fa=t,t=u),t}function pf(){var t,e,n,o;return t=fa,"count"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(au)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="COUNT"):(fa=t,t=u)):(fa=t,t=u),t}function bf(){var t,e,n,o;return t=fa,"extract"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(bu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="EXTRACT"):(fa=t,t=u)):(fa=t,t=u),t}function vf(){var t,e,n,o;return t=fa,"case"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(du)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function df(){var t,e,n,o;return t=fa,"when"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(yu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function yf(){var t,e,n,o;return t=fa,"end"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(mu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?t=e=[e,n]:(fa=t,t=u)):(fa=t,t=u),t}function hf(){var t,e,n,o;return t=fa,"cast"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Lu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CAST"):(fa=t,t=u)):(fa=t,t=u),t}function wf(){var t,e,n,o;return t=fa,"try_cast"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Cu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TRY_CAST"):(fa=t,t=u)):(fa=t,t=u),t}function mf(){var t,e,n,o;return t=fa,"char"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(gu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CHAR"):(fa=t,t=u)):(fa=t,t=u),t}function Lf(){var t,e,n,o;return t=fa,"varchar"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Tu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="VARCHAR"):(fa=t,t=u)):(fa=t,t=u),t}function Cf(){var t,e,n,o;return t=fa,"number"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(_u)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="NUMBER"):(fa=t,t=u)):(fa=t,t=u),t}function Ef(){var t,e,n,o;return t=fa,"decimal"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Su)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DECIMAL"):(fa=t,t=u)):(fa=t,t=u),t}function Af(){var t,e,n,o;return t=fa,"unsigned"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(xu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="UNSIGNED"):(fa=t,t=u)):(fa=t,t=u),t}function gf(){var t,e,n,o;return t=fa,"int"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Iu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="INT"):(fa=t,t=u)):(fa=t,t=u),t}function Tf(){var t,e,n,o;return t=fa,"integer"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Nu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="INTEGER"):(fa=t,t=u)):(fa=t,t=u),t}function _f(){var t,e,n,o;return t=fa,"smallint"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Mu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SMALLINT"):(fa=t,t=u)):(fa=t,t=u),t}function Sf(){var t,e,n,o;return t=fa,"serial"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Du)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="SERIAL"):(fa=t,t=u)):(fa=t,t=u),t}function jf(){var t,e,n,o;return t=fa,"tinyint"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Pu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TINYINT"):(fa=t,t=u)):(fa=t,t=u),t}function xf(){var t,e,n,o;return t=fa,"tinytext"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca($u)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TINYTEXT"):(fa=t,t=u)):(fa=t,t=u),t}function If(){var t,e,n,o;return t=fa,"text"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Fu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TEXT"):(fa=t,t=u)):(fa=t,t=u),t}function Rf(){var t,e,n,o;return t=fa,"mediumtext"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(Gu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="MEDIUMTEXT"):(fa=t,t=u)):(fa=t,t=u),t}function Nf(){var t,e,n,o;return t=fa,"longtext"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Hu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="LONGTEXT"):(fa=t,t=u)):(fa=t,t=u),t}function kf(){var t,e,n,o;return t=fa,"bigint"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Bu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="BIGINT"):(fa=t,t=u)):(fa=t,t=u),t}function Of(){var t,e,n,o;return t=fa,"enum"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(qu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ENUM"):(fa=t,t=u)):(fa=t,t=u),t}function Uf(){var t,e,n,o;return t=fa,"float"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Wu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="FLOAT"):(fa=t,t=u)):(fa=t,t=u),t}function Mf(){var t,e,n,o;return t=fa,"double"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Yu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DOUBLE"):(fa=t,t=u)):(fa=t,t=u),t}function Df(){var t,e,n,o;return t=fa,"bigserial"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Vu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="BIGSERIAL"):(fa=t,t=u)):(fa=t,t=u),t}function Pf(){var t,e,n,o;return t=fa,"real"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Xu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REAL"):(fa=t,t=u)):(fa=t,t=u),t}function $f(){var t,e,n,o;return t=fa,"date"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(un)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DATE"):(fa=t,t=u)):(fa=t,t=u),t}function Ff(){var t,e,n,o;return t=fa,"datetime"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Qu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="DATETIME"):(fa=t,t=u)):(fa=t,t=u),t}function Gf(){var t,e,n,o;return t=fa,"rows"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Ku)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ROWS"):(fa=t,t=u)):(fa=t,t=u),t}function Hf(){var t,e,n,o;return t=fa,"time"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(zu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TIME"):(fa=t,t=u)):(fa=t,t=u),t}function Bf(){var t,e,n,o;return t=fa,"timestamp"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(Zu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TIMESTAMP"):(fa=t,t=u)):(fa=t,t=u),t}function qf(){var t,e,n,o;return t=fa,"truncate"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Ju)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="TRUNCATE"):(fa=t,t=u)):(fa=t,t=u),t}function Wf(){var t,e,n,o;return t=fa,"interval"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(ds)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="INTERVAL"):(fa=t,t=u)):(fa=t,t=u),t}function Yf(){var t,e,n,o;return t=fa,"current_timestamp"===r.substr(fa,17).toLowerCase()?(e=r.substr(fa,17),fa+=17):(e=u,0===ya&&Ca(hs)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CURRENT_TIMESTAMP"):(fa=t,t=u)):(fa=t,t=u),t}function Vf(){var t;return 36===r.charCodeAt(fa)?(t="$",fa++):(t=u,0===ya&&Ca(re)),t}function Xf(){var t;return"$$"===r.substr(fa,2)?(t="$$",fa+=2):(t=u,0===ya&&Ca(_s)),t}function Qf(){var t;return(t=function(){var t;return"@@"===r.substr(fa,2)?(t="@@",fa+=2):(t=u,0===ya&&Ca(Ts)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt(fa)?(t="@",fa++):(t=u,0===ya&&Ca(gs)),t}())===u&&(t=Vf())===u&&(t=Vf()),t}function Kf(){var t;return"::"===r.substr(fa,2)?(t="::",fa+=2):(t=u,0===ya&&Ca(xs)),t}function zf(){var t;return 61===r.charCodeAt(fa)?(t="=",fa++):(t=u,0===ya&&Ca(xt)),t}function Zf(){var t,e,n,o;return t=fa,"add"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(Rs)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ADD"):(fa=t,t=u)):(fa=t,t=u),t}function Jf(){var t,e,n,o;return t=fa,"column"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(Ns)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="COLUMN"):(fa=t,t=u)):(fa=t,t=u),t}function rp(){var t,e,n,o;return t=fa,"index"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(ks)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="INDEX"):(fa=t,t=u)):(fa=t,t=u),t}function tp(){var t,e,n,o;return t=fa,"key"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(M)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="KEY"):(fa=t,t=u)):(fa=t,t=u),t}function ep(){var t,e,n,o;return t=fa,"unique"===r.substr(fa,6).toLowerCase()?(e=r.substr(fa,6),fa+=6):(e=u,0===ya&&Ca(U)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="UNIQUE"):(fa=t,t=u)):(fa=t,t=u),t}function np(){var t,e,n,o;return t=fa,"comment"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Ms)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="COMMENT"):(fa=t,t=u)):(fa=t,t=u),t}function op(){var t,e,n,o;return t=fa,"constraint"===r.substr(fa,10).toLowerCase()?(e=r.substr(fa,10),fa+=10):(e=u,0===ya&&Ca(Ds)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CONSTRAINT"):(fa=t,t=u)):(fa=t,t=u),t}function up(){var t,e,n,o;return t=fa,"concurrently"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(Ps)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CONCURRENTLY"):(fa=t,t=u)):(fa=t,t=u),t}function sp(){var t;return 46===r.charCodeAt(fa)?(t=".",fa++):(t=u,0===ya&&Ca(Vn)),t}function ap(){var t;return 44===r.charCodeAt(fa)?(t=",",fa++):(t=u,0===ya&&Ca(Ys)),t}function ip(){var t;return 42===r.charCodeAt(fa)?(t="*",fa++):(t=u,0===ya&&Ca(Kt)),t}function cp(){var t;return 40===r.charCodeAt(fa)?(t="(",fa++):(t=u,0===ya&&Ca(at)),t}function lp(){var t;return 41===r.charCodeAt(fa)?(t=")",fa++):(t=u,0===ya&&Ca(it)),t}function fp(){var t;return 91===r.charCodeAt(fa)?(t="[",fa++):(t=u,0===ya&&Ca(Vs)),t}function pp(){var t;return 93===r.charCodeAt(fa)?(t="]",fa++):(t=u,0===ya&&Ca(Xs)),t}function bp(){var t;return 59===r.charCodeAt(fa)?(t=";",fa++):(t=u,0===ya&&Ca(st)),t}function vp(){var t;return"->"===r.substr(fa,2)?(t="->",fa+=2):(t=u,0===ya&&Ca(kt)),t}function dp(){var t;return"->>"===r.substr(fa,3)?(t="->>",fa+=3):(t=u,0===ya&&Ca(Qs)),t}function yp(){var t;return(t=function(){var t;return"||"===r.substr(fa,2)?(t="||",fa+=2):(t=u,0===ya&&Ca(Jt)),t}())===u&&(t=function(){var t;return"&&"===r.substr(fa,2)?(t="&&",fa+=2):(t=u,0===ya&&Ca(Ks)),t}()),t}function hp(){var r,t;for(r=[],(t=Ep())===u&&(t=mp());t!==u;)r.push(t),(t=Ep())===u&&(t=mp());return r}function wp(){var r,t;if(r=[],(t=Ep())===u&&(t=mp()),t!==u)for(;t!==u;)r.push(t),(t=Ep())===u&&(t=mp());else r=u;return r}function mp(){var t;return(t=function t(){var e,n,o,s,a,i,c;e=fa,"/*"===r.substr(fa,2)?(n="/*",fa+=2):(n=u,0===ya&&Ca(zs));if(n!==u){for(o=[],s=fa,a=fa,ya++,"*/"===r.substr(fa,2)?(i="*/",fa+=2):(i=u,0===ya&&Ca(Zs)),ya--,i===u?a=void 0:(fa=a,a=u),a!==u?(i=fa,ya++,"/*"===r.substr(fa,2)?(c="/*",fa+=2):(c=u,0===ya&&Ca(zs)),ya--,c===u?i=void 0:(fa=i,i=u),i!==u&&(c=Cp())!==u?s=a=[a,i,c]:(fa=s,s=u)):(fa=s,s=u),s===u&&(s=t());s!==u;)o.push(s),s=fa,a=fa,ya++,"*/"===r.substr(fa,2)?(i="*/",fa+=2):(i=u,0===ya&&Ca(Zs)),ya--,i===u?a=void 0:(fa=a,a=u),a!==u?(i=fa,ya++,"/*"===r.substr(fa,2)?(c="/*",fa+=2):(c=u,0===ya&&Ca(zs)),ya--,c===u?i=void 0:(fa=i,i=u),i!==u&&(c=Cp())!==u?s=a=[a,i,c]:(fa=s,s=u)):(fa=s,s=u),s===u&&(s=t());o!==u?("*/"===r.substr(fa,2)?(s="*/",fa+=2):(s=u,0===ya&&Ca(Zs)),s!==u?e=n=[n,o,s]:(fa=e,e=u)):(fa=e,e=u)}else fa=e,e=u;return e}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,"--"===r.substr(fa,2)?(e="--",fa+=2):(e=u,0===ya&&Ca(Js));if(e!==u){for(n=[],o=fa,s=fa,ya++,a=Ap(),ya--,a===u?s=void 0:(fa=s,s=u),s!==u&&(a=Cp())!==u?o=s=[s,a]:(fa=o,o=u);o!==u;)n.push(o),o=fa,s=fa,ya++,a=Ap(),ya--,a===u?s=void 0:(fa=s,s=u),s!==u&&(a=Cp())!==u?o=s=[s,a]:(fa=o,o=u);n!==u?t=e=[e,n]:(fa=t,t=u)}else fa=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,s,a;t=fa,"//"===r.substr(fa,2)?(e="//",fa+=2):(e=u,0===ya&&Ca(ra));if(e!==u){for(n=[],o=fa,s=fa,ya++,a=Ap(),ya--,a===u?s=void 0:(fa=s,s=u),s!==u&&(a=Cp())!==u?o=s=[s,a]:(fa=o,o=u);o!==u;)n.push(o),o=fa,s=fa,ya++,a=Ap(),ya--,a===u?s=void 0:(fa=s,s=u),s!==u&&(a=Cp())!==u?o=s=[s,a]:(fa=o,o=u);n!==u?t=e=[e,n]:(fa=t,t=u)}else fa=t,t=u;return t}()),t}function Lp(){var r,t,e,n,o,s,a;return r=fa,(t=np())!==u&&hp()!==u?((e=zf())===u&&(e=null),e!==u&&hp()!==u&&(n=il())!==u?(pa=r,s=e,a=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:s,value:a}):(fa=r,r=u)):(fa=r,r=u),r}function Cp(){var t;return r.length>fa?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(ta)),t}function Ep(){var t;return ea.test(r.charAt(fa))?(t=r.charAt(fa),fa++):(t=u,0===ya&&Ca(na)),t}function Ap(){var t,e;if((t=function(){var t,e;t=fa,ya++,r.length>fa?(e=r.charAt(fa),fa++):(e=u,0===ya&&Ca(ta));ya--,e===u?t=void 0:(fa=t,t=u);return t}())===u)if(t=[],Wn.test(r.charAt(fa))?(e=r.charAt(fa),fa++):(e=u,0===ya&&Ca(Yn)),e!==u)for(;e!==u;)t.push(e),Wn.test(r.charAt(fa))?(e=r.charAt(fa),fa++):(e=u,0===ya&&Ca(Yn));else t=u;return t}function gp(){var t,e;return t=fa,pa=fa,Zp=[],(!0?void 0:u)!==u&&hp()!==u?((e=Tp())===u&&(e=function(){var t,e;t=fa,function(){var t;return"return"===r.substr(fa,6).toLowerCase()?(t=r.substr(fa,6),fa+=6):(t=u,0===ya&&Ca(Ss)),t}()!==u&&hp()!==u&&(e=_p())!==u?(pa=t,t={type:"return",expr:e}):(fa=t,t=u);return t}()),e!==u?(pa=t,t={type:"proc",stmt:e,vars:Zp}):(fa=t,t=u)):(fa=t,t=u),t}function Tp(){var t,e,n,o;return t=fa,(e=kp())===u&&(e=Op()),e!==u&&hp()!==u?((n=function(){var t;return":="===r.substr(fa,2)?(t=":=",fa+=2):(t=u,0===ya&&Ca(js)),t}())===u&&(n=zf()),n!==u&&hp()!==u&&(o=_p())!==u?(pa=t,t=e={type:"assign",left:e,symbol:n,right:o}):(fa=t,t=u)):(fa=t,t=u),t}function _p(){var r;return(r=ni())===u&&(r=function(){var r,t,e,n,o;r=fa,(t=kp())!==u&&hp()!==u&&(e=Si())!==u&&hp()!==u&&(n=kp())!==u&&hp()!==u&&(o=Ii())!==u?(pa=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(fa=r,r=u);return r}())===u&&(r=Sp())===u&&(r=function(){var r,t;r=fa,fp()!==u&&hp()!==u&&(t=Np())!==u&&hp()!==u&&pp()!==u?(pa=r,r={type:"array",value:t}):(fa=r,r=u);return r}()),r}function Sp(){var r,t,e,n,o,s,a,i;if(r=fa,(t=jp())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=vc())!==u&&(a=hp())!==u&&(i=jp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=vc())!==u&&(a=hp())!==u&&(i=jp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Ot(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function jp(){var r,t,e,n,o,s,a,i;if(r=fa,(t=xp())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=yc())!==u&&(a=hp())!==u&&(i=xp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=yc())!==u&&(a=hp())!==u&&(i=xp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Ot(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function xp(){var r,t,e;return(r=nl())===u&&(r=kp())===u&&(r=Rp())===u&&(r=Fc())===u&&(r=fa,cp()!==u&&hp()!==u&&(t=Sp())!==u&&hp()!==u&&lp()!==u?(pa=r,(e=t).parentheses=!0,r=e):(fa=r,r=u)),r}function Ip(){var r,t,e,n,o,s,a;return r=fa,(t=Ic())!==u?(e=fa,(n=hp())!==u&&(o=sp())!==u&&(s=hp())!==u&&(a=Ic())!==u?e=n=[n,o,s,a]:(fa=e,e=u),e===u&&(e=null),e!==u?(pa=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=t[3]),e}(t,e)):(fa=r,r=u)):(fa=r,r=u),r}function Rp(){var r,t,e;return r=fa,(t=Ip())!==u&&hp()!==u&&cp()!==u&&hp()!==u?((e=Np())===u&&(e=null),e!==u&&hp()!==u&&lp()!==u?(pa=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...Hp()}):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=Ip())!==u&&(pa=r,t=function(r){return{type:"function",name:r,args:null,...Hp()}}(t)),r=t),r}function Np(){var r,t,e,n,o,s,a,i;if(r=fa,(t=xp())!==u){for(e=[],n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=xp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);n!==u;)e.push(n),n=fa,(o=hp())!==u&&(s=ap())!==u&&(a=hp())!==u&&(i=xp())!==u?n=o=[o,s,a,i]:(fa=n,n=u);e!==u?(pa=r,r=t=Yp(t,e)):(fa=r,r=u)}else fa=r,r=u;return r}function kp(){var t,e,n,o,s,a,i;if(t=fa,(e=Xf())!==u){for(n=[],oa.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(ua));o!==u;)n.push(o),oa.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(ua));n!==u&&(o=Xf())!==u?(pa=t,t=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(fa=t,t=u)}else fa=t,t=u;if(t===u){if(t=fa,(e=Vf())!==u)if((n=Oc())!==u)if((o=Vf())!==u){for(s=[],oa.test(r.charAt(fa))?(a=r.charAt(fa),fa++):(a=u,0===ya&&Ca(ua));a!==u;)s.push(a),oa.test(r.charAt(fa))?(a=r.charAt(fa),fa++):(a=u,0===ya&&Ca(ua));s!==u&&(a=Vf())!==u&&(i=Oc())!==u?(pa=fa,(function(r,t,e){if(r!==e)return!0}(n,0,i)?u:void 0)!==u&&Vf()!==u?(pa=t,t=e=function(r,t,e){return{type:"var",name:t.join(""),prefix:`$${r}$`,suffix:`$${e}$`}}(n,s,i)):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;t===u&&(t=fa,(e=Qf())!==u&&(n=Op())!==u?(pa=t,t=e=function(r,t){return{type:"var",...t,prefix:r}}(e,n)):(fa=t,t=u))}return t}function Op(){var t,e,n,o,s;return t=fa,34===r.charCodeAt(fa)?(e='"',fa++):(e=u,0===ya&&Ca(fe)),e===u&&(e=null),e!==u&&(n=Mc())!==u&&(o=function(){var t,e,n,o,s;t=fa,e=[],n=fa,46===r.charCodeAt(fa)?(o=".",fa++):(o=u,0===ya&&Ca(Vn));o!==u&&(s=Mc())!==u?n=o=[o,s]:(fa=n,n=u);for(;n!==u;)e.push(n),n=fa,46===r.charCodeAt(fa)?(o=".",fa++):(o=u,0===ya&&Ca(Vn)),o!==u&&(s=Mc())!==u?n=o=[o,s]:(fa=n,n=u);e!==u&&(pa=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(34===r.charCodeAt(fa)?(s='"',fa++):(s=u,0===ya&&Ca(fe)),s===u&&(s=null),s!==u?(pa=t,t=e=function(r,t,e,n){if(r&&!n||!r&&n)throw new Error("double quoted not match");return Zp.push(t),{type:"var",name:t,members:e,quoted:r&&n?'"':null,prefix:null}}(e,n,o,s)):(fa=t,t=u)):(fa=t,t=u),t===u&&(t=fa,(e=pl())!==u&&(pa=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Up(){var t;return(t=function(){var r,t,e;r=fa,(t=Pp())===u&&(t=Mp());t!==u&&hp()!==u&&fp()!==u&&hp()!==u&&(e=pp())!==u&&hp()!==u&&fp()!==u&&hp()!==u&&pp()!==u?(pa=r,n=t,t={...n,array:{dimension:2}},r=t):(fa=r,r=u);var n;r===u&&(r=fa,(t=Pp())===u&&(t=Mp()),t!==u&&hp()!==u&&fp()!==u&&hp()!==u?((e=pl())===u&&(e=null),e!==u&&hp()!==u&&pp()!==u?(pa=r,t=function(r,t){return{...r,array:{dimension:1,length:[t]}}}(t,e),r=t):(fa=r,r=u)):(fa=r,r=u),r===u&&(r=fa,(t=Pp())===u&&(t=Mp()),t!==u&&hp()!==u&&ff()!==u?(pa=r,t=function(r){return{...r,array:{keyword:"array"}}}(t),r=t):(fa=r,r=u)));return r}())===u&&(t=Mp())===u&&(t=Pp())===u&&(t=function(){var t,e,n,o;t=fa,(e=$f())===u&&(e=Ff());if(e!==u)if(hp()!==u)if(cp()!==u)if(hp()!==u){if(n=[],Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn)),o!==u)for(;o!==u;)n.push(o),Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn));else n=u;n!==u&&(o=hp())!==u&&lp()!==u?(pa=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;t===u&&(t=fa,(e=$f())===u&&(e=Ff()),e!==u&&(pa=t,e=ia(e)),(t=e)===u&&(t=function(){var t,e,n,o,s,a;t=fa,(e=Hf())===u&&(e=Bf());if(e!==u)if(hp()!==u)if((n=cp())!==u)if(hp()!==u){if(o=[],Xn.test(r.charAt(fa))?(s=r.charAt(fa),fa++):(s=u,0===ya&&Ca(Qn)),s!==u)for(;s!==u;)o.push(s),Xn.test(r.charAt(fa))?(s=r.charAt(fa),fa++):(s=u,0===ya&&Ca(Qn));else o=u;o!==u&&(s=hp())!==u&&lp()!==u&&hp()!==u?((a=$p())===u&&(a=null),a!==u?(pa=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),parentheses:!0,suffix:e}}(e,o,a),t=e):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;t===u&&(t=fa,(e=Hf())===u&&(e=Bf()),e!==u&&hp()!==u?((n=$p())===u&&(n=null),n!==u?(pa=t,e=function(r,t){return{dataType:r,suffix:t}}(e,n),t=e):(fa=t,t=u)):(fa=t,t=u));return t}()));return t}())===u&&(t=function(){var t,e;t=fa,(e=function(){var t,e,n,o;return t=fa,"json"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(ku)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="JSON"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"jsonb"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(Ou)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="JSONB"):(fa=t,t=u)):(fa=t,t=u),t}());e!==u&&(pa=t,e=ia(e));return t=e}())===u&&(t=function(){var t,e;t=fa,(e=function(){var t,e,n,o;return t=fa,"geometry"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Uu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="GEOMETRY"):(fa=t,t=u)):(fa=t,t=u),t}())!==u&&(pa=t,e={dataType:e});return t=e}())===u&&(t=function(){var r,t;r=fa,(t=xf())===u&&(t=If())===u&&(t=Rf())===u&&(t=Nf());t!==u&&fp()!==u&&hp()!==u&&pp()!==u?(pa=r,r=t={dataType:t+"[]"}):(fa=r,r=u);r===u&&(r=fa,(t=xf())===u&&(t=If())===u&&(t=Rf())===u&&(t=Nf()),t!==u&&(pa=r,t=function(r){return{dataType:r}}(t)),r=t);return r}())===u&&(t=function(){var t,e;t=fa,(e=function(){var t,e,n,o;return t=fa,"uuid"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(ts)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="UUID"):(fa=t,t=u)):(fa=t,t=u),t}())!==u&&(pa=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=fa,(e=function(){var t,e,n,o;return t=fa,"bool"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(Eu)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="BOOL"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"boolean"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(Au)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="BOOLEAN"):(fa=t,t=u)):(fa=t,t=u),t}());e!==u&&(pa=t,e=sa(e));return t=e}())===u&&(t=function(){var r,t,e;r=fa,(t=Of())!==u&&hp()!==u&&(e=zi())!==u?(pa=r,n=t,(o=e).parentheses=!0,r=t={dataType:n,expr:o}):(fa=r,r=u);var n,o;return r}())===u&&(t=function(){var r,t;r=fa,(t=Sf())===u&&(t=Wf());t!==u&&(pa=r,t=ia(t));return r=t}())===u&&(t=function(){var t,e;t=fa,"bytea"===r.substr(fa,5).toLowerCase()?(e=r.substr(fa,5),fa+=5):(e=u,0===ya&&Ca(aa));e!==u&&(pa=t,e={dataType:"BYTEA"});return t=e}())===u&&(t=function(){var t,e;t=fa,(e=function(){var t,e,n,o;return t=fa,"oid"===r.substr(fa,3).toLowerCase()?(e=r.substr(fa,3),fa+=3):(e=u,0===ya&&Ca(es)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="OID"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regclass"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(ns)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGCLASS"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regcollation"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(os)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGCOLLATION"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regconfig"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(us)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGCONFIG"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regdictionary"===r.substr(fa,13).toLowerCase()?(e=r.substr(fa,13),fa+=13):(e=u,0===ya&&Ca(ss)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGDICTIONARY"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regnamespace"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(as)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGNAMESPACE"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regoper"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(is)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGOPER"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regoperator"===r.substr(fa,11).toLowerCase()?(e=r.substr(fa,11),fa+=11):(e=u,0===ya&&Ca(cs)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGOPERATOR"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regproc"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(ls)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGPROC"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regprocedure"===r.substr(fa,12).toLowerCase()?(e=r.substr(fa,12),fa+=12):(e=u,0===ya&&Ca(fs)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGPROCEDURE"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regrole"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(ps)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGROLE"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=fa,"regtype"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(bs)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="REGTYPE"):(fa=t,t=u)):(fa=t,t=u),t}());e!==u&&(pa=t,e=sa(e));return t=e}()),t}function Mp(){var t,e,n,o;if(t=fa,(e=mf())===u&&(e=Lf()),e!==u)if(hp()!==u)if(cp()!==u)if(hp()!==u){if(n=[],Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn)),o!==u)for(;o!==u;)n.push(o),Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn));else n=u;n!==u&&(o=hp())!==u&&lp()!==u?(pa=t,t=e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0}):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;return t===u&&(t=fa,(e=mf())===u&&(e=function(){var t,e,n,o;return t=fa,"character"===r.substr(fa,9).toLowerCase()?(e=r.substr(fa,9),fa+=9):(e=u,0===ya&&Ca(kr)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="CHARACTER"):(fa=t,t=u)):(fa=t,t=u),t}()),e!==u&&(pa=t,e=function(r){return{dataType:r}}(e)),(t=e)===u&&(t=fa,(e=Lf())!==u&&(pa=t,e=ia(e)),t=e)),t}function Dp(){var t,e,n;return t=fa,(e=Af())===u&&(e=null),e!==u&&hp()!==u?((n=function(){var t,e,n,o;return t=fa,"zerofill"===r.substr(fa,8).toLowerCase()?(e=r.substr(fa,8),fa+=8):(e=u,0===ya&&Ca(Ru)),e!==u?(n=fa,ya++,o=Dc(),ya--,o===u?n=void 0:(fa=n,n=u),n!==u?(pa=t,t=e="ZEROFILL"):(fa=t,t=u)):(fa=t,t=u),t}())===u&&(n=null),n!==u?(pa=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(fa=t,t=u)):(fa=t,t=u),t}function Pp(){var t,e,n,o,s,a,i,c,l,f,p,b,v,d;if(t=fa,(e=Cf())===u&&(e=Ef())===u&&(e=gf())===u&&(e=Tf())===u&&(e=_f())===u&&(e=jf())===u&&(e=kf())===u&&(e=Uf())===u&&(e=Mf())===u&&(e=Sf())===u&&(e=Df())===u&&(e=Pf()),e!==u)if((n=hp())!==u)if((o=cp())!==u)if((s=hp())!==u){if(a=[],Xn.test(r.charAt(fa))?(i=r.charAt(fa),fa++):(i=u,0===ya&&Ca(Qn)),i!==u)for(;i!==u;)a.push(i),Xn.test(r.charAt(fa))?(i=r.charAt(fa),fa++):(i=u,0===ya&&Ca(Qn));else a=u;if(a!==u)if((i=hp())!==u){if(c=fa,(l=ap())!==u)if((f=hp())!==u){if(p=[],Xn.test(r.charAt(fa))?(b=r.charAt(fa),fa++):(b=u,0===ya&&Ca(Qn)),b!==u)for(;b!==u;)p.push(b),Xn.test(r.charAt(fa))?(b=r.charAt(fa),fa++):(b=u,0===ya&&Ca(Qn));else p=u;p!==u?c=l=[l,f,p]:(fa=c,c=u)}else fa=c,c=u;else fa=c,c=u;c===u&&(c=null),c!==u&&(l=hp())!==u&&(f=lp())!==u&&(p=hp())!==u?((b=Dp())===u&&(b=null),b!==u?(pa=t,v=c,d=b,t=e={dataType:e,length:parseInt(a.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:d}):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;else fa=t,t=u}else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;else fa=t,t=u;if(t===u){if(t=fa,(e=Cf())===u&&(e=Ef())===u&&(e=gf())===u&&(e=Tf())===u&&(e=_f())===u&&(e=jf())===u&&(e=kf())===u&&(e=Uf())===u&&(e=Mf())===u&&(e=Sf())===u&&(e=Df())===u&&(e=Pf()),e!==u){if(n=[],Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn)),o!==u)for(;o!==u;)n.push(o),Xn.test(r.charAt(fa))?(o=r.charAt(fa),fa++):(o=u,0===ya&&Ca(Qn));else n=u;n!==u&&(o=hp())!==u?((s=Dp())===u&&(s=null),s!==u?(pa=t,t=e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,s)):(fa=t,t=u)):(fa=t,t=u)}else fa=t,t=u;t===u&&(t=fa,(e=Cf())===u&&(e=Ef())===u&&(e=gf())===u&&(e=Tf())===u&&(e=_f())===u&&(e=jf())===u&&(e=kf())===u&&(e=Uf())===u&&(e=Mf())===u&&(e=Sf())===u&&(e=Df())===u&&(e=Pf()),e!==u&&(n=hp())!==u?((o=Dp())===u&&(o=null),o!==u&&(s=hp())!==u?(pa=t,t=e=function(r,t){return{dataType:r,suffix:t}}(e,o)):(fa=t,t=u)):(fa=t,t=u))}return t}function $p(){var t,e,n;return t=fa,"without"===r.substr(fa,7).toLowerCase()?(e=r.substr(fa,7),fa+=7):(e=u,0===ya&&Ca(ca)),e===u&&("with"===r.substr(fa,4).toLowerCase()?(e=r.substr(fa,4),fa+=4):(e=u,0===ya&&Ca(yt))),e!==u&&hp()!==u&&Hf()!==u&&hp()!==u?("zone"===r.substr(fa,4).toLowerCase()?(n=r.substr(fa,4),fa+=4):(n=u,0===ya&&Ca(la)),n!==u?(pa=t,t=e=[e.toUpperCase(),"TIME","ZONE"]):(fa=t,t=u)):(fa=t,t=u),t}const Fp={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,ROWS:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0},Gp={avg:!0,sum:!0,count:!0,max:!0,min:!0,group_concat:!0,std:!0,variance:!0,current_date:!0,current_time:!0,current_timestamp:!0,current_user:!0,user:!0,session_user:!0,system_user:!0};function Hp(){return t.includeLocations?{loc:La(pa,fa)}:{}}function Bp(r,t){return{type:"unary_expr",operator:r,expr:t}}function qp(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e,...Hp()}}function Wp(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Yp(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function Vp(r,t){let e=r;for(let r=0;r<t.length;r++)e=qp(t[r][1],e,t[r][3]);return e}function Xp(r){const t=tb[r];return t||(r||null)}function Qp(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=Xp(r[1])),t.add(r.join("::"))}return Array.from(t)}function Kp(r){return"string"==typeof r?{type:"same",value:r}:r}function zp(r){const t=r.type||r.ast&&r.ast.type;if("aggr_func"===t)throw new Error("Aggregations are not supported in lambda expressions");if("select"===t)throw new Error("Subqueries are not supported in lambda expressions");return"binary_expr"===t&&(zp(r.left),zp(r.right)),!0}let Zp=[];const Jp=new Set,rb=new Set,tb={};if((e=a())!==u&&fa===r.length)return e;throw e!==u&&fa<r.length&&Ca({type:"end"}),Ea(da,va<r.length?r.charAt(va):null,va<r.length?La(va,va+1):La(va,va))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return x})),e.d(n,"columnOrderListToSQL",(function(){return I})),e.d(n,"commonKeywordArgsToSQL",(function(){return j})),e.d(n,"commonOptionConnector",(function(){return a})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return L})),e.d(n,"commentToSQL",(function(){return T})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return u})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return m})),e.d(n,"columnIdentifierToSql",(function(){return d})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return y})),e.d(n,"onPartitionsToSQL",(function(){return E})),e.d(n,"replaceParams",(function(){return C})),e.d(n,"returningToSQL",(function(){return S})),e.d(n,"hasVal",(function(){return w})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return h})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return _}));var u={database:"trino",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},s=u;function a(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return s}function b(r){s=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function y(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function h(r){if(r)return r.toUpperCase()}function w(r){return r}function m(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,u=r.suffix,s=r.value,a="object"===o(r)?s:r;switch(e){case"backticks_quote_string":a="`".concat(s,"`");break;case"string":a="'".concat(s,"'");break;case"regex_string":a='r"'.concat(s,'"');break;case"hex_string":a="X'".concat(s,"'");break;case"full_hex_string":a="0x".concat(s);break;case"natural_string":a="N'".concat(s,"'");break;case"bit_string":a="b'".concat(s,"'");break;case"double_quote_string":a='"'.concat(s,'"');break;case"single_quote_string":a="'".concat(s,"'");break;case"boolean":case"bool":a=s?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(s),t=null;break;case"origin":a=s.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(s,"'");break;case"var_string":a="N'".concat(s,"'");break;case"unicode_string":a="U&'".concat(s,"'")}var i=[];return t&&i.push(h(t)),i.push(a),u&&("string"==typeof u&&i.push(u),"object"===o(u)&&(u.collate?i.push(it(u.collate)):i.push(m(u)))),a=i.join(" "),n?"(".concat(a,")"):a}}function L(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():m(n)].filter(w)}function C(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var u=t[n];if("object"!==o(u)||"param"!==u.type)return r(u,e);if(void 0===e[u.value])throw new Error("no value for parameter :".concat(u.value," found"));return t[n]=c(e[u.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function E(r){var t=r.type,e=r.partitions;return[h(t),"(".concat(e.map((function(r){if("range"!==r.type)return m(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(m(t)," ").concat(h(n)," ").concat(m(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,s="";return null!=e&&(s=o?"".concat(e,", ").concat(o):e),n&&(s="(".concat(s,")")),u&&u.length&&(s+=" ".concat(u.join(" "))),"".concat(t).concat(s)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=h(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(w).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function T(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(m(o)),t.join(" ")}}function _(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[h(t)];if(e){var o=e.keyword,u=e.columns;n.push(h(o),u.map(dt).join(", "))}return n.join(" ")})).join(" OR ")}function S(r){return r?["RETURNING",r.columns.map(Et).filter(w).join(", ")].join(" "):""}function j(r){return r?[h(r.keyword),h(r.args)]:[]}function x(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,s=h(e);return u&&(s+="(".concat(m(n),", ").concat(m(o),")")),s}}function I(r){if(r)return r.map(mt).filter(w).join(", ")}function R(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function k(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),h(e)]}function O(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),u=[];switch(u.push(o),o){case"KEY_BLOCK_SIZE":n&&u.push(n),u.push(m(e));break;case"BTREE":case"HASH":u.length=0,u.push.apply(u,R(k(r)));break;case"WITH PARSER":u.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":u.shift(),u.push(T(r));break;case"DATA_COMPRESSION":u.push(n,h(e.value),E(e.on));break;default:u.push(n,m(e))}return u.filter(w).join(" ")}}function U(r){return r?r.map(O):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,u=r.definition,s=r.on,a=r.with,i=[];if(i.push.apply(i,R(k(e))),u&&u.length){var c="CHECK"===h(t)?"(".concat(ut(u[0]),")"):"(".concat(u.map((function(r){return ut(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),a&&i.push("WITH (".concat(U(a).join(", "),")")),s&&i.push("ON [".concat(s,"]")),i}function D(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[Array.isArray(r.left)?r.left.map(ut).join(", "):ut(r.left),t,e,h(o.type),ut(o.value)].filter(w).join(" ");return[r.parentheses?"(".concat(u,")"):u].join(" ")}function P(r){return function(r){if(Array.isArray(r))return $(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return $(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?$(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function F(r){return r?[r.prefix.map(m).join(" "),ut(r.value),r.suffix.map(m).join(" ")]:[]}function G(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(F(n)),P(F(e))).filter(w).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",ut(e[0])):i("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(h(t)," ")))}(r):"";var t,e,n}function H(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(dt).join(", "),")"):"";return"".concat("default"===t.type?y(t.value):m(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function B(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=h(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?y(e):ut(e))}return n.filter(w).join(" ")}}function q(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,u=r.for,s=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,d=r.into,y=void 0===d?{}:d,L=r.isolation,C=r.limit,E=r.options,A=r.orderby,g=r.parentheses_symbol,T=r.qualify,_=r.top,S=r.window,j=r.with,x=r.where,I=[H(j),"SELECT",h(t)];Array.isArray(E)&&I.push(E.join(" ")),I.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[h(t)];return e&&n.push("(".concat(e.map(ut).join(", "),")")),n.filter(w).join(" ")}}(o),v(_),gt(e,s));var R=y.position,N="";R&&(N=a("INTO",B,y)),"column"===R&&I.push(N),I.push(a("FROM",cr,s)),"from"===R&&I.push(N);var k=l||{},O=k.keyword,U=k.expr;I.push(a(O,ut,U)),I.push(a("WHERE",ut,x)),p&&(I.push(i("GROUP BY",st(p.columns).join(", "))),I.push(st(p.modifiers).join(", "))),I.push(a("HAVING",ut,b)),I.push(a("QUALIFY",ut,T)),I.push(a("WINDOW",ut,S)),I.push(at(A,"order by")),I.push(it(n)),I.push(G(C)),L&&I.push(a(L.keyword,m,L.expr)),I.push(h(f)),"end"===R&&I.push(N),I.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[h(r.type),h(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(u));var M=I.filter(w).join(" ");return g?"(".concat(M,")"):M}function W(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Y(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Y(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function Y(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=W(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u={},s=o.value;for(var a in o)"value"!==a&&"keyword"!==a&&(u[a]=o[a]);var i=[dt(u)],c="";s&&(c=ut(s),i.push("=",c)),e.push(i.filter(w).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return q(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(y).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(w).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(dt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[h(e)];switch(n){case"origin":o.push(m(t));break;case"update":o.push("UPDATE",a("SET",V,t.set),a("WHERE",ut,t.where))}return o.filter(w).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(w).join(" ")}function J(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,s=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},d=v.keyword,y=v.set,L=[h(e),h(o),cr(t),Q(f)];return Array.isArray(u)&&L.push("(".concat(u.map(m).join(", "),")")),L.push(a(Array.isArray(i)?"VALUES":"",X,i)),L.push(a("ON CONFLICT",Z,s)),L.push(a("SET",V,b)),L.push(a("WHERE",ut,c)),L.push(a(d,V,y)),L.push(S(p)),L.filter(w).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",ut(t),h(e),ut(n)].filter(w).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(h(t),"(").concat(n&&ut(n)||"",")"),a("AS","string"==typeof e?y:ut,e),a(h(o&&o.keyword),y,o&&o.as)].filter(w).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,s=[ut(n),"FOR",dt(e),D(o)],a=["".concat(h(u),"(").concat(s.join(" "),")")];return t&&a.push("AS",y(t)),a.join(" ")}(r);default:return""}}function ur(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,s=r.prefix,a=[];switch(t.toLowerCase()){case"forceseek":a.push(h(t),"(".concat(y(n)),"(".concat(o.map(ut).filter(w).join(", "),"))"));break;case"spatial_window_max_cells":a.push(h(t),"=",ut(e));break;case"index":a.push(h(s),h(t),u?"(".concat(e.map(y).join(", "),")"):"= ".concat(y(e)));break;default:a.push(ut(e))}return a.filter(w).join(" ")}}function sr(r,t){var e=r.name,n=r.symbol;return[h(e),n,t].filter(w).join(" ")}function ar(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",ut(r.of));break;case"from_to":t.push("FROM",ut(r.from),"TO",ut(r.to));break;case"between_and":t.push("BETWEEN",ut(r.between),"AND",ut(r.and));break;case"contained":t.push("CONTAINED","IN",ut(r.in))}return t.filter(w).join(" ")}function ir(r){if("UNNEST"===h(r.type))return nr(r);var t,e,n,o,u=r.table,s=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,d=r.tablesample,C=r.temporal_table,E=r.table_hint,A=y(b),g=y(s),T=y(p),_=u&&y(u);if(c)switch(c.type){case"values":var S=c.parentheses,j=c.values,x=c.prefix,I=[S&&"(","",S&&")"],R=X(j);x&&(R=R.split("(").slice(1).map((function(r){return"".concat(h(x),"(").concat(r)})).join("")),I[1]="VALUES ".concat(R),_=I.filter(w).join("");break;case"tumble":_=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,u=[y(t.expr.db),y(t.expr.schema),y(t.expr.table)].filter(w).join("."),s="DESCRIPTOR(".concat(dt(e.expr),")"),a=["TABLE(TUMBLE(TABLE ".concat(sr(t,u)),sr(e,s)],i=sr(o,rr(o.expr));return n&&n.expr?a.push(i,"".concat(sr(n,rr(n.expr)),"))")):a.push("".concat(i,"))")),a.filter(w).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return L(r).join(" ")})).join(", "),_="".concat(h(e),"(").concat(h(n),"(").concat(o,"))");break;default:_=ut(c)}var N=[[A,g,T,_=[h(f),_,h(v)].filter(w).join(" ")].filter(w).join(".")];if(d){var k=["TABLESAMPLE",ut(d.expr),m(d.repeatable)].filter(w).join(" ");N.push(k)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[h(t),ar(e)].filter(w).join(" ")}}(C),a("AS","string"==typeof i?y:ut,i),or(l)),E&&N.push(h(E.keyword),"(".concat(E.expr.map(ur).filter(w).join(", "),")"));var O=N.filter(w).join(" ");return r.parentheses?"(".concat(O,")"):O}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var u=[],s=[],i=!0===e?1:e.length,c=0;c++<i;)u.push("("),s.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return u.join("")+o+s.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],d=v.on,y=v.using,L=v.join,C=[];C.push(L?" ".concat(h(L)):","),C.push(ir(v)),C.push(a("ON",ut,d)),y&&C.push("USING (".concat(y.map(m).join(", "),")")),p.push(C.filter(w).join(" "))}return p.filter(w).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=m(n);switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.filter(w).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[y(t.db),y(t.table)].filter(w).join(".");return"".concat(h(e)," ").concat(n);case"column":return"COLUMN ".concat(dt(t));default:return"".concat(h(e)," ").concat(m(t))}}function pr(r){var t=r.keyword,e=r.expr;return[h(t),m(e)].filter(w).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(w).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),s=ut(e);return[u,h(n),s].filter(w).join(" ")}function dr(r){var t,e,n,o,u=r.keyword,s=r.suffix,i="";switch(h(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[a("IN",m,e&&e.right),a("FROM",cr,n),G(o)].filter(w).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===h(t.op)?a("LIKE",m,t.right):a("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=a("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(w).join(" ")}}(r);break;case"CREATE":i=a("",ir,r[s]);break;case"VAR":i=ot(r.var),u=""}return["SHOW",h(u),h(s),i].filter(w).join(" ")}var yr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,s=t.expr,a=t.orderby;return[h(u),h(n),[[y(o.schema),y(o.name)].filter(w).join("."),"(".concat(s.map(Zr).join(", ")).concat(a?[" ORDER","BY",a.map(Zr).join(", ")].join(" "):"",")")].filter(w).join(""),zr(e)].filter(w).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,u=r.expr,s=void 0===u?[]:u,a=h(t),i=cr(e),c=s.map(ut);return[a,"TABLE",h(n),m(o),i,c.join(", ")].filter(w).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[h(r.type),h(e),y(n),zr(t)].filter(w).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[h(r.type),h(e),[y(n.schema),y(n.name)].filter(w).join("."),zr(t)].filter(w).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[h(r.type),h(n),[[y(o.schema),y(o.name)].filter(w).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(w).join(""),zr(e)].filter(w).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,s=r.with,a=h(t),i=ir(u),c=[a,"VIEW",i];e&&c.push("(".concat(e.map(dt).join(", "),")"));n&&c.push("WITH ".concat(n.map(h).join(", ")));c.push("AS",q(o)),s&&c.push(h(s));return c.filter(w).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[h(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[h(t),h(e),ut(n),h(o),y(u)].filter(w).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,s=r.options,a=[h(t),h(e),h(n)],i=[y(o.schema),o.name].filter(w).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return a.push("".concat(i,"(").concat(c,")"),"(".concat(s.map(Qr).join(", "),")")),a.filter(w).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,s=r.temporary,a=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,d=r.unlogged,y=r.with,L=[h(t),h(f),h(s),h(d),h(e),h(a),cr(n)];if(o){var C=o.type,E=cr(o.table);return L.push(h(C),E),L.filter(w).join(" ")}if(b)return L.concat([Yr(b)]).filter(w).join(" ");i&&L.push("(".concat(i.map(qr).join(", "),")"));if(c){var A=p().database,g=A&&"sqlite"===A.toLowerCase()?", ":" ";L.push(c.map(lr).join(g))}if(y){var T=y.map((function(r){return[m(r.keyword),h(r.symbol),m(r.value)].join(" ")})).join(", ");L.push("WITH (".concat(T,")"))}L.push(h(l),h(u)),v&&L.push(hr(v));return L.filter(w).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,s=r.for_each,a=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[h(f),h(l),h(e),h(c),y(t),h(i)],d=_(o);v.push(d,"ON",ir(p)),a&&v.push("FROM",ir(a));v.push.apply(v,Gr(j(n)).concat(Gr(j(s)))),b&&v.push(h(b.type),ut(b.cond));return v.push(h(u.keyword),Fr(u.expr)),v.filter(w).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,s=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,d=[h(u),h(c),ut(t),h(n),h(i),ir(l),h(b),f.map((function(r){var t=[h(r.keyword)],e=r.args;return e&&t.push(h(e.keyword),e.columns.map(dt).join(", ")),t.join(" ")})),"ON",ir(s),h(e&&e.keyword),h(e&&e.args),p&&"".concat(h(p.keyword)," ").concat(y(p.trigger)),a("WHEN",ut,v),h(o.prefix)];switch(o.type){case"set":d.push(a("SET",V,o.expr));break;case"multiple":d.push(wr(o.expr.ast))}return d.push(h(o.suffix)),d.filter(w).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,s=r.type,i=r.with,c=r.version;return[h(s),h(o),h(n),m(t),h(i),a("SCHEMA",m,u),a("VERSION",m,c),a("FROM",m,e)].filter(w).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,s=r.returns,a=r.options,i=r.last,c=[h(t),h(e),h(n)],l=[m(o.schema),o.name.map(m).join(".")].filter(w).join("."),f=u.map(Zr).filter(w).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),Array.isArray(n)?"(".concat(n.map(Lt).join(", "),")"):Vr(n)].filter(w).join(" ")}(s),a.map(Xr).join(" "),i),c.filter(w).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,u=r.include,s=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,d=r.on_kw,L=r.table,C=r.tablespace,E=r.type,A=r.where,g=r.with,T=r.with_before_where,_=g&&"WITH (".concat(U(g).join(", "),")"),S=u&&"".concat(h(u.keyword)," (").concat(u.columns.map((function(r){return"string"==typeof r?y(r):ut(r)})).join(", "),")"),j=l;l&&(j="string"==typeof l?y(l):[y(l.schema),y(l.name)].filter(w).join("."));var x=[h(E),h(i),h(n),h(o),h(t),j,h(d),ir(L)].concat(Gr(k(c)),["(".concat(I(s),")"),S,U(p).join(" "),zr(b),zr(v),a("TABLESPACE",m,C)]);T?x.push(_,a("WHERE",ut,A)):x.push(a("WHERE",ut,A),_);return x.push(a("ON",ut,f),a("FILESTREAM_ON",m,e)),x.filter(w).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,s=r.create_definitions,a=[h(t),h(o),h(e),h(u),cr(n)];s&&a.push(s.map(qr).join(" "));return a.filter(w).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,u=r.create_definitions,s=r[e],a=s.db,i=s.schema,c=[m(a),i.map(m).join(".")].filter(w).join("."),l=[h(t),h(n),h(e),h(o),c];u&&l.push(u.map(lr).join(" "));return l.filter(w).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,s=r.recursive,a=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,m=p.db,C=p.schema,E=p.view,A=[y(m),y(C),y(E)].filter(w).join(".");return[h(f),h(a),h(l),h(s),t&&"ALGORITHM = ".concat(h(t)),ut(n),c&&"SQL SECURITY ".concat(h(c)),h(u),h(o),A,e&&"(".concat(e.map(d).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return L(r).join(" ")})).join(", "),")")].join(" "),"AS",hr(i),h(b)].filter(w).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,s=r.create_definitions,a=[h(n),h(o),[y(e.schema),y(e.name)].filter(w).join("."),h(t),A(u)];if(s&&s.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Hr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}(s);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(ut(f));break;case"default":c.push(h(p),ut(f.value));break;case"constraint":c.push(kr(f))}}}catch(r){l.e(r)}finally{l.f()}a.push(c.filter(w).join(" "))}return a.filter(w).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,s=[h(r.type),h(n),[y(o.schema),y(o.name)].filter(w).join("."),h(t),h(u)];if(e){var a=[];switch(u){case"enum":case"range":a.push(ut(e));break;default:a.push("(".concat(e.map(qr).join(", "),")"))}s.push(a.filter(w).join(" "))}return s.filter(w).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,s=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[xr(t)];return e&&n.push(h(e.keyword),e.auth_plugin,m(e.value)),n.filter(w).join(" ")})).join(", "),b=[h(f),h(u),h(o),p];n&&b.push(h(n.keyword),n.value.map(xr).join(", "));b.push(a(c&&c.keyword,ut,c&&c.value)),l&&b.push(h(l.keyword),l.value.map((function(r){return ut(r)})).join(" "));i&&i.forEach((function(r){return b.push(a(r.keyword,ut,r.value))}));return b.push(m(s),T(e),m(t)),b.filter(w).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[h(r.type),h(e),fr(n),pr(t)].filter(w).join(" ")},select:q,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),ut(n)].filter(w).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,s=r.with,i=r.limit,c=r.returning,l=[H(s),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(a("FROM",cr,e)),l.push(a("WHERE",ut,o)),l.push(at(u,"order by")),l.push(G(i)),l.push(S(c)),l.filter(w).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[h(t),ir(e),(n||[]).map(br).filter(w).join(", ")].filter(w).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[h(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(w).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[h(t),q(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,h(t),n,"IN",wr([o]),"LOOP",wr(u),"END LOOP",e].filter(w).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,s=r.with,i=r.limit,c=r.returning;return[H(s),"UPDATE",cr(e),a("SET",V,n),a("FROM",cr,t),a("WHERE",ut,o),at(u,"order by"),G(i),S(c)].filter(w).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,s=r.go,a=r.semicolons,i=r.suffix,c=[h(r.type),ut(t),m(u),"".concat(Er(o.ast||o)).concat(a[0]),h(s)];n&&c.push(n.map((function(r){return[h(r.type),ut(r.boolean_expr),"THEN",Er(r.then.ast||r.then),r.semicolon].filter(w).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Er(e.ast||e)).concat(a[1]));return c.push(m(i)),c.filter(w).join(" ")},insert:J,drop:Sr,truncate:Sr,replace:J,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[h(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,s=r.not_null,a=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(w).join(""),h(n),h(o)];switch(c){case"variable":l.push(yt(u),ut(r.collate),h(s)),i&&l.push(h(i.keyword),ut(i.value));break;case"cursor":l.push(h(a));break;case"table":l.push(h(a),"(".concat(i.map(qr).join(", "),")"))}return l.filter(w).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=h(t),o=y(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,s=Ar(e);try{for(s.s();!(u=s.n()).done;){var a=u.value.map(ir);n.push(a.join(" TO "))}}catch(r){s.e(r)}finally{s.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=h(t);return"".concat(n," ").concat(y(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=h(t),u=e.map(ut).join(", ");return[o,h(n),u].filter(w).join(" ")},lock:jr,unlock:jr,show:dr,grant:Ir,revoke:Ir,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[h(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[h(t),h(e)];n&&u.push([m(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(w).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(h(o.type),h(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(w).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,u=[m(e),h(n)];return o&&u.push(o.map(m).join(", ")),u.filter(w).join(" ")}};function hr(r){if(!r)return"";for(var t=yr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,s=[n&&"(",t(r)];r._next;){var a=yr[r._next.type],i=h(r.set_op);s.push(i,a(r._next)),r=r._next}return s.push(n&&")",at(o,"order by"),G(u)),s.filter(w).join(" ")}function wr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=hr(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var mr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Lr(r){var t=r&&r.ast?r.ast:r;if(!mr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Cr(r){return Array.isArray(r)?(r.forEach(Lr),wr(r)):(Lr(r),hr(r))}function Er(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Cr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Cr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Tr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function gr(r){return function(r){if(Array.isArray(r))return _r(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Tr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tr(r,t){if(r){if("string"==typeof r)return _r(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_r(r,t):void 0}}function _r(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Sr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=r.suffix,s=[h(t),h(e),h(o)];switch(e){case"table":s.push(cr(n));break;case"trigger":s.push([n[0].schema?"".concat(y(n[0].schema),"."):"",y(n[0].trigger)].filter(w).join(""));break;case"database":case"schema":case"procedure":s.push(y(n));break;case"view":s.push(cr(n),r.options&&r.options.map(ut).filter(w).join(" "));break;case"index":s.push.apply(s,[dt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(ut).filter(w).join(" ")]));break;case"type":s.push(n.map(dt).join(", "),r.options&&r.options.map(ut).filter(w).join(" "))}return u&&s.push(u.map(ut).filter(w).join(" ")),s.filter(w).join(" ")}function jr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),h(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,s=[],a=Ar(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return h(e[r])})).filter(w).join(" "))}s.push(n.join(" "))};for(a.s();!(u=a.n()).done;)i()}catch(r){a.e(r)}finally{a.f()}return o.push.apply(o,[s.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(w).join(" ")}function xr(r){var t=r.name,e=r.host,n=[m(t)];return e&&n.push("@",m(e)),n.join("")}function Ir(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,s=r.to_from,a=r.user_or_roles,i=r.with,c=[h(t),m(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(dt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(m(u.object_type),u.priv_level.map((function(r){return[y(r.prefix),y(r.name)].filter(w).join(".")})).join(", "));break;case"proxy":c.push(xr(u))}return c.push(h(s),a.map(xr).join(", ")),c.push(m(i)),c.filter(w).join(" ")}function Rr(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kr(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,s=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(h(u)),l.push(y(t));var b=h(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&y(o)),l.push.apply(l,Rr(M(r))),l.push.apply(l,Rr(ht(s))),l.push(h(n)),l.push(a("FOR",y,i)),l.push(m(c)),l.filter(w).join(" ")}}function Or(r){if(r){var t=r.type;return"rows"===t?[h(t),ut(r.expr)].filter(w).join(" "):ut(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,at(e,"partition by"),at(n,"order by"),Or(o)].filter(w).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,s=h(o);if("WINDOW"===s)return"OVER ".concat(Ur(t));if("ON UPDATE"===s){var a="".concat(h(o)," ").concat(h(n)),i=ut(e)||[];return u&&(a="".concat(a,"(").concat(i.join(", "),")")),a}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return h(t);for(var e=r.array,n=e.dimension,o=e.length,u=[],s=0;s<n;s++)u.push("["),o&&o[s]&&u.push(m(o[s])),u.push("]");return u.join("")}function $r(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,u=r.as,s=r.offset,a=r.parentheses,i=bt({expr:e,offset:s}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,d=p.dataType,L=p.parentheses,C=p.quoted,E=p.scale,A=p.suffix,g=p.expr,T=g?ut(g):"";null!=v&&(T=E?"".concat(v,", ").concat(E):v),L&&(T="(".concat(T,")")),b&&(T="<".concat(T,">")),A&&A.length&&(T+=" ".concat(A.map(m).join(" ")));var _="::",S="",j=[];"as"===o&&(0===l&&(i="".concat(h(n),"(").concat(i)),S=")",_=" ".concat(o.toUpperCase()," ")),0===l&&j.push(i);var x=Pr(p);j.push(_,C,d,C,x,T,S),c.push(j.filter(w).join(""))}u&&c.push(" AS ".concat(y(u)));var I=c.filter(w).join("");return a?"(".concat(I,")"):I}function Fr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,u=r.parentheses,s=r.within_group,a=r.over,i=r.suffix,c=Dr(a),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[h(t),h(e),"(".concat(at(n,"order by"),")")].filter(w).join(" ")}(s),f=ut(i),p=[m(n.schema),n.name.map(m).join(".")].filter(w).join(".");if(!t)return[p,l,c].filter(w).join(" ");var b=r.separator||", ";"TRIM"===h(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var d=ut(t);if(Array.isArray(b)){for(var y=d[0],L=1,C=d.length;L<C;++L)y=[y,d[L]].join(" ".concat(ut(b[L-1])," "));v.push(y)}else v.push(d.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(w).join(" "),[u?"(".concat(v,")"):v,l,c].filter(w).join(" ")}function Gr(r){return function(r){if(Array.isArray(r))return Br(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Hr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(r,t){if(r){if("string"==typeof r)return Br(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Br(r,t):void 0}}function Br(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function qr(r){if(!r)return[];var t,e,n,o,u=r.resource;switch(u){case"column":return Lt(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(h(n)),e.push(o),e.push.apply(e,R(M(t))),e.filter(w).join(" ");case"constraint":return kr(r);case"sequence":return[h(r.prefix),ut(r.value)].filter(w).join(" ");default:throw new Error("unknown resource = ".concat(u," type"))}}function Wr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(m(r.from),")"),"TO","(".concat(m(r.to),")"));break;case"in":t.push("IN","(".concat(ut(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(m(r.modulus),", REMAINDER ").concat(m(r.remainder),")"))}return t.filter(w).join(" ")}function Yr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,u=[h(t),ir(e),h(n.keyword),Wr(n.expr)];return o&&u.push("TABLESPACE",m(o)),u.filter(w).join(" ")}function Vr(r){return r.dataType?A(r):[y(r.db),y(r.schema),y(r.table)].filter(w).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[h(t),r.symbol,hr(r.declare),h(r.begin),wr(r.expr),h(r.end),r.symbol].filter(w).join(" ");case"set":return[h(t),r.parameter,h(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(w).join(" ");case"return":return[h(t),ut(r.expr)].filter(w).join(" ");default:return ut(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[h(t),e];switch(h(t)){case"SFUNC":o.push([y(n.schema),n.name].filter(w).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(ut(n))}return o.filter(w).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",m(t),"VALUES",h(e.type),"(".concat(m(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,u=r.if_exists,s=r.old_column,a=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Lt(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=y(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=y(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,h(r[i])].filter(w).join(" ");break;case"constraint":f=y(r[i]),p=[qr(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=y(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[h(t),h(o),h(n),h(u),s&&dt(s),h(a),f&&f.trim(),p.filter(w).join(" ")];return l&&b.push(h(l.keyword),l.expr&&dt(l.expr)),b.filter(w).join(" ")}function Zr(r){var t=r.default&&[h(r.default.keyword),ut(r.default.value)].join(" ");return[h(r.mode),r.name,A(r.type),t].filter(w).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(h(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(w).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[h(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(w).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr);u=Array.isArray(u)?u.join(", "):u;var s=r.name,a=Dr(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.separator&&t.separator.delimiter&&(u=[u,m(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(u=[u,ut(t.separator.expr)].join(" ")),t.orderby&&(u=[u,at(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(u=[u,h(t.separator.keyword),m(t.separator.value)].filter(w).join(" "));var i=o?"WITHIN GROUP (".concat(at(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(s,"(").concat(u,")"),i,a,c].filter(w).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,s="".concat(h(e),"(").concat(ut(o));return u&&(s="".concat(s," HAVING ").concat(h(u.prefix)," ").concat(ut(u.expr))),[s="".concat(s,")"),Dr(n)].filter(w).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=r.separator,s=void 0===u?", ":u;return[e,"(",t?ut(t).join(s):"",")",o&&" ",o].filter(w).join("")}(r),Dr(t)].filter(w).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,s=e.length;u<s;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:$r,collate:it,column_ref:dt,column_definition:Lt,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,s=["".concat(h(e),"(").concat(h(n)),"FROM",h(o),ut(u)];return"".concat(s.filter(w).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[h(t),e,ut(n)].filter(w).join(" ")}(t[r])})).filter(w).join(", ");return"".concat(h(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode,s=[h(o),"(".concat(n.map((function(r){return dt(r)})).join(", "),")")].join(" "),a=[h(t),["(",ut(r.expr),u&&" ".concat(m(u)),")"].filter(w).join("")].join(" ");return[s,a,Ct(e)].filter(w).join(" ")},function:Fr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,u=n.map(ut).join(", ");return[o?"(".concat(u,")"):u,"->",ut(e)].join(" ")},insert:hr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[h(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,u=[ut(e),"VALUE",ut(n)];return o&&u.push("ON","NULL",ut(o)),u.filter(w).join(" ")},json_visitor:function(r){return[r.symbol,ut(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,ut(t.expr)].filter(w).join(" ")},show:dr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[m(e.schema),e.name.map(m).join(".")].filter(w).join(".");return["".concat(o,"(").concat(ut(n).join(", "),")"),"AS",Fr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.quoted,s=r.suffix,a=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return s&&(c+=s),a.push(c),[u,a.join(" "),u].filter(w).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}var s=t.type;return"expr"===s?ut(t.expr):nt[s]?nt[s](t):m(t)}}function st(r){return r?(Array.isArray(r)||(r=[r]),r.map(ut)):[]}function at(r,t){if(!Array.isArray(r))return"";var e=[],n=h(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",h(r.nulls)].filter(w).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,u=e.value,s=[h(t)];return u||s.push(o),s.push(Array.isArray(n)?n.map(m).join("."):m(n)),u&&s.push(o),s.push(ut(u)),s.filter(w).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return y(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),m(r.value),"".concat(r.name?")":""),"]"].filter(w).join("")})).join("");return[ut(e),u,o].filter(w).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.brackets?"[".concat(m(o.index),"]"):"".concat(o.notation).concat(m(o.index));o.property&&(u="".concat(u,".").concat(m(o.property))),e.push(u)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function dt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,u=r.db,s=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,d=r.order_by,m=r.subFields,L=void 0===m?[]:m,C="*"===n?"*":bt(n,s),E=[u,f,p].filter(w).map((function(r){return"".concat("string"==typeof r?y(r):ut(r))})),A=E[0];if(A){for(var g=1;g<E.length;++g)A="".concat(A).concat(c[g]||".").concat(E[g]);C="".concat(A).concat(c[g]||".").concat(C)}var T=[C=["".concat(C).concat(vt(t))].concat(lt(L)).join("."),it(o),ut(l),a("AS",ut,e)];T.push("string"==typeof v?h(v):ut(v)),T.push(h(d));var _=T.filter(w).join(" ");return b?"(".concat(_,")"):_}function yt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,u=r.expr,s=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(u&&(s+=ut(u)),r.array){var a=Pr(r);s+=[/^\[.*\]$/.test(a)?"":" ",a].join("")}return s}}function ht(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,s=r.on_action;return t.push(h(n)),t.push(cr(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(h(o)),s.map((function(r){return t.push(h(r.type),ut(r.value))})),t.filter(w)}function wt(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,s=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,d=r.unique,y=r.primary_key,C=r.column_format,E=r.reference_definition,A=[h(e&&e.action),h(e&&e.value)].filter(w).join(" ");if(b||t.push(A),f){var g=f.type,_=f.value;t.push(g.toUpperCase(),ut(_))}var S=p().database;return s&&t.push(h(s.keyword),m(s.constraint)),t.push(kr(o)),t.push(function(r){if(r)return[h(r.value),"(".concat(ut(r.expr),")"),h(r.storage_type)].filter(w).join(" ")}(b)),b&&t.push(A),t.push(x(v),h(y),h(d),T(u)),t.push.apply(t,lt(L(n))),"sqlite"!==S.toLowerCase()&&t.push(ut(i)),t.push.apply(t,lt(L(C))),t.push.apply(t,lt(L(c))),t.push.apply(t,lt(ht(E))),t.push(a("USING",ut,l)),t.filter(w).join(" ")}function mt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by,s="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return s.collate=null,[ut(s),ut(e),o,h(u),h(n)].filter(w).join(" ")}function Lt(r){var t=[],e=dt(r.column),n=yt(r.definition);return t.push(e),t.push(n),t.push(wt(r)),t.filter(w).join(" ")}function Ct(r){return r?"object"===ct(r)?["AS",ut(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?y(r):d(r)].join(" "):""}function Et(r,t){var e=r.expr,n=r.type;if("cast"===n)return $r(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var s=[o],a=u.map((function(r){return Et(r,t)})).join(", ");return s.push([h(n),n&&"(",a,n&&")"].filter(w).join("")),s.filter(w).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,Ct(r.as)].filter(w).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return Et(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=st(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",u=t.join(o);return e?"(".concat(u,")"):u},nt.select=function(r){var t="object"===et(r._next)?hr(r):q(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[m(r.key),m(r.value)].join(", ")})).join(", ");return[h(t),"[".concat(e,"]")].join("")};var Tt=e(0);function _t(r){return(_t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var St,jt,xt,It=(St={},jt="trino",xt=Tt.parse,(jt=function(r){var t=function(r,t){if("object"!=_t(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=_t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==_t(t)?t:t+""}(jt))in St?Object.defineProperty(St,jt,{value:xt,enumerable:!0,configurable:!0,writable:!0}):St[jt]=xt,St);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return kt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?kt(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,s=!0,a=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return s=r.done,r},e:function(r){a=!0,u=r},f:function(){try{s||null==e.return||e.return()}finally{if(a)throw u}}}}function kt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Ot(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&Ot(r.prototype,t),e&&Ot(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),Er(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),ut(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return Et(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=t.database,n=void 0===e?"trino":e;b(t);var o=n.toLowerCase();if(It[o])return It[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||u.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var s,a=this["".concat(o,"List")].bind(this),i=a(r,e),c=!0,l="",f=Nt(i);try{for(f.s();!(s=f.n()).done;){var p,b=s.value,v=!1,d=Nt(t);try{for(d.s();!(p=d.n()).done;){var y=p.value,h=new RegExp("^".concat(y,"$"),"i");if(h.test(b)){v=!0;break}}}catch(r){d.e(r)}finally{d.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=trino.js.map