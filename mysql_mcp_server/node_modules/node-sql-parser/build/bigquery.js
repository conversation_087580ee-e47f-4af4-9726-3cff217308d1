!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},a={start:Qu},s=Qu,i=function(r,t){return dl(r,t)},c=function(r,t){return hl(r,t)},l=function(r,t){return dl(r,t)},f=Yu("=",!1),p=Yu("DUPLICATE",!0),b=Yu("BINARY",!0),v=Yu("MASTER",!0),y=Yu("LOGS",!0),d=Yu("BINLOG",!0),h=Yu("EVENTS",!0),m=Yu("CHARACTER",!0),w=Yu("SET",!0),C=Yu("COLLATION",!0),L=function(r,t){return dl(r,t,1)},A=Yu("IF",!0),E=Yu("CASCADED",!0),g=Yu("LOCAL",!0),_=Yu("CHECK",!0),j=Yu("OPTION",!1),T=Yu("check_option",!0),S=Yu("security_barrier",!0),x=Yu("security_invoker",!0),k=Yu("GRANTS",!0),I=Yu(".",!1),N=Yu("ALGORITHM",!0),O=Yu("DEFAULT",!0),R=Yu("INSTANT",!0),U=Yu("INPLACE",!0),M=Yu("COPY",!0),D=Yu("LOCK",!0),P=Yu("NONE",!0),F=Yu("SHARED",!0),H=Yu("EXCLUSIVE",!0),$=Yu("AUTO_INCREMENT",!0),W=Yu("UNIQUE",!0),Y=Yu("KEY",!0),B=Yu("PRIMARY",!0),G=Yu("FOR",!0),q=Yu("COLUMN_FORMAT",!0),V=Yu("FIXED",!0),X=Yu("DYNAMIC",!0),Q=Yu("STORAGE",!0),K=Yu("DISK",!0),z=Yu("MEMORY",!0),J=Yu("MATCH FULL",!0),Z=Yu("MATCH PARTIAL",!0),rr=Yu("MATCH SIMPLE",!0),tr=Yu("expiration_timestamp",!0),er=Yu("partition_expiration_days",!0),nr=Yu("require_partition_filter",!0),or=Yu("kms_key_name",!0),ur=Yu("friendly_name",!0),ar=Yu("description",!0),sr=Yu("labels",!0),ir=Yu("default_rounding_mode",!0),cr=Yu("AVG_ROW_LENGTH",!0),lr=Yu("KEY_BLOCK_SIZE",!0),fr=Yu("MAX_ROWS",!0),pr=Yu("MIN_ROWS",!0),br=Yu("STATS_SAMPLE_PAGES",!0),vr=Yu("CONNECTION",!0),yr=Yu("COMPRESSION",!0),dr=Yu("'",!1),hr=Yu("ZLIB",!0),mr=Yu("LZ4",!0),wr=Yu("ENGINE",!0),Cr=Yu("CLUSTER",!0),Lr=Yu("BY",!0),Ar=Yu("OPTIONS",!0),Er=Yu("CHARSET",!0),gr=Yu("COLLATE",!0),_r=Yu("READ",!0),jr=Yu("LOW_PRIORITY",!0),Tr=Yu("WRITE",!0),Sr=(Yu("FOREIGN KEY",!0),Yu("NOT",!0)),xr=(Yu("REPLICATION",!0),Yu("BTREE",!0)),kr=Yu("HASH",!0),Ir=Yu("WITH",!0),Nr=Yu("PARSER",!0),Or=Yu("VISIBLE",!0),Rr=Yu("INVISIBLE",!0),Ur=Yu("RESTRICT",!0),Mr=Yu("CASCADE",!0),Dr=Yu("SET NULL",!0),Pr=Yu("NO ACTION",!0),Fr=Yu("SET DEFAULT",!0),Hr=Yu("UPDATE",!0),$r=Yu("CREATE",!0),Wr=Yu("DELETE",!0),Yr=Yu("INSERT",!0),Br=Yu(":=",!1),Gr=Yu("return",!0),qr=Yu("REPLACE",!0),Vr=Yu("ANALYZE",!0),Xr=Yu("ATTACH",!0),Qr=Yu("DATABASE",!0),Kr=Yu("RENAME",!0),zr=Yu("SHOW",!0),Jr=Yu("DESCRIBE",!0),Zr=Yu("@",!1),rt=Yu("@@",!1),tt=Yu("$",!1),et=Yu("TEMPORARY",!0),nt=Yu("TEMP",!0),ot=Yu("SCHEMA",!0),ut=Yu("ALTER",!0),at=Yu("SPATIAL",!0),st=Yu("(",!1),it=Yu(")",!1),ct=Yu("INTERSECT",!0),lt=Yu("EXCEPT",!0),ft=Yu("SYSTEM_TIME",!0),pt=Yu("AS",!0),bt=Yu("OF",!0),vt=Yu("UNNEST",!0),yt=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;El[t]=t,e&&(El[e]=t),function(r){const t=wl(r);r.clear(),t.forEach(t=>r.add(t))}(Al)}),t},dt=/^[@]/,ht=Bu(["@"],!1,!1),mt=/^[{]/,wt=Bu(["{"],!1,!1),Ct=/^[=]/,Lt=Bu(["="],!1,!1),At=/^[}]/,Et=Bu(["}"],!1,!1),gt=Yu("TABLESAMPLE",!0),_t=Yu("BERNOULLI",!0),jt=Yu("RESERVOIR",!0),Tt=Yu("PERCENT",!0),St=Yu("ROWS",!0),xt=Yu("RANGE",!0),kt=Yu("FOLLOWING",!0),It=Yu("PRECEDING",!0),Nt=Yu("CURRENT",!0),Ot=Yu("ROW",!0),Rt=Yu("UNBOUNDED",!0),Ut=Yu("!",!1),Mt=function(r){return r[0]+" "+r[2]},Dt=Yu(">=",!1),Pt=Yu(">",!1),Ft=Yu("<=",!1),Ht=Yu("<>",!1),$t=Yu("<",!1),Wt=Yu("!=",!1),Yt=Yu("+",!1),Bt=Yu("-",!1),Gt=Yu("*",!1),qt=Yu("/",!1),Vt=Yu("%",!1),Xt=Yu("~",!1),Qt=function(r){return!0===ll[r.toUpperCase()]},Kt=Yu('"',!1),zt=/^[^"]/,Jt=Bu(['"'],!0,!1),Zt=/^[^']/,re=Bu(["'"],!0,!1),te=Yu("`",!1),ee=/^[^`]/,ne=Bu(["`"],!0,!1),oe=function(r,t){return r+t.join("")},ue=/^[A-Za-z_]/,ae=Bu([["A","Z"],["a","z"],"_"],!1,!1),se=/^[A-Za-z0-9_\-]/,ie=Bu([["A","Z"],["a","z"],["0","9"],"_","-"],!1,!1),ce=/^[A-Za-z0-9_:]/,le=Bu([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),fe=Yu(":",!1),pe=Yu("string_agg",!0),be=Yu("ANY_VALUE",!0),ve=Yu("YEAR_MONTH",!0),ye=Yu("DAY_HOUR",!0),de=Yu("DAY_MINUTE",!0),he=Yu("DAY_SECOND",!0),me=Yu("DAY_MICROSECOND",!0),we=Yu("HOUR_MINUTE",!0),Ce=Yu("HOUR_SECOND",!0),Le=Yu("HOUR_MICROSECOND",!0),Ae=Yu("MINUTE_SECOND",!0),Ee=Yu("MINUTE_MICROSECOND",!0),ge=Yu("SECOND_MICROSECOND",!0),_e=Yu("TIMEZONE_HOUR",!0),je=Yu("TIMEZONE_MINUTE",!0),Te=Yu("CENTURY",!0),Se=Yu("DAYOFWEEK",!0),xe=Yu("DAY",!0),ke=Yu("DATE",!0),Ie=Yu("DECADE",!0),Ne=Yu("DOW",!0),Oe=Yu("DOY",!0),Re=Yu("EPOCH",!0),Ue=Yu("HOUR",!0),Me=Yu("ISODOW",!0),De=Yu("ISOWEEK",!0),Pe=Yu("ISOYEAR",!0),Fe=Yu("MICROSECONDS",!0),He=Yu("MILLENNIUM",!0),$e=Yu("MILLISECONDS",!0),We=Yu("MINUTE",!0),Ye=Yu("MONTH",!0),Be=Yu("QUARTER",!0),Ge=Yu("SECOND",!0),qe=Yu("TIME",!0),Ve=Yu("TIMEZONE",!0),Xe=Yu("WEEK",!0),Qe=Yu("YEAR",!0),Ke=Yu("DATE_TRUNC",!0),ze=Yu("R",!0),Je=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},Ze=/^[^"\\\0-\x1F\x7F]/,rn=Bu(['"',"\\",["\0",""],""],!0,!1),tn=/^[^'\\]/,en=Bu(["'","\\"],!0,!1),nn=Yu("\\'",!1),on=Yu('\\"',!1),un=Yu("\\\\",!1),an=Yu("\\/",!1),sn=Yu("\\b",!1),cn=Yu("\\f",!1),ln=Yu("\\n",!1),fn=Yu("\\r",!1),pn=Yu("\\t",!1),bn=Yu("\\u",!1),vn=Yu("\\",!1),yn=Yu("''",!1),dn=Yu('""',!1),hn=Yu("``",!1),mn=/^[\n\r]/,wn=Bu(["\n","\r"],!1,!1),Cn=/^[0-9]/,Ln=Bu([["0","9"]],!1,!1),An=/^[0-9a-fA-F]/,En=Bu([["0","9"],["a","f"],["A","F"]],!1,!1),gn=/^[eE]/,_n=Bu(["e","E"],!1,!1),jn=/^[+\-]/,Tn=Bu(["+","-"],!1,!1),Sn=Yu("NULL",!0),xn=Yu("NOT NULL",!0),kn=Yu("TRUE",!0),In=Yu("TO",!0),Nn=Yu("FALSE",!0),On=Yu("DROP",!0),Rn=Yu("USE",!0),Un=Yu("SELECT",!0),Mn=Yu("RECURSIVE",!0),Dn=Yu("IGNORE",!0),Pn=(Yu("EXPLAIN",!0),Yu("PARTITION",!0)),Fn=Yu("INTO",!0),Hn=Yu("FROM",!0),$n=Yu("UNLOCK",!0),Wn=Yu("TABLE",!0),Yn=Yu("TABLES",!0),Bn=Yu("ON",!0),Gn=Yu("LEFT",!0),qn=Yu("RIGHT",!0),Vn=Yu("FULL",!0),Xn=Yu("INNER",!0),Qn=Yu("CROSS",!0),Kn=Yu("JOIN",!0),zn=Yu("OUTER",!0),Jn=Yu("OVER",!0),Zn=Yu("UNION",!0),ro=Yu("VALUE",!0),to=Yu("VALUES",!0),eo=Yu("USING",!0),no=Yu("WHERE",!0),oo=Yu("GROUP",!0),uo=Yu("ORDER",!0),ao=Yu("HAVING",!0),so=Yu("QUALIFY",!0),io=Yu("WINDOW",!0),co=Yu("ORDINAL",!0),lo=Yu("SAFE_ORDINAL",!0),fo=Yu("LIMIT",!0),po=Yu("OFFSET",!0),bo=Yu("SAFE_OFFSET",!0),vo=Yu("ASC",!0),yo=Yu("DESC",!0),ho=Yu("ALL",!0),mo=Yu("DISTINCT",!0),wo=Yu("BETWEEN",!0),Co=Yu("IN",!0),Lo=Yu("IS",!0),Ao=Yu("LIKE",!0),Eo=Yu("EXISTS",!0),go=Yu("AND",!0),_o=Yu("OR",!0),jo=Yu("COUNT",!0),To=Yu("MAX",!0),So=Yu("MIN",!0),xo=Yu("SUM",!0),ko=Yu("AVG",!0),Io=Yu("EXTRACT",!0),No=Yu("CALL",!0),Oo=Yu("CASE",!0),Ro=Yu("WHEN",!0),Uo=Yu("THEN",!0),Mo=Yu("ELSE",!0),Do=Yu("END",!0),Po=Yu("CAST",!0),Fo=Yu("SAFE_CAST",!0),Ho=Yu("ARRAY",!0),$o=Yu("BYTES",!0),Wo=Yu("BOOL",!0),Yo=(Yu("CHAR",!0),Yu("GEOGRAPHY",!0)),Bo=(Yu("VARCHAR",!0),Yu("NUMERIC",!0)),Go=Yu("DECIMAL",!0),qo=Yu("SIGNED",!0),Vo=Yu("UNSIGNED",!0),Xo=Yu("INT64",!0),Qo=(Yu("ZEROFILL",!0),Yu("INTEGER",!0)),Ko=Yu("JSON",!0),zo=(Yu("SMALLINT",!0),Yu("STRING",!0)),Jo=Yu("STRUCT",!0),Zo=(Yu("TINYINT",!0),Yu("TINYTEXT",!0),Yu("TEXT",!0),Yu("MEDIUMTEXT",!0),Yu("LONGTEXT",!0),Yu("BIGINT",!0),Yu("FLOAT64",!0)),ru=(Yu("DOUBLE",!0),Yu("DATETIME",!0)),tu=Yu("TIMESTAMP",!0),eu=Yu("TRUNCATE",!0),nu=(Yu("USER",!0),Yu("CURRENT_DATE",!0)),ou=(Yu("ADDDATE",!0),Yu("INTERVAL",!0)),uu=Yu("CURRENT_TIME",!0),au=Yu("CURRENT_TIMESTAMP",!0),su=Yu("SESSION_USER",!0),iu=Yu("GLOBAL",!0),cu=Yu("SESSION",!0),lu=Yu("PIVOT",!0),fu=Yu("PERSIST",!0),pu=Yu("PERSIST_ONLY",!0),bu=Yu("VIEW",!0),vu=Yu("ADD",!0),yu=Yu("COLUMN",!0),du=Yu("INDEX",!0),hu=Yu("FULLTEXT",!0),mu=Yu("COMMENT",!0),wu=(Yu("CONSTRAINT",!0),Yu("REFERENCES",!0)),Cu=Yu(",",!1),Lu=Yu("[",!1),Au=Yu("]",!1),Eu=Yu(";",!1),gu=Yu("||",!1),_u=Yu("&&",!1),ju=Yu("/*",!1),Tu=Yu("*/",!1),Su=Yu("--",!1),xu=Yu("#",!1),ku={type:"any"},Iu=/^[ \t\n\r]/,Nu=Bu([" ","\t","\n","\r"],!1,!1),Ou=function(r){return{dataType:r}},Ru=Yu("MAX",!1),Uu=Yu("max",!1),Mu=function(r,t){return{dataType:r,definition:t,anglebracket:!0}},Du=0,Pu=0,Fu=[{line:1,column:1}],Hu=0,$u=[],Wu=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=a[t.startRule]}function Yu(r,t){return{type:"literal",text:r,ignoreCase:t}}function Bu(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Gu(t){var e,n=Fu[t];if(n)return n;for(e=t-1;!Fu[e];)e--;for(n={line:(n=Fu[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Fu[t]=n,n}function qu(r,t){var e=Gu(r),n=Gu(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Vu(r){Du<Hu||(Du>Hu&&(Hu=Du,$u=[]),$u.push(r))}function Xu(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function Qu(){var r,t;return r=Du,Zc()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Ku())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=zc())!==u&&(s=Zc())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=zc())!==u&&(s=Zc())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Ll),columnList:wl(Al),ast:n}}(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=r,r=t):(Du=r,r=u),r}function Ku(){var t;return(t=function(){var t,e,n,o,a,s,i;(t=function(){var r,t,e,n;r=Du,(t=qa())!==u&&Zc()!==u?((e=gs())===u&&(e=null),e!==u&&Zc()!==u?((n=Ts())===u&&(n=null),n!==u&&Zc()!==u?(Pu=r,o=t,a=e,s=n,t={tableList:Array.from(Ll),columnList:wl(Al),ast:{...o.ast,_orderby:a,_limit:s,_parentheses:o._parentheses}},r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);var o,a,s;return r}())===u&&(t=Du,e=Du,40===r.charCodeAt(Du)?(n="(",Du++):(n=u,0===Wu&&Vu(st)),n!==u&&(o=Zc())!==u&&(a=Xa())!==u&&(s=Zc())!==u?(41===r.charCodeAt(Du)?(i=")",Du++):(i=u,0===Wu&&Vu(it)),i!==u?e=n=[n,o,a,s,i]:(Du=e,e=u)):(Du=e,e=u),e!==u&&(Pu=t,e={...e[2],parentheses_symbol:!0}),t=e);return t}())===u&&(t=function(){var t;(t=qa())===u&&(t=function(){var r,t,e,n,o,a,s,i;r=Du,(t=Ra())!==u&&Zc()!==u&&(e=is())!==u&&Zc()!==u&&Xi()!==u&&Zc()!==u&&(n=ua())!==u&&Zc()!==u?((o=as())===u&&(o=null),o!==u&&Zc()!==u?((a=ds())===u&&(a=null),a!==u&&Zc()!==u?((s=gs())===u&&(s=null),s!==u&&Zc()!==u?((i=Ts())===u&&(i=null),i!==u?(Pu=r,t=function(r,t,e,n,o,u){const a=r=>{const{server:t,db:e,schema:n,as:o,table:u,join:a}=r,s=a?"select":"update",i=[t,e,n].filter(Boolean).join(".")||null;e&&(dbObj[u]=i),u&&Ll.add(`${s}::${i}::${u}`)};return r&&r.forEach(a),e&&e.forEach(a),t&&t.forEach(r=>Al.add(`update::${r.table}::${r.column}`)),{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"update",table:r,set:t,where:n,orderby:o,limit:u}}}(e,n,o,a,s,i),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o,a,s,i;r=Du,(t=sa())!==u&&Zc()!==u?((e=qi())===u&&(e=null),e!==u&&Zc()!==u&&(n=bs())!==u&&Zc()!==u?((o=ia())===u&&(o=null),o!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(a=ri())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u&&(s=ca())!==u&&Zc()!==u?((i=la())===u&&(i=null),i!==u?(Pu=r,t=function(r,t,e,n,o,u){if(t&&(Ll.add(`insert::${t.db}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>Al.add(`insert::${r}::${t}`))}return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:r,table:[t],columns:n,values:o,partition:e,on_duplicate_update:u}}}(t,n,o,a,s,i),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,a,s,i,c;t=Du,(e=sa())!==u&&Zc()!==u?((n=function(){var t,e,n,o;t=Du,"ignore"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Dn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=null),n!==u&&Zc()!==u?((o=qi())===u&&(o=null),o!==u&&Zc()!==u&&(a=bs())!==u&&Zc()!==u?((s=ia())===u&&(s=null),s!==u&&Zc()!==u&&(i=ca())!==u&&Zc()!==u?((c=la())===u&&(c=null),c!==u?(Pu=t,e=function(r,t,e,n,o,u,a){n&&(Ll.add(`insert::${n.db}::${n.table}`),Al.add(`insert::${n.table}::(.*)`),n.as=null);const s=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:s,on_duplicate_update:a}}}(e,n,o,a,s,i,c),t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var r,t,e,n,o,a,s;r=Du,(t=sa())!==u&&Zc()!==u?((e=qi())===u&&(e=null),e!==u&&Zc()!==u&&(n=bs())!==u&&Zc()!==u?((o=ia())===u&&(o=null),o!==u&&Zc()!==u&&Xi()!==u&&Zc()!==u&&(a=ua())!==u&&Zc()!==u?((s=la())===u&&(s=null),s!==u?(Pu=r,i=t,l=o,f=a,p=s,(c=n)&&(Ll.add(`insert::${c.db}::${c.table}`),Al.add(`insert::${c.table}::(.*)`),c.as=null),t={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:i,table:[c],columns:null,partition:l,set:f,on_duplicate_update:p}},r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);var i,c,l,f,p;return r}())===u&&(t=function(){var r,t,e,n,o,a,s;r=Du,(t=Ma())!==u&&Zc()!==u?((e=is())===u&&(e=null),e!==u&&Zc()!==u?((n=as())===u&&(n=null),n!==u&&Zc()!==u?((o=ds())===u&&(o=null),o!==u&&Zc()!==u?((a=gs())===u&&(a=null),a!==u&&Zc()!==u?((s=Ts())===u&&(s=null),s!==u?(Pu=r,t=function(r,t,e,n,o){if(r&&r.forEach(r=>Ll.add(`delete::${r.db}::${r.table}`)),t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,u=o?"select":"delete";n&&Ll.add(`${u}::${t}::${n}`),o||Al.add(`delete::${n}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"delete",table:r,from:t,where:e,orderby:n,limit:o}}}(e,n,o,a,s),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(t=function(){var t;(t=function(){var t,e,n;t=Du,(e=function(){var t,e,n,o;t=Du,"analyze"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Vr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=bs())!==u&&Zc()!==u?(Pu=t,o=e,a=n,Ll.add(`${o}::${a.db}::${a.table}`),e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:o.toLowerCase(),table:a}},t=e):(Du=t,t=u);var o,a;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Du,(e=function(){var t,e,n,o;t=Du,"attach"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Xr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=Fa())!==u&&Zc()!==u&&(o=ks())!==u&&Zc()!==u&&(a=Qi())!==u&&Zc()!==u&&(s=ei())!==u&&Zc()!==u?(Pu=t,i=e,c=n,l=o,f=a,p=s,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:i.toLowerCase(),database:c,expr:l,as:f&&f[0].toLowerCase(),schema:p}},t=e):(Du=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var r,t,e,n,o,a;r=Du,(t=Bi())!==u&&Zc()!==u&&(e=Ki())!==u&&Zc()!==u&&(n=is())!==u?(Pu=r,s=t,i=e,(c=n)&&c.forEach(r=>Ll.add(`${s}::${r.db}::${r.table}`)),t={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:s.toLowerCase(),keyword:i.toLowerCase(),name:c}},r=t):(Du=r,r=u);var s,i,c;r===u&&(r=Du,(t=Bi())!==u&&Zc()!==u&&(e=Fc())!==u&&Zc()!==u&&(n=Zs())!==u&&Zc()!==u&&Ji()!==u&&Zc()!==u&&(o=bs())!==u&&Zc()!==u?((a=function(){var r,t,e,n,o,a;r=Du,(t=da())===u&&(t=ha());if(t!==u){for(e=[],n=Du,(o=Zc())!==u?((a=da())===u&&(a=ha()),a!==u?n=o=[o,a]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=da())===u&&(a=ha()),a!==u?n=o=[o,a]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,t=L(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())===u&&(a=null),a!==u&&Zc()!==u?(Pu=r,t=function(r,t,e,n,o){return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,a),r=t):(Du=r,r=u)):(Du=r,r=u));return r}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,a,s,i,c,f,p,b,v;r=Du,(t=Ua())!==u&&Zc()!==u?(e=Du,(n=Lc())!==u&&(o=Zc())!==u&&(a=Pa())!==u?e=n=[n,o,a]:(Du=e,e=u),e===u&&(e=null),e!==u&&(n=Zc())!==u?((o=Ba())===u&&(o=Ya()),o===u&&(o=null),o!==u&&(a=Zc())!==u&&(s=Ki())!==u&&Zc()!==u?((i=va())===u&&(i=null),i!==u&&Zc()!==u&&(c=bs())!==u&&Zc()!==u?((f=function(){var r,t,e,n,o,a,s,i,c;if(r=Du,(t=Gc())!==u)if(Zc()!==u)if((e=ma())!==u){for(n=[],o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=ma())!==u?o=a=[a,s,i,c]:(Du=o,o=u);o!==u;)n.push(o),o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=ma())!==u?o=a=[a,s,i,c]:(Du=o,o=u);n!==u&&(o=Zc())!==u&&(a=qc())!==u?(Pu=r,t=l(e,n),r=t):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;return r}())===u&&(f=null),f!==u&&Zc()!==u?((p=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ja())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Yc())===u&&(a=null),a!==u&&(s=Zc())!==u&&(i=ja())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Yc())===u&&(a=null),a!==u&&(s=Zc())!==u&&(i=ja())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,t=dl(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())===u&&(p=null),p!==u&&Zc()!==u?((b=Qi())===u&&(b=null),b!==u&&Zc()!==u?((v=qa())===u&&(v=null),v!==u?(Pu=r,y=t,d=e,h=o,m=i,C=f,L=p,A=b,E=v,(w=c)&&Ll.add(`create::${w.db}::${w.table}`),t={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:y[0].toLowerCase(),keyword:"table",temporary:h&&h[0].toLowerCase(),if_not_exists:m,table:[w],replace:d&&"or replace",as:A&&A[0].toLowerCase(),query_expr:E&&E.ast,create_definitions:C,table_options:L}},r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);var y,d,h,m,w,C,L,A,E;r===u&&(r=Du,(t=Ua())!==u&&Zc()!==u?((e=Ya())===u&&(e=null),e!==u&&(n=Zc())!==u&&(o=Ki())!==u&&(a=Zc())!==u?((s=va())===u&&(s=null),s!==u&&Zc()!==u&&(i=is())!==u&&Zc()!==u&&(c=function r(){var t,e;(t=function(){var r,t;r=Du,hc()!==u&&Zc()!==u&&(t=is())!==u?(Pu=r,r={type:"like",table:t}):(Du=r,r=u);return r}())===u&&(t=Du,Gc()!==u&&Zc()!==u&&(e=r())!==u&&Zc()!==u&&qc()!==u?(Pu=t,(n=e).parentheses=!0,t=n):(Du=t,t=u));var n;return t}())!==u?(Pu=r,t=function(r,t,e,n,o){return n&&n.forEach(r=>Ll.add(`create::${r.db}::${r.table}`)),{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,s,i,c),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,a,s;t=Du,(e=Ua())!==u&&Zc()!==u?((n=Fa())===u&&(n=function(){var t,e,n,o;t=Du,"schema"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(ot));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()),n!==u&&Zc()!==u?((o=va())===u&&(o=null),o!==u&&Zc()!==u&&(a=Li())!==u&&Zc()!==u?((s=function(){var r,t,e,n,o,a;if(r=Du,(t=Ta())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Ta())!==u?n=o=[o,a]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Ta())!==u?n=o=[o,a]:(Du=n,n=u);e!==u?(Pu=r,t=L(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())===u&&(s=null),s!==u?(Pu=t,e=function(r,t,e,n,o){const u=t.toLowerCase();return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:r[0].toLowerCase(),keyword:u,if_not_exists:e,[u]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,a,s),t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,f,p,b,v,y,d,h,m,w,C;t=Du,(e=Ua())!==u&&Zc()!==u?(n=Du,(o=Lc())!==u&&(a=Zc())!==u&&(s=Pa())!==u?n=o=[o,a,s]:(Du=n,n=u),n===u&&(n=null),n!==u&&(o=Zc())!==u?((a=Ba())===u&&(a=Ya()),a===u&&(a=null),a!==u&&(s=Zc())!==u?((i=function(){var t,e,n,o;t=Du,"recursive"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Mn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(i=null),i!==u&&Zc()!==u&&function(){var t,e,n,o;t=Du,"view"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(bu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="VIEW"):(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(c=bs())!==u&&Zc()!==u?(f=Du,(p=Gc())!==u&&(b=Zc())!==u&&(v=ri())!==u&&(y=Zc())!==u&&(d=qc())!==u?f=p=[p,b,v,y,d]:(Du=f,f=u),f===u&&(f=null),f!==u&&(p=Zc())!==u?(b=Du,(v=oc())!==u&&(y=Zc())!==u&&(d=Gc())!==u&&(h=Zc())!==u&&(m=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ya())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ya())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ya())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u&&(w=Zc())!==u&&(C=qc())!==u?b=v=[v,y,d,h,m,w,C]:(Du=b,b=u),b===u&&(b=null),b!==u&&(v=Zc())!==u&&(y=Qi())!==u&&(d=Zc())!==u&&(h=Xa())!==u&&(m=Zc())!==u?((w=function(){var t,e,n,o,a;t=Du,(e=oc())!==u&&Zc()!==u?("cascaded"===r.substr(Du,8).toLowerCase()?(n=r.substr(Du,8),Du+=8):(n=u,0===Wu&&Vu(E)),n===u&&("local"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(g))),n!==u&&Zc()!==u?("check"===r.substr(Du,5).toLowerCase()?(o=r.substr(Du,5),Du+=5):(o=u,0===Wu&&Vu(_)),o!==u&&Zc()!==u?("OPTION"===r.substr(Du,6)?(a="OPTION",Du+=6):(a=u,0===Wu&&Vu(j)),a!==u?(Pu=t,e=`with ${n.toLowerCase()} check option`,t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);t===u&&(t=Du,(e=oc())!==u&&Zc()!==u?("check"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(_)),n!==u&&Zc()!==u?("OPTION"===r.substr(Du,6)?(o="OPTION",Du+=6):(o=u,0===Wu&&Vu(j)),o!==u?(Pu=t,t=e="with check option"):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u));return t}())===u&&(w=null),w!==u?(Pu=t,L=e,A=n,T=a,S=i,k=f,I=b,N=h,O=w,(x=c).view=x.table,delete x.table,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:L[0].toLowerCase(),keyword:"view",replace:A&&"or replace",temporary:T&&T[0].toLowerCase(),recursive:S&&S.toLowerCase(),columns:k&&k[2],select:N,view:x,with_options:I&&I[4],with:O}},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var L,A,T,S,x,k,I,N,O;return t}());return t}())===u&&(t=function(){var t,e,n,o;t=Du,(e=function(){var t,e,n,o;t=Du,"truncate"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(eu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="TRUNCATE"):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=Ki())===u&&(n=null),n!==u&&Zc()!==u&&(o=is())!==u?(Pu=t,a=e,s=n,(i=o)&&i.forEach(r=>Ll.add(`${a}::${r.db}::${r.table}`)),e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:a.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:i}},t=e):(Du=t,t=u)):(Du=t,t=u);var a,s,i;return t}())===u&&(t=function(){var r,t,e;r=Du,(t=Ha())!==u&&Zc()!==u&&Ki()!==u&&Zc()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ss())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=r,(n=e).forEach(r=>r.forEach(r=>r.table&&Ll.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"rename",table:n}},r=t):(Du=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=Du,(e=function(){var t,e,n,o;t=Du,"call"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(No));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="CALL"):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=na())!==u?(Pu=t,o=n,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"call",expr:o}},t=e):(Du=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=Du,(e=function(){var t,e,n,o;t=Du,"use"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Rn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=ei())!==u?(Pu=t,o=n,Ll.add(`use::${o}::null`),e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"use",db:o}},t=e):(Du=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n,o;t=Du,(e=function(){var t,e,n,o;t=Du,"alter"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(ut));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&Ki()!==u&&Zc()!==u&&(n=is())!==u&&Zc()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Sa())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Sa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Sa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=t,s=o,(a=n)&&a.length>0&&a.forEach(r=>Ll.add(`alter::${r.db}::${r.table}`)),e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"alter",table:a,expr:s}},t=e):(Du=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o;t=Du,(e=Xi())!==u&&Zc()!==u?((n=function(){var t,e,n,o;t=Du,"global"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(iu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="GLOBAL"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Du,"session"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(cu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SESSION"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Du,"local"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(g));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="LOCAL"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Du,"persist"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(fu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="PERSIST"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Du,"persist_only"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(pu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="PERSIST_ONLY"):(Du=t,t=u)):(Du=t,t=u);return t}()),n===u&&(n=null),n!==u&&Zc()!==u&&(o=function(){var r,t,e,n,o,a,s,c;if(r=Du,(t=Ju())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=Ju())!==u?n=o=[o,a,s,c]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=Ju())!==u?n=o=[o,a,s,c]:(Du=n,n=u);e!==u?(Pu=r,t=i(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=t,a=n,(s=o).keyword=a,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"set",keyword:a,expr:s}},t=e):(Du=t,t=u)):(Du=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n;t=Du,(e=function(){var t,e,n,o;t=Du,"lock"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(D));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&zi()!==u&&Zc()!==u&&(n=function(){var r,t,e,n,o,a,s,c;if(r=Du,(t=La())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=La())!==u?n=o=[o,a,s,c]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=La())!==u?n=o=[o,a,s,c]:(Du=n,n=u);e!==u?(Pu=r,t=i(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=t,o=n,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(Du=t,t=u);var o;return t}())===u&&(t=function(){var t,e;t=Du,(e=function(){var t,e,n,o;t=Du,"unlock"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu($n));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&zi()!==u?(Pu=t,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"unlock",keyword:"tables"}},t=e):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,a,s,c,l,f;t=Du,(e=$a())!==u&&Zc()!==u?("binary"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(b)),n===u&&("master"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(v))),n!==u&&(o=Zc())!==u?("logs"===r.substr(Du,4).toLowerCase()?(a=r.substr(Du,4),Du+=4):(a=u,0===Wu&&Vu(y)),a!==u?(Pu=t,p=n,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"show",suffix:"logs",keyword:p.toLowerCase()}},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var p;t===u&&(t=Du,(e=$a())!==u&&Zc()!==u?("binlog"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(d)),n!==u&&(o=Zc())!==u?("events"===r.substr(Du,6).toLowerCase()?(a=r.substr(Du,6),Du+=6):(a=u,0===Wu&&Vu(h)),a!==u&&(s=Zc())!==u?((c=Ys())===u&&(c=null),c!==u&&Zc()!==u?((l=as())===u&&(l=null),l!==u&&Zc()!==u?((f=Ts())===u&&(f=null),f!==u?(Pu=t,L=c,A=l,E=f,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"show",suffix:"events",keyword:"binlog",in:L,from:A,limit:E}},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=$a())!==u&&Zc()!==u?(n=Du,"character"===r.substr(Du,9).toLowerCase()?(o=r.substr(Du,9),Du+=9):(o=u,0===Wu&&Vu(m)),o!==u&&(a=Zc())!==u?("set"===r.substr(Du,3).toLowerCase()?(s=r.substr(Du,3),Du+=3):(s=u,0===Wu&&Vu(w)),s!==u?n=o=[o,a,s]:(Du=n,n=u)):(Du=n,n=u),n===u&&("collation"===r.substr(Du,9).toLowerCase()?(n=r.substr(Du,9),Du+=9):(n=u,0===Wu&&Vu(C))),n!==u&&(o=Zc())!==u?((a=Ws())===u&&(a=ds()),a===u&&(a=null),a!==u?(Pu=t,e=function(r,t){let e=Array.isArray(r)&&r||[r];return{tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"show",suffix:e[2]&&e[2].toLowerCase(),keyword:e[0].toLowerCase(),expr:t}}}(n,a),t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=function(){var t,e,n,o;t=Du,(e=$a())!==u&&Zc()!==u?("grants"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(k)),n!==u&&Zc()!==u?((o=function(){var t,e,n,o,a,s,c;t=Du,"for"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(G));e!==u&&Zc()!==u&&(n=ei())!==u&&Zc()!==u?(o=Du,(a=Wa())!==u&&(s=Zc())!==u&&(c=ei())!==u?o=a=[a,s,c]:(Du=o,o=u),o===u&&(o=null),o!==u&&(a=Zc())!==u?((s=function(){var r,t;r=Du,nc()!==u&&Zc()!==u&&(t=function(){var r,t,e,n,o,a,s,c;if(r=Du,(t=ei())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=ei())!==u?n=o=[o,a,s,c]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=ei())!==u?n=o=[o,a,s,c]:(Du=n,n=u);e!==u?(Pu=r,t=i(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=r,r=t):(Du=r,r=u);return r}())===u&&(s=null),s!==u?(Pu=t,f=s,e={user:n,host:(l=o)&&l[2],role_list:f},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var l,f;return t}())===u&&(o=null),o!==u?(Pu=t,a=o,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"show",keyword:"grants",for:a}},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var a;return t}())));var L,A,E;return t}())===u&&(t=function(){var t,e,n;t=Du,(e=fc())===u&&(e=function(){var t,e,n,o;t=Du,"describe"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Jr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}());e!==u&&Zc()!==u&&(n=ei())!==u?(Pu=t,o=n,e={tableList:Array.from(Ll),columnList:wl(Al),ast:{type:"desc",table:o}},t=e):(Du=t,t=u);var o;return t}());return t}())===u&&(t=function(){var r,t;r=[],t=zu();for(;t!==u;)r.push(t),t=zu();return r}());return t}()),t}function zu(){var t,e;return t=Du,Pu=Du,Cl=[],(!0?void 0:u)!==u&&Zc()!==u?((e=Ju())===u&&(e=function(){var t,e;t=Du,function(){var t;"return"===r.substr(Du,6).toLowerCase()?(t=r.substr(Du,6),Du+=6):(t=u,0===Wu&&Vu(Gr));return t}()!==u&&Zc()!==u&&(e=Zu())!==u?(Pu=t,t={type:"return",expr:e}):(Du=t,t=u);return t}()),e!==u?(Pu=t,t={stmt:e,vars:Cl}):(Du=t,t=u)):(Du=t,t=u),t}function Ju(){var t,e,n,o;return t=Du,(e=fa())===u&&(e=pa()),e!==u&&Zc()!==u?((n=function(){var t;":="===r.substr(Du,2)?(t=":=",Du+=2):(t=u,0===Wu&&Vu(Br));return t}())===u&&(n=Da()),n!==u&&Zc()!==u&&(o=Zu())!==u?(Pu=t,t=e={type:"assign",left:e,symbol:n,right:o}):(Du=t,t=u)):(Du=t,t=u),t}function Zu(){var r;return(r=Xa())===u&&(r=function(){var r,t,e,n,o;r=Du,(t=fa())!==u&&Zc()!==u&&(e=ps())!==u&&Zc()!==u&&(n=fa())!==u&&Zc()!==u&&(o=ys())!==u?(Pu=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Du=r,r=u);return r}())===u&&(r=ra())===u&&(r=function(){var r,t;r=Du,Qc()!==u&&Zc()!==u&&(t=oa())!==u&&Zc()!==u&&Kc()!==u?(Pu=r,r={type:"array",value:t,brackets:!0}):(Du=r,r=u);return r}()),r}function ra(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ta())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Gs())!==u&&(s=Zc())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Gs())!==u&&(s=Zc())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=c(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function ta(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ea())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Vs())!==u&&(s=Zc())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Vs())!==u&&(s=Zc())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=c(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function ea(){var r,t,e;return(r=ji())===u&&(r=fa())===u&&(r=na())===u&&(r=di())===u&&(r=Du,Gc()!==u&&Zc()!==u&&(t=ra())!==u&&Zc()!==u&&qc()!==u?(Pu=r,(e=t).parentheses=!0,r=e):(Du=r,r=u)),r}function na(){var r,t,e;return r=Du,(t=Li())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u?((e=oa())===u&&(e=null),e!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...pl()}):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=Li())!==u&&(Pu=r,t=function(r){return{type:"function",name:r,args:null,...pl()}}(t)),r=t),r}function oa(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ea())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function ua(){var r,t,e,n,o,a,s,i;if(r=Du,(t=aa())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=aa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=aa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function aa(){var t,e,n,o,a,s,i,c,l;return t=Du,e=Du,(n=ei())!==u&&(o=Zc())!==u&&(a=Wc())!==u?e=n=[n,o,a]:(Du=e,e=u),e===u&&(e=null),e!==u&&(n=Zc())!==u&&(o=ii())!==u&&(a=Zc())!==u?(61===r.charCodeAt(Du)?(s="=",Du++):(s=u,0===Wu&&Vu(f)),s!==u&&Zc()!==u&&(i=Bs())!==u?(Pu=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,e=Du,(n=ei())!==u&&(o=Zc())!==u&&(a=Wc())!==u?e=n=[n,o,a]:(Du=e,e=u),e===u&&(e=null),e!==u&&(n=Zc())!==u&&(o=ii())!==u&&(a=Zc())!==u?(61===r.charCodeAt(Du)?(s="=",Du++):(s=u,0===Wu&&Vu(f)),s!==u&&Zc()!==u&&(i=ec())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(c=Zs())!==u&&Zc()!==u&&qc()!==u?(Pu=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Du=t,t=u)):(Du=t,t=u)),t}function sa(){var t,e;return t=Du,(e=function(){var t,e,n,o;t=Du,"insert"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Yr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e="insert"),(t=e)===u&&(t=Du,(e=Pa())!==u&&(Pu=t,e="replace"),t=e),t}function ia(){var r,t,e,n,o,a,s,i,c;if(r=Du,Gi()!==u)if(Zc()!==u)if((t=Gc())!==u)if(Zc()!==u)if((e=pi())!==u){for(n=[],o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=pi())!==u?o=a=[a,s,i,c]:(Du=o,o=u);o!==u;)n.push(o),o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=pi())!==u?o=a=[a,s,i,c]:(Du=o,o=u);n!==u&&(o=Zc())!==u&&(a=qc())!==u?(Pu=r,r=dl(e,n)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;return r===u&&(r=Du,Gi()!==u&&Zc()!==u&&(t=ba())!==u?(Pu=r,r=t):(Du=r,r=u)),r}function ca(){var r;return(r=function(){var r,t;r=Du,ec()!==u&&Zc()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ba())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ba())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ba())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=r,r=t):(Du=r,r=u);return r}())===u&&(r=Ka()),r}function la(){var t,e,n;return t=Du,Ji()!==u&&Zc()!==u?("duplicate"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(p)),e!==u&&Zc()!==u&&Hc()!==u&&Zc()!==u&&Ra()!==u&&Zc()!==u&&(n=ua())!==u?(Pu=t,t={keyword:"on duplicate key update",set:n}):(Du=t,t=u)):(Du=t,t=u),t}function fa(){var t,e,n,o,a;return t=Du,(e=function(){var t;(t=function(){var t;"@@"===r.substr(Du,2)?(t="@@",Du+=2):(t=u,0===Wu&&Vu(rt));return t}())===u&&(t=Wa())===u&&(t=function(){var t;36===r.charCodeAt(Du)?(t="$",Du++):(t=u,0===Wu&&Vu(tt));return t}());return t}())!==u&&(n=pa())!==u?(Pu=t,o=e,a=n,t=e={type:"var",...a,prefix:o}):(Du=t,t=u),t}function pa(){var t,e,n,o,a;return t=Du,(e=pi())!==u&&(n=function(){var t,e,n,o,a;t=Du,e=[],n=Du,46===r.charCodeAt(Du)?(o=".",Du++):(o=u,0===Wu&&Vu(I));o!==u&&(a=pi())!==u?n=o=[o,a]:(Du=n,n=u);for(;n!==u;)e.push(n),n=Du,46===r.charCodeAt(Du)?(o=".",Du++):(o=u,0===Wu&&Vu(I)),o!==u&&(a=pi())!==u?n=o=[o,a]:(Du=n,n=u);e!==u&&(Pu=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(Pu=t,o=e,a=n,Cl.push(o),t=e={type:"var",name:o,members:a,prefix:null}):(Du=t,t=u),t===u&&(t=Du,(e=Ri())!==u&&(Pu=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function ba(){var r,t;return r=Du,Gc()!==u&&Zc()!==u&&(t=Ss())!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t):(Du=r,r=u),r}function va(){var t,e;return t=Du,"if"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(A)),e!==u&&Zc()!==u&&wc()!==u&&Zc()!==u&&mc()!==u?(Pu=t,t=e="IF NOT EXISTS"):(Du=t,t=u),t}function ya(){var t,e,n;return t=Du,"check_option"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(T)),e!==u&&Zc()!==u&&Da()!==u&&Zc()!==u?("cascaded"===r.substr(Du,8).toLowerCase()?(n=r.substr(Du,8),Du+=8):(n=u,0===Wu&&Vu(E)),n===u&&("local"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(g))),n!==u?(Pu=t,t=e={type:"check_option",value:n,symbol:"="}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"security_barrier"===r.substr(Du,16).toLowerCase()?(e=r.substr(Du,16),Du+=16):(e=u,0===Wu&&Vu(S)),e===u&&("security_invoker"===r.substr(Du,16).toLowerCase()?(e=r.substr(Du,16),Du+=16):(e=u,0===Wu&&Vu(x))),e!==u&&Zc()!==u&&Da()!==u&&Zc()!==u&&(n=xi())!==u?(Pu=t,t=e=function(r,t){return{type:r.toLowerCase(),value:t.value?"true":"false",symbol:"="}}(e,n)):(Du=t,t=u)),t}function da(){var t,e,n,o;return t=Du,"algorithm"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(N)),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u?("default"===r.substr(Du,7).toLowerCase()?(o=r.substr(Du,7),Du+=7):(o=u,0===Wu&&Vu(O)),o===u&&("instant"===r.substr(Du,7).toLowerCase()?(o=r.substr(Du,7),Du+=7):(o=u,0===Wu&&Vu(R)),o===u&&("inplace"===r.substr(Du,7).toLowerCase()?(o=r.substr(Du,7),Du+=7):(o=u,0===Wu&&Vu(U)),o===u&&("copy"===r.substr(Du,4).toLowerCase()?(o=r.substr(Du,4),Du+=4):(o=u,0===Wu&&Vu(M))))),o!==u?(Pu=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function ha(){var t,e,n,o;return t=Du,"lock"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(D)),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u?("default"===r.substr(Du,7).toLowerCase()?(o=r.substr(Du,7),Du+=7):(o=u,0===Wu&&Vu(O)),o===u&&("none"===r.substr(Du,4).toLowerCase()?(o=r.substr(Du,4),Du+=4):(o=u,0===Wu&&Vu(P)),o===u&&("shared"===r.substr(Du,6).toLowerCase()?(o=r.substr(Du,6),Du+=6):(o=u,0===Wu&&Vu(F)),o===u&&("exclusive"===r.substr(Du,9).toLowerCase()?(o=r.substr(Du,9),Du+=9):(o=u,0===Wu&&Vu(H))))),o!==u?(Pu=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function ma(){var t;return(t=Ca())===u&&(t=function(){var r,t,e,n,o,a;r=Du,(t=Fc())===u&&(t=Hc());t!==u&&Zc()!==u?((e=li())===u&&(e=null),e!==u&&Zc()!==u?((n=xa())===u&&(n=null),n!==u&&Zc()!==u&&(o=ka())!==u&&Zc()!==u?((a=Ia())===u&&(a=null),a!==u&&Zc()!==u?(Pu=r,s=n,i=a,t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i},r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);var s,i;return r}())===u&&(t=function(){var t,e,n,o,a,s;t=Du,(e=function(){var t,e,n,o;t=Du,"fulltext"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(hu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="FULLTEXT"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Du,"spatial"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(at));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}());e!==u&&Zc()!==u?((n=Fc())===u&&(n=Hc()),n===u&&(n=null),n!==u&&Zc()!==u?((o=li())===u&&(o=null),o!==u&&Zc()!==u&&(a=ka())!==u&&Zc()!==u?((s=Ia())===u&&(s=null),s!==u?(Pu=t,i=e,l=s,e={index:o,definition:a,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var i,c,l;return t}()),t}function wa(){var t,e,n,o;return t=Du,(e=function(){var t,e;t=Du,(e=function(){var t,e,n,o;t=Du,"not null"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(xn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={type:"not null",value:"not null"});return t=e}())===u&&(e=Si()),e!==u&&(Pu=t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===u&&(t=Du,(e=function(){var r,t;r=Du,Wi()!==u&&Zc()!==u&&(t=ks())!==u?(Pu=r,r={type:"default",value:t}):(Du=r,r=u);return r}())!==u&&(Pu=t,e={default_val:e}),(t=e)===u&&(t=Du,"auto_increment"===r.substr(Du,14).toLowerCase()?(e=r.substr(Du,14),Du+=14):(e=u,0===Wu&&Vu($)),e!==u&&(Pu=t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=Du,"unique"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(W)),e!==u&&Zc()!==u?("key"===r.substr(Du,3).toLowerCase()?(n=r.substr(Du,3),Du+=3):(n=u,0===Wu&&Vu(Y)),n===u&&(n=null),n!==u?(Pu=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"primary"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(B)),e===u&&(e=null),e!==u&&Zc()!==u?("key"===r.substr(Du,3).toLowerCase()?(n=r.substr(Du,3),Du+=3):(n=u,0===Wu&&Vu(Y)),n!==u?(Pu=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Aa())!==u&&(Pu=t,e={comment:e}),(t=e)===u&&(t=Du,(e=Ea())!==u&&(Pu=t,e={collate:e}),(t=e)===u&&(t=Du,(e=function(){var t,e,n;t=Du,"column_format"===r.substr(Du,13).toLowerCase()?(e=r.substr(Du,13),Du+=13):(e=u,0===Wu&&Vu(q));e!==u&&Zc()!==u?("fixed"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(V)),n===u&&("dynamic"===r.substr(Du,7).toLowerCase()?(n=r.substr(Du,7),Du+=7):(n=u,0===Wu&&Vu(X)),n===u&&("default"===r.substr(Du,7).toLowerCase()?(n=r.substr(Du,7),Du+=7):(n=u,0===Wu&&Vu(O)))),n!==u?(Pu=t,e={type:"column_format",value:n.toLowerCase()},t=e):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={column_format:e}),(t=e)===u&&(t=Du,(e=function(){var t,e,n;t=Du,"storage"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Q));e!==u&&Zc()!==u?("disk"===r.substr(Du,4).toLowerCase()?(n=r.substr(Du,4),Du+=4):(n=u,0===Wu&&Vu(K)),n===u&&("memory"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(z))),n!==u?(Pu=t,e={type:"storage",value:n.toLowerCase()},t=e):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={storage:e}),(t=e)===u&&(t=Du,(e=ga())!==u&&(Pu=t,e={reference_definition:e}),t=e))))))))),t}function Ca(){var r,t,e,n,o,a,s;return r=Du,(t=Zs())!==u&&Zc()!==u&&(e=sl())!==u&&Zc()!==u?((n=function(){var r,t,e,n,o,a;if(r=Du,(t=wa())!==u)if(Zc()!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=wa())!==u?n=o=[o,a]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=wa())!==u?n=o=[o,a]:(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;return r}())===u&&(n=null),n!==u?(Pu=r,o=t,a=e,s=n,Al.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:a,resource:"column",...s||{}}):(Du=r,r=u)):(Du=r,r=u),r}function La(){var t,e,n,o,a;return t=Du,(e=fs())!==u&&Zc()!==u&&(n=function(){var t,e,n;t=Du,"read"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(_r));e!==u&&Zc()!==u?("local"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(g)),n===u&&(n=null),n!==u?(Pu=t,t=e={type:"read",suffix:n&&"local"}):(Du=t,t=u)):(Du=t,t=u);t===u&&(t=Du,"low_priority"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(jr)),e===u&&(e=null),e!==u&&Zc()!==u?("write"===r.substr(Du,5).toLowerCase()?(n=r.substr(Du,5),Du+=5):(n=u,0===Wu&&Vu(Tr)),n!==u?(Pu=t,t=e={type:"write",prefix:e&&"low_priority"}):(Du=t,t=u)):(Du=t,t=u));return t}())!==u?(Pu=t,o=e,a=n,Ll.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:a}):(Du=t,t=u),t}function Aa(){var r,t,e,n,o,a,s;return r=Du,(t=$c())!==u&&Zc()!==u?((e=Da())===u&&(e=null),e!==u&&Zc()!==u&&(n=ki())!==u?(Pu=r,a=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:a,value:s}):(Du=r,r=u)):(Du=r,r=u),r}function Ea(){var t,e,n;return t=Du,function(){var t,e,n,o;t=Du,"collate"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(gr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="COLLATE"):(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u?((e=Da())===u&&(e=null),e!==u&&Zc()!==u&&(n=ei())!==u?(Pu=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Du=t,t=u)):(Du=t,t=u),t}function ga(){var t,e,n,o,a,s,i,c,l,f;return t=Du,(e=function(){var t,e,n,o;t=Du,"references"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(wu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="REFERENCES"):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=is())!==u&&Zc()!==u&&(o=ka())!==u&&Zc()!==u?("match full"===r.substr(Du,10).toLowerCase()?(a=r.substr(Du,10),Du+=10):(a=u,0===Wu&&Vu(J)),a===u&&("match partial"===r.substr(Du,13).toLowerCase()?(a=r.substr(Du,13),Du+=13):(a=u,0===Wu&&Vu(Z)),a===u&&("match simple"===r.substr(Du,12).toLowerCase()?(a=r.substr(Du,12),Du+=12):(a=u,0===Wu&&Vu(rr)))),a===u&&(a=null),a!==u&&Zc()!==u?((s=Oa())===u&&(s=null),s!==u&&Zc()!==u?((i=Oa())===u&&(i=null),i!==u?(Pu=t,c=a,l=s,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Oa())!==u&&(Pu=t,e={on_action:[e]}),t=e),t}function _a(){var t,e,n,o;return t=Du,"expiration_timestamp"===r.substr(Du,20).toLowerCase()?(e=r.substr(Du,20),Du+=20):(e=u,0===Wu&&Vu(tr)),e===u&&("partition_expiration_days"===r.substr(Du,25).toLowerCase()?(e=r.substr(Du,25),Du+=25):(e=u,0===Wu&&Vu(er)),e===u&&("require_partition_filter"===r.substr(Du,24).toLowerCase()?(e=r.substr(Du,24),Du+=24):(e=u,0===Wu&&Vu(nr)),e===u&&("kms_key_name"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(or)),e===u&&("friendly_name"===r.substr(Du,13).toLowerCase()?(e=r.substr(Du,13),Du+=13):(e=u,0===Wu&&Vu(ur)),e===u&&("description"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(ar)),e===u&&("labels"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(sr)),e===u&&("default_rounding_mode"===r.substr(Du,21).toLowerCase()?(e=r.substr(Du,21),Du+=21):(e=u,0===Wu&&Vu(ir))))))))),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u&&(o=ks())!==u?(Pu=t,t=e={keyword:e,symbol:"=",value:o}):(Du=t,t=u)):(Du=t,t=u),t}function ja(){var t,e,n,o,a,s,c,l,f;return t=Du,"auto_increment"===r.substr(Du,14).toLowerCase()?(e=r.substr(Du,14),Du+=14):(e=u,0===Wu&&Vu($)),e===u&&("avg_row_length"===r.substr(Du,14).toLowerCase()?(e=r.substr(Du,14),Du+=14):(e=u,0===Wu&&Vu(cr)),e===u&&("key_block_size"===r.substr(Du,14).toLowerCase()?(e=r.substr(Du,14),Du+=14):(e=u,0===Wu&&Vu(lr)),e===u&&("max_rows"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(fr)),e===u&&("min_rows"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(pr)),e===u&&("stats_sample_pages"===r.substr(Du,18).toLowerCase()?(e=r.substr(Du,18),Du+=18):(e=u,0===Wu&&Vu(br))))))),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u&&(o=Ri())!==u?(Pu=t,l=n,f=o,t=e={keyword:e.toLowerCase(),symbol:l,value:f.value}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Ta())===u&&(t=Du,(e=$c())===u&&("connection"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(vr))),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u&&(o=ki())!==u?(Pu=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"compression"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(yr)),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u?(o=Du,39===r.charCodeAt(Du)?(a="'",Du++):(a=u,0===Wu&&Vu(dr)),a!==u?("zlib"===r.substr(Du,4).toLowerCase()?(s=r.substr(Du,4),Du+=4):(s=u,0===Wu&&Vu(hr)),s===u&&("lz4"===r.substr(Du,3).toLowerCase()?(s=r.substr(Du,3),Du+=3):(s=u,0===Wu&&Vu(mr)),s===u&&("none"===r.substr(Du,4).toLowerCase()?(s=r.substr(Du,4),Du+=4):(s=u,0===Wu&&Vu(P)))),s!==u?(39===r.charCodeAt(Du)?(c="'",Du++):(c=u,0===Wu&&Vu(dr)),c!==u?o=a=[a,s,c]:(Du=o,o=u)):(Du=o,o=u)):(Du=o,o=u),o!==u?(Pu=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"engine"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(wr)),e!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u&&(o=pi())!==u?(Pu=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Gi())!==u&&Zc()!==u&&(n=uc())!==u&&Zc()!==u&&(o=ks())!==u?(Pu=t,t=e=function(r){return{keyword:"partition by",value:r}}(o)):(Du=t,t=u),t===u&&(t=Du,"cluster"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Cr)),e!==u&&Zc()!==u?("by"===r.substr(Du,2).toLowerCase()?(n=r.substr(Du,2),Du+=2):(n=u,0===Wu&&Vu(Lr)),n!==u&&Zc()!==u&&(o=ri())!==u?(Pu=t,t=e={keyword:"cluster by",value:o}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"options"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Ar)),e!==u&&Zc()!==u&&(n=Gc())!==u&&Zc()!==u&&(o=function(){var r,t,e,n,o,a,s,c;if(r=Du,(t=_a())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=_a())!==u?n=o=[o,a,s,c]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(c=_a())!==u?n=o=[o,a,s,c]:(Du=n,n=u);e!==u?(Pu=r,r=t=i(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}())!==u&&(a=Zc())!==u&&(s=qc())!==u?(Pu=t,t=e=function(r){return{keyword:"options",parentheses:!0,value:r}}(o)):(Du=t,t=u))))))),t}function Ta(){var t,e,n,o,a,s,i,c,l;return t=Du,(e=Wi())===u&&(e=null),e!==u&&Zc()!==u?((n=function(){var t,e,n;t=Du,"character"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(m));e!==u&&Zc()!==u?("set"===r.substr(Du,3).toLowerCase()?(n=r.substr(Du,3),Du+=3):(n=u,0===Wu&&Vu(w)),n!==u?(Pu=t,t=e="CHARACTER SET"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&("charset"===r.substr(Du,7).toLowerCase()?(n=r.substr(Du,7),Du+=7):(n=u,0===Wu&&Vu(Er)),n===u&&("collate"===r.substr(Du,7).toLowerCase()?(n=r.substr(Du,7),Du+=7):(n=u,0===Wu&&Vu(gr)))),n!==u&&Zc()!==u?((o=Da())===u&&(o=null),o!==u&&Zc()!==u&&(a=ti())!==u?(Pu=t,i=n,c=o,l=a,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function Sa(){var t;return(t=function(){var t,e,n,o;t=Du,(e=function(){var t,e,n,o;t=Du,"add"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(vu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ADD"):(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=Pc())===u&&(n=null),n!==u&&Zc()!==u&&(o=Ca())!==u?(Pu=t,a=n,s=o,e={action:"add",...s,keyword:a,resource:"column",type:"alter"},t=e):(Du=t,t=u)):(Du=t,t=u);var a,s;return t}())===u&&(t=function(){var r,t,e;r=Du,Bi()!==u&&Zc()!==u?((t=Pc())===u&&(t=null),t!==u&&Zc()!==u&&(e=Zs())!==u?(Pu=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(t=function(){var r,t,e,n;r=Du,(t=Ha())!==u&&Zc()!==u?((e=Yi())===u&&(e=Qi()),e===u&&(e=null),e!==u&&Zc()!==u&&(n=ei())!==u?(Pu=r,a=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:a},r=t):(Du=r,r=u)):(Du=r,r=u);var o,a;return r}()),t}function xa(){var t,e;return t=Du,nc()!==u&&Zc()!==u?("btree"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(xr)),e===u&&("hash"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(kr))),e!==u?(Pu=t,t={keyword:"using",type:e.toLowerCase()}):(Du=t,t=u)):(Du=t,t=u),t}function ka(){var r,t,e,n,o,a,s,i;if(r=Du,Gc()!==u)if(Zc()!==u)if((t=li())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=li())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=li())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u&&(n=Zc())!==u&&(o=qc())!==u?(Pu=r,r=l(t,e)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;return r}function Ia(){var r,t,e,n,o,a;if(r=Du,(t=Na())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Na())!==u?n=o=[o,a]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Na())!==u?n=o=[o,a]:(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Na(){var t,e,n,o,a,s;return t=Du,(e=function(){var t,e,n,o;t=Du,"key_block_size"===r.substr(Du,14).toLowerCase()?(e=r.substr(Du,14),Du+=14):(e=u,0===Wu&&Vu(lr));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=Da())===u&&(n=null),n!==u&&Zc()!==u&&(o=Ri())!==u?(Pu=t,a=n,s=o,t=e={type:e.toLowerCase(),symbol:a,expr:s}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=xa())===u&&(t=Du,"with"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ir)),e!==u&&Zc()!==u?("parser"===r.substr(Du,6).toLowerCase()?(n=r.substr(Du,6),Du+=6):(n=u,0===Wu&&Vu(Nr)),n!==u&&Zc()!==u&&(o=pi())!==u?(Pu=t,t=e={type:"with parser",expr:o}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"visible"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Or)),e===u&&("invisible"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Rr))),e!==u&&(Pu=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=Aa()))),t}function Oa(){var t,e,n,o;return t=Du,Ji()!==u&&Zc()!==u?((e=Ma())===u&&(e=Ra()),e!==u&&Zc()!==u&&(n=function(){var t,e,n;t=Du,(e=Dc())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u?((n=Ss())===u&&(n=null),n!==u&&Zc()!==u&&qc()!==u?(Pu=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Du=t,t=u)):(Du=t,t=u);t===u&&(t=Du,"restrict"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Ur)),e===u&&("cascade"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Mr)),e===u&&("set null"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Dr)),e===u&&("no action"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Pr)),e===u&&("set default"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(Fr)),e===u&&(e=Dc()))))),e!==u&&(Pu=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(Pu=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Du=t,t=u)):(Du=t,t=u),t}function Ra(){var t,e,n,o;return t=Du,"update"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Hr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ua(){var t,e,n,o;return t=Du,"create"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu($r)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ma(){var t,e,n,o;return t=Du,"delete"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Wr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Da(){var t;return 61===r.charCodeAt(Du)?(t="=",Du++):(t=u,0===Wu&&Vu(f)),t}function Pa(){var t,e,n,o;return t=Du,"replace"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(qr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Fa(){var t,e,n,o;return t=Du,"database"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Qr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ha(){var t,e,n,o;return t=Du,"rename"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Kr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function $a(){var t,e,n,o;return t=Du,"show"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(zr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Wa(){var t;return 64===r.charCodeAt(Du)?(t="@",Du++):(t=u,0===Wu&&Vu(Zr)),t}function Ya(){var t,e,n,o;return t=Du,"temporary"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(et)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ba(){var t,e,n,o;return t=Du,"temp"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(nt)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ga(){var t,e,n,o;return t=Du,(e=function(){var t,e,n,o;t=Du,"union"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Zn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=pc())===u&&(n=bc()),n===u&&(n=null),n!==u?(Pu=t,t=e=(o=n)?"union "+o.toLowerCase():"union"):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,"intersect"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(ct)),e===u&&("except"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(lt))),e!==u&&Zc()!==u&&(n=bc())!==u?(Pu=t,t=e=function(r,t){return`${r.toLowerCase()} ${t.toLowerCase()}`}(e,n)):(Du=t,t=u)),t}function qa(){var t,e,n,o,a,s,i;return(t=Va())===u&&(t=Du,e=Du,40===r.charCodeAt(Du)?(n="(",Du++):(n=u,0===Wu&&Vu(st)),n!==u&&(o=Zc())!==u&&(a=Va())!==u&&(s=Zc())!==u?(41===r.charCodeAt(Du)?(i=")",Du++):(i=u,0===Wu&&Vu(it)),i!==u?e=n=[n,o,a,s,i]:(Du=e,e=u)):(Du=e,e=u),e!==u&&(Pu=t,e={...e[2],_parentheses:!0}),t=e),t}function Va(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Xa())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Ga())===u&&(a=null),a!==u&&(s=Zc())!==u&&(i=Xa())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Ga())===u&&(a=null),a!==u&&(s=Zc())!==u&&(i=Xa())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u&&(n=Zc())!==u?((o=gs())===u&&(o=null),o!==u&&(a=Zc())!==u?((s=Ts())===u&&(s=null),s!==u?(Pu=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return{tableList:Array.from(Ll),columnList:wl(Al),ast:r}}(t,e)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)}else Du=r,r=u;return r}function Xa(){var t,e,n,o,a,s,i;return(t=Ka())===u&&(t=Du,e=Du,40===r.charCodeAt(Du)?(n="(",Du++):(n=u,0===Wu&&Vu(st)),n!==u&&(o=Zc())!==u&&(a=Xa())!==u&&(s=Zc())!==u?(41===r.charCodeAt(Du)?(i=")",Du++):(i=u,0===Wu&&Vu(it)),i!==u?e=n=[n,o,a,s,i]:(Du=e,e=u)):(Du=e,e=u),e!==u&&(Pu=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Qa(){var r,t,e,n;return r=Du,(t=ki())===u&&(t=pi()),t!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(e=qa())!==u&&Zc()!==u&&qc()!==u?(Pu=r,"string"==typeof(n=t)&&(n={type:"default",value:n}),r=t={name:n,stmt:e}):(Du=r,r=u),r}function Ka(){var t,e,n,o,a,s,i,c,f,p,b,v,y,d,h,m,w,C,L,A,E,g,_,j,T,S,x;return t=Du,Zc()!==u?((e=function(){var r,t,e,n,o,a,s,i;if(r=Du,oc()!==u)if(Zc()!==u)if((t=Qa())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Qa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Qa())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=l(t,e)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;return r}())===u&&(e=null),e!==u&&Zc()!==u&&function(){var t,e,n,o;t=Du,"select"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Un));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&rl()!==u?((n=function(){var t,e,n;t=Du,(e=Qi())!==u&&Zc()!==u?((n=kc())===u&&(n=function(){var t,e,n,o;t=Du,"value"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(ro));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="VALUE"):(Du=t,t=u)):(Du=t,t=u);return t}()),n!==u?(Pu=t,o=n,e=`${e[0].toLowerCase()} ${o.toLowerCase()}`,t=e):(Du=t,t=u)):(Du=t,t=u);var o;return t}())===u&&(n=null),n!==u&&Zc()!==u?((o=pc())===u&&(o=bc()),o===u&&(o=null),o!==u&&Zc()!==u&&(a=Ja())!==u&&Zc()!==u?((s=as())===u&&(s=null),s!==u&&Zc()!==u?((i=function(){var t,e,n,o,a,s;t=Du,"for"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(G));e!==u&&Zc()!==u?("system_time"===r.substr(Du,11).toLowerCase()?(n=r.substr(Du,11),Du+=11):(n=u,0===Wu&&Vu(ft)),n!==u&&Zc()!==u?("as"===r.substr(Du,2).toLowerCase()?(o=r.substr(Du,2),Du+=2):(o=u,0===Wu&&Vu(pt)),o!==u&&Zc()!==u?("of"===r.substr(Du,2).toLowerCase()?(a=r.substr(Du,2),Du+=2):(a=u,0===Wu&&Vu(bt)),a!==u&&Zc()!==u&&(s=ks())!==u?(Pu=t,t=e={keyword:"for system_time as of",expr:s}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(i=null),i!==u&&Zc()!==u?((c=ds())===u&&(c=null),c!==u&&Zc()!==u?((f=function(){var t,e,n;t=Du,(e=function(){var t,e,n,o;t=Du,"group"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(oo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&uc()!==u&&Zc()!==u&&(n=Ss())!==u?(Pu=t,e={columns:n.value},t=e):(Du=t,t=u);return t}())===u&&(f=null),f!==u&&Zc()!==u?((p=function(){var r,t;r=Du,ac()!==u&&Zc()!==u&&(t=Ms())!==u?(Pu=r,r=t):(Du=r,r=u);return r}())===u&&(p=null),p!==u&&Zc()!==u?((b=function(){var t,e;t=Du,function(){var t,e,n,o;t=Du,"qualify"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(so));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=ks())!==u?(Pu=t,t=e):(Du=t,t=u);return t}())===u&&(b=null),b!==u&&Zc()!==u?((v=gs())===u&&(v=null),v!==u&&Zc()!==u?((y=Ts())===u&&(y=null),y!==u&&Zc()!==u?((d=function(){var t,e;t=Du,function(){var t,e,n,o;t=Du,"window"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(io));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=hs())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=hs())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=hs())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=t,t={keyword:"window",type:"window",expr:e}):(Du=t,t=u);return t}())===u&&(d=null),d!==u?(Pu=t,h=e,m=n,w=o,C=a,L=s,A=i,E=c,g=f,_=p,j=b,T=v,S=y,x=d,Array.isArray(L)&&L.forEach(r=>r.table&&Ll.add(`select::${r.db}::${r.table}`)),t={type:"select",as_struct_val:m,distinct:w,columns:C,from:L,for_sys_time_as_of:A,where:E,with:h,groupby:g,having:_,qualify:j,orderby:T,limit:S,window:x,...pl()}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function za(){var r,t,e;return r=Du,(t=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ks())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc())===u&&(a=Jc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc())===u&&(a=Jc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=vl(t[e][1],n,o)}return o}(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u&&Zc()!==u?((e=os())===u&&(e=null),e!==u?(Pu=r,r=t={expr:t,as:e,...pl()}):(Du=r,r=u)):(Du=r,r=u),r}function Ja(){var r,t,e;return r=Du,(t=Za())!==u&&Zc()!==u?((e=Yc())===u&&(e=null),e!==u?(Pu=r,r=t=t):(Du=r,r=u)):(Du=r,r=u),r}function Za(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ns())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ns())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ns())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function rs(){var r,t;return r=Du,Qc()!==u&&Zc()!==u?((t=Ri())===u&&(t=ki()),t!==u&&Zc()!==u&&Kc()!==u?(Pu=r,r={value:t}):(Du=r,r=u)):(Du=r,r=u),r}function ts(){var r,t,e,n,o,a,s,i,c,l,f,p,b,v;if(r=Du,t=[],(e=rs())!==u)for(;e!==u;)t.push(e),e=rs();else t=u;if(t!==u&&(Pu=r,t=t),(r=t)===u){if(r=Du,t=[],e=Du,(n=Qc())!==u&&(o=Zc())!==u?((a=cc())===u&&(a=sc())===u&&(a=lc())===u&&(a=ic()),a!==u&&(s=Zc())!==u&&(i=Gc())!==u&&(c=Zc())!==u?((l=Ri())===u&&(l=ki()),l!==u&&(f=Zc())!==u&&(p=qc())!==u&&(b=Zc())!==u&&(v=Kc())!==u?e=n=[n,o,a,s,i,c,l,f,p,b,v]:(Du=e,e=u)):(Du=e,e=u)):(Du=e,e=u),e!==u)for(;e!==u;)t.push(e),e=Du,(n=Qc())!==u&&(o=Zc())!==u?((a=cc())===u&&(a=sc())===u&&(a=lc())===u&&(a=ic()),a!==u&&(s=Zc())!==u&&(i=Gc())!==u&&(c=Zc())!==u?((l=Ri())===u&&(l=ki()),l!==u&&(f=Zc())!==u&&(p=qc())!==u&&(b=Zc())!==u&&(v=Kc())!==u?e=n=[n,o,a,s,i,c,l,f,p,b,v]:(Du=e,e=u)):(Du=e,e=u)):(Du=e,e=u);else t=u;t!==u&&(Pu=r,t=t.map(r=>({name:r[2],value:r[6]}))),r=t}return r}function es(){var r,t,e;return r=Du,(t=ks())!==u&&Zc()!==u&&(e=ts())!==u?(Pu=r,r=t={expr:t,offset:e}):(Du=r,r=u),r}function ns(){var t,e,n,o,a,s,i,c,l,f,p,b;return t=Du,e=Du,(n=ii())!==u&&(o=Zc())!==u&&(a=Wc())!==u?e=n=[n,o,a]:(Du=e,e=u),e===u&&(e=null),e!==u&&(n=Bc())!==u&&(o=Zc())!==u?("except"===r.substr(Du,6).toLowerCase()?(a=r.substr(Du,6),Du+=6):(a=u,0===Wu&&Vu(lt)),a===u&&("replace"===r.substr(Du,7).toLowerCase()?(a=r.substr(Du,7),Du+=7):(a=u,0===Wu&&Vu(qr))),a!==u&&(s=Zc())!==u&&(i=Gc())!==u&&(c=Zc())!==u&&(l=Za())!==u&&Zc()!==u&&qc()!==u?(Pu=t,t=e=function(r,t,e){const n=r&&r[0];return Al.add(`select::${n}::(.*)`),{expr_list:e,parentheses:!0,expr:{type:"column_ref",table:n,column:"*"},type:t.toLowerCase(),...pl()}}(e,a,l)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=pc())===u&&(e=Du,(n=Bc())!==u?(o=Du,Wu++,a=bi(),Wu--,a===u?o=void 0:(Du=o,o=u),o!==u?e=n=[n,o]:(Du=e,e=u)):(Du=e,e=u),e===u&&(e=Bc())),e!==u&&(Pu=t,e=function(r){Al.add("select::null::(.*)");return{expr:{type:"column_ref",table:null,column:"*"},as:null,...pl()}}()),(t=e)===u&&(t=Du,(e=ii())!==u&&(n=Zc())!==u&&(o=Wc())!==u?(a=Du,(s=es())===u&&(s=ii()),s!==u&&(i=Zc())!==u&&(c=Wc())!==u?a=s=[s,i,c]:(Du=a,a=u),a===u&&(a=null),a!==u&&(s=Zc())!==u&&(i=Bc())!==u?(Pu=t,t=e=function(r,t){Al.add(`select::${r}::(.*)`);let e="*";const n=t&&t[0];return"string"==typeof n&&(e=n+".*"),n&&n.expr&&n.offset&&(e={...n,suffix:".*"}),{expr:{type:"column_ref",table:r,column:e},as:null,...pl()}}(e,a)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=es())!==u&&(n=Zc())!==u?(o=Du,(a=Wc())!==u&&(s=Zc())!==u&&(i=ii())!==u?o=a=[a,s,i]:(Du=o,o=u),o===u&&(o=null),o!==u&&(a=Zc())!==u?((s=os())===u&&(s=null),s!==u?(Pu=t,f=e,b=s,(p=o)&&(f.suffix="."+p[2]),t=e={expr:{type:"column_ref",table:null,column:f},as:b,...pl()}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=za())))),t}function os(){var r,t,e;return r=Du,(t=Qi())!==u&&Zc()!==u&&(e=function(){var r,t;r=Du,(t=fi())!==u?(Pu=Du,(function(r){if(!0===ll[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?u:void 0)!==u?(Pu=r,r=t=t):(Du=r,r=u)):(Du=r,r=u);r===u&&(r=Du,(t=ni())!==u&&(Pu=r,t=t),r=t);return r}())!==u?(Pu=r,r=t=e):(Du=r,r=u),r===u&&(r=Du,(t=Qi())===u&&(t=null),t!==u&&Zc()!==u&&(e=li())!==u?(Pu=r,r=t=e):(Du=r,r=u)),r}function us(){var t,e,n,o,a;return t=Du,"unnest"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(vt)),e!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u?((n=ks())===u&&(n=null),n!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((o=os())===u&&(o=null),o!==u&&Zc()!==u?((a=function(){var r,t;r=Du,oc()!==u&&Zc()!==u&&cc()!==u&&Zc()!==u?((t=os())===u&&(t=null),t!==u?(Pu=r,r={keyword:"with offset as",as:t}):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(a=null),a!==u?(Pu=t,t=e={type:"unnest",expr:n,parentheses:!0,as:o,with_offset:a}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function as(){var t,e,n,o,a;return t=Du,Vi()!==u&&Zc()!==u&&(e=is())!==u&&Zc()!==u?((n=function(){var t,e,n,o,a,s;t=Du,function(){var t,e,n,o;t=Du,"pivot"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(lu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="PIVOT"):(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(e=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=Du,(t=hi())!==u)if(Zc()!==u)if((e=os())===u&&(e=null),e!==u){for(n=[],o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=hi())!==u&&(l=Zc())!==u?((f=os())===u&&(f=null),f!==u?o=a=[a,s,i,c,l,f]:(Du=o,o=u)):(Du=o,o=u);o!==u;)n.push(o),o=Du,(a=Zc())!==u&&(s=Yc())!==u&&(i=Zc())!==u&&(c=hi())!==u&&(l=Zc())!==u?((f=os())===u&&(f=null),f!==u?o=a=[a,s,i,c,l,f]:(Du=o,o=u)):(Du=o,o=u);n!==u?(Pu=r,t=function(r,t,e){const n={type:"expr_list"};return n.value=dl(r,e),n}(t,0,n),r=t):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;return r}())!==u&&Zc()!==u?("for"===r.substr(Du,3).toLowerCase()?(n=r.substr(Du,3),Du+=3):(n=u,0===Wu&&Vu(G)),n!==u&&Zc()!==u&&(o=Zs())!==u&&Zc()!==u&&(a=Ys())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((s=os())===u&&(s=null),s!==u?(Pu=t,i=e,c=o,f=s,(l=a).operator="=",t={type:"pivot",expr:i,column:c,in_expr:l,as:f}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);var i,c,l,f;return t}())===u&&(n=null),n!==u?(Pu=t,a=n,(o=e)[0]&&(o[0].operator=a),t=o):(Du=t,t=u)):(Du=t,t=u),t}function ss(){var r,t,e;return r=Du,(t=bs())!==u&&Zc()!==u&&Yi()!==u&&Zc()!==u&&(e=bs())!==u?(Pu=r,r=t=[t,e]):(Du=r,r=u),r}function is(){var r,t,e,n;if(r=Du,(t=fs())!==u){for(e=[],n=cs();n!==u;)e.push(n),n=cs();e!==u?(Pu=r,r=t=yt(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function cs(){var r,t,e;return r=Du,Zc()!==u&&(t=Yc())!==u&&Zc()!==u&&(e=fs())!==u?(Pu=r,r=e):(Du=r,r=u),r===u&&(r=Du,Zc()!==u&&(t=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=Du,(t=ps())!==u)if(Zc()!==u)if((e=fs())!==u)if(Zc()!==u)if((n=nc())!==u)if(Zc()!==u)if(Gc()!==u)if(Zc()!==u)if((o=ti())!==u){for(a=[],s=Du,(i=Zc())!==u&&(c=Yc())!==u&&(l=Zc())!==u&&(f=ti())!==u?s=i=[i,c,l,f]:(Du=s,s=u);s!==u;)a.push(s),s=Du,(i=Zc())!==u&&(c=Yc())!==u&&(l=Zc())!==u&&(f=ti())!==u?s=i=[i,c,l,f]:(Du=s,s=u);a!==u&&(s=Zc())!==u&&(i=qc())!==u?(Pu=r,p=t,v=o,y=a,(b=e).join=p,b.using=dl(v,y),r=t=b):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;else Du=r,r=u;var p,b,v,y;r===u&&(r=Du,(t=ps())!==u&&Zc()!==u&&(e=fs())!==u&&Zc()!==u?((n=ys())===u&&(n=null),n!==u?(Pu=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=ps())===u&&(t=Ga()),t!==u&&Zc()!==u&&(e=Gc())!==u&&Zc()!==u&&(n=qa())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((o=os())===u&&(o=null),o!==u&&(a=Zc())!==u?((s=ys())===u&&(s=null),s!==u?(Pu=r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)));return r}())!==u?(Pu=r,r=t):(Du=r,r=u)),r}function ls(){var t,e,n,o,a,s,i,c,l,f,p,b;return t=Du,"tablesample"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(gt)),e!==u&&(n=Zc())!==u?("bernoulli"===r.substr(Du,9).toLowerCase()?(o=r.substr(Du,9),Du+=9):(o=u,0===Wu&&Vu(_t)),o===u&&("reservoir"===r.substr(Du,9).toLowerCase()?(o=r.substr(Du,9),Du+=9):(o=u,0===Wu&&Vu(jt))),o!==u&&(a=Zc())!==u?(40===r.charCodeAt(Du)?(s="(",Du++):(s=u,0===Wu&&Vu(st)),s!==u&&(i=Zc())!==u&&(c=Ui())!==u&&(l=Zc())!==u?("percent"===r.substr(Du,7).toLowerCase()?(f=r.substr(Du,7),Du+=7):(f=u,0===Wu&&Vu(Tt)),f===u&&("rows"===r.substr(Du,4).toLowerCase()?(f=r.substr(Du,4),Du+=4):(f=u,0===Wu&&Vu(St))),f!==u&&(p=Zc())!==u?(41===r.charCodeAt(Du)?(b=")",Du++):(b=u,0===Wu&&Vu(it)),b!==u?t=e=[e,n,o,a,s,i,c,l,f,p,b]:(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function fs(){var t,e,n,o,a,s,i,c;return(t=us())===u&&(t=Du,(e=Ci())!==u&&(n=Zc())!==u?((o=os())===u&&(o=null),o!==u?(Pu=t,t=e={type:"expr",expr:e,as:o}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=bs())!==u?((n=function(){var t,e,n,o,a,s,i,c,l,f,p;return t=Du,dt.test(r.charAt(Du))?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(ht)),e!==u?(mt.test(r.charAt(Du))?(n=r.charAt(Du),Du++):(n=u,0===Wu&&Vu(wt)),n!==u&&(o=Zc())!==u&&(a=pi())!==u&&(s=Zc())!==u?(Ct.test(r.charAt(Du))?(i=r.charAt(Du),Du++):(i=u,0===Wu&&Vu(Lt)),i!==u&&(c=Zc())!==u&&(l=pi())!==u&&(f=Zc())!==u?(At.test(r.charAt(Du))?(p=r.charAt(Du),Du++):(p=u,0===Wu&&Vu(Et)),p!==u?t=e=[e,n,o,a,s,i,c,l,f,p]:(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}())===u&&(n=null),n!==u&&(o=Zc())!==u?((a=ls())===u&&(a=null),a!==u&&Zc()!==u?((s=os())===u&&(s=null),s!==u?(Pu=t,t=e=function(r,t,e,n){return"var"===r.type?(r.as=n,r):{...r,as:n,...pl()}}(e,0,0,s)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Gc())!==u&&(n=Zc())!==u&&(o=qa())!==u&&(a=Zc())!==u&&qc()!==u&&(s=Zc())!==u?((i=ls())===u&&(i=null),i!==u&&Zc()!==u?((c=os())===u&&(c=null),c!==u?(Pu=t,t=e=function(r,t,e){return r.parentheses=!0,{expr:r,as:e,...pl()}}(o,0,c)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)))),t}function ps(){var t,e,n;return t=Du,(e=function(){var t,e,n,o;t=Du,"left"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Gn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=rc())===u&&(n=null),n!==u&&Zc()!==u&&Zi()!==u?(Pu=t,t=e="LEFT JOIN"):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=function(){var t,e,n,o;t=Du,"right"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(qn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=rc())===u&&(n=null),n!==u&&Zc()!==u&&Zi()!==u?(Pu=t,t=e="RIGHT JOIN"):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=function(){var t,e,n,o;t=Du,"full"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Vn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u?((n=rc())===u&&(n=null),n!==u&&Zc()!==u&&Zi()!==u?(Pu=t,t=e="FULL JOIN"):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=function(){var t,e,n,o;t=Du,"cross"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Qn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&Zc()!==u&&(n=Zi())!==u?(Pu=t,t=e=e[0].toUpperCase()+" JOIN"):(Du=t,t=u),t===u&&(t=Du,(e=function(){var t,e,n,o;t=Du,"inner"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Xn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(e=null),e!==u&&Zc()!==u&&(n=Zi())!==u?(Pu=t,t=e=function(r){return r?r[0].toUpperCase()+" JOIN":"JOIN"}(e)):(Du=t,t=u))))),t}function bs(){var r,t,e,n,o,a,s,i;return r=Du,(t=ci())!==u?(e=Du,(n=Zc())!==u&&(o=Wc())!==u&&(a=Zc())!==u&&(s=ci())!==u?e=n=[n,o,a,s]:(Du=e,e=u),e!==u?(n=Du,(o=Zc())!==u&&(a=Wc())!==u&&(s=Zc())!==u&&(i=ci())!==u?n=o=[o,a,s,i]:(Du=n,n=u),n!==u?(Pu=r,r=t=function(r,t,e){const n={db:null,table:r};return null!==e&&(n.db=r,n.catalog=r,n.schema=t[3],n.table=e[3]),n}(t,e,n)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=ci())!==u?(e=Du,(n=Zc())!==u&&(o=Wc())!==u&&(a=Zc())!==u&&(s=ci())!==u?e=n=[n,o,a,s]:(Du=e,e=u),e===u&&(e=null),e!==u?(Pu=r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(Du=r,r=u)):(Du=r,r=u)),r}function vs(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ks())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=vl(t[r][1],n,t[r][3]);return n}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function ys(){var r,t;return r=Du,Ji()!==u&&Zc()!==u&&(t=Ms())!==u?(Pu=r,r=t):(Du=r,r=u),r}function ds(){var t,e;return t=Du,function(){var t,e,n,o;t=Du,"where"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(no));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=Ms())!==u?(Pu=t,t=e):(Du=t,t=u),t}function hs(){var r,t,e;return r=Du,(t=pi())!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&(e=ms())!==u?(Pu=r,r=t={name:t,as_window_specification:e}):(Du=r,r=u),r}function ms(){var t,e,n;return t=Du,(e=pi())!==u&&(Pu=t,e=e),(t=e)===u&&(t=Du,(e=Gc())!==u&&Zc()!==u?((n=function(){var t,e,n,o,a;t=Du,(e=ei())===u&&(e=null);e!==u&&Zc()!==u?((n=Es())===u&&(n=null),n!==u&&Zc()!==u?((o=gs())===u&&(o=null),o!==u&&Zc()!==u?((a=function(){var t,e,n,o,a;t=Du,(e=Oc())!==u&&Zc()!==u?((n=ws())===u&&(n=Cs()),n!==u?(Pu=t,t=e={type:"rows",expr:n}):(Du=t,t=u)):(Du=t,t=u);t===u&&(t=Du,(e=Oc())===u&&("range"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(xt))),e!==u&&Zc()!==u&&(n=vc())!==u&&Zc()!==u&&(o=Cs())!==u&&Zc()!==u&&Cc()!==u&&Zc()!==u&&(a=ws())!==u?(Pu=t,s=o,i=a,e=vl(n,{type:"origin",value:e.toLowerCase()},{type:"expr_list",value:[s,i]}),t=e):(Du=t,t=u));var s,i;return t}())===u&&(a=null),a!==u?(Pu=t,t=e={name:e,partitionby:n,orderby:o,window_frame_clause:a}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(n=null),n!==u&&Zc()!==u&&qc()!==u?(Pu=t,t=e={window_specification:n,parentheses:!0}):(Du=t,t=u)):(Du=t,t=u)),t}function ws(){var t,e,n,o,a;return t=Du,(e=As())!==u&&Zc()!==u?("following"===r.substr(Du,9).toLowerCase()?(n=r.substr(Du,9),Du+=9):(n=u,0===Wu&&Vu(kt)),n===u&&("preceding"===r.substr(Du,9).toLowerCase()?(n=r.substr(Du,9),Du+=9):(n=u,0===Wu&&Vu(It))),n!==u?(Pu=t,a=n,(o=e).value+=" "+a.toUpperCase(),t=e=o):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Ls()),t}function Cs(){var t,e,n,o,a;return t=Du,(e=As())!==u&&Zc()!==u?("preceding"===r.substr(Du,9).toLowerCase()?(n=r.substr(Du,9),Du+=9):(n=u,0===Wu&&Vu(It)),n===u&&("following"===r.substr(Du,9).toLowerCase()?(n=r.substr(Du,9),Du+=9):(n=u,0===Wu&&Vu(kt))),n!==u?(Pu=t,a=n,(o=e).value+=" "+a.toUpperCase(),t=e=o):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Ls()),t}function Ls(){var t,e,n;return t=Du,"current"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Nt)),e!==u&&Zc()!==u?("row"===r.substr(Du,3).toLowerCase()?(n=r.substr(Du,3),Du+=3):(n=u,0===Wu&&Vu(Ot)),n!==u?(Pu=t,t=e={type:"origin",value:"current row",...pl()}):(Du=t,t=u)):(Du=t,t=u),t}function As(){var t,e;return t=Du,"unbounded"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Rt)),e!==u&&(Pu=t,e={type:"origin",value:e.toUpperCase(),...pl()}),(t=e)===u&&(t=Ri()),t}function Es(){var r,t;return r=Du,Gi()!==u&&Zc()!==u&&uc()!==u&&Zc()!==u&&(t=Ja())!==u?(Pu=r,r=t):(Du=r,r=u),r}function gs(){var t,e;return t=Du,function(){var t,e,n,o;t=Du,"order"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(uo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&uc()!==u&&Zc()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=_s())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=_s())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=_s())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=l(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())!==u?(Pu=t,t=e):(Du=t,t=u),t}function _s(){var t,e,n,o,a,s;return t=Du,(e=ks())!==u&&Zc()!==u?(n=Du,"collate"===r.substr(Du,7).toLowerCase()?(o=r.substr(Du,7),Du+=7):(o=u,0===Wu&&Vu(gr)),o!==u&&(a=Zc())!==u&&(s=ki())!==u?n=o=[o,a,s]:(Du=n,n=u),n===u&&(n=null),n!==u&&(o=Zc())!==u?((a=fc())===u&&(a=function(){var t,e,n,o;t=Du,"asc"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(vo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ASC"):(Du=t,t=u)):(Du=t,t=u);return t}()),a===u&&(a=null),a!==u?(Pu=t,t=e={expr:e,type:a}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t}function js(){var r;return(r=Ri())===u&&(r=di()),r}function Ts(){var t,e,n,o,a,s;return t=Du,function(){var t,e,n,o;t=Du,"limit"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(fo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=js())!==u&&Zc()!==u?(n=Du,(o=Yc())===u&&(o=cc()),o!==u&&(a=Zc())!==u&&(s=js())!==u?n=o=[o,a,s]:(Du=n,n=u),n===u&&(n=null),n!==u?(Pu=t,t=function(r,t){const e=[r];return t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e,...pl()}}(e,n)):(Du=t,t=u)):(Du=t,t=u),t}function Ss(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ks())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=dl(r,t),e}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function xs(){var r;return(r=Us())===u&&(r=Rs())===u&&(r=function(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Ds())!==u){for(e=[],n=Du,(o=rl())!==u&&(a=Lc())!==u&&(s=Zc())!==u&&(i=Ds())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=rl())!==u&&(a=Lc())!==u&&(s=Zc())!==u&&(i=Ds())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,t=c(t,e),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a;if(r=Du,(t=Gs())!==u){if(e=[],n=Du,(o=Zc())!==u&&(a=Xs())!==u?n=o=[o,a]:(Du=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Xs())!==u?n=o=[o,a]:(Du=n,n=u);else e=u;e!==u?(Pu=r,t=bl(t,e[0][1]),r=t):(Du=r,r=u)}else Du=r,r=u;return r}())===u&&(r=Os()),r}function ks(){var r;return(r=xs())===u&&(r=qa()),r}function Is(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Ns())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Ns())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=Ns())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Ns(){var r,t;return r=Du,Gc()!==u&&Zc()!==u&&(t=Ja())!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t):(Du=r,r=u),r}function Os(){var r,t,e,n,o;return r=Du,(t=Qc())!==u&&Zc()!==u?((e=Ja())===u&&(e=null),e!==u&&(n=Zc())!==u&&(o=Kc())!==u?(Pu=r,r=t={array_path:e,type:"array",brackets:!0,keyword:""}):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=il())===u&&(t=Tc()),t===u&&(t=null),t!==u&&Qc()!==u&&(e=Zc())!==u&&(n=Ti())!==u&&(o=Zc())!==u&&Kc()!==u?(Pu=r,r=t=function(r,t){return{definition:r,array_path:t.map(r=>({expr:r,as:null})),type:"array",keyword:r&&"array",brackets:!0}}(t,n)):(Du=r,r=u),r===u&&(r=Du,(t=il())===u&&(t=Tc()),t===u&&(t=null),t!==u&&Zc()!==u&&(e=Qc())!==u&&(n=Zc())!==u?((o=Is())===u&&(o=ks()),o!==u&&Zc()!==u&&Kc()!==u?(Pu=r,r=t=function(r,t,e,n){return{definition:r,expr_list:e,type:"array",keyword:r&&"array",brackets:!0,parentheses:!1}}(t,0,o)):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=il())===u&&(t=Tc()),t!==u&&Zc()!==u&&(e=Gc())!==u&&(n=Zc())!==u?((o=Is())===u&&(o=ks()),o!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t=function(r,t,e,n){return{definition:r,expr_list:e,type:"array",keyword:r&&"array",brackets:!1,parentheses:!0}}(t,0,o)):(Du=r,r=u)):(Du=r,r=u)))),r}function Rs(){var t,e;return t=Du,function(){var t,e,n,o;t=Du,"json"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ko));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="JSON"):(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=Ti())!==u?(Pu=t,t={type:"json",keyword:"json",expr_list:e}):(Du=t,t=u),t}function Us(){var r,t,e,n;return r=Du,(t=cl())===u&&(t=kc()),t!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(e=Ja())!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t={definition:n=t,expr_list:e,type:"struct",keyword:n&&"struct",parentheses:!0}):(Du=r,r=u),r}function Ms(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ks())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc())===u&&(a=Yc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Cc())===u&&(a=Lc())===u&&(a=Yc()),a!==u&&(s=Zc())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=vl(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Ds(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Ps())!==u){for(e=[],n=Du,(o=rl())!==u&&(a=Cc())!==u&&(s=Zc())!==u&&(i=Ps())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=rl())!==u&&(a=Cc())!==u&&(s=Zc())!==u&&(i=Ps())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=c(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Ps(){var t,e,n,o,a;return(t=Fs())===u&&(t=function(){var r,t,e;r=Du,(t=function(){var r,t,e,n,o;r=Du,t=Du,(e=wc())!==u&&(n=Zc())!==u&&(o=mc())!==u?t=e=[e,n,o]:(Du=t,t=u);t!==u&&(Pu=r,t=Mt(t));(r=t)===u&&(r=mc());return r}())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(e=qa())!==u&&Zc()!==u&&qc()!==u?(Pu=r,n=t,(o=e).parentheses=!0,t=bl(n,o),r=t):(Du=r,r=u);var n,o;return r}())===u&&(t=Du,(e=wc())===u&&(e=Du,33===r.charCodeAt(Du)?(n="!",Du++):(n=u,0===Wu&&Vu(Ut)),n!==u?(o=Du,Wu++,61===r.charCodeAt(Du)?(a="=",Du++):(a=u,0===Wu&&Vu(f)),Wu--,a===u?o=void 0:(Du=o,o=u),o!==u?e=n=[n,o]:(Du=e,e=u)):(Du=e,e=u)),e!==u&&(n=Zc())!==u&&(o=Ps())!==u?(Pu=t,t=e=bl("NOT",o)):(Du=t,t=u)),t}function Fs(){var r,t,e,n,o;return r=Du,(t=Bs())!==u&&Zc()!==u?((e=function(){var r;(r=function(){var r,t,e,n,o,a,s;r=Du,t=[],e=Du,(n=Zc())!==u&&(o=Hs())!==u&&(a=Zc())!==u&&(s=Bs())!==u?e=n=[n,o,a,s]:(Du=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=Du,(n=Zc())!==u&&(o=Hs())!==u&&(a=Zc())!==u&&(s=Bs())!==u?e=n=[n,o,a,s]:(Du=e,e=u);else t=u;t!==u&&(Pu=r,t={type:"arithmetic",tail:t});return r=t}())===u&&(r=Ys())===u&&(r=function(){var r,t,e,n;r=Du,(t=function(){var r,t,e,n,o;r=Du,t=Du,(e=wc())!==u&&(n=Zc())!==u&&(o=vc())!==u?t=e=[e,n,o]:(Du=t,t=u);t!==u&&(Pu=r,t=Mt(t));(r=t)===u&&(r=vc());return r}())!==u&&Zc()!==u&&(e=Bs())!==u&&Zc()!==u&&Cc()!==u&&Zc()!==u&&(n=Bs())!==u?(Pu=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Du=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o;r=Du,(t=dc())!==u&&(e=Zc())!==u&&(n=Bs())!==u?(Pu=r,r=t={op:"IS",right:n}):(Du=r,r=u);r===u&&(r=Du,t=Du,(e=dc())!==u&&(n=Zc())!==u&&(o=wc())!==u?t=e=[e,n,o]:(Du=t,t=u),t!==u&&(e=Zc())!==u&&(n=Bs())!==u?(Pu=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Du=r,r=u));return r}())===u&&(r=Ws());return r}())===u&&(e=null),e!==u?(Pu=r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?hl(n,o.tail):vl(o.op,n,o.right)):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=ki())===u&&(r=Zs()),r}function Hs(){var t;return">="===r.substr(Du,2)?(t=">=",Du+=2):(t=u,0===Wu&&Vu(Dt)),t===u&&(62===r.charCodeAt(Du)?(t=">",Du++):(t=u,0===Wu&&Vu(Pt)),t===u&&("<="===r.substr(Du,2)?(t="<=",Du+=2):(t=u,0===Wu&&Vu(Ft)),t===u&&("<>"===r.substr(Du,2)?(t="<>",Du+=2):(t=u,0===Wu&&Vu(Ht)),t===u&&(60===r.charCodeAt(Du)?(t="<",Du++):(t=u,0===Wu&&Vu($t)),t===u&&(61===r.charCodeAt(Du)?(t="=",Du++):(t=u,0===Wu&&Vu(f)),t===u&&("!="===r.substr(Du,2)?(t="!=",Du+=2):(t=u,0===Wu&&Vu(Wt)))))))),t}function $s(){var r,t,e,n,o;return r=Du,t=Du,(e=wc())!==u&&(n=Zc())!==u&&(o=yc())!==u?t=e=[e,n,o]:(Du=t,t=u),t!==u&&(Pu=r,t=Mt(t)),(r=t)===u&&(r=yc()),r}function Ws(){var r,t,e;return r=Du,(t=function(){var r,t,e,n,o;return r=Du,t=Du,(e=wc())!==u&&(n=Zc())!==u&&(o=hc())!==u?t=e=[e,n,o]:(Du=t,t=u),t!==u&&(Pu=r,t=Mt(t)),(r=t)===u&&(r=hc()),r}())!==u&&Zc()!==u?((e=ji())===u&&(e=Fs()),e!==u?(Pu=r,r=t={op:t,right:e}):(Du=r,r=u)):(Du=r,r=u),r}function Ys(){var r,t,e,n;return r=Du,(t=$s())!==u&&Zc()!==u&&(e=Gc())!==u&&Zc()!==u&&(n=Ss())!==u&&Zc()!==u&&qc()!==u?(Pu=r,r=t={op:t,right:n}):(Du=r,r=u),r===u&&(r=Du,(t=$s())!==u&&Zc()!==u?((e=ki())===u&&(e=us()),e!==u?(Pu=r,r=t=function(r,t){return{op:r,right:t}}(t,e)):(Du=r,r=u)):(Du=r,r=u)),r}function Bs(){var r,t,e,n,o,a,s,i;if(r=Du,(t=qs())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Gs())!==u&&(s=Zc())!==u&&(i=qs())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Gs())!==u&&(s=Zc())!==u&&(i=qs())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...pl()}));return hl(r,t)}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Gs(){var t;return 43===r.charCodeAt(Du)?(t="+",Du++):(t=u,0===Wu&&Vu(Yt)),t===u&&(45===r.charCodeAt(Du)?(t="-",Du++):(t=u,0===Wu&&Vu(Bt))),t}function qs(){var r,t,e,n,o,a,s,i;if(r=Du,(t=Qs())!==u){for(e=[],n=Du,(o=Zc())!==u?((a=Vs())===u&&(a=Jc()),a!==u&&(s=Zc())!==u&&(i=Qs())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u?((a=Vs())===u&&(a=Jc()),a!==u&&(s=Zc())!==u&&(i=Qs())!==u?n=o=[o,a,s,i]:(Du=n,n=u)):(Du=n,n=u);e!==u?(Pu=r,r=t=hl(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Vs(){var t;return 42===r.charCodeAt(Du)?(t="*",Du++):(t=u,0===Wu&&Vu(Gt)),t===u&&(47===r.charCodeAt(Du)?(t="/",Du++):(t=u,0===Wu&&Vu(qt)),t===u&&(37===r.charCodeAt(Du)?(t="%",Du++):(t=u,0===Wu&&Vu(Vt)))),t}function Xs(){var t,e,n;return(t=Os())===u&&(t=hi())===u&&(t=Ci())===u&&(t=Us())===u&&(t=Rs())===u&&(t=function(){var t,e,n,o,a,s,i;t=Du,(e=_i())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=gi())!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&(o=sl())!==u&&Zc()!==u&&(a=qc())!==u?(Pu=t,c=n,l=o,e={type:"cast",keyword:e.toLowerCase(),...c,symbol:"as",target:[l]},t=e):(Du=t,t=u);var c,l;t===u&&(t=Du,(e=_i())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=gi())!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&(o=Sc())!==u&&Zc()!==u&&(a=Gc())!==u&&Zc()!==u&&(s=Mi())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u&&(i=qc())!==u?(Pu=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),...t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,n,s),t=e):(Du=t,t=u),t===u&&(t=Du,(e=_i())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=gi())!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&(o=Sc())!==u&&Zc()!==u&&(a=Gc())!==u&&Zc()!==u&&(s=Mi())!==u&&Zc()!==u&&Yc()!==u&&Zc()!==u&&(i=Mi())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u&&qc()!==u?(Pu=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),...t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,n,s,i),t=e):(Du=t,t=u),t===u&&(t=Du,(e=_i())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=gi())!==u&&Zc()!==u&&Qi()!==u&&Zc()!==u&&(o=function(){var t;(t=function(){var t,e,n,o;t=Du,"signed"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(qo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SIGNED"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"unsigned"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Vo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="UNSIGNED"):(Du=t,t=u)):(Du=t,t=u);return t}());return t}())!==u&&Zc()!==u?((a=xc())===u&&(a=null),a!==u&&Zc()!==u&&(s=qc())!==u?(Pu=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),...t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,n,o,a),t=e):(Du=t,t=u)):(Du=t,t=u))));return t}())===u&&(t=ji())===u&&(t=function(){var r,t,e,n,o,a,s;r=Du,(t=_c())!==u&&Zc()!==u&&(e=Ks())!==u&&Zc()!==u?((n=Js())===u&&(n=null),n!==u&&Zc()!==u&&(o=jc())!==u&&Zc()!==u?((a=_c())===u&&(a=null),a!==u?(Pu=r,i=e,(c=n)&&i.push(c),r=t={type:"case",expr:null,args:i}):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u);var i,c;r===u&&(r=Du,(t=_c())!==u&&Zc()!==u&&(e=ks())!==u&&Zc()!==u&&(n=Ks())!==u&&Zc()!==u?((o=Js())===u&&(o=null),o!==u&&Zc()!==u&&(a=jc())!==u&&Zc()!==u?((s=_c())===u&&(s=null),s!==u?(Pu=r,t=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(e,n,o),r=t):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u));return r}())===u&&(t=function(){var t,e,n,o;t=Du,(e=Mc())!==u&&Zc()!==u&&(n=ks())!==u&&Zc()!==u&&(o=function(){var t;(t=function(){var t,e,n,o;t=Du,"year"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Qe));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="YEAR"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"isoyear"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Pe));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ISOYEAR"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"month"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Ye));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="MONTH"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"day"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(xe));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DAY"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"hour"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ue));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="HOUR"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"minute"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(We));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="MINUTE"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"second"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Ge));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SECOND"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"week"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Xe));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="WEEK"):(Du=t,t=u)):(Du=t,t=u);return t}());return t}())!==u?(Pu=t,e={type:"interval",expr:n,unit:o.toLowerCase()},t=e):(Du=t,t=u);return t}())===u&&(t=Zs())===u&&(t=di())===u&&(t=Du,Gc()!==u&&Zc()!==u&&(e=Ms())!==u&&Zc()!==u&&qc()!==u?(Pu=t,(n=e).parentheses=!0,t=n):(Du=t,t=u)),t}function Qs(){var t,e,n,o,a;return(t=Xs())===u&&(t=Du,(e=function(){var t;33===r.charCodeAt(Du)?(t="!",Du++):(t=u,0===Wu&&Vu(Ut));t===u&&(45===r.charCodeAt(Du)?(t="-",Du++):(t=u,0===Wu&&Vu(Bt)),t===u&&(43===r.charCodeAt(Du)?(t="+",Du++):(t=u,0===Wu&&Vu(Yt)),t===u&&(126===r.charCodeAt(Du)?(t="~",Du++):(t=u,0===Wu&&Vu(Xt)))));return t}())!==u?(n=Du,(o=Zc())!==u&&(a=Qs())!==u?n=o=[o,a]:(Du=n,n=u),n!==u?(Pu=t,t=e=bl(e,n[1])):(Du=t,t=u)):(Du=t,t=u)),t}function Ks(){var r,t,e,n,o,a;if(r=Du,(t=zs())!==u)if(Zc()!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=zs())!==u?n=o=[o,a]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=zs())!==u?n=o=[o,a]:(Du=n,n=u);e!==u?(Pu=r,r=t=L(t,e)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;return r}function zs(){var t,e,n;return t=Du,function(){var t,e,n,o;t=Du,"when"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ro));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=Ms())!==u&&Zc()!==u&&function(){var t,e,n,o;t=Du,"then"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Uo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(n=ks())!==u?(Pu=t,t={type:"when",cond:e,result:n}):(Du=t,t=u),t}function Js(){var t,e;return t=Du,function(){var t,e,n,o;t=Du,"else"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Mo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}()!==u&&Zc()!==u&&(e=ks())!==u?(Pu=t,t={type:"else",result:e}):(Du=t,t=u),t}function Zs(){var r,t,e,n,o,a,s,i,c,l,f;if(r=Du,(t=ii())!==u){if(e=[],n=Du,(o=Zc())!==u&&(a=Wc())!==u&&(s=Zc())!==u&&(i=ii())!==u?n=o=[o,a,s,i]:(Du=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Wc())!==u&&(s=Zc())!==u&&(i=ii())!==u?n=o=[o,a,s,i]:(Du=n,n=u);else e=u;e!==u&&(n=Zc())!==u?(o=Du,(a=ts())!==u&&(s=Zc())!==u?(i=Du,(c=Wc())!==u&&(l=Zc())!==u&&(f=ii())!==u?i=c=[c,l,f]:(Du=i,i=u),i===u&&(i=null),i!==u?o=a=[a,s,i]:(Du=o,o=u)):(Du=o,o=u),o===u&&(o=null),o!==u?(a=Du,(s=Zc())!==u&&(i=Ea())!==u?a=s=[s,i]:(Du=a,a=u),a===u&&(a=null),a!==u?(Pu=r,r=t=function(r,t,e,n){const o=t.map(r=>r[3]);Al.add(`select::${r}::${o[0]}`);return{type:"column_ref",table:r,...e?{column:{expr:{type:"column_ref",table:null,column:o[0],subFields:o.slice(1)},offset:e&&e[0],suffix:e&&e[2]&&"."+e[2][2]}}:{column:o[0],subFields:o.slice(1)},collate:n&&n[1],...pl()}}(t,e,o,a)):(Du=r,r=u)):(Du=r,r=u)):(Du=r,r=u)}else Du=r,r=u;if(r===u)if(r=Du,(t=ni())===u&&(t=li()),t!==u)if((e=Zc())!==u){for(n=[],o=rs();o!==u;)n.push(o),o=rs();n!==u?(o=Du,(a=Zc())!==u&&(s=Ea())!==u?o=a=[a,s]:(Du=o,o=u),o===u&&(o=null),o!==u?(Pu=r,r=t=function(r,t,e){const n="string"==typeof r?r:r.value;Al.add("select::null::"+n);const o="string"==typeof r?{expr:{type:"default",value:r}}:{expr:r};return t&&(o.offset=t),{type:"column_ref",table:null,column:o,collate:e&&e[1],...pl()}}(t,n,o)):(Du=r,r=u)):(Du=r,r=u)}else Du=r,r=u;else Du=r,r=u;return r}function ri(){var r,t,e,n,o,a,s,i;if(r=Du,(t=li())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=li())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=li())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function ti(){var r,t;return r=Du,(t=pi())!==u&&(Pu=r,t={type:"default",value:t}),(r=t)===u&&(r=ni()),r}function ei(){var r,t;return r=Du,(t=pi())!==u?(Pu=Du,(!0===ll[(""+t).toUpperCase()]?u:void 0)!==u?(Pu=r,r=t=t):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=Du,(t=oi())!==u&&(Pu=r,t=t),r=t),r}function ni(){var r;return(r=ui())===u&&(r=ai())===u&&(r=si()),r}function oi(){var r,t;return r=Du,(t=ui())===u&&(t=ai())===u&&(t=si()),t!==u&&(Pu=r,t=t.value),r=t}function ui(){var t,e,n,o;if(t=Du,34===r.charCodeAt(Du)?(e='"',Du++):(e=u,0===Wu&&Vu(Kt)),e!==u){if(n=[],zt.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(Jt)),o!==u)for(;o!==u;)n.push(o),zt.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(Jt));else n=u;n!==u?(34===r.charCodeAt(Du)?(o='"',Du++):(o=u,0===Wu&&Vu(Kt)),o!==u?(Pu=t,t=e={type:"double_quote_string",value:n.join("")}):(Du=t,t=u)):(Du=t,t=u)}else Du=t,t=u;return t}function ai(){var t,e,n,o;if(t=Du,39===r.charCodeAt(Du)?(e="'",Du++):(e=u,0===Wu&&Vu(dr)),e!==u){if(n=[],Zt.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(re)),o!==u)for(;o!==u;)n.push(o),Zt.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(re));else n=u;n!==u?(39===r.charCodeAt(Du)?(o="'",Du++):(o=u,0===Wu&&Vu(dr)),o!==u?(Pu=t,t=e={type:"single_quote_string",value:n.join("")}):(Du=t,t=u)):(Du=t,t=u)}else Du=t,t=u;return t}function si(){var t,e,n,o;if(t=Du,96===r.charCodeAt(Du)?(e="`",Du++):(e=u,0===Wu&&Vu(te)),e!==u){if(n=[],ee.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(ne)),o!==u)for(;o!==u;)n.push(o),ee.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(ne));else n=u;n!==u?(96===r.charCodeAt(Du)?(o="`",Du++):(o=u,0===Wu&&Vu(te)),o!==u?(Pu=t,t=e={type:"backticks_quote_string",value:n.join("")}):(Du=t,t=u)):(Du=t,t=u)}else Du=t,t=u;return t}function ii(){var r;return(r=fi())===u&&(r=oi()),r}function ci(){var r;return(r=pi())===u&&(r=oi()),r}function li(){var r,t;return r=Du,(t=fi())!==u?(Pu=Du,(Qt(t)?u:void 0)!==u?(Pu=r,r=t=t):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=oi()),r}function fi(){var r,t,e,n;if(r=Du,(t=bi())!==u){for(e=[],n=yi();n!==u;)e.push(n),n=yi();e!==u?(Pu=r,r=t=oe(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function pi(){var r,t,e,n;if(r=Du,(t=bi())!==u){for(e=[],n=vi();n!==u;)e.push(n),n=vi();e!==u?(Pu=r,r=t=oe(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function bi(){var t;return ue.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(ae)),t}function vi(){var t;return se.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(ie)),t}function yi(){var t;return ce.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(le)),t}function di(){var t,e,n;return t=Du,58===r.charCodeAt(Du)?(e=":",Du++):(e=u,0===Wu&&Vu(fe)),e===u&&(64===r.charCodeAt(Du)?(e="@",Du++):(e=u,0===Wu&&Vu(Zr))),e!==u&&(n=pi())!==u?(Pu=t,t=e={type:"param",value:n,prefix:e}):(Du=t,t=u),t}function hi(){var t;return(t=function(){var t,e,n,o;t=Du,(e=function(){var t,e,n,o;t=Du,"count"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(jo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="COUNT"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&("string_agg"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(pe)));e!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;t=Du,(e=function(){var t,e;t=Du,42===r.charCodeAt(Du)?(e="*",Du++):(e=u,0===Wu&&Vu(Gt));e!==u&&(Pu=t,e={type:"star",value:"*"});return t=e}())!==u&&(Pu=t,e={expr:e,...pl()});if((t=e)===u){if(t=Du,(e=bc())===u&&(e=null),e!==u)if(Zc()!==u)if((n=Gc())!==u)if(Zc()!==u)if((o=ks())!==u)if(Zc()!==u)if(qc()!==u){for(a=[],s=Du,(i=Zc())!==u?((c=Cc())===u&&(c=Lc()),c!==u&&(l=Zc())!==u&&(f=ks())!==u?s=i=[i,c,l,f]:(Du=s,s=u)):(Du=s,s=u);s!==u;)a.push(s),s=Du,(i=Zc())!==u?((c=Cc())===u&&(c=Lc()),c!==u&&(l=Zc())!==u&&(f=ks())!==u?s=i=[i,c,l,f]:(Du=s,s=u)):(Du=s,s=u);a!==u&&(s=Zc())!==u?((i=gs())===u&&(i=null),i!==u?(Pu=t,e=function(r,t,e,n){const o=e.length;let u=t;u.parentheses=!0;for(let r=0;r<o;++r)u=vl(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:n,...pl()}}(e,o,a,i),t=e):(Du=t,t=u)):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;t===u&&(t=Du,(e=bc())===u&&(e=null),e!==u&&Zc()!==u&&(n=vs())!==u&&Zc()!==u?((o=gs())===u&&(o=null),o!==u?(Pu=t,e={distinct:e,expr:n,orderby:o,...pl()},t=e):(Du=t,t=u)):(Du=t,t=u))}return t}())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((o=wi())===u&&(o=null),o!==u?(Pu=t,e={type:"aggr_func",name:e,args:n,over:o,...pl()},t=e):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,(e=function(){var t;(t=function(){var t,e,n,o;t=Du,"sum"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(xo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SUM"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=Ac())===u&&(t=Ec())===u&&(t=function(){var t,e,n,o;t=Du,"avg"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(ko));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="AVG"):(Du=t,t=u)):(Du=t,t=u);return t}());return t}())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=Bs())!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((o=wi())===u&&(o=null),o!==u?(Pu=t,e={type:"aggr_func",name:e,args:{expr:n},over:o,...pl()},t=e):(Du=t,t=u)):(Du=t,t=u);return t}()),t}function mi(){var t,e,n,o;return t=Du,Ji()!==u&&Zc()!==u?("update"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Hr)),e!==u&&Zc()!==u&&(n=Dc())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u?((o=Ss())===u&&(o=null),o!==u&&Zc()!==u&&qc()!==u?(Pu=t,t={type:"on update",keyword:n,parentheses:!0,expr:o}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,Ji()!==u&&Zc()!==u?("update"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Hr)),e!==u&&Zc()!==u&&(n=Dc())!==u?(Pu=t,t=function(r){return{type:"on update",keyword:r}}(n)):(Du=t,t=u)):(Du=t,t=u)),t}function wi(){var r,t,e,n;return r=Du,tc()!==u&&Zc()!==u&&(t=ms())!==u?(Pu=r,r={type:"window",as_window_specification:t}):(Du=r,r=u),r===u&&(r=Du,tc()!==u&&Zc()!==u&&(t=Gc())!==u&&Zc()!==u&&(e=Es())!==u&&Zc()!==u?((n=gs())===u&&(n=null),n!==u&&Zc()!==u&&qc()!==u?(Pu=r,r={partitionby:e,orderby:n}):(Du=r,r=u)):(Du=r,r=u),r===u&&(r=mi())),r}function Ci(){var t,e,n,o,a;return(t=function(){var t,e,n,o,a;t=Du,(e=gc())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=Ei())!==u&&Zc()!==u&&Vi()!==u&&Zc()!==u?((o=Uc())===u&&(o=Mc())===u&&(o=Rc())===u&&(o=Ic()),o!==u&&Zc()!==u&&(a=ks())!==u&&Zc()!==u&&qc()!==u?(Pu=t,s=n,i=o,c=a,e={type:e.toLowerCase(),args:{field:s,cast_type:i,source:c},...pl()},t=e):(Du=t,t=u)):(Du=t,t=u);var s,i,c;t===u&&(t=Du,(e=gc())!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=Ei())!==u&&Zc()!==u&&Vi()!==u&&Zc()!==u&&(o=ks())!==u&&Zc()!==u&&(a=qc())!==u?(Pu=t,e=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...pl()}}(e,n,o),t=e):(Du=t,t=u),t===u&&(t=Du,"date_trunc"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(Ke)),e!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=ks())!==u&&Zc()!==u&&Yc()!==u&&Zc()!==u&&(o=Ei())!==u&&Zc()!==u&&(a=qc())!==u?(Pu=t,e=function(r,t){return{type:"function",name:{name:[{type:"origin",value:"date_trunc"}]},args:{type:"expr_list",value:[r,{type:"origin",value:t}]},over:null,...pl()}}(n,o),t=e):(Du=t,t=u)));return t}())===u&&(t=function(){var t,e,n,o,a;t=Du,"any_value"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(be));e!==u&&Zc()!==u&&Gc()!==u&&Zc()!==u&&(n=Ms())!==u&&Zc()!==u?((o=function(){var r,t,e;r=Du,ac()!==u&&Zc()!==u?((t=Ac())===u&&(t=Ec()),t!==u&&Zc()!==u&&(e=Ms())!==u?(Pu=r,r={prefix:t,expr:e}):(Du=r,r=u)):(Du=r,r=u);return r}())===u&&(o=null),o!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((a=wi())===u&&(a=null),a!==u?(Pu=t,e={type:"any_value",args:{expr:n,having:o},over:a,...pl()},t=e):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=Du,(e=function(){var t;(t=Ai())===u&&(t=function(){var t,e,n,o;t=Du,"session_user"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(su));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SESSION_USER"):(Du=t,t=u)):(Du=t,t=u);return t}());return t}())!==u&&Zc()!==u&&(n=Gc())!==u&&Zc()!==u?((o=Ss())===u&&(o=null),o!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((a=wi())===u&&(a=null),a!==u?(Pu=t,t=e={type:"function",name:{name:[{type:"default",value:e}]},args:o||{type:"expr_list",value:[]},over:a,...pl()}):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Ai())!==u&&Zc()!==u?((n=mi())===u&&(n=null),n!==u?(Pu=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...pl()}):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=Du,(e=Li())!==u&&Zc()!==u&&(n=Gc())!==u&&Zc()!==u?((o=Ms())===u&&(o=null),o!==u&&Zc()!==u&&qc()!==u&&Zc()!==u?((a=wi())===u&&(a=null),a!==u?(Pu=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...pl()}}(e,o,a)):(Du=t,t=u)):(Du=t,t=u)):(Du=t,t=u)))),t}function Li(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ti())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Wc())!==u&&(s=Zc())!==u&&(i=ti())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Wc())!==u&&(s=Zc())!==u&&(i=ti())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=t.map(r=>r[3])),e}(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Ai(){var t;return(t=function(){var t,e,n,o;t=Du,"current_date"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(nu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="CURRENT_DATE"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"current_time"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(uu));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="CURRENT_TIME"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=Dc()),t}function Ei(){var t,e;return t=Du,"year_month"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(ve)),e===u&&("day_hour"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(ye)),e===u&&("day_minute"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(de)),e===u&&("day_second"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(he)),e===u&&("day_microsecond"===r.substr(Du,15).toLowerCase()?(e=r.substr(Du,15),Du+=15):(e=u,0===Wu&&Vu(me)),e===u&&("hour_minute"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(we)),e===u&&("hour_second"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(Ce)),e===u&&("hour_microsecond"===r.substr(Du,16).toLowerCase()?(e=r.substr(Du,16),Du+=16):(e=u,0===Wu&&Vu(Le)),e===u&&("minute_second"===r.substr(Du,13).toLowerCase()?(e=r.substr(Du,13),Du+=13):(e=u,0===Wu&&Vu(Ae)),e===u&&("minute_microsecond"===r.substr(Du,18).toLowerCase()?(e=r.substr(Du,18),Du+=18):(e=u,0===Wu&&Vu(Ee)),e===u&&("second_microsecond"===r.substr(Du,18).toLowerCase()?(e=r.substr(Du,18),Du+=18):(e=u,0===Wu&&Vu(ge)),e===u&&("timezone_hour"===r.substr(Du,13).toLowerCase()?(e=r.substr(Du,13),Du+=13):(e=u,0===Wu&&Vu(_e)),e===u&&("timezone_minute"===r.substr(Du,15).toLowerCase()?(e=r.substr(Du,15),Du+=15):(e=u,0===Wu&&Vu(je)),e===u&&("century"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Te)),e===u&&("dayofweek"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Se)),e===u&&("day"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(xe)),e===u&&("date"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(ke)),e===u&&("decade"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Ie)),e===u&&("dow"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Ne)),e===u&&("doy"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Oe)),e===u&&("epoch"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Re)),e===u&&("hour"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ue)),e===u&&("isodow"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Me)),e===u&&("isoweek"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(De)),e===u&&("isoyear"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Pe)),e===u&&("microseconds"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(Fe)),e===u&&("millennium"===r.substr(Du,10).toLowerCase()?(e=r.substr(Du,10),Du+=10):(e=u,0===Wu&&Vu(He)),e===u&&("milliseconds"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu($e)),e===u&&("minute"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(We)),e===u&&("month"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Ye)),e===u&&("quarter"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Be)),e===u&&("second"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Ge)),e===u&&("time"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(qe)),e===u&&("timezone"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(Ve)),e===u&&("week"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Xe)),e===u&&("year"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Qe))))))))))))))))))))))))))))))))))))),e!==u&&(Pu=t,e=e),t=e}function gi(){var r,t,e;return r=Du,(t=ks())!==u&&Zc()!==u?((e=ts())===u&&(e=null),e!==u?(Pu=r,r=t=function(r,t){const e={expr:r};return t&&(e.offset=t),e}(t,e)):(Du=r,r=u)):(Du=r,r=u),r}function _i(){var t;return(t=function(){var t,e,n,o;t=Du,"cast"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Po));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="CAST"):(Du=t,t=u)):(Du=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Du,"safe_cast"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Fo));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SAFE_CAST"):(Du=t,t=u)):(Du=t,t=u);return t}()),t}function ji(){var t;return(t=ki())===u&&(t=Ri())===u&&(t=xi())===u&&(t=Si())===u&&(t=function(){var t,e,n,o,a,s;t=Du,(e=Rc())===u&&(e=Ic())===u&&(e=Uc())===u&&(e=Nc());if(e!==u)if(Zc()!==u){if(n=Du,39===r.charCodeAt(Du)?(o="'",Du++):(o=u,0===Wu&&Vu(dr)),o!==u){for(a=[],s=Ni();s!==u;)a.push(s),s=Ni();a!==u?(39===r.charCodeAt(Du)?(s="'",Du++):(s=u,0===Wu&&Vu(dr)),s!==u?n=o=[o,a,s]:(Du=n,n=u)):(Du=n,n=u)}else Du=n,n=u;n!==u?(Pu=t,e=Je(e,n),t=e):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;if(t===u)if(t=Du,(e=Rc())===u&&(e=Ic())===u&&(e=Uc())===u&&(e=Nc()),e!==u)if(Zc()!==u){if(n=Du,34===r.charCodeAt(Du)?(o='"',Du++):(o=u,0===Wu&&Vu(Kt)),o!==u){for(a=[],s=Ii();s!==u;)a.push(s),s=Ii();a!==u?(34===r.charCodeAt(Du)?(s='"',Du++):(s=u,0===Wu&&Vu(Kt)),s!==u?n=o=[o,a,s]:(Du=n,n=u)):(Du=n,n=u)}else Du=n,n=u;n!==u?(Pu=t,e=Je(e,n),t=e):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;return t}()),t}function Ti(){var r,t,e,n,o,a,s,i;if(r=Du,(t=ji())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ji())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=ji())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function Si(){var t,e;return t=Du,(e=function(){var t,e,n,o;t=Du,"null"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Sn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={type:"null",value:null}),t=e}function xi(){var t,e;return t=Du,(e=function(){var t,e,n,o;t=Du,"true"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(kn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={type:"bool",value:!0}),(t=e)===u&&(t=Du,(e=function(){var t,e,n,o;t=Du,"false"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Nn));e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u);return t}())!==u&&(Pu=t,e={type:"bool",value:!1}),t=e),t}function ki(){var t,e,n,o,a,s;if(t=Du,"r"===r.substr(Du,1).toLowerCase()?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(ze)),e===u&&(e=null),e!==u)if(Zc()!==u){if(n=Du,39===r.charCodeAt(Du)?(o="'",Du++):(o=u,0===Wu&&Vu(dr)),o!==u){for(a=[],s=Ni();s!==u;)a.push(s),s=Ni();a!==u?(39===r.charCodeAt(Du)?(s="'",Du++):(s=u,0===Wu&&Vu(dr)),s!==u?n=o=[o,a,s]:(Du=n,n=u)):(Du=n,n=u)}else Du=n,n=u;n!==u?(Pu=t,t=e={type:e?"regex_string":"single_quote_string",value:n[1].join(""),...pl()}):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;if(t===u)if(t=Du,"r"===r.substr(Du,1).toLowerCase()?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(ze)),e===u&&(e=null),e!==u)if(Zc()!==u){if(n=Du,34===r.charCodeAt(Du)?(o='"',Du++):(o=u,0===Wu&&Vu(Kt)),o!==u){for(a=[],s=Ii();s!==u;)a.push(s),s=Ii();a!==u?(34===r.charCodeAt(Du)?(s='"',Du++):(s=u,0===Wu&&Vu(Kt)),s!==u?n=o=[o,a,s]:(Du=n,n=u)):(Du=n,n=u)}else Du=n,n=u;n!==u?(Pu=t,t=e=function(r,t){return{type:r?"regex_string":"string",value:t[1].join(""),...pl()}}(e,n)):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;return t}function Ii(){var t;return Ze.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(rn)),t===u&&(t=Oi()),t}function Ni(){var t;return tn.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(en)),t===u&&(t=Oi()),t}function Oi(){var t,e,n,o,a,s,i,c,l,f;return t=Du,"\\'"===r.substr(Du,2)?(e="\\'",Du+=2):(e=u,0===Wu&&Vu(nn)),e!==u&&(Pu=t,e="\\'"),(t=e)===u&&(t=Du,'\\"'===r.substr(Du,2)?(e='\\"',Du+=2):(e=u,0===Wu&&Vu(on)),e!==u&&(Pu=t,e='\\"'),(t=e)===u&&(t=Du,"\\\\"===r.substr(Du,2)?(e="\\\\",Du+=2):(e=u,0===Wu&&Vu(un)),e!==u&&(Pu=t,e="\\\\"),(t=e)===u&&(t=Du,"\\/"===r.substr(Du,2)?(e="\\/",Du+=2):(e=u,0===Wu&&Vu(an)),e!==u&&(Pu=t,e="\\/"),(t=e)===u&&(t=Du,"\\b"===r.substr(Du,2)?(e="\\b",Du+=2):(e=u,0===Wu&&Vu(sn)),e!==u&&(Pu=t,e="\b"),(t=e)===u&&(t=Du,"\\f"===r.substr(Du,2)?(e="\\f",Du+=2):(e=u,0===Wu&&Vu(cn)),e!==u&&(Pu=t,e="\f"),(t=e)===u&&(t=Du,"\\n"===r.substr(Du,2)?(e="\\n",Du+=2):(e=u,0===Wu&&Vu(ln)),e!==u&&(Pu=t,e="\n"),(t=e)===u&&(t=Du,"\\r"===r.substr(Du,2)?(e="\\r",Du+=2):(e=u,0===Wu&&Vu(fn)),e!==u&&(Pu=t,e="\r"),(t=e)===u&&(t=Du,"\\t"===r.substr(Du,2)?(e="\\t",Du+=2):(e=u,0===Wu&&Vu(pn)),e!==u&&(Pu=t,e="\t"),(t=e)===u&&(t=Du,"\\u"===r.substr(Du,2)?(e="\\u",Du+=2):(e=u,0===Wu&&Vu(bn)),e!==u&&(n=$i())!==u&&(o=$i())!==u&&(a=$i())!==u&&(s=$i())!==u?(Pu=t,i=n,c=o,l=a,f=s,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Du=t,t=u),t===u&&(t=Du,92===r.charCodeAt(Du)?(e="\\",Du++):(e=u,0===Wu&&Vu(vn)),e!==u&&(Pu=t,e="\\"),(t=e)===u&&(t=Du,"''"===r.substr(Du,2)?(e="''",Du+=2):(e=u,0===Wu&&Vu(yn)),e!==u&&(Pu=t,e="''"),(t=e)===u&&(t=Du,'""'===r.substr(Du,2)?(e='""',Du+=2):(e=u,0===Wu&&Vu(dn)),e!==u&&(Pu=t,e='""'),(t=e)===u&&(t=Du,"``"===r.substr(Du,2)?(e="``",Du+=2):(e=u,0===Wu&&Vu(hn)),e!==u&&(Pu=t,e="``"),t=e))))))))))))),t}function Ri(){var r,t,e;return r=Du,(t=Ui())!==u&&(Pu=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function Ui(){var r,t,e,n;return r=Du,(t=Mi())!==u&&(e=Di())!==u&&(n=Pi())!==u?(Pu=r,r=t={type:"bigint",value:t+e+n}):(Du=r,r=u),r===u&&(r=Du,(t=Mi())!==u&&(e=Di())!==u?(Pu=r,r=t=function(r,t){const e=r+t;return yl(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e)):(Du=r,r=u),r===u&&(r=Du,(t=Mi())!==u&&(e=Pi())!==u?(Pu=r,r=t=function(r,t){return{type:"bigint",value:r+t}}(t,e)):(Du=r,r=u),r===u&&(r=Du,(t=Mi())!==u&&(Pu=r,t=function(r){return yl(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t))),r}function Mi(){var t,e,n;return(t=Fi())===u&&(t=Hi())===u&&(t=Du,45===r.charCodeAt(Du)?(e="-",Du++):(e=u,0===Wu&&Vu(Bt)),e===u&&(43===r.charCodeAt(Du)?(e="+",Du++):(e=u,0===Wu&&Vu(Yt))),e!==u&&(n=Fi())!==u?(Pu=t,t=e=e+n):(Du=t,t=u),t===u&&(t=Du,45===r.charCodeAt(Du)?(e="-",Du++):(e=u,0===Wu&&Vu(Bt)),e===u&&(43===r.charCodeAt(Du)?(e="+",Du++):(e=u,0===Wu&&Vu(Yt))),e!==u&&(n=Hi())!==u?(Pu=t,t=e=function(r,t){return r+t}(e,n)):(Du=t,t=u))),t}function Di(){var t,e,n;return t=Du,46===r.charCodeAt(Du)?(e=".",Du++):(e=u,0===Wu&&Vu(I)),e!==u&&(n=Fi())!==u?(Pu=t,t=e="."+n):(Du=t,t=u),t}function Pi(){var t,e,n;return t=Du,(e=function(){var t,e,n;t=Du,gn.test(r.charAt(Du))?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(_n));e!==u?(jn.test(r.charAt(Du))?(n=r.charAt(Du),Du++):(n=u,0===Wu&&Vu(Tn)),n===u&&(n=null),n!==u?(Pu=t,t=e=e+(null!==(o=n)?o:"")):(Du=t,t=u)):(Du=t,t=u);var o;return t}())!==u&&(n=Fi())!==u?(Pu=t,t=e=e+n):(Du=t,t=u),t}function Fi(){var r,t,e;if(r=Du,t=[],(e=Hi())!==u)for(;e!==u;)t.push(e),e=Hi();else t=u;return t!==u&&(Pu=r,t=t.join("")),r=t}function Hi(){var t;return Cn.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(Ln)),t}function $i(){var t;return An.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(En)),t}function Wi(){var t,e,n,o;return t=Du,"default"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(O)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Yi(){var t,e,n,o;return t=Du,"to"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(In)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Bi(){var t,e,n,o;return t=Du,"drop"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(On)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DROP"):(Du=t,t=u)):(Du=t,t=u),t}function Gi(){var t,e,n,o;return t=Du,"partition"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Pn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="PARTITION"):(Du=t,t=u)):(Du=t,t=u),t}function qi(){var t,e,n,o;return t=Du,"into"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Fn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Vi(){var t,e,n,o;return t=Du,"from"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Hn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Xi(){var t,e,n,o;return t=Du,"set"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(w)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SET"):(Du=t,t=u)):(Du=t,t=u),t}function Qi(){var t,e,n,o;return t=Du,"as"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(pt)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Ki(){var t,e,n,o;return t=Du,"table"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Wn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="TABLE"):(Du=t,t=u)):(Du=t,t=u),t}function zi(){var t,e,n,o;return t=Du,"tables"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Yn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="TABLES"):(Du=t,t=u)):(Du=t,t=u),t}function Ji(){var t,e,n,o;return t=Du,"on"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(Bn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Zi(){var t,e,n,o;return t=Du,"join"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Kn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function rc(){var t,e,n,o;return t=Du,"outer"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(zn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function tc(){var t,e,n,o;return t=Du,"over"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Jn)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function ec(){var t,e,n,o;return t=Du,"values"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(to)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function nc(){var t,e,n,o;return t=Du,"using"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(eo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function oc(){var t,e,n,o;return t=Du,"with"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ir)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function uc(){var t,e,n,o;return t=Du,"by"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(Lr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function ac(){var t,e,n,o;return t=Du,"having"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(ao)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function sc(){var t,e,n,o;return t=Du,"ordinal"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(co)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ORDINAL"):(Du=t,t=u)):(Du=t,t=u),t}function ic(){var t,e,n,o;return t=Du,"safe_ordinal"===r.substr(Du,12).toLowerCase()?(e=r.substr(Du,12),Du+=12):(e=u,0===Wu&&Vu(lo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SAFE_ORDINAL"):(Du=t,t=u)):(Du=t,t=u),t}function cc(){var t,e,n,o;return t=Du,"offset"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(po)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="OFFSET"):(Du=t,t=u)):(Du=t,t=u),t}function lc(){var t,e,n,o;return t=Du,"safe_offset"===r.substr(Du,11).toLowerCase()?(e=r.substr(Du,11),Du+=11):(e=u,0===Wu&&Vu(bo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="SAFE_OFFSET"):(Du=t,t=u)):(Du=t,t=u),t}function fc(){var t,e,n,o;return t=Du,"desc"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(yo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DESC"):(Du=t,t=u)):(Du=t,t=u),t}function pc(){var t,e,n,o;return t=Du,"all"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(ho)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ALL"):(Du=t,t=u)):(Du=t,t=u),t}function bc(){var t,e,n,o;return t=Du,"distinct"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(mo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DISTINCT"):(Du=t,t=u)):(Du=t,t=u),t}function vc(){var t,e,n,o;return t=Du,"between"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(wo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="BETWEEN"):(Du=t,t=u)):(Du=t,t=u),t}function yc(){var t,e,n,o;return t=Du,"in"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(Co)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="IN"):(Du=t,t=u)):(Du=t,t=u),t}function dc(){var t,e,n,o;return t=Du,"is"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(Lo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="IS"):(Du=t,t=u)):(Du=t,t=u),t}function hc(){var t,e,n,o;return t=Du,"like"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Ao)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="LIKE"):(Du=t,t=u)):(Du=t,t=u),t}function mc(){var t,e,n,o;return t=Du,"exists"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Eo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="EXISTS"):(Du=t,t=u)):(Du=t,t=u),t}function wc(){var t,e,n,o;return t=Du,"not"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Sr)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="NOT"):(Du=t,t=u)):(Du=t,t=u),t}function Cc(){var t,e,n,o;return t=Du,"and"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(go)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="AND"):(Du=t,t=u)):(Du=t,t=u),t}function Lc(){var t,e,n,o;return t=Du,"or"===r.substr(Du,2).toLowerCase()?(e=r.substr(Du,2),Du+=2):(e=u,0===Wu&&Vu(_o)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="OR"):(Du=t,t=u)):(Du=t,t=u),t}function Ac(){var t,e,n,o;return t=Du,"max"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(To)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="MAX"):(Du=t,t=u)):(Du=t,t=u),t}function Ec(){var t,e,n,o;return t=Du,"min"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(So)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="MIN"):(Du=t,t=u)):(Du=t,t=u),t}function gc(){var t,e,n,o;return t=Du,"extract"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Io)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="EXTRACT"):(Du=t,t=u)):(Du=t,t=u),t}function _c(){var t,e,n,o;return t=Du,"case"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Oo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function jc(){var t,e,n,o;return t=Du,"end"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Do)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?t=e=[e,n]:(Du=t,t=u)):(Du=t,t=u),t}function Tc(){var t,e,n,o;return t=Du,"array"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Ho)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ARRAY"):(Du=t,t=u)):(Du=t,t=u),t}function Sc(){var t,e,n,o;return t=Du,"decimal"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Go)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DECIMAL"):(Du=t,t=u)):(Du=t,t=u),t}function xc(){var t,e,n,o;return t=Du,"integer"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Qo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="INTEGER"):(Du=t,t=u)):(Du=t,t=u),t}function kc(){var t,e,n,o;return t=Du,"struct"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(Jo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="STRUCT"):(Du=t,t=u)):(Du=t,t=u),t}function Ic(){var t,e,n,o;return t=Du,"date"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(ke)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DATE"):(Du=t,t=u)):(Du=t,t=u),t}function Nc(){var t,e,n,o;return t=Du,"datetime"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(ru)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="DATETIME"):(Du=t,t=u)):(Du=t,t=u),t}function Oc(){var t,e,n,o;return t=Du,"rows"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(St)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="ROWS"):(Du=t,t=u)):(Du=t,t=u),t}function Rc(){var t,e,n,o;return t=Du,"time"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(qe)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="TIME"):(Du=t,t=u)):(Du=t,t=u),t}function Uc(){var t,e,n,o;return t=Du,"timestamp"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(tu)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="TIMESTAMP"):(Du=t,t=u)):(Du=t,t=u),t}function Mc(){var t,e,n,o;return t=Du,"interval"===r.substr(Du,8).toLowerCase()?(e=r.substr(Du,8),Du+=8):(e=u,0===Wu&&Vu(ou)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="INTERVAL"):(Du=t,t=u)):(Du=t,t=u),t}function Dc(){var t,e,n,o;return t=Du,"current_timestamp"===r.substr(Du,17).toLowerCase()?(e=r.substr(Du,17),Du+=17):(e=u,0===Wu&&Vu(au)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="CURRENT_TIMESTAMP"):(Du=t,t=u)):(Du=t,t=u),t}function Pc(){var t,e,n,o;return t=Du,"column"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(yu)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="COLUMN"):(Du=t,t=u)):(Du=t,t=u),t}function Fc(){var t,e,n,o;return t=Du,"index"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(du)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="INDEX"):(Du=t,t=u)):(Du=t,t=u),t}function Hc(){var t,e,n,o;return t=Du,"key"===r.substr(Du,3).toLowerCase()?(e=r.substr(Du,3),Du+=3):(e=u,0===Wu&&Vu(Y)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="KEY"):(Du=t,t=u)):(Du=t,t=u),t}function $c(){var t,e,n,o;return t=Du,"comment"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(mu)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="COMMENT"):(Du=t,t=u)):(Du=t,t=u),t}function Wc(){var t;return 46===r.charCodeAt(Du)?(t=".",Du++):(t=u,0===Wu&&Vu(I)),t}function Yc(){var t;return 44===r.charCodeAt(Du)?(t=",",Du++):(t=u,0===Wu&&Vu(Cu)),t}function Bc(){var t;return 42===r.charCodeAt(Du)?(t="*",Du++):(t=u,0===Wu&&Vu(Gt)),t}function Gc(){var t;return 40===r.charCodeAt(Du)?(t="(",Du++):(t=u,0===Wu&&Vu(st)),t}function qc(){var t;return 41===r.charCodeAt(Du)?(t=")",Du++):(t=u,0===Wu&&Vu(it)),t}function Vc(){var t;return 60===r.charCodeAt(Du)?(t="<",Du++):(t=u,0===Wu&&Vu($t)),t}function Xc(){var t;return 62===r.charCodeAt(Du)?(t=">",Du++):(t=u,0===Wu&&Vu(Pt)),t}function Qc(){var t;return 91===r.charCodeAt(Du)?(t="[",Du++):(t=u,0===Wu&&Vu(Lu)),t}function Kc(){var t;return 93===r.charCodeAt(Du)?(t="]",Du++):(t=u,0===Wu&&Vu(Au)),t}function zc(){var t;return 59===r.charCodeAt(Du)?(t=";",Du++):(t=u,0===Wu&&Vu(Eu)),t}function Jc(){var t;return(t=function(){var t;return"||"===r.substr(Du,2)?(t="||",Du+=2):(t=u,0===Wu&&Vu(gu)),t}())===u&&(t=function(){var t;return"&&"===r.substr(Du,2)?(t="&&",Du+=2):(t=u,0===Wu&&Vu(_u)),t}()),t}function Zc(){var r,t;for(r=[],(t=nl())===u&&(t=tl());t!==u;)r.push(t),(t=nl())===u&&(t=tl());return r}function rl(){var r,t;if(r=[],(t=nl())===u&&(t=tl()),t!==u)for(;t!==u;)r.push(t),(t=nl())===u&&(t=tl());else r=u;return r}function tl(){var t;return(t=function(){var t,e,n,o,a,s;t=Du,"/*"===r.substr(Du,2)?(e="/*",Du+=2):(e=u,0===Wu&&Vu(ju));if(e!==u){for(n=[],o=Du,a=Du,Wu++,"*/"===r.substr(Du,2)?(s="*/",Du+=2):(s=u,0===Wu&&Vu(Tu)),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);o!==u;)n.push(o),o=Du,a=Du,Wu++,"*/"===r.substr(Du,2)?(s="*/",Du+=2):(s=u,0===Wu&&Vu(Tu)),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);n!==u?("*/"===r.substr(Du,2)?(o="*/",Du+=2):(o=u,0===Wu&&Vu(Tu)),o!==u?t=e=[e,n,o]:(Du=t,t=u)):(Du=t,t=u)}else Du=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Du,"--"===r.substr(Du,2)?(e="--",Du+=2):(e=u,0===Wu&&Vu(Su));if(e!==u){for(n=[],o=Du,a=Du,Wu++,s=ol(),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);o!==u;)n.push(o),o=Du,a=Du,Wu++,s=ol(),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);n!==u?t=e=[e,n]:(Du=t,t=u)}else Du=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Du,35===r.charCodeAt(Du)?(e="#",Du++):(e=u,0===Wu&&Vu(xu));if(e!==u){for(n=[],o=Du,a=Du,Wu++,s=ol(),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);o!==u;)n.push(o),o=Du,a=Du,Wu++,s=ol(),Wu--,s===u?a=void 0:(Du=a,a=u),a!==u&&(s=el())!==u?o=a=[a,s]:(Du=o,o=u);n!==u?t=e=[e,n]:(Du=t,t=u)}else Du=t,t=u;return t}()),t}function el(){var t;return r.length>Du?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(ku)),t}function nl(){var t;return Iu.test(r.charAt(Du))?(t=r.charAt(Du),Du++):(t=u,0===Wu&&Vu(Nu)),t}function ol(){var t,e;if((t=function(){var t,e;t=Du,Wu++,r.length>Du?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(ku));Wu--,e===u?t=void 0:(Du=t,t=u);return t}())===u)if(t=[],mn.test(r.charAt(Du))?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(wn)),e!==u)for(;e!==u;)t.push(e),mn.test(r.charAt(Du))?(e=r.charAt(Du),Du++):(e=u,0===Wu&&Vu(wn));else t=u;return t}function ul(){var r,t,e,n,o,a,s,i;if(r=Du,(t=al())!==u){for(e=[],n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=al())!==u?n=o=[o,a,s,i]:(Du=n,n=u);n!==u;)e.push(n),n=Du,(o=Zc())!==u&&(a=Yc())!==u&&(s=Zc())!==u&&(i=al())!==u?n=o=[o,a,s,i]:(Du=n,n=u);e!==u?(Pu=r,r=t=l(t,e)):(Du=r,r=u)}else Du=r,r=u;return r}function al(){var r,t,e,n;return r=Du,t=Du,(e=pi())!==u?(Pu=Du,(n=(n=!0===fl[e.toUpperCase()])?u:void 0)!==u?(Pu=t,t=e=e):(Du=t,t=u)):(Du=t,t=u),t===u&&(t=null),t!==u&&(e=Zc())!==u&&(n=sl())!==u?(Pu=r,r=t=function(r,t){return{field_name:r,field_type:t}}(t,n)):(Du=r,r=u),r}function sl(){var t;return(t=cl())===u&&(t=il())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f;if(t=Du,(e=function(){var t,e,n,o;return t=Du,"string"===r.substr(Du,6).toLowerCase()?(e=r.substr(Du,6),Du+=6):(e=u,0===Wu&&Vu(zo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="STRING"):(Du=t,t=u)):(Du=t,t=u),t}())!==u){if(n=[],o=Du,(a=Zc())!==u)if((s=Gc())!==u)if((i=Zc())!==u){if(c=[],Cn.test(r.charAt(Du))?(l=r.charAt(Du),Du++):(l=u,0===Wu&&Vu(Ln)),l!==u)for(;l!==u;)c.push(l),Cn.test(r.charAt(Du))?(l=r.charAt(Du),Du++):(l=u,0===Wu&&Vu(Ln));else c=u;c!==u&&(l=Zc())!==u&&(f=qc())!==u?o=a=[a,s,i,c,l,f]:(Du=o,o=u)}else Du=o,o=u;else Du=o,o=u;else Du=o,o=u;for(;o!==u;)if(n.push(o),o=Du,(a=Zc())!==u)if((s=Gc())!==u)if((i=Zc())!==u){if(c=[],Cn.test(r.charAt(Du))?(l=r.charAt(Du),Du++):(l=u,0===Wu&&Vu(Ln)),l!==u)for(;l!==u;)c.push(l),Cn.test(r.charAt(Du))?(l=r.charAt(Du),Du++):(l=u,0===Wu&&Vu(Ln));else c=u;c!==u&&(l=Zc())!==u&&(f=qc())!==u?o=a=[a,s,i,c,l,f]:(Du=o,o=u)}else Du=o,o=u;else Du=o,o=u;else Du=o,o=u;n!==u?(Pu=t,e=function(r,t){const e={dataType:r};return t&&0!==t.length?{...e,length:parseInt(t[3].join(""),10),parentheses:!0}:e}(e,n),t=e):(Du=t,t=u)}else Du=t,t=u;return t}())===u&&(t=function(){var t,e;t=Du,(e=function(){var t,e,n,o;return t=Du,"numeric"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Bo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="NUMERIC"):(Du=t,t=u)):(Du=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Du,"int64"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu(Xo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="INT64"):(Du=t,t=u)):(Du=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Du,"float64"===r.substr(Du,7).toLowerCase()?(e=r.substr(Du,7),Du+=7):(e=u,0===Wu&&Vu(Zo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="FLOAT64"):(Du=t,t=u)):(Du=t,t=u),t}())===u&&(e=xc());e!==u&&(Pu=t,e=Ou(e));return t=e}())===u&&(t=function(){var t,e,n,o;t=Du,(e=Ic())===u&&(e=Nc())===u&&(e=Rc())===u&&(e=Uc());if(e!==u)if(Zc()!==u)if(Gc()!==u)if(Zc()!==u){if(n=[],Cn.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(Ln)),o!==u)for(;o!==u;)n.push(o),Cn.test(r.charAt(Du))?(o=r.charAt(Du),Du++):(o=u,0===Wu&&Vu(Ln));else n=u;n!==u&&(o=Zc())!==u&&qc()!==u?(Pu=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Du=t,t=u)}else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;else Du=t,t=u;t===u&&(t=Du,(e=Ic())===u&&(e=Nc())===u&&(e=Rc())===u&&(e=Uc()),e!==u&&(Pu=t,e=Ou(e)),t=e);return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c;if(t=Du,e=Du,(n=function(){var t,e,n,o;return t=Du,"bytes"===r.substr(Du,5).toLowerCase()?(e=r.substr(Du,5),Du+=5):(e=u,0===Wu&&Vu($o)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="BYTES"):(Du=t,t=u)):(Du=t,t=u),t}())!==u)if((o=Gc())!==u)if((a=Zc())!==u){if(s=[],Cn.test(r.charAt(Du))?(i=r.charAt(Du),Du++):(i=u,0===Wu&&Vu(Ln)),i!==u)for(;i!==u;)s.push(i),Cn.test(r.charAt(Du))?(i=r.charAt(Du),Du++):(i=u,0===Wu&&Vu(Ln));else s=u;s===u&&("MAX"===r.substr(Du,3)?(s="MAX",Du+=3):(s=u,0===Wu&&Vu(Ru)),s===u&&("max"===r.substr(Du,3)?(s="max",Du+=3):(s=u,0===Wu&&Vu(Uu)))),s!==u&&(i=Zc())!==u&&(c=qc())!==u?e=n=[n,o,a,s,i,c]:(Du=e,e=u)}else Du=e,e=u;else Du=e,e=u;else Du=e,e=u;e===u&&(e=function(){var t,e,n,o;return t=Du,"bool"===r.substr(Du,4).toLowerCase()?(e=r.substr(Du,4),Du+=4):(e=u,0===Wu&&Vu(Wo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="BOOL"):(Du=t,t=u)):(Du=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Du,"geography"===r.substr(Du,9).toLowerCase()?(e=r.substr(Du,9),Du+=9):(e=u,0===Wu&&Vu(Yo)),e!==u?(n=Du,Wu++,o=bi(),Wu--,o===u?n=void 0:(Du=n,n=u),n!==u?(Pu=t,t=e="GEOGRAPHY"):(Du=t,t=u)):(Du=t,t=u),t}());e!==u&&(Pu=t,e=Ou(e));return t=e}()),t}function il(){var r,t,e;return r=Du,(t=Tc())!==u&&Zc()!==u&&Vc()!==u&&Zc()!==u&&(e=ul())!==u&&Zc()!==u&&Xc()!==u?(Pu=r,r=t=Mu(t,e)):(Du=r,r=u),r}function cl(){var r,t,e;return r=Du,(t=kc())!==u&&Zc()!==u&&Vc()!==u&&Zc()!==u&&(e=ul())!==u&&Zc()!==u&&Xc()!==u?(Pu=r,r=t=Mu(t,e)):(Du=r,r=u),r}const ll={ARRAY:!0,ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CROSS:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,FOR:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!1,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,PARTITION:!0,PIVOT:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!1,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WINDOW:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0,UNNEST:!0},fl={BOOL:!0,BYTE:!0,DATE:!0,DATETIME:!0,FLOAT64:!0,INT64:!0,NUMERIC:!0,STRING:!0,TIME:!0,TIMESTAMP:!0,ARRAY:!0,STRUCT:!0};function pl(){return t.includeLocations?{loc:qu(Pu,Du)}:{}}function bl(r,t){return{type:"unary_expr",operator:r,expr:t}}function vl(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e,...pl()}}function yl(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function dl(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function hl(r,t){let e=r;for(let r=0;r<t.length;r++)e=vl(t[r][1],e,t[r][3]);return e}function ml(r){const t=El[r];return t||(r||null)}function wl(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=ml(r[1])),t.add(r.join("::"))}return Array.from(t)}let Cl=[];const Ll=new Set,Al=new Set,El={};if((e=s())!==u&&Du===r.length)return e;throw e!==u&&Du<r.length&&Vu({type:"end"}),Xu($u,Hu<r.length?r.charAt(Hu):null,Hu<r.length?qu(Hu,Hu+1):qu(Hu,Hu))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return g})),e.d(n,"autoIncrementToSQL",(function(){return x})),e.d(n,"columnOrderListToSQL",(function(){return k})),e.d(n,"commonKeywordArgsToSQL",(function(){return S})),e.d(n,"commonOptionConnector",(function(){return s})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return C})),e.d(n,"commentToSQL",(function(){return _})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return E})),e.d(n,"DEFAULT_OPT",(function(){return u})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return w})),e.d(n,"columnIdentifierToSql",(function(){return y})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return d})),e.d(n,"onPartitionsToSQL",(function(){return A})),e.d(n,"replaceParams",(function(){return L})),e.d(n,"returningToSQL",(function(){return T})),e.d(n,"hasVal",(function(){return m})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return h})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return j}));var u={database:"bigquery",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},a=u;function s(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return a}function b(r){a=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function d(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function h(r){if(r)return r.toUpperCase()}function m(r){return r}function w(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,u=r.suffix,a=r.value,s="object"===o(r)?a:r;switch(e){case"backticks_quote_string":s="`".concat(a,"`");break;case"string":s="'".concat(a,"'");break;case"regex_string":s='r"'.concat(a,'"');break;case"hex_string":s="X'".concat(a,"'");break;case"full_hex_string":s="0x".concat(a);break;case"natural_string":s="N'".concat(a,"'");break;case"bit_string":s="b'".concat(a,"'");break;case"double_quote_string":s='"'.concat(a,'"');break;case"single_quote_string":s="'".concat(a,"'");break;case"boolean":case"bool":s=a?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(t||":").concat(a),t=null;break;case"origin":s=a.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(e.toUpperCase()," '").concat(a,"'");break;case"var_string":s="N'".concat(a,"'");break;case"unicode_string":s="U&'".concat(a,"'")}var i=[];return t&&i.push(h(t)),i.push(s),u&&("string"==typeof u&&i.push(u),"object"===o(u)&&(u.collate?i.push(it(u.collate)):i.push(w(u)))),s=i.join(" "),n?"(".concat(s,")"):s}}function C(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():w(n)].filter(m)}function L(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var u=t[n];if("object"!==o(u)||"param"!==u.type)return r(u,e);if(void 0===e[u.value])throw new Error("no value for parameter :".concat(u.value," found"));return t[n]=c(e[u.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function A(r){var t=r.type,e=r.partitions;return[h(t),"(".concat(e.map((function(r){if("range"!==r.type)return w(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(w(t)," ").concat(h(n)," ").concat(w(e))})).join(", "),")")].join(" ")}function E(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,a="";return null!=e&&(a=o?"".concat(e,", ").concat(o):e),n&&(a="(".concat(a,")")),u&&u.length&&(a+=" ".concat(u.join(" "))),"".concat(t).concat(a)}function g(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=h(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,g(r.field_type)].filter(m).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function _(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(w(o)),t.join(" ")}}function j(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[h(t)];if(e){var o=e.keyword,u=e.columns;n.push(h(o),u.map(yt).join(", "))}return n.join(" ")})).join(" OR ")}function T(r){return r?["RETURNING",r.columns.map(At).filter(m).join(", ")].join(" "):""}function S(r){return r?[h(r.keyword),h(r.args)]:[]}function x(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,a=h(e);return u&&(a+="(".concat(w(n),", ").concat(w(o),")")),a}}function k(r){if(r)return r.map(wt).filter(m).join(", ")}function I(r){return function(r){if(Array.isArray(r))return N(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return N(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?N(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function O(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),h(e)]}function R(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),u=[];switch(u.push(o),o){case"KEY_BLOCK_SIZE":n&&u.push(n),u.push(w(e));break;case"BTREE":case"HASH":u.length=0,u.push.apply(u,I(O(r)));break;case"WITH PARSER":u.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":u.shift(),u.push(_(r));break;case"DATA_COMPRESSION":u.push(n,h(e.value),A(e.on));break;default:u.push(n,w(e))}return u.filter(m).join(" ")}}function U(r){return r?r.map(R):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,u=r.definition,a=r.on,s=r.with,i=[];if(i.push.apply(i,I(O(e))),u&&u.length){var c="CHECK"===h(t)?"(".concat(ut(u[0]),")"):"(".concat(u.map((function(r){return ut(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),s&&i.push("WITH (".concat(U(s).join(", "),")")),a&&i.push("ON [".concat(a,"]")),i}function D(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[Array.isArray(r.left)?r.left.map(ut).join(", "):ut(r.left),t,e,h(o.type),ut(o.value)].filter(m).join(" ");return[r.parentheses?"(".concat(u,")"):u].join(" ")}function P(r){return function(r){if(Array.isArray(r))return F(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return F(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?F(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function H(r){return r?[r.prefix.map(w).join(" "),ut(r.value),r.suffix.map(w).join(" ")]:[]}function $(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(H(n)),P(H(e))).filter(m).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",ut(e[0])):i("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(h(t)," ")))}(r):"";var t,e,n}function W(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(yt).join(", "),")"):"";return"".concat("default"===t.type?d(t.value):w(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function Y(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=h(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?d(e):ut(e))}return n.filter(m).join(" ")}}function B(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,u=r.for,a=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,y=r.into,d=void 0===y?{}:y,C=r.isolation,L=r.limit,A=r.options,E=r.orderby,g=r.parentheses_symbol,_=r.qualify,j=r.top,T=r.window,S=r.with,x=r.where,k=[W(S),"SELECT",h(t)];Array.isArray(A)&&k.push(A.join(" ")),k.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[h(t)];return e&&n.push("(".concat(e.map(ut).join(", "),")")),n.filter(m).join(" ")}}(o),v(j),gt(e,a));var I=d.position,N="";I&&(N=s("INTO",Y,d)),"column"===I&&k.push(N),k.push(s("FROM",cr,a)),"from"===I&&k.push(N);var O=l||{},R=O.keyword,U=O.expr;k.push(s(R,ut,U)),k.push(s("WHERE",ut,x)),p&&(k.push(i("GROUP BY",at(p.columns).join(", "))),k.push(at(p.modifiers).join(", "))),k.push(s("HAVING",ut,b)),k.push(s("QUALIFY",ut,_)),k.push(s("WINDOW",ut,T)),k.push(st(E,"order by")),k.push(it(n)),k.push($(L)),C&&k.push(s(C.keyword,w,C.expr)),k.push(h(f)),"end"===I&&k.push(N),k.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[h(r.type),h(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(u));var M=k.filter(m).join(" ");return g?"(".concat(M,")"):M}function G(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return q(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?q(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function q(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function V(r){if(!r||0===r.length)return"";var t,e=[],n=G(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u={},a=o.value;for(var s in o)"value"!==s&&"keyword"!==s&&(u[s]=o[s]);var i=[yt(u)],c="";a&&(c=ut(a),i.push("=",c)),e.push(i.filter(m).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function X(r){if("select"===r.type)return B(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(d).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(m).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(yt).join(", "),")")}}function z(r){var t=r.expr,e=r.keyword,n=t.type,o=[h(e)];switch(n){case"origin":o.push(w(t));break;case"update":o.push("UPDATE",s("SET",V,t.set),s("WHERE",ut,t.where))}return o.filter(m).join(" ")}function J(r){if(!r)return"";var t=r.action;return[K(r.target),z(t)].filter(m).join(" ")}function Z(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,a=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},y=v.keyword,d=v.set,C=[h(e),h(o),cr(t),Q(f)];return Array.isArray(u)&&C.push("(".concat(u.map(w).join(", "),")")),C.push(s(Array.isArray(i)?"VALUES":"",X,i)),C.push(s("ON CONFLICT",J,a)),C.push(s("SET",V,b)),C.push(s("WHERE",ut,c)),C.push(s(y,V,d)),C.push(T(p)),C.filter(m).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",ut(t),h(e),ut(n)].filter(m).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(h(t),"(").concat(n&&ut(n)||"",")"),s("AS","string"==typeof e?d:ut,e),s(h(o&&o.keyword),d,o&&o.as)].filter(m).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,a=[ut(n),"FOR",yt(e),D(o)],s=["".concat(h(u),"(").concat(a.join(" "),")")];return t&&s.push("AS",d(t)),s.join(" ")}(r);default:return""}}function ur(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,a=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(h(t),"(".concat(d(n)),"(".concat(o.map(ut).filter(m).join(", "),"))"));break;case"spatial_window_max_cells":s.push(h(t),"=",ut(e));break;case"index":s.push(h(a),h(t),u?"(".concat(e.map(d).join(", "),")"):"= ".concat(d(e)));break;default:s.push(ut(e))}return s.filter(m).join(" ")}}function ar(r,t){var e=r.name,n=r.symbol;return[h(e),n,t].filter(m).join(" ")}function sr(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",ut(r.of));break;case"from_to":t.push("FROM",ut(r.from),"TO",ut(r.to));break;case"between_and":t.push("BETWEEN",ut(r.between),"AND",ut(r.and));break;case"contained":t.push("CONTAINED","IN",ut(r.in))}return t.filter(m).join(" ")}function ir(r){if("UNNEST"===h(r.type))return nr(r);var t,e,n,o,u=r.table,a=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,y=r.tablesample,L=r.temporal_table,A=r.table_hint,E=d(b),g=d(a),_=d(p),j=u&&d(u);if(c)switch(c.type){case"values":var T=c.parentheses,S=c.values,x=c.prefix,k=[T&&"(","",T&&")"],I=X(S);x&&(I=I.split("(").slice(1).map((function(r){return"".concat(h(x),"(").concat(r)})).join("")),k[1]="VALUES ".concat(I),j=k.filter(m).join("");break;case"tumble":j=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,u=[d(t.expr.db),d(t.expr.schema),d(t.expr.table)].filter(m).join("."),a="DESCRIPTOR(".concat(yt(e.expr),")"),s=["TABLE(TUMBLE(TABLE ".concat(ar(t,u)),ar(e,a)],i=ar(o,rr(o.expr));return n&&n.expr?s.push(i,"".concat(ar(n,rr(n.expr)),"))")):s.push("".concat(i,"))")),s.filter(m).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return C(r).join(" ")})).join(", "),j="".concat(h(e),"(").concat(h(n),"(").concat(o,"))");break;default:j=ut(c)}var N=[[E,g,_,j=[h(f),j,h(v)].filter(m).join(" ")].filter(m).join(".")];if(y){var O=["TABLESAMPLE",ut(y.expr),w(y.repeatable)].filter(m).join(" ");N.push(O)}N.push(function(r){if(r){var t=r.keyword,e=r.expr;return[h(t),sr(e)].filter(m).join(" ")}}(L),s("AS","string"==typeof i?d:ut,i),or(l)),A&&N.push(h(A.keyword),"(".concat(A.expr.map(ur).filter(m).join(", "),")"));var R=N.filter(m).join(" ");return r.parentheses?"(".concat(R,")"):R}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var u=[],a=[],i=!0===e?1:e.length,c=0;c++<i;)u.push("("),a.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return u.join("")+o+a.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],y=v.on,d=v.using,C=v.join,L=[];L.push(C?" ".concat(h(C)):","),L.push(ir(v)),L.push(s("ON",ut,y)),d&&L.push("USING (".concat(d.map(w).join(", "),")")),p.push(L.filter(m).join(" "))}return p.filter(m).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=w(n);switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.filter(m).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[d(t.db),d(t.table)].filter(m).join(".");return"".concat(h(e)," ").concat(n);case"column":return"COLUMN ".concat(yt(t));default:return"".concat(h(e)," ").concat(w(t))}}function pr(r){var t=r.keyword,e=r.expr;return[h(t),w(e)].filter(m).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(m).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),a=ut(e);return[u,h(n),a].filter(m).join(" ")}function yr(r){var t,e,n,o,u=r.keyword,a=r.suffix,i="";switch(h(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[s("IN",w,e&&e.right),s("FROM",cr,n),$(o)].filter(m).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===h(t.op)?s("LIKE",w,t.right):s("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=s("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(m).join(" ")}}(r);break;case"CREATE":i=s("",ir,r[a]);break;case"VAR":i=ot(r.var),u=""}return["SHOW",h(u),h(a),i].filter(m).join(" ")}var dr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,a=t.expr,s=t.orderby;return[h(u),h(n),[[d(o.schema),d(o.name)].filter(m).join("."),"(".concat(a.map(Jr).join(", ")).concat(s?[" ORDER","BY",s.map(Jr).join(", ")].join(" "):"",")")].filter(m).join(""),zr(e)].filter(m).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,u=r.expr,a=void 0===u?[]:u,s=h(t),i=cr(e),c=a.map(ut);return[s,"TABLE",h(n),w(o),i,c.join(", ")].filter(m).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[h(r.type),h(e),d(n),zr(t)].filter(m).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[h(r.type),h(e),[d(n.schema),d(n.name)].filter(m).join("."),zr(t)].filter(m).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[h(r.type),h(n),[[d(o.schema),d(o.name)].filter(m).join("."),t&&"(".concat(t.expr?t.expr.map(Jr).join(", "):"",")")].filter(m).join(""),zr(e)].filter(m).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,a=r.with,s=h(t),i=ir(u),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(yt).join(", "),")"));n&&c.push("WITH ".concat(n.map(h).join(", ")));c.push("AS",B(o)),a&&c.push(h(a));return c.filter(m).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[h(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[h(t),h(e),ut(n),h(o),d(u)].filter(m).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.options,s=[h(t),h(e),h(n)],i=[d(o.schema),o.name].filter(m).join("."),c="".concat(u.expr.map(Jr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Jr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(a.map(Qr).join(", "),")")),s.filter(m).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,a=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,y=r.unlogged,d=r.with,C=[h(t),h(f),h(a),h(y),h(e),h(s),cr(n)];if(o){var L=o.type,A=cr(o.table);return C.push(h(L),A),C.filter(m).join(" ")}if(b)return C.concat([qr(b)]).filter(m).join(" ");i&&C.push("(".concat(i.map(Br).join(", "),")"));if(c){var E=p().database,g=E&&"sqlite"===E.toLowerCase()?", ":" ";C.push(c.map(lr).join(g))}if(d){var _=d.map((function(r){return[w(r.keyword),h(r.symbol),w(r.value)].join(" ")})).join(", ");C.push("WITH (".concat(_,")"))}C.push(h(l),h(u)),v&&C.push(hr(v));return C.filter(m).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,a=r.for_each,s=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[h(f),h(l),h(e),h(c),d(t),h(i)],y=j(o);v.push(y,"ON",ir(p)),s&&v.push("FROM",ir(s));v.push.apply(v,$r(S(n)).concat($r(S(a)))),b&&v.push(h(b.type),ut(b.cond));return v.push(h(u.keyword),Hr(u.expr)),v.filter(m).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,a=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,y=[h(u),h(c),ut(t),h(n),h(i),ir(l),h(b),f.map((function(r){var t=[h(r.keyword)],e=r.args;return e&&t.push(h(e.keyword),e.columns.map(yt).join(", ")),t.join(" ")})),"ON",ir(a),h(e&&e.keyword),h(e&&e.args),p&&"".concat(h(p.keyword)," ").concat(d(p.trigger)),s("WHEN",ut,v),h(o.prefix)];switch(o.type){case"set":y.push(s("SET",V,o.expr));break;case"multiple":y.push(mr(o.expr.ast))}return y.push(h(o.suffix)),y.filter(m).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,a=r.type,i=r.with,c=r.version;return[h(a),h(o),h(n),w(t),h(i),s("SCHEMA",w,u),s("VERSION",w,c),s("FROM",w,e)].filter(m).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.returns,s=r.options,i=r.last,c=[h(t),h(e),h(n)],l=[w(o.schema),o.name.map(w).join(".")].filter(m).join("."),f=u.map(Jr).filter(m).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),Array.isArray(n)?"(".concat(n.map(Ct).join(", "),")"):Vr(n)].filter(m).join(" ")}(a),s.map(Xr).join(" "),i),c.filter(m).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,u=r.include,a=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,y=r.on_kw,C=r.table,L=r.tablespace,A=r.type,E=r.where,g=r.with,_=r.with_before_where,j=g&&"WITH (".concat(U(g).join(", "),")"),T=u&&"".concat(h(u.keyword)," (").concat(u.columns.map((function(r){return"string"==typeof r?d(r):ut(r)})).join(", "),")"),S=l;l&&(S="string"==typeof l?d(l):[d(l.schema),d(l.name)].filter(m).join("."));var x=[h(A),h(i),h(n),h(o),h(t),S,h(y),ir(C)].concat($r(O(c)),["(".concat(k(a),")"),T,U(p).join(" "),zr(b),zr(v),s("TABLESPACE",w,L)]);_?x.push(j,s("WHERE",ut,E)):x.push(s("WHERE",ut,E),j);return x.push(s("ON",ut,f),s("FILESTREAM_ON",w,e)),x.filter(m).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,a=r.create_definitions,s=[h(t),h(o),h(e),h(u),cr(n)];a&&s.push(a.map(Br).join(" "));return s.filter(m).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,u=r.create_definitions,a=r[e],s=a.db,i=a.schema,c=[w(s),i.map(w).join(".")].filter(m).join("."),l=[h(t),h(n),h(e),h(o),c];u&&l.push(u.map(lr).join(" "));return l.filter(m).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,a=r.recursive,s=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,w=p.db,L=p.schema,A=p.view,E=[d(w),d(L),d(A)].filter(m).join(".");return[h(f),h(s),h(l),h(a),t&&"ALGORITHM = ".concat(h(t)),ut(n),c&&"SQL SECURITY ".concat(h(c)),h(u),h(o),E,e&&"(".concat(e.map(y).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return C(r).join(" ")})).join(", "),")")].join(" "),"AS",hr(i),h(b)].filter(m).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,a=r.create_definitions,s=[h(n),h(o),[d(e.schema),d(e.name)].filter(m).join("."),h(t),E(u)];if(a&&a.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Wr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(a);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(ut(f));break;case"default":c.push(h(p),ut(f.value));break;case"constraint":c.push(Or(f))}}}catch(r){l.e(r)}finally{l.f()}s.push(c.filter(m).join(" "))}return s.filter(m).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,a=[h(r.type),h(n),[d(o.schema),d(o.name)].filter(m).join("."),h(t),h(u)];if(e){var s=[];switch(u){case"enum":case"range":s.push(ut(e));break;default:s.push("(".concat(e.map(Br).join(", "),")"))}a.push(s.filter(m).join(" "))}return a.filter(m).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,a=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[xr(t)];return e&&n.push(h(e.keyword),e.auth_plugin,w(e.value)),n.filter(m).join(" ")})).join(", "),b=[h(f),h(u),h(o),p];n&&b.push(h(n.keyword),n.value.map(xr).join(", "));b.push(s(c&&c.keyword,ut,c&&c.value)),l&&b.push(h(l.keyword),l.value.map((function(r){return ut(r)})).join(" "));i&&i.forEach((function(r){return b.push(s(r.keyword,ut,r.value))}));return b.push(w(a),_(e),w(t)),b.filter(m).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[h(r.type),h(e),fr(n),pr(t)].filter(m).join(" ")},select:B,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),ut(n)].filter(m).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,a=r.with,i=r.limit,c=r.returning,l=[W(a),"DELETE"],f=gt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(s("FROM",cr,e)),l.push(s("WHERE",ut,o)),l.push(st(u,"order by")),l.push($(i)),l.push(T(c)),l.filter(m).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[h(t),ir(e),(n||[]).map(br).filter(m).join(", ")].filter(m).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[h(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(m).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[h(t),B(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,h(t),n,"IN",mr([o]),"LOOP",mr(u),"END LOOP",e].filter(m).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,a=r.with,i=r.limit,c=r.returning;return[W(a),"UPDATE",cr(e),s("SET",V,n),s("FROM",cr,t),s("WHERE",ut,o),st(u,"order by"),$(i),T(c)].filter(m).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,a=r.go,s=r.semicolons,i=r.suffix,c=[h(r.type),ut(t),w(u),"".concat(Ar(o.ast||o)).concat(s[0]),h(a)];n&&c.push(n.map((function(r){return[h(r.type),ut(r.boolean_expr),"THEN",Ar(r.then.ast||r.then),r.semicolon].filter(m).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Ar(e.ast||e)).concat(s[1]));return c.push(w(i)),c.filter(m).join(" ")},insert:Z,drop:Tr,truncate:Tr,replace:Z,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[h(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,a=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(m).join(""),h(n),h(o)];switch(c){case"variable":l.push(dt(u),ut(r.collate),h(a)),i&&l.push(h(i.keyword),ut(i.value));break;case"cursor":l.push(h(s));break;case"table":l.push(h(s),"(".concat(i.map(Br).join(", "),")"))}return l.filter(m).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=h(t),o=d(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,a=Er(e);try{for(a.s();!(u=a.n()).done;){var s=u.value.map(ir);n.push(s.join(" TO "))}}catch(r){a.e(r)}finally{a.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=h(t);return"".concat(n," ").concat(d(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=h(t),u=e.map(ut).join(", ");return[o,h(n),u].filter(m).join(" ")},lock:Sr,unlock:Sr,show:yr,grant:kr,revoke:kr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[h(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[h(t),h(e)];n&&u.push([w(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(m).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(h(o.type),h(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(m).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,u=[w(e),h(n)];return o&&u.push(o.map(w).join(", ")),u.filter(m).join(" ")}};function hr(r){if(!r)return"";for(var t=dr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,a=[n&&"(",t(r)];r._next;){var s=dr[r._next.type],i=h(r.set_op);a.push(i,s(r._next)),r=r._next}return a.push(n&&")",st(o,"order by"),$(u)),a.filter(m).join(" ")}function mr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=hr(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var wr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Cr(r){var t=r&&r.ast?r.ast:r;if(!wr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Lr(r){return Array.isArray(r)?(r.forEach(Cr),mr(r)):(Cr(r),hr(r))}function Ar(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Lr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Lr(r)}function Er(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=_r(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function gr(r){return function(r){if(Array.isArray(r))return jr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||_r(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _r(r,t){if(r){if("string"==typeof r)return jr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?jr(r,t):void 0}}function jr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Tr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=r.suffix,a=[h(t),h(e),h(o)];switch(e){case"table":a.push(cr(n));break;case"trigger":a.push([n[0].schema?"".concat(d(n[0].schema),"."):"",d(n[0].trigger)].filter(m).join(""));break;case"database":case"schema":case"procedure":a.push(d(n));break;case"view":a.push(cr(n),r.options&&r.options.map(ut).filter(m).join(" "));break;case"index":a.push.apply(a,[yt(n)].concat(gr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(ut).filter(m).join(" ")]));break;case"type":a.push(n.map(yt).join(", "),r.options&&r.options.map(ut).filter(m).join(" "))}return u&&a.push(u.map(ut).filter(m).join(" ")),a.filter(m).join(" ")}function Sr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),h(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,a=[],s=Er(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return h(e[r])})).filter(m).join(" "))}a.push(n.join(" "))};for(s.s();!(u=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[a.join(", ")].concat(gr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(m).join(" ")}function xr(r){var t=r.name,e=r.host,n=[w(t)];return e&&n.push("@",w(e)),n.join("")}function kr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,a=r.to_from,s=r.user_or_roles,i=r.with,c=[h(t),w(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(yt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(w(u.object_type),u.priv_level.map((function(r){return[d(r.prefix),d(r.name)].filter(m).join(".")})).join(", "));break;case"proxy":c.push(xr(u))}return c.push(h(a),s.map(xr).join(", ")),c.push(w(i)),c.filter(m).join(" ")}function Ir(r){return function(r){if(Array.isArray(r))return Nr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Nr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Or(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,a=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(h(u)),l.push(d(t));var b=h(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&d(o)),l.push.apply(l,Ir(M(r))),l.push.apply(l,Ir(ht(a))),l.push(h(n)),l.push(s("FOR",d,i)),l.push(w(c)),l.filter(m).join(" ")}}function Rr(r){if(r){var t=r.type;return"rows"===t?[h(t),ut(r.expr)].filter(m).join(" "):ut(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),Rr(o)].filter(m).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,a=h(o);if("WINDOW"===a)return"OVER ".concat(Ur(t));if("ON UPDATE"===a){var s="".concat(h(o)," ").concat(h(n)),i=ut(e)||[];return u&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return h(t);for(var e=r.array,n=e.dimension,o=e.length,u=[],a=0;a<n;a++)u.push("["),o&&o[a]&&u.push(w(o[a])),u.push("]");return u.join("")}function Fr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,u=r.as,a=r.offset,s=r.parentheses,i=bt({expr:e,offset:a}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,y=p.dataType,C=p.parentheses,L=p.quoted,A=p.scale,E=p.suffix,g=p.expr,_=g?ut(g):"";null!=v&&(_=A?"".concat(v,", ").concat(A):v),C&&(_="(".concat(_,")")),b&&(_="<".concat(_,">")),E&&E.length&&(_+=" ".concat(E.map(w).join(" ")));var j="::",T="",S=[];"as"===o&&(0===l&&(i="".concat(h(n),"(").concat(i)),T=")",j=" ".concat(o.toUpperCase()," ")),0===l&&S.push(i);var x=Pr(p);S.push(j,L,y,L,x,_,T),c.push(S.filter(m).join(""))}u&&c.push(" AS ".concat(d(u)));var k=c.filter(m).join("");return s?"(".concat(k,")"):k}function Hr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,u=r.parentheses,a=r.within_group,s=r.over,i=r.suffix,c=Dr(s),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[h(t),h(e),"(".concat(st(n,"order by"),")")].filter(m).join(" ")}(a),f=ut(i),p=[w(n.schema),n.name.map(w).join(".")].filter(m).join(".");if(!t)return[p,l,c].filter(m).join(" ");var b=r.separator||", ";"TRIM"===h(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var y=ut(t);if(Array.isArray(b)){for(var d=y[0],C=1,L=y.length;C<L;++C)d=[d,y[C]].join(" ".concat(ut(b[C-1])," "));v.push(d)}else v.push(y.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(m).join(" "),[u?"(".concat(v,")"):v,l,c].filter(m).join(" ")}function $r(r){return function(r){if(Array.isArray(r))return Yr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Wr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wr(r,t){if(r){if("string"==typeof r)return Yr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Yr(r,t):void 0}}function Yr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Br(r){if(!r)return[];var t,e,n,o,u=r.resource;switch(u){case"column":return Ct(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(h(n)),e.push(o),e.push.apply(e,I(M(t))),e.filter(m).join(" ");case"constraint":return Or(r);case"sequence":return[h(r.prefix),ut(r.value)].filter(m).join(" ");default:throw new Error("unknown resource = ".concat(u," type"))}}function Gr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(w(r.from),")"),"TO","(".concat(w(r.to),")"));break;case"in":t.push("IN","(".concat(ut(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(w(r.modulus),", REMAINDER ").concat(w(r.remainder),")"))}return t.filter(m).join(" ")}function qr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,u=[h(t),ir(e),h(n.keyword),Gr(n.expr)];return o&&u.push("TABLESPACE",w(o)),u.filter(m).join(" ")}function Vr(r){return r.dataType?E(r):[d(r.db),d(r.schema),d(r.table)].filter(m).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[h(t),r.symbol,hr(r.declare),h(r.begin),mr(r.expr),h(r.end),r.symbol].filter(m).join(" ");case"set":return[h(t),r.parameter,h(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(m).join(" ");case"return":return[h(t),ut(r.expr)].filter(m).join(" ");default:return ut(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[h(t),e];switch(h(t)){case"SFUNC":o.push([d(n.schema),n.name].filter(m).join("."));break;case"STYPE":case"MSTYPE":o.push(E(n));break;default:o.push(ut(n))}return o.filter(m).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",w(t),"VALUES",h(e.type),"(".concat(w(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return gt(t)}}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,u=r.if_exists,a=r.old_column,s=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Ct(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=d(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=d(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,h(r[i])].filter(m).join(" ");break;case"constraint":f=d(r[i]),p=[Br(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=d(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[h(t),h(o),h(n),h(u),a&&yt(a),h(s),f&&f.trim(),p.filter(m).join(" ")];return l&&b.push(h(l.keyword),l.expr&&yt(l.expr)),b.filter(m).join(" ")}function Jr(r){var t=r.default&&[h(r.default.keyword),ut(r.default.value)].join(" ");return[h(r.mode),r.name,E(r.type),t].filter(m).join(" ")}function Zr(r){return(Zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(h(r.type)){case"STRUCT":return"(".concat(gt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(gt(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(gt(r),")")})).filter(m).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[h(r.keyword)];return t&&"object"===Zr(t)&&(e.length=0,e.push(g(t))),e.push(rt(r)),e.filter(m).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr);u=Array.isArray(u)?u.join(", "):u;var a=r.name,s=Dr(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.separator&&t.separator.delimiter&&(u=[u,w(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(u=[u,ut(t.separator.expr)].join(" ")),t.orderby&&(u=[u,st(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(u=[u,h(t.separator.keyword),w(t.separator.value)].filter(m).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(a,"(").concat(u,")"),i,s,c].filter(m).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,a="".concat(h(e),"(").concat(ut(o));return u&&(a="".concat(a," HAVING ").concat(h(u.prefix)," ").concat(ut(u.expr))),[a="".concat(a,")"),Dr(n)].filter(m).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=r.separator,a=void 0===u?", ":u;return[e,"(",t?ut(t).join(a):"",")",o&&" ",o].filter(m).join("")}(r),Dr(t)].filter(m).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,a=e.length;u<a;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Fr,collate:it,column_ref:yt,column_definition:Ct,datatype:E,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,a=["".concat(h(e),"(").concat(h(n)),"FROM",h(o),ut(u)];return"".concat(a.filter(m).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[h(t),e,ut(n)].filter(m).join(" ")}(t[r])})).filter(m).join(", ");return"".concat(h(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode,a=[h(o),"(".concat(n.map((function(r){return yt(r)})).join(", "),")")].join(" "),s=[h(t),["(",ut(r.expr),u&&" ".concat(w(u)),")"].filter(m).join("")].join(" ");return[a,s,Lt(e)].filter(m).join(" ")},function:Hr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,u=n.map(ut).join(", ");return[o?"(".concat(u,")"):u,"->",ut(e)].join(" ")},insert:hr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[h(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,u=[ut(e),"VALUE",ut(n)];return o&&u.push("ON","NULL",ut(o)),u.filter(m).join(" ")},json_visitor:function(r){return[r.symbol,ut(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,ut(t.expr)].filter(m).join(" ")},show:yr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[w(e.schema),e.name.map(w).join(".")].filter(m).join(".");return["".concat(o,"(").concat(ut(n).join(", "),")"),"AS",Hr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.quoted,a=r.suffix,s=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return a&&(c+=a),s.push(c),[u,s.join(" "),u].filter(m).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}var a=t.type;return"expr"===a?ut(t.expr):nt[a]?nt[a](t):w(t)}}function at(r){return r?(Array.isArray(r)||(r=[r]),r.map(ut)):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=h(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",h(r.nulls)].filter(m).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,u=e.value,a=[h(t)];return u||a.push(o),a.push(Array.isArray(n)?n.map(w).join("."):w(n)),u&&a.push(o),a.push(ut(u)),a.filter(m).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return d(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),w(r.value),"".concat(r.name?")":""),"]"].filter(m).join("")})).join("");return[ut(e),u,o].filter(m).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.brackets?"[".concat(w(o.index),"]"):"".concat(o.notation).concat(w(o.index));o.property&&(u="".concat(u,".").concat(w(o.property))),e.push(u)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function yt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,u=r.db,a=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,y=r.order_by,w=r.subFields,C=void 0===w?[]:w,L="*"===n?"*":bt(n,a),A=[u,f,p].filter(m).map((function(r){return"".concat("string"==typeof r?d(r):ut(r))})),E=A[0];if(E){for(var g=1;g<A.length;++g)E="".concat(E).concat(c[g]||".").concat(A[g]);L="".concat(E).concat(c[g]||".").concat(L)}var _=[L=["".concat(L).concat(vt(t))].concat(lt(C)).join("."),it(o),ut(l),s("AS",ut,e)];_.push("string"==typeof v?h(v):ut(v)),_.push(h(y));var j=_.filter(m).join(" ");return b?"(".concat(j,")"):j}function dt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,u=r.expr,a=E({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(u&&(a+=ut(u)),r.array){var s=Pr(r);a+=[/^\[.*\]$/.test(s)?"":" ",s].join("")}return a}}function ht(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,a=r.on_action;return t.push(h(n)),t.push(cr(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(h(o)),a.map((function(r){return t.push(h(r.type),ut(r.value))})),t.filter(m)}function mt(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,a=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,y=r.unique,d=r.primary_key,L=r.column_format,A=r.reference_definition,E=[h(e&&e.action),h(e&&e.value)].filter(m).join(" ");if(b||t.push(E),f){var g=f.type,j=f.value;t.push(g.toUpperCase(),ut(j))}var T=p().database;return a&&t.push(h(a.keyword),w(a.constraint)),t.push(Or(o)),t.push(function(r){if(r)return[h(r.value),"(".concat(ut(r.expr),")"),h(r.storage_type)].filter(m).join(" ")}(b)),b&&t.push(E),t.push(x(v),h(d),h(y),_(u)),t.push.apply(t,lt(C(n))),"sqlite"!==T.toLowerCase()&&t.push(ut(i)),t.push.apply(t,lt(C(L))),t.push.apply(t,lt(C(c))),t.push.apply(t,lt(ht(A))),t.push(s("USING",ut,l)),t.filter(m).join(" ")}function wt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by,a="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return a.collate=null,[ut(a),ut(e),o,h(u),h(n)].filter(m).join(" ")}function Ct(r){var t=[],e=yt(r.column),n=dt(r.definition);return t.push(e),t.push(n),t.push(mt(r)),t.filter(m).join(" ")}function Lt(r){return r?"object"===ct(r)?["AS",ut(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?d(r):y(r)].join(" "):""}function At(r,t){var e=r.expr,n=r.type;if("cast"===n)return Fr(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var a=[o],s=u.map((function(r){return At(r,t)})).join(", ");return a.push([h(n),n&&"(",s,n&&")"].filter(m).join("")),a.filter(m).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,Lt(r.as)].filter(m).join(" ")}function Et(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function gt(r,t){if(!r||"*"===r)return r;var e=Et(t);return r.map((function(r){return At(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=at(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",u=t.join(o);return e?"(".concat(u,")"):u},nt.select=function(r){var t="object"===et(r._next)?hr(r):B(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[w(r.key),w(r.value)].join(", ")})).join(", ");return[h(t),"[".concat(e,"]")].join("")};var _t=e(0);function jt(r){return(jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var Tt,St,xt,kt=(Tt={},St="bigquery",xt=_t.parse,(St=function(r){var t=function(r,t){if("object"!=jt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==jt(t)?t:t+""}(St))in Tt?Object.defineProperty(Tt,St,{value:xt,enumerable:!0,configurable:!0,writable:!0}):Tt[St]=xt,Tt);function It(r){return(It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Nt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return Ot(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ot(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Ot(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Rt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=It(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=It(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==It(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&Rt(r.prototype,t),e&&Rt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),Ar(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),ut(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(b(e),!r||"*"===r)return[];var n=Et(t);return r.map((function(r){return At(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=t.database,n=void 0===e?"bigquery":e;b(t);var o=n.toLowerCase();if(kt[o])return kt[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||u.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,s=this["".concat(o,"List")].bind(this),i=s(r,e),c=!0,l="",f=Nt(i);try{for(f.s();!(a=f.n()).done;){var p,b=a.value,v=!1,y=Nt(t);try{for(y.s();!(p=y.n()).done;){var d=p.value,h=new RegExp("^".concat(d,"$"),"i");if(h.test(b)){v=!0;break}}}catch(r){y.e(r)}finally{y.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=bigquery.js.map