!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},a={start:ia},s=ia,i=ea("IF",!0),c=ea("EXTENSION",!0),l=ea("SCHEMA",!0),f=ea("VERSION",!0),p=function(r,t){return Il(r,t,1)},b=ea("NULLS",!0),v=ea("FIRST",!0),y=ea("LAST",!0),d=ea("AUTO_INCREMENT",!0),h=ea("UNIQUE",!0),E=ea("KEY",!0),m=ea("PRIMARY",!0),C=ea("COLUMN_FORMAT",!0),L=ea("FIXED",!0),w=ea("DYNAMIC",!0),A=ea("DEFAULT",!0),T=ea("STORAGE",!0),S=ea("DISK",!0),R=ea("MEMORY",!0),I=ea("ALGORITHM",!0),N=ea("INSTANT",!0),g=ea("INPLACE",!0),_=ea("COPY",!0),j=ea("LOCK",!0),O=ea("NONE",!0),x=ea("SHARED",!0),k=ea("EXCLUSIVE",!0),U=ea("PRIMARY KEY",!0),M=ea("FOREIGN KEY",!0),D=ea("MATCH FULL",!0),P=ea("MATCH PARTIAL",!0),G=ea("MATCH SIMPLE",!0),F=ea("RESTRICT",!0),H=ea("CASCADE",!0),B=ea("SET NULL",!0),Y=ea("NO ACTION",!0),$=ea("SET DEFAULT",!0),W=ea("TRIGGER",!0),V=ea("BEFORE",!0),X=ea("AFTER",!0),q=ea("INSTEAD OF",!0),Q=ea("ON",!0),K=ea("EXECUTE",!0),J=ea("PROCEDURE",!0),Z=ea("FUNCTION",!0),z=ea("OF",!0),rr=ea("NOT",!0),tr=ea("DEFERRABLE",!0),er=ea("INITIALLY IMMEDIATE",!0),nr=ea("INITIALLY DEFERRED",!0),or=ea("FOR",!0),ur=ea("EACH",!0),ar=ea("ROW",!0),sr=ea("STATEMENT",!0),ir=ea("CHARACTER",!0),cr=ea("SET",!0),lr=ea("CHARSET",!0),fr=ea("COLLATE",!0),pr=ea("AVG_ROW_LENGTH",!0),br=ea("KEY_BLOCK_SIZE",!0),vr=ea("MAX_ROWS",!0),yr=ea("MIN_ROWS",!0),dr=ea("STATS_SAMPLE_PAGES",!0),hr=ea("CONNECTION",!0),Er=ea("COMPRESSION",!0),mr=ea("'",!1),Cr=ea("ZLIB",!0),Lr=ea("LZ4",!0),wr=ea("ENGINE",!0),Ar=ea("IN",!0),Tr=ea("ACCESS SHARE",!0),Sr=ea("ROW SHARE",!0),Rr=ea("ROW EXCLUSIVE",!0),Ir=ea("SHARE UPDATE EXCLUSIVE",!0),Nr=ea("SHARE ROW EXCLUSIVE",!0),gr=ea("ACCESS EXCLUSIVE",!0),_r=ea("SHARE",!0),jr=ea("MODE",!0),Or=ea("NOWAIT",!0),xr=ea("(",!1),kr=ea(")",!1),Ur=ea("BTREE",!0),Mr=ea("HASH",!0),Dr=ea("GIST",!0),Pr=ea("GIN",!0),Gr=ea("WITH",!0),Fr=ea("PARSER",!0),Hr=ea("VISIBLE",!0),Br=ea("INVISIBLE",!0),Yr=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Ul[t]=t,e&&(Ul[e]=t),function(r){const t=_l(r);r.clear(),t.forEach(t=>r.add(t))}(kl)}),t},$r=ea("DATA",!0),Wr=ea("TIMECOL",!0),Vr=ea("DESCRIPTOR",!0),Xr=ea("SIZE",!0),qr=ea("OFFSET",!0),Qr=ea("=",!1),Kr=function(r,t){return Nl(r,t)},Jr=ea("!",!1),Zr=ea(">=",!1),zr=ea(">",!1),rt=ea("<=",!1),tt=ea("<>",!1),et=ea("<",!1),nt=ea("!=",!1),ot=ea("ESCAPE",!0),ut=ea("+",!1),at=ea("-",!1),st=ea("*",!1),it=ea("/",!1),ct=ea("%",!1),lt=ea("$",!1),ft=ea("~",!1),pt=ea("?|",!1),bt=ea("?&",!1),vt=ea("?",!1),yt=ea("#-",!1),dt=ea("#>>",!1),ht=ea("#>",!1),Et=ea("@>",!1),mt=ea("<@",!1),Ct=function(r){return!0===wl[r.toUpperCase()]},Lt=ea('"',!1),wt=/^[^"]/,At=na(['"'],!0,!1),Tt=/^[^']/,St=na(["'"],!0,!1),Rt=ea("`",!1),It=/^[^`]/,Nt=na(["`"],!0,!1),gt=/^[A-Za-z_\u4E00-\u9FA5]/,_t=na([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),jt=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,Ot=na([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),xt=/^[A-Za-z0-9_]/,kt=na([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),Ut=ea(":",!1),Mt=ea("OVER",!0),Dt=ea("POSITION",!0),Pt=ea("VALUE",!0),Gt=ea("NULL",!0),Ft=ea("ABSENT",!0),Ht=ea("json_object",!0),Bt=ea("BOTH",!0),Yt=ea("LEADING",!0),$t=ea("TRAILING",!0),Wt=ea("trim",!0),Vt=ea("placing",!0),Xt=ea("for",!0),qt=ea("overlay",!0),Qt=ea("SUBSTRING",!0),Kt=ea("CENTURY",!0),Jt=ea("DAY",!0),Zt=ea("DATE",!0),zt=ea("DECADE",!0),re=ea("DOW",!0),te=ea("DOY",!0),ee=ea("EPOCH",!0),ne=ea("HOUR",!0),oe=ea("ISODOW",!0),ue=ea("ISOYEAR",!0),ae=ea("MICROSECONDS",!0),se=ea("MILLENNIUM",!0),ie=ea("MILLISECONDS",!0),ce=ea("MINUTE",!0),le=ea("MONTH",!0),fe=ea("QUARTER",!0),pe=ea("SECOND",!0),be=ea("TIMEZONE",!0),ve=ea("TIMEZONE_HOUR",!0),ye=ea("TIMEZONE_MINUTE",!0),de=ea("WEEK",!0),he=ea("YEAR",!0),Ee=/^[^"\\\0-\x1F\x7F]/,me=na(['"',"\\",["\0",""],""],!0,!1),Ce=/^[^'\\]/,Le=na(["'","\\"],!0,!1),we=ea("\\'",!1),Ae=ea('\\"',!1),Te=ea("\\\\",!1),Se=ea("\\/",!1),Re=ea("\\b",!1),Ie=ea("\\f",!1),Ne=ea("\\n",!1),ge=ea("\\r",!1),_e=ea("\\t",!1),je=ea("\\u",!1),Oe=ea("\\",!1),xe=ea("''",!1),ke=ea('""',!1),Ue=ea("``",!1),Me=/^[\n\r]/,De=na(["\n","\r"],!1,!1),Pe=ea(".",!1),Ge=/^[0-9]/,Fe=na([["0","9"]],!1,!1),He=/^[0-9a-fA-F]/,Be=na([["0","9"],["a","f"],["A","F"]],!1,!1),Ye=/^[eE]/,$e=na(["e","E"],!1,!1),We=/^[+\-]/,Ve=na(["+","-"],!1,!1),Xe=ea("NOT NULL",!0),qe=ea("TRUE",!0),Qe=ea("TO",!0),Ke=ea("FALSE",!0),Je=(ea("SHOW",!0),ea("DROP",!0)),Ze=ea("USE",!0),ze=ea("ALTER",!0),rn=ea("SELECT",!0),tn=ea("UPDATE",!0),en=ea("CREATE",!0),nn=ea("TEMPORARY",!0),on=ea("DELETE",!0),un=ea("INSERT",!0),an=ea("RECURSIVE",!0),sn=ea("REPLACE",!0),cn=ea("RETURNING",!0),ln=ea("RENAME",!0),fn=ea("IGNORE",!0),pn=(ea("EXPLAIN",!0),ea("PARTITION",!0)),bn=ea("INTO",!0),vn=ea("FROM",!0),yn=ea("AS",!0),dn=ea("TABLE",!0),hn=ea("TABLESPACE",!0),En=ea("DATABASE",!0),mn=ea("NATURAL",!0),Cn=ea("LEFT",!0),Ln=ea("RIGHT",!0),wn=ea("FULL",!0),An=ea("INNER",!0),Tn=ea("JOIN",!0),Sn=ea("CROSS",!0),Rn=ea("APPLY",!0),In=ea("OUTER",!0),Nn=ea("UNION",!0),gn=ea("INTERSECT",!0),_n=ea("EXCEPT",!0),jn=ea("VALUES",!0),On=ea("USING",!0),xn=ea("WHERE",!0),kn=ea("GROUP",!0),Un=ea("BY",!0),Mn=ea("ORDER",!0),Dn=ea("HAVING",!0),Pn=ea("LIMIT",!0),Gn=ea("ASC",!0),Fn=ea("DESC",!0),Hn=ea("ALL",!0),Bn=ea("DISTINCT",!0),Yn=ea("BETWEEN",!0),$n=ea("IS",!0),Wn=ea("LIKE",!0),Vn=ea("SIMILAR",!0),Xn=ea("EXISTS",!0),qn=ea("AND",!0),Qn=ea("OR",!0),Kn=ea("COUNT",!0),Jn=ea("MAX",!0),Zn=ea("MIN",!0),zn=ea("SUM",!0),ro=ea("AVG",!0),to=ea("COLLECT",!0),eo=ea("RANK",!0),no=ea("DENSE_RANK",!0),oo=ea("LISTAGG",!0),uo=ea("ROW_NUMBER",!0),ao=ea("TUMBLE",!0),so=(ea("TUMBLE_START",!0),ea("TUMBLE_END",!0),ea("HOP_START",!0),ea("HOP_END",!0),ea("SESSION_START",!0),ea("SESSION_END",!0),ea("TUMBLE_ROWTIME",!0),ea("HOP_ROWTIME",!0),ea("SESSION_ROWTIME",!0),ea("TUMBLE_PROCTIME",!0),ea("HOP_PROCTIME",!0),ea("SESSION_PROCTIME",!0),ea("EXTRACT",!0)),io=ea("CALL",!0),co=ea("CASE",!0),lo=ea("WHEN",!0),fo=ea("THEN",!0),po=ea("ELSE",!0),bo=ea("END",!0),vo=ea("CAST",!0),yo=ea("TRY_CAST",!0),ho=ea("BOOL",!0),Eo=ea("BOOLEAN",!0),mo=ea("CHAR",!0),Co=ea("VARCHAR",!0),Lo=ea("STRING",!0),wo=ea("NUMERIC",!0),Ao=ea("DECIMAL",!0),To=ea("SIGNED",!0),So=ea("UNSIGNED",!0),Ro=ea("INT",!0),Io=ea("ZEROFILL",!0),No=ea("INTEGER",!0),go=ea("JSON",!0),_o=ea("JSONB",!0),jo=ea("GEOMETRY",!0),Oo=ea("SMALLINT",!0),xo=ea("TINYINT",!0),ko=ea("TINYTEXT",!0),Uo=ea("TEXT",!0),Mo=ea("MEDIUMTEXT",!0),Do=ea("LONGTEXT",!0),Po=ea("BIGINT",!0),Go=ea("FLOAT",!0),Fo=ea("DOUBLE",!0),Ho=ea("DATETIME",!0),Bo=ea("TIME",!0),Yo=ea("TIMESTAMP",!0),$o=ea("TRUNCATE",!0),Wo=ea("USER",!0),Vo=ea("UUID",!0),Xo=ea("ARRAY",!0),qo=ea("MAP",!0),Qo=(ea("MULTISET",!0),ea("CURRENT_DATE",!0)),Ko=(ea("ADDDATE",!0),ea("INTERVAL",!0)),Jo=(ea("SECONDS",!0),ea("CURRENT_TIME",!0)),Zo=ea("CURRENT_TIMESTAMP",!0),zo=ea("CURRENT_USER",!0),ru=ea("SESSION_USER",!0),tu=ea("SYSTEM_USER",!0),eu=ea("GLOBAL",!0),nu=ea("SESSION",!0),ou=ea("LOCAL",!0),uu=ea("PERSIST",!0),au=ea("PERSIST_ONLY",!0),su=ea("@",!1),iu=ea("@@",!1),cu=ea("return",!0),lu=ea(":=",!1),fu=ea("::",!1),pu=ea("DUAL",!0),bu=ea("ADD",!0),vu=ea("COLUMN",!0),yu=ea("INDEX",!0),du=ea("FULLTEXT",!0),hu=ea("SPATIAL",!0),Eu=ea("COMMENT",!0),mu=ea("CONSTRAINT",!0),Cu=ea("CONCURRENTLY",!0),Lu=ea("REFERENCES",!0),wu=ea("SQL_CALC_FOUND_ROWS",!0),Au=ea("SQL_CACHE",!0),Tu=ea("SQL_NO_CACHE",!0),Su=ea("SQL_SMALL_RESULT",!0),Ru=ea("SQL_BIG_RESULT",!0),Iu=ea("SQL_BUFFER_RESULT",!0),Nu=ea(",",!1),gu=ea("[",!1),_u=ea("]",!1),ju=ea(";",!1),Ou=ea("->",!1),xu=ea("->>",!1),ku=ea("=>",!1),Uu=ea("||",!1),Mu=ea("&&",!1),Du=ea("/*",!1),Pu=ea("*/",!1),Gu=ea("--",!1),Fu=(ea("#",!1),{type:"any"}),Hu=ea("years",!0),Bu=ea("months",!0),Yu=ea("days",!0),$u=ea("hours",!0),Wu=ea("minutes",!0),Vu=ea("seconds",!0),Xu=/^[ \t\n\r]/,qu=na([" ","\t","\n","\r"],!1,!1),Qu=function(r){return{dataType:r}},Ku=0,Ju=0,Zu=[{line:1,column:1}],zu=0,ra=[],ta=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=a[t.startRule]}function ea(r,t){return{type:"literal",text:r,ignoreCase:t}}function na(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function oa(t){var e,n=Zu[t];if(n)return n;for(e=t-1;!Zu[e];)e--;for(n={line:(n=Zu[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Zu[t]=n,n}function ua(r,t){var e=oa(r),n=oa(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function aa(r){Ku<zu||(Ku>zu&&(zu=Ku,ra=[]),ra.push(r))}function sa(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function ia(){var r,t;return r=Ku,el()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=la())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Jc())!==u&&(s=el())!==u&&(i=la())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Jc())!==u&&(s=el())!==u&&(i=la())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(xl),columnList:_l(kl),ast:n}}(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=r,r=t):(Ku=r,r=u),r}function ca(){var t;return(t=function(){var r,t,e,n,o,a;r=Ku,(t=Si())!==u&&el()!==u&&(e=Gi())!==u&&el()!==u&&(n=Va())!==u?(Ju=r,s=t,i=e,(c=n)&&c.forEach(r=>xl.add(`${s}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(xl),columnList:_l(kl),ast:{type:s.toLowerCase(),keyword:i.toLowerCase(),name:c}},r=t):(Ku=r,r=u);var s,i,c;r===u&&(r=Ku,(t=Si())!==u&&el()!==u&&(e=Dc())!==u&&el()!==u&&(n=ks())!==u&&el()!==u&&Fi()!==u&&el()!==u&&(o=Ka())!==u&&el()!==u?((a=function(){var r,t,e,n,o,a;r=Ku,(t=wa())===u&&(t=Aa());if(t!==u){for(e=[],n=Ku,(o=el())!==u?((a=wa())===u&&(a=Aa()),a!==u?n=o=[o,a]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=wa())===u&&(a=Aa()),a!==u?n=o=[o,a]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,t=p(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())===u&&(a=null),a!==u&&el()!==u?(Ju=r,t=function(r,t,e,n,o){return{tableList:Array.from(xl),columnList:_l(kl),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,a),r=t):(Ku=r,r=u)):(Ku=r,r=u));return r}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,a,s,i,c,l,f,p,b,v,y,d,h;r=Ku,(t=Ii())!==u&&el()!==u?((e=Ni())===u&&(e=null),e!==u&&el()!==u&&Gi()!==u&&el()!==u?((n=ba())===u&&(n=null),n!==u&&el()!==u&&(o=Va())!==u&&el()!==u&&(a=function(){var r,t,e,n,o,a,s,i,c;if(r=Ku,(t=Wc())!==u)if(el()!==u)if((e=ha())!==u){for(n=[],o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=ha())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);o!==u;)n.push(o),o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=ha())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);n!==u&&(o=el())!==u&&(a=Vc())!==u?(Ju=r,t=Il(e,n),r=t):(Ku=r,r=u)}else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;return r}())!==u&&(s=el())!==u?((i=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ja())!==u){for(e=[],n=Ku,(o=el())!==u?((a=Yc())===u&&(a=null),a!==u&&(s=el())!==u&&(i=ja())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=Yc())===u&&(a=null),a!==u&&(s=el())!==u&&(i=ja())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())===u&&(i=null),i!==u&&(c=el())!==u?(l=Ku,(f=Vi())!==u&&(p=el())!==u&&(b=Wc())!==u&&(v=el())!==u&&(y=ya())!==u&&(d=el())!==u&&(h=Vc())!==u?l=f=[f,p,b,v,y,d,h]:(Ku=l,l=u),l===u&&(l=null),l!==u&&(f=el())!==u?((p=xi())===u&&(p=ji()),p===u&&(p=null),p!==u&&(b=el())!==u?((v=Pi())===u&&(v=null),v!==u&&(y=el())!==u?((d=pa())===u&&(d=null),d!==u?(Ju=r,E=t,m=e,C=n,w=a,A=i,T=l,S=p,R=v,I=d,(L=o)&&L.forEach(r=>xl.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(xl),columnList:_l(kl),ast:{type:E[0].toLowerCase(),keyword:"table",temporary:m&&m[0].toLowerCase(),if_not_exists:C,table:L,ignore_replace:S&&S[0].toLowerCase(),as:R&&R[0].toLowerCase(),query_expr:I&&I.ast,create_definitions:w,table_options:A,with:T&&T[4]}},r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);var E,m,C,L,w,A,T,S,R,I;r===u&&(r=Ku,(t=Ii())!==u&&el()!==u?((e=Ni())===u&&(e=null),e!==u&&el()!==u&&Gi()!==u&&el()!==u?((n=ba())===u&&(n=null),n!==u&&el()!==u&&(o=Va())!==u&&el()!==u?(a=Ku,(s=Vi())!==u&&(i=el())!==u&&(c=Wc())!==u&&(l=el())!==u&&(f=ya())!==u&&(p=el())!==u&&(b=Vc())!==u?a=s=[s,i,c,l,f,p,b]:(Ku=a,a=u),a===u&&(a=null),a!==u&&(s=el())!==u&&(i=function r(){var t,e;(t=function(){var r,t;r=Ku,tc()!==u&&el()!==u&&(t=Va())!==u?(Ju=r,r={type:"like",table:t}):(Ku=r,r=u);return r}())===u&&(t=Ku,Wc()!==u&&el()!==u&&(e=r())!==u&&el()!==u&&Vc()!==u?(Ju=t,(n=e).parentheses=!0,t=n):(Ku=t,t=u));var n;return t}())!==u?(Ju=r,t=function(r,t,e,n,o,u){return n&&n.forEach(r=>xl.add(`create::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),{tableList:Array.from(xl),columnList:_l(kl),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:u,with:o&&o[4]}}}(t,e,n,o,a,i),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b,v,y,d,h,E,m,C,L,w;t=Ku,(e=Ii())!==u&&el()!==u?(n=Ku,(o=ac())!==u&&(a=el())!==u&&(s=ji())!==u?n=o=[o,a,s]:(Ku=n,n=u),n===u&&(n=null),n!==u&&(o=el())!==u?((a=Hc())===u&&(a=null),a!==u&&(s=el())!==u?("trigger"===r.substr(Ku,7).toLowerCase()?(i=r.substr(Ku,7),Ku+=7):(i=u,0===ta&&aa(W)),i!==u&&el()!==u&&(c=Ws())!==u&&el()!==u?("before"===r.substr(Ku,6).toLowerCase()?(l=r.substr(Ku,6),Ku+=6):(l=u,0===ta&&aa(V)),l===u&&("after"===r.substr(Ku,5).toLowerCase()?(l=r.substr(Ku,5),Ku+=5):(l=u,0===ta&&aa(X)),l===u&&("instead of"===r.substr(Ku,10).toLowerCase()?(l=r.substr(Ku,10),Ku+=10):(l=u,0===ta&&aa(q)))),l!==u&&el()!==u&&(f=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ga())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=ac())!==u&&(s=el())!==u&&(i=ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=ac())!==u&&(s=el())!==u&&(i=ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u&&el()!==u?("on"===r.substr(Ku,2).toLowerCase()?(p=r.substr(Ku,2),Ku+=2):(p=u,0===ta&&aa(Q)),p!==u&&el()!==u&&(b=Ka())!==u&&el()!==u?(v=Ku,(y=Mi())!==u&&(d=el())!==u&&(h=Ka())!==u?v=y=[y,d,h]:(Ku=v,v=u),v===u&&(v=null),v!==u&&(y=el())!==u?((d=function(){var t,e,n,o,a;t=Ku,e=Ku,"not"===r.substr(Ku,3).toLowerCase()?(n=r.substr(Ku,3),Ku+=3):(n=u,0===ta&&aa(rr));n===u&&(n=null);n!==u&&(o=el())!==u?("deferrable"===r.substr(Ku,10).toLowerCase()?(a=r.substr(Ku,10),Ku+=10):(a=u,0===ta&&aa(tr)),a!==u?e=n=[n,o,a]:(Ku=e,e=u)):(Ku=e,e=u);e!==u&&(n=el())!==u?("initially immediate"===r.substr(Ku,19).toLowerCase()?(o=r.substr(Ku,19),Ku+=19):(o=u,0===ta&&aa(er)),o===u&&("initially deferred"===r.substr(Ku,18).toLowerCase()?(o=r.substr(Ku,18),Ku+=18):(o=u,0===ta&&aa(nr))),o!==u?(Ju=t,i=o,e={keyword:(s=e)&&s[0]?s[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):(Ku=t,t=u)):(Ku=t,t=u);var s,i;return t}())===u&&(d=null),d!==u&&(h=el())!==u?((E=function(){var t,e,n,o;t=Ku,"for"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(or));e!==u&&el()!==u?("each"===r.substr(Ku,4).toLowerCase()?(n=r.substr(Ku,4),Ku+=4):(n=u,0===ta&&aa(ur)),n===u&&(n=null),n!==u&&el()!==u?("row"===r.substr(Ku,3).toLowerCase()?(o=r.substr(Ku,3),Ku+=3):(o=u,0===ta&&aa(ar)),o===u&&("statement"===r.substr(Ku,9).toLowerCase()?(o=r.substr(Ku,9),Ku+=9):(o=u,0===ta&&aa(sr))),o!==u?(Ju=t,a=e,i=o,e={keyword:(s=n)?`${a.toLowerCase()} ${s.toLowerCase()}`:a.toLowerCase(),args:i.toLowerCase()},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var a,s,i;return t}())===u&&(E=null),E!==u&&el()!==u?((m=function(){var r,t;r=Ku,cc()!==u&&el()!==u&&Wc()!==u&&el()!==u&&(t=ds())!==u&&el()!==u&&Vc()!==u?(Ju=r,r={type:"when",cond:t,parentheses:!0}):(Ku=r,r=u);return r}())===u&&(m=null),m!==u&&el()!==u?("execute"===r.substr(Ku,7).toLowerCase()?(C=r.substr(Ku,7),Ku+=7):(C=u,0===ta&&aa(K)),C!==u&&el()!==u?("procedure"===r.substr(Ku,9).toLowerCase()?(L=r.substr(Ku,9),Ku+=9):(L=u,0===ta&&aa(J)),L===u&&("function"===r.substr(Ku,8).toLowerCase()?(L=r.substr(Ku,8),Ku+=8):(L=u,0===ta&&aa(Z))),L!==u&&el()!==u&&(w=dl())!==u?(Ju=t,A=a,T=i,R=f,I=b,N=v,g=d,_=E,j=m,O=L,x=w,e={type:"create",replace:n&&"or replace",constraint:c,location:(S=l)&&S.toLowerCase(),events:R,table:I,from:N&&N[2],deferrable:g,for_each:_,when:j,execute:{keyword:"execute "+O.toLowerCase(),expr:x},constraint_type:T&&T.toLowerCase(),keyword:T&&T.toLowerCase(),constraint_kw:A&&A.toLowerCase(),resource:"constraint"},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var A,T,S,R,I,N,g,_,j,O,x;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,p,b,v,y,d,h,E;t=Ku,(e=Ii())!==u&&el()!==u?("extension"===r.substr(Ku,9).toLowerCase()?(n=r.substr(Ku,9),Ku+=9):(n=u,0===ta&&aa(c)),n!==u&&el()!==u?((o=ba())===u&&(o=null),o!==u&&el()!==u?((a=Ws())===u&&(a=pi()),a!==u&&el()!==u?((s=Vi())===u&&(s=null),s!==u&&el()!==u?(i=Ku,"schema"===r.substr(Ku,6).toLowerCase()?(p=r.substr(Ku,6),Ku+=6):(p=u,0===ta&&aa(l)),p!==u&&(b=el())!==u&&(v=Ws())!==u?i=p=[p,b,v]:(Ku=i,i=u),i===u&&(i=pi()),i===u&&(i=null),i!==u&&(p=el())!==u?(b=Ku,"version"===r.substr(Ku,7).toLowerCase()?(v=r.substr(Ku,7),Ku+=7):(v=u,0===ta&&aa(f)),v!==u&&(y=el())!==u?((d=Ws())===u&&(d=pi()),d!==u?b=v=[v,y,d]:(Ku=b,b=u)):(Ku=b,b=u),b===u&&(b=null),b!==u&&(v=el())!==u?(y=Ku,(d=Mi())!==u&&(h=el())!==u?((E=Ws())===u&&(E=pi()),E!==u?y=d=[d,h,E]:(Ku=y,y=u)):(Ku=y,y=u),y===u&&(y=null),y!==u?(Ju=t,m=o,C=a,L=s,w=i,A=b,T=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:m,extension:jl(C),with:L&&L[0].toLowerCase(),schema:jl(w&&w[2].toLowerCase()),version:jl(A&&A[2]),from:jl(T&&T[2])},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var m,C,L,w,A,T;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b,v,y,d,h,E,m;t=Ku,(e=Ii())!==u&&el()!==u?((n=Gc())===u&&(n=null),n!==u&&el()!==u&&(o=Dc())!==u&&el()!==u?((a=function(){var t,e,n,o;t=Ku,"concurrently"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(Cu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CONCURRENTLY"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(a=null),a!==u&&el()!==u?((s=Ms())===u&&(s=null),s!==u&&el()!==u&&(i=Fi())!==u&&el()!==u&&(c=Ka())!==u&&el()!==u?((l=Ya())===u&&(l=null),l!==u&&el()!==u&&Wc()!==u&&el()!==u&&(f=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=da())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=da())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=da())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u&&el()!==u&&Vc()!==u&&el()!==u?(p=Ku,(b=Vi())!==u&&(v=el())!==u&&(y=Wc())!==u&&(d=el())!==u&&(h=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Wa())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Wa())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Wa())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u&&(E=el())!==u&&(m=Vc())!==u?p=b=[b,v,y,d,h,E,m]:(Ku=p,p=u),p===u&&(p=null),p!==u&&(b=el())!==u?(v=Ku,(y=function(){var t,e,n,o;t=Ku,"tablespace"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(hn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TABLESPACE"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(d=el())!==u&&(h=Ws())!==u?v=y=[y,d,h]:(Ku=v,v=u),v===u&&(v=null),v!==u&&(y=el())!==u?((d=za())===u&&(d=null),d!==u&&(h=el())!==u?(Ju=t,C=e,L=n,w=o,A=a,T=s,S=i,R=c,I=l,N=f,g=p,_=v,j=d,e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:C[0].toLowerCase(),index_type:L&&L.toLowerCase(),keyword:w.toLowerCase(),concurrently:A&&A.toLowerCase(),index:T,on_kw:S[0].toLowerCase(),table:R,index_using:I,index_columns:N,with:g&&g[4],with_before_where:!0,tablespace:_&&{type:"origin",value:_[2]},where:j}},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var C,L,w,A,T,S,R,I,N,g,_,j;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Ku,(e=Ii())!==u&&el()!==u?((n=function(){var t,e,n,o;t=Ku,"database"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(En));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DATABASE"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ku,"schema"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(l));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SCHEMA"):(Ku=t,t=u)):(Ku=t,t=u);return t}()),n!==u&&el()!==u?((o=ba())===u&&(o=null),o!==u&&el()!==u&&(a=yl())!==u&&el()!==u?((s=function(){var r,t,e,n,o,a;if(r=Ku,(t=_a())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=_a())!==u?n=o=[o,a]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=_a())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u?(Ju=r,t=p(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())===u&&(s=null),s!==u?(Ju=t,e=function(r,t,e,n,o){const u=t.toLowerCase();return{tableList:Array.from(xl),columnList:_l(kl),ast:{type:r[0].toLowerCase(),keyword:u,if_not_exists:e,[u]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,a,s),t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);return t}());return t}())===u&&(t=function(){var r,t,e,n;r=Ku,(t=Nc())!==u&&el()!==u?((e=Gi())===u&&(e=null),e!==u&&el()!==u&&(n=Va())!==u?(Ju=r,o=t,a=e,(s=n)&&s.forEach(r=>xl.add(`${o}::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={tableList:Array.from(xl),columnList:_l(kl),ast:{type:o.toLowerCase(),keyword:a&&a.toLowerCase()||"table",name:s}},r=t):(Ku=r,r=u)):(Ku=r,r=u);var o,a,s;return r}())===u&&(t=function(){var r,t,e;r=Ku,(t=Oi())!==u&&el()!==u&&Gi()!==u&&el()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Ba())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ba())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ba())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=r,(n=e).forEach(r=>r.forEach(r=>r.table&&xl.add(`rename::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`))),t={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"rename",table:n}},r=t):(Ku=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=Ku,(e=function(){var t,e,n,o;t=Ku,"call"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(io));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CALL"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&(n=dl())!==u?(Ju=t,o=n,e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"call",expr:o}},t=e):(Ku=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=Ku,(e=function(){var t,e,n,o;t=Ku,"use"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Ze));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&(n=Ms())!==u?(Ju=t,o=n,xl.add(`use::${o}::null`),e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"use",db:o}},t=e):(Ku=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n,o;t=Ku,(e=function(){var t,e,n,o;t=Ku,"alter"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(ze));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&Gi()!==u&&el()!==u&&(n=Va())!==u&&el()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=La())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=La())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=La())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=t,s=o,(a=n)&&a.length>0&&a.forEach(r=>xl.add(`alter::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"alter",table:a,expr:s}},t=e):(Ku=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o;t=Ku,(e=Di())!==u&&el()!==u?((n=function(){var t,e,n,o;t=Ku,"global"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(eu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="GLOBAL"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ku,"session"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(nu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SESSION"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ku,"local"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(ou));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="LOCAL"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ku,"persist"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(uu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="PERSIST"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Ku,"persist_only"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(au));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="PERSIST_ONLY"):(Ku=t,t=u)):(Ku=t,t=u);return t}()),n===u&&(n=null),n!==u&&el()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ll())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ll())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ll())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=t,a=n,s=o,e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"set",keyword:a,expr:s}},t=e):(Ku=t,t=u)):(Ku=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Ku,(e=function(){var t,e,n,o;t=Ku,"lock"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(j));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u?((n=Gi())===u&&(n=null),n!==u&&el()!==u&&(o=Va())!==u&&el()!==u?((a=function(){var t,e,n,o;t=Ku,"in"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Ar));e!==u&&el()!==u?("access share"===r.substr(Ku,12).toLowerCase()?(n=r.substr(Ku,12),Ku+=12):(n=u,0===ta&&aa(Tr)),n===u&&("row share"===r.substr(Ku,9).toLowerCase()?(n=r.substr(Ku,9),Ku+=9):(n=u,0===ta&&aa(Sr)),n===u&&("row exclusive"===r.substr(Ku,13).toLowerCase()?(n=r.substr(Ku,13),Ku+=13):(n=u,0===ta&&aa(Rr)),n===u&&("share update exclusive"===r.substr(Ku,22).toLowerCase()?(n=r.substr(Ku,22),Ku+=22):(n=u,0===ta&&aa(Ir)),n===u&&("share row exclusive"===r.substr(Ku,19).toLowerCase()?(n=r.substr(Ku,19),Ku+=19):(n=u,0===ta&&aa(Nr)),n===u&&("exclusive"===r.substr(Ku,9).toLowerCase()?(n=r.substr(Ku,9),Ku+=9):(n=u,0===ta&&aa(k)),n===u&&("access exclusive"===r.substr(Ku,16).toLowerCase()?(n=r.substr(Ku,16),Ku+=16):(n=u,0===ta&&aa(gr)),n===u&&("share"===r.substr(Ku,5).toLowerCase()?(n=r.substr(Ku,5),Ku+=5):(n=u,0===ta&&aa(_r))))))))),n!==u&&el()!==u?("mode"===r.substr(Ku,4).toLowerCase()?(o=r.substr(Ku,4),Ku+=4):(o=u,0===ta&&aa(jr)),o!==u?(Ju=t,e={mode:`in ${n.toLowerCase()} mode`},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(a=null),a!==u&&el()!==u?("nowait"===r.substr(Ku,6).toLowerCase()?(s=r.substr(Ku,6),Ku+=6):(s=u,0===ta&&aa(Or)),s===u&&(s=null),s!==u?(Ju=t,i=n,l=a,f=s,(c=o)&&c.forEach(r=>xl.add(`lock::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),e={tableList:Array.from(xl),columnList:_l(kl),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var i,c,l,f;return t}()),t}function la(){var r;return(r=pa())===u&&(r=function(){var r,t,e,n,o,a;r=Ku,(t=Ri())!==u&&el()!==u&&(e=Va())!==u&&el()!==u&&Di()!==u&&el()!==u&&(n=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=us())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=us())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=us())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u&&el()!==u?((o=za())===u&&(o=null),o!==u&&el()!==u?((a=as())===u&&(a=null),a!==u?(Ju=r,t=function(r,t,e,n){const o={};return r&&r.forEach(r=>{const{db:t,as:e,schema:n,table:u,join:a}=r,s=a?"select":"update",i=[t,n].filter(Boolean).join(".")||null;t&&(o[u]=i),u&&xl.add(`${s}::${i}::${u}`)}),t&&t.forEach(r=>{if(r.table){const t=gl(r.table);xl.add(`update::${o[t]||null}::${t}`)}kl.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(xl),columnList:_l(kl),ast:{type:"update",table:r,set:t,where:e,returning:n}}}(e,n,o,a),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=Ku,(t=cs())!==u&&el()!==u?((e=Ui())===u&&(e=null),e!==u&&el()!==u&&(n=Ka())!==u&&el()!==u?((o=is())===u&&(o=null),o!==u&&el()!==u&&Wc()!==u&&el()!==u&&(a=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Ys())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ys())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ys())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u&&el()!==u&&Vc()!==u&&el()!==u&&(s=ss())!==u&&el()!==u?((i=as())===u&&(i=null),i!==u?(Ju=r,t=function(r,t,e,n,o,u){if(t&&(xl.add(`insert::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>kl.add(`insert::${r}::${t}`))}return{tableList:Array.from(xl),columnList:_l(kl),ast:{type:r,table:[t],columns:n,values:o,partition:e,returning:u}}}(t,n,o,a,s,i),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=Ku,(t=cs())!==u&&el()!==u?((e=xi())===u&&(e=null),e!==u&&el()!==u?((n=Ui())===u&&(n=null),n!==u&&el()!==u&&(o=Ka())!==u&&el()!==u?((a=is())===u&&(a=null),a!==u&&el()!==u&&(s=ss())!==u&&el()!==u?((i=as())===u&&(i=null),i!==u?(Ju=r,t=function(r,t,e,n,o,u,a){n&&(xl.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),kl.add(`insert::${n.table}::(.*)`),n.as=null);const s=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(xl),columnList:_l(kl),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:s,returning:a}}}(t,e,n,o,a,s,i),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o;r=Ku,(t=gi())!==u&&el()!==u?((e=Va())===u&&(e=null),e!==u&&el()!==u&&(n=Ha())!==u&&el()!==u?((o=za())===u&&(o=null),o!==u?(Ju=r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,schema:e,as:n,table:o,join:u}=r,a=u?"select":"delete",s=[t,e].filter(Boolean).join(".")||null;o&&xl.add(`${a}::${s}::${o}`),u||kl.add(`delete::${o}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(xl),columnList:_l(kl),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);return r}())===u&&(r=ca())===u&&(r=function(){var r,t;r=[],t=cl();for(;t!==u;)r.push(t),t=cl();return r}()),r}function fa(){var t,e,n,o,a;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"union"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Nn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="UNION"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Ku,"intersect"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(gn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INTERSECT"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Ku,"except"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(_n));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="EXCEPT"):(Ku=t,t=u)):(Ku=t,t=u);return t}()),e!==u&&el()!==u?((n=Ki())===u&&(n=Ji()),n===u&&(n=null),n!==u?(Ju=t,o=e,t=e=(a=n)?`${o.toLowerCase()} ${a.toLowerCase()}`:""+o.toLowerCase()):(Ku=t,t=u)):(Ku=t,t=u),t}function pa(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Oa())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=fa())!==u&&(s=el())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=fa())!==u&&(s=el())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u&&(n=el())!==u?((o=ts())===u&&(o=null),o!==u&&(a=el())!==u?((s=os())===u&&(s=null),s!==u?(Ju=r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(xl),columnList:_l(kl),ast:r}}(t,e,o,s)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)}else Ku=r,r=u;return r}function ba(){var t,e;return t=Ku,"if"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(i)),e!==u&&el()!==u&&oc()!==u&&el()!==u&&nc()!==u?(Ju=t,t=e="IF NOT EXISTS"):(Ku=t,t=u),t}function va(){var r,t,e;return r=Ku,(t=Us())!==u&&el()!==u&&kc()!==u&&el()!==u&&(e=Us())!==u?(Ju=r,r=t={keyword:t,symbol:"=",value:e}):(Ku=r,r=u),r}function ya(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=va())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=va())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=va())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Il(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function da(){var t,e,n,o,a,s,i,c,l,f,p,d,h,E;return t=Ku,(e=ds())!==u&&el()!==u?((n=Ca())===u&&(n=null),n!==u&&el()!==u?((o=Ms())===u&&(o=null),o!==u&&el()!==u?((a=qi())===u&&(a=Qi()),a===u&&(a=null),a!==u&&el()!==u?(s=Ku,"nulls"===r.substr(Ku,5).toLowerCase()?(i=r.substr(Ku,5),Ku+=5):(i=u,0===ta&&aa(b)),i!==u&&(c=el())!==u?("first"===r.substr(Ku,5).toLowerCase()?(l=r.substr(Ku,5),Ku+=5):(l=u,0===ta&&aa(v)),l===u&&("last"===r.substr(Ku,4).toLowerCase()?(l=r.substr(Ku,4),Ku+=4):(l=u,0===ta&&aa(y))),l!==u?s=i=[i,c,l]:(Ku=s,s=u)):(Ku=s,s=u),s===u&&(s=null),s!==u?(Ju=t,f=e,p=n,d=o,h=a,E=s,t=e={...f,collate:p,opclass:d,order_by:h&&h.toLowerCase(),nulls:E&&`${E[0].toLowerCase()} ${E[2].toLowerCase()}`}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function ha(){var t;return(t=ma())===u&&(t=Ta())===u&&(t=Sa())===u&&(t=function(){var t;(t=function(){var t,e,n,o,a,s;t=Ku,(e=Ra())===u&&(e=null);e!==u&&el()!==u?("primary key"===r.substr(Ku,11).toLowerCase()?(n=r.substr(Ku,11),Ku+=11):(n=u,0===ta&&aa(U)),n!==u&&el()!==u?((o=Ya())===u&&(o=null),o!==u&&el()!==u&&(a=Ua())!==u&&el()!==u?((s=$a())===u&&(s=null),s!==u?(Ju=t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var r,t,e,n,o,a,s,i;r=Ku,(t=Ra())===u&&(t=null);t!==u&&el()!==u&&(e=Gc())!==u&&el()!==u?((n=Dc())===u&&(n=Pc()),n===u&&(n=null),n!==u&&el()!==u?((o=Ys())===u&&(o=null),o!==u&&el()!==u?((a=Ya())===u&&(a=null),a!==u&&el()!==u&&(s=Ua())!==u&&el()!==u?((i=$a())===u&&(i=null),i!==u?(Ju=r,l=e,f=n,p=o,b=a,v=s,y=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);var c,l,f,p,b,v,y;return r}())===u&&(t=function(){var t,e,n,o,a,s;t=Ku,(e=Ra())===u&&(e=null);e!==u&&el()!==u?("foreign key"===r.substr(Ku,11).toLowerCase()?(n=r.substr(Ku,11),Ku+=11):(n=u,0===ta&&aa(M)),n!==u&&el()!==u?((o=Ys())===u&&(o=null),o!==u&&el()!==u&&(a=Ua())!==u&&el()!==u?((s=Ia())===u&&(s=null),s!==u?(Ju=t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u);var i,c,l,f,p;return t}());return t}()),t}function Ea(){var t,e,n,o;return t=Ku,(e=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;t=Ku,"not null"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Xe));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={type:"not null",value:"not null"});return t=e}())===u&&(e=fi()),e!==u&&(Ju=t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===u&&(t=Ku,(e=function(){var r,t;r=Ku,Ai()!==u&&el()!==u&&(t=ds())!==u?(Ju=r,r={type:"default",value:t}):(Ku=r,r=u);return r}())!==u&&(Ju=t,e={default_val:e}),(t=e)===u&&(t=Ku,"auto_increment"===r.substr(Ku,14).toLowerCase()?(e=r.substr(Ku,14),Ku+=14):(e=u,0===ta&&aa(d)),e!==u&&(Ju=t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=Ku,"unique"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(h)),e!==u&&el()!==u?("key"===r.substr(Ku,3).toLowerCase()?(n=r.substr(Ku,3),Ku+=3):(n=u,0===ta&&aa(E)),n===u&&(n=null),n!==u?(Ju=t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,"primary"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(m)),e===u&&(e=null),e!==u&&el()!==u?("key"===r.substr(Ku,3).toLowerCase()?(n=r.substr(Ku,3),Ku+=3):(n=u,0===ta&&aa(E)),n!==u?(Ju=t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,(e=ul())!==u&&(Ju=t,e={comment:e}),(t=e)===u&&(t=Ku,(e=Ca())!==u&&(Ju=t,e={collate:e}),(t=e)===u&&(t=Ku,(e=function(){var t,e,n;t=Ku,"column_format"===r.substr(Ku,13).toLowerCase()?(e=r.substr(Ku,13),Ku+=13):(e=u,0===ta&&aa(C));e!==u&&el()!==u?("fixed"===r.substr(Ku,5).toLowerCase()?(n=r.substr(Ku,5),Ku+=5):(n=u,0===ta&&aa(L)),n===u&&("dynamic"===r.substr(Ku,7).toLowerCase()?(n=r.substr(Ku,7),Ku+=7):(n=u,0===ta&&aa(w)),n===u&&("default"===r.substr(Ku,7).toLowerCase()?(n=r.substr(Ku,7),Ku+=7):(n=u,0===ta&&aa(A)))),n!==u?(Ju=t,e={type:"column_format",value:n.toLowerCase()},t=e):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={column_format:e}),(t=e)===u&&(t=Ku,(e=function(){var t,e,n;t=Ku,"storage"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(T));e!==u&&el()!==u?("disk"===r.substr(Ku,4).toLowerCase()?(n=r.substr(Ku,4),Ku+=4):(n=u,0===ta&&aa(S)),n===u&&("memory"===r.substr(Ku,6).toLowerCase()?(n=r.substr(Ku,6),Ku+=6):(n=u,0===ta&&aa(R))),n!==u?(Ju=t,e={type:"storage",value:n.toLowerCase()},t=e):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={storage:e}),(t=e)===u&&(t=Ku,(e=Ia())!==u&&(Ju=t,e={reference_definition:e}),t=e))))))))),t}function ma(){var r,t,e,n,o,a,s;return r=Ku,(t=ks())!==u&&el()!==u&&(e=Cl())!==u&&el()!==u?((n=function(){var r,t,e,n,o,a;if(r=Ku,(t=Ea())!==u)if(el()!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Ea())!==u?n=o=[o,a]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Ea())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;else Ku=r,r=u;return r}())===u&&(n=null),n!==u?(Ju=r,o=t,a=e,s=n,kl.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:a,resource:"column",...s||{}}):(Ku=r,r=u)):(Ku=r,r=u),r}function Ca(){var t,e,n;return t=Ku,function(){var t,e,n,o;t=Ku,"collate"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(fr));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="COLLATE"):(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u?((e=kc())===u&&(e=null),e!==u&&el()!==u&&(n=Ms())!==u?(Ju=t,t={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Ku=t,t=u)):(Ku=t,t=u),t}function La(){var r;return(r=function(){var r,t,e,n;r=Ku,(t=Uc())!==u&&el()!==u?((e=Mc())===u&&(e=null),e!==u&&el()!==u&&(n=ma())!==u?(Ju=r,o=e,a=n,t={action:"add",...a,keyword:o,resource:"column",type:"alter"},r=t):(Ku=r,r=u)):(Ku=r,r=u);var o,a;return r}())===u&&(r=function(){var r,t,e;r=Ku,Si()!==u&&el()!==u?((t=Mc())===u&&(t=null),t!==u&&el()!==u&&(e=ks())!==u?(Ju=r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(Ku=r,r=u)):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Ku,(t=Uc())!==u&&el()!==u&&(e=Ta())!==u?(Ju=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ku=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e;r=Ku,(t=Uc())!==u&&el()!==u&&(e=Sa())!==u?(Ju=r,n=e,t={action:"add",type:"alter",...n},r=t):(Ku=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e,n;r=Ku,(t=Oi())!==u&&el()!==u?((e=Ti())===u&&(e=Pi()),e===u&&(e=null),e!==u&&el()!==u&&(n=Ms())!==u?(Ju=r,a=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:a},r=t):(Ku=r,r=u)):(Ku=r,r=u);var o,a;return r}())===u&&(r=wa())===u&&(r=Aa()),r}function wa(){var t,e,n,o;return t=Ku,"algorithm"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(I)),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u?("default"===r.substr(Ku,7).toLowerCase()?(o=r.substr(Ku,7),Ku+=7):(o=u,0===ta&&aa(A)),o===u&&("instant"===r.substr(Ku,7).toLowerCase()?(o=r.substr(Ku,7),Ku+=7):(o=u,0===ta&&aa(N)),o===u&&("inplace"===r.substr(Ku,7).toLowerCase()?(o=r.substr(Ku,7),Ku+=7):(o=u,0===ta&&aa(g)),o===u&&("copy"===r.substr(Ku,4).toLowerCase()?(o=r.substr(Ku,4),Ku+=4):(o=u,0===ta&&aa(_))))),o!==u?(Ju=t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function Aa(){var t,e,n,o;return t=Ku,"lock"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(j)),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u?("default"===r.substr(Ku,7).toLowerCase()?(o=r.substr(Ku,7),Ku+=7):(o=u,0===ta&&aa(A)),o===u&&("none"===r.substr(Ku,4).toLowerCase()?(o=r.substr(Ku,4),Ku+=4):(o=u,0===ta&&aa(O)),o===u&&("shared"===r.substr(Ku,6).toLowerCase()?(o=r.substr(Ku,6),Ku+=6):(o=u,0===ta&&aa(x)),o===u&&("exclusive"===r.substr(Ku,9).toLowerCase()?(o=r.substr(Ku,9),Ku+=9):(o=u,0===ta&&aa(k))))),o!==u?(Ju=t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function Ta(){var r,t,e,n,o,a,s,i;return r=Ku,(t=Dc())===u&&(t=Pc()),t!==u&&el()!==u?((e=Ys())===u&&(e=null),e!==u&&el()!==u?((n=Ya())===u&&(n=null),n!==u&&el()!==u&&(o=Ua())!==u&&el()!==u?((a=$a())===u&&(a=null),a!==u&&el()!==u?(Ju=r,s=n,i=a,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i}):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u),r}function Sa(){var t,e,n,o,a,s,i,c,l;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"fulltext"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(du));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="FULLTEXT"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Ku,"spatial"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(hu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SPATIAL"):(Ku=t,t=u)):(Ku=t,t=u);return t}()),e!==u&&el()!==u?((n=Dc())===u&&(n=Pc()),n===u&&(n=null),n!==u&&el()!==u?((o=Ys())===u&&(o=null),o!==u&&el()!==u&&(a=Ua())!==u&&el()!==u?((s=$a())===u&&(s=null),s!==u&&el()!==u?(Ju=t,i=e,l=s,t=e={index:o,definition:a,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function Ra(){var r,t,e,n;return r=Ku,(t=Hc())!==u&&el()!==u?((e=Ms())===u&&(e=null),e!==u?(Ju=r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(Ku=r,r=u)):(Ku=r,r=u),r}function Ia(){var t,e,n,o,a,s,i,c,l,f;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"references"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(Lu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="REFERENCES"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&(n=Va())!==u&&el()!==u&&(o=Ua())!==u&&el()!==u?("match full"===r.substr(Ku,10).toLowerCase()?(a=r.substr(Ku,10),Ku+=10):(a=u,0===ta&&aa(D)),a===u&&("match partial"===r.substr(Ku,13).toLowerCase()?(a=r.substr(Ku,13),Ku+=13):(a=u,0===ta&&aa(P)),a===u&&("match simple"===r.substr(Ku,12).toLowerCase()?(a=r.substr(Ku,12),Ku+=12):(a=u,0===ta&&aa(G)))),a===u&&(a=null),a!==u&&el()!==u?((s=Na())===u&&(s=null),s!==u&&el()!==u?((i=Na())===u&&(i=null),i!==u?(Ju=t,c=a,l=s,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function Na(){var t,e,n,o;return t=Ku,Fi()!==u&&el()!==u?((e=gi())===u&&(e=Ri()),e!==u&&el()!==u&&(n=function(){var t,e,n;t=Ku,(e=jc())!==u&&el()!==u&&Wc()!==u&&el()!==u?((n=fs())===u&&(n=null),n!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Ku=t,t=u)):(Ku=t,t=u);t===u&&(t=Ku,"restrict"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(F)),e===u&&("cascade"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(H)),e===u&&("set null"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(B)),e===u&&("no action"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(Y)),e===u&&("set default"===r.substr(Ku,11).toLowerCase()?(e=r.substr(Ku,11),Ku+=11):(e=u,0===ta&&aa($)),e===u&&(e=jc()))))),e!==u&&(Ju=t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(Ju=t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Ku=t,t=u)):(Ku=t,t=u),t}function ga(){var t,e,n,o,a,s,i;return t=Ku,(e=_i())===u&&(e=gi())===u&&(e=Nc()),e!==u&&(Ju=t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===u&&(t=Ku,(e=Ri())!==u&&el()!==u?(n=Ku,"of"===r.substr(Ku,2).toLowerCase()?(o=r.substr(Ku,2),Ku+=2):(o=u,0===ta&&aa(z)),o!==u&&(a=el())!==u&&(s=rs())!==u?n=o=[o,a,s]:(Ku=n,n=u),n===u&&(n=null),n!==u?(Ju=t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):(Ku=t,t=u)):(Ku=t,t=u)),t}function _a(){var t,e,n,o,a,s,i,c,l;return t=Ku,(e=Ai())===u&&(e=null),e!==u&&el()!==u?((n=function(){var t,e,n;return t=Ku,"character"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(ir)),e!==u&&el()!==u?("set"===r.substr(Ku,3).toLowerCase()?(n=r.substr(Ku,3),Ku+=3):(n=u,0===ta&&aa(cr)),n!==u?(Ju=t,t=e="CHARACTER SET"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&("charset"===r.substr(Ku,7).toLowerCase()?(n=r.substr(Ku,7),Ku+=7):(n=u,0===ta&&aa(lr)),n===u&&("collate"===r.substr(Ku,7).toLowerCase()?(n=r.substr(Ku,7),Ku+=7):(n=u,0===ta&&aa(fr)))),n!==u&&el()!==u?((o=kc())===u&&(o=null),o!==u&&el()!==u&&(a=Us())!==u?(Ju=t,i=n,c=o,l=a,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function ja(){var t,e,n,o,a,s,i,c,l;return t=Ku,"auto_increment"===r.substr(Ku,14).toLowerCase()?(e=r.substr(Ku,14),Ku+=14):(e=u,0===ta&&aa(d)),e===u&&("avg_row_length"===r.substr(Ku,14).toLowerCase()?(e=r.substr(Ku,14),Ku+=14):(e=u,0===ta&&aa(pr)),e===u&&("key_block_size"===r.substr(Ku,14).toLowerCase()?(e=r.substr(Ku,14),Ku+=14):(e=u,0===ta&&aa(br)),e===u&&("max_rows"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(vr)),e===u&&("min_rows"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(yr)),e===u&&("stats_sample_pages"===r.substr(Ku,18).toLowerCase()?(e=r.substr(Ku,18),Ku+=18):(e=u,0===ta&&aa(dr))))))),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u&&(o=di())!==u?(Ju=t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=_a())===u&&(t=Ku,(e=Fc())===u&&("connection"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(hr))),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u&&(o=pi())!==u?(Ju=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,"compression"===r.substr(Ku,11).toLowerCase()?(e=r.substr(Ku,11),Ku+=11):(e=u,0===ta&&aa(Er)),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u?(o=Ku,39===r.charCodeAt(Ku)?(a="'",Ku++):(a=u,0===ta&&aa(mr)),a!==u?("zlib"===r.substr(Ku,4).toLowerCase()?(s=r.substr(Ku,4),Ku+=4):(s=u,0===ta&&aa(Cr)),s===u&&("lz4"===r.substr(Ku,3).toLowerCase()?(s=r.substr(Ku,3),Ku+=3):(s=u,0===ta&&aa(Lr)),s===u&&("none"===r.substr(Ku,4).toLowerCase()?(s=r.substr(Ku,4),Ku+=4):(s=u,0===ta&&aa(O)))),s!==u?(39===r.charCodeAt(Ku)?(i="'",Ku++):(i=u,0===ta&&aa(mr)),i!==u?o=a=[a,s,i]:(Ku=o,o=u)):(Ku=o,o=u)):(Ku=o,o=u),o!==u?(Ju=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,"engine"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(wr)),e!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u&&(o=Ws())!==u?(Ju=t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(Ku=t,t=u)):(Ku=t,t=u)))),t}function Oa(){var t,e,n,o,a,s,i;return(t=Ma())===u&&(t=Ku,e=Ku,40===r.charCodeAt(Ku)?(n="(",Ku++):(n=u,0===ta&&aa(xr)),n!==u&&(o=el())!==u&&(a=Oa())!==u&&(s=el())!==u?(41===r.charCodeAt(Ku)?(i=")",Ku++):(i=u,0===ta&&aa(kr)),i!==u?e=n=[n,o,a,s,i]:(Ku=e,e=u)):(Ku=e,e=u),e!==u&&(Ju=t,e={...e[2],parentheses_symbol:!0}),t=e),t}function xa(){var t,e,n,o,a,s,i,c,l;if(t=Ku,Vi()!==u)if(el()!==u)if((e=ka())!==u){for(n=[],o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=ka())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);o!==u;)n.push(o),o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=ka())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);n!==u?(Ju=t,t=Il(e,n)):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;return t===u&&(t=Ku,el()!==u&&Vi()!==u&&(e=el())!==u&&(n=function(){var t,e,n,o;t=Ku,"recursive"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(an));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(o=el())!==u&&(a=ka())!==u?(Ju=t,(l=a).recursive=!0,t=[l]):(Ku=t,t=u)),t}function ka(){var r,t,e,n,o;return r=Ku,(t=pi())===u&&(t=Ws()),t!==u&&el()!==u?((e=Ua())===u&&(e=null),e!==u&&el()!==u&&Pi()!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=pa())!==u&&el()!==u&&Vc()!==u?(Ju=r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e}):(Ku=r,r=u)):(Ku=r,r=u),r}function Ua(){var r,t;return r=Ku,Wc()!==u&&el()!==u&&(t=rs())!==u&&el()!==u&&Vc()!==u?(Ju=r,r=t):(Ku=r,r=u),r}function Ma(){var t,e,n,o,a,s,i,c,l,f,p,b,v,y,d,h,E,m,C,L,w;return t=Ku,el()!==u?((e=xa())===u&&(e=null),e!==u&&el()!==u&&function(){var t,e,n,o;t=Ku,"select"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(rn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&nl()!==u?((n=function(){var r,t,e,n,o,a;if(r=Ku,(t=Da())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Da())!==u?n=o=[o,a]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Da())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u?(Ju=r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())===u&&(n=null),n!==u&&el()!==u?((o=Ji())===u&&(o=null),o!==u&&el()!==u&&(a=Pa())!==u&&el()!==u?((s=Ha())===u&&(s=null),s!==u&&el()!==u?((i=za())===u&&(i=null),i!==u&&el()!==u?((c=function(){var t,e,n;t=Ku,(e=function(){var t,e,n,o;t=Ku,"group"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(kn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&Xi()!==u&&el()!==u&&(n=fs())!==u?(Ju=t,e={columns:n.value},t=e):(Ku=t,t=u);return t}())===u&&(c=null),c!==u&&el()!==u?((l=function(){var t,e;t=Ku,function(){var t,e,n,o;t=Ku,"having"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Dn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u&&(e=Es())!==u?(Ju=t,t=e):(Ku=t,t=u);return t}())===u&&(l=null),l!==u&&el()!==u?((f=ts())===u&&(f=null),f!==u&&el()!==u?((p=os())===u&&(p=null),p!==u?(Ju=t,b=e,v=n,y=o,d=a,E=i,m=c,C=l,L=f,w=p,(h=s)&&h.forEach(r=>r.table&&xl.add(`select::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`)),t={with:b,type:"select",options:v,distinct:y,columns:d,from:h,where:E,groupby:m,having:C,orderby:L,limit:w}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function Da(){var t,e;return t=Ku,(e=function(){var t;"sql_calc_found_rows"===r.substr(Ku,19).toLowerCase()?(t=r.substr(Ku,19),Ku+=19):(t=u,0===ta&&aa(wu));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr(Ku,9).toLowerCase()?(t=r.substr(Ku,9),Ku+=9):(t=u,0===ta&&aa(Au));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr(Ku,12).toLowerCase()?(t=r.substr(Ku,12),Ku+=12):(t=u,0===ta&&aa(Tu));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr(Ku,14).toLowerCase()?(t=r.substr(Ku,14),Ku+=14):(t=u,0===ta&&aa(Ru));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr(Ku,16).toLowerCase()?(t=r.substr(Ku,16),Ku+=16):(t=u,0===ta&&aa(Su));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr(Ku,17).toLowerCase()?(t=r.substr(Ku,17),Ku+=17):(t=u,0===ta&&aa(Iu));return t}())),e!==u&&(Ju=t,e=e),t=e}function Pa(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Ki())===u&&(t=Ku,(e=$c())!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=$c())),t!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){kl.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?Il(e,t):[e]}(0,e)):(Ku=r,r=u)}else Ku=r,r=u;if(r===u)if(r=Ku,(t=Ga())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Ga())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Il(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Ga(){var r,t,e,n,o;return r=Ku,(t=hs())!==u&&(e=xc())!==u&&(n=Cl())!==u?(Ju=r,r=t={type:"cast",expr:t,symbol:"::",target:[n]}):(Ku=r,r=u),r===u&&(r=Ku,t=Ku,(e=Ms())!==u&&(n=el())!==u&&(o=Bc())!==u?t=e=[e,n,o]:(Ku=t,t=u),t===u&&(t=null),t!==u&&(e=el())!==u&&(n=$c())!==u?(Ju=r,r=t=function(r){const t=r&&r[0]||null;return kl.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(Ku=r,r=u),r===u&&(r=Ku,(t=hs())!==u&&(e=el())!==u?((n=Fa())===u&&(n=null),n!==u?(Ju=r,r=t=function(r,t){return{type:"expr",expr:r,as:t}}(t,n)):(Ku=r,r=u)):(Ku=r,r=u))),r}function Fa(){var r,t,e;return r=Ku,(t=Pi())!==u&&el()!==u&&(e=function(){var r,t;r=Ku,(t=Ws())!==u?(Ju=Ku,(function(r){if(!0===wl[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?u:void 0)!==u?(Ju=r,r=t=t):(Ku=r,r=u)):(Ku=r,r=u);r===u&&(r=Ku,(t=Ps())!==u&&(Ju=r,t=t),r=t);return r}())!==u?(Ju=r,r=t=e):(Ku=r,r=u),r===u&&(r=Ku,(t=Pi())===u&&(t=null),t!==u&&el()!==u&&(e=Ms())!==u?(Ju=r,r=t=e):(Ku=r,r=u)),r}function Ha(){var r,t;return r=Ku,Mi()!==u&&el()!==u&&(t=Va())!==u?(Ju=r,r=t):(Ku=r,r=u),r}function Ba(){var r,t,e;return r=Ku,(t=Ka())!==u&&el()!==u&&Ti()!==u&&el()!==u&&(e=Ka())!==u?(Ju=r,r=t=[t,e]):(Ku=r,r=u),r}function Ya(){var t,e;return t=Ku,Wi()!==u&&el()!==u?("btree"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Ur)),e===u&&("hash"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Mr)),e===u&&("gist"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Dr)),e===u&&("gin"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Pr))))),e!==u?(Ju=t,t={keyword:"using",type:e.toLowerCase()}):(Ku=t,t=u)):(Ku=t,t=u),t}function $a(){var r,t,e,n,o,a;if(r=Ku,(t=Wa())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Wa())!==u?n=o=[o,a]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Wa())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Wa(){var t,e,n,o,a,s;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"key_block_size"===r.substr(Ku,14).toLowerCase()?(e=r.substr(Ku,14),Ku+=14):(e=u,0===ta&&aa(br));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="KEY_BLOCK_SIZE"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u?((n=kc())===u&&(n=null),n!==u&&el()!==u&&(o=di())!==u?(Ju=t,a=n,s=o,t=e={type:e.toLowerCase(),symbol:a,expr:s}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,(e=Ws())!==u&&el()!==u&&(n=kc())!==u&&el()!==u?((o=di())===u&&(o=Ms()),o!==u?(Ju=t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ya())===u&&(t=Ku,"with"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Gr)),e!==u&&el()!==u?("parser"===r.substr(Ku,6).toLowerCase()?(n=r.substr(Ku,6),Ku+=6):(n=u,0===ta&&aa(Fr)),n!==u&&el()!==u&&(o=Ws())!==u?(Ju=t,t=e={type:"with parser",expr:o}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,"visible"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Hr)),e===u&&("invisible"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(Br))),e!==u&&(Ju=t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=ul())))),t}function Va(){var r,t,e,n;if(r=Ku,(t=qa())!==u){for(e=[],n=Xa();n!==u;)e.push(n),n=Xa();e!==u?(Ju=r,r=t=Yr(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Xa(){var r,t,e;return r=Ku,el()!==u&&(t=Yc())!==u&&el()!==u&&(e=qa())!==u?(Ju=r,r=e):(Ku=r,r=u),r===u&&(r=Ku,el()!==u&&(t=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=Ku,(t=Qa())!==u)if(el()!==u)if((e=qa())!==u)if(el()!==u)if((n=Wi())!==u)if(el()!==u)if(Wc()!==u)if(el()!==u)if((o=Us())!==u){for(a=[],s=Ku,(i=el())!==u&&(c=Yc())!==u&&(l=el())!==u&&(f=Us())!==u?s=i=[i,c,l,f]:(Ku=s,s=u);s!==u;)a.push(s),s=Ku,(i=el())!==u&&(c=Yc())!==u&&(l=el())!==u&&(f=Us())!==u?s=i=[i,c,l,f]:(Ku=s,s=u);a!==u&&(s=el())!==u&&(i=Vc())!==u?(Ju=r,p=t,v=o,y=a,(b=e).join=p,b.using=Il(v,y),r=t=b):(Ku=r,r=u)}else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;var p,b,v,y;r===u&&(r=Ku,(t=Qa())!==u&&el()!==u&&(e=qa())!==u&&el()!==u?((n=Za())===u&&(n=null),n!==u?(Ju=r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=Qa())===u&&(t=fa()),t!==u&&el()!==u&&(e=Wc())!==u&&el()!==u&&(n=pa())!==u&&el()!==u&&Vc()!==u&&el()!==u?((o=Fa())===u&&(o=null),o!==u&&(a=el())!==u?((s=Za())===u&&(s=null),s!==u?(Ju=r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u)));return r}())!==u?(Ju=r,r=t):(Ku=r,r=u)),r}function qa(){var t,e,n,o,a,s,i,c,l,f,p,b,v,y,d,h,E,m,C,L,w,A,T,S,R,I,N;return t=Ku,(e=function(){var t;"dual"===r.substr(Ku,4).toLowerCase()?(t=r.substr(Ku,4),Ku+=4):(t=u,0===ta&&aa(pu));return t}())!==u&&(Ju=t,e={type:"dual"}),(t=e)===u&&(t=Ku,(e=Ka())!==u&&el()!==u?((n=Fa())===u&&(n=null),n!==u?(Ju=t,N=n,t=e="var"===(I=e).type?(I.as=N,I):{db:I.db,table:I.table,as:N}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,(e=Wc())!==u&&el()!==u&&(n=pa())!==u&&el()!==u&&Vc()!==u&&el()!==u?((o=Fa())===u&&(o=null),o!==u?(Ju=t,t=e=function(r,t){return r.parentheses=!0,{expr:r,as:t}}(n,o)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,(e=Gi())!==u&&el()!==u&&(n=Wc())!==u&&el()!==u&&function(){var t,e,n,o;t=Ku,"tumble"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(ao));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TUMBLE"):(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u&&(o=Wc())!==u&&el()!==u?(a=Ku,"data"===r.substr(Ku,4).toLowerCase()?(s=r.substr(Ku,4),Ku+=4):(s=u,0===ta&&aa($r)),s!==u&&(i=el())!==u&&(c=rl())!==u?a=s=[s,i,c]:(Ku=a,a=u),a===u&&(a=null),a!==u&&(s=el())!==u&&(i=Gi())!==u&&(c=el())!==u&&(l=Ka())!==u&&el()!==u&&Yc()!==u&&el()!==u?(f=Ku,"timecol"===r.substr(Ku,7).toLowerCase()?(p=r.substr(Ku,7),Ku+=7):(p=u,0===ta&&aa(Wr)),p!==u&&(b=el())!==u&&(v=rl())!==u?f=p=[p,b,v]:(Ku=f,f=u),f===u&&(f=null),f!==u&&(p=el())!==u?("descriptor"===r.substr(Ku,10).toLowerCase()?(b=r.substr(Ku,10),Ku+=10):(b=u,0===ta&&aa(Vr)),b!==u&&(v=el())!==u&&Wc()!==u&&el()!==u&&(y=ks())!==u&&el()!==u&&Vc()!==u&&el()!==u&&Yc()!==u&&el()!==u?(d=Ku,"size"===r.substr(Ku,4).toLowerCase()?(h=r.substr(Ku,4),Ku+=4):(h=u,0===ta&&aa(Xr)),h!==u&&(E=el())!==u&&(m=rl())!==u?d=h=[h,E,m]:(Ku=d,d=u),d===u&&(d=null),d!==u&&(h=el())!==u&&(E=ps())!==u?(m=Ku,(C=el())!==u&&(L=Yc())!==u&&(w=el())!==u?(A=Ku,"offset"===r.substr(Ku,6).toLowerCase()?(T=r.substr(Ku,6),Ku+=6):(T=u,0===ta&&aa(qr)),T!==u&&(S=el())!==u&&(R=rl())!==u?A=T=[T,S,R]:(Ku=A,A=u),A===u&&(A=null),A!==u&&(T=el())!==u&&(S=ps())!==u?m=C=[C,L,w,A,T,S]:(Ku=m,m=u)):(Ku=m,m=u),m===u&&(m=null),m!==u&&(C=el())!==u&&(L=Vc())!==u&&(w=el())!==u&&(A=Vc())!==u&&(T=el())!==u?((S=Fa())===u&&(S=null),S!==u?(Ju=t,t=e=function(r,t,e,n,o,u,a,s){const i={expr:{type:"tumble",data:{name:r&&r[0],symbol:r&&r[2],expr:t},timecol:{name:e&&e[0],symbol:e&&e[2],expr:n},size:{name:o&&o[0],symbol:o&&o[2],expr:u}},as:s};return a&&(i.expr.offset={name:a[3]&&a[3][0],symbol:a[3]&&a[3][2],expr:a[5]}),i}(a,l,f,y,d,E,m,S)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)))),t}function Qa(){var t,e,n,o,a,s;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"natural"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(mn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="NATURAL"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(e=null),e!==u&&(n=el())!==u?((o=function(){var t,e,n,o;t=Ku,"left"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Cn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="LEFT"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=Ku,"right"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Ln));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="RIGHT"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=Ku,"full"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(wn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="FULL"):(Ku=t,t=u)):(Ku=t,t=u);return t}()),o===u&&(o=null),o!==u&&el()!==u?((a=Yi())===u&&(a=null),a!==u&&el()!==u&&Hi()!==u?(Ju=t,t=e=`${e?"NATURAL ":""}${(s=o)?s+" ":""}${a?"OUTER ":""}JOIN`):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,e=Ku,(n=function(){var t,e,n,o;t=Ku,"inner"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(An));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INNER"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(o=el())!==u?e=n=[n,o]:(Ku=e,e=u),e===u&&(e=null),e!==u&&(n=Hi())!==u?(Ju=t,t=e=e?"INNER JOIN":"JOIN"):(Ku=t,t=u),t===u&&(t=Ku,(e=Bi())!==u&&(n=el())!==u&&(o=Hi())!==u?(Ju=t,t=e="CROSS JOIN"):(Ku=t,t=u),t===u&&(t=Ku,(e=Bi())===u&&(e=Yi()),e!==u&&(n=el())!==u&&(o=function(){var t,e,n,o;t=Ku,"apply"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Rn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u?(Ju=t,t=e=e[0].toUpperCase()+" APPLY"):(Ku=t,t=u)))),t}function Ka(){var r,t,e,n,o,a,s,i,c;return r=Ku,(t=Ms())!==u?(e=Ku,(n=el())!==u&&(o=Bc())!==u&&(a=el())!==u&&(s=Ms())!==u?e=n=[n,o,a,s]:(Ku=e,e=u),e!==u?(n=Ku,(o=el())!==u&&(a=Bc())!==u&&(s=el())!==u&&(i=Ms())!==u?n=o=[o,a,s,i]:(Ku=n,n=u),n!==u?(Ju=r,r=t=function(r,t,e){const n={db:null,table:r};return null!==e&&(n.db=`${r}.${t[3]}`,n.table=e[3]),n}(t,e,n)):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=Ms())!==u&&(e=el())!==u&&(n=Bc())!==u&&(o=el())!==u&&(a=$c())!==u?(Ju=r,r=t={db:t,table:"*"}):(Ku=r,r=u),r===u&&(r=Ku,(t=Ms())!==u?(e=Ku,(n=el())!==u&&(o=Bc())!==u&&(a=el())!==u&&(s=Ms())!==u?e=n=[n,o,a,s]:(Ku=e,e=u),e===u&&(e=null),e!==u?(Ju=r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=El())!==u&&(Ju=r,(c=t).db=null,c.table=c.name,t=c),r=t))),r}function Ja(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ds())!==u){for(e=[],n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=Sl(t[r][1],n,t[r][3]);return n}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Za(){var r,t;return r=Ku,Fi()!==u&&el()!==u&&(t=Es())!==u?(Ju=r,r=t):(Ku=r,r=u),r}function za(){var t,e;return t=Ku,function(){var t,e,n,o;t=Ku,"where"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(xn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u?((e=Es())===u&&(e=ds()),e!==u?(Ju=t,t=e):(Ku=t,t=u)):(Ku=t,t=u),t}function rs(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ks())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ks())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Il(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function ts(){var t,e;return t=Ku,function(){var t,e,n,o;t=Ku,"order"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Mn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u&&Xi()!==u&&el()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=es())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=es())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=es())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=t,t=e):(Ku=t,t=u),t}function es(){var r,t,e;return r=Ku,(t=ds())!==u&&el()!==u?((e=Qi())===u&&(e=qi()),e===u&&(e=null),e!==u?(Ju=r,r=t={expr:t,type:e}):(Ku=r,r=u)):(Ku=r,r=u),r}function ns(){var r;return(r=di())===u&&(r=Qs()),r}function os(){var t,e,n,o,a,s;return t=Ku,function(){var t,e,n,o;t=Ku,"limit"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Pn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u?((e=ns())===u&&(e=Ki()),e!==u&&el()!==u?(n=Ku,(o=function(){var t,e,n,o;t=Ku,"offset"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(qr));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="OFFSET"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(a=el())!==u&&(s=ns())!==u?n=o=[o,a,s]:(Ku=n,n=u),n===u&&(n=null),n!==u?(Ju=t,t=function(r,t){const e=[];return"string"==typeof r?e.push({type:"origin",value:"all"}):e.push(r),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,n)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function us(){var t,e,n,o,a,s,i,c,l;return t=Ku,e=Ku,(n=Ms())!==u&&(o=el())!==u&&(a=Bc())!==u?e=n=[n,o,a]:(Ku=e,e=u),e===u&&(e=null),e!==u&&(n=el())!==u&&(o=Bs())!==u&&(a=el())!==u?(61===r.charCodeAt(Ku)?(s="=",Ku++):(s=u,0===ta&&aa(Qr)),s!==u&&el()!==u&&(i=Rs())!==u?(Ju=t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,e=Ku,(n=Ms())!==u&&(o=el())!==u&&(a=Bc())!==u?e=n=[n,o,a]:(Ku=e,e=u),e===u&&(e=null),e!==u&&(n=el())!==u&&(o=Bs())!==u&&(a=el())!==u?(61===r.charCodeAt(Ku)?(s="=",Ku++):(s=u,0===ta&&aa(Qr)),s!==u&&el()!==u&&(i=$i())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(c=ks())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Ku=t,t=u)):(Ku=t,t=u)),t}function as(){var t,e,n,o,a;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"returning"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(cn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="RETURNING"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u?((n=Pa())===u&&(n=Oa()),n!==u?(Ju=t,a=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===a&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||a}):(Ku=t,t=u)):(Ku=t,t=u),t}function ss(){var r;return(r=function(){var r,t;r=Ku,$i()!==u&&el()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ls())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ls())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ls())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Il(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())!==u?(Ju=r,r=t):(Ku=r,r=u);return r}())===u&&(r=Ma()),r}function is(){var r,t,e,n,o,a,s,i,c;if(r=Ku,ki()!==u)if(el()!==u)if((t=Wc())!==u)if(el()!==u)if((e=Ws())!==u){for(n=[],o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=Ws())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);o!==u;)n.push(o),o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=Ws())!==u?o=a=[a,s,i,c]:(Ku=o,o=u);n!==u&&(o=el())!==u&&(a=Vc())!==u?(Ju=r,r=Il(e,n)):(Ku=r,r=u)}else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;else Ku=r,r=u;return r===u&&(r=Ku,ki()!==u&&el()!==u&&(t=ls())!==u?(Ju=r,r=t):(Ku=r,r=u)),r}function cs(){var r,t;return r=Ku,(t=_i())!==u&&(Ju=r,t="insert"),(r=t)===u&&(r=Ku,(t=ji())!==u&&(Ju=r,t="replace"),r=t),r}function ls(){var r,t;return r=Ku,Wc()!==u&&el()!==u&&(t=fs())!==u&&el()!==u&&Vc()!==u?(Ju=r,r=t):(Ku=r,r=u),r}function fs(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ds())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){const e={type:"expr_list"};return e.value=Il(r,t),e}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function ps(){var t,e,n;return t=Ku,_c()!==u&&el()!==u&&(e=ds())!==u&&el()!==u&&(n=function(){var t,e;(t=function(){var t,e,n,o;t=Ku,"year"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(he));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="YEAR"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"month"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(le));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MONTH"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"day"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Jt));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DAY"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"hour"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(ne));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="HOUR"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"minute"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(ce));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MINUTE"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"second"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(pe));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SECOND"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=Ku,"years"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Hu)),e===u&&("months"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Bu)),e===u&&("days"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Yu)),e===u&&("hours"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa($u)),e===u&&("minutes"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Wu)),e===u&&("seconds"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Vu))))))),e!==u&&(Ju=t,e=e.toUpperCase()),t=e);return t}())!==u?(Ju=t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(Ku=t,t=u),t===u&&(t=Ku,_c()!==u&&el()!==u&&(e=pi())!==u?(Ju=t,t=function(r){return{type:"interval",expr:r,unit:""}}(e)):(Ku=t,t=u)),t}function bs(){var t,e,n,o,a,s,i,c;return t=Ku,ic()!==u&&el()!==u?((e=ds())===u&&(e=null),e!==u&&el()!==u&&(n=function(){var r,t,e,n,o,a;if(r=Ku,(t=vs())!==u)if(el()!==u){for(e=[],n=Ku,(o=el())!==u&&(a=vs())!==u?n=o=[o,a]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=vs())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u?(Ju=r,t=p(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;else Ku=r,r=u;return r}())!==u&&el()!==u?((o=function(){var t,e;t=Ku,function(){var t,e,n,o;t=Ku,"else"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(po));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u&&(e=ds())!==u?(Ju=t,t={type:"else",result:e}):(Ku=t,t=u);return t}())===u&&(o=null),o!==u&&el()!==u&&function(){var t,e,n,o;t=Ku,"end"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(bo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u?((a=ic())===u&&(a=null),a!==u?(Ju=t,s=e,i=n,(c=o)&&i.push(c),t={type:"case",expr:s||null,args:i}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function vs(){var t,e,n;return t=Ku,cc()!==u&&el()!==u&&(e=Es())!==u&&el()!==u&&function(){var t,e,n,o;t=Ku,"then"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(fo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}()!==u&&el()!==u&&(n=ds())!==u?(Ju=t,t={type:"when",cond:e,result:n}):(Ku=t,t=u),t}function ys(){var r;return(r=function(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ms())!==u){for(e=[],n=Ku,(o=nl())!==u&&(a=ac())!==u&&(s=el())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=nl())!==u&&(a=ac())!==u&&(s=el())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,t=Kr(t,e),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a;if(r=Ku,(t=Is())!==u){if(e=[],n=Ku,(o=el())!==u&&(a=_s())!==u?n=o=[o,a]:(Ku=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=_s())!==u?n=o=[o,a]:(Ku=n,n=u);else e=u;e!==u?(Ju=r,t=Tl(t,e[0][1]),r=t):(Ku=r,r=u)}else Ku=r,r=u;return r}()),r}function ds(){var r;return(r=ys())===u&&(r=pa()),r}function hs(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ds())!==u){for(e=[],n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac())===u&&(a=tl()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac())===u&&(a=tl()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=Sl(t[e][1],n,o)}return o}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Es(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ds())!==u){for(e=[],n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac())===u&&(a=Yc()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=uc())===u&&(a=ac())===u&&(a=Yc()),a!==u&&(s=el())!==u&&(i=ds())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=Sl(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function ms(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Cs())!==u){for(e=[],n=Ku,(o=nl())!==u&&(a=uc())!==u&&(s=el())!==u&&(i=Cs())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=nl())!==u&&(a=uc())!==u&&(s=el())!==u&&(i=Cs())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Kr(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Cs(){var t,e,n,o,a;return(t=Ls())===u&&(t=function(){var r,t,e;r=Ku,(t=ws())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(e=pa())!==u&&el()!==u&&Vc()!==u?(Ju=r,n=t,(o=e).parentheses=!0,t=Tl(n,o),r=t):(Ku=r,r=u);var n,o;return r}())===u&&(t=Ku,(e=oc())===u&&(e=Ku,33===r.charCodeAt(Ku)?(n="!",Ku++):(n=u,0===ta&&aa(Jr)),n!==u?(o=Ku,ta++,61===r.charCodeAt(Ku)?(a="=",Ku++):(a=u,0===ta&&aa(Qr)),ta--,a===u?o=void 0:(Ku=o,o=u),o!==u?e=n=[n,o]:(Ku=e,e=u)):(Ku=e,e=u)),e!==u&&(n=el())!==u&&(o=Cs())!==u?(Ju=t,t=e=Tl("NOT",o)):(Ku=t,t=u)),t}function Ls(){var r,t,e,n,o;return r=Ku,(t=Rs())!==u&&el()!==u?((e=function(){var r;(r=function(){var r,t,e,n,o,a,s;r=Ku,t=[],e=Ku,(n=el())!==u&&(o=As())!==u&&(a=el())!==u&&(s=Rs())!==u?e=n=[n,o,a,s]:(Ku=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=Ku,(n=el())!==u&&(o=As())!==u&&(a=el())!==u&&(s=Rs())!==u?e=n=[n,o,a,s]:(Ku=e,e=u);else t=u;t!==u&&(Ju=r,t={type:"arithmetic",tail:t});return r=t}())===u&&(r=function(){var r,t,e,n;r=Ku,(t=Ss())!==u&&el()!==u&&(e=Wc())!==u&&el()!==u&&(n=fs())!==u&&el()!==u&&Vc()!==u?(Ju=r,r=t={op:t,right:n}):(Ku=r,r=u);r===u&&(r=Ku,(t=Ss())!==u&&el()!==u?((e=El())===u&&(e=pi())===u&&(e=si()),e!==u?(Ju=r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(Ku=r,r=u)):(Ku=r,r=u));return r}())===u&&(r=function(){var r,t,e;r=Ku,(t=ws())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(e=fs())!==u&&el()!==u&&Vc()!==u?(Ju=r,r=t={op:t,right:e}):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e,n;r=Ku,(t=function(){var r,t,e,n,o;r=Ku,t=Ku,(e=oc())!==u&&(n=el())!==u&&(o=Zi())!==u?t=e=[e,n,o]:(Ku=t,t=u);t!==u&&(Ju=r,t=(a=t)[0]+" "+a[2]);var a;(r=t)===u&&(r=Zi());return r}())!==u&&el()!==u&&(e=Rs())!==u&&el()!==u&&uc()!==u&&el()!==u&&(n=Rs())!==u?(Ju=r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Ku,(t=function(){var r;r=Ku,rc()!==u&&el()!==u&&oc()!==u&&el()!==u&&Ji()!==u&&el()!==u&&Mi()!==u?(Ju=r,r="IS NOT DISTINCT FROM"):(Ku=r,r=u);r===u&&(r=Ku,rc()!==u&&el()!==u&&Ji()!==u&&el()!==u&&Mi()!==u?(Ju=r,r="IS DISTINCT FROM"):(Ku=r,r=u));return r}())!==u&&el()!==u&&(e=ds())!==u?(Ju=r,r=t={op:t,right:e}):(Ku=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i,c;r=Ku,(t=rc())!==u&&(e=el())!==u&&(n=Rs())!==u?(Ju=r,r=t={op:"IS",right:n}):(Ku=r,r=u);r===u&&(r=Ku,(t=rc())!==u&&(e=el())!==u?(n=Ku,(o=Ji())!==u&&(a=el())!==u&&(s=Mi())!==u&&(i=el())!==u&&(c=Ka())!==u?n=o=[o,a,s,i,c]:(Ku=n,n=u),n!==u?(Ju=r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"origin",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,t=Ku,(e=rc())!==u&&(n=el())!==u&&(o=oc())!==u?t=e=[e,n,o]:(Ku=t,t=u),t!==u&&(e=el())!==u&&(n=Rs())!==u?(Ju=r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Ku=r,r=u)));return r}())===u&&(r=function(){var r,t,e,n;r=Ku,(t=function(){var r,t,e,n,o;r=Ku,t=Ku,(e=oc())!==u&&(n=el())!==u&&(o=tc())!==u?t=e=[e,n,o]:(Ku=t,t=u);t!==u&&(Ju=r,t=(a=t)[0]+" "+a[2]);var a;(r=t)===u&&(r=tc());return r}())!==u&&el()!==u?((e=li())===u&&(e=Ls()),e!==u&&el()!==u?((n=Ts())===u&&(n=null),n!==u?(Ju=r,o=t,a=e,(s=n)&&(a.escape=s),r=t={op:o,right:a}):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);var o,a,s;return r}())===u&&(r=function(){var r,t,e,n;r=Ku,(t=function(){var r,t,e,n,o,a,s;r=Ku,t=Ku,(e=oc())!==u&&(n=el())!==u&&(o=ec())!==u&&(a=el())!==u&&(s=Ti())!==u?t=e=[e,n,o,a,s]:(Ku=t,t=u);t!==u&&(Ju=r,t="NOT SIMILAR TO");(r=t)===u&&(r=Ku,(t=ec())!==u&&(e=el())!==u&&(n=Ti())!==u?(Ju=r,r=t="SIMILAR TO"):(Ku=r,r=u));return r}())!==u&&el()!==u?((e=li())===u&&(e=Ls()),e!==u&&el()!==u?((n=Ts())===u&&(n=null),n!==u?(Ju=r,o=t,a=e,(s=n)&&(a.escape=s),r=t={op:o,right:a}):(Ku=r,r=u)):(Ku=r,r=u)):(Ku=r,r=u);var o,a,s;return r}());return r}())===u&&(e=null),e!==u?(Ju=r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?Nl(n,o.tail):Sl(o.op,n,o.right)):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=pi())===u&&(r=ks()),r}function ws(){var r,t,e,n,o,a;return r=Ku,t=Ku,(e=oc())!==u&&(n=el())!==u&&(o=nc())!==u?t=e=[e,n,o]:(Ku=t,t=u),t!==u&&(Ju=r,t=(a=t)[0]+" "+a[2]),(r=t)===u&&(r=nc()),r}function As(){var t;return">="===r.substr(Ku,2)?(t=">=",Ku+=2):(t=u,0===ta&&aa(Zr)),t===u&&(62===r.charCodeAt(Ku)?(t=">",Ku++):(t=u,0===ta&&aa(zr)),t===u&&("<="===r.substr(Ku,2)?(t="<=",Ku+=2):(t=u,0===ta&&aa(rt)),t===u&&("<>"===r.substr(Ku,2)?(t="<>",Ku+=2):(t=u,0===ta&&aa(tt)),t===u&&(60===r.charCodeAt(Ku)?(t="<",Ku++):(t=u,0===ta&&aa(et)),t===u&&(61===r.charCodeAt(Ku)?(t="=",Ku++):(t=u,0===ta&&aa(Qr)),t===u&&("!="===r.substr(Ku,2)?(t="!=",Ku+=2):(t=u,0===ta&&aa(nt)))))))),t}function Ts(){var t,e,n;return t=Ku,"escape"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(ot)),e!==u&&el()!==u&&(n=pi())!==u?(Ju=t,t=e={type:"ESCAPE",value:n}):(Ku=t,t=u),t}function Ss(){var r,t,e,n,o,a;return r=Ku,t=Ku,(e=oc())!==u&&(n=el())!==u&&(o=zi())!==u?t=e=[e,n,o]:(Ku=t,t=u),t!==u&&(Ju=r,t=(a=t)[0]+" "+a[2]),(r=t)===u&&(r=zi()),r}function Rs(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Ns())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Is())!==u&&(s=el())!==u&&(i=Ns())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Is())!==u&&(s=el())!==u&&(i=Ns())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=function(r,t){if(t&&t.length&&"column_ref"===r.type&&"*"===r.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Al()}));return Nl(r,t)}(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function Is(){var t;return 43===r.charCodeAt(Ku)?(t="+",Ku++):(t=u,0===ta&&aa(ut)),t===u&&(45===r.charCodeAt(Ku)?(t="-",Ku++):(t=u,0===ta&&aa(at))),t}function Ns(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=js())!==u){for(e=[],n=Ku,(o=el())!==u?((a=gs())===u&&(a=tl()),a!==u&&(s=el())!==u&&(i=js())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u?((a=gs())===u&&(a=tl()),a!==u&&(s=el())!==u&&(i=js())!==u?n=o=[o,a,s,i]:(Ku=n,n=u)):(Ku=n,n=u);e!==u?(Ju=r,r=t=Nl(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function gs(){var t;return 42===r.charCodeAt(Ku)?(t="*",Ku++):(t=u,0===ta&&aa(st)),t===u&&(47===r.charCodeAt(Ku)?(t="/",Ku++):(t=u,0===ta&&aa(it)),t===u&&(37===r.charCodeAt(Ku)?(t="%",Ku++):(t=u,0===ta&&aa(ct)))),t}function _s(){var t,e,n,o;return(t=function(){var t,e,n,o,a,s,i,c;t=Ku,(e=li())===u&&(e=Ks())===u&&(e=si())===u&&(e=bs())===u&&(e=ps())===u&&(e=ks())===u&&(e=Qs());e!==u&&xc()!==u&&(n=Cl())!==u?(Ju=t,t=e={type:"cast",keyword:"cast",expr:e,symbol:"::",target:[n]}):(Ku=t,t=u);t===u&&(t=Ku,(e=lc())===u&&(e=fc()),e!==u&&el()!==u&&(n=Wc())!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Pi()!==u&&el()!==u&&(a=Cl())!==u&&el()!==u&&(s=Vc())!==u?(Ju=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[e]}}(e,o,a),t=e):(Ku=t,t=u),t===u&&(t=Ku,(e=lc())===u&&(e=fc()),e!==u&&el()!==u&&(n=Wc())!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Pi()!==u&&el()!==u&&(a=yc())!==u&&el()!==u&&(s=Wc())!==u&&el()!==u&&(i=hi())!==u&&el()!==u&&Vc()!==u&&el()!==u&&(c=Vc())!==u?(Ju=t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,i),t=e):(Ku=t,t=u),t===u&&(t=Ku,(e=lc())===u&&(e=fc()),e!==u&&el()!==u&&(n=Wc())!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Pi()!==u&&el()!==u&&(a=yc())!==u&&el()!==u&&(s=Wc())!==u&&el()!==u&&(i=hi())!==u&&el()!==u&&Yc()!==u&&el()!==u&&(c=hi())!==u&&el()!==u&&Vc()!==u&&el()!==u&&Vc()!==u?(Ju=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,i,c),t=e):(Ku=t,t=u),t===u&&(t=Ku,(e=lc())===u&&(e=fc()),e!==u&&el()!==u&&(n=Wc())!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Pi()!==u&&el()!==u&&(a=function(){var t;(t=function(){var t,e,n,o;t=Ku,"signed"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(To));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SIGNED"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=dc());return t}())!==u&&el()!==u?((s=Ec())===u&&(s=null),s!==u&&el()!==u&&(i=Vc())!==u?(Ju=t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,s),t=e):(Ku=t,t=u)):(Ku=t,t=u)))));return t}())===u&&(t=li())===u&&(t=Ks())===u&&(t=si())===u&&(t=bs())===u&&(t=ps())===u&&(t=ks())===u&&(t=Qs())===u&&(t=Ku,Wc()!==u&&(e=el())!==u&&(n=Es())!==u&&el()!==u&&Vc()!==u?(Ju=t,(o=n).parentheses=!0,t=o):(Ku=t,t=u),t===u&&(t=El())===u&&(t=Ku,el()!==u?(36===r.charCodeAt(Ku)?(e="$",Ku++):(e=u,0===ta&&aa(lt)),e!==u&&(n=di())!==u?(Ju=t,t={type:"origin",value:"$"+n.value}):(Ku=t,t=u)):(Ku=t,t=u))),t}function js(){var t,e,n,o,a;return(t=function(){var t,e,n,o,a,s,i,c;if(t=Ku,(e=_s())!==u)if(el()!==u){for(n=[],o=Ku,(a=el())!==u?("?|"===r.substr(Ku,2)?(s="?|",Ku+=2):(s=u,0===ta&&aa(pt)),s===u&&("?&"===r.substr(Ku,2)?(s="?&",Ku+=2):(s=u,0===ta&&aa(bt)),s===u&&(63===r.charCodeAt(Ku)?(s="?",Ku++):(s=u,0===ta&&aa(vt)),s===u&&("#-"===r.substr(Ku,2)?(s="#-",Ku+=2):(s=u,0===ta&&aa(yt)),s===u&&("#>>"===r.substr(Ku,3)?(s="#>>",Ku+=3):(s=u,0===ta&&aa(dt)),s===u&&("#>"===r.substr(Ku,2)?(s="#>",Ku+=2):(s=u,0===ta&&aa(ht)),s===u&&(s=zc())===u&&(s=Zc())===u&&("@>"===r.substr(Ku,2)?(s="@>",Ku+=2):(s=u,0===ta&&aa(Et)),s===u&&("<@"===r.substr(Ku,2)?(s="<@",Ku+=2):(s=u,0===ta&&aa(mt))))))))),s!==u&&(i=el())!==u&&(c=_s())!==u?o=a=[a,s,i,c]:(Ku=o,o=u)):(Ku=o,o=u);o!==u;)n.push(o),o=Ku,(a=el())!==u?("?|"===r.substr(Ku,2)?(s="?|",Ku+=2):(s=u,0===ta&&aa(pt)),s===u&&("?&"===r.substr(Ku,2)?(s="?&",Ku+=2):(s=u,0===ta&&aa(bt)),s===u&&(63===r.charCodeAt(Ku)?(s="?",Ku++):(s=u,0===ta&&aa(vt)),s===u&&("#-"===r.substr(Ku,2)?(s="#-",Ku+=2):(s=u,0===ta&&aa(yt)),s===u&&("#>>"===r.substr(Ku,3)?(s="#>>",Ku+=3):(s=u,0===ta&&aa(dt)),s===u&&("#>"===r.substr(Ku,2)?(s="#>",Ku+=2):(s=u,0===ta&&aa(ht)),s===u&&(s=zc())===u&&(s=Zc())===u&&("@>"===r.substr(Ku,2)?(s="@>",Ku+=2):(s=u,0===ta&&aa(Et)),s===u&&("<@"===r.substr(Ku,2)?(s="<@",Ku+=2):(s=u,0===ta&&aa(mt))))))))),s!==u&&(i=el())!==u&&(c=_s())!==u?o=a=[a,s,i,c]:(Ku=o,o=u)):(Ku=o,o=u);n!==u?(Ju=t,l=e,e=(f=n)&&0!==f.length?Nl(l,f):l,t=e):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;var l,f;return t}())===u&&(t=Ku,(e=function(){var t;33===r.charCodeAt(Ku)?(t="!",Ku++):(t=u,0===ta&&aa(Jr));t===u&&(45===r.charCodeAt(Ku)?(t="-",Ku++):(t=u,0===ta&&aa(at)),t===u&&(43===r.charCodeAt(Ku)?(t="+",Ku++):(t=u,0===ta&&aa(ut)),t===u&&(126===r.charCodeAt(Ku)?(t="~",Ku++):(t=u,0===ta&&aa(ft)))));return t}())!==u?(n=Ku,(o=el())!==u&&(a=js())!==u?n=o=[o,a]:(Ku=n,n=u),n!==u?(Ju=t,t=e=Tl(e,n[1])):(Ku=t,t=u)):(Ku=t,t=u)),t}function Os(){var r,t,e,n,o;return r=Ku,(t=pi())!==u&&el()!==u&&Yc()!==u&&el()!==u&&(e=Us())!==u?(Ju=r,n=t,o=e,kl.add("select::null::"+o.value),r=t={key:n,value:o}):(Ku=r,r=u),r}function xs(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=Os())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Os())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=Os())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Il(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function ks(){var r,t,e,n,o,a,s,i,c,l,f,p;return r=Ku,(t=function(){var r,t;return r=Ku,gc()!==u&&el()!==u&&Xc()!==u&&el()!==u&&(t=xs())!==u&&el()!==u&&qc()!==u?(Ju=r,r={type:"map_object",keyword:"map",expr:t}):(Ku=r,r=u),r}())!==u&&(Ju=r,t={type:"column_ref",table:null,column:{expr:t}}),(r=t)===u&&(r=Ku,t=Ku,(e=Ms())!==u&&(n=el())!==u&&(o=Bc())!==u?t=e=[e,n,o]:(Ku=t,t=u),t===u&&(t=null),t!==u&&(e=el())!==u&&(n=$c())!==u?(Ju=r,r=t=function(r){const t=r&&r[0]||null;return kl.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*"}}(t)):(Ku=r,r=u),r===u&&(r=Ku,(t=Ms())!==u&&(e=el())!==u&&(n=Bc())!==u&&(o=el())!==u&&(a=Ys())!==u?(s=Ku,(i=el())!==u&&(c=Ca())!==u?s=i=[i,c]:(Ku=s,s=u),s===u&&(s=null),s!==u?(Ju=r,l=t,f=a,p=s,kl.add(`select::${l}::${f}`),r=t={type:"column_ref",table:l,column:f,collate:p&&p[1]}):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=Ys())!==u?(e=Ku,(n=el())!==u&&(o=Ca())!==u?e=n=[n,o]:(Ku=e,e=u),e===u&&(e=null),e!==u?(Ju=r,r=t=function(r,t){return kl.add("select::null::"+r),{type:"column_ref",table:null,column:r,collate:t&&t[1]}}(t,e)):(Ku=r,r=u)):(Ku=r,r=u)))),r}function Us(){var r,t;return r=Ku,(t=Ws())!==u&&(Ju=r,t={type:"default",value:t}),(r=t)===u&&(r=Ds()),r}function Ms(){var r,t;return r=Ku,(t=Ws())!==u?(Ju=Ku,(Ct(t)?u:void 0)!==u?(Ju=r,r=t=t):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=Ps())!==u&&(Ju=r,t=t),r=t),r}function Ds(){var r;return(r=Gs())===u&&(r=Fs())===u&&(r=Hs()),r}function Ps(){var r,t;return r=Ku,(t=Gs())===u&&(t=Fs())===u&&(t=Hs()),t!==u&&(Ju=r,t=t.value),r=t}function Gs(){var t,e,n,o;if(t=Ku,34===r.charCodeAt(Ku)?(e='"',Ku++):(e=u,0===ta&&aa(Lt)),e!==u){if(n=[],wt.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(At)),o!==u)for(;o!==u;)n.push(o),wt.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(At));else n=u;n!==u?(34===r.charCodeAt(Ku)?(o='"',Ku++):(o=u,0===ta&&aa(Lt)),o!==u?(Ju=t,t=e={type:"double_quote_string",value:n.join("")}):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;return t}function Fs(){var t,e,n,o;if(t=Ku,39===r.charCodeAt(Ku)?(e="'",Ku++):(e=u,0===ta&&aa(mr)),e!==u){if(n=[],Tt.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(St)),o!==u)for(;o!==u;)n.push(o),Tt.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(St));else n=u;n!==u?(39===r.charCodeAt(Ku)?(o="'",Ku++):(o=u,0===ta&&aa(mr)),o!==u?(Ju=t,t=e={type:"single_quote_string",value:n.join("")}):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;return t}function Hs(){var t,e,n,o;if(t=Ku,96===r.charCodeAt(Ku)?(e="`",Ku++):(e=u,0===ta&&aa(Rt)),e!==u){if(n=[],It.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Nt)),o!==u)for(;o!==u;)n.push(o),It.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Nt));else n=u;n!==u?(96===r.charCodeAt(Ku)?(o="`",Ku++):(o=u,0===ta&&aa(Rt)),o!==u?(Ju=t,t=e={type:"backticks_quote_string",value:n.join("")}):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;return t}function Bs(){var r,t;return r=Ku,(t=$s())!==u&&(Ju=r,t=t),(r=t)===u&&(r=Ps()),r}function Ys(){var r,t;return r=Ku,(t=$s())!==u?(Ju=Ku,(Ct(t)?u:void 0)!==u?(Ju=r,r=t=t):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ps()),r}function $s(){var r,t,e,n;if(r=Ku,(t=Vs())!==u){for(e=[],n=qs();n!==u;)e.push(n),n=qs();e!==u?(Ju=r,r=t=t+e.join("")):(Ku=r,r=u)}else Ku=r,r=u;return r}function Ws(){var r,t,e,n;if(r=Ku,(t=Vs())!==u){for(e=[],n=Xs();n!==u;)e.push(n),n=Xs();e!==u?(Ju=r,r=t=t+e.join("")):(Ku=r,r=u)}else Ku=r,r=u;return r}function Vs(){var t;return gt.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(_t)),t}function Xs(){var t;return jt.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(Ot)),t}function qs(){var t;return xt.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(kt)),t}function Qs(){var t,e,n,o;return t=Ku,e=Ku,58===r.charCodeAt(Ku)?(n=":",Ku++):(n=u,0===ta&&aa(Ut)),n!==u&&(o=Ws())!==u?e=n=[n,o]:(Ku=e,e=u),e!==u&&(Ju=t,e={type:"param",value:e[1]}),t=e}function Ks(){var t;return(t=function(){var t,e,n;t=Ku,(e=function(){var t,e,n,o;t=Ku,"count"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Kn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="COUNT"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;t=Ku,(e=function(){var t,e;t=Ku,42===r.charCodeAt(Ku)?(e="*",Ku++):(e=u,0===ta&&aa(st));e!==u&&(Ju=t,e={type:"star",value:"*"});return t=e}())!==u&&(Ju=t,e={expr:e});if((t=e)===u){if(t=Ku,(e=Ji())===u&&(e=null),e!==u)if(el()!==u)if((n=Wc())!==u)if(el()!==u)if((o=ds())!==u)if(el()!==u)if(Vc()!==u){for(a=[],s=Ku,(i=el())!==u?((c=uc())===u&&(c=ac()),c!==u&&(l=el())!==u&&(f=ds())!==u?s=i=[i,c,l,f]:(Ku=s,s=u)):(Ku=s,s=u);s!==u;)a.push(s),s=Ku,(i=el())!==u?((c=uc())===u&&(c=ac()),c!==u&&(l=el())!==u&&(f=ds())!==u?s=i=[i,c,l,f]:(Ku=s,s=u)):(Ku=s,s=u);a!==u&&(s=el())!==u?((i=ts())===u&&(i=null),i!==u?(Ju=t,e=function(r,t,e,n){const o=e.length;let u=t;u.parentheses=!0;for(let r=0;r<o;++r)u=Sl(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:n}}(e,o,a,i),t=e):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;t===u&&(t=Ku,(e=Ji())===u&&(e=null),e!==u&&el()!==u&&(n=Ja())!==u&&el()!==u?((o=ts())===u&&(o=null),o!==u?(Ju=t,t=e={distinct:e,expr:n,orderby:o}):(Ku=t,t=u)):(Ku=t,t=u))}return t}())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"aggr_func",name:e,args:n}):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c;t=Ku,(e=function(){var t;(t=function(){var t,e,n,o;t=Ku,"sum"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(zn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SUM"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"max"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Jn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MAX"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"min"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Zn));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MIN"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"avg"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(ro));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="AVG"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"collect"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(to));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="COLLECT"):(Ku=t,t=u)):(Ku=t,t=u);return t}());return t}())!==u&&el()!==u&&Wc()!==u&&el()!==u?((n=Ji())===u&&(n=null),n!==u&&(o=el())!==u&&(a=Rs())!==u&&(s=el())!==u&&(i=Vc())!==u?(Ju=t,t=e={type:"aggr_func",name:e,args:{expr:a,distinct:n}}):(Ku=t,t=u)):(Ku=t,t=u);t===u&&(t=Ku,(e=function(){var t;(t=function(){var t,e,n,o;t=Ku,"rank"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(eo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="RANK"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"dense_rank"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(no));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DENSE_RANK"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"row_number"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(uo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ROW_NUMBER"):(Ku=t,t=u)):(Ku=t,t=u);return t}());return t}())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=Vc())!==u?(Ju=t,e=function(r){return{type:"aggr_func",name:r}}(e),t=e):(Ku=t,t=u),t===u&&(t=Ku,(e=function(){var t,e,n,o;t=Ku,"listagg"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(oo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="LISTAGG"):(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=Rs())!==u?(o=Ku,(a=el())!==u&&(s=Yc())!==u&&(i=el())!==u&&(c=pi())!==u?o=a=[a,s,i,c]:(Ku=o,o=u),o===u&&(o=null),o!==u&&(a=el())!==u&&(s=Vc())!==u?(Ju=t,e=function(r,t,e){return{type:"aggr_func",name:r,args:{expr:t,separator:e}}}(e,n,o),t=e):(Ku=t,t=u)):(Ku=t,t=u)));return t}()),t}function Js(){var r,t,e;return r=Ku,Fi()!==u&&el()!==u&&Ri()!==u&&el()!==u&&(t=jc())!==u&&el()!==u&&Wc()!==u&&el()!==u?((e=fs())===u&&(e=null),e!==u&&el()!==u&&Vc()!==u?(Ju=r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,Fi()!==u&&el()!==u&&Ri()!==u&&el()!==u&&(t=jc())!==u?(Ju=r,r=function(r){return{type:"on update",keyword:r}}(t)):(Ku=r,r=u)),r}function Zs(){var t,e,n,o;return t=Ku,"over"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Mt)),e!==u&&el()!==u&&Wc()!==u&&el()!==u&&ki()!==u&&el()!==u&&Xi()!==u&&el()!==u&&(n=Pa())!==u&&el()!==u?((o=ts())===u&&(o=null),o!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={partitionby:n,orderby:o}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Js()),t}function zs(){var t,e,n;return t=Ku,"position"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Dt)),e!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=function(){var r,t,e,n,o,a,s,i;return r=Ku,(t=pi())!==u&&el()!==u&&zi()!==u&&el()!==u&&(e=ds())!==u?(n=Ku,(o=el())!==u&&(a=Mi())!==u&&(s=el())!==u&&(i=di())!==u?n=o=[o,a,s,i]:(Ku=n,n=u),n===u&&(n=null),n!==u?(Ju=r,r=t=function(r,t,e){let n=[r,{type:"origin",value:"in"},t];return e&&(n.push({type:"origin",value:"from"}),n.push(e[3])),{type:"expr_list",value:n}}(t,e,n)):(Ku=r,r=u)):(Ku=r,r=u),r}())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:"position"}]},separator:" ",args:n,...Al()}):(Ku=t,t=u),t}function ri(){var t,e,n,o,a,s,i,c,l,f,p;return t=Ku,(e=pi())!==u&&el()!==u?("value"===r.substr(Ku,5).toLowerCase()?(n=r.substr(Ku,5),Ku+=5):(n=u,0===ta&&aa(Pt)),n!==u&&el()!==u&&(o=Ja())!==u&&el()!==u?(a=Ku,(s=Fi())!==u&&(i=el())!==u?("null"===r.substr(Ku,4).toLowerCase()?(c=r.substr(Ku,4),Ku+=4):(c=u,0===ta&&aa(Gt)),c!==u&&(l=el())!==u?("null"===r.substr(Ku,4).toLowerCase()?(f=r.substr(Ku,4),Ku+=4):(f=u,0===ta&&aa(Gt)),f===u&&("absent"===r.substr(Ku,6).toLowerCase()?(f=r.substr(Ku,6),Ku+=6):(f=u,0===ta&&aa(Ft))),f!==u?a=s=[s,i,c,l,f]:(Ku=a,a=u)):(Ku=a,a=u)):(Ku=a,a=u),a===u&&(a=null),a!==u?(Ju=t,t=e={type:"json_object_arg",expr:{key:e,value:o,on:(p=a)&&{type:"origin",value:p[4]}}}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}function ti(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=ri())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ri())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=ri())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t={type:"expr_list",value:Il(t,e)}):(Ku=r,r=u)}else Ku=r,r=u;return r}function ei(){var t,e,n;return t=Ku,"json_object"===r.substr(Ku,11).toLowerCase()?(e=r.substr(Ku,11),Ku+=11):(e=u,0===ta&&aa(Ht)),e!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=ti())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:"json_object"}]},args:n,...Al()}):(Ku=t,t=u),t}function ni(){var t,e,n;return t=Ku,(e=function(){var t;return"both"===r.substr(Ku,4).toLowerCase()?(t=r.substr(Ku,4),Ku+=4):(t=u,0===ta&&aa(Bt)),t===u&&("leading"===r.substr(Ku,7).toLowerCase()?(t=r.substr(Ku,7),Ku+=7):(t=u,0===ta&&aa(Yt)),t===u&&("trailing"===r.substr(Ku,8).toLowerCase()?(t=r.substr(Ku,8),Ku+=8):(t=u,0===ta&&aa($t)))),t}())===u&&(e=null),e!==u&&el()!==u?((n=ds())===u&&(n=null),n!==u&&el()!==u&&Mi()!==u?(Ju=t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Ku=t,t=u)):(Ku=t,t=u),t}function oi(){var t,e,n,o;return t=Ku,"trim"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Wt)),e!==u&&el()!==u&&Wc()!==u&&el()!==u?((n=ni())===u&&(n=null),n!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...Al()}}(n,o)):(Ku=t,t=u)):(Ku=t,t=u),t}function ui(){var t,e,n;return t=Ku,"overlay"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(qt)),e!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;return t=Ku,(e=ds())!==u&&el()!==u?("placing"===r.substr(Ku,7).toLowerCase()?(n=r.substr(Ku,7),Ku+=7):(n=u,0===ta&&aa(Vt)),n!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Mi()!==u&&el()!==u&&(a=di())!==u?(s=Ku,(i=el())!==u?("for"===r.substr(Ku,3).toLowerCase()?(c=r.substr(Ku,3),Ku+=3):(c=u,0===ta&&aa(Xt)),c!==u&&(l=el())!==u&&(f=di())!==u?s=i=[i,c,l,f]:(Ku=s,s=u)):(Ku=s,s=u),s===u&&(s=null),s!==u?(Ju=t,t=e=function(r,t,e,n){let o=[r,{type:"origin",value:"placing"},t,{type:"origin",value:"from"},e];return n&&(o.push({type:"origin",value:"for"}),o.push(n[3])),{type:"expr_list",value:o}}(e,o,a,s)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:"overlay"}]},separator:" ",args:n,...Al()}):(Ku=t,t=u),t}function ai(){var t,e,n;return t=Ku,"substring"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(Qt)),e!==u&&el()!==u&&Wc()!==u&&el()!==u&&(n=function(){var t,e,n,o,a,s,i,c;return t=Ku,(e=ds())!==u&&el()!==u&&Mi()!==u&&el()!==u&&(n=di())!==u?(o=Ku,(a=el())!==u?("for"===r.substr(Ku,3).toLowerCase()?(s=r.substr(Ku,3),Ku+=3):(s=u,0===ta&&aa(Xt)),s!==u&&(i=el())!==u&&(c=di())!==u?o=a=[a,s,i,c]:(Ku=o,o=u)):(Ku=o,o=u),o===u&&(o=null),o!==u?(Ju=t,t=e=function(r,t,e){let n=[r,{type:"origin",value:"from"},t];return e&&(n.push({type:"origin",value:"for"}),n.push(e[3])),{type:"expr_list",value:n}}(e,n,o)):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&el()!==u&&Vc()!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:"substring"}]},separator:" ",args:n,...Al()}):(Ku=t,t=u),t}function si(){var t,e,n,o,a;return(t=zs())===u&&(t=ei())===u&&(t=oi())===u&&(t=ai())===u&&(t=ui())===u&&(t=Ku,(e=function(){var t;(t=ci())===u&&(t=function(){var t,e,n,o;t=Ku,"current_user"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(zo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CURRENT_USER"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"user"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Wo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="USER"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"session_user"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(ru));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SESSION_USER"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"system_user"===r.substr(Ku,11).toLowerCase()?(e=r.substr(Ku,11),Ku+=11):(e=u,0===ta&&aa(tu));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SYSTEM_USER"):(Ku=t,t=u)):(Ku=t,t=u);return t}());return t}())!==u&&el()!==u&&(n=Wc())!==u&&el()!==u?((o=fs())===u&&(o=null),o!==u&&el()!==u&&Vc()!==u&&el()!==u?((a=Zs())===u&&(a=null),a!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"default",value:e}]},args:o||{type:"expr_list",value:[]},over:a,...Al()}):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=function(){var r,t,e,n,o;r=Ku,(t=sc())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(e=ii())!==u&&el()!==u&&Mi()!==u&&el()!==u?((n=Ic())===u&&(n=_c())===u&&(n=Rc())===u&&(n=Tc()),n!==u&&el()!==u&&(o=ds())!==u&&el()!==u&&Vc()!==u?(Ju=r,a=e,s=n,i=o,t={type:t.toLowerCase(),args:{field:a,cast_type:s,source:i},...Al()},r=t):(Ku=r,r=u)):(Ku=r,r=u);var a,s,i;r===u&&(r=Ku,(t=sc())!==u&&el()!==u&&Wc()!==u&&el()!==u&&(e=ii())!==u&&el()!==u&&Mi()!==u&&el()!==u&&(n=ds())!==u&&el()!==u&&(o=Vc())!==u?(Ju=r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e},...Al()}}(t,e,n),r=t):(Ku=r,r=u));return r}())===u&&(t=Ku,(e=ci())!==u&&el()!==u?((n=Js())===u&&(n=null),n!==u?(Ju=t,t=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...Al()}):(Ku=t,t=u)):(Ku=t,t=u),t===u&&(t=Ku,(e=yl())!==u&&el()!==u&&(n=Wc())!==u&&el()!==u?((o=Es())===u&&(o=null),o!==u&&el()!==u&&Vc()!==u&&el()!==u?((a=Zs())===u&&(a=null),a!==u?(Ju=t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,...Al()}}(e,o,a)):(Ku=t,t=u)):(Ku=t,t=u)):(Ku=t,t=u)))),t}function ii(){var t,e;return t=Ku,"century"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Kt)),e===u&&("day"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Jt)),e===u&&("date"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Zt)),e===u&&("decade"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(zt)),e===u&&("dow"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(re)),e===u&&("doy"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(te)),e===u&&("epoch"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(ee)),e===u&&("hour"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(ne)),e===u&&("isodow"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(oe)),e===u&&("isoyear"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(ue)),e===u&&("microseconds"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(ae)),e===u&&("millennium"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(se)),e===u&&("milliseconds"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(ie)),e===u&&("minute"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(ce)),e===u&&("month"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(le)),e===u&&("quarter"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(fe)),e===u&&("second"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(pe)),e===u&&("timezone"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(be)),e===u&&("timezone_hour"===r.substr(Ku,13).toLowerCase()?(e=r.substr(Ku,13),Ku+=13):(e=u,0===ta&&aa(ve)),e===u&&("timezone_minute"===r.substr(Ku,15).toLowerCase()?(e=r.substr(Ku,15),Ku+=15):(e=u,0===ta&&aa(ye)),e===u&&("week"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(de)),e===u&&("year"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(he))))))))))))))))))))))),e!==u&&(Ju=t,e=e),t=e}function ci(){var t;return(t=function(){var t,e,n,o;t=Ku,"current_date"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(Qo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CURRENT_DATE"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Ku,"current_time"===r.substr(Ku,12).toLowerCase()?(e=r.substr(Ku,12),Ku+=12):(e=u,0===ta&&aa(Jo));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CURRENT_TIME"):(Ku=t,t=u)):(Ku=t,t=u);return t}())===u&&(t=jc()),t}function li(){var t;return(t=pi())===u&&(t=di())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;t=Ku,"true"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(qe));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={type:"bool",value:!0});(t=e)===u&&(t=Ku,(e=function(){var t,e,n,o;t=Ku,"false"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Ke));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={type:"bool",value:!1}),t=e);return t}())===u&&(t=fi())===u&&(t=function(){var t,e,n,o,a,s;t=Ku,(e=Rc())===u&&(e=Tc())===u&&(e=Ic())===u&&(e=Sc());if(e!==u)if(el()!==u){if(n=Ku,39===r.charCodeAt(Ku)?(o="'",Ku++):(o=u,0===ta&&aa(mr)),o!==u){for(a=[],s=vi();s!==u;)a.push(s),s=vi();a!==u?(39===r.charCodeAt(Ku)?(s="'",Ku++):(s=u,0===ta&&aa(mr)),s!==u?n=o=[o,a,s]:(Ku=n,n=u)):(Ku=n,n=u)}else Ku=n,n=u;n!==u?(Ju=t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;var i;if(t===u)if(t=Ku,(e=Rc())===u&&(e=Tc())===u&&(e=Ic())===u&&(e=Sc()),e!==u)if(el()!==u){if(n=Ku,34===r.charCodeAt(Ku)?(o='"',Ku++):(o=u,0===ta&&aa(Lt)),o!==u){for(a=[],s=bi();s!==u;)a.push(s),s=bi();a!==u?(34===r.charCodeAt(Ku)?(s='"',Ku++):(s=u,0===ta&&aa(Lt)),s!==u?n=o=[o,a,s]:(Ku=n,n=u)):(Ku=n,n=u)}else Ku=n,n=u;n!==u?(Ju=t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;return t}()),t}function fi(){var t,e;return t=Ku,(e=function(){var t,e,n,o;t=Ku,"null"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Gt));e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u);return t}())!==u&&(Ju=t,e={type:"null",value:null}),t=e}function pi(){var t,e,n,o,a;if(t=Ku,e=Ku,39===r.charCodeAt(Ku)?(n="'",Ku++):(n=u,0===ta&&aa(mr)),n!==u){for(o=[],a=vi();a!==u;)o.push(a),a=vi();o!==u?(39===r.charCodeAt(Ku)?(a="'",Ku++):(a=u,0===ta&&aa(mr)),a!==u?e=n=[n,o,a]:(Ku=e,e=u)):(Ku=e,e=u)}else Ku=e,e=u;if(e!==u&&(Ju=t,e={type:"single_quote_string",value:e[1].join("")}),(t=e)===u){if(t=Ku,e=Ku,34===r.charCodeAt(Ku)?(n='"',Ku++):(n=u,0===ta&&aa(Lt)),n!==u){for(o=[],a=bi();a!==u;)o.push(a),a=bi();o!==u?(34===r.charCodeAt(Ku)?(a='"',Ku++):(a=u,0===ta&&aa(Lt)),a!==u?e=n=[n,o,a]:(Ku=e,e=u)):(Ku=e,e=u)}else Ku=e,e=u;e!==u?(n=Ku,ta++,o=Bc(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)):(Ku=t,t=u)):(Ku=t,t=u)}return t}function bi(){var t;return Ee.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(me)),t===u&&(t=yi()),t}function vi(){var t;return Ce.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(Le)),t===u&&(t=yi()),t}function yi(){var t,e,n,o,a,s,i,c,l,f;return t=Ku,"\\'"===r.substr(Ku,2)?(e="\\'",Ku+=2):(e=u,0===ta&&aa(we)),e!==u&&(Ju=t,e="\\'"),(t=e)===u&&(t=Ku,'\\"'===r.substr(Ku,2)?(e='\\"',Ku+=2):(e=u,0===ta&&aa(Ae)),e!==u&&(Ju=t,e='\\"'),(t=e)===u&&(t=Ku,"\\\\"===r.substr(Ku,2)?(e="\\\\",Ku+=2):(e=u,0===ta&&aa(Te)),e!==u&&(Ju=t,e="\\\\"),(t=e)===u&&(t=Ku,"\\/"===r.substr(Ku,2)?(e="\\/",Ku+=2):(e=u,0===ta&&aa(Se)),e!==u&&(Ju=t,e="\\/"),(t=e)===u&&(t=Ku,"\\b"===r.substr(Ku,2)?(e="\\b",Ku+=2):(e=u,0===ta&&aa(Re)),e!==u&&(Ju=t,e="\b"),(t=e)===u&&(t=Ku,"\\f"===r.substr(Ku,2)?(e="\\f",Ku+=2):(e=u,0===ta&&aa(Ie)),e!==u&&(Ju=t,e="\f"),(t=e)===u&&(t=Ku,"\\n"===r.substr(Ku,2)?(e="\\n",Ku+=2):(e=u,0===ta&&aa(Ne)),e!==u&&(Ju=t,e="\n"),(t=e)===u&&(t=Ku,"\\r"===r.substr(Ku,2)?(e="\\r",Ku+=2):(e=u,0===ta&&aa(ge)),e!==u&&(Ju=t,e="\r"),(t=e)===u&&(t=Ku,"\\t"===r.substr(Ku,2)?(e="\\t",Ku+=2):(e=u,0===ta&&aa(_e)),e!==u&&(Ju=t,e="\t"),(t=e)===u&&(t=Ku,"\\u"===r.substr(Ku,2)?(e="\\u",Ku+=2):(e=u,0===ta&&aa(je)),e!==u&&(n=wi())!==u&&(o=wi())!==u&&(a=wi())!==u&&(s=wi())!==u?(Ju=t,i=n,c=o,l=a,f=s,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Ku=t,t=u),t===u&&(t=Ku,92===r.charCodeAt(Ku)?(e="\\",Ku++):(e=u,0===ta&&aa(Oe)),e!==u&&(Ju=t,e="\\"),(t=e)===u&&(t=Ku,"''"===r.substr(Ku,2)?(e="''",Ku+=2):(e=u,0===ta&&aa(xe)),e!==u&&(Ju=t,e="''"),(t=e)===u&&(t=Ku,'""'===r.substr(Ku,2)?(e='""',Ku+=2):(e=u,0===ta&&aa(ke)),e!==u&&(Ju=t,e='""'),(t=e)===u&&(t=Ku,"``"===r.substr(Ku,2)?(e="``",Ku+=2):(e=u,0===ta&&aa(Ue)),e!==u&&(Ju=t,e="``"),t=e))))))))))))),t}function di(){var r,t,e;return r=Ku,(t=function(){var r,t,e,n;r=Ku,(t=hi())!==u&&(e=Ei())!==u&&(n=mi())!==u?(Ju=r,r=t={type:"bigint",value:t+e+n}):(Ku=r,r=u);r===u&&(r=Ku,(t=hi())!==u&&(e=Ei())!==u?(Ju=r,t=function(r,t){const e=r+t;return Rl(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(Ku=r,r=u),r===u&&(r=Ku,(t=hi())!==u&&(e=mi())!==u?(Ju=r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(Ku=r,r=u),r===u&&(r=Ku,(t=hi())!==u&&(Ju=r,t=function(r){return Rl(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==u&&(Ju=r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function hi(){var t,e,n;return(t=Ci())===u&&(t=Li())===u&&(t=Ku,45===r.charCodeAt(Ku)?(e="-",Ku++):(e=u,0===ta&&aa(at)),e===u&&(43===r.charCodeAt(Ku)?(e="+",Ku++):(e=u,0===ta&&aa(ut))),e!==u&&(n=Ci())!==u?(Ju=t,t=e=e+n):(Ku=t,t=u),t===u&&(t=Ku,45===r.charCodeAt(Ku)?(e="-",Ku++):(e=u,0===ta&&aa(at)),e===u&&(43===r.charCodeAt(Ku)?(e="+",Ku++):(e=u,0===ta&&aa(ut))),e!==u&&(n=Li())!==u?(Ju=t,t=e=function(r,t){return r+t}(e,n)):(Ku=t,t=u))),t}function Ei(){var t,e,n;return t=Ku,46===r.charCodeAt(Ku)?(e=".",Ku++):(e=u,0===ta&&aa(Pe)),e!==u&&(n=Ci())!==u?(Ju=t,t=e="."+n):(Ku=t,t=u),t}function mi(){var t,e,n;return t=Ku,(e=function(){var t,e,n;t=Ku,Ye.test(r.charAt(Ku))?(e=r.charAt(Ku),Ku++):(e=u,0===ta&&aa($e));e!==u?(We.test(r.charAt(Ku))?(n=r.charAt(Ku),Ku++):(n=u,0===ta&&aa(Ve)),n===u&&(n=null),n!==u?(Ju=t,t=e=e+(null!==(o=n)?o:"")):(Ku=t,t=u)):(Ku=t,t=u);var o;return t}())!==u&&(n=Ci())!==u?(Ju=t,t=e=e+n):(Ku=t,t=u),t}function Ci(){var r,t,e;if(r=Ku,t=[],(e=Li())!==u)for(;e!==u;)t.push(e),e=Li();else t=u;return t!==u&&(Ju=r,t=t.join("")),r=t}function Li(){var t;return Ge.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(Fe)),t}function wi(){var t;return He.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(Be)),t}function Ai(){var t,e,n,o;return t=Ku,"default"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(A)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Ti(){var t,e,n,o;return t=Ku,"to"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Qe)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Si(){var t,e,n,o;return t=Ku,"drop"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Je)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DROP"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ri(){var t,e,n,o;return t=Ku,"update"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(tn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Ii(){var t,e,n,o;return t=Ku,"create"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(en)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Ni(){var t,e,n,o;return t=Ku,"temporary"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(nn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function gi(){var t,e,n,o;return t=Ku,"delete"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(on)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function _i(){var t,e,n,o;return t=Ku,"insert"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(un)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function ji(){var t,e,n,o;return t=Ku,"replace"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(sn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Oi(){var t,e,n,o;return t=Ku,"rename"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(ln)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function xi(){var t,e,n,o;return t=Ku,"ignore"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(fn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function ki(){var t,e,n,o;return t=Ku,"partition"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(pn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="PARTITION"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ui(){var t,e,n,o;return t=Ku,"into"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(bn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Mi(){var t,e,n,o;return t=Ku,"from"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(vn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Di(){var t,e,n,o;return t=Ku,"set"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(cr)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SET"):(Ku=t,t=u)):(Ku=t,t=u),t}function Pi(){var t,e,n,o;return t=Ku,"as"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(yn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Gi(){var t,e,n,o;return t=Ku,"table"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(dn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TABLE"):(Ku=t,t=u)):(Ku=t,t=u),t}function Fi(){var t,e,n,o;return t=Ku,"on"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Q)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Hi(){var t,e,n,o;return t=Ku,"join"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Tn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Bi(){var t,e,n,o;return t=Ku,"cross"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Sn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Yi(){var t,e,n,o;return t=Ku,"outer"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(In)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function $i(){var t,e,n,o;return t=Ku,"values"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(jn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Wi(){var t,e,n,o;return t=Ku,"using"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(On)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Vi(){var t,e,n,o;return t=Ku,"with"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Gr)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function Xi(){var t,e,n,o;return t=Ku,"by"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Un)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function qi(){var t,e,n,o;return t=Ku,"asc"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Gn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ASC"):(Ku=t,t=u)):(Ku=t,t=u),t}function Qi(){var t,e,n,o;return t=Ku,"desc"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Fn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DESC"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ki(){var t,e,n,o;return t=Ku,"all"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Hn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ALL"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ji(){var t,e,n,o;return t=Ku,"distinct"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Bn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DISTINCT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Zi(){var t,e,n,o;return t=Ku,"between"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Yn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="BETWEEN"):(Ku=t,t=u)):(Ku=t,t=u),t}function zi(){var t,e,n,o;return t=Ku,"in"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Ar)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="IN"):(Ku=t,t=u)):(Ku=t,t=u),t}function rc(){var t,e,n,o;return t=Ku,"is"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa($n)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="IS"):(Ku=t,t=u)):(Ku=t,t=u),t}function tc(){var t,e,n,o;return t=Ku,"like"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Wn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="LIKE"):(Ku=t,t=u)):(Ku=t,t=u),t}function ec(){var t,e,n,o;return t=Ku,"similar"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Vn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SIMILAR"):(Ku=t,t=u)):(Ku=t,t=u),t}function nc(){var t,e,n,o;return t=Ku,"exists"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Xn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="EXISTS"):(Ku=t,t=u)):(Ku=t,t=u),t}function oc(){var t,e,n,o;return t=Ku,"not"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(rr)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="NOT"):(Ku=t,t=u)):(Ku=t,t=u),t}function uc(){var t,e,n,o;return t=Ku,"and"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(qn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="AND"):(Ku=t,t=u)):(Ku=t,t=u),t}function ac(){var t,e,n,o;return t=Ku,"or"===r.substr(Ku,2).toLowerCase()?(e=r.substr(Ku,2),Ku+=2):(e=u,0===ta&&aa(Qn)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="OR"):(Ku=t,t=u)):(Ku=t,t=u),t}function sc(){var t,e,n,o;return t=Ku,"extract"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(so)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="EXTRACT"):(Ku=t,t=u)):(Ku=t,t=u),t}function ic(){var t,e,n,o;return t=Ku,"case"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(co)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function cc(){var t,e,n,o;return t=Ku,"when"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(lo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?t=e=[e,n]:(Ku=t,t=u)):(Ku=t,t=u),t}function lc(){var t,e,n,o;return t=Ku,"cast"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(vo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CAST"):(Ku=t,t=u)):(Ku=t,t=u),t}function fc(){var t,e,n,o;return t=Ku,"try_cast"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(yo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TRY_CAST"):(Ku=t,t=u)):(Ku=t,t=u),t}function pc(){var t,e,n,o;return t=Ku,"char"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(mo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CHAR"):(Ku=t,t=u)):(Ku=t,t=u),t}function bc(){var t,e,n,o;return t=Ku,"varchar"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Co)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="VARCHAR"):(Ku=t,t=u)):(Ku=t,t=u),t}function vc(){var t,e,n,o;return t=Ku,"numeric"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(wo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="NUMERIC"):(Ku=t,t=u)):(Ku=t,t=u),t}function yc(){var t,e,n,o;return t=Ku,"decimal"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Ao)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DECIMAL"):(Ku=t,t=u)):(Ku=t,t=u),t}function dc(){var t,e,n,o;return t=Ku,"unsigned"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(So)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="UNSIGNED"):(Ku=t,t=u)):(Ku=t,t=u),t}function hc(){var t,e,n,o;return t=Ku,"int"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(Ro)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ec(){var t,e,n,o;return t=Ku,"integer"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(No)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INTEGER"):(Ku=t,t=u)):(Ku=t,t=u),t}function mc(){var t,e,n,o;return t=Ku,"smallint"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Oo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="SMALLINT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Cc(){var t,e,n,o;return t=Ku,"tinyint"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(xo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TINYINT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Lc(){var t,e,n,o;return t=Ku,"bigint"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Po)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="BIGINT"):(Ku=t,t=u)):(Ku=t,t=u),t}function wc(){var t,e,n,o;return t=Ku,"float"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Go)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="FLOAT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ac(){var t,e,n,o;return t=Ku,"double"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Fo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DOUBLE"):(Ku=t,t=u)):(Ku=t,t=u),t}function Tc(){var t,e,n,o;return t=Ku,"date"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Zt)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DATE"):(Ku=t,t=u)):(Ku=t,t=u),t}function Sc(){var t,e,n,o;return t=Ku,"datetime"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Ho)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="DATETIME"):(Ku=t,t=u)):(Ku=t,t=u),t}function Rc(){var t,e,n,o;return t=Ku,"time"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Bo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TIME"):(Ku=t,t=u)):(Ku=t,t=u),t}function Ic(){var t,e,n,o;return t=Ku,"timestamp"===r.substr(Ku,9).toLowerCase()?(e=r.substr(Ku,9),Ku+=9):(e=u,0===ta&&aa(Yo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TIMESTAMP"):(Ku=t,t=u)):(Ku=t,t=u),t}function Nc(){var t,e,n,o;return t=Ku,"truncate"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa($o)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TRUNCATE"):(Ku=t,t=u)):(Ku=t,t=u),t}function gc(){var t,e,n,o;return t=Ku,"map"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(qo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MAP"):(Ku=t,t=u)):(Ku=t,t=u),t}function _c(){var t,e,n,o;return t=Ku,"interval"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Ko)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INTERVAL"):(Ku=t,t=u)):(Ku=t,t=u),t}function jc(){var t,e,n,o;return t=Ku,"current_timestamp"===r.substr(Ku,17).toLowerCase()?(e=r.substr(Ku,17),Ku+=17):(e=u,0===ta&&aa(Zo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CURRENT_TIMESTAMP"):(Ku=t,t=u)):(Ku=t,t=u),t}function Oc(){var t;return(t=function(){var t;return"@@"===r.substr(Ku,2)?(t="@@",Ku+=2):(t=u,0===ta&&aa(iu)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt(Ku)?(t="@",Ku++):(t=u,0===ta&&aa(su)),t}())===u&&(t=function(){var t;return 36===r.charCodeAt(Ku)?(t="$",Ku++):(t=u,0===ta&&aa(lt)),t}()),t}function xc(){var t;return"::"===r.substr(Ku,2)?(t="::",Ku+=2):(t=u,0===ta&&aa(fu)),t}function kc(){var t;return 61===r.charCodeAt(Ku)?(t="=",Ku++):(t=u,0===ta&&aa(Qr)),t}function Uc(){var t,e,n,o;return t=Ku,"add"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(bu)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ADD"):(Ku=t,t=u)):(Ku=t,t=u),t}function Mc(){var t,e,n,o;return t=Ku,"column"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(vu)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="COLUMN"):(Ku=t,t=u)):(Ku=t,t=u),t}function Dc(){var t,e,n,o;return t=Ku,"index"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(yu)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="INDEX"):(Ku=t,t=u)):(Ku=t,t=u),t}function Pc(){var t,e,n,o;return t=Ku,"key"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(E)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="KEY"):(Ku=t,t=u)):(Ku=t,t=u),t}function Gc(){var t,e,n,o;return t=Ku,"unique"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(h)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="UNIQUE"):(Ku=t,t=u)):(Ku=t,t=u),t}function Fc(){var t,e,n,o;return t=Ku,"comment"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Eu)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="COMMENT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Hc(){var t,e,n,o;return t=Ku,"constraint"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(mu)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="CONSTRAINT"):(Ku=t,t=u)):(Ku=t,t=u),t}function Bc(){var t;return 46===r.charCodeAt(Ku)?(t=".",Ku++):(t=u,0===ta&&aa(Pe)),t}function Yc(){var t;return 44===r.charCodeAt(Ku)?(t=",",Ku++):(t=u,0===ta&&aa(Nu)),t}function $c(){var t;return 42===r.charCodeAt(Ku)?(t="*",Ku++):(t=u,0===ta&&aa(st)),t}function Wc(){var t;return 40===r.charCodeAt(Ku)?(t="(",Ku++):(t=u,0===ta&&aa(xr)),t}function Vc(){var t;return 41===r.charCodeAt(Ku)?(t=")",Ku++):(t=u,0===ta&&aa(kr)),t}function Xc(){var t;return 91===r.charCodeAt(Ku)?(t="[",Ku++):(t=u,0===ta&&aa(gu)),t}function qc(){var t;return 93===r.charCodeAt(Ku)?(t="]",Ku++):(t=u,0===ta&&aa(_u)),t}function Qc(){var t;return 60===r.charCodeAt(Ku)?(t="<",Ku++):(t=u,0===ta&&aa(et)),t}function Kc(){var t;return 62===r.charCodeAt(Ku)?(t=">",Ku++):(t=u,0===ta&&aa(zr)),t}function Jc(){var t;return 59===r.charCodeAt(Ku)?(t=";",Ku++):(t=u,0===ta&&aa(ju)),t}function Zc(){var t;return"->"===r.substr(Ku,2)?(t="->",Ku+=2):(t=u,0===ta&&aa(Ou)),t}function zc(){var t;return"->>"===r.substr(Ku,3)?(t="->>",Ku+=3):(t=u,0===ta&&aa(xu)),t}function rl(){var t;return"=>"===r.substr(Ku,2)?(t="=>",Ku+=2):(t=u,0===ta&&aa(ku)),t}function tl(){var t;return(t=function(){var t;return"||"===r.substr(Ku,2)?(t="||",Ku+=2):(t=u,0===ta&&aa(Uu)),t}())===u&&(t=function(){var t;return"&&"===r.substr(Ku,2)?(t="&&",Ku+=2):(t=u,0===ta&&aa(Mu)),t}()),t}function el(){var r,t;for(r=[],(t=sl())===u&&(t=ol());t!==u;)r.push(t),(t=sl())===u&&(t=ol());return r}function nl(){var r,t;if(r=[],(t=sl())===u&&(t=ol()),t!==u)for(;t!==u;)r.push(t),(t=sl())===u&&(t=ol());else r=u;return r}function ol(){var t;return(t=function(){var t,e,n,o,a,s;t=Ku,"/*"===r.substr(Ku,2)?(e="/*",Ku+=2):(e=u,0===ta&&aa(Du));if(e!==u){for(n=[],o=Ku,a=Ku,ta++,"*/"===r.substr(Ku,2)?(s="*/",Ku+=2):(s=u,0===ta&&aa(Pu)),ta--,s===u?a=void 0:(Ku=a,a=u),a!==u&&(s=al())!==u?o=a=[a,s]:(Ku=o,o=u);o!==u;)n.push(o),o=Ku,a=Ku,ta++,"*/"===r.substr(Ku,2)?(s="*/",Ku+=2):(s=u,0===ta&&aa(Pu)),ta--,s===u?a=void 0:(Ku=a,a=u),a!==u&&(s=al())!==u?o=a=[a,s]:(Ku=o,o=u);n!==u?("*/"===r.substr(Ku,2)?(o="*/",Ku+=2):(o=u,0===ta&&aa(Pu)),o!==u?t=e=[e,n,o]:(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Ku,"--"===r.substr(Ku,2)?(e="--",Ku+=2):(e=u,0===ta&&aa(Gu));if(e!==u){for(n=[],o=Ku,a=Ku,ta++,s=il(),ta--,s===u?a=void 0:(Ku=a,a=u),a!==u&&(s=al())!==u?o=a=[a,s]:(Ku=o,o=u);o!==u;)n.push(o),o=Ku,a=Ku,ta++,s=il(),ta--,s===u?a=void 0:(Ku=a,a=u),a!==u&&(s=al())!==u?o=a=[a,s]:(Ku=o,o=u);n!==u?t=e=[e,n]:(Ku=t,t=u)}else Ku=t,t=u;return t}()),t}function ul(){var r,t,e,n,o,a,s;return r=Ku,(t=Fc())!==u&&el()!==u?((e=kc())===u&&(e=null),e!==u&&el()!==u&&(n=pi())!==u?(Ju=r,a=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:a,value:s}):(Ku=r,r=u)):(Ku=r,r=u),r}function al(){var t;return r.length>Ku?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(Fu)),t}function sl(){var t;return Xu.test(r.charAt(Ku))?(t=r.charAt(Ku),Ku++):(t=u,0===ta&&aa(qu)),t}function il(){var t,e;if((t=function(){var t,e;t=Ku,ta++,r.length>Ku?(e=r.charAt(Ku),Ku++):(e=u,0===ta&&aa(Fu));ta--,e===u?t=void 0:(Ku=t,t=u);return t}())===u)if(t=[],Me.test(r.charAt(Ku))?(e=r.charAt(Ku),Ku++):(e=u,0===ta&&aa(De)),e!==u)for(;e!==u;)t.push(e),Me.test(r.charAt(Ku))?(e=r.charAt(Ku),Ku++):(e=u,0===ta&&aa(De));else t=u;return t}function cl(){var t,e;return t=Ku,Ju=Ku,Ol=[],(!0?void 0:u)!==u&&el()!==u?((e=ll())===u&&(e=function(){var t,e;t=Ku,function(){var t;return"return"===r.substr(Ku,6).toLowerCase()?(t=r.substr(Ku,6),Ku+=6):(t=u,0===ta&&aa(cu)),t}()!==u&&el()!==u&&(e=fl())!==u?(Ju=t,t={type:"return",expr:e}):(Ku=t,t=u);return t}()),e!==u?(Ju=t,t={type:"proc",stmt:e,vars:Ol}):(Ku=t,t=u)):(Ku=t,t=u),t}function ll(){var t,e,n,o;return t=Ku,(e=El())===u&&(e=ml()),e!==u&&el()!==u?((n=function(){var t;return":="===r.substr(Ku,2)?(t=":=",Ku+=2):(t=u,0===ta&&aa(lu)),t}())===u&&(n=kc()),n!==u&&el()!==u&&(o=fl())!==u?(Ju=t,t=e={type:"assign",left:e,symbol:n,right:o}):(Ku=t,t=u)):(Ku=t,t=u),t}function fl(){var r;return(r=Oa())===u&&(r=function(){var r,t,e,n,o;r=Ku,(t=El())!==u&&el()!==u&&(e=Qa())!==u&&el()!==u&&(n=El())!==u&&el()!==u&&(o=Za())!==u?(Ju=r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Ku=r,r=u);return r}())===u&&(r=pl())===u&&(r=function(){var r,t;r=Ku,Xc()!==u&&el()!==u&&(t=hl())!==u&&el()!==u&&qc()!==u?(Ju=r,r={type:"array",value:t}):(Ku=r,r=u);return r}()),r}function pl(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=bl())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Is())!==u&&(s=el())!==u&&(i=bl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Is())!==u&&(s=el())!==u&&(i=bl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Kr(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function bl(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=vl())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=gs())!==u&&(s=el())!==u&&(i=vl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=gs())!==u&&(s=el())!==u&&(i=vl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Kr(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function vl(){var r,t,e;return(r=li())===u&&(r=El())===u&&(r=dl())===u&&(r=Qs())===u&&(r=Ku,Wc()!==u&&el()!==u&&(t=pl())!==u&&el()!==u&&Vc()!==u?(Ju=r,(e=t).parentheses=!0,r=e):(Ku=r,r=u)),r}function yl(){var r,t,e,n,o,a,s;return r=Ku,(t=Us())!==u?(e=Ku,(n=el())!==u&&(o=Bc())!==u&&(a=el())!==u&&(s=Us())!==u?e=n=[n,o,a,s]:(Ku=e,e=u),e===u&&(e=null),e!==u?(Ju=r,r=t=function(r,t){const e={name:[r]};return null!==t&&(e.schema=r,e.name=[t[3]]),e}(t,e)):(Ku=r,r=u)):(Ku=r,r=u),r}function dl(){var r,t,e;return r=Ku,(t=yl())!==u&&el()!==u&&Wc()!==u&&el()!==u?((e=hl())===u&&(e=null),e!==u&&el()!==u&&Vc()!==u?(Ju=r,r=t={type:"function",name:t,args:{type:"expr_list",value:e},...Al()}):(Ku=r,r=u)):(Ku=r,r=u),r===u&&(r=Ku,(t=yl())!==u&&(Ju=r,t=function(r){return{type:"function",name:r,args:null,...Al()}}(t)),r=t),r}function hl(){var r,t,e,n,o,a,s,i;if(r=Ku,(t=vl())!==u){for(e=[],n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=vl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);n!==u;)e.push(n),n=Ku,(o=el())!==u&&(a=Yc())!==u&&(s=el())!==u&&(i=vl())!==u?n=o=[o,a,s,i]:(Ku=n,n=u);e!==u?(Ju=r,r=t=Il(t,e)):(Ku=r,r=u)}else Ku=r,r=u;return r}function El(){var r,t,e,n,o;return r=Ku,(t=Oc())!==u&&(e=ml())!==u?(Ju=r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(Ku=r,r=u),r}function ml(){var t,e,n,o,a;return t=Ku,(e=Ws())!==u&&(n=function(){var t,e,n,o,a;t=Ku,e=[],n=Ku,46===r.charCodeAt(Ku)?(o=".",Ku++):(o=u,0===ta&&aa(Pe));o!==u&&(a=Ws())!==u?n=o=[o,a]:(Ku=n,n=u);for(;n!==u;)e.push(n),n=Ku,46===r.charCodeAt(Ku)?(o=".",Ku++):(o=u,0===ta&&aa(Pe)),o!==u&&(a=Ws())!==u?n=o=[o,a]:(Ku=n,n=u);e!==u&&(Ju=t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(Ju=t,o=e,a=n,Ol.push(o),t=e={type:"var",name:o,members:a,prefix:null}):(Ku=t,t=u),t===u&&(t=Ku,(e=di())!==u&&(Ju=t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Cl(){var t;return(t=function(){var t,e,n,o;t=Ku,(e=pc())===u&&(e=bc());if(e!==u)if(el()!==u)if(Wc()!==u)if(el()!==u){if(n=[],Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe)),o!==u)for(;o!==u;)n.push(o),Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe));else n=u;n!==u&&(o=el())!==u&&Vc()!==u?(Ju=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;t===u&&(t=Ku,(e=pc())!==u&&(Ju=t,e=function(r){return{dataType:r}}(e)),(t=e)===u&&(t=Ku,(e=bc())!==u&&(Ju=t,e=Qu(e)),(t=e)===u&&(t=Ku,(e=function(){var t,e,n,o;return t=Ku,"string"===r.substr(Ku,6).toLowerCase()?(e=r.substr(Ku,6),Ku+=6):(e=u,0===ta&&aa(Lo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="STRING"):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&(Ju=t,e=function(r){return{dataType:r}}(e)),t=e)));return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b;t=Ku,(e=vc())===u&&(e=yc())===u&&(e=hc())===u&&(e=Ec())===u&&(e=mc())===u&&(e=Cc())===u&&(e=Lc())===u&&(e=wc())===u&&(e=Ac());if(e!==u)if((n=el())!==u)if((o=Wc())!==u)if((a=el())!==u){if(s=[],Ge.test(r.charAt(Ku))?(i=r.charAt(Ku),Ku++):(i=u,0===ta&&aa(Fe)),i!==u)for(;i!==u;)s.push(i),Ge.test(r.charAt(Ku))?(i=r.charAt(Ku),Ku++):(i=u,0===ta&&aa(Fe));else s=u;if(s!==u)if((i=el())!==u){if(c=Ku,(l=Yc())!==u)if((f=el())!==u){if(p=[],Ge.test(r.charAt(Ku))?(b=r.charAt(Ku),Ku++):(b=u,0===ta&&aa(Fe)),b!==u)for(;b!==u;)p.push(b),Ge.test(r.charAt(Ku))?(b=r.charAt(Ku),Ku++):(b=u,0===ta&&aa(Fe));else p=u;p!==u?c=l=[l,f,p]:(Ku=c,c=u)}else Ku=c,c=u;else Ku=c,c=u;c===u&&(c=null),c!==u&&(l=el())!==u&&(f=Vc())!==u&&(p=el())!==u?((b=Ll())===u&&(b=null),b!==u?(Ju=t,v=c,y=b,e={dataType:e,length:parseInt(s.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:y},t=e):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u}else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;var v,y;if(t===u){if(t=Ku,(e=vc())===u&&(e=yc())===u&&(e=hc())===u&&(e=Ec())===u&&(e=mc())===u&&(e=Cc())===u&&(e=Lc())===u&&(e=wc())===u&&(e=Ac()),e!==u){if(n=[],Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe)),o!==u)for(;o!==u;)n.push(o),Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe));else n=u;n!==u&&(o=el())!==u?((a=Ll())===u&&(a=null),a!==u?(Ju=t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,a),t=e):(Ku=t,t=u)):(Ku=t,t=u)}else Ku=t,t=u;t===u&&(t=Ku,(e=vc())===u&&(e=yc())===u&&(e=hc())===u&&(e=Ec())===u&&(e=mc())===u&&(e=Cc())===u&&(e=Lc())===u&&(e=wc())===u&&(e=Ac()),e!==u&&(n=el())!==u?((o=Ll())===u&&(o=null),o!==u&&(a=el())!==u?(Ju=t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(Ku=t,t=u)):(Ku=t,t=u))}return t}())===u&&(t=function(){var t,e,n,o;t=Ku,(e=Tc())===u&&(e=Sc())===u&&(e=Rc())===u&&(e=Ic());if(e!==u)if(el()!==u)if(Wc()!==u)if(el()!==u){if(n=[],Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe)),o!==u)for(;o!==u;)n.push(o),Ge.test(r.charAt(Ku))?(o=r.charAt(Ku),Ku++):(o=u,0===ta&&aa(Fe));else n=u;n!==u&&(o=el())!==u&&Vc()!==u?(Ju=t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Ku=t,t=u)}else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;else Ku=t,t=u;t===u&&(t=Ku,(e=Tc())===u&&(e=Sc())===u&&(e=Rc())===u&&(e=Ic()),e!==u&&(Ju=t,e=Qu(e)),t=e);return t}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"json"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(go)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="JSON"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ku,"jsonb"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(_o)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="JSONB"):(Ku=t,t=u)):(Ku=t,t=u),t}());e!==u&&(Ju=t,e=Qu(e));return t=e}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"geometry"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(jo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="GEOMETRY"):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&(Ju=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"tinytext"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(ko)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TINYTEXT"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ku,"text"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Uo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="TEXT"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ku,"mediumtext"===r.substr(Ku,10).toLowerCase()?(e=r.substr(Ku,10),Ku+=10):(e=u,0===ta&&aa(Mo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="MEDIUMTEXT"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ku,"longtext"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Do)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="LONGTEXT"):(Ku=t,t=u)):(Ku=t,t=u),t}());e!==u&&(Ju=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"uuid"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(Vo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="UUID"):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&(Ju=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"bool"===r.substr(Ku,4).toLowerCase()?(e=r.substr(Ku,4),Ku+=4):(e=u,0===ta&&aa(ho)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="BOOL"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Ku,"boolean"===r.substr(Ku,7).toLowerCase()?(e=r.substr(Ku,7),Ku+=7):(e=u,0===ta&&aa(Eo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="BOOLEAN"):(Ku=t,t=u)):(Ku=t,t=u),t}());e!==u&&(Ju=t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e,n;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"array"===r.substr(Ku,5).toLowerCase()?(e=r.substr(Ku,5),Ku+=5):(e=u,0===ta&&aa(Xo)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ARRAY"):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&Qc()!==u&&(n=Cl())!==u&&Kc()!==u?(Ju=t,t=e={dataType:e,subType:n}):(Ku=t,t=u);return t}())===u&&(t=function(){var r,t,e;r=Ku,(t=gc())!==u&&Qc()!==u&&Cl()!==u&&Yc()!==u&&(e=Cl())!==u&&Kc()!==u?(Ju=r,r=t={dataType:t,subType:e}):(Ku=r,r=u);return r}())===u&&(t=function(){var t,e;t=Ku,(e=function(){var t,e,n,o;return t=Ku,"row"===r.substr(Ku,3).toLowerCase()?(e=r.substr(Ku,3),Ku+=3):(e=u,0===ta&&aa(ar)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ROW"):(Ku=t,t=u)):(Ku=t,t=u),t}())!==u&&(Ju=t,e={dataType:e});return t=e}()),t}function Ll(){var t,e,n;return t=Ku,(e=dc())===u&&(e=null),e!==u&&el()!==u?((n=function(){var t,e,n,o;return t=Ku,"zerofill"===r.substr(Ku,8).toLowerCase()?(e=r.substr(Ku,8),Ku+=8):(e=u,0===ta&&aa(Io)),e!==u?(n=Ku,ta++,o=Vs(),ta--,o===u?n=void 0:(Ku=n,n=u),n!==u?(Ju=t,t=e="ZEROFILL"):(Ku=t,t=u)):(Ku=t,t=u),t}())===u&&(n=null),n!==u?(Ju=t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(Ku=t,t=u)):(Ku=t,t=u),t}const wl={ABS:!0,ALL:!0,ALLOCATE:!0,ALLOW:!0,ALTER:!0,AND:!0,ANY:!0,ARE:!0,ARRAY:!0,ARRAY_MAX_CARDINALITY:!0,AS:!0,ASENSITIVE:!0,ASYMMETRIC:!0,AT:!0,ATOMIC:!0,AUTHORIZATION:!0,AVG:!0,BEGIN:!0,BEGIN_FRAME:!0,BEGIN_PARTITION:!0,BETWEEN:!0,BIGINT:!0,BINARY:!0,BIT:!0,BLOB:!0,BOOLEAN:!0,BOTH:!0,BY:!0,CALL:!0,CALLED:!0,CARDINALITY:!0,CASCADED:!0,CASE:!0,CAST:!0,CEIL:!0,CEILING:!0,CHAR:!0,CHARACTER:!0,CHARACTER_LENGTH:!0,CHAR_LENGTH:!0,CHECK:!0,CLASSIFIER:!0,CLOB:!0,CLOSE:!0,COALESCE:!0,COLLATE:!0,COLLECT:!0,COLUMN:!0,COMMIT:!0,CONDITION:!0,CONNECT:!0,CONSTRAINT:!0,CONTAINS:!0,CONVERT:!0,CORR:!0,CORRESPONDING:!0,COUNT:!0,COVAR_POP:!0,COVAR_SAMP:!0,CREATE:!0,CROSS:!0,CUBE:!0,CUME_DIST:!0,CURRENT:!0,CURRENT_CATALOG:!0,CURRENT_DATE:!0,CURRENT_DEFAULT_TRANSFORM_GROUP:!0,CURRENT_PATH:!0,CURRENT_ROLE:!0,CURRENT_ROW:!0,CURRENT_SCHEMA:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_TRANSFORM_GROUP_FOR_TYPE:!0,CURRENT_USER:!0,CURSOR:!0,CYCLE:!0,DATE:!0,DAY:!0,DEALLOCATE:!0,DEC:!0,DECIMAL:!0,DECLARE:!0,DEFAULT:!0,DEFINE:!0,DELETE:!0,DENSE_RANK:!0,DEREF:!0,DESCRIBE:!0,DETERMINISTIC:!0,DISALLOW:!0,DISCONNECT:!0,DISTINCT:!0,DOUBLE:!0,DROP:!0,DYNAMIC:!0,EACH:!0,ELEMENT:!0,ELSE:!0,EMPTY:!0,END:!0,"END-EXEC":!0,END_FRAME:!0,END_PARTITION:!0,EQUALS:!0,ESCAPE:!0,EVERY:!0,EXCEPT:!0,EXEC:!0,EXECUTE:!0,EXISTS:!0,EXP:!0,EXPLAIN:!0,EXTEND:!0,EXTERNAL:!0,EXTRACT:!0,FALSE:!0,FETCH:!0,FILTER:!0,FIRST_VALUE:!0,FLOAT:!0,FLOOR:!0,FOR:!0,FOREIGN:!0,FRAME_ROW:!0,FREE:!0,FROM:!0,FULL:!0,FUNCTION:!0,FUSION:!0,GET:!0,GLOBAL:!0,GRANT:!0,GROUP:!0,GROUPING:!0,GROUPS:!0,HAVING:!0,HOLD:!0,HOUR:!0,IDENTITY:!0,IMPORT:!0,IN:!0,INDICATOR:!0,INITIAL:!0,INNER:!0,INOUT:!0,INSENSITIVE:!0,INSERT:!0,INT:!0,INTEGER:!0,INTERSECT:!0,INTERSECTION:!0,INTERVAL:!0,INTO:!0,IS:!0,JOIN:!0,JSON_ARRAY:!0,JSON_ARRAYAGG:!0,JSON_EXISTS:!0,JSON_OBJECT:!0,JSON_OBJECTAGG:!0,JSON_QUERY:!0,JSON_VALUE:!0,LAG:!0,LANGUAGE:!0,LARGE:!0,LAST_VALUE:!0,LATERAL:!0,LEAD:!0,LEADING:!0,LEFT:!0,LIKE:!0,LIKE_REGEX:!0,LIMIT:!0,LN:!0,LOCAL:!0,LOCALTIME:!0,LOCALTIMESTAMP:!0,LOWER:!0,MATCH:!0,MATCHES:!0,MATCH_NUMBER:!0,MATCH_RECOGNIZE:!0,MAX:!0,MEASURES:!0,MEMBER:!0,MERGE:!0,METHOD:!0,MIN:!0,MINUS:!0,MINUTE:!0,MOD:!0,MODIFIES:!0,MODULE:!0,MONTH:!0,MULTISET:!0,NATIONAL:!0,NATURAL:!0,NCHAR:!0,NCLOB:!0,NEW:!0,NEXT:!0,NO:!0,NONE:!0,NORMALIZE:!0,NOT:!0,NTH_VALUE:!0,NTILE:!0,NULL:!0,NULLIF:!0,NUMERIC:!0,OCCURRENCES_REGEX:!0,OCTET_LENGTH:!0,OF:!0,OFFSET:!0,OLD:!0,OMIT:!0,ON:!0,ONE:!0,ONLY:!0,OPEN:!0,OR:!0,ORDER:!0,OUT:!0,OUTER:!0,OVER:!0,OVERLAPS:!0,OVERLAY:!0,PARAMETER:!0,PARTITION:!0,PATTERN:!0,PER:!0,PERCENT:!0,PERCENTILE_CONT:!0,PERCENTILE_DISC:!0,PERCENT_RANK:!0,PERIOD:!0,PERMUTE:!0,PORTION:!0,POSITION:!0,POSITION_REGEX:!0,POWER:!0,PRECEDES:!0,PRECISION:!0,PREPARE:!0,PREV:!0,PRIMARY:!0,PROCEDURE:!0,RANGE:!0,RANK:!0,READS:!0,REAL:!0,RECURSIVE:!0,REF:!0,REFERENCES:!0,REFERENCING:!0,REGR_AVGX:!0,REGR_AVGY:!0,REGR_COUNT:!0,REGR_INTERCEPT:!0,REGR_R2:!0,REGR_SLOPE:!0,REGR_SXX:!0,REGR_SXY:!0,REGR_SYY:!0,RELEASE:!0,RESET:!0,RESULT:!0,RETURN:!0,RETURNS:!0,REVOKE:!0,RIGHT:!0,ROLLBACK:!0,ROLLUP:!0,ROW:!0,ROWS:!0,ROW_NUMBER:!0,RUNNING:!0,SAVEPOINT:!0,SCOPE:!0,SCROLL:!0,SEARCH:!0,SECOND:!0,SEEK:!0,SELECT:!0,SENSITIVE:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SIMILAR:!0,SIMILAR:!0,SKIP:!0,SMALLINT:!0,SOME:!0,SPECIFIC:!0,SPECIFICTYPE:!0,SQL:!0,SQLEXCEPTION:!0,SQLSTATE:!0,SQLWARNING:!0,SQRT:!0,START:!0,STATIC:!0,STDDEV_POP:!0,STDDEV_SAMP:!0,STREAM:!0,SUBMULTISET:!0,SUBSET:!0,SUBSTRING:!0,SUBSTRING_REGEX:!0,SUCCEEDS:!0,SUM:!0,SYMMETRIC:!0,SYSTEM:!0,SYSTEM_TIME:!0,SYSTEM_USER:!0,TABLE:!0,TABLESAMPLE:!0,THEN:!0,TO:!0,TIME:!0,TIMESTAMP:!0,TIMEZONE_HOUR:!0,TIMEZONE_MINUTE:!0,TINYINT:!0,TO:!0,TRAILING:!0,TRANSLATE:!0,TRANSLATE_REGEX:!0,TRANSLATION:!0,TREAT:!0,TRIGGER:!0,TRIM:!0,TRIM_ARRAY:!0,TRUE:!0,TRUNCATE:!0,UESCAPE:!0,UNION:!0,UNIQUE:!0,UNKNOWN:!0,UNNEST:!0,UPDATE:!0,UPPER:!0,UPSERT:!0,USER:!0,USING:!0,VALUE:!0,VALUES:!0,VALUE_OF:!0,VARBINARY:!0,VARCHAR:!0,VARYING:!0,VAR_POP:!0,VAR_SAMP:!0,VERSIONING:!0,WHEN:!0,WHENEVER:!0,WHERE:!0,WIDTH_BUCKET:!0,WINDOW:!0,WITH:!0,WITHIN:!0,WITHOUT:!0,YEAR:!0};function Al(){return t.includeLocations?{loc:ua(Ju,Ku)}:{}}function Tl(r,t){return{type:"unary_expr",operator:r,expr:t}}function Sl(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function Rl(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function Il(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function Nl(r,t){let e=r;for(let r=0;r<t.length;r++)e=Sl(t[r][1],e,t[r][3]);return e}function gl(r){const t=Ul[r];return t||(r||null)}function _l(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=gl(r[1])),t.add(r.join("::"))}return Array.from(t)}function jl(r){return"string"==typeof r?{type:"same",value:r}:r}let Ol=[];const xl=new Set,kl=new Set,Ul={};if((e=s())!==u&&Ku===r.length)return e;throw e!==u&&Ku<r.length&&aa({type:"end"}),sa(ra,zu<r.length?r.charAt(zu):null,zu<r.length?ua(zu,zu+1):ua(zu,zu))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Mt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return T})),e.d(n,"autoIncrementToSQL",(function(){return g})),e.d(n,"columnOrderListToSQL",(function(){return _})),e.d(n,"commonKeywordArgsToSQL",(function(){return N})),e.d(n,"commonOptionConnector",(function(){return s})),e.d(n,"connector",(function(){return i})),e.d(n,"commonTypeValue",(function(){return C})),e.d(n,"commentToSQL",(function(){return S})),e.d(n,"createBinaryExpr",(function(){return l})),e.d(n,"createValueExpr",(function(){return c})),e.d(n,"dataTypeToSQL",(function(){return A})),e.d(n,"DEFAULT_OPT",(function(){return u})),e.d(n,"escape",(function(){return f})),e.d(n,"literalToSQL",(function(){return m})),e.d(n,"columnIdentifierToSql",(function(){return y})),e.d(n,"getParserOpt",(function(){return p})),e.d(n,"identifierToSql",(function(){return d})),e.d(n,"onPartitionsToSQL",(function(){return w})),e.d(n,"replaceParams",(function(){return L})),e.d(n,"returningToSQL",(function(){return I})),e.d(n,"hasVal",(function(){return E})),e.d(n,"setParserOpt",(function(){return b})),e.d(n,"toUpper",(function(){return h})),e.d(n,"topToSQL",(function(){return v})),e.d(n,"triggerEventToSQL",(function(){return R}));var u={database:"flinksql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},a=u;function s(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function i(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function c(r){var t=o(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(c)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function l(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:c(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(r){return r}function p(){return a}function b(r){a=r}function v(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(r){var t=p().database;if(r)switch(t&&t.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function d(r,t){var e=p().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(r,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function h(r){if(r)return r.toUpperCase()}function E(r){return r}function m(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,u=r.suffix,a=r.value,s="object"===o(r)?a:r;switch(e){case"backticks_quote_string":s="`".concat(a,"`");break;case"string":s="'".concat(a,"'");break;case"regex_string":s='r"'.concat(a,'"');break;case"hex_string":s="X'".concat(a,"'");break;case"full_hex_string":s="0x".concat(a);break;case"natural_string":s="N'".concat(a,"'");break;case"bit_string":s="b'".concat(a,"'");break;case"double_quote_string":s='"'.concat(a,'"');break;case"single_quote_string":s="'".concat(a,"'");break;case"boolean":case"bool":s=a?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(t||":").concat(a),t=null;break;case"origin":s=a.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(e.toUpperCase()," '").concat(a,"'");break;case"var_string":s="N'".concat(a,"'");break;case"unicode_string":s="U&'".concat(a,"'")}var i=[];return t&&i.push(h(t)),i.push(s),u&&("string"==typeof u&&i.push(u),"object"===o(u)&&(u.collate?i.push(it(u.collate)):i.push(m(u)))),s=i.join(" "),n?"(".concat(s,")"):s}}function C(r){if(!r)return[];var t=r.type,e=r.symbol,n=r.value;return[t.toUpperCase(),e,"string"==typeof n?n.toUpperCase():m(n)].filter(E)}function L(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var u=t[n];if("object"!==o(u)||"param"!==u.type)return r(u,e);if(void 0===e[u.value])throw new Error("no value for parameter :".concat(u.value," found"));return t[n]=c(e[u.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function w(r){var t=r.type,e=r.partitions;return[h(t),"(".concat(e.map((function(r){if("range"!==r.type)return m(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(m(t)," ").concat(h(n)," ").concat(m(e))})).join(", "),")")].join(" ")}function A(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,a="";return null!=e&&(a=o?"".concat(e,", ").concat(o):e),n&&(a="(".concat(a,")")),u&&u.length&&(a+=" ".concat(u.join(" "))),"".concat(t).concat(a)}function T(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=h(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,T(r.field_type)].filter(E).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function S(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(m(o)),t.join(" ")}}function R(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[h(t)];if(e){var o=e.keyword,u=e.columns;n.push(h(o),u.map(yt).join(", "))}return n.join(" ")})).join(" OR ")}function I(r){return r?["RETURNING",r.columns.map(wt).filter(E).join(", ")].join(" "):""}function N(r){return r?[h(r.keyword),h(r.args)]:[]}function g(r){if(r){if("string"==typeof r){var t=p().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,a=h(e);return u&&(a+="(".concat(m(n),", ").concat(m(o),")")),a}}function _(r){if(r)return r.map(mt).filter(E).join(", ")}function j(r){return function(r){if(Array.isArray(r))return O(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return O(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?O(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function x(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),h(e)]}function k(r){if(r){var t=r.type,e=r.expr,n=r.symbol,o=t.toUpperCase(),u=[];switch(u.push(o),o){case"KEY_BLOCK_SIZE":n&&u.push(n),u.push(m(e));break;case"BTREE":case"HASH":u.length=0,u.push.apply(u,j(x(r)));break;case"WITH PARSER":u.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":u.shift(),u.push(S(r));break;case"DATA_COMPRESSION":u.push(n,h(e.value),w(e.on));break;default:u.push(n,m(e))}return u.filter(E).join(" ")}}function U(r){return r?r.map(k):[]}function M(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,o=void 0===n?[]:n,u=r.definition,a=r.on,s=r.with,i=[];if(i.push.apply(i,j(x(e))),u&&u.length){var c="CHECK"===h(t)?"(".concat(ut(u[0]),")"):"(".concat(u.map((function(r){return ut(r)})).join(", "),")");i.push(c)}return i.push(U(o).join(" ")),s&&i.push("WITH (".concat(U(s).join(", "),")")),a&&i.push("ON [".concat(a,"]")),i}function D(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[Array.isArray(r.left)?r.left.map(ut).join(", "):ut(r.left),t,e,h(o.type),ut(o.value)].filter(E).join(" ");return[r.parentheses?"(".concat(u,")"):u].join(" ")}function P(r){return function(r){if(Array.isArray(r))return G(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return G(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function F(r){return r?[r.prefix.map(m).join(" "),ut(r.value),r.suffix.map(m).join(" ")]:[]}function H(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(P(F(n)),P(F(e))).filter(E).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?i("OFFSET",ut(e[0])):i("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(h(t)," ")))}(r):"";var t,e,n}function B(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(yt).join(", "),")"):"";return"".concat("default"===t.type?d(t.value):m(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function Y(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=h(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?d(e):ut(e))}return n.filter(E).join(" ")}}function $(r){var t=r.as_struct_val,e=r.columns,n=r.collate,o=r.distinct,u=r.for,a=r.from,c=r.for_sys_time_as_of,l=void 0===c?{}:c,f=r.locking_read,p=r.groupby,b=r.having,y=r.into,d=void 0===y?{}:y,C=r.isolation,L=r.limit,w=r.options,A=r.orderby,T=r.parentheses_symbol,S=r.qualify,R=r.top,I=r.window,N=r.with,g=r.where,_=[B(N),"SELECT",h(t)];Array.isArray(w)&&_.push(w.join(" ")),_.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[h(t)];return e&&n.push("(".concat(e.map(ut).join(", "),")")),n.filter(E).join(" ")}}(o),v(R),Tt(e,a));var j=d.position,O="";j&&(O=s("INTO",Y,d)),"column"===j&&_.push(O),_.push(s("FROM",cr,a)),"from"===j&&_.push(O);var x=l||{},k=x.keyword,U=x.expr;_.push(s(k,ut,U)),_.push(s("WHERE",ut,g)),p&&(_.push(i("GROUP BY",at(p.columns).join(", "))),_.push(at(p.modifiers).join(", "))),_.push(s("HAVING",ut,b)),_.push(s("QUALIFY",ut,S)),_.push(s("WINDOW",ut,I)),_.push(st(A,"order by")),_.push(it(n)),_.push(H(L)),C&&_.push(s(C.keyword,m,C.expr)),_.push(h(f)),"end"===j&&_.push(O),_.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[h(r.type),h(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(u));var M=_.filter(E).join(" ");return T?"(".concat(M,")"):M}function W(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return V(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?V(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function V(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function X(r){if(!r||0===r.length)return"";var t,e=[],n=W(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u={},a=o.value;for(var s in o)"value"!==s&&"keyword"!==s&&(u[s]=o[s]);var i=[yt(u)],c="";a&&(c=ut(a),i.push("=",c)),e.push(i.filter(E).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function q(r){if("select"===r.type)return $(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function Q(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(d).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(E).join("")}function K(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(yt).join(", "),")")}}function J(r){var t=r.expr,e=r.keyword,n=t.type,o=[h(e)];switch(n){case"origin":o.push(m(t));break;case"update":o.push("UPDATE",s("SET",X,t.set),s("WHERE",ut,t.where))}return o.filter(E).join(" ")}function Z(r){if(!r)return"";var t=r.action;return[K(r.target),J(t)].filter(E).join(" ")}function z(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,a=r.conflict,i=r.values,c=r.where,l=r.on_duplicate_update,f=r.partition,p=r.returning,b=r.set,v=l||{},y=v.keyword,d=v.set,C=[h(e),h(o),cr(t),Q(f)];return Array.isArray(u)&&C.push("(".concat(u.map(m).join(", "),")")),C.push(s(Array.isArray(i)?"VALUES":"",q,i)),C.push(s("ON CONFLICT",Z,a)),C.push(s("SET",X,b)),C.push(s("WHERE",ut,c)),C.push(s(y,X,d)),C.push(I(p)),C.filter(E).join(" ")}function rr(r){var t=r.expr,e=r.unit,n=r.suffix;return["INTERVAL",ut(t),h(e),ut(n)].filter(E).join(" ")}function tr(r){return function(r){if(Array.isArray(r))return er(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return er(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?er(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function nr(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(h(t),"(").concat(n&&ut(n)||"",")"),s("AS","string"==typeof e?d:ut,e),s(h(o&&o.keyword),d,o&&o.as)].filter(E).join(" ")}function or(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,a=[ut(n),"FOR",yt(e),D(o)],s=["".concat(h(u),"(").concat(a.join(" "),")")];return t&&s.push("AS",d(t)),s.join(" ")}(r);default:return""}}function ur(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,a=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(h(t),"(".concat(d(n)),"(".concat(o.map(ut).filter(E).join(", "),"))"));break;case"spatial_window_max_cells":s.push(h(t),"=",ut(e));break;case"index":s.push(h(a),h(t),u?"(".concat(e.map(d).join(", "),")"):"= ".concat(d(e)));break;default:s.push(ut(e))}return s.filter(E).join(" ")}}function ar(r,t){var e=r.name,n=r.symbol;return[h(e),n,t].filter(E).join(" ")}function sr(r){var t=[];switch(r.keyword){case"as":t.push("AS","OF",ut(r.of));break;case"from_to":t.push("FROM",ut(r.from),"TO",ut(r.to));break;case"between_and":t.push("BETWEEN",ut(r.between),"AND",ut(r.and));break;case"contained":t.push("CONTAINED","IN",ut(r.in))}return t.filter(E).join(" ")}function ir(r){if("UNNEST"===h(r.type))return nr(r);var t,e,n,o,u=r.table,a=r.db,i=r.as,c=r.expr,l=r.operator,f=r.prefix,p=r.schema,b=r.server,v=r.suffix,y=r.tablesample,L=r.temporal_table,w=r.table_hint,A=d(b),T=d(a),S=d(p),R=u&&d(u);if(c)switch(c.type){case"values":var I=c.parentheses,N=c.values,g=c.prefix,_=[I&&"(","",I&&")"],j=q(N);g&&(j=j.split("(").slice(1).map((function(r){return"".concat(h(g),"(").concat(r)})).join("")),_[1]="VALUES ".concat(j),R=_.filter(E).join("");break;case"tumble":R=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.offset,o=r.size,u=[d(t.expr.db),d(t.expr.schema),d(t.expr.table)].filter(E).join("."),a="DESCRIPTOR(".concat(yt(e.expr),")"),s=["TABLE(TUMBLE(TABLE ".concat(ar(t,u)),ar(e,a)],i=ar(o,rr(o.expr));return n&&n.expr?s.push(i,"".concat(ar(n,rr(n.expr)),"))")):s.push("".concat(i,"))")),s.filter(E).join(", ")}(c);break;case"generator":e=(t=c).keyword,n=t.type,o=t.generators.map((function(r){return C(r).join(" ")})).join(", "),R="".concat(h(e),"(").concat(h(n),"(").concat(o,"))");break;default:R=ut(c)}var O=[[A,T,S,R=[h(f),R,h(v)].filter(E).join(" ")].filter(E).join(".")];if(y){var x=["TABLESAMPLE",ut(y.expr),m(y.repeatable)].filter(E).join(" ");O.push(x)}O.push(function(r){if(r){var t=r.keyword,e=r.expr;return[h(t),sr(e)].filter(E).join(" ")}}(L),s("AS","string"==typeof i?d:ut,i),or(l)),w&&O.push(h(w.keyword),"(".concat(w.expr.map(ur).filter(E).join(", "),")"));var k=O.filter(E).join(" ");return r.parentheses?"(".concat(k,")"):k}function cr(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=r.joins,o=cr(t);if(e){for(var u=[],a=[],i=!0===e?1:e.length,c=0;c++<i;)u.push("("),a.push(")");var l=n&&n.length>0?cr([""].concat(tr(n))):"";return u.join("")+o+a.join("")+l}return o}var f=r[0],p=[];if("dual"===f.type)return"DUAL";p.push(ir(f));for(var b=1;b<r.length;++b){var v=r[b],y=v.on,d=v.using,C=v.join,L=[];L.push(C?" ".concat(h(C)):","),L.push(ir(v)),L.push(s("ON",ut,y)),d&&L.push("USING (".concat(d.map(m).join(", "),")")),p.push(L.filter(E).join(" "))}return p.filter(E).join("")}function lr(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=m(n);switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.filter(E).join(" ")}function fr(r){var t=r.name,e=r.type;switch(e){case"table":case"view":var n=[d(t.db),d(t.table)].filter(E).join(".");return"".concat(h(e)," ").concat(n);case"column":return"COLUMN ".concat(yt(t));default:return"".concat(h(e)," ").concat(m(t))}}function pr(r){var t=r.keyword,e=r.expr;return[h(t),m(e)].filter(E).join(" ")}function br(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(E).join(" ")}function vr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),a=ut(e);return[u,h(n),a].filter(E).join(" ")}function yr(r){var t,e,n,o,u=r.keyword,a=r.suffix,i="";switch(h(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,i=[s("IN",m,e&&e.right),s("FROM",cr,n),H(o)].filter(E).join(" ");break;case"CHARACTER":case"COLLATION":i=function(r){var t=r.expr;if(t)return"LIKE"===h(t.op)?s("LIKE",m,t.right):s("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":case"INDEX":i=s("FROM",cr,r.from);break;case"GRANTS":i=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(E).join(" ")}}(r);break;case"CREATE":i=s("",ir,r[a]);break;case"VAR":i=ot(r.var),u=""}return["SHOW",h(u),h(a),i].filter(E).join(" ")}var dr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,a=t.expr,s=t.orderby;return[h(u),h(n),[[d(o.schema),d(o.name)].filter(E).join("."),"(".concat(a.map(Zr).join(", ")).concat(s?[" ORDER","BY",s.map(Zr).join(", ")].join(" "):"",")")].filter(E).join(""),Jr(e)].filter(E).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.if_exists,o=r.prefix,u=r.expr,a=void 0===u?[]:u,s=h(t),i=cr(e),c=a.map(ut);return[s,"TABLE",h(n),m(o),i,c.join(", ")].filter(E).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[h(r.type),h(e),d(n),Jr(t)].filter(E).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[h(r.type),h(e),[d(n.schema),d(n.name)].filter(E).join("."),Jr(t)].filter(E).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[h(r.type),h(n),[[d(o.schema),d(o.name)].filter(E).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(E).join(""),Jr(e)].filter(E).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,a=r.with,s=h(t),i=ir(u),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(yt).join(", "),")"));n&&c.push("WITH ".concat(n.map(h).join(", ")));c.push("AS",$(o)),a&&c.push(h(a));return c.filter(E).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[h(t),ir(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[h(t),h(e),ut(n),h(o),d(u)].filter(E).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.options,s=[h(t),h(e),h(n)],i=[d(o.schema),o.name].filter(E).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(a.map(Qr).join(", "),")")),s.filter(E).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,a=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.replace,b=r.partition_of,v=r.query_expr,y=r.unlogged,d=r.with,C=[h(t),h(f),h(a),h(y),h(e),h(s),cr(n)];if(o){var L=o.type,w=cr(o.table);return C.push(h(L),w),C.filter(E).join(" ")}if(b)return C.concat([Vr(b)]).filter(E).join(" ");i&&C.push("(".concat(i.map($r).join(", "),")"));if(c){var A=p().database,T=A&&"sqlite"===A.toLowerCase()?", ":" ";C.push(c.map(lr).join(T))}if(d){var S=d.map((function(r){return[m(r.keyword),h(r.symbol),m(r.value)].join(" ")})).join(", ");C.push("WITH (".concat(S,")"))}C.push(h(l),h(u)),v&&C.push(hr(v));return C.filter(E).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,a=r.for_each,s=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[h(f),h(l),h(e),h(c),d(t),h(i)],y=R(o);v.push(y,"ON",ir(p)),s&&v.push("FROM",ir(s));v.push.apply(v,Hr(N(n)).concat(Hr(N(a)))),b&&v.push(h(b.type),ut(b.cond));return v.push(h(u.keyword),Fr(u.expr)),v.filter(E).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,a=r.table,i=r.if_not_exists,c=r.temporary,l=r.trigger,f=r.events,p=r.order,b=r.time,v=r.when,y=[h(u),h(c),ut(t),h(n),h(i),ir(l),h(b),f.map((function(r){var t=[h(r.keyword)],e=r.args;return e&&t.push(h(e.keyword),e.columns.map(yt).join(", ")),t.join(" ")})),"ON",ir(a),h(e&&e.keyword),h(e&&e.args),p&&"".concat(h(p.keyword)," ").concat(d(p.trigger)),s("WHEN",ut,v),h(o.prefix)];switch(o.type){case"set":y.push(s("SET",X,o.expr));break;case"multiple":y.push(Er(o.expr.ast))}return y.push(h(o.suffix)),y.filter(E).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,a=r.type,i=r.with,c=r.version;return[h(a),h(o),h(n),m(t),h(i),s("SCHEMA",m,u),s("VERSION",m,c),s("FROM",m,e)].filter(E).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.returns,s=r.options,i=r.last,c=[h(t),h(e),h(n)],l=[m(o.schema),o.name.map(m).join(".")].filter(E).join("."),f=u.map(Zr).filter(E).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),Array.isArray(n)?"(".concat(n.map(Ct).join(", "),")"):Xr(n)].filter(E).join(" ")}(a),s.map(qr).join(" "),i),c.filter(E).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.if_not_exists,u=r.include,a=r.index_columns,i=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,y=r.on_kw,C=r.table,L=r.tablespace,w=r.type,A=r.where,T=r.with,S=r.with_before_where,R=T&&"WITH (".concat(U(T).join(", "),")"),I=u&&"".concat(h(u.keyword)," (").concat(u.columns.map((function(r){return"string"==typeof r?d(r):ut(r)})).join(", "),")"),N=l;l&&(N="string"==typeof l?d(l):[d(l.schema),d(l.name)].filter(E).join("."));var g=[h(w),h(i),h(n),h(o),h(t),N,h(y),ir(C)].concat(Hr(x(c)),["(".concat(_(a),")"),I,U(p).join(" "),Jr(b),Jr(v),s("TABLESPACE",m,L)]);S?g.push(R,s("WHERE",ut,A)):g.push(s("WHERE",ut,A),R);return g.push(s("ON",ut,f),s("FILESTREAM_ON",m,e)),g.filter(E).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,a=r.create_definitions,s=[h(t),h(o),h(e),h(u),cr(n)];a&&s.push(a.map($r).join(" "));return s.filter(E).join(" ")}(r);break;case"database":case"schema":e=function(r){var t=r.type,e=r.keyword,n=r.replace,o=r.if_not_exists,u=r.create_definitions,a=r[e],s=a.db,i=a.schema,c=[m(s),i.map(m).join(".")].filter(E).join("."),l=[h(t),h(n),h(e),h(o),c];u&&l.push(u.map(lr).join(" "));return l.filter(E).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,a=r.recursive,s=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,m=p.db,L=p.schema,w=p.view,A=[d(m),d(L),d(w)].filter(E).join(".");return[h(f),h(s),h(l),h(a),t&&"ALGORITHM = ".concat(h(t)),ut(n),c&&"SQL SECURITY ".concat(h(c)),h(u),h(o),A,e&&"(".concat(e.map(y).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return C(r).join(" ")})).join(", "),")")].join(" "),"AS",hr(i),h(b)].filter(E).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,a=r.create_definitions,s=[h(n),h(o),[d(e.schema),d(e.name)].filter(E).join("."),h(t),A(u)];if(a&&a.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Br(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(a);try{for(l.s();!(i=l.n()).done;){var f=i.value,p=f.type;switch(p){case"collate":c.push(ut(f));break;case"default":c.push(h(p),ut(f.value));break;case"constraint":c.push(xr(f))}}}catch(r){l.e(r)}finally{l.f()}s.push(c.filter(E).join(" "))}return s.filter(E).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,a=[h(r.type),h(n),[d(o.schema),d(o.name)].filter(E).join("."),h(t),h(u)];if(e){var s=[];switch(u){case"enum":case"range":s.push(ut(e));break;default:s.push("(".concat(e.map($r).join(", "),")"))}a.push(s.filter(E).join(" "))}return a.filter(E).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,a=r.lock_option,i=r.password_options,c=r.require,l=r.resource_options,f=r.type,p=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[gr(t)];return e&&n.push(h(e.keyword),e.auth_plugin,m(e.value)),n.filter(E).join(" ")})).join(", "),b=[h(f),h(u),h(o),p];n&&b.push(h(n.keyword),n.value.map(gr).join(", "));b.push(s(c&&c.keyword,ut,c&&c.value)),l&&b.push(h(l.keyword),l.value.map((function(r){return ut(r)})).join(" "));i&&i.forEach((function(r){return b.push(s(r.keyword,ut,r.value))}));return b.push(m(a),S(e),m(t)),b.filter(E).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},comment:function(r){var t=r.expr,e=r.keyword,n=r.target;return[h(r.type),h(e),fr(n),pr(t)].filter(E).join(" ")},select:$,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[h(t),h(e),ut(n)].filter(E).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,a=r.with,i=r.limit,c=r.returning,l=[B(a),"DELETE"],f=Tt(t,e);return l.push(f),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||l.push(cr(n))),l.push(s("FROM",cr,e)),l.push(s("WHERE",ut,o)),l.push(st(u,"order by")),l.push(H(i)),l.push(I(c)),l.filter(E).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[h(t),ir(e),(n||[]).map(br).filter(E).join(", ")].filter(E).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[h(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(E).join(" ")},explain:function(r){var t=r.type,e=r.expr;return[h(t),$(e)].join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,h(t),n,"IN",Er([o]),"LOOP",Er(u),"END LOOP",e].filter(E).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,a=r.with,i=r.limit,c=r.returning;return[B(a),"UPDATE",cr(e),s("SET",X,n),s("FROM",cr,t),s("WHERE",ut,o),st(u,"order by"),H(i),I(c)].filter(E).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,a=r.go,s=r.semicolons,i=r.suffix,c=[h(r.type),ut(t),m(u),"".concat(wr(o.ast||o)).concat(s[0]),h(a)];n&&c.push(n.map((function(r){return[h(r.type),ut(r.boolean_expr),"THEN",wr(r.then.ast||r.then),r.semicolon].filter(E).join(" ")})).join(" "));e&&c.push("ELSE","".concat(wr(e.ast||e)).concat(s[1]));return c.push(m(i)),c.filter(E).join(" ")},insert:z,drop:Ir,truncate:Ir,replace:z,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[h(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,a=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(E).join(""),h(n),h(o)];switch(c){case"variable":l.push(dt(u),ut(r.collate),h(a)),i&&l.push(h(i.keyword),ut(i.value));break;case"cursor":l.push(h(s));break;case"table":l.push(h(s),"(".concat(i.map($r).join(", "),")"))}return l.filter(E).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=h(t),o=d(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,a=Ar(e);try{for(a.s();!(u=a.n()).done;){var s=u.value.map(ir);n.push(s.join(" TO "))}}catch(r){a.e(r)}finally{a.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=h(t);return"".concat(n," ").concat(d(e))},set:function(r){var t=r.type,e=r.expr,n=r.keyword,o=h(t),u=e.map(ut).join(", ");return[o,h(n),u].filter(E).join(" ")},lock:Nr,unlock:Nr,show:yr,grant:_r,revoke:_r,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return vr(t);case"return":return function(r){var t=r.type,e=r.expr;return[h(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[h(t),h(e)];n&&u.push([m(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(E).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(h(o.type),h(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(E).join(" ")},transaction:function(r){var t=r.expr,e=t.action,n=t.keyword,o=t.modes,u=[m(e),h(n)];return o&&u.push(o.map(m).join(", ")),u.filter(E).join(" ")}};function hr(r){if(!r)return"";for(var t=dr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,a=[n&&"(",t(r)];r._next;){var s=dr[r._next.type],i=h(r.set_op);a.push(i,s(r._next)),r=r._next}return a.push(n&&")",st(o,"order by"),H(u)),a.filter(E).join(" ")}function Er(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=hr(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var mr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function Cr(r){var t=r&&r.ast?r.ast:r;if(!mr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Lr(r){return Array.isArray(r)?(r.forEach(Cr),Er(r)):(Cr(r),hr(r))}function wr(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Lr(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Lr(r)}function Ar(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Sr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Tr(r){return function(r){if(Array.isArray(r))return Rr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Sr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sr(r,t){if(r){if("string"==typeof r)return Rr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Rr(r,t):void 0}}function Rr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Ir(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=r.suffix,a=[h(t),h(e),h(o)];switch(e){case"table":a.push(cr(n));break;case"trigger":a.push([n[0].schema?"".concat(d(n[0].schema),"."):"",d(n[0].trigger)].filter(E).join(""));break;case"database":case"schema":case"procedure":a.push(d(n));break;case"view":a.push(cr(n),r.options&&r.options.map(ut).filter(E).join(" "));break;case"index":a.push.apply(a,[yt(n)].concat(Tr(r.table?["ON",ir(r.table)]:[]),[r.options&&r.options.map(ut).filter(E).join(" ")]));break;case"type":a.push(n.map(yt).join(", "),r.options&&r.options.map(ut).filter(E).join(" "))}return u&&a.push(u.map(ut).filter(E).join(" ")),a.filter(E).join(" ")}function Nr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),h(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,a=[],s=Ar(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[ir(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return h(e[r])})).filter(E).join(" "))}a.push(n.join(" "))};for(s.s();!(u=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[a.join(", ")].concat(Tr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(E).join(" ")}function gr(r){var t=r.name,e=r.host,n=[m(t)];return e&&n.push("@",m(e)),n.join("")}function _r(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,a=r.to_from,s=r.user_or_roles,i=r.with,c=[h(t),m(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(yt).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(m(u.object_type),u.priv_level.map((function(r){return[d(r.prefix),d(r.name)].filter(E).join(".")})).join(", "));break;case"proxy":c.push(gr(u))}return c.push(h(a),s.map(gr).join(", ")),c.push(m(i)),c.filter(E).join(" ")}function jr(r){return function(r){if(Array.isArray(r))return Or(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(r){if("string"==typeof r)return Or(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Or(r,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Or(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function xr(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,a=r.reference_definition,i=r.for,c=r.with_values,l=[],f=p().database;l.push(h(u)),l.push(d(t));var b=h(e);return"sqlite"===f.toLowerCase()&&"UNIQUE KEY"===b&&(b="UNIQUE"),l.push(b),l.push("sqlite"!==f.toLowerCase()&&d(o)),l.push.apply(l,jr(M(r))),l.push.apply(l,jr(ht(a))),l.push(h(n)),l.push(s("FOR",d,i)),l.push(m(c)),l.filter(E).join(" ")}}function kr(r){if(r){var t=r.type;return"rows"===t?[h(t),ut(r.expr)].filter(E).join(" "):ut(r)}}function Ur(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),kr(o)].filter(E).join(" ")}(t),")")}function Mr(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(Ur(e))}function Dr(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,a=h(o);if("WINDOW"===a)return"OVER ".concat(Ur(t));if("ON UPDATE"===a){var s="".concat(h(o)," ").concat(h(n)),i=ut(e)||[];return u&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function Pr(r){if(!r||!r.array)return"";var t=r.array.keyword;if(t)return h(t);for(var e=r.array,n=e.dimension,o=e.length,u=[],a=0;a<n;a++)u.push("["),o&&o[a]&&u.push(m(o[a])),u.push("]");return u.join("")}function Gr(r){for(var t=r.target,e=r.expr,n=r.keyword,o=r.symbol,u=r.as,a=r.offset,s=r.parentheses,i=bt({expr:e,offset:a}),c=[],l=0,f=t.length;l<f;++l){var p=t[l],b=p.angle_brackets,v=p.length,y=p.dataType,C=p.parentheses,L=p.quoted,w=p.scale,A=p.suffix,T=p.expr,S=T?ut(T):"";null!=v&&(S=w?"".concat(v,", ").concat(w):v),C&&(S="(".concat(S,")")),b&&(S="<".concat(S,">")),A&&A.length&&(S+=" ".concat(A.map(m).join(" ")));var R="::",I="",N=[];"as"===o&&(0===l&&(i="".concat(h(n),"(").concat(i)),I=")",R=" ".concat(o.toUpperCase()," ")),0===l&&N.push(i);var g=Pr(p);N.push(R,L,y,L,g,S,I),c.push(N.filter(E).join(""))}u&&c.push(" AS ".concat(d(u)));var _=c.filter(E).join("");return s?"(".concat(_,")"):_}function Fr(r){var t=r.args,e=r.array_index,n=r.name,o=r.args_parentheses,u=r.parentheses,a=r.within_group,s=r.over,i=r.suffix,c=Dr(s),l=function(r){if(!r)return"";var t=r.type,e=r.keyword,n=r.orderby;return[h(t),h(e),"(".concat(st(n,"order by"),")")].filter(E).join(" ")}(a),f=ut(i),p=[m(n.schema),n.name.map(m).join(".")].filter(E).join(".");if(!t)return[p,l,c].filter(E).join(" ");var b=r.separator||", ";"TRIM"===h(p)&&(b=" ");var v=[p];v.push(!1===o?" ":"(");var y=ut(t);if(Array.isArray(b)){for(var d=y[0],C=1,L=y.length;C<L;++C)d=[d,y[C]].join(" ".concat(ut(b[C-1])," "));v.push(d)}else v.push(y.join(b));return!1!==o&&v.push(")"),v.push(vt(e)),v=[v.join(""),f].filter(E).join(" "),[u?"(".concat(v,")"):v,l,c].filter(E).join(" ")}function Hr(r){return function(r){if(Array.isArray(r))return Yr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Br(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Br(r,t){if(r){if("string"==typeof r)return Yr(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Yr(r,t):void 0}}function Yr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function $r(r){if(!r)return[];var t,e,n,o,u=r.resource;switch(u){case"column":return Ct(r);case"index":return e=[],n=(t=r).keyword,o=t.index,e.push(h(n)),e.push(o),e.push.apply(e,j(M(t))),e.filter(E).join(" ");case"constraint":return xr(r);case"sequence":return[h(r.prefix),ut(r.value)].filter(E).join(" ");default:throw new Error("unknown resource = ".concat(u," type"))}}function Wr(r){var t=[];switch(r.keyword){case"from":t.push("FROM","(".concat(m(r.from),")"),"TO","(".concat(m(r.to),")"));break;case"in":t.push("IN","(".concat(ut(r.in),")"));break;case"with":t.push("WITH","(MODULUS ".concat(m(r.modulus),", REMAINDER ").concat(m(r.remainder),")"))}return t.filter(E).join(" ")}function Vr(r){var t=r.keyword,e=r.table,n=r.for_values,o=r.tablespace,u=[h(t),ir(e),h(n.keyword),Wr(n.expr)];return o&&u.push("TABLESPACE",m(o)),u.filter(E).join(" ")}function Xr(r){return r.dataType?A(r):[d(r.db),d(r.schema),d(r.table)].filter(E).join(".")}function qr(r){var t=r.type;switch(t){case"as":return[h(t),r.symbol,hr(r.declare),h(r.begin),Er(r.expr),h(r.end),r.symbol].filter(E).join(" ");case"set":return[h(t),r.parameter,h(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(E).join(" ");case"return":return[h(t),ut(r.expr)].filter(E).join(" ");default:return ut(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[h(t),e];switch(h(t)){case"SFUNC":o.push([d(n.schema),n.name].filter(E).join("."));break;case"STYPE":case"MSTYPE":o.push(A(n));break;default:o.push(ut(n))}return o.filter(E).join(" ")}function Kr(r,t){switch(r){case"add":var e=t.map((function(r){var t=r.name,e=r.value;return["PARTITION",m(t),"VALUES",h(e.type),"(".concat(m(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Tt(t)}}function Jr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.if_not_exists,o=r.keyword,u=r.if_exists,a=r.old_column,s=r.prefix,i=r.resource,c=r.symbol,l=r.suffix,f="",p=[];switch(i){case"column":p=[Ct(r)];break;case"index":p=M(r),f=r[i];break;case"table":case"schema":f=d(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=d(r[i]);break;case"algorithm":case"lock":case"table-option":f=[c,h(r[i])].filter(E).join(" ");break;case"constraint":f=d(r[i]),p=[$r(e)];break;case"partition":p=[Kr(t,r.partitions)];break;case"key":f=d(r[i]);break;default:f=[c,r[i]].filter((function(r){return null!==r})).join(" ")}var b=[h(t),h(o),h(n),h(u),a&&yt(a),h(s),f&&f.trim(),p.filter(E).join(" ")];return l&&b.push(h(l.keyword),l.expr&&yt(l.expr)),b.filter(E).join(" ")}function Zr(r){var t=r.default&&[h(r.default.keyword),ut(r.default.value)].join(" ");return[h(r.mode),r.name,A(r.type),t].filter(E).join(" ")}function zr(r){return(zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(h(r.type)){case"STRUCT":return"(".concat(Tt(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(Tt(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(Tt(r),")")})).filter(E).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[h(r.keyword)];return t&&"object"===zr(t)&&(e.length=0,e.push(T(t))),e.push(rt(r)),e.filter(E).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:Jr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr);u=Array.isArray(u)?u.join(", "):u;var a=r.name,s=Dr(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.separator&&t.separator.delimiter&&(u=[u,m(t.separator.delimiter)].join("".concat(t.separator.symbol," "))),t.separator&&t.separator.expr&&(u=[u,ut(t.separator.expr)].join(" ")),t.orderby&&(u=[u,st(t.orderby,"order by")].join(" ")),t.separator&&t.separator.value&&(u=[u,h(t.separator.keyword),m(t.separator.value)].filter(E).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(a,"(").concat(u,")"),i,s,c].filter(E).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,a="".concat(h(e),"(").concat(ut(o));return u&&(a="".concat(a," HAVING ").concat(h(u.prefix)," ").concat(ut(u.expr))),[a="".concat(a,")"),Dr(n)].filter(E).join(" ")},window_func:function(r){var t=r.over;return[function(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=r.separator,a=void 0===u?", ":u;return[e,"(",t?ut(t).join(a):"",")",o&&" ",o].filter(E).join("")}(r),Dr(t)].filter(E).join(" ")},array:tt,assign:vr,binary_expr:D,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,a=e.length;u<a;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:Gr,collate:it,column_ref:yt,column_definition:Ct,datatype:A,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,a=["".concat(h(e),"(").concat(h(n)),"FROM",h(o),ut(u)];return"".concat(a.filter(E).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[h(t),e,ut(n)].filter(E).join(" ")}(t[r])})).filter(E).join(", ");return"".concat(h(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode,a=[h(o),"(".concat(n.map((function(r){return yt(r)})).join(", "),")")].join(" "),s=[h(t),["(",ut(r.expr),u&&" ".concat(m(u)),")"].filter(E).join("")].join(" ");return[a,s,Lt(e)].filter(E).join(" ")},function:Fr,lambda:function(r){var t=r.args,e=r.expr,n=t.value,o=t.parentheses,u=n.map(ut).join(", ");return[o?"(".concat(u,")"):u,"->",ut(e)].join(" ")},insert:hr,interval:rr,json:function(r){var t=r.keyword,e=r.expr_list;return[h(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},json_object_arg:function(r){var t=r.expr,e=t.key,n=t.value,o=t.on,u=[ut(e),"VALUE",ut(n)];return o&&u.push("ON","NULL",ut(o)),u.filter(E).join(" ")},json_visitor:function(r){return[r.symbol,ut(r.expr)].join("")},func_arg:function(r){var t=r.value;return[t.name,t.symbol,ut(t.expr)].filter(E).join(" ")},show:yr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args,o=[m(e.schema),e.name.map(m).join(".")].filter(E).join(".");return["".concat(o,"(").concat(ut(n).join(", "),")"),"AS",Fr(t)].join(" ")},tables:cr,unnest:nr,window:function(r){return r.expr.map(Mr).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.quoted,a=r.suffix,s=[],i=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,c="".concat(e||"").concat(i);return a&&(c+=a),s.push(c),[u,s.join(" "),u].filter(E).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}var a=t.type;return"expr"===a?ut(t.expr):nt[a]?nt[a](t):m(t)}}function at(r){return r?(Array.isArray(r)||(r=[r]),r.map(ut)):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=h(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",h(r.nulls)].filter(E).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return i(n,e.join(", "))}function it(r){if(r){var t=r.keyword,e=r.collate,n=e.name,o=e.symbol,u=e.value,a=[h(t)];return u||a.push(o),a.push(Array.isArray(n)?n.map(m).join("."):m(n)),u&&a.push(o),a.push(ut(u)),a.filter(E).join(" ")}}function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function lt(r){return function(r){if(Array.isArray(r))return pt(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||ft(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(r,t){if(r){if("string"==typeof r)return pt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?pt(r,t):void 0}}function pt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function bt(r,t){if("string"==typeof r)return d(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),m(r.value),"".concat(r.name?")":""),"]"].filter(E).join("")})).join("");return[ut(e),u,o].filter(E).join("")}function vt(r){if(!r||0===r.length)return"";var t,e=[],n=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=ft(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.brackets?"[".concat(m(o.index),"]"):"".concat(o.notation).concat(m(o.index));o.property&&(u="".concat(u,".").concat(m(o.property))),e.push(u)}}catch(r){n.e(r)}finally{n.f()}return e.join("")}function yt(r){var t=r.array_index,e=r.as,n=r.column,o=r.collate,u=r.db,a=r.isDual,i=r.notations,c=void 0===i?[]:i,l=r.options,f=r.schema,p=r.table,b=r.parentheses,v=r.suffix,y=r.order_by,m=r.subFields,C=void 0===m?[]:m,L="*"===n?"*":bt(n,a),w=[u,f,p].filter(E).map((function(r){return"".concat("string"==typeof r?d(r):ut(r))})),A=w[0];if(A){for(var T=1;T<w.length;++T)A="".concat(A).concat(c[T]||".").concat(w[T]);L="".concat(A).concat(c[T]||".").concat(L)}var S=[L=["".concat(L).concat(vt(t))].concat(lt(C)).join("."),it(o),ut(l),s("AS",ut,e)];S.push("string"==typeof v?h(v):ut(v)),S.push(h(y));var R=S.filter(E).join(" ");return b?"(".concat(R,")"):R}function dt(r){if(r){var t=r.dataType,e=r.length,n=r.suffix,o=r.scale,u=r.expr,a=A({dataType:t,length:e,suffix:n,scale:o,parentheses:null!=e});if(u&&(a+=ut(u)),r.array){var s=Pr(r);a+=[/^\[.*\]$/.test(s)?"":" ",s].join("")}return a}}function ht(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,a=r.on_action;return t.push(h(n)),t.push(cr(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(h(o)),a.map((function(r){return t.push(h(r.type),ut(r.value))})),t.filter(E)}function Et(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,a=r.constraint,i=r.collate,c=r.storage,l=r.using,f=r.default_val,b=r.generated,v=r.auto_increment,y=r.unique,d=r.primary_key,L=r.column_format,w=r.reference_definition,A=[h(e&&e.action),h(e&&e.value)].filter(E).join(" ");if(b||t.push(A),f){var T=f.type,R=f.value;t.push(T.toUpperCase(),ut(R))}var I=p().database;return a&&t.push(h(a.keyword),m(a.constraint)),t.push(xr(o)),t.push(function(r){if(r)return[h(r.value),"(".concat(ut(r.expr),")"),h(r.storage_type)].filter(E).join(" ")}(b)),b&&t.push(A),t.push(g(v),h(d),h(y),S(u)),t.push.apply(t,lt(C(n))),"sqlite"!==I.toLowerCase()&&t.push(ut(i)),t.push.apply(t,lt(C(L))),t.push.apply(t,lt(C(c))),t.push.apply(t,lt(ht(w))),t.push(s("USING",ut,l)),t.filter(E).join(" ")}function mt(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by,a="string"==typeof t?{type:"column_ref",table:r.table,column:t}:r;return a.collate=null,[ut(a),ut(e),o,h(u),h(n)].filter(E).join(" ")}function Ct(r){var t=[],e=yt(r.column),n=dt(r.definition);return t.push(e),t.push(n),t.push(Et(r)),t.filter(E).join(" ")}function Lt(r){return r?"object"===ct(r)?["AS",ut(r)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?d(r):y(r)].join(" "):""}function wt(r,t){var e=r.expr,n=r.type;if("cast"===n)return Gr(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var a=[o],s=u.map((function(r){return wt(r,t)})).join(", ");return a.push([h(n),n&&"(",s,n&&")"].filter(E).join("")),a.filter(E).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(vt(e.array_index))),[o,Lt(r.as)].filter(E).join(" ")}function At(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}function Tt(r,t){if(!r||"*"===r)return r;var e=At(t);return r.map((function(r){return wt(r,e)})).join(", ")}nt.var=ot,nt.expr_list=function(r){var t=at(r.value),e=r.parentheses,n=r.separator;if(!e&&!n)return t;var o=n||", ",u=t.join(o);return e?"(".concat(u,")"):u},nt.select=function(r){var t="object"===et(r._next)?hr(r):$(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u},nt.map_object=function(r){var t=r.keyword,e=r.expr.map((function(r){return[m(r.key),m(r.value)].join(", ")})).join(", ");return[h(t),"[".concat(e,"]")].join("")};var St=e(0);function Rt(r){return(Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var It,Nt,gt,_t=(It={},Nt="flinksql",gt=St.parse,(Nt=function(r){var t=function(r,t){if("object"!=Rt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==Rt(t)?t:t+""}(Nt))in It?Object.defineProperty(It,Nt,{value:gt,enumerable:!0,configurable:!0,writable:!0}):It[Nt]=gt,It);function jt(r){return(jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Ot(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(r){if("string"==typeof r)return xt(r,t);var e={}.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?xt(r,t):void 0}}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function xt(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function kt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ut(n.key),n)}}function Ut(r){var t=function(r,t){if("object"!=jt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==jt(t)?t:t+""}var Mt=function(){return function(r,t,e){return t&&kt(r.prototype,t),e&&kt(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}((function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}),[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),wr(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return b(t),ut(r)}},{key:"columnsToSQL",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(b(e),!r||"*"===r)return[];var n=At(t);return r.map((function(r){return wt(r,n)}))}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,e=t.database,n=void 0===e?"flinksql":e;b(t);var o=n.toLowerCase();if(_t[o])return _t[o](!1===t.trimQuery?r:r.trim(),t.parseOptions||u.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,s=this["".concat(o,"List")].bind(this),i=s(r,e),c=!0,l="",f=Ot(i);try{for(f.s();!(a=f.n()).done;){var p,b=a.value,v=!1,y=Ot(t);try{for(y.s();!(p=y.n()).done;){var d=p.value,h=new RegExp("^".concat(d,"$"),"i");if(h.test(b)){v=!0;break}}}catch(r){y.e(r)}finally{y.f()}if(!v){l=b,c=!1;break}}}catch(r){f.e(r)}finally{f.f()}if(!c)throw new Error("authority = '".concat(l,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])}();function Dt(r){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Dt(self))&&self&&(self.NodeSQLParser={Parser:Mt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Dt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Dt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Mt,util:n})}]));
//# sourceMappingURL=flinksql.js.map