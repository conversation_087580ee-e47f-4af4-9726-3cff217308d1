!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var r=e();for(var n in r)("object"==typeof exports?exports:t)[n]=r[n]}}(this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=26)}([function(t,e,r){"use strict";r.r(e),r.d(e,"arrayStructTypeToSQL",(function(){return E})),r.d(e,"autoIncrementToSQL",(function(){return _})),r.d(e,"columnOrderListToSQL",(function(){return x})),r.d(e,"commonKeywordArgsToSQL",(function(){return U})),r.d(e,"commonOptionConnector",(function(){return i})),r.d(e,"connector",(function(){return c})),r.d(e,"commonTypeValue",(function(){return O})),r.d(e,"commentToSQL",(function(){return A})),r.d(e,"createBinaryExpr",(function(){return f})),r.d(e,"createValueExpr",(function(){return l})),r.d(e,"dataTypeToSQL",(function(){return g})),r.d(e,"DEFAULT_OPT",(function(){return s})),r.d(e,"escape",(function(){return p})),r.d(e,"literalToSQL",(function(){return L})),r.d(e,"columnIdentifierToSql",(function(){return d})),r.d(e,"getParserOpt",(function(){return b})),r.d(e,"identifierToSql",(function(){return h})),r.d(e,"onPartitionsToSQL",(function(){return C})),r.d(e,"replaceParams",(function(){return j})),r.d(e,"returningToSQL",(function(){return S})),r.d(e,"hasVal",(function(){return w})),r.d(e,"setParserOpt",(function(){return v})),r.d(e,"toUpper",(function(){return m})),r.d(e,"topToSQL",(function(){return y})),r.d(e,"triggerEventToSQL",(function(){return T}));var n=r(2),o=r(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s={database:"redshift",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function i(t,e,r){if(r)return t?"".concat(t.toUpperCase()," ").concat(e(r)):e(r)}function c(t,e){if(e)return"".concat(t.toUpperCase()," ").concat(e)}function l(t){var e=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(e){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(e,'" to SQL'))}}function f(t,e,r){var n={operator:t,type:"binary_expr"};return n.left=e.type?e:l(e),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(r[0]),l(r[1])]},n):(n.right=r.type?r:l(r),n)}function p(t){return t}function b(){return u}function v(t){u=t}function y(t){if(t){var e=t.value,r=t.percent,n=t.parentheses?"(".concat(e,")"):e,o="TOP ".concat(n);return r?"".concat(o," ").concat(r.toUpperCase()):o}}function d(t){var e=b().database;if(t)switch(e&&e.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function h(t,e){var r=b().database;if(!0===e)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(r&&r.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function w(t){return t}function L(t){if(t){var e=t.prefix,r=t.type,n=t.parentheses,s=t.suffix,u=t.value,i="object"===a(t)?u:t;switch(r){case"backticks_quote_string":i="`".concat(u,"`");break;case"string":i="'".concat(u,"'");break;case"regex_string":i='r"'.concat(u,'"');break;case"hex_string":i="X'".concat(u,"'");break;case"full_hex_string":i="0x".concat(u);break;case"natural_string":i="N'".concat(u,"'");break;case"bit_string":i="b'".concat(u,"'");break;case"double_quote_string":i='"'.concat(u,'"');break;case"single_quote_string":i="'".concat(u,"'");break;case"boolean":case"bool":i=u?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(e||":").concat(u),e=null;break;case"origin":i=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(r.toUpperCase()," '").concat(u,"'");break;case"var_string":i="N'".concat(u,"'");break;case"unicode_string":i="U&'".concat(u,"'")}var c=[];return e&&c.push(m(e)),c.push(i),s&&("string"==typeof s&&c.push(s),"object"===a(s)&&(s.collate?c.push(Object(o.a)(s.collate)):c.push(L(s)))),i=c.join(" "),n?"(".concat(i,")"):i}}function O(t){if(!t)return[];var e=t.type,r=t.symbol,n=t.value;return[e.toUpperCase(),r,"string"==typeof n?n.toUpperCase():L(n)].filter(w)}function j(t,e){return function t(e,r){return Object.keys(e).filter((function(t){var r=e[t];return Array.isArray(r)||"object"===a(r)&&null!==r})).forEach((function(n){var o=e[n];if("object"!==a(o)||"param"!==o.type)return t(o,r);if(void 0===r[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return e[n]=l(r[o.value]),null})),e}(JSON.parse(JSON.stringify(t)),e)}function C(t){var e=t.type,r=t.partitions;return[m(e),"(".concat(r.map((function(t){if("range"!==t.type)return L(t);var e=t.start,r=t.end,n=t.symbol;return"".concat(L(e)," ").concat(m(n)," ").concat(L(r))})).join(", "),")")].join(" ")}function g(t){var e=t.dataType,r=t.length,n=t.parentheses,o=t.scale,a=t.suffix,s="";return null!=r&&(s=o?"".concat(r,", ").concat(o):r),n&&(s="(".concat(s,")")),a&&a.length&&(s+=" ".concat(a.join(" "))),"".concat(e).concat(s)}function E(t){if(t){var e=t.dataType,r=t.definition,n=t.anglebracket,o=m(e);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=r&&r.map((function(t){return[t.field_name,E(t.field_type)].filter(w).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function A(t){if(t){var e=[],r=t.keyword,n=t.symbol,o=t.value;return e.push(r.toUpperCase()),n&&e.push(n),e.push(L(o)),e.join(" ")}}function T(t){return t.map((function(t){var e=t.keyword,r=t.args,o=[m(e)];if(r){var a=r.keyword,s=r.columns;o.push(m(a),s.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function S(t){return t?["RETURNING",t.columns.map(n.h).filter(w).join(", ")].join(" "):""}function U(t){return t?[m(t.keyword),m(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var e=b().database;switch(e&&e.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var r=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,s=m(r);return a&&(s+="(".concat(L(n),", ").concat(L(o),")")),s}}function x(t){if(t)return t.map(n.e).filter(w).join(", ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return C})),r.d(e,"b",(function(){return g})),r.d(e,"d",(function(){return j})),r.d(e,"c",(function(){return E}));var n=r(0),o=r(9),a=r(13);var s=r(22),u=r(21);var i=r(11),c=r(2),l=r(6),f=r(18);var p=r(7),b=r(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t){var e=t.expr_list,r=t.type;switch(Object(n.toUpper)(r)){case"STRUCT":return"(".concat(Object(c.i)(e),")");case"ARRAY":return function(t){var e=t.array_path,r=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(e),"]");var s=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return r?"[".concat(s,"]"):a?"(".concat(s,")"):s}(t);default:return""}}function d(t){var e=t.definition,r=t.keyword,o=[Object(n.toUpper)(r)];return e&&"object"===v(e)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(e))),o.push(y(t)),o.filter(n.hasVal).join("")}var h=r(3),m=r(5),w=r(20);function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var O={alter:o.b,aggr_func:function(t){var e=t.args,r=t.filter,o=t.over,s=t.within_group_orderby,u=C(e.expr);u=Array.isArray(u)?u.join(", "):u;var i=t.name,c=Object(a.a)(o);e.distinct&&(u=["DISTINCT",u].join(" ")),e.separator&&e.separator.delimiter&&(u=[u,Object(n.literalToSQL)(e.separator.delimiter)].join("".concat(e.separator.symbol," "))),e.separator&&e.separator.expr&&(u=[u,C(e.separator.expr)].join(" ")),e.orderby&&(u=[u,E(e.orderby,"order by")].join(" ")),e.separator&&e.separator.value&&(u=[u,Object(n.toUpper)(e.separator.keyword),Object(n.literalToSQL)(e.separator.value)].filter(n.hasVal).join(" "));var l=s?"WITHIN GROUP (".concat(E(s,"order by"),")"):"",f=r?"FILTER (WHERE ".concat(C(r.where),")"):"";return["".concat(i,"(").concat(u,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:w.c,array:d,assign:s.a,binary_expr:u.a,case:function(t){var e=["CASE"],r=t.args,n=t.expr,o=t.parentheses;n&&e.push(C(n));for(var a=0,s=r.length;a<s;++a)e.push(r[a].type.toUpperCase()),r[a].cond&&(e.push(C(r[a].cond)),e.push("THEN")),e.push(C(r[a].result));return e.push("END"),o?"(".concat(e.join(" "),")"):e.join(" ")},cast:l.c,collate:i.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var e=t.keyword,r=t.expr_list;return[Object(n.toUpper)(e),r.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:d,tablefunc:l.j,tables:h.c,unnest:h.d,window:w.b};function j(t){var e=t.prefix,r=void 0===e?"@":e,o=t.name,a=t.members,s=t.quoted,u=t.suffix,i=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(r||"").concat(c);return u&&(l+=u),i.push(l),[s,i.join(" "),s].filter(n.hasVal).join("")}function C(t){if(t){var e=t;if(t.ast){var r=e.ast;Reflect.deleteProperty(e,r);for(var o=0,a=Object.keys(r);o<a.length;o++){var s=a[o];e[s]=r[s]}}var u=e.type;return"expr"===u?C(e.expr):O[u]?O[u](e):Object(n.literalToSQL)(e)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function E(t,e){if(!Array.isArray(t))return"";var r=[],o=Object(n.toUpper)(e);switch(o){case"ORDER BY":r=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:r=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,r.join(", "))}O.var=j,O.expr_list=function(t){var e=g(t.value),r=t.parentheses,n=t.separator;if(!r&&!n)return e;var o=n||", ",a=e.join(o);return r?"(".concat(a,")"):a},O.select=function(t){var e="object"===L(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(e,")"):e},O.unary_expr=function(t){var e=t.operator,r=t.parentheses,n=t.expr,o="-"===e||"+"===e||"~"===e||"!"===e?"":" ",a="".concat(e).concat(o).concat(C(n));return r?"(".concat(a,")"):a},O.map_object=function(t){var e=t.keyword,r=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(e),"[".concat(r,"]")].join("")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"c",(function(){return L})),r.d(e,"f",(function(){return y})),r.d(e,"h",(function(){return C})),r.d(e,"i",(function(){return E})),r.d(e,"b",(function(){return d})),r.d(e,"d",(function(){return b})),r.d(e,"e",(function(){return w})),r.d(e,"g",(function(){return h})),r.d(e,"j",(function(){return j})),r.d(e,"k",(function(){return g}));var n=r(11),o=r(19),a=r(1),s=r(6),u=r(3),i=r(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){if("string"==typeof t)return Object(i.identifierToSql)(t,e);var r=t.expr,n=t.offset,o=t.suffix,s=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(a.a)(r),s,o].filter(i.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var e,r=[],n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=f(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}(t);try{for(n.s();!(e=n.n()).done;){var o=e.value,a=o.brackets?"[".concat(Object(i.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(i.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(i.literalToSQL)(o.property))),r.push(a)}}catch(t){n.e(t)}finally{n.f()}return r.join("")}function y(t){var e=t.array_index,r=t.as,o=t.column,s=t.collate,u=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,y=t.options,d=t.schema,h=t.table,m=t.parentheses,w=t.suffix,L=t.order_by,O=t.subFields,j=void 0===O?[]:O,C="*"===o?"*":b(o,c),g=[u,d,h].filter(i.hasVal).map((function(t){return"".concat("string"==typeof t?Object(i.identifierToSql)(t):Object(a.a)(t))})),E=g[0];if(E){for(var A=1;A<g.length;++A)E="".concat(E).concat(p[A]||".").concat(g[A]);C="".concat(E).concat(p[A]||".").concat(C)}var T=[C=["".concat(C).concat(v(e))].concat(l(j)).join("."),Object(n.a)(s),Object(a.a)(y),Object(i.commonOptionConnector)("AS",a.a,r)];T.push("string"==typeof w?Object(i.toUpper)(w):Object(a.a)(w)),T.push(Object(i.toUpper)(L));var S=T.filter(i.hasVal).join(" ");return m?"(".concat(S,")"):S}function d(t){if(t){var e=t.dataType,r=t.length,n=t.suffix,o=t.scale,u=t.expr,c=null!=r,l=Object(i.dataTypeToSQL)({dataType:e,length:r,suffix:n,scale:o,parentheses:c});if(u&&(l+=Object(a.a)(u)),t.array){var f=Object(s.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function h(t){var e=[];if(!t)return e;var r=t.definition,n=t.keyword,o=t.match,s=t.table,c=t.on_action;return e.push(Object(i.toUpper)(n)),e.push(Object(u.c)(s)),e.push(r&&"(".concat(r.map((function(t){return Object(a.a)(t)})).join(", "),")")),e.push(Object(i.toUpper)(o)),c.map((function(t){return e.push(Object(i.toUpper)(t.type),Object(a.a)(t.value))})),e.filter(i.hasVal)}function m(t){var e=[],r=t.nullable,n=t.character_set,s=t.check,u=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,y=t.generated,d=t.auto_increment,m=t.unique,w=t.primary_key,L=t.column_format,O=t.reference_definition,j=[Object(i.toUpper)(r&&r.action),Object(i.toUpper)(r&&r.value)].filter(i.hasVal).join(" ");if(y||e.push(j),v){var C=v.type,g=v.value;e.push(C.toUpperCase(),Object(a.a)(g))}var E=Object(i.getParserOpt)().database;return c&&e.push(Object(i.toUpper)(c.keyword),Object(i.literalToSQL)(c.constraint)),e.push(Object(o.a)(s)),e.push(function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(y)),y&&e.push(j),e.push(Object(i.autoIncrementToSQL)(d),Object(i.toUpper)(w),Object(i.toUpper)(m),Object(i.commentToSQL)(u)),e.push.apply(e,l(Object(i.commonTypeValue)(n))),"sqlite"!==E.toLowerCase()&&e.push(Object(a.a)(f)),e.push.apply(e,l(Object(i.commonTypeValue)(L))),e.push.apply(e,l(Object(i.commonTypeValue)(p))),e.push.apply(e,l(h(O))),e.push(Object(i.commonOptionConnector)("USING",a.a,b)),e.filter(i.hasVal).join(" ")}function w(t){var e=t.column,r=t.collate,n=t.nulls,o=t.opclass,s=t.order_by,u="string"==typeof e?{type:"column_ref",table:t.table,column:e}:t;return u.collate=null,[Object(a.a)(u),Object(a.a)(r),o,Object(i.toUpper)(s),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function L(t){var e=[],r=y(t.column),n=d(t.definition);return e.push(r),e.push(n),e.push(m(t)),e.filter(i.hasVal).join(" ")}function O(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function j(t){var e=t.against,r=t.as,n=t.columns,o=t.match,s=t.mode;return[[Object(i.toUpper)(o),"(".concat(n.map((function(t){return y(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(e),["(",Object(a.a)(t.expr),s&&" ".concat(Object(i.literalToSQL)(s)),")"].filter(i.hasVal).join("")].join(" "),O(r)].filter(i.hasVal).join(" ")}function C(t,e){var r=t.expr,n=t.type;if("cast"===n)return Object(s.c)(t);e&&(r.isDual=e);var o=Object(a.a)(r),u=t.expr_list;if(u){var c=[o],l=u.map((function(t){return C(t,e)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return r.parentheses&&Reflect.has(r,"array_index")&&"cast"!==r.type&&(o="(".concat(o,")")),r.array_index&&"column_ref"!==r.type&&(o="".concat(o).concat(v(r.array_index))),[o,O(t.as)].filter(i.hasVal).join(" ")}function g(t){var e=Array.isArray(t)&&t[0];return!(!e||"dual"!==e.type)}function E(t,e){if(!t||"*"===t)return t;var r=g(e);return t.map((function(t){return C(t,r)})).join(", ")}},function(t,e,r){"use strict";r.d(e,"c",(function(){return h})),r.d(e,"a",(function(){return m})),r.d(e,"b",(function(){return d})),r.d(e,"d",(function(){return f}));var n=r(21),o=r(2),a=r(1),s=r(17),u=r(18),i=r(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){var e=t.type,r=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(i.toUpper)(e),"(").concat(n&&Object(a.a)(n)||"",")"),Object(i.commonOptionConnector)("AS","string"==typeof r?i.identifierToSql:a.a,r),Object(i.commonOptionConnector)(Object(i.toUpper)(o&&o.keyword),i.identifierToSql,o&&o.as)].filter(i.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var e=t.as,r=t.column,s=t.expr,u=t.in_expr,c=t.type,l=[Object(a.a)(s),"FOR",Object(o.f)(r),Object(n.a)(u)],f=["".concat(Object(i.toUpper)(c),"(").concat(l.join(" "),")")];return e&&f.push("AS",Object(i.identifierToSql)(e)),f.join(" ")}(t);default:return""}}function b(t){if(t){var e=t.keyword,r=t.expr,n=t.index,o=t.index_columns,s=t.parentheses,u=t.prefix,c=[];switch(e.toLowerCase()){case"forceseek":c.push(Object(i.toUpper)(e),"(".concat(Object(i.identifierToSql)(n)),"(".concat(o.map(a.a).filter(i.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(i.toUpper)(e),"=",Object(a.a)(r));break;case"index":c.push(Object(i.toUpper)(u),Object(i.toUpper)(e),s?"(".concat(r.map(i.identifierToSql).join(", "),")"):"= ".concat(Object(i.identifierToSql)(r)));break;default:c.push(Object(a.a)(r))}return c.filter(i.hasVal).join(" ")}}function v(t,e){var r=t.name,n=t.symbol;return[Object(i.toUpper)(r),n,e].filter(i.hasVal).join(" ")}function y(t){var e=[];switch(t.keyword){case"as":e.push("AS","OF",Object(a.a)(t.of));break;case"from_to":e.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":e.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":e.push("CONTAINED","IN",Object(a.a)(t.in))}return e.filter(i.hasVal).join(" ")}function d(t){if("UNNEST"===Object(i.toUpper)(t.type))return f(t);var e,r,n,c,l=t.table,d=t.db,h=t.as,m=t.expr,w=t.operator,L=t.prefix,O=t.schema,j=t.server,C=t.suffix,g=t.tablesample,E=t.temporal_table,A=t.table_hint,T=Object(i.identifierToSql)(j),S=Object(i.identifierToSql)(d),U=Object(i.identifierToSql)(O),_=l&&Object(i.identifierToSql)(l);if(m)switch(m.type){case"values":var x=m.parentheses,I=m.values,N=m.prefix,R=[x&&"(","",x&&")"],k=Object(s.b)(I);N&&(k=k.split("(").slice(1).map((function(t){return"".concat(Object(i.toUpper)(N),"(").concat(t)})).join("")),R[1]="VALUES ".concat(k),_=R.filter(i.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var e=t.data,r=t.timecol,n=t.offset,a=t.size,s=[Object(i.identifierToSql)(e.expr.db),Object(i.identifierToSql)(e.expr.schema),Object(i.identifierToSql)(e.expr.table)].filter(i.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(r.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(e,s)),v(r,c)],f=v(a,Object(u.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(u.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(i.hasVal).join(", ")}(m);break;case"generator":r=(e=m).keyword,n=e.type,c=e.generators.map((function(t){return Object(i.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(i.toUpper)(r),"(").concat(Object(i.toUpper)(n),"(").concat(c,"))");break;default:_=Object(a.a)(m)}var V=[[T,S,U,_=[Object(i.toUpper)(L),_,Object(i.toUpper)(C)].filter(i.hasVal).join(" ")].filter(i.hasVal).join(".")];if(g){var M=["TABLESAMPLE",Object(a.a)(g.expr),Object(i.literalToSQL)(g.repeatable)].filter(i.hasVal).join(" ");V.push(M)}V.push(function(t){if(t){var e=t.keyword,r=t.expr;return[Object(i.toUpper)(e),y(r)].filter(i.hasVal).join(" ")}}(E),Object(i.commonOptionConnector)("AS","string"==typeof h?i.identifierToSql:a.a,h),p(w)),A&&V.push(Object(i.toUpper)(A.keyword),"(".concat(A.expr.map(b).filter(i.hasVal).join(", "),")"));var q=V.filter(i.hasVal).join(" ");return t.parentheses?"(".concat(q,")"):q}function h(t){if(!t)return"";if(!Array.isArray(t)){var e=t.expr,r=t.parentheses,n=t.joins,o=h(e);if(r){for(var s=[],u=[],l=!0===r?1:r.length,f=0;f++<l;)s.push("("),u.push(")");var p=n&&n.length>0?h([""].concat(c(n))):"";return s.join("")+o+u.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(d(b));for(var y=1;y<t.length;++y){var m=t[y],w=m.on,L=m.using,O=m.join,j=[];j.push(O?" ".concat(Object(i.toUpper)(O)):","),j.push(d(m)),j.push(Object(i.commonOptionConnector)("ON",a.a,w)),L&&j.push("USING (".concat(L.map(i.literalToSQL).join(", "),")")),v.push(j.filter(i.hasVal).join(" "))}return v.filter(i.hasVal).join("")}function m(t){var e=t.keyword,r=t.symbol,n=t.value,o=[e.toUpperCase()];r&&o.push(r);var s=Object(i.literalToSQL)(n);switch(e){case"partition by":case"default collate":s=Object(a.a)(n);break;case"options":s="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(a.a).join(", ")}return o.push(s),o.filter(i.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return y})),r.d(e,"c",(function(){return g})),r.d(e,"d",(function(){return E})),r.d(e,"e",(function(){return d})),r.d(e,"f",(function(){return h})),r.d(e,"g",(function(){return m})),r.d(e,"h",(function(){return S})),r.d(e,"i",(function(){return T})),r.d(e,"j",(function(){return A})),r.d(e,"l",(function(){return w})),r.d(e,"m",(function(){return L})),r.d(e,"o",(function(){return O})),r.d(e,"n",(function(){return j})),r.d(e,"k",(function(){return C}));var n=r(2),o=r(14),a=r(0),s=r(1),u=r(3),i=r(16),c=r(5);function l(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=p(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t){var e=Object(s.a)(t.expr);return"".concat("CALL"," ").concat(e)}function y(t){var e=t.type,r=t.keyword,o=t.name,i=t.prefix,c=t.suffix,l=[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(a.toUpper)(i)];switch(r){case"table":l.push(Object(u.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(u.c)(o),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(u.b)(t.table)]:[]),[t.options&&t.options.map(s.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(s.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function d(t){var e=t.type,r=t.table,n=Object(a.toUpper)(e);return"".concat(n," ").concat(Object(a.identifierToSql)(r))}function h(t){var e=t.type,r=t.name,n=t.args,o=[Object(a.toUpper)(e)],u=[r];return n&&u.push("(".concat(Object(s.a)(n).join(", "),")")),o.push(u.join("")),o.filter(a.hasVal).join(" ")}function m(t){var e=t.type,r=t.label,n=t.target,o=t.query,s=t.stmts;return[r,Object(a.toUpper)(e),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(s),"END LOOP",r].filter(a.hasVal).join(" ")}function w(t){var e=t.type,r=t.level,n=t.raise,o=t.using,u=[Object(a.toUpper)(e),Object(a.toUpper)(r)];return n&&u.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(s.a)(t)})).join(", ")),o&&u.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(s.a)(t)})).join(", ")),u.filter(a.hasVal).join(" ")}function L(t){var e=t.type,r=t.table,n=[],o="".concat(e&&e.toUpperCase()," TABLE");if(r){var a,s=l(r);try{for(s.s();!(a=s.n()).done;){var i=a.value.map(u.b);n.push(i.join(" TO "))}}catch(t){s.e(t)}finally{s.f()}}return"".concat(o," ").concat(n.join(", "))}function O(t){var e=t.type,r=t.db,n=Object(a.toUpper)(e),o=Object(a.identifierToSql)(r);return"".concat(n," ").concat(o)}function j(t){var e=t.type,r=t.expr,n=t.keyword,o=Object(a.toUpper)(e),u=r.map(s.a).join(", ");return[o,Object(a.toUpper)(n),u].filter(a.hasVal).join(" ")}function C(t){var e=t.type,r=t.keyword,n=t.tables,o=[e.toUpperCase(),Object(a.toUpper)(r)];if("UNLOCK"===e.toUpperCase())return o.join(" ");var s,i=[],c=l(n);try{var p=function(){var t=s.value,e=t.table,r=t.lock_type,n=[Object(u.b)(e)];if(r){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(r[t])})).filter(a.hasVal).join(" "))}i.push(n.join(" "))};for(c.s();!(s=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[i.join(", ")].concat(f(function(t){var e=t.lock_mode,r=t.nowait,n=[];if(e){var o=e.mode;n.push(o.toUpperCase())}return r&&n.push(r.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(s.a)(n)].filter(a.hasVal).join(" ")}function E(t){var e=t.type,r=t.declare,u=t.symbol,i=[Object(a.toUpper)(e)],c=r.map((function(t){var e=t.at,r=t.name,u=t.as,i=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[e,r].filter(a.hasVal).join(""),Object(a.toUpper)(u),Object(a.toUpper)(i)];switch(b){case"variable":v.push(Object(n.b)(c),Object(s.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(s.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(u," "));return i.push(c),i.join(" ")}function A(t){var e=t.boolean_expr,r=t.else_expr,n=t.elseif_expr,o=t.if_expr,u=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(s.a)(e),Object(a.literalToSQL)(u),"".concat(Object(i.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(s.a)(t.boolean_expr),"THEN",Object(i.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),r&&b.push("ELSE","".concat(Object(i.a)(r.ast||r)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function T(t){var e=t.name,r=t.host,n=[Object(a.literalToSQL)(e)];return r&&n.push("@",Object(a.literalToSQL)(r)),n.join("")}function S(t){var e=t.type,r=t.grant_option_for,o=t.keyword,u=t.objects,i=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(e),Object(a.literalToSQL)(r)],b=u.map((function(t){var e=t.priv,r=t.columns,o=[Object(s.a)(e)];return r&&o.push("(".concat(r.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),i)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(i.object_type),i.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(i))}return p.push(Object(a.toUpper)(c),l.map(T).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"b",(function(){return O})),r.d(e,"a",(function(){return j}));var n=r(9),o=r(1),a=r(3),s=r(0);var u=r(14),i=r(2);function c(t){var e=t.name,r=t.type;switch(r){case"table":case"view":var n=[Object(s.identifierToSql)(e.db),Object(s.identifierToSql)(e.table)].filter(s.hasVal).join(".");return"".concat(Object(s.toUpper)(r)," ").concat(n);case"column":return"COLUMN ".concat(Object(i.f)(e));default:return"".concat(Object(s.toUpper)(r)," ").concat(Object(s.literalToSQL)(e))}}function l(t){var e=t.keyword,r=t.expr;return[Object(s.toUpper)(e),Object(s.literalToSQL)(r)].filter(s.hasVal).join(" ")}var f=r(7);var p=r(8),b=r(15);var v=r(12),y=r(17),d=r(4);function h(t){var e=t.name,r=t.value;return["@".concat(e),"=",Object(o.a)(r)].filter(s.hasVal).join(" ")}var m=r(22);var w=r(23),L={alter:n.c,analyze:function(t){var e=t.type,r=t.table;return[Object(s.toUpper)(e),Object(a.b)(r)].join(" ")},attach:function(t){var e=t.type,r=t.database,n=t.expr,a=t.as,u=t.schema;return[Object(s.toUpper)(e),Object(s.toUpper)(r),Object(o.a)(n),Object(s.toUpper)(a),Object(s.identifierToSql)(u)].filter(s.hasVal).join(" ")},create:u.b,comment:function(t){var e=t.expr,r=t.keyword,n=t.target,o=t.type;return[Object(s.toUpper)(o),Object(s.toUpper)(r),c(n),l(e)].filter(s.hasVal).join(" ")},select:f.a,deallocate:d.c,delete:function(t){var e=t.columns,r=t.from,n=t.table,u=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,y=[Object(b.a)(l),"DELETE"],d=Object(i.i)(e,r);return y.push(d),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||y.push(Object(a.c)(n))),y.push(Object(s.commonOptionConnector)("FROM",a.c,r)),y.push(Object(s.commonOptionConnector)("WHERE",o.a,u)),y.push(Object(o.c)(c,"order by")),y.push(Object(p.a)(f)),y.push(Object(s.returningToSQL)(v)),y.filter(s.hasVal).join(" ")},exec:function(t){var e=t.keyword,r=t.module,n=t.parameters;return[Object(s.toUpper)(e),Object(a.b)(r),(n||[]).map(h).filter(s.hasVal).join(", ")].filter(s.hasVal).join(" ")},execute:d.f,explain:function(t){var e=t.type,r=t.expr;return[Object(s.toUpper)(e),Object(f.a)(r)].join(" ")},for:d.g,update:v.b,if:d.j,insert:y.a,drop:d.b,truncate:d.b,replace:y.a,declare:d.d,use:d.o,rename:d.m,call:d.a,desc:d.e,set:d.n,lock:d.k,unlock:d.k,show:w.a,grant:d.h,revoke:d.h,proc:function(t){var e=t.stmt;switch(e.type){case"assign":return Object(m.a)(e);case"return":return function(t){var e=t.type,r=t.expr;return[Object(s.toUpper)(e),Object(o.a)(r)].join(" ")}(e)}},raise:d.l,transaction:function(t){var e=t.expr,r=e.action,n=e.keyword,o=e.modes,a=[Object(s.literalToSQL)(r),Object(s.toUpper)(n)];return o&&a.push(o.map(s.literalToSQL).join(", ")),a.filter(s.hasVal).join(" ")}};function O(t){if(!t)return"";for(var e=L[t.type],r=t,n=r._parentheses,a=r._orderby,u=r._limit,i=[n&&"(",e(t)];t._next;){var c=L[t._next.type],l=Object(s.toUpper)(t.set_op);i.push(l,c(t._next)),t=t._next}return i.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(u)),i.filter(s.hasVal).join(" ")}function j(t){for(var e=[],r=0,n=t.length;r<n;++r){var o=t[r]&&t[r].ast?t[r].ast:t[r],a=O(o);r===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),e.push(a)}return e.join(" ; ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u})),r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return c})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return p})),r.d(e,"f",(function(){return b})),r.d(e,"g",(function(){return v})),r.d(e,"h",(function(){return f})),r.d(e,"i",(function(){return d})),r.d(e,"j",(function(){return y}));var n=r(2),o=r(1),a=r(0),s=r(13);function u(t){var e=t.args,r=t.type,n=t.over,u=e.expr,i=e.having,c="".concat(Object(a.toUpper)(r),"(").concat(Object(o.a)(u));return i&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(i.prefix)," ").concat(Object(o.a)(i.expr))),[c="".concat(c,")"),Object(s.a)(n)].filter(a.hasVal).join(" ")}function i(t){if(!t||!t.array)return"";var e=t.array.keyword;if(e)return Object(a.toUpper)(e);for(var r=t.array,n=r.dimension,o=r.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(Object(a.literalToSQL)(o[u])),s.push("]");return s.join("")}function c(t){for(var e=t.target,r=t.expr,s=t.keyword,u=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:r,offset:l}),b=[],v=0,y=e.length;v<y;++v){var d=e[v],h=d.angle_brackets,m=d.length,w=d.dataType,L=d.parentheses,O=d.quoted,j=d.scale,C=d.suffix,g=d.expr,E=g?Object(o.a)(g):"";null!=m&&(E=j?"".concat(m,", ").concat(j):m),L&&(E="(".concat(E,")")),h&&(E="<".concat(E,">")),C&&C.length&&(E+=" ".concat(C.map(a.literalToSQL).join(" ")));var A="::",T="",S=[];"as"===u&&(0===v&&(p="".concat(Object(a.toUpper)(s),"(").concat(p)),T=")",A=" ".concat(u.toUpperCase()," ")),0===v&&S.push(p);var U=i(d);S.push(A,O,w,O,U,E,T),b.push(S.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var _=b.filter(a.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var e=t.args,r=t.type,n=e.field,s=e.cast_type,u=e.source,i=["".concat(Object(a.toUpper)(r),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(s),Object(o.a)(u)];return"".concat(i.filter(a.hasVal).join(" "),")")}function f(t){var e=t.expr,r=e.key,n=e.value,s=e.on,u=[Object(o.a)(r),"VALUE",Object(o.a)(n)];return s&&u.push("ON","NULL",Object(o.a)(s)),u.filter(a.hasVal).join(" ")}function p(t){var e=t.args,r=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var e=t.type,r=t.symbol,n=t.value;return[Object(a.toUpper)(e),r,Object(o.a)(n)].filter(a.hasVal).join(" ")}(e[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(r),"(").concat(n,")")}function b(t){var e=t.value,r=e.name,n=e.symbol,s=e.expr;return[r,n,Object(o.a)(s)].filter(a.hasVal).join(" ")}function v(t){var e=t.args,r=t.array_index,u=t.name,i=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(s.a)(f),v=function(t){if(!t)return"";var e=t.type,r=t.keyword,n=t.orderby;return[Object(a.toUpper)(e),Object(a.toUpper)(r),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),y=Object(o.a)(p),d=[Object(a.literalToSQL)(u.schema),u.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!e)return[d,v,b].filter(a.hasVal).join(" ");var h=t.separator||", ";"TRIM"===Object(a.toUpper)(d)&&(h=" ");var m=[d];m.push(!1===i?" ":"(");var w=Object(o.a)(e);if(Array.isArray(h)){for(var L=w[0],O=1,j=w.length;O<j;++O)L=[L,w[O]].join(" ".concat(Object(o.a)(h[O-1])," "));m.push(L)}else m.push(w.join(h));return!1!==i&&m.push(")"),m.push(Object(n.a)(r)),m=[m.join(""),y].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function y(t){var e=t.as,r=t.name,n=t.args,s=[Object(a.literalToSQL)(r.schema),r.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(s,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(e)].join(" ")}function d(t){var e=t.args,r=t.expr,n=e.value,a=e.parentheses,s=n.map(o.a).join(", ");return[a?"(".concat(s,")"):s,"->",Object(o.a)(r)].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f}));var n=r(1),o=r(2),a=r(8),s=r(15),u=r(3),i=r(0),c=r(11);function l(t){if(t&&t.position){var e=t.keyword,r=t.expr,o=[],a=Object(i.toUpper)(e);switch(a){case"VAR":o.push(r.map(n.d).join(", "));break;default:o.push(a,"string"==typeof r?Object(i.identifierToSql)(r):Object(n.a)(r))}return o.filter(i.hasVal).join(" ")}}function f(t){var e=t.as_struct_val,r=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,y=t.for_sys_time_as_of,d=void 0===y?{}:y,h=t.locking_read,m=t.groupby,w=t.having,L=t.into,O=void 0===L?{}:L,j=t.isolation,C=t.limit,g=t.options,E=t.orderby,A=t.parentheses_symbol,T=t.qualify,S=t.top,U=t.window,_=t.with,x=t.where,I=[Object(s.a)(_),"SELECT",Object(i.toUpper)(e)];Array.isArray(g)&&I.push(g.join(" ")),I.push(function(t){if(t){if("string"==typeof t)return t;var e=t.type,r=t.columns,o=[Object(i.toUpper)(e)];return r&&o.push("(".concat(r.map(n.a).join(", "),")")),o.filter(i.hasVal).join(" ")}}(p),Object(i.topToSQL)(S),Object(o.i)(r,v));var N=O.position,R="";N&&(R=Object(i.commonOptionConnector)("INTO",l,O)),"column"===N&&I.push(R),I.push(Object(i.commonOptionConnector)("FROM",u.c,v)),"from"===N&&I.push(R);var k=d||{},V=k.keyword,M=k.expr;I.push(Object(i.commonOptionConnector)(V,n.a,M)),I.push(Object(i.commonOptionConnector)("WHERE",n.a,x)),m&&(I.push(Object(i.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),I.push(Object(n.b)(m.modifiers).join(", "))),I.push(Object(i.commonOptionConnector)("HAVING",n.a,w)),I.push(Object(i.commonOptionConnector)("QUALIFY",n.a,T)),I.push(Object(i.commonOptionConnector)("WINDOW",n.a,U)),I.push(Object(n.c)(E,"order by")),I.push(Object(c.a)(f)),I.push(Object(a.a)(C)),j&&I.push(Object(i.commonOptionConnector)(j.keyword,i.literalToSQL,j.expr)),I.push(Object(i.toUpper)(h)),"end"===N&&I.push(R),I.push(function(t){if(t){var e=t.expr,r=t.keyword,o=t.type,a=[Object(i.toUpper)(o),Object(i.toUpper)(r)];return e?"".concat(a.join(" "),"(").concat(Object(n.a)(e),")"):a.join(" ")}}(b));var q=I.filter(i.hasVal).join(" ");return A?"(".concat(q,")"):q}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function i(t){return t?t.fetch?(r=(e=t).fetch,s=e.offset,[].concat(a(u(s)),a(u(r))).filter(n.hasVal).join(" ")):function(t){var e=t.seperator,r=t.value;return 1===r.length&&"offset"===e?Object(n.connector)("OFFSET",Object(o.a)(r[0])):Object(n.connector)("LIMIT",r.map(o.a).join("".concat("offset"===e?" ":"").concat(Object(n.toUpper)(e)," ")))}(t):"";var e,r,s}},function(t,e,r){"use strict";r.d(e,"a",(function(){return p})),r.d(e,"c",(function(){return b})),r.d(e,"b",(function(){return f}));var n=r(2),o=r(14),a=r(10),s=r(3),u=r(1),i=r(7),c=r(0);function l(t,e){switch(t){case"add":var r=e.map((function(t){var e=t.name,r=t.value;return["PARTITION",Object(c.literalToSQL)(e),"VALUES",Object(c.toUpper)(r.type),"(".concat(Object(c.literalToSQL)(r.expr),")")].join(" ")})).join(", ");return"(".concat(r,")");default:return Object(n.i)(e)}}function f(t){if(!t)return"";var e=t.action,r=t.create_definitions,s=t.if_not_exists,u=t.keyword,i=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,y=t.suffix,d="",h=[];switch(b){case"column":h=[Object(n.c)(t)];break;case"index":h=Object(a.c)(t),d=t[b];break;case"table":case"schema":d=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":d=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":d=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":d=Object(c.identifierToSql)(t[b]),h=[Object(o.a)(r)];break;case"partition":h=[l(e,t.partitions)];break;case"key":d=Object(c.identifierToSql)(t[b]);break;default:d=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(e),Object(c.toUpper)(u),Object(c.toUpper)(s),Object(c.toUpper)(i),f&&Object(n.f)(f),Object(c.toUpper)(p),d&&d.trim(),h.filter(c.hasVal).join(" ")];return y&&m.push(Object(c.toUpper)(y.keyword),y.expr&&Object(n.f)(y.expr)),m.filter(c.hasVal).join(" ")}function p(t){var e=t.default&&[Object(c.toUpper)(t.default.keyword),Object(u.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),e].filter(c.hasVal).join(" ")}function b(t){var e=t.keyword;switch(void 0===e?"table":e){case"aggregate":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type,s=e.expr,u=e.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(s.map(p).join(", ")).concat(u?[" ORDER","BY",u.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var e=t.type,r=t.table,n=t.if_exists,o=t.prefix,a=t.expr,i=void 0===a?[]:a,l=Object(c.toUpper)(e),f=Object(s.c)(r),p=i.map(u.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var e=t.expr,r=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),Object(c.identifierToSql)(n),f(e)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var e=t.expr,r=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(e)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),e&&"(".concat(e.expr?e.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var e=t.type,r=t.columns,o=t.attributes,a=t.select,u=t.view,l=t.with,f=[Object(c.toUpper)(e),"VIEW",Object(s.b)(u)];return r&&f.push("(".concat(r.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(i.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f})),r.d(e,"d",(function(){return u})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t){if(!t)return[];var e=t.keyword,r=t.type;return[e.toUpperCase(),Object(n.toUpper)(r)]}function i(t){if(t){var e=t.type,r=t.expr,o=t.symbol,s=e.toUpperCase(),i=[];switch(i.push(s),s){case"KEY_BLOCK_SIZE":o&&i.push(o),i.push(Object(n.literalToSQL)(r));break;case"BTREE":case"HASH":i.length=0,i.push.apply(i,a(u(t)));break;case"WITH PARSER":i.push(r);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":i.shift(),i.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":i.push(o,Object(n.toUpper)(r.value),Object(n.onPartitionsToSQL)(r.on));break;default:i.push(o,Object(n.literalToSQL)(r))}return i.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(i):[]}function l(t){var e=t.constraint_type,r=t.index_type,s=t.index_options,i=void 0===s?[]:s,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(u(r))),l&&l.length){var v="CHECK"===Object(n.toUpper)(e)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(i).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var e=[],r=t.keyword,o=t.index;return e.push(Object(n.toUpper)(r)),e.push(o),e.push.apply(e,a(l(t))),e.filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){if(t){var e=t.keyword,r=t.collate,a=r.name,s=r.symbol,u=r.value,i=[Object(o.toUpper)(e)];return u||i.push(s),i.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),u&&i.push(s),i.push(Object(n.a)(u)),i.filter(o.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return p})),r.d(e,"a",(function(){return f}));var n=r(3),o=r(1),a=r(2),s=r(8),u=r(0),i=r(15);function c(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){if(!t||0===t.length)return"";var e,r=[],n=c(t);try{for(n.s();!(e=n.n()).done;){var s=e.value,i={},l=s.value;for(var f in s)"value"!==f&&"keyword"!==f&&(i[f]=s[f]);var p=[Object(a.f)(i)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),r.push(p.filter(u.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return r.join(", ")}function p(t){var e=t.from,r=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(r),Object(u.commonOptionConnector)("SET",f,a),Object(u.commonOptionConnector)("FROM",n.c,e),Object(u.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(s.a)(b),Object(u.returningToSQL)(v)].filter(u.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(0),o=r(1),a=r(20);function s(t){if(t){var e=t.as_window_specification,r=t.expr,s=t.keyword,u=t.type,i=t.parentheses,c=Object(n.toUpper)(u);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(e));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(u)," ").concat(Object(n.toUpper)(s)),f=Object(o.a)(r)||[];return i&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return E})),r.d(e,"a",(function(){return h}));var n=r(9),o=r(1),a=r(10),s=r(2),u=r(4),i=r(19),c=r(6),l=r(3),f=r(12),p=r(5),b=r(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t){if(!t)return[];var e=t.resource;switch(e){case"column":return Object(s.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(i.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(e," type"))}}function m(t){var e=[];switch(t.keyword){case"from":e.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":e.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":e.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return e.filter(b.hasVal).join(" ")}function w(t){var e=t.keyword,r=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(e),Object(l.b)(r),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function L(t){var e=t.as,r=t.domain,n=t.type,a=t.keyword,s=t.target,u=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(r.schema),Object(b.identifierToSql)(r.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.dataTypeToSQL)(s)];if(u&&u.length>0){var l,f=[],p=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=y(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}(u);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(i.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function O(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function j(t){var e=t.type;switch(e){case"as":return[Object(b.toUpper)(e),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(e),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(e),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=u.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(b.toUpper)(e),Object(b.toUpper)(r),Array.isArray(n)?"(".concat(n.map(s.c).join(", "),")"):O(n)].filter(b.hasVal).join(" ")}(i),c.map(j).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var e=t.type,r=t.symbol,n=t.value,a=[Object(b.toUpper)(e),r];switch(Object(b.toUpper)(e)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function E(t){var e=t.keyword,r="";switch(e.toLowerCase()){case"aggregate":r=function(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,s=t.args,u=t.options,i=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(s.expr.map(n.a).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(n.a).join(", ")].join(" "):"");return i.push("".concat(c,"(").concat(l,")"),"(".concat(u.map(g).join(", "),")")),i.filter(b.hasVal).join(" ")}(t);break;case"table":r=function(t){var e=t.type,r=t.keyword,n=t.table,o=t.like,a=t.as,s=t.temporary,u=t.if_not_exists,i=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,y=t.partition_of,d=t.query_expr,m=t.unlogged,L=t.with,O=[Object(b.toUpper)(e),Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(m),Object(b.toUpper)(r),Object(b.toUpper)(u),Object(l.c)(n)];if(o){var j=o.type,C=o.table,g=Object(l.c)(C);return O.push(Object(b.toUpper)(j),g),O.filter(b.hasVal).join(" ")}if(y)return O.concat([w(y)]).filter(b.hasVal).join(" ");if(i&&O.push("(".concat(i.map(h).join(", "),")")),c){var E=Object(b.getParserOpt)().database,A=E&&"sqlite"===E.toLowerCase()?", ":" ";O.push(c.map(l.a).join(A))}if(L){var T=L.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");O.push("WITH (".concat(T,")"))}return O.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),d&&O.push(Object(p.b)(d)),O.filter(b.hasVal).join(" ")}(t);break;case"trigger":r="constraint"===t.resource?function(t){var e=t.constraint,r=t.constraint_kw,n=t.deferrable,a=t.events,s=t.execute,u=t.for_each,i=t.from,f=t.location,p=t.keyword,y=t.or,d=t.type,h=t.table,m=t.when,w=[Object(b.toUpper)(d),Object(b.toUpper)(y),Object(b.toUpper)(r),Object(b.toUpper)(p),Object(b.identifierToSql)(e),Object(b.toUpper)(f)],L=Object(b.triggerEventToSQL)(a);return w.push(L,"ON",Object(l.b)(h)),i&&w.push("FROM",Object(l.b)(i)),w.push.apply(w,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(u)))),m&&w.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),w.push(Object(b.toUpper)(s.keyword),Object(c.g)(s.expr)),w.filter(b.hasVal).join(" ")}(t):function(t){var e=t.definer,r=t.for_each,n=t.keyword,a=t.execute,u=t.type,i=t.table,c=t.if_not_exists,v=t.temporary,y=t.trigger,d=t.events,h=t.order,m=t.time,w=t.when,L=[Object(b.toUpper)(u),Object(b.toUpper)(v),Object(o.a)(e),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(y),Object(b.toUpper)(m),d.map((function(t){var e=[Object(b.toUpper)(t.keyword)],r=t.args;return r&&e.push(Object(b.toUpper)(r.keyword),r.columns.map(s.f).join(", ")),e.join(" ")})),"ON",Object(l.b)(i),Object(b.toUpper)(r&&r.keyword),Object(b.toUpper)(r&&r.args),h&&"".concat(Object(b.toUpper)(h.keyword)," ").concat(Object(b.identifierToSql)(h.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,w),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":L.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":L.push(Object(p.a)(a.expr.ast))}return L.push(Object(b.toUpper)(a.suffix)),L.filter(b.hasVal).join(" ")}(t);break;case"extension":r=function(t){var e=t.extension,r=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,s=t.type,u=t.with,i=t.version;return[Object(b.toUpper)(s),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(e),Object(b.toUpper)(u),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,i),Object(b.commonOptionConnector)("FROM",b.literalToSQL,r)].filter(b.hasVal).join(" ")}(t);break;case"function":r=C(t);break;case"index":r=function(t){var e=t.concurrently,r=t.filestream_on,s=t.keyword,u=t.if_not_exists,i=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,y=t.index,d=t.on,h=t.index_options,m=t.algorithm_option,w=t.lock_option,L=t.on_kw,O=t.table,j=t.tablespace,C=t.type,g=t.where,E=t.with,A=t.with_before_where,T=E&&"WITH (".concat(Object(a.b)(E).join(", "),")"),S=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=y;y&&(U="string"==typeof y?Object(b.identifierToSql)(y):[Object(b.identifierToSql)(y.schema),Object(b.identifierToSql)(y.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(s),Object(b.toUpper)(u),Object(b.toUpper)(e),U,Object(b.toUpper)(L),Object(l.b)(O)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),S,Object(a.b)(h).join(" "),Object(n.b)(m),Object(n.b)(w),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,j)]);return A?_.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),_.push(Object(b.commonOptionConnector)("ON",o.a,d),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,r)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":r=function(t){var e=t.type,r=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,s=t.create_definitions,u=[Object(b.toUpper)(e),Object(b.toUpper)(o),Object(b.toUpper)(r),Object(b.toUpper)(a),Object(l.c)(n)];return s&&u.push(s.map(h).join(" ")),u.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":r=function(t){var e=t.type,r=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,s=t[r],u=s.db,i=s.schema,c=[Object(b.literalToSQL)(u),i.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(e),Object(b.toUpper)(n),Object(b.toUpper)(r),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":r=function(t){var e=t.algorithm,r=t.columns,n=t.definer,a=t.if_not_exists,s=t.keyword,u=t.recursive,i=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,y=t.view,d=t.with,h=t.with_options,m=y.db,w=y.schema,L=y.view,O=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(w),Object(b.identifierToSql)(L)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(f),Object(b.toUpper)(u),e&&"ALGORITHM = ".concat(Object(b.toUpper)(e)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(s),Object(b.toUpper)(a),O,r&&"(".concat(r.map(b.columnIdentifierToSql).join(", "),")"),h&&["WITH","(".concat(h.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(d)].filter(b.hasVal).join(" ")}(t);break;case"domain":r=L(t);break;case"type":r=function(t){var e=t.as,r=t.create_definitions,n=t.keyword,a=t.name,s=t.resource,u=t.type,i=[Object(b.toUpper)(u),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.toUpper)(s)];if(r){var c=[];switch(s){case"enum":case"range":c.push(Object(o.a)(r));break;default:c.push("(".concat(r.map(h).join(", "),")"))}i.push(c.filter(b.hasVal).join(" "))}return i.filter(b.hasVal).join(" ")}(t);break;case"user":r=function(t){var e=t.attribute,r=t.comment,n=t.default_role,a=t.if_not_exists,s=t.keyword,i=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var e=t.user,r=t.auth_option,n=[Object(u.i)(e)];return r&&n.push(Object(b.toUpper)(r.keyword),r.auth_plugin,Object(b.literalToSQL)(r.value)),n.filter(b.hasVal).join(" ")})).join(", "),y=[Object(b.toUpper)(p),Object(b.toUpper)(s),Object(b.toUpper)(a),v];return n&&y.push(Object(b.toUpper)(n.keyword),n.value.map(u.i).join(", ")),y.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&y.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return y.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),y.push(Object(b.literalToSQL)(i),Object(b.commentToSQL)(r),Object(b.literalToSQL)(e)),y.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(e))}return r}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(2),o=r(1),a=r(0);function s(t){if(t&&0!==t.length){var e=t[0].recursive?"RECURSIVE ":"",r=t.map((function(t){var e=t.name,r=t.stmt,s=t.columns,u=Array.isArray(s)?"(".concat(s.map(n.f).join(", "),")"):"";return"".concat("default"===e.type?Object(a.identifierToSql)(e.value):Object(a.literalToSQL)(e)).concat(u," AS (").concat(Object(o.a)(r),")")})).join(", ");return"WITH ".concat(e).concat(r)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var e=t&&t.ast?t.ast:t;if(!o.includes(e.type))throw new Error("".concat(e.type," statements not supported at the moment"))}function s(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function u(t){return"go"===t.go?function t(e){if(!e||0===e.length)return"";var r=[s(e.ast)];return e.go_next&&r.push(e.go.toUpperCase(),t(e.go_next)),r.filter((function(t){return t})).join(" ")}(t):s(t)}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return c}));var n=r(3),o=r(1),a=r(2),s=r(0),u=r(7),i=r(12);function c(t){if("select"===t.type)return Object(u.a)(t);var e=t.map(o.a);return"(".concat(e.join("), ("),")")}function l(t){if(!t)return"";var e=["PARTITION","("];if(Array.isArray(t))e.push(t.map(s.identifierToSql).join(", "));else{var r=t.value;e.push(r.map(o.a).join(", "))}return e.push(")"),e.filter(s.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var e=t.expr,r=t.keyword,n=e.type,a=[Object(s.toUpper)(r)];switch(n){case"origin":a.push(Object(s.literalToSQL)(e));break;case"update":a.push("UPDATE",Object(s.commonOptionConnector)("SET",i.a,e.set),Object(s.commonOptionConnector)("WHERE",o.a,e.where))}return a.filter(s.hasVal).join(" ")}function b(t){if(!t)return"";var e=t.action;return[f(t.target),p(e)].filter(s.hasVal).join(" ")}function v(t){var e=t.table,r=t.type,a=t.prefix,u=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,y=t.where,d=t.on_duplicate_update,h=t.partition,m=t.returning,w=t.set,L=d||{},O=L.keyword,j=L.set,C=[Object(s.toUpper)(r),Object(s.toUpper)(u),Object(n.c)(e),l(h)];return Array.isArray(f)&&C.push("(".concat(f.map(s.literalToSQL).join(", "),")")),C.push(Object(s.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(s.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(s.commonOptionConnector)("SET",i.a,w)),C.push(Object(s.commonOptionConnector)("WHERE",o.a,y)),C.push(Object(s.commonOptionConnector)(O,i.a,j)),C.push(Object(s.returningToSQL)(m)),C.filter(s.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(0),o=r(1);function a(t){var e=t.expr,r=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(e),Object(n.toUpper)(r),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(0),o=r(10),a=r(2);function s(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function i(t){if(t){var e=t.constraint,r=t.constraint_type,u=t.enforced,i=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(e));var y=Object(n.toUpper)(r);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===y&&(y="UNIQUE"),b.push(y),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(i)),b.push.apply(b,s(Object(o.c)(t))),b.push.apply(b,s(Object(a.g)(l))),b.push(Object(n.toUpper)(u)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1),a=r(13);function s(t){if(t){var e=t.type;return"rows"===e?[Object(n.toUpper)(e),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function u(t){if("string"==typeof t)return t;var e=t.window_specification;return"(".concat(function(t){var e=t.name,r=t.partitionby,a=t.orderby,u=t.window_frame_clause;return[e,Object(o.c)(r,"partition by"),Object(o.c)(a,"order by"),s(u)].filter(n.hasVal).join(" ")}(e),")")}function i(t){var e=t.name,r=t.as_window_specification;return"".concat(e," AS ").concat(u(r))}function c(t){return t.expr.map(i).join(", ")}function l(t){var e=t.over;return[function(t){var e=t.args,r=t.name,a=t.consider_nulls,s=void 0===a?"":a,u=t.separator,i=void 0===u?", ":u;return[r,"(",e?Object(o.a)(e).join(i):"",")",s&&" ",s].filter(n.hasVal).join("")}(t),Object(a.a)(e)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.operator||t.op,r=Object(n.a)(t.right),a=!1;if(Array.isArray(r)){switch(e){case"=":e="IN";break;case"!=":e="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,r="".concat(r[0]," AND ").concat(r[1])}a||(r="(".concat(r.join(", "),")"))}var s=t.right.escape||{},u=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),e,r,Object(o.toUpper)(s.type),Object(n.a)(s.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(u,")"):u].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.left,r=t.right,a=t.symbol,s=t.keyword;e.keyword=s;var u=Object(n.a)(e),i=Object(n.a)(r);return[u,Object(o.toUpper)(a),i].filter(o.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(1),o=r(8),a=r(3),s=r(0);function u(t){var e,r,u,i,c=t.keyword,l=t.suffix,f="";switch(Object(s.toUpper)(c)){case"BINLOG":r=(e=t).in,u=e.from,i=e.limit,f=[Object(s.commonOptionConnector)("IN",s.literalToSQL,r&&r.right),Object(s.commonOptionConnector)("FROM",a.c,u),Object(o.a)(i)].filter(s.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var e=t.expr;if(e){var r=e.op;return"LIKE"===Object(s.toUpper)(r)?Object(s.commonOptionConnector)("LIKE",s.literalToSQL,e.right):Object(s.commonOptionConnector)("WHERE",n.a,e)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(s.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var e=t.for;if(e){var r=e.user,n=e.host,o=e.role_list,a="'".concat(r,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(s.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(s.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(s.toUpper)(c),Object(s.toUpper)(l),f].filter(s.hasVal).join(" ")}},function(t,e,r){"use strict";var n=r(2),o=r(1),a=r(25);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u,i,c,l=(u={},i="redshift",c=a.parse,(i=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(i))in u?Object.defineProperty(u,i,{value:c,enumerable:!0,configurable:!0,writable:!0}):u[i]=c,u),f=r(16),p=r(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}var m=function(){return function(t,e,r){return e&&d(t.prototype,e),r&&d(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=this.parse(t,e);return r&&r.ast}},{key:"sqlify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(f.a)(t,e)}},{key:"exprToSQL",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(r),!t||"*"===t)return[];var o=Object(n.k)(e);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=e.database,n=void 0===r?"redshift":r;Object(p.setParserOpt)(e);var o=n.toLowerCase();if(l[o])return l[o](!1===e.trimQuery?t:t.trim(),e.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(e&&0!==e.length){var n=r.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,s=this["".concat(o,"List")].bind(this),u=s(t,r),i=!0,c="",l=v(u);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,y=!1,d=v(e);try{for(d.s();!(f=d.n()).done;){var h=f.value,m=new RegExp("^".concat(h,"$"),"i");if(m.test(b)){y=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!y){c=b,i=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,e){var r=this.parse(t,e);return r&&r.tableList}},{key:"columnList",value:function(t,e){var r=this.parse(t,e);return r&&r.columnList}}])}();e.a=m},function(t,e,r){"use strict";var n=r(29);function o(t,e,r,n){this.message=t,this.expected=e,this.found=r,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(o,Error),o.buildMessage=function(t,e){var r={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var e,r="";for(e=0;e<t.parts.length;e++)r+=t.parts[e]instanceof Array?a(t.parts[e][0])+"-"+a(t.parts[e][1]):a(t.parts[e]);return"["+(t.inverted?"^":"")+r+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var e,n,o,a=new Array(t.length);for(e=0;e<t.length;e++)a[e]=(o=t[e],r[o.type](o));if(a.sort(),a.length>0){for(e=1,n=1;e<a.length;e++)a[e-1]!==a[e]&&(a[n]=a[e],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(e)+" found."},t.exports={SyntaxError:o,parse:function(t,e){e=void 0!==e?e:{};var r,a={},s={start:Pi},u=Pi,i=Ni("IF",!0),c=Ni("EXTENSION",!0),l=Ni("SCHEMA",!0),f=Ni("VERSION",!0),p=Ni("CASCADED",!0),b=Ni("LOCAL",!0),v=Ni("CHECK",!0),y=Ni("OPTION",!1),d=Ni("check_option",!0),h=Ni("security_barrier",!0),m=Ni("security_invoker",!0),w=Ni("SFUNC",!0),L=Ni("STYPE",!0),O=Ni("AGGREGATE",!0),j=Ni("RETURNS",!0),C=Ni("SETOF",!0),g=Ni("CONSTANT",!0),E=Ni(":=",!1),A=Ni("BEGIN",!0),T=Ni("DECLARE",!0),S=Ni("LANGUAGE",!1),U=Ni("TRANSORM",!0),_=Ni("FOR",!1),x=Ni("TYPE",!1),I=Ni("WINDOW",!0),N=Ni("IMMUTABLE",!0),R=Ni("STABLE",!0),k=Ni("VOLATILE",!0),V=Ni("STRICT",!0),M=Ni("NOT",!0),q=Ni("LEAKPROOF",!0),P=Ni("CALLED",!0),D=Ni("NULL",!0),Q=Ni("ON",!0),F=Ni("INPUT",!0),G=Ni("EXTERNAL",!0),$=Ni("SECURITY",!0),B=Ni("INVOKER",!0),H=Ni("DEFINER",!0),Y=Ni("PARALLEL",!0),W=Ni("UNSAFE",!0),X=Ni("RESTRICTED",!0),Z=Ni("SAFE",!0),K=/^[^ s\t\n\r]/,J=Ri([" ","s","\t","\n","\r"],!0,!1),z=/^[^ s\t\n\r;]/,tt=Ri([" ","s","\t","\n","\r",";"],!0,!1),et=Ni("COST",!0),rt=Ni("ROWS",!0),nt=Ni("SUPPORT",!0),ot=Ni("TO",!0),at=Ni("=",!1),st=Ni("CURRENT",!0),ut=Ni("FUNCTION",!0),it=Ni("TYPE",!0),ct=Ni("DOMAIN",!0),lt=Ni("INCREMENT",!0),ft=Ni("MINVALUE",!0),pt=function(t,e){return{resource:"sequence",prefix:t.toLowerCase(),value:e}},bt=Ni("NO",!0),vt=Ni("MAXVALUE",!0),yt=Ni("START",!0),dt=Ni("CACHE",!0),ht=Ni("CYCLE",!0),mt=Ni("OWNED",!0),wt=Ni("NONE",!0),Lt=Ni("NULLS",!0),Ot=Ni("FIRST",!0),jt=Ni("LAST",!0),Ct=Ni("AUTO_INCREMENT",!0),gt=Ni("UNIQUE",!0),Et=Ni("KEY",!0),At=Ni("PRIMARY",!0),Tt=Ni("COLUMN_FORMAT",!0),St=Ni("FIXED",!0),Ut=Ni("DYNAMIC",!0),_t=Ni("DEFAULT",!0),xt=Ni("STORAGE",!0),It=Ni("DISK",!0),Nt=Ni("MEMORY",!0),Rt=Ni("CASCADE",!0),kt=Ni("RESTRICT",!0),Vt=Ni("OUT",!0),Mt=Ni("VARIADIC",!0),qt=Ni("INOUT",!0),Pt=Ni("OWNER",!0),Dt=Ni("CURRENT_ROLE",!0),Qt=Ni("CURRENT_USER",!0),Ft=Ni("SESSION_USER",!0),Gt=Ni("ALGORITHM",!0),$t=Ni("INSTANT",!0),Bt=Ni("INPLACE",!0),Ht=Ni("COPY",!0),Yt=Ni("LOCK",!0),Wt=Ni("SHARED",!0),Xt=Ni("EXCLUSIVE",!0),Zt=Ni("PRIMARY KEY",!0),Kt=Ni("FOREIGN KEY",!0),Jt=Ni("MATCH FULL",!0),zt=Ni("MATCH PARTIAL",!0),te=Ni("MATCH SIMPLE",!0),ee=Ni("SET NULL",!0),re=Ni("NO ACTION",!0),ne=Ni("SET DEFAULT",!0),oe=Ni("TRIGGER",!0),ae=Ni("BEFORE",!0),se=Ni("AFTER",!0),ue=Ni("INSTEAD OF",!0),ie=Ni("EXECUTE",!0),ce=Ni("PROCEDURE",!0),le=Ni("OF",!0),fe=Ni("DEFERRABLE",!0),pe=Ni("INITIALLY IMMEDIATE",!0),be=Ni("INITIALLY DEFERRED",!0),ve=Ni("FOR",!0),ye=Ni("EACH",!0),de=Ni("ROW",!0),he=Ni("STATEMENT",!0),me=Ni("CHARACTER",!0),we=Ni("SET",!0),Le=Ni("CHARSET",!0),Oe=Ni("COLLATE",!0),je=Ni("AVG_ROW_LENGTH",!0),Ce=Ni("KEY_BLOCK_SIZE",!0),ge=Ni("MAX_ROWS",!0),Ee=Ni("MIN_ROWS",!0),Ae=Ni("STATS_SAMPLE_PAGES",!0),Te=Ni("CONNECTION",!0),Se=Ni("COMPRESSION",!0),Ue=Ni("'",!1),_e=Ni("ZLIB",!0),xe=Ni("LZ4",!0),Ie=Ni("ENGINE",!0),Ne=Ni("IN",!0),Re=Ni("ACCESS SHARE",!0),Ve=Ni("ROW SHARE",!0),Me=Ni("ROW EXCLUSIVE",!0),qe=Ni("SHARE UPDATE EXCLUSIVE",!0),Pe=Ni("SHARE ROW EXCLUSIVE",!0),De=Ni("ACCESS EXCLUSIVE",!0),Qe=Ni("SHARE",!0),Fe=Ni("MODE",!0),Ge=Ni("NOWAIT",!0),$e=Ni("TABLES",!0),Be=Ni("PREPARE",!0),He=Ni("USAGE",!0),Ye=function(t){return{type:"origin",value:Array.isArray(t)?t[0]:t}},We=Ni("CONNECT",!0),Xe=Ni("PRIVILEGES",!0),Ze=function(t){return{type:"origin",value:t}},Ke=Ni("SEQUENCE",!0),Je=Ni("DATABASE",!0),ze=Ni("DOMAIN",!1),tr=Ni("FUNCTION",!1),er=Ni("ROUTINE",!0),rr=Ni("LANGUAGE",!0),nr=Ni("LARGE",!0),or=Ni("SCHEMA",!1),ar=Ni("FUNCTIONS",!0),sr=Ni("PROCEDURES",!0),ur=Ni("ROUTINES",!0),ir=Ni("PUBLIC",!0),cr=Ni("GRANT",!0),lr=Ni("OPTION",!0),fr=Ni("ADMIN",!0),pr=Ni("REVOKE",!0),br=Ni("ELSEIF",!0),vr=Ni("THEN",!0),yr=Ni("END",!0),dr=Ni("DEBUG",!0),hr=Ni("LOG",!0),mr=Ni("INFO",!0),wr=Ni("NOTICE",!0),Lr=Ni("WARNING",!0),Or=Ni("EXCEPTION",!0),jr=Ni("MESSAGE",!0),Cr=Ni("DETAIL",!0),gr=Ni("HINT",!0),Er=Ni("ERRCODE",!0),Ar=Ni("COLUMN",!0),Tr=Ni("CONSTRAINT",!0),Sr=Ni("DATATYPE",!0),Ur=Ni("TABLE",!0),_r=Ni("SQLSTATE",!0),xr=Ni("RAISE",!0),Ir=Ni("LOOP",!0),Nr=Ni("begin",!0),Rr=Ni("commit",!0),kr=Ni("rollback",!0),Vr=Ni(";",!1),Mr=Ni("(",!1),qr=Ni(")",!1),Pr=Ni('"',!1),Dr=Ni("OUTFILE",!0),Qr=Ni("DUMPFILE",!0),Fr=Ni("BTREE",!0),Gr=Ni("HASH",!0),$r=Ni("GIST",!0),Br=Ni("GIN",!0),Hr=Ni("WITH",!0),Yr=Ni("PARSER",!0),Wr=Ni("VISIBLE",!0),Xr=Ni("INVISIBLE",!0),Zr=function(t,e){return e.unshift(t),e.forEach(t=>{const{table:e,as:r}=t;qv[e]=e,r&&(qv[r]=e),function(t){const e=Iv(t);t.clear(),e.forEach(e=>t.add(e))}(Vv)}),e},Kr=Ni("LATERAL",!0),Jr=Ni("TABLESAMPLE",!0),zr=Ni("REPEATABLE",!0),tn=Ni("CROSS",!0),en=Ni("FOLLOWING",!0),rn=Ni("PRECEDING",!0),nn=Ni("UNBOUNDED",!0),on=Ni("DO",!0),an=Ni("NOTHING",!0),sn=Ni("CONFLICT",!0),un=function(t,e){return _v(t,e)},cn=Ni("!",!1),ln=Ni(">=",!1),fn=Ni(">",!1),pn=Ni("<=",!1),bn=Ni("<>",!1),vn=Ni("<",!1),yn=Ni("!=",!1),dn=Ni("SIMILAR",!0),hn=Ni("!~*",!1),mn=Ni("~*",!1),wn=Ni("~",!1),Ln=Ni("!~",!1),On=Ni("ESCAPE",!0),jn=Ni("+",!1),Cn=Ni("-",!1),gn=Ni("*",!1),En=Ni("/",!1),An=Ni("%",!1),Tn=Ni("||",!1),Sn=Ni("$",!1),Un=Ni("?|",!1),_n=Ni("?&",!1),xn=Ni("?",!1),In=Ni("#-",!1),Nn=Ni("#>>",!1),Rn=Ni("#>",!1),kn=Ni("@>",!1),Vn=Ni("<@",!1),Mn=Ni("E",!0),qn=function(t){return{type:"default",value:t}},Pn=function(t){return!0===gv[t.toUpperCase()]},Dn=/^[^"]/,Qn=Ri(['"'],!0,!1),Fn=/^[^']/,Gn=Ri(["'"],!0,!1),$n=Ni("`",!1),Bn=/^[^`]/,Hn=Ri(["`"],!0,!1),Yn=/^[A-Za-z_\u4E00-\u9FA5]/,Wn=Ri([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),Xn=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,Zn=Ri([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),Kn=/^[A-Za-z0-9_\u4E00-\u9FA5\xC0-\u017F]/,Jn=Ri([["A","Z"],["a","z"],["0","9"],"_",["一","龥"],["À","ſ"]],!1,!1),zn=Ni(":",!1),to=Ni("OVER",!0),eo=Ni("FILTER",!0),ro=Ni("FIRST_VALUE",!0),no=Ni("LAST_VALUE",!0),oo=Ni("ROW_NUMBER",!0),ao=Ni("DENSE_RANK",!0),so=Ni("RANK",!0),uo=Ni("LAG",!0),io=Ni("LEAD",!0),co=Ni("NTH_VALUE",!0),lo=Ni("IGNORE",!0),fo=Ni("RESPECT",!0),po=Ni("percentile_cont",!0),bo=Ni("percentile_disc",!0),vo=Ni("within",!0),yo=Ni("mode",!0),ho=Ni("BOTH",!0),mo=Ni("LEADING",!0),wo=Ni("TRAILING",!0),Lo=Ni("trim",!0),Oo=Ni("crosstab",!0),jo=Ni("POSITION",!0),Co=Ni("now",!0),go=Ni("at",!0),Eo=Ni("zone",!0),Ao=Ni("CENTURY",!0),To=Ni("DAY",!0),So=Ni("DATE",!0),Uo=Ni("DECADE",!0),_o=Ni("DOW",!0),xo=Ni("DOY",!0),Io=Ni("EPOCH",!0),No=Ni("HOUR",!0),Ro=Ni("ISODOW",!0),ko=Ni("ISOYEAR",!0),Vo=Ni("MICROSECONDS",!0),Mo=Ni("MILLENNIUM",!0),qo=Ni("MILLISECONDS",!0),Po=Ni("MINUTE",!0),Do=Ni("MONTH",!0),Qo=Ni("QUARTER",!0),Fo=Ni("SECOND",!0),Go=Ni("TIMEZONE",!0),$o=Ni("TIMEZONE_HOUR",!0),Bo=Ni("TIMEZONE_MINUTE",!0),Ho=Ni("WEEK",!0),Yo=Ni("YEAR",!0),Wo=Ni("NTILE",!0),Xo=/^[\n]/,Zo=Ri(["\n"],!1,!1),Ko=/^[^"\\\0-\x1F\x7F]/,Jo=Ri(['"',"\\",["\0",""],""],!0,!1),zo=/^[^'\\]/,ta=Ri(["'","\\"],!0,!1),ea=Ni("\\'",!1),ra=Ni('\\"',!1),na=Ni("\\\\",!1),oa=Ni("\\/",!1),aa=Ni("\\b",!1),sa=Ni("\\f",!1),ua=Ni("\\n",!1),ia=Ni("\\r",!1),ca=Ni("\\t",!1),la=Ni("\\u",!1),fa=Ni("\\",!1),pa=Ni("''",!1),ba=/^[\n\r]/,va=Ri(["\n","\r"],!1,!1),ya=Ni(".",!1),da=/^[0-9]/,ha=Ri([["0","9"]],!1,!1),ma=/^[0-9a-fA-F]/,wa=Ri([["0","9"],["a","f"],["A","F"]],!1,!1),La=/^[eE]/,Oa=Ri(["e","E"],!1,!1),ja=/^[+\-]/,Ca=Ri(["+","-"],!1,!1),ga=Ni("NOT NULL",!0),Ea=Ni("TRUE",!0),Aa=Ni("FALSE",!0),Ta=Ni("SHOW",!0),Sa=Ni("DROP",!0),Ua=Ni("USE",!0),_a=Ni("ALTER",!0),xa=Ni("SELECT",!0),Ia=Ni("UPDATE",!0),Na=Ni("CREATE",!0),Ra=Ni("TEMPORARY",!0),ka=Ni("TEMP",!0),Va=Ni("DELETE",!0),Ma=Ni("INSERT",!0),qa=Ni("RECURSIVE",!0),Pa=Ni("REPLACE",!0),Da=Ni("RETURNING",!0),Qa=Ni("RENAME",!0),Fa=(Ni("EXPLAIN",!0),Ni("PARTITION",!0)),Ga=Ni("INTO",!0),$a=Ni("FROM",!0),Ba=Ni("AS",!0),Ha=Ni("TABLESPACE",!0),Ya=Ni("DEALLOCATE",!0),Wa=Ni("LEFT",!0),Xa=Ni("RIGHT",!0),Za=Ni("FULL",!0),Ka=Ni("INNER",!0),Ja=Ni("JOIN",!0),za=Ni("OUTER",!0),ts=Ni("UNION",!0),es=Ni("INTERSECT",!0),rs=Ni("EXCEPT",!0),ns=Ni("VALUES",!0),os=Ni("USING",!0),as=Ni("WHERE",!0),ss=Ni("GROUP",!0),us=Ni("BY",!0),is=Ni("ORDER",!0),cs=Ni("HAVING",!0),ls=Ni("QUALIFY",!0),fs=Ni("LIMIT",!0),ps=Ni("OFFSET",!0),bs=Ni("ASC",!0),vs=Ni("DESC",!0),ys=Ni("ALL",!0),ds=Ni("DISTINCT",!0),hs=Ni("BETWEEN",!0),ms=Ni("IS",!0),ws=Ni("LIKE",!0),Ls=Ni("ILIKE",!0),Os=Ni("EXISTS",!0),js=Ni("AND",!0),Cs=Ni("OR",!0),gs=Ni("ARRAY",!0),Es=Ni("ARRAY_AGG",!0),As=Ni("STRING_AGG",!0),Ts=Ni("COUNT",!0),Ss=Ni("GROUP_CONCAT",!0),Us=Ni("MAX",!0),_s=Ni("MIN",!0),xs=Ni("SUM",!0),Is=Ni("AVG",!0),Ns=Ni("EXTRACT",!0),Rs=Ni("CALL",!0),ks=Ni("CASE",!0),Vs=Ni("WHEN",!0),Ms=Ni("ELSE",!0),qs=Ni("CAST",!0),Ps=Ni("BOOL",!0),Ds=Ni("BOOLEAN",!0),Qs=Ni("CHAR",!0),Fs=Ni("VARCHAR",!0),Gs=Ni("NUMERIC",!0),$s=Ni("DECIMAL",!0),Bs=Ni("SIGNED",!0),Hs=Ni("UNSIGNED",!0),Ys=Ni("INT",!0),Ws=Ni("ZEROFILL",!0),Xs=Ni("INTEGER",!0),Zs=Ni("JSON",!0),Ks=Ni("JSONB",!0),Js=Ni("GEOMETRY",!0),zs=Ni("SMALLINT",!0),tu=Ni("SERIAL",!0),eu=Ni("TINYINT",!0),ru=Ni("TINYTEXT",!0),nu=Ni("TEXT",!0),ou=Ni("MEDIUMTEXT",!0),au=Ni("LONGTEXT",!0),su=Ni("BIGINT",!0),uu=Ni("ENUM",!0),iu=Ni("FLOAT",!0),cu=Ni("DOUBLE",!0),lu=Ni("BIGSERIAL",!0),fu=Ni("REAL",!0),pu=Ni("DATETIME",!0),bu=Ni("TIME",!0),vu=Ni("TIMESTAMP",!0),yu=Ni("TRUNCATE",!0),du=Ni("USER",!0),hu=Ni("UUID",!0),mu=Ni("OID",!0),wu=Ni("REGCLASS",!0),Lu=Ni("REGCOLLATION",!0),Ou=Ni("REGCONFIG",!0),ju=Ni("REGDICTIONARY",!0),Cu=Ni("REGNAMESPACE",!0),gu=Ni("REGOPER",!0),Eu=Ni("REGOPERATOR",!0),Au=Ni("REGPROC",!0),Tu=Ni("REGPROCEDURE",!0),Su=Ni("REGROLE",!0),Uu=Ni("REGTYPE",!0),_u=Ni("CURRENT_DATE",!0),xu=(Ni("ADDDATE",!0),Ni("INTERVAL",!0)),Iu=Ni("CURRENT_TIME",!0),Nu=Ni("CURRENT_TIMESTAMP",!0),Ru=Ni("SYSTEM_USER",!0),ku=Ni("GLOBAL",!0),Vu=Ni("SESSION",!0),Mu=Ni("PERSIST",!0),qu=Ni("PERSIST_ONLY",!0),Pu=Ni("VIEW",!0),Du=Ni("@",!1),Qu=Ni("@@",!1),Fu=Ni("$$",!1),Gu=Ni("return",!0),$u=Ni("::",!1),Bu=Ni("DUAL",!0),Hu=Ni("ADD",!0),Yu=Ni("INDEX",!0),Wu=Ni("FULLTEXT",!0),Xu=Ni("SPATIAL",!0),Zu=Ni("COMMENT",!0),Ku=Ni("CONCURRENTLY",!0),Ju=Ni("REFERENCES",!0),zu=Ni("SQL_CALC_FOUND_ROWS",!0),ti=Ni("SQL_CACHE",!0),ei=Ni("SQL_NO_CACHE",!0),ri=Ni("SQL_SMALL_RESULT",!0),ni=Ni("SQL_BIG_RESULT",!0),oi=Ni("SQL_BUFFER_RESULT",!0),ai=Ni(",",!1),si=Ni("[",!1),ui=Ni("]",!1),ii=Ni("->",!1),ci=Ni("->>",!1),li=Ni("&&",!1),fi=Ni("/*",!1),pi=Ni("*/",!1),bi=Ni("--",!1),vi=(Ni("#",!1),{type:"any"}),yi=/^[ \t\n\r]/,di=Ri([" ","\t","\n","\r"],!1,!1),hi=/^[^$]/,mi=Ri(["$"],!0,!1),wi=function(t){return{dataType:t}},Li=Ni("bytea",!0),Oi=Ni("varying",!0),ji=Ni("PRECISION",!0),Ci=Ni("WITHOUT",!0),gi=Ni("ZONE",!0),Ei=function(t){return{dataType:t}},Ai=Ni("RECORD",!0),Ti=0,Si=0,Ui=[{line:1,column:1}],_i=0,xi=[],Ii=0;if("startRule"in e){if(!(e.startRule in s))throw new Error("Can't start parsing from rule \""+e.startRule+'".');u=s[e.startRule]}function Ni(t,e){return{type:"literal",text:t,ignoreCase:e}}function Ri(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function ki(e){var r,n=Ui[e];if(n)return n;for(r=e-1;!Ui[r];)r--;for(n={line:(n=Ui[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return Ui[e]=n,n}function Vi(t,e){var r=ki(t),n=ki(e);return{start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function Mi(t){Ti<_i||(Ti>_i&&(_i=Ti,xi=[]),xi.push(t))}function qi(t,e,r){return new o(o.buildMessage(t,e),t,e,r)}function Pi(){var t,e;return t=Ti,tv()!==a?((e=tc())===a&&(e=Fi()),e!==a?(Si=t,t=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=tc())===a&&(t=Fi()),t}function Di(){var e;return(e=function(){var e,r,n,o,s,u,c,l,f;e=Ti,(r=op())!==a&&tv()!==a&&(n=jp())!==a&&tv()!==a&&(o=al())!==a?(Si=e,p=r,b=n,(v=o)&&v.forEach(t=>kv.add(`${p}::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:p.toLowerCase(),keyword:b.toLowerCase(),name:v}},e=r):(Ti=e,e=a);var p,b,v;e===a&&(e=Ti,(r=op())!==a&&tv()!==a&&(n=Vb())!==a&&tv()!==a?((o=Qb())===a&&(o=null),o!==a&&tv()!==a?(s=Ti,"if"===t.substr(Ti,2).toLowerCase()?(u=t.substr(Ti,2),Ti+=2):(u=a,0===Ii&&Mi(i)),u!==a&&(c=tv())!==a&&(l=Fp())!==a?s=u=[u,c,l]:(Ti=s,s=a),s===a&&(s=null),s!==a&&(u=tv())!==a&&(c=ef())!==a&&(l=tv())!==a?("cascade"===t.substr(Ti,7).toLowerCase()?(f=t.substr(Ti,7),Ti+=7):(f=a,0===Ii&&Mi(Rt)),f===a&&("restrict"===t.substr(Ti,8).toLowerCase()?(f=t.substr(Ti,8),Ti+=8):(f=a,0===Ii&&Mi(kt))),f===a&&(f=null),f!==a?(Si=e,r=function(t,e,r,n,o,a){return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t.toLowerCase(),keyword:e.toLowerCase(),prefix:r,name:o,options:a&&[{type:"origin",value:a}]}}}(r,n,o,0,c,f),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a));return e}())===a&&(e=function(){var e;(e=function(){var t,e,r,n,o,s,u,i,c,l;t=Ti,(e=ip())!==a&&tv()!==a?((r=cp())===a&&(r=null),r!==a&&tv()!==a&&jp()!==a&&tv()!==a?((n=Bi())===a&&(n=null),n!==a&&tv()!==a&&(o=al())!==a&&tv()!==a&&(s=function(){var t,e,r,n,o,s,u,i,c;if(t=Ti,(e=Hb())!==a)if(tv()!==a)if((r=nc())!==a){for(n=[],o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=nc())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);o!==a;)n.push(o),o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=nc())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);n!==a&&(o=tv())!==a&&(s=Yb())!==a?(Si=t,e=Uv(r,n),t=e):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;return t}())!==a&&tv()!==a?((u=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Uc())!==a){for(r=[],n=Ti,(o=tv())!==a?((s=$b())===a&&(s=null),s!==a&&(u=tv())!==a&&(i=Uc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a?((s=$b())===a&&(s=null),s!==a&&(u=tv())!==a&&(i=Uc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())===a&&(u=null),u!==a&&tv()!==a?((i=dp())===a&&(i=vp()),i===a&&(i=null),i!==a&&tv()!==a?((c=Op())===a&&(c=null),c!==a&&tv()!==a?((l=$i())===a&&(l=null),l!==a?(Si=t,f=e,p=r,b=n,y=s,d=u,h=i,m=c,w=l,(v=o)&&v.forEach(t=>kv.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:h&&h[0].toLowerCase(),as:m&&m[0].toLowerCase(),query_expr:w&&w.ast,create_definitions:y,table_options:d}},t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);var f,p,b,v,y,d,h,m,w;t===a&&(t=Ti,(e=ip())!==a&&tv()!==a?((r=cp())===a&&(r=null),r!==a&&tv()!==a&&jp()!==a&&tv()!==a?((n=Bi())===a&&(n=null),n!==a&&tv()!==a&&(o=al())!==a&&tv()!==a&&(s=function t(){var e,r;(e=function(){var t,e;t=Ti,Dp()!==a&&tv()!==a&&(e=al())!==a?(Si=t,t={type:"like",table:e}):(Ti=t,t=a);return t}())===a&&(e=Ti,Hb()!==a&&tv()!==a&&(r=t())!==a&&tv()!==a&&Yb()!==a?(Si=e,(n=r).parentheses=!0,e=n):(Ti=e,e=a));var n;return e}())!==a?(Si=t,e=function(t,e,r,n,o){return n&&n.forEach(t=>kv.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:e&&e[0].toLowerCase(),if_not_exists:r,table:n,like:o}}}(e,r,n,o,s),t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a));return t}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m,w,L,O,j;e=Ti,(r=ip())!==a&&tv()!==a?(n=Ti,(o=Bp())!==a&&(s=tv())!==a&&(u=vp())!==a?n=o=[o,s,u]:(Ti=n,n=a),n===a&&(n=null),n!==a&&(o=tv())!==a?((s=Db())===a&&(s=null),s!==a&&(u=tv())!==a?("trigger"===t.substr(Ti,7).toLowerCase()?(i=t.substr(Ti,7),Ti+=7):(i=a,0===Ii&&Mi(oe)),i!==a&&tv()!==a&&(c=wf())!==a&&tv()!==a?("before"===t.substr(Ti,6).toLowerCase()?(l=t.substr(Ti,6),Ti+=6):(l=a,0===Ii&&Mi(ae)),l===a&&("after"===t.substr(Ti,5).toLowerCase()?(l=t.substr(Ti,5),Ti+=5):(l=a,0===Ii&&Mi(se)),l===a&&("instead of"===t.substr(Ti,10).toLowerCase()?(l=t.substr(Ti,10),Ti+=10):(l=a,0===Ii&&Mi(ue)))),l!==a&&tv()!==a&&(f=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Ac())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Bp())!==a&&(u=tv())!==a&&(i=Ac())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Bp())!==a&&(u=tv())!==a&&(i=Ac())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&tv()!==a?("on"===t.substr(Ti,2).toLowerCase()?(p=t.substr(Ti,2),Ti+=2):(p=a,0===Ii&&Mi(Q)),p!==a&&tv()!==a&&(b=cl())!==a&&tv()!==a?(v=Ti,(y=wp())!==a&&(d=tv())!==a&&(h=cl())!==a?v=y=[y,d,h]:(Ti=v,v=a),v===a&&(v=null),v!==a&&(y=tv())!==a?((d=function(){var e,r,n,o,s;e=Ti,r=Ti,"not"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(M));n===a&&(n=null);n!==a&&(o=tv())!==a?("deferrable"===t.substr(Ti,10).toLowerCase()?(s=t.substr(Ti,10),Ti+=10):(s=a,0===Ii&&Mi(fe)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a);r!==a&&(n=tv())!==a?("initially immediate"===t.substr(Ti,19).toLowerCase()?(o=t.substr(Ti,19),Ti+=19):(o=a,0===Ii&&Mi(pe)),o===a&&("initially deferred"===t.substr(Ti,18).toLowerCase()?(o=t.substr(Ti,18),Ti+=18):(o=a,0===Ii&&Mi(be))),o!==a?(Si=e,i=o,r={keyword:(u=r)&&u[0]?u[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},e=r):(Ti=e,e=a)):(Ti=e,e=a);var u,i;return e}())===a&&(d=null),d!==a&&(h=tv())!==a?((m=function(){var e,r,n,o;e=Ti,"for"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(ve));r!==a&&tv()!==a?("each"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(ye)),n===a&&(n=null),n!==a&&tv()!==a?("row"===t.substr(Ti,3).toLowerCase()?(o=t.substr(Ti,3),Ti+=3):(o=a,0===Ii&&Mi(de)),o===a&&("statement"===t.substr(Ti,9).toLowerCase()?(o=t.substr(Ti,9),Ti+=9):(o=a,0===Ii&&Mi(he))),o!==a?(Si=e,s=r,i=o,r={keyword:(u=n)?`${s.toLowerCase()} ${u.toLowerCase()}`:s.toLowerCase(),args:i.toLowerCase()},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var s,u,i;return e}())===a&&(m=null),m!==a&&tv()!==a?((w=function(){var t,e;t=Ti,Xp()!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(e=Pl())!==a&&tv()!==a&&Yb()!==a?(Si=t,t={type:"when",cond:e,parentheses:!0}):(Ti=t,t=a);return t}())===a&&(w=null),w!==a&&tv()!==a?("execute"===t.substr(Ti,7).toLowerCase()?(L=t.substr(Ti,7),Ti+=7):(L=a,0===Ii&&Mi(ie)),L!==a&&tv()!==a?("procedure"===t.substr(Ti,9).toLowerCase()?(O=t.substr(Ti,9),Ti+=9):(O=a,0===Ii&&Mi(ce)),O===a&&("function"===t.substr(Ti,8).toLowerCase()?(O=t.substr(Ti,8),Ti+=8):(O=a,0===Ii&&Mi(ut))),O!==a&&tv()!==a&&(j=vv())!==a?(Si=e,C=s,g=i,A=f,T=b,S=v,U=d,_=m,x=w,I=O,N=j,r={type:"create",replace:n&&"or replace",constraint:c,location:(E=l)&&E.toLowerCase(),events:A,table:T,from:S&&S[2],deferrable:U,for_each:_,when:x,execute:{keyword:"execute "+I.toLowerCase(),expr:N},constraint_type:g&&g.toLowerCase(),keyword:g&&g.toLowerCase(),constraint_kw:C&&C.toLowerCase(),resource:"constraint"},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var C,g,E,A,T,S,U,_,x,I,N;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,p,b,v,y,d,h,m;e=Ti,(r=ip())!==a&&tv()!==a?("extension"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(c)),n!==a&&tv()!==a?((o=Bi())===a&&(o=null),o!==a&&tv()!==a?((s=wf())===a&&(s=Ff()),s!==a&&tv()!==a?((u=Up())===a&&(u=null),u!==a&&tv()!==a?(i=Ti,"schema"===t.substr(Ti,6).toLowerCase()?(p=t.substr(Ti,6),Ti+=6):(p=a,0===Ii&&Mi(l)),p!==a&&(b=tv())!==a&&(v=wf())!==a?i=p=[p,b,v]:(Ti=i,i=a),i===a&&(i=Ff()),i===a&&(i=null),i!==a&&(p=tv())!==a?(b=Ti,"version"===t.substr(Ti,7).toLowerCase()?(v=t.substr(Ti,7),Ti+=7):(v=a,0===Ii&&Mi(f)),v!==a&&(y=tv())!==a?((d=wf())===a&&(d=Ff()),d!==a?b=v=[v,y,d]:(Ti=b,b=a)):(Ti=b,b=a),b===a&&(b=null),b!==a&&(v=tv())!==a?(y=Ti,(d=wp())!==a&&(h=tv())!==a?((m=wf())===a&&(m=Ff()),m!==a?y=d=[d,h,m]:(Ti=y,y=a)):(Ti=y,y=a),y===a&&(y=null),y!==a?(Si=e,w=o,L=s,O=u,j=i,C=b,g=y,r={type:"create",keyword:n.toLowerCase(),if_not_exists:w,extension:Nv(L),with:O&&O[0].toLowerCase(),schema:Nv(j&&j[2].toLowerCase()),version:Nv(C&&C[2]),from:Nv(g&&g[2])},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var w,L,O,j,C,g;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m,w;e=Ti,(r=ip())!==a&&tv()!==a?((n=qb())===a&&(n=null),n!==a&&tv()!==a&&(o=Vb())!==a&&tv()!==a?((s=Qb())===a&&(s=null),s!==a&&tv()!==a?((u=sf())===a&&(u=null),u!==a&&tv()!==a&&(i=gp())!==a&&tv()!==a&&(c=cl())!==a&&tv()!==a?((l=rl())===a&&(l=null),l!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(f=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=rc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=rc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=rc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&tv()!==a&&Yb()!==a&&tv()!==a?(p=Ti,(b=Up())!==a&&(v=tv())!==a&&(y=Hb())!==a&&(d=tv())!==a&&(h=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=ol())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=ol())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=ol())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&(m=tv())!==a&&(w=Yb())!==a?p=b=[b,v,y,d,h,m,w]:(Ti=p,p=a),p===a&&(p=null),p!==a&&(b=tv())!==a?(v=Ti,(y=function(){var e,r,n,o;e=Ti,"tablespace"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Ha));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TABLESPACE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(d=tv())!==a&&(h=wf())!==a?v=y=[y,d,h]:(Ti=v,v=a),v===a&&(v=null),v!==a&&(y=tv())!==a?((d=pl())===a&&(d=null),d!==a&&(h=tv())!==a?(Si=e,L=r,O=n,j=o,C=s,g=u,E=i,A=c,T=l,S=f,U=p,_=v,x=d,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:L[0].toLowerCase(),index_type:O&&O.toLowerCase(),keyword:j.toLowerCase(),concurrently:C&&C.toLowerCase(),index:g,on_kw:E[0].toLowerCase(),table:A,index_using:T,index_columns:S,with:U&&U[4],with_before_where:!0,tablespace:_&&{type:"origin",value:_[2]},where:x}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var L,O,j,C,g,E,A,T,S,U,_,x;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=ip())!==a&&tv()!==a?((n=cp())===a&&(n=lp()),n===a&&(n=null),n!==a&&tv()!==a&&function(){var e,r,n,o;e=Ti,"sequence"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Ke));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SEQUENCE"):(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a?((o=Bi())===a&&(o=null),o!==a&&tv()!==a&&(s=cl())!==a&&tv()!==a?(u=Ti,(i=Op())!==a&&(c=tv())!==a&&(l=cf())!==a?u=i=[i,c,l]:(Ti=u,u=a),u===a&&(u=null),u!==a&&(i=tv())!==a?((c=function(){var t,e,r,n,o,s;if(t=Ti,(e=ec())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=ec())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=ec())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r,1),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())===a&&(c=null),c!==a?(Si=e,f=r,p=n,b=o,y=u,d=c,(v=s).as=y&&y[2],r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:f[0].toLowerCase(),keyword:"sequence",temporary:p&&p[0].toLowerCase(),if_not_exists:b,sequence:[v],create_definitions:d}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var f,p,b,v,y,d;return e}())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=ip())!==a&&tv()!==a?((n=function(){var e,r,n,o;e=Ti,"database"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Je));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DATABASE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(n=Cp()),n!==a&&tv()!==a?((o=Bi())===a&&(o=null),o!==a&&tv()!==a&&(s=bv())!==a&&tv()!==a?((u=function(){var t,e,r,n,o,s;if(t=Ti,(e=Sc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Sc())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Sc())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r,1),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())===a&&(u=null),u!==a?(Si=e,r=function(t,e,r,n,o){const a=e.toLowerCase();return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:r,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(r,n,o,s,u),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=ip())!==a&&tv()!==a?("domain"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(ct)),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a?((s=Op())===a&&(s=null),s!==a&&tv()!==a&&(u=mv())!==a&&tv()!==a?((i=sc())===a&&(i=null),i!==a&&tv()!==a?((c=ic())===a&&(c=null),c!==a&&tv()!==a?((l=Cc())===a&&(l=null),l!==a?(Si=e,r=function(t,e,r,n,o,a,s,u){u&&(u.type="constraint");const i=[a,s,u].filter(t=>t);return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t[0].toLowerCase(),keyword:e.toLowerCase(),domain:{schema:r.db,name:r.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i}}}(r,n,o,s,u,i,c,l),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s,u,i;e=Ti,(r=ip())!==a&&tv()!==a?("type"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(it)),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a&&(s=Op())!==a&&tv()!==a&&(u=vb())!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((i=Nl())===a&&(i=null),i!==a&&tv()!==a&&Yb()!==a?(Si=e,c=r,l=n,f=o,p=s,b=u,(v=i).parentheses=!0,Mv.add([f.db,f.table].filter(t=>t).join(".")),r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:c[0].toLowerCase(),keyword:l.toLowerCase(),name:{schema:f.db,name:f.table},as:p&&p[0]&&p[0].toLowerCase(),resource:b.toLowerCase(),create_definitions:v}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var c,l,f,p,b,v;e===a&&(e=Ti,(r=ip())!==a&&tv()!==a?("type"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(it)),n!==a&&tv()!==a&&(o=cl())!==a?(Si=e,r=function(t,e,r){return Mv.add([r.db,r.table].filter(t=>t).join(".")),{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t[0].toLowerCase(),keyword:e.toLowerCase(),name:{schema:r.db,name:r.table}}}}(r,n,o),e=r):(Ti=e,e=a)):(Ti=e,e=a));return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l,f,d,h,m,w,L,O,j,C;e=Ti,(r=ip())!==a&&tv()!==a?(n=Ti,(o=Bp())!==a&&(s=tv())!==a&&(u=vp())!==a?n=o=[o,s,u]:(Ti=n,n=a),n===a&&(n=null),n!==a&&(o=tv())!==a?((s=lp())===a&&(s=cp()),s===a&&(s=null),s!==a&&(u=tv())!==a?((i=bp())===a&&(i=null),i!==a&&tv()!==a&&function(){var e,r,n,o;e=Ti,"view"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Pu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="VIEW"):(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(c=cl())!==a&&tv()!==a?(l=Ti,(f=Hb())!==a&&(d=tv())!==a&&(h=nf())!==a&&(m=tv())!==a&&(w=Yb())!==a?l=f=[f,d,h,m,w]:(Ti=l,l=a),l===a&&(l=null),l!==a&&(f=tv())!==a?(d=Ti,(h=Up())!==a&&(m=tv())!==a&&(w=Hb())!==a&&(L=tv())!==a&&(O=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Hi())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Hi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Hi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&(j=tv())!==a&&(C=Yb())!==a?d=h=[h,m,w,L,O,j,C]:(Ti=d,d=a),d===a&&(d=null),d!==a&&(h=tv())!==a&&(m=Op())!==a&&(w=tv())!==a&&(L=$c())!==a&&(O=tv())!==a?((j=function(){var e,r,n,o,s;e=Ti,(r=Up())!==a&&tv()!==a?("cascaded"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(p)),n===a&&("local"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(b))),n!==a&&tv()!==a?("check"===t.substr(Ti,5).toLowerCase()?(o=t.substr(Ti,5),Ti+=5):(o=a,0===Ii&&Mi(v)),o!==a&&tv()!==a?("OPTION"===t.substr(Ti,6)?(s="OPTION",Ti+=6):(s=a,0===Ii&&Mi(y)),s!==a?(Si=e,r=`with ${n.toLowerCase()} check option`,e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);e===a&&(e=Ti,(r=Up())!==a&&tv()!==a?("check"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(v)),n!==a&&tv()!==a?("OPTION"===t.substr(Ti,6)?(o="OPTION",Ti+=6):(o=a,0===Ii&&Mi(y)),o!==a?(Si=e,e=r="with check option"):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a));return e}())===a&&(j=null),j!==a?(Si=e,g=r,E=n,A=s,T=i,U=l,_=d,x=L,I=j,(S=c).view=S.table,delete S.table,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:g[0].toLowerCase(),keyword:"view",replace:E&&"or replace",temporary:A&&A[0].toLowerCase(),recursive:T&&T.toLowerCase(),columns:U&&U[2],select:x,view:S,with_options:_&&_[4],with:I}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var g,E,A,T,S,U,_,x,I;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=ip())!==a&&tv()!==a?(n=Ti,(o=Bp())!==a&&(s=tv())!==a&&(u=vp())!==a?n=o=[o,s,u]:(Ti=n,n=a),n===a&&(n=null),n!==a&&(o=tv())!==a?("aggregate"===t.substr(Ti,9).toLowerCase()?(s=t.substr(Ti,9),Ti+=9):(s=a,0===Ii&&Mi(O)),s!==a&&(u=tv())!==a&&(i=cl())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(c=cc())!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(l=function(){var e,r,n,o,s,u,i,c;if(e=Ti,(r=function(){var e,r,n,o,s;e=Ti,"sfunc"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(w));r!==a&&tv()!==a&&Nb()!==a&&tv()!==a&&(n=cl())!==a&&tv()!==a&&$b()!==a&&tv()!==a?("stype"===t.substr(Ti,5).toLowerCase()?(o=t.substr(Ti,5),Ti+=5):(o=a,0===Ii&&Mi(L)),o!==a&&tv()!==a&&Nb()!==a&&tv()!==a&&(s=mv())!==a?(Si=e,i=s,r=[{type:"sfunc",symbol:"=",value:{schema:(u=n).db,name:u.table}},{type:"stype",symbol:"=",value:i}],e=r):(Ti=e,e=a)):(Ti=e,e=a);var u,i;return e}())!==a){for(n=[],o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=Yi())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);o!==a;)n.push(o),o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=Yi())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);n!==a?(Si=e,r=Uv(r,n),e=r):(Ti=e,e=a)}else Ti=e,e=a;return e}())!==a&&tv()!==a&&Yb()!==a?(Si=e,f=i,p=c,b=l,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"create",keyword:"aggregate",name:{schema:f.db,name:f.table},args:{parentheses:!0,expr:p,orderby:p.orderby},options:b}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var f,p,b;return e}());return e}())===a&&(e=Ji())===a&&(e=function(){var t,e,r,n;t=Ti,(e=gb())!==a&&tv()!==a?((r=jp())===a&&(r=null),r!==a&&tv()!==a&&(n=al())!==a?(Si=t,o=e,s=r,(u=n)&&u.forEach(t=>kv.add(`${o}::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:o.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:u}},t=e):(Ti=t,t=a)):(Ti=t,t=a);var o,s,u;return t}())===a&&(e=function(){var t,e,r;t=Ti,(e=yp())!==a&&tv()!==a&&jp()!==a&&tv()!==a&&(r=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=el())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=el())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=el())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=t,(n=r).forEach(t=>t.forEach(t=>t.table&&kv.add(`rename::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`))),e={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"rename",table:n}},t=e):(Ti=t,t=a);var n;return t}())===a&&(e=function(){var e,r,n;e=Ti,(r=function(){var e,r,n,o;e=Ti,"call"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Rs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CALL"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a&&(n=vv())!==a?(Si=e,o=n,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"call",expr:o}},e=r):(Ti=e,e=a);var o;return e}())===a&&(e=function(){var e,r,n;e=Ti,(r=function(){var e,r,n,o;e=Ti,"use"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Ua));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a&&(n=sf())!==a?(Si=e,o=n,kv.add(`use::${o}::null`),r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"use",db:o}},e=r):(Ti=e,e=a);var o;return e}())===a&&(e=function(){var e;(e=function(){var t,e,r,n;t=Ti,(e=ap())!==a&&tv()!==a&&jp()!==a&&tv()!==a&&(r=al())!==a&&tv()!==a&&(n=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=bc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=bc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=bc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=t,s=n,(o=r)&&o.length>0&&o.forEach(t=>kv.add(`alter::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"alter",table:o,expr:s}},t=e):(Ti=t,t=a);var o,s;return t}())===a&&(e=function(){var t,e,r,n,o;t=Ti,(e=ap())!==a&&tv()!==a&&(r=Cp())!==a&&tv()!==a&&(n=wf())!==a&&tv()!==a?((o=vc())===a&&(o=yc())===a&&(o=dc()),o!==a?(Si=t,e=function(t,e,r){const n=t.toLowerCase();return r.resource=n,r[n]=r.table,delete r.table,{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"alter",keyword:n,schema:e,expr:r}}}(r,n,o),t=e):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(e=function(){var e,r,n,o,s;e=Ti,(r=ap())!==a&&tv()!==a?("domain"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(ct)),n===a&&("type"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(it))),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a?((s=vc())===a&&(s=yc())===a&&(s=dc()),s!==a?(Si=e,r=function(t,e,r){const n=t.toLowerCase();return r.resource=n,r[n]=r.table,delete r.table,{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"alter",keyword:n,name:{schema:e.db,name:e.table},expr:r}}}(n,o,s),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l,f;e=Ti,(r=ap())!==a&&tv()!==a?("function"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(ut)),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a?(s=Ti,(u=Hb())!==a&&(i=tv())!==a?((c=pc())===a&&(c=null),c!==a&&(l=tv())!==a&&(f=Yb())!==a?s=u=[u,i,c,l,f]:(Ti=s,s=a)):(Ti=s,s=a),s===a&&(s=null),s!==a&&(u=tv())!==a?((i=vc())===a&&(i=yc())===a&&(i=dc()),i!==a?(Si=e,r=function(t,e,r,n){const o=t.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const a={};return r&&r[0]&&(a.parentheses=!0),a.expr=r&&r[2],{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"alter",keyword:o,name:{schema:e.db,name:e.table},args:a,expr:n}}}(n,o,s,i),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=ap())!==a&&tv()!==a?("aggregate"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(O)),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(s=cc())!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((u=vc())===a&&(u=yc())===a&&(u=dc()),u!==a?(Si=e,r=function(t,e,r,n){const o=t.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"alter",keyword:o,name:{schema:e.db,name:e.table},args:{parentheses:!0,expr:r,orderby:r.orderby},expr:n}}}(n,o,s,u),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}());return e}())===a&&(e=function(){var e,r,n,o;e=Ti,(r=Lp())!==a&&tv()!==a?((n=function(){var e,r,n,o;e=Ti,"global"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(ku));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="GLOBAL"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ti,"session"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Vu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SESSION"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ti,"local"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(b));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="LOCAL"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ti,"persist"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Mu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="PERSIST"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ti,"persist_only"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(qu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="PERSIST_ONLY"):(Ti=e,e=a)):(Ti=e,e=a);return e}()),n===a&&(n=null),n!==a&&tv()!==a&&(o=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=iv())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=iv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=iv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=e,s=n,(u=o).keyword=s,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"set",keyword:s,expr:u}},e=r):(Ti=e,e=a)):(Ti=e,e=a);var s,u;return e}())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=function(){var e,r,n,o;e=Ti,"lock"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Yt));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a?((n=jp())===a&&(n=null),n!==a&&tv()!==a&&(o=al())!==a&&tv()!==a?((s=function(){var e,r,n,o;e=Ti,"in"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(Ne));r!==a&&tv()!==a?("access share"===t.substr(Ti,12).toLowerCase()?(n=t.substr(Ti,12),Ti+=12):(n=a,0===Ii&&Mi(Re)),n===a&&("row share"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(Ve)),n===a&&("row exclusive"===t.substr(Ti,13).toLowerCase()?(n=t.substr(Ti,13),Ti+=13):(n=a,0===Ii&&Mi(Me)),n===a&&("share update exclusive"===t.substr(Ti,22).toLowerCase()?(n=t.substr(Ti,22),Ti+=22):(n=a,0===Ii&&Mi(qe)),n===a&&("share row exclusive"===t.substr(Ti,19).toLowerCase()?(n=t.substr(Ti,19),Ti+=19):(n=a,0===Ii&&Mi(Pe)),n===a&&("exclusive"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(Xt)),n===a&&("access exclusive"===t.substr(Ti,16).toLowerCase()?(n=t.substr(Ti,16),Ti+=16):(n=a,0===Ii&&Mi(De)),n===a&&("share"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(Qe))))))))),n!==a&&tv()!==a?("mode"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(Fe)),o!==a?(Si=e,r={mode:`in ${n.toLowerCase()} mode`},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(s=null),s!==a&&tv()!==a?("nowait"===t.substr(Ti,6).toLowerCase()?(u=t.substr(Ti,6),Ti+=6):(u=a,0===Ii&&Mi(Ge)),u===a&&(u=null),u!==a?(Si=e,i=n,l=s,f=u,(c=o)&&c.forEach(t=>kv.add(`lock::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(t=>({table:t})),lock_mode:l,nowait:f}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var i,c,l,f;return e}())===a&&(e=function(){var e,r,n;e=Ti,(r=np())!==a&&tv()!==a?("tables"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi($e)),n!==a?(Si=e,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"show",keyword:"tables"}},e=r):(Ti=e,e=a)):(Ti=e,e=a);e===a&&(e=Ti,(r=np())!==a&&tv()!==a&&(n=hv())!==a?(Si=e,o=n,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"show",keyword:"var",var:o}},e=r):(Ti=e,e=a));var o;return e}())===a&&(e=function(){var e,r,n,o;e=Ti,(r=function(){var e,r,n,o;e=Ti,"deallocate"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Ya));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DEALLOCATE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a?("prepare"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(Be)),n===a&&(n=null),n!==a&&tv()!==a?((o=wf())===a&&(o=kp()),o!==a?(Si=e,s=n,u=o,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"deallocate",keyword:s,expr:{type:"default",value:u}}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var s,u;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l,f,p;e=Ti,(r=Mc())!==a&&tv()!==a&&(n=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Nc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Nc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Nc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&tv()!==a&&(o=gp())!==a&&tv()!==a?((s=function(){var e,r,n;e=Ti,(r=jp())===a&&("sequence"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Ke)),r===a&&("database"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Je)),r===a&&("DOMAIN"===t.substr(Ti,6)?(r="DOMAIN",Ti+=6):(r=a,0===Ii&&Mi(ze)),r===a&&("FUNCTION"===t.substr(Ti,8)?(r="FUNCTION",Ti+=8):(r=a,0===Ii&&Mi(tr)),r===a&&("procedure"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(ce)),r===a&&("routine"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(er)),r===a&&("language"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(rr)),r===a&&("large"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(nr)),r===a&&("SCHEMA"===t.substr(Ti,6)?(r="SCHEMA",Ti+=6):(r=a,0===Ii&&Mi(or)))))))))));r!==a&&(Si=e,r={type:"origin",value:r.toUpperCase()});(e=r)===a&&(e=Ti,(r=kp())!==a&&tv()!==a?("tables"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi($e)),n===a&&("sequence"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(Ke)),n===a&&("functions"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(ar)),n===a&&("procedures"===t.substr(Ti,10).toLowerCase()?(n=t.substr(Ti,10),Ti+=10):(n=a,0===Ii&&Mi(sr)),n===a&&("routines"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(ur)))))),n!==a&&tv()!==a&&qp()!==a&&tv()!==a&&Cp()!==a?(Si=e,e=r={type:"origin",value:`all ${n} in schema`}):(Ti=e,e=a)):(Ti=e,e=a));return e}())===a&&(s=null),s!==a&&(u=tv())!==a&&(i=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Rc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Rc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Rc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&(c=tv())!==a?((l=rp())===a&&(l=wp()),l!==a?(Si=Ti,b=l,({revoke:"from",grant:"to"}[r.type].toLowerCase()===b[0].toLowerCase()?void 0:a)!==a&&tv()!==a&&(f=Vc())!==a&&tv()!==a?((p=function(){var e,r,n;e=Ti,Up()!==a&&tv()!==a?("grant"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(cr)),r!==a&&tv()!==a?("option"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(lr)),n!==a?(Si=e,e={type:"origin",value:"with grant option"}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(p=null),p!==a?(Si=e,r=function(t,e,r,n,o,a,s){return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{...t,keyword:"priv",objects:e,on:{object_type:r,priv_level:n},to_from:o[0],user_or_roles:a,with:s}}}(r,n,s,i,l,f,p),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var b;e===a&&(e=Ti,(r=Mc())!==a&&tv()!==a&&(n=uf())!==a&&tv()!==a?((o=rp())===a&&(o=wp()),o!==a?(Si=Ti,(function(t,e,r){return{revoke:"from",grant:"to"}[t.type].toLowerCase()===r[0].toLowerCase()}(r,0,o)?void 0:a)!==a&&(s=tv())!==a&&(u=Vc())!==a&&(i=tv())!==a?((c=function(){var e,r,n;e=Ti,Up()!==a&&tv()!==a?("admin"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(fr)),r!==a&&tv()!==a?("option"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(lr)),n!==a?(Si=e,e={type:"origin",value:"with admin option"}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(c=null),c!==a?(Si=e,r=function(t,e,r,n,o){return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{...t,keyword:"role",objects:e.map(t=>({priv:{type:"string",value:t}})),to_from:r[0],user_or_roles:n,with:o}}}(r,n,o,u,c),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a));return e}())===a&&(e=function(){var e,r,n,o,s,u,c,l,f,p,b,v,y;e=Ti,"if"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(i));r!==a&&tv()!==a&&(n=Pl())!==a&&tv()!==a?("then"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(vr)),o!==a&&tv()!==a&&(s=Qi())!==a&&tv()!==a?((u=Zb())===a&&(u=null),u!==a&&tv()!==a?((c=function(){var t,e,r,n,o,s;if(t=Ti,(e=qc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=qc())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=qc())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r,1),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())===a&&(c=null),c!==a&&tv()!==a?(l=Ti,(f=Zp())!==a&&(p=tv())!==a&&(b=Qi())!==a?l=f=[f,p,b]:(Ti=l,l=a),l===a&&(l=null),l!==a&&(f=tv())!==a?((p=Zb())===a&&(p=null),p!==a&&(b=tv())!==a?("end"===t.substr(Ti,3).toLowerCase()?(v=t.substr(Ti,3),Ti+=3):(v=a,0===Ii&&Mi(yr)),v!==a&&tv()!==a?("if"===t.substr(Ti,2).toLowerCase()?(y=t.substr(Ti,2),Ti+=2):(y=a,0===Ii&&Mi(i)),y!==a?(Si=e,d=n,h=s,m=u,w=c,L=l,O=p,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"if",keyword:"if",boolean_expr:d,semicolons:[m||"",O||""],prefix:{type:"origin",value:"then"},if_expr:h,elseif_expr:w,else_expr:L&&L[2],suffix:{type:"origin",value:"end if"}}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var d,h,m,w,L,O;return e}())===a&&(e=function(){var e,r,n,o,s;e=Ti,"raise"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(xr));r!==a&&tv()!==a?((n=function(){var e;"debug"===t.substr(Ti,5).toLowerCase()?(e=t.substr(Ti,5),Ti+=5):(e=a,0===Ii&&Mi(dr));e===a&&("log"===t.substr(Ti,3).toLowerCase()?(e=t.substr(Ti,3),Ti+=3):(e=a,0===Ii&&Mi(hr)),e===a&&("info"===t.substr(Ti,4).toLowerCase()?(e=t.substr(Ti,4),Ti+=4):(e=a,0===Ii&&Mi(mr)),e===a&&("notice"===t.substr(Ti,6).toLowerCase()?(e=t.substr(Ti,6),Ti+=6):(e=a,0===Ii&&Mi(wr)),e===a&&("warning"===t.substr(Ti,7).toLowerCase()?(e=t.substr(Ti,7),Ti+=7):(e=a,0===Ii&&Mi(Lr)),e===a&&("exception"===t.substr(Ti,9).toLowerCase()?(e=t.substr(Ti,9),Ti+=9):(e=a,0===Ii&&Mi(Or)))))));return e}())===a&&(n=null),n!==a&&tv()!==a?((o=function(){var e,r,n,o,s,u,i,c;if(e=Ti,(r=Ff())!==a){for(n=[],o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=pv())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);o!==a;)n.push(o),o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=pv())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);n!==a?(Si=e,r={type:"format",keyword:r,expr:(l=n)&&l.map(t=>t[3])},e=r):(Ti=e,e=a)}else Ti=e,e=a;var l;e===a&&(e=Ti,"sqlstate"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(_r)),r!==a&&(n=tv())!==a&&(o=Ff())!==a?(Si=e,e=r={type:"sqlstate",keyword:{type:"origin",value:"SQLSTATE"},expr:[o]}):(Ti=e,e=a),e===a&&(e=Ti,(r=sf())!==a&&(Si=e,r={type:"condition",expr:[{type:"default",value:r}]}),e=r));return e}())===a&&(o=null),o!==a&&tv()!==a?((s=function(){var e,r,n,o,s,u,i,c,f,p;if(e=Ti,(r=Sp())!==a)if(tv()!==a)if("message"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(jr)),n===a&&("detail"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(Cr)),n===a&&("hint"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(gr)),n===a&&("errcode"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(Er)),n===a&&("column"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(Ar)),n===a&&("constraint"===t.substr(Ti,10).toLowerCase()?(n=t.substr(Ti,10),Ti+=10):(n=a,0===Ii&&Mi(Tr)),n===a&&("datatype"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(Sr)),n===a&&("table"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(Ur)),n===a&&("schema"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(l)))))))))),n!==a)if(tv()!==a)if(Nb()!==a)if(tv()!==a)if((o=Pl())!==a){for(s=[],u=Ti,(i=tv())!==a&&(c=$b())!==a&&(f=tv())!==a&&(p=Pl())!==a?u=i=[i,c,f,p]:(Ti=u,u=a);u!==a;)s.push(u),u=Ti,(i=tv())!==a&&(c=$b())!==a&&(f=tv())!==a&&(p=Pl())!==a?u=i=[i,c,f,p]:(Ti=u,u=a);s!==a?(Si=e,r=function(t,e,r){const n=[e];return r&&r.forEach(t=>n.push(t[3])),{type:"using",option:t,symbol:"=",expr:n}}(n,o,s),e=r):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;return e}())===a&&(s=null),s!==a?(Si=e,u=n,i=o,c=s,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"raise",level:u,using:c,raise:i}},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var u,i,c;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,"execute"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(ie));r!==a&&tv()!==a&&(n=sf())!==a&&tv()!==a?(o=Ti,(s=Hb())!==a&&(u=tv())!==a&&(i=yv())!==a&&(c=tv())!==a&&(l=Yb())!==a?o=s=[s,u,i,c,l]:(Ti=o,o=a),o===a&&(o=null),o!==a?(Si=e,f=n,p=o,r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"execute",name:f,args:p&&{type:"expr_list",value:p[2]}}},e=r):(Ti=e,e=a)):(Ti=e,e=a);var f,p;return e}())===a&&(e=function(){var e,r,n,o,s,u,i,c;e=Ti,(r=function(){var e,r,n;e=Ti,"for"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(ve));r!==a&&(Si=e,r={label:null,keyword:"for"});(e=r)===a&&(e=Ti,(r=sf())!==a&&tv()!==a?("for"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(ve)),n!==a?(Si=e,e=r={label:r,keyword:"for"}):(Ti=e,e=a)):(Ti=e,e=a));return e}())!==a&&tv()!==a&&(n=sf())!==a&&tv()!==a&&qp()!==a&&tv()!==a&&(o=Pc())!==a&&tv()!==a?("loop"===t.substr(Ti,4).toLowerCase()?(s=t.substr(Ti,4),Ti+=4):(s=a,0===Ii&&Mi(Ir)),s!==a&&tv()!==a&&(u=Fi())!==a&&tv()!==a&&Kp()!==a&&tv()!==a?("loop"===t.substr(Ti,4).toLowerCase()?(i=t.substr(Ti,4),Ti+=4):(i=a,0===Ii&&Mi(Ir)),i!==a&&tv()!==a?((c=sf())===a&&(c=null),c!==a?(Si=Ti,f=c,(!(!(l=r).label||!f||l.label!==f)||!l.label&&!f?void 0:a)!==a?(Si=e,r=function(t,e,r,n,o){return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"for",label:o,target:e,query:r,stmts:n.ast}}}(0,n,o,u,c),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var l,f;return e}())===a&&(e=function(){var e,r;e=Ti,"begin"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Nr));r===a&&("commit"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Rr)),r===a&&("rollback"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(kr))));r!==a&&(Si=e,r={type:"transaction",expr:{action:{type:"origin",value:r}}});return e=r}()),e}function Qi(){var e;return(e=$i())===a&&(e=function(){var t,e,r,n,o,s,u,i;t=Ti,(e=tv())!==a?((r=Dc())===a&&(r=null),r!==a&&tv()!==a&&up()!==a&&tv()!==a&&(n=al())!==a&&tv()!==a&&Lp()!==a&&tv()!==a&&(o=El())!==a&&tv()!==a?((s=tl())===a&&(s=null),s!==a&&tv()!==a?((u=pl())===a&&(u=null),u!==a&&tv()!==a?((i=Tl())===a&&(i=null),i!==a?(Si=t,e=function(t,e,r,n,o,a){const s={},u=t=>{const{server:e,db:r,schema:n,as:o,table:a,join:u}=t,i=u?"select":"update",c=[e,r,n].filter(Boolean).join(".")||null;r&&(s[a]=c),a&&kv.add(`${i}::${c}::${a}`)};return e&&e.forEach(u),n&&n.forEach(u),r&&r.forEach(t=>{if(t.table){const e=xv(t.table);kv.add(`update::${s[e]||null}::${e}`)}Vv.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(kv),columnList:Iv(Vv),ast:{with:t,type:"update",table:e,set:r,from:n,where:o,returning:a}}}(r,n,o,s,u,i),t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=_l())!==a&&tv()!==a?((n=mp())===a&&(n=null),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a?((s=Ul())===a&&(s=null),s!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(u=nf())!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&(i=Sl())!==a&&tv()!==a?((c=function(){var e,r,n,o;e=Ti,gp()!==a&&tv()!==a?("conflict"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(sn)),r!==a&&tv()!==a?((n=function(){var t,e;t=Ti,Hb()!==a&&tv()!==a&&(e=bl())!==a&&tv()!==a&&Yb()!==a?(Si=t,t={type:"column",expr:e,parentheses:!0}):(Ti=t,t=a);return t}())===a&&(n=null),n!==a&&tv()!==a&&(o=function(){var e,r,n,o,s;e=Ti,"do"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(on));r!==a&&tv()!==a?("nothing"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(an)),n!==a?(Si=e,e=r={keyword:"do",expr:{type:"origin",value:"nothing"}}):(Ti=e,e=a)):(Ti=e,e=a);e===a&&(e=Ti,"do"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(on)),r!==a&&tv()!==a&&(n=up())!==a&&tv()!==a&&Lp()!==a&&tv()!==a&&(o=El())!==a&&tv()!==a?((s=pl())===a&&(s=null),s!==a?(Si=e,e=r={keyword:"do",expr:{type:"update",set:o,where:s}}):(Ti=e,e=a)):(Ti=e,e=a));return e}())!==a?(Si=e,e={type:"conflict",keyword:"on",target:n,action:o}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(c=null),c!==a&&tv()!==a?((l=Tl())===a&&(l=null),l!==a?(Si=e,r=function(t,e,r,n,o,a,s){if(e&&(kv.add(`insert::${[e.db,e.schema].filter(Boolean).join(".")||null}::${e.table}`),e.as=null),n){let t=e&&e.table||null;Array.isArray(o)&&o.forEach((t,e)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(e+1))}),n.forEach(e=>Vv.add(`insert::${t}::${e.value}`))}return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t,table:[e],columns:n,values:o,partition:r,conflict:a,returning:s}}}(r,o,s,u,i,c,l),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var t,e,r,n,o,s,u,i;t=Ti,(e=_l())!==a&&tv()!==a?((r=dp())===a&&(r=null),r!==a&&tv()!==a?((n=mp())===a&&(n=null),n!==a&&tv()!==a&&(o=cl())!==a&&tv()!==a?((s=Ul())===a&&(s=null),s!==a&&tv()!==a&&(u=Sl())!==a&&tv()!==a?((i=Tl())===a&&(i=null),i!==a?(Si=t,e=function(t,e,r,n,o,a,s){n&&(kv.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),Vv.add(`insert::${n.table}::(.*)`),n.as=null);const u=[e,r].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:u,returning:s}}}(e,r,n,o,s,u,i),t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(e=function(){var t,e,r,n,o;t=Ti,(e=fp())!==a&&tv()!==a?((r=al())===a&&(r=null),r!==a&&tv()!==a&&(n=tl())!==a&&tv()!==a?((o=pl())===a&&(o=null),o!==a?(Si=t,e=function(t,e,r){if(e&&e.forEach(t=>{const{db:e,as:r,schema:n,table:o,join:a}=t,s=a?"select":"delete",u=[e,n].filter(Boolean).join(".")||null;o&&kv.add(`${s}::${u}::${o}`),a||Vv.add(`delete::${o}::(.*)`)}),null===t&&1===e.length){const r=e[0];t=[{db:r.db,schema:r.schema,table:r.table,as:r.as,addition:!0}]}return{tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"delete",table:t,from:e,where:r}}}(r,n,o),t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(e=Di())===a&&(e=function(){var t,e;t=[],e=uv();for(;e!==a;)t.push(e),e=uv();return t}()),e}function Fi(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Qi())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Zb())!==a&&(u=tv())!==a&&(i=Qi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Zb())!==a&&(u=tv())!==a&&(i=Qi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){const r=t&&t.ast||t,n=e&&e.length&&e[0].length>=4?[r]:r;for(let t=0;t<e.length;t++)e[t][3]&&0!==e[t][3].length&&n.push(e[t][3]&&e[t][3].ast||e[t][3]);return{tableList:Array.from(kv),columnList:Iv(Vv),ast:n}}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Gi(){var e,r,n,o;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"union"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(ts));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a?((n=kp())===a&&(n=Vp()),n===a&&(n=null),n!==a?(Si=e,e=r=(o=n)?"union "+o.toLowerCase():"union"):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=function(){var e,r,n,o;e=Ti,"intersect"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(es));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r="intersect"),(e=r)===a&&(e=Ti,(r=function(){var e,r,n,o;e=Ti,"except"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(rs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r="except"),e=r)),e}function $i(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Pc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Gi())!==a&&(u=tv())!==a&&(i=Pc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Gi())!==a&&(u=tv())!==a&&(i=Pc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a&&(n=tv())!==a?((o=Ol())===a&&(o=null),o!==a&&(s=tv())!==a?((u=gl())===a&&(u=null),u!==a?(Si=t,t=e=function(t,e,r,n){let o=t;for(let t=0;t<e.length;t++)o._next=e[t][3],o.set_op=e[t][1],o=o._next;return r&&(t._orderby=r),n&&n.value&&n.value.length>0&&(t._limit=n),{tableList:Array.from(kv),columnList:Iv(Vv),ast:t}}(e,r,o,u)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Bi(){var e,r;return e=Ti,"if"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(i)),r!==a&&tv()!==a&&Gp()!==a&&tv()!==a&&Fp()!==a?(Si=e,e=r="IF NOT EXISTS"):(Ti=e,e=a),e}function Hi(){var e,r,n;return e=Ti,"check_option"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(d)),r!==a&&tv()!==a&&Nb()!==a&&tv()!==a?("cascaded"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(p)),n===a&&("local"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(b))),n!==a?(Si=e,e=r={type:"check_option",value:n,symbol:"="}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"security_barrier"===t.substr(Ti,16).toLowerCase()?(r=t.substr(Ti,16),Ti+=16):(r=a,0===Ii&&Mi(h)),r===a&&("security_invoker"===t.substr(Ti,16).toLowerCase()?(r=t.substr(Ti,16),Ti+=16):(r=a,0===Ii&&Mi(m))),r!==a&&tv()!==a&&Nb()!==a&&tv()!==a&&(n=Qf())!==a?(Si=e,e=r=function(t,e){return{type:t.toLowerCase(),value:e.value?"true":"false",symbol:"="}}(r,n)):(Ti=e,e=a)),e}function Yi(){var t,e,r,n;return t=Ti,(e=sf())!==a&&tv()!==a&&Nb()!==a&&tv()!==a?((r=sf())===a&&(r=Pl()),r!==a?(Si=t,t=e={type:e,symbol:"=",value:"string"==typeof(n=r)?{type:"default",value:n}:n}):(Ti=t,t=a)):(Ti=t,t=a),t}function Wi(){var t,e,r;return t=Ti,(e=ef())!==a&&tv()!==a&&(r=mv())!==a?(Si=t,t=e={column:e,definition:r}):(Ti=t,t=a),t}function Xi(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Wi())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Wi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Wi())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Zi(){var e,r,n,o,s,u,i,c,l,f,p,b;return e=Ti,(r=wf())!==a?(Si=Ti,("begin"!==r.toLowerCase()?void 0:a)!==a&&tv()!==a?("constant"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(g)),n===a&&(n=null),n!==a&&tv()!==a&&(o=mv())!==a&&tv()!==a?((s=sc())===a&&(s=null),s!==a&&tv()!==a?(u=Ti,(i=Gp())!==a&&(c=tv())!==a&&(l=tp())!==a?u=i=[i,c,l]:(Ti=u,u=a),u===a&&(u=null),u!==a&&(i=tv())!==a?(c=Ti,(l=ep())===a&&(":="===t.substr(Ti,2)?(l=":=",Ti+=2):(l=a,0===Ii&&Mi(E))),l===a&&(l=null),l!==a&&(f=tv())!==a?(p=Ti,Ii++,"begin"===t.substr(Ti,5).toLowerCase()?(b=t.substr(Ti,5),Ti+=5):(b=a,0===Ii&&Mi(A)),Ii--,b!==a?(Ti=p,p=void 0):p=a,p===a&&(p=Mf())===a&&(p=Pl()),p!==a?c=l=[l,f,p]:(Ti=c,c=a)):(Ti=c,c=a),c===a&&(c=null),c!==a&&(l=tv())!==a?((f=Zb())===a&&(f=null),f!==a?(Si=e,e=r=function(t,e,r,n,o,a,s){return{keyword:"variable",name:t,constant:e,datatype:r,collate:n,not_null:o&&"not null",definition:a&&a[0]&&{type:"default",keyword:a[0],value:a[2]}}}(r,n,o,s,u,c)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Ki(){var t,e,r,n,o,s;if(t=Ti,(e=Zi())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Zi())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Zi())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r,1)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Ji(){var e,r,n,o;return e=Ti,"declare"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(T)),r!==a&&tv()!==a&&(n=Ki())!==a?(Si=e,o=n,e=r={tableList:Array.from(kv),columnList:Iv(Vv),ast:{type:"declare",declare:o,symbol:";"}}):(Ti=e,e=a),e}function zi(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m;if(e=Ti,"LANGUAGE"===t.substr(Ti,8)?(r="LANGUAGE",Ti+=8):(r=a,0===Ii&&Mi(S)),r!==a&&(n=tv())!==a&&(o=wf())!==a&&(s=tv())!==a?(Si=e,e=r={prefix:"LANGUAGE",type:"default",value:o}):(Ti=e,e=a),e===a&&(e=Ti,"transorm"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(U)),r!==a&&(n=tv())!==a?(o=Ti,"FOR"===t.substr(Ti,3)?(s="FOR",Ti+=3):(s=a,0===Ii&&Mi(_)),s!==a&&(u=tv())!==a?("TYPE"===t.substr(Ti,4)?(i="TYPE",Ti+=4):(i=a,0===Ii&&Mi(x)),i!==a&&(c=tv())!==a&&(l=wf())!==a?o=s=[s,u,i,c,l]:(Ti=o,o=a)):(Ti=o,o=a),o===a&&(o=null),o!==a&&(s=tv())!==a?(Si=e,e=r=(m=o)?{prefix:["TRANSORM",m[0].toUpperCase(),m[2].toUpperCase()].join(" "),type:"default",value:m[4]}:{type:"origin",value:"TRANSORM"}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"window"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(I)),r===a&&("immutable"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(N)),r===a&&("stable"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(R)),r===a&&("volatile"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(k)),r===a&&("strict"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(V)))))),r!==a&&(n=tv())!==a?(Si=e,e=r={type:"origin",value:r}):(Ti=e,e=a),e===a&&(e=Ti,"not"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(M)),r===a&&(r=null),r!==a&&(n=tv())!==a?("leakproof"===t.substr(Ti,9).toLowerCase()?(o=t.substr(Ti,9),Ti+=9):(o=a,0===Ii&&Mi(q)),o!==a&&(s=tv())!==a?(Si=e,e=r={type:"origin",value:[r,"LEAKPROOF"].filter(t=>t).join(" ")}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"called"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(P)),r===a&&(r=Ti,"returns"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(j)),n!==a&&(o=tv())!==a?("null"===t.substr(Ti,4).toLowerCase()?(s=t.substr(Ti,4),Ti+=4):(s=a,0===Ii&&Mi(D)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a)),r===a&&(r=null),r!==a&&(n=tv())!==a?("on"===t.substr(Ti,2).toLowerCase()?(o=t.substr(Ti,2),Ti+=2):(o=a,0===Ii&&Mi(Q)),o!==a&&(s=tv())!==a?("null"===t.substr(Ti,4).toLowerCase()?(u=t.substr(Ti,4),Ti+=4):(u=a,0===Ii&&Mi(D)),u!==a&&(i=tv())!==a?("input"===t.substr(Ti,5).toLowerCase()?(c=t.substr(Ti,5),Ti+=5):(c=a,0===Ii&&Mi(F)),c!==a&&(l=tv())!==a?(Si=e,e=r=function(t){return Array.isArray(t)&&(t=[t[0],t[2]].join(" ")),{type:"origin",value:t+" ON NULL INPUT"}}(r)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"external"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(G)),r===a&&(r=null),r!==a&&(n=tv())!==a?("security"===t.substr(Ti,8).toLowerCase()?(o=t.substr(Ti,8),Ti+=8):(o=a,0===Ii&&Mi($)),o!==a&&(s=tv())!==a?("invoker"===t.substr(Ti,7).toLowerCase()?(u=t.substr(Ti,7),Ti+=7):(u=a,0===Ii&&Mi(B)),u===a&&("definer"===t.substr(Ti,7).toLowerCase()?(u=t.substr(Ti,7),Ti+=7):(u=a,0===Ii&&Mi(H))),u!==a&&(i=tv())!==a?(Si=e,e=r=function(t,e){return{type:"origin",value:[t,"SECURITY",e].filter(t=>t).join(" ")}}(r,u)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"parallel"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Y)),r!==a&&(n=tv())!==a?("unsafe"===t.substr(Ti,6).toLowerCase()?(o=t.substr(Ti,6),Ti+=6):(o=a,0===Ii&&Mi(W)),o===a&&("restricted"===t.substr(Ti,10).toLowerCase()?(o=t.substr(Ti,10),Ti+=10):(o=a,0===Ii&&Mi(X)),o===a&&("safe"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(Z)))),o!==a&&(s=tv())!==a?(Si=e,e=r=function(t){return{type:"origin",value:["PARALLEL",t].join(" ")}}(o)):(Ti=e,e=a)):(Ti=e,e=a),e===a))))))){if(e=Ti,(r=Op())!==a)if((n=tv())!==a){if(o=[],K.test(t.charAt(Ti))?(s=t.charAt(Ti),Ti++):(s=a,0===Ii&&Mi(J)),s!==a)for(;s!==a;)o.push(s),K.test(t.charAt(Ti))?(s=t.charAt(Ti),Ti++):(s=a,0===Ii&&Mi(J));else o=a;if(o!==a)if((s=tv())!==a)if((u=Ji())===a&&(u=null),u!==a)if((i=tv())!==a)if("begin"===t.substr(Ti,5).toLowerCase()?(c=t.substr(Ti,5),Ti+=5):(c=a,0===Ii&&Mi(A)),c===a&&(c=null),c!==a)if((l=tv())!==a)if((f=Fi())!==a)if(tv()!==a)if((p=Kp())===a&&(p=null),p!==a)if(Si=Ti,h=p,((d=c)&&h||!d&&!h?void 0:a)!==a)if(tv()!==a)if((b=Zb())===a&&(b=null),b!==a)if(tv()!==a){if(v=[],z.test(t.charAt(Ti))?(y=t.charAt(Ti),Ti++):(y=a,0===Ii&&Mi(tt)),y!==a)for(;y!==a;)v.push(y),z.test(t.charAt(Ti))?(y=t.charAt(Ti),Ti++):(y=a,0===Ii&&Mi(tt));else v=a;v!==a&&(y=tv())!==a?(Si=e,e=r=function(t,e,r,n,o,a){const s=t.join(""),u=a.join("");if(s!==u)throw new Error(`start symbol '${s}'is not same with end symbol '${u}'`);return{type:"as",declare:e&&e.ast,begin:r,expr:Array.isArray(n.ast)?n.ast.flat():[n.ast],end:o&&o[0],symbol:s}}(o,u,c,f,p,v)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a}else Ti=e,e=a;else Ti=e,e=a;e===a&&(e=Ti,"cost"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(et)),r===a&&("rows"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(rt))),r!==a&&(n=tv())!==a&&(o=Yf())!==a&&(s=tv())!==a?(Si=e,e=r=function(t,e){return e.prefix=t,e}(r,o)):(Ti=e,e=a),e===a&&(e=Ti,"support"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(nt)),r!==a&&(n=tv())!==a&&(o=bv())!==a&&(s=tv())!==a?(Si=e,e=r=function(t){return{prefix:"support",type:"default",value:[t.schema&&t.schema.value,t.name.value].filter(t=>t).join(".")}}(o)):(Ti=e,e=a),e===a&&(e=Ti,(r=Lp())!==a&&(n=tv())!==a&&(o=wf())!==a&&(s=tv())!==a?(u=Ti,"to"===t.substr(Ti,2).toLowerCase()?(i=t.substr(Ti,2),Ti+=2):(i=a,0===Ii&&Mi(ot)),i===a&&(61===t.charCodeAt(Ti)?(i="=",Ti++):(i=a,0===Ii&&Mi(at))),i!==a&&(c=tv())!==a&&(l=uf())!==a?u=i=[i,c,l]:(Ti=u,u=a),u===a&&(u=Ti,(i=wp())!==a&&(c=tv())!==a?("current"===t.substr(Ti,7).toLowerCase()?(l=t.substr(Ti,7),Ti+=7):(l=a,0===Ii&&Mi(st)),l!==a?u=i=[i,c,l]:(Ti=u,u=a)):(Ti=u,u=a)),u===a&&(u=null),u!==a&&(i=tv())!==a?(Si=e,e=r=function(t,e){let r;if(e){const t=Array.isArray(e[2])?e[2]:[e[2]];r={prefix:e[0],expr:t.map(t=>({type:"default",value:t}))}}return{type:"set",parameter:t,value:r}}(o,u)):(Ti=e,e=a)):(Ti=e,e=a))))}return e}function tc(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m;if(e=Ti,ip()!==a)if(tv()!==a)if(r=Ti,(n=Bp())!==a&&(o=tv())!==a&&(s=vp())!==a?r=n=[n,o,s]:(Ti=r,r=a),r===a&&(r=null),r!==a)if((n=tv())!==a)if("function"===t.substr(Ti,8).toLowerCase()?(o=t.substr(Ti,8),Ti+=8):(o=a,0===Ii&&Mi(ut)),o!==a)if((s=tv())!==a)if((u=cl())!==a)if(tv()!==a)if(Hb()!==a)if(tv()!==a)if((i=pc())===a&&(i=null),i!==a)if(tv()!==a)if(Yb()!==a)if(tv()!==a)if((c=function(){var e,r,n,o,s;return e=Ti,"returns"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(j)),r!==a&&tv()!==a?("setof"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(C)),n===a&&(n=null),n!==a&&tv()!==a?((o=mv())===a&&(o=cl()),o!==a?(Si=e,e=r={type:"returns",keyword:n,expr:o}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"returns"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(j)),r!==a&&tv()!==a&&(n=jp())!==a&&tv()!==a&&(o=Hb())!==a&&tv()!==a&&(s=Xi())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={type:"returns",keyword:"table",expr:s}):(Ti=e,e=a)),e}())===a&&(c=null),c!==a)if(tv()!==a){for(l=[],f=zi();f!==a;)l.push(f),f=zi();l!==a&&(f=tv())!==a?((p=Zb())===a&&(p=null),p!==a&&tv()!==a?(Si=e,b=r,v=o,y=u,d=i,h=c,m=l,e={tableList:Array.from(kv),columnList:Iv(Vv),ast:{args:d||[],type:"create",replace:b&&"or replace",name:{schema:y.db,name:y.table},returns:h,keyword:v&&v.toLowerCase(),options:m||[]}}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;return e}function ec(){var e;return(e=function(){var e,r,n,o,s,u;return e=Ti,"increment"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(lt)),r!==a&&tv()!==a?((n=xp())===a&&(n=null),n!==a&&tv()!==a&&(o=Yf())!==a?(Si=e,s=r,u=o,e=r={resource:"sequence",prefix:n?s.toLowerCase()+" by":s.toLowerCase(),value:u}):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(e=function(){var e,r,n;return e=Ti,"minvalue"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ft)),r!==a&&tv()!==a&&(n=Yf())!==a?(Si=e,e=r=pt(r,n)):(Ti=e,e=a),e===a&&(e=Ti,"no"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(bt)),r!==a&&tv()!==a?("minvalue"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(ft)),n!==a?(Si=e,e=r={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(Ti=e,e=a)):(Ti=e,e=a)),e}())===a&&(e=function(){var e,r,n;return e=Ti,"maxvalue"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(vt)),r!==a&&tv()!==a&&(n=Yf())!==a?(Si=e,e=r=pt(r,n)):(Ti=e,e=a),e===a&&(e=Ti,"no"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(bt)),r!==a&&tv()!==a?("maxvalue"===t.substr(Ti,8).toLowerCase()?(n=t.substr(Ti,8),Ti+=8):(n=a,0===Ii&&Mi(vt)),n!==a?(Si=e,e=r={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(Ti=e,e=a)):(Ti=e,e=a)),e}())===a&&(e=function(){var e,r,n,o,s,u;return e=Ti,"start"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(yt)),r!==a&&tv()!==a?((n=Up())===a&&(n=null),n!==a&&tv()!==a&&(o=Yf())!==a?(Si=e,s=r,u=o,e=r={resource:"sequence",prefix:n?s.toLowerCase()+" with":s.toLowerCase(),value:u}):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(e=function(){var e,r,n;return e=Ti,"cache"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(dt)),r!==a&&tv()!==a&&(n=Yf())!==a?(Si=e,e=r=pt(r,n)):(Ti=e,e=a),e}())===a&&(e=function(){var e,r,n;return e=Ti,"no"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(bt)),r===a&&(r=null),r!==a&&tv()!==a?("cycle"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(ht)),n!==a?(Si=e,e=r={resource:"sequence",value:{type:"origin",value:r?"no cycle":"cycle"}}):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(e=function(){var e,r,n;return e=Ti,"owned"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(mt)),r!==a&&tv()!==a&&xp()!==a&&tv()!==a?("none"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(wt)),n!==a?(Si=e,e=r={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"owned"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(mt)),r!==a&&tv()!==a&&xp()!==a&&tv()!==a&&(n=ef())!==a?(Si=e,e=r={resource:"sequence",prefix:"owned by",value:n}):(Ti=e,e=a)),e}()),e}function rc(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y;return e=Ti,(r=Pl())!==a&&tv()!==a?((n=sc())===a&&(n=null),n!==a&&tv()!==a?((o=sf())===a&&(o=null),o!==a&&tv()!==a?((s=Np())===a&&(s=Rp()),s===a&&(s=null),s!==a&&tv()!==a?(u=Ti,"nulls"===t.substr(Ti,5).toLowerCase()?(i=t.substr(Ti,5),Ti+=5):(i=a,0===Ii&&Mi(Lt)),i!==a&&(c=tv())!==a?("first"===t.substr(Ti,5).toLowerCase()?(l=t.substr(Ti,5),Ti+=5):(l=a,0===Ii&&Mi(Ot)),l===a&&("last"===t.substr(Ti,4).toLowerCase()?(l=t.substr(Ti,4),Ti+=4):(l=a,0===Ii&&Mi(jt))),l!==a?u=i=[i,c,l]:(Ti=u,u=a)):(Ti=u,u=a),u===a&&(u=null),u!==a?(Si=e,f=r,p=n,b=o,v=s,y=u,e=r={...f,collate:p,opclass:b,order_by:v&&v.toLowerCase(),nulls:y&&`${y[0].toLowerCase()} ${y[2].toLowerCase()}`}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function nc(){var t;return(t=ac())===a&&(t=wc())===a&&(t=Lc())===a&&(t=Oc()),t}function oc(){var e,r,n,o;return(e=function(){var t,e,r;t=Ti,(e=Df())===a&&(e=Pf());e!==a&&tv()!==a?((r=ic())===a&&(r=null),r!==a?(Si=t,o=r,(n=e)&&!n.value&&(n.value="null"),t=e={default_val:o,nullable:n}):(Ti=t,t=a)):(Ti=t,t=a);var n,o;t===a&&(t=Ti,(e=ic())!==a&&tv()!==a?((r=Df())===a&&(r=Pf()),r===a&&(r=null),r!==a?(Si=t,e=function(t,e){return e&&!e.value&&(e.value="null"),{default_val:t,nullable:e}}(e,r),t=e):(Ti=t,t=a)):(Ti=t,t=a));return t}())===a&&(e=Ti,"auto_increment"===t.substr(Ti,14).toLowerCase()?(r=t.substr(Ti,14),Ti+=14):(r=a,0===Ii&&Mi(Ct)),r!==a&&(Si=e,r={auto_increment:r.toLowerCase()}),(e=r)===a&&(e=Ti,"unique"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(gt)),r!==a&&tv()!==a?("key"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(Et)),n===a&&(n=null),n!==a?(Si=e,e=r=function(t){const e=["unique"];return t&&e.push(t),{unique:e.join(" ").toLowerCase("")}}(n)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"primary"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(At)),r===a&&(r=null),r!==a&&tv()!==a?("key"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(Et)),n!==a?(Si=e,e=r=function(t){const e=[];return t&&e.push("primary"),e.push("key"),{primary_key:e.join(" ").toLowerCase("")}}(r)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=nv())!==a&&(Si=e,r={comment:r}),(e=r)===a&&(e=Ti,(r=sc())!==a&&(Si=e,r={collate:r}),(e=r)===a&&(e=Ti,(r=function(){var e,r,n;e=Ti,"column_format"===t.substr(Ti,13).toLowerCase()?(r=t.substr(Ti,13),Ti+=13):(r=a,0===Ii&&Mi(Tt));r!==a&&tv()!==a?("fixed"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(St)),n===a&&("dynamic"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(Ut)),n===a&&("default"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(_t)))),n!==a?(Si=e,r={type:"column_format",value:n.toLowerCase()},e=r):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r={column_format:r}),(e=r)===a&&(e=Ti,(r=function(){var e,r,n;e=Ti,"storage"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(xt));r!==a&&tv()!==a?("disk"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(It)),n===a&&("memory"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(Nt))),n!==a?(Si=e,r={type:"storage",value:n.toLowerCase()},e=r):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r={storage:r}),(e=r)===a&&(e=Ti,(r=gc())!==a&&(Si=e,r={reference_definition:r}),(e=r)===a&&(e=Ti,(r=Tc())!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a&&(o=of())!==a?(Si=e,e=r=function(t,e,r){return{character_set:{type:t,value:r,symbol:e}}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a)))))))))),e}function ac(){var t,e,r,n,o,s,u;return t=Ti,(e=ef())!==a&&tv()!==a&&(r=mv())!==a&&tv()!==a?((n=function(){var t,e,r,n,o,s;if(t=Ti,(e=oc())!==a)if(tv()!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=oc())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=oc())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){let r=t;for(let t=0;t<e.length;t++)r={...r,...e[t][1]};return r}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;return t}())===a&&(n=null),n!==a?(Si=t,o=e,s=r,u=n,Vv.add(`create::${o.table}::${o.column.expr.value}`),t=e={column:o,definition:s,resource:"column",...u||{}}):(Ti=t,t=a)):(Ti=t,t=a),t}function sc(){var e,r,n;return e=Ti,function(){var e,r,n,o;e=Ti,"collate"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Oe));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="COLLATE"):(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a?((r=Nb())===a&&(r=null),r!==a&&tv()!==a&&(n=sf())!==a?(Si=e,e={type:"collate",keyword:"collate",collate:{name:n,symbol:r}}):(Ti=e,e=a)):(Ti=e,e=a),e}function uc(){var t,e,r,n,o;return t=Ti,(e=ep())===a&&(e=Nb()),e===a&&(e=null),e!==a&&tv()!==a&&(r=Pl())!==a?(Si=t,o=r,t=e={type:"default",keyword:(n=e)&&n[0],value:o}):(Ti=t,t=a),t}function ic(){var t,e;return t=Ti,ep()!==a&&tv()!==a&&(e=Pl())!==a?(Si=t,t={type:"default",value:e}):(Ti=t,t=a),t}function cc(){var t,e,r;return t=Ti,(e=Bb())!==a&&(Si=t,e=[{name:"*"}]),(t=e)===a&&(t=Ti,(e=pc())===a&&(e=null),e!==a&&tv()!==a&&Ip()!==a&&tv()!==a&&xp()!==a&&tv()!==a&&(r=pc())!==a?(Si=t,t=e=function(t,e){const r=t||[];return r.orderby=e,r}(e,r)):(Ti=t,t=a),t===a&&(t=pc())),t}function lc(){var e,r;return e=Ti,(r=qp())===a&&("out"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Vt)),r===a&&("variadic"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Mt)),r===a&&("inout"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(qt))))),r!==a&&(Si=e,r=r.toUpperCase()),e=r}function fc(){var t,e,r,n,o;return t=Ti,(e=lc())===a&&(e=null),e!==a&&tv()!==a&&(r=mv())!==a&&tv()!==a?((n=uc())===a&&(n=null),n!==a?(Si=t,t=e={mode:e,type:r,default:n}):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=lc())===a&&(e=null),e!==a&&tv()!==a&&(r=wf())!==a&&tv()!==a&&(n=mv())!==a&&tv()!==a?((o=uc())===a&&(o=null),o!==a?(Si=t,t=e=function(t,e,r,n){return{mode:t,name:e,type:r,default:n}}(e,r,n,o)):(Ti=t,t=a)):(Ti=t,t=a)),t}function pc(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=fc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=fc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=fc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function bc(){var t;return(t=function(){var t,e,r,n;t=Ti,(e=Rb())!==a&&tv()!==a?((r=kb())===a&&(r=null),r!==a&&tv()!==a&&(n=ac())!==a?(Si=t,o=r,s=n,e={action:"add",...s,keyword:o,resource:"column",type:"alter"},t=e):(Ti=t,t=a)):(Ti=t,t=a);var o,s;return t}())===a&&(t=function(){var t,e;t=Ti,Rb()!==a&&tv()!==a&&(e=Oc())!==a?(Si=t,t={action:"add",create_definitions:e,resource:"constraint",type:"alter"}):(Ti=t,t=a);return t}())===a&&(t=function(){var t,e,r;t=Ti,op()!==a&&tv()!==a?((e=kb())===a&&(e=null),e!==a&&tv()!==a&&(r=ef())!==a?(Si=t,t={action:"drop",column:r,keyword:e,resource:"column",type:"alter"}):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(t=function(){var t,e,r;t=Ti,(e=Rb())!==a&&tv()!==a&&(r=wc())!==a?(Si=t,n=r,e={action:"add",type:"alter",...n},t=e):(Ti=t,t=a);var n;return t}())===a&&(t=function(){var t,e,r;t=Ti,(e=Rb())!==a&&tv()!==a&&(r=Lc())!==a?(Si=t,n=r,e={action:"add",type:"alter",...n},t=e):(Ti=t,t=a);var n;return t}())===a&&(t=vc())===a&&(t=hc())===a&&(t=mc()),t}function vc(){var t,e,r,n,o;return t=Ti,yp()!==a&&tv()!==a?((e=rp())===a&&(e=Op()),e===a&&(e=null),e!==a&&tv()!==a&&(r=sf())!==a?(Si=t,o=r,t={action:"rename",type:"alter",resource:"table",keyword:(n=e)&&n[0].toLowerCase(),table:o}):(Ti=t,t=a)):(Ti=t,t=a),t}function yc(){var e,r,n;return e=Ti,"owner"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Pt)),r!==a&&tv()!==a&&rp()!==a&&tv()!==a?((n=sf())===a&&("current_role"===t.substr(Ti,12).toLowerCase()?(n=t.substr(Ti,12),Ti+=12):(n=a,0===Ii&&Mi(Dt)),n===a&&("current_user"===t.substr(Ti,12).toLowerCase()?(n=t.substr(Ti,12),Ti+=12):(n=a,0===Ii&&Mi(Qt)),n===a&&("session_user"===t.substr(Ti,12).toLowerCase()?(n=t.substr(Ti,12),Ti+=12):(n=a,0===Ii&&Mi(Ft))))),n!==a?(Si=e,e=r={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(Ti=e,e=a)):(Ti=e,e=a),e}function dc(){var t,e;return t=Ti,Lp()!==a&&tv()!==a&&Cp()!==a&&tv()!==a&&(e=sf())!==a?(Si=t,t={action:"set",type:"alter",resource:"table",keyword:"schema",table:e}):(Ti=t,t=a),t}function hc(){var e,r,n,o;return e=Ti,"algorithm"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Gt)),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a?("default"===t.substr(Ti,7).toLowerCase()?(o=t.substr(Ti,7),Ti+=7):(o=a,0===Ii&&Mi(_t)),o===a&&("instant"===t.substr(Ti,7).toLowerCase()?(o=t.substr(Ti,7),Ti+=7):(o=a,0===Ii&&Mi($t)),o===a&&("inplace"===t.substr(Ti,7).toLowerCase()?(o=t.substr(Ti,7),Ti+=7):(o=a,0===Ii&&Mi(Bt)),o===a&&("copy"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(Ht))))),o!==a?(Si=e,e=r={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function mc(){var e,r,n,o;return e=Ti,"lock"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Yt)),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a?("default"===t.substr(Ti,7).toLowerCase()?(o=t.substr(Ti,7),Ti+=7):(o=a,0===Ii&&Mi(_t)),o===a&&("none"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(wt)),o===a&&("shared"===t.substr(Ti,6).toLowerCase()?(o=t.substr(Ti,6),Ti+=6):(o=a,0===Ii&&Mi(Wt)),o===a&&("exclusive"===t.substr(Ti,9).toLowerCase()?(o=t.substr(Ti,9),Ti+=9):(o=a,0===Ii&&Mi(Xt))))),o!==a?(Si=e,e=r={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function wc(){var t,e,r,n,o,s,u,i;return t=Ti,(e=Vb())===a&&(e=Mb()),e!==a&&tv()!==a?((r=hf())===a&&(r=null),r!==a&&tv()!==a?((n=rl())===a&&(n=null),n!==a&&tv()!==a&&(o=Fc())!==a&&tv()!==a?((s=nl())===a&&(s=null),s!==a&&tv()!==a?(Si=t,u=n,i=s,t=e={index:r,definition:o,keyword:e.toLowerCase(),index_type:u,resource:"index",index_options:i}):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a),t}function Lc(){var e,r,n,o,s,u,i,c,l;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"fulltext"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Wu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="FULLTEXT"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Ti,"spatial"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Xu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SPATIAL"):(Ti=e,e=a)):(Ti=e,e=a);return e}()),r!==a&&tv()!==a?((n=Vb())===a&&(n=Mb()),n===a&&(n=null),n!==a&&tv()!==a?((o=hf())===a&&(o=null),o!==a&&tv()!==a&&(s=Fc())!==a&&tv()!==a?((u=nl())===a&&(u=null),u!==a&&tv()!==a?(Si=e,i=r,l=u,e=r={index:o,definition:s,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Oc(){var e;return(e=function(){var e,r,n,o,s,u;e=Ti,(r=jc())===a&&(r=null);r!==a&&tv()!==a?("primary key"===t.substr(Ti,11).toLowerCase()?(n=t.substr(Ti,11),Ti+=11):(n=a,0===Ii&&Mi(Zt)),n!==a&&tv()!==a?((o=rl())===a&&(o=null),o!==a&&tv()!==a&&(s=Fc())!==a&&tv()!==a?((u=nl())===a&&(u=null),u!==a?(Si=e,c=n,l=o,f=s,p=u,r={constraint:(i=r)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var i,c,l,f,p;return e}())===a&&(e=function(){var t,e,r,n,o,s,u,i;t=Ti,(e=jc())===a&&(e=null);e!==a&&tv()!==a&&(r=qb())!==a&&tv()!==a?((n=Vb())===a&&(n=Mb()),n===a&&(n=null),n!==a&&tv()!==a?((o=hf())===a&&(o=null),o!==a&&tv()!==a?((s=rl())===a&&(s=null),s!==a&&tv()!==a&&(u=Fc())!==a&&tv()!==a?((i=nl())===a&&(i=null),i!==a?(Si=t,l=r,f=n,p=o,b=s,v=u,y=i,e={constraint:(c=e)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);var c,l,f,p,b,v,y;return t}())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=jc())===a&&(r=null);r!==a&&tv()!==a?("foreign key"===t.substr(Ti,11).toLowerCase()?(n=t.substr(Ti,11),Ti+=11):(n=a,0===Ii&&Mi(Kt)),n!==a&&tv()!==a?((o=hf())===a&&(o=null),o!==a&&tv()!==a&&(s=Fc())!==a&&tv()!==a?((u=gc())===a&&(u=null),u!==a?(Si=e,c=n,l=o,f=s,p=u,r={constraint:(i=r)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var i,c,l,f,p;return e}())===a&&(e=Cc()),e}function jc(){var t,e,r,n;return t=Ti,(e=Db())!==a&&tv()!==a?((r=sf())===a&&(r=null),r!==a?(Si=t,n=r,t=e={keyword:e.toLowerCase(),constraint:n}):(Ti=t,t=a)):(Ti=t,t=a),t}function Cc(){var e,r,n,o,s,u,i;return e=Ti,(r=jc())===a&&(r=null),r!==a&&tv()!==a?("check"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(v)),n!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(o=Dl())!==a&&tv()!==a&&Yb()!==a?(Si=e,u=n,i=o,e=r={constraint:(s=r)&&s.constraint,definition:[i],constraint_type:u.toLowerCase(),keyword:s&&s.keyword,resource:"constraint"}):(Ti=e,e=a)):(Ti=e,e=a),e}function gc(){var e,r,n,o,s,u,i,c,l,f;return e=Ti,(r=Fb())!==a&&tv()!==a&&(n=cl())!==a&&tv()!==a&&(o=Fc())!==a&&tv()!==a?("match full"===t.substr(Ti,10).toLowerCase()?(s=t.substr(Ti,10),Ti+=10):(s=a,0===Ii&&Mi(Jt)),s===a&&("match partial"===t.substr(Ti,13).toLowerCase()?(s=t.substr(Ti,13),Ti+=13):(s=a,0===Ii&&Mi(zt)),s===a&&("match simple"===t.substr(Ti,12).toLowerCase()?(s=t.substr(Ti,12),Ti+=12):(s=a,0===Ii&&Mi(te)))),s===a&&(s=null),s!==a&&tv()!==a?((u=Ec())===a&&(u=null),u!==a&&tv()!==a?((i=Ec())===a&&(i=null),i!==a?(Si=e,c=s,l=u,f=i,e=r={definition:o,table:[n],keyword:r.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=Ec())!==a&&(Si=e,r={on_action:[r]}),e=r),e}function Ec(){var e,r,n,o;return e=Ti,gp()!==a&&tv()!==a?((r=fp())===a&&(r=up()),r!==a&&tv()!==a&&(n=function(){var e,r,n;e=Ti,(r=Ab())!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((n=Nl())===a&&(n=null),n!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},args:n}):(Ti=e,e=a)):(Ti=e,e=a);e===a&&(e=Ti,"restrict"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(kt)),r===a&&("cascade"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Rt)),r===a&&("set null"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ee)),r===a&&("no action"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(re)),r===a&&("set default"===t.substr(Ti,11).toLowerCase()?(r=t.substr(Ti,11),Ti+=11):(r=a,0===Ii&&Mi(ne)),r===a&&(r=Ab()))))),r!==a&&(Si=e,r={type:"origin",value:r.toLowerCase()}),e=r);return e}())!==a?(Si=e,o=n,e={type:"on "+r[0].toLowerCase(),value:o}):(Ti=e,e=a)):(Ti=e,e=a),e}function Ac(){var e,r,n,o,s,u,i;return e=Ti,(r=pp())===a&&(r=fp())===a&&(r=gb()),r!==a&&(Si=e,i=r,r={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(e=r)===a&&(e=Ti,(r=up())!==a&&tv()!==a?(n=Ti,"of"===t.substr(Ti,2).toLowerCase()?(o=t.substr(Ti,2),Ti+=2):(o=a,0===Ii&&Mi(le)),o!==a&&(s=tv())!==a&&(u=bl())!==a?n=o=[o,s,u]:(Ti=n,n=a),n===a&&(n=null),n!==a?(Si=e,e=r=function(t,e){return{keyword:t&&t[0]&&t[0].toLowerCase(),args:e&&{keyword:e[0],columns:e[2]}||null}}(r,n)):(Ti=e,e=a)):(Ti=e,e=a)),e}function Tc(){var e,r,n;return e=Ti,"character"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(me)),r!==a&&tv()!==a?("set"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(we)),n!==a?(Si=e,e=r="CHARACTER SET"):(Ti=e,e=a)):(Ti=e,e=a),e}function Sc(){var e,r,n,o,s,u,i,c,l;return e=Ti,(r=ep())===a&&(r=null),r!==a&&tv()!==a?((n=Tc())===a&&("charset"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(Le)),n===a&&("collate"===t.substr(Ti,7).toLowerCase()?(n=t.substr(Ti,7),Ti+=7):(n=a,0===Ii&&Mi(Oe)))),n!==a&&tv()!==a?((o=Nb())===a&&(o=null),o!==a&&tv()!==a&&(s=of())!==a?(Si=e,i=n,c=o,l=s,e=r={keyword:(u=r)&&`${u[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Uc(){var e,r,n,o,s,u,i,c,l;return e=Ti,"auto_increment"===t.substr(Ti,14).toLowerCase()?(r=t.substr(Ti,14),Ti+=14):(r=a,0===Ii&&Mi(Ct)),r===a&&("avg_row_length"===t.substr(Ti,14).toLowerCase()?(r=t.substr(Ti,14),Ti+=14):(r=a,0===Ii&&Mi(je)),r===a&&("key_block_size"===t.substr(Ti,14).toLowerCase()?(r=t.substr(Ti,14),Ti+=14):(r=a,0===Ii&&Mi(Ce)),r===a&&("max_rows"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ge)),r===a&&("min_rows"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Ee)),r===a&&("stats_sample_pages"===t.substr(Ti,18).toLowerCase()?(r=t.substr(Ti,18),Ti+=18):(r=a,0===Ii&&Mi(Ae))))))),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a&&(o=Yf())!==a?(Si=e,c=n,l=o,e=r={keyword:r.toLowerCase(),symbol:c,value:l.value}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Sc())===a&&(e=Ti,(r=Pb())===a&&("connection"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Te))),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a&&(o=Ff())!==a?(Si=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:`'${r.value}'`}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"compression"===t.substr(Ti,11).toLowerCase()?(r=t.substr(Ti,11),Ti+=11):(r=a,0===Ii&&Mi(Se)),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a?(o=Ti,39===t.charCodeAt(Ti)?(s="'",Ti++):(s=a,0===Ii&&Mi(Ue)),s!==a?("zlib"===t.substr(Ti,4).toLowerCase()?(u=t.substr(Ti,4),Ti+=4):(u=a,0===Ii&&Mi(_e)),u===a&&("lz4"===t.substr(Ti,3).toLowerCase()?(u=t.substr(Ti,3),Ti+=3):(u=a,0===Ii&&Mi(xe)),u===a&&("none"===t.substr(Ti,4).toLowerCase()?(u=t.substr(Ti,4),Ti+=4):(u=a,0===Ii&&Mi(wt)))),u!==a?(39===t.charCodeAt(Ti)?(i="'",Ti++):(i=a,0===Ii&&Mi(Ue)),i!==a?o=s=[s,u,i]:(Ti=o,o=a)):(Ti=o,o=a)):(Ti=o,o=a),o!==a?(Si=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.join("").toUpperCase()}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"engine"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ie)),r!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a&&(o=wf())!==a?(Si=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.toUpperCase()}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=hp())!==a&&tv()!==a&&(n=xp())!==a&&tv()!==a&&(o=Pl())!==a?(Si=e,e=r=function(t){return{keyword:"partition by",value:t}}(o)):(Ti=e,e=a))))),e}function _c(){var e,r,n;return e=Ti,(r=sp())===a&&(r=pp())===a&&(r=up())===a&&(r=fp())===a&&(r=gb())===a&&(r=Fb())===a&&("trigger"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(oe))),r!==a&&(Si=e,n=r,r={type:"origin",value:Array.isArray(n)?n[0]:n}),e=r}function xc(){var e,r,n,o;return e=Ti,kp()!==a?(r=Ti,(n=tv())!==a?("privileges"===t.substr(Ti,10).toLowerCase()?(o=t.substr(Ti,10),Ti+=10):(o=a,0===Ii&&Mi(Xe)),o!==a?r=n=[n,o]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=null),r!==a?(Si=e,e={type:"origin",value:r?"all privileges":"all"}):(Ti=e,e=a)):(Ti=e,e=a),e}function Ic(){var e;return(e=_c())===a&&(e=function(){var e,r;return e=Ti,"usage"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(He)),r===a&&(r=sp())===a&&(r=up()),r!==a&&(Si=e,r=Ye(r)),e=r}())===a&&(e=function(){var e,r;return e=Ti,(r=ip())===a&&("connect"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(We)),r===a&&(r=cp())===a&&(r=lp())),r!==a&&(Si=e,r=Ye(r)),e=r}())===a&&(e=function(){var e,r;return e=Ti,"usage"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(He)),r!==a&&(Si=e,r=Ze(r)),(e=r)===a&&(e=xc()),e}())===a&&(e=function(){var e,r;return e=Ti,"execute"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(ie)),r!==a&&(Si=e,r=Ze(r)),(e=r)===a&&(e=xc()),e}()),e}function Nc(){var t,e,r,n,o,s,u,i,c;return t=Ti,(e=Ic())!==a&&tv()!==a?(r=Ti,(n=Hb())!==a&&(o=tv())!==a&&(s=bl())!==a&&(u=tv())!==a&&(i=Yb())!==a?r=n=[n,o,s,u,i]:(Ti=r,r=a),r===a&&(r=null),r!==a?(Si=t,t=e={priv:e,columns:(c=r)&&c[2]}):(Ti=t,t=a)):(Ti=t,t=a),t}function Rc(){var t,e,r,n,o,s,u;return t=Ti,e=Ti,(r=sf())!==a&&(n=tv())!==a&&(o=Gb())!==a?e=r=[r,n,o]:(Ti=e,e=a),e===a&&(e=null),e!==a&&(r=tv())!==a?((n=sf())===a&&(n=Bb()),n!==a?(Si=t,u=n,t=e={prefix:(s=e)&&s[0],name:u}):(Ti=t,t=a)):(Ti=t,t=a),t}function kc(){var e,r,n,o;return e=Ti,(r=_p())===a&&(r=null),r!==a&&tv()!==a&&(n=sf())!==a?(Si=e,o=n,e=r={name:{type:"origin",value:r?`${group} ${o}`:o}}):(Ti=e,e=a),e===a&&(e=Ti,"public"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(ir)),r===a&&(r=function(){var e,r,n,o;e=Ti,"current_role"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Dt));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CURRENT_ROLE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(r=Tb())===a&&(r=Sb()),r!==a&&(Si=e,r=function(t){return{name:{type:"origin",value:t}}}(r)),e=r),e}function Vc(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=kc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=kc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=kc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Mc(){var e,r,n,o,s,u,i,c;return e=Ti,"grant"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(cr)),r!==a&&(Si=e,r={type:"grant"}),(e=r)===a&&(e=Ti,"revoke"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(pr)),r!==a&&tv()!==a?(n=Ti,"grant"===t.substr(Ti,5).toLowerCase()?(o=t.substr(Ti,5),Ti+=5):(o=a,0===Ii&&Mi(cr)),o!==a&&(s=tv())!==a?("option"===t.substr(Ti,6).toLowerCase()?(u=t.substr(Ti,6),Ti+=6):(u=a,0===Ii&&Mi(lr)),u!==a&&(i=tv())!==a?("for"===t.substr(Ti,3).toLowerCase()?(c=t.substr(Ti,3),Ti+=3):(c=a,0===Ii&&Mi(ve)),c!==a?n=o=[o,s,u,i,c]:(Ti=n,n=a)):(Ti=n,n=a)):(Ti=n,n=a),n===a&&(n=null),n!==a?(Si=e,e=r={type:"revoke",grant_option_for:n&&{type:"origin",value:"grant option for"}}):(Ti=e,e=a)):(Ti=e,e=a)),e}function qc(){var e,r,n,o,s,u;return e=Ti,"elseif"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(br)),r!==a&&tv()!==a&&(n=Pl())!==a&&tv()!==a?("then"===t.substr(Ti,4).toLowerCase()?(o=t.substr(Ti,4),Ti+=4):(o=a,0===Ii&&Mi(vr)),o!==a&&tv()!==a&&(s=Qi())!==a&&tv()!==a?((u=Zb())===a&&(u=null),u!==a?(Si=e,e=r={type:"elseif",boolean_expr:n,then:s,semicolon:u}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Pc(){var e,r,n,o,s,u,i;return e=Ti,(r=sp())!==a&&(n=tv())!==a?(59===t.charCodeAt(Ti)?(o=";",Ti++):(o=a,0===Ii&&Mi(Vr)),o!==a?(Si=e,e=r={type:"select"}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=$c())===a&&(e=Ti,r=Ti,40===t.charCodeAt(Ti)?(n="(",Ti++):(n=a,0===Ii&&Mi(Mr)),n!==a&&(o=tv())!==a&&(s=Pc())!==a&&(u=tv())!==a?(41===t.charCodeAt(Ti)?(i=")",Ti++):(i=a,0===Ii&&Mi(qr)),i!==a?r=n=[n,o,s,u,i]:(Ti=r,r=a)):(Ti=r,r=a),r!==a&&(Si=e,r={...r[2],parentheses_symbol:!0}),e=r),e}function Dc(){var t,e,r,n,o,s,u,i,c;if(t=Ti,Up()!==a)if(tv()!==a)if((e=Qc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Qc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Qc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;return t===a&&(t=Ti,tv()!==a&&Up()!==a&&(e=tv())!==a&&(r=bp())!==a&&(n=tv())!==a&&(o=Qc())!==a?(Si=t,(c=o).recursive=!0,t=[c]):(Ti=t,t=a)),t}function Qc(){var t,e,r,n,o,s;return t=Ti,(e=Ff())===a&&(e=wf()),e!==a&&tv()!==a?((r=Fc())===a&&(r=null),r!==a&&tv()!==a&&Op()!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Qi())!==a&&tv()!==a&&Yb()!==a?(Si=t,s=r,"string"==typeof(o=e)&&(o={type:"default",value:o}),t=e={name:o,stmt:n.ast,columns:s}):(Ti=t,t=a)):(Ti=t,t=a),t}function Fc(){var t,e;return t=Ti,Hb()!==a&&tv()!==a&&(e=bl())!==a&&tv()!==a&&Yb()!==a?(Si=t,t=e):(Ti=t,t=a),t}function Gc(){var t,e,r,n,o;return t=Ti,(e=Vp())!==a&&tv()!==a&&gp()!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(r=bl())!==a&&tv()!==a&&Yb()!==a?(Si=t,n=e,o=r,console.lo,t=e={type:n+" ON",columns:o}):(Ti=t,t=a),t===a&&(t=Ti,(e=Vp())===a&&(e=null),e!==a&&(Si=t,e=function(t){return{type:t}}(e)),t=e),t}function $c(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h;return e=Ti,tv()!==a?((r=Dc())===a&&(r=null),r!==a&&tv()!==a&&sp()!==a&&ev()!==a?((n=function(){var t,e,r,n,o,s;if(t=Ti,(e=Bc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Bc())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Bc())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,e=function(t,e){const r=[t];for(let t=0,n=e.length;t<n;++t)r.push(e[t][1]);return r}(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())===a&&(n=null),n!==a&&tv()!==a?((o=Gc())===a&&(o=null),o!==a&&tv()!==a&&(s=Hc())!==a&&tv()!==a?((u=zc())===a&&(u=null),u!==a&&tv()!==a?((i=tl())===a&&(i=null),i!==a&&tv()!==a?((c=zc())===a&&(c=null),c!==a&&tv()!==a?((l=pl())===a&&(l=null),l!==a&&tv()!==a?((f=function(){var t,e,r;t=Ti,(e=_p())!==a&&tv()!==a&&xp()!==a&&tv()!==a&&(r=Nl())!==a?(Si=t,e={columns:r.value},t=e):(Ti=t,t=a);return t}())===a&&(f=null),f!==a&&tv()!==a?((p=function(){var e,r;e=Ti,function(){var e,r,n,o;e=Ti,"having"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(cs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(r=Dl())!==a?(Si=e,e=r):(Ti=e,e=a);return e}())===a&&(p=null),p!==a&&tv()!==a?((b=function(){var e,r;e=Ti,function(){var e,r,n,o;e=Ti,"qualify"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(ls));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(r=Dl())!==a?(Si=e,e=r):(Ti=e,e=a);return e}())===a&&(b=null),b!==a&&tv()!==a?((v=Ol())===a&&(v=null),v!==a&&tv()!==a?((y=gl())===a&&(y=null),y!==a&&tv()!==a?((d=function(){var e,r;e=Ti,function(){var e,r,n,o;e=Ti,"window"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(I));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(r=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=vl())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=vl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=vl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=e,e={keyword:"window",type:"window",expr:r}):(Ti=e,e=a);return e}())===a&&(d=null),d!==a&&tv()!==a?((h=zc())===a&&(h=null),h!==a?(Si=e,e=function(t,e,r,n,o,a,s,u,i,c,l,f,p,b,v){if(o&&s||o&&v||s&&v||o&&s&&v)throw new Error("A given SQL statement can contain at most one INTO clause");return a&&a.forEach(t=>t.table&&kv.add(`select::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{with:t,type:"select",options:e,distinct:r,columns:n,into:{...o||s||v||{},position:(o?"column":s&&"from")||v&&"end"},from:a,where:u,groupby:i,having:c,qualify:l,orderby:f,limit:p,window:b}}(r,n,o,s,u,i,c,l,f,p,b,v,y,d,h)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Bc(){var e,r;return e=Ti,(r=function(){var e;"sql_calc_found_rows"===t.substr(Ti,19).toLowerCase()?(e=t.substr(Ti,19),Ti+=19):(e=a,0===Ii&&Mi(zu));return e}())===a&&((r=function(){var e;"sql_cache"===t.substr(Ti,9).toLowerCase()?(e=t.substr(Ti,9),Ti+=9):(e=a,0===Ii&&Mi(ti));return e}())===a&&(r=function(){var e;"sql_no_cache"===t.substr(Ti,12).toLowerCase()?(e=t.substr(Ti,12),Ti+=12):(e=a,0===Ii&&Mi(ei));return e}()),r===a&&(r=function(){var e;"sql_big_result"===t.substr(Ti,14).toLowerCase()?(e=t.substr(Ti,14),Ti+=14):(e=a,0===Ii&&Mi(ni));return e}())===a&&(r=function(){var e;"sql_small_result"===t.substr(Ti,16).toLowerCase()?(e=t.substr(Ti,16),Ti+=16):(e=a,0===Ii&&Mi(ri));return e}())===a&&(r=function(){var e;"sql_buffer_result"===t.substr(Ti,17).toLowerCase()?(e=t.substr(Ti,17),Ti+=17):(e=a,0===Ii&&Mi(oi));return e}())),r!==a&&(Si=e,r=r),e=r}function Hc(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=kp())===a&&(e=Ti,(r=Bb())!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Bb())),e!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Zc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Zc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){Vv.add("select::null::(.*)");const r={expr:{type:"column_ref",table:null,column:"*"},as:null};return e&&e.length>0?Uv(r,e):[r]}(0,r)):(Ti=t,t=a)}else Ti=t,t=a;if(t===a)if(t=Ti,(e=Zc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Zc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Zc())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Yc(){var t,e;return t=Ti,Wb()!==a&&tv()!==a?((e=Yf())===a&&(e=Ff()),e!==a&&tv()!==a&&Xb()!==a?(Si=t,t={brackets:!0,index:e}):(Ti=t,t=a)):(Ti=t,t=a),t}function Wc(){var t,e,r,n,o,s;if(t=Ti,(e=Yc())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Yc())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Yc())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r,1)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Xc(){var t,e,r,n,o;return t=Ti,(e=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Pl())!==a){for(r=[],n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp())===a&&(s=zb()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp())===a&&(s=zb()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);r!==a?(Si=t,e=function(t,e){const r=t.ast;if(r&&"select"===r.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==r.columns.length||"*"===r.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!e||0===e.length)return t;const n=e.length;let o=e[n-1][3];for(let r=n-1;r>=0;r--){const n=0===r?t:e[r-1][3];o=Tv(e[r][1],n,o)}return o}(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a&&tv()!==a?((r=Wc())===a&&(r=null),r!==a?(Si=t,n=e,(o=r)&&(n.array_index=o),t=e=n):(Ti=t,t=a)):(Ti=t,t=a),t}function Zc(){var t,e,r,n,o,s,u,i,c,l,f;if(t=Ti,(e=tf())!==a&&(Si=t,e={expr:e,as:null}),(t=e)===a){if(t=Ti,(e=rf())===a&&(e=Xc()),e!==a)if((r=tv())!==a)if((n=Vf())!==a)if((o=tv())!==a){for(s=[],u=Ti,(i=tv())!==a?((c=Wl())===a&&(c=Zl()),c!==a&&(l=tv())!==a&&(f=Xc())!==a?u=i=[i,c,l,f]:(Ti=u,u=a)):(Ti=u,u=a);u!==a;)s.push(u),u=Ti,(i=tv())!==a?((c=Wl())===a&&(c=Zl()),c!==a&&(l=tv())!==a&&(f=Xc())!==a?u=i=[i,c,l,f]:(Ti=u,u=a)):(Ti=u,u=a);s!==a&&(u=tv())!==a?((i=Jc())===a&&(i=null),i!==a?(Si=t,t=e=function(t,e,r,n){return{...e,as:n,type:"cast",expr:t,tail:r&&r[0]&&{operator:r[0][1],expr:r[0][3]}}}(e,n,s,i)):(Ti=t,t=a)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;t===a&&(t=Ti,(e=af())!==a&&(r=tv())!==a&&(n=Gb())!==a?(o=Ti,(s=af())!==a&&(u=tv())!==a&&(i=Gb())!==a?o=s=[s,u,i]:(Ti=o,o=a),o===a&&(o=null),o!==a&&(s=tv())!==a&&(u=Bb())!==a?(Si=t,t=e=function(t,e){const r=e&&e[0];let n;r&&(n=t,t=r),Vv.add(`select::${t}::(.*)`);return{expr:{type:"column_ref",table:t,schema:n,column:"*"},as:null}}(e,o)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,e=Ti,(r=af())!==a&&(n=tv())!==a&&(o=Gb())!==a?e=r=[r,n,o]:(Ti=e,e=a),e===a&&(e=null),e!==a&&(r=tv())!==a&&(n=Bb())!==a?(Si=t,t=e=function(t){const e=t&&t[0]||null;return Vv.add(`select::${e.value}::(.*)`),{expr:{type:"column_ref",table:e,column:"*"},as:null}}(e)):(Ti=t,t=a),t===a&&(t=Ti,(e=Xc())!==a&&(r=tv())!==a?((n=Jc())===a&&(n=null),n!==a?(Si=t,t=e={type:"expr",expr:e,as:n}):(Ti=t,t=a)):(Ti=t,t=a))))}return t}function Kc(){var t,e,r;return t=Ti,(e=Op())===a&&(e=null),e!==a&&tv()!==a&&(r=cf())!==a?(Si=t,t=e=r):(Ti=t,t=a),t}function Jc(){var t,e,r;return t=Ti,(e=Op())!==a&&tv()!==a&&(r=cf())!==a?(Si=t,t=e=r):(Ti=t,t=a),t===a&&(t=Ti,(e=Op())===a&&(e=null),e!==a&&tv()!==a&&(r=cf())!==a?(Si=t,t=e=r):(Ti=t,t=a)),t}function zc(){var e,r,n;return e=Ti,mp()!==a&&tv()!==a&&(r=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=dv())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=dv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=dv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=e,e={keyword:"var",type:"into",expr:r}):(Ti=e,e=a),e===a&&(e=Ti,mp()!==a&&tv()!==a?("outfile"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Dr)),r===a&&("dumpfile"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Qr))),r===a&&(r=null),r!==a&&tv()!==a?((n=Ff())===a&&(n=sf()),n!==a?(Si=e,e={keyword:r,type:"into",expr:n}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)),e}function tl(){var t,e;return t=Ti,wp()!==a&&tv()!==a&&(e=al())!==a?(Si=t,t=e):(Ti=t,t=a),t}function el(){var t,e,r;return t=Ti,(e=cl())!==a&&tv()!==a&&rp()!==a&&tv()!==a&&(r=cl())!==a?(Si=t,t=e=[e,r]):(Ti=t,t=a),t}function rl(){var e,r;return e=Ti,Sp()!==a&&tv()!==a?("btree"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Fr)),r===a&&("hash"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Gr)),r===a&&("gist"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi($r)),r===a&&("gin"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Br))))),r!==a?(Si=e,e={keyword:"using",type:r.toLowerCase()}):(Ti=e,e=a)):(Ti=e,e=a),e}function nl(){var t,e,r,n,o,s;if(t=Ti,(e=ol())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=ol())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=ol())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){const r=[t];for(let t=0;t<e.length;t++)r.push(e[t][1]);return r}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function ol(){var e,r,n,o,s,u;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"key_block_size"===t.substr(Ti,14).toLowerCase()?(r=t.substr(Ti,14),Ti+=14):(r=a,0===Ii&&Mi(Ce));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="KEY_BLOCK_SIZE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a?((n=Nb())===a&&(n=null),n!==a&&tv()!==a&&(o=Yf())!==a?(Si=e,s=n,u=o,e=r={type:r.toLowerCase(),symbol:s,expr:u}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=wf())!==a&&tv()!==a&&(n=Nb())!==a&&tv()!==a?((o=Yf())===a&&(o=sf()),o!==a?(Si=e,e=r=function(t,e,r){return{type:t.toLowerCase(),symbol:e,expr:"string"==typeof r&&{type:"origin",value:r}||r}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=rl())===a&&(e=Ti,"with"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Hr)),r!==a&&tv()!==a?("parser"===t.substr(Ti,6).toLowerCase()?(n=t.substr(Ti,6),Ti+=6):(n=a,0===Ii&&Mi(Yr)),n!==a&&tv()!==a&&(o=wf())!==a?(Si=e,e=r={type:"with parser",expr:o}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"visible"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Wr)),r===a&&("invisible"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Xr))),r!==a&&(Si=e,r=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(r)),(e=r)===a&&(e=nv())))),e}function al(){var t,e,r,n;if(t=Ti,(e=ul())!==a){for(r=[],n=sl();n!==a;)r.push(n),n=sl();r!==a?(Si=t,t=e=Zr(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function sl(){var t,e,r;return t=Ti,tv()!==a&&(e=$b())!==a&&tv()!==a&&(r=ul())!==a?(Si=t,t=r):(Ti=t,t=a),t===a&&(t=Ti,tv()!==a&&(e=function(){var t,e,r,n,o,s,u,i,c,l,f;if(t=Ti,(e=il())!==a)if(tv()!==a)if((r=ul())!==a)if(tv()!==a)if((n=Sp())!==a)if(tv()!==a)if(Hb()!==a)if(tv()!==a)if((o=of())!==a){for(s=[],u=Ti,(i=tv())!==a&&(c=$b())!==a&&(l=tv())!==a&&(f=of())!==a?u=i=[i,c,l,f]:(Ti=u,u=a);u!==a;)s.push(u),u=Ti,(i=tv())!==a&&(c=$b())!==a&&(l=tv())!==a&&(f=of())!==a?u=i=[i,c,l,f]:(Ti=u,u=a);s!==a&&(u=tv())!==a&&(i=Yb())!==a?(Si=t,p=e,v=o,y=s,(b=r).join=p,b.using=Uv(v,y),t=e=b):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;var p,b,v,y;t===a&&(t=Ti,(e=il())!==a&&tv()!==a&&(r=ul())!==a&&tv()!==a?((n=fl())===a&&(n=null),n!==a?(Si=t,e=function(t,e,r){return e.join=t,e.on=r,e}(e,r,n),t=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=il())===a&&(e=Gi()),e!==a&&tv()!==a&&(r=Hb())!==a&&tv()!==a?((n=$i())===a&&(n=al()),n!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((o=Jc())===a&&(o=null),o!==a&&(s=tv())!==a?((u=fl())===a&&(u=null),u!==a?(Si=t,e=function(t,e,r,n){return Array.isArray(e)&&(e={type:"tables",expr:e}),e.parentheses=!0,{expr:e,as:r,join:t,on:n}}(e,n,o,u),t=e):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)));return t}())!==a?(Si=t,t=e):(Ti=t,t=a)),t}function ul(){var e,r,n,o,s,u,i,c,l,f,p,b;return e=Ti,(r=function(){var e;"dual"===t.substr(Ti,4).toLowerCase()?(e=t.substr(Ti,4),Ti+=4):(e=a,0===Ii&&Mi(Bu));return e}())!==a&&(Si=e,r={type:"dual"}),(e=r)===a&&(e=Ti,(r=xl())!==a&&tv()!==a?((n=Kc())===a&&(n=null),n!==a?(Si=e,e=r={expr:{type:"values",values:r},as:n}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"lateral"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Kr)),r===a&&(r=null),r!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a?((o=$i())===a&&(o=xl()),o!==a&&tv()!==a&&(s=Yb())!==a&&(u=tv())!==a?((i=Kc())===a&&(i=null),i!==a?(Si=e,e=r=function(t,e,r){return Array.isArray(e)&&(e={type:"values",values:e}),e.parentheses=!0,{prefix:t,expr:e,as:r}}(r,o,i)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"lateral"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Kr)),r===a&&(r=null),r!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a&&(o=al())!==a&&tv()!==a&&(s=Yb())!==a&&(u=tv())!==a?((i=Kc())===a&&(i=null),i!==a?(Si=e,e=r=function(t,e,r){return{prefix:t,expr:e={type:"tables",expr:e,parentheses:!0},as:r}}(r,o,i)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"lateral"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Kr)),r===a&&(r=null),r!==a&&tv()!==a&&(n=Nf())!==a&&tv()!==a?((o=Jc())===a&&(o=null),o!==a?(Si=e,e=r=function(t,e,r){return{prefix:t,type:"expr",expr:e,as:r}}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=cl())!==a&&tv()!==a?("tablesample"===t.substr(Ti,11).toLowerCase()?(n=t.substr(Ti,11),Ti+=11):(n=a,0===Ii&&Mi(Jr)),n!==a&&tv()!==a&&(o=Nf())!==a&&tv()!==a?(s=Ti,"repeatable"===t.substr(Ti,10).toLowerCase()?(u=t.substr(Ti,10),Ti+=10):(u=a,0===Ii&&Mi(zr)),u!==a&&(i=tv())!==a&&(c=Hb())!==a&&(l=tv())!==a&&(f=Yf())!==a&&(p=tv())!==a&&(b=Yb())!==a?s=u=[u,i,c,l,f,p,b]:(Ti=s,s=a),s===a&&(s=null),s!==a&&(u=tv())!==a?((i=Jc())===a&&(i=null),i!==a?(Si=e,e=r=function(t,e,r,n){return{...t,as:n,tablesample:{expr:e,repeatable:r&&r[4]}}}(r,o,s,i)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=cl())!==a&&tv()!==a?((n=Jc())===a&&(n=null),n!==a?(Si=e,e=r=function(t,e){return"var"===t.type?(t.as=e,t):{...t,as:e}}(r,n)):(Ti=e,e=a)):(Ti=e,e=a))))))),e}function il(){var e,r,n,o;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"left"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Wa));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(n=tv())!==a?((o=Ap())===a&&(o=null),o!==a&&tv()!==a&&Ep()!==a?(Si=e,e=r="LEFT JOIN"):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=function(){var e,r,n,o;e=Ti,"right"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Xa));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(n=tv())!==a?((o=Ap())===a&&(o=null),o!==a&&tv()!==a&&Ep()!==a?(Si=e,e=r="RIGHT JOIN"):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=function(){var e,r,n,o;e=Ti,"full"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Za));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(n=tv())!==a?((o=Ap())===a&&(o=null),o!==a&&tv()!==a&&Ep()!==a?(Si=e,e=r="FULL JOIN"):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"cross"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(tn)),r!==a&&(n=tv())!==a&&(o=Ep())!==a?(Si=e,e=r="CROSS JOIN"):(Ti=e,e=a),e===a&&(e=Ti,r=Ti,(n=function(){var e,r,n,o;e=Ti,"inner"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Ka));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(o=tv())!==a?r=n=[n,o]:(Ti=r,r=a),r===a&&(r=null),r!==a&&(n=Ep())!==a?(Si=e,e=r="INNER JOIN"):(Ti=e,e=a))))),e}function cl(){var t,e,r,n,o,s,u,i,c;return t=Ti,(e=sf())!==a?(r=Ti,(n=tv())!==a&&(o=Gb())!==a&&(s=tv())!==a?((u=sf())===a&&(u=Bb()),u!==a?r=n=[n,o,s,u]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=null),r!==a?(n=Ti,(o=tv())!==a&&(s=Gb())!==a&&(u=tv())!==a?((i=sf())===a&&(i=Bb()),i!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a),n===a&&(n=null),n!==a?(Si=t,t=e=function(t,e,r){const n={db:null,table:t};return null!==r?(n.db=t,n.schema=e[3],n.table=r[3],n):(null!==e&&(n.db=t,n.table=e[3]),n)}(e,r,n)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=dv())!==a&&(Si=t,(c=e).db=null,c.table=c.name,e=c),t=e),t}function ll(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Pl())!==a){for(r=[],n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){const r=e.length;let n=t;for(let t=0;t<r;++t)n=Tv(e[t][1],n,e[t][3]);return n}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function fl(){var t,e;return t=Ti,gp()!==a&&tv()!==a&&(e=Dl())!==a?(Si=t,t=e):(Ti=t,t=a),t}function pl(){var e,r;return e=Ti,function(){var e,r,n,o;e=Ti,"where"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(as));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(r=Dl())!==a?(Si=e,e=r):(Ti=e,e=a),e}function bl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=ef())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=ef())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=ef())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function vl(){var t,e,r;return t=Ti,(e=wf())!==a&&tv()!==a&&Op()!==a&&tv()!==a&&(r=yl())!==a?(Si=t,t=e={name:e,as_window_specification:r}):(Ti=t,t=a),t}function yl(){var t,e;return(t=wf())===a&&(t=Ti,Hb()!==a&&tv()!==a?((e=function(){var t,e,r,n;t=Ti,(e=Ll())===a&&(e=null);e!==a&&tv()!==a?((r=Ol())===a&&(r=null),r!==a&&tv()!==a?((n=function(){var t,e,r,n,o;t=Ti,(e=Ob())!==a&&tv()!==a?((r=dl())===a&&(r=hl()),r!==a?(Si=t,t=e={type:"rows",expr:r}):(Ti=t,t=a)):(Ti=t,t=a);t===a&&(t=Ti,(e=Ob())!==a&&tv()!==a&&(r=Mp())!==a&&tv()!==a&&(n=hl())!==a&&tv()!==a&&$p()!==a&&tv()!==a&&(o=dl())!==a?(Si=t,e=Tv(r,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),t=e):(Ti=t,t=a));return t}())===a&&(n=null),n!==a?(Si=t,t=e={name:null,partitionby:e,orderby:r,window_frame_clause:n}):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a);return t}())===a&&(e=null),e!==a&&tv()!==a&&Yb()!==a?(Si=t,t={window_specification:e||{},parentheses:!0}):(Ti=t,t=a)):(Ti=t,t=a)),t}function dl(){var e,r,n,o;return e=Ti,(r=wl())!==a&&tv()!==a?("following"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(en)),n!==a?(Si=e,(o=r).value+=" FOLLOWING",e=r=o):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=ml()),e}function hl(){var e,r,n,o,s;return e=Ti,(r=wl())!==a&&tv()!==a?("preceding"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(rn)),n===a&&("following"===t.substr(Ti,9).toLowerCase()?(n=t.substr(Ti,9),Ti+=9):(n=a,0===Ii&&Mi(en))),n!==a?(Si=e,s=n,(o=r).value+=" "+s.toUpperCase(),e=r=o):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=ml()),e}function ml(){var e,r,n;return e=Ti,"current"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(st)),r!==a&&tv()!==a?("row"===t.substr(Ti,3).toLowerCase()?(n=t.substr(Ti,3),Ti+=3):(n=a,0===Ii&&Mi(de)),n!==a?(Si=e,e=r={type:"origin",value:"current row"}):(Ti=e,e=a)):(Ti=e,e=a),e}function wl(){var e,r;return e=Ti,"unbounded"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(nn)),r!==a&&(Si=e,r={type:"origin",value:r.toUpperCase()}),(e=r)===a&&(e=Yf()),e}function Ll(){var t,e;return t=Ti,hp()!==a&&tv()!==a&&xp()!==a&&tv()!==a&&(e=Hc())!==a?(Si=t,t=e):(Ti=t,t=a),t}function Ol(){var t,e;return t=Ti,Ip()!==a&&tv()!==a&&xp()!==a&&tv()!==a&&(e=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=jl())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=jl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=jl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=t,t=e):(Ti=t,t=a),t}function jl(){var e,r,n,o,s,u,i;return e=Ti,(r=Pl())!==a&&tv()!==a?((n=Rp())===a&&(n=Np()),n===a&&(n=null),n!==a&&tv()!==a?(o=Ti,"nulls"===t.substr(Ti,5).toLowerCase()?(s=t.substr(Ti,5),Ti+=5):(s=a,0===Ii&&Mi(Lt)),s!==a&&(u=tv())!==a?("first"===t.substr(Ti,5).toLowerCase()?(i=t.substr(Ti,5),Ti+=5):(i=a,0===Ii&&Mi(Ot)),i===a&&("last"===t.substr(Ti,4).toLowerCase()?(i=t.substr(Ti,4),Ti+=4):(i=a,0===Ii&&Mi(jt))),i===a&&(i=null),i!==a?o=s=[s,u,i]:(Ti=o,o=a)):(Ti=o,o=a),o===a&&(o=null),o!==a?(Si=e,e=r=function(t,e,r){const n={expr:t,type:e};return n.nulls=r&&[r[0],r[2]].filter(t=>t).join(" "),n}(r,n,o)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e}function Cl(){var t;return(t=Yf())===a&&(t=dv())===a&&(t=Cf()),t}function gl(){var e,r,n,o,s,u,i;return e=Ti,r=Ti,(n=function(){var e,r,n,o;e=Ti,"limit"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(fs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(o=tv())!==a?((s=Cl())===a&&(s=kp()),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=null),r!==a&&(n=tv())!==a?(o=Ti,(s=function(){var e,r,n,o;e=Ti,"offset"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(ps));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="OFFSET"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(u=tv())!==a&&(i=Cl())!==a?o=s=[s,u,i]:(Ti=o,o=a),o===a&&(o=null),o!==a?(Si=e,e=r=function(t,e){const r=[];return t&&r.push("string"==typeof t[2]?{type:"origin",value:"all"}:t[2]),e&&r.push(e[2]),{seperator:e&&e[0]&&e[0].toLowerCase()||"",value:r}}(r,o)):(Ti=e,e=a)):(Ti=e,e=a),e}function El(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Al())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Al())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Al())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Al(){var e,r,n,o,s,u,i,c,l;return e=Ti,r=Ti,(n=sf())!==a&&(o=tv())!==a&&(s=Gb())!==a?r=n=[n,o,s]:(Ti=r,r=a),r===a&&(r=null),r!==a&&(n=tv())!==a&&(o=yf())!==a&&(s=tv())!==a?(61===t.charCodeAt(Ti)?(u="=",Ti++):(u=a,0===Ii&&Mi(at)),u!==a&&tv()!==a&&(i=Yl())!==a?(Si=e,e=r={column:{expr:o},value:i,table:(l=r)&&l[0]}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,r=Ti,(n=sf())!==a&&(o=tv())!==a&&(s=Gb())!==a?r=n=[n,o,s]:(Ti=r,r=a),r===a&&(r=null),r!==a&&(n=tv())!==a&&(o=yf())!==a&&(s=tv())!==a?(61===t.charCodeAt(Ti)?(u="=",Ti++):(u=a,0===Ii&&Mi(at)),u!==a&&tv()!==a&&(i=Tp())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(c=ef())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r=function(t,e,r){return{column:{expr:e},value:r,table:t&&t[0],keyword:"values"}}(r,o,c)):(Ti=e,e=a)):(Ti=e,e=a)),e}function Tl(){var e,r,n,o,s;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"returning"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Da));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="RETURNING"):(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&tv()!==a?((n=Hc())===a&&(n=Pc()),n!==a?(Si=e,s=n,e=r={type:(o=r)&&o.toLowerCase()||"returning",columns:"*"===s&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||s}):(Ti=e,e=a)):(Ti=e,e=a),e}function Sl(){var t;return(t=xl())===a&&(t=$c()),t}function Ul(){var t,e,r,n,o,s,u,i,c;if(t=Ti,hp()!==a)if(tv()!==a)if((e=Hb())!==a)if(tv()!==a)if((r=wf())!==a){for(n=[],o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=wf())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);o!==a;)n.push(o),o=Ti,(s=tv())!==a&&(u=$b())!==a&&(i=tv())!==a&&(c=wf())!==a?o=s=[s,u,i,c]:(Ti=o,o=a);n!==a&&(o=tv())!==a&&(s=Yb())!==a?(Si=t,t=Uv(r,n)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;return t===a&&(t=Ti,hp()!==a&&tv()!==a&&(e=Il())!==a?(Si=t,t=e):(Ti=t,t=a)),t}function _l(){var t,e;return t=Ti,(e=pp())!==a&&(Si=t,e="insert"),(t=e)===a&&(t=Ti,(e=vp())!==a&&(Si=t,e="replace"),t=e),t}function xl(){var t,e;return t=Ti,Tp()!==a&&tv()!==a&&(e=function(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Il())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,e=Uv(e,r),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}())!==a?(Si=t,t=e):(Ti=t,t=a),t}function Il(){var t,e;return t=Ti,Hb()!==a&&tv()!==a&&(e=Nl())!==a&&tv()!==a&&Yb()!==a?(Si=t,t=e):(Ti=t,t=a),t}function Nl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Pl())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){const r={type:"expr_list"};return r.value=Uv(t,e),r}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Rl(){var e,r,n;return e=Ti,Eb()!==a&&tv()!==a&&(r=Pl())!==a&&tv()!==a&&(n=function(){var e;(e=function(){var e,r,n,o;e=Ti,"year"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Yo));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="YEAR"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"month"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Do));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="MONTH"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"day"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(To));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DAY"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"hour"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(No));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="HOUR"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"minute"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Po));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="MINUTE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"second"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Fo));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SECOND"):(Ti=e,e=a)):(Ti=e,e=a);return e}());return e}())!==a?(Si=e,e={type:"interval",expr:r,unit:n.toLowerCase()}):(Ti=e,e=a),e===a&&(e=Ti,Eb()!==a&&tv()!==a&&(r=Ff())!==a?(Si=e,e=function(t){return{type:"interval",expr:t,unit:""}}(r)):(Ti=e,e=a)),e}function kl(){var t,e,r,n,o,s;if(t=Ti,(e=Vl())!==a)if(tv()!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Vl())!==a?n=o=[o,s]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Vl())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r,1)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;return t}function Vl(){var e,r,n;return e=Ti,Xp()!==a&&tv()!==a&&(r=Dl())!==a&&tv()!==a&&function(){var e,r,n,o;e=Ti,"then"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(vr));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}()!==a&&tv()!==a&&(n=Pl())!==a?(Si=e,e={type:"when",cond:r,result:n}):(Ti=e,e=a),e}function Ml(){var t,e;return t=Ti,Zp()!==a&&tv()!==a&&(e=Pl())!==a?(Si=t,t={type:"else",result:e}):(Ti=t,t=a),t}function ql(){var t;return(t=Ql())===a&&(t=function(){var t,e,r,n,o,s;if(t=Ti,(e=Wl())!==a){if(r=[],n=Ti,(o=tv())!==a&&(s=Jl())!==a?n=o=[o,s]:(Ti=n,n=a),n!==a)for(;n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Jl())!==a?n=o=[o,s]:(Ti=n,n=a);else r=a;r!==a?(Si=t,e=Av(e,r[0][1]),t=e):(Ti=t,t=a)}else Ti=t,t=a;return t}()),t}function Pl(){var t;return(t=ql())===a&&(t=$i()),t}function Dl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Pl())!==a){for(r=[],n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp())===a&&(s=$b()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a?((s=$p())===a&&(s=Bp())===a&&(s=$b()),s!==a&&(u=tv())!==a&&(i=Pl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){const r=e.length;let n=t,o="";for(let t=0;t<r;++t)","===e[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(e[t][3])):n=Tv(e[t][1],n,e[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Ql(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Fl())!==a){for(r=[],n=Ti,(o=ev())!==a&&(s=Bp())!==a&&(u=tv())!==a&&(i=Fl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=ev())!==a&&(s=Bp())!==a&&(u=tv())!==a&&(i=Fl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=un(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Fl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Gl())!==a){for(r=[],n=Ti,(o=ev())!==a&&(s=$p())!==a&&(u=tv())!==a&&(i=Gl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=ev())!==a&&(s=$p())!==a&&(u=tv())!==a&&(i=Gl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=un(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Gl(){var e,r,n,o,s;return(e=$l())===a&&(e=function(){var t,e,r;t=Ti,(e=function(){var t,e,r,n,o;t=Ti,e=Ti,(r=Gp())!==a&&(n=tv())!==a&&(o=Fp())!==a?e=r=[r,n,o]:(Ti=e,e=a);e!==a&&(Si=t,e=(s=e)[0]+" "+s[2]);var s;(t=e)===a&&(t=Fp());return t}())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(r=$i())!==a&&tv()!==a&&Yb()!==a?(Si=t,n=e,(o=r).parentheses=!0,e=Av(n,o),t=e):(Ti=t,t=a);var n,o;return t}())===a&&(e=Ti,(r=Gp())===a&&(r=Ti,33===t.charCodeAt(Ti)?(n="!",Ti++):(n=a,0===Ii&&Mi(cn)),n!==a?(o=Ti,Ii++,61===t.charCodeAt(Ti)?(s="=",Ti++):(s=a,0===Ii&&Mi(at)),Ii--,s===a?o=void 0:(Ti=o,o=a),o!==a?r=n=[n,o]:(Ti=r,r=a)):(Ti=r,r=a)),r!==a&&(n=tv())!==a&&(o=Gl())!==a?(Si=e,e=r=Av("NOT",o)):(Ti=e,e=a)),e}function $l(){var e,r,n,o,s;return e=Ti,(r=Yl())!==a&&tv()!==a?((n=function(){var e;(e=function(){var t,e,r,n,o,s,u;t=Ti,e=[],r=Ti,(n=tv())!==a&&(o=Bl())!==a&&(s=tv())!==a&&(u=Yl())!==a?r=n=[n,o,s,u]:(Ti=r,r=a);if(r!==a)for(;r!==a;)e.push(r),r=Ti,(n=tv())!==a&&(o=Bl())!==a&&(s=tv())!==a&&(u=Yl())!==a?r=n=[n,o,s,u]:(Ti=r,r=a);else e=a;e!==a&&(Si=t,e={type:"arithmetic",tail:e});return t=e}())===a&&(e=function(){var t,e,r,n;t=Ti,(e=Hl())!==a&&tv()!==a&&(r=Hb())!==a&&tv()!==a&&(n=Nl())!==a&&tv()!==a&&Yb()!==a?(Si=t,t=e={op:e,right:n}):(Ti=t,t=a);t===a&&(t=Ti,(e=Hl())!==a&&tv()!==a?((r=dv())===a&&(r=Ff())===a&&(r=Nf()),r!==a?(Si=t,e=function(t,e){return{op:t,right:e}}(e,r),t=e):(Ti=t,t=a)):(Ti=t,t=a));return t}())===a&&(e=function(){var t,e,r,n;t=Ti,(e=function(){var t,e,r,n,o;t=Ti,e=Ti,(r=Gp())!==a&&(n=tv())!==a&&(o=Mp())!==a?e=r=[r,n,o]:(Ti=e,e=a);e!==a&&(Si=t,e=(s=e)[0]+" "+s[2]);var s;(t=e)===a&&(t=Mp());return t}())!==a&&tv()!==a&&(r=Yl())!==a&&tv()!==a&&$p()!==a&&tv()!==a&&(n=Yl())!==a?(Si=t,t=e={op:e,right:{type:"expr_list",value:[r,n]}}):(Ti=t,t=a);return t}())===a&&(e=function(){var t,e,r,n,o,s,u,i,c;t=Ti,(e=Pp())!==a&&(r=tv())!==a&&(n=Yl())!==a?(Si=t,t=e={op:"IS",right:n}):(Ti=t,t=a);t===a&&(t=Ti,(e=Pp())!==a&&(r=tv())!==a?(n=Ti,(o=Vp())!==a&&(s=tv())!==a&&(u=wp())!==a&&(i=tv())!==a&&(c=cl())!==a?n=o=[o,s,u,i,c]:(Ti=n,n=a),n!==a?(Si=t,e=function(t){const{db:e,table:r}=t.pop(),n="*"===r?"*":`"${r}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(e?`"${e}".${n}`:n)}}}(n),t=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,e=Ti,(r=Pp())!==a&&(n=tv())!==a&&(o=Gp())!==a?e=r=[r,n,o]:(Ti=e,e=a),e!==a&&(r=tv())!==a&&(n=Yl())!==a?(Si=t,e=function(t){return{op:"IS NOT",right:t}}(n),t=e):(Ti=t,t=a)));return t}())===a&&(e=function(){var e,r,n,o;e=Ti,(r=function(){var e,r,n,o,s;e=Ti,r=Ti,(n=Gp())!==a&&(o=tv())!==a?((s=Dp())===a&&(s=Qp()),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a);r!==a&&(Si=e,r=(u=r)[0]+" "+u[2]);var u;(e=r)===a&&(e=Dp())===a&&(e=Qp())===a&&(e=Ti,"similar"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(dn)),r!==a&&(n=tv())!==a&&(o=rp())!==a?(Si=e,e=r="SIMILAR TO"):(Ti=e,e=a),e===a&&(e=Ti,(r=Gp())!==a&&(n=tv())!==a?("similar"===t.substr(Ti,7).toLowerCase()?(o=t.substr(Ti,7),Ti+=7):(o=a,0===Ii&&Mi(dn)),o!==a&&(s=tv())!==a&&rp()!==a?(Si=e,e=r="NOT SIMILAR TO"):(Ti=e,e=a)):(Ti=e,e=a)));return e}())!==a&&tv()!==a?((n=Mf())===a&&(n=$l()),n!==a&&tv()!==a?((o=function(){var e,r,n;e=Ti,"escape"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(On));r!==a&&tv()!==a&&(n=Ff())!==a?(Si=e,e=r={type:"ESCAPE",value:n}):(Ti=e,e=a);return e}())===a&&(o=null),o!==a?(Si=e,s=r,u=n,(i=o)&&(u.escape=i),e=r={op:s,right:u}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a);var s,u,i;return e}())===a&&(e=function(){var e,r,n;e=Ti,(r=function(){var e;"!~*"===t.substr(Ti,3)?(e="!~*",Ti+=3):(e=a,0===Ii&&Mi(hn));e===a&&("~*"===t.substr(Ti,2)?(e="~*",Ti+=2):(e=a,0===Ii&&Mi(mn)),e===a&&(126===t.charCodeAt(Ti)?(e="~",Ti++):(e=a,0===Ii&&Mi(wn)),e===a&&("!~"===t.substr(Ti,2)?(e="!~",Ti+=2):(e=a,0===Ii&&Mi(Ln)))));return e}())!==a&&tv()!==a?((n=Mf())===a&&(n=$l()),n!==a?(Si=e,e=r={op:r,right:n}):(Ti=e,e=a)):(Ti=e,e=a);return e}());return e}())===a&&(n=null),n!==a?(Si=e,o=r,e=r=null===(s=n)?o:"arithmetic"===s.type?_v(o,s.tail):Tv(s.op,o,s.right)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ff())===a&&(e=ef()),e}function Bl(){var e;return">="===t.substr(Ti,2)?(e=">=",Ti+=2):(e=a,0===Ii&&Mi(ln)),e===a&&(62===t.charCodeAt(Ti)?(e=">",Ti++):(e=a,0===Ii&&Mi(fn)),e===a&&("<="===t.substr(Ti,2)?(e="<=",Ti+=2):(e=a,0===Ii&&Mi(pn)),e===a&&("<>"===t.substr(Ti,2)?(e="<>",Ti+=2):(e=a,0===Ii&&Mi(bn)),e===a&&(60===t.charCodeAt(Ti)?(e="<",Ti++):(e=a,0===Ii&&Mi(vn)),e===a&&(61===t.charCodeAt(Ti)?(e="=",Ti++):(e=a,0===Ii&&Mi(at)),e===a&&("!="===t.substr(Ti,2)?(e="!=",Ti+=2):(e=a,0===Ii&&Mi(yn)))))))),e}function Hl(){var t,e,r,n,o,s;return t=Ti,e=Ti,(r=Gp())!==a&&(n=tv())!==a&&(o=qp())!==a?e=r=[r,n,o]:(Ti=e,e=a),e!==a&&(Si=t,e=(s=e)[0]+" "+s[2]),(t=e)===a&&(t=qp()),t}function Yl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=Xl())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Wl())!==a&&(u=tv())!==a&&(i=Xl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Wl())!==a&&(u=tv())!==a&&(i=Xl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=function(t,e){if(e&&e.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Ev()}));return _v(t,e)}(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Wl(){var e;return 43===t.charCodeAt(Ti)?(e="+",Ti++):(e=a,0===Ii&&Mi(jn)),e===a&&(45===t.charCodeAt(Ti)?(e="-",Ti++):(e=a,0===Ii&&Mi(Cn))),e}function Xl(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=zl())!==a){for(r=[],n=Ti,(o=tv())!==a?((s=Zl())===a&&(s=zb()),s!==a&&(u=tv())!==a&&(i=zl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a?((s=Zl())===a&&(s=zb()),s!==a&&(u=tv())!==a&&(i=zl())!==a?n=o=[o,s,u,i]:(Ti=n,n=a)):(Ti=n,n=a);r!==a?(Si=t,t=e=_v(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function Zl(){var e;return 42===t.charCodeAt(Ti)?(e="*",Ti++):(e=a,0===Ii&&Mi(gn)),e===a&&(47===t.charCodeAt(Ti)?(e="/",Ti++):(e=a,0===Ii&&Mi(En)),e===a&&(37===t.charCodeAt(Ti)?(e="%",Ti++):(e=a,0===Ii&&Mi(An)),e===a&&("||"===t.substr(Ti,2)?(e="||",Ti+=2):(e=a,0===Ii&&Mi(Tn))))),e}function Kl(){var t,e,r,n,o;return t=Ti,(e=ef())!==a&&tv()!==a?((r=Wc())===a&&(r=null),r!==a?(Si=t,n=e,(o=r)&&(n.array_index=o),t=e=n):(Ti=t,t=a)):(Ti=t,t=a),t}function Jl(){var e,r,n,o,s,u;return(e=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=Jp())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&(s=Op())!==a&&tv()!==a&&(u=mv())!==a&&tv()!==a&&(i=Yb())!==a?(Si=e,f=o,p=u,r={type:"cast",keyword:r.toLowerCase(),expr:f,symbol:"as",target:[p]},e=r):(Ti=e,e=a);var f,p;e===a&&(e=Ti,(r=Jp())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&(s=Op())!==a&&tv()!==a&&(u=rb())!==a&&tv()!==a&&(i=Hb())!==a&&tv()!==a&&(c=Wf())!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&(l=Yb())!==a?(Si=e,r=function(t,e,r){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:"DECIMAL("+r+")"}]}}(r,o,c),e=r):(Ti=e,e=a),e===a&&(e=Ti,(r=Jp())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&(s=Op())!==a&&tv()!==a&&(u=rb())!==a&&tv()!==a&&(i=Hb())!==a&&tv()!==a&&(c=Wf())!==a&&tv()!==a&&$b()!==a&&tv()!==a&&(l=Wf())!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&Yb()!==a?(Si=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:"DECIMAL("+r+", "+n+")"}]}}(r,o,c,l),e=r):(Ti=e,e=a),e===a&&(e=Ti,(r=Jp())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&(s=Op())!==a&&tv()!==a&&(u=function(){var e;(e=function(){var e,r,n,o;e=Ti,"signed"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Bs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SIGNED"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=nb());return e}())!==a&&tv()!==a?((i=ab())===a&&(i=null),i!==a&&tv()!==a&&(c=Yb())!==a?(Si=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:r+(n?" "+n:"")}]}}(r,o,u,i),e=r):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=Hb())!==a&&tv()!==a?((n=Ql())===a&&(n=Kl())===a&&(n=Cf()),n!==a&&tv()!==a&&(o=Yb())!==a&&tv()!==a?((s=Vf())===a&&(s=null),s!==a?(Si=e,r=function(t,e){return t.parentheses=!0,e?{type:"cast",keyword:"cast",expr:t,...e}:t}(n,s),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=rf())===a&&(r=Mf())===a&&(r=function(){var e,r,n;e=Ti,(r=function(){var e,r,n,o,s,u,i,c,l;e=Ti,(r=function(){var e,r,n,o;e=Ti,"count"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Ts));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="COUNT"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Ti,"group_concat"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Ss));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="GROUP_CONCAT"):(Ti=e,e=a)):(Ti=e,e=a);return e}());r!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=function(){var e,r;e=Ti,(r=function(){var e,r;e=Ti,42===t.charCodeAt(Ti)?(r="*",Ti++):(r=a,0===Ii&&Mi(gn));r!==a&&(Si=e,r={type:"star",value:"*"});return e=r}())!==a&&(Si=e,r={expr:r});(e=r)===a&&(e=Sf());return e}())!==a&&tv()!==a&&(o=Yb())!==a&&tv()!==a?((s=Ef())===a&&(s=null),s!==a?(Si=e,e=r={type:"aggr_func",name:r,args:n,over:s}):(Ti=e,e=a)):(Ti=e,e=a);e===a&&(e=Ti,"percentile_cont"===t.substr(Ti,15).toLowerCase()?(r=t.substr(Ti,15),Ti+=15):(r=a,0===Ii&&Mi(po)),r===a&&("percentile_disc"===t.substr(Ti,15).toLowerCase()?(r=t.substr(Ti,15),Ti+=15):(r=a,0===Ii&&Mi(bo))),r!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((n=Yf())===a&&(n=qf()),n!==a&&tv()!==a&&(o=Yb())!==a&&tv()!==a?("within"===t.substr(Ti,6).toLowerCase()?(s=t.substr(Ti,6),Ti+=6):(s=a,0===Ii&&Mi(vo)),s!==a&&tv()!==a&&_p()!==a&&tv()!==a&&(u=Hb())!==a&&tv()!==a&&(i=Ol())!==a&&tv()!==a&&(c=Yb())!==a&&tv()!==a?((l=Ef())===a&&(l=null),l!==a?(Si=e,r=function(t,e,r,n){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:e},within_group_orderby:r,over:n}}(r,n,i,l),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,"mode"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(yo)),r!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Yb())!==a&&tv()!==a?("within"===t.substr(Ti,6).toLowerCase()?(o=t.substr(Ti,6),Ti+=6):(o=a,0===Ii&&Mi(vo)),o!==a&&tv()!==a&&(s=_p())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(u=Ol())!==a&&tv()!==a&&(i=Yb())!==a&&tv()!==a?((c=Ef())===a&&(c=null),c!==a?(Si=e,r=function(t,e,r){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:{}},within_group_orderby:e,over:r}}(r,u,c),e=r):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)));return e}())===a&&(r=function(){var e,r,n,o;e=Ti,(r=function(){var e;(e=function(){var e,r,n,o;e=Ti,"sum"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(xs));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SUM"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"max"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Us));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="MAX"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"min"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(_s));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="MIN"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"avg"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Is));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="AVG"):(Ti=e,e=a)):(Ti=e,e=a);return e}());return e}())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Yl())!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((o=Ef())===a&&(o=null),o!==a?(Si=e,r={type:"aggr_func",name:r,args:{expr:n},over:o,...Ev()},e=r):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(r=function(){var e,r,n,o,s,u;e=Ti,r=Ti,(n=sf())!==a&&(o=tv())!==a&&(s=Gb())!==a?r=n=[n,o,s]:(Ti=r,r=a);r===a&&(r=null);r!==a&&(n=tv())!==a?((o=function(){var e,r,n,o;e=Ti,"array_agg"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Es));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ARRAY_AGG"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(o=function(){var e,r,n,o;e=Ti,"string_agg"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(As));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="STRING_AGG"):(Ti=e,e=a)):(Ti=e,e=a);return e}()),o!==a&&(s=tv())!==a&&Hb()!==a&&tv()!==a&&(u=Sf())!==a&&tv()!==a&&Yb()!==a?(Si=e,c=o,l=u,r={type:"aggr_func",name:(i=r)?`${i[0]}.${c}`:c,args:l},e=r):(Ti=e,e=a)):(Ti=e,e=a);var i,c,l;return e}());r!==a&&tv()!==a?((n=function(){var e,r,n;e=Ti,"filter"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(eo));r!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=pl())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={keyword:"filter",parentheses:!0,where:n}):(Ti=e,e=a);return e}())===a&&(n=null),n!==a?(Si=e,r=function(t,e){return e&&(t.filter=e),t}(r,n),e=r):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(r=function(){var e;(e=function(){var e,r,n;e=Ti,(r=function(){var e;"row_number"===t.substr(Ti,10).toLowerCase()?(e=t.substr(Ti,10),Ti+=10):(e=a,0===Ii&&Mi(oo));e===a&&("dense_rank"===t.substr(Ti,10).toLowerCase()?(e=t.substr(Ti,10),Ti+=10):(e=a,0===Ii&&Mi(ao)),e===a&&("rank"===t.substr(Ti,4).toLowerCase()?(e=t.substr(Ti,4),Ti+=4):(e=a,0===Ii&&Mi(so))));return e}())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&(n=Ef())!==a?(Si=e,e=r={type:"window_func",name:r,over:n}):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s;e=Ti,(r=function(){var e;"lag"===t.substr(Ti,3).toLowerCase()?(e=t.substr(Ti,3),Ti+=3):(e=a,0===Ii&&Mi(uo));e===a&&("lead"===t.substr(Ti,4).toLowerCase()?(e=t.substr(Ti,4),Ti+=4):(e=a,0===Ii&&Mi(io)),e===a&&("nth_value"===t.substr(Ti,9).toLowerCase()?(e=t.substr(Ti,9),Ti+=9):(e=a,0===Ii&&Mi(co))));return e}())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Nl())!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((o=Af())===a&&(o=null),o!==a&&tv()!==a&&(s=Ef())!==a?(Si=e,e=r={type:"window_func",name:r,args:n,over:s,consider_nulls:o}):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,s;e=Ti,(r=function(){var e;"first_value"===t.substr(Ti,11).toLowerCase()?(e=t.substr(Ti,11),Ti+=11):(e=a,0===Ii&&Mi(ro));e===a&&("last_value"===t.substr(Ti,10).toLowerCase()?(e=t.substr(Ti,10),Ti+=10):(e=a,0===Ii&&Mi(no)));return e}())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Pl())!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((o=Af())===a&&(o=null),o!==a&&tv()!==a&&(s=Ef())!==a?(Si=e,e=r={type:"window_func",name:r,args:{type:"expr_list",value:[n]},over:s,consider_nulls:o}):(Ti=e,e=a)):(Ti=e,e=a);return e}());return e}())===a&&(r=Nf())===a&&(r=function(){var t,e,r,n,o,s,u,i;return t=Ti,Wp()!==a&&tv()!==a&&(e=kl())!==a&&tv()!==a?((r=Ml())===a&&(r=null),r!==a&&tv()!==a&&(n=Kp())!==a&&tv()!==a?((o=Wp())===a&&(o=null),o!==a?(Si=t,u=e,(i=r)&&u.push(i),t={type:"case",expr:null,args:u}):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,Wp()!==a&&tv()!==a&&(e=Pl())!==a&&tv()!==a&&(r=kl())!==a&&tv()!==a?((n=Ml())===a&&(n=null),n!==a&&tv()!==a&&(o=Kp())!==a&&tv()!==a?((s=Wp())===a&&(s=null),s!==a?(Si=t,t=function(t,e,r){return r&&e.push(r),{type:"case",expr:t,args:e}}(e,r,n)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)),t}())===a&&(r=Rl())===a&&(r=Kl())===a&&(r=Cf()),r!==a&&tv()!==a?((n=Vf())===a&&(n=null),n!==a?(Si=e,r=function(t,e){return e?{type:"cast",keyword:"cast",expr:t,...e}:t}(r,n),e=r):(Ti=e,e=a)):(Ti=e,e=a))))));return e}())===a&&(e=Ti,Hb()!==a&&(r=tv())!==a&&(n=Dl())!==a&&(o=tv())!==a&&(s=Yb())!==a?(Si=e,(u=n).parentheses=!0,e=u):(Ti=e,e=a),e===a&&(e=dv())===a&&(e=Ti,tv()!==a?(36===t.charCodeAt(Ti)?(r="$",Ti++):(r=a,0===Ii&&Mi(Sn)),r!==a?(60===t.charCodeAt(Ti)?(n="<",Ti++):(n=a,0===Ii&&Mi(vn)),n!==a&&(o=Yf())!==a?(62===t.charCodeAt(Ti)?(s=">",Ti++):(s=a,0===Ii&&Mi(fn)),s!==a?(Si=e,e={type:"origin",value:`$<${o.value}>`}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a))),e}function zl(){var e,r,n,o,s;return(e=function(){var e,r,n,o,s,u,i,c;if(e=Ti,(r=Jl())!==a)if(tv()!==a){for(n=[],o=Ti,(s=tv())!==a?("?|"===t.substr(Ti,2)?(u="?|",Ti+=2):(u=a,0===Ii&&Mi(Un)),u===a&&("?&"===t.substr(Ti,2)?(u="?&",Ti+=2):(u=a,0===Ii&&Mi(_n)),u===a&&(63===t.charCodeAt(Ti)?(u="?",Ti++):(u=a,0===Ii&&Mi(xn)),u===a&&("#-"===t.substr(Ti,2)?(u="#-",Ti+=2):(u=a,0===Ii&&Mi(In)),u===a&&("#>>"===t.substr(Ti,3)?(u="#>>",Ti+=3):(u=a,0===Ii&&Mi(Nn)),u===a&&("#>"===t.substr(Ti,2)?(u="#>",Ti+=2):(u=a,0===Ii&&Mi(Rn)),u===a&&(u=Jb())===a&&(u=Kb())===a&&("@>"===t.substr(Ti,2)?(u="@>",Ti+=2):(u=a,0===Ii&&Mi(kn)),u===a&&("<@"===t.substr(Ti,2)?(u="<@",Ti+=2):(u=a,0===Ii&&Mi(Vn))))))))),u!==a&&(i=tv())!==a&&(c=Jl())!==a?o=s=[s,u,i,c]:(Ti=o,o=a)):(Ti=o,o=a);o!==a;)n.push(o),o=Ti,(s=tv())!==a?("?|"===t.substr(Ti,2)?(u="?|",Ti+=2):(u=a,0===Ii&&Mi(Un)),u===a&&("?&"===t.substr(Ti,2)?(u="?&",Ti+=2):(u=a,0===Ii&&Mi(_n)),u===a&&(63===t.charCodeAt(Ti)?(u="?",Ti++):(u=a,0===Ii&&Mi(xn)),u===a&&("#-"===t.substr(Ti,2)?(u="#-",Ti+=2):(u=a,0===Ii&&Mi(In)),u===a&&("#>>"===t.substr(Ti,3)?(u="#>>",Ti+=3):(u=a,0===Ii&&Mi(Nn)),u===a&&("#>"===t.substr(Ti,2)?(u="#>",Ti+=2):(u=a,0===Ii&&Mi(Rn)),u===a&&(u=Jb())===a&&(u=Kb())===a&&("@>"===t.substr(Ti,2)?(u="@>",Ti+=2):(u=a,0===Ii&&Mi(kn)),u===a&&("<@"===t.substr(Ti,2)?(u="<@",Ti+=2):(u=a,0===Ii&&Mi(Vn))))))))),u!==a&&(i=tv())!==a&&(c=Jl())!==a?o=s=[s,u,i,c]:(Ti=o,o=a)):(Ti=o,o=a);n!==a?(Si=e,l=r,r=(f=n)&&0!==f.length?_v(l,f):l,e=r):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;var l,f;return e}())===a&&(e=Ti,(r=function(){var e;33===t.charCodeAt(Ti)?(e="!",Ti++):(e=a,0===Ii&&Mi(cn));e===a&&(45===t.charCodeAt(Ti)?(e="-",Ti++):(e=a,0===Ii&&Mi(Cn)),e===a&&(43===t.charCodeAt(Ti)?(e="+",Ti++):(e=a,0===Ii&&Mi(jn)),e===a&&(126===t.charCodeAt(Ti)?(e="~",Ti++):(e=a,0===Ii&&Mi(wn)))));return e}())!==a?(n=Ti,(o=tv())!==a&&(s=zl())!==a?n=o=[o,s]:(Ti=n,n=a),n!==a?(Si=e,e=r=Av(r,n[1])):(Ti=e,e=a)):(Ti=e,e=a)),e}function tf(){var e,r,n,o,s,u;if(e=Ti,"e"===t.substr(Ti,1).toLowerCase()?(r=t.charAt(Ti),Ti++):(r=a,0===Ii&&Mi(Mn)),r!==a)if(39===t.charCodeAt(Ti)?(n="'",Ti++):(n=a,0===Ii&&Mi(Ue)),n!==a)if(tv()!==a){for(o=[],s=Bf();s!==a;)o.push(s),s=Bf();o!==a&&(s=tv())!==a?(39===t.charCodeAt(Ti)?(u="'",Ti++):(u=a,0===Ii&&Mi(Ue)),u!==a?(Si=e,e=r={type:"origin",value:`E'${o.join("")}'`}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;return e}function ef(){var t,e,r,n,o,s,u,i,c,l,f,p,b;return(t=tf())===a&&(t=Ti,e=Ti,(r=sf())!==a&&(n=tv())!==a&&(o=Gb())!==a?e=r=[r,n,o]:(Ti=e,e=a),e===a&&(e=null),e!==a&&(r=tv())!==a&&(n=Bb())!==a?(Si=t,t=e=function(t){const e=t&&t[0]||null;return Vv.add(`select::${e}::(.*)`),{type:"column_ref",table:e,column:"*"}}(e)):(Ti=t,t=a),t===a&&(t=Ti,(e=sf())!==a?(r=Ti,(n=tv())!==a&&(o=Gb())!==a&&(s=tv())!==a&&(u=sf())!==a?r=n=[n,o,s,u]:(Ti=r,r=a),r!==a?(n=Ti,(o=tv())!==a&&(s=Gb())!==a&&(u=tv())!==a&&(i=df())!==a?n=o=[o,s,u,i]:(Ti=n,n=a),n!==a?(o=Ti,(s=tv())!==a&&(u=sc())!==a?o=s=[s,u]:(Ti=o,o=a),o===a&&(o=null),o!==a?(Si=t,l=e,f=r,p=n,b=o,Vv.add(`select::${l}.${f[3]}::${p[3].value}`),t=e={type:"column_ref",schema:l,table:f[3],column:{expr:p[3]},collate:b&&b[1]}):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=sf())!==a&&(r=tv())!==a&&(n=Gb())!==a&&(o=tv())!==a&&(s=df())!==a?(u=Ti,(i=tv())!==a&&(c=sc())!==a?u=i=[i,c]:(Ti=u,u=a),u===a&&(u=null),u!==a?(Si=t,t=e=function(t,e,r){return Vv.add(`select::${t}::${e.value}`),{type:"column_ref",table:t,column:{expr:e},collate:r&&r[1]}}(e,s,u)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=df())!==a?(r=Ti,Ii++,n=Hb(),Ii--,n===a?r=void 0:(Ti=r,r=a),r!==a?(n=Ti,(o=tv())!==a&&(s=sc())!==a?n=o=[o,s]:(Ti=n,n=a),n===a&&(n=null),n!==a?(Si=t,t=e=function(t,e){return Vv.add("select::null::"+t.value),{type:"column_ref",table:null,column:{expr:t},collate:e&&e[1]}}(e,n)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a))))),t}function rf(){var t,e,r;return t=Ti,(e=Gf())!==a&&(Si=t,r=e,Vv.add("select::null::"+r.value),e={type:"column_ref",table:null,column:{expr:r}}),t=e}function nf(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=df())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=df())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=df())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function of(){var t,e;return t=Ti,(e=wf())!==a&&(Si=t,e=qn(e)),(t=e)===a&&(t=lf()),t}function af(){var t,e;return t=Ti,(e=wf())!==a?(Si=Ti,(Pn(e)?a:void 0)!==a?(Si=t,t=e={type:"default",value:e}):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=lf()),t}function sf(){var t,e;return t=Ti,(e=wf())!==a?(Si=Ti,(Pn(e)?a:void 0)!==a?(Si=t,t=e=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=ff()),t}function uf(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=sf())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=sf())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=sf())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function cf(){var t,e,r,n,o,s,u,i,c;return t=Ti,(e=mf())!==a?(Si=Ti,(!0===gv[e.toUpperCase()]?a:void 0)!==a?(r=Ti,(n=tv())!==a&&(o=Hb())!==a&&(s=tv())!==a&&(u=nf())!==a&&(i=tv())!==a&&(c=Yb())!==a?r=n=[n,o,s,u,i,c]:(Ti=r,r=a),r===a&&(r=null),r!==a?(Si=t,t=e=function(t,e){return e?`${t}(${e[3].map(t=>t.value).join(", ")})`:t}(e,r)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=pf())!==a&&(Si=t,e=function(t){return t.value}(e)),t=e),t}function lf(){var t;return(t=pf())===a&&(t=bf())===a&&(t=vf()),t}function ff(){var t,e;return t=Ti,(e=pf())===a&&(e=bf())===a&&(e=vf()),e!==a&&(Si=t,e=e.value),t=e}function pf(){var e,r,n,o;if(e=Ti,34===t.charCodeAt(Ti)?(r='"',Ti++):(r=a,0===Ii&&Mi(Pr)),r!==a){if(n=[],Dn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Qn)),o!==a)for(;o!==a;)n.push(o),Dn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Qn));else n=a;n!==a?(34===t.charCodeAt(Ti)?(o='"',Ti++):(o=a,0===Ii&&Mi(Pr)),o!==a?(Si=e,e=r={type:"double_quote_string",value:n.join("")}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;return e}function bf(){var e,r,n,o;if(e=Ti,39===t.charCodeAt(Ti)?(r="'",Ti++):(r=a,0===Ii&&Mi(Ue)),r!==a){if(n=[],Fn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Gn)),o!==a)for(;o!==a;)n.push(o),Fn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Gn));else n=a;n!==a?(39===t.charCodeAt(Ti)?(o="'",Ti++):(o=a,0===Ii&&Mi(Ue)),o!==a?(Si=e,e=r={type:"single_quote_string",value:n.join("")}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;return e}function vf(){var e,r,n,o;if(e=Ti,96===t.charCodeAt(Ti)?(r="`",Ti++):(r=a,0===Ii&&Mi($n)),r!==a){if(n=[],Bn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Hn)),o!==a)for(;o!==a;)n.push(o),Bn.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Hn));else n=a;n!==a?(96===t.charCodeAt(Ti)?(o="`",Ti++):(o=a,0===Ii&&Mi($n)),o!==a?(Si=e,e=r={type:"backticks_quote_string",value:n.join("")}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;return e}function yf(){var t,e;return t=Ti,(e=mf())!==a&&(Si=t,e=qn(e)),(t=e)===a&&(t=lf()),t}function df(){var t,e;return t=Ti,(e=mf())!==a?(Si=Ti,(Pn(e)?a:void 0)!==a?(Si=t,t=e={type:"default",value:e}):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=lf()),t}function hf(){var t,e;return t=Ti,(e=mf())!==a?(Si=Ti,(Pn(e)?a:void 0)!==a?(Si=t,t=e=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=ff()),t}function mf(){var t,e,r,n;if(t=Ti,(e=Lf())!==a){for(r=[],n=jf();n!==a;)r.push(n),n=jf();r!==a?(Si=t,t=e=e+r.join("")):(Ti=t,t=a)}else Ti=t,t=a;return t}function wf(){var t,e,r,n;if(t=Ti,(e=Lf())!==a){for(r=[],n=Of();n!==a;)r.push(n),n=Of();r!==a?(Si=t,t=e=e+r.join("")):(Ti=t,t=a)}else Ti=t,t=a;return t}function Lf(){var e;return Yn.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(Wn)),e}function Of(){var e;return Xn.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(Zn)),e}function jf(){var e;return Kn.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(Jn)),e}function Cf(){var e,r,n,o;return e=Ti,r=Ti,58===t.charCodeAt(Ti)?(n=":",Ti++):(n=a,0===Ii&&Mi(zn)),n!==a&&(o=wf())!==a?r=n=[n,o]:(Ti=r,r=a),r!==a&&(Si=e,r={type:"param",value:r[1]}),e=r}function gf(){var t,e,r;return t=Ti,gp()!==a&&tv()!==a&&up()!==a&&tv()!==a&&(e=Ab())!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((r=Nl())===a&&(r=null),r!==a&&tv()!==a&&Yb()!==a?(Si=t,t={type:"on update",keyword:e,parentheses:!0,expr:r}):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,gp()!==a&&tv()!==a&&up()!==a&&tv()!==a&&(e=Ab())!==a?(Si=t,t=function(t){return{type:"on update",keyword:t}}(e)):(Ti=t,t=a)),t}function Ef(){var e,r,n,o,s;return e=Ti,"over"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(to)),r!==a&&tv()!==a&&(n=yl())!==a?(Si=e,e=r={type:"window",as_window_specification:n}):(Ti=e,e=a),e===a&&(e=Ti,"over"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(to)),r!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a?((o=Ll())===a&&(o=null),o!==a&&tv()!==a?((s=Ol())===a&&(s=null),s!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={partitionby:o,orderby:s}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=gf())),e}function Af(){var e,r,n;return e=Ti,"ignore"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(lo)),r===a&&("respect"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(fo))),r!==a&&tv()!==a?("nulls"===t.substr(Ti,5).toLowerCase()?(n=t.substr(Ti,5),Ti+=5):(n=a,0===Ii&&Mi(Lt)),n!==a?(Si=e,e=r=r.toUpperCase()+" NULLS"):(Ti=e,e=a)):(Ti=e,e=a),e}function Tf(){var t,e;return t=Ti,$b()!==a&&tv()!==a&&(e=Ff())!==a?(Si=t,t={symbol:ke,delimiter:e}):(Ti=t,t=a),t}function Sf(){var t,e,r,n,o,s,u,i,c,l,f;if(t=Ti,(e=Vp())===a&&(e=null),e!==a)if(tv()!==a)if((r=Hb())!==a)if(tv()!==a)if((n=Pl())!==a)if(tv()!==a)if((o=Yb())!==a)if(tv()!==a){for(s=[],u=Ti,(i=tv())!==a?((c=$p())===a&&(c=Bp()),c!==a&&(l=tv())!==a&&(f=Pl())!==a?u=i=[i,c,l,f]:(Ti=u,u=a)):(Ti=u,u=a);u!==a;)s.push(u),u=Ti,(i=tv())!==a?((c=$p())===a&&(c=Bp()),c!==a&&(l=tv())!==a&&(f=Pl())!==a?u=i=[i,c,l,f]:(Ti=u,u=a)):(Ti=u,u=a);s!==a&&(u=tv())!==a?((i=Tf())===a&&(i=null),i!==a&&(c=tv())!==a?((l=Ol())===a&&(l=null),l!==a?(Si=t,t=e=function(t,e,r,n,o){const a=r.length;let s=e;s.parentheses=!0;for(let t=0;t<a;++t)s=Tv(r[t][1],s,r[t][3]);return{distinct:t,expr:s,orderby:o,separator:n}}(e,n,s,i,l)):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)}else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;else Ti=t,t=a;return t===a&&(t=Ti,(e=Vp())===a&&(e=null),e!==a&&tv()!==a&&(r=ll())!==a&&tv()!==a?((n=Tf())===a&&(n=null),n!==a&&tv()!==a?((o=Ol())===a&&(o=null),o!==a?(Si=t,t=e={distinct:e,expr:r,orderby:o,separator:n}):(Ti=t,t=a)):(Ti=t,t=a)):(Ti=t,t=a)),t}function Uf(){var e,r,n;return e=Ti,(r=function(){var e;return"both"===t.substr(Ti,4).toLowerCase()?(e=t.substr(Ti,4),Ti+=4):(e=a,0===Ii&&Mi(ho)),e===a&&("leading"===t.substr(Ti,7).toLowerCase()?(e=t.substr(Ti,7),Ti+=7):(e=a,0===Ii&&Mi(mo)),e===a&&("trailing"===t.substr(Ti,8).toLowerCase()?(e=t.substr(Ti,8),Ti+=8):(e=a,0===Ii&&Mi(wo)))),e}())===a&&(r=null),r!==a&&tv()!==a?((n=Pl())===a&&(n=null),n!==a&&tv()!==a&&wp()!==a?(Si=e,e=r=function(t,e,r){let n=[];return t&&n.push({type:"origin",value:t}),e&&n.push(e),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(r,n)):(Ti=e,e=a)):(Ti=e,e=a),e}function _f(){var e,r,n,o;return e=Ti,"trim"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Lo)),r!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((n=Uf())===a&&(n=null),n!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r=function(t,e){let r=t||{type:"expr_list",value:[]};return r.value.push(e),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:r,...Ev()}}(n,o)):(Ti=e,e=a)):(Ti=e,e=a),e}function xf(){var e,r,n,o,s;return e=Ti,"crosstab"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Oo)),r!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=Nl())!==a&&tv()!==a&&Yb()!==a&&tv()!==a&&Op()!==a&&tv()!==a&&(o=wf())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(s=Xi())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={type:"tablefunc",name:{name:[{type:"default",value:"crosstab"}]},args:n,as:{type:"function",name:{name:[{type:"default",value:o}]},args:{type:"expr_list",value:s.map(t=>({...t,type:"column_definition"}))},...Ev()},...Ev()}):(Ti=e,e=a),e}function If(){var e,r,n;return e=Ti,"position"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(jo)),r!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(n=function(){var t,e,r,n,o,s,u,i;return t=Ti,(e=Ff())!==a&&tv()!==a&&qp()!==a&&tv()!==a&&(r=Pl())!==a?(n=Ti,(o=tv())!==a&&(s=wp())!==a&&(u=tv())!==a&&(i=Yf())!==a?n=o=[o,s,u,i]:(Ti=n,n=a),n===a&&(n=null),n!==a?(Si=t,t=e=function(t,e,r){let n=[t,{type:"origin",value:"in"},e];return r&&(n.push({type:"origin",value:"from"}),n.push(r[3])),{type:"expr_list",value:n}}(e,r,n)):(Ti=t,t=a)):(Ti=t,t=a),t}())!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r={type:"function",name:{name:[{type:"origin",value:"position"}]},separator:" ",args:n,...Ev()}):(Ti=e,e=a),e}function Nf(){var e,r,n,o,s,u,i,c,l,f;return(e=_f())===a&&(e=xf())===a&&(e=If())===a&&(e=Ti,"now"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Co)),r!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a?((o=Nl())===a&&(o=null),o!==a&&tv()!==a&&Yb()!==a&&tv()!==a?("at"===t.substr(Ti,2).toLowerCase()?(s=t.substr(Ti,2),Ti+=2):(s=a,0===Ii&&Mi(go)),s!==a&&tv()!==a&&jb()!==a&&tv()!==a?("zone"===t.substr(Ti,4).toLowerCase()?(u=t.substr(Ti,4),Ti+=4):(u=a,0===Ii&&Mi(Eo)),u!==a&&tv()!==a&&(i=Ff())!==a?(Si=e,c=r,l=o,(f=i).prefix="at time zone",e=r={type:"function",name:{name:[{type:"default",value:c}]},args:l||{type:"expr_list",value:[]},suffix:f,...Ev()}):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=function(){var e;(e=kf())===a&&(e=Tb())===a&&(e=function(){var e,r,n,o;e=Ti,"user"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(du));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="USER"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=Sb())===a&&(e=function(){var e,r,n,o;e=Ti,"system_user"===t.substr(Ti,11).toLowerCase()?(r=t.substr(Ti,11),Ti+=11):(r=a,0===Ii&&Mi(Ru));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SYSTEM_USER"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&("ntile"===t.substr(Ti,5).toLowerCase()?(e=t.substr(Ti,5),Ti+=5):(e=a,0===Ii&&Mi(Wo)));return e}())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a?((o=Nl())===a&&(o=null),o!==a&&tv()!==a&&Yb()!==a&&tv()!==a?((s=Ef())===a&&(s=null),s!==a?(Si=e,e=r=function(t,e,r){return{type:"function",name:{name:[{type:"origin",value:t}]},args:e||{type:"expr_list",value:[]},over:r,...Ev()}}(r,o,s)):(Ti=e,e=a)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=function(){var t,e,r,n,o;t=Ti,(e=Yp())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(r=Rf())!==a&&tv()!==a&&wp()!==a&&tv()!==a?((n=Cb())===a&&(n=Eb())===a&&(n=jb())===a&&(n=wb()),n===a&&(n=null),n!==a&&tv()!==a&&(o=Pl())!==a&&tv()!==a&&Yb()!==a?(Si=t,s=r,u=n,i=o,e={type:e.toLowerCase(),args:{field:s,cast_type:u,source:i},...Ev()},t=e):(Ti=t,t=a)):(Ti=t,t=a);var s,u,i;t===a&&(t=Ti,(e=Yp())!==a&&tv()!==a&&Hb()!==a&&tv()!==a&&(r=Rf())!==a&&tv()!==a&&wp()!==a&&tv()!==a&&(n=Pl())!==a&&tv()!==a&&(o=Yb())!==a?(Si=t,e=function(t,e,r){return{type:t.toLowerCase(),args:{field:e,source:r},...Ev()}}(e,r,n),t=e):(Ti=t,t=a));return t}())===a&&(e=Ti,(r=kf())!==a&&tv()!==a?((n=gf())===a&&(n=null),n!==a?(Si=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},over:n,...Ev()}):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=bv())!==a&&tv()!==a&&(n=Hb())!==a&&tv()!==a?((o=Dl())===a&&(o=null),o!==a&&tv()!==a&&Yb()!==a?(Si=e,e=r=function(t,e){return e&&"expr_list"!==e.type&&(e={type:"expr_list",value:[e]}),{type:"function",name:t,args:e||{type:"expr_list",value:[]},...Ev()}}(r,o)):(Ti=e,e=a)):(Ti=e,e=a))))),e}function Rf(){var e,r;return e=Ti,"century"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Ao)),r===a&&("day"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(To)),r===a&&("date"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(So)),r===a&&("decade"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Uo)),r===a&&("dow"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(_o)),r===a&&("doy"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(xo)),r===a&&("epoch"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Io)),r===a&&("hour"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(No)),r===a&&("isodow"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ro)),r===a&&("isoyear"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(ko)),r===a&&("microseconds"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Vo)),r===a&&("millennium"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Mo)),r===a&&("milliseconds"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(qo)),r===a&&("minute"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Po)),r===a&&("month"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Do)),r===a&&("quarter"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Qo)),r===a&&("second"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Fo)),r===a&&("timezone"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Go)),r===a&&("timezone_hour"===t.substr(Ti,13).toLowerCase()?(r=t.substr(Ti,13),Ti+=13):(r=a,0===Ii&&Mi($o)),r===a&&("timezone_minute"===t.substr(Ti,15).toLowerCase()?(r=t.substr(Ti,15),Ti+=15):(r=a,0===Ii&&Mi(Bo)),r===a&&("week"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ho)),r===a&&("year"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Yo))))))))))))))))))))))),r!==a&&(Si=e,r=r),e=r}function kf(){var e;return(e=function(){var e,r,n,o;e=Ti,"current_date"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(_u));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CURRENT_DATE"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ti,"current_time"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Iu));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CURRENT_TIME"):(Ti=e,e=a)):(Ti=e,e=a);return e}())===a&&(e=Ab()),e}function Vf(){var t,e,r,n,o,s;if(t=Ti,e=[],r=Ti,(n=Ib())!==a&&(o=tv())!==a&&(s=mv())!==a?r=n=[n,o,s]:(Ti=r,r=a),r!==a)for(;r!==a;)e.push(r),r=Ti,(n=Ib())!==a&&(o=tv())!==a&&(s=mv())!==a?r=n=[n,o,s]:(Ti=r,r=a);else e=a;return e!==a&&(r=tv())!==a?((n=Jc())===a&&(n=null),n!==a?(Si=t,t=e={as:n,symbol:"::",target:e.map(t=>t[2])}):(Ti=t,t=a)):(Ti=t,t=a),t}function Mf(){var e;return(e=Ff())===a&&(e=Yf())===a&&(e=Qf())===a&&(e=Pf())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=jb())===a&&(r=wb())===a&&(r=Cb())===a&&(r=Lb());if(r!==a)if(tv()!==a){if(n=Ti,39===t.charCodeAt(Ti)?(o="'",Ti++):(o=a,0===Ii&&Mi(Ue)),o!==a){for(s=[],u=Bf();u!==a;)s.push(u),u=Bf();s!==a?(39===t.charCodeAt(Ti)?(u="'",Ti++):(u=a,0===Ii&&Mi(Ue)),u!==a?n=o=[o,s,u]:(Ti=n,n=a)):(Ti=n,n=a)}else Ti=n,n=a;n!==a?(Si=e,i=n,r={type:r.toLowerCase(),value:i[1].join("")},e=r):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;var i;if(e===a)if(e=Ti,(r=jb())===a&&(r=wb())===a&&(r=Cb())===a&&(r=Lb()),r!==a)if(tv()!==a){if(n=Ti,34===t.charCodeAt(Ti)?(o='"',Ti++):(o=a,0===Ii&&Mi(Pr)),o!==a){for(s=[],u=$f();u!==a;)s.push(u),u=$f();s!==a?(34===t.charCodeAt(Ti)?(u='"',Ti++):(u=a,0===Ii&&Mi(Pr)),u!==a?n=o=[o,s,u]:(Ti=n,n=a)):(Ti=n,n=a)}else Ti=n,n=a;n!==a?(Si=e,r=function(t,e){return{type:t.toLowerCase(),value:e[1].join("")}}(r,n),e=r):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;return e}())===a&&(e=qf()),e}function qf(){var t,e;return t=Ti,Hp()!==a&&tv()!==a&&Wb()!==a&&tv()!==a?((e=Nl())===a&&(e=null),e!==a&&tv()!==a&&Xb()!==a?(Si=t,t={expr_list:e||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}):(Ti=t,t=a)):(Ti=t,t=a),t}function Pf(){var t,e;return t=Ti,(e=tp())!==a&&(Si=t,e={type:"null",value:null}),t=e}function Df(){var e,r;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"not null"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ga));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r={type:"not null",value:"not null"}),e=r}function Qf(){var e,r;return e=Ti,(r=function(){var e,r,n,o;e=Ti,"true"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ea));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r={type:"bool",value:!0}),(e=r)===a&&(e=Ti,(r=function(){var e,r,n,o;e=Ti,"false"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Aa));r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a);return e}())!==a&&(Si=e,r={type:"bool",value:!1}),e=r),e}function Ff(){var e,r,n,o,s,u,i,c,l;if(e=Ti,r=Ti,39===t.charCodeAt(Ti)?(n="'",Ti++):(n=a,0===Ii&&Mi(Ue)),n!==a){for(o=[],s=Bf();s!==a;)o.push(s),s=Bf();o!==a?(39===t.charCodeAt(Ti)?(s="'",Ti++):(s=a,0===Ii&&Mi(Ue)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a)}else Ti=r,r=a;if(r!==a){if(n=[],Xo.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Zo)),o!==a)for(;o!==a;)n.push(o),Xo.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(Zo));else n=a;if(n!==a)if((o=tv())!==a){if(s=Ti,39===t.charCodeAt(Ti)?(u="'",Ti++):(u=a,0===Ii&&Mi(Ue)),u!==a){for(i=[],c=Bf();c!==a;)i.push(c),c=Bf();i!==a?(39===t.charCodeAt(Ti)?(c="'",Ti++):(c=a,0===Ii&&Mi(Ue)),c!==a?s=u=[u,i,c]:(Ti=s,s=a)):(Ti=s,s=a)}else Ti=s,s=a;s!==a?(Si=e,l=s,e=r={type:"single_quote_string",value:`${r[1].join("")}${l[1].join("")}`}):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a}else Ti=e,e=a;if(e===a){if(e=Ti,r=Ti,39===t.charCodeAt(Ti)?(n="'",Ti++):(n=a,0===Ii&&Mi(Ue)),n!==a){for(o=[],s=Bf();s!==a;)o.push(s),s=Bf();o!==a?(39===t.charCodeAt(Ti)?(s="'",Ti++):(s=a,0===Ii&&Mi(Ue)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a)}else Ti=r,r=a;r!==a&&(Si=e,r=function(t){return{type:"single_quote_string",value:t[1].join("")}}(r)),(e=r)===a&&(e=Gf())}return e}function Gf(){var e,r,n,o,s;if(e=Ti,r=Ti,34===t.charCodeAt(Ti)?(n='"',Ti++):(n=a,0===Ii&&Mi(Pr)),n!==a){for(o=[],s=$f();s!==a;)o.push(s),s=$f();o!==a?(34===t.charCodeAt(Ti)?(s='"',Ti++):(s=a,0===Ii&&Mi(Pr)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a)}else Ti=r,r=a;return r!==a?(n=Ti,Ii++,o=Gb(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r={type:"double_quote_string",value:r[1].join("")}):(Ti=e,e=a)):(Ti=e,e=a),e}function $f(){var e;return Ko.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(Jo)),e===a&&(e=Hf()),e}function Bf(){var e;return zo.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(ta)),e===a&&(e=Hf()),e}function Hf(){var e,r,n,o,s,u,i,c,l,f;return e=Ti,"\\'"===t.substr(Ti,2)?(r="\\'",Ti+=2):(r=a,0===Ii&&Mi(ea)),r!==a&&(Si=e,r="\\'"),(e=r)===a&&(e=Ti,'\\"'===t.substr(Ti,2)?(r='\\"',Ti+=2):(r=a,0===Ii&&Mi(ra)),r!==a&&(Si=e,r='\\"'),(e=r)===a&&(e=Ti,"\\\\"===t.substr(Ti,2)?(r="\\\\",Ti+=2):(r=a,0===Ii&&Mi(na)),r!==a&&(Si=e,r="\\\\"),(e=r)===a&&(e=Ti,"\\/"===t.substr(Ti,2)?(r="\\/",Ti+=2):(r=a,0===Ii&&Mi(oa)),r!==a&&(Si=e,r="\\/"),(e=r)===a&&(e=Ti,"\\b"===t.substr(Ti,2)?(r="\\b",Ti+=2):(r=a,0===Ii&&Mi(aa)),r!==a&&(Si=e,r="\b"),(e=r)===a&&(e=Ti,"\\f"===t.substr(Ti,2)?(r="\\f",Ti+=2):(r=a,0===Ii&&Mi(sa)),r!==a&&(Si=e,r="\f"),(e=r)===a&&(e=Ti,"\\n"===t.substr(Ti,2)?(r="\\n",Ti+=2):(r=a,0===Ii&&Mi(ua)),r!==a&&(Si=e,r="\n"),(e=r)===a&&(e=Ti,"\\r"===t.substr(Ti,2)?(r="\\r",Ti+=2):(r=a,0===Ii&&Mi(ia)),r!==a&&(Si=e,r="\r"),(e=r)===a&&(e=Ti,"\\t"===t.substr(Ti,2)?(r="\\t",Ti+=2):(r=a,0===Ii&&Mi(ca)),r!==a&&(Si=e,r="\t"),(e=r)===a&&(e=Ti,"\\u"===t.substr(Ti,2)?(r="\\u",Ti+=2):(r=a,0===Ii&&Mi(la)),r!==a&&(n=zf())!==a&&(o=zf())!==a&&(s=zf())!==a&&(u=zf())!==a?(Si=e,i=n,c=o,l=s,f=u,e=r=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Ti=e,e=a),e===a&&(e=Ti,92===t.charCodeAt(Ti)?(r="\\",Ti++):(r=a,0===Ii&&Mi(fa)),r!==a&&(Si=e,r="\\"),(e=r)===a&&(e=Ti,"''"===t.substr(Ti,2)?(r="''",Ti+=2):(r=a,0===Ii&&Mi(pa)),r!==a&&(Si=e,r="''"),e=r))))))))))),e}function Yf(){var t,e,r;return t=Ti,(e=function(){var t,e,r,n;t=Ti,(e=Wf())===a&&(e=null);e!==a&&(r=Xf())!==a&&(n=Zf())!==a?(Si=t,t=e={type:"bigint",value:(e||"")+r+n}):(Ti=t,t=a);t===a&&(t=Ti,(e=Wf())===a&&(e=null),e!==a&&(r=Xf())!==a?(Si=t,e=function(t,e){const r=(t||"")+e;return t&&Sv(t)?{type:"bigint",value:r}:parseFloat(r)}(e,r),t=e):(Ti=t,t=a),t===a&&(t=Ti,(e=Wf())!==a&&(r=Zf())!==a?(Si=t,e=function(t,e){return{type:"bigint",value:t+e}}(e,r),t=e):(Ti=t,t=a),t===a&&(t=Ti,(e=Wf())!==a&&(Si=t,e=function(t){return Sv(t)?{type:"bigint",value:t}:parseFloat(t)}(e)),t=e)));return t}())!==a&&(Si=t,e=(r=e)&&"bigint"===r.type?r:{type:"number",value:r}),t=e}function Wf(){var e,r,n;return(e=Kf())===a&&(e=Jf())===a&&(e=Ti,45===t.charCodeAt(Ti)?(r="-",Ti++):(r=a,0===Ii&&Mi(Cn)),r===a&&(43===t.charCodeAt(Ti)?(r="+",Ti++):(r=a,0===Ii&&Mi(jn))),r!==a&&(n=Kf())!==a?(Si=e,e=r=r+n):(Ti=e,e=a),e===a&&(e=Ti,45===t.charCodeAt(Ti)?(r="-",Ti++):(r=a,0===Ii&&Mi(Cn)),r===a&&(43===t.charCodeAt(Ti)?(r="+",Ti++):(r=a,0===Ii&&Mi(jn))),r!==a&&(n=Jf())!==a?(Si=e,e=r=function(t,e){return t+e}(r,n)):(Ti=e,e=a))),e}function Xf(){var e,r,n;return e=Ti,46===t.charCodeAt(Ti)?(r=".",Ti++):(r=a,0===Ii&&Mi(ya)),r!==a&&(n=Kf())!==a?(Si=e,e=r="."+n):(Ti=e,e=a),e}function Zf(){var e,r,n;return e=Ti,(r=function(){var e,r,n;e=Ti,La.test(t.charAt(Ti))?(r=t.charAt(Ti),Ti++):(r=a,0===Ii&&Mi(Oa));r!==a?(ja.test(t.charAt(Ti))?(n=t.charAt(Ti),Ti++):(n=a,0===Ii&&Mi(Ca)),n===a&&(n=null),n!==a?(Si=e,e=r=r+(null!==(o=n)?o:"")):(Ti=e,e=a)):(Ti=e,e=a);var o;return e}())!==a&&(n=Kf())!==a?(Si=e,e=r=r+n):(Ti=e,e=a),e}function Kf(){var t,e,r;if(t=Ti,e=[],(r=Jf())!==a)for(;r!==a;)e.push(r),r=Jf();else e=a;return e!==a&&(Si=t,e=e.join("")),t=e}function Jf(){var e;return da.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(ha)),e}function zf(){var e;return ma.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(wa)),e}function tp(){var e,r,n,o;return e=Ti,"null"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(D)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function ep(){var e,r,n,o;return e=Ti,"default"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(_t)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function rp(){var e,r,n,o;return e=Ti,"to"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(ot)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function np(){var e,r,n,o;return e=Ti,"show"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ta)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function op(){var e,r,n,o;return e=Ti,"drop"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Sa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DROP"):(Ti=e,e=a)):(Ti=e,e=a),e}function ap(){var e,r,n,o;return e=Ti,"alter"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(_a)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function sp(){var e,r,n,o;return e=Ti,"select"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(xa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function up(){var e,r,n,o;return e=Ti,"update"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ia)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function ip(){var e,r,n,o;return e=Ti,"create"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Na)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function cp(){var e,r,n,o;return e=Ti,"temporary"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Ra)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function lp(){var e,r,n,o;return e=Ti,"temp"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(ka)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function fp(){var e,r,n,o;return e=Ti,"delete"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Va)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function pp(){var e,r,n,o;return e=Ti,"insert"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ma)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function bp(){var e,r,n,o;return e=Ti,"recursive"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(qa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="RECURSIVE"):(Ti=e,e=a)):(Ti=e,e=a),e}function vp(){var e,r,n,o;return e=Ti,"replace"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Pa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function yp(){var e,r,n,o;return e=Ti,"rename"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Qa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function dp(){var e,r,n,o;return e=Ti,"ignore"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(lo)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function hp(){var e,r,n,o;return e=Ti,"partition"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Fa)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="PARTITION"):(Ti=e,e=a)):(Ti=e,e=a),e}function mp(){var e,r,n,o;return e=Ti,"into"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ga)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function wp(){var e,r,n,o;return e=Ti,"from"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi($a)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Lp(){var e,r,n,o;return e=Ti,"set"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(we)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SET"):(Ti=e,e=a)):(Ti=e,e=a),e}function Op(){var e,r,n,o;return e=Ti,"as"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(Ba)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function jp(){var e,r,n,o;return e=Ti,"table"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Ur)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TABLE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Cp(){var e,r,n,o;return e=Ti,"schema"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(l)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SCHEMA"):(Ti=e,e=a)):(Ti=e,e=a),e}function gp(){var e,r,n,o;return e=Ti,"on"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(Q)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Ep(){var e,r,n,o;return e=Ti,"join"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ja)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Ap(){var e,r,n,o;return e=Ti,"outer"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(za)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Tp(){var e,r,n,o;return e=Ti,"values"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(ns)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Sp(){var e,r,n,o;return e=Ti,"using"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(os)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Up(){var e,r,n,o;return e=Ti,"with"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Hr)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function _p(){var e,r,n,o;return e=Ti,"group"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(ss)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function xp(){var e,r,n,o;return e=Ti,"by"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(us)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Ip(){var e,r,n,o;return e=Ti,"order"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(is)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Np(){var e,r,n,o;return e=Ti,"asc"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(bs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ASC"):(Ti=e,e=a)):(Ti=e,e=a),e}function Rp(){var e,r,n,o;return e=Ti,"desc"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(vs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DESC"):(Ti=e,e=a)):(Ti=e,e=a),e}function kp(){var e,r,n,o;return e=Ti,"all"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(ys)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ALL"):(Ti=e,e=a)):(Ti=e,e=a),e}function Vp(){var e,r,n,o;return e=Ti,"distinct"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ds)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DISTINCT"):(Ti=e,e=a)):(Ti=e,e=a),e}function Mp(){var e,r,n,o;return e=Ti,"between"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(hs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="BETWEEN"):(Ti=e,e=a)):(Ti=e,e=a),e}function qp(){var e,r,n,o;return e=Ti,"in"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(Ne)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="IN"):(Ti=e,e=a)):(Ti=e,e=a),e}function Pp(){var e,r,n,o;return e=Ti,"is"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(ms)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="IS"):(Ti=e,e=a)):(Ti=e,e=a),e}function Dp(){var e,r,n,o;return e=Ti,"like"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(ws)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="LIKE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Qp(){var e,r,n,o;return e=Ti,"ilike"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Ls)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ILIKE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Fp(){var e,r,n,o;return e=Ti,"exists"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Os)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="EXISTS"):(Ti=e,e=a)):(Ti=e,e=a),e}function Gp(){var e,r,n,o;return e=Ti,"not"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(M)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="NOT"):(Ti=e,e=a)):(Ti=e,e=a),e}function $p(){var e,r,n,o;return e=Ti,"and"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(js)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="AND"):(Ti=e,e=a)):(Ti=e,e=a),e}function Bp(){var e,r,n,o;return e=Ti,"or"===t.substr(Ti,2).toLowerCase()?(r=t.substr(Ti,2),Ti+=2):(r=a,0===Ii&&Mi(Cs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="OR"):(Ti=e,e=a)):(Ti=e,e=a),e}function Hp(){var e,r,n,o;return e=Ti,"array"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(gs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ARRAY"):(Ti=e,e=a)):(Ti=e,e=a),e}function Yp(){var e,r,n,o;return e=Ti,"extract"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Ns)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="EXTRACT"):(Ti=e,e=a)):(Ti=e,e=a),e}function Wp(){var e,r,n,o;return e=Ti,"case"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(ks)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Xp(){var e,r,n,o;return e=Ti,"when"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Vs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Zp(){var e,r,n,o;return e=Ti,"else"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ms)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Kp(){var e,r,n,o;return e=Ti,"end"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(yr)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?e=r=[r,n]:(Ti=e,e=a)):(Ti=e,e=a),e}function Jp(){var e,r,n,o;return e=Ti,"cast"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(qs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CAST"):(Ti=e,e=a)):(Ti=e,e=a),e}function zp(){var e,r,n,o;return e=Ti,"char"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Qs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CHAR"):(Ti=e,e=a)):(Ti=e,e=a),e}function tb(){var e,r,n,o;return e=Ti,"varchar"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Fs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="VARCHAR"):(Ti=e,e=a)):(Ti=e,e=a),e}function eb(){var e,r,n,o;return e=Ti,"numeric"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Gs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="NUMERIC"):(Ti=e,e=a)):(Ti=e,e=a),e}function rb(){var e,r,n,o;return e=Ti,"decimal"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi($s)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DECIMAL"):(Ti=e,e=a)):(Ti=e,e=a),e}function nb(){var e,r,n,o;return e=Ti,"unsigned"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Hs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="UNSIGNED"):(Ti=e,e=a)):(Ti=e,e=a),e}function ob(){var e,r,n,o;return e=Ti,"int"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Ys)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="INT"):(Ti=e,e=a)):(Ti=e,e=a),e}function ab(){var e,r,n,o;return e=Ti,"integer"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Xs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="INTEGER"):(Ti=e,e=a)):(Ti=e,e=a),e}function sb(){var e,r,n,o;return e=Ti,"smallint"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(zs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SMALLINT"):(Ti=e,e=a)):(Ti=e,e=a),e}function ub(){var e,r,n,o;return e=Ti,"serial"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(tu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SERIAL"):(Ti=e,e=a)):(Ti=e,e=a),e}function ib(){var e,r,n,o;return e=Ti,"tinyint"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(eu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TINYINT"):(Ti=e,e=a)):(Ti=e,e=a),e}function cb(){var e,r,n,o;return e=Ti,"tinytext"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(ru)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TINYTEXT"):(Ti=e,e=a)):(Ti=e,e=a),e}function lb(){var e,r,n,o;return e=Ti,"text"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(nu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TEXT"):(Ti=e,e=a)):(Ti=e,e=a),e}function fb(){var e,r,n,o;return e=Ti,"mediumtext"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(ou)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="MEDIUMTEXT"):(Ti=e,e=a)):(Ti=e,e=a),e}function pb(){var e,r,n,o;return e=Ti,"longtext"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(au)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="LONGTEXT"):(Ti=e,e=a)):(Ti=e,e=a),e}function bb(){var e,r,n,o;return e=Ti,"bigint"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(su)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="BIGINT"):(Ti=e,e=a)):(Ti=e,e=a),e}function vb(){var e,r,n,o;return e=Ti,"enum"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(uu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ENUM"):(Ti=e,e=a)):(Ti=e,e=a),e}function yb(){var e,r,n,o;return e=Ti,"float"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(iu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="FLOAT"):(Ti=e,e=a)):(Ti=e,e=a),e}function db(){var e,r,n,o;return e=Ti,"double"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(cu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DOUBLE"):(Ti=e,e=a)):(Ti=e,e=a),e}function hb(){var e,r,n,o;return e=Ti,"bigserial"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(lu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="BIGSERIAL"):(Ti=e,e=a)):(Ti=e,e=a),e}function mb(){var e,r,n,o;return e=Ti,"real"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(fu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REAL"):(Ti=e,e=a)):(Ti=e,e=a),e}function wb(){var e,r,n,o;return e=Ti,"date"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(So)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DATE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Lb(){var e,r,n,o;return e=Ti,"datetime"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(pu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="DATETIME"):(Ti=e,e=a)):(Ti=e,e=a),e}function Ob(){var e,r,n,o;return e=Ti,"rows"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(rt)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ROWS"):(Ti=e,e=a)):(Ti=e,e=a),e}function jb(){var e,r,n,o;return e=Ti,"time"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(bu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TIME"):(Ti=e,e=a)):(Ti=e,e=a),e}function Cb(){var e,r,n,o;return e=Ti,"timestamp"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(vu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TIMESTAMP"):(Ti=e,e=a)):(Ti=e,e=a),e}function gb(){var e,r,n,o;return e=Ti,"truncate"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(yu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="TRUNCATE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Eb(){var e,r,n,o;return e=Ti,"interval"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(xu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="INTERVAL"):(Ti=e,e=a)):(Ti=e,e=a),e}function Ab(){var e,r,n,o;return e=Ti,"current_timestamp"===t.substr(Ti,17).toLowerCase()?(r=t.substr(Ti,17),Ti+=17):(r=a,0===Ii&&Mi(Nu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CURRENT_TIMESTAMP"):(Ti=e,e=a)):(Ti=e,e=a),e}function Tb(){var e,r,n,o;return e=Ti,"current_user"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Qt)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CURRENT_USER"):(Ti=e,e=a)):(Ti=e,e=a),e}function Sb(){var e,r,n,o;return e=Ti,"session_user"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Ft)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="SESSION_USER"):(Ti=e,e=a)):(Ti=e,e=a),e}function Ub(){var e;return 36===t.charCodeAt(Ti)?(e="$",Ti++):(e=a,0===Ii&&Mi(Sn)),e}function _b(){var e;return"$$"===t.substr(Ti,2)?(e="$$",Ti+=2):(e=a,0===Ii&&Mi(Fu)),e}function xb(){var e;return(e=function(){var e;return"@@"===t.substr(Ti,2)?(e="@@",Ti+=2):(e=a,0===Ii&&Mi(Qu)),e}())===a&&(e=function(){var e;return 64===t.charCodeAt(Ti)?(e="@",Ti++):(e=a,0===Ii&&Mi(Du)),e}())===a&&(e=Ub())===a&&(e=Ub()),e}function Ib(){var e;return"::"===t.substr(Ti,2)?(e="::",Ti+=2):(e=a,0===Ii&&Mi($u)),e}function Nb(){var e;return 61===t.charCodeAt(Ti)?(e="=",Ti++):(e=a,0===Ii&&Mi(at)),e}function Rb(){var e,r,n,o;return e=Ti,"add"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Hu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ADD"):(Ti=e,e=a)):(Ti=e,e=a),e}function kb(){var e,r,n,o;return e=Ti,"column"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ar)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="COLUMN"):(Ti=e,e=a)):(Ti=e,e=a),e}function Vb(){var e,r,n,o;return e=Ti,"index"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Yu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="INDEX"):(Ti=e,e=a)):(Ti=e,e=a),e}function Mb(){var e,r,n,o;return e=Ti,"key"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(Et)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="KEY"):(Ti=e,e=a)):(Ti=e,e=a),e}function qb(){var e,r,n,o;return e=Ti,"unique"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(gt)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="UNIQUE"):(Ti=e,e=a)):(Ti=e,e=a),e}function Pb(){var e,r,n,o;return e=Ti,"comment"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Zu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="COMMENT"):(Ti=e,e=a)):(Ti=e,e=a),e}function Db(){var e,r,n,o;return e=Ti,"constraint"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Tr)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CONSTRAINT"):(Ti=e,e=a)):(Ti=e,e=a),e}function Qb(){var e,r,n,o;return e=Ti,"concurrently"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Ku)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CONCURRENTLY"):(Ti=e,e=a)):(Ti=e,e=a),e}function Fb(){var e,r,n,o;return e=Ti,"references"===t.substr(Ti,10).toLowerCase()?(r=t.substr(Ti,10),Ti+=10):(r=a,0===Ii&&Mi(Ju)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REFERENCES"):(Ti=e,e=a)):(Ti=e,e=a),e}function Gb(){var e;return 46===t.charCodeAt(Ti)?(e=".",Ti++):(e=a,0===Ii&&Mi(ya)),e}function $b(){var e;return 44===t.charCodeAt(Ti)?(e=",",Ti++):(e=a,0===Ii&&Mi(ai)),e}function Bb(){var e;return 42===t.charCodeAt(Ti)?(e="*",Ti++):(e=a,0===Ii&&Mi(gn)),e}function Hb(){var e;return 40===t.charCodeAt(Ti)?(e="(",Ti++):(e=a,0===Ii&&Mi(Mr)),e}function Yb(){var e;return 41===t.charCodeAt(Ti)?(e=")",Ti++):(e=a,0===Ii&&Mi(qr)),e}function Wb(){var e;return 91===t.charCodeAt(Ti)?(e="[",Ti++):(e=a,0===Ii&&Mi(si)),e}function Xb(){var e;return 93===t.charCodeAt(Ti)?(e="]",Ti++):(e=a,0===Ii&&Mi(ui)),e}function Zb(){var e;return 59===t.charCodeAt(Ti)?(e=";",Ti++):(e=a,0===Ii&&Mi(Vr)),e}function Kb(){var e;return"->"===t.substr(Ti,2)?(e="->",Ti+=2):(e=a,0===Ii&&Mi(ii)),e}function Jb(){var e;return"->>"===t.substr(Ti,3)?(e="->>",Ti+=3):(e=a,0===Ii&&Mi(ci)),e}function zb(){var e;return(e=function(){var e;return"||"===t.substr(Ti,2)?(e="||",Ti+=2):(e=a,0===Ii&&Mi(Tn)),e}())===a&&(e=function(){var e;return"&&"===t.substr(Ti,2)?(e="&&",Ti+=2):(e=a,0===Ii&&Mi(li)),e}()),e}function tv(){var t,e;for(t=[],(e=av())===a&&(e=rv());e!==a;)t.push(e),(e=av())===a&&(e=rv());return t}function ev(){var t,e;if(t=[],(e=av())===a&&(e=rv()),e!==a)for(;e!==a;)t.push(e),(e=av())===a&&(e=rv());else t=a;return t}function rv(){var e;return(e=function e(){var r,n,o,s,u,i,c;r=Ti,"/*"===t.substr(Ti,2)?(n="/*",Ti+=2):(n=a,0===Ii&&Mi(fi));if(n!==a){for(o=[],s=Ti,u=Ti,Ii++,"*/"===t.substr(Ti,2)?(i="*/",Ti+=2):(i=a,0===Ii&&Mi(pi)),Ii--,i===a?u=void 0:(Ti=u,u=a),u!==a?(i=Ti,Ii++,"/*"===t.substr(Ti,2)?(c="/*",Ti+=2):(c=a,0===Ii&&Mi(fi)),Ii--,c===a?i=void 0:(Ti=i,i=a),i!==a&&(c=ov())!==a?s=u=[u,i,c]:(Ti=s,s=a)):(Ti=s,s=a),s===a&&(s=e());s!==a;)o.push(s),s=Ti,u=Ti,Ii++,"*/"===t.substr(Ti,2)?(i="*/",Ti+=2):(i=a,0===Ii&&Mi(pi)),Ii--,i===a?u=void 0:(Ti=u,u=a),u!==a?(i=Ti,Ii++,"/*"===t.substr(Ti,2)?(c="/*",Ti+=2):(c=a,0===Ii&&Mi(fi)),Ii--,c===a?i=void 0:(Ti=i,i=a),i!==a&&(c=ov())!==a?s=u=[u,i,c]:(Ti=s,s=a)):(Ti=s,s=a),s===a&&(s=e());o!==a?("*/"===t.substr(Ti,2)?(s="*/",Ti+=2):(s=a,0===Ii&&Mi(pi)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a)}else Ti=r,r=a;return r}())===a&&(e=function(){var e,r,n,o,s,u;e=Ti,"--"===t.substr(Ti,2)?(r="--",Ti+=2):(r=a,0===Ii&&Mi(bi));if(r!==a){for(n=[],o=Ti,s=Ti,Ii++,u=sv(),Ii--,u===a?s=void 0:(Ti=s,s=a),s!==a&&(u=ov())!==a?o=s=[s,u]:(Ti=o,o=a);o!==a;)n.push(o),o=Ti,s=Ti,Ii++,u=sv(),Ii--,u===a?s=void 0:(Ti=s,s=a),s!==a&&(u=ov())!==a?o=s=[s,u]:(Ti=o,o=a);n!==a?e=r=[r,n]:(Ti=e,e=a)}else Ti=e,e=a;return e}()),e}function nv(){var t,e,r,n,o,s,u;return t=Ti,(e=Pb())!==a&&tv()!==a?((r=Nb())===a&&(r=null),r!==a&&tv()!==a&&(n=Ff())!==a?(Si=t,s=r,u=n,t=e={type:(o=e).toLowerCase(),keyword:o.toLowerCase(),symbol:s,value:u}):(Ti=t,t=a)):(Ti=t,t=a),t}function ov(){var e;return t.length>Ti?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(vi)),e}function av(){var e;return yi.test(t.charAt(Ti))?(e=t.charAt(Ti),Ti++):(e=a,0===Ii&&Mi(di)),e}function sv(){var e,r;if((e=function(){var e,r;e=Ti,Ii++,t.length>Ti?(r=t.charAt(Ti),Ti++):(r=a,0===Ii&&Mi(vi));Ii--,r===a?e=void 0:(Ti=e,e=a);return e}())===a)if(e=[],ba.test(t.charAt(Ti))?(r=t.charAt(Ti),Ti++):(r=a,0===Ii&&Mi(va)),r!==a)for(;r!==a;)e.push(r),ba.test(t.charAt(Ti))?(r=t.charAt(Ti),Ti++):(r=a,0===Ii&&Mi(va));else e=a;return e}function uv(){var e,r;return e=Ti,Si=Ti,Rv=[],(!0?void 0:a)!==a&&tv()!==a?((r=iv())===a&&(r=function(){var e,r;e=Ti,function(){var e;return"return"===t.substr(Ti,6).toLowerCase()?(e=t.substr(Ti,6),Ti+=6):(e=a,0===Ii&&Mi(Gu)),e}()!==a&&tv()!==a&&(r=cv())!==a?(Si=e,e={type:"return",expr:r}):(Ti=e,e=a);return e}()),r!==a?(Si=e,e={type:"proc",stmt:r,vars:Rv}):(Ti=e,e=a)):(Ti=e,e=a),e}function iv(){var e,r,n,o,s,u;return e=Ti,(r=dv())===a&&(r=hv()),r!==a&&tv()!==a?((n=function(){var e;return":="===t.substr(Ti,2)?(e=":=",Ti+=2):(e=a,0===Ii&&Mi(E)),e}())===a&&(n=Nb())===a&&(n=rp()),n!==a&&tv()!==a&&(o=cv())!==a?(Si=e,s=n,u=o,e=r={type:"assign",left:r,symbol:Array.isArray(s)?s[0]:s,right:u}):(Ti=e,e=a)):(Ti=e,e=a),e}function cv(){var t;return(t=Pc())===a&&(t=function(){var t,e,r,n,o;t=Ti,(e=dv())!==a&&tv()!==a&&(r=il())!==a&&tv()!==a&&(n=dv())!==a&&tv()!==a&&(o=fl())!==a?(Si=t,t=e={type:"join",ltable:e,rtable:n,op:r,on:o}):(Ti=t,t=a);return t}())===a&&(t=lv())===a&&(t=function(){var t,e;t=Ti,Wb()!==a&&tv()!==a&&(e=yv())!==a&&tv()!==a&&Xb()!==a?(Si=t,t={type:"array",value:e}):(Ti=t,t=a);return t}()),t}function lv(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=fv())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Wl())!==a&&(u=tv())!==a&&(i=fv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Wl())!==a&&(u=tv())!==a&&(i=fv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=un(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function fv(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=pv())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=Zl())!==a&&(u=tv())!==a&&(i=pv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=Zl())!==a&&(u=tv())!==a&&(i=pv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=un(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function pv(){var t,e,r,n,o,s,u,i,c;return(t=Mf())===a&&(t=dv())===a&&(t=vv())===a&&(t=Cf())===a&&(t=Ti,(e=Hb())!==a&&(r=tv())!==a&&(n=lv())!==a&&(o=tv())!==a&&(s=Yb())!==a?(Si=t,(c=n).parentheses=!0,t=e=c):(Ti=t,t=a),t===a&&(t=Ti,(e=wf())!==a?(r=Ti,(n=Gb())!==a&&(o=tv())!==a&&(s=wf())!==a?r=n=[n,o,s]:(Ti=r,r=a),r===a&&(r=null),r!==a?(Si=t,u=e,t=e=(i=r)?{type:"column_ref",table:u,column:i[2]}:{type:"var",name:u,prefix:null}):(Ti=t,t=a)):(Ti=t,t=a))),t}function bv(){var t,e,r,n,o,s,u;return t=Ti,(e=of())!==a?(r=Ti,(n=tv())!==a&&(o=Gb())!==a&&(s=tv())!==a&&(u=of())!==a?r=n=[n,o,s,u]:(Ti=r,r=a),r===a&&(r=null),r!==a?(Si=t,t=e=function(t,e){const r={name:[t]};return null!==e&&(r.schema=t,r.name=[e[3]]),r}(e,r)):(Ti=t,t=a)):(Ti=t,t=a),t}function vv(){var t,e,r;return t=Ti,(e=bv())!==a&&tv()!==a&&Hb()!==a&&tv()!==a?((r=yv())===a&&(r=null),r!==a&&tv()!==a&&Yb()!==a?(Si=t,t=e={type:"function",name:e,args:{type:"expr_list",value:r},...Ev()}):(Ti=t,t=a)):(Ti=t,t=a),t}function yv(){var t,e,r,n,o,s,u,i;if(t=Ti,(e=pv())!==a){for(r=[],n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=pv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);n!==a;)r.push(n),n=Ti,(o=tv())!==a&&(s=$b())!==a&&(u=tv())!==a&&(i=pv())!==a?n=o=[o,s,u,i]:(Ti=n,n=a);r!==a?(Si=t,t=e=Uv(e,r)):(Ti=t,t=a)}else Ti=t,t=a;return t}function dv(){var e,r,n,o,s,u,i;if(e=Ti,(r=_b())!==a){for(n=[],hi.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(mi));o!==a;)n.push(o),hi.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(mi));n!==a&&(o=_b())!==a?(Si=e,e=r={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(Ti=e,e=a)}else Ti=e,e=a;if(e===a){if(e=Ti,(r=Ub())!==a)if((n=hf())!==a)if((o=Ub())!==a){for(s=[],hi.test(t.charAt(Ti))?(u=t.charAt(Ti),Ti++):(u=a,0===Ii&&Mi(mi));u!==a;)s.push(u),hi.test(t.charAt(Ti))?(u=t.charAt(Ti),Ti++):(u=a,0===Ii&&Mi(mi));s!==a&&(u=Ub())!==a&&(i=hf())!==a?(Si=Ti,(function(t,e,r){if(t!==r)return!0}(n,0,i)?a:void 0)!==a&&Ub()!==a?(Si=e,e=r=function(t,e,r){return{type:"var",name:e.join(""),prefix:`$${t}$`,suffix:`$${r}$`}}(n,s,i)):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;e===a&&(e=Ti,(r=xb())!==a&&(n=hv())!==a?(Si=e,e=r=function(t,e){return{type:"var",...e,prefix:t}}(r,n)):(Ti=e,e=a))}return e}function hv(){var e,r,n,o,s;return e=Ti,34===t.charCodeAt(Ti)?(r='"',Ti++):(r=a,0===Ii&&Mi(Pr)),r===a&&(r=null),r!==a&&(n=wf())!==a&&(o=function(){var e,r,n,o,s;e=Ti,r=[],n=Ti,46===t.charCodeAt(Ti)?(o=".",Ti++):(o=a,0===Ii&&Mi(ya));o!==a&&(s=wf())!==a?n=o=[o,s]:(Ti=n,n=a);for(;n!==a;)r.push(n),n=Ti,46===t.charCodeAt(Ti)?(o=".",Ti++):(o=a,0===Ii&&Mi(ya)),o!==a&&(s=wf())!==a?n=o=[o,s]:(Ti=n,n=a);r!==a&&(Si=e,r=function(t){const e=[];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(r));return e=r}())!==a?(34===t.charCodeAt(Ti)?(s='"',Ti++):(s=a,0===Ii&&Mi(Pr)),s===a&&(s=null),s!==a?(Si=e,e=r=function(t,e,r,n){if(t&&!n||!t&&n)throw new Error("double quoted not match");return Rv.push(e),{type:"var",name:e,members:r,quoted:t&&n?'"':null,prefix:null}}(r,n,o,s)):(Ti=e,e=a)):(Ti=e,e=a),e===a&&(e=Ti,(r=Yf())!==a&&(Si=e,r={type:"var",name:r.value,members:[],quoted:null,prefix:null}),e=r),e}function mv(){var e;return(e=function(){var t,e,r;t=Ti,(e=jv())===a&&(e=Lv());e!==a&&tv()!==a&&Wb()!==a&&tv()!==a&&(r=Xb())!==a&&tv()!==a&&Wb()!==a&&tv()!==a&&Xb()!==a?(Si=t,n=e,e={...n,array:{dimension:2}},t=e):(Ti=t,t=a);var n;t===a&&(t=Ti,(e=jv())===a&&(e=Lv()),e!==a&&tv()!==a&&Wb()!==a&&tv()!==a?((r=Yf())===a&&(r=null),r!==a&&tv()!==a&&Xb()!==a?(Si=t,e=function(t,e){return{...t,array:{dimension:1,length:[e]}}}(e,r),t=e):(Ti=t,t=a)):(Ti=t,t=a),t===a&&(t=Ti,(e=jv())===a&&(e=Lv()),e!==a&&tv()!==a&&Hp()!==a?(Si=t,e=function(t){return{...t,array:{keyword:"array"}}}(e),t=e):(Ti=t,t=a)));return t}())===a&&(e=Lv())===a&&(e=jv())===a&&(e=function(){var e,r,n,o;e=Ti,(r=wb())===a&&(r=Lb());if(r!==a)if(tv()!==a)if(Hb()!==a)if(tv()!==a){if(n=[],da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha)),o!==a)for(;o!==a;)n.push(o),da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha));else n=a;n!==a&&(o=tv())!==a&&Yb()!==a?(Si=e,r={dataType:r,length:parseInt(n.join(""),10),parentheses:!0},e=r):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;e===a&&(e=Ti,(r=wb())===a&&(r=Lb()),r!==a&&(Si=e,r=Ei(r)),(e=r)===a&&(e=function(){var e,r,n,o,s,u;e=Ti,(r=jb())===a&&(r=Cb());if(r!==a)if(tv()!==a)if((n=Hb())!==a)if(tv()!==a){if(o=[],da.test(t.charAt(Ti))?(s=t.charAt(Ti),Ti++):(s=a,0===Ii&&Mi(ha)),s!==a)for(;s!==a;)o.push(s),da.test(t.charAt(Ti))?(s=t.charAt(Ti),Ti++):(s=a,0===Ii&&Mi(ha));else o=a;o!==a&&(s=tv())!==a&&Yb()!==a&&tv()!==a?((u=Cv())===a&&(u=null),u!==a?(Si=e,r=function(t,e,r){return{dataType:t,length:parseInt(e.join(""),10),parentheses:!0,suffix:r}}(r,o,u),e=r):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;e===a&&(e=Ti,(r=jb())===a&&(r=Cb()),r!==a&&tv()!==a?((n=Cv())===a&&(n=null),n!==a?(Si=e,r=function(t,e){return{dataType:t,suffix:e}}(r,n),e=r):(Ti=e,e=a)):(Ti=e,e=a));return e}()));return e}())===a&&(e=function(){var e,r;e=Ti,(r=function(){var e,r,n,o;return e=Ti,"json"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Zs)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="JSON"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"jsonb"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Ks)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="JSONB"):(Ti=e,e=a)):(Ti=e,e=a),e}());r!==a&&(Si=e,r=Ei(r));return e=r}())===a&&(e=function(){var e,r;e=Ti,(r=function(){var e,r,n,o;return e=Ti,"geometry"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Js)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="GEOMETRY"):(Ti=e,e=a)):(Ti=e,e=a),e}())!==a&&(Si=e,r={dataType:r});return e=r}())===a&&(e=function(){var t,e;t=Ti,(e=cb())===a&&(e=lb())===a&&(e=fb())===a&&(e=pb());e!==a&&Wb()!==a&&tv()!==a&&Xb()!==a?(Si=t,t=e={dataType:e+"[]"}):(Ti=t,t=a);t===a&&(t=Ti,(e=cb())===a&&(e=lb())===a&&(e=fb())===a&&(e=pb()),e!==a&&(Si=t,e=function(t){return{dataType:t}}(e)),t=e);return t}())===a&&(e=function(){var e,r;e=Ti,(r=function(){var e,r,n,o;return e=Ti,"uuid"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(hu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="UUID"):(Ti=e,e=a)):(Ti=e,e=a),e}())!==a&&(Si=e,r={dataType:r});return e=r}())===a&&(e=function(){var e,r;e=Ti,(r=function(){var e,r,n,o;return e=Ti,"bool"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Ps)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="BOOL"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"boolean"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Ds)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="BOOLEAN"):(Ti=e,e=a)):(Ti=e,e=a),e}());r!==a&&(Si=e,r=wi(r));return e=r}())===a&&(e=function(){var t,e,r;t=Ti,(e=vb())!==a&&tv()!==a&&(r=Il())!==a?(Si=t,n=e,(o=r).parentheses=!0,t=e={dataType:n,expr:o}):(Ti=t,t=a);var n,o;return t}())===a&&(e=function(){var t,e;t=Ti,(e=ub())===a&&(e=Eb());e!==a&&(Si=t,e=Ei(e));return t=e}())===a&&(e=function(){var e,r;e=Ti,"bytea"===t.substr(Ti,5).toLowerCase()?(r=t.substr(Ti,5),Ti+=5):(r=a,0===Ii&&Mi(Li));r!==a&&(Si=e,r={dataType:"BYTEA"});return e=r}())===a&&(e=function(){var e,r;e=Ti,(r=function(){var e,r,n,o;return e=Ti,"oid"===t.substr(Ti,3).toLowerCase()?(r=t.substr(Ti,3),Ti+=3):(r=a,0===Ii&&Mi(mu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="OID"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regclass"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(wu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGCLASS"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regcollation"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Lu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGCOLLATION"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regconfig"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(Ou)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGCONFIG"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regdictionary"===t.substr(Ti,13).toLowerCase()?(r=t.substr(Ti,13),Ti+=13):(r=a,0===Ii&&Mi(ju)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGDICTIONARY"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regnamespace"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Cu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGNAMESPACE"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regoper"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(gu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGOPER"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regoperator"===t.substr(Ti,11).toLowerCase()?(r=t.substr(Ti,11),Ti+=11):(r=a,0===Ii&&Mi(Eu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGOPERATOR"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regproc"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Au)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGPROC"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regprocedure"===t.substr(Ti,12).toLowerCase()?(r=t.substr(Ti,12),Ti+=12):(r=a,0===Ii&&Mi(Tu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGPROCEDURE"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regrole"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Su)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGROLE"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ti,"regtype"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Uu)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="REGTYPE"):(Ti=e,e=a)):(Ti=e,e=a),e}());r!==a&&(Si=e,r=wi(r));return e=r}())===a&&(e=function(){var e,r;e=Ti,"record"===t.substr(Ti,6).toLowerCase()?(r=t.substr(Ti,6),Ti+=6):(r=a,0===Ii&&Mi(Ai));r!==a&&(Si=e,r={dataType:"RECORD"});return e=r}())===a&&(e=function(){var t,e;t=Ti,(e=wf())!==a?(Si=Ti,r=e,(Mv.has(r)?void 0:a)!==a?(Si=t,e=function(t){return{dataType:t}}(e),t=e):(Ti=t,t=a)):(Ti=t,t=a);var r;return t}()),e}function wv(){var e,r;return e=Ti,function(){var e,r,n,o;return e=Ti,"character"===t.substr(Ti,9).toLowerCase()?(r=t.substr(Ti,9),Ti+=9):(r=a,0===Ii&&Mi(me)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="CHARACTER"):(Ti=e,e=a)):(Ti=e,e=a),e}()!==a&&tv()!==a?("varying"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Oi)),r===a&&(r=null),r!==a?(Si=e,e="CHARACTER VARYING"):(Ti=e,e=a)):(Ti=e,e=a),e}function Lv(){var e,r,n,o;if(e=Ti,(r=zp())===a&&(r=tb())===a&&(r=wv()),r!==a)if(tv()!==a)if(Hb()!==a)if(tv()!==a){if(n=[],da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha)),o!==a)for(;o!==a;)n.push(o),da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha));else n=a;n!==a&&(o=tv())!==a&&Yb()!==a?(Si=e,e=r={dataType:r,length:parseInt(n.join(""),10),parentheses:!0}):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;return e===a&&(e=Ti,(r=zp())===a&&(r=wv())===a&&(r=tb()),r!==a&&(Si=e,r=function(t){return{dataType:t}}(r)),e=r),e}function Ov(){var e,r,n;return e=Ti,(r=nb())===a&&(r=null),r!==a&&tv()!==a?((n=function(){var e,r,n,o;return e=Ti,"zerofill"===t.substr(Ti,8).toLowerCase()?(r=t.substr(Ti,8),Ti+=8):(r=a,0===Ii&&Mi(Ws)),r!==a?(n=Ti,Ii++,o=Lf(),Ii--,o===a?n=void 0:(Ti=n,n=a),n!==a?(Si=e,e=r="ZEROFILL"):(Ti=e,e=a)):(Ti=e,e=a),e}())===a&&(n=null),n!==a?(Si=e,e=r=function(t,e){const r=[];return t&&r.push(t),e&&r.push(e),r}(r,n)):(Ti=e,e=a)):(Ti=e,e=a),e}function jv(){var e,r,n,o,s,u,i,c,l,f,p,b,v,y,d,h;if(e=Ti,(r=eb())===a&&(r=rb())===a&&(r=ob())===a&&(r=ab())===a&&(r=sb())===a&&(r=ib())===a&&(r=bb())===a&&(r=yb())===a&&(r=Ti,(n=db())!==a&&(o=tv())!==a?("precision"===t.substr(Ti,9).toLowerCase()?(s=t.substr(Ti,9),Ti+=9):(s=a,0===Ii&&Mi(ji)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=db())===a&&(r=ub())===a&&(r=hb())===a&&(r=mb())),r!==a)if((n=tv())!==a)if((o=Hb())!==a)if((s=tv())!==a){if(u=[],da.test(t.charAt(Ti))?(i=t.charAt(Ti),Ti++):(i=a,0===Ii&&Mi(ha)),i!==a)for(;i!==a;)u.push(i),da.test(t.charAt(Ti))?(i=t.charAt(Ti),Ti++):(i=a,0===Ii&&Mi(ha));else u=a;if(u!==a)if((i=tv())!==a){if(c=Ti,(l=$b())!==a)if((f=tv())!==a){if(p=[],da.test(t.charAt(Ti))?(b=t.charAt(Ti),Ti++):(b=a,0===Ii&&Mi(ha)),b!==a)for(;b!==a;)p.push(b),da.test(t.charAt(Ti))?(b=t.charAt(Ti),Ti++):(b=a,0===Ii&&Mi(ha));else p=a;p!==a?c=l=[l,f,p]:(Ti=c,c=a)}else Ti=c,c=a;else Ti=c,c=a;c===a&&(c=null),c!==a&&(l=tv())!==a&&(f=Yb())!==a&&(p=tv())!==a?((b=Ov())===a&&(b=null),b!==a?(Si=e,v=r,y=u,d=c,h=b,e=r={dataType:Array.isArray(v)?`${v[0].toUpperCase()} ${v[2].toUpperCase()}`:v,length:parseInt(y.join(""),10),scale:d&&parseInt(d[2].join(""),10),parentheses:!0,suffix:h}):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;else Ti=e,e=a}else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;else Ti=e,e=a;if(e===a){if(e=Ti,(r=eb())===a&&(r=rb())===a&&(r=ob())===a&&(r=ab())===a&&(r=sb())===a&&(r=ib())===a&&(r=bb())===a&&(r=yb())===a&&(r=Ti,(n=db())!==a&&(o=tv())!==a?("precision"===t.substr(Ti,9).toLowerCase()?(s=t.substr(Ti,9),Ti+=9):(s=a,0===Ii&&Mi(ji)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=db())===a&&(r=ub())===a&&(r=hb())===a&&(r=mb())),r!==a){if(n=[],da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha)),o!==a)for(;o!==a;)n.push(o),da.test(t.charAt(Ti))?(o=t.charAt(Ti),Ti++):(o=a,0===Ii&&Mi(ha));else n=a;n!==a&&(o=tv())!==a?((s=Ov())===a&&(s=null),s!==a?(Si=e,e=r=function(t,e,r){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,length:parseInt(e.join(""),10),suffix:r}}(r,n,s)):(Ti=e,e=a)):(Ti=e,e=a)}else Ti=e,e=a;e===a&&(e=Ti,(r=eb())===a&&(r=rb())===a&&(r=ob())===a&&(r=ab())===a&&(r=sb())===a&&(r=ib())===a&&(r=bb())===a&&(r=yb())===a&&(r=Ti,(n=db())!==a&&(o=tv())!==a?("precision"===t.substr(Ti,9).toLowerCase()?(s=t.substr(Ti,9),Ti+=9):(s=a,0===Ii&&Mi(ji)),s!==a?r=n=[n,o,s]:(Ti=r,r=a)):(Ti=r,r=a),r===a&&(r=db())===a&&(r=ub())===a&&(r=hb())===a&&(r=mb())),r!==a&&(n=tv())!==a?((o=Ov())===a&&(o=null),o!==a&&(s=tv())!==a?(Si=e,e=r=function(t,e){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,suffix:e}}(r,o)):(Ti=e,e=a)):(Ti=e,e=a))}return e}function Cv(){var e,r,n;return e=Ti,"without"===t.substr(Ti,7).toLowerCase()?(r=t.substr(Ti,7),Ti+=7):(r=a,0===Ii&&Mi(Ci)),r===a&&("with"===t.substr(Ti,4).toLowerCase()?(r=t.substr(Ti,4),Ti+=4):(r=a,0===Ii&&Mi(Hr))),r!==a&&tv()!==a&&jb()!==a&&tv()!==a?("zone"===t.substr(Ti,4).toLowerCase()?(n=t.substr(Ti,4),Ti+=4):(n=a,0===Ii&&Mi(gi)),n!==a?(Si=e,e=r=[r.toUpperCase(),"TIME","ZONE"]):(Ti=e,e=a)):(Ti=e,e=a),e}const gv={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function Ev(){return e.includeLocations?{loc:Vi(Si,Ti)}:{}}function Av(t,e){return{type:"unary_expr",operator:t,expr:e}}function Tv(t,e,r){return{type:"binary_expr",operator:t,left:e,right:r}}function Sv(t){const e=n(Number.MAX_SAFE_INTEGER);return!(n(t)<e)}function Uv(t,e,r=3){const n=Array.isArray(t)?t:[t];for(let t=0;t<e.length;t++)delete e[t][r].tableList,delete e[t][r].columnList,n.push(e[t][r]);return n}function _v(t,e){let r=t;for(let t=0;t<e.length;t++)r=Tv(e[t][1],r,e[t][3]);return r}function xv(t){const e=qv[t];return e||(t||null)}function Iv(t){const e=new Set;for(let r of t.keys()){const t=r.split("::");if(!t){e.add(r);break}t&&t[1]&&(t[1]=xv(t[1])),e.add(t.join("::"))}return Array.from(e)}function Nv(t){return"string"==typeof t?{type:"same",value:t}:t}let Rv=[];const kv=new Set,Vv=new Set,Mv=new Set,qv={};if((r=u())!==a&&Ti===t.length)return r;throw r!==a&&Ti<t.length&&Mi({type:"end"}),qi(xi,_i<t.length?t.charAt(_i):null,_i<t.length?Vi(_i,_i+1):Vi(_i,_i))}}},function(t,e,r){t.exports=r(27)},function(t,e,r){"use strict";r.r(e),function(t){var n=r(24);r.d(e,"Parser",(function(){return n.a}));var o=r(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,r(28))},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){(function(t){var n,o=function(t){"use strict";var e=1e7,r=9007199254740992,n=f(r),a="function"==typeof BigInt;function s(t,e,r,n){return void 0===t?s[0]:void 0!==e&&(10!=+e||r)?D(t,e,r,n):B(t)}function u(t,e){this.value=t,this.sign=e,this.isSmall=!1}function i(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-r<t&&t<r}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var r=t.length;if(r<4&&S(t,n)<0)switch(r){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*e;default:return t[0]+(t[1]+t[2]*e)*e}return t}function b(t){for(var e=t.length;0===t[--e];);t.length=e+1}function v(t){for(var e=new Array(t),r=-1;++r<t;)e[r]=0;return e}function y(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,r){var n,o,a=t.length,s=r.length,u=new Array(a),i=0,c=e;for(o=0;o<s;o++)i=(n=t[o]+r[o]+i)>=c?1:0,u[o]=n-i*c;for(;o<a;)i=(n=t[o]+i)===c?1:0,u[o++]=n-i*c;return i>0&&u.push(i),u}function h(t,e){return t.length>=e.length?d(t,e):d(e,t)}function m(t,r){var n,o,a=t.length,s=new Array(a),u=e;for(o=0;o<a;o++)n=t[o]-u+r,r=Math.floor(n/u),s[o]=n-r*u,r+=1;for(;r>0;)s[o++]=r%u,r=Math.floor(r/u);return s}function w(t,e){var r,n,o=t.length,a=e.length,s=new Array(o),u=0;for(r=0;r<a;r++)(n=t[r]-u-e[r])<0?(n+=1e7,u=1):u=0,s[r]=n;for(r=a;r<o;r++){if(!((n=t[r]-u)<0)){s[r++]=n;break}n+=1e7,s[r]=n}for(;r<o;r++)s[r]=t[r];return b(s),s}function L(t,e,r){var n,o,a=t.length,s=new Array(a),c=-e;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,s[n]=o<0?o+1e7:o;return"number"==typeof(s=p(s))?(r&&(s=-s),new i(s)):new u(s,r)}function O(t,e){var r,n,o,a,s=t.length,u=e.length,i=v(s+u);for(o=0;o<s;++o){a=t[o];for(var c=0;c<u;++c)r=a*e[c]+i[o+c],n=Math.floor(r/1e7),i[o+c]=r-1e7*n,i[o+c+1]+=n}return b(i),i}function j(t,r){var n,o,a=t.length,s=new Array(a),u=e,i=0;for(o=0;o<a;o++)n=t[o]*r+i,i=Math.floor(n/u),s[o]=n-i*u;for(;i>0;)s[o++]=i%u,i=Math.floor(i/u);return s}function C(t,e){for(var r=[];e-- >0;)r.push(0);return r.concat(t)}function g(t,r,n){return new u(t<e?j(r,t):O(r,f(t)),n)}function E(t){var e,r,n,o,a=t.length,s=v(a+a);for(n=0;n<a;n++){r=0-(o=t[n])*o;for(var u=n;u<a;u++)e=o*t[u]*2+s[n+u]+r,r=Math.floor(e/1e7),s[n+u]=e-1e7*r;s[n+a]=r}return b(s),s}function A(t,e){var r,n,o,a,s=t.length,u=v(s);for(o=0,r=s-1;r>=0;--r)o=(a=1e7*o+t[r])-(n=y(a/e))*e,u[r]=0|n;return[u,0|o]}function T(t,r){var n,o=B(r);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,h=o.value;if(0===h)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new i(y(d/h)),new i(d%h)]:[s[0],t];if(o.isSmall){if(1===h)return[t,s[0]];if(-1==h)return[t.negate(),s[0]];var m=Math.abs(h);if(m<e){l=p((n=A(d,m))[0]);var L=n[1];return t.sign&&(L=-L),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new i(l),new i(L)]):[new u(l,t.sign!==o.sign),new i(L)]}h=f(m)}var O=S(d,h);if(-1===O)return[s[0],t];if(0===O)return[s[t.sign===o.sign?1:-1],s[0]];l=(n=d.length+h.length<=200?function(t,r){var n,o,a,s,u,i,c,l=t.length,f=r.length,b=e,y=v(r.length),d=r[f-1],h=Math.ceil(b/(2*d)),m=j(t,h),w=j(r,h);for(m.length<=l&&m.push(0),w.push(0),d=w[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==d&&(n=Math.floor((m[o+f]*b+m[o+f-1])/d)),a=0,s=0,i=w.length,u=0;u<i;u++)a+=n*w[u],c=Math.floor(a/b),s+=m[o+u]-(a-c*b),a=c,s<0?(m[o+u]=s+b,s=-1):(m[o+u]=s,s=0);for(;0!==s;){for(n-=1,a=0,u=0;u<i;u++)(a+=m[o+u]-b+w[u])<0?(m[o+u]=a+b,a=0):(m[o+u]=a,a=1);s+=a}y[o]=n}return m=A(m,h)[0],[p(y),p(m)]}(d,h):function(t,e){for(var r,n,o,a,s,u=t.length,i=e.length,c=[],l=[];u;)if(l.unshift(t[--u]),b(l),S(l,e)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*e[i-1]+e[i-2],n>i&&(o=1e7*(o+1)),r=Math.ceil(o/a);do{if(S(s=j(e,r),l)<=0)break;r--}while(r);c.push(r),l=w(l,s)}return c.reverse(),[p(c),p(l)]}(d,h))[0];var C=t.sign!==o.sign,g=n[1],E=t.sign;return"number"==typeof l?(C&&(l=-l),l=new i(l)):l=new u(l,C),"number"==typeof g?(E&&(g=-g),g=new i(g)):g=new u(g,E),[l,g]}function S(t,e){if(t.length!==e.length)return t.length>e.length?1:-1;for(var r=t.length-1;r>=0;r--)if(t[r]!==e[r])return t[r]>e[r]?1:-1;return 0}function U(t){var e=t.abs();return!e.isUnit()&&(!!(e.equals(2)||e.equals(3)||e.equals(5))||!(e.isEven()||e.isDivisibleBy(3)||e.isDivisibleBy(5))&&(!!e.lesser(49)||void 0))}function _(t,e){for(var r,n,a,s=t.prev(),u=s,i=0;u.isEven();)u=u.divide(2),i++;t:for(n=0;n<e.length;n++)if(!t.lesser(e[n])&&!(a=o(e[n]).modPow(u,t)).isUnit()&&!a.equals(s)){for(r=i-1;0!=r;r--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(s))continue t}return!1}return!0}u.prototype=Object.create(s.prototype),i.prototype=Object.create(s.prototype),c.prototype=Object.create(s.prototype),u.prototype.add=function(t){var e=B(t);if(this.sign!==e.sign)return this.subtract(e.negate());var r=this.value,n=e.value;return e.isSmall?new u(m(r,Math.abs(n)),this.sign):new u(h(r,n),this.sign)},u.prototype.plus=u.prototype.add,i.prototype.add=function(t){var e=B(t),r=this.value;if(r<0!==e.sign)return this.subtract(e.negate());var n=e.value;if(e.isSmall){if(l(r+n))return new i(r+n);n=f(Math.abs(n))}return new u(m(n,Math.abs(r)),r<0)},i.prototype.plus=i.prototype.add,c.prototype.add=function(t){return new c(this.value+B(t).value)},c.prototype.plus=c.prototype.add,u.prototype.subtract=function(t){var e=B(t);if(this.sign!==e.sign)return this.add(e.negate());var r=this.value,n=e.value;return e.isSmall?L(r,Math.abs(n),this.sign):function(t,e,r){var n;return S(t,e)>=0?n=w(t,e):(n=w(e,t),r=!r),"number"==typeof(n=p(n))?(r&&(n=-n),new i(n)):new u(n,r)}(r,n,this.sign)},u.prototype.minus=u.prototype.subtract,i.prototype.subtract=function(t){var e=B(t),r=this.value;if(r<0!==e.sign)return this.add(e.negate());var n=e.value;return e.isSmall?new i(r-n):L(n,Math.abs(r),r>=0)},i.prototype.minus=i.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-B(t).value)},c.prototype.minus=c.prototype.subtract,u.prototype.negate=function(){return new u(this.value,!this.sign)},i.prototype.negate=function(){var t=this.sign,e=new i(-this.value);return e.sign=!t,e},c.prototype.negate=function(){return new c(-this.value)},u.prototype.abs=function(){return new u(this.value,!1)},i.prototype.abs=function(){return new i(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},u.prototype.multiply=function(t){var r,n,o,a=B(t),i=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return s[0];if(1===c)return this;if(-1===c)return this.negate();if((r=Math.abs(c))<e)return new u(j(i,r),l);c=f(r)}return n=i.length,o=c.length,new u(-.012*n-.012*o+15e-6*n*o>0?function t(e,r){var n=Math.max(e.length,r.length);if(n<=30)return O(e,r);n=Math.ceil(n/2);var o=e.slice(n),a=e.slice(0,n),s=r.slice(n),u=r.slice(0,n),i=t(a,u),c=t(o,s),l=t(h(a,o),h(u,s)),f=h(h(i,C(w(w(l,i),c),n)),C(c,2*n));return b(f),f}(i,c):O(i,c),l)},u.prototype.times=u.prototype.multiply,i.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new i(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},u.prototype._multiplyBySmall=function(t){return 0===t.value?s[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},i.prototype.multiply=function(t){return B(t)._multiplyBySmall(this)},i.prototype.times=i.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*B(t).value)},c.prototype.times=c.prototype.multiply,u.prototype.square=function(){return new u(E(this.value),!1)},i.prototype.square=function(){var t=this.value*this.value;return l(t)?new i(t):new u(E(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},u.prototype.divmod=function(t){var e=T(this,t);return{quotient:e[0],remainder:e[1]}},c.prototype.divmod=i.prototype.divmod=u.prototype.divmod,u.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/B(t).value)},i.prototype.over=i.prototype.divide=u.prototype.over=u.prototype.divide,u.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%B(t).value)},i.prototype.remainder=i.prototype.mod=u.prototype.remainder=u.prototype.mod,u.prototype.pow=function(t){var e,r,n,o=B(t),a=this.value,u=o.value;if(0===u)return s[1];if(0===a)return s[0];if(1===a)return s[1];if(-1===a)return o.isEven()?s[1]:s[-1];if(o.sign)return s[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(e=Math.pow(a,u)))return new i(y(e));for(r=this,n=s[1];!0&u&&(n=n.times(r),--u),0!==u;)u/=2,r=r.square();return n},i.prototype.pow=u.prototype.pow,c.prototype.pow=function(t){var e=B(t),r=this.value,n=e.value,o=BigInt(0),a=BigInt(1),u=BigInt(2);if(n===o)return s[1];if(r===o)return s[0];if(r===a)return s[1];if(r===BigInt(-1))return e.isEven()?s[1]:s[-1];if(e.isNegative())return new c(o);for(var i=this,l=s[1];(n&a)===a&&(l=l.times(i),--n),n!==o;)n/=u,i=i.square();return l},u.prototype.modPow=function(t,e){if(t=B(t),(e=B(e)).isZero())throw new Error("Cannot take modPow with modulus 0");var r=s[1],n=this.mod(e);for(t.isNegative()&&(t=t.multiply(s[-1]),n=n.modInv(e));t.isPositive();){if(n.isZero())return s[0];t.isOdd()&&(r=r.multiply(n).mod(e)),t=t.divide(2),n=n.square().mod(e)}return r},c.prototype.modPow=i.prototype.modPow=u.prototype.modPow,u.prototype.compareAbs=function(t){var e=B(t),r=this.value,n=e.value;return e.isSmall?1:S(r,n)},i.prototype.compareAbs=function(t){var e=B(t),r=Math.abs(this.value),n=e.value;return e.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},c.prototype.compareAbs=function(t){var e=this.value,r=B(t).value;return(e=e>=0?e:-e)===(r=r>=0?r:-r)?0:e>r?1:-1},u.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=B(t),r=this.value,n=e.value;return this.sign!==e.sign?e.sign?1:-1:e.isSmall?this.sign?-1:1:S(r,n)*(this.sign?-1:1)},u.prototype.compareTo=u.prototype.compare,i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=B(t),r=this.value,n=e.value;return e.isSmall?r==n?0:r>n?1:-1:r<0!==e.sign?r<0?-1:1:r<0?1:-1},i.prototype.compareTo=i.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=this.value,r=B(t).value;return e===r?0:e>r?1:-1},c.prototype.compareTo=c.prototype.compare,u.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=i.prototype.eq=i.prototype.equals=u.prototype.eq=u.prototype.equals,u.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=i.prototype.neq=i.prototype.notEquals=u.prototype.neq=u.prototype.notEquals,u.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=i.prototype.gt=i.prototype.greater=u.prototype.gt=u.prototype.greater,u.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=i.prototype.lt=i.prototype.lesser=u.prototype.lt=u.prototype.lesser,u.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals=u.prototype.geq=u.prototype.greaterOrEquals,u.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals=u.prototype.leq=u.prototype.lesserOrEquals,u.prototype.isEven=function(){return 0==(1&this.value[0])},i.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},u.prototype.isOdd=function(){return 1==(1&this.value[0])},i.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},u.prototype.isPositive=function(){return!this.sign},i.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=i.prototype.isPositive,u.prototype.isNegative=function(){return this.sign},i.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=i.prototype.isNegative,u.prototype.isUnit=function(){return!1},i.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},u.prototype.isZero=function(){return!1},i.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},u.prototype.isDivisibleBy=function(t){var e=B(t);return!e.isZero()&&(!!e.isUnit()||(0===e.compareAbs(2)?this.isEven():this.mod(e).isZero()))},c.prototype.isDivisibleBy=i.prototype.isDivisibleBy=u.prototype.isDivisibleBy,u.prototype.isPrime=function(t){var e=U(this);if(void 0!==e)return e;var r=this.abs(),n=r.bitLength();if(n<=64)return _(r,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),s=Math.ceil(!0===t?2*Math.pow(a,2):a),u=[],i=0;i<s;i++)u.push(o(i+2));return _(r,u)},c.prototype.isPrime=i.prototype.isPrime=u.prototype.isPrime,u.prototype.isProbablePrime=function(t,e){var r=U(this);if(void 0!==r)return r;for(var n=this.abs(),a=void 0===t?5:t,s=[],u=0;u<a;u++)s.push(o.randBetween(2,n.minus(2),e));return _(n,s)},c.prototype.isProbablePrime=i.prototype.isProbablePrime=u.prototype.isProbablePrime,u.prototype.modInv=function(t){for(var e,r,n,a=o.zero,s=o.one,u=B(t),i=this.abs();!i.isZero();)e=u.divide(i),r=a,n=u,a=s,u=i,s=r.subtract(e.multiply(s)),i=n.subtract(e.multiply(i));if(!u.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=i.prototype.modInv=u.prototype.modInv,u.prototype.next=function(){var t=this.value;return this.sign?L(t,1,this.sign):new u(m(t,1),this.sign)},i.prototype.next=function(){var t=this.value;return t+1<r?new i(t+1):new u(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},u.prototype.prev=function(){var t=this.value;return this.sign?new u(m(t,1),!0):L(t,1,this.sign)},i.prototype.prev=function(){var t=this.value;return t-1>-r?new i(t-1):new u(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=e;)x.push(2*x[x.length-1]);var I=x.length,N=x[I-1];function R(t){return Math.abs(t)<=e}function k(t,e,r){e=B(e);for(var n=t.isNegative(),a=e.isNegative(),s=n?t.not():t,u=a?e.not():e,i=0,c=0,l=null,f=null,p=[];!s.isZero()||!u.isZero();)i=(l=T(s,N))[1].toJSNumber(),n&&(i=N-1-i),c=(f=T(u,N))[1].toJSNumber(),a&&(c=N-1-c),s=l[0],u=f[0],p.push(r(i,c));for(var b=0!==r(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(N).add(o(p[v]));return b}u.prototype.shiftLeft=function(t){var e=B(t).toJSNumber();if(!R(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftRight(-e);var r=this;if(r.isZero())return r;for(;e>=I;)r=r.multiply(N),e-=I-1;return r.multiply(x[e])},c.prototype.shiftLeft=i.prototype.shiftLeft=u.prototype.shiftLeft,u.prototype.shiftRight=function(t){var e,r=B(t).toJSNumber();if(!R(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=I;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(e=T(n,N))[1].isNegative()?e[0].prev():e[0],r-=I-1}return(e=T(n,x[r]))[1].isNegative()?e[0].prev():e[0]},c.prototype.shiftRight=i.prototype.shiftRight=u.prototype.shiftRight,u.prototype.not=function(){return this.negate().prev()},c.prototype.not=i.prototype.not=u.prototype.not,u.prototype.and=function(t){return k(this,t,(function(t,e){return t&e}))},c.prototype.and=i.prototype.and=u.prototype.and,u.prototype.or=function(t){return k(this,t,(function(t,e){return t|e}))},c.prototype.or=i.prototype.or=u.prototype.or,u.prototype.xor=function(t){return k(this,t,(function(t,e){return t^e}))},c.prototype.xor=i.prototype.xor=u.prototype.xor;function V(t){var r=t.value,n="number"==typeof r?r|1<<30:"bigint"==typeof r?r|BigInt(1<<30):r[0]+r[1]*e|1073758208;return n&-n}function M(t,e){return t=B(t),e=B(e),t.greater(e)?t:e}function q(t,e){return t=B(t),e=B(e),t.lesser(e)?t:e}function P(t,e){if(t=B(t).abs(),e=B(e).abs(),t.equals(e))return t;if(t.isZero())return e;if(e.isZero())return t;for(var r,n,o=s[1];t.isEven()&&e.isEven();)r=q(V(t),V(e)),t=t.divide(r),e=e.divide(r),o=o.multiply(r);for(;t.isEven();)t=t.divide(V(t));do{for(;e.isEven();)e=e.divide(V(e));t.greater(e)&&(n=e,e=t,t=n),e=e.subtract(t)}while(!e.isZero());return o.isUnit()?t:t.multiply(o)}u.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(e,r){if(r.compareTo(e)<=0){var n=t(e,r.square(r)),a=n.p,s=n.e,u=a.multiply(r);return u.compareTo(e)<=0?{p:u,e:2*s+1}:{p:a,e:2*s}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=i.prototype.bitLength=u.prototype.bitLength;var D=function(t,e,r,n){r=r||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),r=r.toLowerCase());var o,a=t.length,s=Math.abs(e),u={};for(o=0;o<r.length;o++)u[r[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in u&&u[l]>=s)){if("1"===l&&1===s)continue;throw new Error(l+" is not a valid digit in base "+e+".")}}e=B(e);var i=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in u)i.push(B(u[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);i.push(B(t.slice(f+1,o)))}}return Q(i,e,c)};function Q(t,e,r){var n,o=s[0],a=s[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(e);return r?o.negate():o}function F(t,e){if((e=o(e)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(e.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var n=!1;if(t.isNegative()&&e.isPositive()&&(n=!0,t=t.abs()),e.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,s=[],u=t;u.isNegative()||u.compareAbs(e)>=0;){a=u.divmod(e),u=a.quotient;var i=a.remainder;i.isNegative()&&(i=e.minus(i).abs(),u=u.next()),s.push(i.toJSNumber())}return s.push(u.toJSNumber()),{value:s.reverse(),isNegative:n}}function G(t,e,r){var n=F(t,e);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,e){return t<(e=e||"0123456789abcdefghijklmnopqrstuvwxyz").length?e[t]:"<"+t+">"}(t,r)})).join("")}function $(t){if(l(+t)){var e=+t;if(e===y(e))return a?new c(BigInt(e)):new i(e);throw new Error("Invalid integer: "+t)}var r="-"===t[0];r&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==y(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var s=n[0],f=s.indexOf(".");if(f>=0&&(o-=s.length-f-1,s=s.slice(0,f)+s.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=s+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(r?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new u(p,r)}function B(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==y(t))throw new Error(t+" is not an integer.");return new i(t)}return $(t.toString())}(t):"string"==typeof t?$(t):"bigint"==typeof t?new c(t):t}u.prototype.toArray=function(t){return F(this,t)},i.prototype.toArray=function(t){return F(this,t)},c.prototype.toArray=function(t){return F(this,t)},u.prototype.toString=function(t,e){if(void 0===t&&(t=10),10!==t||e)return G(this,t,e);for(var r,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)r=String(n[o]),a+="0000000".slice(r.length)+r;return(this.sign?"-":"")+a},i.prototype.toString=function(t,e){return void 0===t&&(t=10),10!=t||e?G(this,t,e):String(this.value)},c.prototype.toString=i.prototype.toString,c.prototype.toJSON=u.prototype.toJSON=i.prototype.toJSON=function(){return this.toString()},u.prototype.valueOf=function(){return parseInt(this.toString(),10)},u.prototype.toJSNumber=u.prototype.valueOf,i.prototype.valueOf=function(){return this.value},i.prototype.toJSNumber=i.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var H=0;H<1e3;H++)s[H]=B(H),H>0&&(s[-H]=B(-H));return s.one=s[1],s.zero=s[0],s.minusOne=s[-1],s.max=M,s.min=q,s.gcd=P,s.lcm=function(t,e){return t=B(t).abs(),e=B(e).abs(),t.divide(P(t,e)).multiply(e)},s.isInstance=function(t){return t instanceof u||t instanceof i||t instanceof c},s.randBetween=function(t,r,n){t=B(t),r=B(r);var o=n||Math.random,a=q(t,r),u=M(t,r).subtract(a).add(1);if(u.isSmall)return a.add(Math.floor(o()*u));for(var i=F(u,e).value,c=[],l=!0,f=0;f<i.length;f++){var p=l?i[f]+(f+1<i.length?i[f+1]/e:0):e,b=y(o()*p);c.push(b),b<i[f]&&(l=!1)}return a.add(s.fromArray(c,e,!1))},s.fromArray=function(t,e,r){return Q(t.map(B),B(e||10),r)},s}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(e,r,e,t))||(t.exports=n)}).call(this,r(30)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=redshift.umd.js.map