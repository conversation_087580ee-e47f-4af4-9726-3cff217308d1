!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=26)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return A})),e.d(r,"autoIncrementToSQL",(function(){return U})),e.d(r,"columnOrderListToSQL",(function(){return x})),e.d(r,"commonKeywordArgsToSQL",(function(){return _})),e.d(r,"commonOptionConnector",(function(){return i})),e.d(r,"connector",(function(){return c})),e.d(r,"commonTypeValue",(function(){return O})),e.d(r,"commentToSQL",(function(){return T})),e.d(r,"createBinaryExpr",(function(){return f})),e.d(r,"createValueExpr",(function(){return l})),e.d(r,"dataTypeToSQL",(function(){return g})),e.d(r,"DEFAULT_OPT",(function(){return u})),e.d(r,"escape",(function(){return p})),e.d(r,"literalToSQL",(function(){return w})),e.d(r,"columnIdentifierToSql",(function(){return d})),e.d(r,"getParserOpt",(function(){return b})),e.d(r,"identifierToSql",(function(){return y})),e.d(r,"onPartitionsToSQL",(function(){return C})),e.d(r,"replaceParams",(function(){return L})),e.d(r,"returningToSQL",(function(){return S})),e.d(r,"hasVal",(function(){return j})),e.d(r,"setParserOpt",(function(){return v})),e.d(r,"toUpper",(function(){return m})),e.d(r,"topToSQL",(function(){return h})),e.d(r,"triggerEventToSQL",(function(){return E}));var n=e(2),o=e(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u={database:"transactsql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},s=u;function i(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function c(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function l(t){var r=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function f(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:l(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(e[0]),l(e[1])]},n):(n.right=e.type?e:l(e),n)}function p(t){return t}function b(){return s}function v(t){s=t}function h(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(t){var r=b().database;if(t)switch(r&&r.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function y(t,r){var e=b().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function j(t){return t}function w(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,u=t.suffix,s=t.value,i="object"===a(t)?s:t;switch(e){case"backticks_quote_string":i="`".concat(s,"`");break;case"string":i="'".concat(s,"'");break;case"regex_string":i='r"'.concat(s,'"');break;case"hex_string":i="X'".concat(s,"'");break;case"full_hex_string":i="0x".concat(s);break;case"natural_string":i="N'".concat(s,"'");break;case"bit_string":i="b'".concat(s,"'");break;case"double_quote_string":i='"'.concat(s,'"');break;case"single_quote_string":i="'".concat(s,"'");break;case"boolean":case"bool":i=s?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(r||":").concat(s),r=null;break;case"origin":i=s.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(e.toUpperCase()," '").concat(s,"'");break;case"var_string":i="N'".concat(s,"'");break;case"unicode_string":i="U&'".concat(s,"'")}var c=[];return r&&c.push(m(r)),c.push(i),u&&("string"==typeof u&&c.push(u),"object"===a(u)&&(u.collate?c.push(Object(o.a)(u.collate)):c.push(w(u)))),i=c.join(" "),n?"(".concat(i,")"):i}}function O(t){if(!t)return[];var r=t.type,e=t.symbol,n=t.value;return[r.toUpperCase(),e,"string"==typeof n?n.toUpperCase():w(n)].filter(j)}function L(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===a(e)&&null!==e})).forEach((function(n){var o=r[n];if("object"!==a(o)||"param"!==o.type)return t(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return r[n]=l(e[o.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function C(t){var r=t.type,e=t.partitions;return[m(r),"(".concat(e.map((function(t){if("range"!==t.type)return w(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(w(r)," ").concat(m(n)," ").concat(w(e))})).join(", "),")")].join(" ")}function g(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,a=t.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),a&&a.length&&(u+=" ".concat(a.join(" "))),"".concat(r).concat(u)}function A(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=m(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(t){return[t.field_name,A(t.field_type)].filter(j).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function T(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(w(o)),r.join(" ")}}function E(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[m(r)];if(e){var a=e.keyword,u=e.columns;o.push(m(a),u.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function S(t){return t?["RETURNING",t.columns.map(n.h).filter(j).join(", ")].join(" "):""}function _(t){return t?[m(t.keyword),m(t.args)]:[]}function U(t){if(t){if("string"==typeof t){var r=b().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,u=m(e);return a&&(u+="(".concat(w(n),", ").concat(w(o),")")),u}}function x(t){if(t)return t.map(n.e).filter(j).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return C})),e.d(r,"b",(function(){return g})),e.d(r,"d",(function(){return L})),e.d(r,"c",(function(){return A}));var n=e(0),o=e(9),a=e(13);var u=e(22),s=e(21);var i=e(11),c=e(2),l=e(6),f=e(18);var p=e(7),b=e(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(c.i)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(r),"]");var u=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return e?"[".concat(u,"]"):a?"(".concat(u,")"):u}(t);default:return""}}function d(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===v(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(h(t)),o.filter(n.hasVal).join("")}var y=e(3),m=e(5),j=e(20);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var O={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,u=t.within_group_orderby,s=C(r.expr);s=Array.isArray(s)?s.join(", "):s;var i=t.name,c=Object(a.a)(o);r.distinct&&(s=["DISTINCT",s].join(" ")),r.separator&&r.separator.delimiter&&(s=[s,Object(n.literalToSQL)(r.separator.delimiter)].join("".concat(r.separator.symbol," "))),r.separator&&r.separator.expr&&(s=[s,C(r.separator.expr)].join(" ")),r.orderby&&(s=[s,A(r.orderby,"order by")].join(" ")),r.separator&&r.separator.value&&(s=[s,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=u?"WITHIN GROUP (".concat(A(u,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(C(e.where),")"):"";return["".concat(i,"(").concat(s,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:j.c,array:d,assign:u.a,binary_expr:s.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(C(n));for(var a=0,u=e.length;a<u;++a)r.push(e[a].type.toUpperCase()),e[a].cond&&(r.push(C(e[a].cond)),r.push("THEN")),r.push(C(e[a].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:l.c,collate:i.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:d,tablefunc:l.j,tables:y.c,unnest:y.d,window:j.b};function L(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,a=t.members,u=t.quoted,s=t.suffix,i=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(e||"").concat(c);return s&&(l+=s),i.push(l),[u,i.join(" "),u].filter(n.hasVal).join("")}function C(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,a=Object.keys(e);o<a.length;o++){var u=a[o];r[u]=e[u]}}var s=r.type;return"expr"===s?C(r.expr):O[s]?O[s](r):Object(n.literalToSQL)(r)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function A(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,e.join(", "))}O.var=L,O.expr_list=function(t){var r=g(t.value),e=t.parentheses,n=t.separator;if(!e&&!n)return r;var o=n||", ",a=r.join(o);return e?"(".concat(a,")"):a},O.select=function(t){var r="object"===w(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(r,")"):r},O.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",a="".concat(r).concat(o).concat(C(n));return e?"(".concat(a,")"):a},O.map_object=function(t){var r=t.keyword,e=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(r),"[".concat(e,"]")].join("")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"c",(function(){return w})),e.d(r,"f",(function(){return h})),e.d(r,"h",(function(){return C})),e.d(r,"i",(function(){return A})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return b})),e.d(r,"e",(function(){return j})),e.d(r,"g",(function(){return y})),e.d(r,"j",(function(){return L})),e.d(r,"k",(function(){return g}));var n=e(11),o=e(19),a=e(1),u=e(6),s=e(3),i=e(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function b(t,r){if("string"==typeof t)return Object(i.identifierToSql)(t,r);var e=t.expr,n=t.offset,o=t.suffix,u=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(a.a)(e),u,o].filter(i.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var r,e=[],n=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=f(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){s=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}(t);try{for(n.s();!(r=n.n()).done;){var o=r.value,a=o.brackets?"[".concat(Object(i.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(i.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(i.literalToSQL)(o.property))),e.push(a)}}catch(t){n.e(t)}finally{n.f()}return e.join("")}function h(t){var r=t.array_index,e=t.as,o=t.column,u=t.collate,s=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,h=t.options,d=t.schema,y=t.table,m=t.parentheses,j=t.suffix,w=t.order_by,O=t.subFields,L=void 0===O?[]:O,C="*"===o?"*":b(o,c),g=[s,d,y].filter(i.hasVal).map((function(t){return"".concat("string"==typeof t?Object(i.identifierToSql)(t):Object(a.a)(t))})),A=g[0];if(A){for(var T=1;T<g.length;++T)A="".concat(A).concat(p[T]||".").concat(g[T]);C="".concat(A).concat(p[T]||".").concat(C)}var E=[C=["".concat(C).concat(v(r))].concat(l(L)).join("."),Object(n.a)(u),Object(a.a)(h),Object(i.commonOptionConnector)("AS",a.a,e)];E.push("string"==typeof j?Object(i.toUpper)(j):Object(a.a)(j)),E.push(Object(i.toUpper)(w));var S=E.filter(i.hasVal).join(" ");return m?"(".concat(S,")"):S}function d(t){if(t){var r=t.dataType,e=t.length,n=t.suffix,o=t.scale,s=t.expr,c=null!=e,l=Object(i.dataTypeToSQL)({dataType:r,length:e,suffix:n,scale:o,parentheses:c});if(s&&(l+=Object(a.a)(s)),t.array){var f=Object(u.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function y(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,o=t.match,u=t.table,c=t.on_action;return r.push(Object(i.toUpper)(n)),r.push(Object(s.c)(u)),r.push(e&&"(".concat(e.map((function(t){return Object(a.a)(t)})).join(", "),")")),r.push(Object(i.toUpper)(o)),c.map((function(t){return r.push(Object(i.toUpper)(t.type),Object(a.a)(t.value))})),r.filter(i.hasVal)}function m(t){var r=[],e=t.nullable,n=t.character_set,u=t.check,s=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,h=t.generated,d=t.auto_increment,m=t.unique,j=t.primary_key,w=t.column_format,O=t.reference_definition,L=[Object(i.toUpper)(e&&e.action),Object(i.toUpper)(e&&e.value)].filter(i.hasVal).join(" ");if(h||r.push(L),v){var C=v.type,g=v.value;r.push(C.toUpperCase(),Object(a.a)(g))}var A=Object(i.getParserOpt)().database;return c&&r.push(Object(i.toUpper)(c.keyword),Object(i.literalToSQL)(c.constraint)),r.push(Object(o.a)(u)),r.push(function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(h)),h&&r.push(L),r.push(Object(i.autoIncrementToSQL)(d),Object(i.toUpper)(j),Object(i.toUpper)(m),Object(i.commentToSQL)(s)),r.push.apply(r,l(Object(i.commonTypeValue)(n))),"sqlite"!==A.toLowerCase()&&r.push(Object(a.a)(f)),r.push.apply(r,l(Object(i.commonTypeValue)(w))),r.push.apply(r,l(Object(i.commonTypeValue)(p))),r.push.apply(r,l(y(O))),r.push(Object(i.commonOptionConnector)("USING",a.a,b)),r.filter(i.hasVal).join(" ")}function j(t){var r=t.column,e=t.collate,n=t.nulls,o=t.opclass,u=t.order_by,s="string"==typeof r?{type:"column_ref",table:t.table,column:r}:t;return s.collate=null,[Object(a.a)(s),Object(a.a)(e),o,Object(i.toUpper)(u),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function w(t){var r=[],e=h(t.column),n=d(t.definition);return r.push(e),r.push(n),r.push(m(t)),r.filter(i.hasVal).join(" ")}function O(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function L(t){var r=t.against,e=t.as,n=t.columns,o=t.match,u=t.mode;return[[Object(i.toUpper)(o),"(".concat(n.map((function(t){return h(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(r),["(",Object(a.a)(t.expr),u&&" ".concat(Object(i.literalToSQL)(u)),")"].filter(i.hasVal).join("")].join(" "),O(e)].filter(i.hasVal).join(" ")}function C(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(u.c)(t);r&&(e.isDual=r);var o=Object(a.a)(e),s=t.expr_list;if(s){var c=[o],l=s.map((function(t){return C(t,r)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(v(e.array_index))),[o,O(t.as)].filter(i.hasVal).join(" ")}function g(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}function A(t,r){if(!t||"*"===t)return t;var e=g(r);return t.map((function(t){return C(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"c",(function(){return y})),e.d(r,"a",(function(){return m})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return f}));var n=e(21),o=e(2),a=e(1),u=e(17),s=e(18),i=e(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(i.toUpper)(r),"(").concat(n&&Object(a.a)(n)||"",")"),Object(i.commonOptionConnector)("AS","string"==typeof e?i.identifierToSql:a.a,e),Object(i.commonOptionConnector)(Object(i.toUpper)(o&&o.keyword),i.identifierToSql,o&&o.as)].filter(i.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,u=t.expr,s=t.in_expr,c=t.type,l=[Object(a.a)(u),"FOR",Object(o.f)(e),Object(n.a)(s)],f=["".concat(Object(i.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(i.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function b(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,u=t.parentheses,s=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(i.toUpper)(r),"(".concat(Object(i.identifierToSql)(n)),"(".concat(o.map(a.a).filter(i.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(i.toUpper)(r),"=",Object(a.a)(e));break;case"index":c.push(Object(i.toUpper)(s),Object(i.toUpper)(r),u?"(".concat(e.map(i.identifierToSql).join(", "),")"):"= ".concat(Object(i.identifierToSql)(e)));break;default:c.push(Object(a.a)(e))}return c.filter(i.hasVal).join(" ")}}function v(t,r){var e=t.name,n=t.symbol;return[Object(i.toUpper)(e),n,r].filter(i.hasVal).join(" ")}function h(t){var r=[];switch(t.keyword){case"as":r.push("AS","OF",Object(a.a)(t.of));break;case"from_to":r.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":r.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":r.push("CONTAINED","IN",Object(a.a)(t.in))}return r.filter(i.hasVal).join(" ")}function d(t){if("UNNEST"===Object(i.toUpper)(t.type))return f(t);var r,e,n,c,l=t.table,d=t.db,y=t.as,m=t.expr,j=t.operator,w=t.prefix,O=t.schema,L=t.server,C=t.suffix,g=t.tablesample,A=t.temporal_table,T=t.table_hint,E=Object(i.identifierToSql)(L),S=Object(i.identifierToSql)(d),_=Object(i.identifierToSql)(O),U=l&&Object(i.identifierToSql)(l);if(m)switch(m.type){case"values":var x=m.parentheses,I=m.values,k=m.prefix,N=[x&&"(","",x&&")"],R=Object(u.b)(I);k&&(R=R.split("(").slice(1).map((function(t){return"".concat(Object(i.toUpper)(k),"(").concat(t)})).join("")),N[1]="VALUES ".concat(R),U=N.filter(i.hasVal).join("");break;case"tumble":U=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.offset,a=t.size,u=[Object(i.identifierToSql)(r.expr.db),Object(i.identifierToSql)(r.expr.schema),Object(i.identifierToSql)(r.expr.table)].filter(i.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(e.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(r,u)),v(e,c)],f=v(a,Object(s.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(s.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(i.hasVal).join(", ")}(m);break;case"generator":e=(r=m).keyword,n=r.type,c=r.generators.map((function(t){return Object(i.commonTypeValue)(t).join(" ")})).join(", "),U="".concat(Object(i.toUpper)(e),"(").concat(Object(i.toUpper)(n),"(").concat(c,"))");break;default:U=Object(a.a)(m)}var V=[[E,S,_,U=[Object(i.toUpper)(w),U,Object(i.toUpper)(C)].filter(i.hasVal).join(" ")].filter(i.hasVal).join(".")];if(g){var M=["TABLESAMPLE",Object(a.a)(g.expr),Object(i.literalToSQL)(g.repeatable)].filter(i.hasVal).join(" ");V.push(M)}V.push(function(t){if(t){var r=t.keyword,e=t.expr;return[Object(i.toUpper)(r),h(e)].filter(i.hasVal).join(" ")}}(A),Object(i.commonOptionConnector)("AS","string"==typeof y?i.identifierToSql:a.a,y),p(j)),T&&V.push(Object(i.toUpper)(T.keyword),"(".concat(T.expr.map(b).filter(i.hasVal).join(", "),")"));var q=V.filter(i.hasVal).join(" ");return t.parentheses?"(".concat(q,")"):q}function y(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=t.joins,o=y(r);if(e){for(var u=[],s=[],l=!0===e?1:e.length,f=0;f++<l;)u.push("("),s.push(")");var p=n&&n.length>0?y([""].concat(c(n))):"";return u.join("")+o+s.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(d(b));for(var h=1;h<t.length;++h){var m=t[h],j=m.on,w=m.using,O=m.join,L=[];L.push(O?" ".concat(Object(i.toUpper)(O)):","),L.push(d(m)),L.push(Object(i.commonOptionConnector)("ON",a.a,j)),w&&L.push("USING (".concat(w.map(i.literalToSQL).join(", "),")")),v.push(L.filter(i.hasVal).join(" "))}return v.filter(i.hasVal).join("")}function m(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var u=Object(i.literalToSQL)(n);switch(r){case"partition by":case"default collate":u=Object(a.a)(n);break;case"options":u="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(a.a).join(", ")}return o.push(u),o.filter(i.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return h})),e.d(r,"c",(function(){return g})),e.d(r,"d",(function(){return A})),e.d(r,"e",(function(){return d})),e.d(r,"f",(function(){return y})),e.d(r,"g",(function(){return m})),e.d(r,"h",(function(){return S})),e.d(r,"i",(function(){return E})),e.d(r,"j",(function(){return T})),e.d(r,"l",(function(){return j})),e.d(r,"m",(function(){return w})),e.d(r,"o",(function(){return O})),e.d(r,"n",(function(){return L})),e.d(r,"k",(function(){return C}));var n=e(2),o=e(14),a=e(0),u=e(1),s=e(3),i=e(16),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){s=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(u.a)(t.expr);return"".concat("CALL"," ").concat(r)}function h(t){var r=t.type,e=t.keyword,o=t.name,i=t.prefix,c=t.suffix,l=[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(a.toUpper)(i)];switch(e){case"table":l.push(Object(s.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(s.c)(o),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(s.b)(t.table)]:[]),[t.options&&t.options.map(u.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(u.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function d(t){var r=t.type,e=t.table,n=Object(a.toUpper)(r);return"".concat(n," ").concat(Object(a.identifierToSql)(e))}function y(t){var r=t.type,e=t.name,n=t.args,o=[Object(a.toUpper)(r)],s=[e];return n&&s.push("(".concat(Object(u.a)(n).join(", "),")")),o.push(s.join("")),o.filter(a.hasVal).join(" ")}function m(t){var r=t.type,e=t.label,n=t.target,o=t.query,u=t.stmts;return[e,Object(a.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(u),"END LOOP",e].filter(a.hasVal).join(" ")}function j(t){var r=t.type,e=t.level,n=t.raise,o=t.using,s=[Object(a.toUpper)(r),Object(a.toUpper)(e)];return n&&s.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(u.a)(t)})).join(", ")),o&&s.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(u.a)(t)})).join(", ")),s.filter(a.hasVal).join(" ")}function w(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var a,u=l(e);try{for(u.s();!(a=u.n()).done;){var i=a.value.map(s.b);n.push(i.join(" TO "))}}catch(t){u.e(t)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))}function O(t){var r=t.type,e=t.db,n=Object(a.toUpper)(r),o=Object(a.identifierToSql)(e);return"".concat(n," ").concat(o)}function L(t){var r=t.type,e=t.expr,n=t.keyword,o=Object(a.toUpper)(r),s=e.map(u.a).join(", ");return[o,Object(a.toUpper)(n),s].filter(a.hasVal).join(" ")}function C(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(a.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var u,i=[],c=l(n);try{var p=function(){var t=u.value,r=t.table,e=t.lock_type,n=[Object(s.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(e[t])})).filter(a.hasVal).join(" "))}i.push(n.join(" "))};for(c.s();!(u=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[i.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(u.a)(n)].filter(a.hasVal).join(" ")}function A(t){var r=t.type,e=t.declare,s=t.symbol,i=[Object(a.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,s=t.as,i=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[r,e].filter(a.hasVal).join(""),Object(a.toUpper)(s),Object(a.toUpper)(i)];switch(b){case"variable":v.push(Object(n.b)(c),Object(u.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(u.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(s," "));return i.push(c),i.join(" ")}function T(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,s=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(u.a)(r),Object(a.literalToSQL)(s),"".concat(Object(i.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(u.a)(t.boolean_expr),"THEN",Object(i.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(i.a)(e.ast||e)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function E(t){var r=t.name,e=t.host,n=[Object(a.literalToSQL)(r)];return e&&n.push("@",Object(a.literalToSQL)(e)),n.join("")}function S(t){var r=t.type,e=t.grant_option_for,o=t.keyword,s=t.objects,i=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(r),Object(a.literalToSQL)(e)],b=s.map((function(t){var r=t.priv,e=t.columns,o=[Object(u.a)(r)];return e&&o.push("(".concat(e.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),i)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(i.object_type),i.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(E(i))}return p.push(Object(a.toUpper)(c),l.map(E).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return O})),e.d(r,"a",(function(){return L}));var n=e(9),o=e(1),a=e(3),u=e(0);var s=e(14),i=e(2);function c(t){var r=t.name,e=t.type;switch(e){case"table":case"view":var n=[Object(u.identifierToSql)(r.db),Object(u.identifierToSql)(r.table)].filter(u.hasVal).join(".");return"".concat(Object(u.toUpper)(e)," ").concat(n);case"column":return"COLUMN ".concat(Object(i.f)(r));default:return"".concat(Object(u.toUpper)(e)," ").concat(Object(u.literalToSQL)(r))}}function l(t){var r=t.keyword,e=t.expr;return[Object(u.toUpper)(r),Object(u.literalToSQL)(e)].filter(u.hasVal).join(" ")}var f=e(7);var p=e(8),b=e(15);var v=e(12),h=e(17),d=e(4);function y(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(u.hasVal).join(" ")}var m=e(22);var j=e(23),w={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(u.toUpper)(r),Object(a.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,a=t.as,s=t.schema;return[Object(u.toUpper)(r),Object(u.toUpper)(e),Object(o.a)(n),Object(u.toUpper)(a),Object(u.identifierToSql)(s)].filter(u.hasVal).join(" ")},create:s.b,comment:function(t){var r=t.expr,e=t.keyword,n=t.target,o=t.type;return[Object(u.toUpper)(o),Object(u.toUpper)(e),c(n),l(r)].filter(u.hasVal).join(" ")},select:f.a,deallocate:d.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,s=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,h=[Object(b.a)(l),"DELETE"],d=Object(i.i)(r,e);return h.push(d),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||h.push(Object(a.c)(n))),h.push(Object(u.commonOptionConnector)("FROM",a.c,e)),h.push(Object(u.commonOptionConnector)("WHERE",o.a,s)),h.push(Object(o.c)(c,"order by")),h.push(Object(p.a)(f)),h.push(Object(u.returningToSQL)(v)),h.filter(u.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(u.toUpper)(r),Object(a.b)(e),(n||[]).map(y).filter(u.hasVal).join(", ")].filter(u.hasVal).join(" ")},execute:d.f,explain:function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(f.a)(e)].join(" ")},for:d.g,update:v.b,if:d.j,insert:h.a,drop:d.b,truncate:d.b,replace:h.a,declare:d.d,use:d.o,rename:d.m,call:d.a,desc:d.e,set:d.n,lock:d.k,unlock:d.k,show:j.a,grant:d.h,revoke:d.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(m.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:d.l,transaction:function(t){var r=t.expr,e=r.action,n=r.keyword,o=r.modes,a=[Object(u.literalToSQL)(e),Object(u.toUpper)(n)];return o&&a.push(o.map(u.literalToSQL).join(", ")),a.filter(u.hasVal).join(" ")}};function O(t){if(!t)return"";for(var r=w[t.type],e=t,n=e._parentheses,a=e._orderby,s=e._limit,i=[n&&"(",r(t)];t._next;){var c=w[t._next.type],l=Object(u.toUpper)(t.set_op);i.push(l,c(t._next)),t=t._next}return i.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(s)),i.filter(u.hasVal).join(" ")}function L(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],a=O(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),r.push(a)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s})),e.d(r,"b",(function(){return i})),e.d(r,"c",(function(){return c})),e.d(r,"d",(function(){return l})),e.d(r,"e",(function(){return p})),e.d(r,"f",(function(){return b})),e.d(r,"g",(function(){return v})),e.d(r,"h",(function(){return f})),e.d(r,"i",(function(){return d})),e.d(r,"j",(function(){return h}));var n=e(2),o=e(1),a=e(0),u=e(13);function s(t){var r=t.args,e=t.type,n=t.over,s=r.expr,i=r.having,c="".concat(Object(a.toUpper)(e),"(").concat(Object(o.a)(s));return i&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(i.prefix)," ").concat(Object(o.a)(i.expr))),[c="".concat(c,")"),Object(u.a)(n)].filter(a.hasVal).join(" ")}function i(t){if(!t||!t.array)return"";var r=t.array.keyword;if(r)return Object(a.toUpper)(r);for(var e=t.array,n=e.dimension,o=e.length,u=[],s=0;s<n;s++)u.push("["),o&&o[s]&&u.push(Object(a.literalToSQL)(o[s])),u.push("]");return u.join("")}function c(t){for(var r=t.target,e=t.expr,u=t.keyword,s=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:e,offset:l}),b=[],v=0,h=r.length;v<h;++v){var d=r[v],y=d.angle_brackets,m=d.length,j=d.dataType,w=d.parentheses,O=d.quoted,L=d.scale,C=d.suffix,g=d.expr,A=g?Object(o.a)(g):"";null!=m&&(A=L?"".concat(m,", ").concat(L):m),w&&(A="(".concat(A,")")),y&&(A="<".concat(A,">")),C&&C.length&&(A+=" ".concat(C.map(a.literalToSQL).join(" ")));var T="::",E="",S=[];"as"===s&&(0===v&&(p="".concat(Object(a.toUpper)(u),"(").concat(p)),E=")",T=" ".concat(s.toUpperCase()," ")),0===v&&S.push(p);var _=i(d);S.push(T,O,j,O,_,A,E),b.push(S.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var U=b.filter(a.hasVal).join("");return f?"(".concat(U,")"):U}function l(t){var r=t.args,e=t.type,n=r.field,u=r.cast_type,s=r.source,i=["".concat(Object(a.toUpper)(e),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(u),Object(o.a)(s)];return"".concat(i.filter(a.hasVal).join(" "),")")}function f(t){var r=t.expr,e=r.key,n=r.value,u=r.on,s=[Object(o.a)(e),"VALUE",Object(o.a)(n)];return u&&s.push("ON","NULL",Object(o.a)(u)),s.filter(a.hasVal).join(" ")}function p(t){var r=t.args,e=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,n=t.value;return[Object(a.toUpper)(r),e,Object(o.a)(n)].filter(a.hasVal).join(" ")}(r[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(e),"(").concat(n,")")}function b(t){var r=t.value,e=r.name,n=r.symbol,u=r.expr;return[e,n,Object(o.a)(u)].filter(a.hasVal).join(" ")}function v(t){var r=t.args,e=t.array_index,s=t.name,i=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(u.a)(f),v=function(t){if(!t)return"";var r=t.type,e=t.keyword,n=t.orderby;return[Object(a.toUpper)(r),Object(a.toUpper)(e),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),h=Object(o.a)(p),d=[Object(a.literalToSQL)(s.schema),s.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!r)return[d,v,b].filter(a.hasVal).join(" ");var y=t.separator||", ";"TRIM"===Object(a.toUpper)(d)&&(y=" ");var m=[d];m.push(!1===i?" ":"(");var j=Object(o.a)(r);if(Array.isArray(y)){for(var w=j[0],O=1,L=j.length;O<L;++O)w=[w,j[O]].join(" ".concat(Object(o.a)(y[O-1])," "));m.push(w)}else m.push(j.join(y));return!1!==i&&m.push(")"),m.push(Object(n.a)(e)),m=[m.join(""),h].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function h(t){var r=t.as,e=t.name,n=t.args,u=[Object(a.literalToSQL)(e.schema),e.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(u,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(r)].join(" ")}function d(t){var r=t.args,e=t.expr,n=r.value,a=r.parentheses,u=n.map(o.a).join(", ");return[a?"(".concat(u,")"):u,"->",Object(o.a)(e)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f}));var n=e(1),o=e(2),a=e(8),u=e(15),s=e(3),i=e(0),c=e(11);function l(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],a=Object(i.toUpper)(r);switch(a){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(a,"string"==typeof e?Object(i.identifierToSql)(e):Object(n.a)(e))}return o.filter(i.hasVal).join(" ")}}function f(t){var r=t.as_struct_val,e=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,h=t.for_sys_time_as_of,d=void 0===h?{}:h,y=t.locking_read,m=t.groupby,j=t.having,w=t.into,O=void 0===w?{}:w,L=t.isolation,C=t.limit,g=t.options,A=t.orderby,T=t.parentheses_symbol,E=t.qualify,S=t.top,_=t.window,U=t.with,x=t.where,I=[Object(u.a)(U),"SELECT",Object(i.toUpper)(r)];Array.isArray(g)&&I.push(g.join(" ")),I.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,o=[Object(i.toUpper)(r)];return e&&o.push("(".concat(e.map(n.a).join(", "),")")),o.filter(i.hasVal).join(" ")}}(p),Object(i.topToSQL)(S),Object(o.i)(e,v));var k=O.position,N="";k&&(N=Object(i.commonOptionConnector)("INTO",l,O)),"column"===k&&I.push(N),I.push(Object(i.commonOptionConnector)("FROM",s.c,v)),"from"===k&&I.push(N);var R=d||{},V=R.keyword,M=R.expr;I.push(Object(i.commonOptionConnector)(V,n.a,M)),I.push(Object(i.commonOptionConnector)("WHERE",n.a,x)),m&&(I.push(Object(i.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),I.push(Object(n.b)(m.modifiers).join(", "))),I.push(Object(i.commonOptionConnector)("HAVING",n.a,j)),I.push(Object(i.commonOptionConnector)("QUALIFY",n.a,E)),I.push(Object(i.commonOptionConnector)("WINDOW",n.a,_)),I.push(Object(n.c)(A,"order by")),I.push(Object(c.a)(f)),I.push(Object(a.a)(C)),L&&I.push(Object(i.commonOptionConnector)(L.keyword,i.literalToSQL,L.expr)),I.push(Object(i.toUpper)(y)),"end"===k&&I.push(N),I.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,a=[Object(i.toUpper)(o),Object(i.toUpper)(e)];return r?"".concat(a.join(" "),"(").concat(Object(n.a)(r),")"):a.join(" ")}}(b));var q=I.filter(i.hasVal).join(" ");return T?"(".concat(q,")"):q}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function s(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function i(t){return t?t.fetch?(e=(r=t).fetch,u=r.offset,[].concat(a(s(u)),a(s(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,u}},function(t,r,e){"use strict";e.d(r,"a",(function(){return p})),e.d(r,"c",(function(){return b})),e.d(r,"b",(function(){return f}));var n=e(2),o=e(14),a=e(10),u=e(3),s=e(1),i=e(7),c=e(0);function l(t,r){switch(t){case"add":var e=r.map((function(t){var r=t.name,e=t.value;return["PARTITION",Object(c.literalToSQL)(r),"VALUES",Object(c.toUpper)(e.type),"(".concat(Object(c.literalToSQL)(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Object(n.i)(r)}}function f(t){if(!t)return"";var r=t.action,e=t.create_definitions,u=t.if_not_exists,s=t.keyword,i=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,h=t.suffix,d="",y=[];switch(b){case"column":y=[Object(n.c)(t)];break;case"index":y=Object(a.c)(t),d=t[b];break;case"table":case"schema":d=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":d=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":d=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":d=Object(c.identifierToSql)(t[b]),y=[Object(o.a)(e)];break;case"partition":y=[l(r,t.partitions)];break;case"key":d=Object(c.identifierToSql)(t[b]);break;default:d=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(r),Object(c.toUpper)(s),Object(c.toUpper)(u),Object(c.toUpper)(i),f&&Object(n.f)(f),Object(c.toUpper)(p),d&&d.trim(),y.filter(c.hasVal).join(" ")];return h&&m.push(Object(c.toUpper)(h.keyword),h.expr&&Object(n.f)(h.expr)),m.filter(c.hasVal).join(" ")}function p(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(s.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function b(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type,u=r.expr,s=r.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(u.map(p).join(", ")).concat(s?[" ORDER","BY",s.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.if_exists,o=t.prefix,a=t.expr,i=void 0===a?[]:a,l=Object(c.toUpper)(r),f=Object(u.c)(e),p=i.map(s.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),f(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,a=t.select,s=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(u.b)(s)];return e&&f.push("(".concat(e.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(i.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return s})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function s(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function i(t){if(t){var r=t.type,e=t.expr,o=t.symbol,u=r.toUpperCase(),i=[];switch(i.push(u),u){case"KEY_BLOCK_SIZE":o&&i.push(o),i.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":i.length=0,i.push.apply(i,a(s(t)));break;case"WITH PARSER":i.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":i.shift(),i.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":i.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:i.push(o,Object(n.literalToSQL)(e))}return i.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(i):[]}function l(t){var r=t.constraint_type,e=t.index_type,u=t.index_options,i=void 0===u?[]:u,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(s(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(i).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,a(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){if(t){var r=t.keyword,e=t.collate,a=e.name,u=e.symbol,s=e.value,i=[Object(o.toUpper)(r)];return s||i.push(u),i.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),s&&i.push(u),i.push(Object(n.a)(s)),i.filter(o.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return p})),e.d(r,"a",(function(){return f}));var n=e(3),o=e(1),a=e(2),u=e(8),s=e(0),i=e(15);function c(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){s=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){if(!t||0===t.length)return"";var r,e=[],n=c(t);try{for(n.s();!(r=n.n()).done;){var u=r.value,i={},l=u.value;for(var f in u)"value"!==f&&"keyword"!==f&&(i[f]=u[f]);var p=[Object(a.f)(i)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),e.push(p.filter(s.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function p(t){var r=t.from,e=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(e),Object(s.commonOptionConnector)("SET",f,a),Object(s.commonOptionConnector)("FROM",n.c,r),Object(s.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(u.a)(b),Object(s.returningToSQL)(v)].filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(0),o=e(1),a=e(20);function u(t){if(t){var r=t.as_window_specification,e=t.expr,u=t.keyword,s=t.type,i=t.parentheses,c=Object(n.toUpper)(s);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(s)," ").concat(Object(n.toUpper)(u)),f=Object(o.a)(e)||[];return i&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return A})),e.d(r,"a",(function(){return y}));var n=e(9),o=e(1),a=e(10),u=e(2),s=e(4),i=e(19),c=e(6),l=e(3),f=e(12),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,r){if(t){if("string"==typeof t)return d(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?d(t,r):void 0}}function d(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function y(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(u.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(i.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function m(t){var r=[];switch(t.keyword){case"from":r.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":r.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":r.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return r.filter(b.hasVal).join(" ")}function j(t){var r=t.keyword,e=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(r),Object(l.b)(e),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function w(t){var r=t.as,e=t.domain,n=t.type,a=t.keyword,u=t.target,s=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(u)];if(s&&s.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=h(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){s=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}(s);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(i.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function O(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function L(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(r),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,s=t.args,i=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=s.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(u.c).join(", "),")"):O(n)].filter(b.hasVal).join(" ")}(i),c.map(L).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var r=t.type,e=t.symbol,n=t.value,a=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function A(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,u=t.args,s=t.options,i=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(u.expr.map(n.a).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(n.a).join(", ")].join(" "):"");return i.push("".concat(c,"(").concat(l,")"),"(".concat(s.map(g).join(", "),")")),i.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,a=t.as,u=t.temporary,s=t.if_not_exists,i=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,h=t.partition_of,d=t.query_expr,m=t.unlogged,w=t.with,O=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(u),Object(b.toUpper)(m),Object(b.toUpper)(e),Object(b.toUpper)(s),Object(l.c)(n)];if(o){var L=o.type,C=o.table,g=Object(l.c)(C);return O.push(Object(b.toUpper)(L),g),O.filter(b.hasVal).join(" ")}if(h)return O.concat([j(h)]).filter(b.hasVal).join(" ");if(i&&O.push("(".concat(i.map(y).join(", "),")")),c){var A=Object(b.getParserOpt)().database,T=A&&"sqlite"===A.toLowerCase()?", ":" ";O.push(c.map(l.a).join(T))}if(w){var E=w.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");O.push("WITH (".concat(E,")"))}return O.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),d&&O.push(Object(p.b)(d)),O.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,a=t.events,u=t.execute,s=t.for_each,i=t.from,f=t.location,p=t.keyword,h=t.or,d=t.type,y=t.table,m=t.when,j=[Object(b.toUpper)(d),Object(b.toUpper)(h),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],w=Object(b.triggerEventToSQL)(a);return j.push(w,"ON",Object(l.b)(y)),i&&j.push("FROM",Object(l.b)(i)),j.push.apply(j,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(s)))),m&&j.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),j.push(Object(b.toUpper)(u.keyword),Object(c.g)(u.expr)),j.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,a=t.execute,s=t.type,i=t.table,c=t.if_not_exists,v=t.temporary,h=t.trigger,d=t.events,y=t.order,m=t.time,j=t.when,w=[Object(b.toUpper)(s),Object(b.toUpper)(v),Object(o.a)(r),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(h),Object(b.toUpper)(m),d.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(u.f).join(", ")),r.join(" ")})),"ON",Object(l.b)(i),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),y&&"".concat(Object(b.toUpper)(y.keyword)," ").concat(Object(b.identifierToSql)(y.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,j),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":w.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":w.push(Object(p.a)(a.expr.ast))}return w.push(Object(b.toUpper)(a.suffix)),w.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,u=t.type,s=t.with,i=t.version;return[Object(b.toUpper)(u),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(s),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,i),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=C(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,u=t.keyword,s=t.if_not_exists,i=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,h=t.index,d=t.on,y=t.index_options,m=t.algorithm_option,j=t.lock_option,w=t.on_kw,O=t.table,L=t.tablespace,C=t.type,g=t.where,A=t.with,T=t.with_before_where,E=A&&"WITH (".concat(Object(a.b)(A).join(", "),")"),S=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),_=h;h&&(_="string"==typeof h?Object(b.identifierToSql)(h):[Object(b.identifierToSql)(h.schema),Object(b.identifierToSql)(h.name)].filter(b.hasVal).join("."));var U=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(u),Object(b.toUpper)(s),Object(b.toUpper)(r),_,Object(b.toUpper)(w),Object(l.b)(O)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),S,Object(a.b)(y).join(" "),Object(n.b)(m),Object(n.b)(j),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,L)]);return T?U.push(E,Object(b.commonOptionConnector)("WHERE",o.a,g)):U.push(Object(b.commonOptionConnector)("WHERE",o.a,g),E),U.push(Object(b.commonOptionConnector)("ON",o.a,d),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),U.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,u=t.create_definitions,s=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];return u&&s.push(u.map(y).join(" ")),s.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":e=function(t){var r=t.type,e=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,u=t[e],s=u.db,i=u.schema,c=[Object(b.literalToSQL)(s),i.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(r),Object(b.toUpper)(n),Object(b.toUpper)(e),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,a=t.if_not_exists,u=t.keyword,s=t.recursive,i=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,h=t.view,d=t.with,y=t.with_options,m=h.db,j=h.schema,w=h.view,O=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(j),Object(b.identifierToSql)(w)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(f),Object(b.toUpper)(s),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(u),Object(b.toUpper)(a),O,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),y&&["WITH","(".concat(y.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(d)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=w(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,a=t.name,u=t.resource,s=t.type,i=[Object(b.toUpper)(s),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(u)];if(e){var c=[];switch(u){case"enum":case"range":c.push(Object(o.a)(e));break;default:c.push("(".concat(e.map(y).join(", "),")"))}i.push(c.filter(b.hasVal).join(" "))}return i.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,a=t.if_not_exists,u=t.keyword,i=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(s.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),h=[Object(b.toUpper)(p),Object(b.toUpper)(u),Object(b.toUpper)(a),v];return n&&h.push(Object(b.toUpper)(n.keyword),n.value.map(s.i).join(", ")),h.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&h.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return h.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),h.push(Object(b.literalToSQL)(i),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),h.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(2),o=e(1),a=e(0);function u(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,u=t.columns,s=Array.isArray(u)?"(".concat(u.map(n.f).join(", "),")"):"";return"".concat("default"===r.type?Object(a.identifierToSql)(r.value):Object(a.literalToSQL)(r)).concat(s," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function u(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function s(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[u(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):u(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(3),o=e(1),a=e(2),u=e(0),s=e(7),i=e(12);function c(t){if("select"===t.type)return Object(s.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(u.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(u.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,a=[Object(u.toUpper)(e)];switch(n){case"origin":a.push(Object(u.literalToSQL)(r));break;case"update":a.push("UPDATE",Object(u.commonOptionConnector)("SET",i.a,r.set),Object(u.commonOptionConnector)("WHERE",o.a,r.where))}return a.filter(u.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(u.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,a=t.prefix,s=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,h=t.where,d=t.on_duplicate_update,y=t.partition,m=t.returning,j=t.set,w=d||{},O=w.keyword,L=w.set,C=[Object(u.toUpper)(e),Object(u.toUpper)(s),Object(n.c)(r),l(y)];return Array.isArray(f)&&C.push("(".concat(f.map(u.literalToSQL).join(", "),")")),C.push(Object(u.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(u.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(u.commonOptionConnector)("SET",i.a,j)),C.push(Object(u.commonOptionConnector)("WHERE",o.a,h)),C.push(Object(u.commonOptionConnector)(O,i.a,L)),C.push(Object(u.returningToSQL)(m)),C.filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(0),o=e(1);function a(t){var r=t.expr,e=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(10),a=e(2);function u(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return s(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?s(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(t){var r=t.constraint,e=t.constraint_type,s=t.enforced,i=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(r));var h=Object(n.toUpper)(e);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===h&&(h="UNIQUE"),b.push(h),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(i)),b.push.apply(b,u(Object(o.c)(t))),b.push.apply(b,u(Object(a.g)(l))),b.push(Object(n.toUpper)(s)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),a=e(13);function u(t){if(t){var r=t.type;return"rows"===r?[Object(n.toUpper)(r),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function s(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,a=t.orderby,s=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(a,"order by"),u(s)].filter(n.hasVal).join(" ")}(r),")")}function i(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(s(e))}function c(t){return t.expr.map(i).join(", ")}function l(t){var r=t.over;return[function(t){var r=t.args,e=t.name,a=t.consider_nulls,u=void 0===a?"":a,s=t.separator,i=void 0===s?", ":s;return[e,"(",r?Object(o.a)(r).join(i):"",")",u&&" ",u].filter(n.hasVal).join("")}(t),Object(a.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.operator||t.op,e=Object(n.a)(t.right),a=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,e="".concat(e[0]," AND ").concat(e[1])}a||(e="(".concat(e.join(", "),")"))}var u=t.right.escape||{},s=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),r,e,Object(o.toUpper)(u.type),Object(n.a)(u.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(s,")"):s].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.left,e=t.right,a=t.symbol,u=t.keyword;r.keyword=u;var s=Object(n.a)(r),i=Object(n.a)(e);return[s,Object(o.toUpper)(a),i].filter(o.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(1),o=e(8),a=e(3),u=e(0);function s(t){var r,e,s,i,c=t.keyword,l=t.suffix,f="";switch(Object(u.toUpper)(c)){case"BINLOG":e=(r=t).in,s=r.from,i=r.limit,f=[Object(u.commonOptionConnector)("IN",u.literalToSQL,e&&e.right),Object(u.commonOptionConnector)("FROM",a.c,s),Object(o.a)(i)].filter(u.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(u.toUpper)(e)?Object(u.commonOptionConnector)("LIKE",u.literalToSQL,r.right):Object(u.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(u.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(u.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(u.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(u.toUpper)(c),Object(u.toUpper)(l),f].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(2),o=e(1),a=e(25);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s,i,c,l=(s={},i="transactsql",c=a.parse,(i=function(t){var r=function(t,r){if("object"!=u(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==u(r)?r:r+""}(i))in s?Object.defineProperty(s,i,{value:c,enumerable:!0,configurable:!0,writable:!0}):s[i]=c,s),f=e(16),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return h(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?h(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,s=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){s=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(s)throw a}}}}function h(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function d(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:r+""}var m=function(){return function(t,r,e){return r&&d(t.prototype,r),e&&d(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(e),!t||"*"===t)return[];var o=Object(n.k)(r);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"transactsql":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](!1===r.trimQuery?t:t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,u=this["".concat(o,"List")].bind(this),s=u(t,e),i=!0,c="",l=v(s);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,h=!1,d=v(r);try{for(d.s();!(f=d.n()).done;){var y=f.value,m=new RegExp("^".concat(y,"$"),"i");if(m.test(b)){h=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!h){c=b,i=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])}();r.a=m},function(t,r,e){"use strict";var n=e(29);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?a(t.parts[r][0])+"-"+a(t.parts[r][1]):a(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,a=new Array(t.length);for(r=0;r<t.length;r++)a[r]=(o=t[r],e[o.type](o));if(a.sort(),a.length>0){for(r=1,n=1;r<a.length;r++)a[r-1]!==a[r]&&(a[n]=a[r],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,a={},u={start:Os},s=Os,i=function(t,r){return pp(t,r)},c=function(t,r){return{...t,order_by:r.toLowerCase()}},l=hs("INCLUDE",!0),f=hs("FILESTREAM_ON",!0),p=function(t,r){return pp(t,r,1)},b=hs("IF",!0),v=function(t,r){return pp(t,r)},h=hs("UNIQUE",!0),d=hs("KEY",!0),y=hs("PRIMARY",!0),m=hs("IDENTITY",!0),j=hs("COLUMN_FORMAT",!0),w=hs("FIXED",!0),O=hs("DYNAMIC",!0),L=hs("DEFAULT",!0),C=hs("STORAGE",!0),g=hs("DISK",!0),A=hs("MEMORY",!0),T=hs("CURSOR",!0),E=hs("EXECUTE",!0),S=hs("EXEC",!0),_=hs("@",!1),U=hs("if",!0),x=hs("exists",!0),I=hs("PROCEDURE",!0),k=hs("ENCRYPTION",!0),N=hs("SCHEMABINDING",!0),R=hs("VIEW_METADATA",!0),V=hs("CHECK",!0),M=hs("OPTION",!0),q=hs("ALGORITHM",!0),P=hs("INSTANT",!0),D=hs("INPLACE",!0),Q=hs("COPY",!0),B=hs("LOCK",!0),F=hs("NONE",!0),H=hs("SHARED",!0),$=hs("EXCLUSIVE",!0),W=hs("NOCHECK",!0),G=hs("PRIMARY KEY",!0),Y=hs("NOT",!0),K=hs("FOR",!0),X=hs("REPLICATION",!0),Z=hs("FOREIGN KEY",!0),z=hs("MATCH FULL",!0),J=hs("MATCH PARTIAL",!0),tt=hs("MATCH SIMPLE",!0),rt=hs("RESTRICT",!0),et=hs("CASCADE",!0),nt=hs("SET NULL",!0),ot=hs("NO ACTION",!0),at=hs("SET DEFAULT",!0),ut=hs("CHARACTER",!0),st=hs("SET",!0),it=hs("CHARSET",!0),ct=hs("COLLATE",!0),lt=hs("AUTO_INCREMENT",!0),ft=hs("AVG_ROW_LENGTH",!0),pt=hs("KEY_BLOCK_SIZE",!0),bt=hs("MAX_ROWS",!0),vt=hs("MIN_ROWS",!0),ht=hs("STATS_SAMPLE_PAGES",!0),dt=hs("CONNECTION",!0),yt=hs("COMPRESSION",!0),mt=hs("'",!1),jt=hs("ZLIB",!0),wt=hs("LZ4",!0),Ot=hs("ENGINE",!0),Lt=hs("TEXTIMAGE_ON",!0),Ct=hs("result",!0),gt=hs("caching",!0),At=hs("statistics",!0),Tt=hs("io",!0),Et=hs("xml",!0),St=hs("profile",!0),_t=hs("time",!0),Ut=hs("datefirst",!0),xt=hs("dateformat",!0),It=hs("deadlock_priority",!0),kt=hs("lock_timeout",!0),Nt=hs("concat_null_yields_null",!0),Rt=hs("cursor_close_on_commit",!0),Vt=hs("fips_flagger",!0),Mt=hs("identity_insert",!0),qt=hs("language",!0),Pt=hs("offsets",!0),Dt=hs("quoted_identifier",!0),Qt=hs("arithabort",!0),Bt=hs("arithignore",!0),Ft=hs("fmtonly",!0),Ht=hs("nocount",!0),$t=hs("noexec",!0),Wt=hs("numberic_roundabort",!0),Gt=hs("parseonly",!0),Yt=hs("query_governor_cost_limit",!0),Kt=hs("rowcount",!0),Xt=hs("textsize",!0),Zt=hs("ansi_defaults",!0),zt=hs("ansi_null_dflt_off",!0),Jt=hs("ansi_null_dflt_on",!0),tr=hs("ansi_nulls",!0),rr=hs("ansi_padding",!0),er=hs("ansi_warnings",!0),nr=hs("forceplan",!0),or=hs("showplan_all",!0),ar=hs("showplan_text",!0),ur=hs("showplan_xml",!0),sr=hs("implicit_transactions",!0),ir=hs("remote_proc_transactions",!0),cr=hs("xact_abort",!0),lr=function(t){return{type:"origin",value:t.toLowerCase()}},fr=hs("read",!0),pr=hs("uncommitted",!0),br=hs("committed",!0),vr=hs("REPEATABLE",!0),hr=hs("snapshot",!0),dr=hs("serializable",!0),yr=hs("transaction",!0),mr=hs("isolation",!0),jr=hs("level",!0),wr=hs("READ",!0),Or=hs("LOCAL",!0),Lr=hs("LOW_PRIORITY",!0),Cr=hs("WRITE",!0),gr=function(t,r){return pp(t,r)},Ar=hs("(",!1),Tr=hs(")",!1),Er=hs("PERCENT",!0),Sr=hs("SYSTEM_TIME",!0),_r=hs("OF",!0),Ur=hs("CONTAINED",!0),xr=hs("BTREE",!0),Ir=hs("HASH",!0),kr=hs("PARTITIONS",!0),Nr=function(t,r,e){return{type:t.toLowerCase(),symbol:r,expr:e}},Rr=hs("FILLFACTOR",!0),Vr=hs("MAX_DURATION",!0),Mr=hs("MAXDOP",!0),qr=hs("WITH",!0),Pr=hs("PARSER",!0),Dr=hs("VISIBLE",!0),Qr=hs("INVISIBLE",!0),Br=hs("PAD_INDEX",!0),Fr=hs("SORT_IN_TEMPDB",!0),Hr=hs("IGNORE_DUP_KEY",!0),$r=hs("STATISTICS_NORECOMPUTE",!0),Wr=hs("STATISTICS_INCREMENTAL",!0),Gr=hs("DROP_EXISTING",!0),Yr=hs("ONLINE",!0),Kr=hs("RESUMABLE",!0),Xr=hs("ALLOW_ROW_LOCKS",!0),Zr=hs("ALLOW_PAGE_LOCKS",!0),zr=hs("OPTIMIZE_FOR_SEQUENTIAL_KEY",!0),Jr=hs("DATA_COMPRESSION",!0),te=hs("ROW",!0),re=hs("PAGE",!1),ee=function(t,r){return r.unshift(t),r.forEach(t=>{const{table:r,as:e}=t;jp[r]=r,e&&(jp[e]=r),function(t){const r=hp(t);t.clear(),r.forEach(r=>t.add(r))}(mp)}),r},ne=hs("FORCESEEK",!0),oe=hs("SPATIAL_WINDOW_MAX_CELLS",!0),ae=hs("NOEXPAND",!0),ue=hs("FORCESCAN",!0),se=hs("HOLDLOCK",!0),ie=hs("NOLOCK",!0),ce=hs("NOWAIT",!0),le=hs("PAGLOCK",!0),fe=hs("READCOMMITTED",!0),pe=hs("READCOMMITTEDLOCK",!0),be=hs("READPAST",!0),ve=hs("READUNCOMMITTED",!0),he=hs("REPEATABLEREAD ",!0),de=hs("ROWLOCK",!0),ye=hs("SERIALIZABLE",!0),me=hs("SNAPSHOT",!0),je=hs("TABLOCK",!0),we=hs("TABLOCKX",!0),Oe=hs("UPDLOCK",!0),Le=hs("XLOCK",!0),Ce=hs("##",!1),ge=hs("#",!1),Ae=hs("FIRST",!0),Te=hs("ROWS",!0),Ee=hs("ONLY",!0),Se=hs("NEXT",!0),_e=hs("RAW",!0),Ue=hs("AUTO",!0),xe=hs("EXPLICIT",!0),Ie=function(t){return{keyword:t}},ke=hs("PATH",!0),Ne=hs("XML",!0),Re=hs("JSON",!0),Ve=hs("=",!1),Me=function(t,r){return bp(t,r)},qe=hs("!",!1),Pe=function(t){return t[0]+" "+t[2]},De=hs(">=",!1),Qe=hs(">",!1),Be=hs("<=",!1),Fe=hs("<>",!1),He=hs("<",!1),$e=hs("!=",!1),We=hs("+",!1),Ge=hs("-",!1),Ye=hs("*",!1),Ke=hs("/",!1),Xe=hs("%",!1),Ze=hs("~",!1),ze=function(t){return!0===sp[t.toUpperCase()]},Je=hs('"',!1),tn=/^[^"]/,rn=ds(['"'],!0,!1),en=/^[^']/,nn=ds(["'"],!0,!1),on=hs("`",!1),an=/^[^`]/,un=ds(["`"],!0,!1),sn=hs("[",!1),cn=/^[^\]]/,ln=ds(["]"],!0,!1),fn=hs("]",!1),pn=function(t,r){return t+r.join("")},bn=/^[A-Za-z_@#\u4E00-\u9FA5]/,vn=ds([["A","Z"],["a","z"],"_","@","#",["一","龥"]],!1,!1),hn=/^[A-Za-z0-9_\-@$$\u4E00-\u9FA5\xC0-\u017F]/,dn=ds([["A","Z"],["a","z"],["0","9"],"_","-","@","$","$",["一","龥"],["À","ſ"]],!1,!1),yn=/^[A-Za-z0-9_:]/,mn=ds([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),jn=hs(":",!1),wn=hs("OVER",!0),On=(hs("WINDOW",!0),hs("FOLLOWING",!0)),Ln=hs("PRECEDING",!0),Cn=hs("CURRENT",!0),gn=hs("UNBOUNDED",!0),An=hs("WITHIN",!0),Tn=hs("N",!0),En=hs("_binary",!0),Sn=hs("_latin1",!0),_n=hs("0x",!0),Un=/^[0-9A-Fa-f]/,xn=ds([["0","9"],["A","F"],["a","f"]],!1,!1),In=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}},kn=/^[^"\\\0-\x1F\x7F]/,Nn=ds(['"',"\\",["\0",""],""],!0,!1),Rn=/^[^'\\]/,Vn=ds(["'","\\"],!0,!1),Mn=hs("\\'",!1),qn=hs('\\"',!1),Pn=hs("\\\\",!1),Dn=hs("\\/",!1),Qn=hs("\\b",!1),Bn=hs("\\f",!1),Fn=hs("\\n",!1),Hn=hs("\\r",!1),$n=hs("\\t",!1),Wn=hs("\\u",!1),Gn=hs("\\",!1),Yn=hs("''",!1),Kn=hs('""',!1),Xn=hs("``",!1),Zn=/^[\n\r]/,zn=ds(["\n","\r"],!1,!1),Jn=hs(".",!1),to=/^[0-9]/,ro=ds([["0","9"]],!1,!1),eo=/^[0-9a-fA-F]/,no=ds([["0","9"],["a","f"],["A","F"]],!1,!1),oo=/^[eE]/,ao=ds(["e","E"],!1,!1),uo=/^[+\-]/,so=ds(["+","-"],!1,!1),io=hs("NULL",!0),co=hs("NOT NULL",!0),lo=hs("TRUE",!0),fo=hs("TO",!0),po=hs("TOP",!0),bo=hs("FALSE",!0),vo=(hs("SHOW",!0),hs("DROP",!0)),ho=hs("DECLARE",!0),yo=hs("USE",!0),mo=hs("ALTER",!0),jo=hs("SELECT",!0),wo=hs("UPDATE",!0),Oo=hs("CREATE",!0),Lo=hs("TEMPORARY",!0),Co=hs("DELETE",!0),go=hs("INSERT",!0),Ao=hs("RECURSIVE",!0),To=hs("REPLACE",!0),Eo=hs("RENAME",!0),So=hs("IGNORE",!0),_o=(hs("EXPLAIN",!0),hs("PARTITION",!0)),Uo=hs("INTO",!0),xo=hs("FROM",!0),Io=hs("UNLOCK",!0),ko=hs("AS",!0),No=hs("TABLE",!0),Ro=hs("VIEW",!0),Vo=hs("DATABASE",!0),Mo=hs("SCHEMA",!0),qo=hs("TABLES",!0),Po=hs("ON",!0),Do=hs("OFF",!0),Qo=hs("LEFT",!0),Bo=hs("RIGHT",!0),Fo=hs("FULL",!0),Ho=hs("INNER",!0),$o=hs("CROSS",!0),Wo=hs("JOIN",!0),Go=hs("APPLY",!0),Yo=hs("OUTER",!0),Ko=hs("UNION",!0),Xo=hs("VALUES",!0),Zo=hs("USING",!0),zo=hs("WHERE",!0),Jo=hs("GO",!0),ta=hs("GROUP",!0),ra=hs("BY",!0),ea=hs("ORDER",!0),na=hs("HAVING",!0),oa=hs("LIMIT",!0),aa=hs("OFFSET",!0),ua=hs("FETCH",!0),sa=hs("ASC",!0),ia=hs("DESC",!0),ca=hs("ALL",!0),la=hs("DISTINCT",!0),fa=hs("BETWEEN",!0),pa=hs("IN",!0),ba=hs("IS",!0),va=hs("LIKE",!0),ha=hs("EXISTS",!0),da=hs("AND",!0),ya=hs("OR",!0),ma=hs("ARRAY_AGG",!0),ja=hs("STRING_AGG",!0),wa=hs("COUNT",!0),Oa=hs("MAX",!0),La=hs("MIN",!0),Ca=hs("SUM",!0),ga=hs("AVG",!0),Aa=hs("CALL",!0),Ta=hs("CASE",!0),Ea=hs("WHEN",!0),Sa=hs("THEN",!0),_a=hs("ELSE",!0),Ua=hs("END",!0),xa=hs("CAST",!0),Ia=hs("BIT",!0),ka=hs("MONEY",!0),Na=hs("SMALLMONEY",!0),Ra=hs("CHAR",!0),Va=hs("VARCHAR",!0),Ma=hs("BINARY",!0),qa=hs("VARBINARY",!0),Pa=hs("NCHAR",!0),Da=hs("NVARCHAR",!0),Qa=hs("NUMERIC",!0),Ba=hs("DECIMAL",!0),Fa=hs("SIGNED",!0),Ha=hs("UNSIGNED",!0),$a=hs("INT",!0),Wa=hs("ZEROFILL",!0),Ga=hs("INTEGER",!0),Ya=hs("SMALLINT",!0),Ka=hs("TINYINT",!0),Xa=hs("TINYTEXT",!0),Za=hs("TEXT",!0),za=hs("MEDIUMTEXT",!0),Ja=hs("LONGTEXT",!0),tu=hs("BIGINT",!0),ru=hs("FLOAT",!0),eu=hs("REAL",!0),nu=hs("DOUBLE",!0),ou=hs("DATE",!0),au=hs("SMALLDATETIME",!0),uu=hs("DATETIME",!0),su=hs("DATETIME2",!0),iu=hs("DATETIMEOFFSET",!0),cu=hs("TIME",!0),lu=hs("TIMESTAMP",!0),fu=hs("TRUNCATE",!0),pu=hs("UNIQUEIDENTIFIER",!0),bu=hs("USER",!0),vu=hs("CURRENT_DATE",!0),hu=(hs("ADDDATE",!0),hs("INTERVAL",!0)),du=hs("YEAR",!0),yu=hs("MONTH",!0),mu=hs("DAY",!0),ju=hs("HOUR",!0),wu=hs("MINUTE",!0),Ou=hs("SECOND",!0),Lu=hs("CURRENT_TIME",!0),Cu=hs("CURRENT_TIMESTAMP",!0),gu=hs("CURRENT_USER",!0),Au=hs("SESSION_USER",!0),Tu=hs("SYSTEM_USER",!0),Eu=(hs("GLOBAL",!0),hs("SESSION",!0),hs("PIVOT",!0)),Su=hs("UNPIVOT",!0),_u=(hs("PERSIST",!0),hs("PERSIST_ONLY",!0),hs("@@",!1)),Uu=hs("$",!1),xu=hs("return",!0),Iu=hs(":=",!1),ku=hs("DUAL",!0),Nu=hs("ADD",!0),Ru=hs("COLUMN",!0),Vu=hs("INDEX",!0),Mu=hs("FULLTEXT",!0),qu=hs("SPATIAL",!0),Pu=hs("CLUSTERED",!0),Du=hs("NONCLUSTERED",!0),Qu=hs("COMMENT",!0),Bu=hs("CONSTRAINT",!0),Fu=hs("REFERENCES",!0),Hu=hs("SQL_CALC_FOUND_ROWS",!0),$u=hs("SQL_CACHE",!0),Wu=hs("SQL_NO_CACHE",!0),Gu=hs("SQL_SMALL_RESULT",!0),Yu=hs("SQL_BIG_RESULT",!0),Ku=hs("SQL_BUFFER_RESULT",!0),Xu=hs(",",!1),Zu=hs(";",!1),zu=hs("||",!1),Ju=hs("&&",!1),ts=hs("/*",!1),rs=hs("*/",!1),es=hs("--",!1),ns={type:"any"},os=/^[ \t\n\r]/,as=ds([" ","\t","\n","\r"],!1,!1),us=function(t,r,e){return t&&!e||!t&&e},ss=function(t,r,e){return{dataType:r}},is=function(t,r,e){return{dataType:r}},cs=0,ls=0,fs=[{line:1,column:1}],ps=0,bs=[],vs=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');s=u[r.startRule]}function hs(t,r){return{type:"literal",text:t,ignoreCase:r}}function ds(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function ys(r){var e,n=fs[r];if(n)return n;for(e=r-1;!fs[e];)e--;for(n={line:(n=fs[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return fs[r]=n,n}function ms(t,r){var e=ys(t),n=ys(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function js(t){cs<ps||(cs>ps&&(ps=cs,bs=[]),bs.push(t))}function ws(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function Os(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Ls())!==a)if(Qf()!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=Sl())!==a&&(s=Qf())!==a?((i=Ls())===a&&(i=null),i!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=Sl())!==a&&(s=Qf())!==a?((i=Ls())===a&&(i=null),i!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){if(!r||0===r.length)return t;delete t.tableList,delete t.columnList;let e=t;for(let t=0;t<r.length;t++){const n=r[t][3]||[];delete n.tableList,delete n.columnList,e.go_next=n,e.go="go",e=e.go_next}return{tableList:Array.from(yp),columnList:hp(mp),ast:t}}(r,e)):(cs=t,t=a)}else cs=t,t=a;else cs=t,t=a;return t}function Ls(){var t,r,e;return t=cs,Qf()!==a&&(r=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=gs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=Pf())!==a&&(s=Qf())!==a&&(i=gs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=Pf())!==a&&(s=Qf())!==a&&(i=gs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(yp),columnList:hp(mp),ast:n}}(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&Qf()!==a?((e=Pf())===a&&(e=null),e!==a?(ls=t,t=r):(cs=t,t=a)):(cs=t,t=a),t}function Cs(){var r;return(r=function(){var r,e,n,o,u,s,i;r=cs,(e=el())!==a&&Qf()!==a&&(n=yl())!==a&&Qf()!==a?((o=Ms())===a&&(o=null),o!==a&&Qf()!==a&&(u=ji())!==a?(ls=r,c=e,l=n,f=o,(b=u)&&b.forEach(t=>yp.add(`${c}::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:c.toLowerCase(),keyword:l.toLowerCase(),prefix:f,name:b}},r=e):(cs=r,r=a)):(cs=r,r=a);var c,l,f,b;r===a&&(r=cs,(e=el())!==a&&Qf()!==a?("procedure"===t.substr(cs,9).toLowerCase()?(n=t.substr(cs,9),cs+=9):(n=a,0===vs&&js(I)),n!==a&&Qf()!==a&&(o=lc())!==a?(ls=r,e=function(t,r,e){return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),name:e}}}(e,n,o),r=e):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=el())!==a&&Qf()!==a&&(n=Af())!==a&&Qf()!==a&&(o=sc())!==a&&Qf()!==a&&(u=wl())!==a&&Qf()!==a&&(s=Ai())!==a&&Qf()!==a?((i=function(){var t,r,e,n,o,u;t=cs,(r=Ds())===a&&(r=Qs());if(r!==a){for(e=[],n=cs,(o=Qf())!==a?((u=Ds())===a&&(u=Qs()),u!==a?n=o=[o,u]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=Ds())===a&&(u=Qs()),u!==a?n=o=[o,u]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,r=p(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(i=null),i!==a&&Qf()!==a?(ls=r,e=function(t,r,e,n,o){return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),name:e,table:n,options:o}}}(e,n,o,s,i),r=e):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=el())!==a&&Qf()!==a&&(n=ml())!==a&&Qf()!==a?((o=Ms())===a&&(o=null),o!==a&&Qf()!==a&&(u=ji())!==a?(ls=r,e=function(t,r,e,n){return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:n}}}(e,n,o,u),r=e):(cs=r,r=a)):(cs=r,r=a))));return r}())===a&&(r=function(){var r;(r=function(){var t,r,e,n,o,u,s,c,l,f;t=cs,(r=ul())!==a&&Qf()!==a?((e=sl())===a&&(e=null),e!==a&&Qf()!==a&&yl()!==a&&Qf()!==a?((n=_s())===a&&(n=null),n!==a&&Qf()!==a&&(o=ji())!==a&&Qf()!==a&&(u=Us())!==a&&Qf()!==a?((s=function(){var t,r,e,n,o,u,s,c;if(t=cs,(r=Zs())!==a){for(e=[],n=cs,(o=Qf())!==a?((u=kf())===a&&(u=null),u!==a&&(s=Qf())!==a&&(c=Zs())!==a?n=o=[o,u,s,c]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=kf())===a&&(u=null),u!==a&&(s=Qf())!==a&&(c=Zs())!==a?n=o=[o,u,s,c]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,r=i(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(s=null),s!==a&&Qf()!==a?((c=fl())===a&&(c=cl()),c===a&&(c=null),c!==a&&Qf()!==a?((l=dl())===a&&(l=null),l!==a&&Qf()!==a?((f=Ts())===a&&(f=null),f!==a?(ls=t,p=r,b=e,v=n,d=u,y=s,m=c,j=l,w=f,(h=o)&&h.forEach(t=>yp.add(`create::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(yp),columnList:hp(mp),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:v,table:h,ignore_replace:m&&m[0].toLowerCase(),as:j&&j[0].toLowerCase(),query_expr:w&&w.ast,create_definitions:d,table_options:y}},t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);var p,b,v,h,d,y,m,j,w;t===a&&(t=cs,(r=ul())!==a&&Qf()!==a?((e=sl())===a&&(e=null),e!==a&&Qf()!==a&&yl()!==a&&Qf()!==a?((n=_s())===a&&(n=null),n!==a&&Qf()!==a&&(o=ji())!==a&&Qf()!==a&&(u=function t(){var r,e;(r=function(){var t,r,e;t=cs,(r=Dl())!==a&&Qf()!==a&&(e=ji())!==a?(ls=t,r=function(t){return{type:"like",table:t}}(e),t=r):(cs=t,t=a);return t}())===a&&(r=cs,Rf()!==a&&Qf()!==a&&(e=t())!==a&&Qf()!==a&&Vf()!==a?(ls=r,(n=e).parentheses=!0,r=n):(cs=r,r=a));var n;return r}())!==a?(ls=t,r=function(t,r,e,n,o){return n&&n.forEach(t=>yp.add(`create::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(r,e,n,o,u),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,u,s,i,c,p,b,v,h,d,y,m,j,w,O;r=cs,(e=ul())!==a&&Qf()!==a?((n=Ef())===a&&(n=Sf())===a&&(n=_f()),n===a&&(n=null),n!==a&&Qf()!==a&&(o=Af())!==a&&Qf()!==a&&(u=lc())!==a&&Qf()!==a&&(s=wl())!==a&&Qf()!==a&&(i=Ai())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(c=Es())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((p=function(){var r,e,n;r=cs,"include"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(l));e!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=ic())!==a&&Qf()!==a&&Vf()!==a?(ls=r,u=n,e={type:(o=e).toLowerCase(),keyword:o.toLowerCase(),columns:u},r=e):(cs=r,r=a);var o,u;return r}())===a&&(p=null),p!==a&&Qf()!==a?((b=Si())===a&&(b=null),b!==a&&Qf()!==a?(v=cs,(h=El())!==a&&(d=Qf())!==a&&(y=Rf())!==a&&(m=Qf())!==a&&(j=vi())!==a&&(w=Qf())!==a&&(O=Vf())!==a?v=h=[h,d,y,m,j,w,O]:(cs=v,v=a),v===a&&(v=null),v!==a&&(h=Qf())!==a?((d=Ei())===a&&(d=null),d!==a&&(y=Qf())!==a?(m=cs,"filestream_on"===t.substr(cs,13).toLowerCase()?(j=t.substr(cs,13),cs+=13):(j=a,0===vs&&js(f)),j!==a&&(w=Qf())!==a&&(O=lc())!==a?m=j=[j,w,O]:(cs=m,m=a),m===a&&(m=null),m!==a?(ls=r,L=e,C=n,g=o,A=u,T=s,E=i,S=c,_=p,U=b,x=v,I=d,k=m,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:L[0].toLowerCase(),index_type:C&&C.toLowerCase(),keyword:g.toLowerCase(),index:A,on_kw:T[0].toLowerCase(),table:E,index_columns:S,include:_,where:U,with:x&&x[4],on:I,filestream_on:k&&{value:k[2]}}},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var L,C,g,A,T,E,S,_,U,x,I,k;return r}())===a&&(r=function(){var r,e,n,o,u,s;r=cs,(e=ul())!==a&&Qf()!==a?((n=function(){var r,e,n,o;r=cs,"database"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Vo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DATABASE"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=cs,"schema"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Mo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SCHEMA"):(cs=r,r=a)):(cs=r,r=a);return r}()),n!==a&&Qf()!==a?((o=_s())===a&&(o=null),o!==a&&Qf()!==a&&(u=tp())!==a&&Qf()!==a?((s=function(){var t,r,e,n,o,u;if(t=cs,(r=Xs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=Xs())!==a?n=o=[o,u]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=Xs())!==a?n=o=[o,u]:(cs=n,n=a);e!==a?(ls=t,r=p(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(s=null),s!==a?(ls=r,e=function(t,r,e,n,o){const a=r.toLowerCase();return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:e,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,u,s),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}());return r}())===a&&(r=function(){var r,e,n,o;r=cs,(e=function(){var r,e,n,o;r=cs,"truncate"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(fu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TRUNCATE"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a?((n=yl())===a&&(n=null),n!==a&&Qf()!==a&&(o=ji())!==a?(ls=r,u=e,s=n,(i=o)&&i.forEach(t=>yp.add(`${u}::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:u.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:i}},r=e):(cs=r,r=a)):(cs=r,r=a);var u,s,i;return r}())===a&&(r=function(){var t,r,e;t=cs,(r=ll())!==a&&Qf()!==a&&yl()!==a&&Qf()!==a&&(e=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=pi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=pi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=pi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=t,(n=e).forEach(t=>t.forEach(t=>t.table&&yp.add(`rename::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`))),r={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"rename",table:n}},t=r):(cs=t,t=a);var n;return t}())===a&&(r=function(){var r,e,n;r=cs,(e=function(){var r,e,n,o;r=cs,"call"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Aa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CALL"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&(n=rp())!==a?(ls=r,o=n,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"call",expr:o}},r=e):(cs=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n;r=cs,(e=function(){var r,e,n,o;r=cs,"use"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(yo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&(n=lc())!==a?(ls=r,o=n,yp.add(`use::${o}::null`),e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"use",db:o}},r=e):(cs=r,r=a);var o;return r}())===a&&(r=function(){var r;(r=function(){var t,r,e,n;t=cs,(r=ol())!==a&&Qf()!==a&&yl()!==a&&Qf()!==a&&(e=ji())!==a&&Qf()!==a&&(n=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Ps())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ps())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ps())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=t,u=n,(o=e)&&o.length>0&&o.forEach(t=>yp.add(`alter::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"alter",keyword:"table",table:o,expr:u}},t=r):(cs=t,t=a);var o,u;return t}())===a&&(r=function(){var r,e,n,o,u,s,c,l,f,p,b;r=cs,(e=ol())!==a&&Qf()!==a&&ml()!==a&&Qf()!==a&&(n=Ai())!==a&&Qf()!==a?(o=cs,(u=Rf())!==a&&(s=Qf())!==a&&(c=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=sc())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=sc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=sc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&(l=Qf())!==a&&(f=Vf())!==a?o=u=[u,s,c,l,f]:(cs=o,o=a),o===a&&(o=null),o!==a&&(u=Qf())!==a?(s=cs,(c=El())!==a&&(l=Qf())!==a&&(f=function(){var t,r,e,n,o,u,s,c;if(t=cs,(r=qs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=qs())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=qs())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,r=i(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?s=c=[c,l,f]:(cs=s,s=a),s===a&&(s=null),s!==a&&(c=Qf())!==a&&(l=dl())!==a&&(f=Qf())!==a&&(p=ni())!==a&&Qf()!==a?((b=function(){var r,e,n;r=cs,El()!==a&&Qf()!==a?("check"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(V)),e!==a&&Qf()!==a?("option"===t.substr(cs,6).toLowerCase()?(n=t.substr(cs,6),cs+=6):(n=a,0===vs&&js(M)),n!==a?(ls=r,r="with check option"):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(b=null),b!==a?(ls=r,d=o,y=s,m=p,j=b,(h=n)&&h.length>0&&h.forEach(t=>yp.add(`alter::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"alter",keyword:"view",view:h,columns:d&&d[2],attributes:y&&y[2],select:m,with:j}},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var h,d,y,m,j;return r}());return r}())===a&&(r=function(){var r,e,n,o,u,s;r=cs,(e=hl())!==a&&Qf()!==a?("transaction"===t.substr(cs,11).toLowerCase()?(n=t.substr(cs,11),cs+=11):(n=a,0===vs&&js(yr)),n!==a&&Qf()!==a?("isolation"===t.substr(cs,9).toLowerCase()?(o=t.substr(cs,9),cs+=9):(o=a,0===vs&&js(mr)),o!==a&&Qf()!==a?("level"===t.substr(cs,5).toLowerCase()?(u=t.substr(cs,5),cs+=5):(u=a,0===vs&&js(jr)),u!==a&&Qf()!==a&&(s=function(){var r,e,n;r=cs,"read"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(fr));e!==a&&Qf()!==a?("uncommitted"===t.substr(cs,11).toLowerCase()?(n=t.substr(cs,11),cs+=11):(n=a,0===vs&&js(pr)),n===a&&("committed"===t.substr(cs,9).toLowerCase()?(n=t.substr(cs,9),cs+=9):(n=a,0===vs&&js(br))),n!==a?(ls=r,e={type:"origin",value:"read "+n.toLowerCase()},r=e):(cs=r,r=a)):(cs=r,r=a);r===a&&(r=cs,"repeatable"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(vr)),e!==a&&Qf()!==a?("read"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(fr)),n!==a?(ls=r,r=e={type:"origin",value:"repeatable read"}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"snapshot"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(hr)),e===a&&("serializable"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(dr))),e!==a&&(ls=r,e=lr(e)),r=e));return r}())!==a?(ls=r,i=s,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"set",expr:[{type:"assign",left:{type:"origin",value:"transaction isolation level"},right:i}]}},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var i;r===a&&(r=cs,(e=hl())!==a&&Qf()!==a?((n=function(){var r,e,n,o;r=cs,"result"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Ct));e!==a&&Qf()!==a&&(n=hl())!==a&&Qf()!==a?("caching"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(gt)),o!==a?(ls=r,r=e={type:"origin",value:"result set caching"}):(cs=r,r=a)):(cs=r,r=a);r===a&&(r=cs,"statistics"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(At)),e!==a&&Qf()!==a?("io"===t.substr(cs,2).toLowerCase()?(n=t.substr(cs,2),cs+=2):(n=a,0===vs&&js(Tt)),n===a&&("xml"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(Et)),n===a&&("profile"===t.substr(cs,7).toLowerCase()?(n=t.substr(cs,7),cs+=7):(n=a,0===vs&&js(St)),n===a&&("time"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(_t))))),n!==a?(ls=r,e={type:"origin",value:"statistics "+n.toLowerCase()},r=e):(cs=r,r=a)):(cs=r,r=a));return r}())===a&&(n=function(){var r,e,n,o;r=cs,"datefirst"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Ut));e===a&&("dateformat"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(xt)),e===a&&("deadlock_priority"===t.substr(cs,17).toLowerCase()?(e=t.substr(cs,17),cs+=17):(e=a,0===vs&&js(It)),e===a&&("lock_timeout"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(kt)),e===a&&("concat_null_yields_null"===t.substr(cs,23).toLowerCase()?(e=t.substr(cs,23),cs+=23):(e=a,0===vs&&js(Nt)),e===a&&("cursor_close_on_commit"===t.substr(cs,22).toLowerCase()?(e=t.substr(cs,22),cs+=22):(e=a,0===vs&&js(Rt)),e===a&&("fips_flagger"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Vt)),e===a&&("identity_insert"===t.substr(cs,15).toLowerCase()?(e=t.substr(cs,15),cs+=15):(e=a,0===vs&&js(Mt)),e===a&&("language"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(qt)),e===a&&("offsets"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Pt)),e===a&&("quoted_identifier"===t.substr(cs,17).toLowerCase()?(e=t.substr(cs,17),cs+=17):(e=a,0===vs&&js(Dt)),e===a&&("arithabort"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(Qt)),e===a&&("arithignore"===t.substr(cs,11).toLowerCase()?(e=t.substr(cs,11),cs+=11):(e=a,0===vs&&js(Bt)),e===a&&("fmtonly"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Ft)),e===a&&("nocount"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Ht)),e===a&&("noexec"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js($t)),e===a&&("numberic_roundabort"===t.substr(cs,19).toLowerCase()?(e=t.substr(cs,19),cs+=19):(e=a,0===vs&&js(Wt)),e===a&&("parseonly"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Gt)),e===a&&("query_governor_cost_limit"===t.substr(cs,25).toLowerCase()?(e=t.substr(cs,25),cs+=25):(e=a,0===vs&&js(Yt)),e===a&&("rowcount"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Kt)),e===a&&("textsize"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Xt)),e===a&&(e=cs,"ansi_defaults"===t.substr(cs,13).toLowerCase()?(n=t.substr(cs,13),cs+=13):(n=a,0===vs&&js(Zt)),n!==a?("ansi_null_dflt_off"===t.substr(cs,18).toLowerCase()?(o=t.substr(cs,18),cs+=18):(o=a,0===vs&&js(zt)),o!==a?e=n=[n,o]:(cs=e,e=a)):(cs=e,e=a),e===a&&("ansi_null_dflt_on"===t.substr(cs,17).toLowerCase()?(e=t.substr(cs,17),cs+=17):(e=a,0===vs&&js(Jt)),e===a&&("ansi_nulls"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(tr)),e===a&&("ansi_padding"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(rr)),e===a&&("ansi_warnings"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(er)),e===a&&("forceplan"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(nr)),e===a&&("showplan_all"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(or)),e===a&&("showplan_text"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(ar)),e===a&&("showplan_xml"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(ur)),e===a&&("implicit_transactions"===t.substr(cs,21).toLowerCase()?(e=t.substr(cs,21),cs+=21):(e=a,0===vs&&js(sr)),e===a&&("remote_proc_transactions"===t.substr(cs,24).toLowerCase()?(e=t.substr(cs,24),cs+=24):(e=a,0===vs&&js(ir)),e===a&&("xact_abort"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(cr))))))))))))))))))))))))))))))))));e!==a&&(ls=r,e=lr(e));return r=e}()),n!==a&&Qf()!==a&&(o=Xf())!==a?(ls=r,e=function(t,r){return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:"set",expr:[{type:"assign",left:t,right:r}]}}}(n,o),r=e):(cs=r,r=a)):(cs=r,r=a));return r}())===a&&(r=function(){var r,e,n;r=cs,(e=function(){var r,e,n,o;r=cs,"lock"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(B));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&jl()!==a&&Qf()!==a&&(n=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=zs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=zs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=zs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=gr(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=r,o=n,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"lock",keyword:"tables",tables:o}},r=e):(cs=r,r=a);var o;return r}())===a&&(r=function(){var r,e;r=cs,(e=function(){var r,e,n,o;r=cs,"unlock"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Io));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&jl()!==a?(ls=r,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"unlock",keyword:"tables"}},r=e):(cs=r,r=a);return r}())===a&&(r=function(){var t,r,e,n,o,u;t=cs,(r=nl())!==a&&Qf()!==a&&(e=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Rs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Rs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Rs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=t,s=e,r={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"declare",declare:s,symbol:","}},t=r):(cs=t,t=a);var s;t===a&&(t=cs,(r=nl())!==a&&Qf()!==a&&(e=wf())!==a&&Qf()!==a&&(n=wc())!==a&&Qf()!==a?((o=dl())===a&&(o=null),o!==a&&Qf()!==a&&yl()!==a&&Qf()!==a&&(u=Us())!==a?(ls=t,i=n,c=o,l=u,r={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"declare",declare:[{at:"@",name:i,as:c&&c[0].toLowerCase(),keyword:"table",prefix:"table",definition:l}]}},t=r):(cs=t,t=a)):(cs=t,t=a));var i,c,l;return t}())===a&&(r=function(){var r,e,n,o;r=cs,"execute"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(E));e===a&&("exec"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(S)));e!==a&&Qf()!==a&&(n=Ai())!==a&&Qf()!==a?((o=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Vs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Vs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Vs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=pp(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(o=null),o!==a?(ls=r,u=e,s=n,i=o,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"exec",keyword:u,module:s,parameters:i}},r=e):(cs=r,r=a)):(cs=r,r=a);var u,s,i;return r}())===a&&(r=function(){var r,e,n,o,u,s,i,c,l,f;r=cs,"if"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(U));e!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&(o=gs())!==a&&Qf()!==a?((u=Pf())===a&&(u=null),u!==a&&Qf()!==a?((s=Sl())===a&&(s=null),s!==a&&Qf()!==a?(i=cs,(c=Wl())!==a&&(l=Qf())!==a&&(f=gs())!==a?i=c=[c,l,f]:(cs=i,i=a),i===a&&(i=null),i!==a&&(c=Qf())!==a?((l=Pf())===a&&(l=null),l!==a?(ls=r,p=n,b=o,v=u,h=s,d=i,y=l,e={tableList:Array.from(yp),columnList:hp(mp),ast:{type:"if",keyword:"if",boolean_expr:p,semicolons:[v||"",y||""],go:h,if_expr:b,else_expr:d&&d[2]}},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var p,b,v,h,d,y;return r}()),r}function gs(){var t;return(t=Ts())===a&&(t=function(){var t,r,e,n,o,u,s;t=cs,(r=Qf())!==a?((e=ti())===a&&(e=null),e!==a&&Qf()!==a&&al()!==a&&Qf()!==a&&(n=ji())!==a&&Qf()!==a&&hl()!==a&&Qf()!==a&&(o=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Ri())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ri())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ri())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&Qf()!==a?((u=ci())===a&&(u=null),u!==a&&Qf()!==a?((s=Si())===a&&(s=null),s!==a?(ls=t,r=function(t,r,e,n,o){const a={},u=t=>{const{server:r,db:e,schema:n,as:o,table:u,join:s}=t,i=s?"select":"update",c=[r,e,n].filter(Boolean).join(".")||null;e&&(a[u]=c),u&&yp.add(`${i}::${c}::${u}`)};return r&&r.forEach(u),n&&n.forEach(u),e&&e.forEach(t=>{if(t.table){const r=vp(t.table);yp.add(`update::${a[r]||null}::${r}`)}mp.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(yp),columnList:hp(mp),ast:{with:t,type:"update",table:r,set:e,from:n,where:o}}}(e,n,o,u,s),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,u,s;t=cs,(r=qi())!==a&&Qf()!==a?((e=bl())===a&&(e=null),e!==a&&Qf()!==a&&(n=Ai())!==a&&Qf()!==a?((o=Mi())===a&&(o=null),o!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(u=ic())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a&&(s=Vi())!==a?(ls=t,r=function(t,r,e,n,o){if(r&&(yp.add(`insert::${[r.server,r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`),r.as=null),n){let t=r&&r.table||null;Array.isArray(o)&&o.forEach((t,r)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),n.forEach(r=>mp.add(`insert::${t}::${r}`))}return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t,table:[r],columns:n,values:o,partition:e}}}(r,n,o,u,s),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,u,s;t=cs,(r=qi())!==a&&Qf()!==a?((e=fl())===a&&(e=null),e!==a&&Qf()!==a?((n=bl())===a&&(n=null),n!==a&&Qf()!==a&&(o=Ai())!==a&&Qf()!==a?((u=Mi())===a&&(u=null),u!==a&&Qf()!==a&&(s=Vi())!==a?(ls=t,r=function(t,r,e,n,o,a){n&&(yp.add(`insert::${[n.server,n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),mp.add(`insert::${n.table}::(.*)`),n.as=null);const u=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:u}}}(r,e,n,o,u,s),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o;t=cs,(r=il())!==a&&Qf()!==a?((e=ji())===a&&(e=null),e!==a&&Qf()!==a&&(n=ci())!==a&&Qf()!==a?((o=Si())===a&&(o=null),o!==a?(ls=t,r=function(t,r,e){if(r&&r.forEach(t=>{const{server:r,db:e,schema:n,as:o,table:a,join:u}=t,s=u?"select":"delete",i=[r,e,n].filter(Boolean).join(".")||null;a&&yp.add(`${s}::${i}::${a}`),u||mp.add(`delete::${a}::(.*)`)}),null===t&&1===r.length){const e=r[0];t=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(yp),columnList:hp(mp),ast:{type:"delete",table:t,from:r,where:e}}}(e,n,o),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(t=Cs())===a&&(t=function(){var t,r;if(t=[],(r=Yf())!==a)for(;r!==a;)t.push(r),r=Yf();else t=a;return t}()),t}function As(){var t,r;return t=cs,(r=gl())!==a&&Qf()!==a&&Rl()!==a?(ls=t,t=r="union all"):(cs=t,t=a),t===a&&(t=cs,(r=gl())!==a&&(ls=t,r="union"),t=r),t}function Ts(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Js())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=As())!==a&&(s=Qf())!==a&&(i=Js())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=As())!==a&&(s=Qf())!==a&&(i=Js())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a&&(n=Qf())!==a?((o=xi())===a&&(o=null),o!==a&&(u=Qf())!==a?((s=Ni())===a&&(s=null),s!==a?(ls=t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&(t._limit=n),{tableList:Array.from(yp),columnList:hp(mp),ast:t}}(r,e,o,s)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)}else cs=t,t=a;return t}function Es(){var t,r,e,n,o,u,s,c;if(t=cs,(r=Ss())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=Ss())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=Ss())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,t=r=i(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Ss(){var t,r,e;return t=cs,Mf()!==a&&Qf()!==a&&(r=sc())!==a&&Qf()!==a&&qf()!==a&&Qf()!==a?((e=kl())===a&&(e=Nl()),e!==a?(ls=t,t=c(r,e)):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,Mf()!==a&&Qf()!==a&&(r=sc())!==a&&Qf()!==a&&qf()!==a&&Qf()!==a?(ls=t,t=r):(cs=t,t=a),t===a&&(t=function(){var t,r,e;t=cs,(r=sc())!==a&&Qf()!==a?((e=kl())===a&&(e=Nl()),e!==a?(ls=t,r=c(r,e),t=r):(cs=t,t=a)):(cs=t,t=a);t===a&&(t=sc());return t}())),t}function _s(){var r,e;return r=cs,"if"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(b)),e!==a&&Qf()!==a&&Bl()!==a&&Qf()!==a&&Ql()!==a?(ls=r,r=e="IF NOT EXISTS"):(cs=r,r=a),r}function Us(){var t,r,e,n,o,u,s,i;if(t=cs,Rf()!==a)if(Qf()!==a)if((r=xs())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=xs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=xs())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a&&(n=Qf())!==a?((o=kf())===a&&(o=null),o!==a&&(u=Qf())!==a&&(s=Vf())!==a?(ls=t,t=v(r,e)):(cs=t,t=a)):(cs=t,t=a)}else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;return t}function xs(){var t;return(t=Hs())===a&&(t=ks())===a&&(t=Bs())===a&&(t=Fs()),t}function Is(){var r,e,n,o,u;return r=cs,(e=function(){var r,e;r=cs,(e=function(){var r,e,n,o;r=cs,"not null"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(co));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={type:"not null",value:"not null"});return r=e}())===a&&(e=Dc()),e!==a&&(ls=r,(u=e)&&!u.value&&(u.value="null"),e={nullable:u}),(r=e)===a&&(r=cs,(e=function(){var t,r;t=cs,Jc()!==a&&Qf()!==a&&(r=Gi())!==a?(ls=t,t={type:"default",value:r}):(cs=t,t=a);return t}())!==a&&(ls=r,e={default_val:e}),(r=e)===a&&(r=cs,(e=Ws())!==a&&(ls=r,e={check:e}),(r=e)===a&&(r=cs,"unique"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(h)),e!==a&&Qf()!==a?("key"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(d)),n===a&&(n=null),n!==a?(ls=r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"primary"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(y)),e===a&&(e=null),e!==a&&Qf()!==a?("key"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(d)),n!==a?(ls=r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=function(){var r,e,n,o,u,s,i,c,l,f,p,b;r=cs,"identity"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(m));e!==a&&Qf()!==a?(n=cs,(o=Rf())!==a&&(u=Qf())!==a&&(s=$c())!==a&&(i=Qf())!==a&&(c=kf())!==a&&(l=Qf())!==a&&(f=$c())!==a&&(p=Qf())!==a&&(b=Vf())!==a?n=o=[o,u,s,i,c,l,f,p,b]:(cs=n,n=a),n===a&&(n=null),n!==a?(ls=r,e={keyword:"identity",seed:(v=n)&&v[2],increment:v&&v[6],parentheses:!!v},r=e):(cs=r,r=a)):(cs=r,r=a);var v;return r}())!==a&&(ls=r,e={auto_increment:e}),(r=e)===a&&(r=cs,(e=Hf())!==a&&(ls=r,e={comment:e}),(r=e)===a&&(r=cs,(e=Ns())!==a&&(ls=r,e={collate:e}),(r=e)===a&&(r=cs,(e=function(){var r,e,n;r=cs,"column_format"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(j));e!==a&&Qf()!==a?("fixed"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(w)),n===a&&("dynamic"===t.substr(cs,7).toLowerCase()?(n=t.substr(cs,7),cs+=7):(n=a,0===vs&&js(O)),n===a&&("default"===t.substr(cs,7).toLowerCase()?(n=t.substr(cs,7),cs+=7):(n=a,0===vs&&js(L)))),n!==a?(ls=r,e={type:"column_format",value:n.toLowerCase()},r=e):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={column_format:e}),(r=e)===a&&(r=cs,(e=function(){var r,e,n;r=cs,"storage"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(C));e!==a&&Qf()!==a?("disk"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(g)),n===a&&("memory"===t.substr(cs,6).toLowerCase()?(n=t.substr(cs,6),cs+=6):(n=a,0===vs&&js(A))),n!==a?(ls=r,e={type:"storage",value:n.toLowerCase()},r=e):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={storage:e}),(r=e)===a&&(r=cs,(e=Gs())!==a&&(ls=r,e={reference_definition:e}),(r=e)===a&&(r=cs,(e=Ks())!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a&&(o=cc())!==a?(ls=r,r=e=function(t,r,e){return{character_set:{type:t,value:e,symbol:r}}}(e,n,o)):(cs=r,r=a)):(cs=r,r=a)))))))))))),r}function ks(){var t,r,e,n,o,u,s,i,c;return t=cs,(r=sc())!==a&&Qf()!==a&&(e=ap())!==a&&(n=Qf())!==a?((o=function(){var t,r,e,n,o,u;if(t=cs,(r=Is())!==a)if(Qf()!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=Is())!==a?n=o=[o,u]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=Is())!==a?n=o=[o,u]:(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(cs=t,t=a)}else cs=t,t=a;else cs=t,t=a;return t}())===a&&(o=null),o!==a?(ls=t,s=r,i=e,c=o,mp.add(`create::${s.table}::${s.column}`),t=r={column:s,definition:i,resource:"column",...c||{}}):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,(r=sc())!==a&&Qf()!==a?(e=cs,(n=dl())!==a&&(o=Qf())!==a&&(u=Gi())!==a?e=n=[n,o,u]:(cs=e,e=a),e===a&&(e=null),e!==a?(ls=t,t=r=function(t,r){return r&&(t.as=r[2]),{column:t,resource:"column"}}(r,e)):(cs=t,t=a)):(cs=t,t=a)),t}function Ns(){var r,e,n;return r=cs,function(){var r,e,n,o;r=cs,"collate"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(ct));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="COLLATE"):(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a?((e=Lf())===a&&(e=null),e!==a&&Qf()!==a&&(n=lc())!==a?(ls=r,r={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(cs=r,r=a)):(cs=r,r=a),r}function Rs(){var r,e,n,o,u,s,i,c,l,f,p;return r=cs,wf()!==a&&Qf()!==a&&(e=wc())!==a&&Qf()!==a?((n=dl())===a&&(n=null),n!==a&&Qf()!==a&&(o=ap())!==a&&Qf()!==a?(u=cs,(s=Lf())!==a&&(i=Qf())!==a&&(c=Gi())!==a?u=s=[s,i,c]:(cs=u,u=a),u===a&&(u=null),u!==a?(ls=r,f=o,p=u,r={at:"@",name:e,as:(l=n)&&l[0].toLowerCase(),datatype:f,keyword:"variable",definition:p&&{type:"default",keyword:p[0],value:p[2]}}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,wf()!==a&&Qf()!==a&&(e=wc())!==a&&Qf()!==a?("cursor"===t.substr(cs,6).toLowerCase()?(n=t.substr(cs,6),cs+=6):(n=a,0===vs&&js(T)),n!==a?(ls=r,r=function(t,r){return{at:"@",name:r,keyword:"cursor",prefix:"cursor"}}(0,e)):(cs=r,r=a)):(cs=r,r=a)),r}function Vs(){var r,e,n,o;return r=cs,64===t.charCodeAt(cs)?(e="@",cs++):(e=a,0===vs&&js(_)),e!==a&&(n=lc())!==a&&Qf()!==a&&Lf()!==a&&Qf()!==a&&(o=Gi())!==a?(ls=r,r=e={type:"variable",name:n,value:o}):(cs=r,r=a),r}function Ms(){var r,e,n;return r=cs,"if"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(U)),e!==a&&Qf()!==a?("exists"===t.substr(cs,6).toLowerCase()?(n=t.substr(cs,6),cs+=6):(n=a,0===vs&&js(x)),n!==a?(ls=r,r=e="if exists"):(cs=r,r=a)):(cs=r,r=a),r}function qs(){var r;return"encryption"===t.substr(cs,10).toLowerCase()?(r=t.substr(cs,10),cs+=10):(r=a,0===vs&&js(k)),r===a&&("schemabinding"===t.substr(cs,13).toLowerCase()?(r=t.substr(cs,13),cs+=13):(r=a,0===vs&&js(N)),r===a&&("view_metadata"===t.substr(cs,13).toLowerCase()?(r=t.substr(cs,13),cs+=13):(r=a,0===vs&&js(R)))),r}function Ps(){var r;return(r=function(){var t,r;t=cs,Cf()!==a&&Qf()!==a&&(r=Hs())!==a?(ls=t,t={action:"add",create_definitions:r,resource:"constraint",type:"alter"}):(cs=t,t=a);return t}())===a&&(r=function(){var t,r,e,n;t=cs,(r=el())!==a&&Qf()!==a&&(e=xf())!==a&&Qf()!==a&&(n=wc())!==a?(ls=t,r={action:"drop",constraint:n,keyword:e.toLowerCase(),resource:"constraint",type:"alter"},t=r):(cs=t,t=a);return t}())===a&&(r=function(){var r,e,n,o;r=cs,El()!==a&&Qf()!==a?("check"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(V)),e!==a&&Qf()!==a?("check"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(V)),n!==a&&Qf()!==a&&xf()!==a&&Qf()!==a&&(o=wc())!==a?(ls=r,r={action:"with",constraint:o,keyword:"check check constraint",resource:"constraint",type:"alter"}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n;r=cs,"nocheck"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(W));e!==a&&Qf()!==a&&xf()!==a&&Qf()!==a&&(n=wc())!==a?(ls=r,r=e={action:"nocheck",keyword:"constraint",constraint:n,resource:"constraint",type:"alter"}):(cs=r,r=a);return r}())===a&&(r=function(){var t,r,e,n;t=cs,(r=Cf())===a&&(r=ol());r!==a&&Qf()!==a?((e=gf())===a&&(e=null),e!==a&&Qf()!==a&&(n=ks())!==a?(ls=t,o=e,u=n,r={action:r.toLowerCase(),...u,keyword:o,resource:"column",type:"alter"},t=r):(cs=t,t=a)):(cs=t,t=a);var o,u;return t}())===a&&(r=function(){var t,r,e;t=cs,el()!==a&&Qf()!==a?((r=gf())===a&&(r=null),r!==a&&Qf()!==a&&(e=sc())!==a?(ls=t,t={action:"drop",column:e,keyword:r,resource:"column",type:"alter"}):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(r=function(){var t,r,e;t=cs,(r=Cf())!==a&&Qf()!==a&&(e=Bs())!==a?(ls=t,n=e,r={action:"add",type:"alter",...n},t=r):(cs=t,t=a);var n;return t}())===a&&(r=function(){var t,r,e;t=cs,(r=Cf())!==a&&Qf()!==a&&(e=Fs())!==a?(ls=t,n=e,r={action:"add",type:"alter",...n},t=r):(cs=t,t=a);var n;return t}())===a&&(r=function(){var t,r,e,n;t=cs,(r=ll())!==a&&Qf()!==a?((e=tl())===a&&(e=dl()),e===a&&(e=null),e!==a&&Qf()!==a&&(n=lc())!==a?(ls=t,u=n,r={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},t=r):(cs=t,t=a)):(cs=t,t=a);var o,u;return t}())===a&&(r=Ds())===a&&(r=Qs()),r}function Ds(){var r,e,n,o;return r=cs,"algorithm"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(q)),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a?("default"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(L)),o===a&&("instant"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(P)),o===a&&("inplace"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(D)),o===a&&("copy"===t.substr(cs,4).toLowerCase()?(o=t.substr(cs,4),cs+=4):(o=a,0===vs&&js(Q))))),o!==a?(ls=r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function Qs(){var r,e,n,o;return r=cs,"lock"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(B)),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a?("default"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(L)),o===a&&("none"===t.substr(cs,4).toLowerCase()?(o=t.substr(cs,4),cs+=4):(o=a,0===vs&&js(F)),o===a&&("shared"===t.substr(cs,6).toLowerCase()?(o=t.substr(cs,6),cs+=6):(o=a,0===vs&&js(H)),o===a&&("exclusive"===t.substr(cs,9).toLowerCase()?(o=t.substr(cs,9),cs+=9):(o=a,0===vs&&js($))))),o!==a?(ls=r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function Bs(){var t,r,e,n,o,u,s,i;return t=cs,(r=Af())===a&&(r=Tf()),r!==a&&Qf()!==a?((e=mc())===a&&(e=null),e!==a&&Qf()!==a?((n=bi())===a&&(n=null),n!==a&&Qf()!==a&&(o=ei())!==a&&Qf()!==a?((u=hi())===a&&(u=null),u!==a&&Qf()!==a?(ls=t,s=n,i=u,t=r={index:e,definition:o,keyword:r.toLowerCase(),index_type:s,resource:"index",index_options:i}):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a),t}function Fs(){var r,e,n,o,u,s,i,c,l;return r=cs,(e=function(){var r,e,n,o;r=cs,"fulltext"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Mu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="FULLTEXT"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=cs,"spatial"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(qu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SPATIAL"):(cs=r,r=a)):(cs=r,r=a);return r}()),e!==a&&Qf()!==a?((n=Af())===a&&(n=Tf()),n===a&&(n=null),n!==a&&Qf()!==a?((o=mc())===a&&(o=null),o!==a&&Qf()!==a&&(u=ei())!==a&&Qf()!==a?((s=hi())===a&&(s=null),s!==a&&Qf()!==a?(ls=r,i=e,l=s,r=e={index:o,definition:u,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function Hs(){var r;return(r=function(){var r,e,n,o,u,s;r=cs,(e=$s())===a&&(e=null);e!==a&&Qf()!==a?("primary key"===t.substr(cs,11).toLowerCase()?(n=t.substr(cs,11),cs+=11):(n=a,0===vs&&js(G)),n!==a&&Qf()!==a?((o=bi())===a&&(o=null),o!==a&&Qf()!==a&&(u=ei())!==a&&Qf()!==a?((s=function(){var t,r,e,n;t=cs,(r=El())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(e=vi())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a&&wl()!==a&&Qf()!==a&&Mf()!==a&&Qf()!==a&&(n=wc())!==a&&Qf()!==a&&qf()!==a?(ls=t,t=r={with:e,on:n}):(cs=t,t=a);t===a&&(t=cs,(r=hi())===a&&(r=vi()),r!==a&&(ls=t,r=function(t){return{index_options:t}}(r)),t=r);return t}())===a&&(s=null),s!==a?(ls=r,c=n,l=o,f=u,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",...p},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var i,c,l,f,p;return r}())===a&&(r=function(){var t,r,e,n,o,u,s,i;t=cs,(r=$s())===a&&(r=null);r!==a&&Qf()!==a&&(e=Ef())!==a&&Qf()!==a?((n=Af())===a&&(n=Tf()),n===a&&(n=null),n!==a&&Qf()!==a?((o=mc())===a&&(o=null),o!==a&&Qf()!==a?((u=bi())===a&&(u=null),u!==a&&Qf()!==a&&(s=ei())!==a&&Qf()!==a?((i=hi())===a&&(i=null),i!==a?(ls=t,l=e,f=n,p=o,b=u,v=s,h=i,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:h},t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);var c,l,f,p,b,v,h;return t}())===a&&(r=function(){var r,e,n,o,u,s;r=cs,(e=$s())===a&&(e=null);e!==a&&Qf()!==a?("foreign key"===t.substr(cs,11).toLowerCase()?(n=t.substr(cs,11),cs+=11):(n=a,0===vs&&js(Z)),n!==a&&Qf()!==a?((o=mc())===a&&(o=null),o!==a&&Qf()!==a&&(u=ei())!==a&&Qf()!==a?((s=Gs())===a&&(s=null),s!==a?(ls=r,c=n,l=o,f=u,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var i,c,l,f,p;return r}())===a&&(r=Ws())===a&&(r=function(){var r,e,n,o,u,s,i,c,l,f;r=cs,(e=$s())===a&&(e=null);e!==a&&Qf()!==a&&(n=Jc())!==a&&Qf()!==a&&(o=Yi())!==a&&Qf()!==a?("for"===t.substr(cs,3).toLowerCase()?(u=t.substr(cs,3),cs+=3):(u=a,0===vs&&js(K)),u!==a&&Qf()!==a?((s=mc())===a&&(s=null),s!==a&&Qf()!==a?(i=cs,(c=El())!==a&&(l=Qf())!==a&&(f=Al())!==a?i=c=[c,l,f]:(cs=i,i=a),i===a&&(i=null),i!==a?(ls=r,p=e,b=o,v=s,h=i,e={constraint_type:n[0].toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,definition:[b],resource:"constraint",for:v,with_values:h&&{type:"origin",value:"with values"}},r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);var p,b,v,h;return r}()),r}function $s(){var t,r,e,n;return t=cs,(r=xf())!==a&&Qf()!==a?((e=lc())===a&&(e=null),e!==a?(ls=t,n=e,t=r={keyword:r.toLowerCase(),constraint:n}):(cs=t,t=a)):(cs=t,t=a),t}function Ws(){var r,e,n,o,u,s,i,c,l,f,p,b,v;return r=cs,(e=$s())===a&&(e=null),e!==a&&Qf()!==a?("check"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(V)),n!==a&&Qf()!==a?(o=cs,"not"===t.substr(cs,3).toLowerCase()?(u=t.substr(cs,3),cs+=3):(u=a,0===vs&&js(Y)),u!==a&&(s=Qf())!==a?("for"===t.substr(cs,3).toLowerCase()?(i=t.substr(cs,3),cs+=3):(i=a,0===vs&&js(K)),i!==a&&(c=Qf())!==a?("replication"===t.substr(cs,11).toLowerCase()?(l=t.substr(cs,11),cs+=11):(l=a,0===vs&&js(X)),l!==a&&(f=Qf())!==a?o=u=[u,s,i,c,l,f]:(cs=o,o=a)):(cs=o,o=a)):(cs=o,o=a),o===a&&(o=null),o!==a&&(u=Rf())!==a&&(s=Qf())!==a&&(i=Yi())!==a&&(c=Qf())!==a&&(l=Vf())!==a?(ls=r,p=e,b=o,v=i,r=e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication",type:""},definition:[v],resource:"constraint"}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function Gs(){var r,e,n,o,u,s,i,c,l,f;return r=cs,(e=function(){var r,e,n,o;r=cs,"references"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(Fu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="REFERENCES"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&(n=ji())!==a&&Qf()!==a&&(o=ei())!==a&&Qf()!==a?("match full"===t.substr(cs,10).toLowerCase()?(u=t.substr(cs,10),cs+=10):(u=a,0===vs&&js(z)),u===a&&("match partial"===t.substr(cs,13).toLowerCase()?(u=t.substr(cs,13),cs+=13):(u=a,0===vs&&js(J)),u===a&&("match simple"===t.substr(cs,12).toLowerCase()?(u=t.substr(cs,12),cs+=12):(u=a,0===vs&&js(tt)))),u===a&&(u=null),u!==a&&Qf()!==a?((s=Ys())===a&&(s=null),s!==a&&Qf()!==a?((i=Ys())===a&&(i=null),i!==a?(ls=r,c=u,l=s,f=i,r=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Ys())!==a&&(ls=r,e={on_action:[e]}),r=e),r}function Ys(){var r,e,n,o;return r=cs,wl()!==a&&Qf()!==a?((e=il())===a&&(e=al()),e!==a&&Qf()!==a&&(n=function(){var r,e,n;r=cs,(e=jf())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a?((n=Qi())===a&&(n=null),n!==a&&Qf()!==a&&Vf()!==a?(ls=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(cs=r,r=a)):(cs=r,r=a);r===a&&(r=cs,"restrict"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(rt)),e===a&&("cascade"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(et)),e===a&&("set null"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(nt)),e===a&&("no action"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ot)),e===a&&("set default"===t.substr(cs,11).toLowerCase()?(e=t.substr(cs,11),cs+=11):(e=a,0===vs&&js(at)),e===a&&(e=jf()))))),e!==a&&(ls=r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==a?(ls=r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(cs=r,r=a)):(cs=r,r=a),r}function Ks(){var r,e,n;return r=cs,"character"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ut)),e!==a&&Qf()!==a?("set"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(st)),n!==a?(ls=r,r=e="CHARACTER SET"):(cs=r,r=a)):(cs=r,r=a),r}function Xs(){var r,e,n,o,u,s,i,c,l;return r=cs,(e=Jc())===a&&(e=null),e!==a&&Qf()!==a?((n=Ks())===a&&("charset"===t.substr(cs,7).toLowerCase()?(n=t.substr(cs,7),cs+=7):(n=a,0===vs&&js(it)),n===a&&("collate"===t.substr(cs,7).toLowerCase()?(n=t.substr(cs,7),cs+=7):(n=a,0===vs&&js(ct)))),n!==a&&Qf()!==a?((o=Lf())===a&&(o=null),o!==a&&Qf()!==a&&(u=cc())!==a?(ls=r,i=n,c=o,l=u,r=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function Zs(){var r,e,n,o,u,s,i,c,l;return r=cs,"auto_increment"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(lt)),e===a&&("avg_row_length"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(ft)),e===a&&("key_block_size"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(pt)),e===a&&("max_rows"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(bt)),e===a&&("min_rows"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(vt)),e===a&&("stats_sample_pages"===t.substr(cs,18).toLowerCase()?(e=t.substr(cs,18),cs+=18):(e=a,0===vs&&js(ht))))))),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a&&(o=$c())!==a?(ls=r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=Xs())===a&&(r=cs,(e=Uf())===a&&("connection"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(dt))),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a&&(o=Qc())!==a?(ls=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"compression"===t.substr(cs,11).toLowerCase()?(e=t.substr(cs,11),cs+=11):(e=a,0===vs&&js(yt)),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a?(o=cs,39===t.charCodeAt(cs)?(u="'",cs++):(u=a,0===vs&&js(mt)),u!==a?("zlib"===t.substr(cs,4).toLowerCase()?(s=t.substr(cs,4),cs+=4):(s=a,0===vs&&js(jt)),s===a&&("lz4"===t.substr(cs,3).toLowerCase()?(s=t.substr(cs,3),cs+=3):(s=a,0===vs&&js(wt)),s===a&&("none"===t.substr(cs,4).toLowerCase()?(s=t.substr(cs,4),cs+=4):(s=a,0===vs&&js(F)))),s!==a?(39===t.charCodeAt(cs)?(i="'",cs++):(i=a,0===vs&&js(mt)),i!==a?o=u=[u,s,i]:(cs=o,o=a)):(cs=o,o=a)):(cs=o,o=a),o!==a?(ls=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"engine"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Ot)),e!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a&&(o=wc())!==a?(ls=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}}(e,n,o)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=wl())!==a&&Qf()!==a&&(n=Mf())!==a&&Qf()!==a&&(o=wc())!==a&&(u=Qf())!==a&&(s=qf())!==a?(ls=r,r=e={keyword:"on",value:`[${o}]`}):(cs=r,r=a),r===a&&(r=cs,"textimage_on"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Lt)),e!==a&&Qf()!==a&&(n=Mf())!==a&&Qf()!==a&&(o=wc())!==a&&(u=Qf())!==a&&(s=qf())!==a?(ls=r,r=e={keyword:"textimage_on",value:`[${o}]`}):(cs=r,r=a)))))),r}function zs(){var r,e,n,o,u;return r=cs,(e=Ci())!==a&&Qf()!==a&&(n=function(){var r,e,n;return r=cs,"read"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(wr)),e!==a&&Qf()!==a?("local"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(Or)),n===a&&(n=null),n!==a?(ls=r,r=e={type:"read",suffix:n&&"local"}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"low_priority"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Lr)),e===a&&(e=null),e!==a&&Qf()!==a?("write"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(Cr)),n!==a?(ls=r,r=e={type:"write",prefix:e&&"low_priority"}):(cs=r,r=a)):(cs=r,r=a)),r}())!==a?(ls=r,o=e,u=n,yp.add(`lock::${[o.server,o.db,o.schema].filter(Boolean).join(".")||null}::${o.table}`),r=e={table:o,lock_type:u}):(cs=r,r=a),r}function Js(){var r,e,n,o,u,s,i;return(r=ni())===a&&(r=cs,e=cs,40===t.charCodeAt(cs)?(n="(",cs++):(n=a,0===vs&&js(Ar)),n!==a&&(o=Qf())!==a&&(u=Js())!==a&&(s=Qf())!==a?(41===t.charCodeAt(cs)?(i=")",cs++):(i=a,0===vs&&js(Tr)),i!==a?e=n=[n,o,u,s,i]:(cs=e,e=a)):(cs=e,e=a),e!==a&&(ls=r,e={...e[2],parentheses_symbol:!0}),r=e),r}function ti(){var r,e,n,o,u,s,i,c,l;if(r=cs,El()!==a)if(Qf()!==a)if((e=ri())!==a){for(n=[],o=cs,(u=Qf())!==a&&(s=kf())!==a&&(i=Qf())!==a&&(c=ri())!==a?o=u=[u,s,i,c]:(cs=o,o=a);o!==a;)n.push(o),o=cs,(u=Qf())!==a&&(s=kf())!==a&&(i=Qf())!==a&&(c=ri())!==a?o=u=[u,s,i,c]:(cs=o,o=a);n!==a?(ls=r,r=v(e,n)):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;return r===a&&(r=cs,Qf()!==a&&El()!==a&&(e=Qf())!==a&&(n=function(){var r,e,n,o;r=cs,"recursive"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Ao));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(o=Qf())!==a&&(u=ri())!==a?(ls=r,(l=u).recursive=!0,r=[l]):(cs=r,r=a)),r}function ri(){var t,r,e,n,o;return t=cs,(r=Qc())===a&&(r=wc()),r!==a&&Qf()!==a?((e=ei())===a&&(e=null),e!==a&&Qf()!==a&&dl()!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Ts())!==a&&Qf()!==a&&Vf()!==a?(ls=t,"string"==typeof(o=r)&&(o={type:"default",value:o}),t=r={name:o,stmt:n,columns:e}):(cs=t,t=a)):(cs=t,t=a),t}function ei(){var t,r;return t=cs,Rf()!==a&&Qf()!==a&&(r=_i())!==a&&Qf()!==a&&Vf()!==a?(ls=t,t=r):(cs=t,t=a),t}function ni(){var r,e,n,o,u,s,i,c,l,f,p,b,v,h,d,y,m,j,w,O,L,C,g,A,T,E,S;return r=cs,Qf()!==a?((e=ti())===a&&(e=null),e!==a&&Qf()!==a&&function(){var r,e,n,o;r=cs,"select"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(jo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Bf()!==a?((n=function(){var t,r,e,n,o,u;if(t=cs,(r=oi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=oi())!==a?n=o=[o,u]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=oi())!==a?n=o=[o,u]:(cs=n,n=a);e!==a?(ls=t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(n=null),n!==a&&Qf()!==a?((o=Vl())===a&&(o=null),o!==a&&Qf()!==a?((u=function(){var r,e,n,o,u;r=cs,(e=rl())!==a&&Qf()!==a&&(n=Rf())!==a&&Qf()!==a&&(o=Wc())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?("percent"===t.substr(cs,7).toLowerCase()?(u=t.substr(cs,7),cs+=7):(u=a,0===vs&&js(Er)),u===a&&(u=null),u!==a?(ls=r,e={value:o,percent:(s=u)&&s.toLowerCase(),parentheses:!0},r=e):(cs=r,r=a)):(cs=r,r=a);var s;r===a&&(r=cs,(e=rl())!==a&&Qf()!==a&&(n=Wc())!==a&&Qf()!==a?("percent"===t.substr(cs,7).toLowerCase()?(o=t.substr(cs,7),cs+=7):(o=a,0===vs&&js(Er)),o===a&&(o=null),o!==a?(ls=r,e=function(t,r){return{value:t,percent:r&&r.toLowerCase()}}(n,o),r=e):(cs=r,r=a)):(cs=r,r=a));return r}())===a&&(u=null),u!==a&&Qf()!==a&&(s=ai())!==a&&Qf()!==a?((i=function(){var t,r;t=cs,bl()!==a&&Qf()!==a&&(r=lc())!==a?(ls=t,t={type:"into",expr:r}):(cs=t,t=a);return t}())===a&&(i=null),i!==a&&Qf()!==a?((c=ci())===a&&(c=null),c!==a&&Qf()!==a?((l=Si())===a&&(l=null),l!==a&&Qf()!==a?((f=function(){var t,r,e;t=cs,(r=_l())!==a&&Qf()!==a&&Ul()!==a&&Qf()!==a&&(e=Qi())!==a?(ls=t,r={columns:e.value},t=r):(cs=t,t=a);return t}())===a&&(f=null),f!==a&&Qf()!==a?((p=function(){var r,e;r=cs,function(){var r,e,n,o;r=cs,"having"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(na));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(e=Yi())!==a?(ls=r,r=e):(cs=r,r=a);return r}())===a&&(p=null),p!==a&&Qf()!==a?((b=xi())===a&&(b=null),b!==a&&Qf()!==a?((v=Ni())===a&&(v=null),v!==a&&Qf()!==a?((h=function(){var r;(r=function(){var r,e,n,o;r=cs,"for"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(K));e!==a&&Qf()!==a?("json"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(Re)),n!==a&&Qf()!==a&&(o=function(){var r,e;r=cs,"path"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(ke));e!==a&&(ls=r,e=Ie(e));return r=e}())!==a?(ls=r,u=o,e={type:"for json",...u},r=e):(cs=r,r=a)):(cs=r,r=a);var u;return r}())===a&&(r=function(){var r,e,n,o;r=cs,"for"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(K));e!==a&&Qf()!==a?("xml"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(Ne)),n!==a&&Qf()!==a&&(o=function(){var r,e,n,o,u,s,i,c;r=cs,"raw"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(_e));e===a&&("auto"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Ue)),e===a&&("explicit"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(xe))));e!==a&&(ls=r,e=Ie(e));(r=e)===a&&(r=cs,"path"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(ke)),e!==a&&Qf()!==a?(n=cs,(o=Rf())!==a&&(u=Qf())!==a?((s=sc())===a&&(s=Qc()),s===a&&(s=null),s!==a&&(i=Qf())!==a&&(c=Vf())!==a?n=o=[o,u,s,i,c]:(cs=n,n=a)):(cs=n,n=a),n===a&&(n=null),n!==a?(ls=r,e={keyword:e,expr:(l=n)&&l[2]},r=e):(cs=r,r=a)):(cs=r,r=a));var l;return r}())!==a?(ls=r,u=o,e={type:"for xml",...u},r=e):(cs=r,r=a)):(cs=r,r=a);var u;return r}());return r}())===a&&(h=null),h!==a?(ls=r,d=e,y=n,m=o,j=u,w=s,O=i,C=l,g=f,A=p,T=b,E=v,S=h,(L=c)&&L.forEach(t=>t.table&&yp.add(`select::${[t.server,t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={with:d,type:"select",options:y,distinct:m,columns:w,into:{...O||{},position:O&&"column"},from:L,for:S,where:C,groupby:g,having:A,top:j,orderby:T,limit:E}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r}function oi(){var r,e;return r=cs,(e=function(){var r;"sql_calc_found_rows"===t.substr(cs,19).toLowerCase()?(r=t.substr(cs,19),cs+=19):(r=a,0===vs&&js(Hu));return r}())===a&&((e=function(){var r;"sql_cache"===t.substr(cs,9).toLowerCase()?(r=t.substr(cs,9),cs+=9):(r=a,0===vs&&js($u));return r}())===a&&(e=function(){var r;"sql_no_cache"===t.substr(cs,12).toLowerCase()?(r=t.substr(cs,12),cs+=12):(r=a,0===vs&&js(Wu));return r}()),e===a&&(e=function(){var r;"sql_big_result"===t.substr(cs,14).toLowerCase()?(r=t.substr(cs,14),cs+=14):(r=a,0===vs&&js(Yu));return r}())===a&&(e=function(){var r;"sql_small_result"===t.substr(cs,16).toLowerCase()?(r=t.substr(cs,16),cs+=16):(r=a,0===vs&&js(Gu));return r}())===a&&(e=function(){var r;"sql_buffer_result"===t.substr(cs,17).toLowerCase()?(r=t.substr(cs,17),cs+=17):(r=a,0===vs&&js(Ku));return r}())),e!==a&&(ls=r,e=e),r=e}function ai(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Rl())===a&&(r=cs,(e=Nf())!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r===a&&(r=Nf())),r!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=ui())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=ui())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){mp.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?pp(e,r):[e]}(0,e)):(cs=t,t=a)}else cs=t,t=a;if(t===a)if(t=cs,(r=ui())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=ui())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=ui())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=v(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function ui(){var t,r,e,n,o;return t=cs,r=cs,(e=lc())!==a&&(n=Qf())!==a&&(o=If())!==a?r=e=[e,n,o]:(cs=r,r=a),r===a&&(r=null),r!==a&&(e=Qf())!==a&&(n=Nf())!==a?(ls=t,t=r=function(t){const r=t&&t[0]||null;return mp.add(`select::${r}::(.*)`),{expr:{type:"column_ref",table:r,column:"*"},as:null}}(r)):(cs=t,t=a),t===a&&(t=cs,(r=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Gi())!==a){for(e=[],n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl())===a&&(u=Df()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl())===a&&(u=Df()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,r=function(t,r){const e=t.ast;if(e&&"select"===e.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!r||0===r.length)return t;const n=r.length;let o=r[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?t:r[e-1][3];o=lp(r[e][1],n,o)}return o}(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&(e=Qf())!==a?((n=ii())===a&&(n=null),n!==a?(ls=t,t=r={expr:r,as:n}):(cs=t,t=a)):(cs=t,t=a)),t}function si(){var t,r,e,n,o,u,s,i,c,l,f,p;return t=cs,(r=dl())===a&&(r=null),r!==a&&Qf()!==a&&(e=lc())!==a?(n=cs,(o=Qf())!==a&&(u=Rf())!==a&&(s=Qf())!==a&&(i=ic())!==a&&(c=Qf())!==a&&(l=Vf())!==a?n=o=[o,u,s,i,c,l]:(cs=n,n=a),n===a&&(n=null),n!==a?(ls=t,f=e,t=r=(p=n)?`${f}(${p[3].join(", ")})`:f):(cs=t,t=a)):(cs=t,t=a),t}function ii(){var t,r,e;return t=cs,(r=dl())!==a&&Qf()!==a&&(e=function(){var t,r;t=cs,(r=wc())!==a?(ls=cs,(function(t){if(!0===sp[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(r)?a:void 0)!==a?(ls=t,t=r=r):(cs=t,t=a)):(cs=t,t=a);t===a&&(t=cs,(r=pc())!==a&&(ls=t,r=r),t=r);return t}())!==a?(ls=t,t=r=e):(cs=t,t=a),t===a&&(t=cs,(r=dl())===a&&(r=null),r!==a&&Qf()!==a&&(e=lc())!==a?(ls=t,t=r=e):(cs=t,t=a)),t}function ci(){var r,e,n,o,u;return r=cs,vl()!==a&&Qf()!==a&&(e=ji())!==a&&Qf()!==a?((n=function(){var r,e,n,o,u;r=cs,(e=function(){var r,e,n,o;r=cs,"pivot"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Eu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="PIVOT"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Tc())!==a&&Qf()!==a&&(o=fi())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((u=ii())===a&&(u=null),u!==a?(ls=r,s=o,i=u,e={type:"pivot",expr:n,...s,as:i},r=e):(cs=r,r=a)):(cs=r,r=a);var s,i;r===a&&(r=cs,(e=function(){var r,e,n,o;r=cs,"unpivot"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Su));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="UNPIVOT"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=sc())!==a&&Qf()!==a&&(o=fi())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((u=ii())===a&&(u=null),u!==a?(ls=r,e=function(t,r,e){return{type:"unpivot",expr:t,...r,as:e}}(n,o,u),r=e):(cs=r,r=a)):(cs=r,r=a));return r}())===a&&(n=null),n!==a?(ls=r,u=n,(o=e)[0]&&(o[0].operator=u),r=o):(cs=r,r=a)):(cs=r,r=a),r}function li(){var r,e,n,o;return r=cs,"for"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(K)),e!==a&&Qf()!==a?("system_time"===t.substr(cs,11).toLowerCase()?(n=t.substr(cs,11),cs+=11):(n=a,0===vs&&js(Sr)),n!==a&&Qf()!==a&&(o=function(){var r,e,n,o,u;r=cs,(e=dl())!==a&&Qf()!==a?("of"===t.substr(cs,2).toLowerCase()?(n=t.substr(cs,2),cs+=2):(n=a,0===vs&&js(_r)),n!==a&&Qf()!==a&&(o=Gi())!==a?(ls=r,r=e={type:"temporal_table_option",keyword:"as",of:o}):(cs=r,r=a)):(cs=r,r=a);r===a&&(r=cs,(e=vl())!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&(o=tl())!==a&&Qf()!==a&&(u=Gi())!==a?(ls=r,e=function(t,r){return{type:"temporal_table_option",keyword:"from_to",from:t,to:r}}(n,u),r=e):(cs=r,r=a),r===a&&(r=cs,(e=Ml())!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&(o=Fl())!==a&&Qf()!==a&&(u=Gi())!==a?(ls=r,r=e={type:"temporal_table_option",keyword:"between_and",between:n,and:u}):(cs=r,r=a),r===a&&(r=cs,"contained"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Ur)),e!==a&&Qf()!==a&&(n=ql())!==a&&Qf()!==a&&(o=Rf())!==a&&Qf()!==a&&(u=Qi())!==a&&Qf()!==a&&Vf()!==a?(ls=r,e=function(t){return t.parentheses=!0,{type:"temporal_table_option",keyword:"contained",in:t}}(u),r=e):(cs=r,r=a))));return r}())!==a?(ls=r,r=e={keyword:"for system_time",expr:o}):(cs=r,r=a)):(cs=r,r=a),r}function fi(){var r,e,n,o;return r=cs,"for"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(K)),e!==a&&Qf()!==a&&(n=sc())!==a&&Qf()!==a&&(o=tc())!==a?(ls=r,r=e={column:n,in_expr:o}):(cs=r,r=a),r}function pi(){var t,r,e;return t=cs,(r=Ai())!==a&&Qf()!==a&&tl()!==a&&Qf()!==a&&(e=Ai())!==a?(ls=t,t=r=[r,e]):(cs=t,t=a),t}function bi(){var r,e,n;return r=cs,(e=Tl())!==a&&Qf()!==a?("btree"===t.substr(cs,5).toLowerCase()?(n=t.substr(cs,5),cs+=5):(n=a,0===vs&&js(xr)),n===a&&("hash"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(Ir))),n!==a?(ls=r,r=e={keyword:"using",type:n.toLowerCase()}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Sf())===a&&(e=_f()),e!==a&&(ls=r,e={keyword:e.toLowerCase()}),r=e),r}function vi(){var t,r,e,n,o,u,s,c;if(t=cs,(r=mi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=mi())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=mi())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,t=r=i(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function hi(){var t,r,e,n,o,u;if(t=cs,(r=mi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=mi())!==a?n=o=[o,u]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=mi())!==a?n=o=[o,u]:(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function di(){var t,r,e,n;return t=cs,(r=$c())!==a&&Qf()!==a&&(e=tl())!==a&&Qf()!==a&&$c()!==a?(ls=t,n=r,t=r={type:"range",symbol:e[0],start:n,end:n}):(cs=t,t=a),t===a&&(t=$c()),t}function yi(){var r,e,n;return r=cs,wl()!==a&&Qf()!==a?("partitions"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(kr)),e!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=function(){var t,r,e,n,o,u,s,c;if(t=cs,(r=di())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=di())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=di())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,t=r=i(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&Qf()!==a&&Vf()!==a?(ls=r,r={type:"on partitions",partitions:n}):(cs=r,r=a)):(cs=r,r=a),r}function mi(){var r,e,n,o,u,s;return r=cs,(e=function(){var r,e,n,o;r=cs,"key_block_size"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(pt));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="KEY_BLOCK_SIZE"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a?((n=Lf())===a&&(n=null),n!==a&&Qf()!==a&&(o=$c())!==a?(ls=r,r=e=Nr(e,n,o)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"fillfactor"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(Rr)),e===a&&("max_duration"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Vr)),e===a&&("maxdop"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Mr)))),e!==a&&Qf()!==a&&(n=Lf())!==a&&Qf()!==a&&(o=$c())!==a?(ls=r,r=e=Nr(e,n,o)):(cs=r,r=a),r===a&&(r=bi())===a&&(r=cs,"with"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(qr)),e!==a&&Qf()!==a?("parser"===t.substr(cs,6).toLowerCase()?(n=t.substr(cs,6),cs+=6):(n=a,0===vs&&js(Pr)),n!==a&&Qf()!==a&&(o=wc())!==a?(ls=r,r=e={type:"with parser",expr:o}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"visible"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Dr)),e===a&&("invisible"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Qr))),e!==a&&(ls=r,e={type:(s=e).toLowerCase(),expr:s.toLowerCase()}),(r=e)===a&&(r=cs,"pad_index"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Br)),e===a&&("sort_in_tempdb"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(Fr)),e===a&&("ignore_dup_key"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(Hr)),e===a&&("statistics_norecompute"===t.substr(cs,22).toLowerCase()?(e=t.substr(cs,22),cs+=22):(e=a,0===vs&&js($r)),e===a&&("statistics_incremental"===t.substr(cs,22).toLowerCase()?(e=t.substr(cs,22),cs+=22):(e=a,0===vs&&js(Wr)),e===a&&("drop_existing"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(Gr)),e===a&&("online"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Yr)),e===a&&("resumable"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Kr)),e===a&&("allow_row_locks"===t.substr(cs,15).toLowerCase()?(e=t.substr(cs,15),cs+=15):(e=a,0===vs&&js(Xr)),e===a&&("allow_page_locks"===t.substr(cs,16).toLowerCase()?(e=t.substr(cs,16),cs+=16):(e=a,0===vs&&js(Zr)),e===a&&("optimize_for_sequential_key"===t.substr(cs,27).toLowerCase()?(e=t.substr(cs,27),cs+=27):(e=a,0===vs&&js(zr)))))))))))),e!==a&&Qf()!==a&&(n=Lf())!==a&&Qf()!==a?((o=wl())===a&&(o=function(){var r,e,n,o;r=cs,"off"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Do));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()),o!==a?(ls=r,r=e=function(t,r,e){return{type:t.toLowerCase(),symbol:r,expr:{type:"origin",value:e[0]}}}(e,n,o)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,"data_compression"===t.substr(cs,16).toLowerCase()?(e=t.substr(cs,16),cs+=16):(e=a,0===vs&&js(Jr)),e!==a&&Qf()!==a&&(n=Lf())!==a&&Qf()!==a?("none"===t.substr(cs,4).toLowerCase()?(o=t.substr(cs,4),cs+=4):(o=a,0===vs&&js(F)),o===a&&("row"===t.substr(cs,3).toLowerCase()?(o=t.substr(cs,3),cs+=3):(o=a,0===vs&&js(te)),o===a&&("PAGE"===t.substr(cs,4)?(o="PAGE",cs+=4):(o=a,0===vs&&js(re)))),o!==a&&Qf()!==a?((u=yi())===a&&(u=null),u!==a?(ls=r,r=e=function(t,r,e,n){return{type:t.toLowerCase(),symbol:r,expr:{value:e,on:n}}}(e,n,o,u)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=Hf())))))),r}function ji(){var t,r,e,n;if(t=cs,(r=Ci())!==a){for(e=[],n=wi();n!==a;)e.push(n),n=wi();e!==a?(ls=t,t=r=ee(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function wi(){var t,r,e;return t=cs,Qf()!==a&&(r=kf())!==a&&Qf()!==a&&(e=Ci())!==a?(ls=t,t=e):(cs=t,t=a),t===a&&(t=cs,Qf()!==a&&(r=function(){var t,r,e,n,o,u;t=cs,(r=gi())!==a&&Qf()!==a&&(e=Ci())!==a&&Qf()!==a&&(n=Tl())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(o=Oc())!==a&&Qf()!==a&&(u=Vf())!==a?(ls=t,s=r,c=o,(i=e).join=s,i.using=c,t=r=i):(cs=t,t=a);var s,i,c;t===a&&(t=cs,(r=gi())!==a&&Qf()!==a&&(e=Ci())!==a&&Qf()!==a?((n=Ei())===a&&(n=null),n!==a?(ls=t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,(r=gi())===a&&(r=As()),r!==a&&Qf()!==a&&(e=Rf())!==a&&Qf()!==a&&(n=Ts())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((o=ii())===a&&(o=null),o!==a&&Qf()!==a?((u=Ei())===a&&(u=null),u!==a?(ls=t,r=function(t,r,e,n){return r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,u),t=r):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)));return t}())!==a?(ls=t,t=r):(cs=t,t=a)),t}function Oi(){var r,e,n,o,u,s;return r=cs,"forceseek"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ne)),e!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=lc())!==a&&Qf()!==a&&(o=Rf())!==a&&Qf()!==a&&(u=_i())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a&&Vf()!==a?(ls=r,r=e={keyword:"forceseek",index:n,index_columns:u,parentheses:!0}):(cs=r,r=a),r===a&&(r=cs,"spatial_window_max_cells"===t.substr(cs,24).toLowerCase()?(e=t.substr(cs,24),cs+=24):(e=a,0===vs&&js(oe)),e!==a&&Qf()!==a&&Lf()!==a&&Qf()!==a&&(n=$c())!==a?(ls=r,r=e={keyword:"spatial_window_max_cells",expr:n}):(cs=r,r=a),r===a&&(r=cs,"noexpand"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(ae)),e===a&&(e=null),e!==a&&Qf()!==a&&Af()!==a&&Qf()!==a&&(n=Rf())!==a&&Qf()!==a&&(o=Oc())!==a&&Qf()!==a&&(u=Vf())!==a?(ls=r,r=e={keyword:"index",expr:o,parentheses:!0,prefix:(s=e)&&s.toLowerCase()}):(cs=r,r=a),r===a&&(r=cs,"noexpand"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(ae)),e===a&&(e=null),e!==a&&Qf()!==a&&Af()!==a&&Qf()!==a&&(n=Lf())!==a&&Qf()!==a&&(o=lc())!==a?(ls=r,r=e=function(t,r){return{keyword:"index",expr:r,prefix:t&&t.toLowerCase()}}(e,o)):(cs=r,r=a),r===a&&(r=cs,"noexpand"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(ae)),e===a&&("forcescan"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ue)),e===a&&("forceseek"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ne)),e===a&&("holdlock"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(se)),e===a&&("nolock"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(ie)),e===a&&("nowait"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(ce)),e===a&&("paglock"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(le)),e===a&&("readcommitted"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(fe)),e===a&&("readcommittedlock"===t.substr(cs,17).toLowerCase()?(e=t.substr(cs,17),cs+=17):(e=a,0===vs&&js(pe)),e===a&&("readpast"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(be)),e===a&&("readuncommitted"===t.substr(cs,15).toLowerCase()?(e=t.substr(cs,15),cs+=15):(e=a,0===vs&&js(ve)),e===a&&("repeatableread "===t.substr(cs,15).toLowerCase()?(e=t.substr(cs,15),cs+=15):(e=a,0===vs&&js(he)),e===a&&("rowlock"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(de)),e===a&&("serializable"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(ye)),e===a&&("snapshot"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(me)),e===a&&("tablock"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(je)),e===a&&("tablockx"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(we)),e===a&&("updlock"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Oe)),e===a&&("xlock"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Le)))))))))))))))))))),e!==a&&(ls=r,e=function(t){return{keyword:"literal_string",expr:{type:"origin",value:t}}}(e)),r=e)))),r}function Li(){var t,r,e,n,o;return t=cs,(r=El())===a&&(r=null),r!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(e=function(){var t,r,e,n,o,u,s,c;if(t=cs,(r=Oi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=Oi())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=Oi())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,t=r=i(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}())!==a&&Qf()!==a&&Vf()!==a?(ls=t,o=e,t=r={keyword:(n=r)&&n[0].toLowerCase(),expr:o,parentheses:!0}):(cs=t,t=a),t}function Ci(){var r,e,n,o,u;return r=cs,(e=function(){var r;"dual"===t.substr(cs,4).toLowerCase()?(r=t.substr(cs,4),cs+=4):(r=a,0===vs&&js(ku));return r}())!==a&&(ls=r,e={type:"dual"}),(r=e)===a&&(r=cs,(e=Mc())!==a&&Qf()!==a?((n=ii())===a&&(n=null),n!==a?(ls=r,r=e={type:"expr",expr:e,as:n}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Ai())!==a&&Qf()!==a?((n=li())===a&&(n=null),n!==a&&Qf()!==a?((o=ii())===a&&(o=null),o!==a&&Qf()!==a?((u=Li())===a&&(u=null),u!==a?(ls=r,r=e=function(t,r,e,n){return t.as=e,t.table_hint=n,t.temporal_table=r,t}(e,n,o,u)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Pi())!==a&&Qf()!==a?((n=si())===a&&(n=null),n!==a?(ls=r,r=e=function(t,r){return{expr:{type:"values",values:t},as:r}}(e,n)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Rf())!==a&&Qf()!==a?((n=Ts())===a&&(n=Pi()),n!==a&&Qf()!==a&&(o=Vf())!==a&&Qf()!==a?((u=si())===a&&(u=null),u!==a?(ls=r,r=e=function(t,r){return Array.isArray(t)&&(t={type:"values",values:t}),t.parentheses=!0,{expr:t,as:r}}(n,u)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a))))),r}function gi(){var r,e,n,o;return r=cs,(e=function(){var r,e,n,o;r=cs,"left"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Qo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=cs,"right"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Bo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=cs,"full"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Fo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()),e!==a&&Qf()!==a?((n=Cl())===a&&(n=null),n!==a&&Qf()!==a&&Ol()!==a?(ls=r,o=n,r=e=[e[0].toUpperCase(),o&&o[0],"JOIN"].filter(t=>t).join(" ")):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=function(){var r,e,n,o;r=cs,"cross"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js($o));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a?((n=Ol())===a&&(n=Ll()),n!==a?(ls=r,r=e="CROSS "+n[0].toUpperCase()):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Cl())!==a&&Qf()!==a&&(n=Ll())!==a?(ls=r,r=e="OUTER APPLY"):(cs=r,r=a),r===a&&(r=cs,(e=function(){var r,e,n,o;r=cs,"inner"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Ho));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(e=null),e!==a&&Qf()!==a&&(n=Ol())!==a?(ls=r,r=e=function(t){return t?"INNER JOIN":"JOIN"}(e)):(cs=r,r=a)))),r}function Ai(){var r,e,n,o,u,s,i,c,l,f;return r=cs,(e=lc())!==a&&(n=Qf())!==a&&(o=If())!==a&&(u=Qf())!==a&&(s=lc())!==a&&(i=Qf())!==a&&If()!==a&&Qf()!==a&&(c=lc())!==a&&Qf()!==a&&If()!==a&&Qf()!==a&&(l=lc())!==a?(ls=r,r=e={server:e,db:s,schema:c,table:l}):(cs=r,r=a),r===a&&(r=cs,(e=lc())!==a&&(n=Qf())!==a&&(o=If())!==a&&(u=Qf())!==a&&(s=lc())!==a&&(i=Qf())!==a&&If()!==a&&Qf()!==a&&(c=lc())!==a?(ls=r,r=e=function(t,r,e){return{db:t,schema:r,table:e}}(e,s,c)):(cs=r,r=a),r===a&&(r=cs,(e=lc())!==a?(n=cs,(o=Qf())!==a&&(u=If())!==a&&(s=Qf())!==a&&(i=lc())!==a?n=o=[o,u,s,i]:(cs=n,n=a),n===a&&(n=null),n!==a?(ls=r,r=e=function(t,r){const e={db:null,table:t};return null!==r&&(e.db=t,e.table=r[3]),e}(e,n)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=np())!==a&&(ls=r,(f=e).db=null,f.table=f.name,e=f),(r=e)===a&&(r=cs,"##"===t.substr(cs,2)?(e="##",cs+=2):(e=a,0===vs&&js(Ce)),e===a&&(35===t.charCodeAt(cs)?(e="#",cs++):(e=a,0===vs&&js(ge))),e!==a&&(n=lc())!==a?(ls=r,r=e={db:null,table:`${e}${n}`}):(cs=r,r=a))))),r}function Ti(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Gi())!==a){for(e=[],n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=lp(r[t][1],n,r[t][3]);return n}(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Ei(){var t,r;return t=cs,wl()!==a&&Qf()!==a&&(r=Yi())!==a?(ls=t,t=r):(cs=t,t=a),t}function Si(){var r,e;return r=cs,function(){var r,e,n,o;r=cs,"where"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(zo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(e=Yi())!==a?(ls=r,r=e):(cs=r,r=a),r}function _i(){var t;return(t=Es())===a&&(t=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Pc())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Pc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Pc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}()),t}function Ui(){var t,r;return t=cs,pl()!==a&&Qf()!==a&&Ul()!==a&&Qf()!==a&&(r=ai())!==a?(ls=t,t=r):(cs=t,t=a),t}function xi(){var r,e;return r=cs,function(){var r,e,n,o;r=cs,"order"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(ea));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&Ul()!==a&&Qf()!==a&&(e=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Ii())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ii())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Ii())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=r,r=e):(cs=r,r=a),r}function Ii(){var t,r,e;return t=cs,(r=Gi())!==a&&Qf()!==a?((e=Nl())===a&&(e=kl()),e===a&&(e=null),e!==a?(ls=t,t=r={expr:r,type:e}):(cs=t,t=a)):(cs=t,t=a),t}function ki(){var t;return(t=$c())===a&&(t=Ac()),t}function Ni(){var r,e,n,o,u,s,i,c,l,f;return r=cs,function(){var r,e,n,o;r=cs,"limit"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(oa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(e=ki())!==a&&Qf()!==a?(n=cs,(o=kf())===a&&(o=xl()),o!==a&&(u=Qf())!==a&&(s=ki())!==a?n=o=[o,u,s]:(cs=n,n=a),n===a&&(n=null),n!==a?(ls=r,r=function(t,r){const e=[t];return r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,n)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,Il()!==a&&Qf()!==a?("first"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Ae)),e!==a&&Qf()!==a&&(n=ki())!==a&&(o=Qf())!==a?("rows"===t.substr(cs,4).toLowerCase()?(u=t.substr(cs,4),cs+=4):(u=a,0===vs&&js(Te)),u===a&&("row"===t.substr(cs,3).toLowerCase()?(u=t.substr(cs,3),cs+=3):(u=a,0===vs&&js(te))),u!==a&&(s=Qf())!==a?("only"===t.substr(cs,4).toLowerCase()?(i=t.substr(cs,4),cs+=4):(i=a,0===vs&&js(Ee)),i!==a?(ls=r,r={fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"first"}],value:n,suffix:[{type:"origin",value:u},{type:"origin",value:"only"}]}}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,xl()!==a&&Qf()!==a&&(e=ki())!==a&&Qf()!==a?("rows"===t.substr(cs,4).toLowerCase()?(n=t.substr(cs,4),cs+=4):(n=a,0===vs&&js(Te)),n!==a&&(o=Qf())!==a&&(u=Il())!==a&&(s=Qf())!==a?("next"===t.substr(cs,4).toLowerCase()?(i=t.substr(cs,4),cs+=4):(i=a,0===vs&&js(Se)),i!==a&&Qf()!==a&&(c=ki())!==a&&Qf()!==a?("rows"===t.substr(cs,4).toLowerCase()?(l=t.substr(cs,4),cs+=4):(l=a,0===vs&&js(Te)),l===a&&("row"===t.substr(cs,3).toLowerCase()?(l=t.substr(cs,3),cs+=3):(l=a,0===vs&&js(te))),l!==a&&Qf()!==a?("only"===t.substr(cs,4).toLowerCase()?(f=t.substr(cs,4),cs+=4):(f=a,0===vs&&js(Ee)),f!==a?(ls=r,r=function(t,r,e){return{offset:{prefix:[{type:"origin",value:"offset"}],value:t,suffix:[{type:"origin",value:"rows"}]},fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"next"}],value:r,suffix:[{type:"origin",value:e},{type:"origin",value:"only"}]}}}(e,c,l)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a))),r}function Ri(){var r,e,n,o,u,s,i,c,l;return r=cs,e=cs,(n=lc())!==a&&(o=Qf())!==a&&(u=If())!==a?e=n=[n,o,u]:(cs=e,e=a),e===a&&(e=null),e!==a&&(n=Qf())!==a&&(o=yc())!==a&&(u=Qf())!==a?(61===t.charCodeAt(cs)?(s="=",cs++):(s=a,0===vs&&js(Ve)),s!==a&&Qf()!==a&&(i=rc())!==a?(ls=r,r=e={column:o,value:i,table:(l=e)&&l[0]}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,e=cs,(n=lc())!==a&&(o=Qf())!==a&&(u=If())!==a?e=n=[n,o,u]:(cs=e,e=a),e===a&&(e=null),e!==a&&(n=Qf())!==a&&(o=yc())!==a&&(u=Qf())!==a?(61===t.charCodeAt(cs)?(s="=",cs++):(s=a,0===vs&&js(Ve)),s!==a&&Qf()!==a&&(i=Al())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(c=sc())!==a&&Qf()!==a&&Vf()!==a?(ls=r,r=e=function(t,r,e){return{column:r,value:e,table:t&&t[0],keyword:"values"}}(e,o,c)):(cs=r,r=a)):(cs=r,r=a)),r}function Vi(){var t;return(t=Pi())===a&&(t=ni()),t}function Mi(){var t,r,e;return t=cs,pl()!==a&&Qf()!==a&&(r=Rf())!==a&&Qf()!==a&&(e=Oc())!==a&&Qf()!==a&&Vf()!==a?(ls=t,t=e):(cs=t,t=a),t===a&&(t=cs,pl()!==a&&Qf()!==a&&(r=Di())!==a?(ls=t,t=r):(cs=t,t=a)),t}function qi(){var r,e;return r=cs,(e=function(){var r,e,n,o;r=cs,"insert"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(go));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e="insert"),(r=e)===a&&(r=cs,(e=cl())!==a&&(ls=r,e="replace"),r=e),r}function Pi(){var t,r;return t=cs,Al()!==a&&Qf()!==a&&(r=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Di())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Di())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Di())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=v(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())!==a?(ls=t,t=r):(cs=t,t=a),t}function Di(){var t,r;return t=cs,Rf()!==a&&Qf()!==a&&(r=Qi())!==a&&Qf()!==a&&Vf()!==a?(ls=t,t=r):(cs=t,t=a),t}function Qi(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Gi())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){const e={type:"expr_list"};return e.value=pp(t,r),e}(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Bi(){var r,e,n;return r=cs,function(){var r,e,n,o;r=cs,"interval"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(hu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="INTERVAL"):(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(e=Gi())!==a&&Qf()!==a&&(n=function(){var r;(r=function(){var r,e,n,o;r=cs,"year"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(du));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="YEAR"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"month"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(yu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MONTH"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"day"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(mu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DAY"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"hour"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(ju));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="HOUR"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"minute"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(wu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MINUTE"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"second"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Ou));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SECOND"):(cs=r,r=a)):(cs=r,r=a);return r}());return r}())!==a?(ls=r,r={type:"interval",expr:e,unit:n.toLowerCase()}):(cs=r,r=a),r}function Fi(){var t,r,e,n,o,u;if(t=cs,(r=Hi())!==a)if(Qf()!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=Hi())!==a?n=o=[o,u]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=Hi())!==a?n=o=[o,u]:(cs=n,n=a);e!==a?(ls=t,t=r=p(r,e)):(cs=t,t=a)}else cs=t,t=a;else cs=t,t=a;return t}function Hi(){var r,e,n;return r=cs,function(){var r,e,n,o;r=cs,"when"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Ea));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(e=Yi())!==a&&Qf()!==a&&function(){var r,e,n,o;r=cs,"then"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Sa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}()!==a&&Qf()!==a&&(n=Gi())!==a?(ls=r,r={type:"when",cond:e,result:n}):(cs=r,r=a),r}function $i(){var t,r;return t=cs,Wl()!==a&&Qf()!==a&&(r=Gi())!==a?(ls=t,t={type:"else",result:r}):(cs=t,t=a),t}function Wi(){var t;return(t=function(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Ki())!==a){for(e=[],n=cs,(o=Bf())!==a&&(u=Hl())!==a&&(s=Qf())!==a&&(i=Ki())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Bf())!==a&&(u=Hl())!==a&&(s=Qf())!==a&&(i=Ki())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,r=Me(r,e),t=r):(cs=t,t=a)}else cs=t,t=a;return t}())===a&&(t=function(){var t,r,e,n,o,u;if(t=cs,(r=ec())!==a){if(e=[],n=cs,(o=Qf())!==a&&(u=ac())!==a?n=o=[o,u]:(cs=n,n=a),n!==a)for(;n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=ac())!==a?n=o=[o,u]:(cs=n,n=a);else e=a;e!==a?(ls=t,r=cp(r,e[0][1]),t=r):(cs=t,t=a)}else cs=t,t=a;return t}()),t}function Gi(){var t;return(t=Wi())===a&&(t=Ts()),t}function Yi(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Gi())!==a){for(e=[],n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl())===a&&(u=kf()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=Fl())===a&&(u=Hl())===a&&(u=kf()),u!==a&&(s=Qf())!==a&&(i=Gi())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=lp(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Ki(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Xi())!==a){for(e=[],n=cs,(o=Bf())!==a&&(u=Fl())!==a&&(s=Qf())!==a&&(i=Xi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Bf())!==a&&(u=Fl())!==a&&(s=Qf())!==a&&(i=Xi())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=Me(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Xi(){var r,e,n,o,u;return(r=Zi())===a&&(r=function(){var t,r,e;t=cs,(r=function(){var t,r,e,n,o;t=cs,r=cs,(e=Bl())!==a&&(n=Qf())!==a&&(o=Ql())!==a?r=e=[e,n,o]:(cs=r,r=a);r!==a&&(ls=t,r=Pe(r));(t=r)===a&&(t=Ql());return t}())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(e=Ts())!==a&&Qf()!==a&&Vf()!==a?(ls=t,n=r,(o=e).parentheses=!0,r=cp(n,o),t=r):(cs=t,t=a);var n,o;return t}())===a&&(r=cs,(e=Bl())===a&&(e=cs,33===t.charCodeAt(cs)?(n="!",cs++):(n=a,0===vs&&js(qe)),n!==a?(o=cs,vs++,61===t.charCodeAt(cs)?(u="=",cs++):(u=a,0===vs&&js(Ve)),vs--,u===a?o=void 0:(cs=o,o=a),o!==a?e=n=[n,o]:(cs=e,e=a)):(cs=e,e=a)),e!==a&&(n=Qf())!==a&&(o=Xi())!==a?(ls=r,r=e=cp("NOT",o)):(cs=r,r=a)),r}function Zi(){var t,r,e,n,o;return t=cs,(r=rc())!==a&&Qf()!==a?((e=function(){var t;(t=function(){var t,r,e,n,o,u,s;t=cs,r=[],e=cs,(n=Qf())!==a&&(o=zi())!==a&&(u=Qf())!==a&&(s=rc())!==a?e=n=[n,o,u,s]:(cs=e,e=a);if(e!==a)for(;e!==a;)r.push(e),e=cs,(n=Qf())!==a&&(o=zi())!==a&&(u=Qf())!==a&&(s=rc())!==a?e=n=[n,o,u,s]:(cs=e,e=a);else r=a;r!==a&&(ls=t,r={type:"arithmetic",tail:r});return t=r}())===a&&(t=tc())===a&&(t=function(){var t,r,e,n;t=cs,(r=function(){var t,r,e,n,o;t=cs,r=cs,(e=Bl())!==a&&(n=Qf())!==a&&(o=Ml())!==a?r=e=[e,n,o]:(cs=r,r=a);r!==a&&(ls=t,r=Pe(r));(t=r)===a&&(t=Ml());return t}())!==a&&Qf()!==a&&(e=rc())!==a&&Qf()!==a&&Fl()!==a&&Qf()!==a&&(n=rc())!==a?(ls=t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(cs=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o;t=cs,(r=Pl())!==a&&(e=Qf())!==a&&(n=rc())!==a?(ls=t,t=r={op:"IS",right:n}):(cs=t,t=a);t===a&&(t=cs,r=cs,(e=Pl())!==a&&(n=Qf())!==a&&(o=Bl())!==a?r=e=[e,n,o]:(cs=r,r=a),r!==a&&(e=Qf())!==a&&(n=rc())!==a?(ls=t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(cs=t,t=a));return t}())===a&&(t=function(){var t,r,e;t=cs,(r=function(){var t,r,e,n,o;t=cs,r=cs,(e=Bl())!==a&&(n=Qf())!==a&&(o=Dl())!==a?r=e=[e,n,o]:(cs=r,r=a);r!==a&&(ls=t,r=Pe(r));(t=r)===a&&(t=Dl());return t}())!==a&&Qf()!==a?((e=Pc())===a&&(e=Zi()),e!==a?(ls=t,t=r={op:r,right:e}):(cs=t,t=a)):(cs=t,t=a);return t}());return t}())===a&&(e=null),e!==a?(ls=t,n=r,t=r=null===(o=e)?n:"arithmetic"===o.type?bp(n,o.tail):lp(o.op,n,o.right)):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=Qc())===a&&(t=sc()),t}function zi(){var r;return">="===t.substr(cs,2)?(r=">=",cs+=2):(r=a,0===vs&&js(De)),r===a&&(62===t.charCodeAt(cs)?(r=">",cs++):(r=a,0===vs&&js(Qe)),r===a&&("<="===t.substr(cs,2)?(r="<=",cs+=2):(r=a,0===vs&&js(Be)),r===a&&("<>"===t.substr(cs,2)?(r="<>",cs+=2):(r=a,0===vs&&js(Fe)),r===a&&(60===t.charCodeAt(cs)?(r="<",cs++):(r=a,0===vs&&js(He)),r===a&&(61===t.charCodeAt(cs)?(r="=",cs++):(r=a,0===vs&&js(Ve)),r===a&&("!="===t.substr(cs,2)?(r="!=",cs+=2):(r=a,0===vs&&js($e)))))))),r}function Ji(){var t,r,e,n,o;return t=cs,r=cs,(e=Bl())!==a&&(n=Qf())!==a&&(o=ql())!==a?r=e=[e,n,o]:(cs=r,r=a),r!==a&&(ls=t,r=Pe(r)),(t=r)===a&&(t=ql()),t}function tc(){var t,r,e,n;return t=cs,(r=Ji())!==a&&Qf()!==a&&(e=Rf())!==a&&Qf()!==a&&(n=Qi())!==a&&Qf()!==a&&Vf()!==a?(ls=t,t=r={op:r,right:n}):(cs=t,t=a),t===a&&(t=cs,(r=Ji())!==a&&Qf()!==a?((e=np())===a&&(e=Qc())===a&&(e=Mc()),e!==a?(ls=t,t=r=function(t,r){return{op:t,right:r}}(r,e)):(cs=t,t=a)):(cs=t,t=a)),t}function rc(){var t,r,e,n,o,u,s,i;if(t=cs,(r=nc())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=ec())!==a&&(s=Qf())!==a&&(i=nc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=ec())!==a&&(s=Qf())!==a&&(i=nc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=function(t,r){if(r&&r.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...ip()}));return bp(t,r)}(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function ec(){var r;return 43===t.charCodeAt(cs)?(r="+",cs++):(r=a,0===vs&&js(We)),r===a&&(45===t.charCodeAt(cs)?(r="-",cs++):(r=a,0===vs&&js(Ge))),r}function nc(){var t,r,e,n,o,u,s,i;if(t=cs,(r=uc())!==a){for(e=[],n=cs,(o=Qf())!==a?((u=oc())===a&&(u=Df()),u!==a&&(s=Qf())!==a&&(i=uc())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a?((u=oc())===a&&(u=Df()),u!==a&&(s=Qf())!==a&&(i=uc())!==a?n=o=[o,u,s,i]:(cs=n,n=a)):(cs=n,n=a);e!==a?(ls=t,t=r=bp(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function oc(){var r;return 42===t.charCodeAt(cs)?(r="*",cs++):(r=a,0===vs&&js(Ye)),r===a&&(47===t.charCodeAt(cs)?(r="/",cs++):(r=a,0===vs&&js(Ke)),r===a&&(37===t.charCodeAt(cs)?(r="%",cs++):(r=a,0===vs&&js(Xe)))),r}function ac(){var r,e,n;return(r=Tc())===a&&(r=Mc())===a&&(r=function(){var r,e,n,o,u,s,i;r=cs,(e=Yl())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&dl()!==a&&Qf()!==a&&(o=ap())!==a&&Qf()!==a&&(u=Vf())!==a?(ls=r,c=n,l=o,e={type:"cast",keyword:e.toLowerCase(),expr:c,symbol:"as",target:[l]},r=e):(cs=r,r=a);var c,l;r===a&&(r=cs,(e=Yl())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&dl()!==a&&Qf()!==a&&(o=rf())!==a&&Qf()!==a&&(u=Rf())!==a&&Qf()!==a&&(s=Gc())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a&&(i=Vf())!==a?(ls=r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,n,s),r=e):(cs=r,r=a),r===a&&(r=cs,(e=Yl())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&dl()!==a&&Qf()!==a&&(o=rf())!==a&&Qf()!==a&&(u=Rf())!==a&&Qf()!==a&&(s=Gc())!==a&&Qf()!==a&&kf()!==a&&Qf()!==a&&(i=Gc())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a&&Vf()!==a?(ls=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,n,s,i),r=e):(cs=r,r=a),r===a&&(r=cs,(e=Yl())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=Gi())!==a&&Qf()!==a&&dl()!==a&&Qf()!==a&&(o=function(){var r;(r=function(){var r,e,n,o;r=cs,"signed"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Fa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SIGNED"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=ef());return r}())!==a&&Qf()!==a?((u=of())===a&&(u=null),u!==a&&Qf()!==a&&(s=Vf())!==a?(ls=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,n,o,u),r=e):(cs=r,r=a)):(cs=r,r=a))));return r}())===a&&(r=function(){var t,r,e,n,o,u,s,i;return t=cs,$l()!==a&&Qf()!==a&&(r=Fi())!==a&&Qf()!==a?((e=$i())===a&&(e=null),e!==a&&Qf()!==a&&(n=Gl())!==a&&Qf()!==a?((o=$l())===a&&(o=null),o!==a?(ls=t,s=r,(i=e)&&s.push(i),t={type:"case",expr:null,args:s}):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,$l()!==a&&Qf()!==a&&(r=Gi())!==a&&Qf()!==a&&(e=Fi())!==a&&Qf()!==a?((n=$i())===a&&(n=null),n!==a&&Qf()!==a&&(o=Gl())!==a&&Qf()!==a?((u=$l())===a&&(u=null),u!==a?(ls=t,t=function(t,r,e){return e&&r.push(e),{type:"case",expr:t,args:r}}(r,e,n)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)),t}())===a&&(r=Bi())===a&&(r=Pc())===a&&(r=sc())===a&&(r=Ac())===a&&(r=cs,Rf()!==a&&Qf()!==a&&(e=Yi())!==a&&Qf()!==a&&Vf()!==a?(ls=r,(n=e).parentheses=!0,r=n):(cs=r,r=a),r===a&&(r=np())),r}function uc(){var r,e,n,o,u;return(r=ac())===a&&(r=cs,(e=function(){var r;33===t.charCodeAt(cs)?(r="!",cs++):(r=a,0===vs&&js(qe));r===a&&(45===t.charCodeAt(cs)?(r="-",cs++):(r=a,0===vs&&js(Ge)),r===a&&(43===t.charCodeAt(cs)?(r="+",cs++):(r=a,0===vs&&js(We)),r===a&&(126===t.charCodeAt(cs)?(r="~",cs++):(r=a,0===vs&&js(Ze)))));return r}())!==a?(n=cs,(o=Qf())!==a&&(u=uc())!==a?n=o=[o,u]:(cs=n,n=a),n!==a?(ls=r,r=e=cp(e,n[1])):(cs=r,r=a)):(cs=r,r=a)),r}function sc(){var t,r,e,n,o,u,s,i,c,l,f;return t=cs,r=cs,(e=lc())!==a&&(n=Qf())!==a&&(o=If())!==a?r=e=[e,n,o]:(cs=r,r=a),r===a&&(r=null),r!==a&&(e=Qf())!==a?(n=cs,(o=lc())!==a&&(u=Qf())!==a&&(s=If())!==a?n=o=[o,u,s]:(cs=n,n=a),n===a&&(n=null),n!==a&&(o=Qf())!==a?(u=cs,(s=lc())!==a&&(i=Qf())!==a&&(c=If())!==a?u=s=[s,i,c]:(cs=u,u=a),u===a&&(u=null),u!==a&&(s=Qf())!==a&&(i=mc())!==a?(c=cs,(l=Qf())!==a&&(f=Ns())!==a?c=l=[l,f]:(cs=c,c=a),c===a&&(c=null),c!==a?(ls=t,t=r=function(t,r,e,n,o){const a={table:null,db:null,schema:null};null!==t&&(a.table=t[0]),null!==r&&(a.table=r[0],a.schema=t[0]),null!==e&&(a.table=e[0],a.db=t[0],a.schema=r[0]);const u=[a.db,a.schema,a.table].filter(Boolean).join(".")||"null";return mp.add(`select::${u}::${n}`),{type:"column_ref",...a,column:n,collate:o&&o[1]}}(r,n,u,i,c)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a),t}function ic(){var t,r,e,n,o,u,s,i;if(t=cs,(r=mc())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=mc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=mc())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=v(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function cc(){var t,r;return t=cs,(r=wc())!==a&&(ls=t,r={type:"default",value:r}),(t=r)===a&&(t=fc()),t}function lc(){var t,r;return t=cs,(r=wc())!==a?(ls=cs,(ze(r)?a:void 0)!==a?(ls=t,t=r=r):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,(r=pc())!==a&&(ls=t,r=r),t=r),t}function fc(){var t;return(t=bc())===a&&(t=vc())===a&&(t=hc())===a&&(t=dc()),t}function pc(){var t,r;return t=cs,(r=bc())===a&&(r=vc())===a&&(r=hc())===a&&(r=dc()),r!==a&&(ls=t,r=r.value),t=r}function bc(){var r,e,n,o;if(r=cs,34===t.charCodeAt(cs)?(e='"',cs++):(e=a,0===vs&&js(Je)),e!==a){if(n=[],tn.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(rn)),o!==a)for(;o!==a;)n.push(o),tn.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(rn));else n=a;n!==a?(34===t.charCodeAt(cs)?(o='"',cs++):(o=a,0===vs&&js(Je)),o!==a?(ls=r,r=e={type:"double_quote_string",value:n.join("")}):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;return r}function vc(){var r,e,n,o;if(r=cs,39===t.charCodeAt(cs)?(e="'",cs++):(e=a,0===vs&&js(mt)),e!==a){if(n=[],en.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(nn)),o!==a)for(;o!==a;)n.push(o),en.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(nn));else n=a;n!==a?(39===t.charCodeAt(cs)?(o="'",cs++):(o=a,0===vs&&js(mt)),o!==a?(ls=r,r=e={type:"single_quote_string",value:n.join("")}):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;return r}function hc(){var r,e,n,o;if(r=cs,96===t.charCodeAt(cs)?(e="`",cs++):(e=a,0===vs&&js(on)),e!==a){if(n=[],an.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(un)),o!==a)for(;o!==a;)n.push(o),an.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(un));else n=a;n!==a?(96===t.charCodeAt(cs)?(o="`",cs++):(o=a,0===vs&&js(on)),o!==a?(ls=r,r=e={type:"backticks_quote_string",value:n.join("")}):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;return r}function dc(){var r,e,n,o;if(r=cs,91===t.charCodeAt(cs)?(e="[",cs++):(e=a,0===vs&&js(sn)),e!==a){if(n=[],cn.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(ln)),o!==a)for(;o!==a;)n.push(o),cn.test(t.charAt(cs))?(o=t.charAt(cs),cs++):(o=a,0===vs&&js(ln));else n=a;n!==a?(93===t.charCodeAt(cs)?(o="]",cs++):(o=a,0===vs&&js(fn)),o!==a?(ls=r,r=e={type:"brackets_quote_string",value:n.join("")}):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;return r}function yc(){var t,r;return t=cs,(r=jc())!==a&&(ls=t,r=r),(t=r)===a&&(t=pc()),t}function mc(){var t,r;return t=cs,(r=jc())!==a?(ls=cs,(ze(r)?a:void 0)!==a?(ls=t,t=r=r):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=pc()),t}function jc(){var t,r,e,n;if(t=cs,(r=Lc())!==a){for(e=[],n=gc();n!==a;)e.push(n),n=gc();e!==a?(ls=t,t=r=pn(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function wc(){var t,r,e,n;if(t=cs,(r=Lc())!==a){for(e=[],n=Cc();n!==a;)e.push(n),n=Cc();e!==a?(ls=t,t=r=pn(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Oc(){var t,r,e,n,o,u,s,c;if(t=cs,(r=wc())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=wc())!==a?n=o=[o,u,s,c]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(c=wc())!==a?n=o=[o,u,s,c]:(cs=n,n=a);e!==a?(ls=t,t=r=i(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Lc(){var r;return bn.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(vn)),r}function Cc(){var r;return hn.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(dn)),r}function gc(){var r;return yn.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(mn)),r}function Ac(){var r,e,n,o;return r=cs,e=cs,58===t.charCodeAt(cs)?(n=":",cs++):(n=a,0===vs&&js(jn)),n!==a&&(o=wc())!==a?e=n=[n,o]:(cs=e,e=a),e!==a&&(ls=r,e={type:"param",value:e[1]}),r=e}function Tc(){var r;return(r=function(){var r,e,n,o;r=cs,(e=function(){var r,e,n,o;r=cs,"count"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(wa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="COUNT"):(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=function(){var r,e;r=cs,(e=function(){var r,e;r=cs,42===t.charCodeAt(cs)?(e="*",cs++):(e=a,0===vs&&js(Ye));e!==a&&(ls=r,e={type:"star",value:"*"});return r=e}())!==a&&(ls=r,e={expr:e});(r=e)===a&&(r=Vc());return r}())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((o=Sc())===a&&(o=null),o!==a?(ls=r,r=e={type:"aggr_func",name:e,args:n,over:o}):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,(e=function(){var r;(r=function(){var r,e,n,o;r=cs,"sum"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Ca));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SUM"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"max"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Oa));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MAX"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"min"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(La));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MIN"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"avg"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(ga));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="AVG"):(cs=r,r=a)):(cs=r,r=a);return r}());return r}())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=rc())!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((o=Sc())===a&&(o=null),o!==a?(ls=r,e={type:"aggr_func",name:e,args:{expr:n},over:o,...ip()},r=e):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,u,s;r=cs,e=cs,(n=lc())!==a&&(o=Qf())!==a&&(u=If())!==a?e=n=[n,o,u]:(cs=e,e=a);e===a&&(e=null);e!==a&&(n=Qf())!==a?((o=function(){var r,e,n,o;r=cs,"array_agg"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(ma));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ARRAY_AGG"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(o=function(){var r,e,n,o;r=cs,"string_agg"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(ja));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="STRING_AGG"):(cs=r,r=a)):(cs=r,r=a);return r}()),o!==a&&(u=Qf())!==a&&Rf()!==a&&Qf()!==a&&(s=Vc())!==a&&Qf()!==a&&Vf()!==a?(ls=r,c=o,l=s,e={type:"aggr_func",name:(i=e)?`${i[0]}.${c}`:c,args:l},r=e):(cs=r,r=a)):(cs=r,r=a);var i,c,l;return r}()),r}function Ec(){var t,r,e;return t=cs,wl()!==a&&Qf()!==a&&al()!==a&&Qf()!==a&&(r=jf())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a?((e=Qi())===a&&(e=null),e!==a&&Qf()!==a&&Vf()!==a?(ls=t,t={type:"on update",keyword:r,parentheses:!0,expr:e}):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,wl()!==a&&Qf()!==a&&al()!==a&&Qf()!==a&&(r=jf())!==a?(ls=t,t=function(t){return{type:"on update",keyword:t}}(r)):(cs=t,t=a)),t}function Sc(){var r,e,n;return r=cs,"over"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(wn)),e!==a&&Qf()!==a&&(n=_c())!==a?(ls=r,r=e={type:"window",as_window_specification:n}):(cs=r,r=a),r===a&&(r=Ec()),r}function _c(){var t,r;return(t=wc())===a&&(t=cs,Rf()!==a&&Qf()!==a?((r=function(){var t,r,e,n;t=cs,(r=Ui())===a&&(r=null);r!==a&&Qf()!==a?((e=xi())===a&&(e=null),e!==a&&Qf()!==a?((n=function(){var t,r,e,n,o;t=cs,(r=df())!==a&&Qf()!==a?((e=xc())===a&&(e=Ic()),e!==a?(ls=t,t=r={type:"rows",expr:e}):(cs=t,t=a)):(cs=t,t=a);t===a&&(t=cs,(r=df())!==a&&Qf()!==a&&(e=Ml())!==a&&Qf()!==a&&(n=Uc())!==a&&Qf()!==a&&Fl()!==a&&Qf()!==a&&(o=Uc())!==a?(ls=t,r=lp(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),t=r):(cs=t,t=a));return t}())===a&&(n=null),n!==a?(ls=t,t=r={name:null,partitionby:r,orderby:e,window_frame_clause:n}):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a);return t}())===a&&(r=null),r!==a&&Qf()!==a&&Vf()!==a?(ls=t,t={window_specification:r||{},parentheses:!0}):(cs=t,t=a)):(cs=t,t=a)),t}function Uc(){var t;return(t=Ic())===a&&(t=xc()),t}function xc(){var r,e,n,o;return r=cs,(e=Nc())!==a&&Qf()!==a?("following"===t.substr(cs,9).toLowerCase()?(n=t.substr(cs,9),cs+=9):(n=a,0===vs&&js(On)),n!==a?(ls=r,(o=e).value+=" FOLLOWING",r=e=o):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=kc()),r}function Ic(){var r,e,n,o,u;return r=cs,(e=Nc())!==a&&Qf()!==a?("preceding"===t.substr(cs,9).toLowerCase()?(n=t.substr(cs,9),cs+=9):(n=a,0===vs&&js(Ln)),n===a&&("following"===t.substr(cs,9).toLowerCase()?(n=t.substr(cs,9),cs+=9):(n=a,0===vs&&js(On))),n!==a?(ls=r,u=n,(o=e).value+=" "+u.toUpperCase(),r=e=o):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=kc()),r}function kc(){var r,e,n;return r=cs,"current"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Cn)),e!==a&&Qf()!==a?("row"===t.substr(cs,3).toLowerCase()?(n=t.substr(cs,3),cs+=3):(n=a,0===vs&&js(te)),n!==a?(ls=r,r=e={type:"origin",value:"current row"}):(cs=r,r=a)):(cs=r,r=a),r}function Nc(){var r,e;return r=cs,"unbounded"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(gn)),e!==a&&(ls=r,e={type:"origin",value:e.toUpperCase()}),(r=e)===a&&(r=$c()),r}function Rc(){var t,r,e;return t=cs,(r=kf())!==a&&Qf()!==a&&(e=Qc())!==a?(ls=t,t=r={symbol:r,delimiter:e}):(cs=t,t=a),t}function Vc(){var t,r,e,n,o,u,s,i,c,l,f;if(t=cs,(r=Vl())===a&&(r=null),r!==a)if(Qf()!==a)if((e=Rf())!==a)if(Qf()!==a)if((n=Gi())!==a)if(Qf()!==a)if((o=Vf())!==a)if(Qf()!==a){for(u=[],s=cs,(i=Qf())!==a?((c=Fl())===a&&(c=Hl()),c!==a&&(l=Qf())!==a&&(f=Gi())!==a?s=i=[i,c,l,f]:(cs=s,s=a)):(cs=s,s=a);s!==a;)u.push(s),s=cs,(i=Qf())!==a?((c=Fl())===a&&(c=Hl()),c!==a&&(l=Qf())!==a&&(f=Gi())!==a?s=i=[i,c,l,f]:(cs=s,s=a)):(cs=s,s=a);u!==a&&(s=Qf())!==a?((i=Rc())===a&&(i=null),i!==a&&(c=Qf())!==a?((l=xi())===a&&(l=null),l!==a?(ls=t,t=r=function(t,r,e,n,o){const a=e.length;let u=r;u.parentheses=!0;for(let t=0;t<a;++t)u=lp(e[t][1],u,e[t][3]);return{distinct:t,expr:u,orderby:o,separator:n}}(r,n,u,i,l)):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)}else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;else cs=t,t=a;return t===a&&(t=cs,(r=Vl())===a&&(r=null),r!==a&&Qf()!==a&&(e=Ti())!==a&&Qf()!==a?((n=Rc())===a&&(n=null),n!==a&&Qf()!==a?((o=xi())===a&&(o=null),o!==a?(ls=t,t=r={distinct:r,expr:e,orderby:o,separator:n}):(cs=t,t=a)):(cs=t,t=a)):(cs=t,t=a)),t}function Mc(){var r,e,n,o,u,s;return r=cs,(e=function(){var r;(r=qc())===a&&(r=function(){var r,e,n,o;r=cs,"current_user"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(gu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CURRENT_USER"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"user"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(bu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="USER"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"session_user"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Au));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SESSION_USER"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"system_user"===t.substr(cs,11).toLowerCase()?(e=t.substr(cs,11),cs+=11):(e=a,0===vs&&js(Tu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SYSTEM_USER"):(cs=r,r=a)):(cs=r,r=a);return r}());return r}())!==a&&Qf()!==a&&(n=Rf())!==a&&Qf()!==a?((o=Qi())===a&&(o=null),o!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((u=Sc())===a&&(u=null),u!==a?(ls=r,r=e={type:"function",name:{name:[{type:"default",value:e}]},args:o||{type:"expr_list",value:[]},over:u,...ip()}):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=qc())!==a&&Qf()!==a?((n=Ec())===a&&(n=null),n!==a?(ls=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...ip()}):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=tp())!==a&&Qf()!==a&&(n=Rf())!==a&&Qf()!==a?((o=Yi())===a&&(o=null),o!==a&&Qf()!==a&&Vf()!==a&&Qf()!==a?((u=function(){var r,e,n;return r=cs,"within"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(An)),e!==a&&Qf()!==a&&_l()!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a&&(n=xi())!==a&&Qf()!==a&&Vf()!==a?(ls=r,r=e={type:"within",keyword:"group",orderby:n}):(cs=r,r=a),r}())===a&&(u=null),u!==a&&Qf()!==a?((s=Sc())===a&&(s=null),s!==a?(ls=r,r=e=function(t,r,e,n){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},within_group:e,over:n,...ip()}}(e,o,u,s)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a))),r}function qc(){var r;return(r=function(){var r,e,n,o;r=cs,"current_date"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(vu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CURRENT_DATE"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,"current_time"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Lu));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CURRENT_TIME"):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=jf()),r}function Pc(){var r;return(r=Qc())===a&&(r=$c())===a&&(r=function(){var r,e;r=cs,(e=function(){var r,e,n,o;r=cs,"true"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(lo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={type:"bool",value:!0});(r=e)===a&&(r=cs,(e=function(){var r,e,n,o;r=cs,"false"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(bo));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={type:"bool",value:!1}),r=e);return r}())===a&&(r=Dc())===a&&(r=function(){var r,e,n,o,u,s;r=cs,(e=yf())===a&&(e=pf())===a&&(e=mf())===a&&(e=bf());if(e!==a)if(Qf()!==a){if(n=cs,39===t.charCodeAt(cs)?(o="'",cs++):(o=a,0===vs&&js(mt)),o!==a){for(u=[],s=Fc();s!==a;)u.push(s),s=Fc();u!==a?(39===t.charCodeAt(cs)?(s="'",cs++):(s=a,0===vs&&js(mt)),s!==a?n=o=[o,u,s]:(cs=n,n=a)):(cs=n,n=a)}else cs=n,n=a;n!==a?(ls=r,e=In(e,n),r=e):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;if(r===a)if(r=cs,(e=yf())===a&&(e=pf())===a&&(e=mf())===a&&(e=bf()),e!==a)if(Qf()!==a){if(n=cs,34===t.charCodeAt(cs)?(o='"',cs++):(o=a,0===vs&&js(Je)),o!==a){for(u=[],s=Bc();s!==a;)u.push(s),s=Bc();u!==a?(34===t.charCodeAt(cs)?(s='"',cs++):(s=a,0===vs&&js(Je)),s!==a?n=o=[o,u,s]:(cs=n,n=a)):(cs=n,n=a)}else cs=n,n=a;n!==a?(ls=r,e=In(e,n),r=e):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;return r}()),r}function Dc(){var r,e;return r=cs,(e=function(){var r,e,n,o;r=cs,"null"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(io));e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a);return r}())!==a&&(ls=r,e={type:"null",value:null}),r=e}function Qc(){var r,e,n,o,u,s;if(r=cs,"n"===t.substr(cs,1).toLowerCase()?(e=t.charAt(cs),cs++):(e=a,0===vs&&js(Tn)),e===a&&(e=null),e!==a){if(n=cs,39===t.charCodeAt(cs)?(o="'",cs++):(o=a,0===vs&&js(mt)),o!==a){for(u=[],s=Fc();s!==a;)u.push(s),s=Fc();u!==a?(39===t.charCodeAt(cs)?(s="'",cs++):(s=a,0===vs&&js(mt)),s!==a?n=o=[o,u,s]:(cs=n,n=a)):(cs=n,n=a)}else cs=n,n=a;n!==a?(ls=r,r=e={type:e?"var_string":"single_quote_string",value:n[1].join("")}):(cs=r,r=a)}else cs=r,r=a;if(r===a){if(r=cs,e=cs,34===t.charCodeAt(cs)?(n='"',cs++):(n=a,0===vs&&js(Je)),n!==a){for(o=[],u=Bc();u!==a;)o.push(u),u=Bc();o!==a?(34===t.charCodeAt(cs)?(u='"',cs++):(u=a,0===vs&&js(Je)),u!==a?e=n=[n,o,u]:(cs=e,e=a)):(cs=e,e=a)}else cs=e,e=a;if(e!==a?(n=cs,vs++,o=If(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e=function(t){return{type:"double_quote_string",value:t[1].join("")}}(e)):(cs=r,r=a)):(cs=r,r=a),r===a)if(r=cs,"_binary"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(En)),e===a&&("_latin1"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Sn))),e===a&&(e=null),e!==a)if((n=Qf())!==a)if("0x"===t.substr(cs,2).toLowerCase()?(o=t.substr(cs,2),cs+=2):(o=a,0===vs&&js(_n)),o!==a){for(u=[],Un.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(xn));s!==a;)u.push(s),Un.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(xn));u!==a?(ls=r,r=e=function(t,r,e){return{type:"full_hex_string",prefix:t,value:e.join("")}}(e,0,u)):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a}return r}function Bc(){var r;return kn.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(Nn)),r===a&&(r=Hc()),r}function Fc(){var r;return Rn.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(Vn)),r===a&&(r=Hc()),r}function Hc(){var r,e,n,o,u,s,i,c,l,f;return r=cs,"\\'"===t.substr(cs,2)?(e="\\'",cs+=2):(e=a,0===vs&&js(Mn)),e!==a&&(ls=r,e="\\'"),(r=e)===a&&(r=cs,'\\"'===t.substr(cs,2)?(e='\\"',cs+=2):(e=a,0===vs&&js(qn)),e!==a&&(ls=r,e='\\"'),(r=e)===a&&(r=cs,"\\\\"===t.substr(cs,2)?(e="\\\\",cs+=2):(e=a,0===vs&&js(Pn)),e!==a&&(ls=r,e="\\\\"),(r=e)===a&&(r=cs,"\\/"===t.substr(cs,2)?(e="\\/",cs+=2):(e=a,0===vs&&js(Dn)),e!==a&&(ls=r,e="\\/"),(r=e)===a&&(r=cs,"\\b"===t.substr(cs,2)?(e="\\b",cs+=2):(e=a,0===vs&&js(Qn)),e!==a&&(ls=r,e="\b"),(r=e)===a&&(r=cs,"\\f"===t.substr(cs,2)?(e="\\f",cs+=2):(e=a,0===vs&&js(Bn)),e!==a&&(ls=r,e="\f"),(r=e)===a&&(r=cs,"\\n"===t.substr(cs,2)?(e="\\n",cs+=2):(e=a,0===vs&&js(Fn)),e!==a&&(ls=r,e="\n"),(r=e)===a&&(r=cs,"\\r"===t.substr(cs,2)?(e="\\r",cs+=2):(e=a,0===vs&&js(Hn)),e!==a&&(ls=r,e="\r"),(r=e)===a&&(r=cs,"\\t"===t.substr(cs,2)?(e="\\t",cs+=2):(e=a,0===vs&&js($n)),e!==a&&(ls=r,e="\t"),(r=e)===a&&(r=cs,"\\u"===t.substr(cs,2)?(e="\\u",cs+=2):(e=a,0===vs&&js(Wn)),e!==a&&(n=zc())!==a&&(o=zc())!==a&&(u=zc())!==a&&(s=zc())!==a?(ls=r,i=n,c=o,l=u,f=s,r=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(cs=r,r=a),r===a&&(r=cs,92===t.charCodeAt(cs)?(e="\\",cs++):(e=a,0===vs&&js(Gn)),e!==a&&(ls=r,e="\\"),(r=e)===a&&(r=cs,"''"===t.substr(cs,2)?(e="''",cs+=2):(e=a,0===vs&&js(Yn)),e!==a&&(ls=r,e="''"),(r=e)===a&&(r=cs,'""'===t.substr(cs,2)?(e='""',cs+=2):(e=a,0===vs&&js(Kn)),e!==a&&(ls=r,e='""'),(r=e)===a&&(r=cs,"``"===t.substr(cs,2)?(e="``",cs+=2):(e=a,0===vs&&js(Xn)),e!==a&&(ls=r,e="``"),r=e))))))))))))),r}function $c(){var t,r,e;return t=cs,(r=Wc())!==a&&(ls=t,r=(e=r)&&"bigint"===e.type?e:{type:"number",value:e}),t=r}function Wc(){var t,r,e,n;return t=cs,(r=Gc())!==a&&(e=Yc())!==a&&(n=Kc())!==a?(ls=t,t=r={type:"bigint",value:r+e+n}):(cs=t,t=a),t===a&&(t=cs,(r=Gc())!==a&&(e=Yc())!==a?(ls=t,t=r=function(t,r){const e=t+r;return fp(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e)):(cs=t,t=a),t===a&&(t=cs,(r=Gc())!==a&&(e=Kc())!==a?(ls=t,t=r=function(t,r){return{type:"bigint",value:t+r}}(r,e)):(cs=t,t=a),t===a&&(t=cs,(r=Gc())!==a&&(ls=t,r=function(t){return fp(t)?{type:"bigint",value:t}:parseFloat(t)}(r)),t=r))),t}function Gc(){var r,e,n;return(r=Xc())===a&&(r=Zc())===a&&(r=cs,45===t.charCodeAt(cs)?(e="-",cs++):(e=a,0===vs&&js(Ge)),e===a&&(43===t.charCodeAt(cs)?(e="+",cs++):(e=a,0===vs&&js(We))),e!==a&&(n=Xc())!==a?(ls=r,r=e=e+n):(cs=r,r=a),r===a&&(r=cs,45===t.charCodeAt(cs)?(e="-",cs++):(e=a,0===vs&&js(Ge)),e===a&&(43===t.charCodeAt(cs)?(e="+",cs++):(e=a,0===vs&&js(We))),e!==a&&(n=Zc())!==a?(ls=r,r=e=function(t,r){return t+r}(e,n)):(cs=r,r=a))),r}function Yc(){var r,e,n;return r=cs,46===t.charCodeAt(cs)?(e=".",cs++):(e=a,0===vs&&js(Jn)),e!==a&&(n=Xc())!==a?(ls=r,r=e="."+n):(cs=r,r=a),r}function Kc(){var r,e,n;return r=cs,(e=function(){var r,e,n;r=cs,oo.test(t.charAt(cs))?(e=t.charAt(cs),cs++):(e=a,0===vs&&js(ao));e!==a?(uo.test(t.charAt(cs))?(n=t.charAt(cs),cs++):(n=a,0===vs&&js(so)),n===a&&(n=null),n!==a?(ls=r,r=e=e+(null!==(o=n)?o:"")):(cs=r,r=a)):(cs=r,r=a);var o;return r}())!==a&&(n=Xc())!==a?(ls=r,r=e=e+n):(cs=r,r=a),r}function Xc(){var t,r,e;if(t=cs,r=[],(e=Zc())!==a)for(;e!==a;)r.push(e),e=Zc();else r=a;return r!==a&&(ls=t,r=r.join("")),t=r}function Zc(){var r;return to.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(ro)),r}function zc(){var r;return eo.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(no)),r}function Jc(){var r,e,n,o;return r=cs,"default"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(L)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function tl(){var r,e,n,o;return r=cs,"to"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(fo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function rl(){var r,e,n,o;return r=cs,"top"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(po)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function el(){var r,e,n,o;return r=cs,"drop"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(vo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DROP"):(cs=r,r=a)):(cs=r,r=a),r}function nl(){var r,e,n,o;return r=cs,"declare"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(ho)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DECLARE"):(cs=r,r=a)):(cs=r,r=a),r}function ol(){var r,e,n,o;return r=cs,"alter"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(mo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ALTER"):(cs=r,r=a)):(cs=r,r=a),r}function al(){var r,e,n,o;return r=cs,"update"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(wo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function ul(){var r,e,n,o;return r=cs,"create"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Oo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function sl(){var r,e,n,o;return r=cs,"temporary"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Lo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function il(){var r,e,n,o;return r=cs,"delete"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Co)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function cl(){var r,e,n,o;return r=cs,"replace"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(To)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function ll(){var r,e,n,o;return r=cs,"rename"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Eo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function fl(){var r,e,n,o;return r=cs,"ignore"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(So)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function pl(){var r,e,n,o;return r=cs,"partition"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(_o)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="PARTITION"):(cs=r,r=a)):(cs=r,r=a),r}function bl(){var r,e,n,o;return r=cs,"into"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Uo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function vl(){var r,e,n,o;return r=cs,"from"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(xo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function hl(){var r,e,n,o;return r=cs,"set"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(st)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SET"):(cs=r,r=a)):(cs=r,r=a),r}function dl(){var r,e,n,o;return r=cs,"as"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(ko)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function yl(){var r,e,n,o;return r=cs,"table"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(No)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TABLE"):(cs=r,r=a)):(cs=r,r=a),r}function ml(){var r,e,n,o;return r=cs,"view"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Ro)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="VIEW"):(cs=r,r=a)):(cs=r,r=a),r}function jl(){var r,e,n,o;return r=cs,"tables"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(qo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TABLES"):(cs=r,r=a)):(cs=r,r=a),r}function wl(){var r,e,n,o;return r=cs,"on"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(Po)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Ol(){var r,e,n,o;return r=cs,"join"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Wo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Ll(){var r,e,n,o;return r=cs,"apply"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Go)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Cl(){var r,e,n,o;return r=cs,"outer"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Yo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function gl(){var r,e,n,o;return r=cs,"union"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Ko)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Al(){var r,e,n,o;return r=cs,"values"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Xo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Tl(){var r,e,n,o;return r=cs,"using"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Zo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function El(){var r,e,n,o;return r=cs,"with"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(qr)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Sl(){var r,e,n,o;return r=cs,"go"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(Jo)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="GO"):(cs=r,r=a)):(cs=r,r=a),r}function _l(){var r,e,n,o;return r=cs,"group"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(ta)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Ul(){var r,e,n,o;return r=cs,"by"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(ra)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function xl(){var r,e,n,o;return r=cs,"offset"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(aa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="OFFSET"):(cs=r,r=a)):(cs=r,r=a),r}function Il(){var r,e,n,o;return r=cs,"fetch"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(ua)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="FETCH"):(cs=r,r=a)):(cs=r,r=a),r}function kl(){var r,e,n,o;return r=cs,"asc"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(sa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ASC"):(cs=r,r=a)):(cs=r,r=a),r}function Nl(){var r,e,n,o;return r=cs,"desc"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(ia)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DESC"):(cs=r,r=a)):(cs=r,r=a),r}function Rl(){var r,e,n,o;return r=cs,"all"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(ca)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ALL"):(cs=r,r=a)):(cs=r,r=a),r}function Vl(){var r,e,n,o;return r=cs,"distinct"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(la)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DISTINCT"):(cs=r,r=a)):(cs=r,r=a),r}function Ml(){var r,e,n,o;return r=cs,"between"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(fa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="BETWEEN"):(cs=r,r=a)):(cs=r,r=a),r}function ql(){var r,e,n,o;return r=cs,"in"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(pa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="IN"):(cs=r,r=a)):(cs=r,r=a),r}function Pl(){var r,e,n,o;return r=cs,"is"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(ba)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="IS"):(cs=r,r=a)):(cs=r,r=a),r}function Dl(){var r,e,n,o;return r=cs,"like"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(va)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="LIKE"):(cs=r,r=a)):(cs=r,r=a),r}function Ql(){var r,e,n,o;return r=cs,"exists"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(ha)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="EXISTS"):(cs=r,r=a)):(cs=r,r=a),r}function Bl(){var r,e,n,o;return r=cs,"not"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Y)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="NOT"):(cs=r,r=a)):(cs=r,r=a),r}function Fl(){var r,e,n,o;return r=cs,"and"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(da)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="AND"):(cs=r,r=a)):(cs=r,r=a),r}function Hl(){var r,e,n,o;return r=cs,"or"===t.substr(cs,2).toLowerCase()?(e=t.substr(cs,2),cs+=2):(e=a,0===vs&&js(ya)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="OR"):(cs=r,r=a)):(cs=r,r=a),r}function $l(){var r,e,n,o;return r=cs,"case"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Ta)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Wl(){var r,e,n,o;return r=cs,"else"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(_a)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Gl(){var r,e,n,o;return r=cs,"end"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Ua)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?r=e=[e,n]:(cs=r,r=a)):(cs=r,r=a),r}function Yl(){var r,e,n,o;return r=cs,"cast"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(xa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CAST"):(cs=r,r=a)):(cs=r,r=a),r}function Kl(){var r,e,n,o;return r=cs,"char"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Ra)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CHAR"):(cs=r,r=a)):(cs=r,r=a),r}function Xl(){var r,e,n,o;return r=cs,"varchar"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Va)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="VARCHAR"):(cs=r,r=a)):(cs=r,r=a),r}function Zl(){var r,e,n,o;return r=cs,"binary"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Ma)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="BINARY"):(cs=r,r=a)):(cs=r,r=a),r}function zl(){var r,e,n,o;return r=cs,"varbinary"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(qa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="VARBINARY"):(cs=r,r=a)):(cs=r,r=a),r}function Jl(){var r,e,n,o;return r=cs,"nvarchar"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Da)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="NVARCHAR"):(cs=r,r=a)):(cs=r,r=a),r}function tf(){var r,e,n,o;return r=cs,"numeric"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Qa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="NUMERIC"):(cs=r,r=a)):(cs=r,r=a),r}function rf(){var r,e,n,o;return r=cs,"decimal"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Ba)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DECIMAL"):(cs=r,r=a)):(cs=r,r=a),r}function ef(){var r,e,n,o;return r=cs,"unsigned"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Ha)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="UNSIGNED"):(cs=r,r=a)):(cs=r,r=a),r}function nf(){var r,e,n,o;return r=cs,"int"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js($a)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="INT"):(cs=r,r=a)):(cs=r,r=a),r}function of(){var r,e,n,o;return r=cs,"integer"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Ga)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="INTEGER"):(cs=r,r=a)):(cs=r,r=a),r}function af(){var r,e,n,o;return r=cs,"smallint"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Ya)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SMALLINT"):(cs=r,r=a)):(cs=r,r=a),r}function uf(){var r,e,n,o;return r=cs,"tinyint"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Ka)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TINYINT"):(cs=r,r=a)):(cs=r,r=a),r}function sf(){var r,e,n,o;return r=cs,"bigint"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(tu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="BIGINT"):(cs=r,r=a)):(cs=r,r=a),r}function cf(){var r,e,n,o;return r=cs,"float"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(ru)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="FLOAT"):(cs=r,r=a)):(cs=r,r=a),r}function lf(){var r,e,n,o;return r=cs,"real"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(eu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="REAL"):(cs=r,r=a)):(cs=r,r=a),r}function ff(){var r,e,n,o;return r=cs,"double"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(nu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DOUBLE"):(cs=r,r=a)):(cs=r,r=a),r}function pf(){var r,e,n,o;return r=cs,"date"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(ou)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DATE"):(cs=r,r=a)):(cs=r,r=a),r}function bf(){var r,e,n,o;return r=cs,"datetime"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(uu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DATETIME"):(cs=r,r=a)):(cs=r,r=a),r}function vf(){var r,e,n,o;return r=cs,"datetime2"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(su)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DATETIME2"):(cs=r,r=a)):(cs=r,r=a),r}function hf(){var r,e,n,o;return r=cs,"datetimeoffset"===t.substr(cs,14).toLowerCase()?(e=t.substr(cs,14),cs+=14):(e=a,0===vs&&js(iu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="DATETIMEOFFSET"):(cs=r,r=a)):(cs=r,r=a),r}function df(){var r,e,n,o;return r=cs,"rows"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Te)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ROWS"):(cs=r,r=a)):(cs=r,r=a),r}function yf(){var r,e,n,o;return r=cs,"time"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(cu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TIME"):(cs=r,r=a)):(cs=r,r=a),r}function mf(){var r,e,n,o;return r=cs,"timestamp"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(lu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TIMESTAMP"):(cs=r,r=a)):(cs=r,r=a),r}function jf(){var r,e,n,o;return r=cs,"current_timestamp"===t.substr(cs,17).toLowerCase()?(e=t.substr(cs,17),cs+=17):(e=a,0===vs&&js(Cu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CURRENT_TIMESTAMP"):(cs=r,r=a)):(cs=r,r=a),r}function wf(){var r;return 64===t.charCodeAt(cs)?(r="@",cs++):(r=a,0===vs&&js(_)),r}function Of(){var r;return(r=function(){var r;return"@@"===t.substr(cs,2)?(r="@@",cs+=2):(r=a,0===vs&&js(_u)),r}())===a&&(r=wf())===a&&(r=function(){var r;return 36===t.charCodeAt(cs)?(r="$",cs++):(r=a,0===vs&&js(Uu)),r}()),r}function Lf(){var r;return 61===t.charCodeAt(cs)?(r="=",cs++):(r=a,0===vs&&js(Ve)),r}function Cf(){var r,e,n,o;return r=cs,"add"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Nu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ADD"):(cs=r,r=a)):(cs=r,r=a),r}function gf(){var r,e,n,o;return r=cs,"column"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(Ru)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="COLUMN"):(cs=r,r=a)):(cs=r,r=a),r}function Af(){var r,e,n,o;return r=cs,"index"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Vu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="INDEX"):(cs=r,r=a)):(cs=r,r=a),r}function Tf(){var r,e,n,o;return r=cs,"key"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(d)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="KEY"):(cs=r,r=a)):(cs=r,r=a),r}function Ef(){var r,e,n,o;return r=cs,"unique"===t.substr(cs,6).toLowerCase()?(e=t.substr(cs,6),cs+=6):(e=a,0===vs&&js(h)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="UNIQUE"):(cs=r,r=a)):(cs=r,r=a),r}function Sf(){var r,e,n,o;return r=cs,"clustered"===t.substr(cs,9).toLowerCase()?(e=t.substr(cs,9),cs+=9):(e=a,0===vs&&js(Pu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CLUSTERED"):(cs=r,r=a)):(cs=r,r=a),r}function _f(){var r,e,n,o;return r=cs,"nonclustered"===t.substr(cs,12).toLowerCase()?(e=t.substr(cs,12),cs+=12):(e=a,0===vs&&js(Du)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="NONCLUSTERED"):(cs=r,r=a)):(cs=r,r=a),r}function Uf(){var r,e,n,o;return r=cs,"comment"===t.substr(cs,7).toLowerCase()?(e=t.substr(cs,7),cs+=7):(e=a,0===vs&&js(Qu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="COMMENT"):(cs=r,r=a)):(cs=r,r=a),r}function xf(){var r,e,n,o;return r=cs,"constraint"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(Bu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="CONSTRAINT"):(cs=r,r=a)):(cs=r,r=a),r}function If(){var r;return 46===t.charCodeAt(cs)?(r=".",cs++):(r=a,0===vs&&js(Jn)),r}function kf(){var r;return 44===t.charCodeAt(cs)?(r=",",cs++):(r=a,0===vs&&js(Xu)),r}function Nf(){var r;return 42===t.charCodeAt(cs)?(r="*",cs++):(r=a,0===vs&&js(Ye)),r}function Rf(){var r;return 40===t.charCodeAt(cs)?(r="(",cs++):(r=a,0===vs&&js(Ar)),r}function Vf(){var r;return 41===t.charCodeAt(cs)?(r=")",cs++):(r=a,0===vs&&js(Tr)),r}function Mf(){var r;return 91===t.charCodeAt(cs)?(r="[",cs++):(r=a,0===vs&&js(sn)),r}function qf(){var r;return 93===t.charCodeAt(cs)?(r="]",cs++):(r=a,0===vs&&js(fn)),r}function Pf(){var r;return 59===t.charCodeAt(cs)?(r=";",cs++):(r=a,0===vs&&js(Zu)),r}function Df(){var r;return(r=function(){var r;return"||"===t.substr(cs,2)?(r="||",cs+=2):(r=a,0===vs&&js(zu)),r}())===a&&(r=function(){var r;return"&&"===t.substr(cs,2)?(r="&&",cs+=2):(r=a,0===vs&&js(Ju)),r}()),r}function Qf(){var t,r;for(t=[],(r=Wf())===a&&(r=Ff());r!==a;)t.push(r),(r=Wf())===a&&(r=Ff());return t}function Bf(){var t,r;if(t=[],(r=Wf())===a&&(r=Ff()),r!==a)for(;r!==a;)t.push(r),(r=Wf())===a&&(r=Ff());else t=a;return t}function Ff(){var r;return(r=function r(){var e,n,o,u,s,i,c;e=cs,"/*"===t.substr(cs,2)?(n="/*",cs+=2):(n=a,0===vs&&js(ts));if(n!==a){for(o=[],u=cs,s=cs,vs++,"*/"===t.substr(cs,2)?(i="*/",cs+=2):(i=a,0===vs&&js(rs)),vs--,i===a?s=void 0:(cs=s,s=a),s!==a?(i=cs,vs++,"/*"===t.substr(cs,2)?(c="/*",cs+=2):(c=a,0===vs&&js(ts)),vs--,c===a?i=void 0:(cs=i,i=a),i!==a&&(c=$f())!==a?u=s=[s,i,c]:(cs=u,u=a)):(cs=u,u=a),u===a&&(u=r());u!==a;)o.push(u),u=cs,s=cs,vs++,"*/"===t.substr(cs,2)?(i="*/",cs+=2):(i=a,0===vs&&js(rs)),vs--,i===a?s=void 0:(cs=s,s=a),s!==a?(i=cs,vs++,"/*"===t.substr(cs,2)?(c="/*",cs+=2):(c=a,0===vs&&js(ts)),vs--,c===a?i=void 0:(cs=i,i=a),i!==a&&(c=$f())!==a?u=s=[s,i,c]:(cs=u,u=a)):(cs=u,u=a),u===a&&(u=r());o!==a?("*/"===t.substr(cs,2)?(u="*/",cs+=2):(u=a,0===vs&&js(rs)),u!==a?e=n=[n,o,u]:(cs=e,e=a)):(cs=e,e=a)}else cs=e,e=a;return e}())===a&&(r=function(){var r,e,n,o,u,s;r=cs,"--"===t.substr(cs,2)?(e="--",cs+=2):(e=a,0===vs&&js(es));if(e!==a){for(n=[],o=cs,u=cs,vs++,s=Gf(),vs--,s===a?u=void 0:(cs=u,u=a),u!==a&&(s=$f())!==a?o=u=[u,s]:(cs=o,o=a);o!==a;)n.push(o),o=cs,u=cs,vs++,s=Gf(),vs--,s===a?u=void 0:(cs=u,u=a),u!==a&&(s=$f())!==a?o=u=[u,s]:(cs=o,o=a);n!==a?r=e=[e,n]:(cs=r,r=a)}else cs=r,r=a;return r}()),r}function Hf(){var t,r,e,n,o,u,s;return t=cs,(r=Uf())!==a&&Qf()!==a?((e=Lf())===a&&(e=null),e!==a&&Qf()!==a&&(n=Qc())!==a?(ls=t,u=e,s=n,t=r={type:(o=r).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:s}):(cs=t,t=a)):(cs=t,t=a),t}function $f(){var r;return t.length>cs?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(ns)),r}function Wf(){var r;return os.test(t.charAt(cs))?(r=t.charAt(cs),cs++):(r=a,0===vs&&js(as)),r}function Gf(){var r,e;if((r=function(){var r,e;r=cs,vs++,t.length>cs?(e=t.charAt(cs),cs++):(e=a,0===vs&&js(ns));vs--,e===a?r=void 0:(cs=r,r=a);return r}())===a)if(r=[],Zn.test(t.charAt(cs))?(e=t.charAt(cs),cs++):(e=a,0===vs&&js(zn)),e!==a)for(;e!==a;)r.push(e),Zn.test(t.charAt(cs))?(e=t.charAt(cs),cs++):(e=a,0===vs&&js(zn));else r=a;return r}function Yf(){var r,e;return r=cs,ls=cs,dp=[],(!0?void 0:a)!==a&&Qf()!==a?((e=Kf())===a&&(e=function(){var r,e;r=cs,function(){var r;return"return"===t.substr(cs,6).toLowerCase()?(r=t.substr(cs,6),cs+=6):(r=a,0===vs&&js(xu)),r}()!==a&&Qf()!==a&&(e=Xf())!==a?(ls=r,r={type:"return",expr:e}):(cs=r,r=a);return r}()),e!==a?(ls=r,r={stmt:e,vars:dp}):(cs=r,r=a)):(cs=r,r=a),r}function Kf(){var r,e,n,o;return r=cs,(e=np())===a&&(e=op()),e!==a&&Qf()!==a?((n=function(){var r;return":="===t.substr(cs,2)?(r=":=",cs+=2):(r=a,0===vs&&js(Iu)),r}())===a&&(n=Lf()),n===a&&(n=null),n!==a&&Qf()!==a&&(o=Xf())!==a?(ls=r,r=e={type:"assign",left:e,symbol:n,right:o}):(cs=r,r=a)):(cs=r,r=a),r}function Xf(){var t;return(t=Js())===a&&(t=function(){var t,r,e,n,o;t=cs,(r=np())!==a&&Qf()!==a&&(e=gi())!==a&&Qf()!==a&&(n=np())!==a&&Qf()!==a&&(o=Ei())!==a?(ls=t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(cs=t,t=a);return t}())===a&&(t=Zf())===a&&(t=function(){var t,r;t=cs,Mf()!==a&&Qf()!==a&&(r=ep())!==a&&Qf()!==a&&qf()!==a?(ls=t,t={type:"array",value:r}):(cs=t,t=a);return t}()),t}function Zf(){var t,r,e,n,o,u,s,i;if(t=cs,(r=zf())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=ec())!==a&&(s=Qf())!==a&&(i=zf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=ec())!==a&&(s=Qf())!==a&&(i=zf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=Me(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function zf(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Jf())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=oc())!==a&&(s=Qf())!==a&&(i=Jf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=oc())!==a&&(s=Qf())!==a&&(i=Jf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=Me(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function Jf(){var t,r,e;return(t=Pc())===a&&(t=np())===a&&(t=rp())===a&&(t=Ac())===a&&(t=cs,Rf()!==a&&Qf()!==a&&(r=Zf())!==a&&Qf()!==a&&Vf()!==a?(ls=t,(e=r).parentheses=!0,t=e):(cs=t,t=a)),t}function tp(){var t,r,e,n,o,u,s;return t=cs,(r=cc())!==a?(e=cs,(n=Qf())!==a&&(o=If())!==a&&(u=Qf())!==a&&(s=cc())!==a?e=n=[n,o,u,s]:(cs=e,e=a),e===a&&(e=null),e!==a?(ls=t,t=r=function(t,r){const e={name:[t]};return null!==r&&(e.schema=t,e.name=[r[3]]),e}(r,e)):(cs=t,t=a)):(cs=t,t=a),t}function rp(){var t,r,e;return t=cs,(r=tp())!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a?((e=ep())===a&&(e=null),e!==a&&Qf()!==a&&Vf()!==a?(ls=t,t=r={type:"function",name:r,args:{type:"expr_list",value:e},...ip()}):(cs=t,t=a)):(cs=t,t=a),t===a&&(t=cs,(r=tp())!==a&&(ls=t,r=function(t){return{type:"function",name:t,args:null,...ip()}}(r)),t=r),t}function ep(){var t,r,e,n,o,u,s,i;if(t=cs,(r=Jf())!==a){for(e=[],n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Jf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);n!==a;)e.push(n),n=cs,(o=Qf())!==a&&(u=kf())!==a&&(s=Qf())!==a&&(i=Jf())!==a?n=o=[o,u,s,i]:(cs=n,n=a);e!==a?(ls=t,t=r=v(r,e)):(cs=t,t=a)}else cs=t,t=a;return t}function np(){var t,r,e,n,o;return t=cs,(r=Of())!==a&&(e=op())!==a?(ls=t,n=r,o=e,t=r={type:"var",...o,prefix:n}):(cs=t,t=a),t}function op(){var r,e,n,o,u;return r=cs,(e=wc())!==a&&(n=function(){var r,e,n,o,u;r=cs,e=[],n=cs,46===t.charCodeAt(cs)?(o=".",cs++):(o=a,0===vs&&js(Jn));o!==a&&(u=wc())!==a?n=o=[o,u]:(cs=n,n=a);for(;n!==a;)e.push(n),n=cs,46===t.charCodeAt(cs)?(o=".",cs++):(o=a,0===vs&&js(Jn)),o!==a&&(u=wc())!==a?n=o=[o,u]:(cs=n,n=a);e!==a&&(ls=r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==a?(ls=r,o=e,u=n,dp.push(o),r=e={type:"var",name:o,members:u,prefix:null}):(cs=r,r=a),r===a&&(r=cs,(e=$c())!==a&&(ls=r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function ap(){var r;return(r=function(){var r,e,n,o,u,s;r=cs,(e=Mf())===a&&(e=null);if(e!==a)if(Qf()!==a)if((n=Kl())===a&&(n=Xl())===a&&(n=function(){var r,e,n,o;return r=cs,"nchar"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(Pa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="NCHAR"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=Jl())===a&&(n=Zl())===a&&(n=zl()),n!==a)if(Qf()!==a)if((o=qf())===a&&(o=null),o!==a)if(ls=cs,(us(e,0,o)?a:void 0)!==a)if(Qf()!==a)if(Rf()!==a)if(Qf()!==a){if(u=[],to.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(ro)),s!==a)for(;s!==a;)u.push(s),to.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(ro));else u=a;u!==a&&(s=Qf())!==a&&Vf()!==a?(ls=r,e={dataType:n,length:parseInt(u.join(""),10),parentheses:!0},r=e):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;r===a&&(r=cs,(e=Mf())===a&&(e=null),e!==a&&Qf()!==a?((n=Jl())===a&&(n=Xl())===a&&(n=zl()),n!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a&&Qf()!==a&&Rf()!==a&&Qf()!==a?("max"===t.substr(cs,3).toLowerCase()?(u=t.substr(cs,3),cs+=3):(u=a,0===vs&&js(Oa)),u!==a&&(s=Qf())!==a&&Vf()!==a?(ls=r,e=function(t,r,e,n){return{dataType:r,length:"max"}}(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a),r===a&&(r=cs,(e=Mf())===a&&(e=null),e!==a&&Qf()!==a?((n=Kl())===a&&(n=Xl())===a&&(n=Zl())===a&&(n=zl()),n!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a?(ls=r,e=ss(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)));return r}())===a&&(r=function(){var r,e,n,o,u,s,i,c,l,f,p,b,v,h,d;r=cs,(e=Mf())===a&&(e=null);if(e!==a)if(Qf()!==a)if((n=tf())===a&&(n=rf())===a&&(n=nf())===a&&(n=of())===a&&(n=af())===a&&(n=uf())===a&&(n=sf())===a&&(n=cf())===a&&(n=lf())===a&&(n=ff()),n!==a)if((o=Qf())!==a)if((u=qf())===a&&(u=null),u!==a)if(ls=cs,(us(e,0,u)?a:void 0)!==a)if((s=Qf())!==a)if((i=Rf())!==a)if((c=Qf())!==a){if(l=[],to.test(t.charAt(cs))?(f=t.charAt(cs),cs++):(f=a,0===vs&&js(ro)),f!==a)for(;f!==a;)l.push(f),to.test(t.charAt(cs))?(f=t.charAt(cs),cs++):(f=a,0===vs&&js(ro));else l=a;if(l!==a)if((f=Qf())!==a){if(p=cs,(b=kf())!==a)if((v=Qf())!==a){if(h=[],to.test(t.charAt(cs))?(d=t.charAt(cs),cs++):(d=a,0===vs&&js(ro)),d!==a)for(;d!==a;)h.push(d),to.test(t.charAt(cs))?(d=t.charAt(cs),cs++):(d=a,0===vs&&js(ro));else h=a;h!==a?p=b=[b,v,h]:(cs=p,p=a)}else cs=p,p=a;else cs=p,p=a;p===a&&(p=null),p!==a&&(b=Qf())!==a&&(v=Vf())!==a&&(h=Qf())!==a?((d=up())===a&&(d=null),d!==a?(ls=r,y=p,m=d,e={dataType:n,length:parseInt(l.join(""),10),scale:y&&parseInt(y[2].join(""),10),parentheses:!0,suffix:m},r=e):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;var y,m;if(r===a){if(r=cs,(e=Mf())===a&&(e=null),e!==a)if(Qf()!==a)if((n=tf())===a&&(n=rf())===a&&(n=nf())===a&&(n=of())===a&&(n=af())===a&&(n=uf())===a&&(n=sf())===a&&(n=cf())===a&&(n=lf())===a&&(n=ff()),n!==a)if((o=qf())===a&&(o=null),o!==a)if(ls=cs,(u=(u=us(e,0,o))?a:void 0)!==a)if(Qf()!==a){if(s=[],to.test(t.charAt(cs))?(i=t.charAt(cs),cs++):(i=a,0===vs&&js(ro)),i!==a)for(;i!==a;)s.push(i),to.test(t.charAt(cs))?(i=t.charAt(cs),cs++):(i=a,0===vs&&js(ro));else s=a;s!==a&&(i=Qf())!==a?((c=up())===a&&(c=null),c!==a?(ls=r,e=function(t,r,e,n,o){return{dataType:r,length:parseInt(n.join(""),10),suffix:o}}(0,n,0,s,c),r=e):(cs=r,r=a)):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;r===a&&(r=cs,(e=Mf())===a&&(e=null),e!==a&&Qf()!==a?((n=tf())===a&&(n=rf())===a&&(n=nf())===a&&(n=of())===a&&(n=af())===a&&(n=uf())===a&&(n=sf())===a&&(n=cf())===a&&(n=lf())===a&&(n=ff())===a&&(n=function(){var r,e,n,o;return r=cs,"bit"===t.substr(cs,3).toLowerCase()?(e=t.substr(cs,3),cs+=3):(e=a,0===vs&&js(Ia)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="BIT"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=function(){var r,e,n,o;return r=cs,"money"===t.substr(cs,5).toLowerCase()?(e=t.substr(cs,5),cs+=5):(e=a,0===vs&&js(ka)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MONEY"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=function(){var r,e,n,o;return r=cs,"smallmoney"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(Na)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SMALLMONEY"):(cs=r,r=a)):(cs=r,r=a),r}()),n!==a&&(o=Qf())!==a?((u=qf())===a&&(u=null),u!==a?(ls=cs,(us(e,0,u)?a:void 0)!==a&&(s=Qf())!==a?((i=up())===a&&(i=null),i!==a&&(c=Qf())!==a?(ls=r,e=function(t,r,e,n){return{dataType:r,suffix:n}}(0,n,0,i),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a))}return r}())===a&&(r=function(){var r,e,n,o,u,s,i,c,l,f,p;r=cs,(e=Mf())===a&&(e=null);if(e!==a)if(Qf()!==a)if((n=vf())===a&&(n=hf())===a&&(n=yf()),n!==a)if(Qf()!==a)if((o=qf())===a&&(o=null),o!==a)if(ls=cs,(us(e,0,o)?a:void 0)!==a)if(Rf()!==a)if(Qf()!==a){if(u=[],to.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(ro)),s!==a)for(;s!==a;)u.push(s),to.test(t.charAt(cs))?(s=t.charAt(cs),cs++):(s=a,0===vs&&js(ro));else u=a;if(u!==a)if((s=Qf())!==a){if(i=cs,(c=kf())!==a)if((l=Qf())!==a){if(f=[],to.test(t.charAt(cs))?(p=t.charAt(cs),cs++):(p=a,0===vs&&js(ro)),p!==a)for(;p!==a;)f.push(p),to.test(t.charAt(cs))?(p=t.charAt(cs),cs++):(p=a,0===vs&&js(ro));else f=a;f!==a?i=c=[c,l,f]:(cs=i,i=a)}else cs=i,i=a;else cs=i,i=a;i===a&&(i=null),i!==a&&(c=Qf())!==a&&(l=Vf())!==a?(ls=r,e={dataType:n,length:parseInt(u.join(""),10),parentheses:!0},r=e):(cs=r,r=a)}else cs=r,r=a;else cs=r,r=a}else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;else cs=r,r=a;r===a&&(r=cs,(e=Mf())===a&&(e=null),e!==a&&Qf()!==a?((n=pf())===a&&(n=function(){var r,e,n,o;return r=cs,"smalldatetime"===t.substr(cs,13).toLowerCase()?(e=t.substr(cs,13),cs+=13):(e=a,0===vs&&js(au)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="SMALLDATETIME"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=vf())===a&&(n=bf())===a&&(n=hf())===a&&(n=yf())===a&&(n=mf()),n!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a?(ls=r,e=ss(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a));return r}())===a&&(r=function(){var r,e,n,o;r=cs,(e=Mf())===a&&(e=null);e!==a&&Qf()!==a&&(n=function(){var r,e,n,o;return r=cs,"json"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Re)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="JSON"):(cs=r,r=a)):(cs=r,r=a),r}())!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a?(ls=r,e=ss(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,(e=Mf())===a&&(e=null);e!==a&&Qf()!==a?((n=function(){var r,e,n,o;return r=cs,"tinytext"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Xa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TINYTEXT"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=function(){var r,e,n,o;return r=cs,"text"===t.substr(cs,4).toLowerCase()?(e=t.substr(cs,4),cs+=4):(e=a,0===vs&&js(Za)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="TEXT"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=function(){var r,e,n,o;return r=cs,"mediumtext"===t.substr(cs,10).toLowerCase()?(e=t.substr(cs,10),cs+=10):(e=a,0===vs&&js(za)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="MEDIUMTEXT"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=function(){var r,e,n,o;return r=cs,"longtext"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Ja)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="LONGTEXT"):(cs=r,r=a)):(cs=r,r=a),r}()),n!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a?(ls=r,e=is(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=cs,(e=Mf())===a&&(e=null);e!==a&&Qf()!==a&&(n=function(){var r,e,n,o;return r=cs,"uniqueidentifier"===t.substr(cs,16).toLowerCase()?(e=t.substr(cs,16),cs+=16):(e=a,0===vs&&js(pu)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="UNIQUEIDENTIFIER"):(cs=r,r=a)):(cs=r,r=a),r}())!==a&&Qf()!==a?((o=qf())===a&&(o=null),o!==a?(ls=cs,(us(e,0,o)?a:void 0)!==a?(ls=r,e=is(0,n),r=e):(cs=r,r=a)):(cs=r,r=a)):(cs=r,r=a);return r}()),r}function up(){var r,e,n;return r=cs,(e=ef())===a&&(e=null),e!==a&&Qf()!==a?((n=function(){var r,e,n,o;return r=cs,"zerofill"===t.substr(cs,8).toLowerCase()?(e=t.substr(cs,8),cs+=8):(e=a,0===vs&&js(Wa)),e!==a?(n=cs,vs++,o=Lc(),vs--,o===a?n=void 0:(cs=n,n=a),n!==a?(ls=r,r=e="ZEROFILL"):(cs=r,r=a)):(cs=r,r=a),r}())===a&&(n=null),n!==a?(ls=r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(cs=r,r=a)):(cs=r,r=a),r}const sp={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CROSS:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,FOR:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,NOCHECK:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0,PIVOT:!0,UNPIVOT:!0};function ip(){return r.includeLocations?{loc:ms(ls,cs)}:{}}function cp(t,r){return{type:"unary_expr",operator:t,expr:r}}function lp(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e}}function fp(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function pp(t,r,e=3){const n=[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function bp(t,r){let e=t;for(let t=0;t<r.length;t++)e=lp(r[t][1],e,r[t][3]);return e}function vp(t){const r=jp[t];return r||(t||null)}function hp(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=vp(t[1])),r.add(t.join("::"))}return Array.from(r)}let dp=[];const yp=new Set,mp=new Set,jp={};if((e=s())!==a&&cs===t.length)return e;throw e!==a&&cs<t.length&&js({type:"end"}),ws(bs,ps<t.length?t.charAt(ps):null,ps<t.length?ms(ps,ps+1):ms(ps,ps))}}},function(t,r,e){t.exports=e(27)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(24);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(28))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),a="function"==typeof BigInt;function u(t,r,e,n){return void 0===t?u[0]:void 0!==r&&(10!=+r||e)?D(t,r,e,n):$(t)}function s(t,r){this.value=t,this.sign=r,this.isSmall=!1}function i(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&S(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function h(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,e){var n,o,a=t.length,u=e.length,s=new Array(a),i=0,c=r;for(o=0;o<u;o++)i=(n=t[o]+e[o]+i)>=c?1:0,s[o]=n-i*c;for(;o<a;)i=(n=t[o]+i)===c?1:0,s[o++]=n-i*c;return i>0&&s.push(i),s}function y(t,r){return t.length>=r.length?d(t,r):d(r,t)}function m(t,e){var n,o,a=t.length,u=new Array(a),s=r;for(o=0;o<a;o++)n=t[o]-s+e,e=Math.floor(n/s),u[o]=n-e*s,e+=1;for(;e>0;)u[o++]=e%s,e=Math.floor(e/s);return u}function j(t,r){var e,n,o=t.length,a=r.length,u=new Array(o),s=0;for(e=0;e<a;e++)(n=t[e]-s-r[e])<0?(n+=1e7,s=1):s=0,u[e]=n;for(e=a;e<o;e++){if(!((n=t[e]-s)<0)){u[e++]=n;break}n+=1e7,u[e]=n}for(;e<o;e++)u[e]=t[e];return b(u),u}function w(t,r,e){var n,o,a=t.length,u=new Array(a),c=-r;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,u[n]=o<0?o+1e7:o;return"number"==typeof(u=p(u))?(e&&(u=-u),new i(u)):new s(u,e)}function O(t,r){var e,n,o,a,u=t.length,s=r.length,i=v(u+s);for(o=0;o<u;++o){a=t[o];for(var c=0;c<s;++c)e=a*r[c]+i[o+c],n=Math.floor(e/1e7),i[o+c]=e-1e7*n,i[o+c+1]+=n}return b(i),i}function L(t,e){var n,o,a=t.length,u=new Array(a),s=r,i=0;for(o=0;o<a;o++)n=t[o]*e+i,i=Math.floor(n/s),u[o]=n-i*s;for(;i>0;)u[o++]=i%s,i=Math.floor(i/s);return u}function C(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function g(t,e,n){return new s(t<r?L(e,t):O(e,f(t)),n)}function A(t){var r,e,n,o,a=t.length,u=v(a+a);for(n=0;n<a;n++){e=0-(o=t[n])*o;for(var s=n;s<a;s++)r=o*t[s]*2+u[n+s]+e,e=Math.floor(r/1e7),u[n+s]=r-1e7*e;u[n+a]=e}return b(u),u}function T(t,r){var e,n,o,a,u=t.length,s=v(u);for(o=0,e=u-1;e>=0;--e)o=(a=1e7*o+t[e])-(n=h(a/r))*r,s[e]=0|n;return[s,0|o]}function E(t,e){var n,o=$(e);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,y=o.value;if(0===y)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new i(h(d/y)),new i(d%y)]:[u[0],t];if(o.isSmall){if(1===y)return[t,u[0]];if(-1==y)return[t.negate(),u[0]];var m=Math.abs(y);if(m<r){l=p((n=T(d,m))[0]);var w=n[1];return t.sign&&(w=-w),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new i(l),new i(w)]):[new s(l,t.sign!==o.sign),new i(w)]}y=f(m)}var O=S(d,y);if(-1===O)return[u[0],t];if(0===O)return[u[t.sign===o.sign?1:-1],u[0]];l=(n=d.length+y.length<=200?function(t,e){var n,o,a,u,s,i,c,l=t.length,f=e.length,b=r,h=v(e.length),d=e[f-1],y=Math.ceil(b/(2*d)),m=L(t,y),j=L(e,y);for(m.length<=l&&m.push(0),j.push(0),d=j[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==d&&(n=Math.floor((m[o+f]*b+m[o+f-1])/d)),a=0,u=0,i=j.length,s=0;s<i;s++)a+=n*j[s],c=Math.floor(a/b),u+=m[o+s]-(a-c*b),a=c,u<0?(m[o+s]=u+b,u=-1):(m[o+s]=u,u=0);for(;0!==u;){for(n-=1,a=0,s=0;s<i;s++)(a+=m[o+s]-b+j[s])<0?(m[o+s]=a+b,a=0):(m[o+s]=a,a=1);u+=a}h[o]=n}return m=T(m,y)[0],[p(h),p(m)]}(d,y):function(t,r){for(var e,n,o,a,u,s=t.length,i=r.length,c=[],l=[];s;)if(l.unshift(t[--s]),b(l),S(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*r[i-1]+r[i-2],n>i&&(o=1e7*(o+1)),e=Math.ceil(o/a);do{if(S(u=L(r,e),l)<=0)break;e--}while(e);c.push(e),l=j(l,u)}return c.reverse(),[p(c),p(l)]}(d,y))[0];var C=t.sign!==o.sign,g=n[1],A=t.sign;return"number"==typeof l?(C&&(l=-l),l=new i(l)):l=new s(l,C),"number"==typeof g?(A&&(g=-g),g=new i(g)):g=new s(g,A),[l,g]}function S(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function _(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function U(t,r){for(var e,n,a,u=t.prev(),s=u,i=0;s.isEven();)s=s.divide(2),i++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(a=o(r[n]).modPow(s,t)).isUnit()&&!a.equals(u)){for(e=i-1;0!=e;e--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(u))continue t}return!1}return!0}s.prototype=Object.create(u.prototype),i.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),s.prototype.add=function(t){var r=$(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new s(m(e,Math.abs(n)),this.sign):new s(y(e,n),this.sign)},s.prototype.plus=s.prototype.add,i.prototype.add=function(t){var r=$(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new i(e+n);n=f(Math.abs(n))}return new s(m(n,Math.abs(e)),e<0)},i.prototype.plus=i.prototype.add,c.prototype.add=function(t){return new c(this.value+$(t).value)},c.prototype.plus=c.prototype.add,s.prototype.subtract=function(t){var r=$(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?w(e,Math.abs(n),this.sign):function(t,r,e){var n;return S(t,r)>=0?n=j(t,r):(n=j(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new i(n)):new s(n,e)}(e,n,this.sign)},s.prototype.minus=s.prototype.subtract,i.prototype.subtract=function(t){var r=$(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new i(e-n):w(n,Math.abs(e),e>=0)},i.prototype.minus=i.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-$(t).value)},c.prototype.minus=c.prototype.subtract,s.prototype.negate=function(){return new s(this.value,!this.sign)},i.prototype.negate=function(){var t=this.sign,r=new i(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},s.prototype.abs=function(){return new s(this.value,!1)},i.prototype.abs=function(){return new i(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},s.prototype.multiply=function(t){var e,n,o,a=$(t),i=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new s(L(i,e),l);c=f(e)}return n=i.length,o=c.length,new s(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return O(r,e);n=Math.ceil(n/2);var o=r.slice(n),a=r.slice(0,n),u=e.slice(n),s=e.slice(0,n),i=t(a,s),c=t(o,u),l=t(y(a,o),y(s,u)),f=y(y(i,C(j(j(l,i),c),n)),C(c,2*n));return b(f),f}(i,c):O(i,c),l)},s.prototype.times=s.prototype.multiply,i.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new i(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},s.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},i.prototype.multiply=function(t){return $(t)._multiplyBySmall(this)},i.prototype.times=i.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*$(t).value)},c.prototype.times=c.prototype.multiply,s.prototype.square=function(){return new s(A(this.value),!1)},i.prototype.square=function(){var t=this.value*this.value;return l(t)?new i(t):new s(A(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},s.prototype.divmod=function(t){var r=E(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=i.prototype.divmod=s.prototype.divmod,s.prototype.divide=function(t){return E(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/$(t).value)},i.prototype.over=i.prototype.divide=s.prototype.over=s.prototype.divide,s.prototype.mod=function(t){return E(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%$(t).value)},i.prototype.remainder=i.prototype.mod=s.prototype.remainder=s.prototype.mod,s.prototype.pow=function(t){var r,e,n,o=$(t),a=this.value,s=o.value;if(0===s)return u[1];if(0===a)return u[0];if(1===a)return u[1];if(-1===a)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(a,s)))return new i(h(r));for(e=this,n=u[1];!0&s&&(n=n.times(e),--s),0!==s;)s/=2,e=e.square();return n},i.prototype.pow=s.prototype.pow,c.prototype.pow=function(t){var r=$(t),e=this.value,n=r.value,o=BigInt(0),a=BigInt(1),s=BigInt(2);if(n===o)return u[1];if(e===o)return u[0];if(e===a)return u[1];if(e===BigInt(-1))return r.isEven()?u[1]:u[-1];if(r.isNegative())return new c(o);for(var i=this,l=u[1];(n&a)===a&&(l=l.times(i),--n),n!==o;)n/=s,i=i.square();return l},s.prototype.modPow=function(t,r){if(t=$(t),(r=$(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=u[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(u[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=i.prototype.modPow=s.prototype.modPow,s.prototype.compareAbs=function(t){var r=$(t),e=this.value,n=r.value;return r.isSmall?1:S(e,n)},i.prototype.compareAbs=function(t){var r=$(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=$(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=$(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:S(e,n)*(this.sign?-1:1)},s.prototype.compareTo=s.prototype.compare,i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=$(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},i.prototype.compareTo=i.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=$(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,s.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=i.prototype.eq=i.prototype.equals=s.prototype.eq=s.prototype.equals,s.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=i.prototype.neq=i.prototype.notEquals=s.prototype.neq=s.prototype.notEquals,s.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=i.prototype.gt=i.prototype.greater=s.prototype.gt=s.prototype.greater,s.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=i.prototype.lt=i.prototype.lesser=s.prototype.lt=s.prototype.lesser,s.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals,s.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals,s.prototype.isEven=function(){return 0==(1&this.value[0])},i.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},s.prototype.isOdd=function(){return 1==(1&this.value[0])},i.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},s.prototype.isPositive=function(){return!this.sign},i.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=i.prototype.isPositive,s.prototype.isNegative=function(){return this.sign},i.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=i.prototype.isNegative,s.prototype.isUnit=function(){return!1},i.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},s.prototype.isZero=function(){return!1},i.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},s.prototype.isDivisibleBy=function(t){var r=$(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=i.prototype.isDivisibleBy=s.prototype.isDivisibleBy,s.prototype.isPrime=function(t){var r=_(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return U(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),u=Math.ceil(!0===t?2*Math.pow(a,2):a),s=[],i=0;i<u;i++)s.push(o(i+2));return U(e,s)},c.prototype.isPrime=i.prototype.isPrime=s.prototype.isPrime,s.prototype.isProbablePrime=function(t,r){var e=_(this);if(void 0!==e)return e;for(var n=this.abs(),a=void 0===t?5:t,u=[],s=0;s<a;s++)u.push(o.randBetween(2,n.minus(2),r));return U(n,u)},c.prototype.isProbablePrime=i.prototype.isProbablePrime=s.prototype.isProbablePrime,s.prototype.modInv=function(t){for(var r,e,n,a=o.zero,u=o.one,s=$(t),i=this.abs();!i.isZero();)r=s.divide(i),e=a,n=s,a=u,s=i,u=e.subtract(r.multiply(u)),i=n.subtract(r.multiply(i));if(!s.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=i.prototype.modInv=s.prototype.modInv,s.prototype.next=function(){var t=this.value;return this.sign?w(t,1,this.sign):new s(m(t,1),this.sign)},i.prototype.next=function(){var t=this.value;return t+1<e?new i(t+1):new s(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},s.prototype.prev=function(){var t=this.value;return this.sign?new s(m(t,1),!0):w(t,1,this.sign)},i.prototype.prev=function(){var t=this.value;return t-1>-e?new i(t-1):new s(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=r;)x.push(2*x[x.length-1]);var I=x.length,k=x[I-1];function N(t){return Math.abs(t)<=r}function R(t,r,e){r=$(r);for(var n=t.isNegative(),a=r.isNegative(),u=n?t.not():t,s=a?r.not():r,i=0,c=0,l=null,f=null,p=[];!u.isZero()||!s.isZero();)i=(l=E(u,k))[1].toJSNumber(),n&&(i=k-1-i),c=(f=E(s,k))[1].toJSNumber(),a&&(c=k-1-c),u=l[0],s=f[0],p.push(e(i,c));for(var b=0!==e(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(k).add(o(p[v]));return b}s.prototype.shiftLeft=function(t){var r=$(t).toJSNumber();if(!N(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=I;)e=e.multiply(k),r-=I-1;return e.multiply(x[r])},c.prototype.shiftLeft=i.prototype.shiftLeft=s.prototype.shiftLeft,s.prototype.shiftRight=function(t){var r,e=$(t).toJSNumber();if(!N(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=I;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=E(n,k))[1].isNegative()?r[0].prev():r[0],e-=I-1}return(r=E(n,x[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=i.prototype.shiftRight=s.prototype.shiftRight,s.prototype.not=function(){return this.negate().prev()},c.prototype.not=i.prototype.not=s.prototype.not,s.prototype.and=function(t){return R(this,t,(function(t,r){return t&r}))},c.prototype.and=i.prototype.and=s.prototype.and,s.prototype.or=function(t){return R(this,t,(function(t,r){return t|r}))},c.prototype.or=i.prototype.or=s.prototype.or,s.prototype.xor=function(t){return R(this,t,(function(t,r){return t^r}))},c.prototype.xor=i.prototype.xor=s.prototype.xor;function V(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function M(t,r){return t=$(t),r=$(r),t.greater(r)?t:r}function q(t,r){return t=$(t),r=$(r),t.lesser(r)?t:r}function P(t,r){if(t=$(t).abs(),r=$(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=u[1];t.isEven()&&r.isEven();)e=q(V(t),V(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(V(t));do{for(;r.isEven();)r=r.divide(V(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}s.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),a=n.p,u=n.e,s=a.multiply(e);return s.compareTo(r)<=0?{p:s,e:2*u+1}:{p:a,e:2*u}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=i.prototype.bitLength=s.prototype.bitLength;var D=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,a=t.length,u=Math.abs(r),s={};for(o=0;o<e.length;o++)s[e[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in s&&s[l]>=u)){if("1"===l&&1===u)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=$(r);var i=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in s)i.push($(s[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);i.push($(t.slice(f+1,o)))}}return Q(i,r,c)};function Q(t,r,e){var n,o=u[0],a=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(r);return e?o.negate():o}function B(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,u=[],s=t;s.isNegative()||s.compareAbs(r)>=0;){a=s.divmod(r),s=a.quotient;var i=a.remainder;i.isNegative()&&(i=r.minus(i).abs(),s=s.next()),u.push(i.toJSNumber())}return u.push(s.toJSNumber()),{value:u.reverse(),isNegative:n}}function F(t,r,e){var n=B(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function H(t){if(l(+t)){var r=+t;if(r===h(r))return a?new c(BigInt(r)):new i(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==h(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var u=n[0],f=u.indexOf(".");if(f>=0&&(o-=u.length-f-1,u=u.slice(0,f)+u.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new s(p,e)}function $(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==h(t))throw new Error(t+" is not an integer.");return new i(t)}return H(t.toString())}(t):"string"==typeof t?H(t):"bigint"==typeof t?new c(t):t}s.prototype.toArray=function(t){return B(this,t)},i.prototype.toArray=function(t){return B(this,t)},c.prototype.toArray=function(t){return B(this,t)},s.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return F(this,t,r);for(var e,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)e=String(n[o]),a+="0000000".slice(e.length)+e;return(this.sign?"-":"")+a},i.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?F(this,t,r):String(this.value)},c.prototype.toString=i.prototype.toString,c.prototype.toJSON=s.prototype.toJSON=i.prototype.toJSON=function(){return this.toString()},s.prototype.valueOf=function(){return parseInt(this.toString(),10)},s.prototype.toJSNumber=s.prototype.valueOf,i.prototype.valueOf=function(){return this.value},i.prototype.toJSNumber=i.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var W=0;W<1e3;W++)u[W]=$(W),W>0&&(u[-W]=$(-W));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=M,u.min=q,u.gcd=P,u.lcm=function(t,r){return t=$(t).abs(),r=$(r).abs(),t.divide(P(t,r)).multiply(r)},u.isInstance=function(t){return t instanceof s||t instanceof i||t instanceof c},u.randBetween=function(t,e,n){t=$(t),e=$(e);var o=n||Math.random,a=q(t,e),s=M(t,e).subtract(a).add(1);if(s.isSmall)return a.add(Math.floor(o()*s));for(var i=B(s,r).value,c=[],l=!0,f=0;f<i.length;f++){var p=l?i[f]+(f+1<i.length?i[f+1]/r:0):r,b=h(o()*p);c.push(b),b<i[f]&&(l=!1)}return a.add(u.fromArray(c,r,!1))},u.fromArray=function(t,r,e){return Q(t.map($),$(r||10),e)},u}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(30)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=transactsql.umd.js.map