!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var r=e();for(var n in r)("object"==typeof exports?exports:t)[n]=r[n]}}(this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=26)}([function(t,e,r){"use strict";r.r(e),r.d(e,"arrayStructTypeToSQL",(function(){return S})),r.d(e,"autoIncrementToSQL",(function(){return _})),r.d(e,"columnOrderListToSQL",(function(){return x})),r.d(e,"commonKeywordArgsToSQL",(function(){return U})),r.d(e,"commonOptionConnector",(function(){return s})),r.d(e,"connector",(function(){return c})),r.d(e,"commonTypeValue",(function(){return w})),r.d(e,"commentToSQL",(function(){return T})),r.d(e,"createBinaryExpr",(function(){return f})),r.d(e,"createValueExpr",(function(){return l})),r.d(e,"dataTypeToSQL",(function(){return C})),r.d(e,"DEFAULT_OPT",(function(){return i})),r.d(e,"escape",(function(){return p})),r.d(e,"literalToSQL",(function(){return O})),r.d(e,"columnIdentifierToSql",(function(){return d})),r.d(e,"getParserOpt",(function(){return b})),r.d(e,"identifierToSql",(function(){return y})),r.d(e,"onPartitionsToSQL",(function(){return g})),r.d(e,"replaceParams",(function(){return L})),r.d(e,"returningToSQL",(function(){return A})),r.d(e,"hasVal",(function(){return m})),r.d(e,"setParserOpt",(function(){return v})),r.d(e,"toUpper",(function(){return j})),r.d(e,"topToSQL",(function(){return h})),r.d(e,"triggerEventToSQL",(function(){return E}));var n=r(2),o=r(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var i={database:"db2",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=i;function s(t,e,r){if(r)return t?"".concat(t.toUpperCase()," ").concat(e(r)):e(r)}function c(t,e){if(e)return"".concat(t.toUpperCase()," ").concat(e)}function l(t){var e=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(e){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(e,'" to SQL'))}}function f(t,e,r){var n={operator:t,type:"binary_expr"};return n.left=e.type?e:l(e),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(r[0]),l(r[1])]},n):(n.right=r.type?r:l(r),n)}function p(t){return t}function b(){return u}function v(t){u=t}function h(t){if(t){var e=t.value,r=t.percent,n=t.parentheses?"(".concat(e,")"):e,o="TOP ".concat(n);return r?"".concat(o," ").concat(r.toUpperCase()):o}}function d(t){var e=b().database;if(t)switch(e&&e.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function y(t,e){var r=b().database;if(!0===e)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(r&&r.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function j(t){if(t)return t.toUpperCase()}function m(t){return t}function O(t){if(t){var e=t.prefix,r=t.type,n=t.parentheses,i=t.suffix,u=t.value,s="object"===a(t)?u:t;switch(r){case"backticks_quote_string":s="`".concat(u,"`");break;case"string":s="'".concat(u,"'");break;case"regex_string":s='r"'.concat(u,'"');break;case"hex_string":s="X'".concat(u,"'");break;case"full_hex_string":s="0x".concat(u);break;case"natural_string":s="N'".concat(u,"'");break;case"bit_string":s="b'".concat(u,"'");break;case"double_quote_string":s='"'.concat(u,'"');break;case"single_quote_string":s="'".concat(u,"'");break;case"boolean":case"bool":s=u?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(e||":").concat(u),e=null;break;case"origin":s=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(r.toUpperCase()," '").concat(u,"'");break;case"var_string":s="N'".concat(u,"'");break;case"unicode_string":s="U&'".concat(u,"'")}var c=[];return e&&c.push(j(e)),c.push(s),i&&("string"==typeof i&&c.push(i),"object"===a(i)&&(i.collate?c.push(Object(o.a)(i.collate)):c.push(O(i)))),s=c.join(" "),n?"(".concat(s,")"):s}}function w(t){if(!t)return[];var e=t.type,r=t.symbol,n=t.value;return[e.toUpperCase(),r,"string"==typeof n?n.toUpperCase():O(n)].filter(m)}function L(t,e){return function t(e,r){return Object.keys(e).filter((function(t){var r=e[t];return Array.isArray(r)||"object"===a(r)&&null!==r})).forEach((function(n){var o=e[n];if("object"!==a(o)||"param"!==o.type)return t(o,r);if(void 0===r[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return e[n]=l(r[o.value]),null})),e}(JSON.parse(JSON.stringify(t)),e)}function g(t){var e=t.type,r=t.partitions;return[j(e),"(".concat(r.map((function(t){if("range"!==t.type)return O(t);var e=t.start,r=t.end,n=t.symbol;return"".concat(O(e)," ").concat(j(n)," ").concat(O(r))})).join(", "),")")].join(" ")}function C(t){var e=t.dataType,r=t.length,n=t.parentheses,o=t.scale,a=t.suffix,i="";return null!=r&&(i=o?"".concat(r,", ").concat(o):r),n&&(i="(".concat(i,")")),a&&a.length&&(i+=" ".concat(a.join(" "))),"".concat(e).concat(i)}function S(t){if(t){var e=t.dataType,r=t.definition,n=t.anglebracket,o=j(e);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=r&&r.map((function(t){return[t.field_name,S(t.field_type)].filter(m).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function T(t){if(t){var e=[],r=t.keyword,n=t.symbol,o=t.value;return e.push(r.toUpperCase()),n&&e.push(n),e.push(O(o)),e.join(" ")}}function E(t){return t.map((function(t){var e=t.keyword,r=t.args,o=[j(e)];if(r){var a=r.keyword,i=r.columns;o.push(j(a),i.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function A(t){return t?["RETURNING",t.columns.map(n.h).filter(m).join(", ")].join(" "):""}function U(t){return t?[j(t.keyword),j(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var e=b().database;switch(e&&e.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var r=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,i=j(r);return a&&(i+="(".concat(O(n),", ").concat(O(o),")")),i}}function x(t){if(t)return t.map(n.e).filter(m).join(", ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return g})),r.d(e,"b",(function(){return C})),r.d(e,"d",(function(){return L})),r.d(e,"c",(function(){return S}));var n=r(0),o=r(9),a=r(13);var i=r(22),u=r(21);var s=r(11),c=r(2),l=r(6),f=r(18);var p=r(7),b=r(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t){var e=t.expr_list,r=t.type;switch(Object(n.toUpper)(r)){case"STRUCT":return"(".concat(Object(c.i)(e),")");case"ARRAY":return function(t){var e=t.array_path,r=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(e),"]");var i=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):g(o);return r?"[".concat(i,"]"):a?"(".concat(i,")"):i}(t);default:return""}}function d(t){var e=t.definition,r=t.keyword,o=[Object(n.toUpper)(r)];return e&&"object"===v(e)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(e))),o.push(h(t)),o.filter(n.hasVal).join("")}var y=r(3),j=r(5),m=r(20);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={alter:o.b,aggr_func:function(t){var e=t.args,r=t.filter,o=t.over,i=t.within_group_orderby,u=g(e.expr);u=Array.isArray(u)?u.join(", "):u;var s=t.name,c=Object(a.a)(o);e.distinct&&(u=["DISTINCT",u].join(" ")),e.separator&&e.separator.delimiter&&(u=[u,Object(n.literalToSQL)(e.separator.delimiter)].join("".concat(e.separator.symbol," "))),e.separator&&e.separator.expr&&(u=[u,g(e.separator.expr)].join(" ")),e.orderby&&(u=[u,S(e.orderby,"order by")].join(" ")),e.separator&&e.separator.value&&(u=[u,Object(n.toUpper)(e.separator.keyword),Object(n.literalToSQL)(e.separator.value)].filter(n.hasVal).join(" "));var l=i?"WITHIN GROUP (".concat(S(i,"order by"),")"):"",f=r?"FILTER (WHERE ".concat(g(r.where),")"):"";return["".concat(s,"(").concat(u,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:m.c,array:d,assign:i.a,binary_expr:u.a,case:function(t){var e=["CASE"],r=t.args,n=t.expr,o=t.parentheses;n&&e.push(g(n));for(var a=0,i=r.length;a<i;++a)e.push(r[a].type.toUpperCase()),r[a].cond&&(e.push(g(r[a].cond)),e.push("THEN")),e.push(g(r[a].result));return e.push("END"),o?"(".concat(e.join(" "),")"):e.join(" ")},cast:l.c,collate:s.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:j.b,interval:f.a,json:function(t){var e=t.keyword,r=t.expr_list;return[Object(n.toUpper)(e),r.map((function(t){return g(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,g(t.expr)].join("")},func_arg:l.f,show:b.a,struct:d,tablefunc:l.j,tables:y.c,unnest:y.d,window:m.b};function L(t){var e=t.prefix,r=void 0===e?"@":e,o=t.name,a=t.members,i=t.quoted,u=t.suffix,s=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(r||"").concat(c);return u&&(l+=u),s.push(l),[i,s.join(" "),i].filter(n.hasVal).join("")}function g(t){if(t){var e=t;if(t.ast){var r=e.ast;Reflect.deleteProperty(e,r);for(var o=0,a=Object.keys(r);o<a.length;o++){var i=a[o];e[i]=r[i]}}var u=e.type;return"expr"===u?g(e.expr):w[u]?w[u](e):Object(n.literalToSQL)(e)}}function C(t){return t?(Array.isArray(t)||(t=[t]),t.map(g)):[]}function S(t,e){if(!Array.isArray(t))return"";var r=[],o=Object(n.toUpper)(e);switch(o){case"ORDER BY":r=t.map((function(t){return[g(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:r=t.map((function(t){return g(t.expr)}))}return Object(n.connector)(o,r.join(", "))}w.var=L,w.expr_list=function(t){var e=C(t.value),r=t.parentheses,n=t.separator;if(!r&&!n)return e;var o=n||", ",a=e.join(o);return r?"(".concat(a,")"):a},w.select=function(t){var e="object"===O(t._next)?Object(j.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(e,")"):e},w.unary_expr=function(t){var e=t.operator,r=t.parentheses,n=t.expr,o="-"===e||"+"===e||"~"===e||"!"===e?"":" ",a="".concat(e).concat(o).concat(g(n));return r?"(".concat(a,")"):a},w.map_object=function(t){var e=t.keyword,r=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(e),"[".concat(r,"]")].join("")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"c",(function(){return O})),r.d(e,"f",(function(){return h})),r.d(e,"h",(function(){return g})),r.d(e,"i",(function(){return S})),r.d(e,"b",(function(){return d})),r.d(e,"d",(function(){return b})),r.d(e,"e",(function(){return m})),r.d(e,"g",(function(){return y})),r.d(e,"j",(function(){return L})),r.d(e,"k",(function(){return C}));var n=r(11),o=r(19),a=r(1),i=r(6),u=r(3),s=r(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){if("string"==typeof t)return Object(s.identifierToSql)(t,e);var r=t.expr,n=t.offset,o=t.suffix,i=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(s.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(s.hasVal).join("")})).join("");return[Object(a.a)(r),i,o].filter(s.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var e,r=[],n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=f(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){u=!0,a=t},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw a}}}}(t);try{for(n.s();!(e=n.n()).done;){var o=e.value,a=o.brackets?"[".concat(Object(s.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(s.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(s.literalToSQL)(o.property))),r.push(a)}}catch(t){n.e(t)}finally{n.f()}return r.join("")}function h(t){var e=t.array_index,r=t.as,o=t.column,i=t.collate,u=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,h=t.options,d=t.schema,y=t.table,j=t.parentheses,m=t.suffix,O=t.order_by,w=t.subFields,L=void 0===w?[]:w,g="*"===o?"*":b(o,c),C=[u,d,y].filter(s.hasVal).map((function(t){return"".concat("string"==typeof t?Object(s.identifierToSql)(t):Object(a.a)(t))})),S=C[0];if(S){for(var T=1;T<C.length;++T)S="".concat(S).concat(p[T]||".").concat(C[T]);g="".concat(S).concat(p[T]||".").concat(g)}var E=[g=["".concat(g).concat(v(e))].concat(l(L)).join("."),Object(n.a)(i),Object(a.a)(h),Object(s.commonOptionConnector)("AS",a.a,r)];E.push("string"==typeof m?Object(s.toUpper)(m):Object(a.a)(m)),E.push(Object(s.toUpper)(O));var A=E.filter(s.hasVal).join(" ");return j?"(".concat(A,")"):A}function d(t){if(t){var e=t.dataType,r=t.length,n=t.suffix,o=t.scale,u=t.expr,c=null!=r,l=Object(s.dataTypeToSQL)({dataType:e,length:r,suffix:n,scale:o,parentheses:c});if(u&&(l+=Object(a.a)(u)),t.array){var f=Object(i.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function y(t){var e=[];if(!t)return e;var r=t.definition,n=t.keyword,o=t.match,i=t.table,c=t.on_action;return e.push(Object(s.toUpper)(n)),e.push(Object(u.c)(i)),e.push(r&&"(".concat(r.map((function(t){return Object(a.a)(t)})).join(", "),")")),e.push(Object(s.toUpper)(o)),c.map((function(t){return e.push(Object(s.toUpper)(t.type),Object(a.a)(t.value))})),e.filter(s.hasVal)}function j(t){var e=[],r=t.nullable,n=t.character_set,i=t.check,u=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,h=t.generated,d=t.auto_increment,j=t.unique,m=t.primary_key,O=t.column_format,w=t.reference_definition,L=[Object(s.toUpper)(r&&r.action),Object(s.toUpper)(r&&r.value)].filter(s.hasVal).join(" ");if(h||e.push(L),v){var g=v.type,C=v.value;e.push(g.toUpperCase(),Object(a.a)(C))}var S=Object(s.getParserOpt)().database;return c&&e.push(Object(s.toUpper)(c.keyword),Object(s.literalToSQL)(c.constraint)),e.push(Object(o.a)(i)),e.push(function(t){if(t)return[Object(s.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(s.toUpper)(t.storage_type)].filter(s.hasVal).join(" ")}(h)),h&&e.push(L),e.push(Object(s.autoIncrementToSQL)(d),Object(s.toUpper)(m),Object(s.toUpper)(j),Object(s.commentToSQL)(u)),e.push.apply(e,l(Object(s.commonTypeValue)(n))),"sqlite"!==S.toLowerCase()&&e.push(Object(a.a)(f)),e.push.apply(e,l(Object(s.commonTypeValue)(O))),e.push.apply(e,l(Object(s.commonTypeValue)(p))),e.push.apply(e,l(y(w))),e.push(Object(s.commonOptionConnector)("USING",a.a,b)),e.filter(s.hasVal).join(" ")}function m(t){var e=t.column,r=t.collate,n=t.nulls,o=t.opclass,i=t.order_by,u="string"==typeof e?{type:"column_ref",table:t.table,column:e}:t;return u.collate=null,[Object(a.a)(u),Object(a.a)(r),o,Object(s.toUpper)(i),Object(s.toUpper)(n)].filter(s.hasVal).join(" ")}function O(t){var e=[],r=h(t.column),n=d(t.definition);return e.push(r),e.push(n),e.push(j(t)),e.filter(s.hasVal).join(" ")}function w(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(s.identifierToSql)(t):Object(s.columnIdentifierToSql)(t)].join(" "):""}function L(t){var e=t.against,r=t.as,n=t.columns,o=t.match,i=t.mode;return[[Object(s.toUpper)(o),"(".concat(n.map((function(t){return h(t)})).join(", "),")")].join(" "),[Object(s.toUpper)(e),["(",Object(a.a)(t.expr),i&&" ".concat(Object(s.literalToSQL)(i)),")"].filter(s.hasVal).join("")].join(" "),w(r)].filter(s.hasVal).join(" ")}function g(t,e){var r=t.expr,n=t.type;if("cast"===n)return Object(i.c)(t);e&&(r.isDual=e);var o=Object(a.a)(r),u=t.expr_list;if(u){var c=[o],l=u.map((function(t){return g(t,e)})).join(", ");return c.push([Object(s.toUpper)(n),n&&"(",l,n&&")"].filter(s.hasVal).join("")),c.filter(s.hasVal).join(" ")}return r.parentheses&&Reflect.has(r,"array_index")&&"cast"!==r.type&&(o="(".concat(o,")")),r.array_index&&"column_ref"!==r.type&&(o="".concat(o).concat(v(r.array_index))),[o,w(t.as)].filter(s.hasVal).join(" ")}function C(t){var e=Array.isArray(t)&&t[0];return!(!e||"dual"!==e.type)}function S(t,e){if(!t||"*"===t)return t;var r=C(e);return t.map((function(t){return g(t,r)})).join(", ")}},function(t,e,r){"use strict";r.d(e,"c",(function(){return y})),r.d(e,"a",(function(){return j})),r.d(e,"b",(function(){return d})),r.d(e,"d",(function(){return f}));var n=r(21),o=r(2),a=r(1),i=r(17),u=r(18),s=r(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){var e=t.type,r=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(s.toUpper)(e),"(").concat(n&&Object(a.a)(n)||"",")"),Object(s.commonOptionConnector)("AS","string"==typeof r?s.identifierToSql:a.a,r),Object(s.commonOptionConnector)(Object(s.toUpper)(o&&o.keyword),s.identifierToSql,o&&o.as)].filter(s.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var e=t.as,r=t.column,i=t.expr,u=t.in_expr,c=t.type,l=[Object(a.a)(i),"FOR",Object(o.f)(r),Object(n.a)(u)],f=["".concat(Object(s.toUpper)(c),"(").concat(l.join(" "),")")];return e&&f.push("AS",Object(s.identifierToSql)(e)),f.join(" ")}(t);default:return""}}function b(t){if(t){var e=t.keyword,r=t.expr,n=t.index,o=t.index_columns,i=t.parentheses,u=t.prefix,c=[];switch(e.toLowerCase()){case"forceseek":c.push(Object(s.toUpper)(e),"(".concat(Object(s.identifierToSql)(n)),"(".concat(o.map(a.a).filter(s.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(s.toUpper)(e),"=",Object(a.a)(r));break;case"index":c.push(Object(s.toUpper)(u),Object(s.toUpper)(e),i?"(".concat(r.map(s.identifierToSql).join(", "),")"):"= ".concat(Object(s.identifierToSql)(r)));break;default:c.push(Object(a.a)(r))}return c.filter(s.hasVal).join(" ")}}function v(t,e){var r=t.name,n=t.symbol;return[Object(s.toUpper)(r),n,e].filter(s.hasVal).join(" ")}function h(t){var e=[];switch(t.keyword){case"as":e.push("AS","OF",Object(a.a)(t.of));break;case"from_to":e.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":e.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":e.push("CONTAINED","IN",Object(a.a)(t.in))}return e.filter(s.hasVal).join(" ")}function d(t){if("UNNEST"===Object(s.toUpper)(t.type))return f(t);var e,r,n,c,l=t.table,d=t.db,y=t.as,j=t.expr,m=t.operator,O=t.prefix,w=t.schema,L=t.server,g=t.suffix,C=t.tablesample,S=t.temporal_table,T=t.table_hint,E=Object(s.identifierToSql)(L),A=Object(s.identifierToSql)(d),U=Object(s.identifierToSql)(w),_=l&&Object(s.identifierToSql)(l);if(j)switch(j.type){case"values":var x=j.parentheses,k=j.values,I=j.prefix,N=[x&&"(","",x&&")"],R=Object(i.b)(k);I&&(R=R.split("(").slice(1).map((function(t){return"".concat(Object(s.toUpper)(I),"(").concat(t)})).join("")),N[1]="VALUES ".concat(R),_=N.filter(s.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var e=t.data,r=t.timecol,n=t.offset,a=t.size,i=[Object(s.identifierToSql)(e.expr.db),Object(s.identifierToSql)(e.expr.schema),Object(s.identifierToSql)(e.expr.table)].filter(s.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(r.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(e,i)),v(r,c)],f=v(a,Object(u.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(u.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(s.hasVal).join(", ")}(j);break;case"generator":r=(e=j).keyword,n=e.type,c=e.generators.map((function(t){return Object(s.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(s.toUpper)(r),"(").concat(Object(s.toUpper)(n),"(").concat(c,"))");break;default:_=Object(a.a)(j)}var V=[[E,A,U,_=[Object(s.toUpper)(O),_,Object(s.toUpper)(g)].filter(s.hasVal).join(" ")].filter(s.hasVal).join(".")];if(C){var q=["TABLESAMPLE",Object(a.a)(C.expr),Object(s.literalToSQL)(C.repeatable)].filter(s.hasVal).join(" ");V.push(q)}V.push(function(t){if(t){var e=t.keyword,r=t.expr;return[Object(s.toUpper)(e),h(r)].filter(s.hasVal).join(" ")}}(S),Object(s.commonOptionConnector)("AS","string"==typeof y?s.identifierToSql:a.a,y),p(m)),T&&V.push(Object(s.toUpper)(T.keyword),"(".concat(T.expr.map(b).filter(s.hasVal).join(", "),")"));var M=V.filter(s.hasVal).join(" ");return t.parentheses?"(".concat(M,")"):M}function y(t){if(!t)return"";if(!Array.isArray(t)){var e=t.expr,r=t.parentheses,n=t.joins,o=y(e);if(r){for(var i=[],u=[],l=!0===r?1:r.length,f=0;f++<l;)i.push("("),u.push(")");var p=n&&n.length>0?y([""].concat(c(n))):"";return i.join("")+o+u.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(d(b));for(var h=1;h<t.length;++h){var j=t[h],m=j.on,O=j.using,w=j.join,L=[];L.push(w?" ".concat(Object(s.toUpper)(w)):","),L.push(d(j)),L.push(Object(s.commonOptionConnector)("ON",a.a,m)),O&&L.push("USING (".concat(O.map(s.literalToSQL).join(", "),")")),v.push(L.filter(s.hasVal).join(" "))}return v.filter(s.hasVal).join("")}function j(t){var e=t.keyword,r=t.symbol,n=t.value,o=[e.toUpperCase()];r&&o.push(r);var i=Object(s.literalToSQL)(n);switch(e){case"partition by":case"default collate":i=Object(a.a)(n);break;case"options":i="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":i=n.map(a.a).join(", ")}return o.push(i),o.filter(s.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return h})),r.d(e,"c",(function(){return C})),r.d(e,"d",(function(){return S})),r.d(e,"e",(function(){return d})),r.d(e,"f",(function(){return y})),r.d(e,"g",(function(){return j})),r.d(e,"h",(function(){return A})),r.d(e,"i",(function(){return E})),r.d(e,"j",(function(){return T})),r.d(e,"l",(function(){return m})),r.d(e,"m",(function(){return O})),r.d(e,"o",(function(){return w})),r.d(e,"n",(function(){return L})),r.d(e,"k",(function(){return g}));var n=r(2),o=r(14),a=r(0),i=r(1),u=r(3),s=r(16),c=r(5);function l(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=p(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){u=!0,a=t},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t){var e=Object(i.a)(t.expr);return"".concat("CALL"," ").concat(e)}function h(t){var e=t.type,r=t.keyword,o=t.name,s=t.prefix,c=t.suffix,l=[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(a.toUpper)(s)];switch(r){case"table":l.push(Object(u.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(u.c)(o),t.options&&t.options.map(i.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(u.b)(t.table)]:[]),[t.options&&t.options.map(i.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(i.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(i.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function d(t){var e=t.type,r=t.table,n=Object(a.toUpper)(e);return"".concat(n," ").concat(Object(a.identifierToSql)(r))}function y(t){var e=t.type,r=t.name,n=t.args,o=[Object(a.toUpper)(e)],u=[r];return n&&u.push("(".concat(Object(i.a)(n).join(", "),")")),o.push(u.join("")),o.filter(a.hasVal).join(" ")}function j(t){var e=t.type,r=t.label,n=t.target,o=t.query,i=t.stmts;return[r,Object(a.toUpper)(e),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(i),"END LOOP",r].filter(a.hasVal).join(" ")}function m(t){var e=t.type,r=t.level,n=t.raise,o=t.using,u=[Object(a.toUpper)(e),Object(a.toUpper)(r)];return n&&u.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(i.a)(t)})).join(", ")),o&&u.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(i.a)(t)})).join(", ")),u.filter(a.hasVal).join(" ")}function O(t){var e=t.type,r=t.table,n=[],o="".concat(e&&e.toUpperCase()," TABLE");if(r){var a,i=l(r);try{for(i.s();!(a=i.n()).done;){var s=a.value.map(u.b);n.push(s.join(" TO "))}}catch(t){i.e(t)}finally{i.f()}}return"".concat(o," ").concat(n.join(", "))}function w(t){var e=t.type,r=t.db,n=Object(a.toUpper)(e),o=Object(a.identifierToSql)(r);return"".concat(n," ").concat(o)}function L(t){var e=t.type,r=t.expr,n=t.keyword,o=Object(a.toUpper)(e),u=r.map(i.a).join(", ");return[o,Object(a.toUpper)(n),u].filter(a.hasVal).join(" ")}function g(t){var e=t.type,r=t.keyword,n=t.tables,o=[e.toUpperCase(),Object(a.toUpper)(r)];if("UNLOCK"===e.toUpperCase())return o.join(" ");var i,s=[],c=l(n);try{var p=function(){var t=i.value,e=t.table,r=t.lock_type,n=[Object(u.b)(e)];if(r){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(r[t])})).filter(a.hasVal).join(" "))}s.push(n.join(" "))};for(c.s();!(i=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[s.join(", ")].concat(f(function(t){var e=t.lock_mode,r=t.nowait,n=[];if(e){var o=e.mode;n.push(o.toUpperCase())}return r&&n.push(r.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function C(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(i.a)(n)].filter(a.hasVal).join(" ")}function S(t){var e=t.type,r=t.declare,u=t.symbol,s=[Object(a.toUpper)(e)],c=r.map((function(t){var e=t.at,r=t.name,u=t.as,s=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[e,r].filter(a.hasVal).join(""),Object(a.toUpper)(u),Object(a.toUpper)(s)];switch(b){case"variable":v.push(Object(n.b)(c),Object(i.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(i.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(u," "));return s.push(c),s.join(" ")}function T(t){var e=t.boolean_expr,r=t.else_expr,n=t.elseif_expr,o=t.if_expr,u=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(i.a)(e),Object(a.literalToSQL)(u),"".concat(Object(s.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(i.a)(t.boolean_expr),"THEN",Object(s.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),r&&b.push("ELSE","".concat(Object(s.a)(r.ast||r)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function E(t){var e=t.name,r=t.host,n=[Object(a.literalToSQL)(e)];return r&&n.push("@",Object(a.literalToSQL)(r)),n.join("")}function A(t){var e=t.type,r=t.grant_option_for,o=t.keyword,u=t.objects,s=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(e),Object(a.literalToSQL)(r)],b=u.map((function(t){var e=t.priv,r=t.columns,o=[Object(i.a)(e)];return r&&o.push("(".concat(r.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),s)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(s.object_type),s.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(E(s))}return p.push(Object(a.toUpper)(c),l.map(E).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"b",(function(){return w})),r.d(e,"a",(function(){return L}));var n=r(9),o=r(1),a=r(3),i=r(0);var u=r(14),s=r(2);function c(t){var e=t.name,r=t.type;switch(r){case"table":case"view":var n=[Object(i.identifierToSql)(e.db),Object(i.identifierToSql)(e.table)].filter(i.hasVal).join(".");return"".concat(Object(i.toUpper)(r)," ").concat(n);case"column":return"COLUMN ".concat(Object(s.f)(e));default:return"".concat(Object(i.toUpper)(r)," ").concat(Object(i.literalToSQL)(e))}}function l(t){var e=t.keyword,r=t.expr;return[Object(i.toUpper)(e),Object(i.literalToSQL)(r)].filter(i.hasVal).join(" ")}var f=r(7);var p=r(8),b=r(15);var v=r(12),h=r(17),d=r(4);function y(t){var e=t.name,r=t.value;return["@".concat(e),"=",Object(o.a)(r)].filter(i.hasVal).join(" ")}var j=r(22);var m=r(23),O={alter:n.c,analyze:function(t){var e=t.type,r=t.table;return[Object(i.toUpper)(e),Object(a.b)(r)].join(" ")},attach:function(t){var e=t.type,r=t.database,n=t.expr,a=t.as,u=t.schema;return[Object(i.toUpper)(e),Object(i.toUpper)(r),Object(o.a)(n),Object(i.toUpper)(a),Object(i.identifierToSql)(u)].filter(i.hasVal).join(" ")},create:u.b,comment:function(t){var e=t.expr,r=t.keyword,n=t.target,o=t.type;return[Object(i.toUpper)(o),Object(i.toUpper)(r),c(n),l(e)].filter(i.hasVal).join(" ")},select:f.a,deallocate:d.c,delete:function(t){var e=t.columns,r=t.from,n=t.table,u=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,h=[Object(b.a)(l),"DELETE"],d=Object(s.i)(e,r);return h.push(d),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||h.push(Object(a.c)(n))),h.push(Object(i.commonOptionConnector)("FROM",a.c,r)),h.push(Object(i.commonOptionConnector)("WHERE",o.a,u)),h.push(Object(o.c)(c,"order by")),h.push(Object(p.a)(f)),h.push(Object(i.returningToSQL)(v)),h.filter(i.hasVal).join(" ")},exec:function(t){var e=t.keyword,r=t.module,n=t.parameters;return[Object(i.toUpper)(e),Object(a.b)(r),(n||[]).map(y).filter(i.hasVal).join(", ")].filter(i.hasVal).join(" ")},execute:d.f,explain:function(t){var e=t.type,r=t.expr;return[Object(i.toUpper)(e),Object(f.a)(r)].join(" ")},for:d.g,update:v.b,if:d.j,insert:h.a,drop:d.b,truncate:d.b,replace:h.a,declare:d.d,use:d.o,rename:d.m,call:d.a,desc:d.e,set:d.n,lock:d.k,unlock:d.k,show:m.a,grant:d.h,revoke:d.h,proc:function(t){var e=t.stmt;switch(e.type){case"assign":return Object(j.a)(e);case"return":return function(t){var e=t.type,r=t.expr;return[Object(i.toUpper)(e),Object(o.a)(r)].join(" ")}(e)}},raise:d.l,transaction:function(t){var e=t.expr,r=e.action,n=e.keyword,o=e.modes,a=[Object(i.literalToSQL)(r),Object(i.toUpper)(n)];return o&&a.push(o.map(i.literalToSQL).join(", ")),a.filter(i.hasVal).join(" ")}};function w(t){if(!t)return"";for(var e=O[t.type],r=t,n=r._parentheses,a=r._orderby,u=r._limit,s=[n&&"(",e(t)];t._next;){var c=O[t._next.type],l=Object(i.toUpper)(t.set_op);s.push(l,c(t._next)),t=t._next}return s.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(u)),s.filter(i.hasVal).join(" ")}function L(t){for(var e=[],r=0,n=t.length;r<n;++r){var o=t[r]&&t[r].ast?t[r].ast:t[r],a=w(o);r===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),e.push(a)}return e.join(" ; ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u})),r.d(e,"b",(function(){return s})),r.d(e,"c",(function(){return c})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return p})),r.d(e,"f",(function(){return b})),r.d(e,"g",(function(){return v})),r.d(e,"h",(function(){return f})),r.d(e,"i",(function(){return d})),r.d(e,"j",(function(){return h}));var n=r(2),o=r(1),a=r(0),i=r(13);function u(t){var e=t.args,r=t.type,n=t.over,u=e.expr,s=e.having,c="".concat(Object(a.toUpper)(r),"(").concat(Object(o.a)(u));return s&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(s.prefix)," ").concat(Object(o.a)(s.expr))),[c="".concat(c,")"),Object(i.a)(n)].filter(a.hasVal).join(" ")}function s(t){if(!t||!t.array)return"";var e=t.array.keyword;if(e)return Object(a.toUpper)(e);for(var r=t.array,n=r.dimension,o=r.length,i=[],u=0;u<n;u++)i.push("["),o&&o[u]&&i.push(Object(a.literalToSQL)(o[u])),i.push("]");return i.join("")}function c(t){for(var e=t.target,r=t.expr,i=t.keyword,u=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:r,offset:l}),b=[],v=0,h=e.length;v<h;++v){var d=e[v],y=d.angle_brackets,j=d.length,m=d.dataType,O=d.parentheses,w=d.quoted,L=d.scale,g=d.suffix,C=d.expr,S=C?Object(o.a)(C):"";null!=j&&(S=L?"".concat(j,", ").concat(L):j),O&&(S="(".concat(S,")")),y&&(S="<".concat(S,">")),g&&g.length&&(S+=" ".concat(g.map(a.literalToSQL).join(" ")));var T="::",E="",A=[];"as"===u&&(0===v&&(p="".concat(Object(a.toUpper)(i),"(").concat(p)),E=")",T=" ".concat(u.toUpperCase()," ")),0===v&&A.push(p);var U=s(d);A.push(T,w,m,w,U,S,E),b.push(A.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var _=b.filter(a.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var e=t.args,r=t.type,n=e.field,i=e.cast_type,u=e.source,s=["".concat(Object(a.toUpper)(r),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(i),Object(o.a)(u)];return"".concat(s.filter(a.hasVal).join(" "),")")}function f(t){var e=t.expr,r=e.key,n=e.value,i=e.on,u=[Object(o.a)(r),"VALUE",Object(o.a)(n)];return i&&u.push("ON","NULL",Object(o.a)(i)),u.filter(a.hasVal).join(" ")}function p(t){var e=t.args,r=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var e=t.type,r=t.symbol,n=t.value;return[Object(a.toUpper)(e),r,Object(o.a)(n)].filter(a.hasVal).join(" ")}(e[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(r),"(").concat(n,")")}function b(t){var e=t.value,r=e.name,n=e.symbol,i=e.expr;return[r,n,Object(o.a)(i)].filter(a.hasVal).join(" ")}function v(t){var e=t.args,r=t.array_index,u=t.name,s=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(i.a)(f),v=function(t){if(!t)return"";var e=t.type,r=t.keyword,n=t.orderby;return[Object(a.toUpper)(e),Object(a.toUpper)(r),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),h=Object(o.a)(p),d=[Object(a.literalToSQL)(u.schema),u.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!e)return[d,v,b].filter(a.hasVal).join(" ");var y=t.separator||", ";"TRIM"===Object(a.toUpper)(d)&&(y=" ");var j=[d];j.push(!1===s?" ":"(");var m=Object(o.a)(e);if(Array.isArray(y)){for(var O=m[0],w=1,L=m.length;w<L;++w)O=[O,m[w]].join(" ".concat(Object(o.a)(y[w-1])," "));j.push(O)}else j.push(m.join(y));return!1!==s&&j.push(")"),j.push(Object(n.a)(r)),j=[j.join(""),h].filter(a.hasVal).join(" "),[c?"(".concat(j,")"):j,v,b].filter(a.hasVal).join(" ")}function h(t){var e=t.as,r=t.name,n=t.args,i=[Object(a.literalToSQL)(r.schema),r.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(i,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(e)].join(" ")}function d(t){var e=t.args,r=t.expr,n=e.value,a=e.parentheses,i=n.map(o.a).join(", ");return[a?"(".concat(i,")"):i,"->",Object(o.a)(r)].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f}));var n=r(1),o=r(2),a=r(8),i=r(15),u=r(3),s=r(0),c=r(11);function l(t){if(t&&t.position){var e=t.keyword,r=t.expr,o=[],a=Object(s.toUpper)(e);switch(a){case"VAR":o.push(r.map(n.d).join(", "));break;default:o.push(a,"string"==typeof r?Object(s.identifierToSql)(r):Object(n.a)(r))}return o.filter(s.hasVal).join(" ")}}function f(t){var e=t.as_struct_val,r=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,h=t.for_sys_time_as_of,d=void 0===h?{}:h,y=t.locking_read,j=t.groupby,m=t.having,O=t.into,w=void 0===O?{}:O,L=t.isolation,g=t.limit,C=t.options,S=t.orderby,T=t.parentheses_symbol,E=t.qualify,A=t.top,U=t.window,_=t.with,x=t.where,k=[Object(i.a)(_),"SELECT",Object(s.toUpper)(e)];Array.isArray(C)&&k.push(C.join(" ")),k.push(function(t){if(t){if("string"==typeof t)return t;var e=t.type,r=t.columns,o=[Object(s.toUpper)(e)];return r&&o.push("(".concat(r.map(n.a).join(", "),")")),o.filter(s.hasVal).join(" ")}}(p),Object(s.topToSQL)(A),Object(o.i)(r,v));var I=w.position,N="";I&&(N=Object(s.commonOptionConnector)("INTO",l,w)),"column"===I&&k.push(N),k.push(Object(s.commonOptionConnector)("FROM",u.c,v)),"from"===I&&k.push(N);var R=d||{},V=R.keyword,q=R.expr;k.push(Object(s.commonOptionConnector)(V,n.a,q)),k.push(Object(s.commonOptionConnector)("WHERE",n.a,x)),j&&(k.push(Object(s.connector)("GROUP BY",Object(n.b)(j.columns).join(", "))),k.push(Object(n.b)(j.modifiers).join(", "))),k.push(Object(s.commonOptionConnector)("HAVING",n.a,m)),k.push(Object(s.commonOptionConnector)("QUALIFY",n.a,E)),k.push(Object(s.commonOptionConnector)("WINDOW",n.a,U)),k.push(Object(n.c)(S,"order by")),k.push(Object(c.a)(f)),k.push(Object(a.a)(g)),L&&k.push(Object(s.commonOptionConnector)(L.keyword,s.literalToSQL,L.expr)),k.push(Object(s.toUpper)(y)),"end"===I&&k.push(N),k.push(function(t){if(t){var e=t.expr,r=t.keyword,o=t.type,a=[Object(s.toUpper)(o),Object(s.toUpper)(r)];return e?"".concat(a.join(" "),"(").concat(Object(n.a)(e),")"):a.join(" ")}}(b));var M=k.filter(s.hasVal).join(" ");return T?"(".concat(M,")"):M}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return i(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function s(t){return t?t.fetch?(r=(e=t).fetch,i=e.offset,[].concat(a(u(i)),a(u(r))).filter(n.hasVal).join(" ")):function(t){var e=t.seperator,r=t.value;return 1===r.length&&"offset"===e?Object(n.connector)("OFFSET",Object(o.a)(r[0])):Object(n.connector)("LIMIT",r.map(o.a).join("".concat("offset"===e?" ":"").concat(Object(n.toUpper)(e)," ")))}(t):"";var e,r,i}},function(t,e,r){"use strict";r.d(e,"a",(function(){return p})),r.d(e,"c",(function(){return b})),r.d(e,"b",(function(){return f}));var n=r(2),o=r(14),a=r(10),i=r(3),u=r(1),s=r(7),c=r(0);function l(t,e){switch(t){case"add":var r=e.map((function(t){var e=t.name,r=t.value;return["PARTITION",Object(c.literalToSQL)(e),"VALUES",Object(c.toUpper)(r.type),"(".concat(Object(c.literalToSQL)(r.expr),")")].join(" ")})).join(", ");return"(".concat(r,")");default:return Object(n.i)(e)}}function f(t){if(!t)return"";var e=t.action,r=t.create_definitions,i=t.if_not_exists,u=t.keyword,s=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,h=t.suffix,d="",y=[];switch(b){case"column":y=[Object(n.c)(t)];break;case"index":y=Object(a.c)(t),d=t[b];break;case"table":case"schema":d=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":d=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":d=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":d=Object(c.identifierToSql)(t[b]),y=[Object(o.a)(r)];break;case"partition":y=[l(e,t.partitions)];break;case"key":d=Object(c.identifierToSql)(t[b]);break;default:d=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var j=[Object(c.toUpper)(e),Object(c.toUpper)(u),Object(c.toUpper)(i),Object(c.toUpper)(s),f&&Object(n.f)(f),Object(c.toUpper)(p),d&&d.trim(),y.filter(c.hasVal).join(" ")];return h&&j.push(Object(c.toUpper)(h.keyword),h.expr&&Object(n.f)(h.expr)),j.filter(c.hasVal).join(" ")}function p(t){var e=t.default&&[Object(c.toUpper)(t.default.keyword),Object(u.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),e].filter(c.hasVal).join(" ")}function b(t){var e=t.keyword;switch(void 0===e?"table":e){case"aggregate":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type,i=e.expr,u=e.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(i.map(p).join(", ")).concat(u?[" ORDER","BY",u.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var e=t.type,r=t.table,n=t.if_exists,o=t.prefix,a=t.expr,s=void 0===a?[]:a,l=Object(c.toUpper)(e),f=Object(i.c)(r),p=s.map(u.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var e=t.expr,r=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),Object(c.identifierToSql)(n),f(e)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var e=t.expr,r=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(e)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),e&&"(".concat(e.expr?e.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var e=t.type,r=t.columns,o=t.attributes,a=t.select,u=t.view,l=t.with,f=[Object(c.toUpper)(e),"VIEW",Object(i.b)(u)];return r&&f.push("(".concat(r.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(s.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f})),r.d(e,"d",(function(){return u})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return i(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t){if(!t)return[];var e=t.keyword,r=t.type;return[e.toUpperCase(),Object(n.toUpper)(r)]}function s(t){if(t){var e=t.type,r=t.expr,o=t.symbol,i=e.toUpperCase(),s=[];switch(s.push(i),i){case"KEY_BLOCK_SIZE":o&&s.push(o),s.push(Object(n.literalToSQL)(r));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,a(u(t)));break;case"WITH PARSER":s.push(r);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":s.push(o,Object(n.toUpper)(r.value),Object(n.onPartitionsToSQL)(r.on));break;default:s.push(o,Object(n.literalToSQL)(r))}return s.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(s):[]}function l(t){var e=t.constraint_type,r=t.index_type,i=t.index_options,s=void 0===i?[]:i,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(u(r))),l&&l.length){var v="CHECK"===Object(n.toUpper)(e)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(s).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var e=[],r=t.keyword,o=t.index;return e.push(Object(n.toUpper)(r)),e.push(o),e.push.apply(e,a(l(t))),e.filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){if(t){var e=t.keyword,r=t.collate,a=r.name,i=r.symbol,u=r.value,s=[Object(o.toUpper)(e)];return u||s.push(i),s.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),u&&s.push(i),s.push(Object(n.a)(u)),s.filter(o.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return p})),r.d(e,"a",(function(){return f}));var n=r(3),o=r(1),a=r(2),i=r(8),u=r(0),s=r(15);function c(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){u=!0,a=t},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw a}}}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){if(!t||0===t.length)return"";var e,r=[],n=c(t);try{for(n.s();!(e=n.n()).done;){var i=e.value,s={},l=i.value;for(var f in i)"value"!==f&&"keyword"!==f&&(s[f]=i[f]);var p=[Object(a.f)(s)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),r.push(p.filter(u.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return r.join(", ")}function p(t){var e=t.from,r=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(s.a)(p),"UPDATE",Object(n.c)(r),Object(u.commonOptionConnector)("SET",f,a),Object(u.commonOptionConnector)("FROM",n.c,e),Object(u.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(i.a)(b),Object(u.returningToSQL)(v)].filter(u.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(0),o=r(1),a=r(20);function i(t){if(t){var e=t.as_window_specification,r=t.expr,i=t.keyword,u=t.type,s=t.parentheses,c=Object(n.toUpper)(u);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(e));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(u)," ").concat(Object(n.toUpper)(i)),f=Object(o.a)(r)||[];return s&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return S})),r.d(e,"a",(function(){return y}));var n=r(9),o=r(1),a=r(10),i=r(2),u=r(4),s=r(19),c=r(6),l=r(3),f=r(12),p=r(5),b=r(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t){if(!t)return[];var e=t.resource;switch(e){case"column":return Object(i.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(s.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(e," type"))}}function j(t){var e=[];switch(t.keyword){case"from":e.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":e.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":e.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return e.filter(b.hasVal).join(" ")}function m(t){var e=t.keyword,r=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(e),Object(l.b)(r),Object(b.toUpper)(n.keyword),j(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function O(t){var e=t.as,r=t.domain,n=t.type,a=t.keyword,i=t.target,u=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(r.schema),Object(b.identifierToSql)(r.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.dataTypeToSQL)(i)];if(u&&u.length>0){var l,f=[],p=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=h(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){u=!0,a=t},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw a}}}}(u);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(s.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function w(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function L(t){var e=t.type;switch(e){case"as":return[Object(b.toUpper)(e),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(e),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(e),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function g(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,u=t.args,s=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=u.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(b.toUpper)(e),Object(b.toUpper)(r),Array.isArray(n)?"(".concat(n.map(i.c).join(", "),")"):w(n)].filter(b.hasVal).join(" ")}(s),c.map(L).join(" "),l),f.filter(b.hasVal).join(" ")}function C(t){var e=t.type,r=t.symbol,n=t.value,a=[Object(b.toUpper)(e),r];switch(Object(b.toUpper)(e)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function S(t){var e=t.keyword,r="";switch(e.toLowerCase()){case"aggregate":r=function(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,i=t.args,u=t.options,s=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(i.expr.map(n.a).join(", ")).concat(i.orderby?[" ORDER","BY",i.orderby.map(n.a).join(", ")].join(" "):"");return s.push("".concat(c,"(").concat(l,")"),"(".concat(u.map(C).join(", "),")")),s.filter(b.hasVal).join(" ")}(t);break;case"table":r=function(t){var e=t.type,r=t.keyword,n=t.table,o=t.like,a=t.as,i=t.temporary,u=t.if_not_exists,s=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,h=t.partition_of,d=t.query_expr,j=t.unlogged,O=t.with,w=[Object(b.toUpper)(e),Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(j),Object(b.toUpper)(r),Object(b.toUpper)(u),Object(l.c)(n)];if(o){var L=o.type,g=o.table,C=Object(l.c)(g);return w.push(Object(b.toUpper)(L),C),w.filter(b.hasVal).join(" ")}if(h)return w.concat([m(h)]).filter(b.hasVal).join(" ");if(s&&w.push("(".concat(s.map(y).join(", "),")")),c){var S=Object(b.getParserOpt)().database,T=S&&"sqlite"===S.toLowerCase()?", ":" ";w.push(c.map(l.a).join(T))}if(O){var E=O.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");w.push("WITH (".concat(E,")"))}return w.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),d&&w.push(Object(p.b)(d)),w.filter(b.hasVal).join(" ")}(t);break;case"trigger":r="constraint"===t.resource?function(t){var e=t.constraint,r=t.constraint_kw,n=t.deferrable,a=t.events,i=t.execute,u=t.for_each,s=t.from,f=t.location,p=t.keyword,h=t.or,d=t.type,y=t.table,j=t.when,m=[Object(b.toUpper)(d),Object(b.toUpper)(h),Object(b.toUpper)(r),Object(b.toUpper)(p),Object(b.identifierToSql)(e),Object(b.toUpper)(f)],O=Object(b.triggerEventToSQL)(a);return m.push(O,"ON",Object(l.b)(y)),s&&m.push("FROM",Object(l.b)(s)),m.push.apply(m,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(u)))),j&&m.push(Object(b.toUpper)(j.type),Object(o.a)(j.cond)),m.push(Object(b.toUpper)(i.keyword),Object(c.g)(i.expr)),m.filter(b.hasVal).join(" ")}(t):function(t){var e=t.definer,r=t.for_each,n=t.keyword,a=t.execute,u=t.type,s=t.table,c=t.if_not_exists,v=t.temporary,h=t.trigger,d=t.events,y=t.order,j=t.time,m=t.when,O=[Object(b.toUpper)(u),Object(b.toUpper)(v),Object(o.a)(e),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(h),Object(b.toUpper)(j),d.map((function(t){var e=[Object(b.toUpper)(t.keyword)],r=t.args;return r&&e.push(Object(b.toUpper)(r.keyword),r.columns.map(i.f).join(", ")),e.join(" ")})),"ON",Object(l.b)(s),Object(b.toUpper)(r&&r.keyword),Object(b.toUpper)(r&&r.args),y&&"".concat(Object(b.toUpper)(y.keyword)," ").concat(Object(b.identifierToSql)(y.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,m),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":O.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":O.push(Object(p.a)(a.expr.ast))}return O.push(Object(b.toUpper)(a.suffix)),O.filter(b.hasVal).join(" ")}(t);break;case"extension":r=function(t){var e=t.extension,r=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,i=t.type,u=t.with,s=t.version;return[Object(b.toUpper)(i),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(e),Object(b.toUpper)(u),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,s),Object(b.commonOptionConnector)("FROM",b.literalToSQL,r)].filter(b.hasVal).join(" ")}(t);break;case"function":r=g(t);break;case"index":r=function(t){var e=t.concurrently,r=t.filestream_on,i=t.keyword,u=t.if_not_exists,s=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,h=t.index,d=t.on,y=t.index_options,j=t.algorithm_option,m=t.lock_option,O=t.on_kw,w=t.table,L=t.tablespace,g=t.type,C=t.where,S=t.with,T=t.with_before_where,E=S&&"WITH (".concat(Object(a.b)(S).join(", "),")"),A=s&&"".concat(Object(b.toUpper)(s.keyword)," (").concat(s.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=h;h&&(U="string"==typeof h?Object(b.identifierToSql)(h):[Object(b.identifierToSql)(h.schema),Object(b.identifierToSql)(h.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(g),Object(b.toUpper)(f),Object(b.toUpper)(i),Object(b.toUpper)(u),Object(b.toUpper)(e),U,Object(b.toUpper)(O),Object(l.b)(w)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),A,Object(a.b)(y).join(" "),Object(n.b)(j),Object(n.b)(m),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,L)]);return T?_.push(E,Object(b.commonOptionConnector)("WHERE",o.a,C)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,C),E),_.push(Object(b.commonOptionConnector)("ON",o.a,d),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,r)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":r=function(t){var e=t.type,r=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,i=t.create_definitions,u=[Object(b.toUpper)(e),Object(b.toUpper)(o),Object(b.toUpper)(r),Object(b.toUpper)(a),Object(l.c)(n)];return i&&u.push(i.map(y).join(" ")),u.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":r=function(t){var e=t.type,r=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,i=t[r],u=i.db,s=i.schema,c=[Object(b.literalToSQL)(u),s.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(e),Object(b.toUpper)(n),Object(b.toUpper)(r),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":r=function(t){var e=t.algorithm,r=t.columns,n=t.definer,a=t.if_not_exists,i=t.keyword,u=t.recursive,s=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,h=t.view,d=t.with,y=t.with_options,j=h.db,m=h.schema,O=h.view,w=[Object(b.identifierToSql)(j),Object(b.identifierToSql)(m),Object(b.identifierToSql)(O)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(f),Object(b.toUpper)(u),e&&"ALGORITHM = ".concat(Object(b.toUpper)(e)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(i),Object(b.toUpper)(a),w,r&&"(".concat(r.map(b.columnIdentifierToSql).join(", "),")"),y&&["WITH","(".concat(y.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(d)].filter(b.hasVal).join(" ")}(t);break;case"domain":r=O(t);break;case"type":r=function(t){var e=t.as,r=t.create_definitions,n=t.keyword,a=t.name,i=t.resource,u=t.type,s=[Object(b.toUpper)(u),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.toUpper)(i)];if(r){var c=[];switch(i){case"enum":case"range":c.push(Object(o.a)(r));break;default:c.push("(".concat(r.map(y).join(", "),")"))}s.push(c.filter(b.hasVal).join(" "))}return s.filter(b.hasVal).join(" ")}(t);break;case"user":r=function(t){var e=t.attribute,r=t.comment,n=t.default_role,a=t.if_not_exists,i=t.keyword,s=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var e=t.user,r=t.auth_option,n=[Object(u.i)(e)];return r&&n.push(Object(b.toUpper)(r.keyword),r.auth_plugin,Object(b.literalToSQL)(r.value)),n.filter(b.hasVal).join(" ")})).join(", "),h=[Object(b.toUpper)(p),Object(b.toUpper)(i),Object(b.toUpper)(a),v];return n&&h.push(Object(b.toUpper)(n.keyword),n.value.map(u.i).join(", ")),h.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&h.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return h.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),h.push(Object(b.literalToSQL)(s),Object(b.commentToSQL)(r),Object(b.literalToSQL)(e)),h.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(e))}return r}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(2),o=r(1),a=r(0);function i(t){if(t&&0!==t.length){var e=t[0].recursive?"RECURSIVE ":"",r=t.map((function(t){var e=t.name,r=t.stmt,i=t.columns,u=Array.isArray(i)?"(".concat(i.map(n.f).join(", "),")"):"";return"".concat("default"===e.type?Object(a.identifierToSql)(e.value):Object(a.literalToSQL)(e)).concat(u," AS (").concat(Object(o.a)(r),")")})).join(", ");return"WITH ".concat(e).concat(r)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var e=t&&t.ast?t.ast:t;if(!o.includes(e.type))throw new Error("".concat(e.type," statements not supported at the moment"))}function i(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function u(t){return"go"===t.go?function t(e){if(!e||0===e.length)return"";var r=[i(e.ast)];return e.go_next&&r.push(e.go.toUpperCase(),t(e.go_next)),r.filter((function(t){return t})).join(" ")}(t):i(t)}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return c}));var n=r(3),o=r(1),a=r(2),i=r(0),u=r(7),s=r(12);function c(t){if("select"===t.type)return Object(u.a)(t);var e=t.map(o.a);return"(".concat(e.join("), ("),")")}function l(t){if(!t)return"";var e=["PARTITION","("];if(Array.isArray(t))e.push(t.map(i.identifierToSql).join(", "));else{var r=t.value;e.push(r.map(o.a).join(", "))}return e.push(")"),e.filter(i.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var e=t.expr,r=t.keyword,n=e.type,a=[Object(i.toUpper)(r)];switch(n){case"origin":a.push(Object(i.literalToSQL)(e));break;case"update":a.push("UPDATE",Object(i.commonOptionConnector)("SET",s.a,e.set),Object(i.commonOptionConnector)("WHERE",o.a,e.where))}return a.filter(i.hasVal).join(" ")}function b(t){if(!t)return"";var e=t.action;return[f(t.target),p(e)].filter(i.hasVal).join(" ")}function v(t){var e=t.table,r=t.type,a=t.prefix,u=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,h=t.where,d=t.on_duplicate_update,y=t.partition,j=t.returning,m=t.set,O=d||{},w=O.keyword,L=O.set,g=[Object(i.toUpper)(r),Object(i.toUpper)(u),Object(n.c)(e),l(y)];return Array.isArray(f)&&g.push("(".concat(f.map(i.literalToSQL).join(", "),")")),g.push(Object(i.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),g.push(Object(i.commonOptionConnector)("ON CONFLICT",b,p)),g.push(Object(i.commonOptionConnector)("SET",s.a,m)),g.push(Object(i.commonOptionConnector)("WHERE",o.a,h)),g.push(Object(i.commonOptionConnector)(w,s.a,L)),g.push(Object(i.returningToSQL)(j)),g.filter(i.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(0),o=r(1);function a(t){var e=t.expr,r=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(e),Object(n.toUpper)(r),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(0),o=r(10),a=r(2);function i(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t){if(t){var e=t.constraint,r=t.constraint_type,u=t.enforced,s=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(e));var h=Object(n.toUpper)(r);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===h&&(h="UNIQUE"),b.push(h),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(s)),b.push.apply(b,i(Object(o.c)(t))),b.push.apply(b,i(Object(a.g)(l))),b.push(Object(n.toUpper)(u)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1),a=r(13);function i(t){if(t){var e=t.type;return"rows"===e?[Object(n.toUpper)(e),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function u(t){if("string"==typeof t)return t;var e=t.window_specification;return"(".concat(function(t){var e=t.name,r=t.partitionby,a=t.orderby,u=t.window_frame_clause;return[e,Object(o.c)(r,"partition by"),Object(o.c)(a,"order by"),i(u)].filter(n.hasVal).join(" ")}(e),")")}function s(t){var e=t.name,r=t.as_window_specification;return"".concat(e," AS ").concat(u(r))}function c(t){return t.expr.map(s).join(", ")}function l(t){var e=t.over;return[function(t){var e=t.args,r=t.name,a=t.consider_nulls,i=void 0===a?"":a,u=t.separator,s=void 0===u?", ":u;return[r,"(",e?Object(o.a)(e).join(s):"",")",i&&" ",i].filter(n.hasVal).join("")}(t),Object(a.a)(e)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.operator||t.op,r=Object(n.a)(t.right),a=!1;if(Array.isArray(r)){switch(e){case"=":e="IN";break;case"!=":e="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,r="".concat(r[0]," AND ").concat(r[1])}a||(r="(".concat(r.join(", "),")"))}var i=t.right.escape||{},u=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),e,r,Object(o.toUpper)(i.type),Object(n.a)(i.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(u,")"):u].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.left,r=t.right,a=t.symbol,i=t.keyword;e.keyword=i;var u=Object(n.a)(e),s=Object(n.a)(r);return[u,Object(o.toUpper)(a),s].filter(o.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(1),o=r(8),a=r(3),i=r(0);function u(t){var e,r,u,s,c=t.keyword,l=t.suffix,f="";switch(Object(i.toUpper)(c)){case"BINLOG":r=(e=t).in,u=e.from,s=e.limit,f=[Object(i.commonOptionConnector)("IN",i.literalToSQL,r&&r.right),Object(i.commonOptionConnector)("FROM",a.c,u),Object(o.a)(s)].filter(i.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var e=t.expr;if(e){var r=e.op;return"LIKE"===Object(i.toUpper)(r)?Object(i.commonOptionConnector)("LIKE",i.literalToSQL,e.right):Object(i.commonOptionConnector)("WHERE",n.a,e)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(i.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var e=t.for;if(e){var r=e.user,n=e.host,o=e.role_list,a="'".concat(r,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(i.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(i.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(i.toUpper)(c),Object(i.toUpper)(l),f].filter(i.hasVal).join(" ")}},function(t,e,r){"use strict";var n=r(2),o=r(1),a=r(25);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u,s,c,l=(u={},s="db2",c=a.parse,(s=function(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}(s))in u?Object.defineProperty(u,s,{value:c,enumerable:!0,configurable:!0,writable:!0}):u[s]=c,u),f=r(16),p=r(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){u=!0,a=t},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw a}}}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}var j=function(){return function(t,e,r){return e&&d(t.prototype,e),r&&d(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=this.parse(t,e);return r&&r.ast}},{key:"sqlify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(f.a)(t,e)}},{key:"exprToSQL",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(r),!t||"*"===t)return[];var o=Object(n.k)(e);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=e.database,n=void 0===r?"db2":r;Object(p.setParserOpt)(e);var o=n.toLowerCase();if(l[o])return l[o](!1===e.trimQuery?t:t.trim(),e.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(e&&0!==e.length){var n=r.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,i=this["".concat(o,"List")].bind(this),u=i(t,r),s=!0,c="",l=v(u);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,h=!1,d=v(e);try{for(d.s();!(f=d.n()).done;){var y=f.value,j=new RegExp("^".concat(y,"$"),"i");if(j.test(b)){h=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!h){c=b,s=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!s)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,e){var r=this.parse(t,e);return r&&r.tableList}},{key:"columnList",value:function(t,e){var r=this.parse(t,e);return r&&r.columnList}}])}();e.a=j},function(t,e,r){"use strict";var n=r(29);function o(t,e,r,n){this.message=t,this.expected=e,this.found=r,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(o,Error),o.buildMessage=function(t,e){var r={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var e,r="";for(e=0;e<t.parts.length;e++)r+=t.parts[e]instanceof Array?a(t.parts[e][0])+"-"+a(t.parts[e][1]):a(t.parts[e]);return"["+(t.inverted?"^":"")+r+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var e,n,o,a=new Array(t.length);for(e=0;e<t.length;e++)a[e]=(o=t[e],r[o.type](o));if(a.sort(),a.length>0){for(e=1,n=1;e<a.length;e++)a[e-1]!==a[e]&&(a[n]=a[e],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(e)+" found."},t.exports={SyntaxError:o,parse:function(t,e){e=void 0!==e?e:{};var r,a={},i={start:Go},u=Go,s=function(t,e){return Vs(t,e,1)},c=Fo("IF",!0),l=function(t,e){return Vs(t,e)},f=Fo("AUTO_INCREMENT",!0),p=Fo("UNIQUE",!0),b=Fo("KEY",!0),v=Fo("PRIMARY",!0),h=Fo("COLUMN_FORMAT",!0),d=Fo("FIXED",!0),y=Fo("DYNAMIC",!0),j=Fo("DEFAULT",!0),m=Fo("STORAGE",!0),O=Fo("DISK",!0),w=Fo("MEMORY",!0),L=Fo("ALGORITHM",!0),g=Fo("INSTANT",!0),C=Fo("INPLACE",!0),S=Fo("COPY",!0),T=Fo("LOCK",!0),E=Fo("NONE",!0),A=Fo("SHARED",!0),U=Fo("EXCLUSIVE",!0),_=Fo("CHECK",!0),x=Fo("NOCHECK",!0),k=Fo("PRIMARY KEY",!0),I=Fo("NOT",!0),N=Fo("FOR",!0),R=Fo("REPLICATION",!0),V=Fo("FOREIGN KEY",!0),q=Fo("MATCH FULL",!0),M=Fo("MATCH PARTIAL",!0),P=Fo("MATCH SIMPLE",!0),Q=Fo("RESTRICT",!0),D=Fo("CASCADE",!0),F=Fo("SET NULL",!0),B=Fo("NO ACTION",!0),H=Fo("SET DEFAULT",!0),$=Fo("CHARACTER",!0),W=Fo("SET",!0),Y=Fo("CHARSET",!0),G=Fo("COLLATE",!0),Z=Fo("AVG_ROW_LENGTH",!0),X=Fo("KEY_BLOCK_SIZE",!0),J=Fo("MAX_ROWS",!0),K=Fo("MIN_ROWS",!0),z=Fo("STATS_SAMPLE_PAGES",!0),tt=Fo("CONNECTION",!0),et=Fo("COMPRESSION",!0),rt=Fo("'",!1),nt=Fo("ZLIB",!0),ot=Fo("LZ4",!0),at=Fo("ENGINE",!0),it=Fo("READ",!0),ut=Fo("LOCAL",!0),st=Fo("LOW_PRIORITY",!0),ct=Fo("WRITE",!0),lt=function(t,e){return Vs(t,e)},ft=Fo("(",!1),pt=Fo(")",!1),bt=Fo("BTREE",!0),vt=Fo("HASH",!0),ht=Fo("WITH",!0),dt=Fo("PARSER",!0),yt=Fo("VISIBLE",!0),jt=Fo("INVISIBLE",!0),mt=function(t,e){return e.unshift(t),e.forEach(t=>{const{table:e,as:r}=t;Bs[e]=e,r&&(Bs[r]=e),function(t){const e=Ps(t);t.clear(),e.forEach(e=>t.add(e))}(Fs)}),e},Ot=Fo("FIRST",!0),wt=Fo("ROWS",!0),Lt=Fo("ROW",!0),gt=Fo("ONLY",!0),Ct=Fo("NEXT",!0),St=Fo("CS",!0),Tt=Fo("UR",!0),Et=Fo("RS",!0),At=Fo("RR",!0),Ut=Fo("=",!1),_t=Fo("DUPLICATE",!0),xt=function(t,e){return qs(t,e)},kt=Fo("!",!1),It=function(t){return t[0]+" "+t[2]},Nt=Fo(">=",!1),Rt=Fo(">",!1),Vt=Fo("<=",!1),qt=Fo("<>",!1),Mt=Fo("<",!1),Pt=Fo("!=",!1),Qt=Fo("+",!1),Dt=Fo("-",!1),Ft=Fo("*",!1),Bt=Fo("/",!1),Ht=Fo("%",!1),$t=Fo("~",!1),Wt=Fo("?|",!1),Yt=Fo("?&",!1),Gt=Fo("?",!1),Zt=Fo("#-",!1),Xt=Fo("#>>",!1),Jt=Fo("#>",!1),Kt=Fo("@>",!1),zt=Fo("<@",!1),te=function(t){return!0===xs[t.toUpperCase()]},ee=Fo('"',!1),re=/^[^"]/,ne=Bo(['"'],!0,!1),oe=/^[^']/,ae=Bo(["'"],!0,!1),ie=Fo("`",!1),ue=/^[^`]/,se=Bo(["`"],!0,!1),ce=function(t,e){return t+e.join("")},le=/^[A-Za-z_\u4E00-\u9FA5]/,fe=Bo([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),pe=/^[A-Za-z0-9_$\u4E00-\u9FA5\xC0-\u017F]/,be=Bo([["A","Z"],["a","z"],["0","9"],"_","$",["一","龥"],["À","ſ"]],!1,!1),ve=/^[A-Za-z0-9_:]/,he=Bo([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),de=Fo(":",!1),ye=function(t,e){return{type:t.toLowerCase(),value:e[1].join("")}},je=/^[^"\\\0-\x1F\x7F]/,me=Bo(['"',"\\",["\0",""],""],!0,!1),Oe=/^[^'\\]/,we=Bo(["'","\\"],!0,!1),Le=Fo("\\'",!1),ge=Fo('\\"',!1),Ce=Fo("\\\\",!1),Se=Fo("\\/",!1),Te=Fo("\\b",!1),Ee=Fo("\\f",!1),Ae=Fo("\\n",!1),Ue=Fo("\\r",!1),_e=Fo("\\t",!1),xe=Fo("\\u",!1),ke=Fo("\\",!1),Ie=Fo("''",!1),Ne=Fo('""',!1),Re=Fo("``",!1),Ve=/^[\n\r]/,qe=Bo(["\n","\r"],!1,!1),Me=Fo(".",!1),Pe=/^[0-9]/,Qe=Bo([["0","9"]],!1,!1),De=/^[0-9a-fA-F]/,Fe=Bo([["0","9"],["a","f"],["A","F"]],!1,!1),Be=/^[eE]/,He=Bo(["e","E"],!1,!1),$e=/^[+\-]/,We=Bo(["+","-"],!1,!1),Ye=Fo("NULL",!0),Ge=Fo("NOT NULL",!0),Ze=Fo("TRUE",!0),Xe=Fo("TO",!0),Je=Fo("FALSE",!0),Ke=(Fo("SHOW",!0),Fo("DROP",!0)),ze=Fo("USE",!0),tr=Fo("ALTER",!0),er=Fo("SELECT",!0),rr=Fo("UPDATE",!0),nr=Fo("CREATE",!0),or=Fo("TEMPORARY",!0),ar=Fo("DELETE",!0),ir=Fo("INSERT",!0),ur=Fo("RECURSIVE",!0),sr=Fo("REPLACE",!0),cr=Fo("RENAME",!0),lr=Fo("IGNORE",!0),fr=(Fo("EXPLAIN",!0),Fo("PARTITION",!0)),pr=Fo("INTO",!0),br=Fo("FROM",!0),vr=Fo("UNLOCK",!0),hr=Fo("AS",!0),dr=Fo("TABLE",!0),yr=Fo("TABLES",!0),jr=Fo("DATABASE",!0),mr=Fo("SCHEMA",!0),Or=Fo("ON",!0),wr=Fo("LEFT",!0),Lr=Fo("RIGHT",!0),gr=Fo("FULL",!0),Cr=Fo("INNER",!0),Sr=Fo("JOIN",!0),Tr=Fo("OUTER",!0),Er=Fo("OVER",!0),Ar=Fo("UNION",!0),Ur=Fo("MINUS",!0),_r=Fo("INTERSECT",!0),xr=Fo("EXCEPT",!0),kr=Fo("VALUES",!0),Ir=Fo("USING",!0),Nr=Fo("WHERE",!0),Rr=Fo("GROUP",!0),Vr=Fo("BY",!0),qr=Fo("ORDER",!0),Mr=Fo("HAVING",!0),Pr=Fo("FETCH",!0),Qr=Fo("OFFSET",!0),Dr=Fo("ASC",!0),Fr=Fo("DESC",!0),Br=Fo("ALL",!0),Hr=Fo("DISTINCT",!0),$r=Fo("BETWEEN",!0),Wr=Fo("IN",!0),Yr=Fo("IS",!0),Gr=Fo("LIKE",!0),Zr=Fo("EXISTS",!0),Xr=Fo("AND",!0),Jr=Fo("OR",!0),Kr=Fo("COUNT",!0),zr=Fo("MAX",!0),tn=Fo("MIN",!0),en=Fo("SUM",!0),rn=Fo("AVG",!0),nn=Fo("CALL",!0),on=Fo("CASE",!0),an=Fo("WHEN",!0),un=Fo("THEN",!0),sn=Fo("ELSE",!0),cn=Fo("END",!0),ln=Fo("CAST",!0),fn=Fo("CHAR",!0),pn=Fo("VARCHAR",!0),bn=Fo("NUMERIC",!0),vn=Fo("DECIMAL",!0),hn=Fo("SIGNED",!0),dn=Fo("UNSIGNED",!0),yn=Fo("INT",!0),jn=Fo("ZEROFILL",!0),mn=Fo("INTEGER",!0),On=Fo("JSON",!0),wn=Fo("SMALLINT",!0),Ln=Fo("TINYINT",!0),gn=Fo("TINYTEXT",!0),Cn=Fo("TEXT",!0),Sn=Fo("MEDIUMTEXT",!0),Tn=Fo("LONGTEXT",!0),En=Fo("BIGINT",!0),An=Fo("FLOAT",!0),Un=Fo("DOUBLE",!0),_n=Fo("DATE",!0),xn=Fo("DATETIME",!0),kn=Fo("TIME",!0),In=Fo("TIMESTAMP",!0),Nn=Fo("TRUNCATE",!0),Rn=Fo("USER",!0),Vn=Fo("CURRENT_DATE",!0),qn=(Fo("ADDDATE",!0),Fo("INTERVAL",!0)),Mn=Fo("YEAR",!0),Pn=Fo("MONTH",!0),Qn=Fo("DAY",!0),Dn=Fo("HOUR",!0),Fn=Fo("MINUTE",!0),Bn=Fo("SECOND",!0),Hn=Fo("CURRENT_TIME",!0),$n=Fo("CURRENT_TIMESTAMP",!0),Wn=Fo("CURRENT_USER",!0),Yn=Fo("SESSION_USER",!0),Gn=Fo("SYSTEM_USER",!0),Zn=Fo("GLOBAL",!0),Xn=Fo("SESSION",!0),Jn=Fo("PERSIST",!0),Kn=Fo("PERSIST_ONLY",!0),zn=Fo("@",!1),to=Fo("@@",!1),eo=Fo("$",!1),ro=Fo("return",!0),no=Fo(":=",!1),oo=Fo("DUAL",!0),ao=Fo("ADD",!0),io=Fo("COLUMN",!0),uo=Fo("INDEX",!0),so=Fo("FULLTEXT",!0),co=Fo("SPATIAL",!0),lo=Fo("COMMENT",!0),fo=Fo("CONSTRAINT",!0),po=Fo("REFERENCES",!0),bo=Fo("SQL_CALC_FOUND_ROWS",!0),vo=Fo("SQL_CACHE",!0),ho=Fo("SQL_NO_CACHE",!0),yo=Fo("SQL_SMALL_RESULT",!0),jo=Fo("SQL_BIG_RESULT",!0),mo=Fo("SQL_BUFFER_RESULT",!0),Oo=Fo(",",!1),wo=Fo("[",!1),Lo=Fo("]",!1),go=Fo(";",!1),Co=Fo("->",!1),So=Fo("->>",!1),To=Fo("||",!1),Eo=Fo("&&",!1),Ao=Fo("/*",!1),Uo=Fo("*/",!1),_o=Fo("--",!1),xo=Fo("#",!1),ko={type:"any"},Io=/^[ \t\n\r]/,No=Bo([" ","\t","\n","\r"],!1,!1),Ro=function(t){return{dataType:t}},Vo=0,qo=0,Mo=[{line:1,column:1}],Po=0,Qo=[],Do=0;if("startRule"in e){if(!(e.startRule in i))throw new Error("Can't start parsing from rule \""+e.startRule+'".');u=i[e.startRule]}function Fo(t,e){return{type:"literal",text:t,ignoreCase:e}}function Bo(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function Ho(e){var r,n=Mo[e];if(n)return n;for(r=e-1;!Mo[r];)r--;for(n={line:(n=Mo[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return Mo[e]=n,n}function $o(t,e){var r=Ho(t),n=Ho(e);return{start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function Wo(t){Vo<Po||(Vo>Po&&(Po=Vo,Qo=[]),Qo.push(t))}function Yo(t,e,r){return new o(o.buildMessage(t,e),t,e,r)}function Go(){var t,e;return t=Vo,fs()!==a&&(e=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Xo())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=us())!==a&&(u=fs())!==a&&(s=Xo())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=us())!==a&&(u=fs())!==a&&(s=Xo())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=function(t,e){const r=t&&t.ast||t,n=e&&e.length&&e[0].length>=4?[r]:r;for(let t=0;t<e.length;t++)e[t][3]&&0!==e[t][3].length&&n.push(e[t][3]&&e[t][3].ast||e[t][3]);return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:n}}(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=t,t=e):(Vo=t,t=a),t}function Zo(){var e;return(e=function(){var t,e,r,n,o,i;t=Vo,(e=Xi())!==a&&fs()!==a&&(r=su())!==a&&fs()!==a&&(n=_a())!==a?(qo=t,u=e,c=r,(l=n)&&l.forEach(t=>Ds.add(`${u}::${t.db}::${t.table}`)),e={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:u.toLowerCase(),keyword:c.toLowerCase(),name:l}},t=e):(Vo=t,t=a);var u,c,l;t===a&&(t=Vo,(e=Xi())!==a&&fs()!==a&&(r=Ku())!==a&&fs()!==a&&(n=hi())!==a&&fs()!==a&&lu()!==a&&fs()!==a&&(o=Na())!==a&&fs()!==a?((i=function(){var t,e,r,n,o,i;t=Vo,(e=aa())===a&&(e=ia());if(e!==a){for(r=[],n=Vo,(o=fs())!==a?((i=aa())===a&&(i=ia()),i!==a?n=o=[o,i]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=aa())===a&&(i=ia()),i!==a?n=o=[o,i]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,e=s(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())===a&&(i=null),i!==a&&fs()!==a?(qo=t,e=function(t,e,r,n,o){return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:t.toLowerCase(),keyword:e.toLowerCase(),name:r,table:n,options:o}}}(e,r,n,o,i),t=e):(Vo=t,t=a)):(Vo=t,t=a));return t}())===a&&(e=function(){var e;(e=function(){var t,e,r,n,o,i,u,s,c,f;t=Vo,(e=Ki())!==a&&fs()!==a?((r=zi())===a&&(r=null),r!==a&&fs()!==a&&su()!==a&&fs()!==a?((n=zo())===a&&(n=null),n!==a&&fs()!==a&&(o=_a())!==a&&fs()!==a&&(i=function(){var t,e,r,n,o,i,u,s,c;if(t=Vo,(e=as())!==a)if(fs()!==a)if((r=ta())!==a){for(n=[],o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=ta())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=ta())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);n!==a&&(o=fs())!==a&&(i=is())!==a?(qo=t,e=l(r,n),t=e):(Vo=t,t=a)}else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;return t}())!==a&&fs()!==a?((u=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=va())!==a){for(r=[],n=Vo,(o=fs())!==a?((i=ns())===a&&(i=null),i!==a&&(u=fs())!==a&&(s=va())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=ns())===a&&(i=null),i!==a&&(u=fs())!==a&&(s=va())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,e=Vs(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())===a&&(u=null),u!==a&&fs()!==a?((s=nu())===a&&(s=eu()),s===a&&(s=null),s!==a&&fs()!==a?((c=uu())===a&&(c=null),c!==a&&fs()!==a?((f=Ko())===a&&(f=null),f!==a?(qo=t,p=e,b=r,v=n,d=i,y=u,j=s,m=c,O=f,(h=o)&&h.forEach(t=>Ds.add(`create::${t.db}::${t.table}`)),e={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:v,table:h,ignore_replace:j&&j[0].toLowerCase(),as:m&&m[0].toLowerCase(),query_expr:O&&O.ast,create_definitions:d,table_options:y}},t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a);var p,b,v,h,d,y,j,m,O;t===a&&(t=Vo,(e=Ki())!==a&&fs()!==a?((r=zi())===a&&(r=null),r!==a&&fs()!==a&&su()!==a&&fs()!==a?((n=zo())===a&&(n=null),n!==a&&fs()!==a&&(o=_a())!==a&&fs()!==a&&(i=function t(){var e,r;(e=function(){var t,e;t=Vo,gu()!==a&&fs()!==a&&(e=_a())!==a?(qo=t,t={type:"like",table:e}):(Vo=t,t=a);return t}())===a&&(e=Vo,as()!==a&&fs()!==a&&(r=t())!==a&&fs()!==a&&is()!==a?(qo=e,(n=r).parentheses=!0,e=n):(Vo=e,e=a));var n;return e}())!==a?(qo=t,e=function(t,e,r,n,o){return n&&n.forEach(t=>Ds.add(`create::${t.db}::${t.table}`)),{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:e&&e[0].toLowerCase(),if_not_exists:r,table:n,like:o}}}(e,r,n,o,i),t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a));return t}())===a&&(e=function(){var e,r,n,o,i,u;e=Vo,(r=Ki())!==a&&fs()!==a?((n=function(){var e,r,n,o;e=Vo,"database"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(jr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DATABASE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"schema"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(mr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SCHEMA"):(Vo=e,e=a)):(Vo=e,e=a);return e}()),n!==a&&fs()!==a?((o=zo())===a&&(o=null),o!==a&&fs()!==a&&(i=Cs())!==a&&fs()!==a?((u=function(){var t,e,r,n,o,i;if(t=Vo,(e=ba())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ba())!==a?n=o=[o,i]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ba())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a?(qo=t,e=s(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())===a&&(u=null),u!==a?(qo=e,r=function(t,e,r,n,o){const a=e.toLowerCase();return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:r,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(r,n,o,i,u),e=r):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);return e}());return e}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=function(){var e,r,n,o;e=Vo,"truncate"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(Nn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TRUNCATE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a?((n=su())===a&&(n=null),n!==a&&fs()!==a&&(o=_a())!==a?(qo=e,i=r,u=n,(s=o)&&s.forEach(t=>Ds.add(`${i}::${t.db}::${t.table}`)),r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:i.toLowerCase(),keyword:u&&u.toLowerCase()||"table",name:s}},e=r):(Vo=e,e=a)):(Vo=e,e=a);var i,u,s;return e}())===a&&(e=function(){var t,e,r;t=Vo,(e=ru())!==a&&fs()!==a&&su()!==a&&fs()!==a&&(r=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Ta())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ta())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ta())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=l(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=t,(n=r).forEach(t=>t.forEach(t=>t.table&&Ds.add(`rename::${t.db}::${t.table}`))),e={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"rename",table:n}},t=e):(Vo=t,t=a);var n;return t}())===a&&(e=function(){var e,r,n;e=Vo,(r=function(){var e,r,n,o;e=Vo,"call"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(nn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CALL"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&(n=Ss())!==a?(qo=e,o=n,r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"call",expr:o}},e=r):(Vo=e,e=a);var o;return e}())===a&&(e=function(){var e,r,n;e=Vo,(r=function(){var e,r,n,o;e=Vo,"use"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(ze));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&(n=yi())!==a?(qo=e,o=n,Ds.add(`use::${o}::null`),r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"use",db:o}},e=r):(Vo=e,e=a);var o;return e}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=function(){var e,r,n,o;e=Vo,"alter"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(tr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&su()!==a&&fs()!==a&&(n=_a())!==a&&fs()!==a&&(o=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=oa())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=oa())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=oa())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=l(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=e,u=o,(i=n)&&i.length>0&&i.forEach(t=>Ds.add(`alter::${t.db}::${t.table}`)),r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"alter",table:i,expr:u}},e=r):(Vo=e,e=a);var i,u;return e}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=iu())!==a&&fs()!==a?((n=function(){var e,r,n,o;e=Vo,"global"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Zn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="GLOBAL"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"session"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(Xn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SESSION"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"local"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(ut));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="LOCAL"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"persist"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(Jn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="PERSIST"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"persist_only"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(Kn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="PERSIST_ONLY"):(Vo=e,e=a)):(Vo=e,e=a);return e}()),n===a&&(n=null),n!==a&&fs()!==a&&(o=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ms())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ms())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ms())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=lt(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=e,i=n,(u=o).keyword=i,r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"set",keyword:i,expr:u}},e=r):(Vo=e,e=a)):(Vo=e,e=a);var i,u;return e}())===a&&(e=function(){var e,r,n;e=Vo,(r=function(){var e,r,n,o;e=Vo,"lock"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(T));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&cu()!==a&&fs()!==a&&(n=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ha())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ha())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ha())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=lt(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=e,o=n,r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"lock",keyword:"tables",tables:o}},e=r):(Vo=e,e=a);var o;return e}())===a&&(e=function(){var e,r;e=Vo,(r=function(){var e,r,n,o;e=Vo,"unlock"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(vr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&cu()!==a?(qo=e,r={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"unlock",keyword:"tables"}},e=r):(Vo=e,e=a);return e}()),e}function Xo(){var t;return(t=Ko())===a&&(t=function(){var t,e,r,n,o;t=Vo,(e=Ji())!==a&&fs()!==a&&(r=_a())!==a&&fs()!==a&&iu()!==a&&fs()!==a&&(n=Ba())!==a&&fs()!==a?((o=qa())===a&&(o=null),o!==a?(qo=t,e=function(t,e,r){const n={};return t&&t.forEach(t=>{const{server:e,db:r,schema:o,as:a,table:i,join:u}=t,s=u?"select":"update",c=[e,r,o].filter(Boolean).join(".")||null;r&&(n[i]=c),i&&Ds.add(`${s}::${c}::${i}`)}),e&&e.forEach(t=>{if(t.table){const e=Ms(t.table);Ds.add(`update::${n[e]||null}::${e}`)}Fs.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"update",table:t,set:e,where:r}}}(r,n,o),t=e):(Vo=t,t=a)):(Vo=t,t=a);return t}())===a&&(t=function(){var t,e,r,n,o,i,u,s;t=Vo,(e=Ga())!==a&&fs()!==a?((r=au())===a&&(r=null),r!==a&&fs()!==a&&(n=Na())!==a&&fs()!==a?((o=Wa())===a&&(o=null),o!==a&&fs()!==a&&as()!==a&&fs()!==a&&(i=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Ci())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ci())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ci())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=l(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a&&fs()!==a&&is()!==a&&fs()!==a&&(u=$a())!==a&&fs()!==a?((s=Ya())===a&&(s=null),s!==a?(qo=t,e=function(t,e,r,n,o,a){if(e&&(Ds.add(`insert::${e.db}::${e.table}`),e.as=null),n){let t=e&&e.table||null;Array.isArray(o)&&o.forEach((t,e)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(e+1))}),n.forEach(e=>Fs.add(`insert::${t}::${e}`))}return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:t,table:[e],columns:n,values:o,partition:r,on_duplicate_update:a}}}(e,n,o,i,u,s),t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a);return t}())===a&&(t=function(){var t,e,r,n,o,i,u,s;t=Vo,(e=Ga())!==a&&fs()!==a?((r=nu())===a&&(r=null),r!==a&&fs()!==a?((n=au())===a&&(n=null),n!==a&&fs()!==a&&(o=Na())!==a&&fs()!==a?((i=Wa())===a&&(i=null),i!==a&&fs()!==a&&(u=$a())!==a&&fs()!==a?((s=Ya())===a&&(s=null),s!==a?(qo=t,e=function(t,e,r,n,o,a,i){n&&(Ds.add(`insert::${n.db}::${n.table}`),Fs.add(`insert::${n.table}::(.*)`),n.as=null);const u=[e,r].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:u,on_duplicate_update:i}}}(e,r,n,o,i,u,s),t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a);return t}())===a&&(t=function(){var t,e,r,n,o,i;t=Vo,(e=Ga())!==a&&fs()!==a&&au()!==a&&fs()!==a&&(r=Na())!==a&&fs()!==a?((n=Wa())===a&&(n=null),n!==a&&fs()!==a&&iu()!==a&&fs()!==a&&(o=Ba())!==a&&fs()!==a?((i=Ya())===a&&(i=null),i!==a?(qo=t,u=e,c=n,l=o,f=i,(s=r)&&(Ds.add(`insert::${s.db}::${s.table}`),Fs.add(`insert::${s.table}::(.*)`),s.as=null),e={tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:u,table:[s],columns:null,partition:c,set:l,on_duplicate_update:f}},t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a);var u,s,c,l,f;return t}())===a&&(t=function(){var t,e,r,n,o;t=Vo,(e=tu())!==a&&fs()!==a?((r=_a())===a&&(r=null),r!==a&&fs()!==a&&(n=Sa())!==a&&fs()!==a?((o=qa())===a&&(o=null),o!==a?(qo=t,e=function(t,e,r){if(e&&e.forEach(t=>{const{db:e,as:r,table:n,join:o}=t,a=o?"select":"delete";n&&Ds.add(`${a}::${e}::${n}`),o||Fs.add(`delete::${n}::(.*)`)}),null===t&&1===e.length){const r=e[0];t=[{db:r.db,table:r.table,as:r.as,addition:!0}]}return{tableList:Array.from(Ds),columnList:Ps(Fs),ast:{type:"delete",table:t,from:e,where:r}}}(r,n,o),t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a);return t}())===a&&(t=Zo())===a&&(t=function(){var t,e;t=[],e=js();for(;e!==a;)t.push(e),e=js();return t}()),t}function Jo(){var e,r,n,o,i;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"union"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Ar));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="UNION"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Vo,"intersect"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(_r));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="INTERSECT"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Vo,"except"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(xr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="EXCEPT"):(Vo=e,e=a)):(Vo=e,e=a);return e}()),r!==a&&fs()!==a?((n=ju())===a&&(n=mu()),n===a&&(n=null),n!==a?(qo=e,o=r,e=r=(i=n)?`${o.toLowerCase()} ${i.toLowerCase()}`:""+o.toLowerCase()):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=function(){var e,r,n,o;e=Vo,"minus"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Ur));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MINUS"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r="minus"),e=r),e}function Ko(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=da())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=Jo())!==a&&(u=fs())!==a&&(s=da())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=Jo())!==a&&(u=fs())!==a&&(s=da())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a&&(n=fs())!==a?((o=Pa())===a&&(o=null),o!==a&&(i=fs())!==a?((u=Fa())===a&&(u=null),u!==a?(qo=t,t=e=function(t,e,r,n){let o=t;for(let t=0;t<e.length;t++)o._next=e[t][3],o.set_op=e[t][1],o=o._next;return r&&(t._orderby=r),n&&(t._limit=n),{tableList:Array.from(Ds),columnList:Ps(Fs),ast:t}}(e,r,o,u)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)}else Vo=t,t=a;return t}function zo(){var e,r;return e=Vo,"if"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(c)),r!==a&&fs()!==a&&Su()!==a&&fs()!==a&&Cu()!==a?(qo=e,e=r="IF NOT EXISTS"):(Vo=e,e=a),e}function ta(){var t;return(t=ca())===a&&(t=ra())===a&&(t=ua())===a&&(t=sa()),t}function ea(){var e,r,n,o;return e=Vo,(r=function(){var e,r;e=Vo,(r=function(){var e,r,n,o;e=Vo,"not null"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(Ge));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={type:"not null",value:"not null"});return e=r}())===a&&(r=Vi()),r!==a&&(qo=e,(o=r)&&!o.value&&(o.value="null"),r={nullable:o}),(e=r)===a&&(e=Vo,(r=function(){var t,e;t=Vo,Gi()!==a&&fs()!==a&&(e=ri())!==a?(qo=t,t={type:"default",value:e}):(Vo=t,t=a);return t}())!==a&&(qo=e,r={default_val:r}),(e=r)===a&&(e=Vo,"auto_increment"===t.substr(Vo,14).toLowerCase()?(r=t.substr(Vo,14),Vo+=14):(r=a,0===Do&&Wo(f)),r!==a&&(qo=e,r={auto_increment:r.toLowerCase()}),(e=r)===a&&(e=Vo,"unique"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(p)),r!==a&&fs()!==a?("key"===t.substr(Vo,3).toLowerCase()?(n=t.substr(Vo,3),Vo+=3):(n=a,0===Do&&Wo(b)),n===a&&(n=null),n!==a?(qo=e,e=r=function(t){const e=["unique"];return t&&e.push(t),{unique:e.join(" ").toLowerCase("")}}(n)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,"primary"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(v)),r===a&&(r=null),r!==a&&fs()!==a?("key"===t.substr(Vo,3).toLowerCase()?(n=t.substr(Vo,3),Vo+=3):(n=a,0===Do&&Wo(b)),n!==a?(qo=e,e=r=function(t){const e=[];return t&&e.push("primary"),e.push("key"),{primary_key:e.join(" ").toLowerCase("")}}(r)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=vs())!==a&&(qo=e,r={comment:r}),(e=r)===a&&(e=Vo,(r=na())!==a&&(qo=e,r={collate:r}),(e=r)===a&&(e=Vo,(r=function(){var e,r,n;e=Vo,"column_format"===t.substr(Vo,13).toLowerCase()?(r=t.substr(Vo,13),Vo+=13):(r=a,0===Do&&Wo(h));r!==a&&fs()!==a?("fixed"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(d)),n===a&&("dynamic"===t.substr(Vo,7).toLowerCase()?(n=t.substr(Vo,7),Vo+=7):(n=a,0===Do&&Wo(y)),n===a&&("default"===t.substr(Vo,7).toLowerCase()?(n=t.substr(Vo,7),Vo+=7):(n=a,0===Do&&Wo(j)))),n!==a?(qo=e,r={type:"column_format",value:n.toLowerCase()},e=r):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={column_format:r}),(e=r)===a&&(e=Vo,(r=function(){var e,r,n;e=Vo,"storage"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(m));r!==a&&fs()!==a?("disk"===t.substr(Vo,4).toLowerCase()?(n=t.substr(Vo,4),Vo+=4):(n=a,0===Do&&Wo(O)),n===a&&("memory"===t.substr(Vo,6).toLowerCase()?(n=t.substr(Vo,6),Vo+=6):(n=a,0===Do&&Wo(w))),n!==a?(qo=e,r={type:"storage",value:n.toLowerCase()},e=r):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={storage:r}),(e=r)===a&&(e=Vo,(r=fa())!==a&&(qo=e,r={reference_definition:r}),e=r))))))))),e}function ra(){var t,e,r,n,o,i,u;return t=Vo,(e=hi())!==a&&fs()!==a&&(r=Us())!==a&&fs()!==a?((n=function(){var t,e,r,n,o,i;if(t=Vo,(e=ea())!==a)if(fs()!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ea())!==a?n=o=[o,i]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ea())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){let r=t;for(let t=0;t<e.length;t++)r={...r,...e[t][1]};return r}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;else Vo=t,t=a;return t}())===a&&(n=null),n!==a?(qo=t,o=e,i=r,u=n,Fs.add(`create::${o.table}::${o.column}`),t=e={column:o,definition:i,resource:"column",...u||{}}):(Vo=t,t=a)):(Vo=t,t=a),t}function na(){var e,r,n;return e=Vo,function(){var e,r,n,o;e=Vo,"collate"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(G));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="COLLATE"):(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a?((r=Zu())===a&&(r=null),r!==a&&fs()!==a&&(n=yi())!==a?(qo=e,e={type:"collate",keyword:"collate",collate:{name:n,symbol:r}}):(Vo=e,e=a)):(Vo=e,e=a),e}function oa(){var e;return(e=function(){var t,e;t=Vo,Xu()!==a&&fs()!==a&&(e=ca())!==a?(qo=t,t={action:"add",create_definitions:e,resource:"constraint",type:"alter"}):(Vo=t,t=a);return t}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=Xi())!==a&&fs()!==a?("check"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(_)),n!==a&&fs()!==a&&(o=Ti())!==a?(qo=e,r={action:"drop",constraint:o,keyword:n.toLowerCase(),resource:"constraint",type:"alter"},e=r):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,hu()!==a&&fs()!==a?("check"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(_)),r!==a&&fs()!==a?("check"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(_)),n!==a&&fs()!==a&&es()!==a&&fs()!==a&&(o=Ti())!==a?(qo=e,e={action:"with",constraint:o,keyword:"check check",resource:"constraint",type:"alter"}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n;e=Vo,"nocheck"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(x));r!==a&&fs()!==a&&es()!==a&&fs()!==a&&(n=Ti())!==a?(qo=e,e=r={action:"nocheck",constraint:n,resource:"constraint",type:"alter"}):(Vo=e,e=a);return e}())===a&&(e=function(){var t,e,r,n;t=Vo,(e=Xu())!==a&&fs()!==a?((r=Ju())===a&&(r=null),r!==a&&fs()!==a&&(n=ra())!==a?(qo=t,o=r,i=n,e={action:"add",...i,keyword:o,resource:"column",type:"alter"},t=e):(Vo=t,t=a)):(Vo=t,t=a);var o,i;return t}())===a&&(e=function(){var t,e,r;t=Vo,Xi()!==a&&fs()!==a?((e=Ju())===a&&(e=null),e!==a&&fs()!==a&&(r=hi())!==a?(qo=t,t={action:"drop",column:r,keyword:e,resource:"column",type:"alter"}):(Vo=t,t=a)):(Vo=t,t=a);return t}())===a&&(e=function(){var t,e,r;t=Vo,(e=Xu())!==a&&fs()!==a&&(r=ua())!==a?(qo=t,n=r,e={action:"add",type:"alter",...n},t=e):(Vo=t,t=a);var n;return t}())===a&&(e=function(){var t,e,r;t=Vo,(e=Xu())!==a&&fs()!==a&&(r=sa())!==a?(qo=t,n=r,e={action:"add",type:"alter",...n},t=e):(Vo=t,t=a);var n;return t}())===a&&(e=function(){var t,e,r,n;t=Vo,(e=ru())!==a&&fs()!==a?((r=Zi())===a&&(r=uu()),r===a&&(r=null),r!==a&&fs()!==a&&(n=yi())!==a?(qo=t,i=n,e={action:"rename",type:"alter",resource:"table",keyword:(o=r)&&o[0].toLowerCase(),table:i},t=e):(Vo=t,t=a)):(Vo=t,t=a);var o,i;return t}())===a&&(e=aa())===a&&(e=ia()),e}function aa(){var e,r,n,o;return e=Vo,"algorithm"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(L)),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a?("default"===t.substr(Vo,7).toLowerCase()?(o=t.substr(Vo,7),Vo+=7):(o=a,0===Do&&Wo(j)),o===a&&("instant"===t.substr(Vo,7).toLowerCase()?(o=t.substr(Vo,7),Vo+=7):(o=a,0===Do&&Wo(g)),o===a&&("inplace"===t.substr(Vo,7).toLowerCase()?(o=t.substr(Vo,7),Vo+=7):(o=a,0===Do&&Wo(C)),o===a&&("copy"===t.substr(Vo,4).toLowerCase()?(o=t.substr(Vo,4),Vo+=4):(o=a,0===Do&&Wo(S))))),o!==a?(qo=e,e=r={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e}function ia(){var e,r,n,o;return e=Vo,"lock"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(T)),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a?("default"===t.substr(Vo,7).toLowerCase()?(o=t.substr(Vo,7),Vo+=7):(o=a,0===Do&&Wo(j)),o===a&&("none"===t.substr(Vo,4).toLowerCase()?(o=t.substr(Vo,4),Vo+=4):(o=a,0===Do&&Wo(E)),o===a&&("shared"===t.substr(Vo,6).toLowerCase()?(o=t.substr(Vo,6),Vo+=6):(o=a,0===Do&&Wo(A)),o===a&&("exclusive"===t.substr(Vo,9).toLowerCase()?(o=t.substr(Vo,9),Vo+=9):(o=a,0===Do&&Wo(U))))),o!==a?(qo=e,e=r={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e}function ua(){var t,e,r,n,o,i,u,s;return t=Vo,(e=Ku())===a&&(e=zu()),e!==a&&fs()!==a?((r=Ci())===a&&(r=null),r!==a&&fs()!==a?((n=Ea())===a&&(n=null),n!==a&&fs()!==a&&(o=ma())!==a&&fs()!==a?((i=Aa())===a&&(i=null),i!==a&&fs()!==a?(qo=t,u=n,s=i,t=e={index:r,definition:o,keyword:e.toLowerCase(),index_type:u,resource:"index",index_options:s}):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a),t}function sa(){var e,r,n,o,i,u,s,c,l;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"fulltext"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(so));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="FULLTEXT"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Vo,"spatial"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(co));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SPATIAL"):(Vo=e,e=a)):(Vo=e,e=a);return e}()),r!==a&&fs()!==a?((n=Ku())===a&&(n=zu()),n===a&&(n=null),n!==a&&fs()!==a?((o=Ci())===a&&(o=null),o!==a&&fs()!==a&&(i=ma())!==a&&fs()!==a?((u=Aa())===a&&(u=null),u!==a&&fs()!==a?(qo=e,s=r,l=u,e=r={index:o,definition:i,keyword:(c=n)&&`${s.toLowerCase()} ${c.toLowerCase()}`||s.toLowerCase(),index_options:l,resource:"index"}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e}function ca(){var e;return(e=function(){var e,r,n,o,i,u;e=Vo,(r=la())===a&&(r=null);r!==a&&fs()!==a?("primary key"===t.substr(Vo,11).toLowerCase()?(n=t.substr(Vo,11),Vo+=11):(n=a,0===Do&&Wo(k)),n!==a&&fs()!==a?((o=Ea())===a&&(o=null),o!==a&&fs()!==a&&(i=ma())!==a&&fs()!==a?((u=Aa())===a&&(u=null),u!==a?(qo=e,c=n,l=o,f=i,p=u,r={constraint:(s=r)&&s.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:s&&s.keyword,index_type:l,resource:"constraint",index_options:p},e=r):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);var s,c,l,f,p;return e}())===a&&(e=function(){var e,r,n,o,i,u,s,c;e=Vo,(r=la())===a&&(r=null);r!==a&&fs()!==a&&(n=function(){var e,r,n,o;e=Vo,"unique"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(p));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="UNIQUE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a?((o=Ku())===a&&(o=zu()),o===a&&(o=null),o!==a&&fs()!==a?((i=Ci())===a&&(i=null),i!==a&&fs()!==a?((u=Ea())===a&&(u=null),u!==a&&fs()!==a&&(s=ma())!==a&&fs()!==a?((c=Aa())===a&&(c=null),c!==a?(qo=e,f=n,b=o,v=i,h=u,d=s,y=c,r={constraint:(l=r)&&l.constraint,definition:d,constraint_type:b&&`${f.toLowerCase()} ${b.toLowerCase()}`||f.toLowerCase(),keyword:l&&l.keyword,index_type:h,index:v,resource:"constraint",index_options:y},e=r):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);var l,f,b,v,h,d,y;return e}())===a&&(e=function(){var e,r,n,o,i,u;e=Vo,(r=la())===a&&(r=null);r!==a&&fs()!==a?("foreign key"===t.substr(Vo,11).toLowerCase()?(n=t.substr(Vo,11),Vo+=11):(n=a,0===Do&&Wo(V)),n!==a&&fs()!==a?((o=Ci())===a&&(o=null),o!==a&&fs()!==a&&(i=ma())!==a&&fs()!==a?((u=fa())===a&&(u=null),u!==a?(qo=e,c=n,l=o,f=i,p=u,r={constraint:(s=r)&&s.constraint,definition:f,constraint_type:c,keyword:s&&s.keyword,index:l,resource:"constraint",reference_definition:p},e=r):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);var s,c,l,f,p;return e}())===a&&(e=function(){var e,r,n,o,i,u,s,c,l,f;e=Vo,(r=la())===a&&(r=null);r!==a&&fs()!==a?("check"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(_)),n!==a&&fs()!==a?(o=Vo,"not"===t.substr(Vo,3).toLowerCase()?(i=t.substr(Vo,3),Vo+=3):(i=a,0===Do&&Wo(I)),i!==a&&(u=fs())!==a?("for"===t.substr(Vo,3).toLowerCase()?(s=t.substr(Vo,3),Vo+=3):(s=a,0===Do&&Wo(N)),s!==a&&(c=fs())!==a?("replication"===t.substr(Vo,11).toLowerCase()?(l=t.substr(Vo,11),Vo+=11):(l=a,0===Do&&Wo(R)),l!==a&&(f=fs())!==a?o=i=[i,u,s,c,l,f]:(Vo=o,o=a)):(Vo=o,o=a)):(Vo=o,o=a),o===a&&(o=null),o!==a&&(i=as())!==a&&(u=fs())!==a&&(s=ri())!==a&&(c=fs())!==a&&(l=is())!==a?(qo=e,p=r,b=o,v=s,r={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication"},definition:[v],resource:"constraint"},e=r):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a);var p,b,v;return e}()),e}function la(){var t,e,r,n;return t=Vo,(e=es())!==a&&fs()!==a?((r=yi())===a&&(r=null),r!==a?(qo=t,n=r,t=e={keyword:e.toLowerCase(),constraint:n}):(Vo=t,t=a)):(Vo=t,t=a),t}function fa(){var e,r,n,o,i,u,s,c,l,f;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"references"===t.substr(Vo,10).toLowerCase()?(r=t.substr(Vo,10),Vo+=10):(r=a,0===Do&&Wo(po));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="REFERENCES"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&(n=_a())!==a&&fs()!==a&&(o=ma())!==a&&fs()!==a?("match full"===t.substr(Vo,10).toLowerCase()?(i=t.substr(Vo,10),Vo+=10):(i=a,0===Do&&Wo(q)),i===a&&("match partial"===t.substr(Vo,13).toLowerCase()?(i=t.substr(Vo,13),Vo+=13):(i=a,0===Do&&Wo(M)),i===a&&("match simple"===t.substr(Vo,12).toLowerCase()?(i=t.substr(Vo,12),Vo+=12):(i=a,0===Do&&Wo(P)))),i===a&&(i=null),i!==a&&fs()!==a?((u=pa())===a&&(u=null),u!==a&&fs()!==a?((s=pa())===a&&(s=null),s!==a?(qo=e,c=i,l=u,f=s,e=r={definition:o,table:n,keyword:r.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=pa())!==a&&(qo=e,r={on_action:[r]}),e=r),e}function pa(){var e,r,n,o;return e=Vo,lu()!==a&&fs()!==a?((r=tu())===a&&(r=Ji()),r!==a&&fs()!==a&&(n=function(){var e,r,n;e=Vo,(r=Yu())!==a&&fs()!==a&&as()!==a&&fs()!==a?((n=Xa())===a&&(n=null),n!==a&&fs()!==a&&is()!==a?(qo=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},args:n}):(Vo=e,e=a)):(Vo=e,e=a);e===a&&(e=Vo,"restrict"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(Q)),r===a&&("cascade"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(D)),r===a&&("set null"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(F)),r===a&&("no action"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(B)),r===a&&("set default"===t.substr(Vo,11).toLowerCase()?(r=t.substr(Vo,11),Vo+=11):(r=a,0===Do&&Wo(H)),r===a&&(r=Yu()))))),r!==a&&(qo=e,r={type:"origin",value:r.toLowerCase()}),e=r);return e}())!==a?(qo=e,o=n,e={type:"on "+r[0].toLowerCase(),value:o}):(Vo=e,e=a)):(Vo=e,e=a),e}function ba(){var e,r,n,o,i,u,s,c,l;return e=Vo,(r=Gi())===a&&(r=null),r!==a&&fs()!==a?((n=function(){var e,r,n;return e=Vo,"character"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo($)),r!==a&&fs()!==a?("set"===t.substr(Vo,3).toLowerCase()?(n=t.substr(Vo,3),Vo+=3):(n=a,0===Do&&Wo(W)),n!==a?(qo=e,e=r="CHARACTER SET"):(Vo=e,e=a)):(Vo=e,e=a),e}())===a&&("charset"===t.substr(Vo,7).toLowerCase()?(n=t.substr(Vo,7),Vo+=7):(n=a,0===Do&&Wo(Y)),n===a&&("collate"===t.substr(Vo,7).toLowerCase()?(n=t.substr(Vo,7),Vo+=7):(n=a,0===Do&&Wo(G)))),n!==a&&fs()!==a?((o=Zu())===a&&(o=null),o!==a&&fs()!==a&&(i=di())!==a?(qo=e,s=n,c=o,l=i,e=r={keyword:(u=r)&&`${u[0].toLowerCase()} ${s.toLowerCase()}`||s.toLowerCase(),symbol:c,value:l}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e}function va(){var e,r,n,o,i,u,s,c,l;return e=Vo,"auto_increment"===t.substr(Vo,14).toLowerCase()?(r=t.substr(Vo,14),Vo+=14):(r=a,0===Do&&Wo(f)),r===a&&("avg_row_length"===t.substr(Vo,14).toLowerCase()?(r=t.substr(Vo,14),Vo+=14):(r=a,0===Do&&Wo(Z)),r===a&&("key_block_size"===t.substr(Vo,14).toLowerCase()?(r=t.substr(Vo,14),Vo+=14):(r=a,0===Do&&Wo(X)),r===a&&("max_rows"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(J)),r===a&&("min_rows"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(K)),r===a&&("stats_sample_pages"===t.substr(Vo,18).toLowerCase()?(r=t.substr(Vo,18),Vo+=18):(r=a,0===Do&&Wo(z))))))),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a&&(o=Di())!==a?(qo=e,c=n,l=o,e=r={keyword:r.toLowerCase(),symbol:c,value:l.value}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=ba())===a&&(e=Vo,(r=ts())===a&&("connection"===t.substr(Vo,10).toLowerCase()?(r=t.substr(Vo,10),Vo+=10):(r=a,0===Do&&Wo(tt))),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a&&(o=qi())!==a?(qo=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:`'${r.value}'`}}(r,n,o)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,"compression"===t.substr(Vo,11).toLowerCase()?(r=t.substr(Vo,11),Vo+=11):(r=a,0===Do&&Wo(et)),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a?(o=Vo,39===t.charCodeAt(Vo)?(i="'",Vo++):(i=a,0===Do&&Wo(rt)),i!==a?("zlib"===t.substr(Vo,4).toLowerCase()?(u=t.substr(Vo,4),Vo+=4):(u=a,0===Do&&Wo(nt)),u===a&&("lz4"===t.substr(Vo,3).toLowerCase()?(u=t.substr(Vo,3),Vo+=3):(u=a,0===Do&&Wo(ot)),u===a&&("none"===t.substr(Vo,4).toLowerCase()?(u=t.substr(Vo,4),Vo+=4):(u=a,0===Do&&Wo(E)))),u!==a?(39===t.charCodeAt(Vo)?(s="'",Vo++):(s=a,0===Do&&Wo(rt)),s!==a?o=i=[i,u,s]:(Vo=o,o=a)):(Vo=o,o=a)):(Vo=o,o=a),o!==a?(qo=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.join("").toUpperCase()}}(r,n,o)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,"engine"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(at)),r!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a&&(o=Ti())!==a?(qo=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.toUpperCase()}}(r,n,o)):(Vo=e,e=a)):(Vo=e,e=a)))),e}function ha(){var e,r,n,o,i;return e=Vo,(r=ka())!==a&&fs()!==a&&(n=function(){var e,r,n;return e=Vo,"read"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(it)),r!==a&&fs()!==a?("local"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(ut)),n===a&&(n=null),n!==a?(qo=e,e=r={type:"read",suffix:n&&"local"}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,"low_priority"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(st)),r===a&&(r=null),r!==a&&fs()!==a?("write"===t.substr(Vo,5).toLowerCase()?(n=t.substr(Vo,5),Vo+=5):(n=a,0===Do&&Wo(ct)),n!==a?(qo=e,e=r={type:"write",prefix:r&&"low_priority"}):(Vo=e,e=a)):(Vo=e,e=a)),e}())!==a?(qo=e,o=r,i=n,Ds.add(`lock::${o.db}::${o.table}`),e=r={table:o,lock_type:i}):(Vo=e,e=a),e}function da(){var e,r,n,o,i,u,s;return(e=Oa())===a&&(e=Vo,r=Vo,40===t.charCodeAt(Vo)?(n="(",Vo++):(n=a,0===Do&&Wo(ft)),n!==a&&(o=fs())!==a&&(i=da())!==a&&(u=fs())!==a?(41===t.charCodeAt(Vo)?(s=")",Vo++):(s=a,0===Do&&Wo(pt)),s!==a?r=n=[n,o,i,u,s]:(Vo=r,r=a)):(Vo=r,r=a),r!==a&&(qo=e,r={...r[2],parentheses_symbol:!0}),e=r),e}function ya(){var e,r,n,o,i,u,s,c,f;if(e=Vo,hu()!==a)if(fs()!==a)if((r=ja())!==a){for(n=[],o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=ja())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=ja())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);n!==a?(qo=e,e=l(r,n)):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;return e===a&&(e=Vo,fs()!==a&&hu()!==a&&(r=fs())!==a&&(n=function(){var e,r,n,o;e=Vo,"recursive"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(ur));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(o=fs())!==a&&(i=ja())!==a?(qo=e,(f=i).recursive=!0,e=[f]):(Vo=e,e=a)),e}function ja(){var t,e,r,n,o;return t=Vo,(e=qi())===a&&(e=Ti()),e!==a&&fs()!==a?((r=ma())===a&&(r=null),r!==a&&fs()!==a&&uu()!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=Ko())!==a&&fs()!==a&&is()!==a?(qo=t,"string"==typeof(o=e)&&(o={type:"default",value:o}),t=e={name:o,stmt:n,columns:r}):(Vo=t,t=a)):(Vo=t,t=a),t}function ma(){var t,e;return t=Vo,as()!==a&&fs()!==a&&(e=Ma())!==a&&fs()!==a&&is()!==a?(qo=t,t=e):(Vo=t,t=a),t}function Oa(){var e,r,n,o,i,u,s,c,l,f,p,b,v,h,d,y,j,m,O,w,L,g,C;return e=Vo,fs()!==a?((r=ya())===a&&(r=null),r!==a&&fs()!==a&&function(){var e,r,n,o;e=Vo,"select"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(er));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&ps()!==a?((n=function(){var t,e,r,n,o,i;if(t=Vo,(e=wa())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=wa())!==a?n=o=[o,i]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=wa())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a?(qo=t,e=function(t,e){const r=[t];for(let t=0,n=e.length;t<n;++t)r.push(e[t][1]);return r}(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())===a&&(n=null),n!==a&&fs()!==a?((o=mu())===a&&(o=null),o!==a&&fs()!==a&&(i=La())!==a&&fs()!==a?((u=Sa())===a&&(u=null),u!==a&&fs()!==a?((s=qa())===a&&(s=null),s!==a&&fs()!==a?((c=function(){var e,r,n;e=Vo,(r=function(){var e,r,n,o;e=Vo,"group"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Rr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&du()!==a&&fs()!==a&&(n=Xa())!==a?(qo=e,r={columns:n.value},e=r):(Vo=e,e=a);return e}())===a&&(c=null),c!==a&&fs()!==a?((l=function(){var e,r;e=Vo,function(){var e,r,n,o;e=Vo,"having"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Mr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=ni())!==a?(qo=e,e=r):(Vo=e,e=a);return e}())===a&&(l=null),l!==a&&fs()!==a?((f=Pa())===a&&(f=null),f!==a&&fs()!==a?((p=Fa())===a&&(p=null),p!==a?((b=function(){var e,r;e=Vo,hu()!==a&&fs()!==a?("cs"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(St)),r===a&&("ur"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Tt)),r===a&&("rs"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Et)),r===a&&("rr"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(At))))),r!==a?(qo=e,e={type:"isolation",keyword:"with",expr:{type:"origin",value:r}}):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(b=null),b!==a&&fs()!==a?(qo=e,v=r,h=n,d=o,y=i,m=s,O=c,w=l,L=f,g=p,C=b,(j=u)&&j.forEach(t=>t.table&&Ds.add(`select::${t.db}::${t.table}`)),e={with:v,type:"select",options:h,distinct:d,columns:y,from:j,where:m,groupby:O,having:w,orderby:L,limit:g,isolation:C}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e}function wa(){var e,r;return e=Vo,(r=function(){var e;"sql_calc_found_rows"===t.substr(Vo,19).toLowerCase()?(e=t.substr(Vo,19),Vo+=19):(e=a,0===Do&&Wo(bo));return e}())===a&&((r=function(){var e;"sql_cache"===t.substr(Vo,9).toLowerCase()?(e=t.substr(Vo,9),Vo+=9):(e=a,0===Do&&Wo(vo));return e}())===a&&(r=function(){var e;"sql_no_cache"===t.substr(Vo,12).toLowerCase()?(e=t.substr(Vo,12),Vo+=12):(e=a,0===Do&&Wo(ho));return e}()),r===a&&(r=function(){var e;"sql_big_result"===t.substr(Vo,14).toLowerCase()?(e=t.substr(Vo,14),Vo+=14):(e=a,0===Do&&Wo(jo));return e}())===a&&(r=function(){var e;"sql_small_result"===t.substr(Vo,16).toLowerCase()?(e=t.substr(Vo,16),Vo+=16):(e=a,0===Do&&Wo(yo));return e}())===a&&(r=function(){var e;"sql_buffer_result"===t.substr(Vo,17).toLowerCase()?(e=t.substr(Vo,17),Vo+=17):(e=a,0===Do&&Wo(mo));return e}())),r!==a&&(qo=e,r=r),e=r}function La(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ju())===a&&(e=Vo,(r=os())!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=os())),e!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ga())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ga())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){Fs.add("select::null::(.*)");const r={expr:{type:"column_ref",table:null,column:"*"},as:null};return e&&e.length>0?Vs(r,e):[r]}(0,r)):(Vo=t,t=a)}else Vo=t,t=a;if(t===a)if(t=Vo,(e=ga())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ga())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ga())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=l(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function ga(){var t,e,r,n,o;return t=Vo,e=Vo,(r=yi())!==a&&(n=fs())!==a&&(o=rs())!==a?e=r=[r,n,o]:(Vo=e,e=a),e===a&&(e=null),e!==a&&(r=fs())!==a&&(n=os())!==a?(qo=t,t=e=function(t){const e=t&&t[0]||null;return Fs.add(`select::${e}::(.*)`),{expr:{type:"column_ref",table:e,column:"*"},as:null}}(e)):(Vo=t,t=a),t===a&&(t=Vo,(e=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ri())!==a){for(r=[],n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu())===a&&(i=ls()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu())===a&&(i=ls()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,e=function(t,e){const r=t.ast;if(r&&"select"===r.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==r.columns.length||"*"===r.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!e||0===e.length)return t;const n=e.length;let o=e[n-1][3];for(let r=n-1;r>=0;r--){const n=0===r?t:e[r-1][3];o=Ns(e[r][1],n,o)}return o}(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a&&(r=fs())!==a?((n=Ca())===a&&(n=null),n!==a?(qo=t,t=e={expr:e,as:n}):(Vo=t,t=a)):(Vo=t,t=a)),t}function Ca(){var t,e,r;return t=Vo,(e=uu())!==a&&fs()!==a&&(r=function(){var t,e;t=Vo,(e=Ti())!==a?(qo=Vo,(function(t){if(!0===xs[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(e)?a:void 0)!==a?(qo=t,t=e=e):(Vo=t,t=a)):(Vo=t,t=a);t===a&&(t=Vo,(e=mi())!==a&&(qo=t,e=e),t=e);return t}())!==a?(qo=t,t=e=r):(Vo=t,t=a),t===a&&(t=Vo,(e=uu())===a&&(e=null),e!==a&&fs()!==a&&(r=yi())!==a?(qo=t,t=e=r):(Vo=t,t=a)),t}function Sa(){var e,r;return e=Vo,function(){var e,r,n,o;e=Vo,"from"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(br));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=_a())!==a?(qo=e,e=r):(Vo=e,e=a),e}function Ta(){var t,e,r;return t=Vo,(e=Na())!==a&&fs()!==a&&Zi()!==a&&fs()!==a&&(r=Na())!==a?(qo=t,t=e=[e,r]):(Vo=t,t=a),t}function Ea(){var e,r;return e=Vo,vu()!==a&&fs()!==a?("btree"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(bt)),r===a&&("hash"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(vt))),r!==a?(qo=e,e={keyword:"using",type:r.toLowerCase()}):(Vo=e,e=a)):(Vo=e,e=a),e}function Aa(){var t,e,r,n,o,i;if(t=Vo,(e=Ua())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=Ua())!==a?n=o=[o,i]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=Ua())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){const r=[t];for(let t=0;t<e.length;t++)r.push(e[t][1]);return r}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ua(){var e,r,n,o,i,u;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"key_block_size"===t.substr(Vo,14).toLowerCase()?(r=t.substr(Vo,14),Vo+=14):(r=a,0===Do&&Wo(X));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="KEY_BLOCK_SIZE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a?((n=Zu())===a&&(n=null),n!==a&&fs()!==a&&(o=Di())!==a?(qo=e,i=n,u=o,e=r={type:r.toLowerCase(),symbol:i,expr:u}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Ea())===a&&(e=Vo,"with"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(ht)),r!==a&&fs()!==a?("parser"===t.substr(Vo,6).toLowerCase()?(n=t.substr(Vo,6),Vo+=6):(n=a,0===Do&&Wo(dt)),n!==a&&fs()!==a&&(o=Ti())!==a?(qo=e,e=r={type:"with parser",expr:o}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,"visible"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(yt)),r===a&&("invisible"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(jt))),r!==a&&(qo=e,r=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(r)),(e=r)===a&&(e=vs()))),e}function _a(){var t,e,r,n;if(t=Vo,(e=ka())!==a){for(r=[],n=xa();n!==a;)r.push(n),n=xa();r!==a?(qo=t,t=e=mt(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function xa(){var t,e,r;return t=Vo,fs()!==a&&(e=ns())!==a&&fs()!==a&&(r=ka())!==a?(qo=t,t=r):(Vo=t,t=a),t===a&&(t=Vo,fs()!==a&&(e=function(){var t,e,r,n,o,i,u,s,c,l,f;if(t=Vo,(e=Ia())!==a)if(fs()!==a)if((r=ka())!==a)if(fs()!==a)if((n=vu())!==a)if(fs()!==a)if(as()!==a)if(fs()!==a)if((o=di())!==a){for(i=[],u=Vo,(s=fs())!==a&&(c=ns())!==a&&(l=fs())!==a&&(f=di())!==a?u=s=[s,c,l,f]:(Vo=u,u=a);u!==a;)i.push(u),u=Vo,(s=fs())!==a&&(c=ns())!==a&&(l=fs())!==a&&(f=di())!==a?u=s=[s,c,l,f]:(Vo=u,u=a);i!==a&&(u=fs())!==a&&(s=is())!==a?(qo=t,p=e,v=o,h=i,(b=r).join=p,b.using=Vs(v,h),t=e=b):(Vo=t,t=a)}else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;var p,b,v,h;t===a&&(t=Vo,(e=Ia())!==a&&fs()!==a&&(r=ka())!==a&&fs()!==a?((n=Va())===a&&(n=null),n!==a?(qo=t,e=function(t,e,r){return e.join=t,e.on=r,e}(e,r,n),t=e):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,(e=Ia())===a&&(e=Jo()),e!==a&&fs()!==a&&(r=as())!==a&&fs()!==a&&(n=Ko())!==a&&fs()!==a&&is()!==a&&fs()!==a?((o=Ca())===a&&(o=null),o!==a&&(i=fs())!==a?((u=Va())===a&&(u=null),u!==a?(qo=t,e=function(t,e,r,n){return e.parentheses=!0,{expr:e,as:r,join:t,on:n}}(e,n,o,u),t=e):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)));return t}())!==a?(qo=t,t=e):(Vo=t,t=a)),t}function ka(){var e,r,n,o,i,u;return e=Vo,(r=function(){var e;"dual"===t.substr(Vo,4).toLowerCase()?(e=t.substr(Vo,4),Vo+=4):(e=a,0===Do&&Wo(oo));return e}())!==a&&(qo=e,r={type:"dual"}),(e=r)===a&&(e=Vo,(r=Na())!==a&&fs()!==a?((n=Ca())===a&&(n=null),n!==a?(qo=e,u=n,e=r="var"===(i=r).type?(i.as=u,i):{db:i.db,table:i.table,as:u}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=as())!==a&&fs()!==a&&(n=Ko())!==a&&fs()!==a&&is()!==a&&fs()!==a?((o=Ca())===a&&(o=null),o!==a?(qo=e,e=r=function(t,e){return t.parentheses=!0,{expr:t,as:e}}(n,o)):(Vo=e,e=a)):(Vo=e,e=a))),e}function Ia(){var e,r,n,o;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"left"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(wr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(n=fs())!==a?((o=pu())===a&&(o=null),o!==a&&fs()!==a&&fu()!==a?(qo=e,e=r="LEFT JOIN"):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=function(){var e,r,n,o;e=Vo,"right"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Lr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(n=fs())!==a?((o=pu())===a&&(o=null),o!==a&&fs()!==a&&fu()!==a?(qo=e,e=r="RIGHT JOIN"):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=function(){var e,r,n,o;e=Vo,"full"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(gr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(n=fs())!==a?((o=pu())===a&&(o=null),o!==a&&fs()!==a&&fu()!==a?(qo=e,e=r="FULL JOIN"):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,r=Vo,(n=function(){var e,r,n,o;e=Vo,"inner"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Cr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(o=fs())!==a?r=n=[n,o]:(Vo=r,r=a),r===a&&(r=null),r!==a&&(n=fu())!==a?(qo=e,e=r="INNER JOIN"):(Vo=e,e=a)))),e}function Na(){var t,e,r,n,o,i,u,s;return t=Vo,(e=yi())!==a?(r=Vo,(n=fs())!==a&&(o=rs())!==a&&(i=fs())!==a&&(u=yi())!==a?r=n=[n,o,i,u]:(Vo=r,r=a),r===a&&(r=null),r!==a?(qo=t,t=e=function(t,e){const r={db:null,table:t};return null!==e&&(r.db=t,r.table=e[3]),r}(e,r)):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,(e=Es())!==a&&(qo=t,(s=e).db=null,s.table=s.name,e=s),t=e),t}function Ra(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ri())!==a){for(r=[],n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){const r=e.length;let n=t;for(let t=0;t<r;++t)n=Ns(e[t][1],n,e[t][3]);return n}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Va(){var t,e;return t=Vo,lu()!==a&&fs()!==a&&(e=ni())!==a?(qo=t,t=e):(Vo=t,t=a),t}function qa(){var e,r;return e=Vo,function(){var e,r,n,o;e=Vo,"where"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Nr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=ni())!==a?(qo=e,e=r):(Vo=e,e=a),e}function Ma(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=hi())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=hi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=hi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=l(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Pa(){var e,r;return e=Vo,function(){var e,r,n,o;e=Vo,"order"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(qr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&du()!==a&&fs()!==a&&(r=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Qa())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Qa())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Qa())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=l(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=e,e=r):(Vo=e,e=a),e}function Qa(){var e,r,n;return e=Vo,(r=ri())!==a&&fs()!==a?((n=function(){var e,r,n,o;e=Vo,"desc"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Fr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DESC"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Vo,"asc"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(Dr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="ASC"):(Vo=e,e=a)):(Vo=e,e=a);return e}()),n===a&&(n=null),n!==a?(qo=e,e=r={expr:r,type:n}):(Vo=e,e=a)):(Vo=e,e=a),e}function Da(){var t;return(t=Di())===a&&(t=_i()),t}function Fa(){var e,r,n,o,i,u,s,c;return e=Vo,yu()!==a&&fs()!==a?("first"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Ot)),r!==a&&fs()!==a&&(n=Da())!==a&&fs()!==a?("rows"===t.substr(Vo,4).toLowerCase()?(o=t.substr(Vo,4),Vo+=4):(o=a,0===Do&&Wo(wt)),o===a&&("row"===t.substr(Vo,3).toLowerCase()?(o=t.substr(Vo,3),Vo+=3):(o=a,0===Do&&Wo(Lt))),o!==a&&fs()!==a?("only"===t.substr(Vo,4).toLowerCase()?(i=t.substr(Vo,4),Vo+=4):(i=a,0===Do&&Wo(gt)),i!==a?(qo=e,e={fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"first"}],value:n,suffix:[{type:"origin",value:o},{type:"origin",value:"only"}]}}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,function(){var e,r,n,o;e=Vo,"offset"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Qr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="OFFSET"):(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=Da())!==a&&fs()!==a?("rows"===t.substr(Vo,4).toLowerCase()?(n=t.substr(Vo,4),Vo+=4):(n=a,0===Do&&Wo(wt)),n!==a&&fs()!==a&&(o=yu())!==a&&fs()!==a?("next"===t.substr(Vo,4).toLowerCase()?(i=t.substr(Vo,4),Vo+=4):(i=a,0===Do&&Wo(Ct)),i!==a&&fs()!==a&&(u=Da())!==a&&fs()!==a?("rows"===t.substr(Vo,4).toLowerCase()?(s=t.substr(Vo,4),Vo+=4):(s=a,0===Do&&Wo(wt)),s===a&&("row"===t.substr(Vo,3).toLowerCase()?(s=t.substr(Vo,3),Vo+=3):(s=a,0===Do&&Wo(Lt))),s!==a&&fs()!==a?("only"===t.substr(Vo,4).toLowerCase()?(c=t.substr(Vo,4),Vo+=4):(c=a,0===Do&&Wo(gt)),c!==a?(qo=e,e=function(t,e,r){return{offset:{prefix:[{type:"origin",value:"offset"}],value:t,suffix:[{type:"origin",value:"rows"}]},fetch:{prefix:[{type:"origin",value:"fetch"},{type:"origin",value:"next"}],value:e,suffix:[{type:"origin",value:r},{type:"origin",value:"only"}]}}}(r,u,s)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a)),e}function Ba(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Ha())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ha())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Ha())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=l(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ha(){var e,r,n,o,i,u,s,c,l;return e=Vo,r=Vo,(n=yi())!==a&&(o=fs())!==a&&(i=rs())!==a?r=n=[n,o,i]:(Vo=r,r=a),r===a&&(r=null),r!==a&&(n=fs())!==a&&(o=gi())!==a&&(i=fs())!==a?(61===t.charCodeAt(Vo)?(u="=",Vo++):(u=a,0===Do&&Wo(Ut)),u!==a&&fs()!==a&&(s=ci())!==a?(qo=e,e=r={column:o,value:s,table:(l=r)&&l[0]}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,r=Vo,(n=yi())!==a&&(o=fs())!==a&&(i=rs())!==a?r=n=[n,o,i]:(Vo=r,r=a),r===a&&(r=null),r!==a&&(n=fs())!==a&&(o=gi())!==a&&(i=fs())!==a?(61===t.charCodeAt(Vo)?(u="=",Vo++):(u=a,0===Do&&Wo(Ut)),u!==a&&fs()!==a&&(s=bu())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(c=hi())!==a&&fs()!==a&&is()!==a?(qo=e,e=r=function(t,e,r){return{column:e,value:r,table:t&&t[0],keyword:"values"}}(r,o,c)):(Vo=e,e=a)):(Vo=e,e=a)),e}function $a(){var t;return(t=function(){var t,e;t=Vo,bu()!==a&&fs()!==a&&(e=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Za())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Za())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=Za())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=l(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())!==a?(qo=t,t=e):(Vo=t,t=a);return t}())===a&&(t=Oa()),t}function Wa(){var t,e,r,n,o,i,u,s,c;if(t=Vo,ou()!==a)if(fs()!==a)if((e=as())!==a)if(fs()!==a)if((r=Ti())!==a){for(n=[],o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=Ti())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,(i=fs())!==a&&(u=ns())!==a&&(s=fs())!==a&&(c=Ti())!==a?o=i=[i,u,s,c]:(Vo=o,o=a);n!==a&&(o=fs())!==a&&(i=is())!==a?(qo=t,t=Vs(r,n)):(Vo=t,t=a)}else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;else Vo=t,t=a;return t===a&&(t=Vo,ou()!==a&&fs()!==a&&(e=Za())!==a?(qo=t,t=e):(Vo=t,t=a)),t}function Ya(){var e,r,n;return e=Vo,lu()!==a&&fs()!==a?("duplicate"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(_t)),r!==a&&fs()!==a&&zu()!==a&&fs()!==a&&Ji()!==a&&fs()!==a&&(n=Ba())!==a?(qo=e,e={keyword:"on duplicate key update",set:n}):(Vo=e,e=a)):(Vo=e,e=a),e}function Ga(){var e,r;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"insert"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(ir));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r="insert"),(e=r)===a&&(e=Vo,(r=eu())!==a&&(qo=e,r="replace"),e=r),e}function Za(){var t,e;return t=Vo,as()!==a&&fs()!==a&&(e=Xa())!==a&&fs()!==a&&is()!==a?(qo=t,t=e):(Vo=t,t=a),t}function Xa(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ri())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){const r={type:"expr_list"};return r.value=Vs(t,e),r}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ja(){var e,r,n;return e=Vo,function(){var e,r,n,o;e=Vo,"interval"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(qn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="INTERVAL"):(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=ri())!==a&&fs()!==a&&(n=function(){var e;(e=function(){var e,r,n,o;e=Vo,"year"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Mn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="YEAR"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"month"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Pn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MONTH"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"day"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(Qn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DAY"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"hour"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Dn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="HOUR"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"minute"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Fn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MINUTE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"second"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Bn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SECOND"):(Vo=e,e=a)):(Vo=e,e=a);return e}());return e}())!==a?(qo=e,e={type:"interval",expr:r,unit:n.toLowerCase()}):(Vo=e,e=a),e}function Ka(){var t,e,r,n,o,i;if(t=Vo,(e=za())!==a)if(fs()!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=za())!==a?n=o=[o,i]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=za())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a?(qo=t,t=e=s(e,r)):(Vo=t,t=a)}else Vo=t,t=a;else Vo=t,t=a;return t}function za(){var e,r,n;return e=Vo,function(){var e,r,n,o;e=Vo,"when"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(an));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=ni())!==a&&fs()!==a&&function(){var e,r,n,o;e=Vo,"then"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(un));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(n=ri())!==a?(qo=e,e={type:"when",cond:r,result:n}):(Vo=e,e=a),e}function ti(){var e,r;return e=Vo,function(){var e,r,n,o;e=Vo,"else"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(sn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&(r=ri())!==a?(qo=e,e={type:"else",result:r}):(Vo=e,e=a),e}function ei(){var t;return(t=function(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=oi())!==a){for(r=[],n=Vo,(o=ps())!==a&&(i=Eu())!==a&&(u=fs())!==a&&(s=oi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=ps())!==a&&(i=Eu())!==a&&(u=fs())!==a&&(s=oi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,e=xt(e,r),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}())===a&&(t=function(){var t,e,r,n,o,i;if(t=Vo,(e=li())!==a){if(r=[],n=Vo,(o=fs())!==a&&(i=bi())!==a?n=o=[o,i]:(Vo=n,n=a),n!==a)for(;n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=bi())!==a?n=o=[o,i]:(Vo=n,n=a);else r=a;r!==a?(qo=t,e=Is(e,r[0][1]),t=e):(Vo=t,t=a)}else Vo=t,t=a;return t}()),t}function ri(){var t;return(t=ei())===a&&(t=Ko()),t}function ni(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ri())!==a){for(r=[],n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu())===a&&(i=ns()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=Tu())===a&&(i=Eu())===a&&(i=ns()),i!==a&&(u=fs())!==a&&(s=ri())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){const r=e.length;let n=t,o="";for(let t=0;t<r;++t)","===e[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(e[t][3])):n=Ns(e[t][1],n,e[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function oi(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=ai())!==a){for(r=[],n=Vo,(o=ps())!==a&&(i=Tu())!==a&&(u=fs())!==a&&(s=ai())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=ps())!==a&&(i=Tu())!==a&&(u=fs())!==a&&(s=ai())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=xt(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function ai(){var e,r,n,o,i;return(e=ii())===a&&(e=function(){var t,e,r;t=Vo,(e=function(){var t,e,r,n,o;t=Vo,e=Vo,(r=Su())!==a&&(n=fs())!==a&&(o=Cu())!==a?e=r=[r,n,o]:(Vo=e,e=a);e!==a&&(qo=t,e=It(e));(t=e)===a&&(t=Cu());return t}())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(r=Ko())!==a&&fs()!==a&&is()!==a?(qo=t,n=e,(o=r).parentheses=!0,e=Is(n,o),t=e):(Vo=t,t=a);var n,o;return t}())===a&&(e=Vo,(r=Su())===a&&(r=Vo,33===t.charCodeAt(Vo)?(n="!",Vo++):(n=a,0===Do&&Wo(kt)),n!==a?(o=Vo,Do++,61===t.charCodeAt(Vo)?(i="=",Vo++):(i=a,0===Do&&Wo(Ut)),Do--,i===a?o=void 0:(Vo=o,o=a),o!==a?r=n=[n,o]:(Vo=r,r=a)):(Vo=r,r=a)),r!==a&&(n=fs())!==a&&(o=ai())!==a?(qo=e,e=r=Is("NOT",o)):(Vo=e,e=a)),e}function ii(){var t,e,r,n,o;return t=Vo,(e=ci())!==a&&fs()!==a?((r=function(){var t;(t=function(){var t,e,r,n,o,i,u;t=Vo,e=[],r=Vo,(n=fs())!==a&&(o=ui())!==a&&(i=fs())!==a&&(u=ci())!==a?r=n=[n,o,i,u]:(Vo=r,r=a);if(r!==a)for(;r!==a;)e.push(r),r=Vo,(n=fs())!==a&&(o=ui())!==a&&(i=fs())!==a&&(u=ci())!==a?r=n=[n,o,i,u]:(Vo=r,r=a);else e=a;e!==a&&(qo=t,e={type:"arithmetic",tail:e});return t=e}())===a&&(t=function(){var t,e,r,n;t=Vo,(e=si())!==a&&fs()!==a&&(r=as())!==a&&fs()!==a&&(n=Xa())!==a&&fs()!==a&&is()!==a?(qo=t,t=e={op:e,right:n}):(Vo=t,t=a);t===a&&(t=Vo,(e=si())!==a&&fs()!==a?((r=Es())===a&&(r=qi())===a&&(r=Ii()),r!==a?(qo=t,e=function(t,e){return{op:t,right:e}}(e,r),t=e):(Vo=t,t=a)):(Vo=t,t=a));return t}())===a&&(t=function(){var t,e,r,n;t=Vo,(e=function(){var t,e,r,n,o;t=Vo,e=Vo,(r=Su())!==a&&(n=fs())!==a&&(o=Ou())!==a?e=r=[r,n,o]:(Vo=e,e=a);e!==a&&(qo=t,e=It(e));(t=e)===a&&(t=Ou());return t}())!==a&&fs()!==a&&(r=ci())!==a&&fs()!==a&&Tu()!==a&&fs()!==a&&(n=ci())!==a?(qo=t,t=e={op:e,right:{type:"expr_list",value:[r,n]}}):(Vo=t,t=a);return t}())===a&&(t=function(){var t,e,r,n,o;t=Vo,(e=Lu())!==a&&(r=fs())!==a&&(n=ci())!==a?(qo=t,t=e={op:"IS",right:n}):(Vo=t,t=a);t===a&&(t=Vo,e=Vo,(r=Lu())!==a&&(n=fs())!==a&&(o=Su())!==a?e=r=[r,n,o]:(Vo=e,e=a),e!==a&&(r=fs())!==a&&(n=ci())!==a?(qo=t,e=function(t){return{op:"IS NOT",right:t}}(n),t=e):(Vo=t,t=a));return t}())===a&&(t=function(){var t,e,r;t=Vo,(e=function(){var t,e,r,n,o;t=Vo,e=Vo,(r=Su())!==a&&(n=fs())!==a&&(o=gu())!==a?e=r=[r,n,o]:(Vo=e,e=a);e!==a&&(qo=t,e=It(e));(t=e)===a&&(t=gu());return t}())!==a&&fs()!==a?((r=Ri())===a&&(r=ii()),r!==a?(qo=t,t=e={op:e,right:r}):(Vo=t,t=a)):(Vo=t,t=a);return t}());return t}())===a&&(r=null),r!==a?(qo=t,n=e,t=e=null===(o=r)?n:"arithmetic"===o.type?qs(n,o.tail):Ns(o.op,n,o.right)):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=qi())===a&&(t=hi()),t}function ui(){var e;return">="===t.substr(Vo,2)?(e=">=",Vo+=2):(e=a,0===Do&&Wo(Nt)),e===a&&(62===t.charCodeAt(Vo)?(e=">",Vo++):(e=a,0===Do&&Wo(Rt)),e===a&&("<="===t.substr(Vo,2)?(e="<=",Vo+=2):(e=a,0===Do&&Wo(Vt)),e===a&&("<>"===t.substr(Vo,2)?(e="<>",Vo+=2):(e=a,0===Do&&Wo(qt)),e===a&&(60===t.charCodeAt(Vo)?(e="<",Vo++):(e=a,0===Do&&Wo(Mt)),e===a&&(61===t.charCodeAt(Vo)?(e="=",Vo++):(e=a,0===Do&&Wo(Ut)),e===a&&("!="===t.substr(Vo,2)?(e="!=",Vo+=2):(e=a,0===Do&&Wo(Pt)))))))),e}function si(){var t,e,r,n,o;return t=Vo,e=Vo,(r=Su())!==a&&(n=fs())!==a&&(o=wu())!==a?e=r=[r,n,o]:(Vo=e,e=a),e!==a&&(qo=t,e=It(e)),(t=e)===a&&(t=wu()),t}function ci(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=fi())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=li())!==a&&(u=fs())!==a&&(s=fi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=li())!==a&&(u=fs())!==a&&(s=fi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=function(t,e){if(e&&e.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...ks()}));return qs(t,e)}(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function li(){var e;return 43===t.charCodeAt(Vo)?(e="+",Vo++):(e=a,0===Do&&Wo(Qt)),e===a&&(45===t.charCodeAt(Vo)?(e="-",Vo++):(e=a,0===Do&&Wo(Dt))),e}function fi(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=vi())!==a){for(r=[],n=Vo,(o=fs())!==a?((i=pi())===a&&(i=ls()),i!==a&&(u=fs())!==a&&(s=vi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a?((i=pi())===a&&(i=ls()),i!==a&&(u=fs())!==a&&(s=vi())!==a?n=o=[o,i,u,s]:(Vo=n,n=a)):(Vo=n,n=a);r!==a?(qo=t,t=e=qs(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function pi(){var e;return 42===t.charCodeAt(Vo)?(e="*",Vo++):(e=a,0===Do&&Wo(Ft)),e===a&&(47===t.charCodeAt(Vo)?(e="/",Vo++):(e=a,0===Do&&Wo(Bt)),e===a&&(37===t.charCodeAt(Vo)?(e="%",Vo++):(e=a,0===Do&&Wo(Ht)))),e}function bi(){var e,r,n;return(e=function(){var e,r,n,o,i,u,s;e=Vo,(r=_u())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=ri())!==a&&fs()!==a&&uu()!==a&&fs()!==a&&(o=Us())!==a&&fs()!==a&&(i=is())!==a?(qo=e,c=n,l=o,r={type:"cast",keyword:r.toLowerCase(),expr:c,symbol:"as",target:[l]},e=r):(Vo=e,e=a);var c,l;e===a&&(e=Vo,(r=_u())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=ri())!==a&&fs()!==a&&uu()!==a&&fs()!==a&&(o=Nu())!==a&&fs()!==a&&(i=as())!==a&&fs()!==a&&(u=Fi())!==a&&fs()!==a&&is()!==a&&fs()!==a&&(s=is())!==a?(qo=e,r=function(t,e,r){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:"DECIMAL("+r+")"}]}}(r,n,u),e=r):(Vo=e,e=a),e===a&&(e=Vo,(r=_u())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=ri())!==a&&fs()!==a&&uu()!==a&&fs()!==a&&(o=Nu())!==a&&fs()!==a&&(i=as())!==a&&fs()!==a&&(u=Fi())!==a&&fs()!==a&&ns()!==a&&fs()!==a&&(s=Fi())!==a&&fs()!==a&&is()!==a&&fs()!==a&&is()!==a?(qo=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:"DECIMAL("+r+", "+n+")"}]}}(r,n,u,s),e=r):(Vo=e,e=a),e===a&&(e=Vo,(r=_u())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=ri())!==a&&fs()!==a&&uu()!==a&&fs()!==a&&(o=function(){var e;(e=function(){var e,r,n,o;e=Vo,"signed"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(hn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SIGNED"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=Ru());return e}())!==a&&fs()!==a?((i=qu())===a&&(i=null),i!==a&&fs()!==a&&(u=is())!==a?(qo=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),expr:e,symbol:"as",target:[{dataType:r+(n?" "+n:"")}]}}(r,n,o,i),e=r):(Vo=e,e=a)):(Vo=e,e=a))));return e}())===a&&(e=Ri())===a&&(e=function(){var e;(e=function(){var e,r,n,o;e=Vo,(r=function(){var e,r,n,o;e=Vo,"count"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Kr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="COUNT"):(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=function(){var e,r,n,o,i,u,s,c,l,f;e=Vo,(r=function(){var e,r;e=Vo,42===t.charCodeAt(Vo)?(r="*",Vo++):(r=a,0===Do&&Wo(Ft));r!==a&&(qo=e,r={type:"star",value:"*"});return e=r}())!==a&&(qo=e,r={expr:r});if((e=r)===a){if(e=Vo,(r=mu())===a&&(r=null),r!==a)if(fs()!==a)if((n=as())!==a)if(fs()!==a)if((o=ri())!==a)if(fs()!==a)if(is()!==a){for(i=[],u=Vo,(s=fs())!==a?((c=Tu())===a&&(c=Eu()),c!==a&&(l=fs())!==a&&(f=ri())!==a?u=s=[s,c,l,f]:(Vo=u,u=a)):(Vo=u,u=a);u!==a;)i.push(u),u=Vo,(s=fs())!==a?((c=Tu())===a&&(c=Eu()),c!==a&&(l=fs())!==a&&(f=ri())!==a?u=s=[s,c,l,f]:(Vo=u,u=a)):(Vo=u,u=a);i!==a&&(u=fs())!==a?((s=Pa())===a&&(s=null),s!==a?(qo=e,r=function(t,e,r,n){const o=r.length;let a=e;a.parentheses=!0;for(let t=0;t<o;++t)a=Ns(r[t][1],a,r[t][3]);return{distinct:t,expr:a,orderby:n}}(r,o,i,s),e=r):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;e===a&&(e=Vo,(r=mu())===a&&(r=null),r!==a&&fs()!==a&&(n=Ra())!==a&&fs()!==a?((o=Pa())===a&&(o=null),o!==a?(qo=e,e=r={distinct:r,expr:n,orderby:o}):(Vo=e,e=a)):(Vo=e,e=a))}return e}())!==a&&fs()!==a&&is()!==a&&fs()!==a?((o=ki())===a&&(o=null),o!==a?(qo=e,e=r={type:"aggr_func",name:r,args:n,over:o}):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=function(){var e;(e=function(){var e,r,n,o;e=Vo,"sum"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(en));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SUM"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"max"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(zr));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MAX"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"min"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(tn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MIN"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"avg"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(rn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="AVG"):(Vo=e,e=a)):(Vo=e,e=a);return e}());return e}())!==a&&fs()!==a&&as()!==a&&fs()!==a&&(n=ci())!==a&&fs()!==a&&is()!==a&&fs()!==a?((o=ki())===a&&(o=null),o!==a?(qo=e,r={type:"aggr_func",name:r,args:{expr:n},over:o,...ks()},e=r):(Vo=e,e=a)):(Vo=e,e=a);return e}());return e}())===a&&(e=Ii())===a&&(e=function(){var t,e,r,n,o,i,u,s;return t=Vo,Au()!==a&&fs()!==a&&(e=Ka())!==a&&fs()!==a?((r=ti())===a&&(r=null),r!==a&&fs()!==a&&(n=Uu())!==a&&fs()!==a?((o=Au())===a&&(o=null),o!==a?(qo=t,u=e,(s=r)&&u.push(s),t={type:"case",expr:null,args:u}):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,Au()!==a&&fs()!==a&&(e=ri())!==a&&fs()!==a&&(r=Ka())!==a&&fs()!==a?((n=ti())===a&&(n=null),n!==a&&fs()!==a&&(o=Uu())!==a&&fs()!==a?((i=Au())===a&&(i=null),i!==a?(qo=t,t=function(t,e,r){return r&&e.push(r),{type:"case",expr:t,args:e}}(e,r,n)):(Vo=t,t=a)):(Vo=t,t=a)):(Vo=t,t=a)),t}())===a&&(e=Ja())===a&&(e=hi())===a&&(e=_i())===a&&(e=Vo,as()!==a&&fs()!==a&&(r=ni())!==a&&fs()!==a&&is()!==a?(qo=e,(n=r).parentheses=!0,e=n):(Vo=e,e=a),e===a&&(e=Es())),e}function vi(){var e,r,n,o,i;return(e=function(){var e,r,n,o,i,u,s,c;if(e=Vo,(r=bi())!==a)if(fs()!==a){for(n=[],o=Vo,(i=fs())!==a?("?|"===t.substr(Vo,2)?(u="?|",Vo+=2):(u=a,0===Do&&Wo(Wt)),u===a&&("?&"===t.substr(Vo,2)?(u="?&",Vo+=2):(u=a,0===Do&&Wo(Yt)),u===a&&(63===t.charCodeAt(Vo)?(u="?",Vo++):(u=a,0===Do&&Wo(Gt)),u===a&&("#-"===t.substr(Vo,2)?(u="#-",Vo+=2):(u=a,0===Do&&Wo(Zt)),u===a&&("#>>"===t.substr(Vo,3)?(u="#>>",Vo+=3):(u=a,0===Do&&Wo(Xt)),u===a&&("#>"===t.substr(Vo,2)?(u="#>",Vo+=2):(u=a,0===Do&&Wo(Jt)),u===a&&(u=cs())===a&&(u=ss())===a&&("@>"===t.substr(Vo,2)?(u="@>",Vo+=2):(u=a,0===Do&&Wo(Kt)),u===a&&("<@"===t.substr(Vo,2)?(u="<@",Vo+=2):(u=a,0===Do&&Wo(zt))))))))),u!==a&&(s=fs())!==a&&(c=bi())!==a?o=i=[i,u,s,c]:(Vo=o,o=a)):(Vo=o,o=a);o!==a;)n.push(o),o=Vo,(i=fs())!==a?("?|"===t.substr(Vo,2)?(u="?|",Vo+=2):(u=a,0===Do&&Wo(Wt)),u===a&&("?&"===t.substr(Vo,2)?(u="?&",Vo+=2):(u=a,0===Do&&Wo(Yt)),u===a&&(63===t.charCodeAt(Vo)?(u="?",Vo++):(u=a,0===Do&&Wo(Gt)),u===a&&("#-"===t.substr(Vo,2)?(u="#-",Vo+=2):(u=a,0===Do&&Wo(Zt)),u===a&&("#>>"===t.substr(Vo,3)?(u="#>>",Vo+=3):(u=a,0===Do&&Wo(Xt)),u===a&&("#>"===t.substr(Vo,2)?(u="#>",Vo+=2):(u=a,0===Do&&Wo(Jt)),u===a&&(u=cs())===a&&(u=ss())===a&&("@>"===t.substr(Vo,2)?(u="@>",Vo+=2):(u=a,0===Do&&Wo(Kt)),u===a&&("<@"===t.substr(Vo,2)?(u="<@",Vo+=2):(u=a,0===Do&&Wo(zt))))))))),u!==a&&(s=fs())!==a&&(c=bi())!==a?o=i=[i,u,s,c]:(Vo=o,o=a)):(Vo=o,o=a);n!==a?(qo=e,l=r,r=(f=n)&&0!==f.length?qs(l,f):l,e=r):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;var l,f;return e}())===a&&(e=Vo,(r=function(){var e;33===t.charCodeAt(Vo)?(e="!",Vo++):(e=a,0===Do&&Wo(kt));e===a&&(45===t.charCodeAt(Vo)?(e="-",Vo++):(e=a,0===Do&&Wo(Dt)),e===a&&(43===t.charCodeAt(Vo)?(e="+",Vo++):(e=a,0===Do&&Wo(Qt)),e===a&&(126===t.charCodeAt(Vo)?(e="~",Vo++):(e=a,0===Do&&Wo($t)))));return e}())!==a?(n=Vo,(o=fs())!==a&&(i=vi())!==a?n=o=[o,i]:(Vo=n,n=a),n!==a?(qo=e,e=r=Is(r,n[1])):(Vo=e,e=a)):(Vo=e,e=a)),e}function hi(){var t,e,r,n,o,i,u,s,c,l,f,p;return t=Vo,(e=yi())!==a&&(r=fs())!==a&&(n=rs())!==a&&(o=fs())!==a&&(i=gi())!==a?(u=Vo,(s=fs())!==a&&(c=na())!==a?u=s=[s,c]:(Vo=u,u=a),u===a&&(u=null),u!==a?(qo=t,l=e,f=i,p=u,Fs.add(`select::${l}::${f}`),t=e={type:"column_ref",table:l,column:f,collate:p&&p[1]}):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,(e=Ci())!==a?(r=Vo,(n=fs())!==a&&(o=na())!==a?r=n=[n,o]:(Vo=r,r=a),r===a&&(r=null),r!==a?(qo=t,t=e=function(t,e){return Fs.add("select::null::"+t),{type:"column_ref",table:null,column:t,collate:e&&e[1]}}(e,r)):(Vo=t,t=a)):(Vo=t,t=a)),t}function di(){var t,e;return t=Vo,(e=Ti())!==a&&(qo=t,e={type:"default",value:e}),(t=e)===a&&(t=ji()),t}function yi(){var t,e;return t=Vo,(e=Ti())!==a?(qo=Vo,(te(e)?a:void 0)!==a?(qo=t,t=e=e):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,(e=mi())!==a&&(qo=t,e=e),t=e),t}function ji(){var t;return(t=Oi())===a&&(t=wi())===a&&(t=Li()),t}function mi(){var t,e;return t=Vo,(e=Oi())===a&&(e=wi())===a&&(e=Li()),e!==a&&(qo=t,e=e.value),t=e}function Oi(){var e,r,n,o;if(e=Vo,34===t.charCodeAt(Vo)?(r='"',Vo++):(r=a,0===Do&&Wo(ee)),r!==a){if(n=[],re.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(ne)),o!==a)for(;o!==a;)n.push(o),re.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(ne));else n=a;n!==a?(34===t.charCodeAt(Vo)?(o='"',Vo++):(o=a,0===Do&&Wo(ee)),o!==a?(qo=e,e=r={type:"double_quote_string",value:n.join("")}):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;return e}function wi(){var e,r,n,o;if(e=Vo,39===t.charCodeAt(Vo)?(r="'",Vo++):(r=a,0===Do&&Wo(rt)),r!==a){if(n=[],oe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(ae)),o!==a)for(;o!==a;)n.push(o),oe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(ae));else n=a;n!==a?(39===t.charCodeAt(Vo)?(o="'",Vo++):(o=a,0===Do&&Wo(rt)),o!==a?(qo=e,e=r={type:"single_quote_string",value:n.join("")}):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;return e}function Li(){var e,r,n,o;if(e=Vo,96===t.charCodeAt(Vo)?(r="`",Vo++):(r=a,0===Do&&Wo(ie)),r!==a){if(n=[],ue.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(se)),o!==a)for(;o!==a;)n.push(o),ue.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(se));else n=a;n!==a?(96===t.charCodeAt(Vo)?(o="`",Vo++):(o=a,0===Do&&Wo(ie)),o!==a?(qo=e,e=r={type:"backticks_quote_string",value:n.join("")}):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;return e}function gi(){var t,e;return t=Vo,(e=Si())!==a&&(qo=t,e=e),(t=e)===a&&(t=mi()),t}function Ci(){var t,e;return t=Vo,(e=Si())!==a?(qo=Vo,(te(e)?a:void 0)!==a?(qo=t,t=e=e):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=mi()),t}function Si(){var t,e,r,n;if(t=Vo,(e=Ei())!==a){for(r=[],n=Ui();n!==a;)r.push(n),n=Ui();r!==a?(qo=t,t=e=ce(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ti(){var t,e,r,n;if(t=Vo,(e=Ei())!==a){for(r=[],n=Ai();n!==a;)r.push(n),n=Ai();r!==a?(qo=t,t=e=ce(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ei(){var e;return le.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(fe)),e}function Ai(){var e;return pe.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(be)),e}function Ui(){var e;return ve.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(he)),e}function _i(){var e,r,n,o;return e=Vo,r=Vo,58===t.charCodeAt(Vo)?(n=":",Vo++):(n=a,0===Do&&Wo(de)),n!==a&&(o=Ti())!==a?r=n=[n,o]:(Vo=r,r=a),r!==a&&(qo=e,r={type:"param",value:r[1]}),e=r}function xi(){var t,e,r;return t=Vo,lu()!==a&&fs()!==a&&Ji()!==a&&fs()!==a&&(e=Yu())!==a&&fs()!==a&&as()!==a&&fs()!==a?((r=Xa())===a&&(r=null),r!==a&&fs()!==a&&is()!==a?(qo=t,t={type:"on update",keyword:e,parentheses:!0,expr:r}):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,lu()!==a&&fs()!==a&&Ji()!==a&&fs()!==a&&(e=Yu())!==a?(qo=t,t=function(t){return{type:"on update",keyword:t}}(e)):(Vo=t,t=a)),t}function ki(){var e,r,n;return e=Vo,function(){var e,r,n,o;e=Vo,"over"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Er));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}()!==a&&fs()!==a&&as()!==a&&fs()!==a&&ou()!==a&&fs()!==a&&du()!==a&&fs()!==a&&(r=La())!==a&&fs()!==a?((n=Pa())===a&&(n=null),n!==a&&fs()!==a&&is()!==a?(qo=e,e={partitionby:r,orderby:n}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=xi()),e}function Ii(){var e,r,n,o,i;return e=Vo,(r=function(){var e;(e=Ni())===a&&(e=function(){var e,r,n,o;e=Vo,"current_user"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(Wn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CURRENT_USER"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"user"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Rn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="USER"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"session_user"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(Yn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SESSION_USER"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"system_user"===t.substr(Vo,11).toLowerCase()?(r=t.substr(Vo,11),Vo+=11):(r=a,0===Do&&Wo(Gn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SYSTEM_USER"):(Vo=e,e=a)):(Vo=e,e=a);return e}());return e}())!==a&&fs()!==a&&(n=as())!==a&&fs()!==a?((o=Xa())===a&&(o=null),o!==a&&fs()!==a&&is()!==a&&fs()!==a?((i=ki())===a&&(i=null),i!==a?(qo=e,e=r={type:"function",name:{name:[{type:"default",value:r}]},args:o||{type:"expr_list",value:[]},over:i,...ks()}):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=Ni())!==a&&fs()!==a?((n=xi())===a&&(n=null),n!==a?(qo=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},over:n,...ks()}):(Vo=e,e=a)):(Vo=e,e=a),e===a&&(e=Vo,(r=Cs())!==a&&fs()!==a&&(n=as())!==a&&fs()!==a?((o=ni())===a&&(o=null),o!==a&&fs()!==a&&is()!==a&&fs()!==a?((i=ki())===a&&(i=null),i!==a?(qo=e,e=r=function(t,e,r){return e&&"expr_list"!==e.type&&(e={type:"expr_list",value:[e]}),{type:"function",name:t,args:e||{type:"expr_list",value:[]},over:r,...ks()}}(r,o,i)):(Vo=e,e=a)):(Vo=e,e=a)):(Vo=e,e=a))),e}function Ni(){var e;return(e=function(){var e,r,n,o;e=Vo,"current_date"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(Vn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CURRENT_DATE"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Vo,"current_time"===t.substr(Vo,12).toLowerCase()?(r=t.substr(Vo,12),Vo+=12):(r=a,0===Do&&Wo(Hn));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CURRENT_TIME"):(Vo=e,e=a)):(Vo=e,e=a);return e}())===a&&(e=Yu()),e}function Ri(){var e;return(e=qi())===a&&(e=Di())===a&&(e=function(){var e,r;e=Vo,(r=function(){var e,r,n,o;e=Vo,"true"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Ze));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={type:"bool",value:!0});(e=r)===a&&(e=Vo,(r=function(){var e,r,n,o;e=Vo,"false"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Je));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={type:"bool",value:!1}),e=r);return e}())===a&&(e=Vi())===a&&(e=function(){var e,r,n,o,i,u;e=Vo,(r=$u())===a&&(r=Bu())===a&&(r=Wu())===a&&(r=Hu());if(r!==a)if(fs()!==a){if(n=Vo,39===t.charCodeAt(Vo)?(o="'",Vo++):(o=a,0===Do&&Wo(rt)),o!==a){for(i=[],u=Pi();u!==a;)i.push(u),u=Pi();i!==a?(39===t.charCodeAt(Vo)?(u="'",Vo++):(u=a,0===Do&&Wo(rt)),u!==a?n=o=[o,i,u]:(Vo=n,n=a)):(Vo=n,n=a)}else Vo=n,n=a;n!==a?(qo=e,r=ye(r,n),e=r):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;if(e===a)if(e=Vo,(r=$u())===a&&(r=Bu())===a&&(r=Wu())===a&&(r=Hu()),r!==a)if(fs()!==a){if(n=Vo,34===t.charCodeAt(Vo)?(o='"',Vo++):(o=a,0===Do&&Wo(ee)),o!==a){for(i=[],u=Mi();u!==a;)i.push(u),u=Mi();i!==a?(34===t.charCodeAt(Vo)?(u='"',Vo++):(u=a,0===Do&&Wo(ee)),u!==a?n=o=[o,i,u]:(Vo=n,n=a)):(Vo=n,n=a)}else Vo=n,n=a;n!==a?(qo=e,r=ye(r,n),e=r):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;return e}()),e}function Vi(){var e,r;return e=Vo,(r=function(){var e,r,n,o;e=Vo,"null"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Ye));r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a);return e}())!==a&&(qo=e,r={type:"null",value:null}),e=r}function qi(){var e,r,n,o,i;if(e=Vo,r=Vo,39===t.charCodeAt(Vo)?(n="'",Vo++):(n=a,0===Do&&Wo(rt)),n!==a){for(o=[],i=Pi();i!==a;)o.push(i),i=Pi();o!==a?(39===t.charCodeAt(Vo)?(i="'",Vo++):(i=a,0===Do&&Wo(rt)),i!==a?r=n=[n,o,i]:(Vo=r,r=a)):(Vo=r,r=a)}else Vo=r,r=a;if(r!==a&&(qo=e,r={type:"single_quote_string",value:r[1].join("")}),(e=r)===a){if(e=Vo,r=Vo,34===t.charCodeAt(Vo)?(n='"',Vo++):(n=a,0===Do&&Wo(ee)),n!==a){for(o=[],i=Mi();i!==a;)o.push(i),i=Mi();o!==a?(34===t.charCodeAt(Vo)?(i='"',Vo++):(i=a,0===Do&&Wo(ee)),i!==a?r=n=[n,o,i]:(Vo=r,r=a)):(Vo=r,r=a)}else Vo=r,r=a;r!==a&&(qo=e,r=function(t){return{type:"double_quote_string",value:t[1].join("")}}(r)),e=r}return e}function Mi(){var e;return je.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(me)),e===a&&(e=Qi()),e}function Pi(){var e;return Oe.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(we)),e===a&&(e=Qi()),e}function Qi(){var e,r,n,o,i,u,s,c,l,f;return e=Vo,"\\'"===t.substr(Vo,2)?(r="\\'",Vo+=2):(r=a,0===Do&&Wo(Le)),r!==a&&(qo=e,r="\\'"),(e=r)===a&&(e=Vo,'\\"'===t.substr(Vo,2)?(r='\\"',Vo+=2):(r=a,0===Do&&Wo(ge)),r!==a&&(qo=e,r='\\"'),(e=r)===a&&(e=Vo,"\\\\"===t.substr(Vo,2)?(r="\\\\",Vo+=2):(r=a,0===Do&&Wo(Ce)),r!==a&&(qo=e,r="\\\\"),(e=r)===a&&(e=Vo,"\\/"===t.substr(Vo,2)?(r="\\/",Vo+=2):(r=a,0===Do&&Wo(Se)),r!==a&&(qo=e,r="\\/"),(e=r)===a&&(e=Vo,"\\b"===t.substr(Vo,2)?(r="\\b",Vo+=2):(r=a,0===Do&&Wo(Te)),r!==a&&(qo=e,r="\b"),(e=r)===a&&(e=Vo,"\\f"===t.substr(Vo,2)?(r="\\f",Vo+=2):(r=a,0===Do&&Wo(Ee)),r!==a&&(qo=e,r="\f"),(e=r)===a&&(e=Vo,"\\n"===t.substr(Vo,2)?(r="\\n",Vo+=2):(r=a,0===Do&&Wo(Ae)),r!==a&&(qo=e,r="\n"),(e=r)===a&&(e=Vo,"\\r"===t.substr(Vo,2)?(r="\\r",Vo+=2):(r=a,0===Do&&Wo(Ue)),r!==a&&(qo=e,r="\r"),(e=r)===a&&(e=Vo,"\\t"===t.substr(Vo,2)?(r="\\t",Vo+=2):(r=a,0===Do&&Wo(_e)),r!==a&&(qo=e,r="\t"),(e=r)===a&&(e=Vo,"\\u"===t.substr(Vo,2)?(r="\\u",Vo+=2):(r=a,0===Do&&Wo(xe)),r!==a&&(n=Yi())!==a&&(o=Yi())!==a&&(i=Yi())!==a&&(u=Yi())!==a?(qo=e,s=n,c=o,l=i,f=u,e=r=String.fromCharCode(parseInt("0x"+s+c+l+f))):(Vo=e,e=a),e===a&&(e=Vo,92===t.charCodeAt(Vo)?(r="\\",Vo++):(r=a,0===Do&&Wo(ke)),r!==a&&(qo=e,r="\\"),(e=r)===a&&(e=Vo,"''"===t.substr(Vo,2)?(r="''",Vo+=2):(r=a,0===Do&&Wo(Ie)),r!==a&&(qo=e,r="''"),(e=r)===a&&(e=Vo,'""'===t.substr(Vo,2)?(r='""',Vo+=2):(r=a,0===Do&&Wo(Ne)),r!==a&&(qo=e,r='""'),(e=r)===a&&(e=Vo,"``"===t.substr(Vo,2)?(r="``",Vo+=2):(r=a,0===Do&&Wo(Re)),r!==a&&(qo=e,r="``"),e=r))))))))))))),e}function Di(){var t,e,r;return t=Vo,(e=function(){var t,e,r,n;t=Vo,(e=Fi())!==a&&(r=Bi())!==a&&(n=Hi())!==a?(qo=t,t=e={type:"bigint",value:e+r+n}):(Vo=t,t=a);t===a&&(t=Vo,(e=Fi())!==a&&(r=Bi())!==a?(qo=t,e=function(t,e){const r=t+e;return Rs(t)?{type:"bigint",value:r}:parseFloat(r)}(e,r),t=e):(Vo=t,t=a),t===a&&(t=Vo,(e=Fi())!==a&&(r=Hi())!==a?(qo=t,e=function(t,e){return{type:"bigint",value:t+e}}(e,r),t=e):(Vo=t,t=a),t===a&&(t=Vo,(e=Fi())!==a&&(qo=t,e=function(t){return Rs(t)?{type:"bigint",value:t}:parseFloat(t)}(e)),t=e)));return t}())!==a&&(qo=t,e=(r=e)&&"bigint"===r.type?r:{type:"number",value:r}),t=e}function Fi(){var e,r,n;return(e=$i())===a&&(e=Wi())===a&&(e=Vo,45===t.charCodeAt(Vo)?(r="-",Vo++):(r=a,0===Do&&Wo(Dt)),r===a&&(43===t.charCodeAt(Vo)?(r="+",Vo++):(r=a,0===Do&&Wo(Qt))),r!==a&&(n=$i())!==a?(qo=e,e=r=r+n):(Vo=e,e=a),e===a&&(e=Vo,45===t.charCodeAt(Vo)?(r="-",Vo++):(r=a,0===Do&&Wo(Dt)),r===a&&(43===t.charCodeAt(Vo)?(r="+",Vo++):(r=a,0===Do&&Wo(Qt))),r!==a&&(n=Wi())!==a?(qo=e,e=r=function(t,e){return t+e}(r,n)):(Vo=e,e=a))),e}function Bi(){var e,r,n;return e=Vo,46===t.charCodeAt(Vo)?(r=".",Vo++):(r=a,0===Do&&Wo(Me)),r!==a&&(n=$i())!==a?(qo=e,e=r="."+n):(Vo=e,e=a),e}function Hi(){var e,r,n;return e=Vo,(r=function(){var e,r,n;e=Vo,Be.test(t.charAt(Vo))?(r=t.charAt(Vo),Vo++):(r=a,0===Do&&Wo(He));r!==a?($e.test(t.charAt(Vo))?(n=t.charAt(Vo),Vo++):(n=a,0===Do&&Wo(We)),n===a&&(n=null),n!==a?(qo=e,e=r=r+(null!==(o=n)?o:"")):(Vo=e,e=a)):(Vo=e,e=a);var o;return e}())!==a&&(n=$i())!==a?(qo=e,e=r=r+n):(Vo=e,e=a),e}function $i(){var t,e,r;if(t=Vo,e=[],(r=Wi())!==a)for(;r!==a;)e.push(r),r=Wi();else e=a;return e!==a&&(qo=t,e=e.join("")),t=e}function Wi(){var e;return Pe.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(Qe)),e}function Yi(){var e;return De.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(Fe)),e}function Gi(){var e,r,n,o;return e=Vo,"default"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(j)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function Zi(){var e,r,n,o;return e=Vo,"to"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Xe)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function Xi(){var e,r,n,o;return e=Vo,"drop"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Ke)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DROP"):(Vo=e,e=a)):(Vo=e,e=a),e}function Ji(){var e,r,n,o;return e=Vo,"update"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(rr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function Ki(){var e,r,n,o;return e=Vo,"create"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(nr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function zi(){var e,r,n,o;return e=Vo,"temporary"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(or)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function tu(){var e,r,n,o;return e=Vo,"delete"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(ar)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function eu(){var e,r,n,o;return e=Vo,"replace"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(sr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function ru(){var e,r,n,o;return e=Vo,"rename"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(cr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function nu(){var e,r,n,o;return e=Vo,"ignore"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(lr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function ou(){var e,r,n,o;return e=Vo,"partition"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(fr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="PARTITION"):(Vo=e,e=a)):(Vo=e,e=a),e}function au(){var e,r,n,o;return e=Vo,"into"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(pr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function iu(){var e,r,n,o;return e=Vo,"set"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(W)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SET"):(Vo=e,e=a)):(Vo=e,e=a),e}function uu(){var e,r,n,o;return e=Vo,"as"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(hr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function su(){var e,r,n,o;return e=Vo,"table"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(dr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TABLE"):(Vo=e,e=a)):(Vo=e,e=a),e}function cu(){var e,r,n,o;return e=Vo,"tables"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(yr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TABLES"):(Vo=e,e=a)):(Vo=e,e=a),e}function lu(){var e,r,n,o;return e=Vo,"on"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Or)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function fu(){var e,r,n,o;return e=Vo,"join"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Sr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function pu(){var e,r,n,o;return e=Vo,"outer"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Tr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function bu(){var e,r,n,o;return e=Vo,"values"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(kr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function vu(){var e,r,n,o;return e=Vo,"using"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Ir)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function hu(){var e,r,n,o;return e=Vo,"with"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(ht)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function du(){var e,r,n,o;return e=Vo,"by"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Vr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function yu(){var e,r,n,o;return e=Vo,"fetch"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(Pr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="FETCH"):(Vo=e,e=a)):(Vo=e,e=a),e}function ju(){var e,r,n,o;return e=Vo,"all"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(Br)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="ALL"):(Vo=e,e=a)):(Vo=e,e=a),e}function mu(){var e,r,n,o;return e=Vo,"distinct"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(Hr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DISTINCT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Ou(){var e,r,n,o;return e=Vo,"between"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo($r)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="BETWEEN"):(Vo=e,e=a)):(Vo=e,e=a),e}function wu(){var e,r,n,o;return e=Vo,"in"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Wr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="IN"):(Vo=e,e=a)):(Vo=e,e=a),e}function Lu(){var e,r,n,o;return e=Vo,"is"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Yr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="IS"):(Vo=e,e=a)):(Vo=e,e=a),e}function gu(){var e,r,n,o;return e=Vo,"like"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Gr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="LIKE"):(Vo=e,e=a)):(Vo=e,e=a),e}function Cu(){var e,r,n,o;return e=Vo,"exists"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Zr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="EXISTS"):(Vo=e,e=a)):(Vo=e,e=a),e}function Su(){var e,r,n,o;return e=Vo,"not"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(I)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="NOT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Tu(){var e,r,n,o;return e=Vo,"and"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(Xr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="AND"):(Vo=e,e=a)):(Vo=e,e=a),e}function Eu(){var e,r,n,o;return e=Vo,"or"===t.substr(Vo,2).toLowerCase()?(r=t.substr(Vo,2),Vo+=2):(r=a,0===Do&&Wo(Jr)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="OR"):(Vo=e,e=a)):(Vo=e,e=a),e}function Au(){var e,r,n,o;return e=Vo,"case"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(on)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function Uu(){var e,r,n,o;return e=Vo,"end"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(cn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?e=r=[r,n]:(Vo=e,e=a)):(Vo=e,e=a),e}function _u(){var e,r,n,o;return e=Vo,"cast"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(ln)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CAST"):(Vo=e,e=a)):(Vo=e,e=a),e}function xu(){var e,r,n,o;return e=Vo,"char"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(fn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CHAR"):(Vo=e,e=a)):(Vo=e,e=a),e}function ku(){var e,r,n,o;return e=Vo,"varchar"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(pn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="VARCHAR"):(Vo=e,e=a)):(Vo=e,e=a),e}function Iu(){var e,r,n,o;return e=Vo,"numeric"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(bn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="NUMERIC"):(Vo=e,e=a)):(Vo=e,e=a),e}function Nu(){var e,r,n,o;return e=Vo,"decimal"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(vn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DECIMAL"):(Vo=e,e=a)):(Vo=e,e=a),e}function Ru(){var e,r,n,o;return e=Vo,"unsigned"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(dn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="UNSIGNED"):(Vo=e,e=a)):(Vo=e,e=a),e}function Vu(){var e,r,n,o;return e=Vo,"int"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(yn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="INT"):(Vo=e,e=a)):(Vo=e,e=a),e}function qu(){var e,r,n,o;return e=Vo,"integer"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(mn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="INTEGER"):(Vo=e,e=a)):(Vo=e,e=a),e}function Mu(){var e,r,n,o;return e=Vo,"smallint"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(wn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="SMALLINT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Pu(){var e,r,n,o;return e=Vo,"tinyint"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(Ln)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TINYINT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Qu(){var e,r,n,o;return e=Vo,"bigint"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(En)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="BIGINT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Du(){var e,r,n,o;return e=Vo,"float"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(An)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="FLOAT"):(Vo=e,e=a)):(Vo=e,e=a),e}function Fu(){var e,r,n,o;return e=Vo,"double"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(Un)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DOUBLE"):(Vo=e,e=a)):(Vo=e,e=a),e}function Bu(){var e,r,n,o;return e=Vo,"date"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(_n)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DATE"):(Vo=e,e=a)):(Vo=e,e=a),e}function Hu(){var e,r,n,o;return e=Vo,"datetime"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(xn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="DATETIME"):(Vo=e,e=a)):(Vo=e,e=a),e}function $u(){var e,r,n,o;return e=Vo,"time"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(kn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TIME"):(Vo=e,e=a)):(Vo=e,e=a),e}function Wu(){var e,r,n,o;return e=Vo,"timestamp"===t.substr(Vo,9).toLowerCase()?(r=t.substr(Vo,9),Vo+=9):(r=a,0===Do&&Wo(In)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TIMESTAMP"):(Vo=e,e=a)):(Vo=e,e=a),e}function Yu(){var e,r,n,o;return e=Vo,"current_timestamp"===t.substr(Vo,17).toLowerCase()?(r=t.substr(Vo,17),Vo+=17):(r=a,0===Do&&Wo($n)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CURRENT_TIMESTAMP"):(Vo=e,e=a)):(Vo=e,e=a),e}function Gu(){var e;return(e=function(){var e;return"@@"===t.substr(Vo,2)?(e="@@",Vo+=2):(e=a,0===Do&&Wo(to)),e}())===a&&(e=function(){var e;return 64===t.charCodeAt(Vo)?(e="@",Vo++):(e=a,0===Do&&Wo(zn)),e}())===a&&(e=function(){var e;return 36===t.charCodeAt(Vo)?(e="$",Vo++):(e=a,0===Do&&Wo(eo)),e}()),e}function Zu(){var e;return 61===t.charCodeAt(Vo)?(e="=",Vo++):(e=a,0===Do&&Wo(Ut)),e}function Xu(){var e,r,n,o;return e=Vo,"add"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(ao)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="ADD"):(Vo=e,e=a)):(Vo=e,e=a),e}function Ju(){var e,r,n,o;return e=Vo,"column"===t.substr(Vo,6).toLowerCase()?(r=t.substr(Vo,6),Vo+=6):(r=a,0===Do&&Wo(io)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="COLUMN"):(Vo=e,e=a)):(Vo=e,e=a),e}function Ku(){var e,r,n,o;return e=Vo,"index"===t.substr(Vo,5).toLowerCase()?(r=t.substr(Vo,5),Vo+=5):(r=a,0===Do&&Wo(uo)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="INDEX"):(Vo=e,e=a)):(Vo=e,e=a),e}function zu(){var e,r,n,o;return e=Vo,"key"===t.substr(Vo,3).toLowerCase()?(r=t.substr(Vo,3),Vo+=3):(r=a,0===Do&&Wo(b)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="KEY"):(Vo=e,e=a)):(Vo=e,e=a),e}function ts(){var e,r,n,o;return e=Vo,"comment"===t.substr(Vo,7).toLowerCase()?(r=t.substr(Vo,7),Vo+=7):(r=a,0===Do&&Wo(lo)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="COMMENT"):(Vo=e,e=a)):(Vo=e,e=a),e}function es(){var e,r,n,o;return e=Vo,"constraint"===t.substr(Vo,10).toLowerCase()?(r=t.substr(Vo,10),Vo+=10):(r=a,0===Do&&Wo(fo)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="CONSTRAINT"):(Vo=e,e=a)):(Vo=e,e=a),e}function rs(){var e;return 46===t.charCodeAt(Vo)?(e=".",Vo++):(e=a,0===Do&&Wo(Me)),e}function ns(){var e;return 44===t.charCodeAt(Vo)?(e=",",Vo++):(e=a,0===Do&&Wo(Oo)),e}function os(){var e;return 42===t.charCodeAt(Vo)?(e="*",Vo++):(e=a,0===Do&&Wo(Ft)),e}function as(){var e;return 40===t.charCodeAt(Vo)?(e="(",Vo++):(e=a,0===Do&&Wo(ft)),e}function is(){var e;return 41===t.charCodeAt(Vo)?(e=")",Vo++):(e=a,0===Do&&Wo(pt)),e}function us(){var e;return 59===t.charCodeAt(Vo)?(e=";",Vo++):(e=a,0===Do&&Wo(go)),e}function ss(){var e;return"->"===t.substr(Vo,2)?(e="->",Vo+=2):(e=a,0===Do&&Wo(Co)),e}function cs(){var e;return"->>"===t.substr(Vo,3)?(e="->>",Vo+=3):(e=a,0===Do&&Wo(So)),e}function ls(){var e;return(e=function(){var e;return"||"===t.substr(Vo,2)?(e="||",Vo+=2):(e=a,0===Do&&Wo(To)),e}())===a&&(e=function(){var e;return"&&"===t.substr(Vo,2)?(e="&&",Vo+=2):(e=a,0===Do&&Wo(Eo)),e}()),e}function fs(){var t,e;for(t=[],(e=ds())===a&&(e=bs());e!==a;)t.push(e),(e=ds())===a&&(e=bs());return t}function ps(){var t,e;if(t=[],(e=ds())===a&&(e=bs()),e!==a)for(;e!==a;)t.push(e),(e=ds())===a&&(e=bs());else t=a;return t}function bs(){var e;return(e=function(){var e,r,n,o,i,u;e=Vo,"/*"===t.substr(Vo,2)?(r="/*",Vo+=2):(r=a,0===Do&&Wo(Ao));if(r!==a){for(n=[],o=Vo,i=Vo,Do++,"*/"===t.substr(Vo,2)?(u="*/",Vo+=2):(u=a,0===Do&&Wo(Uo)),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,i=Vo,Do++,"*/"===t.substr(Vo,2)?(u="*/",Vo+=2):(u=a,0===Do&&Wo(Uo)),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);n!==a?("*/"===t.substr(Vo,2)?(o="*/",Vo+=2):(o=a,0===Do&&Wo(Uo)),o!==a?e=r=[r,n,o]:(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;return e}())===a&&(e=function(){var e,r,n,o,i,u;e=Vo,"--"===t.substr(Vo,2)?(r="--",Vo+=2):(r=a,0===Do&&Wo(_o));if(r!==a){for(n=[],o=Vo,i=Vo,Do++,u=ys(),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,i=Vo,Do++,u=ys(),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);n!==a?e=r=[r,n]:(Vo=e,e=a)}else Vo=e,e=a;return e}())===a&&(e=function(){var e,r,n,o,i,u;e=Vo,35===t.charCodeAt(Vo)?(r="#",Vo++):(r=a,0===Do&&Wo(xo));if(r!==a){for(n=[],o=Vo,i=Vo,Do++,u=ys(),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);o!==a;)n.push(o),o=Vo,i=Vo,Do++,u=ys(),Do--,u===a?i=void 0:(Vo=i,i=a),i!==a&&(u=hs())!==a?o=i=[i,u]:(Vo=o,o=a);n!==a?e=r=[r,n]:(Vo=e,e=a)}else Vo=e,e=a;return e}()),e}function vs(){var t,e,r,n,o,i,u;return t=Vo,(e=ts())!==a&&fs()!==a?((r=Zu())===a&&(r=null),r!==a&&fs()!==a&&(n=qi())!==a?(qo=t,i=r,u=n,t=e={type:(o=e).toLowerCase(),keyword:o.toLowerCase(),symbol:i,value:u}):(Vo=t,t=a)):(Vo=t,t=a),t}function hs(){var e;return t.length>Vo?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(ko)),e}function ds(){var e;return Io.test(t.charAt(Vo))?(e=t.charAt(Vo),Vo++):(e=a,0===Do&&Wo(No)),e}function ys(){var e,r;if((e=function(){var e,r;e=Vo,Do++,t.length>Vo?(r=t.charAt(Vo),Vo++):(r=a,0===Do&&Wo(ko));Do--,r===a?e=void 0:(Vo=e,e=a);return e}())===a)if(e=[],Ve.test(t.charAt(Vo))?(r=t.charAt(Vo),Vo++):(r=a,0===Do&&Wo(qe)),r!==a)for(;r!==a;)e.push(r),Ve.test(t.charAt(Vo))?(r=t.charAt(Vo),Vo++):(r=a,0===Do&&Wo(qe));else e=a;return e}function js(){var e,r;return e=Vo,qo=Vo,Qs=[],(!0?void 0:a)!==a&&fs()!==a?((r=ms())===a&&(r=function(){var e,r;e=Vo,function(){var e;return"return"===t.substr(Vo,6).toLowerCase()?(e=t.substr(Vo,6),Vo+=6):(e=a,0===Do&&Wo(ro)),e}()!==a&&fs()!==a&&(r=Os())!==a?(qo=e,e={type:"return",expr:r}):(Vo=e,e=a);return e}()),r!==a?(qo=e,e={stmt:r,vars:Qs}):(Vo=e,e=a)):(Vo=e,e=a),e}function ms(){var e,r,n,o;return e=Vo,(r=Es())===a&&(r=As()),r!==a&&fs()!==a?((n=function(){var e;return":="===t.substr(Vo,2)?(e=":=",Vo+=2):(e=a,0===Do&&Wo(no)),e}())===a&&(n=Zu()),n!==a&&fs()!==a&&(o=Os())!==a?(qo=e,e=r={type:"assign",left:r,symbol:n,right:o}):(Vo=e,e=a)):(Vo=e,e=a),e}function Os(){var e;return(e=da())===a&&(e=function(){var t,e,r,n,o;t=Vo,(e=Es())!==a&&fs()!==a&&(r=Ia())!==a&&fs()!==a&&(n=Es())!==a&&fs()!==a&&(o=Va())!==a?(qo=t,t=e={type:"join",ltable:e,rtable:n,op:r,on:o}):(Vo=t,t=a);return t}())===a&&(e=ws())===a&&(e=function(){var e,r;e=Vo,function(){var e;return 91===t.charCodeAt(Vo)?(e="[",Vo++):(e=a,0===Do&&Wo(wo)),e}()!==a&&fs()!==a&&(r=Ts())!==a&&fs()!==a&&function(){var e;return 93===t.charCodeAt(Vo)?(e="]",Vo++):(e=a,0===Do&&Wo(Lo)),e}()!==a?(qo=e,e={type:"array",value:r}):(Vo=e,e=a);return e}()),e}function ws(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=Ls())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=li())!==a&&(u=fs())!==a&&(s=Ls())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=li())!==a&&(u=fs())!==a&&(s=Ls())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=xt(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Ls(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=gs())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=pi())!==a&&(u=fs())!==a&&(s=gs())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=pi())!==a&&(u=fs())!==a&&(s=gs())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=xt(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function gs(){var t,e,r;return(t=Ri())===a&&(t=Es())===a&&(t=Ss())===a&&(t=_i())===a&&(t=Vo,as()!==a&&fs()!==a&&(e=ws())!==a&&fs()!==a&&is()!==a?(qo=t,(r=e).parentheses=!0,t=r):(Vo=t,t=a)),t}function Cs(){var t,e,r,n,o,i,u;return t=Vo,(e=di())!==a?(r=Vo,(n=fs())!==a&&(o=rs())!==a&&(i=fs())!==a&&(u=di())!==a?r=n=[n,o,i,u]:(Vo=r,r=a),r===a&&(r=null),r!==a?(qo=t,t=e=function(t,e){const r={name:[t]};return null!==e&&(r.schema=t,r.name=[e[3]]),r}(e,r)):(Vo=t,t=a)):(Vo=t,t=a),t}function Ss(){var t,e,r;return t=Vo,(e=Cs())!==a&&fs()!==a&&as()!==a&&fs()!==a?((r=Ts())===a&&(r=null),r!==a&&fs()!==a&&is()!==a?(qo=t,t=e={type:"function",name:e,args:{type:"expr_list",value:r},...ks()}):(Vo=t,t=a)):(Vo=t,t=a),t===a&&(t=Vo,(e=Cs())!==a&&(qo=t,e=function(t){return{type:"function",name:t,args:null,...ks()}}(e)),t=e),t}function Ts(){var t,e,r,n,o,i,u,s;if(t=Vo,(e=gs())!==a){for(r=[],n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=gs())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);n!==a;)r.push(n),n=Vo,(o=fs())!==a&&(i=ns())!==a&&(u=fs())!==a&&(s=gs())!==a?n=o=[o,i,u,s]:(Vo=n,n=a);r!==a?(qo=t,t=e=l(e,r)):(Vo=t,t=a)}else Vo=t,t=a;return t}function Es(){var t,e,r,n,o;return t=Vo,(e=Gu())!==a&&(r=As())!==a?(qo=t,n=e,o=r,t=e={type:"var",...o,prefix:n}):(Vo=t,t=a),t}function As(){var e,r,n,o,i;return e=Vo,(r=Ti())!==a&&(n=function(){var e,r,n,o,i;e=Vo,r=[],n=Vo,46===t.charCodeAt(Vo)?(o=".",Vo++):(o=a,0===Do&&Wo(Me));o!==a&&(i=Ti())!==a?n=o=[o,i]:(Vo=n,n=a);for(;n!==a;)r.push(n),n=Vo,46===t.charCodeAt(Vo)?(o=".",Vo++):(o=a,0===Do&&Wo(Me)),o!==a&&(i=Ti())!==a?n=o=[o,i]:(Vo=n,n=a);r!==a&&(qo=e,r=function(t){const e=[];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(r));return e=r}())!==a?(qo=e,o=r,i=n,Qs.push(o),e=r={type:"var",name:o,members:i,prefix:null}):(Vo=e,e=a),e===a&&(e=Vo,(r=Di())!==a&&(qo=e,r={type:"var",name:r.value,members:[],quoted:null,prefix:null}),e=r),e}function Us(){var e;return(e=function(){var e,r,n,o;e=Vo,(r=xu())===a&&(r=ku());if(r!==a)if(fs()!==a)if(as()!==a)if(fs()!==a){if(n=[],Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe)),o!==a)for(;o!==a;)n.push(o),Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe));else n=a;n!==a&&(o=fs())!==a&&is()!==a?(qo=e,r={dataType:r,length:parseInt(n.join(""),10),parentheses:!0},e=r):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;e===a&&(e=Vo,(r=xu())!==a&&(qo=e,r=Ro(r)),(e=r)===a&&(e=Vo,(r=ku())!==a&&(qo=e,r=Ro(r)),e=r));return e}())===a&&(e=function(){var e,r,n,o,i,u,s,c,l,f,p,b;e=Vo,(r=Iu())===a&&(r=Nu())===a&&(r=Vu())===a&&(r=qu())===a&&(r=Mu())===a&&(r=Pu())===a&&(r=Qu())===a&&(r=Du())===a&&(r=Fu());if(r!==a)if((n=fs())!==a)if((o=as())!==a)if((i=fs())!==a){if(u=[],Pe.test(t.charAt(Vo))?(s=t.charAt(Vo),Vo++):(s=a,0===Do&&Wo(Qe)),s!==a)for(;s!==a;)u.push(s),Pe.test(t.charAt(Vo))?(s=t.charAt(Vo),Vo++):(s=a,0===Do&&Wo(Qe));else u=a;if(u!==a)if((s=fs())!==a){if(c=Vo,(l=ns())!==a)if((f=fs())!==a){if(p=[],Pe.test(t.charAt(Vo))?(b=t.charAt(Vo),Vo++):(b=a,0===Do&&Wo(Qe)),b!==a)for(;b!==a;)p.push(b),Pe.test(t.charAt(Vo))?(b=t.charAt(Vo),Vo++):(b=a,0===Do&&Wo(Qe));else p=a;p!==a?c=l=[l,f,p]:(Vo=c,c=a)}else Vo=c,c=a;else Vo=c,c=a;c===a&&(c=null),c!==a&&(l=fs())!==a&&(f=is())!==a&&(p=fs())!==a?((b=_s())===a&&(b=null),b!==a?(qo=e,v=c,h=b,r={dataType:r,length:parseInt(u.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:h},e=r):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a}else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;var v,h;if(e===a){if(e=Vo,(r=Iu())===a&&(r=Nu())===a&&(r=Vu())===a&&(r=qu())===a&&(r=Mu())===a&&(r=Pu())===a&&(r=Qu())===a&&(r=Du())===a&&(r=Fu()),r!==a){if(n=[],Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe)),o!==a)for(;o!==a;)n.push(o),Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe));else n=a;n!==a&&(o=fs())!==a?((i=_s())===a&&(i=null),i!==a?(qo=e,r=function(t,e,r){return{dataType:t,length:parseInt(e.join(""),10),suffix:r}}(r,n,i),e=r):(Vo=e,e=a)):(Vo=e,e=a)}else Vo=e,e=a;e===a&&(e=Vo,(r=Iu())===a&&(r=Nu())===a&&(r=Vu())===a&&(r=qu())===a&&(r=Mu())===a&&(r=Pu())===a&&(r=Qu())===a&&(r=Du())===a&&(r=Fu()),r!==a&&(n=fs())!==a?((o=_s())===a&&(o=null),o!==a&&(i=fs())!==a?(qo=e,r=function(t,e){return{dataType:t,suffix:e}}(r,o),e=r):(Vo=e,e=a)):(Vo=e,e=a))}return e}())===a&&(e=function(){var e,r,n,o;e=Vo,(r=Bu())===a&&(r=Hu())===a&&(r=$u())===a&&(r=Wu());if(r!==a)if(fs()!==a)if(as()!==a)if(fs()!==a){if(n=[],Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe)),o!==a)for(;o!==a;)n.push(o),Pe.test(t.charAt(Vo))?(o=t.charAt(Vo),Vo++):(o=a,0===Do&&Wo(Qe));else n=a;n!==a&&(o=fs())!==a&&is()!==a?(qo=e,r={dataType:r,length:parseInt(n.join(""),10),parentheses:!0},e=r):(Vo=e,e=a)}else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;else Vo=e,e=a;e===a&&(e=Vo,(r=Bu())===a&&(r=Hu())===a&&(r=$u())===a&&(r=Wu()),r!==a&&(qo=e,r=Ro(r)),e=r);return e}())===a&&(e=function(){var e,r;e=Vo,(r=function(){var e,r,n,o;return e=Vo,"json"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(On)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="JSON"):(Vo=e,e=a)):(Vo=e,e=a),e}())!==a&&(qo=e,r=Ro(r));return e=r}())===a&&(e=function(){var e,r;e=Vo,(r=function(){var e,r,n,o;return e=Vo,"tinytext"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(gn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TINYTEXT"):(Vo=e,e=a)):(Vo=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Vo,"text"===t.substr(Vo,4).toLowerCase()?(r=t.substr(Vo,4),Vo+=4):(r=a,0===Do&&Wo(Cn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="TEXT"):(Vo=e,e=a)):(Vo=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Vo,"mediumtext"===t.substr(Vo,10).toLowerCase()?(r=t.substr(Vo,10),Vo+=10):(r=a,0===Do&&Wo(Sn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="MEDIUMTEXT"):(Vo=e,e=a)):(Vo=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Vo,"longtext"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(Tn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="LONGTEXT"):(Vo=e,e=a)):(Vo=e,e=a),e}());r!==a&&(qo=e,r={dataType:r});return e=r}()),e}function _s(){var e,r,n;return e=Vo,(r=Ru())===a&&(r=null),r!==a&&fs()!==a?((n=function(){var e,r,n,o;return e=Vo,"zerofill"===t.substr(Vo,8).toLowerCase()?(r=t.substr(Vo,8),Vo+=8):(r=a,0===Do&&Wo(jn)),r!==a?(n=Vo,Do++,o=Ei(),Do--,o===a?n=void 0:(Vo=n,n=a),n!==a?(qo=e,e=r="ZEROFILL"):(Vo=e,e=a)):(Vo=e,e=a),e}())===a&&(n=null),n!==a?(qo=e,e=r=function(t,e){const r=[];return t&&r.push(t),e&&r.push(e),r}(r,n)):(Vo=e,e=a)):(Vo=e,e=a),e}const xs={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXCEPT:!0,EXPLAIN:!0,FALSE:!0,FETCH:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,MINUS:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,TYPE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function ks(){return e.includeLocations?{loc:$o(qo,Vo)}:{}}function Is(t,e){return{type:"unary_expr",operator:t,expr:e}}function Ns(t,e,r){return{type:"binary_expr",operator:t,left:e,right:r}}function Rs(t){const e=n(Number.MAX_SAFE_INTEGER);return!(n(t)<e)}function Vs(t,e,r=3){const n=[t];for(let t=0;t<e.length;t++)delete e[t][r].tableList,delete e[t][r].columnList,n.push(e[t][r]);return n}function qs(t,e){let r=t;for(let t=0;t<e.length;t++)r=Ns(e[t][1],r,e[t][3]);return r}function Ms(t){const e=Bs[t];return e||(t||null)}function Ps(t){const e=new Set;for(let r of t.keys()){const t=r.split("::");if(!t){e.add(r);break}t&&t[1]&&(t[1]=Ms(t[1])),e.add(t.join("::"))}return Array.from(e)}let Qs=[];const Ds=new Set,Fs=new Set,Bs={};if((r=u())!==a&&Vo===t.length)return r;throw r!==a&&Vo<t.length&&Wo({type:"end"}),Yo(Qo,Po<t.length?t.charAt(Po):null,Po<t.length?$o(Po,Po+1):$o(Po,Po))}}},function(t,e,r){t.exports=r(27)},function(t,e,r){"use strict";r.r(e),function(t){var n=r(24);r.d(e,"Parser",(function(){return n.a}));var o=r(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,r(28))},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){(function(t){var n,o=function(t){"use strict";var e=1e7,r=9007199254740992,n=f(r),a="function"==typeof BigInt;function i(t,e,r,n){return void 0===t?i[0]:void 0!==e&&(10!=+e||r)?Q(t,e,r,n):$(t)}function u(t,e){this.value=t,this.sign=e,this.isSmall=!1}function s(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-r<t&&t<r}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var r=t.length;if(r<4&&A(t,n)<0)switch(r){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*e;default:return t[0]+(t[1]+t[2]*e)*e}return t}function b(t){for(var e=t.length;0===t[--e];);t.length=e+1}function v(t){for(var e=new Array(t),r=-1;++r<t;)e[r]=0;return e}function h(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,r){var n,o,a=t.length,i=r.length,u=new Array(a),s=0,c=e;for(o=0;o<i;o++)s=(n=t[o]+r[o]+s)>=c?1:0,u[o]=n-s*c;for(;o<a;)s=(n=t[o]+s)===c?1:0,u[o++]=n-s*c;return s>0&&u.push(s),u}function y(t,e){return t.length>=e.length?d(t,e):d(e,t)}function j(t,r){var n,o,a=t.length,i=new Array(a),u=e;for(o=0;o<a;o++)n=t[o]-u+r,r=Math.floor(n/u),i[o]=n-r*u,r+=1;for(;r>0;)i[o++]=r%u,r=Math.floor(r/u);return i}function m(t,e){var r,n,o=t.length,a=e.length,i=new Array(o),u=0;for(r=0;r<a;r++)(n=t[r]-u-e[r])<0?(n+=1e7,u=1):u=0,i[r]=n;for(r=a;r<o;r++){if(!((n=t[r]-u)<0)){i[r++]=n;break}n+=1e7,i[r]=n}for(;r<o;r++)i[r]=t[r];return b(i),i}function O(t,e,r){var n,o,a=t.length,i=new Array(a),c=-e;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,i[n]=o<0?o+1e7:o;return"number"==typeof(i=p(i))?(r&&(i=-i),new s(i)):new u(i,r)}function w(t,e){var r,n,o,a,i=t.length,u=e.length,s=v(i+u);for(o=0;o<i;++o){a=t[o];for(var c=0;c<u;++c)r=a*e[c]+s[o+c],n=Math.floor(r/1e7),s[o+c]=r-1e7*n,s[o+c+1]+=n}return b(s),s}function L(t,r){var n,o,a=t.length,i=new Array(a),u=e,s=0;for(o=0;o<a;o++)n=t[o]*r+s,s=Math.floor(n/u),i[o]=n-s*u;for(;s>0;)i[o++]=s%u,s=Math.floor(s/u);return i}function g(t,e){for(var r=[];e-- >0;)r.push(0);return r.concat(t)}function C(t,r,n){return new u(t<e?L(r,t):w(r,f(t)),n)}function S(t){var e,r,n,o,a=t.length,i=v(a+a);for(n=0;n<a;n++){r=0-(o=t[n])*o;for(var u=n;u<a;u++)e=o*t[u]*2+i[n+u]+r,r=Math.floor(e/1e7),i[n+u]=e-1e7*r;i[n+a]=r}return b(i),i}function T(t,e){var r,n,o,a,i=t.length,u=v(i);for(o=0,r=i-1;r>=0;--r)o=(a=1e7*o+t[r])-(n=h(a/e))*e,u[r]=0|n;return[u,0|o]}function E(t,r){var n,o=$(r);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,y=o.value;if(0===y)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new s(h(d/y)),new s(d%y)]:[i[0],t];if(o.isSmall){if(1===y)return[t,i[0]];if(-1==y)return[t.negate(),i[0]];var j=Math.abs(y);if(j<e){l=p((n=T(d,j))[0]);var O=n[1];return t.sign&&(O=-O),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new s(l),new s(O)]):[new u(l,t.sign!==o.sign),new s(O)]}y=f(j)}var w=A(d,y);if(-1===w)return[i[0],t];if(0===w)return[i[t.sign===o.sign?1:-1],i[0]];l=(n=d.length+y.length<=200?function(t,r){var n,o,a,i,u,s,c,l=t.length,f=r.length,b=e,h=v(r.length),d=r[f-1],y=Math.ceil(b/(2*d)),j=L(t,y),m=L(r,y);for(j.length<=l&&j.push(0),m.push(0),d=m[f-1],o=l-f;o>=0;o--){for(n=b-1,j[o+f]!==d&&(n=Math.floor((j[o+f]*b+j[o+f-1])/d)),a=0,i=0,s=m.length,u=0;u<s;u++)a+=n*m[u],c=Math.floor(a/b),i+=j[o+u]-(a-c*b),a=c,i<0?(j[o+u]=i+b,i=-1):(j[o+u]=i,i=0);for(;0!==i;){for(n-=1,a=0,u=0;u<s;u++)(a+=j[o+u]-b+m[u])<0?(j[o+u]=a+b,a=0):(j[o+u]=a,a=1);i+=a}h[o]=n}return j=T(j,y)[0],[p(h),p(j)]}(d,y):function(t,e){for(var r,n,o,a,i,u=t.length,s=e.length,c=[],l=[];u;)if(l.unshift(t[--u]),b(l),A(l,e)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*e[s-1]+e[s-2],n>s&&(o=1e7*(o+1)),r=Math.ceil(o/a);do{if(A(i=L(e,r),l)<=0)break;r--}while(r);c.push(r),l=m(l,i)}return c.reverse(),[p(c),p(l)]}(d,y))[0];var g=t.sign!==o.sign,C=n[1],S=t.sign;return"number"==typeof l?(g&&(l=-l),l=new s(l)):l=new u(l,g),"number"==typeof C?(S&&(C=-C),C=new s(C)):C=new u(C,S),[l,C]}function A(t,e){if(t.length!==e.length)return t.length>e.length?1:-1;for(var r=t.length-1;r>=0;r--)if(t[r]!==e[r])return t[r]>e[r]?1:-1;return 0}function U(t){var e=t.abs();return!e.isUnit()&&(!!(e.equals(2)||e.equals(3)||e.equals(5))||!(e.isEven()||e.isDivisibleBy(3)||e.isDivisibleBy(5))&&(!!e.lesser(49)||void 0))}function _(t,e){for(var r,n,a,i=t.prev(),u=i,s=0;u.isEven();)u=u.divide(2),s++;t:for(n=0;n<e.length;n++)if(!t.lesser(e[n])&&!(a=o(e[n]).modPow(u,t)).isUnit()&&!a.equals(i)){for(r=s-1;0!=r;r--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(i))continue t}return!1}return!0}u.prototype=Object.create(i.prototype),s.prototype=Object.create(i.prototype),c.prototype=Object.create(i.prototype),u.prototype.add=function(t){var e=$(t);if(this.sign!==e.sign)return this.subtract(e.negate());var r=this.value,n=e.value;return e.isSmall?new u(j(r,Math.abs(n)),this.sign):new u(y(r,n),this.sign)},u.prototype.plus=u.prototype.add,s.prototype.add=function(t){var e=$(t),r=this.value;if(r<0!==e.sign)return this.subtract(e.negate());var n=e.value;if(e.isSmall){if(l(r+n))return new s(r+n);n=f(Math.abs(n))}return new u(j(n,Math.abs(r)),r<0)},s.prototype.plus=s.prototype.add,c.prototype.add=function(t){return new c(this.value+$(t).value)},c.prototype.plus=c.prototype.add,u.prototype.subtract=function(t){var e=$(t);if(this.sign!==e.sign)return this.add(e.negate());var r=this.value,n=e.value;return e.isSmall?O(r,Math.abs(n),this.sign):function(t,e,r){var n;return A(t,e)>=0?n=m(t,e):(n=m(e,t),r=!r),"number"==typeof(n=p(n))?(r&&(n=-n),new s(n)):new u(n,r)}(r,n,this.sign)},u.prototype.minus=u.prototype.subtract,s.prototype.subtract=function(t){var e=$(t),r=this.value;if(r<0!==e.sign)return this.add(e.negate());var n=e.value;return e.isSmall?new s(r-n):O(n,Math.abs(r),r>=0)},s.prototype.minus=s.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-$(t).value)},c.prototype.minus=c.prototype.subtract,u.prototype.negate=function(){return new u(this.value,!this.sign)},s.prototype.negate=function(){var t=this.sign,e=new s(-this.value);return e.sign=!t,e},c.prototype.negate=function(){return new c(-this.value)},u.prototype.abs=function(){return new u(this.value,!1)},s.prototype.abs=function(){return new s(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},u.prototype.multiply=function(t){var r,n,o,a=$(t),s=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return i[0];if(1===c)return this;if(-1===c)return this.negate();if((r=Math.abs(c))<e)return new u(L(s,r),l);c=f(r)}return n=s.length,o=c.length,new u(-.012*n-.012*o+15e-6*n*o>0?function t(e,r){var n=Math.max(e.length,r.length);if(n<=30)return w(e,r);n=Math.ceil(n/2);var o=e.slice(n),a=e.slice(0,n),i=r.slice(n),u=r.slice(0,n),s=t(a,u),c=t(o,i),l=t(y(a,o),y(u,i)),f=y(y(s,g(m(m(l,s),c),n)),g(c,2*n));return b(f),f}(s,c):w(s,c),l)},u.prototype.times=u.prototype.multiply,s.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new s(t.value*this.value):C(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},u.prototype._multiplyBySmall=function(t){return 0===t.value?i[0]:1===t.value?this:-1===t.value?this.negate():C(Math.abs(t.value),this.value,this.sign!==t.sign)},s.prototype.multiply=function(t){return $(t)._multiplyBySmall(this)},s.prototype.times=s.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*$(t).value)},c.prototype.times=c.prototype.multiply,u.prototype.square=function(){return new u(S(this.value),!1)},s.prototype.square=function(){var t=this.value*this.value;return l(t)?new s(t):new u(S(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},u.prototype.divmod=function(t){var e=E(this,t);return{quotient:e[0],remainder:e[1]}},c.prototype.divmod=s.prototype.divmod=u.prototype.divmod,u.prototype.divide=function(t){return E(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/$(t).value)},s.prototype.over=s.prototype.divide=u.prototype.over=u.prototype.divide,u.prototype.mod=function(t){return E(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%$(t).value)},s.prototype.remainder=s.prototype.mod=u.prototype.remainder=u.prototype.mod,u.prototype.pow=function(t){var e,r,n,o=$(t),a=this.value,u=o.value;if(0===u)return i[1];if(0===a)return i[0];if(1===a)return i[1];if(-1===a)return o.isEven()?i[1]:i[-1];if(o.sign)return i[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(e=Math.pow(a,u)))return new s(h(e));for(r=this,n=i[1];!0&u&&(n=n.times(r),--u),0!==u;)u/=2,r=r.square();return n},s.prototype.pow=u.prototype.pow,c.prototype.pow=function(t){var e=$(t),r=this.value,n=e.value,o=BigInt(0),a=BigInt(1),u=BigInt(2);if(n===o)return i[1];if(r===o)return i[0];if(r===a)return i[1];if(r===BigInt(-1))return e.isEven()?i[1]:i[-1];if(e.isNegative())return new c(o);for(var s=this,l=i[1];(n&a)===a&&(l=l.times(s),--n),n!==o;)n/=u,s=s.square();return l},u.prototype.modPow=function(t,e){if(t=$(t),(e=$(e)).isZero())throw new Error("Cannot take modPow with modulus 0");var r=i[1],n=this.mod(e);for(t.isNegative()&&(t=t.multiply(i[-1]),n=n.modInv(e));t.isPositive();){if(n.isZero())return i[0];t.isOdd()&&(r=r.multiply(n).mod(e)),t=t.divide(2),n=n.square().mod(e)}return r},c.prototype.modPow=s.prototype.modPow=u.prototype.modPow,u.prototype.compareAbs=function(t){var e=$(t),r=this.value,n=e.value;return e.isSmall?1:A(r,n)},s.prototype.compareAbs=function(t){var e=$(t),r=Math.abs(this.value),n=e.value;return e.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},c.prototype.compareAbs=function(t){var e=this.value,r=$(t).value;return(e=e>=0?e:-e)===(r=r>=0?r:-r)?0:e>r?1:-1},u.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=$(t),r=this.value,n=e.value;return this.sign!==e.sign?e.sign?1:-1:e.isSmall?this.sign?-1:1:A(r,n)*(this.sign?-1:1)},u.prototype.compareTo=u.prototype.compare,s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=$(t),r=this.value,n=e.value;return e.isSmall?r==n?0:r>n?1:-1:r<0!==e.sign?r<0?-1:1:r<0?1:-1},s.prototype.compareTo=s.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=this.value,r=$(t).value;return e===r?0:e>r?1:-1},c.prototype.compareTo=c.prototype.compare,u.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=s.prototype.eq=s.prototype.equals=u.prototype.eq=u.prototype.equals,u.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=s.prototype.neq=s.prototype.notEquals=u.prototype.neq=u.prototype.notEquals,u.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=s.prototype.gt=s.prototype.greater=u.prototype.gt=u.prototype.greater,u.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=s.prototype.lt=s.prototype.lesser=u.prototype.lt=u.prototype.lesser,u.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals=u.prototype.geq=u.prototype.greaterOrEquals,u.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals=u.prototype.leq=u.prototype.lesserOrEquals,u.prototype.isEven=function(){return 0==(1&this.value[0])},s.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},u.prototype.isOdd=function(){return 1==(1&this.value[0])},s.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},u.prototype.isPositive=function(){return!this.sign},s.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=s.prototype.isPositive,u.prototype.isNegative=function(){return this.sign},s.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=s.prototype.isNegative,u.prototype.isUnit=function(){return!1},s.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},u.prototype.isZero=function(){return!1},s.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},u.prototype.isDivisibleBy=function(t){var e=$(t);return!e.isZero()&&(!!e.isUnit()||(0===e.compareAbs(2)?this.isEven():this.mod(e).isZero()))},c.prototype.isDivisibleBy=s.prototype.isDivisibleBy=u.prototype.isDivisibleBy,u.prototype.isPrime=function(t){var e=U(this);if(void 0!==e)return e;var r=this.abs(),n=r.bitLength();if(n<=64)return _(r,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),i=Math.ceil(!0===t?2*Math.pow(a,2):a),u=[],s=0;s<i;s++)u.push(o(s+2));return _(r,u)},c.prototype.isPrime=s.prototype.isPrime=u.prototype.isPrime,u.prototype.isProbablePrime=function(t,e){var r=U(this);if(void 0!==r)return r;for(var n=this.abs(),a=void 0===t?5:t,i=[],u=0;u<a;u++)i.push(o.randBetween(2,n.minus(2),e));return _(n,i)},c.prototype.isProbablePrime=s.prototype.isProbablePrime=u.prototype.isProbablePrime,u.prototype.modInv=function(t){for(var e,r,n,a=o.zero,i=o.one,u=$(t),s=this.abs();!s.isZero();)e=u.divide(s),r=a,n=u,a=i,u=s,i=r.subtract(e.multiply(i)),s=n.subtract(e.multiply(s));if(!u.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=s.prototype.modInv=u.prototype.modInv,u.prototype.next=function(){var t=this.value;return this.sign?O(t,1,this.sign):new u(j(t,1),this.sign)},s.prototype.next=function(){var t=this.value;return t+1<r?new s(t+1):new u(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},u.prototype.prev=function(){var t=this.value;return this.sign?new u(j(t,1),!0):O(t,1,this.sign)},s.prototype.prev=function(){var t=this.value;return t-1>-r?new s(t-1):new u(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=e;)x.push(2*x[x.length-1]);var k=x.length,I=x[k-1];function N(t){return Math.abs(t)<=e}function R(t,e,r){e=$(e);for(var n=t.isNegative(),a=e.isNegative(),i=n?t.not():t,u=a?e.not():e,s=0,c=0,l=null,f=null,p=[];!i.isZero()||!u.isZero();)s=(l=E(i,I))[1].toJSNumber(),n&&(s=I-1-s),c=(f=E(u,I))[1].toJSNumber(),a&&(c=I-1-c),i=l[0],u=f[0],p.push(r(s,c));for(var b=0!==r(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(I).add(o(p[v]));return b}u.prototype.shiftLeft=function(t){var e=$(t).toJSNumber();if(!N(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftRight(-e);var r=this;if(r.isZero())return r;for(;e>=k;)r=r.multiply(I),e-=k-1;return r.multiply(x[e])},c.prototype.shiftLeft=s.prototype.shiftLeft=u.prototype.shiftLeft,u.prototype.shiftRight=function(t){var e,r=$(t).toJSNumber();if(!N(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=k;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(e=E(n,I))[1].isNegative()?e[0].prev():e[0],r-=k-1}return(e=E(n,x[r]))[1].isNegative()?e[0].prev():e[0]},c.prototype.shiftRight=s.prototype.shiftRight=u.prototype.shiftRight,u.prototype.not=function(){return this.negate().prev()},c.prototype.not=s.prototype.not=u.prototype.not,u.prototype.and=function(t){return R(this,t,(function(t,e){return t&e}))},c.prototype.and=s.prototype.and=u.prototype.and,u.prototype.or=function(t){return R(this,t,(function(t,e){return t|e}))},c.prototype.or=s.prototype.or=u.prototype.or,u.prototype.xor=function(t){return R(this,t,(function(t,e){return t^e}))},c.prototype.xor=s.prototype.xor=u.prototype.xor;function V(t){var r=t.value,n="number"==typeof r?r|1<<30:"bigint"==typeof r?r|BigInt(1<<30):r[0]+r[1]*e|1073758208;return n&-n}function q(t,e){return t=$(t),e=$(e),t.greater(e)?t:e}function M(t,e){return t=$(t),e=$(e),t.lesser(e)?t:e}function P(t,e){if(t=$(t).abs(),e=$(e).abs(),t.equals(e))return t;if(t.isZero())return e;if(e.isZero())return t;for(var r,n,o=i[1];t.isEven()&&e.isEven();)r=M(V(t),V(e)),t=t.divide(r),e=e.divide(r),o=o.multiply(r);for(;t.isEven();)t=t.divide(V(t));do{for(;e.isEven();)e=e.divide(V(e));t.greater(e)&&(n=e,e=t,t=n),e=e.subtract(t)}while(!e.isZero());return o.isUnit()?t:t.multiply(o)}u.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(e,r){if(r.compareTo(e)<=0){var n=t(e,r.square(r)),a=n.p,i=n.e,u=a.multiply(r);return u.compareTo(e)<=0?{p:u,e:2*i+1}:{p:a,e:2*i}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=s.prototype.bitLength=u.prototype.bitLength;var Q=function(t,e,r,n){r=r||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),r=r.toLowerCase());var o,a=t.length,i=Math.abs(e),u={};for(o=0;o<r.length;o++)u[r[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in u&&u[l]>=i)){if("1"===l&&1===i)continue;throw new Error(l+" is not a valid digit in base "+e+".")}}e=$(e);var s=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in u)s.push($(u[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);s.push($(t.slice(f+1,o)))}}return D(s,e,c)};function D(t,e,r){var n,o=i[0],a=i[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(e);return r?o.negate():o}function F(t,e){if((e=o(e)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(e.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var n=!1;if(t.isNegative()&&e.isPositive()&&(n=!0,t=t.abs()),e.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,i=[],u=t;u.isNegative()||u.compareAbs(e)>=0;){a=u.divmod(e),u=a.quotient;var s=a.remainder;s.isNegative()&&(s=e.minus(s).abs(),u=u.next()),i.push(s.toJSNumber())}return i.push(u.toJSNumber()),{value:i.reverse(),isNegative:n}}function B(t,e,r){var n=F(t,e);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,e){return t<(e=e||"0123456789abcdefghijklmnopqrstuvwxyz").length?e[t]:"<"+t+">"}(t,r)})).join("")}function H(t){if(l(+t)){var e=+t;if(e===h(e))return a?new c(BigInt(e)):new s(e);throw new Error("Invalid integer: "+t)}var r="-"===t[0];r&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==h(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var i=n[0],f=i.indexOf(".");if(f>=0&&(o-=i.length-f-1,i=i.slice(0,f)+i.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=i+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(r?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new u(p,r)}function $(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==h(t))throw new Error(t+" is not an integer.");return new s(t)}return H(t.toString())}(t):"string"==typeof t?H(t):"bigint"==typeof t?new c(t):t}u.prototype.toArray=function(t){return F(this,t)},s.prototype.toArray=function(t){return F(this,t)},c.prototype.toArray=function(t){return F(this,t)},u.prototype.toString=function(t,e){if(void 0===t&&(t=10),10!==t||e)return B(this,t,e);for(var r,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)r=String(n[o]),a+="0000000".slice(r.length)+r;return(this.sign?"-":"")+a},s.prototype.toString=function(t,e){return void 0===t&&(t=10),10!=t||e?B(this,t,e):String(this.value)},c.prototype.toString=s.prototype.toString,c.prototype.toJSON=u.prototype.toJSON=s.prototype.toJSON=function(){return this.toString()},u.prototype.valueOf=function(){return parseInt(this.toString(),10)},u.prototype.toJSNumber=u.prototype.valueOf,s.prototype.valueOf=function(){return this.value},s.prototype.toJSNumber=s.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var W=0;W<1e3;W++)i[W]=$(W),W>0&&(i[-W]=$(-W));return i.one=i[1],i.zero=i[0],i.minusOne=i[-1],i.max=q,i.min=M,i.gcd=P,i.lcm=function(t,e){return t=$(t).abs(),e=$(e).abs(),t.divide(P(t,e)).multiply(e)},i.isInstance=function(t){return t instanceof u||t instanceof s||t instanceof c},i.randBetween=function(t,r,n){t=$(t),r=$(r);var o=n||Math.random,a=M(t,r),u=q(t,r).subtract(a).add(1);if(u.isSmall)return a.add(Math.floor(o()*u));for(var s=F(u,e).value,c=[],l=!0,f=0;f<s.length;f++){var p=l?s[f]+(f+1<s.length?s[f+1]/e:0):e,b=h(o()*p);c.push(b),b<s[f]&&(l=!1)}return a.add(i.fromArray(c,e,!1))},i.fromArray=function(t,e,r){return D(t.map($),$(e||10),r)},i}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(e,r,e,t))||(t.exports=n)}).call(this,r(30)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=db2.umd.js.map