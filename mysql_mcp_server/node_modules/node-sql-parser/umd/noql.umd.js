!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=26)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return E})),e.d(r,"autoIncrementToSQL",(function(){return _})),e.d(r,"columnOrderListToSQL",(function(){return x})),e.d(r,"commonKeywordArgsToSQL",(function(){return U})),e.d(r,"commonOptionConnector",(function(){return i})),e.d(r,"connector",(function(){return c})),e.d(r,"commonTypeValue",(function(){return O})),e.d(r,"commentToSQL",(function(){return A})),e.d(r,"createBinaryExpr",(function(){return f})),e.d(r,"createValueExpr",(function(){return l})),e.d(r,"dataTypeToSQL",(function(){return g})),e.d(r,"DEFAULT_OPT",(function(){return s})),e.d(r,"escape",(function(){return p})),e.d(r,"literalToSQL",(function(){return L})),e.d(r,"columnIdentifierToSql",(function(){return d})),e.d(r,"getParserOpt",(function(){return b})),e.d(r,"identifierToSql",(function(){return h})),e.d(r,"onPartitionsToSQL",(function(){return C})),e.d(r,"replaceParams",(function(){return j})),e.d(r,"returningToSQL",(function(){return S})),e.d(r,"hasVal",(function(){return w})),e.d(r,"setParserOpt",(function(){return v})),e.d(r,"toUpper",(function(){return m})),e.d(r,"topToSQL",(function(){return y})),e.d(r,"triggerEventToSQL",(function(){return T}));var n=e(2),o=e(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s={database:"noql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function i(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function c(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function l(t){var r=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function f(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:l(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(e[0]),l(e[1])]},n):(n.right=e.type?e:l(e),n)}function p(t){return t}function b(){return u}function v(t){u=t}function y(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(t){var r=b().database;if(t)switch(r&&r.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function h(t,r){var e=b().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function w(t){return t}function L(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,s=t.suffix,u=t.value,i="object"===a(t)?u:t;switch(e){case"backticks_quote_string":i="`".concat(u,"`");break;case"string":i="'".concat(u,"'");break;case"regex_string":i='r"'.concat(u,'"');break;case"hex_string":i="X'".concat(u,"'");break;case"full_hex_string":i="0x".concat(u);break;case"natural_string":i="N'".concat(u,"'");break;case"bit_string":i="b'".concat(u,"'");break;case"double_quote_string":i='"'.concat(u,'"');break;case"single_quote_string":i="'".concat(u,"'");break;case"boolean":case"bool":i=u?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(r||":").concat(u),r=null;break;case"origin":i=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":i="N'".concat(u,"'");break;case"unicode_string":i="U&'".concat(u,"'")}var c=[];return r&&c.push(m(r)),c.push(i),s&&("string"==typeof s&&c.push(s),"object"===a(s)&&(s.collate?c.push(Object(o.a)(s.collate)):c.push(L(s)))),i=c.join(" "),n?"(".concat(i,")"):i}}function O(t){if(!t)return[];var r=t.type,e=t.symbol,n=t.value;return[r.toUpperCase(),e,"string"==typeof n?n.toUpperCase():L(n)].filter(w)}function j(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===a(e)&&null!==e})).forEach((function(n){var o=r[n];if("object"!==a(o)||"param"!==o.type)return t(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return r[n]=l(e[o.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function C(t){var r=t.type,e=t.partitions;return[m(r),"(".concat(e.map((function(t){if("range"!==t.type)return L(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(L(r)," ").concat(m(n)," ").concat(L(e))})).join(", "),")")].join(" ")}function g(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,a=t.suffix,s="";return null!=e&&(s=o?"".concat(e,", ").concat(o):e),n&&(s="(".concat(s,")")),a&&a.length&&(s+=" ".concat(a.join(" "))),"".concat(r).concat(s)}function E(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=m(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(t){return[t.field_name,E(t.field_type)].filter(w).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function A(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(L(o)),r.join(" ")}}function T(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[m(r)];if(e){var a=e.keyword,s=e.columns;o.push(m(a),s.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function S(t){return t?["RETURNING",t.columns.map(n.h).filter(w).join(", ")].join(" "):""}function U(t){return t?[m(t.keyword),m(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var r=b().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,s=m(e);return a&&(s+="(".concat(L(n),", ").concat(L(o),")")),s}}function x(t){if(t)return t.map(n.e).filter(w).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return C})),e.d(r,"b",(function(){return g})),e.d(r,"d",(function(){return j})),e.d(r,"c",(function(){return E}));var n=e(0),o=e(9),a=e(13);var s=e(22),u=e(21);var i=e(11),c=e(2),l=e(6),f=e(18);var p=e(7),b=e(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(c.i)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(r),"]");var s=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return e?"[".concat(s,"]"):a?"(".concat(s,")"):s}(t);default:return""}}function d(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===v(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(y(t)),o.filter(n.hasVal).join("")}var h=e(3),m=e(5),w=e(20);function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var O={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,s=t.within_group_orderby,u=C(r.expr);u=Array.isArray(u)?u.join(", "):u;var i=t.name,c=Object(a.a)(o);r.distinct&&(u=["DISTINCT",u].join(" ")),r.separator&&r.separator.delimiter&&(u=[u,Object(n.literalToSQL)(r.separator.delimiter)].join("".concat(r.separator.symbol," "))),r.separator&&r.separator.expr&&(u=[u,C(r.separator.expr)].join(" ")),r.orderby&&(u=[u,E(r.orderby,"order by")].join(" ")),r.separator&&r.separator.value&&(u=[u,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=s?"WITHIN GROUP (".concat(E(s,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(C(e.where),")"):"";return["".concat(i,"(").concat(u,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:w.c,array:d,assign:s.a,binary_expr:u.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(C(n));for(var a=0,s=e.length;a<s;++a)r.push(e[a].type.toUpperCase()),e[a].cond&&(r.push(C(e[a].cond)),r.push("THEN")),r.push(C(e[a].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:l.c,collate:i.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:d,tablefunc:l.j,tables:h.c,unnest:h.d,window:w.b};function j(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,a=t.members,s=t.quoted,u=t.suffix,i=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(e||"").concat(c);return u&&(l+=u),i.push(l),[s,i.join(" "),s].filter(n.hasVal).join("")}function C(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,a=Object.keys(e);o<a.length;o++){var s=a[o];r[s]=e[s]}}var u=r.type;return"expr"===u?C(r.expr):O[u]?O[u](r):Object(n.literalToSQL)(r)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function E(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,e.join(", "))}O.var=j,O.expr_list=function(t){var r=g(t.value),e=t.parentheses,n=t.separator;if(!e&&!n)return r;var o=n||", ",a=r.join(o);return e?"(".concat(a,")"):a},O.select=function(t){var r="object"===L(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(r,")"):r},O.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",a="".concat(r).concat(o).concat(C(n));return e?"(".concat(a,")"):a},O.map_object=function(t){var r=t.keyword,e=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(r),"[".concat(e,"]")].join("")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"c",(function(){return L})),e.d(r,"f",(function(){return y})),e.d(r,"h",(function(){return C})),e.d(r,"i",(function(){return E})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return b})),e.d(r,"e",(function(){return w})),e.d(r,"g",(function(){return h})),e.d(r,"j",(function(){return j})),e.d(r,"k",(function(){return g}));var n=e(11),o=e(19),a=e(1),s=e(6),u=e(3),i=e(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function b(t,r){if("string"==typeof t)return Object(i.identifierToSql)(t,r);var e=t.expr,n=t.offset,o=t.suffix,s=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(a.a)(e),s,o].filter(i.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var r,e=[],n=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=f(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}(t);try{for(n.s();!(r=n.n()).done;){var o=r.value,a=o.brackets?"[".concat(Object(i.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(i.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(i.literalToSQL)(o.property))),e.push(a)}}catch(t){n.e(t)}finally{n.f()}return e.join("")}function y(t){var r=t.array_index,e=t.as,o=t.column,s=t.collate,u=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,y=t.options,d=t.schema,h=t.table,m=t.parentheses,w=t.suffix,L=t.order_by,O=t.subFields,j=void 0===O?[]:O,C="*"===o?"*":b(o,c),g=[u,d,h].filter(i.hasVal).map((function(t){return"".concat("string"==typeof t?Object(i.identifierToSql)(t):Object(a.a)(t))})),E=g[0];if(E){for(var A=1;A<g.length;++A)E="".concat(E).concat(p[A]||".").concat(g[A]);C="".concat(E).concat(p[A]||".").concat(C)}var T=[C=["".concat(C).concat(v(r))].concat(l(j)).join("."),Object(n.a)(s),Object(a.a)(y),Object(i.commonOptionConnector)("AS",a.a,e)];T.push("string"==typeof w?Object(i.toUpper)(w):Object(a.a)(w)),T.push(Object(i.toUpper)(L));var S=T.filter(i.hasVal).join(" ");return m?"(".concat(S,")"):S}function d(t){if(t){var r=t.dataType,e=t.length,n=t.suffix,o=t.scale,u=t.expr,c=null!=e,l=Object(i.dataTypeToSQL)({dataType:r,length:e,suffix:n,scale:o,parentheses:c});if(u&&(l+=Object(a.a)(u)),t.array){var f=Object(s.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function h(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,o=t.match,s=t.table,c=t.on_action;return r.push(Object(i.toUpper)(n)),r.push(Object(u.c)(s)),r.push(e&&"(".concat(e.map((function(t){return Object(a.a)(t)})).join(", "),")")),r.push(Object(i.toUpper)(o)),c.map((function(t){return r.push(Object(i.toUpper)(t.type),Object(a.a)(t.value))})),r.filter(i.hasVal)}function m(t){var r=[],e=t.nullable,n=t.character_set,s=t.check,u=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,y=t.generated,d=t.auto_increment,m=t.unique,w=t.primary_key,L=t.column_format,O=t.reference_definition,j=[Object(i.toUpper)(e&&e.action),Object(i.toUpper)(e&&e.value)].filter(i.hasVal).join(" ");if(y||r.push(j),v){var C=v.type,g=v.value;r.push(C.toUpperCase(),Object(a.a)(g))}var E=Object(i.getParserOpt)().database;return c&&r.push(Object(i.toUpper)(c.keyword),Object(i.literalToSQL)(c.constraint)),r.push(Object(o.a)(s)),r.push(function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(y)),y&&r.push(j),r.push(Object(i.autoIncrementToSQL)(d),Object(i.toUpper)(w),Object(i.toUpper)(m),Object(i.commentToSQL)(u)),r.push.apply(r,l(Object(i.commonTypeValue)(n))),"sqlite"!==E.toLowerCase()&&r.push(Object(a.a)(f)),r.push.apply(r,l(Object(i.commonTypeValue)(L))),r.push.apply(r,l(Object(i.commonTypeValue)(p))),r.push.apply(r,l(h(O))),r.push(Object(i.commonOptionConnector)("USING",a.a,b)),r.filter(i.hasVal).join(" ")}function w(t){var r=t.column,e=t.collate,n=t.nulls,o=t.opclass,s=t.order_by,u="string"==typeof r?{type:"column_ref",table:t.table,column:r}:t;return u.collate=null,[Object(a.a)(u),Object(a.a)(e),o,Object(i.toUpper)(s),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function L(t){var r=[],e=y(t.column),n=d(t.definition);return r.push(e),r.push(n),r.push(m(t)),r.filter(i.hasVal).join(" ")}function O(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function j(t){var r=t.against,e=t.as,n=t.columns,o=t.match,s=t.mode;return[[Object(i.toUpper)(o),"(".concat(n.map((function(t){return y(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(r),["(",Object(a.a)(t.expr),s&&" ".concat(Object(i.literalToSQL)(s)),")"].filter(i.hasVal).join("")].join(" "),O(e)].filter(i.hasVal).join(" ")}function C(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(s.c)(t);r&&(e.isDual=r);var o=Object(a.a)(e),u=t.expr_list;if(u){var c=[o],l=u.map((function(t){return C(t,r)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(v(e.array_index))),[o,O(t.as)].filter(i.hasVal).join(" ")}function g(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}function E(t,r){if(!t||"*"===t)return t;var e=g(r);return t.map((function(t){return C(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"c",(function(){return h})),e.d(r,"a",(function(){return m})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return f}));var n=e(21),o=e(2),a=e(1),s=e(17),u=e(18),i=e(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(i.toUpper)(r),"(").concat(n&&Object(a.a)(n)||"",")"),Object(i.commonOptionConnector)("AS","string"==typeof e?i.identifierToSql:a.a,e),Object(i.commonOptionConnector)(Object(i.toUpper)(o&&o.keyword),i.identifierToSql,o&&o.as)].filter(i.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,s=t.expr,u=t.in_expr,c=t.type,l=[Object(a.a)(s),"FOR",Object(o.f)(e),Object(n.a)(u)],f=["".concat(Object(i.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(i.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function b(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,s=t.parentheses,u=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(i.toUpper)(r),"(".concat(Object(i.identifierToSql)(n)),"(".concat(o.map(a.a).filter(i.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(i.toUpper)(r),"=",Object(a.a)(e));break;case"index":c.push(Object(i.toUpper)(u),Object(i.toUpper)(r),s?"(".concat(e.map(i.identifierToSql).join(", "),")"):"= ".concat(Object(i.identifierToSql)(e)));break;default:c.push(Object(a.a)(e))}return c.filter(i.hasVal).join(" ")}}function v(t,r){var e=t.name,n=t.symbol;return[Object(i.toUpper)(e),n,r].filter(i.hasVal).join(" ")}function y(t){var r=[];switch(t.keyword){case"as":r.push("AS","OF",Object(a.a)(t.of));break;case"from_to":r.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":r.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":r.push("CONTAINED","IN",Object(a.a)(t.in))}return r.filter(i.hasVal).join(" ")}function d(t){if("UNNEST"===Object(i.toUpper)(t.type))return f(t);var r,e,n,c,l=t.table,d=t.db,h=t.as,m=t.expr,w=t.operator,L=t.prefix,O=t.schema,j=t.server,C=t.suffix,g=t.tablesample,E=t.temporal_table,A=t.table_hint,T=Object(i.identifierToSql)(j),S=Object(i.identifierToSql)(d),U=Object(i.identifierToSql)(O),_=l&&Object(i.identifierToSql)(l);if(m)switch(m.type){case"values":var x=m.parentheses,I=m.values,N=m.prefix,R=[x&&"(","",x&&")"],k=Object(s.b)(I);N&&(k=k.split("(").slice(1).map((function(t){return"".concat(Object(i.toUpper)(N),"(").concat(t)})).join("")),R[1]="VALUES ".concat(k),_=R.filter(i.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.offset,a=t.size,s=[Object(i.identifierToSql)(r.expr.db),Object(i.identifierToSql)(r.expr.schema),Object(i.identifierToSql)(r.expr.table)].filter(i.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(e.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(r,s)),v(e,c)],f=v(a,Object(u.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(u.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(i.hasVal).join(", ")}(m);break;case"generator":e=(r=m).keyword,n=r.type,c=r.generators.map((function(t){return Object(i.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(i.toUpper)(e),"(").concat(Object(i.toUpper)(n),"(").concat(c,"))");break;default:_=Object(a.a)(m)}var V=[[T,S,U,_=[Object(i.toUpper)(L),_,Object(i.toUpper)(C)].filter(i.hasVal).join(" ")].filter(i.hasVal).join(".")];if(g){var M=["TABLESAMPLE",Object(a.a)(g.expr),Object(i.literalToSQL)(g.repeatable)].filter(i.hasVal).join(" ");V.push(M)}V.push(function(t){if(t){var r=t.keyword,e=t.expr;return[Object(i.toUpper)(r),y(e)].filter(i.hasVal).join(" ")}}(E),Object(i.commonOptionConnector)("AS","string"==typeof h?i.identifierToSql:a.a,h),p(w)),A&&V.push(Object(i.toUpper)(A.keyword),"(".concat(A.expr.map(b).filter(i.hasVal).join(", "),")"));var q=V.filter(i.hasVal).join(" ");return t.parentheses?"(".concat(q,")"):q}function h(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=t.joins,o=h(r);if(e){for(var s=[],u=[],l=!0===e?1:e.length,f=0;f++<l;)s.push("("),u.push(")");var p=n&&n.length>0?h([""].concat(c(n))):"";return s.join("")+o+u.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(d(b));for(var y=1;y<t.length;++y){var m=t[y],w=m.on,L=m.using,O=m.join,j=[];j.push(O?" ".concat(Object(i.toUpper)(O)):","),j.push(d(m)),j.push(Object(i.commonOptionConnector)("ON",a.a,w)),L&&j.push("USING (".concat(L.map(i.literalToSQL).join(", "),")")),v.push(j.filter(i.hasVal).join(" "))}return v.filter(i.hasVal).join("")}function m(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var s=Object(i.literalToSQL)(n);switch(r){case"partition by":case"default collate":s=Object(a.a)(n);break;case"options":s="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(a.a).join(", ")}return o.push(s),o.filter(i.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return y})),e.d(r,"c",(function(){return g})),e.d(r,"d",(function(){return E})),e.d(r,"e",(function(){return d})),e.d(r,"f",(function(){return h})),e.d(r,"g",(function(){return m})),e.d(r,"h",(function(){return S})),e.d(r,"i",(function(){return T})),e.d(r,"j",(function(){return A})),e.d(r,"l",(function(){return w})),e.d(r,"m",(function(){return L})),e.d(r,"o",(function(){return O})),e.d(r,"n",(function(){return j})),e.d(r,"k",(function(){return C}));var n=e(2),o=e(14),a=e(0),s=e(1),u=e(3),i=e(16),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(s.a)(t.expr);return"".concat("CALL"," ").concat(r)}function y(t){var r=t.type,e=t.keyword,o=t.name,i=t.prefix,c=t.suffix,l=[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(a.toUpper)(i)];switch(e){case"table":l.push(Object(u.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(u.c)(o),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(u.b)(t.table)]:[]),[t.options&&t.options.map(s.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(s.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function d(t){var r=t.type,e=t.table,n=Object(a.toUpper)(r);return"".concat(n," ").concat(Object(a.identifierToSql)(e))}function h(t){var r=t.type,e=t.name,n=t.args,o=[Object(a.toUpper)(r)],u=[e];return n&&u.push("(".concat(Object(s.a)(n).join(", "),")")),o.push(u.join("")),o.filter(a.hasVal).join(" ")}function m(t){var r=t.type,e=t.label,n=t.target,o=t.query,s=t.stmts;return[e,Object(a.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(s),"END LOOP",e].filter(a.hasVal).join(" ")}function w(t){var r=t.type,e=t.level,n=t.raise,o=t.using,u=[Object(a.toUpper)(r),Object(a.toUpper)(e)];return n&&u.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(s.a)(t)})).join(", ")),o&&u.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(s.a)(t)})).join(", ")),u.filter(a.hasVal).join(" ")}function L(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var a,s=l(e);try{for(s.s();!(a=s.n()).done;){var i=a.value.map(u.b);n.push(i.join(" TO "))}}catch(t){s.e(t)}finally{s.f()}}return"".concat(o," ").concat(n.join(", "))}function O(t){var r=t.type,e=t.db,n=Object(a.toUpper)(r),o=Object(a.identifierToSql)(e);return"".concat(n," ").concat(o)}function j(t){var r=t.type,e=t.expr,n=t.keyword,o=Object(a.toUpper)(r),u=e.map(s.a).join(", ");return[o,Object(a.toUpper)(n),u].filter(a.hasVal).join(" ")}function C(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(a.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var s,i=[],c=l(n);try{var p=function(){var t=s.value,r=t.table,e=t.lock_type,n=[Object(u.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(e[t])})).filter(a.hasVal).join(" "))}i.push(n.join(" "))};for(c.s();!(s=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[i.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(s.a)(n)].filter(a.hasVal).join(" ")}function E(t){var r=t.type,e=t.declare,u=t.symbol,i=[Object(a.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,u=t.as,i=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[r,e].filter(a.hasVal).join(""),Object(a.toUpper)(u),Object(a.toUpper)(i)];switch(b){case"variable":v.push(Object(n.b)(c),Object(s.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(s.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(u," "));return i.push(c),i.join(" ")}function A(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,u=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(s.a)(r),Object(a.literalToSQL)(u),"".concat(Object(i.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(s.a)(t.boolean_expr),"THEN",Object(i.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(i.a)(e.ast||e)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function T(t){var r=t.name,e=t.host,n=[Object(a.literalToSQL)(r)];return e&&n.push("@",Object(a.literalToSQL)(e)),n.join("")}function S(t){var r=t.type,e=t.grant_option_for,o=t.keyword,u=t.objects,i=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(r),Object(a.literalToSQL)(e)],b=u.map((function(t){var r=t.priv,e=t.columns,o=[Object(s.a)(r)];return e&&o.push("(".concat(e.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),i)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(i.object_type),i.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(i))}return p.push(Object(a.toUpper)(c),l.map(T).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return O})),e.d(r,"a",(function(){return j}));var n=e(9),o=e(1),a=e(3),s=e(0);var u=e(14),i=e(2);function c(t){var r=t.name,e=t.type;switch(e){case"table":case"view":var n=[Object(s.identifierToSql)(r.db),Object(s.identifierToSql)(r.table)].filter(s.hasVal).join(".");return"".concat(Object(s.toUpper)(e)," ").concat(n);case"column":return"COLUMN ".concat(Object(i.f)(r));default:return"".concat(Object(s.toUpper)(e)," ").concat(Object(s.literalToSQL)(r))}}function l(t){var r=t.keyword,e=t.expr;return[Object(s.toUpper)(r),Object(s.literalToSQL)(e)].filter(s.hasVal).join(" ")}var f=e(7);var p=e(8),b=e(15);var v=e(12),y=e(17),d=e(4);function h(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(s.hasVal).join(" ")}var m=e(22);var w=e(23),L={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(s.toUpper)(r),Object(a.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,a=t.as,u=t.schema;return[Object(s.toUpper)(r),Object(s.toUpper)(e),Object(o.a)(n),Object(s.toUpper)(a),Object(s.identifierToSql)(u)].filter(s.hasVal).join(" ")},create:u.b,comment:function(t){var r=t.expr,e=t.keyword,n=t.target,o=t.type;return[Object(s.toUpper)(o),Object(s.toUpper)(e),c(n),l(r)].filter(s.hasVal).join(" ")},select:f.a,deallocate:d.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,u=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,y=[Object(b.a)(l),"DELETE"],d=Object(i.i)(r,e);return y.push(d),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||y.push(Object(a.c)(n))),y.push(Object(s.commonOptionConnector)("FROM",a.c,e)),y.push(Object(s.commonOptionConnector)("WHERE",o.a,u)),y.push(Object(o.c)(c,"order by")),y.push(Object(p.a)(f)),y.push(Object(s.returningToSQL)(v)),y.filter(s.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(s.toUpper)(r),Object(a.b)(e),(n||[]).map(h).filter(s.hasVal).join(", ")].filter(s.hasVal).join(" ")},execute:d.f,explain:function(t){var r=t.type,e=t.expr;return[Object(s.toUpper)(r),Object(f.a)(e)].join(" ")},for:d.g,update:v.b,if:d.j,insert:y.a,drop:d.b,truncate:d.b,replace:y.a,declare:d.d,use:d.o,rename:d.m,call:d.a,desc:d.e,set:d.n,lock:d.k,unlock:d.k,show:w.a,grant:d.h,revoke:d.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(m.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(s.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:d.l,transaction:function(t){var r=t.expr,e=r.action,n=r.keyword,o=r.modes,a=[Object(s.literalToSQL)(e),Object(s.toUpper)(n)];return o&&a.push(o.map(s.literalToSQL).join(", ")),a.filter(s.hasVal).join(" ")}};function O(t){if(!t)return"";for(var r=L[t.type],e=t,n=e._parentheses,a=e._orderby,u=e._limit,i=[n&&"(",r(t)];t._next;){var c=L[t._next.type],l=Object(s.toUpper)(t.set_op);i.push(l,c(t._next)),t=t._next}return i.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(u)),i.filter(s.hasVal).join(" ")}function j(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],a=O(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),r.push(a)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return i})),e.d(r,"c",(function(){return c})),e.d(r,"d",(function(){return l})),e.d(r,"e",(function(){return p})),e.d(r,"f",(function(){return b})),e.d(r,"g",(function(){return v})),e.d(r,"h",(function(){return f})),e.d(r,"i",(function(){return d})),e.d(r,"j",(function(){return y}));var n=e(2),o=e(1),a=e(0),s=e(13);function u(t){var r=t.args,e=t.type,n=t.over,u=r.expr,i=r.having,c="".concat(Object(a.toUpper)(e),"(").concat(Object(o.a)(u));return i&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(i.prefix)," ").concat(Object(o.a)(i.expr))),[c="".concat(c,")"),Object(s.a)(n)].filter(a.hasVal).join(" ")}function i(t){if(!t||!t.array)return"";var r=t.array.keyword;if(r)return Object(a.toUpper)(r);for(var e=t.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(Object(a.literalToSQL)(o[u])),s.push("]");return s.join("")}function c(t){for(var r=t.target,e=t.expr,s=t.keyword,u=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:e,offset:l}),b=[],v=0,y=r.length;v<y;++v){var d=r[v],h=d.angle_brackets,m=d.length,w=d.dataType,L=d.parentheses,O=d.quoted,j=d.scale,C=d.suffix,g=d.expr,E=g?Object(o.a)(g):"";null!=m&&(E=j?"".concat(m,", ").concat(j):m),L&&(E="(".concat(E,")")),h&&(E="<".concat(E,">")),C&&C.length&&(E+=" ".concat(C.map(a.literalToSQL).join(" ")));var A="::",T="",S=[];"as"===u&&(0===v&&(p="".concat(Object(a.toUpper)(s),"(").concat(p)),T=")",A=" ".concat(u.toUpperCase()," ")),0===v&&S.push(p);var U=i(d);S.push(A,O,w,O,U,E,T),b.push(S.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var _=b.filter(a.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var r=t.args,e=t.type,n=r.field,s=r.cast_type,u=r.source,i=["".concat(Object(a.toUpper)(e),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(s),Object(o.a)(u)];return"".concat(i.filter(a.hasVal).join(" "),")")}function f(t){var r=t.expr,e=r.key,n=r.value,s=r.on,u=[Object(o.a)(e),"VALUE",Object(o.a)(n)];return s&&u.push("ON","NULL",Object(o.a)(s)),u.filter(a.hasVal).join(" ")}function p(t){var r=t.args,e=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,n=t.value;return[Object(a.toUpper)(r),e,Object(o.a)(n)].filter(a.hasVal).join(" ")}(r[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(e),"(").concat(n,")")}function b(t){var r=t.value,e=r.name,n=r.symbol,s=r.expr;return[e,n,Object(o.a)(s)].filter(a.hasVal).join(" ")}function v(t){var r=t.args,e=t.array_index,u=t.name,i=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(s.a)(f),v=function(t){if(!t)return"";var r=t.type,e=t.keyword,n=t.orderby;return[Object(a.toUpper)(r),Object(a.toUpper)(e),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),y=Object(o.a)(p),d=[Object(a.literalToSQL)(u.schema),u.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!r)return[d,v,b].filter(a.hasVal).join(" ");var h=t.separator||", ";"TRIM"===Object(a.toUpper)(d)&&(h=" ");var m=[d];m.push(!1===i?" ":"(");var w=Object(o.a)(r);if(Array.isArray(h)){for(var L=w[0],O=1,j=w.length;O<j;++O)L=[L,w[O]].join(" ".concat(Object(o.a)(h[O-1])," "));m.push(L)}else m.push(w.join(h));return!1!==i&&m.push(")"),m.push(Object(n.a)(e)),m=[m.join(""),y].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function y(t){var r=t.as,e=t.name,n=t.args,s=[Object(a.literalToSQL)(e.schema),e.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(s,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(r)].join(" ")}function d(t){var r=t.args,e=t.expr,n=r.value,a=r.parentheses,s=n.map(o.a).join(", ");return[a?"(".concat(s,")"):s,"->",Object(o.a)(e)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f}));var n=e(1),o=e(2),a=e(8),s=e(15),u=e(3),i=e(0),c=e(11);function l(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],a=Object(i.toUpper)(r);switch(a){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(a,"string"==typeof e?Object(i.identifierToSql)(e):Object(n.a)(e))}return o.filter(i.hasVal).join(" ")}}function f(t){var r=t.as_struct_val,e=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,y=t.for_sys_time_as_of,d=void 0===y?{}:y,h=t.locking_read,m=t.groupby,w=t.having,L=t.into,O=void 0===L?{}:L,j=t.isolation,C=t.limit,g=t.options,E=t.orderby,A=t.parentheses_symbol,T=t.qualify,S=t.top,U=t.window,_=t.with,x=t.where,I=[Object(s.a)(_),"SELECT",Object(i.toUpper)(r)];Array.isArray(g)&&I.push(g.join(" ")),I.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,o=[Object(i.toUpper)(r)];return e&&o.push("(".concat(e.map(n.a).join(", "),")")),o.filter(i.hasVal).join(" ")}}(p),Object(i.topToSQL)(S),Object(o.i)(e,v));var N=O.position,R="";N&&(R=Object(i.commonOptionConnector)("INTO",l,O)),"column"===N&&I.push(R),I.push(Object(i.commonOptionConnector)("FROM",u.c,v)),"from"===N&&I.push(R);var k=d||{},V=k.keyword,M=k.expr;I.push(Object(i.commonOptionConnector)(V,n.a,M)),I.push(Object(i.commonOptionConnector)("WHERE",n.a,x)),m&&(I.push(Object(i.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),I.push(Object(n.b)(m.modifiers).join(", "))),I.push(Object(i.commonOptionConnector)("HAVING",n.a,w)),I.push(Object(i.commonOptionConnector)("QUALIFY",n.a,T)),I.push(Object(i.commonOptionConnector)("WINDOW",n.a,U)),I.push(Object(n.c)(E,"order by")),I.push(Object(c.a)(f)),I.push(Object(a.a)(C)),j&&I.push(Object(i.commonOptionConnector)(j.keyword,i.literalToSQL,j.expr)),I.push(Object(i.toUpper)(h)),"end"===N&&I.push(R),I.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,a=[Object(i.toUpper)(o),Object(i.toUpper)(e)];return r?"".concat(a.join(" "),"(").concat(Object(n.a)(r),")"):a.join(" ")}}(b));var q=I.filter(i.hasVal).join(" ");return A?"(".concat(q,")"):q}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return s(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?s(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function u(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function i(t){return t?t.fetch?(e=(r=t).fetch,s=r.offset,[].concat(a(u(s)),a(u(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,s}},function(t,r,e){"use strict";e.d(r,"a",(function(){return p})),e.d(r,"c",(function(){return b})),e.d(r,"b",(function(){return f}));var n=e(2),o=e(14),a=e(10),s=e(3),u=e(1),i=e(7),c=e(0);function l(t,r){switch(t){case"add":var e=r.map((function(t){var r=t.name,e=t.value;return["PARTITION",Object(c.literalToSQL)(r),"VALUES",Object(c.toUpper)(e.type),"(".concat(Object(c.literalToSQL)(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Object(n.i)(r)}}function f(t){if(!t)return"";var r=t.action,e=t.create_definitions,s=t.if_not_exists,u=t.keyword,i=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,y=t.suffix,d="",h=[];switch(b){case"column":h=[Object(n.c)(t)];break;case"index":h=Object(a.c)(t),d=t[b];break;case"table":case"schema":d=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":d=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":d=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":d=Object(c.identifierToSql)(t[b]),h=[Object(o.a)(e)];break;case"partition":h=[l(r,t.partitions)];break;case"key":d=Object(c.identifierToSql)(t[b]);break;default:d=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(r),Object(c.toUpper)(u),Object(c.toUpper)(s),Object(c.toUpper)(i),f&&Object(n.f)(f),Object(c.toUpper)(p),d&&d.trim(),h.filter(c.hasVal).join(" ")];return y&&m.push(Object(c.toUpper)(y.keyword),y.expr&&Object(n.f)(y.expr)),m.filter(c.hasVal).join(" ")}function p(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(u.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function b(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type,s=r.expr,u=r.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(s.map(p).join(", ")).concat(u?[" ORDER","BY",u.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.if_exists,o=t.prefix,a=t.expr,i=void 0===a?[]:a,l=Object(c.toUpper)(r),f=Object(s.c)(e),p=i.map(u.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),f(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,a=t.select,u=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(s.b)(u)];return e&&f.push("(".concat(e.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(i.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return u})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return s(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?s(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function u(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function i(t){if(t){var r=t.type,e=t.expr,o=t.symbol,s=r.toUpperCase(),i=[];switch(i.push(s),s){case"KEY_BLOCK_SIZE":o&&i.push(o),i.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":i.length=0,i.push.apply(i,a(u(t)));break;case"WITH PARSER":i.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":i.shift(),i.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":i.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:i.push(o,Object(n.literalToSQL)(e))}return i.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(i):[]}function l(t){var r=t.constraint_type,e=t.index_type,s=t.index_options,i=void 0===s?[]:s,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(u(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(i).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,a(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){if(t){var r=t.keyword,e=t.collate,a=e.name,s=e.symbol,u=e.value,i=[Object(o.toUpper)(r)];return u||i.push(s),i.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),u&&i.push(s),i.push(Object(n.a)(u)),i.filter(o.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return p})),e.d(r,"a",(function(){return f}));var n=e(3),o=e(1),a=e(2),s=e(8),u=e(0),i=e(15);function c(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){if(!t||0===t.length)return"";var r,e=[],n=c(t);try{for(n.s();!(r=n.n()).done;){var s=r.value,i={},l=s.value;for(var f in s)"value"!==f&&"keyword"!==f&&(i[f]=s[f]);var p=[Object(a.f)(i)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),e.push(p.filter(u.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function p(t){var r=t.from,e=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(e),Object(u.commonOptionConnector)("SET",f,a),Object(u.commonOptionConnector)("FROM",n.c,r),Object(u.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(s.a)(b),Object(u.returningToSQL)(v)].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(1),a=e(20);function s(t){if(t){var r=t.as_window_specification,e=t.expr,s=t.keyword,u=t.type,i=t.parentheses,c=Object(n.toUpper)(u);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(u)," ").concat(Object(n.toUpper)(s)),f=Object(o.a)(e)||[];return i&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return E})),e.d(r,"a",(function(){return h}));var n=e(9),o=e(1),a=e(10),s=e(2),u=e(4),i=e(19),c=e(6),l=e(3),f=e(12),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,r){if(t){if("string"==typeof t)return d(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?d(t,r):void 0}}function d(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function h(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(s.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(i.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function m(t){var r=[];switch(t.keyword){case"from":r.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":r.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":r.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return r.filter(b.hasVal).join(" ")}function w(t){var r=t.keyword,e=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(r),Object(l.b)(e),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function L(t){var r=t.as,e=t.domain,n=t.type,a=t.keyword,s=t.target,u=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(s)];if(u&&u.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=y(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}(u);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(i.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function O(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function j(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(r),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=u.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(s.c).join(", "),")"):O(n)].filter(b.hasVal).join(" ")}(i),c.map(j).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var r=t.type,e=t.symbol,n=t.value,a=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function E(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,s=t.args,u=t.options,i=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(s.expr.map(n.a).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(n.a).join(", ")].join(" "):"");return i.push("".concat(c,"(").concat(l,")"),"(".concat(u.map(g).join(", "),")")),i.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,a=t.as,s=t.temporary,u=t.if_not_exists,i=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,y=t.partition_of,d=t.query_expr,m=t.unlogged,L=t.with,O=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(m),Object(b.toUpper)(e),Object(b.toUpper)(u),Object(l.c)(n)];if(o){var j=o.type,C=o.table,g=Object(l.c)(C);return O.push(Object(b.toUpper)(j),g),O.filter(b.hasVal).join(" ")}if(y)return O.concat([w(y)]).filter(b.hasVal).join(" ");if(i&&O.push("(".concat(i.map(h).join(", "),")")),c){var E=Object(b.getParserOpt)().database,A=E&&"sqlite"===E.toLowerCase()?", ":" ";O.push(c.map(l.a).join(A))}if(L){var T=L.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");O.push("WITH (".concat(T,")"))}return O.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),d&&O.push(Object(p.b)(d)),O.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,a=t.events,s=t.execute,u=t.for_each,i=t.from,f=t.location,p=t.keyword,y=t.or,d=t.type,h=t.table,m=t.when,w=[Object(b.toUpper)(d),Object(b.toUpper)(y),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],L=Object(b.triggerEventToSQL)(a);return w.push(L,"ON",Object(l.b)(h)),i&&w.push("FROM",Object(l.b)(i)),w.push.apply(w,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(u)))),m&&w.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),w.push(Object(b.toUpper)(s.keyword),Object(c.g)(s.expr)),w.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,a=t.execute,u=t.type,i=t.table,c=t.if_not_exists,v=t.temporary,y=t.trigger,d=t.events,h=t.order,m=t.time,w=t.when,L=[Object(b.toUpper)(u),Object(b.toUpper)(v),Object(o.a)(r),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(y),Object(b.toUpper)(m),d.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(s.f).join(", ")),r.join(" ")})),"ON",Object(l.b)(i),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),h&&"".concat(Object(b.toUpper)(h.keyword)," ").concat(Object(b.identifierToSql)(h.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,w),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":L.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":L.push(Object(p.a)(a.expr.ast))}return L.push(Object(b.toUpper)(a.suffix)),L.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,s=t.type,u=t.with,i=t.version;return[Object(b.toUpper)(s),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(u),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,i),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=C(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,s=t.keyword,u=t.if_not_exists,i=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,y=t.index,d=t.on,h=t.index_options,m=t.algorithm_option,w=t.lock_option,L=t.on_kw,O=t.table,j=t.tablespace,C=t.type,g=t.where,E=t.with,A=t.with_before_where,T=E&&"WITH (".concat(Object(a.b)(E).join(", "),")"),S=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=y;y&&(U="string"==typeof y?Object(b.identifierToSql)(y):[Object(b.identifierToSql)(y.schema),Object(b.identifierToSql)(y.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(s),Object(b.toUpper)(u),Object(b.toUpper)(r),U,Object(b.toUpper)(L),Object(l.b)(O)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),S,Object(a.b)(h).join(" "),Object(n.b)(m),Object(n.b)(w),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,j)]);return A?_.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),_.push(Object(b.commonOptionConnector)("ON",o.a,d),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,s=t.create_definitions,u=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];return s&&u.push(s.map(h).join(" ")),u.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":e=function(t){var r=t.type,e=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,s=t[e],u=s.db,i=s.schema,c=[Object(b.literalToSQL)(u),i.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(r),Object(b.toUpper)(n),Object(b.toUpper)(e),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,a=t.if_not_exists,s=t.keyword,u=t.recursive,i=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,y=t.view,d=t.with,h=t.with_options,m=y.db,w=y.schema,L=y.view,O=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(w),Object(b.identifierToSql)(L)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(f),Object(b.toUpper)(u),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(s),Object(b.toUpper)(a),O,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),h&&["WITH","(".concat(h.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(d)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=L(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,a=t.name,s=t.resource,u=t.type,i=[Object(b.toUpper)(u),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(s)];if(e){var c=[];switch(s){case"enum":case"range":c.push(Object(o.a)(e));break;default:c.push("(".concat(e.map(h).join(", "),")"))}i.push(c.filter(b.hasVal).join(" "))}return i.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,a=t.if_not_exists,s=t.keyword,i=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(u.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),y=[Object(b.toUpper)(p),Object(b.toUpper)(s),Object(b.toUpper)(a),v];return n&&y.push(Object(b.toUpper)(n.keyword),n.value.map(u.i).join(", ")),y.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&y.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return y.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),y.push(Object(b.literalToSQL)(i),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),y.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(2),o=e(1),a=e(0);function s(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,s=t.columns,u=Array.isArray(s)?"(".concat(s.map(n.f).join(", "),")"):"";return"".concat("default"===r.type?Object(a.identifierToSql)(r.value):Object(a.literalToSQL)(r)).concat(u," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function s(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function u(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[s(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):s(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(3),o=e(1),a=e(2),s=e(0),u=e(7),i=e(12);function c(t){if("select"===t.type)return Object(u.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(s.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(s.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,a=[Object(s.toUpper)(e)];switch(n){case"origin":a.push(Object(s.literalToSQL)(r));break;case"update":a.push("UPDATE",Object(s.commonOptionConnector)("SET",i.a,r.set),Object(s.commonOptionConnector)("WHERE",o.a,r.where))}return a.filter(s.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(s.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,a=t.prefix,u=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,y=t.where,d=t.on_duplicate_update,h=t.partition,m=t.returning,w=t.set,L=d||{},O=L.keyword,j=L.set,C=[Object(s.toUpper)(e),Object(s.toUpper)(u),Object(n.c)(r),l(h)];return Array.isArray(f)&&C.push("(".concat(f.map(s.literalToSQL).join(", "),")")),C.push(Object(s.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(s.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(s.commonOptionConnector)("SET",i.a,w)),C.push(Object(s.commonOptionConnector)("WHERE",o.a,y)),C.push(Object(s.commonOptionConnector)(O,i.a,j)),C.push(Object(s.returningToSQL)(m)),C.filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(0),o=e(1);function a(t){var r=t.expr,e=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(10),a=e(2);function s(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(t){var r=t.constraint,e=t.constraint_type,u=t.enforced,i=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(r));var y=Object(n.toUpper)(e);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===y&&(y="UNIQUE"),b.push(y),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(i)),b.push.apply(b,s(Object(o.c)(t))),b.push.apply(b,s(Object(a.g)(l))),b.push(Object(n.toUpper)(u)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),a=e(13);function s(t){if(t){var r=t.type;return"rows"===r?[Object(n.toUpper)(r),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function u(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,a=t.orderby,u=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(a,"order by"),s(u)].filter(n.hasVal).join(" ")}(r),")")}function i(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(u(e))}function c(t){return t.expr.map(i).join(", ")}function l(t){var r=t.over;return[function(t){var r=t.args,e=t.name,a=t.consider_nulls,s=void 0===a?"":a,u=t.separator,i=void 0===u?", ":u;return[e,"(",r?Object(o.a)(r).join(i):"",")",s&&" ",s].filter(n.hasVal).join("")}(t),Object(a.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.operator||t.op,e=Object(n.a)(t.right),a=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,e="".concat(e[0]," AND ").concat(e[1])}a||(e="(".concat(e.join(", "),")"))}var s=t.right.escape||{},u=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),r,e,Object(o.toUpper)(s.type),Object(n.a)(s.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(u,")"):u].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.left,e=t.right,a=t.symbol,s=t.keyword;r.keyword=s;var u=Object(n.a)(r),i=Object(n.a)(e);return[u,Object(o.toUpper)(a),i].filter(o.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(1),o=e(8),a=e(3),s=e(0);function u(t){var r,e,u,i,c=t.keyword,l=t.suffix,f="";switch(Object(s.toUpper)(c)){case"BINLOG":e=(r=t).in,u=r.from,i=r.limit,f=[Object(s.commonOptionConnector)("IN",s.literalToSQL,e&&e.right),Object(s.commonOptionConnector)("FROM",a.c,u),Object(o.a)(i)].filter(s.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(s.toUpper)(e)?Object(s.commonOptionConnector)("LIKE",s.literalToSQL,r.right):Object(s.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(s.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(s.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(s.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(s.toUpper)(c),Object(s.toUpper)(l),f].filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(2),o=e(1),a=e(25);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u,i,c,l=(u={},i="noql",c=a.parse,(i=function(t){var r=function(t,r){if("object"!=s(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==s(r)?r:r+""}(i))in u?Object.defineProperty(u,i,{value:c,enumerable:!0,configurable:!0,writable:!0}):u[i]=c,u),f=e(16),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return y(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?y(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function y(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function d(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:r+""}var m=function(){return function(t,r,e){return r&&d(t.prototype,r),e&&d(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(e),!t||"*"===t)return[];var o=Object(n.k)(r);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"noql":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](!1===r.trimQuery?t:t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,s=this["".concat(o,"List")].bind(this),u=s(t,e),i=!0,c="",l=v(u);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,y=!1,d=v(r);try{for(d.s();!(f=d.n()).done;){var h=f.value,m=new RegExp("^".concat(h,"$"),"i");if(m.test(b)){y=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!y){c=b,i=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])}();r.a=m},function(t,r,e){"use strict";var n=e(29);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?a(t.parts[r][0])+"-"+a(t.parts[r][1]):a(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,a=new Array(t.length);for(r=0;r<t.length;r++)a[r]=(o=t[r],e[o.type](o));if(a.sort(),a.length>0){for(r=1,n=1;r<a.length;r++)a[r-1]!==a[r]&&(a[n]=a[r],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,a={},s={start:Ni},u=Ni,i=Ti("IF",!0),c=Ti("EXTENSION",!0),l=Ti("SCHEMA",!0),f=Ti("VERSION",!0),p=Ti("CASCADED",!0),b=Ti("LOCAL",!0),v=Ti("CHECK",!0),y=Ti("OPTION",!1),d=Ti("check_option",!0),h=Ti("security_barrier",!0),m=Ti("security_invoker",!0),w=Ti("SFUNC",!0),L=Ti("STYPE",!0),O=Ti("AGGREGATE",!0),j=Ti("RETURNS",!0),C=Ti("SETOF",!0),g=Ti("CONSTANT",!0),E=Ti(":=",!1),A=Ti("BEGIN",!0),T=Ti("DECLARE",!0),S=Ti("LANGUAGE",!1),U=Ti("TRANSORM",!0),_=Ti("FOR",!1),x=Ti("TYPE",!1),I=Ti("WINDOW",!0),N=Ti("IMMUTABLE",!0),R=Ti("STABLE",!0),k=Ti("VOLATILE",!0),V=Ti("STRICT",!0),M=Ti("NOT",!0),q=Ti("LEAKPROOF",!0),P=Ti("CALLED",!0),D=Ti("NULL",!0),Q=Ti("ON",!0),F=Ti("INPUT",!0),G=Ti("EXTERNAL",!0),$=Ti("SECURITY",!0),B=Ti("INVOKER",!0),H=Ti("DEFINER",!0),Y=Ti("PARALLEL",!0),W=Ti("UNSAFE",!0),X=Ti("RESTRICTED",!0),Z=Ti("SAFE",!0),K=/^[^ s\t\n\r]/,J=Si([" ","s","\t","\n","\r"],!0,!1),z=/^[^ s\t\n\r;]/,tt=Si([" ","s","\t","\n","\r",";"],!0,!1),rt=Ti("COST",!0),et=Ti("ROWS",!0),nt=Ti("SUPPORT",!0),ot=Ti("TO",!0),at=Ti("=",!1),st=Ti("CURRENT",!0),ut=Ti("FUNCTION",!0),it=Ti("TYPE",!0),ct=Ti("DOMAIN",!0),lt=Ti("INCREMENT",!0),ft=Ti("MINVALUE",!0),pt=function(t,r){return{resource:"sequence",prefix:t.toLowerCase(),value:r}},bt=Ti("NO",!0),vt=Ti("MAXVALUE",!0),yt=Ti("START",!0),dt=Ti("CACHE",!0),ht=Ti("CYCLE",!0),mt=Ti("OWNED",!0),wt=Ti("NONE",!0),Lt=Ti("NULLS",!0),Ot=Ti("FIRST",!0),jt=Ti("LAST",!0),Ct=Ti("AUTO_INCREMENT",!0),gt=Ti("UNIQUE",!0),Et=Ti("KEY",!0),At=Ti("PRIMARY",!0),Tt=Ti("COLUMN_FORMAT",!0),St=Ti("FIXED",!0),Ut=Ti("DYNAMIC",!0),_t=Ti("DEFAULT",!0),xt=Ti("STORAGE",!0),It=Ti("DISK",!0),Nt=Ti("MEMORY",!0),Rt=Ti("CASCADE",!0),kt=Ti("RESTRICT",!0),Vt=Ti("OUT",!0),Mt=Ti("VARIADIC",!0),qt=Ti("INOUT",!0),Pt=Ti("OWNER",!0),Dt=Ti("CURRENT_ROLE",!0),Qt=Ti("CURRENT_USER",!0),Ft=Ti("SESSION_USER",!0),Gt=Ti("ALGORITHM",!0),$t=Ti("INSTANT",!0),Bt=Ti("INPLACE",!0),Ht=Ti("COPY",!0),Yt=Ti("LOCK",!0),Wt=Ti("SHARED",!0),Xt=Ti("EXCLUSIVE",!0),Zt=Ti("PRIMARY KEY",!0),Kt=Ti("FOREIGN KEY",!0),Jt=Ti("MATCH FULL",!0),zt=Ti("MATCH PARTIAL",!0),tr=Ti("MATCH SIMPLE",!0),rr=Ti("SET NULL",!0),er=Ti("NO ACTION",!0),nr=Ti("SET DEFAULT",!0),or=Ti("TRIGGER",!0),ar=Ti("BEFORE",!0),sr=Ti("AFTER",!0),ur=Ti("INSTEAD OF",!0),ir=Ti("EXECUTE",!0),cr=Ti("PROCEDURE",!0),lr=Ti("OF",!0),fr=Ti("DEFERRABLE",!0),pr=Ti("INITIALLY IMMEDIATE",!0),br=Ti("INITIALLY DEFERRED",!0),vr=Ti("FOR",!0),yr=Ti("EACH",!0),dr=Ti("ROW",!0),hr=Ti("STATEMENT",!0),mr=Ti("CHARACTER",!0),wr=Ti("SET",!0),Lr=Ti("CHARSET",!0),Or=Ti("COLLATE",!0),jr=Ti("AVG_ROW_LENGTH",!0),Cr=Ti("KEY_BLOCK_SIZE",!0),gr=Ti("MAX_ROWS",!0),Er=Ti("MIN_ROWS",!0),Ar=Ti("STATS_SAMPLE_PAGES",!0),Tr=Ti("CONNECTION",!0),Sr=Ti("COMPRESSION",!0),Ur=Ti("'",!1),_r=Ti("ZLIB",!0),xr=Ti("LZ4",!0),Ir=Ti("ENGINE",!0),Nr=Ti("IN",!0),Rr=Ti("ACCESS SHARE",!0),kr=Ti("ROW SHARE",!0),Vr=Ti("ROW EXCLUSIVE",!0),Mr=Ti("SHARE UPDATE EXCLUSIVE",!0),qr=Ti("SHARE ROW EXCLUSIVE",!0),Pr=Ti("ACCESS EXCLUSIVE",!0),Dr=Ti("SHARE",!0),Qr=Ti("MODE",!0),Fr=Ti("NOWAIT",!0),Gr=Ti("TABLES",!0),$r=Ti("PREPARE",!0),Br=Ti("USAGE",!0),Hr=function(t){return{type:"origin",value:Array.isArray(t)?t[0]:t}},Yr=Ti("CONNECT",!0),Wr=Ti("PRIVILEGES",!0),Xr=function(t){return{type:"origin",value:t}},Zr=Ti("SEQUENCE",!0),Kr=Ti("DATABASE",!0),Jr=Ti("DOMAIN",!1),zr=Ti("FUNCTION",!1),te=Ti("ROUTINE",!0),re=Ti("LANGUAGE",!0),ee=Ti("LARGE",!0),ne=Ti("SCHEMA",!1),oe=Ti("FUNCTIONS",!0),ae=Ti("PROCEDURES",!0),se=Ti("ROUTINES",!0),ue=Ti("PUBLIC",!0),ie=Ti("GRANT",!0),ce=Ti("OPTION",!0),le=Ti("ADMIN",!0),fe=Ti("REVOKE",!0),pe=Ti("ELSEIF",!0),be=Ti("THEN",!0),ve=Ti("END",!0),ye=Ti("DEBUG",!0),de=Ti("LOG",!0),he=Ti("INFO",!0),me=Ti("NOTICE",!0),we=Ti("WARNING",!0),Le=Ti("EXCEPTION",!0),Oe=Ti("MESSAGE",!0),je=Ti("DETAIL",!0),Ce=Ti("HINT",!0),ge=Ti("ERRCODE",!0),Ee=Ti("COLUMN",!0),Ae=Ti("CONSTRAINT",!0),Te=Ti("DATATYPE",!0),Se=Ti("TABLE",!0),Ue=Ti("SQLSTATE",!0),_e=Ti("RAISE",!0),xe=Ti("LOOP",!0),Ie=Ti(";",!1),Ne=Ti("(",!1),Re=Ti(")",!1),Ve=Ti('"',!1),Me=Ti("OUTFILE",!0),qe=Ti("DUMPFILE",!0),Pe=Ti("BTREE",!0),De=Ti("HASH",!0),Qe=Ti("GIST",!0),Fe=Ti("GIN",!0),Ge=Ti("WITH",!0),$e=Ti("PARSER",!0),Be=Ti("VISIBLE",!0),He=Ti("INVISIBLE",!0),Ye=function(t,r){return r.unshift(t),r.forEach(t=>{const{table:r,as:e}=t;Ev[r]=r,e&&(Ev[e]=r),function(t){const r=Lv(t);t.clear(),r.forEach(r=>t.add(r))}(gv)}),r},We=Ti("LATERAL",!0),Xe=Ti("TABLESAMPLE",!0),Ze=Ti("REPEATABLE",!0),Ke=Ti("CROSS",!0),Je=Ti("FOLLOWING",!0),ze=Ti("PRECEDING",!0),tn=Ti("UNBOUNDED",!0),rn=Ti("DO",!0),en=Ti("NOTHING",!0),nn=Ti("CONFLICT",!0),on=function(t,r){return mv(t,r)},an=Ti("!",!1),sn=Ti(">=",!1),un=Ti(">",!1),cn=Ti("<=",!1),ln=Ti("<>",!1),fn=Ti("<",!1),pn=Ti("!=",!1),bn=Ti("SIMILAR",!0),vn=Ti("!~*",!1),yn=Ti("~*",!1),dn=Ti("~",!1),hn=Ti("!~",!1),mn=Ti("ESCAPE",!0),wn=Ti("+",!1),Ln=Ti("-",!1),On=Ti("*",!1),jn=Ti("/",!1),Cn=Ti("%",!1),gn=Ti("||",!1),En=Ti("$",!1),An=Ti("?|",!1),Tn=Ti("?&",!1),Sn=Ti("?",!1),Un=Ti("#-",!1),_n=Ti("#>>",!1),xn=Ti("#>",!1),In=Ti("@>",!1),Nn=Ti("<@",!1),Rn=Ti("E",!0),kn=function(t){return!0===pv[t.toUpperCase()]},Vn=/^[^"]/,Mn=Si(['"'],!0,!1),qn=/^[^']/,Pn=Si(["'"],!0,!1),Dn=Ti("`",!1),Qn=/^[^`]/,Fn=Si(["`"],!0,!1),Gn=/^[A-Za-z_\u4E00-\u9FA5]/,$n=Si([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),Bn=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,Hn=Si([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),Yn=/^[A-Za-z0-9_\u4E00-\u9FA5\xC0-\u017F]/,Wn=Si([["A","Z"],["a","z"],["0","9"],"_",["一","龥"],["À","ſ"]],!1,!1),Xn=Ti(":",!1),Zn=Ti("OVER",!0),Kn=Ti("FILTER",!0),Jn=Ti("FIRST_VALUE",!0),zn=Ti("LAST_VALUE",!0),to=Ti("ROW_NUMBER",!0),ro=Ti("DENSE_RANK",!0),eo=Ti("RANK",!0),no=Ti("LAG",!0),oo=Ti("LEAD",!0),ao=Ti("NTH_VALUE",!0),so=Ti("IGNORE",!0),uo=Ti("RESPECT",!0),io=Ti("percentile_cont",!0),co=Ti("percentile_disc",!0),lo=Ti("within",!0),fo=Ti("mode",!0),po=Ti("BOTH",!0),bo=Ti("LEADING",!0),vo=Ti("TRAILING",!0),yo=Ti("trim",!0),ho=Ti("crosstab",!0),mo=Ti("now",!0),wo=Ti("at",!0),Lo=Ti("zone",!0),Oo=Ti("CENTURY",!0),jo=Ti("DAY",!0),Co=Ti("DATE",!0),go=Ti("DECADE",!0),Eo=Ti("DOW",!0),Ao=Ti("DOY",!0),To=Ti("EPOCH",!0),So=Ti("HOUR",!0),Uo=Ti("ISODOW",!0),_o=Ti("ISOYEAR",!0),xo=Ti("MICROSECONDS",!0),Io=Ti("MILLENNIUM",!0),No=Ti("MILLISECONDS",!0),Ro=Ti("MINUTE",!0),ko=Ti("MONTH",!0),Vo=Ti("QUARTER",!0),Mo=Ti("SECOND",!0),qo=Ti("TIMEZONE",!0),Po=Ti("TIMEZONE_HOUR",!0),Do=Ti("TIMEZONE_MINUTE",!0),Qo=Ti("WEEK",!0),Fo=Ti("YEAR",!0),Go=Ti("NTILE",!0),$o=/^[\n]/,Bo=Si(["\n"],!1,!1),Ho=/^[^"\\\0-\x1F\x7F]/,Yo=Si(['"',"\\",["\0",""],""],!0,!1),Wo=/^[^'\\]/,Xo=Si(["'","\\"],!0,!1),Zo=Ti("\\'",!1),Ko=Ti('\\"',!1),Jo=Ti("\\\\",!1),zo=Ti("\\/",!1),ta=Ti("\\b",!1),ra=Ti("\\f",!1),ea=Ti("\\n",!1),na=Ti("\\r",!1),oa=Ti("\\t",!1),aa=Ti("\\u",!1),sa=Ti("\\",!1),ua=Ti("''",!1),ia=/^[\n\r]/,ca=Si(["\n","\r"],!1,!1),la=Ti(".",!1),fa=/^[0-9]/,pa=Si([["0","9"]],!1,!1),ba=/^[0-9a-fA-F]/,va=Si([["0","9"],["a","f"],["A","F"]],!1,!1),ya=/^[eE]/,da=Si(["e","E"],!1,!1),ha=/^[+\-]/,ma=Si(["+","-"],!1,!1),wa=Ti("NOT NULL",!0),La=Ti("TRUE",!0),Oa=Ti("FALSE",!0),ja=Ti("SHOW",!0),Ca=Ti("DROP",!0),ga=Ti("USE",!0),Ea=Ti("ALTER",!0),Aa=Ti("SELECT",!0),Ta=Ti("UPDATE",!0),Sa=Ti("CREATE",!0),Ua=Ti("TEMPORARY",!0),_a=Ti("TEMP",!0),xa=Ti("DELETE",!0),Ia=Ti("INSERT",!0),Na=Ti("RECURSIVE",!0),Ra=Ti("REPLACE",!0),ka=Ti("RETURNING",!0),Va=Ti("RENAME",!0),Ma=(Ti("EXPLAIN",!0),Ti("PARTITION",!0)),qa=Ti("INTO",!0),Pa=Ti("FROM",!0),Da=Ti("AS",!0),Qa=Ti("TABLESPACE",!0),Fa=Ti("DEALLOCATE",!0),Ga=Ti("LEFT",!0),$a=Ti("RIGHT",!0),Ba=Ti("FULL",!0),Ha=Ti("INNER",!0),Ya=Ti("JOIN",!0),Wa=Ti("OUTER",!0),Xa=Ti("UNION",!0),Za=Ti("INTERSECT",!0),Ka=Ti("EXCEPT",!0),Ja=Ti("VALUES",!0),za=Ti("USING",!0),ts=Ti("WHERE",!0),rs=Ti("GROUP",!0),es=Ti("BY",!0),ns=Ti("ORDER",!0),os=Ti("HAVING",!0),as=Ti("LIMIT",!0),ss=Ti("OFFSET",!0),us=Ti("ASC",!0),is=Ti("DESC",!0),cs=Ti("ALL",!0),ls=Ti("DISTINCT",!0),fs=Ti("BETWEEN",!0),ps=Ti("IS",!0),bs=Ti("LIKE",!0),vs=Ti("ILIKE",!0),ys=Ti("EXISTS",!0),ds=Ti("AND",!0),hs=Ti("OR",!0),ms=Ti("ARRAY",!0),ws=Ti("ARRAY_AGG",!0),Ls=Ti("STRING_AGG",!0),Os=Ti("COUNT",!0),js=Ti("GROUP_CONCAT",!0),Cs=Ti("MAX",!0),gs=Ti("MIN",!0),Es=Ti("SUM",!0),As=Ti("AVG",!0),Ts=Ti("EXTRACT",!0),Ss=Ti("CALL",!0),Us=Ti("CASE",!0),_s=Ti("WHEN",!0),xs=Ti("ELSE",!0),Is=Ti("CAST",!0),Ns=Ti("BOOL",!0),Rs=Ti("BOOLEAN",!0),ks=Ti("CHAR",!0),Vs=Ti("VARCHAR",!0),Ms=Ti("NUMERIC",!0),qs=Ti("DECIMAL",!0),Ps=Ti("SIGNED",!0),Ds=Ti("UNSIGNED",!0),Qs=Ti("INT",!0),Fs=Ti("ZEROFILL",!0),Gs=Ti("INTEGER",!0),$s=Ti("JSON",!0),Bs=Ti("JSONB",!0),Hs=Ti("GEOMETRY",!0),Ys=Ti("SMALLINT",!0),Ws=Ti("SERIAL",!0),Xs=Ti("TINYINT",!0),Zs=Ti("TINYTEXT",!0),Ks=Ti("TEXT",!0),Js=Ti("MEDIUMTEXT",!0),zs=Ti("LONGTEXT",!0),tu=Ti("BIGINT",!0),ru=Ti("ENUM",!0),eu=Ti("FLOAT",!0),nu=Ti("DOUBLE",!0),ou=Ti("BIGSERIAL",!0),au=Ti("REAL",!0),su=Ti("DATETIME",!0),uu=Ti("TIME",!0),iu=Ti("TIMESTAMP",!0),cu=Ti("TRUNCATE",!0),lu=Ti("USER",!0),fu=Ti("UUID",!0),pu=Ti("OID",!0),bu=Ti("REGCLASS",!0),vu=Ti("REGCOLLATION",!0),yu=Ti("REGCONFIG",!0),du=Ti("REGDICTIONARY",!0),hu=Ti("REGNAMESPACE",!0),mu=Ti("REGOPER",!0),wu=Ti("REGOPERATOR",!0),Lu=Ti("REGPROC",!0),Ou=Ti("REGPROCEDURE",!0),ju=Ti("REGROLE",!0),Cu=Ti("REGTYPE",!0),gu=Ti("CURRENT_DATE",!0),Eu=(Ti("ADDDATE",!0),Ti("INTERVAL",!0)),Au=Ti("CURRENT_TIME",!0),Tu=Ti("CURRENT_TIMESTAMP",!0),Su=Ti("SYSTEM_USER",!0),Uu=Ti("GLOBAL",!0),_u=Ti("SESSION",!0),xu=Ti("PERSIST",!0),Iu=Ti("PERSIST_ONLY",!0),Nu=Ti("VIEW",!0),Ru=Ti("@",!1),ku=Ti("@@",!1),Vu=Ti("$$",!1),Mu=Ti("return",!0),qu=Ti("::",!1),Pu=Ti("DUAL",!0),Du=Ti("ADD",!0),Qu=Ti("INDEX",!0),Fu=Ti("FULLTEXT",!0),Gu=Ti("SPATIAL",!0),$u=Ti("COMMENT",!0),Bu=Ti("CONCURRENTLY",!0),Hu=Ti("REFERENCES",!0),Yu=Ti("SQL_CALC_FOUND_ROWS",!0),Wu=Ti("SQL_CACHE",!0),Xu=Ti("SQL_NO_CACHE",!0),Zu=Ti("SQL_SMALL_RESULT",!0),Ku=Ti("SQL_BIG_RESULT",!0),Ju=Ti("SQL_BUFFER_RESULT",!0),zu=Ti(",",!1),ti=Ti("[",!1),ri=Ti("]",!1),ei=Ti("->",!1),ni=Ti("->>",!1),oi=Ti("&&",!1),ai=Ti("/*",!1),si=Ti("*/",!1),ui=Ti("--",!1),ii=(Ti("#",!1),{type:"any"}),ci=/^[ \t\n\r]/,li=Si([" ","\t","\n","\r"],!1,!1),fi=/^[^$]/,pi=Si(["$"],!0,!1),bi=function(t){return{dataType:t}},vi=Ti("bytea",!0),yi=Ti("varying",!0),di=Ti("PRECISION",!0),hi=Ti("WITHOUT",!0),mi=Ti("ZONE",!0),wi=function(t){return{dataType:t}},Li=Ti("RECORD",!0),Oi=0,ji=0,Ci=[{line:1,column:1}],gi=0,Ei=[],Ai=0;if("startRule"in r){if(!(r.startRule in s))throw new Error("Can't start parsing from rule \""+r.startRule+'".');u=s[r.startRule]}function Ti(t,r){return{type:"literal",text:t,ignoreCase:r}}function Si(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function Ui(r){var e,n=Ci[r];if(n)return n;for(e=r-1;!Ci[e];)e--;for(n={line:(n=Ci[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Ci[r]=n,n}function _i(t,r){var e=Ui(t),n=Ui(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function xi(t){Oi<gi||(Oi>gi&&(gi=Oi,Ei=[]),Ei.push(t))}function Ii(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function Ni(){var r,e;return r=Oi,Fb()!==a?((e=function(){var r,e,n,o,s,u,i,c,l,f,p,b;if(r=Oi,(e=Zf())!==a)if(Fb()!==a)if(n=Oi,(o=Np())!==a&&(s=Fb())!==a&&(u=ep())!==a?n=o=[o,s,u]:(Oi=n,n=a),n===a&&(n=null),n!==a)if((o=Fb())!==a)if("function"===t.substr(Oi,8).toLowerCase()?(s=t.substr(Oi,8),Oi+=8):(s=a,0===Ai&&xi(ut)),s!==a)if((u=Fb())!==a)if((i=el())!==a)if(Fb()!==a)if(Rb()!==a)if(Fb()!==a)if((c=ac())===a&&(c=null),c!==a)if(Fb()!==a)if(kb()!==a)if(Fb()!==a)if((l=function(){var r,e,n,o,s;r=Oi,"returns"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(j));e!==a&&Fb()!==a?("setof"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(C)),n===a&&(n=null),n!==a&&Fb()!==a?((o=sv())===a&&(o=el()),o!==a?(ji=r,r=e={type:"returns",keyword:n,expr:o}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,"returns"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(j)),e!==a&&Fb()!==a&&(n=lp())!==a&&Fb()!==a&&(o=Rb())!==a&&Fb()!==a&&(s=Gi())!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e={type:"returns",keyword:"table",expr:s}):(Oi=r,r=a));return r}())===a&&(l=null),l!==a)if(Fb()!==a){for(f=[],p=Yi();p!==a;)f.push(p),p=Yi();f!==a&&(p=Fb())!==a?((b=qb())===a&&(b=null),b!==a&&Fb()!==a?(ji=r,v=n,y=s,d=i,h=c,m=l,w=f,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{args:h||[],type:"create",replace:v&&"or replace",name:{schema:d.db,name:d.table},returns:m,keyword:y&&y.toLowerCase(),options:w||[]}},r=e):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;var v,y,d,h,m,w;return r}())===a&&(e=Vi()),e!==a?(ji=r,r=e):(Oi=r,r=a)):(Oi=r,r=a),r}function Ri(){var r;return(r=function(){var r,e,n,o,s,u,c,l,f;r=Oi,(e=Hf())!==a&&Fb()!==a&&(n=lp())!==a&&Fb()!==a&&(o=Jc())!==a?(ji=r,p=e,b=n,(v=o)&&v.forEach(t=>Cv.add(`${p}::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:p.toLowerCase(),keyword:b.toLowerCase(),name:v}},r=e):(Oi=r,r=a);var p,b,v;r===a&&(r=Oi,(e=Hf())!==a&&Fb()!==a&&(n=gb())!==a&&Fb()!==a?((o=Ub())===a&&(o=null),o!==a&&Fb()!==a?(s=Oi,"if"===t.substr(Oi,2).toLowerCase()?(u=t.substr(Oi,2),Oi+=2):(u=a,0===Ai&&xi(i)),u!==a&&(c=Fb())!==a&&(l=_p())!==a?s=u=[u,c,l]:(Oi=s,s=a),s===a&&(s=null),s!==a&&(u=Fb())!==a&&(c=Wl())!==a&&(l=Fb())!==a?("cascade"===t.substr(Oi,7).toLowerCase()?(f=t.substr(Oi,7),Oi+=7):(f=a,0===Ai&&xi(Rt)),f===a&&("restrict"===t.substr(Oi,8).toLowerCase()?(f=t.substr(Oi,8),Oi+=8):(f=a,0===Ai&&xi(kt))),f===a&&(f=null),f!==a?(ji=r,e=function(t,r,e,n,o,a){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:o,options:a&&[{type:"origin",value:a}]}}}(e,n,o,0,c,f),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a));return r}())===a&&(r=function(){var r;(r=function(){var t,r,e,n,o,s,u,i,c,l;t=Oi,(r=Zf())!==a&&Fb()!==a?((e=Kf())===a&&(e=null),e!==a&&Fb()!==a&&lp()!==a&&Fb()!==a?((n=Pi())===a&&(n=null),n!==a&&Fb()!==a&&(o=Jc())!==a&&Fb()!==a&&(s=function(){var t,r,e,n,o,s,u,i,c;if(t=Oi,(r=Rb())!==a)if(Fb()!==a)if((e=Zi())!==a){for(n=[],o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=Zi())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);o!==a;)n.push(o),o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=Zi())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);n!==a&&(o=Fb())!==a&&(s=kb())!==a?(ji=t,r=hv(e,n),t=r):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;return t}())!==a&&Fb()!==a?((u=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=jc())!==a){for(e=[],n=Oi,(o=Fb())!==a?((s=Ib())===a&&(s=null),s!==a&&(u=Fb())!==a&&(i=jc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a?((s=Ib())===a&&(s=null),s!==a&&(u=Fb())!==a&&(i=jc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())===a&&(u=null),u!==a&&Fb()!==a?((i=op())===a&&(i=ep()),i===a&&(i=null),i!==a&&Fb()!==a?((c=cp())===a&&(c=null),c!==a&&Fb()!==a?((l=qi())===a&&(l=null),l!==a?(ji=t,f=r,p=e,b=n,y=s,d=u,h=i,m=c,w=l,(v=o)&&v.forEach(t=>Cv.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:h&&h[0].toLowerCase(),as:m&&m[0].toLowerCase(),query_expr:w&&w.ast,create_definitions:y,table_options:d}},t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);var f,p,b,v,y,d,h,m,w;t===a&&(t=Oi,(r=Zf())!==a&&Fb()!==a?((e=Kf())===a&&(e=null),e!==a&&Fb()!==a&&lp()!==a&&Fb()!==a?((n=Pi())===a&&(n=null),n!==a&&Fb()!==a&&(o=Jc())!==a&&Fb()!==a&&(s=function t(){var r,e;(r=function(){var t,r;t=Oi,Sp()!==a&&Fb()!==a&&(r=Jc())!==a?(ji=t,t={type:"like",table:r}):(Oi=t,t=a);return t}())===a&&(r=Oi,Rb()!==a&&Fb()!==a&&(e=t())!==a&&Fb()!==a&&kb()!==a?(ji=r,(n=e).parentheses=!0,r=n):(Oi=r,r=a));var n;return r}())!==a?(ji=t,r=function(t,r,e,n,o){return n&&n.forEach(t=>Cv.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(r,e,n,o,s),t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m,w,L,O,j;r=Oi,(e=Zf())!==a&&Fb()!==a?(n=Oi,(o=Np())!==a&&(s=Fb())!==a&&(u=ep())!==a?n=o=[o,s,u]:(Oi=n,n=a),n===a&&(n=null),n!==a&&(o=Fb())!==a?((s=Sb())===a&&(s=null),s!==a&&(u=Fb())!==a?("trigger"===t.substr(Oi,7).toLowerCase()?(i=t.substr(Oi,7),Oi+=7):(i=a,0===Ai&&xi(or)),i!==a&&Fb()!==a&&(c=cf())!==a&&Fb()!==a?("before"===t.substr(Oi,6).toLowerCase()?(l=t.substr(Oi,6),Oi+=6):(l=a,0===Ai&&xi(ar)),l===a&&("after"===t.substr(Oi,5).toLowerCase()?(l=t.substr(Oi,5),Oi+=5):(l=a,0===Ai&&xi(sr)),l===a&&("instead of"===t.substr(Oi,10).toLowerCase()?(l=t.substr(Oi,10),Oi+=10):(l=a,0===Ai&&xi(ur)))),l!==a&&Fb()!==a&&(f=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=wc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Np())!==a&&(u=Fb())!==a&&(i=wc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Np())!==a&&(u=Fb())!==a&&(i=wc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&Fb()!==a?("on"===t.substr(Oi,2).toLowerCase()?(p=t.substr(Oi,2),Oi+=2):(p=a,0===Ai&&xi(Q)),p!==a&&Fb()!==a&&(b=el())!==a&&Fb()!==a?(v=Oi,(y=up())!==a&&(d=Fb())!==a&&(h=el())!==a?v=y=[y,d,h]:(Oi=v,v=a),v===a&&(v=null),v!==a&&(y=Fb())!==a?((d=function(){var r,e,n,o,s;r=Oi,e=Oi,"not"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(M));n===a&&(n=null);n!==a&&(o=Fb())!==a?("deferrable"===t.substr(Oi,10).toLowerCase()?(s=t.substr(Oi,10),Oi+=10):(s=a,0===Ai&&xi(fr)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a);e!==a&&(n=Fb())!==a?("initially immediate"===t.substr(Oi,19).toLowerCase()?(o=t.substr(Oi,19),Oi+=19):(o=a,0===Ai&&xi(pr)),o===a&&("initially deferred"===t.substr(Oi,18).toLowerCase()?(o=t.substr(Oi,18),Oi+=18):(o=a,0===Ai&&xi(br))),o!==a?(ji=r,i=o,e={keyword:(u=e)&&u[0]?u[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},r=e):(Oi=r,r=a)):(Oi=r,r=a);var u,i;return r}())===a&&(d=null),d!==a&&(h=Fb())!==a?((m=function(){var r,e,n,o;r=Oi,"for"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(vr));e!==a&&Fb()!==a?("each"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(yr)),n===a&&(n=null),n!==a&&Fb()!==a?("row"===t.substr(Oi,3).toLowerCase()?(o=t.substr(Oi,3),Oi+=3):(o=a,0===Ai&&xi(dr)),o===a&&("statement"===t.substr(Oi,9).toLowerCase()?(o=t.substr(Oi,9),Oi+=9):(o=a,0===Ai&&xi(hr))),o!==a?(ji=r,s=e,i=o,e={keyword:(u=n)?`${s.toLowerCase()} ${u.toLowerCase()}`:s.toLowerCase(),args:i.toLowerCase()},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var s,u,i;return r}())===a&&(m=null),m!==a&&Fb()!==a?((w=function(){var t,r;t=Oi,Mp()!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(r=Il())!==a&&Fb()!==a&&kb()!==a?(ji=t,t={type:"when",cond:r,parentheses:!0}):(Oi=t,t=a);return t}())===a&&(w=null),w!==a&&Fb()!==a?("execute"===t.substr(Oi,7).toLowerCase()?(L=t.substr(Oi,7),Oi+=7):(L=a,0===Ai&&xi(ir)),L!==a&&Fb()!==a?("procedure"===t.substr(Oi,9).toLowerCase()?(O=t.substr(Oi,9),Oi+=9):(O=a,0===Ai&&xi(cr)),O===a&&("function"===t.substr(Oi,8).toLowerCase()?(O=t.substr(Oi,8),Oi+=8):(O=a,0===Ai&&xi(ut))),O!==a&&Fb()!==a&&(j=ev())!==a?(ji=r,C=s,g=i,A=f,T=b,S=v,U=d,_=m,x=w,I=O,N=j,e={type:"create",replace:n&&"or replace",constraint:c,location:(E=l)&&E.toLowerCase(),events:A,table:T,from:S&&S[2],deferrable:U,for_each:_,when:x,execute:{keyword:"execute "+I.toLowerCase(),expr:N},constraint_type:g&&g.toLowerCase(),keyword:g&&g.toLowerCase(),constraint_kw:C&&C.toLowerCase(),resource:"constraint"},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var C,g,E,A,T,S,U,_,x,I,N;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,p,b,v,y,d,h,m;r=Oi,(e=Zf())!==a&&Fb()!==a?("extension"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(c)),n!==a&&Fb()!==a?((o=Pi())===a&&(o=null),o!==a&&Fb()!==a?((s=cf())===a&&(s=xf()),s!==a&&Fb()!==a?((u=hp())===a&&(u=null),u!==a&&Fb()!==a?(i=Oi,"schema"===t.substr(Oi,6).toLowerCase()?(p=t.substr(Oi,6),Oi+=6):(p=a,0===Ai&&xi(l)),p!==a&&(b=Fb())!==a&&(v=cf())!==a?i=p=[p,b,v]:(Oi=i,i=a),i===a&&(i=xf()),i===a&&(i=null),i!==a&&(p=Fb())!==a?(b=Oi,"version"===t.substr(Oi,7).toLowerCase()?(v=t.substr(Oi,7),Oi+=7):(v=a,0===Ai&&xi(f)),v!==a&&(y=Fb())!==a?((d=cf())===a&&(d=xf()),d!==a?b=v=[v,y,d]:(Oi=b,b=a)):(Oi=b,b=a),b===a&&(b=null),b!==a&&(v=Fb())!==a?(y=Oi,(d=up())!==a&&(h=Fb())!==a?((m=cf())===a&&(m=xf()),m!==a?y=d=[d,h,m]:(Oi=y,y=a)):(Oi=y,y=a),y===a&&(y=null),y!==a?(ji=r,w=o,L=s,O=u,j=i,C=b,g=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:w,extension:Ov(L),with:O&&O[0].toLowerCase(),schema:Ov(j&&j[2].toLowerCase()),version:Ov(C&&C[2]),from:Ov(g&&g[2])},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var w,L,O,j,C,g;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m,w;r=Oi,(e=Zf())!==a&&Fb()!==a?((n=Ab())===a&&(n=null),n!==a&&Fb()!==a&&(o=gb())!==a&&Fb()!==a?((s=Ub())===a&&(s=null),s!==a&&Fb()!==a?((u=Kl())===a&&(u=null),u!==a&&Fb()!==a&&(i=pp())!==a&&Fb()!==a&&(c=el())!==a&&Fb()!==a?((l=Xc())===a&&(l=null),l!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(f=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Xi())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Xi())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Xi())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?(p=Oi,(b=hp())!==a&&(v=Fb())!==a&&(y=Rb())!==a&&(d=Fb())!==a&&(h=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Kc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Kc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Kc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&(m=Fb())!==a&&(w=kb())!==a?p=b=[b,v,y,d,h,m,w]:(Oi=p,p=a),p===a&&(p=null),p!==a&&(b=Fb())!==a?(v=Oi,(y=function(){var r,e,n,o;r=Oi,"tablespace"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Qa));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TABLESPACE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(d=Fb())!==a&&(h=cf())!==a?v=y=[y,d,h]:(Oi=v,v=a),v===a&&(v=null),v!==a&&(y=Fb())!==a?((d=al())===a&&(d=null),d!==a&&(h=Fb())!==a?(ji=r,L=e,O=n,j=o,C=s,g=u,E=i,A=c,T=l,S=f,U=p,_=v,x=d,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:L[0].toLowerCase(),index_type:O&&O.toLowerCase(),keyword:j.toLowerCase(),concurrently:C&&C.toLowerCase(),index:g,on_kw:E[0].toLowerCase(),table:A,index_using:T,index_columns:S,with:U&&U[4],with_before_where:!0,tablespace:_&&{type:"origin",value:_[2]},where:x}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var L,O,j,C,g,E,A,T,S,U,_,x;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=Zf())!==a&&Fb()!==a?((n=Kf())===a&&(n=Jf()),n===a&&(n=null),n!==a&&Fb()!==a&&function(){var r,e,n,o;r=Oi,"sequence"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Zr));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SEQUENCE"):(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a?((o=Pi())===a&&(o=null),o!==a&&Fb()!==a&&(s=el())!==a&&Fb()!==a?(u=Oi,(i=cp())!==a&&(c=Fb())!==a&&(l=zl())!==a?u=i=[i,c,l]:(Oi=u,u=a),u===a&&(u=null),u!==a&&(i=Fb())!==a?((c=function(){var t,r,e,n,o,s;if(t=Oi,(r=Wi())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Wi())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Wi())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e,1),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())===a&&(c=null),c!==a?(ji=r,f=e,p=n,b=o,y=u,d=c,(v=s).as=y&&y[2],e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:f[0].toLowerCase(),keyword:"sequence",temporary:p&&p[0].toLowerCase(),if_not_exists:b,sequence:[v],create_definitions:d}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var f,p,b,v,y,d;return r}())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=Zf())!==a&&Fb()!==a?((n=function(){var r,e,n,o;r=Oi,"database"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Kr));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DATABASE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(n=fp()),n!==a&&Fb()!==a?((o=Pi())===a&&(o=null),o!==a&&Fb()!==a&&(s=rv())!==a&&Fb()!==a?((u=function(){var t,r,e,n,o,s;if(t=Oi,(r=Oc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Oc())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Oc())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e,1),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())===a&&(u=null),u!==a?(ji=r,e=function(t,r,e,n,o){const a=r.toLowerCase();return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:e,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,s,u),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=Zf())!==a&&Fb()!==a?("domain"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(ct)),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a?((s=cp())===a&&(s=null),s!==a&&Fb()!==a&&(u=sv())!==a&&Fb()!==a?((i=zi())===a&&(i=null),i!==a&&Fb()!==a?((c=rc())===a&&(c=null),c!==a&&Fb()!==a?((l=dc())===a&&(l=null),l!==a?(ji=r,e=function(t,r,e,n,o,a,s,u){u&&(u.type="constraint");const i=[a,s,u].filter(t=>t);return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t[0].toLowerCase(),keyword:r.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i}}}(e,n,o,s,u,i,c,l),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u,i;r=Oi,(e=Zf())!==a&&Fb()!==a?("type"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(it)),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a&&(s=cp())!==a&&Fb()!==a&&(u=eb())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((i=Al())===a&&(i=null),i!==a&&Fb()!==a&&kb()!==a?(ji=r,c=e,l=n,f=o,p=s,b=u,(v=i).parentheses=!0,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:c[0].toLowerCase(),keyword:l.toLowerCase(),name:{schema:f.db,name:f.table},as:p&&p[0]&&p[0].toLowerCase(),resource:b.toLowerCase(),create_definitions:v}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var c,l,f,p,b,v;r===a&&(r=Oi,(e=Zf())!==a&&Fb()!==a?("type"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(it)),n!==a&&Fb()!==a&&(o=el())!==a?(ji=r,e=function(t,r,e){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t[0].toLowerCase(),keyword:r.toLowerCase(),name:{schema:e.db,name:e.table}}}}(e,n,o),r=e):(Oi=r,r=a)):(Oi=r,r=a));return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,d,h,m,w,L,O,j,C;r=Oi,(e=Zf())!==a&&Fb()!==a?(n=Oi,(o=Np())!==a&&(s=Fb())!==a&&(u=ep())!==a?n=o=[o,s,u]:(Oi=n,n=a),n===a&&(n=null),n!==a&&(o=Fb())!==a?((s=Jf())===a&&(s=Kf()),s===a&&(s=null),s!==a&&(u=Fb())!==a?((i=rp())===a&&(i=null),i!==a&&Fb()!==a&&function(){var r,e,n,o;r=Oi,"view"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Nu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="VIEW"):(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a&&(c=el())!==a&&Fb()!==a?(l=Oi,(f=Rb())!==a&&(d=Fb())!==a&&(h=Xl())!==a&&(m=Fb())!==a&&(w=kb())!==a?l=f=[f,d,h,m,w]:(Oi=l,l=a),l===a&&(l=null),l!==a&&(f=Fb())!==a?(d=Oi,(h=hp())!==a&&(m=Fb())!==a&&(w=Rb())!==a&&(L=Fb())!==a&&(O=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Di())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Di())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Di())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&(j=Fb())!==a&&(C=kb())!==a?d=h=[h,m,w,L,O,j,C]:(Oi=d,d=a),d===a&&(d=null),d!==a&&(h=Fb())!==a&&(m=cp())!==a&&(w=Fb())!==a&&(L=Mc())!==a&&(O=Fb())!==a?((j=function(){var r,e,n,o,s;r=Oi,(e=hp())!==a&&Fb()!==a?("cascaded"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(p)),n===a&&("local"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(b))),n!==a&&Fb()!==a?("check"===t.substr(Oi,5).toLowerCase()?(o=t.substr(Oi,5),Oi+=5):(o=a,0===Ai&&xi(v)),o!==a&&Fb()!==a?("OPTION"===t.substr(Oi,6)?(s="OPTION",Oi+=6):(s=a,0===Ai&&xi(y)),s!==a?(ji=r,e=`with ${n.toLowerCase()} check option`,r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,(e=hp())!==a&&Fb()!==a?("check"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(v)),n!==a&&Fb()!==a?("OPTION"===t.substr(Oi,6)?(o="OPTION",Oi+=6):(o=a,0===Ai&&xi(y)),o!==a?(ji=r,r=e="with check option"):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a));return r}())===a&&(j=null),j!==a?(ji=r,g=e,E=n,A=s,T=i,U=l,_=d,x=L,I=j,(S=c).view=S.table,delete S.table,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:g[0].toLowerCase(),keyword:"view",replace:E&&"or replace",temporary:A&&A[0].toLowerCase(),recursive:T&&T.toLowerCase(),columns:U&&U[2],select:x,view:S,with_options:_&&_[4],with:I}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var g,E,A,T,S,U,_,x,I;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=Zf())!==a&&Fb()!==a?(n=Oi,(o=Np())!==a&&(s=Fb())!==a&&(u=ep())!==a?n=o=[o,s,u]:(Oi=n,n=a),n===a&&(n=null),n!==a&&(o=Fb())!==a?("aggregate"===t.substr(Oi,9).toLowerCase()?(s=t.substr(Oi,9),Oi+=9):(s=a,0===Ai&&xi(O)),s!==a&&(u=Fb())!==a&&(i=el())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(c=ec())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(l=function(){var r,e,n,o,s,u,i,c;if(r=Oi,(e=function(){var r,e,n,o,s;r=Oi,"sfunc"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(w));e!==a&&Fb()!==a&&Ob()!==a&&Fb()!==a&&(n=el())!==a&&Fb()!==a&&Ib()!==a&&Fb()!==a?("stype"===t.substr(Oi,5).toLowerCase()?(o=t.substr(Oi,5),Oi+=5):(o=a,0===Ai&&xi(L)),o!==a&&Fb()!==a&&Ob()!==a&&Fb()!==a&&(s=sv())!==a?(ji=r,i=s,e=[{type:"sfunc",symbol:"=",value:{schema:(u=n).db,name:u.table}},{type:"stype",symbol:"=",value:i}],r=e):(Oi=r,r=a)):(Oi=r,r=a);var u,i;return r}())!==a){for(n=[],o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=Qi())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);o!==a;)n.push(o),o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=Qi())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);n!==a?(ji=r,e=hv(e,n),r=e):(Oi=r,r=a)}else Oi=r,r=a;return r}())!==a&&Fb()!==a&&kb()!==a?(ji=r,f=n,p=i,b=c,v=l,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"create",keyword:"aggregate",replace:f&&"or replace",name:{schema:p.db,name:p.table},args:{parentheses:!0,expr:b,orderby:b.orderby},options:v}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var f,p,b,v;return r}());return r}())===a&&(r=Hi())===a&&(r=function(){var t,r,e,n;t=Oi,(r=pb())!==a&&Fb()!==a?((e=lp())===a&&(e=null),e!==a&&Fb()!==a&&(n=Jc())!==a?(ji=t,o=r,s=e,(u=n)&&u.forEach(t=>Cv.add(`${o}::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:o.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:u}},t=r):(Oi=t,t=a)):(Oi=t,t=a);var o,s,u;return t}())===a&&(r=function(){var t,r,e;t=Oi,(r=np())!==a&&Fb()!==a&&lp()!==a&&Fb()!==a&&(e=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Wc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Wc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Wc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=t,(n=e).forEach(t=>t.forEach(t=>t.table&&Cv.add(`rename::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`))),r={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"rename",table:n}},t=r):(Oi=t,t=a);var n;return t}())===a&&(r=function(){var r,e,n;r=Oi,(e=function(){var r,e,n,o;r=Oi,"call"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ss));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CALL"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a&&(n=ev())!==a?(ji=r,o=n,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"call",expr:o}},r=e):(Oi=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n;r=Oi,(e=function(){var r,e,n,o;r=Oi,"use"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(ga));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a&&(n=Kl())!==a?(ji=r,o=n,Cv.add(`use::${o}::null`),e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"use",db:o}},r=e):(Oi=r,r=a);var o;return r}())===a&&(r=function(){var r;(r=function(){var t,r,e,n;t=Oi,(r=Yf())!==a&&Fb()!==a&&lp()!==a&&Fb()!==a&&(e=Jc())!==a&&Fb()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=sc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=sc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=sc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=t,s=n,(o=e)&&o.length>0&&o.forEach(t=>Cv.add(`alter::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),r={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"alter",table:o,expr:s}},t=r):(Oi=t,t=a);var o,s;return t}())===a&&(r=function(){var t,r,e,n,o;t=Oi,(r=Yf())!==a&&Fb()!==a&&(e=fp())!==a&&Fb()!==a&&(n=cf())!==a&&Fb()!==a?((o=uc())===a&&(o=ic())===a&&(o=cc()),o!==a?(ji=t,r=function(t,r,e){const n=t.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"alter",keyword:n,schema:r,expr:e}}}(e,n,o),t=r):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(r=function(){var r,e,n,o,s;r=Oi,(e=Yf())!==a&&Fb()!==a?("domain"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(ct)),n===a&&("type"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(it))),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a?((s=uc())===a&&(s=ic())===a&&(s=cc()),s!==a?(ji=r,e=function(t,r,e){const n=t.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"alter",keyword:n,name:{schema:r.db,name:r.table},expr:e}}}(n,o,s),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f;r=Oi,(e=Yf())!==a&&Fb()!==a?("function"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(ut)),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a?(s=Oi,(u=Rb())!==a&&(i=Fb())!==a?((c=ac())===a&&(c=null),c!==a&&(l=Fb())!==a&&(f=kb())!==a?s=u=[u,i,c,l,f]:(Oi=s,s=a)):(Oi=s,s=a),s===a&&(s=null),s!==a&&(u=Fb())!==a?((i=uc())===a&&(i=ic())===a&&(i=cc()),i!==a?(ji=r,e=function(t,r,e,n){const o=t.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const a={};return e&&e[0]&&(a.parentheses=!0),a.expr=e&&e[2],{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"alter",keyword:o,name:{schema:r.db,name:r.table},args:a,expr:n}}}(n,o,s,i),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=Yf())!==a&&Fb()!==a?("aggregate"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(O)),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(s=ec())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((u=uc())===a&&(u=ic())===a&&(u=cc()),u!==a?(ji=r,e=function(t,r,e,n){const o=t.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"alter",keyword:o,name:{schema:r.db,name:r.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n}}}(n,o,s,u),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}());return r}())===a&&(r=function(){var r,e,n,o;r=Oi,(e=ip())!==a&&Fb()!==a?((n=function(){var r,e,n,o;r=Oi,"global"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Uu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="GLOBAL"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Oi,"session"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(_u));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SESSION"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Oi,"local"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(b));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="LOCAL"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Oi,"persist"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(xu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="PERSIST"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Oi,"persist_only"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Iu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="PERSIST_ONLY"):(Oi=r,r=a)):(Oi=r,r=a);return r}()),n===a&&(n=null),n!==a&&Fb()!==a&&(o=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Zb())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Zb())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Zb())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=r,s=n,(u=o).keyword=s,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"set",keyword:s,expr:u}},r=e):(Oi=r,r=a)):(Oi=r,r=a);var s,u;return r}())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=function(){var r,e,n,o;r=Oi,"lock"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Yt));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a?((n=lp())===a&&(n=null),n!==a&&Fb()!==a&&(o=Jc())!==a&&Fb()!==a?((s=function(){var r,e,n,o;r=Oi,"in"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(Nr));e!==a&&Fb()!==a?("access share"===t.substr(Oi,12).toLowerCase()?(n=t.substr(Oi,12),Oi+=12):(n=a,0===Ai&&xi(Rr)),n===a&&("row share"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(kr)),n===a&&("row exclusive"===t.substr(Oi,13).toLowerCase()?(n=t.substr(Oi,13),Oi+=13):(n=a,0===Ai&&xi(Vr)),n===a&&("share update exclusive"===t.substr(Oi,22).toLowerCase()?(n=t.substr(Oi,22),Oi+=22):(n=a,0===Ai&&xi(Mr)),n===a&&("share row exclusive"===t.substr(Oi,19).toLowerCase()?(n=t.substr(Oi,19),Oi+=19):(n=a,0===Ai&&xi(qr)),n===a&&("exclusive"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(Xt)),n===a&&("access exclusive"===t.substr(Oi,16).toLowerCase()?(n=t.substr(Oi,16),Oi+=16):(n=a,0===Ai&&xi(Pr)),n===a&&("share"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(Dr))))))))),n!==a&&Fb()!==a?("mode"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(Qr)),o!==a?(ji=r,e={mode:`in ${n.toLowerCase()} mode`},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(s=null),s!==a&&Fb()!==a?("nowait"===t.substr(Oi,6).toLowerCase()?(u=t.substr(Oi,6),Oi+=6):(u=a,0===Ai&&xi(Fr)),u===a&&(u=null),u!==a?(ji=r,i=n,l=s,f=u,(c=o)&&c.forEach(t=>Cv.add(`lock::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(t=>({table:t})),lock_mode:l,nowait:f}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var i,c,l,f;return r}())===a&&(r=function(){var r,e,n;r=Oi,(e=Bf())!==a&&Fb()!==a?("tables"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(Gr)),n!==a?(ji=r,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"show",keyword:"tables"}},r=e):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,(e=Bf())!==a&&Fb()!==a&&(n=av())!==a?(ji=r,o=n,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"show",keyword:"var",var:o}},r=e):(Oi=r,r=a));var o;return r}())===a&&(r=function(){var r,e,n,o;r=Oi,(e=function(){var r,e,n,o;r=Oi,"deallocate"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Fa));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DEALLOCATE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a?("prepare"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi($r)),n===a&&(n=null),n!==a&&Fb()!==a?((o=cf())===a&&(o=Cp()),o!==a?(ji=r,s=n,u=o,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"deallocate",keyword:s,expr:{type:"default",value:u}}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var s,u;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p;r=Oi,(e=_c())!==a&&Fb()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Ac())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Ac())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Ac())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&Fb()!==a&&(o=pp())!==a&&Fb()!==a?((s=function(){var r,e,n;r=Oi,(e=lp())===a&&("sequence"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Zr)),e===a&&("database"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Kr)),e===a&&("DOMAIN"===t.substr(Oi,6)?(e="DOMAIN",Oi+=6):(e=a,0===Ai&&xi(Jr)),e===a&&("FUNCTION"===t.substr(Oi,8)?(e="FUNCTION",Oi+=8):(e=a,0===Ai&&xi(zr)),e===a&&("procedure"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(cr)),e===a&&("routine"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(te)),e===a&&("language"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(re)),e===a&&("large"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ee)),e===a&&("SCHEMA"===t.substr(Oi,6)?(e="SCHEMA",Oi+=6):(e=a,0===Ai&&xi(ne)))))))))));e!==a&&(ji=r,e={type:"origin",value:e.toUpperCase()});(r=e)===a&&(r=Oi,(e=Cp())!==a&&Fb()!==a?("tables"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(Gr)),n===a&&("sequence"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(Zr)),n===a&&("functions"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(oe)),n===a&&("procedures"===t.substr(Oi,10).toLowerCase()?(n=t.substr(Oi,10),Oi+=10):(n=a,0===Ai&&xi(ae)),n===a&&("routines"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(se)))))),n!==a&&Fb()!==a&&Ap()!==a&&Fb()!==a&&fp()!==a?(ji=r,r=e={type:"origin",value:`all ${n} in schema`}):(Oi=r,r=a)):(Oi=r,r=a));return r}())===a&&(s=null),s!==a&&(u=Fb())!==a&&(i=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Tc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Tc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Tc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&(c=Fb())!==a?((l=$f())===a&&(l=up()),l!==a?(ji=Oi,b=l,({revoke:"from",grant:"to"}[e.type].toLowerCase()===b[0].toLowerCase()?void 0:a)!==a&&Fb()!==a&&(f=Uc())!==a&&Fb()!==a?((p=function(){var r,e,n;r=Oi,hp()!==a&&Fb()!==a?("grant"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ie)),e!==a&&Fb()!==a?("option"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(ce)),n!==a?(ji=r,r={type:"origin",value:"with grant option"}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(p=null),p!==a?(ji=r,e=function(t,r,e,n,o,a,s){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{...t,keyword:"priv",objects:r,on:{object_type:e,priv_level:n},to_from:o[0],user_or_roles:a,with:s}}}(e,n,s,i,l,f,p),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var b;r===a&&(r=Oi,(e=_c())!==a&&Fb()!==a&&(n=Jl())!==a&&Fb()!==a?((o=$f())===a&&(o=up()),o!==a?(ji=Oi,(function(t,r,e){return{revoke:"from",grant:"to"}[t.type].toLowerCase()===e[0].toLowerCase()}(e,0,o)?void 0:a)!==a&&(s=Fb())!==a&&(u=Uc())!==a&&(i=Fb())!==a?((c=function(){var r,e,n;r=Oi,hp()!==a&&Fb()!==a?("admin"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(le)),e!==a&&Fb()!==a?("option"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(ce)),n!==a?(ji=r,r={type:"origin",value:"with admin option"}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(c=null),c!==a?(ji=r,e=function(t,r,e,n,o){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{...t,keyword:"role",objects:r.map(t=>({priv:{type:"string",value:t}})),to_from:e[0],user_or_roles:n,with:o}}}(e,n,o,u,c),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a));return r}())===a&&(r=function(){var r,e,n,o,s,u,c,l,f,p,b,v,y;r=Oi,"if"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(i));e!==a&&Fb()!==a&&(n=Il())!==a&&Fb()!==a?("then"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(be)),o!==a&&Fb()!==a&&(s=ki())!==a&&Fb()!==a?((u=qb())===a&&(u=null),u!==a&&Fb()!==a?((c=function(){var t,r,e,n,o,s;if(t=Oi,(r=xc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=xc())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=xc())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e,1),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())===a&&(c=null),c!==a&&Fb()!==a?(l=Oi,(f=qp())!==a&&(p=Fb())!==a&&(b=ki())!==a?l=f=[f,p,b]:(Oi=l,l=a),l===a&&(l=null),l!==a&&(f=Fb())!==a?((p=qb())===a&&(p=null),p!==a&&(b=Fb())!==a?("end"===t.substr(Oi,3).toLowerCase()?(v=t.substr(Oi,3),Oi+=3):(v=a,0===Ai&&xi(ve)),v!==a&&Fb()!==a?("if"===t.substr(Oi,2).toLowerCase()?(y=t.substr(Oi,2),Oi+=2):(y=a,0===Ai&&xi(i)),y!==a?(ji=r,d=n,h=s,m=u,w=c,L=l,O=p,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"if",keyword:"if",boolean_expr:d,semicolons:[m||"",O||""],prefix:{type:"origin",value:"then"},if_expr:h,elseif_expr:w,else_expr:L&&L[2],suffix:{type:"origin",value:"end if"}}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var d,h,m,w,L,O;return r}())===a&&(r=function(){var r,e,n,o,s;r=Oi,"raise"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(_e));e!==a&&Fb()!==a?((n=function(){var r;"debug"===t.substr(Oi,5).toLowerCase()?(r=t.substr(Oi,5),Oi+=5):(r=a,0===Ai&&xi(ye));r===a&&("log"===t.substr(Oi,3).toLowerCase()?(r=t.substr(Oi,3),Oi+=3):(r=a,0===Ai&&xi(de)),r===a&&("info"===t.substr(Oi,4).toLowerCase()?(r=t.substr(Oi,4),Oi+=4):(r=a,0===Ai&&xi(he)),r===a&&("notice"===t.substr(Oi,6).toLowerCase()?(r=t.substr(Oi,6),Oi+=6):(r=a,0===Ai&&xi(me)),r===a&&("warning"===t.substr(Oi,7).toLowerCase()?(r=t.substr(Oi,7),Oi+=7):(r=a,0===Ai&&xi(we)),r===a&&("exception"===t.substr(Oi,9).toLowerCase()?(r=t.substr(Oi,9),Oi+=9):(r=a,0===Ai&&xi(Le)))))));return r}())===a&&(n=null),n!==a&&Fb()!==a?((o=function(){var r,e,n,o,s,u,i,c;if(r=Oi,(e=xf())!==a){for(n=[],o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=tv())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);o!==a;)n.push(o),o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=tv())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);n!==a?(ji=r,e={type:"format",keyword:e,expr:(l=n)&&l.map(t=>t[3])},r=e):(Oi=r,r=a)}else Oi=r,r=a;var l;r===a&&(r=Oi,"sqlstate"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Ue)),e!==a&&(n=Fb())!==a&&(o=xf())!==a?(ji=r,r=e={type:"sqlstate",keyword:{type:"origin",value:"SQLSTATE"},expr:[o]}):(Oi=r,r=a),r===a&&(r=Oi,(e=Kl())!==a&&(ji=r,e={type:"condition",expr:[{type:"default",value:e}]}),r=e));return r}())===a&&(o=null),o!==a&&Fb()!==a?((s=function(){var r,e,n,o,s,u,i,c,f,p;if(r=Oi,(e=dp())!==a)if(Fb()!==a)if("message"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(Oe)),n===a&&("detail"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(je)),n===a&&("hint"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(Ce)),n===a&&("errcode"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(ge)),n===a&&("column"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(Ee)),n===a&&("constraint"===t.substr(Oi,10).toLowerCase()?(n=t.substr(Oi,10),Oi+=10):(n=a,0===Ai&&xi(Ae)),n===a&&("datatype"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(Te)),n===a&&("table"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(Se)),n===a&&("schema"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(l)))))))))),n!==a)if(Fb()!==a)if(Ob()!==a)if(Fb()!==a)if((o=Il())!==a){for(s=[],u=Oi,(i=Fb())!==a&&(c=Ib())!==a&&(f=Fb())!==a&&(p=Il())!==a?u=i=[i,c,f,p]:(Oi=u,u=a);u!==a;)s.push(u),u=Oi,(i=Fb())!==a&&(c=Ib())!==a&&(f=Fb())!==a&&(p=Il())!==a?u=i=[i,c,f,p]:(Oi=u,u=a);s!==a?(ji=r,e=function(t,r,e){const n=[r];return e&&e.forEach(t=>n.push(t[3])),{type:"using",option:t,symbol:"=",expr:n}}(n,o,s),r=e):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;return r}())===a&&(s=null),s!==a?(ji=r,u=n,i=o,c=s,e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"raise",level:u,using:c,raise:i}},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var u,i,c;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,"execute"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(ir));e!==a&&Fb()!==a&&(n=Kl())!==a&&Fb()!==a?(o=Oi,(s=Rb())!==a&&(u=Fb())!==a&&(i=nv())!==a&&(c=Fb())!==a&&(l=kb())!==a?o=s=[s,u,i,c,l]:(Oi=o,o=a),o===a&&(o=null),o!==a?(ji=r,e=function(t,r){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"execute",name:t,args:r&&{type:"expr_list",value:r[2]}}}}(n,o),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c;r=Oi,(e=function(){var r,e,n;r=Oi,"for"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(vr));e!==a&&(ji=r,e={label:null,keyword:"for"});(r=e)===a&&(r=Oi,(e=Kl())!==a&&Fb()!==a?("for"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(vr)),n!==a?(ji=r,r=e={label:e,keyword:"for"}):(Oi=r,r=a)):(Oi=r,r=a));return r}())!==a&&Fb()!==a&&(n=Kl())!==a&&Fb()!==a&&Ap()!==a&&Fb()!==a&&(o=Ic())!==a&&Fb()!==a?("loop"===t.substr(Oi,4).toLowerCase()?(s=t.substr(Oi,4),Oi+=4):(s=a,0===Ai&&xi(xe)),s!==a&&Fb()!==a&&(u=Vi())!==a&&Fb()!==a&&Pp()!==a&&Fb()!==a?("loop"===t.substr(Oi,4).toLowerCase()?(i=t.substr(Oi,4),Oi+=4):(i=a,0===Ai&&xi(xe)),i!==a&&Fb()!==a?((c=Kl())===a&&(c=null),c!==a?(ji=Oi,f=c,(!(!(l=e).label||!f||l.label!==f)||!l.label&&!f?void 0:a)!==a?(ji=r,e=function(t,r,e,n,o){return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"for",label:o,target:r,query:e,stmts:n.ast}}}(0,n,o,u,c),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var l,f;return r}()),r}function ki(){var r;return(r=qi())===a&&(r=function(){var t,r,e,n,o,s,u,i;t=Oi,(r=Fb())!==a?((e=Nc())===a&&(e=null),e!==a&&Fb()!==a&&Xf()!==a&&Fb()!==a&&(n=Jc())!==a&&Fb()!==a&&ip()!==a&&Fb()!==a&&(o=ml())!==a&&Fb()!==a?((s=Yc())===a&&(s=null),s!==a&&Fb()!==a?((u=al())===a&&(u=null),u!==a&&Fb()!==a?((i=Ll())===a&&(i=null),i!==a?(ji=t,r=function(t,r,e,n,o,a){const s={},u=t=>{const{server:r,db:e,schema:n,as:o,table:a,join:u}=t,i=u?"select":"update",c=[r,e,n].filter(Boolean).join(".")||null;e&&(s[a]=c),a&&Cv.add(`${i}::${c}::${a}`)};return r&&r.forEach(u),n&&n.forEach(u),e&&e.forEach(t=>{if(t.table){const r=wv(t.table);Cv.add(`update::${s[r]||null}::${r}`)}gv.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(Cv),columnList:Lv(gv),ast:{with:t,type:"update",table:r,set:e,from:n,where:o,returning:a}}}(e,n,o,s,u,i),t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=Cl())!==a&&Fb()!==a?((n=sp())===a&&(n=null),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a?((s=jl())===a&&(s=null),s!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(u=Xl())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&(i=Ol())!==a&&Fb()!==a?((c=function(){var r,e,n,o;r=Oi,pp()!==a&&Fb()!==a?("conflict"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(nn)),e!==a&&Fb()!==a?((n=function(){var t,r;t=Oi,Rb()!==a&&Fb()!==a&&(r=sl())!==a&&Fb()!==a&&kb()!==a?(ji=t,t={type:"column",expr:r,parentheses:!0}):(Oi=t,t=a);return t}())===a&&(n=null),n!==a&&Fb()!==a&&(o=function(){var r,e,n,o,s;r=Oi,"do"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(rn));e!==a&&Fb()!==a?("nothing"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(en)),n!==a?(ji=r,r=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,"do"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(rn)),e!==a&&Fb()!==a&&(n=Xf())!==a&&Fb()!==a&&ip()!==a&&Fb()!==a&&(o=ml())!==a&&Fb()!==a?((s=al())===a&&(s=null),s!==a?(ji=r,r=e={keyword:"do",expr:{type:"update",set:o,where:s}}):(Oi=r,r=a)):(Oi=r,r=a));return r}())!==a?(ji=r,r={type:"conflict",keyword:"on",target:n,action:o}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(c=null),c!==a&&Fb()!==a?((l=Ll())===a&&(l=null),l!==a?(ji=r,e=function(t,r,e,n,o,a,s){if(r&&(Cv.add(`insert::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`),r.as=null),n){let t=r&&r.table||null;Array.isArray(o)&&o.forEach((t,r)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),n.forEach(r=>gv.add(`insert::${t}::${r}`))}return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t,table:[r],columns:n,values:o,partition:e,conflict:a,returning:s}}}(e,o,s,u,i,c,l),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var t,r,e,n,o,s,u,i;t=Oi,(r=Cl())!==a&&Fb()!==a?((e=op())===a&&(e=null),e!==a&&Fb()!==a?((n=sp())===a&&(n=null),n!==a&&Fb()!==a&&(o=el())!==a&&Fb()!==a?((s=jl())===a&&(s=null),s!==a&&Fb()!==a&&(u=Ol())!==a&&Fb()!==a?((i=Ll())===a&&(i=null),i!==a?(ji=t,r=function(t,r,e,n,o,a,s){n&&(Cv.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),gv.add(`insert::${n.table}::(.*)`),n.as=null);const u=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:u,returning:s}}}(r,e,n,o,s,u,i),t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o;t=Oi,(r=zf())!==a&&Fb()!==a?((e=Jc())===a&&(e=null),e!==a&&Fb()!==a&&(n=Yc())!==a&&Fb()!==a?((o=al())===a&&(o=null),o!==a?(ji=t,r=function(t,r,e){if(r&&r.forEach(t=>{const{db:r,as:e,schema:n,table:o,join:a}=t,s=a?"select":"delete",u=[r,n].filter(Boolean).join(".")||null;o&&Cv.add(`${s}::${u}::${o}`),a||gv.add(`delete::${o}::(.*)`)}),null===t&&1===r.length){const e=r[0];t=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"delete",table:t,from:r,where:e}}}(e,n,o),t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(r=Ri())===a&&(r=function(){var t,r;t=[],r=Xb();for(;r!==a;)t.push(r),r=Xb();return t}()),r}function Vi(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=ki())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=qb())!==a&&(u=Fb())!==a&&(i=ki())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=qb())!==a&&(u=Fb())!==a&&(i=ki())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(Cv),columnList:Lv(gv),ast:n}}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Mi(){var r,e,n,o;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"union"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Xa));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a?((n=Cp())===a&&(n=gp()),n===a&&(n=null),n!==a?(ji=r,r=e=(o=n)?"union "+o.toLowerCase():"union"):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=function(){var r,e,n,o;r=Oi,"intersect"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(Za));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e="intersect"),(r=e)===a&&(r=Oi,(e=function(){var r,e,n,o;r=Oi,"except"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ka));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e="except"),r=e)),r}function qi(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Ic())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Mi())!==a&&(u=Fb())!==a&&(i=Ic())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Mi())!==a&&(u=Fb())!==a&&(i=Ic())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a&&(n=Fb())!==a?((o=vl())===a&&(o=null),o!==a&&(s=Fb())!==a?((u=hl())===a&&(u=null),u!==a?(ji=t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&n.value&&n.value.length>0&&(t._limit=n),{tableList:Array.from(Cv),columnList:Lv(gv),ast:t}}(r,e,o,u)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Pi(){var r,e;return r=Oi,"if"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(i)),e!==a&&Fb()!==a&&xp()!==a&&Fb()!==a&&_p()!==a?(ji=r,r=e="IF NOT EXISTS"):(Oi=r,r=a),r}function Di(){var r,e,n;return r=Oi,"check_option"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(d)),e!==a&&Fb()!==a&&Ob()!==a&&Fb()!==a?("cascaded"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(p)),n===a&&("local"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(b))),n!==a?(ji=r,r=e={type:"check_option",value:n,symbol:"="}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"security_barrier"===t.substr(Oi,16).toLowerCase()?(e=t.substr(Oi,16),Oi+=16):(e=a,0===Ai&&xi(h)),e===a&&("security_invoker"===t.substr(Oi,16).toLowerCase()?(e=t.substr(Oi,16),Oi+=16):(e=a,0===Ai&&xi(m))),e!==a&&Fb()!==a&&Ob()!==a&&Fb()!==a&&(n=_f())!==a?(ji=r,r=e=function(t,r){return{type:t.toLowerCase(),value:r.value?"true":"false",symbol:"="}}(e,n)):(Oi=r,r=a)),r}function Qi(){var t,r,e,n;return t=Oi,(r=Kl())!==a&&Fb()!==a&&Ob()!==a&&Fb()!==a?((e=Kl())===a&&(e=Il()),e!==a?(ji=t,t=r={type:r,symbol:"=",value:"string"==typeof(n=e)?{type:"default",value:n}:n}):(Oi=t,t=a)):(Oi=t,t=a),t}function Fi(){var t,r,e;return t=Oi,(r=Wl())!==a&&Fb()!==a&&(e=sv())!==a?(ji=t,t=r={column:r,definition:e}):(Oi=t,t=a),t}function Gi(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Fi())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Fi())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Fi())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function $i(){var r,e,n,o,s,u,i,c,l,f,p,b;return r=Oi,(e=cf())!==a?(ji=Oi,("begin"!==e.toLowerCase()?void 0:a)!==a&&Fb()!==a?("constant"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(g)),n===a&&(n=null),n!==a&&Fb()!==a&&(o=sv())!==a&&Fb()!==a?((s=zi())===a&&(s=null),s!==a&&Fb()!==a?(u=Oi,(i=xp())!==a&&(c=Fb())!==a&&(l=Ff())!==a?u=i=[i,c,l]:(Oi=u,u=a),u===a&&(u=null),u!==a&&(i=Fb())!==a?(c=Oi,(l=Gf())===a&&(":="===t.substr(Oi,2)?(l=":=",Oi+=2):(l=a,0===Ai&&xi(E))),l===a&&(l=null),l!==a&&(f=Fb())!==a?(p=Oi,Ai++,"begin"===t.substr(Oi,5).toLowerCase()?(b=t.substr(Oi,5),Oi+=5):(b=a,0===Ai&&xi(A)),Ai--,b!==a?(Oi=p,p=void 0):p=a,p===a&&(p=Af())===a&&(p=Il()),p!==a?c=l=[l,f,p]:(Oi=c,c=a)):(Oi=c,c=a),c===a&&(c=null),c!==a&&(l=Fb())!==a?((f=qb())===a&&(f=null),f!==a?(ji=r,r=e=function(t,r,e,n,o,a,s){return{keyword:"variable",name:t,constant:r,datatype:e,collate:n,not_null:o&&"not null",definition:a&&a[0]&&{type:"default",keyword:a[0],value:a[2]}}}(e,n,o,s,u,c)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function Bi(){var t,r,e,n,o,s;if(t=Oi,(r=$i())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=$i())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=$i())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e,1)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Hi(){var r,e,n,o;return r=Oi,"declare"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(T)),e!==a&&Fb()!==a&&(n=Bi())!==a?(ji=r,o=n,r=e={tableList:Array.from(Cv),columnList:Lv(gv),ast:{type:"declare",declare:o,symbol:";"}}):(Oi=r,r=a),r}function Yi(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y,d,h,m;if(r=Oi,"LANGUAGE"===t.substr(Oi,8)?(e="LANGUAGE",Oi+=8):(e=a,0===Ai&&xi(S)),e!==a&&(n=Fb())!==a&&(o=cf())!==a&&(s=Fb())!==a?(ji=r,r=e={prefix:"LANGUAGE",type:"default",value:o}):(Oi=r,r=a),r===a&&(r=Oi,"transorm"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(U)),e!==a&&(n=Fb())!==a?(o=Oi,"FOR"===t.substr(Oi,3)?(s="FOR",Oi+=3):(s=a,0===Ai&&xi(_)),s!==a&&(u=Fb())!==a?("TYPE"===t.substr(Oi,4)?(i="TYPE",Oi+=4):(i=a,0===Ai&&xi(x)),i!==a&&(c=Fb())!==a&&(l=cf())!==a?o=s=[s,u,i,c,l]:(Oi=o,o=a)):(Oi=o,o=a),o===a&&(o=null),o!==a&&(s=Fb())!==a?(ji=r,r=e=(m=o)?{prefix:["TRANSORM",m[0].toUpperCase(),m[2].toUpperCase()].join(" "),type:"default",value:m[4]}:{type:"origin",value:"TRANSORM"}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"window"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(I)),e===a&&("immutable"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(N)),e===a&&("stable"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(R)),e===a&&("volatile"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(k)),e===a&&("strict"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(V)))))),e!==a&&(n=Fb())!==a?(ji=r,r=e={type:"origin",value:e}):(Oi=r,r=a),r===a&&(r=Oi,"not"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(M)),e===a&&(e=null),e!==a&&(n=Fb())!==a?("leakproof"===t.substr(Oi,9).toLowerCase()?(o=t.substr(Oi,9),Oi+=9):(o=a,0===Ai&&xi(q)),o!==a&&(s=Fb())!==a?(ji=r,r=e={type:"origin",value:[e,"LEAKPROOF"].filter(t=>t).join(" ")}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"called"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(P)),e===a&&(e=Oi,"returns"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(j)),n!==a&&(o=Fb())!==a?("null"===t.substr(Oi,4).toLowerCase()?(s=t.substr(Oi,4),Oi+=4):(s=a,0===Ai&&xi(D)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a)),e===a&&(e=null),e!==a&&(n=Fb())!==a?("on"===t.substr(Oi,2).toLowerCase()?(o=t.substr(Oi,2),Oi+=2):(o=a,0===Ai&&xi(Q)),o!==a&&(s=Fb())!==a?("null"===t.substr(Oi,4).toLowerCase()?(u=t.substr(Oi,4),Oi+=4):(u=a,0===Ai&&xi(D)),u!==a&&(i=Fb())!==a?("input"===t.substr(Oi,5).toLowerCase()?(c=t.substr(Oi,5),Oi+=5):(c=a,0===Ai&&xi(F)),c!==a&&(l=Fb())!==a?(ji=r,r=e=function(t){return Array.isArray(t)&&(t=[t[0],t[2]].join(" ")),{type:"origin",value:t+" ON NULL INPUT"}}(e)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"external"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(G)),e===a&&(e=null),e!==a&&(n=Fb())!==a?("security"===t.substr(Oi,8).toLowerCase()?(o=t.substr(Oi,8),Oi+=8):(o=a,0===Ai&&xi($)),o!==a&&(s=Fb())!==a?("invoker"===t.substr(Oi,7).toLowerCase()?(u=t.substr(Oi,7),Oi+=7):(u=a,0===Ai&&xi(B)),u===a&&("definer"===t.substr(Oi,7).toLowerCase()?(u=t.substr(Oi,7),Oi+=7):(u=a,0===Ai&&xi(H))),u!==a&&(i=Fb())!==a?(ji=r,r=e=function(t,r){return{type:"origin",value:[t,"SECURITY",r].filter(t=>t).join(" ")}}(e,u)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"parallel"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Y)),e!==a&&(n=Fb())!==a?("unsafe"===t.substr(Oi,6).toLowerCase()?(o=t.substr(Oi,6),Oi+=6):(o=a,0===Ai&&xi(W)),o===a&&("restricted"===t.substr(Oi,10).toLowerCase()?(o=t.substr(Oi,10),Oi+=10):(o=a,0===Ai&&xi(X)),o===a&&("safe"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(Z)))),o!==a&&(s=Fb())!==a?(ji=r,r=e=function(t){return{type:"origin",value:["PARALLEL",t].join(" ")}}(o)):(Oi=r,r=a)):(Oi=r,r=a),r===a))))))){if(r=Oi,(e=cp())!==a)if((n=Fb())!==a){if(o=[],K.test(t.charAt(Oi))?(s=t.charAt(Oi),Oi++):(s=a,0===Ai&&xi(J)),s!==a)for(;s!==a;)o.push(s),K.test(t.charAt(Oi))?(s=t.charAt(Oi),Oi++):(s=a,0===Ai&&xi(J));else o=a;if(o!==a)if((s=Fb())!==a)if((u=Hi())===a&&(u=null),u!==a)if((i=Fb())!==a)if("begin"===t.substr(Oi,5).toLowerCase()?(c=t.substr(Oi,5),Oi+=5):(c=a,0===Ai&&xi(A)),c===a&&(c=null),c!==a)if((l=Fb())!==a)if((f=Vi())!==a)if(Fb()!==a)if((p=Pp())===a&&(p=null),p!==a)if(ji=Oi,h=p,((d=c)&&h||!d&&!h?void 0:a)!==a)if(Fb()!==a)if((b=qb())===a&&(b=null),b!==a)if(Fb()!==a){if(v=[],z.test(t.charAt(Oi))?(y=t.charAt(Oi),Oi++):(y=a,0===Ai&&xi(tt)),y!==a)for(;y!==a;)v.push(y),z.test(t.charAt(Oi))?(y=t.charAt(Oi),Oi++):(y=a,0===Ai&&xi(tt));else v=a;v!==a&&(y=Fb())!==a?(ji=r,r=e=function(t,r,e,n,o,a){const s=t.join(""),u=a.join("");if(s!==u)throw new Error(`start symbol '${s}'is not same with end symbol '${u}'`);return{type:"as",declare:r&&r.ast,begin:e,expr:Array.isArray(n.ast)?n.ast.flat():[n.ast],end:o&&o[0],symbol:s}}(o,u,c,f,p,v)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a}else Oi=r,r=a;else Oi=r,r=a;r===a&&(r=Oi,"cost"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(rt)),e===a&&("rows"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(et))),e!==a&&(n=Fb())!==a&&(o=kf())!==a&&(s=Fb())!==a?(ji=r,r=e=function(t,r){return r.prefix=t,r}(e,o)):(Oi=r,r=a),r===a&&(r=Oi,"support"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(nt)),e!==a&&(n=Fb())!==a&&(o=rv())!==a&&(s=Fb())!==a?(ji=r,r=e=function(t){return{prefix:"support",type:"default",value:t}}(o)):(Oi=r,r=a),r===a&&(r=Oi,(e=ip())!==a&&(n=Fb())!==a&&(o=cf())!==a&&(s=Fb())!==a?(u=Oi,"to"===t.substr(Oi,2).toLowerCase()?(i=t.substr(Oi,2),Oi+=2):(i=a,0===Ai&&xi(ot)),i===a&&(61===t.charCodeAt(Oi)?(i="=",Oi++):(i=a,0===Ai&&xi(at))),i!==a&&(c=Fb())!==a&&(l=Jl())!==a?u=i=[i,c,l]:(Oi=u,u=a),u===a&&(u=Oi,(i=up())!==a&&(c=Fb())!==a?("current"===t.substr(Oi,7).toLowerCase()?(l=t.substr(Oi,7),Oi+=7):(l=a,0===Ai&&xi(st)),l!==a?u=i=[i,c,l]:(Oi=u,u=a)):(Oi=u,u=a)),u===a&&(u=null),u!==a&&(i=Fb())!==a?(ji=r,r=e=function(t,r){let e;if(r){const t=Array.isArray(r[2])?r[2]:[r[2]];e={prefix:r[0],expr:t.map(t=>({type:"default",value:t}))}}return{type:"set",parameter:t,value:e}}(o,u)):(Oi=r,r=a)):(Oi=r,r=a))))}return r}function Wi(){var r;return(r=function(){var r,e,n,o,s,u;return r=Oi,"increment"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(lt)),e!==a&&Fb()!==a?((n=wp())===a&&(n=null),n!==a&&Fb()!==a&&(o=kf())!==a?(ji=r,s=e,u=o,r=e={resource:"sequence",prefix:n?s.toLowerCase()+" by":s.toLowerCase(),value:u}):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(r=function(){var r,e,n;return r=Oi,"minvalue"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(ft)),e!==a&&Fb()!==a&&(n=kf())!==a?(ji=r,r=e=pt(e,n)):(Oi=r,r=a),r===a&&(r=Oi,"no"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(bt)),e!==a&&Fb()!==a?("minvalue"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(ft)),n!==a?(ji=r,r=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(Oi=r,r=a)):(Oi=r,r=a)),r}())===a&&(r=function(){var r,e,n;return r=Oi,"maxvalue"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(vt)),e!==a&&Fb()!==a&&(n=kf())!==a?(ji=r,r=e=pt(e,n)):(Oi=r,r=a),r===a&&(r=Oi,"no"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(bt)),e!==a&&Fb()!==a?("maxvalue"===t.substr(Oi,8).toLowerCase()?(n=t.substr(Oi,8),Oi+=8):(n=a,0===Ai&&xi(vt)),n!==a?(ji=r,r=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(Oi=r,r=a)):(Oi=r,r=a)),r}())===a&&(r=function(){var r,e,n,o,s,u;return r=Oi,"start"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(yt)),e!==a&&Fb()!==a?((n=hp())===a&&(n=null),n!==a&&Fb()!==a&&(o=kf())!==a?(ji=r,s=e,u=o,r=e={resource:"sequence",prefix:n?s.toLowerCase()+" with":s.toLowerCase(),value:u}):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(r=function(){var r,e,n;return r=Oi,"cache"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(dt)),e!==a&&Fb()!==a&&(n=kf())!==a?(ji=r,r=e=pt(e,n)):(Oi=r,r=a),r}())===a&&(r=function(){var r,e,n;return r=Oi,"no"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(bt)),e===a&&(e=null),e!==a&&Fb()!==a?("cycle"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(ht)),n!==a?(ji=r,r=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(r=function(){var r,e,n;return r=Oi,"owned"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(mt)),e!==a&&Fb()!==a&&wp()!==a&&Fb()!==a?("none"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(wt)),n!==a?(ji=r,r=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"owned"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(mt)),e!==a&&Fb()!==a&&wp()!==a&&Fb()!==a&&(n=Wl())!==a?(ji=r,r=e={resource:"sequence",prefix:"owned by",value:n}):(Oi=r,r=a)),r}()),r}function Xi(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y;return r=Oi,(e=Il())!==a&&Fb()!==a?((n=zi())===a&&(n=null),n!==a&&Fb()!==a?((o=Kl())===a&&(o=null),o!==a&&Fb()!==a?((s=Op())===a&&(s=jp()),s===a&&(s=null),s!==a&&Fb()!==a?(u=Oi,"nulls"===t.substr(Oi,5).toLowerCase()?(i=t.substr(Oi,5),Oi+=5):(i=a,0===Ai&&xi(Lt)),i!==a&&(c=Fb())!==a?("first"===t.substr(Oi,5).toLowerCase()?(l=t.substr(Oi,5),Oi+=5):(l=a,0===Ai&&xi(Ot)),l===a&&("last"===t.substr(Oi,4).toLowerCase()?(l=t.substr(Oi,4),Oi+=4):(l=a,0===Ai&&xi(jt))),l!==a?u=i=[i,c,l]:(Oi=u,u=a)):(Oi=u,u=a),u===a&&(u=null),u!==a?(ji=r,f=e,p=n,b=o,v=s,y=u,r=e={...f,collate:p,opclass:b,order_by:v&&v.toLowerCase(),nulls:y&&`${y[0].toLowerCase()} ${y[2].toLowerCase()}`}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function Zi(){var t;return(t=Ji())===a&&(t=pc())===a&&(t=bc())===a&&(t=vc()),t}function Ki(){var r,e,n,o;return(r=function(){var t,r,e;t=Oi,(r=Uf())===a&&(r=Sf());r!==a&&Fb()!==a?((e=rc())===a&&(e=null),e!==a?(ji=t,o=e,(n=r)&&!n.value&&(n.value="null"),t=r={default_val:o,nullable:n}):(Oi=t,t=a)):(Oi=t,t=a);var n,o;t===a&&(t=Oi,(r=rc())!==a&&Fb()!==a?((e=Uf())===a&&(e=Sf()),e===a&&(e=null),e!==a?(ji=t,r=function(t,r){return r&&!r.value&&(r.value="null"),{default_val:t,nullable:r}}(r,e),t=r):(Oi=t,t=a)):(Oi=t,t=a));return t}())===a&&(r=Oi,"auto_increment"===t.substr(Oi,14).toLowerCase()?(e=t.substr(Oi,14),Oi+=14):(e=a,0===Ai&&xi(Ct)),e!==a&&(ji=r,e={auto_increment:e.toLowerCase()}),(r=e)===a&&(r=Oi,"unique"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(gt)),e!==a&&Fb()!==a?("key"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(Et)),n===a&&(n=null),n!==a?(ji=r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"primary"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(At)),e===a&&(e=null),e!==a&&Fb()!==a?("key"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(Et)),n!==a?(ji=r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=Bb())!==a&&(ji=r,e={comment:e}),(r=e)===a&&(r=Oi,(e=zi())!==a&&(ji=r,e={collate:e}),(r=e)===a&&(r=Oi,(e=function(){var r,e,n;r=Oi,"column_format"===t.substr(Oi,13).toLowerCase()?(e=t.substr(Oi,13),Oi+=13):(e=a,0===Ai&&xi(Tt));e!==a&&Fb()!==a?("fixed"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(St)),n===a&&("dynamic"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(Ut)),n===a&&("default"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(_t)))),n!==a?(ji=r,e={type:"column_format",value:n.toLowerCase()},r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e={column_format:e}),(r=e)===a&&(r=Oi,(e=function(){var r,e,n;r=Oi,"storage"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(xt));e!==a&&Fb()!==a?("disk"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(It)),n===a&&("memory"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi(Nt))),n!==a?(ji=r,e={type:"storage",value:n.toLowerCase()},r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e={storage:e}),(r=e)===a&&(r=Oi,(e=hc())!==a&&(ji=r,e={reference_definition:e}),(r=e)===a&&(r=Oi,(e=Lc())!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a&&(o=Zl())!==a?(ji=r,r=e=function(t,r,e){return{character_set:{type:t,value:e,symbol:r}}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a)))))))))),r}function Ji(){var t,r,e,n,o,s,u;return t=Oi,(r=Wl())!==a&&Fb()!==a&&(e=sv())!==a&&Fb()!==a?((n=function(){var t,r,e,n,o,s;if(t=Oi,(r=Ki())!==a)if(Fb()!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ki())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ki())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;return t}())===a&&(n=null),n!==a?(ji=t,o=r,s=e,u=n,gv.add(`create::${o.table}::${o.column}`),t=r={column:o,definition:s,resource:"column",...u||{}}):(Oi=t,t=a)):(Oi=t,t=a),t}function zi(){var r,e,n;return r=Oi,function(){var r,e,n,o;r=Oi,"collate"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Or));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="COLLATE"):(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a?((e=Ob())===a&&(e=null),e!==a&&Fb()!==a&&(n=Kl())!==a?(ji=r,r={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(Oi=r,r=a)):(Oi=r,r=a),r}function tc(){var t,r,e,n,o;return t=Oi,(r=Gf())===a&&(r=Ob()),r===a&&(r=null),r!==a&&Fb()!==a&&(e=Il())!==a?(ji=t,o=e,t=r={type:"default",keyword:(n=r)&&n[0],value:o}):(Oi=t,t=a),t}function rc(){var t,r;return t=Oi,Gf()!==a&&Fb()!==a&&(r=Il())!==a?(ji=t,t={type:"default",value:r}):(Oi=t,t=a),t}function ec(){var t,r,e;return t=Oi,(r=Nb())!==a&&(ji=t,r=[{name:"*"}]),(t=r)===a&&(t=Oi,(r=ac())===a&&(r=null),r!==a&&Fb()!==a&&Lp()!==a&&Fb()!==a&&wp()!==a&&Fb()!==a&&(e=ac())!==a?(ji=t,t=r=function(t,r){const e=t||[];return e.orderby=r,e}(r,e)):(Oi=t,t=a),t===a&&(t=ac())),t}function nc(){var r,e;return r=Oi,(e=Ap())===a&&("out"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Vt)),e===a&&("variadic"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Mt)),e===a&&("inout"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(qt))))),e!==a&&(ji=r,e=e.toUpperCase()),r=e}function oc(){var t,r,e,n,o;return t=Oi,(r=nc())===a&&(r=null),r!==a&&Fb()!==a&&(e=sv())!==a&&Fb()!==a?((n=tc())===a&&(n=null),n!==a?(ji=t,t=r={mode:r,type:e,default:n}):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=nc())===a&&(r=null),r!==a&&Fb()!==a&&(e=cf())!==a&&Fb()!==a&&(n=sv())!==a&&Fb()!==a?((o=tc())===a&&(o=null),o!==a?(ji=t,t=r=function(t,r,e,n){return{mode:t,name:r,type:e,default:n}}(r,e,n,o)):(Oi=t,t=a)):(Oi=t,t=a)),t}function ac(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=oc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=oc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=oc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function sc(){var t;return(t=function(){var t,r,e,n;t=Oi,(r=jb())!==a&&Fb()!==a?((e=Cb())===a&&(e=null),e!==a&&Fb()!==a&&(n=Ji())!==a?(ji=t,o=e,s=n,r={action:"add",...s,keyword:o,resource:"column",type:"alter"},t=r):(Oi=t,t=a)):(Oi=t,t=a);var o,s;return t}())===a&&(t=function(){var t,r;t=Oi,jb()!==a&&Fb()!==a&&(r=vc())!==a?(ji=t,t={action:"add",create_definitions:r,resource:"constraint",type:"alter"}):(Oi=t,t=a);return t}())===a&&(t=function(){var t,r,e;t=Oi,Hf()!==a&&Fb()!==a?((r=Cb())===a&&(r=null),r!==a&&Fb()!==a&&(e=Wl())!==a?(ji=t,t={action:"drop",column:e,keyword:r,resource:"column",type:"alter"}):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(t=function(){var t,r,e;t=Oi,(r=jb())!==a&&Fb()!==a&&(e=pc())!==a?(ji=t,n=e,r={action:"add",type:"alter",...n},t=r):(Oi=t,t=a);var n;return t}())===a&&(t=function(){var t,r,e;t=Oi,(r=jb())!==a&&Fb()!==a&&(e=bc())!==a?(ji=t,n=e,r={action:"add",type:"alter",...n},t=r):(Oi=t,t=a);var n;return t}())===a&&(t=uc())===a&&(t=lc())===a&&(t=fc()),t}function uc(){var t,r,e,n,o;return t=Oi,np()!==a&&Fb()!==a?((r=$f())===a&&(r=cp()),r===a&&(r=null),r!==a&&Fb()!==a&&(e=Kl())!==a?(ji=t,o=e,t={action:"rename",type:"alter",resource:"table",keyword:(n=r)&&n[0].toLowerCase(),table:o}):(Oi=t,t=a)):(Oi=t,t=a),t}function ic(){var r,e,n;return r=Oi,"owner"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Pt)),e!==a&&Fb()!==a&&$f()!==a&&Fb()!==a?((n=Kl())===a&&("current_role"===t.substr(Oi,12).toLowerCase()?(n=t.substr(Oi,12),Oi+=12):(n=a,0===Ai&&xi(Dt)),n===a&&("current_user"===t.substr(Oi,12).toLowerCase()?(n=t.substr(Oi,12),Oi+=12):(n=a,0===Ai&&xi(Qt)),n===a&&("session_user"===t.substr(Oi,12).toLowerCase()?(n=t.substr(Oi,12),Oi+=12):(n=a,0===Ai&&xi(Ft))))),n!==a?(ji=r,r=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(Oi=r,r=a)):(Oi=r,r=a),r}function cc(){var t,r;return t=Oi,ip()!==a&&Fb()!==a&&fp()!==a&&Fb()!==a&&(r=Kl())!==a?(ji=t,t={action:"set",type:"alter",resource:"table",keyword:"schema",table:r}):(Oi=t,t=a),t}function lc(){var r,e,n,o;return r=Oi,"algorithm"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(Gt)),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a?("default"===t.substr(Oi,7).toLowerCase()?(o=t.substr(Oi,7),Oi+=7):(o=a,0===Ai&&xi(_t)),o===a&&("instant"===t.substr(Oi,7).toLowerCase()?(o=t.substr(Oi,7),Oi+=7):(o=a,0===Ai&&xi($t)),o===a&&("inplace"===t.substr(Oi,7).toLowerCase()?(o=t.substr(Oi,7),Oi+=7):(o=a,0===Ai&&xi(Bt)),o===a&&("copy"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(Ht))))),o!==a?(ji=r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function fc(){var r,e,n,o;return r=Oi,"lock"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Yt)),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a?("default"===t.substr(Oi,7).toLowerCase()?(o=t.substr(Oi,7),Oi+=7):(o=a,0===Ai&&xi(_t)),o===a&&("none"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(wt)),o===a&&("shared"===t.substr(Oi,6).toLowerCase()?(o=t.substr(Oi,6),Oi+=6):(o=a,0===Ai&&xi(Wt)),o===a&&("exclusive"===t.substr(Oi,9).toLowerCase()?(o=t.substr(Oi,9),Oi+=9):(o=a,0===Ai&&xi(Xt))))),o!==a?(ji=r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function pc(){var t,r,e,n,o,s,u,i;return t=Oi,(r=gb())===a&&(r=Eb()),r!==a&&Fb()!==a?((e=sf())===a&&(e=null),e!==a&&Fb()!==a?((n=Xc())===a&&(n=null),n!==a&&Fb()!==a&&(o=kc())!==a&&Fb()!==a?((s=Zc())===a&&(s=null),s!==a&&Fb()!==a?(ji=t,u=n,i=s,t=r={index:e,definition:o,keyword:r.toLowerCase(),index_type:u,resource:"index",index_options:i}):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t}function bc(){var r,e,n,o,s,u,i,c,l;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"fulltext"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Fu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="FULLTEXT"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=Oi,"spatial"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Gu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SPATIAL"):(Oi=r,r=a)):(Oi=r,r=a);return r}()),e!==a&&Fb()!==a?((n=gb())===a&&(n=Eb()),n===a&&(n=null),n!==a&&Fb()!==a?((o=sf())===a&&(o=null),o!==a&&Fb()!==a&&(s=kc())!==a&&Fb()!==a?((u=Zc())===a&&(u=null),u!==a&&Fb()!==a?(ji=r,i=e,l=u,r=e={index:o,definition:s,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function vc(){var r;return(r=function(){var r,e,n,o,s,u;r=Oi,(e=yc())===a&&(e=null);e!==a&&Fb()!==a?("primary key"===t.substr(Oi,11).toLowerCase()?(n=t.substr(Oi,11),Oi+=11):(n=a,0===Ai&&xi(Zt)),n!==a&&Fb()!==a?((o=Xc())===a&&(o=null),o!==a&&Fb()!==a&&(s=kc())!==a&&Fb()!==a?((u=Zc())===a&&(u=null),u!==a?(ji=r,c=n,l=o,f=s,p=u,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var i,c,l,f,p;return r}())===a&&(r=function(){var t,r,e,n,o,s,u,i;t=Oi,(r=yc())===a&&(r=null);r!==a&&Fb()!==a&&(e=Ab())!==a&&Fb()!==a?((n=gb())===a&&(n=Eb()),n===a&&(n=null),n!==a&&Fb()!==a?((o=sf())===a&&(o=null),o!==a&&Fb()!==a?((s=Xc())===a&&(s=null),s!==a&&Fb()!==a&&(u=kc())!==a&&Fb()!==a?((i=Zc())===a&&(i=null),i!==a?(ji=t,l=e,f=n,p=o,b=s,v=u,y=i,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);var c,l,f,p,b,v,y;return t}())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=yc())===a&&(e=null);e!==a&&Fb()!==a?("foreign key"===t.substr(Oi,11).toLowerCase()?(n=t.substr(Oi,11),Oi+=11):(n=a,0===Ai&&xi(Kt)),n!==a&&Fb()!==a?((o=sf())===a&&(o=null),o!==a&&Fb()!==a&&(s=kc())!==a&&Fb()!==a?((u=hc())===a&&(u=null),u!==a?(ji=r,c=n,l=o,f=s,p=u,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var i,c,l,f,p;return r}())===a&&(r=dc()),r}function yc(){var t,r,e,n;return t=Oi,(r=Sb())!==a&&Fb()!==a?((e=Kl())===a&&(e=null),e!==a?(ji=t,n=e,t=r={keyword:r.toLowerCase(),constraint:n}):(Oi=t,t=a)):(Oi=t,t=a),t}function dc(){var r,e,n,o,s,u,i;return r=Oi,(e=yc())===a&&(e=null),e!==a&&Fb()!==a?("check"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(v)),n!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(o=Nl())!==a&&Fb()!==a&&kb()!==a?(ji=r,u=n,i=o,r=e={constraint:(s=e)&&s.constraint,definition:[i],constraint_type:u.toLowerCase(),keyword:s&&s.keyword,resource:"constraint"}):(Oi=r,r=a)):(Oi=r,r=a),r}function hc(){var r,e,n,o,s,u,i,c,l,f;return r=Oi,(e=_b())!==a&&Fb()!==a&&(n=el())!==a&&Fb()!==a&&(o=kc())!==a&&Fb()!==a?("match full"===t.substr(Oi,10).toLowerCase()?(s=t.substr(Oi,10),Oi+=10):(s=a,0===Ai&&xi(Jt)),s===a&&("match partial"===t.substr(Oi,13).toLowerCase()?(s=t.substr(Oi,13),Oi+=13):(s=a,0===Ai&&xi(zt)),s===a&&("match simple"===t.substr(Oi,12).toLowerCase()?(s=t.substr(Oi,12),Oi+=12):(s=a,0===Ai&&xi(tr)))),s===a&&(s=null),s!==a&&Fb()!==a?((u=mc())===a&&(u=null),u!==a&&Fb()!==a?((i=mc())===a&&(i=null),i!==a?(ji=r,c=s,l=u,f=i,r=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=mc())!==a&&(ji=r,e={on_action:[e]}),r=e),r}function mc(){var r,e,n,o;return r=Oi,pp()!==a&&Fb()!==a?((e=zf())===a&&(e=Xf()),e!==a&&Fb()!==a&&(n=function(){var r,e,n;r=Oi,(e=vb())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((n=Al())===a&&(n=null),n!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,"restrict"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(kt)),e===a&&("cascade"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Rt)),e===a&&("set null"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(rr)),e===a&&("no action"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(er)),e===a&&("set default"===t.substr(Oi,11).toLowerCase()?(e=t.substr(Oi,11),Oi+=11):(e=a,0===Ai&&xi(nr)),e===a&&(e=vb()))))),e!==a&&(ji=r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==a?(ji=r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(Oi=r,r=a)):(Oi=r,r=a),r}function wc(){var r,e,n,o,s,u,i;return r=Oi,(e=tp())===a&&(e=zf())===a&&(e=pb()),e!==a&&(ji=r,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(r=e)===a&&(r=Oi,(e=Xf())!==a&&Fb()!==a?(n=Oi,"of"===t.substr(Oi,2).toLowerCase()?(o=t.substr(Oi,2),Oi+=2):(o=a,0===Ai&&xi(lr)),o!==a&&(s=Fb())!==a&&(u=sl())!==a?n=o=[o,s,u]:(Oi=n,n=a),n===a&&(n=null),n!==a?(ji=r,r=e=function(t,r){return{keyword:t&&t[0]&&t[0].toLowerCase(),args:r&&{keyword:r[0],columns:r[2]}||null}}(e,n)):(Oi=r,r=a)):(Oi=r,r=a)),r}function Lc(){var r,e,n;return r=Oi,"character"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(mr)),e!==a&&Fb()!==a?("set"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(wr)),n!==a?(ji=r,r=e="CHARACTER SET"):(Oi=r,r=a)):(Oi=r,r=a),r}function Oc(){var r,e,n,o,s,u,i,c,l;return r=Oi,(e=Gf())===a&&(e=null),e!==a&&Fb()!==a?((n=Lc())===a&&("charset"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(Lr)),n===a&&("collate"===t.substr(Oi,7).toLowerCase()?(n=t.substr(Oi,7),Oi+=7):(n=a,0===Ai&&xi(Or)))),n!==a&&Fb()!==a?((o=Ob())===a&&(o=null),o!==a&&Fb()!==a&&(s=Zl())!==a?(ji=r,i=n,c=o,l=s,r=e={keyword:(u=e)&&`${u[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function jc(){var r,e,n,o,s,u,i,c,l;return r=Oi,"auto_increment"===t.substr(Oi,14).toLowerCase()?(e=t.substr(Oi,14),Oi+=14):(e=a,0===Ai&&xi(Ct)),e===a&&("avg_row_length"===t.substr(Oi,14).toLowerCase()?(e=t.substr(Oi,14),Oi+=14):(e=a,0===Ai&&xi(jr)),e===a&&("key_block_size"===t.substr(Oi,14).toLowerCase()?(e=t.substr(Oi,14),Oi+=14):(e=a,0===Ai&&xi(Cr)),e===a&&("max_rows"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(gr)),e===a&&("min_rows"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Er)),e===a&&("stats_sample_pages"===t.substr(Oi,18).toLowerCase()?(e=t.substr(Oi,18),Oi+=18):(e=a,0===Ai&&xi(Ar))))))),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a&&(o=kf())!==a?(ji=r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oc())===a&&(r=Oi,(e=Tb())===a&&("connection"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Tr))),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a&&(o=xf())!==a?(ji=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"compression"===t.substr(Oi,11).toLowerCase()?(e=t.substr(Oi,11),Oi+=11):(e=a,0===Ai&&xi(Sr)),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a?(o=Oi,39===t.charCodeAt(Oi)?(s="'",Oi++):(s=a,0===Ai&&xi(Ur)),s!==a?("zlib"===t.substr(Oi,4).toLowerCase()?(u=t.substr(Oi,4),Oi+=4):(u=a,0===Ai&&xi(_r)),u===a&&("lz4"===t.substr(Oi,3).toLowerCase()?(u=t.substr(Oi,3),Oi+=3):(u=a,0===Ai&&xi(xr)),u===a&&("none"===t.substr(Oi,4).toLowerCase()?(u=t.substr(Oi,4),Oi+=4):(u=a,0===Ai&&xi(wt)))),u!==a?(39===t.charCodeAt(Oi)?(i="'",Oi++):(i=a,0===Ai&&xi(Ur)),i!==a?o=s=[s,u,i]:(Oi=o,o=a)):(Oi=o,o=a)):(Oi=o,o=a),o!==a?(ji=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"engine"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ir)),e!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a&&(o=cf())!==a?(ji=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a)))),r}function Cc(){var r,e,n;return r=Oi,(e=Wf())===a&&(e=tp())===a&&(e=Xf())===a&&(e=zf())===a&&(e=pb())===a&&(e=_b())===a&&("trigger"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(or))),e!==a&&(ji=r,n=e,e={type:"origin",value:Array.isArray(n)?n[0]:n}),r=e}function gc(){var r,e,n,o;return r=Oi,Cp()!==a?(e=Oi,(n=Fb())!==a?("privileges"===t.substr(Oi,10).toLowerCase()?(o=t.substr(Oi,10),Oi+=10):(o=a,0===Ai&&xi(Wr)),o!==a?e=n=[n,o]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=r,r={type:"origin",value:e?"all privileges":"all"}):(Oi=r,r=a)):(Oi=r,r=a),r}function Ec(){var r;return(r=Cc())===a&&(r=function(){var r,e;return r=Oi,"usage"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Br)),e===a&&(e=Wf())===a&&(e=Xf()),e!==a&&(ji=r,e=Hr(e)),r=e}())===a&&(r=function(){var r,e;return r=Oi,(e=Zf())===a&&("connect"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Yr)),e===a&&(e=Kf())===a&&(e=Jf())),e!==a&&(ji=r,e=Hr(e)),r=e}())===a&&(r=function(){var r,e;return r=Oi,"usage"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Br)),e!==a&&(ji=r,e=Xr(e)),(r=e)===a&&(r=gc()),r}())===a&&(r=function(){var r,e;return r=Oi,"execute"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(ir)),e!==a&&(ji=r,e=Xr(e)),(r=e)===a&&(r=gc()),r}()),r}function Ac(){var t,r,e,n,o,s,u,i,c;return t=Oi,(r=Ec())!==a&&Fb()!==a?(e=Oi,(n=Rb())!==a&&(o=Fb())!==a&&(s=sl())!==a&&(u=Fb())!==a&&(i=kb())!==a?e=n=[n,o,s,u,i]:(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=t,t=r={priv:r,columns:(c=e)&&c[2]}):(Oi=t,t=a)):(Oi=t,t=a),t}function Tc(){var t,r,e,n,o;return t=Oi,r=Oi,(e=Kl())!==a&&(n=Fb())!==a&&(o=xb())!==a?r=e=[e,n,o]:(Oi=r,r=a),r===a&&(r=null),r!==a&&(e=Fb())!==a?((n=Kl())===a&&(n=Nb()),n!==a?(ji=t,t=r=function(t,r){return{prefix:t&&t[0],name:r}}(r,n)):(Oi=t,t=a)):(Oi=t,t=a),t}function Sc(){var r,e,n,o;return r=Oi,(e=mp())===a&&(e=null),e!==a&&Fb()!==a&&(n=Kl())!==a?(ji=r,o=n,r=e={name:{type:"origin",value:e?`${group} ${o}`:o}}):(Oi=r,r=a),r===a&&(r=Oi,"public"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(ue)),e===a&&(e=function(){var r,e,n,o;r=Oi,"current_role"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Dt));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CURRENT_ROLE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(e=yb())===a&&(e=db()),e!==a&&(ji=r,e=function(t){return{name:{type:"origin",value:t}}}(e)),r=e),r}function Uc(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Sc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Sc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Sc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function _c(){var r,e,n,o,s,u,i,c;return r=Oi,"grant"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ie)),e!==a&&(ji=r,e={type:"grant"}),(r=e)===a&&(r=Oi,"revoke"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(fe)),e!==a&&Fb()!==a?(n=Oi,"grant"===t.substr(Oi,5).toLowerCase()?(o=t.substr(Oi,5),Oi+=5):(o=a,0===Ai&&xi(ie)),o!==a&&(s=Fb())!==a?("option"===t.substr(Oi,6).toLowerCase()?(u=t.substr(Oi,6),Oi+=6):(u=a,0===Ai&&xi(ce)),u!==a&&(i=Fb())!==a?("for"===t.substr(Oi,3).toLowerCase()?(c=t.substr(Oi,3),Oi+=3):(c=a,0===Ai&&xi(vr)),c!==a?n=o=[o,s,u,i,c]:(Oi=n,n=a)):(Oi=n,n=a)):(Oi=n,n=a),n===a&&(n=null),n!==a?(ji=r,r=e={type:"revoke",grant_option_for:n&&{type:"origin",value:"grant option for"}}):(Oi=r,r=a)):(Oi=r,r=a)),r}function xc(){var r,e,n,o,s,u;return r=Oi,"elseif"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(pe)),e!==a&&Fb()!==a&&(n=Il())!==a&&Fb()!==a?("then"===t.substr(Oi,4).toLowerCase()?(o=t.substr(Oi,4),Oi+=4):(o=a,0===Ai&&xi(be)),o!==a&&Fb()!==a&&(s=ki())!==a&&Fb()!==a?((u=qb())===a&&(u=null),u!==a?(ji=r,r=e={type:"elseif",boolean_expr:n,then:s,semicolon:u}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function Ic(){var r,e,n,o,s,u,i;return r=Oi,(e=Wf())!==a&&(n=Fb())!==a?(59===t.charCodeAt(Oi)?(o=";",Oi++):(o=a,0===Ai&&xi(Ie)),o!==a?(ji=r,r=e={type:"select"}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Mc())===a&&(r=Oi,e=Oi,40===t.charCodeAt(Oi)?(n="(",Oi++):(n=a,0===Ai&&xi(Ne)),n!==a&&(o=Fb())!==a&&(s=Ic())!==a&&(u=Fb())!==a?(41===t.charCodeAt(Oi)?(i=")",Oi++):(i=a,0===Ai&&xi(Re)),i!==a?e=n=[n,o,s,u,i]:(Oi=e,e=a)):(Oi=e,e=a),e!==a&&(ji=r,e={...e[2],parentheses_symbol:!0}),r=e),r}function Nc(){var t,r,e,n,o,s,u,i,c;if(t=Oi,hp()!==a)if(Fb()!==a)if((r=Rc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Rc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Rc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;return t===a&&(t=Oi,Fb()!==a&&hp()!==a&&(r=Fb())!==a&&(e=rp())!==a&&(n=Fb())!==a&&(o=Rc())!==a?(ji=t,(c=o).recursive=!0,t=[c]):(Oi=t,t=a)),t}function Rc(){var t,r,e,n;return t=Oi,(r=xf())===a&&(r=cf()),r!==a&&Fb()!==a?((e=kc())===a&&(e=null),e!==a&&Fb()!==a&&cp()!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=ki())!==a&&Fb()!==a&&kb()!==a?(ji=t,t=r=function(t,r,e){return"string"==typeof t&&(t={type:"default",value:t}),{name:t,stmt:e.ast,columns:r}}(r,e,n)):(Oi=t,t=a)):(Oi=t,t=a),t}function kc(){var t,r;return t=Oi,Rb()!==a&&Fb()!==a&&(r=sl())!==a&&Fb()!==a&&kb()!==a?(ji=t,t=r):(Oi=t,t=a),t}function Vc(){var t,r,e,n,o;return t=Oi,(r=gp())!==a&&Fb()!==a&&pp()!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(e=sl())!==a&&Fb()!==a&&kb()!==a?(ji=t,n=r,o=e,console.lo,t=r={type:n+" ON",columns:o}):(Oi=t,t=a),t===a&&(t=Oi,(r=gp())===a&&(r=null),r!==a&&(ji=t,r=function(t){return{type:t}}(r)),t=r),t}function Mc(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y,d;return r=Oi,Fb()!==a?((e=Nc())===a&&(e=null),e!==a&&Fb()!==a&&Wf()!==a&&Gb()!==a?((n=function(){var t,r,e,n,o,s;if(t=Oi,(r=qc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=qc())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=qc())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())===a&&(n=null),n!==a&&Fb()!==a?((o=Vc())===a&&(o=null),o!==a&&Fb()!==a&&(s=Pc())!==a&&Fb()!==a?((u=Hc())===a&&(u=null),u!==a&&Fb()!==a?((i=Yc())===a&&(i=null),i!==a&&Fb()!==a?((c=Hc())===a&&(c=null),c!==a&&Fb()!==a?((l=al())===a&&(l=null),l!==a&&Fb()!==a?((f=function(){var t,r,e;t=Oi,(r=mp())!==a&&Fb()!==a&&wp()!==a&&Fb()!==a&&(e=Al())!==a?(ji=t,r={columns:e.value},t=r):(Oi=t,t=a);return t}())===a&&(f=null),f!==a&&Fb()!==a?((p=function(){var r,e;r=Oi,function(){var r,e,n,o;r=Oi,"having"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(os));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a&&(e=Nl())!==a?(ji=r,r=e):(Oi=r,r=a);return r}())===a&&(p=null),p!==a&&Fb()!==a?((b=vl())===a&&(b=null),b!==a&&Fb()!==a?((v=hl())===a&&(v=null),v!==a&&Fb()!==a?((y=function(){var r,e;r=Oi,function(){var r,e,n,o;r=Oi,"window"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(I));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a&&(e=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=ul())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=ul())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=ul())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=r,r={keyword:"window",type:"window",expr:e}):(Oi=r,r=a);return r}())===a&&(y=null),y!==a&&Fb()!==a?((d=Hc())===a&&(d=null),d!==a?(ji=r,r=function(t,r,e,n,o,a,s,u,i,c,l,f,p,b){if(o&&s||o&&b||s&&b||o&&s&&b)throw new Error("A given SQL statement can contain at most one INTO clause");return a&&a.forEach(t=>t.table&&Cv.add(`select::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{with:t,type:"select",options:r,distinct:e,columns:n,into:{...o||s||b||{},position:(o?"column":s&&"from")||b&&"end"},from:a,where:u,groupby:i,having:c,orderby:l,limit:f,window:p}}(e,n,o,s,u,i,c,l,f,p,b,v,y,d)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function qc(){var r,e;return r=Oi,(e=function(){var r;"sql_calc_found_rows"===t.substr(Oi,19).toLowerCase()?(r=t.substr(Oi,19),Oi+=19):(r=a,0===Ai&&xi(Yu));return r}())===a&&((e=function(){var r;"sql_cache"===t.substr(Oi,9).toLowerCase()?(r=t.substr(Oi,9),Oi+=9):(r=a,0===Ai&&xi(Wu));return r}())===a&&(e=function(){var r;"sql_no_cache"===t.substr(Oi,12).toLowerCase()?(r=t.substr(Oi,12),Oi+=12):(r=a,0===Ai&&xi(Xu));return r}()),e===a&&(e=function(){var r;"sql_big_result"===t.substr(Oi,14).toLowerCase()?(r=t.substr(Oi,14),Oi+=14):(r=a,0===Ai&&xi(Ku));return r}())===a&&(e=function(){var r;"sql_small_result"===t.substr(Oi,16).toLowerCase()?(r=t.substr(Oi,16),Oi+=16):(r=a,0===Ai&&xi(Zu));return r}())===a&&(e=function(){var r;"sql_buffer_result"===t.substr(Oi,17).toLowerCase()?(r=t.substr(Oi,17),Oi+=17):(r=a,0===Ai&&xi(Ju));return r}())),e!==a&&(ji=r,e=e),r=e}function Pc(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Cp())===a&&(r=Oi,(e=Nb())!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Nb())),r!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Gc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Gc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){gv.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?hv(e,r):[e]}(0,e)):(Oi=t,t=a)}else Oi=t,t=a;if(t===a)if(t=Oi,(r=Gc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Gc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Gc())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Dc(){var t,r;return t=Oi,Vb()!==a&&Fb()!==a?((r=kf())===a&&(r=xf()),r!==a&&Fb()!==a&&Mb()!==a?(ji=t,t={brackets:!0,index:r}):(Oi=t,t=a)):(Oi=t,t=a),t}function Qc(){var t,r,e,n,o,s;if(t=Oi,(r=Dc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Dc())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Dc())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e,1)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Fc(){var t,r,e,n,o;return t=Oi,(r=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Il())!==a){for(e=[],n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np())===a&&(s=Qb()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np())===a&&(s=Qb()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);e!==a?(ji=t,r=function(t,r){t.ast;if(!r||0===r.length)return t;const e=r.length;let n=r[e-1][3];for(let o=e-1;o>=0;o--){const e=0===o?t:r[o-1][3];n=yv(r[o][1],e,n)}return n}(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a&&Fb()!==a?((e=Qc())===a&&(e=null),e!==a?(ji=t,n=r,(o=e)&&(n.array_index=o),t=r=n):(Oi=t,t=a)):(Oi=t,t=a),t}function Gc(){var t,r,e,n,o,s,u,i,c,l,f;if(t=Oi,(r=Yl())!==a&&(ji=t,r={expr:r,as:null}),(t=r)===a){if(t=Oi,(r=Fc())!==a)if((e=Fb())!==a)if((n=Ef())!==a)if((o=Fb())!==a){for(s=[],u=Oi,(i=Fb())!==a?((c=Ql())===a&&(c=Gl()),c!==a&&(l=Fb())!==a&&(f=Fc())!==a?u=i=[i,c,l,f]:(Oi=u,u=a)):(Oi=u,u=a);u!==a;)s.push(u),u=Oi,(i=Fb())!==a?((c=Ql())===a&&(c=Gl()),c!==a&&(l=Fb())!==a&&(f=Fc())!==a?u=i=[i,c,l,f]:(Oi=u,u=a)):(Oi=u,u=a);s!==a&&(u=Fb())!==a?((i=Bc())===a&&(i=null),i!==a?(ji=t,t=r=function(t,r,e,n){return{...r,as:n,type:"cast",expr:t,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]}}}(r,n,s,i)):(Oi=t,t=a)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;t===a&&(t=Oi,(r=Kl())!==a&&(e=Fb())!==a&&(n=xb())!==a?(o=Oi,(s=Kl())!==a&&(u=Fb())!==a&&(i=xb())!==a?o=s=[s,u,i]:(Oi=o,o=a),o===a&&(o=null),o!==a&&(s=Fb())!==a&&(u=Nb())!==a?(ji=t,t=r=function(t,r){const e=r&&r[0];let n;e&&(n=t,t=e),gv.add(`select::${t}::(.*)`);return{expr:{type:"column_ref",table:t,schema:n,column:"*"},as:null}}(r,o)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,r=Oi,(e=Kl())!==a&&(n=Fb())!==a&&(o=xb())!==a?r=e=[e,n,o]:(Oi=r,r=a),r===a&&(r=null),r!==a&&(e=Fb())!==a&&(n=Nb())!==a?(ji=t,t=r=function(t){const r=t&&t[0]||null;return gv.add(`select::${r}::(.*)`),{expr:{type:"column_ref",table:r,column:"*"},as:null}}(r)):(Oi=t,t=a),t===a&&(t=Oi,(r=ef())!==a&&(e=Fb())!==a?((n=xb())===a&&(n=null),n!==a?(ji=Oi,(o=(o=function(t,r){if(r)return!0}(0,n))?a:void 0)!==a&&(s=Fb())!==a?((u=Bc())===a&&(u=null),u!==a?(ji=t,t=r=function(t,r,e){return gv.add("select::null::"+t.value),{type:"expr",expr:{type:"column_ref",table:null,column:{expr:t}},as:e}}(r,0,u)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=Fc())!==a&&(e=Fb())!==a?((n=Bc())===a&&(n=null),n!==a?(ji=t,t=r={type:"expr",expr:r,as:n}):(Oi=t,t=a)):(Oi=t,t=a)))))}return t}function $c(){var t,r,e;return t=Oi,(r=cp())===a&&(r=null),r!==a&&Fb()!==a&&(e=zl())!==a?(ji=t,t=r=e):(Oi=t,t=a),t}function Bc(){var t,r,e;return t=Oi,(r=cp())!==a&&Fb()!==a&&(e=zl())!==a?(ji=t,t=r=e):(Oi=t,t=a),t===a&&(t=Oi,(r=cp())===a&&(r=null),r!==a&&Fb()!==a&&(e=zl())!==a?(ji=t,t=r=e):(Oi=t,t=a)),t}function Hc(){var r,e,n;return r=Oi,sp()!==a&&Fb()!==a&&(e=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=ov())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=ov())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=ov())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=r,r={keyword:"var",type:"into",expr:e}):(Oi=r,r=a),r===a&&(r=Oi,sp()!==a&&Fb()!==a?("outfile"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Me)),e===a&&("dumpfile"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(qe))),e===a&&(e=null),e!==a&&Fb()!==a?((n=xf())===a&&(n=Kl()),n!==a?(ji=r,r={keyword:e,type:"into",expr:n}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)),r}function Yc(){var t,r;return t=Oi,up()!==a&&Fb()!==a&&(r=Jc())!==a?(ji=t,t=r):(Oi=t,t=a),t}function Wc(){var t,r,e;return t=Oi,(r=el())!==a&&Fb()!==a&&$f()!==a&&Fb()!==a&&(e=el())!==a?(ji=t,t=r=[r,e]):(Oi=t,t=a),t}function Xc(){var r,e;return r=Oi,dp()!==a&&Fb()!==a?("btree"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Pe)),e===a&&("hash"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(De)),e===a&&("gist"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Qe)),e===a&&("gin"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Fe))))),e!==a?(ji=r,r={keyword:"using",type:e.toLowerCase()}):(Oi=r,r=a)):(Oi=r,r=a),r}function Zc(){var t,r,e,n,o,s;if(t=Oi,(r=Kc())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Kc())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Kc())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Kc(){var r,e,n,o,s,u;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"key_block_size"===t.substr(Oi,14).toLowerCase()?(e=t.substr(Oi,14),Oi+=14):(e=a,0===Ai&&xi(Cr));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="KEY_BLOCK_SIZE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a?((n=Ob())===a&&(n=null),n!==a&&Fb()!==a&&(o=kf())!==a?(ji=r,s=n,u=o,r=e={type:e.toLowerCase(),symbol:s,expr:u}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=cf())!==a&&Fb()!==a&&(n=Ob())!==a&&Fb()!==a?((o=kf())===a&&(o=Kl()),o!==a?(ji=r,r=e=function(t,r,e){return{type:t.toLowerCase(),symbol:r,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Xc())===a&&(r=Oi,"with"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ge)),e!==a&&Fb()!==a?("parser"===t.substr(Oi,6).toLowerCase()?(n=t.substr(Oi,6),Oi+=6):(n=a,0===Ai&&xi($e)),n!==a&&Fb()!==a&&(o=cf())!==a?(ji=r,r=e={type:"with parser",expr:o}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"visible"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Be)),e===a&&("invisible"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(He))),e!==a&&(ji=r,e=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(e)),(r=e)===a&&(r=Bb())))),r}function Jc(){var t,r,e,n;if(t=Oi,(r=tl())!==a){for(e=[],n=zc();n!==a;)e.push(n),n=zc();e!==a?(ji=t,t=r=Ye(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function zc(){var t,r,e;return t=Oi,Fb()!==a&&(r=Ib())!==a&&Fb()!==a&&(e=tl())!==a?(ji=t,t=e):(Oi=t,t=a),t===a&&(t=Oi,Fb()!==a&&(r=function(){var t,r,e,n,o,s,u,i,c,l,f;if(t=Oi,(r=rl())!==a)if(Fb()!==a)if((e=tl())!==a)if(Fb()!==a)if((n=dp())!==a)if(Fb()!==a)if(Rb()!==a)if(Fb()!==a)if((o=Zl())!==a){for(s=[],u=Oi,(i=Fb())!==a&&(c=Ib())!==a&&(l=Fb())!==a&&(f=Zl())!==a?u=i=[i,c,l,f]:(Oi=u,u=a);u!==a;)s.push(u),u=Oi,(i=Fb())!==a&&(c=Ib())!==a&&(l=Fb())!==a&&(f=Zl())!==a?u=i=[i,c,l,f]:(Oi=u,u=a);s!==a&&(u=Fb())!==a&&(i=kb())!==a?(ji=t,p=r,v=o,y=s,(b=e).join=p,b.using=hv(v,y),t=r=b):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;var p,b,v,y;t===a&&(t=Oi,(r=rl())!==a&&Fb()!==a&&(e=tl())!==a&&Fb()!==a?((n=ol())===a&&(n=null),n!==a?(ji=t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=rl())===a&&(r=Mi()),r!==a&&Fb()!==a&&(e=Rb())!==a&&Fb()!==a?((n=qi())===a&&(n=Jc()),n!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((o=Bc())===a&&(o=null),o!==a&&(s=Fb())!==a?((u=ol())===a&&(u=null),u!==a?(ji=t,r=function(t,r,e,n){return Array.isArray(r)&&(r={type:"tables",expr:r}),r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,u),t=r):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)));return t}())!==a?(ji=t,t=r):(Oi=t,t=a)),t}function tl(){var r,e,n,o,s,u,i,c,l,f,p,b;return r=Oi,(e=function(){var r;"dual"===t.substr(Oi,4).toLowerCase()?(r=t.substr(Oi,4),Oi+=4):(r=a,0===Ai&&xi(Pu));return r}())!==a&&(ji=r,e={type:"dual"}),(r=e)===a&&(r=Oi,(e=gl())!==a&&Fb()!==a?((n=$c())===a&&(n=null),n!==a?(ji=r,r=e={expr:{type:"values",values:e},as:n}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"lateral"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(We)),e===a&&(e=null),e!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a?((o=qi())===a&&(o=gl()),o!==a&&Fb()!==a&&(s=kb())!==a&&(u=Fb())!==a?((i=$c())===a&&(i=null),i!==a?(ji=r,r=e=function(t,r,e){return Array.isArray(r)&&(r={type:"values",values:r}),r.parentheses=!0,{prefix:t,expr:r,as:e}}(e,o,i)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"lateral"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(We)),e===a&&(e=null),e!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a&&(o=Jc())!==a&&Fb()!==a&&(s=kb())!==a&&(u=Fb())!==a?((i=$c())===a&&(i=null),i!==a?(ji=r,r=e=function(t,r,e){return{prefix:t,expr:r={type:"tables",expr:r,parentheses:!0},as:e}}(e,o,i)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"lateral"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(We)),e===a&&(e=null),e!==a&&Fb()!==a&&(n=jf())!==a&&Fb()!==a?((o=Bc())===a&&(o=null),o!==a?(ji=r,r=e=function(t,r,e){return{prefix:t,type:"expr",expr:r,as:e}}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=el())!==a&&Fb()!==a?("tablesample"===t.substr(Oi,11).toLowerCase()?(n=t.substr(Oi,11),Oi+=11):(n=a,0===Ai&&xi(Xe)),n!==a&&Fb()!==a&&(o=jf())!==a&&Fb()!==a?(s=Oi,"repeatable"===t.substr(Oi,10).toLowerCase()?(u=t.substr(Oi,10),Oi+=10):(u=a,0===Ai&&xi(Ze)),u!==a&&(i=Fb())!==a&&(c=Rb())!==a&&(l=Fb())!==a&&(f=kf())!==a&&(p=Fb())!==a&&(b=kb())!==a?s=u=[u,i,c,l,f,p,b]:(Oi=s,s=a),s===a&&(s=null),s!==a&&(u=Fb())!==a?((i=Bc())===a&&(i=null),i!==a?(ji=r,r=e=function(t,r,e,n){return{...t,as:n,tablesample:{expr:r,repeatable:e&&e[4]}}}(e,o,s,i)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=el())!==a&&Fb()!==a?((n=Bc())===a&&(n=null),n!==a?(ji=r,r=e=function(t,r){return"var"===t.type?(t.as=r,t):{...t,as:r}}(e,n)):(Oi=r,r=a)):(Oi=r,r=a))))))),r}function rl(){var r,e,n,o;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"left"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ga));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(n=Fb())!==a?((o=vp())===a&&(o=null),o!==a&&Fb()!==a&&bp()!==a?(ji=r,r=e="LEFT JOIN"):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=function(){var r,e,n,o;r=Oi,"right"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi($a));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(n=Fb())!==a?((o=vp())===a&&(o=null),o!==a&&Fb()!==a&&bp()!==a?(ji=r,r=e="RIGHT JOIN"):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=function(){var r,e,n,o;r=Oi,"full"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ba));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(n=Fb())!==a?((o=vp())===a&&(o=null),o!==a&&Fb()!==a&&bp()!==a?(ji=r,r=e="FULL JOIN"):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"cross"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Ke)),e!==a&&(n=Fb())!==a&&(o=bp())!==a?(ji=r,r=e="CROSS JOIN"):(Oi=r,r=a),r===a&&(r=Oi,e=Oi,(n=function(){var r,e,n,o;r=Oi,"inner"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Ha));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(o=Fb())!==a?e=n=[n,o]:(Oi=e,e=a),e===a&&(e=null),e!==a&&(n=bp())!==a?(ji=r,r=e="INNER JOIN"):(Oi=r,r=a))))),r}function el(){var t,r,e,n,o,s,u,i,c;return t=Oi,(r=Kl())!==a?(e=Oi,(n=Fb())!==a&&(o=xb())!==a&&(s=Fb())!==a?((u=Kl())===a&&(u=Nb()),u!==a?e=n=[n,o,s,u]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=null),e!==a?(n=Oi,(o=Fb())!==a&&(s=xb())!==a&&(u=Fb())!==a?((i=Kl())===a&&(i=Nb()),i!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a),n===a&&(n=null),n!==a?(ji=t,t=r=function(t,r,e){const n={db:null,table:t};return null!==e?(n.db=t,n.schema=r[3],n.table=e[3],n):(null!==r&&(n.db=t,n.table=r[3]),n)}(r,e,n)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=ov())!==a&&(ji=t,(c=r).db=null,c.table=c.name,r=c),t=r),t}function nl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Il())!==a){for(e=[],n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=yv(r[t][1],n,r[t][3]);return n}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function ol(){var t,r;return t=Oi,pp()!==a&&Fb()!==a&&(r=Nl())!==a?(ji=t,t=r):(Oi=t,t=a),t}function al(){var r,e;return r=Oi,function(){var r,e,n,o;r=Oi,"where"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ts));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a&&(e=Nl())!==a?(ji=r,r=e):(Oi=r,r=a),r}function sl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Wl())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Wl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Wl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function ul(){var t,r,e;return t=Oi,(r=cf())!==a&&Fb()!==a&&cp()!==a&&Fb()!==a&&(e=il())!==a?(ji=t,t=r={name:r,as_window_specification:e}):(Oi=t,t=a),t}function il(){var t,r;return(t=cf())===a&&(t=Oi,Rb()!==a&&Fb()!==a?((r=function(){var t,r,e,n;t=Oi,(r=bl())===a&&(r=null);r!==a&&Fb()!==a?((e=vl())===a&&(e=null),e!==a&&Fb()!==a?((n=function(){var t,r,e,n,o;t=Oi,(r=cb())!==a&&Fb()!==a?((e=cl())===a&&(e=ll()),e!==a?(ji=t,t=r={type:"rows",expr:e}):(Oi=t,t=a)):(Oi=t,t=a);t===a&&(t=Oi,(r=cb())!==a&&Fb()!==a&&(e=Ep())!==a&&Fb()!==a&&(n=ll())!==a&&Fb()!==a&&Ip()!==a&&Fb()!==a&&(o=cl())!==a?(ji=t,r=yv(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),t=r):(Oi=t,t=a));return t}())===a&&(n=null),n!==a?(ji=t,t=r={name:null,partitionby:r,orderby:e,window_frame_clause:n}):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a);return t}())===a&&(r=null),r!==a&&Fb()!==a&&kb()!==a?(ji=t,t={window_specification:r||{},parentheses:!0}):(Oi=t,t=a)):(Oi=t,t=a)),t}function cl(){var r,e,n,o;return r=Oi,(e=pl())!==a&&Fb()!==a?("following"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(Je)),n!==a?(ji=r,(o=e).value+=" FOLLOWING",r=e=o):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=fl()),r}function ll(){var r,e,n,o,s;return r=Oi,(e=pl())!==a&&Fb()!==a?("preceding"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(ze)),n===a&&("following"===t.substr(Oi,9).toLowerCase()?(n=t.substr(Oi,9),Oi+=9):(n=a,0===Ai&&xi(Je))),n!==a?(ji=r,s=n,(o=e).value+=" "+s.toUpperCase(),r=e=o):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=fl()),r}function fl(){var r,e,n;return r=Oi,"current"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(st)),e!==a&&Fb()!==a?("row"===t.substr(Oi,3).toLowerCase()?(n=t.substr(Oi,3),Oi+=3):(n=a,0===Ai&&xi(dr)),n!==a?(ji=r,r=e={type:"origin",value:"current row"}):(Oi=r,r=a)):(Oi=r,r=a),r}function pl(){var r,e;return r=Oi,"unbounded"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(tn)),e!==a&&(ji=r,e={type:"origin",value:e.toUpperCase()}),(r=e)===a&&(r=kf()),r}function bl(){var t,r;return t=Oi,ap()!==a&&Fb()!==a&&wp()!==a&&Fb()!==a&&(r=Pc())!==a?(ji=t,t=r):(Oi=t,t=a),t}function vl(){var t,r;return t=Oi,Lp()!==a&&Fb()!==a&&wp()!==a&&Fb()!==a&&(r=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=yl())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=yl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=yl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=t,t=r):(Oi=t,t=a),t}function yl(){var r,e,n,o,s,u,i;return r=Oi,(e=Il())!==a&&Fb()!==a?((n=jp())===a&&(n=Op()),n===a&&(n=null),n!==a&&Fb()!==a?(o=Oi,"nulls"===t.substr(Oi,5).toLowerCase()?(s=t.substr(Oi,5),Oi+=5):(s=a,0===Ai&&xi(Lt)),s!==a&&(u=Fb())!==a?("first"===t.substr(Oi,5).toLowerCase()?(i=t.substr(Oi,5),Oi+=5):(i=a,0===Ai&&xi(Ot)),i===a&&("last"===t.substr(Oi,4).toLowerCase()?(i=t.substr(Oi,4),Oi+=4):(i=a,0===Ai&&xi(jt))),i===a&&(i=null),i!==a?o=s=[s,u,i]:(Oi=o,o=a)):(Oi=o,o=a),o===a&&(o=null),o!==a?(ji=r,r=e=function(t,r,e){const n={expr:t,type:r};return n.nulls=e&&[e[0],e[2]].filter(t=>t).join(" "),n}(e,n,o)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r}function dl(){var t;return(t=kf())===a&&(t=ov())===a&&(t=bf()),t}function hl(){var r,e,n,o,s,u,i;return r=Oi,e=Oi,(n=function(){var r,e,n,o;r=Oi,"limit"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(as));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(o=Fb())!==a?((s=dl())===a&&(s=Cp()),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=null),e!==a&&(n=Fb())!==a?(o=Oi,(s=function(){var r,e,n,o;r=Oi,"offset"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(ss));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="OFFSET"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(u=Fb())!==a&&(i=dl())!==a?o=s=[s,u,i]:(Oi=o,o=a),o===a&&(o=null),o!==a?(ji=r,r=e=function(t,r){const e=[];return t&&e.push("string"==typeof t[2]?{type:"origin",value:"all"}:t[2]),r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,o)):(Oi=r,r=a)):(Oi=r,r=a),r}function ml(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=wl())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=wl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=wl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function wl(){var r,e,n,o,s,u,i,c,l;return r=Oi,e=Oi,(n=Kl())!==a&&(o=Fb())!==a&&(s=xb())!==a?e=n=[n,o,s]:(Oi=e,e=a),e===a&&(e=null),e!==a&&(n=Fb())!==a&&(o=af())!==a&&(s=Fb())!==a?(61===t.charCodeAt(Oi)?(u="=",Oi++):(u=a,0===Ai&&xi(at)),u!==a&&Fb()!==a&&(i=Dl())!==a?(ji=r,r=e={column:o,value:i,table:(l=e)&&l[0]}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,e=Oi,(n=Kl())!==a&&(o=Fb())!==a&&(s=xb())!==a?e=n=[n,o,s]:(Oi=e,e=a),e===a&&(e=null),e!==a&&(n=Fb())!==a&&(o=af())!==a&&(s=Fb())!==a?(61===t.charCodeAt(Oi)?(u="=",Oi++):(u=a,0===Ai&&xi(at)),u!==a&&Fb()!==a&&(i=yp())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(c=Wl())!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e=function(t,r,e){return{column:r,value:e,table:t&&t[0],keyword:"values"}}(e,o,c)):(Oi=r,r=a)):(Oi=r,r=a)),r}function Ll(){var r,e,n,o,s;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"returning"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(ka));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="RETURNING"):(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&Fb()!==a?((n=Pc())===a&&(n=Ic()),n!==a?(ji=r,s=n,r=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===s&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||s}):(Oi=r,r=a)):(Oi=r,r=a),r}function Ol(){var t;return(t=gl())===a&&(t=Mc()),t}function jl(){var t,r,e,n,o,s,u,i,c;if(t=Oi,ap()!==a)if(Fb()!==a)if((r=Rb())!==a)if(Fb()!==a)if((e=cf())!==a){for(n=[],o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=cf())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);o!==a;)n.push(o),o=Oi,(s=Fb())!==a&&(u=Ib())!==a&&(i=Fb())!==a&&(c=cf())!==a?o=s=[s,u,i,c]:(Oi=o,o=a);n!==a&&(o=Fb())!==a&&(s=kb())!==a?(ji=t,t=hv(e,n)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;return t===a&&(t=Oi,ap()!==a&&Fb()!==a&&(r=El())!==a?(ji=t,t=r):(Oi=t,t=a)),t}function Cl(){var t,r;return t=Oi,(r=tp())!==a&&(ji=t,r="insert"),(t=r)===a&&(t=Oi,(r=ep())!==a&&(ji=t,r="replace"),t=r),t}function gl(){var t,r;return t=Oi,yp()!==a&&Fb()!==a&&(r=function(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=El())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=El())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=El())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,r=hv(r,e),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}())!==a?(ji=t,t=r):(Oi=t,t=a),t}function El(){var t,r;return t=Oi,Rb()!==a&&Fb()!==a&&(r=Al())!==a&&Fb()!==a&&kb()!==a?(ji=t,t=r):(Oi=t,t=a),t}function Al(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Il())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){const e={type:"expr_list"};return e.value=hv(t,r),e}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Tl(){var r,e,n;return r=Oi,bb()!==a&&Fb()!==a&&(e=Il())!==a&&Fb()!==a&&(n=function(){var r;(r=function(){var r,e,n,o;r=Oi,"year"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Fo));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="YEAR"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"month"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ko));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="MONTH"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"day"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(jo));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DAY"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"hour"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(So));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="HOUR"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"minute"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ro));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="MINUTE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"second"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Mo));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SECOND"):(Oi=r,r=a)):(Oi=r,r=a);return r}());return r}())!==a?(ji=r,r={type:"interval",expr:e,unit:n.toLowerCase()}):(Oi=r,r=a),r===a&&(r=Oi,bb()!==a&&Fb()!==a&&(e=xf())!==a?(ji=r,r=function(t){return{type:"interval",expr:t,unit:""}}(e)):(Oi=r,r=a)),r}function Sl(){var t,r,e,n,o,s;if(t=Oi,(r=Ul())!==a)if(Fb()!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ul())!==a?n=o=[o,s]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ul())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e,1)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;return t}function Ul(){var r,e,n;return r=Oi,Mp()!==a&&Fb()!==a&&(e=Nl())!==a&&Fb()!==a&&function(){var r,e,n,o;r=Oi,"then"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(be));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}()!==a&&Fb()!==a&&(n=Il())!==a?(ji=r,r={type:"when",cond:e,result:n}):(Oi=r,r=a),r}function _l(){var t,r;return t=Oi,qp()!==a&&Fb()!==a&&(r=Il())!==a?(ji=t,t={type:"else",result:r}):(Oi=t,t=a),t}function xl(){var t;return(t=Rl())===a&&(t=function(){var t,r,e,n,o,s;if(t=Oi,(r=Ql())!==a){if(e=[],n=Oi,(o=Fb())!==a&&(s=Bl())!==a?n=o=[o,s]:(Oi=n,n=a),n!==a)for(;n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Bl())!==a?n=o=[o,s]:(Oi=n,n=a);else e=a;e!==a?(ji=t,r=vv(r,e[0][1]),t=r):(Oi=t,t=a)}else Oi=t,t=a;return t}()),t}function Il(){var t;return(t=xl())===a&&(t=qi()),t}function Nl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Il())!==a){for(e=[],n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np())===a&&(s=Ib()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a?((s=Ip())===a&&(s=Np())===a&&(s=Ib()),s!==a&&(u=Fb())!==a&&(i=Il())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=yv(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Rl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=kl())!==a){for(e=[],n=Oi,(o=Gb())!==a&&(s=Np())!==a&&(u=Fb())!==a&&(i=kl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Gb())!==a&&(s=Np())!==a&&(u=Fb())!==a&&(i=kl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=on(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function kl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Vl())!==a){for(e=[],n=Oi,(o=Gb())!==a&&(s=Ip())!==a&&(u=Fb())!==a&&(i=Vl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Gb())!==a&&(s=Ip())!==a&&(u=Fb())!==a&&(i=Vl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=on(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Vl(){var r,e,n,o,s;return(r=Ml())===a&&(r=function(){var t,r,e;t=Oi,(r=function(){var t,r,e,n,o;t=Oi,r=Oi,(e=xp())!==a&&(n=Fb())!==a&&(o=_p())!==a?r=e=[e,n,o]:(Oi=r,r=a);r!==a&&(ji=t,r=(s=r)[0]+" "+s[2]);var s;(t=r)===a&&(t=_p());return t}())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(e=qi())!==a&&Fb()!==a&&kb()!==a?(ji=t,n=r,(o=e).parentheses=!0,r=vv(n,o),t=r):(Oi=t,t=a);var n,o;return t}())===a&&(r=Oi,(e=xp())===a&&(e=Oi,33===t.charCodeAt(Oi)?(n="!",Oi++):(n=a,0===Ai&&xi(an)),n!==a?(o=Oi,Ai++,61===t.charCodeAt(Oi)?(s="=",Oi++):(s=a,0===Ai&&xi(at)),Ai--,s===a?o=void 0:(Oi=o,o=a),o!==a?e=n=[n,o]:(Oi=e,e=a)):(Oi=e,e=a)),e!==a&&(n=Fb())!==a&&(o=Vl())!==a?(ji=r,r=e=vv("NOT",o)):(Oi=r,r=a)),r}function Ml(){var r,e,n,o,s;return r=Oi,(e=Dl())!==a&&Fb()!==a?((n=function(){var r;(r=function(){var t,r,e,n,o,s,u;t=Oi,r=[],e=Oi,(n=Fb())!==a&&(o=ql())!==a&&(s=Fb())!==a&&(u=Dl())!==a?e=n=[n,o,s,u]:(Oi=e,e=a);if(e!==a)for(;e!==a;)r.push(e),e=Oi,(n=Fb())!==a&&(o=ql())!==a&&(s=Fb())!==a&&(u=Dl())!==a?e=n=[n,o,s,u]:(Oi=e,e=a);else r=a;r!==a&&(ji=t,r={type:"arithmetic",tail:r});return t=r}())===a&&(r=function(){var t,r,e,n;t=Oi,(r=Pl())!==a&&Fb()!==a&&(e=Rb())!==a&&Fb()!==a&&(n=Al())!==a&&Fb()!==a&&kb()!==a?(ji=t,t=r={op:r,right:n}):(Oi=t,t=a);t===a&&(t=Oi,(r=Pl())!==a&&Fb()!==a?((e=ov())===a&&(e=xf())===a&&(e=jf()),e!==a?(ji=t,r=function(t,r){return{op:t,right:r}}(r,e),t=r):(Oi=t,t=a)):(Oi=t,t=a));return t}())===a&&(r=function(){var t,r,e,n;t=Oi,(r=function(){var t,r,e,n,o;t=Oi,r=Oi,(e=xp())!==a&&(n=Fb())!==a&&(o=Ep())!==a?r=e=[e,n,o]:(Oi=r,r=a);r!==a&&(ji=t,r=(s=r)[0]+" "+s[2]);var s;(t=r)===a&&(t=Ep());return t}())!==a&&Fb()!==a&&(e=Dl())!==a&&Fb()!==a&&Ip()!==a&&Fb()!==a&&(n=Dl())!==a?(ji=t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(Oi=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o,s,u,i,c;t=Oi,(r=Tp())!==a&&(e=Fb())!==a&&(n=Dl())!==a?(ji=t,t=r={op:"IS",right:n}):(Oi=t,t=a);t===a&&(t=Oi,(r=Tp())!==a&&(e=Fb())!==a?(n=Oi,(o=gp())!==a&&(s=Fb())!==a&&(u=up())!==a&&(i=Fb())!==a&&(c=el())!==a?n=o=[o,s,u,i,c]:(Oi=n,n=a),n!==a?(ji=t,r=function(t){const{db:r,table:e}=t.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(r?`"${r}".${n}`:n)}}}(n),t=r):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,r=Oi,(e=Tp())!==a&&(n=Fb())!==a&&(o=xp())!==a?r=e=[e,n,o]:(Oi=r,r=a),r!==a&&(e=Fb())!==a&&(n=Dl())!==a?(ji=t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(Oi=t,t=a)));return t}())===a&&(r=function(){var r,e,n,o;r=Oi,(e=function(){var r,e,n,o,s;r=Oi,e=Oi,(n=xp())!==a&&(o=Fb())!==a?((s=Sp())===a&&(s=Up()),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a);e!==a&&(ji=r,e=(u=e)[0]+" "+u[2]);var u;(r=e)===a&&(r=Sp())===a&&(r=Up())===a&&(r=Oi,"similar"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(bn)),e!==a&&(n=Fb())!==a&&(o=$f())!==a?(ji=r,r=e="SIMILAR TO"):(Oi=r,r=a),r===a&&(r=Oi,(e=xp())!==a&&(n=Fb())!==a?("similar"===t.substr(Oi,7).toLowerCase()?(o=t.substr(Oi,7),Oi+=7):(o=a,0===Ai&&xi(bn)),o!==a&&(s=Fb())!==a&&$f()!==a?(ji=r,r=e="NOT SIMILAR TO"):(Oi=r,r=a)):(Oi=r,r=a)));return r}())!==a&&Fb()!==a?((n=Af())===a&&(n=Ml()),n!==a&&Fb()!==a?((o=function(){var r,e,n;r=Oi,"escape"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(mn));e!==a&&Fb()!==a&&(n=xf())!==a?(ji=r,r=e={type:"ESCAPE",value:n}):(Oi=r,r=a);return r}())===a&&(o=null),o!==a?(ji=r,s=e,u=n,(i=o)&&(u.escape=i),r=e={op:s,right:u}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a);var s,u,i;return r}())===a&&(r=function(){var r,e,n;r=Oi,(e=function(){var r;"!~*"===t.substr(Oi,3)?(r="!~*",Oi+=3):(r=a,0===Ai&&xi(vn));r===a&&("~*"===t.substr(Oi,2)?(r="~*",Oi+=2):(r=a,0===Ai&&xi(yn)),r===a&&(126===t.charCodeAt(Oi)?(r="~",Oi++):(r=a,0===Ai&&xi(dn)),r===a&&("!~"===t.substr(Oi,2)?(r="!~",Oi+=2):(r=a,0===Ai&&xi(hn)))));return r}())!==a&&Fb()!==a?((n=Af())===a&&(n=Ml()),n!==a?(ji=r,r=e={op:e,right:n}):(Oi=r,r=a)):(Oi=r,r=a);return r}());return r}())===a&&(n=null),n!==a?(ji=r,o=e,r=e=null===(s=n)?o:"arithmetic"===s.type?mv(o,s.tail):yv(s.op,o,s.right)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=xf())===a&&(r=Wl()),r}function ql(){var r;return">="===t.substr(Oi,2)?(r=">=",Oi+=2):(r=a,0===Ai&&xi(sn)),r===a&&(62===t.charCodeAt(Oi)?(r=">",Oi++):(r=a,0===Ai&&xi(un)),r===a&&("<="===t.substr(Oi,2)?(r="<=",Oi+=2):(r=a,0===Ai&&xi(cn)),r===a&&("<>"===t.substr(Oi,2)?(r="<>",Oi+=2):(r=a,0===Ai&&xi(ln)),r===a&&(60===t.charCodeAt(Oi)?(r="<",Oi++):(r=a,0===Ai&&xi(fn)),r===a&&(61===t.charCodeAt(Oi)?(r="=",Oi++):(r=a,0===Ai&&xi(at)),r===a&&("!="===t.substr(Oi,2)?(r="!=",Oi+=2):(r=a,0===Ai&&xi(pn)))))))),r}function Pl(){var t,r,e,n,o,s;return t=Oi,r=Oi,(e=xp())!==a&&(n=Fb())!==a&&(o=Ap())!==a?r=e=[e,n,o]:(Oi=r,r=a),r!==a&&(ji=t,r=(s=r)[0]+" "+s[2]),(t=r)===a&&(t=Ap()),t}function Dl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Fl())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ql())!==a&&(u=Fb())!==a&&(i=Fl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ql())!==a&&(u=Fb())!==a&&(i=Fl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=function(t,r){if(r&&r.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...bv()}));return mv(t,r)}(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Ql(){var r;return 43===t.charCodeAt(Oi)?(r="+",Oi++):(r=a,0===Ai&&xi(wn)),r===a&&(45===t.charCodeAt(Oi)?(r="-",Oi++):(r=a,0===Ai&&xi(Ln))),r}function Fl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Hl())!==a){for(e=[],n=Oi,(o=Fb())!==a?((s=Gl())===a&&(s=Qb()),s!==a&&(u=Fb())!==a&&(i=Hl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a?((s=Gl())===a&&(s=Qb()),s!==a&&(u=Fb())!==a&&(i=Hl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a)):(Oi=n,n=a);e!==a?(ji=t,t=r=mv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Gl(){var r;return 42===t.charCodeAt(Oi)?(r="*",Oi++):(r=a,0===Ai&&xi(On)),r===a&&(47===t.charCodeAt(Oi)?(r="/",Oi++):(r=a,0===Ai&&xi(jn)),r===a&&(37===t.charCodeAt(Oi)?(r="%",Oi++):(r=a,0===Ai&&xi(Cn)),r===a&&("||"===t.substr(Oi,2)?(r="||",Oi+=2):(r=a,0===Ai&&xi(gn))))),r}function $l(){var t,r,e,n,o;return t=Oi,(r=Wl())!==a&&Fb()!==a?((e=Qc())===a&&(e=null),e!==a?(ji=t,n=r,(o=e)&&(n.array_index=o),t=r=n):(Oi=t,t=a)):(Oi=t,t=a),t}function Bl(){var r,e,n,o,s,u;return(r=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=Dp())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&(s=cp())!==a&&Fb()!==a&&(u=sv())!==a&&Fb()!==a&&(i=kb())!==a?(ji=r,f=o,p=u,e={type:"cast",keyword:e.toLowerCase(),expr:f,symbol:"as",target:[p]},r=e):(Oi=r,r=a);var f,p;r===a&&(r=Oi,(e=Dp())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&(s=cp())!==a&&Fb()!==a&&(u=$p())!==a&&Fb()!==a&&(i=Rb())!==a&&Fb()!==a&&(c=Vf())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&(l=kb())!==a?(ji=r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),r=e):(Oi=r,r=a),r===a&&(r=Oi,(e=Dp())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&(s=cp())!==a&&Fb()!==a&&(u=$p())!==a&&Fb()!==a&&(i=Rb())!==a&&Fb()!==a&&(c=Vf())!==a&&Fb()!==a&&Ib()!==a&&Fb()!==a&&(l=Vf())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&kb()!==a?(ji=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),r=e):(Oi=r,r=a),r===a&&(r=Oi,(e=Dp())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&(s=cp())!==a&&Fb()!==a&&(u=function(){var r;(r=function(){var r,e,n,o;r=Oi,"signed"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ps));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SIGNED"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=Bp());return r}())!==a&&Fb()!==a?((i=Yp())===a&&(i=null),i!==a&&Fb()!==a&&(c=kb())!==a?(ji=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,u,i),r=e):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=Rb())!==a&&Fb()!==a?((n=Rl())===a&&(n=$l())===a&&(n=bf()),n!==a&&Fb()!==a&&(o=kb())!==a&&Fb()!==a?((s=Ef())===a&&(s=null),s!==a?(ji=r,e=function(t,r){return t.parentheses=!0,r?{type:"cast",keyword:"cast",expr:t,...r}:t}(n,s),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=Af())===a&&(e=function(){var r,e,n;r=Oi,(e=function(){var r,e,n,o,s,u,i,c,l;r=Oi,(e=function(){var r,e,n,o;r=Oi,"count"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Os));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="COUNT"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=Oi,"group_concat"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(js));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="GROUP_CONCAT"):(Oi=r,r=a)):(Oi=r,r=a);return r}());e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=function(){var r,e;r=Oi,(e=function(){var r,e;r=Oi,42===t.charCodeAt(Oi)?(e="*",Oi++):(e=a,0===Ai&&xi(On));e!==a&&(ji=r,e={type:"star",value:"*"});return r=e}())!==a&&(ji=r,e={expr:e});(r=e)===a&&(r=mf());return r}())!==a&&Fb()!==a&&(o=kb())!==a&&Fb()!==a?((s=yf())===a&&(s=null),s!==a?(ji=r,e=function(t,r,e){return{type:"aggr_func",name:t,args:r,over:e}}(e,n,s),r=e):(Oi=r,r=a)):(Oi=r,r=a);r===a&&(r=Oi,"percentile_cont"===t.substr(Oi,15).toLowerCase()?(e=t.substr(Oi,15),Oi+=15):(e=a,0===Ai&&xi(io)),e===a&&("percentile_disc"===t.substr(Oi,15).toLowerCase()?(e=t.substr(Oi,15),Oi+=15):(e=a,0===Ai&&xi(co))),e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((n=kf())===a&&(n=Tf()),n!==a&&Fb()!==a&&(o=kb())!==a&&Fb()!==a?("within"===t.substr(Oi,6).toLowerCase()?(s=t.substr(Oi,6),Oi+=6):(s=a,0===Ai&&xi(lo)),s!==a&&Fb()!==a&&mp()!==a&&Fb()!==a&&(u=Rb())!==a&&Fb()!==a&&(i=vl())!==a&&Fb()!==a&&(c=kb())!==a&&Fb()!==a?((l=yf())===a&&(l=null),l!==a?(ji=r,e=function(t,r,e,n){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:r},within_group_orderby:e,over:n}}(e,n,i,l),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,"mode"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(fo)),e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=kb())!==a&&Fb()!==a?("within"===t.substr(Oi,6).toLowerCase()?(o=t.substr(Oi,6),Oi+=6):(o=a,0===Ai&&xi(lo)),o!==a&&Fb()!==a&&(s=mp())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(u=vl())!==a&&Fb()!==a&&(i=kb())!==a&&Fb()!==a?((c=yf())===a&&(c=null),c!==a?(ji=r,e=function(t,r,e){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:{}},within_group_orderby:r,over:e}}(e,u,c),r=e):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)));return r}())===a&&(e=function(){var r,e,n,o;r=Oi,(e=function(){var r;(r=function(){var r,e,n,o;r=Oi,"sum"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Es));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SUM"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"max"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Cs));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="MAX"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"min"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(gs));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="MIN"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"avg"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(As));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="AVG"):(Oi=r,r=a)):(Oi=r,r=a);return r}());return r}())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=Dl())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((o=yf())===a&&(o=null),o!==a?(ji=r,e=function(t,r,e){return{type:"aggr_func",name:t,args:{expr:r},over:e,...bv()}}(e,n,o),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(e=function(){var r,e,n,o,s,u;r=Oi,e=Oi,(n=Kl())!==a&&(o=Fb())!==a&&(s=xb())!==a?e=n=[n,o,s]:(Oi=e,e=a);e===a&&(e=null);e!==a&&(n=Fb())!==a?((o=function(){var r,e,n,o;r=Oi,"array_agg"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(ws));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ARRAY_AGG"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(o=function(){var r,e,n,o;r=Oi,"string_agg"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Ls));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="STRING_AGG"):(Oi=r,r=a)):(Oi=r,r=a);return r}()),o!==a&&(s=Fb())!==a&&Rb()!==a&&Fb()!==a&&(u=mf())!==a&&Fb()!==a&&kb()!==a?(ji=r,e=function(t,r,e){return{type:"aggr_func",name:t?`${t[0]}.${r}`:r,args:e}}(e,o,u),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}());e!==a&&Fb()!==a?((n=function(){var r,e,n;r=Oi,"filter"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Kn));e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=al())!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e={keyword:"filter",parentheses:!0,where:n}):(Oi=r,r=a);return r}())===a&&(n=null),n!==a?(ji=r,e=function(t,r){return r&&(t.filter=r),t}(e,n),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(e=function(){var r;(r=function(){var r,e,n;r=Oi,(e=function(){var r;"row_number"===t.substr(Oi,10).toLowerCase()?(r=t.substr(Oi,10),Oi+=10):(r=a,0===Ai&&xi(to));r===a&&("dense_rank"===t.substr(Oi,10).toLowerCase()?(r=t.substr(Oi,10),Oi+=10):(r=a,0===Ai&&xi(ro)),r===a&&("rank"===t.substr(Oi,4).toLowerCase()?(r=t.substr(Oi,4),Oi+=4):(r=a,0===Ai&&xi(eo))));return r}())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&(n=yf())!==a?(ji=r,e=function(t,r){return{type:"window_func",name:t,over:r}}(e,n),r=e):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s;r=Oi,(e=function(){var r;"lag"===t.substr(Oi,3).toLowerCase()?(r=t.substr(Oi,3),Oi+=3):(r=a,0===Ai&&xi(no));r===a&&("lead"===t.substr(Oi,4).toLowerCase()?(r=t.substr(Oi,4),Oi+=4):(r=a,0===Ai&&xi(oo)),r===a&&("nth_value"===t.substr(Oi,9).toLowerCase()?(r=t.substr(Oi,9),Oi+=9):(r=a,0===Ai&&xi(ao))));return r}())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=Al())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((o=df())===a&&(o=null),o!==a&&Fb()!==a&&(s=yf())!==a?(ji=r,e=function(t,r,e,n){return{type:"window_func",name:t,args:r,over:n,consider_nulls:e}}(e,n,o,s),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s;r=Oi,(e=function(){var r;"first_value"===t.substr(Oi,11).toLowerCase()?(r=t.substr(Oi,11),Oi+=11):(r=a,0===Ai&&xi(Jn));r===a&&("last_value"===t.substr(Oi,10).toLowerCase()?(r=t.substr(Oi,10),Oi+=10):(r=a,0===Ai&&xi(zn)));return r}())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=Il())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((o=df())===a&&(o=null),o!==a&&Fb()!==a&&(s=yf())!==a?(ji=r,e=function(t,r,e,n){return{type:"window_func",name:t,args:{type:"expr_list",value:[r]},over:n,consider_nulls:e}}(e,n,o,s),r=e):(Oi=r,r=a)):(Oi=r,r=a);return r}());return r}())===a&&(e=jf())===a&&(e=function(){var t,r,e,n,o,s,u,i;return t=Oi,Vp()!==a&&Fb()!==a&&(r=Sl())!==a&&Fb()!==a?((e=_l())===a&&(e=null),e!==a&&Fb()!==a&&(n=Pp())!==a&&Fb()!==a?((o=Vp())===a&&(o=null),o!==a?(ji=t,u=r,(i=e)&&u.push(i),t={type:"case",expr:null,args:u}):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,Vp()!==a&&Fb()!==a&&(r=Il())!==a&&Fb()!==a&&(e=Sl())!==a&&Fb()!==a?((n=_l())===a&&(n=null),n!==a&&Fb()!==a&&(o=Pp())!==a&&Fb()!==a?((s=Vp())===a&&(s=null),s!==a?(ji=t,t=function(t,r,e){return e&&r.push(e),{type:"case",expr:t,args:r}}(r,e,n)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)),t}())===a&&(e=Tl())===a&&(e=$l())===a&&(e=bf()),e!==a&&Fb()!==a?((n=Ef())===a&&(n=null),n!==a?(ji=r,e=function(t,r){return r?{type:"cast",keyword:"cast",expr:t,...r}:t}(e,n),r=e):(Oi=r,r=a)):(Oi=r,r=a))))));return r}())===a&&(r=Oi,Rb()!==a&&(e=Fb())!==a&&(n=Nl())!==a&&(o=Fb())!==a&&(s=kb())!==a?(ji=r,(u=n).parentheses=!0,r=u):(Oi=r,r=a),r===a&&(r=ov())===a&&(r=Oi,Fb()!==a?(36===t.charCodeAt(Oi)?(e="$",Oi++):(e=a,0===Ai&&xi(En)),e!==a?(60===t.charCodeAt(Oi)?(n="<",Oi++):(n=a,0===Ai&&xi(fn)),n!==a&&(o=kf())!==a?(62===t.charCodeAt(Oi)?(s=">",Oi++):(s=a,0===Ai&&xi(un)),s!==a?(ji=r,r={type:"origin",value:`$<${o.value}>`}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a))),r}function Hl(){var r,e,n,o,s;return(r=function(){var r,e,n,o,s,u,i,c;if(r=Oi,(e=Bl())!==a)if(Fb()!==a){for(n=[],o=Oi,(s=Fb())!==a?("?|"===t.substr(Oi,2)?(u="?|",Oi+=2):(u=a,0===Ai&&xi(An)),u===a&&("?&"===t.substr(Oi,2)?(u="?&",Oi+=2):(u=a,0===Ai&&xi(Tn)),u===a&&(63===t.charCodeAt(Oi)?(u="?",Oi++):(u=a,0===Ai&&xi(Sn)),u===a&&("#-"===t.substr(Oi,2)?(u="#-",Oi+=2):(u=a,0===Ai&&xi(Un)),u===a&&("#>>"===t.substr(Oi,3)?(u="#>>",Oi+=3):(u=a,0===Ai&&xi(_n)),u===a&&("#>"===t.substr(Oi,2)?(u="#>",Oi+=2):(u=a,0===Ai&&xi(xn)),u===a&&(u=Db())===a&&(u=Pb())===a&&("@>"===t.substr(Oi,2)?(u="@>",Oi+=2):(u=a,0===Ai&&xi(In)),u===a&&("<@"===t.substr(Oi,2)?(u="<@",Oi+=2):(u=a,0===Ai&&xi(Nn))))))))),u!==a&&(i=Fb())!==a&&(c=Bl())!==a?o=s=[s,u,i,c]:(Oi=o,o=a)):(Oi=o,o=a);o!==a;)n.push(o),o=Oi,(s=Fb())!==a?("?|"===t.substr(Oi,2)?(u="?|",Oi+=2):(u=a,0===Ai&&xi(An)),u===a&&("?&"===t.substr(Oi,2)?(u="?&",Oi+=2):(u=a,0===Ai&&xi(Tn)),u===a&&(63===t.charCodeAt(Oi)?(u="?",Oi++):(u=a,0===Ai&&xi(Sn)),u===a&&("#-"===t.substr(Oi,2)?(u="#-",Oi+=2):(u=a,0===Ai&&xi(Un)),u===a&&("#>>"===t.substr(Oi,3)?(u="#>>",Oi+=3):(u=a,0===Ai&&xi(_n)),u===a&&("#>"===t.substr(Oi,2)?(u="#>",Oi+=2):(u=a,0===Ai&&xi(xn)),u===a&&(u=Db())===a&&(u=Pb())===a&&("@>"===t.substr(Oi,2)?(u="@>",Oi+=2):(u=a,0===Ai&&xi(In)),u===a&&("<@"===t.substr(Oi,2)?(u="<@",Oi+=2):(u=a,0===Ai&&xi(Nn))))))))),u!==a&&(i=Fb())!==a&&(c=Bl())!==a?o=s=[s,u,i,c]:(Oi=o,o=a)):(Oi=o,o=a);n!==a?(ji=r,l=e,e=(f=n)&&0!==f.length?mv(l,f):l,r=e):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;var l,f;return r}())===a&&(r=Oi,(e=function(){var r;33===t.charCodeAt(Oi)?(r="!",Oi++):(r=a,0===Ai&&xi(an));r===a&&(45===t.charCodeAt(Oi)?(r="-",Oi++):(r=a,0===Ai&&xi(Ln)),r===a&&(43===t.charCodeAt(Oi)?(r="+",Oi++):(r=a,0===Ai&&xi(wn)),r===a&&(126===t.charCodeAt(Oi)?(r="~",Oi++):(r=a,0===Ai&&xi(dn)))));return r}())!==a?(n=Oi,(o=Fb())!==a&&(s=Hl())!==a?n=o=[o,s]:(Oi=n,n=a),n!==a?(ji=r,r=e=vv(e,n[1])):(Oi=r,r=a)):(Oi=r,r=a)),r}function Yl(){var r,e,n,o,s,u;if(r=Oi,"e"===t.substr(Oi,1).toLowerCase()?(e=t.charAt(Oi),Oi++):(e=a,0===Ai&&xi(Rn)),e!==a)if(39===t.charCodeAt(Oi)?(n="'",Oi++):(n=a,0===Ai&&xi(Ur)),n!==a)if(Fb()!==a){for(o=[],s=Nf();s!==a;)o.push(s),s=Nf();o!==a&&(s=Fb())!==a?(39===t.charCodeAt(Oi)?(u="'",Oi++):(u=a,0===Ai&&xi(Ur)),u!==a?(ji=r,r=e={type:"origin",value:`E'${o.join("")}'`}):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;return r}function Wl(){var t,r,e,n,o,s,u,i,c,l,f,p,b;return(t=Yl())===a&&(t=Oi,r=Oi,(e=Kl())!==a&&(n=Fb())!==a&&(o=xb())!==a?r=e=[e,n,o]:(Oi=r,r=a),r===a&&(r=null),r!==a&&(e=Fb())!==a&&(n=Nb())!==a?(ji=t,t=r=function(t){const r=t&&t[0]||null;return gv.add(`select::${r}::(.*)`),{type:"column_ref",table:r,column:"*"}}(r)):(Oi=t,t=a),t===a&&(t=Oi,(r=Kl())!==a?(e=Oi,(n=Fb())!==a&&(o=xb())!==a&&(s=Fb())!==a&&(u=Kl())!==a?e=n=[n,o,s,u]:(Oi=e,e=a),e!==a?(n=Oi,(o=Fb())!==a&&(s=xb())!==a&&(u=Fb())!==a&&(i=sf())!==a?n=o=[o,s,u,i]:(Oi=n,n=a),n!==a?(o=Oi,(s=Fb())!==a&&(u=zi())!==a?o=s=[s,u]:(Oi=o,o=a),o===a&&(o=null),o!==a?(ji=t,l=r,f=e,p=n,b=o,gv.add(`select::${l}.${f[3]}::${p[3]}`),t=r={type:"column_ref",schema:l,table:f[3],column:p[3],collate:b&&b[1]}):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=Kl())!==a&&(e=Fb())!==a&&(n=xb())!==a&&(o=Fb())!==a&&(s=sf())!==a?(u=Oi,(i=Fb())!==a&&(c=zi())!==a?u=i=[i,c]:(Oi=u,u=a),u===a&&(u=null),u!==a?(ji=t,t=r=function(t,r,e){return gv.add(`select::${t}::${r}`),{type:"column_ref",table:t,column:r,collate:e&&e[1]}}(r,s,u)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=sf())!==a?(e=Oi,(n=Fb())!==a&&(o=zi())!==a?e=n=[n,o]:(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=t,t=r=function(t,r){return gv.add("select::null::"+t),{type:"column_ref",table:null,column:t,collate:r&&r[1]}}(r,e)):(Oi=t,t=a)):(Oi=t,t=a))))),t}function Xl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=sf())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=sf())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=sf())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function Zl(){var t,r;return t=Oi,(r=cf())!==a&&(ji=t,r={type:"default",value:r}),(t=r)===a&&(t=tf()),t}function Kl(){var t,r;return t=Oi,(r=cf())!==a?(ji=Oi,(kn(r)?a:void 0)!==a?(ji=t,t=r=r):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=rf())!==a&&(ji=t,r=r),t=r),t}function Jl(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=Kl())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Kl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=Kl())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function zl(){var t,r,e,n,o,s,u,i,c;return t=Oi,(r=cf())!==a?(ji=Oi,(function(t){return!0===pv[t.toUpperCase()]}(r)?a:void 0)!==a?(e=Oi,(n=Fb())!==a&&(o=Rb())!==a&&(s=Fb())!==a&&(u=Xl())!==a&&(i=Fb())!==a&&(c=kb())!==a?e=n=[n,o,s,u,i,c]:(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=t,t=r=function(t,r){return r?`${t}(${r[3].join(", ")})`:t}(r,e)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=rf())!==a&&(ji=t,r=r),t=r),t}function tf(){var t;return(t=ef())===a&&(t=nf())===a&&(t=of()),t}function rf(){var t,r;return t=Oi,(r=ef())===a&&(r=nf())===a&&(r=of()),r!==a&&(ji=t,r=r.value),t=r}function ef(){var r,e,n,o;if(r=Oi,34===t.charCodeAt(Oi)?(e='"',Oi++):(e=a,0===Ai&&xi(Ve)),e!==a){if(n=[],Vn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Mn)),o!==a)for(;o!==a;)n.push(o),Vn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Mn));else n=a;n!==a?(34===t.charCodeAt(Oi)?(o='"',Oi++):(o=a,0===Ai&&xi(Ve)),o!==a?(ji=r,r=e={type:"double_quote_string",value:n.join("")}):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;return r}function nf(){var r,e,n,o;if(r=Oi,39===t.charCodeAt(Oi)?(e="'",Oi++):(e=a,0===Ai&&xi(Ur)),e!==a){if(n=[],qn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Pn)),o!==a)for(;o!==a;)n.push(o),qn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Pn));else n=a;n!==a?(39===t.charCodeAt(Oi)?(o="'",Oi++):(o=a,0===Ai&&xi(Ur)),o!==a?(ji=r,r=e={type:"single_quote_string",value:n.join("")}):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;return r}function of(){var r,e,n,o;if(r=Oi,96===t.charCodeAt(Oi)?(e="`",Oi++):(e=a,0===Ai&&xi(Dn)),e!==a){if(n=[],Qn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Fn)),o!==a)for(;o!==a;)n.push(o),Qn.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Fn));else n=a;n!==a?(96===t.charCodeAt(Oi)?(o="`",Oi++):(o=a,0===Ai&&xi(Dn)),o!==a?(ji=r,r=e={type:"backticks_quote_string",value:n.join("")}):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;return r}function af(){var t;return(t=uf())===a&&(t=rf()),t}function sf(){var t,r;return t=Oi,(r=uf())!==a?(ji=Oi,(kn(r)?a:void 0)!==a?(ji=t,t=r=r):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=rf()),t}function uf(){var t,r,e,n;if(t=Oi,(r=lf())!==a){for(e=[],n=pf();n!==a;)e.push(n),n=pf();e!==a?(ji=t,t=r=r+e.join("")):(Oi=t,t=a)}else Oi=t,t=a;return t}function cf(){var t,r,e,n;if(t=Oi,(r=lf())!==a){for(e=[],n=ff();n!==a;)e.push(n),n=ff();e!==a?(ji=t,t=r=r+e.join("")):(Oi=t,t=a)}else Oi=t,t=a;return t}function lf(){var r;return Gn.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi($n)),r}function ff(){var r;return Bn.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(Hn)),r}function pf(){var r;return Yn.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(Wn)),r}function bf(){var r,e,n,o;return r=Oi,e=Oi,58===t.charCodeAt(Oi)?(n=":",Oi++):(n=a,0===Ai&&xi(Xn)),n!==a&&(o=cf())!==a?e=n=[n,o]:(Oi=e,e=a),e!==a&&(ji=r,e={type:"param",value:e[1]}),r=e}function vf(){var t,r,e;return t=Oi,pp()!==a&&Fb()!==a&&Xf()!==a&&Fb()!==a&&(r=vb())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((e=Al())===a&&(e=null),e!==a&&Fb()!==a&&kb()!==a?(ji=t,t={type:"on update",keyword:r,parentheses:!0,expr:e}):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,pp()!==a&&Fb()!==a&&Xf()!==a&&Fb()!==a&&(r=vb())!==a?(ji=t,t=function(t){return{type:"on update",keyword:t}}(r)):(Oi=t,t=a)),t}function yf(){var r,e,n,o,s;return r=Oi,"over"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Zn)),e!==a&&Fb()!==a&&(n=il())!==a?(ji=r,r=e={type:"window",as_window_specification:n}):(Oi=r,r=a),r===a&&(r=Oi,"over"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Zn)),e!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a?((o=bl())===a&&(o=null),o!==a&&Fb()!==a?((s=vl())===a&&(s=null),s!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e={partitionby:o,orderby:s}):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=vf())),r}function df(){var r,e,n;return r=Oi,"ignore"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(so)),e===a&&("respect"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(uo))),e!==a&&Fb()!==a?("nulls"===t.substr(Oi,5).toLowerCase()?(n=t.substr(Oi,5),Oi+=5):(n=a,0===Ai&&xi(Lt)),n!==a?(ji=r,r=e=e.toUpperCase()+" NULLS"):(Oi=r,r=a)):(Oi=r,r=a),r}function hf(){var t,r;return t=Oi,Ib()!==a&&Fb()!==a&&(r=xf())!==a?(ji=t,t={symbol:ke,delimiter:r}):(Oi=t,t=a),t}function mf(){var t,r,e,n,o,s,u,i,c,l,f;if(t=Oi,(r=gp())===a&&(r=null),r!==a)if(Fb()!==a)if((e=Rb())!==a)if(Fb()!==a)if((n=Il())!==a)if(Fb()!==a)if((o=kb())!==a)if(Fb()!==a){for(s=[],u=Oi,(i=Fb())!==a?((c=Ip())===a&&(c=Np()),c!==a&&(l=Fb())!==a&&(f=Il())!==a?u=i=[i,c,l,f]:(Oi=u,u=a)):(Oi=u,u=a);u!==a;)s.push(u),u=Oi,(i=Fb())!==a?((c=Ip())===a&&(c=Np()),c!==a&&(l=Fb())!==a&&(f=Il())!==a?u=i=[i,c,l,f]:(Oi=u,u=a)):(Oi=u,u=a);s!==a&&(u=Fb())!==a?((i=hf())===a&&(i=null),i!==a&&(c=Fb())!==a?((l=vl())===a&&(l=null),l!==a?(ji=t,t=r=function(t,r,e,n,o){const a=e.length;let s=r;s.parentheses=!0;for(let t=0;t<a;++t)s=yv(e[t][1],s,e[t][3]);return{distinct:t,expr:s,orderby:o,separator:n}}(r,n,s,i,l)):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)}else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;else Oi=t,t=a;return t===a&&(t=Oi,(r=gp())===a&&(r=null),r!==a&&Fb()!==a&&(e=nl())!==a&&Fb()!==a?((n=hf())===a&&(n=null),n!==a&&Fb()!==a?((o=vl())===a&&(o=null),o!==a?(ji=t,t=r={distinct:r,expr:e,orderby:o,separator:n}):(Oi=t,t=a)):(Oi=t,t=a)):(Oi=t,t=a)),t}function wf(){var r,e,n;return r=Oi,(e=function(){var r;return"both"===t.substr(Oi,4).toLowerCase()?(r=t.substr(Oi,4),Oi+=4):(r=a,0===Ai&&xi(po)),r===a&&("leading"===t.substr(Oi,7).toLowerCase()?(r=t.substr(Oi,7),Oi+=7):(r=a,0===Ai&&xi(bo)),r===a&&("trailing"===t.substr(Oi,8).toLowerCase()?(r=t.substr(Oi,8),Oi+=8):(r=a,0===Ai&&xi(vo)))),r}())===a&&(e=null),e!==a&&Fb()!==a?((n=Il())===a&&(n=null),n!==a&&Fb()!==a&&up()!==a?(ji=r,r=e=function(t,r,e){let n=[];return t&&n.push({type:"origin",value:t}),r&&n.push(r),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Oi=r,r=a)):(Oi=r,r=a),r}function Lf(){var r,e,n,o;return r=Oi,"trim"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(yo)),e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((n=wf())===a&&(n=null),n!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e=function(t,r){let e=t||{type:"expr_list",value:[]};return e.value.push(r),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...bv()}}(n,o)):(Oi=r,r=a)):(Oi=r,r=a),r}function Of(){var r,e,n,o;return r=Oi,"crosstab"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(ho)),e!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(n=Al())!==a&&Fb()!==a&&kb()!==a&&Fb()!==a&&cp()!==a&&Fb()!==a&&cf()!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(o=Gi())!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e={type:"tablefunc",name:{name:[{type:"origin",value:"crosstab"}]},args:n,as:{type:"function",name:{name:[{type:"default",value:name}]},args:{type:"expr_list",value:o.map(t=>({...t,type:"column_definition"}))},...bv()},...bv()}):(Oi=r,r=a),r}function jf(){var r,e,n,o,s,u,i;return(r=Lf())===a&&(r=Of())===a&&(r=Oi,"now"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(mo)),e!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a?((o=Al())===a&&(o=null),o!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?("at"===t.substr(Oi,2).toLowerCase()?(s=t.substr(Oi,2),Oi+=2):(s=a,0===Ai&&xi(wo)),s!==a&&Fb()!==a&&lb()!==a&&Fb()!==a?("zone"===t.substr(Oi,4).toLowerCase()?(u=t.substr(Oi,4),Oi+=4):(u=a,0===Ai&&xi(Lo)),u!==a&&Fb()!==a&&(i=xf())!==a?(ji=r,r=e=function(t,r,e){return e.prefix="at time zone",{type:"function",name:{name:[{type:"default",value:t}]},args:r||{type:"expr_list",value:[]},suffix:e,...bv()}}(e,o,i)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=function(){var r;(r=gf())===a&&(r=yb())===a&&(r=function(){var r,e,n,o;r=Oi,"user"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(lu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="USER"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=db())===a&&(r=function(){var r,e,n,o;r=Oi,"system_user"===t.substr(Oi,11).toLowerCase()?(e=t.substr(Oi,11),Oi+=11):(e=a,0===Ai&&xi(Su));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SYSTEM_USER"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&("ntile"===t.substr(Oi,5).toLowerCase()?(r=t.substr(Oi,5),Oi+=5):(r=a,0===Ai&&xi(Go)));return r}())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a?((o=Al())===a&&(o=null),o!==a&&Fb()!==a&&kb()!==a&&Fb()!==a?((s=yf())===a&&(s=null),s!==a?(ji=r,r=e=function(t,r,e){return{type:"function",name:{name:[{type:"default",value:t}]},args:r||{type:"expr_list",value:[]},over:e,...bv()}}(e,o,s)):(Oi=r,r=a)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=function(){var t,r,e,n,o;t=Oi,(r=kp())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(e=Cf())!==a&&Fb()!==a&&up()!==a&&Fb()!==a?((n=fb())===a&&(n=bb())===a&&(n=lb())===a&&(n=ub()),n===a&&(n=null),n!==a&&Fb()!==a&&(o=Il())!==a&&Fb()!==a&&kb()!==a?(ji=t,s=e,u=n,i=o,r={type:r.toLowerCase(),args:{field:s,cast_type:u,source:i},...bv()},t=r):(Oi=t,t=a)):(Oi=t,t=a);var s,u,i;t===a&&(t=Oi,(r=kp())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a&&(e=Cf())!==a&&Fb()!==a&&up()!==a&&Fb()!==a&&(n=Il())!==a&&Fb()!==a&&(o=kb())!==a?(ji=t,r=function(t,r,e){return{type:t.toLowerCase(),args:{field:r,source:e},...bv()}}(r,e,n),t=r):(Oi=t,t=a));return t}())===a&&(r=Oi,(e=gf())!==a&&Fb()!==a?((n=vf())===a&&(n=null),n!==a?(ji=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...bv()}):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=rv())!==a&&Fb()!==a&&(n=Rb())!==a&&Fb()!==a?((o=Nl())===a&&(o=null),o!==a&&Fb()!==a&&kb()!==a?(ji=r,r=e=function(t,r){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},...bv()}}(e,o)):(Oi=r,r=a)):(Oi=r,r=a))))),r}function Cf(){var r,e;return r=Oi,"century"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Oo)),e===a&&("day"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(jo)),e===a&&("date"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Co)),e===a&&("decade"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(go)),e===a&&("dow"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Eo)),e===a&&("doy"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Ao)),e===a&&("epoch"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(To)),e===a&&("hour"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(So)),e===a&&("isodow"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Uo)),e===a&&("isoyear"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(_o)),e===a&&("microseconds"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(xo)),e===a&&("millennium"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Io)),e===a&&("milliseconds"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(No)),e===a&&("minute"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ro)),e===a&&("month"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ko)),e===a&&("quarter"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Vo)),e===a&&("second"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Mo)),e===a&&("timezone"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(qo)),e===a&&("timezone_hour"===t.substr(Oi,13).toLowerCase()?(e=t.substr(Oi,13),Oi+=13):(e=a,0===Ai&&xi(Po)),e===a&&("timezone_minute"===t.substr(Oi,15).toLowerCase()?(e=t.substr(Oi,15),Oi+=15):(e=a,0===Ai&&xi(Do)),e===a&&("week"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Qo)),e===a&&("year"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Fo))))))))))))))))))))))),e!==a&&(ji=r,e=e),r=e}function gf(){var r;return(r=function(){var r,e,n,o;r=Oi,"current_date"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(gu));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CURRENT_DATE"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Oi,"current_time"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Au));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CURRENT_TIME"):(Oi=r,r=a)):(Oi=r,r=a);return r}())===a&&(r=vb()),r}function Ef(){var t,r,e,n,o,s;if(t=Oi,r=[],e=Oi,(n=Lb())!==a&&(o=Fb())!==a&&(s=sv())!==a?e=n=[n,o,s]:(Oi=e,e=a),e!==a)for(;e!==a;)r.push(e),e=Oi,(n=Lb())!==a&&(o=Fb())!==a&&(s=sv())!==a?e=n=[n,o,s]:(Oi=e,e=a);else r=a;return r!==a&&(e=Fb())!==a?((n=Bc())===a&&(n=null),n!==a?(ji=t,t=r={as:n,symbol:"::",target:r.map(t=>t[2])}):(Oi=t,t=a)):(Oi=t,t=a),t}function Af(){var r;return(r=xf())===a&&(r=kf())===a&&(r=_f())===a&&(r=Sf())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=lb())===a&&(e=ub())===a&&(e=fb())===a&&(e=ib());if(e!==a)if(Fb()!==a){if(n=Oi,39===t.charCodeAt(Oi)?(o="'",Oi++):(o=a,0===Ai&&xi(Ur)),o!==a){for(s=[],u=Nf();u!==a;)s.push(u),u=Nf();s!==a?(39===t.charCodeAt(Oi)?(u="'",Oi++):(u=a,0===Ai&&xi(Ur)),u!==a?n=o=[o,s,u]:(Oi=n,n=a)):(Oi=n,n=a)}else Oi=n,n=a;n!==a?(ji=r,i=n,e={type:e.toLowerCase(),value:i[1].join("")},r=e):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;var i;if(r===a)if(r=Oi,(e=lb())===a&&(e=ub())===a&&(e=fb())===a&&(e=ib()),e!==a)if(Fb()!==a){if(n=Oi,34===t.charCodeAt(Oi)?(o='"',Oi++):(o=a,0===Ai&&xi(Ve)),o!==a){for(s=[],u=If();u!==a;)s.push(u),u=If();s!==a?(34===t.charCodeAt(Oi)?(u='"',Oi++):(u=a,0===Ai&&xi(Ve)),u!==a?n=o=[o,s,u]:(Oi=n,n=a)):(Oi=n,n=a)}else Oi=n,n=a;n!==a?(ji=r,e=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}}(e,n),r=e):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;return r}())===a&&(r=Tf()),r}function Tf(){var t,r;return t=Oi,Rp()!==a&&Fb()!==a&&Vb()!==a&&Fb()!==a?((r=Al())===a&&(r=null),r!==a&&Fb()!==a&&Mb()!==a?(ji=t,t={expr_list:r||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}):(Oi=t,t=a)):(Oi=t,t=a),t}function Sf(){var t,r;return t=Oi,(r=Ff())!==a&&(ji=t,r={type:"null",value:null}),t=r}function Uf(){var r,e;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"not null"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(wa));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e={type:"not null",value:"not null"}),r=e}function _f(){var r,e;return r=Oi,(e=function(){var r,e,n,o;r=Oi,"true"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(La));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e={type:"bool",value:!0}),(r=e)===a&&(r=Oi,(e=function(){var r,e,n,o;r=Oi,"false"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Oa));e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a);return r}())!==a&&(ji=r,e={type:"bool",value:!1}),r=e),r}function xf(){var r,e,n,o,s,u,i,c,l;if(r=Oi,e=Oi,39===t.charCodeAt(Oi)?(n="'",Oi++):(n=a,0===Ai&&xi(Ur)),n!==a){for(o=[],s=Nf();s!==a;)o.push(s),s=Nf();o!==a?(39===t.charCodeAt(Oi)?(s="'",Oi++):(s=a,0===Ai&&xi(Ur)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a)}else Oi=e,e=a;if(e!==a){if(n=[],$o.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Bo)),o!==a)for(;o!==a;)n.push(o),$o.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(Bo));else n=a;if(n!==a)if((o=Fb())!==a){if(s=Oi,39===t.charCodeAt(Oi)?(u="'",Oi++):(u=a,0===Ai&&xi(Ur)),u!==a){for(i=[],c=Nf();c!==a;)i.push(c),c=Nf();i!==a?(39===t.charCodeAt(Oi)?(c="'",Oi++):(c=a,0===Ai&&xi(Ur)),c!==a?s=u=[u,i,c]:(Oi=s,s=a)):(Oi=s,s=a)}else Oi=s,s=a;s!==a?(ji=r,l=s,r=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`}):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a}else Oi=r,r=a;if(r===a){if(r=Oi,e=Oi,39===t.charCodeAt(Oi)?(n="'",Oi++):(n=a,0===Ai&&xi(Ur)),n!==a){for(o=[],s=Nf();s!==a;)o.push(s),s=Nf();o!==a?(39===t.charCodeAt(Oi)?(s="'",Oi++):(s=a,0===Ai&&xi(Ur)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a)}else Oi=e,e=a;if(e!==a&&(ji=r,e=function(t){return{type:"single_quote_string",value:t[1].join("")}}(e)),(r=e)===a){if(r=Oi,e=Oi,34===t.charCodeAt(Oi)?(n='"',Oi++):(n=a,0===Ai&&xi(Ve)),n!==a){for(o=[],s=If();s!==a;)o.push(s),s=If();o!==a?(34===t.charCodeAt(Oi)?(s='"',Oi++):(s=a,0===Ai&&xi(Ve)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a)}else Oi=e,e=a;e!==a?(n=Oi,Ai++,o=xb(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e=function(t){return{type:"double_quote_string",value:t[1].join("")}}(e)):(Oi=r,r=a)):(Oi=r,r=a)}}return r}function If(){var r;return Ho.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(Yo)),r===a&&(r=Rf()),r}function Nf(){var r;return Wo.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(Xo)),r===a&&(r=Rf()),r}function Rf(){var r,e,n,o,s,u,i,c,l,f;return r=Oi,"\\'"===t.substr(Oi,2)?(e="\\'",Oi+=2):(e=a,0===Ai&&xi(Zo)),e!==a&&(ji=r,e="\\'"),(r=e)===a&&(r=Oi,'\\"'===t.substr(Oi,2)?(e='\\"',Oi+=2):(e=a,0===Ai&&xi(Ko)),e!==a&&(ji=r,e='\\"'),(r=e)===a&&(r=Oi,"\\\\"===t.substr(Oi,2)?(e="\\\\",Oi+=2):(e=a,0===Ai&&xi(Jo)),e!==a&&(ji=r,e="\\\\"),(r=e)===a&&(r=Oi,"\\/"===t.substr(Oi,2)?(e="\\/",Oi+=2):(e=a,0===Ai&&xi(zo)),e!==a&&(ji=r,e="\\/"),(r=e)===a&&(r=Oi,"\\b"===t.substr(Oi,2)?(e="\\b",Oi+=2):(e=a,0===Ai&&xi(ta)),e!==a&&(ji=r,e="\b"),(r=e)===a&&(r=Oi,"\\f"===t.substr(Oi,2)?(e="\\f",Oi+=2):(e=a,0===Ai&&xi(ra)),e!==a&&(ji=r,e="\f"),(r=e)===a&&(r=Oi,"\\n"===t.substr(Oi,2)?(e="\\n",Oi+=2):(e=a,0===Ai&&xi(ea)),e!==a&&(ji=r,e="\n"),(r=e)===a&&(r=Oi,"\\r"===t.substr(Oi,2)?(e="\\r",Oi+=2):(e=a,0===Ai&&xi(na)),e!==a&&(ji=r,e="\r"),(r=e)===a&&(r=Oi,"\\t"===t.substr(Oi,2)?(e="\\t",Oi+=2):(e=a,0===Ai&&xi(oa)),e!==a&&(ji=r,e="\t"),(r=e)===a&&(r=Oi,"\\u"===t.substr(Oi,2)?(e="\\u",Oi+=2):(e=a,0===Ai&&xi(aa)),e!==a&&(n=Qf())!==a&&(o=Qf())!==a&&(s=Qf())!==a&&(u=Qf())!==a?(ji=r,i=n,c=o,l=s,f=u,r=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Oi=r,r=a),r===a&&(r=Oi,92===t.charCodeAt(Oi)?(e="\\",Oi++):(e=a,0===Ai&&xi(sa)),e!==a&&(ji=r,e="\\"),(r=e)===a&&(r=Oi,"''"===t.substr(Oi,2)?(e="''",Oi+=2):(e=a,0===Ai&&xi(ua)),e!==a&&(ji=r,e="''"),r=e))))))))))),r}function kf(){var t,r,e;return t=Oi,(r=function(){var t,r,e,n;t=Oi,(r=Vf())===a&&(r=null);r!==a&&(e=Mf())!==a&&(n=qf())!==a?(ji=t,t=r={type:"bigint",value:(r||"")+e+n}):(Oi=t,t=a);t===a&&(t=Oi,(r=Vf())===a&&(r=null),r!==a&&(e=Mf())!==a?(ji=t,r=function(t,r){const e=(t||"")+r;return t&&dv(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e),t=r):(Oi=t,t=a),t===a&&(t=Oi,(r=Vf())!==a&&(e=qf())!==a?(ji=t,r=function(t,r){return{type:"bigint",value:t+r}}(r,e),t=r):(Oi=t,t=a),t===a&&(t=Oi,(r=Vf())!==a&&(ji=t,r=function(t){return dv(t)?{type:"bigint",value:t}:parseFloat(t)}(r)),t=r)));return t}())!==a&&(ji=t,r=(e=r)&&"bigint"===e.type?e:{type:"number",value:e}),t=r}function Vf(){var r,e,n;return(r=Pf())===a&&(r=Df())===a&&(r=Oi,45===t.charCodeAt(Oi)?(e="-",Oi++):(e=a,0===Ai&&xi(Ln)),e===a&&(43===t.charCodeAt(Oi)?(e="+",Oi++):(e=a,0===Ai&&xi(wn))),e!==a&&(n=Pf())!==a?(ji=r,r=e=e+n):(Oi=r,r=a),r===a&&(r=Oi,45===t.charCodeAt(Oi)?(e="-",Oi++):(e=a,0===Ai&&xi(Ln)),e===a&&(43===t.charCodeAt(Oi)?(e="+",Oi++):(e=a,0===Ai&&xi(wn))),e!==a&&(n=Df())!==a?(ji=r,r=e=function(t,r){return t+r}(e,n)):(Oi=r,r=a))),r}function Mf(){var r,e,n;return r=Oi,46===t.charCodeAt(Oi)?(e=".",Oi++):(e=a,0===Ai&&xi(la)),e!==a&&(n=Pf())!==a?(ji=r,r=e="."+n):(Oi=r,r=a),r}function qf(){var r,e,n;return r=Oi,(e=function(){var r,e,n;r=Oi,ya.test(t.charAt(Oi))?(e=t.charAt(Oi),Oi++):(e=a,0===Ai&&xi(da));e!==a?(ha.test(t.charAt(Oi))?(n=t.charAt(Oi),Oi++):(n=a,0===Ai&&xi(ma)),n===a&&(n=null),n!==a?(ji=r,r=e=e+(null!==(o=n)?o:"")):(Oi=r,r=a)):(Oi=r,r=a);var o;return r}())!==a&&(n=Pf())!==a?(ji=r,r=e=e+n):(Oi=r,r=a),r}function Pf(){var t,r,e;if(t=Oi,r=[],(e=Df())!==a)for(;e!==a;)r.push(e),e=Df();else r=a;return r!==a&&(ji=t,r=r.join("")),t=r}function Df(){var r;return fa.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(pa)),r}function Qf(){var r;return ba.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(va)),r}function Ff(){var r,e,n,o;return r=Oi,"null"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(D)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Gf(){var r,e,n,o;return r=Oi,"default"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(_t)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function $f(){var r,e,n,o;return r=Oi,"to"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(ot)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Bf(){var r,e,n,o;return r=Oi,"show"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(ja)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Hf(){var r,e,n,o;return r=Oi,"drop"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ca)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DROP"):(Oi=r,r=a)):(Oi=r,r=a),r}function Yf(){var r,e,n,o;return r=Oi,"alter"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Ea)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Wf(){var r,e,n,o;return r=Oi,"select"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Aa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Xf(){var r,e,n,o;return r=Oi,"update"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ta)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Zf(){var r,e,n,o;return r=Oi,"create"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Sa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Kf(){var r,e,n,o;return r=Oi,"temporary"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(Ua)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Jf(){var r,e,n,o;return r=Oi,"temp"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(_a)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function zf(){var r,e,n,o;return r=Oi,"delete"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(xa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function tp(){var r,e,n,o;return r=Oi,"insert"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ia)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function rp(){var r,e,n,o;return r=Oi,"recursive"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(Na)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="RECURSIVE"):(Oi=r,r=a)):(Oi=r,r=a),r}function ep(){var r,e,n,o;return r=Oi,"replace"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Ra)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function np(){var r,e,n,o;return r=Oi,"rename"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Va)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function op(){var r,e,n,o;return r=Oi,"ignore"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(so)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function ap(){var r,e,n,o;return r=Oi,"partition"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(Ma)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="PARTITION"):(Oi=r,r=a)):(Oi=r,r=a),r}function sp(){var r,e,n,o;return r=Oi,"into"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(qa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function up(){var r,e,n,o;return r=Oi,"from"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Pa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function ip(){var r,e,n,o;return r=Oi,"set"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(wr)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SET"):(Oi=r,r=a)):(Oi=r,r=a),r}function cp(){var r,e,n,o;return r=Oi,"as"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(Da)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function lp(){var r,e,n,o;return r=Oi,"table"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Se)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TABLE"):(Oi=r,r=a)):(Oi=r,r=a),r}function fp(){var r,e,n,o;return r=Oi,"schema"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(l)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SCHEMA"):(Oi=r,r=a)):(Oi=r,r=a),r}function pp(){var r,e,n,o;return r=Oi,"on"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(Q)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function bp(){var r,e,n,o;return r=Oi,"join"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ya)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function vp(){var r,e,n,o;return r=Oi,"outer"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Wa)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function yp(){var r,e,n,o;return r=Oi,"values"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ja)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function dp(){var r,e,n,o;return r=Oi,"using"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(za)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function hp(){var r,e,n,o;return r=Oi,"with"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ge)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function mp(){var r,e,n,o;return r=Oi,"group"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(rs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function wp(){var r,e,n,o;return r=Oi,"by"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(es)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Lp(){var r,e,n,o;return r=Oi,"order"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ns)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Op(){var r,e,n,o;return r=Oi,"asc"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(us)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ASC"):(Oi=r,r=a)):(Oi=r,r=a),r}function jp(){var r,e,n,o;return r=Oi,"desc"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(is)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DESC"):(Oi=r,r=a)):(Oi=r,r=a),r}function Cp(){var r,e,n,o;return r=Oi,"all"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(cs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ALL"):(Oi=r,r=a)):(Oi=r,r=a),r}function gp(){var r,e,n,o;return r=Oi,"distinct"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(ls)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DISTINCT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Ep(){var r,e,n,o;return r=Oi,"between"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(fs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="BETWEEN"):(Oi=r,r=a)):(Oi=r,r=a),r}function Ap(){var r,e,n,o;return r=Oi,"in"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(Nr)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="IN"):(Oi=r,r=a)):(Oi=r,r=a),r}function Tp(){var r,e,n,o;return r=Oi,"is"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(ps)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="IS"):(Oi=r,r=a)):(Oi=r,r=a),r}function Sp(){var r,e,n,o;return r=Oi,"like"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(bs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="LIKE"):(Oi=r,r=a)):(Oi=r,r=a),r}function Up(){var r,e,n,o;return r=Oi,"ilike"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(vs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ILIKE"):(Oi=r,r=a)):(Oi=r,r=a),r}function _p(){var r,e,n,o;return r=Oi,"exists"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(ys)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="EXISTS"):(Oi=r,r=a)):(Oi=r,r=a),r}function xp(){var r,e,n,o;return r=Oi,"not"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(M)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="NOT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Ip(){var r,e,n,o;return r=Oi,"and"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(ds)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="AND"):(Oi=r,r=a)):(Oi=r,r=a),r}function Np(){var r,e,n,o;return r=Oi,"or"===t.substr(Oi,2).toLowerCase()?(e=t.substr(Oi,2),Oi+=2):(e=a,0===Ai&&xi(hs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="OR"):(Oi=r,r=a)):(Oi=r,r=a),r}function Rp(){var r,e,n,o;return r=Oi,"array"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(ms)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ARRAY"):(Oi=r,r=a)):(Oi=r,r=a),r}function kp(){var r,e,n,o;return r=Oi,"extract"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Ts)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="EXTRACT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Vp(){var r,e,n,o;return r=Oi,"case"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Us)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Mp(){var r,e,n,o;return r=Oi,"when"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(_s)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function qp(){var r,e,n,o;return r=Oi,"else"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(xs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Pp(){var r,e,n,o;return r=Oi,"end"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(ve)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?r=e=[e,n]:(Oi=r,r=a)):(Oi=r,r=a),r}function Dp(){var r,e,n,o;return r=Oi,"cast"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Is)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CAST"):(Oi=r,r=a)):(Oi=r,r=a),r}function Qp(){var r,e,n,o;return r=Oi,"char"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(ks)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CHAR"):(Oi=r,r=a)):(Oi=r,r=a),r}function Fp(){var r,e,n,o;return r=Oi,"varchar"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Vs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="VARCHAR"):(Oi=r,r=a)):(Oi=r,r=a),r}function Gp(){var r,e,n,o;return r=Oi,"numeric"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Ms)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="NUMERIC"):(Oi=r,r=a)):(Oi=r,r=a),r}function $p(){var r,e,n,o;return r=Oi,"decimal"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(qs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DECIMAL"):(Oi=r,r=a)):(Oi=r,r=a),r}function Bp(){var r,e,n,o;return r=Oi,"unsigned"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Ds)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="UNSIGNED"):(Oi=r,r=a)):(Oi=r,r=a),r}function Hp(){var r,e,n,o;return r=Oi,"int"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Qs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="INT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Yp(){var r,e,n,o;return r=Oi,"integer"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Gs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="INTEGER"):(Oi=r,r=a)):(Oi=r,r=a),r}function Wp(){var r,e,n,o;return r=Oi,"smallint"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Ys)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SMALLINT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Xp(){var r,e,n,o;return r=Oi,"serial"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ws)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SERIAL"):(Oi=r,r=a)):(Oi=r,r=a),r}function Zp(){var r,e,n,o;return r=Oi,"tinyint"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Xs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TINYINT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Kp(){var r,e,n,o;return r=Oi,"tinytext"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Zs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TINYTEXT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Jp(){var r,e,n,o;return r=Oi,"text"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ks)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TEXT"):(Oi=r,r=a)):(Oi=r,r=a),r}function zp(){var r,e,n,o;return r=Oi,"mediumtext"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Js)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="MEDIUMTEXT"):(Oi=r,r=a)):(Oi=r,r=a),r}function tb(){var r,e,n,o;return r=Oi,"longtext"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(zs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="LONGTEXT"):(Oi=r,r=a)):(Oi=r,r=a),r}function rb(){var r,e,n,o;return r=Oi,"bigint"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(tu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="BIGINT"):(Oi=r,r=a)):(Oi=r,r=a),r}function eb(){var r,e,n,o;return r=Oi,"enum"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(ru)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ENUM"):(Oi=r,r=a)):(Oi=r,r=a),r}function nb(){var r,e,n,o;return r=Oi,"float"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(eu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="FLOAT"):(Oi=r,r=a)):(Oi=r,r=a),r}function ob(){var r,e,n,o;return r=Oi,"double"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(nu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DOUBLE"):(Oi=r,r=a)):(Oi=r,r=a),r}function ab(){var r,e,n,o;return r=Oi,"bigserial"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(ou)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="BIGSERIAL"):(Oi=r,r=a)):(Oi=r,r=a),r}function sb(){var r,e,n,o;return r=Oi,"real"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(au)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REAL"):(Oi=r,r=a)):(Oi=r,r=a),r}function ub(){var r,e,n,o;return r=Oi,"date"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Co)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DATE"):(Oi=r,r=a)):(Oi=r,r=a),r}function ib(){var r,e,n,o;return r=Oi,"datetime"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(su)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="DATETIME"):(Oi=r,r=a)):(Oi=r,r=a),r}function cb(){var r,e,n,o;return r=Oi,"rows"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(et)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ROWS"):(Oi=r,r=a)):(Oi=r,r=a),r}function lb(){var r,e,n,o;return r=Oi,"time"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(uu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TIME"):(Oi=r,r=a)):(Oi=r,r=a),r}function fb(){var r,e,n,o;return r=Oi,"timestamp"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(iu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TIMESTAMP"):(Oi=r,r=a)):(Oi=r,r=a),r}function pb(){var r,e,n,o;return r=Oi,"truncate"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(cu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="TRUNCATE"):(Oi=r,r=a)):(Oi=r,r=a),r}function bb(){var r,e,n,o;return r=Oi,"interval"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Eu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="INTERVAL"):(Oi=r,r=a)):(Oi=r,r=a),r}function vb(){var r,e,n,o;return r=Oi,"current_timestamp"===t.substr(Oi,17).toLowerCase()?(e=t.substr(Oi,17),Oi+=17):(e=a,0===Ai&&xi(Tu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CURRENT_TIMESTAMP"):(Oi=r,r=a)):(Oi=r,r=a),r}function yb(){var r,e,n,o;return r=Oi,"current_user"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Qt)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CURRENT_USER"):(Oi=r,r=a)):(Oi=r,r=a),r}function db(){var r,e,n,o;return r=Oi,"session_user"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Ft)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="SESSION_USER"):(Oi=r,r=a)):(Oi=r,r=a),r}function hb(){var r;return 36===t.charCodeAt(Oi)?(r="$",Oi++):(r=a,0===Ai&&xi(En)),r}function mb(){var r;return"$$"===t.substr(Oi,2)?(r="$$",Oi+=2):(r=a,0===Ai&&xi(Vu)),r}function wb(){var r;return(r=function(){var r;return"@@"===t.substr(Oi,2)?(r="@@",Oi+=2):(r=a,0===Ai&&xi(ku)),r}())===a&&(r=function(){var r;return 64===t.charCodeAt(Oi)?(r="@",Oi++):(r=a,0===Ai&&xi(Ru)),r}())===a&&(r=hb())===a&&(r=hb()),r}function Lb(){var r;return"::"===t.substr(Oi,2)?(r="::",Oi+=2):(r=a,0===Ai&&xi(qu)),r}function Ob(){var r;return 61===t.charCodeAt(Oi)?(r="=",Oi++):(r=a,0===Ai&&xi(at)),r}function jb(){var r,e,n,o;return r=Oi,"add"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Du)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ADD"):(Oi=r,r=a)):(Oi=r,r=a),r}function Cb(){var r,e,n,o;return r=Oi,"column"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Ee)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="COLUMN"):(Oi=r,r=a)):(Oi=r,r=a),r}function gb(){var r,e,n,o;return r=Oi,"index"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Qu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="INDEX"):(Oi=r,r=a)):(Oi=r,r=a),r}function Eb(){var r,e,n,o;return r=Oi,"key"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(Et)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="KEY"):(Oi=r,r=a)):(Oi=r,r=a),r}function Ab(){var r,e,n,o;return r=Oi,"unique"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(gt)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="UNIQUE"):(Oi=r,r=a)):(Oi=r,r=a),r}function Tb(){var r,e,n,o;return r=Oi,"comment"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi($u)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="COMMENT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Sb(){var r,e,n,o;return r=Oi,"constraint"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Ae)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CONSTRAINT"):(Oi=r,r=a)):(Oi=r,r=a),r}function Ub(){var r,e,n,o;return r=Oi,"concurrently"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Bu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CONCURRENTLY"):(Oi=r,r=a)):(Oi=r,r=a),r}function _b(){var r,e,n,o;return r=Oi,"references"===t.substr(Oi,10).toLowerCase()?(e=t.substr(Oi,10),Oi+=10):(e=a,0===Ai&&xi(Hu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REFERENCES"):(Oi=r,r=a)):(Oi=r,r=a),r}function xb(){var r;return 46===t.charCodeAt(Oi)?(r=".",Oi++):(r=a,0===Ai&&xi(la)),r}function Ib(){var r;return 44===t.charCodeAt(Oi)?(r=",",Oi++):(r=a,0===Ai&&xi(zu)),r}function Nb(){var r;return 42===t.charCodeAt(Oi)?(r="*",Oi++):(r=a,0===Ai&&xi(On)),r}function Rb(){var r;return 40===t.charCodeAt(Oi)?(r="(",Oi++):(r=a,0===Ai&&xi(Ne)),r}function kb(){var r;return 41===t.charCodeAt(Oi)?(r=")",Oi++):(r=a,0===Ai&&xi(Re)),r}function Vb(){var r;return 91===t.charCodeAt(Oi)?(r="[",Oi++):(r=a,0===Ai&&xi(ti)),r}function Mb(){var r;return 93===t.charCodeAt(Oi)?(r="]",Oi++):(r=a,0===Ai&&xi(ri)),r}function qb(){var r;return 59===t.charCodeAt(Oi)?(r=";",Oi++):(r=a,0===Ai&&xi(Ie)),r}function Pb(){var r;return"->"===t.substr(Oi,2)?(r="->",Oi+=2):(r=a,0===Ai&&xi(ei)),r}function Db(){var r;return"->>"===t.substr(Oi,3)?(r="->>",Oi+=3):(r=a,0===Ai&&xi(ni)),r}function Qb(){var r;return(r=function(){var r;return"||"===t.substr(Oi,2)?(r="||",Oi+=2):(r=a,0===Ai&&xi(gn)),r}())===a&&(r=function(){var r;return"&&"===t.substr(Oi,2)?(r="&&",Oi+=2):(r=a,0===Ai&&xi(oi)),r}()),r}function Fb(){var t,r;for(t=[],(r=Yb())===a&&(r=$b());r!==a;)t.push(r),(r=Yb())===a&&(r=$b());return t}function Gb(){var t,r;if(t=[],(r=Yb())===a&&(r=$b()),r!==a)for(;r!==a;)t.push(r),(r=Yb())===a&&(r=$b());else t=a;return t}function $b(){var r;return(r=function r(){var e,n,o,s,u,i,c;e=Oi,"/*"===t.substr(Oi,2)?(n="/*",Oi+=2):(n=a,0===Ai&&xi(ai));if(n!==a){for(o=[],s=Oi,u=Oi,Ai++,"*/"===t.substr(Oi,2)?(i="*/",Oi+=2):(i=a,0===Ai&&xi(si)),Ai--,i===a?u=void 0:(Oi=u,u=a),u!==a?(i=Oi,Ai++,"/*"===t.substr(Oi,2)?(c="/*",Oi+=2):(c=a,0===Ai&&xi(ai)),Ai--,c===a?i=void 0:(Oi=i,i=a),i!==a&&(c=Hb())!==a?s=u=[u,i,c]:(Oi=s,s=a)):(Oi=s,s=a),s===a&&(s=r());s!==a;)o.push(s),s=Oi,u=Oi,Ai++,"*/"===t.substr(Oi,2)?(i="*/",Oi+=2):(i=a,0===Ai&&xi(si)),Ai--,i===a?u=void 0:(Oi=u,u=a),u!==a?(i=Oi,Ai++,"/*"===t.substr(Oi,2)?(c="/*",Oi+=2):(c=a,0===Ai&&xi(ai)),Ai--,c===a?i=void 0:(Oi=i,i=a),i!==a&&(c=Hb())!==a?s=u=[u,i,c]:(Oi=s,s=a)):(Oi=s,s=a),s===a&&(s=r());o!==a?("*/"===t.substr(Oi,2)?(s="*/",Oi+=2):(s=a,0===Ai&&xi(si)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a)}else Oi=e,e=a;return e}())===a&&(r=function(){var r,e,n,o,s,u;r=Oi,"--"===t.substr(Oi,2)?(e="--",Oi+=2):(e=a,0===Ai&&xi(ui));if(e!==a){for(n=[],o=Oi,s=Oi,Ai++,u=Wb(),Ai--,u===a?s=void 0:(Oi=s,s=a),s!==a&&(u=Hb())!==a?o=s=[s,u]:(Oi=o,o=a);o!==a;)n.push(o),o=Oi,s=Oi,Ai++,u=Wb(),Ai--,u===a?s=void 0:(Oi=s,s=a),s!==a&&(u=Hb())!==a?o=s=[s,u]:(Oi=o,o=a);n!==a?r=e=[e,n]:(Oi=r,r=a)}else Oi=r,r=a;return r}()),r}function Bb(){var t,r,e,n,o,s,u;return t=Oi,(r=Tb())!==a&&Fb()!==a?((e=Ob())===a&&(e=null),e!==a&&Fb()!==a&&(n=xf())!==a?(ji=t,s=e,u=n,t=r={type:(o=r).toLowerCase(),keyword:o.toLowerCase(),symbol:s,value:u}):(Oi=t,t=a)):(Oi=t,t=a),t}function Hb(){var r;return t.length>Oi?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(ii)),r}function Yb(){var r;return ci.test(t.charAt(Oi))?(r=t.charAt(Oi),Oi++):(r=a,0===Ai&&xi(li)),r}function Wb(){var r,e;if((r=function(){var r,e;r=Oi,Ai++,t.length>Oi?(e=t.charAt(Oi),Oi++):(e=a,0===Ai&&xi(ii));Ai--,e===a?r=void 0:(Oi=r,r=a);return r}())===a)if(r=[],ia.test(t.charAt(Oi))?(e=t.charAt(Oi),Oi++):(e=a,0===Ai&&xi(ca)),e!==a)for(;e!==a;)r.push(e),ia.test(t.charAt(Oi))?(e=t.charAt(Oi),Oi++):(e=a,0===Ai&&xi(ca));else r=a;return r}function Xb(){var r,e;return r=Oi,ji=Oi,jv=[],(!0?void 0:a)!==a&&Fb()!==a?((e=Zb())===a&&(e=function(){var r,e;r=Oi,function(){var r;return"return"===t.substr(Oi,6).toLowerCase()?(r=t.substr(Oi,6),Oi+=6):(r=a,0===Ai&&xi(Mu)),r}()!==a&&Fb()!==a&&(e=Kb())!==a?(ji=r,r={type:"return",expr:e}):(Oi=r,r=a);return r}()),e!==a?(ji=r,r={type:"proc",stmt:e,vars:jv}):(Oi=r,r=a)):(Oi=r,r=a),r}function Zb(){var r,e,n,o;return r=Oi,(e=ov())===a&&(e=av()),e!==a&&Fb()!==a?((n=function(){var r;return":="===t.substr(Oi,2)?(r=":=",Oi+=2):(r=a,0===Ai&&xi(E)),r}())===a&&(n=Ob()),n!==a&&Fb()!==a&&(o=Kb())!==a?(ji=r,r=e={type:"assign",left:e,symbol:n,right:o}):(Oi=r,r=a)):(Oi=r,r=a),r}function Kb(){var t;return(t=Ic())===a&&(t=function(){var t,r,e,n,o;t=Oi,(r=ov())!==a&&Fb()!==a&&(e=rl())!==a&&Fb()!==a&&(n=ov())!==a&&Fb()!==a&&(o=ol())!==a?(ji=t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(Oi=t,t=a);return t}())===a&&(t=Jb())===a&&(t=function(){var t,r;t=Oi,Vb()!==a&&Fb()!==a&&(r=nv())!==a&&Fb()!==a&&Mb()!==a?(ji=t,t={type:"array",value:r}):(Oi=t,t=a);return t}()),t}function Jb(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=zb())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ql())!==a&&(u=Fb())!==a&&(i=zb())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ql())!==a&&(u=Fb())!==a&&(i=zb())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=on(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function zb(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=tv())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Gl())!==a&&(u=Fb())!==a&&(i=tv())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Gl())!==a&&(u=Fb())!==a&&(i=tv())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=on(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function tv(){var t,r,e,n,o,s,u,i,c;return(t=Af())===a&&(t=ov())===a&&(t=ev())===a&&(t=bf())===a&&(t=Oi,(r=Rb())!==a&&(e=Fb())!==a&&(n=Jb())!==a&&(o=Fb())!==a&&(s=kb())!==a?(ji=t,(c=n).parentheses=!0,t=r=c):(Oi=t,t=a),t===a&&(t=Oi,(r=cf())!==a?(e=Oi,(n=xb())!==a&&(o=Fb())!==a&&(s=cf())!==a?e=n=[n,o,s]:(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=t,u=r,t=r=(i=e)?{type:"column_ref",table:u,column:i[2]}:{type:"var",name:u,prefix:null}):(Oi=t,t=a)):(Oi=t,t=a))),t}function rv(){var t,r,e,n,o,s,u;return t=Oi,(r=Zl())!==a?(e=Oi,(n=Fb())!==a&&(o=xb())!==a&&(s=Fb())!==a&&(u=Zl())!==a?e=n=[n,o,s,u]:(Oi=e,e=a),e===a&&(e=null),e!==a?(ji=t,t=r=function(t,r){const e={name:[t]};return null!==r&&(e.schema=t,e.name=[r[3]]),e}(r,e)):(Oi=t,t=a)):(Oi=t,t=a),t}function ev(){var t,r,e;return t=Oi,(r=rv())!==a&&Fb()!==a&&Rb()!==a&&Fb()!==a?((e=nv())===a&&(e=null),e!==a&&Fb()!==a&&kb()!==a?(ji=t,t=r=function(t,r){return{type:"function",name:t,args:{type:"expr_list",value:r},...bv()}}(r,e)):(Oi=t,t=a)):(Oi=t,t=a),t}function nv(){var t,r,e,n,o,s,u,i;if(t=Oi,(r=tv())!==a){for(e=[],n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=tv())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);n!==a;)e.push(n),n=Oi,(o=Fb())!==a&&(s=Ib())!==a&&(u=Fb())!==a&&(i=tv())!==a?n=o=[o,s,u,i]:(Oi=n,n=a);e!==a?(ji=t,t=r=hv(r,e)):(Oi=t,t=a)}else Oi=t,t=a;return t}function ov(){var r,e,n,o,s,u,i;if(r=Oi,(e=mb())!==a){for(n=[],fi.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pi));o!==a;)n.push(o),fi.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pi));n!==a&&(o=mb())!==a?(ji=r,r=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(Oi=r,r=a)}else Oi=r,r=a;if(r===a){if(r=Oi,(e=hb())!==a)if((n=sf())!==a)if((o=hb())!==a){for(s=[],fi.test(t.charAt(Oi))?(u=t.charAt(Oi),Oi++):(u=a,0===Ai&&xi(pi));u!==a;)s.push(u),fi.test(t.charAt(Oi))?(u=t.charAt(Oi),Oi++):(u=a,0===Ai&&xi(pi));s!==a&&(u=hb())!==a&&(i=sf())!==a?(ji=Oi,(function(t,r,e){if(t!==e)return!0}(n,0,i)?a:void 0)!==a&&hb()!==a?(ji=r,r=e=function(t,r,e){return{type:"var",name:r.join(""),prefix:`$${t}$`,suffix:`$${e}$`}}(n,s,i)):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;r===a&&(r=Oi,(e=wb())!==a&&(n=av())!==a?(ji=r,r=e=function(t,r){return{type:"var",...r,prefix:t}}(e,n)):(Oi=r,r=a))}return r}function av(){var r,e,n,o,s;return r=Oi,34===t.charCodeAt(Oi)?(e='"',Oi++):(e=a,0===Ai&&xi(Ve)),e===a&&(e=null),e!==a&&(n=cf())!==a&&(o=function(){var r,e,n,o,s;r=Oi,e=[],n=Oi,46===t.charCodeAt(Oi)?(o=".",Oi++):(o=a,0===Ai&&xi(la));o!==a&&(s=cf())!==a?n=o=[o,s]:(Oi=n,n=a);for(;n!==a;)e.push(n),n=Oi,46===t.charCodeAt(Oi)?(o=".",Oi++):(o=a,0===Ai&&xi(la)),o!==a&&(s=cf())!==a?n=o=[o,s]:(Oi=n,n=a);e!==a&&(ji=r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==a?(34===t.charCodeAt(Oi)?(s='"',Oi++):(s=a,0===Ai&&xi(Ve)),s===a&&(s=null),s!==a?(ji=r,r=e=function(t,r,e,n){if(t&&!n||!t&&n)throw new Error("double quoted not match");return jv.push(r),{type:"var",name:r,members:e,quoted:t&&n?'"':null,prefix:null}}(e,n,o,s)):(Oi=r,r=a)):(Oi=r,r=a),r===a&&(r=Oi,(e=kf())!==a&&(ji=r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function sv(){var r;return(r=function(){var t,r,e;t=Oi,(r=lv())===a&&(r=iv());r!==a&&Fb()!==a&&Vb()!==a&&Fb()!==a&&(e=Mb())!==a&&Fb()!==a&&Vb()!==a&&Fb()!==a&&Mb()!==a?(ji=t,n=r,r={...n,array:{dimension:2}},t=r):(Oi=t,t=a);var n;t===a&&(t=Oi,(r=lv())===a&&(r=iv()),r!==a&&Fb()!==a&&Vb()!==a&&Fb()!==a?((e=kf())===a&&(e=null),e!==a&&Fb()!==a&&Mb()!==a?(ji=t,r=function(t,r){return{...t,array:{dimension:1,length:[r]}}}(r,e),t=r):(Oi=t,t=a)):(Oi=t,t=a),t===a&&(t=Oi,(r=lv())===a&&(r=iv()),r!==a&&Fb()!==a&&Rp()!==a?(ji=t,r=function(t){return{...t,array:{keyword:"array"}}}(r),t=r):(Oi=t,t=a)));return t}())===a&&(r=iv())===a&&(r=lv())===a&&(r=function(){var r,e,n,o;r=Oi,(e=ub())===a&&(e=ib());if(e!==a)if(Fb()!==a)if(Rb()!==a)if(Fb()!==a){if(n=[],fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa)),o!==a)for(;o!==a;)n.push(o),fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa));else n=a;n!==a&&(o=Fb())!==a&&kb()!==a?(ji=r,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},r=e):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;r===a&&(r=Oi,(e=ub())===a&&(e=ib()),e!==a&&(ji=r,e=wi(e)),(r=e)===a&&(r=function(){var r,e,n,o,s,u;r=Oi,(e=lb())===a&&(e=fb());if(e!==a)if(Fb()!==a)if((n=Rb())!==a)if(Fb()!==a){if(o=[],fa.test(t.charAt(Oi))?(s=t.charAt(Oi),Oi++):(s=a,0===Ai&&xi(pa)),s!==a)for(;s!==a;)o.push(s),fa.test(t.charAt(Oi))?(s=t.charAt(Oi),Oi++):(s=a,0===Ai&&xi(pa));else o=a;o!==a&&(s=Fb())!==a&&kb()!==a&&Fb()!==a?((u=fv())===a&&(u=null),u!==a?(ji=r,e=function(t,r,e){return{dataType:t,length:parseInt(r.join(""),10),parentheses:!0,suffix:e}}(e,o,u),r=e):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;r===a&&(r=Oi,(e=lb())===a&&(e=fb()),e!==a&&Fb()!==a?((n=fv())===a&&(n=null),n!==a?(ji=r,e=function(t,r){return{dataType:t,suffix:r}}(e,n),r=e):(Oi=r,r=a)):(Oi=r,r=a));return r}()));return r}())===a&&(r=function(){var r,e;r=Oi,(e=function(){var r,e,n,o;return r=Oi,"json"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi($s)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="JSON"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"jsonb"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(Bs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="JSONB"):(Oi=r,r=a)):(Oi=r,r=a),r}());e!==a&&(ji=r,e=wi(e));return r=e}())===a&&(r=function(){var r,e;r=Oi,(e=function(){var r,e,n,o;return r=Oi,"geometry"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Hs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="GEOMETRY"):(Oi=r,r=a)):(Oi=r,r=a),r}())!==a&&(ji=r,e={dataType:e});return r=e}())===a&&(r=function(){var t,r;t=Oi,(r=Kp())===a&&(r=Jp())===a&&(r=zp())===a&&(r=tb());r!==a&&Vb()!==a&&Fb()!==a&&Mb()!==a?(ji=t,t=r={dataType:r+"[]"}):(Oi=t,t=a);t===a&&(t=Oi,(r=Kp())===a&&(r=Jp())===a&&(r=zp())===a&&(r=tb()),r!==a&&(ji=t,r=function(t){return{dataType:t}}(r)),t=r);return t}())===a&&(r=function(){var r,e;r=Oi,(e=function(){var r,e,n,o;return r=Oi,"uuid"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(fu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="UUID"):(Oi=r,r=a)):(Oi=r,r=a),r}())!==a&&(ji=r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e;r=Oi,(e=function(){var r,e,n,o;return r=Oi,"bool"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ns)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="BOOL"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"boolean"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Rs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="BOOLEAN"):(Oi=r,r=a)):(Oi=r,r=a),r}());e!==a&&(ji=r,e=bi(e));return r=e}())===a&&(r=function(){var t,r,e;t=Oi,(r=eb())!==a&&Fb()!==a&&(e=El())!==a?(ji=t,n=r,(o=e).parentheses=!0,t=r={dataType:n,expr:o}):(Oi=t,t=a);var n,o;return t}())===a&&(r=function(){var t,r;t=Oi,(r=Xp())===a&&(r=bb());r!==a&&(ji=t,r=wi(r));return t=r}())===a&&(r=function(){var r,e;r=Oi,"bytea"===t.substr(Oi,5).toLowerCase()?(e=t.substr(Oi,5),Oi+=5):(e=a,0===Ai&&xi(vi));e!==a&&(ji=r,e={dataType:"BYTEA"});return r=e}())===a&&(r=function(){var r,e;r=Oi,(e=function(){var r,e,n,o;return r=Oi,"oid"===t.substr(Oi,3).toLowerCase()?(e=t.substr(Oi,3),Oi+=3):(e=a,0===Ai&&xi(pu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="OID"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regclass"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(bu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGCLASS"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regcollation"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(vu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGCOLLATION"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regconfig"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(yu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGCONFIG"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regdictionary"===t.substr(Oi,13).toLowerCase()?(e=t.substr(Oi,13),Oi+=13):(e=a,0===Ai&&xi(du)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGDICTIONARY"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regnamespace"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(hu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGNAMESPACE"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regoper"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(mu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGOPER"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regoperator"===t.substr(Oi,11).toLowerCase()?(e=t.substr(Oi,11),Oi+=11):(e=a,0===Ai&&xi(wu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGOPERATOR"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regproc"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Lu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGPROC"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regprocedure"===t.substr(Oi,12).toLowerCase()?(e=t.substr(Oi,12),Oi+=12):(e=a,0===Ai&&xi(Ou)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGPROCEDURE"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regrole"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(ju)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGROLE"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Oi,"regtype"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(Cu)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="REGTYPE"):(Oi=r,r=a)):(Oi=r,r=a),r}());e!==a&&(ji=r,e=bi(e));return r=e}())===a&&(r=function(){var r,e;r=Oi,"record"===t.substr(Oi,6).toLowerCase()?(e=t.substr(Oi,6),Oi+=6):(e=a,0===Ai&&xi(Li));e!==a&&(ji=r,e={dataType:"RECORD"});return r=e}()),r}function uv(){var r,e;return r=Oi,function(){var r,e,n,o;return r=Oi,"character"===t.substr(Oi,9).toLowerCase()?(e=t.substr(Oi,9),Oi+=9):(e=a,0===Ai&&xi(mr)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="CHARACTER"):(Oi=r,r=a)):(Oi=r,r=a),r}()!==a&&Fb()!==a?("varying"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(yi)),e===a&&(e=null),e!==a?(ji=r,r="CHARACTER VARYING"):(Oi=r,r=a)):(Oi=r,r=a),r}function iv(){var r,e,n,o;if(r=Oi,(e=Qp())===a&&(e=Fp())===a&&(e=uv()),e!==a)if(Fb()!==a)if(Rb()!==a)if(Fb()!==a){if(n=[],fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa)),o!==a)for(;o!==a;)n.push(o),fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa));else n=a;n!==a&&(o=Fb())!==a&&kb()!==a?(ji=r,r=e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0}):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;return r===a&&(r=Oi,(e=Qp())===a&&(e=uv())===a&&(e=Fp()),e!==a&&(ji=r,e=function(t){return{dataType:t}}(e)),r=e),r}function cv(){var r,e,n;return r=Oi,(e=Bp())===a&&(e=null),e!==a&&Fb()!==a?((n=function(){var r,e,n,o;return r=Oi,"zerofill"===t.substr(Oi,8).toLowerCase()?(e=t.substr(Oi,8),Oi+=8):(e=a,0===Ai&&xi(Fs)),e!==a?(n=Oi,Ai++,o=lf(),Ai--,o===a?n=void 0:(Oi=n,n=a),n!==a?(ji=r,r=e="ZEROFILL"):(Oi=r,r=a)):(Oi=r,r=a),r}())===a&&(n=null),n!==a?(ji=r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(Oi=r,r=a)):(Oi=r,r=a),r}function lv(){var r,e,n,o,s,u,i,c,l,f,p,b,v,y,d,h;if(r=Oi,(e=Gp())===a&&(e=$p())===a&&(e=Hp())===a&&(e=Yp())===a&&(e=Wp())===a&&(e=Zp())===a&&(e=rb())===a&&(e=nb())===a&&(e=Oi,(n=ob())!==a&&(o=Fb())!==a?("precision"===t.substr(Oi,9).toLowerCase()?(s=t.substr(Oi,9),Oi+=9):(s=a,0===Ai&&xi(di)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=ob())===a&&(e=Xp())===a&&(e=ab())===a&&(e=sb())),e!==a)if((n=Fb())!==a)if((o=Rb())!==a)if((s=Fb())!==a){if(u=[],fa.test(t.charAt(Oi))?(i=t.charAt(Oi),Oi++):(i=a,0===Ai&&xi(pa)),i!==a)for(;i!==a;)u.push(i),fa.test(t.charAt(Oi))?(i=t.charAt(Oi),Oi++):(i=a,0===Ai&&xi(pa));else u=a;if(u!==a)if((i=Fb())!==a){if(c=Oi,(l=Ib())!==a)if((f=Fb())!==a){if(p=[],fa.test(t.charAt(Oi))?(b=t.charAt(Oi),Oi++):(b=a,0===Ai&&xi(pa)),b!==a)for(;b!==a;)p.push(b),fa.test(t.charAt(Oi))?(b=t.charAt(Oi),Oi++):(b=a,0===Ai&&xi(pa));else p=a;p!==a?c=l=[l,f,p]:(Oi=c,c=a)}else Oi=c,c=a;else Oi=c,c=a;c===a&&(c=null),c!==a&&(l=Fb())!==a&&(f=kb())!==a&&(p=Fb())!==a?((b=cv())===a&&(b=null),b!==a?(ji=r,v=e,y=u,d=c,h=b,r=e={dataType:Array.isArray(v)?`${v[0].toUpperCase()} ${v[2].toUpperCase()}`:v,length:parseInt(y.join(""),10),scale:d&&parseInt(d[2].join(""),10),parentheses:!0,suffix:h}):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;else Oi=r,r=a}else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;else Oi=r,r=a;if(r===a){if(r=Oi,(e=Gp())===a&&(e=$p())===a&&(e=Hp())===a&&(e=Yp())===a&&(e=Wp())===a&&(e=Zp())===a&&(e=rb())===a&&(e=nb())===a&&(e=Oi,(n=ob())!==a&&(o=Fb())!==a?("precision"===t.substr(Oi,9).toLowerCase()?(s=t.substr(Oi,9),Oi+=9):(s=a,0===Ai&&xi(di)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=ob())===a&&(e=Xp())===a&&(e=ab())===a&&(e=sb())),e!==a){if(n=[],fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa)),o!==a)for(;o!==a;)n.push(o),fa.test(t.charAt(Oi))?(o=t.charAt(Oi),Oi++):(o=a,0===Ai&&xi(pa));else n=a;n!==a&&(o=Fb())!==a?((s=cv())===a&&(s=null),s!==a?(ji=r,r=e=function(t,r,e){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,length:parseInt(r.join(""),10),suffix:e}}(e,n,s)):(Oi=r,r=a)):(Oi=r,r=a)}else Oi=r,r=a;r===a&&(r=Oi,(e=Gp())===a&&(e=$p())===a&&(e=Hp())===a&&(e=Yp())===a&&(e=Wp())===a&&(e=Zp())===a&&(e=rb())===a&&(e=nb())===a&&(e=Oi,(n=ob())!==a&&(o=Fb())!==a?("precision"===t.substr(Oi,9).toLowerCase()?(s=t.substr(Oi,9),Oi+=9):(s=a,0===Ai&&xi(di)),s!==a?e=n=[n,o,s]:(Oi=e,e=a)):(Oi=e,e=a),e===a&&(e=ob())===a&&(e=Xp())===a&&(e=ab())===a&&(e=sb())),e!==a&&(n=Fb())!==a?((o=cv())===a&&(o=null),o!==a&&(s=Fb())!==a?(ji=r,r=e=function(t,r){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,suffix:r}}(e,o)):(Oi=r,r=a)):(Oi=r,r=a))}return r}function fv(){var r,e,n;return r=Oi,"without"===t.substr(Oi,7).toLowerCase()?(e=t.substr(Oi,7),Oi+=7):(e=a,0===Ai&&xi(hi)),e===a&&("with"===t.substr(Oi,4).toLowerCase()?(e=t.substr(Oi,4),Oi+=4):(e=a,0===Ai&&xi(Ge))),e!==a&&Fb()!==a&&lb()!==a&&Fb()!==a?("zone"===t.substr(Oi,4).toLowerCase()?(n=t.substr(Oi,4),Oi+=4):(n=a,0===Ai&&xi(mi)),n!==a?(ji=r,r=e=[e.toUpperCase(),"TIME","ZONE"]):(Oi=r,r=a)):(Oi=r,r=a),r}const pv={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function bv(){return r.includeLocations?{loc:_i(ji,Oi)}:{}}function vv(t,r){return{type:"unary_expr",operator:t,expr:r}}function yv(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e}}function dv(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function hv(t,r,e=3){const n=Array.isArray(t)?t:[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function mv(t,r){let e=t;for(let t=0;t<r.length;t++)e=yv(r[t][1],e,r[t][3]);return e}function wv(t){const r=Ev[t];return r||(t||null)}function Lv(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=wv(t[1])),r.add(t.join("::"))}return Array.from(r)}function Ov(t){return"string"==typeof t?{type:"same",value:t}:t}let jv=[];const Cv=new Set,gv=new Set,Ev={};if((e=u())!==a&&Oi===t.length)return e;throw e!==a&&Oi<t.length&&xi({type:"end"}),Ii(Ei,gi<t.length?t.charAt(gi):null,gi<t.length?_i(gi,gi+1):_i(gi,gi))}}},function(t,r,e){t.exports=e(27)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(24);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(28))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),a="function"==typeof BigInt;function s(t,r,e,n){return void 0===t?s[0]:void 0!==r&&(10!=+r||e)?D(t,r,e,n):B(t)}function u(t,r){this.value=t,this.sign=r,this.isSmall=!1}function i(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&S(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function y(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,e){var n,o,a=t.length,s=e.length,u=new Array(a),i=0,c=r;for(o=0;o<s;o++)i=(n=t[o]+e[o]+i)>=c?1:0,u[o]=n-i*c;for(;o<a;)i=(n=t[o]+i)===c?1:0,u[o++]=n-i*c;return i>0&&u.push(i),u}function h(t,r){return t.length>=r.length?d(t,r):d(r,t)}function m(t,e){var n,o,a=t.length,s=new Array(a),u=r;for(o=0;o<a;o++)n=t[o]-u+e,e=Math.floor(n/u),s[o]=n-e*u,e+=1;for(;e>0;)s[o++]=e%u,e=Math.floor(e/u);return s}function w(t,r){var e,n,o=t.length,a=r.length,s=new Array(o),u=0;for(e=0;e<a;e++)(n=t[e]-u-r[e])<0?(n+=1e7,u=1):u=0,s[e]=n;for(e=a;e<o;e++){if(!((n=t[e]-u)<0)){s[e++]=n;break}n+=1e7,s[e]=n}for(;e<o;e++)s[e]=t[e];return b(s),s}function L(t,r,e){var n,o,a=t.length,s=new Array(a),c=-r;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,s[n]=o<0?o+1e7:o;return"number"==typeof(s=p(s))?(e&&(s=-s),new i(s)):new u(s,e)}function O(t,r){var e,n,o,a,s=t.length,u=r.length,i=v(s+u);for(o=0;o<s;++o){a=t[o];for(var c=0;c<u;++c)e=a*r[c]+i[o+c],n=Math.floor(e/1e7),i[o+c]=e-1e7*n,i[o+c+1]+=n}return b(i),i}function j(t,e){var n,o,a=t.length,s=new Array(a),u=r,i=0;for(o=0;o<a;o++)n=t[o]*e+i,i=Math.floor(n/u),s[o]=n-i*u;for(;i>0;)s[o++]=i%u,i=Math.floor(i/u);return s}function C(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function g(t,e,n){return new u(t<r?j(e,t):O(e,f(t)),n)}function E(t){var r,e,n,o,a=t.length,s=v(a+a);for(n=0;n<a;n++){e=0-(o=t[n])*o;for(var u=n;u<a;u++)r=o*t[u]*2+s[n+u]+e,e=Math.floor(r/1e7),s[n+u]=r-1e7*e;s[n+a]=e}return b(s),s}function A(t,r){var e,n,o,a,s=t.length,u=v(s);for(o=0,e=s-1;e>=0;--e)o=(a=1e7*o+t[e])-(n=y(a/r))*r,u[e]=0|n;return[u,0|o]}function T(t,e){var n,o=B(e);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,h=o.value;if(0===h)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new i(y(d/h)),new i(d%h)]:[s[0],t];if(o.isSmall){if(1===h)return[t,s[0]];if(-1==h)return[t.negate(),s[0]];var m=Math.abs(h);if(m<r){l=p((n=A(d,m))[0]);var L=n[1];return t.sign&&(L=-L),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new i(l),new i(L)]):[new u(l,t.sign!==o.sign),new i(L)]}h=f(m)}var O=S(d,h);if(-1===O)return[s[0],t];if(0===O)return[s[t.sign===o.sign?1:-1],s[0]];l=(n=d.length+h.length<=200?function(t,e){var n,o,a,s,u,i,c,l=t.length,f=e.length,b=r,y=v(e.length),d=e[f-1],h=Math.ceil(b/(2*d)),m=j(t,h),w=j(e,h);for(m.length<=l&&m.push(0),w.push(0),d=w[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==d&&(n=Math.floor((m[o+f]*b+m[o+f-1])/d)),a=0,s=0,i=w.length,u=0;u<i;u++)a+=n*w[u],c=Math.floor(a/b),s+=m[o+u]-(a-c*b),a=c,s<0?(m[o+u]=s+b,s=-1):(m[o+u]=s,s=0);for(;0!==s;){for(n-=1,a=0,u=0;u<i;u++)(a+=m[o+u]-b+w[u])<0?(m[o+u]=a+b,a=0):(m[o+u]=a,a=1);s+=a}y[o]=n}return m=A(m,h)[0],[p(y),p(m)]}(d,h):function(t,r){for(var e,n,o,a,s,u=t.length,i=r.length,c=[],l=[];u;)if(l.unshift(t[--u]),b(l),S(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*r[i-1]+r[i-2],n>i&&(o=1e7*(o+1)),e=Math.ceil(o/a);do{if(S(s=j(r,e),l)<=0)break;e--}while(e);c.push(e),l=w(l,s)}return c.reverse(),[p(c),p(l)]}(d,h))[0];var C=t.sign!==o.sign,g=n[1],E=t.sign;return"number"==typeof l?(C&&(l=-l),l=new i(l)):l=new u(l,C),"number"==typeof g?(E&&(g=-g),g=new i(g)):g=new u(g,E),[l,g]}function S(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function U(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function _(t,r){for(var e,n,a,s=t.prev(),u=s,i=0;u.isEven();)u=u.divide(2),i++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(a=o(r[n]).modPow(u,t)).isUnit()&&!a.equals(s)){for(e=i-1;0!=e;e--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(s))continue t}return!1}return!0}u.prototype=Object.create(s.prototype),i.prototype=Object.create(s.prototype),c.prototype=Object.create(s.prototype),u.prototype.add=function(t){var r=B(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new u(m(e,Math.abs(n)),this.sign):new u(h(e,n),this.sign)},u.prototype.plus=u.prototype.add,i.prototype.add=function(t){var r=B(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new i(e+n);n=f(Math.abs(n))}return new u(m(n,Math.abs(e)),e<0)},i.prototype.plus=i.prototype.add,c.prototype.add=function(t){return new c(this.value+B(t).value)},c.prototype.plus=c.prototype.add,u.prototype.subtract=function(t){var r=B(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?L(e,Math.abs(n),this.sign):function(t,r,e){var n;return S(t,r)>=0?n=w(t,r):(n=w(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new i(n)):new u(n,e)}(e,n,this.sign)},u.prototype.minus=u.prototype.subtract,i.prototype.subtract=function(t){var r=B(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new i(e-n):L(n,Math.abs(e),e>=0)},i.prototype.minus=i.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-B(t).value)},c.prototype.minus=c.prototype.subtract,u.prototype.negate=function(){return new u(this.value,!this.sign)},i.prototype.negate=function(){var t=this.sign,r=new i(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},u.prototype.abs=function(){return new u(this.value,!1)},i.prototype.abs=function(){return new i(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},u.prototype.multiply=function(t){var e,n,o,a=B(t),i=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return s[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new u(j(i,e),l);c=f(e)}return n=i.length,o=c.length,new u(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return O(r,e);n=Math.ceil(n/2);var o=r.slice(n),a=r.slice(0,n),s=e.slice(n),u=e.slice(0,n),i=t(a,u),c=t(o,s),l=t(h(a,o),h(u,s)),f=h(h(i,C(w(w(l,i),c),n)),C(c,2*n));return b(f),f}(i,c):O(i,c),l)},u.prototype.times=u.prototype.multiply,i.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new i(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},u.prototype._multiplyBySmall=function(t){return 0===t.value?s[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},i.prototype.multiply=function(t){return B(t)._multiplyBySmall(this)},i.prototype.times=i.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*B(t).value)},c.prototype.times=c.prototype.multiply,u.prototype.square=function(){return new u(E(this.value),!1)},i.prototype.square=function(){var t=this.value*this.value;return l(t)?new i(t):new u(E(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},u.prototype.divmod=function(t){var r=T(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=i.prototype.divmod=u.prototype.divmod,u.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/B(t).value)},i.prototype.over=i.prototype.divide=u.prototype.over=u.prototype.divide,u.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%B(t).value)},i.prototype.remainder=i.prototype.mod=u.prototype.remainder=u.prototype.mod,u.prototype.pow=function(t){var r,e,n,o=B(t),a=this.value,u=o.value;if(0===u)return s[1];if(0===a)return s[0];if(1===a)return s[1];if(-1===a)return o.isEven()?s[1]:s[-1];if(o.sign)return s[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(a,u)))return new i(y(r));for(e=this,n=s[1];!0&u&&(n=n.times(e),--u),0!==u;)u/=2,e=e.square();return n},i.prototype.pow=u.prototype.pow,c.prototype.pow=function(t){var r=B(t),e=this.value,n=r.value,o=BigInt(0),a=BigInt(1),u=BigInt(2);if(n===o)return s[1];if(e===o)return s[0];if(e===a)return s[1];if(e===BigInt(-1))return r.isEven()?s[1]:s[-1];if(r.isNegative())return new c(o);for(var i=this,l=s[1];(n&a)===a&&(l=l.times(i),--n),n!==o;)n/=u,i=i.square();return l},u.prototype.modPow=function(t,r){if(t=B(t),(r=B(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=s[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(s[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return s[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=i.prototype.modPow=u.prototype.modPow,u.prototype.compareAbs=function(t){var r=B(t),e=this.value,n=r.value;return r.isSmall?1:S(e,n)},i.prototype.compareAbs=function(t){var r=B(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=B(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},u.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=B(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:S(e,n)*(this.sign?-1:1)},u.prototype.compareTo=u.prototype.compare,i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=B(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},i.prototype.compareTo=i.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=B(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,u.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=i.prototype.eq=i.prototype.equals=u.prototype.eq=u.prototype.equals,u.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=i.prototype.neq=i.prototype.notEquals=u.prototype.neq=u.prototype.notEquals,u.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=i.prototype.gt=i.prototype.greater=u.prototype.gt=u.prototype.greater,u.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=i.prototype.lt=i.prototype.lesser=u.prototype.lt=u.prototype.lesser,u.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals=u.prototype.geq=u.prototype.greaterOrEquals,u.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals=u.prototype.leq=u.prototype.lesserOrEquals,u.prototype.isEven=function(){return 0==(1&this.value[0])},i.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},u.prototype.isOdd=function(){return 1==(1&this.value[0])},i.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},u.prototype.isPositive=function(){return!this.sign},i.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=i.prototype.isPositive,u.prototype.isNegative=function(){return this.sign},i.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=i.prototype.isNegative,u.prototype.isUnit=function(){return!1},i.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},u.prototype.isZero=function(){return!1},i.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},u.prototype.isDivisibleBy=function(t){var r=B(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=i.prototype.isDivisibleBy=u.prototype.isDivisibleBy,u.prototype.isPrime=function(t){var r=U(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return _(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),s=Math.ceil(!0===t?2*Math.pow(a,2):a),u=[],i=0;i<s;i++)u.push(o(i+2));return _(e,u)},c.prototype.isPrime=i.prototype.isPrime=u.prototype.isPrime,u.prototype.isProbablePrime=function(t,r){var e=U(this);if(void 0!==e)return e;for(var n=this.abs(),a=void 0===t?5:t,s=[],u=0;u<a;u++)s.push(o.randBetween(2,n.minus(2),r));return _(n,s)},c.prototype.isProbablePrime=i.prototype.isProbablePrime=u.prototype.isProbablePrime,u.prototype.modInv=function(t){for(var r,e,n,a=o.zero,s=o.one,u=B(t),i=this.abs();!i.isZero();)r=u.divide(i),e=a,n=u,a=s,u=i,s=e.subtract(r.multiply(s)),i=n.subtract(r.multiply(i));if(!u.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=i.prototype.modInv=u.prototype.modInv,u.prototype.next=function(){var t=this.value;return this.sign?L(t,1,this.sign):new u(m(t,1),this.sign)},i.prototype.next=function(){var t=this.value;return t+1<e?new i(t+1):new u(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},u.prototype.prev=function(){var t=this.value;return this.sign?new u(m(t,1),!0):L(t,1,this.sign)},i.prototype.prev=function(){var t=this.value;return t-1>-e?new i(t-1):new u(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=r;)x.push(2*x[x.length-1]);var I=x.length,N=x[I-1];function R(t){return Math.abs(t)<=r}function k(t,r,e){r=B(r);for(var n=t.isNegative(),a=r.isNegative(),s=n?t.not():t,u=a?r.not():r,i=0,c=0,l=null,f=null,p=[];!s.isZero()||!u.isZero();)i=(l=T(s,N))[1].toJSNumber(),n&&(i=N-1-i),c=(f=T(u,N))[1].toJSNumber(),a&&(c=N-1-c),s=l[0],u=f[0],p.push(e(i,c));for(var b=0!==e(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(N).add(o(p[v]));return b}u.prototype.shiftLeft=function(t){var r=B(t).toJSNumber();if(!R(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=I;)e=e.multiply(N),r-=I-1;return e.multiply(x[r])},c.prototype.shiftLeft=i.prototype.shiftLeft=u.prototype.shiftLeft,u.prototype.shiftRight=function(t){var r,e=B(t).toJSNumber();if(!R(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=I;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=T(n,N))[1].isNegative()?r[0].prev():r[0],e-=I-1}return(r=T(n,x[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=i.prototype.shiftRight=u.prototype.shiftRight,u.prototype.not=function(){return this.negate().prev()},c.prototype.not=i.prototype.not=u.prototype.not,u.prototype.and=function(t){return k(this,t,(function(t,r){return t&r}))},c.prototype.and=i.prototype.and=u.prototype.and,u.prototype.or=function(t){return k(this,t,(function(t,r){return t|r}))},c.prototype.or=i.prototype.or=u.prototype.or,u.prototype.xor=function(t){return k(this,t,(function(t,r){return t^r}))},c.prototype.xor=i.prototype.xor=u.prototype.xor;function V(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function M(t,r){return t=B(t),r=B(r),t.greater(r)?t:r}function q(t,r){return t=B(t),r=B(r),t.lesser(r)?t:r}function P(t,r){if(t=B(t).abs(),r=B(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=s[1];t.isEven()&&r.isEven();)e=q(V(t),V(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(V(t));do{for(;r.isEven();)r=r.divide(V(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}u.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),a=n.p,s=n.e,u=a.multiply(e);return u.compareTo(r)<=0?{p:u,e:2*s+1}:{p:a,e:2*s}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=i.prototype.bitLength=u.prototype.bitLength;var D=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,a=t.length,s=Math.abs(r),u={};for(o=0;o<e.length;o++)u[e[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in u&&u[l]>=s)){if("1"===l&&1===s)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=B(r);var i=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in u)i.push(B(u[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);i.push(B(t.slice(f+1,o)))}}return Q(i,r,c)};function Q(t,r,e){var n,o=s[0],a=s[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(r);return e?o.negate():o}function F(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,s=[],u=t;u.isNegative()||u.compareAbs(r)>=0;){a=u.divmod(r),u=a.quotient;var i=a.remainder;i.isNegative()&&(i=r.minus(i).abs(),u=u.next()),s.push(i.toJSNumber())}return s.push(u.toJSNumber()),{value:s.reverse(),isNegative:n}}function G(t,r,e){var n=F(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function $(t){if(l(+t)){var r=+t;if(r===y(r))return a?new c(BigInt(r)):new i(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==y(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var s=n[0],f=s.indexOf(".");if(f>=0&&(o-=s.length-f-1,s=s.slice(0,f)+s.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=s+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new u(p,e)}function B(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==y(t))throw new Error(t+" is not an integer.");return new i(t)}return $(t.toString())}(t):"string"==typeof t?$(t):"bigint"==typeof t?new c(t):t}u.prototype.toArray=function(t){return F(this,t)},i.prototype.toArray=function(t){return F(this,t)},c.prototype.toArray=function(t){return F(this,t)},u.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return G(this,t,r);for(var e,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)e=String(n[o]),a+="0000000".slice(e.length)+e;return(this.sign?"-":"")+a},i.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?G(this,t,r):String(this.value)},c.prototype.toString=i.prototype.toString,c.prototype.toJSON=u.prototype.toJSON=i.prototype.toJSON=function(){return this.toString()},u.prototype.valueOf=function(){return parseInt(this.toString(),10)},u.prototype.toJSNumber=u.prototype.valueOf,i.prototype.valueOf=function(){return this.value},i.prototype.toJSNumber=i.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var H=0;H<1e3;H++)s[H]=B(H),H>0&&(s[-H]=B(-H));return s.one=s[1],s.zero=s[0],s.minusOne=s[-1],s.max=M,s.min=q,s.gcd=P,s.lcm=function(t,r){return t=B(t).abs(),r=B(r).abs(),t.divide(P(t,r)).multiply(r)},s.isInstance=function(t){return t instanceof u||t instanceof i||t instanceof c},s.randBetween=function(t,e,n){t=B(t),e=B(e);var o=n||Math.random,a=q(t,e),u=M(t,e).subtract(a).add(1);if(u.isSmall)return a.add(Math.floor(o()*u));for(var i=F(u,r).value,c=[],l=!0,f=0;f<i.length;f++){var p=l?i[f]+(f+1<i.length?i[f+1]/r:0):r,b=y(o()*p);c.push(b),b<i[f]&&(l=!1)}return a.add(s.fromArray(c,r,!1))},s.fromArray=function(t,r,e){return Q(t.map(B),B(r||10),e)},s}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(30)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=noql.umd.js.map