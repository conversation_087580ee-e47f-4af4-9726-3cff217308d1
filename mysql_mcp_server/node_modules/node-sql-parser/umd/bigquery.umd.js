!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var r=e();for(var n in r)("object"==typeof exports?exports:t)[n]=r[n]}}(this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=26)}([function(t,e,r){"use strict";r.r(e),r.d(e,"arrayStructTypeToSQL",(function(){return S})),r.d(e,"autoIncrementToSQL",(function(){return _})),r.d(e,"columnOrderListToSQL",(function(){return x})),r.d(e,"commonKeywordArgsToSQL",(function(){return U})),r.d(e,"commonOptionConnector",(function(){return s})),r.d(e,"connector",(function(){return c})),r.d(e,"commonTypeValue",(function(){return w})),r.d(e,"commentToSQL",(function(){return A})),r.d(e,"createBinaryExpr",(function(){return f})),r.d(e,"createValueExpr",(function(){return l})),r.d(e,"dataTypeToSQL",(function(){return g})),r.d(e,"DEFAULT_OPT",(function(){return u})),r.d(e,"escape",(function(){return p})),r.d(e,"literalToSQL",(function(){return O})),r.d(e,"columnIdentifierToSql",(function(){return y})),r.d(e,"getParserOpt",(function(){return b})),r.d(e,"identifierToSql",(function(){return d})),r.d(e,"onPartitionsToSQL",(function(){return C})),r.d(e,"replaceParams",(function(){return L})),r.d(e,"returningToSQL",(function(){return E})),r.d(e,"hasVal",(function(){return j})),r.d(e,"setParserOpt",(function(){return v})),r.d(e,"toUpper",(function(){return m})),r.d(e,"topToSQL",(function(){return h})),r.d(e,"triggerEventToSQL",(function(){return T}));var n=r(2),o=r(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u={database:"bigquery",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},i=u;function s(t,e,r){if(r)return t?"".concat(t.toUpperCase()," ").concat(e(r)):e(r)}function c(t,e){if(e)return"".concat(t.toUpperCase()," ").concat(e)}function l(t){var e=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(e){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(e,'" to SQL'))}}function f(t,e,r){var n={operator:t,type:"binary_expr"};return n.left=e.type?e:l(e),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(r[0]),l(r[1])]},n):(n.right=r.type?r:l(r),n)}function p(t){return t}function b(){return i}function v(t){i=t}function h(t){if(t){var e=t.value,r=t.percent,n=t.parentheses?"(".concat(e,")"):e,o="TOP ".concat(n);return r?"".concat(o," ").concat(r.toUpperCase()):o}}function y(t){var e=b().database;if(t)switch(e&&e.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function d(t,e){var r=b().database;if(!0===e)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(r&&r.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function j(t){return t}function O(t){if(t){var e=t.prefix,r=t.type,n=t.parentheses,u=t.suffix,i=t.value,s="object"===a(t)?i:t;switch(r){case"backticks_quote_string":s="`".concat(i,"`");break;case"string":s="'".concat(i,"'");break;case"regex_string":s='r"'.concat(i,'"');break;case"hex_string":s="X'".concat(i,"'");break;case"full_hex_string":s="0x".concat(i);break;case"natural_string":s="N'".concat(i,"'");break;case"bit_string":s="b'".concat(i,"'");break;case"double_quote_string":s='"'.concat(i,'"');break;case"single_quote_string":s="'".concat(i,"'");break;case"boolean":case"bool":s=i?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(e||":").concat(i),e=null;break;case"origin":s=i.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(r.toUpperCase()," '").concat(i,"'");break;case"var_string":s="N'".concat(i,"'");break;case"unicode_string":s="U&'".concat(i,"'")}var c=[];return e&&c.push(m(e)),c.push(s),u&&("string"==typeof u&&c.push(u),"object"===a(u)&&(u.collate?c.push(Object(o.a)(u.collate)):c.push(O(u)))),s=c.join(" "),n?"(".concat(s,")"):s}}function w(t){if(!t)return[];var e=t.type,r=t.symbol,n=t.value;return[e.toUpperCase(),r,"string"==typeof n?n.toUpperCase():O(n)].filter(j)}function L(t,e){return function t(e,r){return Object.keys(e).filter((function(t){var r=e[t];return Array.isArray(r)||"object"===a(r)&&null!==r})).forEach((function(n){var o=e[n];if("object"!==a(o)||"param"!==o.type)return t(o,r);if(void 0===r[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return e[n]=l(r[o.value]),null})),e}(JSON.parse(JSON.stringify(t)),e)}function C(t){var e=t.type,r=t.partitions;return[m(e),"(".concat(r.map((function(t){if("range"!==t.type)return O(t);var e=t.start,r=t.end,n=t.symbol;return"".concat(O(e)," ").concat(m(n)," ").concat(O(r))})).join(", "),")")].join(" ")}function g(t){var e=t.dataType,r=t.length,n=t.parentheses,o=t.scale,a=t.suffix,u="";return null!=r&&(u=o?"".concat(r,", ").concat(o):r),n&&(u="(".concat(u,")")),a&&a.length&&(u+=" ".concat(a.join(" "))),"".concat(e).concat(u)}function S(t){if(t){var e=t.dataType,r=t.definition,n=t.anglebracket,o=m(e);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=r&&r.map((function(t){return[t.field_name,S(t.field_type)].filter(j).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function A(t){if(t){var e=[],r=t.keyword,n=t.symbol,o=t.value;return e.push(r.toUpperCase()),n&&e.push(n),e.push(O(o)),e.join(" ")}}function T(t){return t.map((function(t){var e=t.keyword,r=t.args,o=[m(e)];if(r){var a=r.keyword,u=r.columns;o.push(m(a),u.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function E(t){return t?["RETURNING",t.columns.map(n.h).filter(j).join(", ")].join(" "):""}function U(t){return t?[m(t.keyword),m(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var e=b().database;switch(e&&e.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var r=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,u=m(r);return a&&(u+="(".concat(O(n),", ").concat(O(o),")")),u}}function x(t){if(t)return t.map(n.e).filter(j).join(", ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return C})),r.d(e,"b",(function(){return g})),r.d(e,"d",(function(){return L})),r.d(e,"c",(function(){return S}));var n=r(0),o=r(9),a=r(13);var u=r(22),i=r(21);var s=r(11),c=r(2),l=r(6),f=r(18);var p=r(7),b=r(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t){var e=t.expr_list,r=t.type;switch(Object(n.toUpper)(r)){case"STRUCT":return"(".concat(Object(c.i)(e),")");case"ARRAY":return function(t){var e=t.array_path,r=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(e),"]");var u=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return r?"[".concat(u,"]"):a?"(".concat(u,")"):u}(t);default:return""}}function y(t){var e=t.definition,r=t.keyword,o=[Object(n.toUpper)(r)];return e&&"object"===v(e)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(e))),o.push(h(t)),o.filter(n.hasVal).join("")}var d=r(3),m=r(5),j=r(20);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={alter:o.b,aggr_func:function(t){var e=t.args,r=t.filter,o=t.over,u=t.within_group_orderby,i=C(e.expr);i=Array.isArray(i)?i.join(", "):i;var s=t.name,c=Object(a.a)(o);e.distinct&&(i=["DISTINCT",i].join(" ")),e.separator&&e.separator.delimiter&&(i=[i,Object(n.literalToSQL)(e.separator.delimiter)].join("".concat(e.separator.symbol," "))),e.separator&&e.separator.expr&&(i=[i,C(e.separator.expr)].join(" ")),e.orderby&&(i=[i,S(e.orderby,"order by")].join(" ")),e.separator&&e.separator.value&&(i=[i,Object(n.toUpper)(e.separator.keyword),Object(n.literalToSQL)(e.separator.value)].filter(n.hasVal).join(" "));var l=u?"WITHIN GROUP (".concat(S(u,"order by"),")"):"",f=r?"FILTER (WHERE ".concat(C(r.where),")"):"";return["".concat(s,"(").concat(i,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:j.c,array:y,assign:u.a,binary_expr:i.a,case:function(t){var e=["CASE"],r=t.args,n=t.expr,o=t.parentheses;n&&e.push(C(n));for(var a=0,u=r.length;a<u;++a)e.push(r[a].type.toUpperCase()),r[a].cond&&(e.push(C(r[a].cond)),e.push("THEN")),e.push(C(r[a].result));return e.push("END"),o?"(".concat(e.join(" "),")"):e.join(" ")},cast:l.c,collate:s.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var e=t.keyword,r=t.expr_list;return[Object(n.toUpper)(e),r.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:y,tablefunc:l.j,tables:d.c,unnest:d.d,window:j.b};function L(t){var e=t.prefix,r=void 0===e?"@":e,o=t.name,a=t.members,u=t.quoted,i=t.suffix,s=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(r||"").concat(c);return i&&(l+=i),s.push(l),[u,s.join(" "),u].filter(n.hasVal).join("")}function C(t){if(t){var e=t;if(t.ast){var r=e.ast;Reflect.deleteProperty(e,r);for(var o=0,a=Object.keys(r);o<a.length;o++){var u=a[o];e[u]=r[u]}}var i=e.type;return"expr"===i?C(e.expr):w[i]?w[i](e):Object(n.literalToSQL)(e)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function S(t,e){if(!Array.isArray(t))return"";var r=[],o=Object(n.toUpper)(e);switch(o){case"ORDER BY":r=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:r=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,r.join(", "))}w.var=L,w.expr_list=function(t){var e=g(t.value),r=t.parentheses,n=t.separator;if(!r&&!n)return e;var o=n||", ",a=e.join(o);return r?"(".concat(a,")"):a},w.select=function(t){var e="object"===O(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(e,")"):e},w.unary_expr=function(t){var e=t.operator,r=t.parentheses,n=t.expr,o="-"===e||"+"===e||"~"===e||"!"===e?"":" ",a="".concat(e).concat(o).concat(C(n));return r?"(".concat(a,")"):a},w.map_object=function(t){var e=t.keyword,r=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(e),"[".concat(r,"]")].join("")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"c",(function(){return O})),r.d(e,"f",(function(){return h})),r.d(e,"h",(function(){return C})),r.d(e,"i",(function(){return S})),r.d(e,"b",(function(){return y})),r.d(e,"d",(function(){return b})),r.d(e,"e",(function(){return j})),r.d(e,"g",(function(){return d})),r.d(e,"j",(function(){return L})),r.d(e,"k",(function(){return g}));var n=r(11),o=r(19),a=r(1),u=r(6),i=r(3),s=r(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){if("string"==typeof t)return Object(s.identifierToSql)(t,e);var r=t.expr,n=t.offset,o=t.suffix,u=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(s.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(s.hasVal).join("")})).join("");return[Object(a.a)(r),u,o].filter(s.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var e,r=[],n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=f(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==r.return||r.return()}finally{if(i)throw a}}}}(t);try{for(n.s();!(e=n.n()).done;){var o=e.value,a=o.brackets?"[".concat(Object(s.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(s.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(s.literalToSQL)(o.property))),r.push(a)}}catch(t){n.e(t)}finally{n.f()}return r.join("")}function h(t){var e=t.array_index,r=t.as,o=t.column,u=t.collate,i=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,h=t.options,y=t.schema,d=t.table,m=t.parentheses,j=t.suffix,O=t.order_by,w=t.subFields,L=void 0===w?[]:w,C="*"===o?"*":b(o,c),g=[i,y,d].filter(s.hasVal).map((function(t){return"".concat("string"==typeof t?Object(s.identifierToSql)(t):Object(a.a)(t))})),S=g[0];if(S){for(var A=1;A<g.length;++A)S="".concat(S).concat(p[A]||".").concat(g[A]);C="".concat(S).concat(p[A]||".").concat(C)}var T=[C=["".concat(C).concat(v(e))].concat(l(L)).join("."),Object(n.a)(u),Object(a.a)(h),Object(s.commonOptionConnector)("AS",a.a,r)];T.push("string"==typeof j?Object(s.toUpper)(j):Object(a.a)(j)),T.push(Object(s.toUpper)(O));var E=T.filter(s.hasVal).join(" ");return m?"(".concat(E,")"):E}function y(t){if(t){var e=t.dataType,r=t.length,n=t.suffix,o=t.scale,i=t.expr,c=null!=r,l=Object(s.dataTypeToSQL)({dataType:e,length:r,suffix:n,scale:o,parentheses:c});if(i&&(l+=Object(a.a)(i)),t.array){var f=Object(u.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function d(t){var e=[];if(!t)return e;var r=t.definition,n=t.keyword,o=t.match,u=t.table,c=t.on_action;return e.push(Object(s.toUpper)(n)),e.push(Object(i.c)(u)),e.push(r&&"(".concat(r.map((function(t){return Object(a.a)(t)})).join(", "),")")),e.push(Object(s.toUpper)(o)),c.map((function(t){return e.push(Object(s.toUpper)(t.type),Object(a.a)(t.value))})),e.filter(s.hasVal)}function m(t){var e=[],r=t.nullable,n=t.character_set,u=t.check,i=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,h=t.generated,y=t.auto_increment,m=t.unique,j=t.primary_key,O=t.column_format,w=t.reference_definition,L=[Object(s.toUpper)(r&&r.action),Object(s.toUpper)(r&&r.value)].filter(s.hasVal).join(" ");if(h||e.push(L),v){var C=v.type,g=v.value;e.push(C.toUpperCase(),Object(a.a)(g))}var S=Object(s.getParserOpt)().database;return c&&e.push(Object(s.toUpper)(c.keyword),Object(s.literalToSQL)(c.constraint)),e.push(Object(o.a)(u)),e.push(function(t){if(t)return[Object(s.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(s.toUpper)(t.storage_type)].filter(s.hasVal).join(" ")}(h)),h&&e.push(L),e.push(Object(s.autoIncrementToSQL)(y),Object(s.toUpper)(j),Object(s.toUpper)(m),Object(s.commentToSQL)(i)),e.push.apply(e,l(Object(s.commonTypeValue)(n))),"sqlite"!==S.toLowerCase()&&e.push(Object(a.a)(f)),e.push.apply(e,l(Object(s.commonTypeValue)(O))),e.push.apply(e,l(Object(s.commonTypeValue)(p))),e.push.apply(e,l(d(w))),e.push(Object(s.commonOptionConnector)("USING",a.a,b)),e.filter(s.hasVal).join(" ")}function j(t){var e=t.column,r=t.collate,n=t.nulls,o=t.opclass,u=t.order_by,i="string"==typeof e?{type:"column_ref",table:t.table,column:e}:t;return i.collate=null,[Object(a.a)(i),Object(a.a)(r),o,Object(s.toUpper)(u),Object(s.toUpper)(n)].filter(s.hasVal).join(" ")}function O(t){var e=[],r=h(t.column),n=y(t.definition);return e.push(r),e.push(n),e.push(m(t)),e.filter(s.hasVal).join(" ")}function w(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(s.identifierToSql)(t):Object(s.columnIdentifierToSql)(t)].join(" "):""}function L(t){var e=t.against,r=t.as,n=t.columns,o=t.match,u=t.mode;return[[Object(s.toUpper)(o),"(".concat(n.map((function(t){return h(t)})).join(", "),")")].join(" "),[Object(s.toUpper)(e),["(",Object(a.a)(t.expr),u&&" ".concat(Object(s.literalToSQL)(u)),")"].filter(s.hasVal).join("")].join(" "),w(r)].filter(s.hasVal).join(" ")}function C(t,e){var r=t.expr,n=t.type;if("cast"===n)return Object(u.c)(t);e&&(r.isDual=e);var o=Object(a.a)(r),i=t.expr_list;if(i){var c=[o],l=i.map((function(t){return C(t,e)})).join(", ");return c.push([Object(s.toUpper)(n),n&&"(",l,n&&")"].filter(s.hasVal).join("")),c.filter(s.hasVal).join(" ")}return r.parentheses&&Reflect.has(r,"array_index")&&"cast"!==r.type&&(o="(".concat(o,")")),r.array_index&&"column_ref"!==r.type&&(o="".concat(o).concat(v(r.array_index))),[o,w(t.as)].filter(s.hasVal).join(" ")}function g(t){var e=Array.isArray(t)&&t[0];return!(!e||"dual"!==e.type)}function S(t,e){if(!t||"*"===t)return t;var r=g(e);return t.map((function(t){return C(t,r)})).join(", ")}},function(t,e,r){"use strict";r.d(e,"c",(function(){return d})),r.d(e,"a",(function(){return m})),r.d(e,"b",(function(){return y})),r.d(e,"d",(function(){return f}));var n=r(21),o=r(2),a=r(1),u=r(17),i=r(18),s=r(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){var e=t.type,r=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(s.toUpper)(e),"(").concat(n&&Object(a.a)(n)||"",")"),Object(s.commonOptionConnector)("AS","string"==typeof r?s.identifierToSql:a.a,r),Object(s.commonOptionConnector)(Object(s.toUpper)(o&&o.keyword),s.identifierToSql,o&&o.as)].filter(s.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var e=t.as,r=t.column,u=t.expr,i=t.in_expr,c=t.type,l=[Object(a.a)(u),"FOR",Object(o.f)(r),Object(n.a)(i)],f=["".concat(Object(s.toUpper)(c),"(").concat(l.join(" "),")")];return e&&f.push("AS",Object(s.identifierToSql)(e)),f.join(" ")}(t);default:return""}}function b(t){if(t){var e=t.keyword,r=t.expr,n=t.index,o=t.index_columns,u=t.parentheses,i=t.prefix,c=[];switch(e.toLowerCase()){case"forceseek":c.push(Object(s.toUpper)(e),"(".concat(Object(s.identifierToSql)(n)),"(".concat(o.map(a.a).filter(s.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(s.toUpper)(e),"=",Object(a.a)(r));break;case"index":c.push(Object(s.toUpper)(i),Object(s.toUpper)(e),u?"(".concat(r.map(s.identifierToSql).join(", "),")"):"= ".concat(Object(s.identifierToSql)(r)));break;default:c.push(Object(a.a)(r))}return c.filter(s.hasVal).join(" ")}}function v(t,e){var r=t.name,n=t.symbol;return[Object(s.toUpper)(r),n,e].filter(s.hasVal).join(" ")}function h(t){var e=[];switch(t.keyword){case"as":e.push("AS","OF",Object(a.a)(t.of));break;case"from_to":e.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":e.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":e.push("CONTAINED","IN",Object(a.a)(t.in))}return e.filter(s.hasVal).join(" ")}function y(t){if("UNNEST"===Object(s.toUpper)(t.type))return f(t);var e,r,n,c,l=t.table,y=t.db,d=t.as,m=t.expr,j=t.operator,O=t.prefix,w=t.schema,L=t.server,C=t.suffix,g=t.tablesample,S=t.temporal_table,A=t.table_hint,T=Object(s.identifierToSql)(L),E=Object(s.identifierToSql)(y),U=Object(s.identifierToSql)(w),_=l&&Object(s.identifierToSql)(l);if(m)switch(m.type){case"values":var x=m.parentheses,I=m.values,N=m.prefix,k=[x&&"(","",x&&")"],R=Object(u.b)(I);N&&(R=R.split("(").slice(1).map((function(t){return"".concat(Object(s.toUpper)(N),"(").concat(t)})).join("")),k[1]="VALUES ".concat(R),_=k.filter(s.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var e=t.data,r=t.timecol,n=t.offset,a=t.size,u=[Object(s.identifierToSql)(e.expr.db),Object(s.identifierToSql)(e.expr.schema),Object(s.identifierToSql)(e.expr.table)].filter(s.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(r.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(e,u)),v(r,c)],f=v(a,Object(i.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(i.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(s.hasVal).join(", ")}(m);break;case"generator":r=(e=m).keyword,n=e.type,c=e.generators.map((function(t){return Object(s.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(s.toUpper)(r),"(").concat(Object(s.toUpper)(n),"(").concat(c,"))");break;default:_=Object(a.a)(m)}var V=[[T,E,U,_=[Object(s.toUpper)(O),_,Object(s.toUpper)(C)].filter(s.hasVal).join(" ")].filter(s.hasVal).join(".")];if(g){var M=["TABLESAMPLE",Object(a.a)(g.expr),Object(s.literalToSQL)(g.repeatable)].filter(s.hasVal).join(" ");V.push(M)}V.push(function(t){if(t){var e=t.keyword,r=t.expr;return[Object(s.toUpper)(e),h(r)].filter(s.hasVal).join(" ")}}(S),Object(s.commonOptionConnector)("AS","string"==typeof d?s.identifierToSql:a.a,d),p(j)),A&&V.push(Object(s.toUpper)(A.keyword),"(".concat(A.expr.map(b).filter(s.hasVal).join(", "),")"));var q=V.filter(s.hasVal).join(" ");return t.parentheses?"(".concat(q,")"):q}function d(t){if(!t)return"";if(!Array.isArray(t)){var e=t.expr,r=t.parentheses,n=t.joins,o=d(e);if(r){for(var u=[],i=[],l=!0===r?1:r.length,f=0;f++<l;)u.push("("),i.push(")");var p=n&&n.length>0?d([""].concat(c(n))):"";return u.join("")+o+i.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(y(b));for(var h=1;h<t.length;++h){var m=t[h],j=m.on,O=m.using,w=m.join,L=[];L.push(w?" ".concat(Object(s.toUpper)(w)):","),L.push(y(m)),L.push(Object(s.commonOptionConnector)("ON",a.a,j)),O&&L.push("USING (".concat(O.map(s.literalToSQL).join(", "),")")),v.push(L.filter(s.hasVal).join(" "))}return v.filter(s.hasVal).join("")}function m(t){var e=t.keyword,r=t.symbol,n=t.value,o=[e.toUpperCase()];r&&o.push(r);var u=Object(s.literalToSQL)(n);switch(e){case"partition by":case"default collate":u=Object(a.a)(n);break;case"options":u="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(a.a).join(", ")}return o.push(u),o.filter(s.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return h})),r.d(e,"c",(function(){return g})),r.d(e,"d",(function(){return S})),r.d(e,"e",(function(){return y})),r.d(e,"f",(function(){return d})),r.d(e,"g",(function(){return m})),r.d(e,"h",(function(){return E})),r.d(e,"i",(function(){return T})),r.d(e,"j",(function(){return A})),r.d(e,"l",(function(){return j})),r.d(e,"m",(function(){return O})),r.d(e,"o",(function(){return w})),r.d(e,"n",(function(){return L})),r.d(e,"k",(function(){return C}));var n=r(2),o=r(14),a=r(0),u=r(1),i=r(3),s=r(16),c=r(5);function l(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=p(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==r.return||r.return()}finally{if(i)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t){var e=Object(u.a)(t.expr);return"".concat("CALL"," ").concat(e)}function h(t){var e=t.type,r=t.keyword,o=t.name,s=t.prefix,c=t.suffix,l=[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(a.toUpper)(s)];switch(r){case"table":l.push(Object(i.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(i.c)(o),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(i.b)(t.table)]:[]),[t.options&&t.options.map(u.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(u.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function y(t){var e=t.type,r=t.table,n=Object(a.toUpper)(e);return"".concat(n," ").concat(Object(a.identifierToSql)(r))}function d(t){var e=t.type,r=t.name,n=t.args,o=[Object(a.toUpper)(e)],i=[r];return n&&i.push("(".concat(Object(u.a)(n).join(", "),")")),o.push(i.join("")),o.filter(a.hasVal).join(" ")}function m(t){var e=t.type,r=t.label,n=t.target,o=t.query,u=t.stmts;return[r,Object(a.toUpper)(e),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(u),"END LOOP",r].filter(a.hasVal).join(" ")}function j(t){var e=t.type,r=t.level,n=t.raise,o=t.using,i=[Object(a.toUpper)(e),Object(a.toUpper)(r)];return n&&i.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(u.a)(t)})).join(", ")),o&&i.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(u.a)(t)})).join(", ")),i.filter(a.hasVal).join(" ")}function O(t){var e=t.type,r=t.table,n=[],o="".concat(e&&e.toUpperCase()," TABLE");if(r){var a,u=l(r);try{for(u.s();!(a=u.n()).done;){var s=a.value.map(i.b);n.push(s.join(" TO "))}}catch(t){u.e(t)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))}function w(t){var e=t.type,r=t.db,n=Object(a.toUpper)(e),o=Object(a.identifierToSql)(r);return"".concat(n," ").concat(o)}function L(t){var e=t.type,r=t.expr,n=t.keyword,o=Object(a.toUpper)(e),i=r.map(u.a).join(", ");return[o,Object(a.toUpper)(n),i].filter(a.hasVal).join(" ")}function C(t){var e=t.type,r=t.keyword,n=t.tables,o=[e.toUpperCase(),Object(a.toUpper)(r)];if("UNLOCK"===e.toUpperCase())return o.join(" ");var u,s=[],c=l(n);try{var p=function(){var t=u.value,e=t.table,r=t.lock_type,n=[Object(i.b)(e)];if(r){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(r[t])})).filter(a.hasVal).join(" "))}s.push(n.join(" "))};for(c.s();!(u=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[s.join(", ")].concat(f(function(t){var e=t.lock_mode,r=t.nowait,n=[];if(e){var o=e.mode;n.push(o.toUpperCase())}return r&&n.push(r.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(a.toUpper)(e),Object(a.toUpper)(r),Object(u.a)(n)].filter(a.hasVal).join(" ")}function S(t){var e=t.type,r=t.declare,i=t.symbol,s=[Object(a.toUpper)(e)],c=r.map((function(t){var e=t.at,r=t.name,i=t.as,s=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[e,r].filter(a.hasVal).join(""),Object(a.toUpper)(i),Object(a.toUpper)(s)];switch(b){case"variable":v.push(Object(n.b)(c),Object(u.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(u.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(i," "));return s.push(c),s.join(" ")}function A(t){var e=t.boolean_expr,r=t.else_expr,n=t.elseif_expr,o=t.if_expr,i=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(u.a)(e),Object(a.literalToSQL)(i),"".concat(Object(s.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(u.a)(t.boolean_expr),"THEN",Object(s.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),r&&b.push("ELSE","".concat(Object(s.a)(r.ast||r)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function T(t){var e=t.name,r=t.host,n=[Object(a.literalToSQL)(e)];return r&&n.push("@",Object(a.literalToSQL)(r)),n.join("")}function E(t){var e=t.type,r=t.grant_option_for,o=t.keyword,i=t.objects,s=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(e),Object(a.literalToSQL)(r)],b=i.map((function(t){var e=t.priv,r=t.columns,o=[Object(u.a)(e)];return r&&o.push("(".concat(r.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),s)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(s.object_type),s.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(s))}return p.push(Object(a.toUpper)(c),l.map(T).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"b",(function(){return w})),r.d(e,"a",(function(){return L}));var n=r(9),o=r(1),a=r(3),u=r(0);var i=r(14),s=r(2);function c(t){var e=t.name,r=t.type;switch(r){case"table":case"view":var n=[Object(u.identifierToSql)(e.db),Object(u.identifierToSql)(e.table)].filter(u.hasVal).join(".");return"".concat(Object(u.toUpper)(r)," ").concat(n);case"column":return"COLUMN ".concat(Object(s.f)(e));default:return"".concat(Object(u.toUpper)(r)," ").concat(Object(u.literalToSQL)(e))}}function l(t){var e=t.keyword,r=t.expr;return[Object(u.toUpper)(e),Object(u.literalToSQL)(r)].filter(u.hasVal).join(" ")}var f=r(7);var p=r(8),b=r(15);var v=r(12),h=r(17),y=r(4);function d(t){var e=t.name,r=t.value;return["@".concat(e),"=",Object(o.a)(r)].filter(u.hasVal).join(" ")}var m=r(22);var j=r(23),O={alter:n.c,analyze:function(t){var e=t.type,r=t.table;return[Object(u.toUpper)(e),Object(a.b)(r)].join(" ")},attach:function(t){var e=t.type,r=t.database,n=t.expr,a=t.as,i=t.schema;return[Object(u.toUpper)(e),Object(u.toUpper)(r),Object(o.a)(n),Object(u.toUpper)(a),Object(u.identifierToSql)(i)].filter(u.hasVal).join(" ")},create:i.b,comment:function(t){var e=t.expr,r=t.keyword,n=t.target,o=t.type;return[Object(u.toUpper)(o),Object(u.toUpper)(r),c(n),l(e)].filter(u.hasVal).join(" ")},select:f.a,deallocate:y.c,delete:function(t){var e=t.columns,r=t.from,n=t.table,i=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,h=[Object(b.a)(l),"DELETE"],y=Object(s.i)(e,r);return h.push(y),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||h.push(Object(a.c)(n))),h.push(Object(u.commonOptionConnector)("FROM",a.c,r)),h.push(Object(u.commonOptionConnector)("WHERE",o.a,i)),h.push(Object(o.c)(c,"order by")),h.push(Object(p.a)(f)),h.push(Object(u.returningToSQL)(v)),h.filter(u.hasVal).join(" ")},exec:function(t){var e=t.keyword,r=t.module,n=t.parameters;return[Object(u.toUpper)(e),Object(a.b)(r),(n||[]).map(d).filter(u.hasVal).join(", ")].filter(u.hasVal).join(" ")},execute:y.f,explain:function(t){var e=t.type,r=t.expr;return[Object(u.toUpper)(e),Object(f.a)(r)].join(" ")},for:y.g,update:v.b,if:y.j,insert:h.a,drop:y.b,truncate:y.b,replace:h.a,declare:y.d,use:y.o,rename:y.m,call:y.a,desc:y.e,set:y.n,lock:y.k,unlock:y.k,show:j.a,grant:y.h,revoke:y.h,proc:function(t){var e=t.stmt;switch(e.type){case"assign":return Object(m.a)(e);case"return":return function(t){var e=t.type,r=t.expr;return[Object(u.toUpper)(e),Object(o.a)(r)].join(" ")}(e)}},raise:y.l,transaction:function(t){var e=t.expr,r=e.action,n=e.keyword,o=e.modes,a=[Object(u.literalToSQL)(r),Object(u.toUpper)(n)];return o&&a.push(o.map(u.literalToSQL).join(", ")),a.filter(u.hasVal).join(" ")}};function w(t){if(!t)return"";for(var e=O[t.type],r=t,n=r._parentheses,a=r._orderby,i=r._limit,s=[n&&"(",e(t)];t._next;){var c=O[t._next.type],l=Object(u.toUpper)(t.set_op);s.push(l,c(t._next)),t=t._next}return s.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(i)),s.filter(u.hasVal).join(" ")}function L(t){for(var e=[],r=0,n=t.length;r<n;++r){var o=t[r]&&t[r].ast?t[r].ast:t[r],a=w(o);r===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),e.push(a)}return e.join(" ; ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"b",(function(){return s})),r.d(e,"c",(function(){return c})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return p})),r.d(e,"f",(function(){return b})),r.d(e,"g",(function(){return v})),r.d(e,"h",(function(){return f})),r.d(e,"i",(function(){return y})),r.d(e,"j",(function(){return h}));var n=r(2),o=r(1),a=r(0),u=r(13);function i(t){var e=t.args,r=t.type,n=t.over,i=e.expr,s=e.having,c="".concat(Object(a.toUpper)(r),"(").concat(Object(o.a)(i));return s&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(s.prefix)," ").concat(Object(o.a)(s.expr))),[c="".concat(c,")"),Object(u.a)(n)].filter(a.hasVal).join(" ")}function s(t){if(!t||!t.array)return"";var e=t.array.keyword;if(e)return Object(a.toUpper)(e);for(var r=t.array,n=r.dimension,o=r.length,u=[],i=0;i<n;i++)u.push("["),o&&o[i]&&u.push(Object(a.literalToSQL)(o[i])),u.push("]");return u.join("")}function c(t){for(var e=t.target,r=t.expr,u=t.keyword,i=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:r,offset:l}),b=[],v=0,h=e.length;v<h;++v){var y=e[v],d=y.angle_brackets,m=y.length,j=y.dataType,O=y.parentheses,w=y.quoted,L=y.scale,C=y.suffix,g=y.expr,S=g?Object(o.a)(g):"";null!=m&&(S=L?"".concat(m,", ").concat(L):m),O&&(S="(".concat(S,")")),d&&(S="<".concat(S,">")),C&&C.length&&(S+=" ".concat(C.map(a.literalToSQL).join(" ")));var A="::",T="",E=[];"as"===i&&(0===v&&(p="".concat(Object(a.toUpper)(u),"(").concat(p)),T=")",A=" ".concat(i.toUpperCase()," ")),0===v&&E.push(p);var U=s(y);E.push(A,w,j,w,U,S,T),b.push(E.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var _=b.filter(a.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var e=t.args,r=t.type,n=e.field,u=e.cast_type,i=e.source,s=["".concat(Object(a.toUpper)(r),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(u),Object(o.a)(i)];return"".concat(s.filter(a.hasVal).join(" "),")")}function f(t){var e=t.expr,r=e.key,n=e.value,u=e.on,i=[Object(o.a)(r),"VALUE",Object(o.a)(n)];return u&&i.push("ON","NULL",Object(o.a)(u)),i.filter(a.hasVal).join(" ")}function p(t){var e=t.args,r=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var e=t.type,r=t.symbol,n=t.value;return[Object(a.toUpper)(e),r,Object(o.a)(n)].filter(a.hasVal).join(" ")}(e[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(r),"(").concat(n,")")}function b(t){var e=t.value,r=e.name,n=e.symbol,u=e.expr;return[r,n,Object(o.a)(u)].filter(a.hasVal).join(" ")}function v(t){var e=t.args,r=t.array_index,i=t.name,s=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(u.a)(f),v=function(t){if(!t)return"";var e=t.type,r=t.keyword,n=t.orderby;return[Object(a.toUpper)(e),Object(a.toUpper)(r),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),h=Object(o.a)(p),y=[Object(a.literalToSQL)(i.schema),i.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!e)return[y,v,b].filter(a.hasVal).join(" ");var d=t.separator||", ";"TRIM"===Object(a.toUpper)(y)&&(d=" ");var m=[y];m.push(!1===s?" ":"(");var j=Object(o.a)(e);if(Array.isArray(d)){for(var O=j[0],w=1,L=j.length;w<L;++w)O=[O,j[w]].join(" ".concat(Object(o.a)(d[w-1])," "));m.push(O)}else m.push(j.join(d));return!1!==s&&m.push(")"),m.push(Object(n.a)(r)),m=[m.join(""),h].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function h(t){var e=t.as,r=t.name,n=t.args,u=[Object(a.literalToSQL)(r.schema),r.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(u,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(e)].join(" ")}function y(t){var e=t.args,r=t.expr,n=e.value,a=e.parentheses,u=n.map(o.a).join(", ");return[a?"(".concat(u,")"):u,"->",Object(o.a)(r)].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f}));var n=r(1),o=r(2),a=r(8),u=r(15),i=r(3),s=r(0),c=r(11);function l(t){if(t&&t.position){var e=t.keyword,r=t.expr,o=[],a=Object(s.toUpper)(e);switch(a){case"VAR":o.push(r.map(n.d).join(", "));break;default:o.push(a,"string"==typeof r?Object(s.identifierToSql)(r):Object(n.a)(r))}return o.filter(s.hasVal).join(" ")}}function f(t){var e=t.as_struct_val,r=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,h=t.for_sys_time_as_of,y=void 0===h?{}:h,d=t.locking_read,m=t.groupby,j=t.having,O=t.into,w=void 0===O?{}:O,L=t.isolation,C=t.limit,g=t.options,S=t.orderby,A=t.parentheses_symbol,T=t.qualify,E=t.top,U=t.window,_=t.with,x=t.where,I=[Object(u.a)(_),"SELECT",Object(s.toUpper)(e)];Array.isArray(g)&&I.push(g.join(" ")),I.push(function(t){if(t){if("string"==typeof t)return t;var e=t.type,r=t.columns,o=[Object(s.toUpper)(e)];return r&&o.push("(".concat(r.map(n.a).join(", "),")")),o.filter(s.hasVal).join(" ")}}(p),Object(s.topToSQL)(E),Object(o.i)(r,v));var N=w.position,k="";N&&(k=Object(s.commonOptionConnector)("INTO",l,w)),"column"===N&&I.push(k),I.push(Object(s.commonOptionConnector)("FROM",i.c,v)),"from"===N&&I.push(k);var R=y||{},V=R.keyword,M=R.expr;I.push(Object(s.commonOptionConnector)(V,n.a,M)),I.push(Object(s.commonOptionConnector)("WHERE",n.a,x)),m&&(I.push(Object(s.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),I.push(Object(n.b)(m.modifiers).join(", "))),I.push(Object(s.commonOptionConnector)("HAVING",n.a,j)),I.push(Object(s.commonOptionConnector)("QUALIFY",n.a,T)),I.push(Object(s.commonOptionConnector)("WINDOW",n.a,U)),I.push(Object(n.c)(S,"order by")),I.push(Object(c.a)(f)),I.push(Object(a.a)(C)),L&&I.push(Object(s.commonOptionConnector)(L.keyword,s.literalToSQL,L.expr)),I.push(Object(s.toUpper)(d)),"end"===N&&I.push(k),I.push(function(t){if(t){var e=t.expr,r=t.keyword,o=t.type,a=[Object(s.toUpper)(o),Object(s.toUpper)(r)];return e?"".concat(a.join(" "),"(").concat(Object(n.a)(e),")"):a.join(" ")}}(b));var q=I.filter(s.hasVal).join(" ");return A?"(".concat(q,")"):q}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function i(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function s(t){return t?t.fetch?(r=(e=t).fetch,u=e.offset,[].concat(a(i(u)),a(i(r))).filter(n.hasVal).join(" ")):function(t){var e=t.seperator,r=t.value;return 1===r.length&&"offset"===e?Object(n.connector)("OFFSET",Object(o.a)(r[0])):Object(n.connector)("LIMIT",r.map(o.a).join("".concat("offset"===e?" ":"").concat(Object(n.toUpper)(e)," ")))}(t):"";var e,r,u}},function(t,e,r){"use strict";r.d(e,"a",(function(){return p})),r.d(e,"c",(function(){return b})),r.d(e,"b",(function(){return f}));var n=r(2),o=r(14),a=r(10),u=r(3),i=r(1),s=r(7),c=r(0);function l(t,e){switch(t){case"add":var r=e.map((function(t){var e=t.name,r=t.value;return["PARTITION",Object(c.literalToSQL)(e),"VALUES",Object(c.toUpper)(r.type),"(".concat(Object(c.literalToSQL)(r.expr),")")].join(" ")})).join(", ");return"(".concat(r,")");default:return Object(n.i)(e)}}function f(t){if(!t)return"";var e=t.action,r=t.create_definitions,u=t.if_not_exists,i=t.keyword,s=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,h=t.suffix,y="",d=[];switch(b){case"column":d=[Object(n.c)(t)];break;case"index":d=Object(a.c)(t),y=t[b];break;case"table":case"schema":y=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":y=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":y=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":y=Object(c.identifierToSql)(t[b]),d=[Object(o.a)(r)];break;case"partition":d=[l(e,t.partitions)];break;case"key":y=Object(c.identifierToSql)(t[b]);break;default:y=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(e),Object(c.toUpper)(i),Object(c.toUpper)(u),Object(c.toUpper)(s),f&&Object(n.f)(f),Object(c.toUpper)(p),y&&y.trim(),d.filter(c.hasVal).join(" ")];return h&&m.push(Object(c.toUpper)(h.keyword),h.expr&&Object(n.f)(h.expr)),m.filter(c.hasVal).join(" ")}function p(t){var e=t.default&&[Object(c.toUpper)(t.default.keyword),Object(i.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),e].filter(c.hasVal).join(" ")}function b(t){var e=t.keyword;switch(void 0===e?"table":e){case"aggregate":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type,u=e.expr,i=e.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(u.map(p).join(", ")).concat(i?[" ORDER","BY",i.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var e=t.type,r=t.table,n=t.if_exists,o=t.prefix,a=t.expr,s=void 0===a?[]:a,l=Object(c.toUpper)(e),f=Object(u.c)(r),p=s.map(i.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var e=t.expr,r=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),Object(c.identifierToSql)(n),f(e)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var e=t.expr,r=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(r),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(e)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var e=t.args,r=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),e&&"(".concat(e.expr?e.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(r)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var e=t.type,r=t.columns,o=t.attributes,a=t.select,i=t.view,l=t.with,f=[Object(c.toUpper)(e),"VIEW",Object(u.b)(i)];return r&&f.push("(".concat(r.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(s.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return f})),r.d(e,"d",(function(){return i})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function i(t){if(!t)return[];var e=t.keyword,r=t.type;return[e.toUpperCase(),Object(n.toUpper)(r)]}function s(t){if(t){var e=t.type,r=t.expr,o=t.symbol,u=e.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":o&&s.push(o),s.push(Object(n.literalToSQL)(r));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,a(i(t)));break;case"WITH PARSER":s.push(r);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":s.push(o,Object(n.toUpper)(r.value),Object(n.onPartitionsToSQL)(r.on));break;default:s.push(o,Object(n.literalToSQL)(r))}return s.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(s):[]}function l(t){var e=t.constraint_type,r=t.index_type,u=t.index_options,s=void 0===u?[]:u,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(i(r))),l&&l.length){var v="CHECK"===Object(n.toUpper)(e)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(s).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var e=[],r=t.keyword,o=t.index;return e.push(Object(n.toUpper)(r)),e.push(o),e.push.apply(e,a(l(t))),e.filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){if(t){var e=t.keyword,r=t.collate,a=r.name,u=r.symbol,i=r.value,s=[Object(o.toUpper)(e)];return i||s.push(u),s.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),i&&s.push(u),s.push(Object(n.a)(i)),s.filter(o.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return p})),r.d(e,"a",(function(){return f}));var n=r(3),o=r(1),a=r(2),u=r(8),i=r(0),s=r(15);function c(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==r.return||r.return()}finally{if(i)throw a}}}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t){if(!t||0===t.length)return"";var e,r=[],n=c(t);try{for(n.s();!(e=n.n()).done;){var u=e.value,s={},l=u.value;for(var f in u)"value"!==f&&"keyword"!==f&&(s[f]=u[f]);var p=[Object(a.f)(s)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),r.push(p.filter(i.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return r.join(", ")}function p(t){var e=t.from,r=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(s.a)(p),"UPDATE",Object(n.c)(r),Object(i.commonOptionConnector)("SET",f,a),Object(i.commonOptionConnector)("FROM",n.c,e),Object(i.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(u.a)(b),Object(i.returningToSQL)(v)].filter(i.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(0),o=r(1),a=r(20);function u(t){if(t){var e=t.as_window_specification,r=t.expr,u=t.keyword,i=t.type,s=t.parentheses,c=Object(n.toUpper)(i);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(e));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(i)," ").concat(Object(n.toUpper)(u)),f=Object(o.a)(r)||[];return s&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,e,r){"use strict";r.d(e,"b",(function(){return S})),r.d(e,"a",(function(){return d}));var n=r(9),o=r(1),a=r(10),u=r(2),i=r(4),s=r(19),c=r(6),l=r(3),f=r(12),p=r(5),b=r(0);function v(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t){if(!t)return[];var e=t.resource;switch(e){case"column":return Object(u.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(s.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(e," type"))}}function m(t){var e=[];switch(t.keyword){case"from":e.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":e.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":e.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return e.filter(b.hasVal).join(" ")}function j(t){var e=t.keyword,r=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(e),Object(l.b)(r),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function O(t){var e=t.as,r=t.domain,n=t.type,a=t.keyword,u=t.target,i=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(r.schema),Object(b.identifierToSql)(r.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.dataTypeToSQL)(u)];if(i&&i.length>0){var l,f=[],p=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=h(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==r.return||r.return()}finally{if(i)throw a}}}}(i);try{for(p.s();!(l=p.n()).done;){var v=l.value,y=v.type;switch(y){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(y),Object(o.a)(v.value));break;case"constraint":f.push(Object(s.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function w(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function L(t){var e=t.type;switch(e){case"as":return[Object(b.toUpper)(e),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(e),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(e),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,i=t.args,s=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=i.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var e=t.type,r=t.keyword,n=t.expr;return[Object(b.toUpper)(e),Object(b.toUpper)(r),Array.isArray(n)?"(".concat(n.map(u.c).join(", "),")"):w(n)].filter(b.hasVal).join(" ")}(s),c.map(L).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var e=t.type,r=t.symbol,n=t.value,a=[Object(b.toUpper)(e),r];switch(Object(b.toUpper)(e)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function S(t){var e=t.keyword,r="";switch(e.toLowerCase()){case"aggregate":r=function(t){var e=t.type,r=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.options,s=[Object(b.toUpper)(e),Object(b.toUpper)(r),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(u.expr.map(n.a).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(n.a).join(", ")].join(" "):"");return s.push("".concat(c,"(").concat(l,")"),"(".concat(i.map(g).join(", "),")")),s.filter(b.hasVal).join(" ")}(t);break;case"table":r=function(t){var e=t.type,r=t.keyword,n=t.table,o=t.like,a=t.as,u=t.temporary,i=t.if_not_exists,s=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,h=t.partition_of,y=t.query_expr,m=t.unlogged,O=t.with,w=[Object(b.toUpper)(e),Object(b.toUpper)(v),Object(b.toUpper)(u),Object(b.toUpper)(m),Object(b.toUpper)(r),Object(b.toUpper)(i),Object(l.c)(n)];if(o){var L=o.type,C=o.table,g=Object(l.c)(C);return w.push(Object(b.toUpper)(L),g),w.filter(b.hasVal).join(" ")}if(h)return w.concat([j(h)]).filter(b.hasVal).join(" ");if(s&&w.push("(".concat(s.map(d).join(", "),")")),c){var S=Object(b.getParserOpt)().database,A=S&&"sqlite"===S.toLowerCase()?", ":" ";w.push(c.map(l.a).join(A))}if(O){var T=O.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");w.push("WITH (".concat(T,")"))}return w.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),y&&w.push(Object(p.b)(y)),w.filter(b.hasVal).join(" ")}(t);break;case"trigger":r="constraint"===t.resource?function(t){var e=t.constraint,r=t.constraint_kw,n=t.deferrable,a=t.events,u=t.execute,i=t.for_each,s=t.from,f=t.location,p=t.keyword,h=t.or,y=t.type,d=t.table,m=t.when,j=[Object(b.toUpper)(y),Object(b.toUpper)(h),Object(b.toUpper)(r),Object(b.toUpper)(p),Object(b.identifierToSql)(e),Object(b.toUpper)(f)],O=Object(b.triggerEventToSQL)(a);return j.push(O,"ON",Object(l.b)(d)),s&&j.push("FROM",Object(l.b)(s)),j.push.apply(j,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(i)))),m&&j.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),j.push(Object(b.toUpper)(u.keyword),Object(c.g)(u.expr)),j.filter(b.hasVal).join(" ")}(t):function(t){var e=t.definer,r=t.for_each,n=t.keyword,a=t.execute,i=t.type,s=t.table,c=t.if_not_exists,v=t.temporary,h=t.trigger,y=t.events,d=t.order,m=t.time,j=t.when,O=[Object(b.toUpper)(i),Object(b.toUpper)(v),Object(o.a)(e),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(h),Object(b.toUpper)(m),y.map((function(t){var e=[Object(b.toUpper)(t.keyword)],r=t.args;return r&&e.push(Object(b.toUpper)(r.keyword),r.columns.map(u.f).join(", ")),e.join(" ")})),"ON",Object(l.b)(s),Object(b.toUpper)(r&&r.keyword),Object(b.toUpper)(r&&r.args),d&&"".concat(Object(b.toUpper)(d.keyword)," ").concat(Object(b.identifierToSql)(d.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,j),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":O.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":O.push(Object(p.a)(a.expr.ast))}return O.push(Object(b.toUpper)(a.suffix)),O.filter(b.hasVal).join(" ")}(t);break;case"extension":r=function(t){var e=t.extension,r=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,u=t.type,i=t.with,s=t.version;return[Object(b.toUpper)(u),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(e),Object(b.toUpper)(i),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,s),Object(b.commonOptionConnector)("FROM",b.literalToSQL,r)].filter(b.hasVal).join(" ")}(t);break;case"function":r=C(t);break;case"index":r=function(t){var e=t.concurrently,r=t.filestream_on,u=t.keyword,i=t.if_not_exists,s=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,h=t.index,y=t.on,d=t.index_options,m=t.algorithm_option,j=t.lock_option,O=t.on_kw,w=t.table,L=t.tablespace,C=t.type,g=t.where,S=t.with,A=t.with_before_where,T=S&&"WITH (".concat(Object(a.b)(S).join(", "),")"),E=s&&"".concat(Object(b.toUpper)(s.keyword)," (").concat(s.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=h;h&&(U="string"==typeof h?Object(b.identifierToSql)(h):[Object(b.identifierToSql)(h.schema),Object(b.identifierToSql)(h.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(u),Object(b.toUpper)(i),Object(b.toUpper)(e),U,Object(b.toUpper)(O),Object(l.b)(w)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),E,Object(a.b)(d).join(" "),Object(n.b)(m),Object(n.b)(j),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,L)]);return A?_.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),_.push(Object(b.commonOptionConnector)("ON",o.a,y),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,r)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":r=function(t){var e=t.type,r=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,u=t.create_definitions,i=[Object(b.toUpper)(e),Object(b.toUpper)(o),Object(b.toUpper)(r),Object(b.toUpper)(a),Object(l.c)(n)];return u&&i.push(u.map(d).join(" ")),i.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":r=function(t){var e=t.type,r=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,u=t[r],i=u.db,s=u.schema,c=[Object(b.literalToSQL)(i),s.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(e),Object(b.toUpper)(n),Object(b.toUpper)(r),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":r=function(t){var e=t.algorithm,r=t.columns,n=t.definer,a=t.if_not_exists,u=t.keyword,i=t.recursive,s=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,h=t.view,y=t.with,d=t.with_options,m=h.db,j=h.schema,O=h.view,w=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(j),Object(b.identifierToSql)(O)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(f),Object(b.toUpper)(i),e&&"ALGORITHM = ".concat(Object(b.toUpper)(e)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(u),Object(b.toUpper)(a),w,r&&"(".concat(r.map(b.columnIdentifierToSql).join(", "),")"),d&&["WITH","(".concat(d.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(y)].filter(b.hasVal).join(" ")}(t);break;case"domain":r=O(t);break;case"type":r=function(t){var e=t.as,r=t.create_definitions,n=t.keyword,a=t.name,u=t.resource,i=t.type,s=[Object(b.toUpper)(i),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(e),Object(b.toUpper)(u)];if(r){var c=[];switch(u){case"enum":case"range":c.push(Object(o.a)(r));break;default:c.push("(".concat(r.map(d).join(", "),")"))}s.push(c.filter(b.hasVal).join(" "))}return s.filter(b.hasVal).join(" ")}(t);break;case"user":r=function(t){var e=t.attribute,r=t.comment,n=t.default_role,a=t.if_not_exists,u=t.keyword,s=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var e=t.user,r=t.auth_option,n=[Object(i.i)(e)];return r&&n.push(Object(b.toUpper)(r.keyword),r.auth_plugin,Object(b.literalToSQL)(r.value)),n.filter(b.hasVal).join(" ")})).join(", "),h=[Object(b.toUpper)(p),Object(b.toUpper)(u),Object(b.toUpper)(a),v];return n&&h.push(Object(b.toUpper)(n.keyword),n.value.map(i.i).join(", ")),h.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&h.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return h.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),h.push(Object(b.literalToSQL)(s),Object(b.commentToSQL)(r),Object(b.literalToSQL)(e)),h.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(e))}return r}},function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var n=r(2),o=r(1),a=r(0);function u(t){if(t&&0!==t.length){var e=t[0].recursive?"RECURSIVE ":"",r=t.map((function(t){var e=t.name,r=t.stmt,u=t.columns,i=Array.isArray(u)?"(".concat(u.map(n.f).join(", "),")"):"";return"".concat("default"===e.type?Object(a.identifierToSql)(e.value):Object(a.literalToSQL)(e)).concat(i," AS (").concat(Object(o.a)(r),")")})).join(", ");return"WITH ".concat(e).concat(r)}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var e=t&&t.ast?t.ast:t;if(!o.includes(e.type))throw new Error("".concat(e.type," statements not supported at the moment"))}function u(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function i(t){return"go"===t.go?function t(e){if(!e||0===e.length)return"";var r=[u(e.ast)];return e.go_next&&r.push(e.go.toUpperCase(),t(e.go_next)),r.filter((function(t){return t})).join(" ")}(t):u(t)}},function(t,e,r){"use strict";r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return c}));var n=r(3),o=r(1),a=r(2),u=r(0),i=r(7),s=r(12);function c(t){if("select"===t.type)return Object(i.a)(t);var e=t.map(o.a);return"(".concat(e.join("), ("),")")}function l(t){if(!t)return"";var e=["PARTITION","("];if(Array.isArray(t))e.push(t.map(u.identifierToSql).join(", "));else{var r=t.value;e.push(r.map(o.a).join(", "))}return e.push(")"),e.filter(u.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var e=t.expr,r=t.keyword,n=e.type,a=[Object(u.toUpper)(r)];switch(n){case"origin":a.push(Object(u.literalToSQL)(e));break;case"update":a.push("UPDATE",Object(u.commonOptionConnector)("SET",s.a,e.set),Object(u.commonOptionConnector)("WHERE",o.a,e.where))}return a.filter(u.hasVal).join(" ")}function b(t){if(!t)return"";var e=t.action;return[f(t.target),p(e)].filter(u.hasVal).join(" ")}function v(t){var e=t.table,r=t.type,a=t.prefix,i=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,h=t.where,y=t.on_duplicate_update,d=t.partition,m=t.returning,j=t.set,O=y||{},w=O.keyword,L=O.set,C=[Object(u.toUpper)(r),Object(u.toUpper)(i),Object(n.c)(e),l(d)];return Array.isArray(f)&&C.push("(".concat(f.map(u.literalToSQL).join(", "),")")),C.push(Object(u.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(u.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(u.commonOptionConnector)("SET",s.a,j)),C.push(Object(u.commonOptionConnector)("WHERE",o.a,h)),C.push(Object(u.commonOptionConnector)(w,s.a,L)),C.push(Object(u.returningToSQL)(m)),C.filter(u.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(0),o=r(1);function a(t){var e=t.expr,r=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(e),Object(n.toUpper)(r),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r(0),o=r(10),a=r(2);function u(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return i(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t){if(t){var e=t.constraint,r=t.constraint_type,i=t.enforced,s=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(e));var h=Object(n.toUpper)(r);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===h&&(h="UNIQUE"),b.push(h),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(s)),b.push.apply(b,u(Object(o.c)(t))),b.push.apply(b,u(Object(a.g)(l))),b.push(Object(n.toUpper)(i)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l}));var n=r(0),o=r(1),a=r(13);function u(t){if(t){var e=t.type;return"rows"===e?[Object(n.toUpper)(e),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function i(t){if("string"==typeof t)return t;var e=t.window_specification;return"(".concat(function(t){var e=t.name,r=t.partitionby,a=t.orderby,i=t.window_frame_clause;return[e,Object(o.c)(r,"partition by"),Object(o.c)(a,"order by"),u(i)].filter(n.hasVal).join(" ")}(e),")")}function s(t){var e=t.name,r=t.as_window_specification;return"".concat(e," AS ").concat(i(r))}function c(t){return t.expr.map(s).join(", ")}function l(t){var e=t.over;return[function(t){var e=t.args,r=t.name,a=t.consider_nulls,u=void 0===a?"":a,i=t.separator,s=void 0===i?", ":i;return[r,"(",e?Object(o.a)(e).join(s):"",")",u&&" ",u].filter(n.hasVal).join("")}(t),Object(a.a)(e)].filter(n.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.operator||t.op,r=Object(n.a)(t.right),a=!1;if(Array.isArray(r)){switch(e){case"=":e="IN";break;case"!=":e="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,r="".concat(r[0]," AND ").concat(r[1])}a||(r="(".concat(r.join(", "),")"))}var u=t.right.escape||{},i=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),e,r,Object(o.toUpper)(u.type),Object(n.a)(u.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(i,")"):i].join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));var n=r(1),o=r(0);function a(t){var e=t.left,r=t.right,a=t.symbol,u=t.keyword;e.keyword=u;var i=Object(n.a)(e),s=Object(n.a)(r);return[i,Object(o.toUpper)(a),s].filter(o.hasVal).join(" ")}},function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));var n=r(1),o=r(8),a=r(3),u=r(0);function i(t){var e,r,i,s,c=t.keyword,l=t.suffix,f="";switch(Object(u.toUpper)(c)){case"BINLOG":r=(e=t).in,i=e.from,s=e.limit,f=[Object(u.commonOptionConnector)("IN",u.literalToSQL,r&&r.right),Object(u.commonOptionConnector)("FROM",a.c,i),Object(o.a)(s)].filter(u.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var e=t.expr;if(e){var r=e.op;return"LIKE"===Object(u.toUpper)(r)?Object(u.commonOptionConnector)("LIKE",u.literalToSQL,e.right):Object(u.commonOptionConnector)("WHERE",n.a,e)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(u.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var e=t.for;if(e){var r=e.user,n=e.host,o=e.role_list,a="'".concat(r,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(u.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(u.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(u.toUpper)(c),Object(u.toUpper)(l),f].filter(u.hasVal).join(" ")}},function(t,e,r){"use strict";var n=r(2),o=r(1),a=r(25);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var i,s,c,l=(i={},s="bigquery",c=a.parse,(s=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(s))in i?Object.defineProperty(i,s,{value:c,enumerable:!0,configurable:!0,writable:!0}):i[s]=c,i),f=r(16),p=r(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==r.return||r.return()}finally{if(i)throw a}}}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function d(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}var m=function(){return function(t,e,r){return e&&y(t.prototype,e),r&&y(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=this.parse(t,e);return r&&r.ast}},{key:"sqlify",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(f.a)(t,e)}},{key:"exprToSQL",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(e),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(r),!t||"*"===t)return[];var o=Object(n.k)(e);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,r=e.database,n=void 0===r?"bigquery":r;Object(p.setParserOpt)(e);var o=n.toLowerCase();if(l[o])return l[o](!1===e.trimQuery?t:t.trim(),e.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(e&&0!==e.length){var n=r.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,u=this["".concat(o,"List")].bind(this),i=u(t,r),s=!0,c="",l=v(i);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,h=!1,y=v(e);try{for(y.s();!(f=y.n()).done;){var d=f.value,m=new RegExp("^".concat(d,"$"),"i");if(m.test(b)){h=!0;break}}}catch(t){y.e(t)}finally{y.f()}if(!h){c=b,s=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!s)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,e){var r=this.parse(t,e);return r&&r.tableList}},{key:"columnList",value:function(t,e){var r=this.parse(t,e);return r&&r.columnList}}])}();e.a=m},function(t,e,r){"use strict";var n=r(29);function o(t,e,r,n){this.message=t,this.expected=e,this.found=r,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(o,Error),o.buildMessage=function(t,e){var r={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var e,r="";for(e=0;e<t.parts.length;e++)r+=t.parts[e]instanceof Array?a(t.parts[e][0])+"-"+a(t.parts[e][1]):a(t.parts[e]);return"["+(t.inverted?"^":"")+r+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var e,n,o,a=new Array(t.length);for(e=0;e<t.length;e++)a[e]=(o=t[e],r[o.type](o));if(a.sort(),a.length>0){for(e=1,n=1;e<a.length;e++)a[e-1]!==a[e]&&(a[n]=a[e],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(e)+" found."},t.exports={SyntaxError:o,parse:function(t,e){e=void 0!==e?e:{};var r,a={},u={start:Za},i=Za,s=function(t,e){return yl(t,e)},c=function(t,e){return dl(t,e)},l=function(t,e){return yl(t,e)},f=Ba("=",!1),p=Ba("DUPLICATE",!0),b=Ba("BINARY",!0),v=Ba("MASTER",!0),h=Ba("LOGS",!0),y=Ba("BINLOG",!0),d=Ba("EVENTS",!0),m=Ba("CHARACTER",!0),j=Ba("SET",!0),O=Ba("COLLATION",!0),w=function(t,e){return yl(t,e,1)},L=Ba("IF",!0),C=Ba("CASCADED",!0),g=Ba("LOCAL",!0),S=Ba("CHECK",!0),A=Ba("OPTION",!1),T=Ba("check_option",!0),E=Ba("security_barrier",!0),U=Ba("security_invoker",!0),_=Ba("GRANTS",!0),x=Ba(".",!1),I=Ba("ALGORITHM",!0),N=Ba("DEFAULT",!0),k=Ba("INSTANT",!0),R=Ba("INPLACE",!0),V=Ba("COPY",!0),M=Ba("LOCK",!0),q=Ba("NONE",!0),P=Ba("SHARED",!0),D=Ba("EXCLUSIVE",!0),Q=Ba("AUTO_INCREMENT",!0),F=Ba("UNIQUE",!0),B=Ba("KEY",!0),H=Ba("PRIMARY",!0),$=Ba("FOR",!0),W=Ba("COLUMN_FORMAT",!0),Y=Ba("FIXED",!0),G=Ba("DYNAMIC",!0),Z=Ba("STORAGE",!0),J=Ba("DISK",!0),X=Ba("MEMORY",!0),K=Ba("MATCH FULL",!0),z=Ba("MATCH PARTIAL",!0),tt=Ba("MATCH SIMPLE",!0),et=Ba("expiration_timestamp",!0),rt=Ba("partition_expiration_days",!0),nt=Ba("require_partition_filter",!0),ot=Ba("kms_key_name",!0),at=Ba("friendly_name",!0),ut=Ba("description",!0),it=Ba("labels",!0),st=Ba("default_rounding_mode",!0),ct=Ba("AVG_ROW_LENGTH",!0),lt=Ba("KEY_BLOCK_SIZE",!0),ft=Ba("MAX_ROWS",!0),pt=Ba("MIN_ROWS",!0),bt=Ba("STATS_SAMPLE_PAGES",!0),vt=Ba("CONNECTION",!0),ht=Ba("COMPRESSION",!0),yt=Ba("'",!1),dt=Ba("ZLIB",!0),mt=Ba("LZ4",!0),jt=Ba("ENGINE",!0),Ot=Ba("CLUSTER",!0),wt=Ba("BY",!0),Lt=Ba("OPTIONS",!0),Ct=Ba("CHARSET",!0),gt=Ba("COLLATE",!0),St=Ba("READ",!0),At=Ba("LOW_PRIORITY",!0),Tt=Ba("WRITE",!0),Et=(Ba("FOREIGN KEY",!0),Ba("NOT",!0)),Ut=(Ba("REPLICATION",!0),Ba("BTREE",!0)),_t=Ba("HASH",!0),xt=Ba("WITH",!0),It=Ba("PARSER",!0),Nt=Ba("VISIBLE",!0),kt=Ba("INVISIBLE",!0),Rt=Ba("RESTRICT",!0),Vt=Ba("CASCADE",!0),Mt=Ba("SET NULL",!0),qt=Ba("NO ACTION",!0),Pt=Ba("SET DEFAULT",!0),Dt=Ba("UPDATE",!0),Qt=Ba("CREATE",!0),Ft=Ba("DELETE",!0),Bt=Ba("INSERT",!0),Ht=Ba(":=",!1),$t=Ba("return",!0),Wt=Ba("REPLACE",!0),Yt=Ba("ANALYZE",!0),Gt=Ba("ATTACH",!0),Zt=Ba("DATABASE",!0),Jt=Ba("RENAME",!0),Xt=Ba("SHOW",!0),Kt=Ba("DESCRIBE",!0),zt=Ba("@",!1),te=Ba("@@",!1),ee=Ba("$",!1),re=Ba("TEMPORARY",!0),ne=Ba("TEMP",!0),oe=Ba("SCHEMA",!0),ae=Ba("ALTER",!0),ue=Ba("SPATIAL",!0),ie=Ba("(",!1),se=Ba(")",!1),ce=Ba("INTERSECT",!0),le=Ba("EXCEPT",!0),fe=Ba("SYSTEM_TIME",!0),pe=Ba("AS",!0),be=Ba("OF",!0),ve=Ba("UNNEST",!0),he=function(t,e){return e.unshift(t),e.forEach(t=>{const{table:e,as:r}=t;Cl[e]=e,r&&(Cl[r]=e),function(t){const e=jl(t);t.clear(),e.forEach(e=>t.add(e))}(Ll)}),e},ye=/^[@]/,de=Ha(["@"],!1,!1),me=/^[{]/,je=Ha(["{"],!1,!1),Oe=/^[=]/,we=Ha(["="],!1,!1),Le=/^[}]/,Ce=Ha(["}"],!1,!1),ge=Ba("TABLESAMPLE",!0),Se=Ba("BERNOULLI",!0),Ae=Ba("RESERVOIR",!0),Te=Ba("PERCENT",!0),Ee=Ba("ROWS",!0),Ue=Ba("RANGE",!0),_e=Ba("FOLLOWING",!0),xe=Ba("PRECEDING",!0),Ie=Ba("CURRENT",!0),Ne=Ba("ROW",!0),ke=Ba("UNBOUNDED",!0),Re=Ba("!",!1),Ve=function(t){return t[0]+" "+t[2]},Me=Ba(">=",!1),qe=Ba(">",!1),Pe=Ba("<=",!1),De=Ba("<>",!1),Qe=Ba("<",!1),Fe=Ba("!=",!1),Be=Ba("+",!1),He=Ba("-",!1),$e=Ba("*",!1),We=Ba("/",!1),Ye=Ba("%",!1),Ge=Ba("~",!1),Ze=function(t){return!0===ll[t.toUpperCase()]},Je=Ba('"',!1),Xe=/^[^"]/,Ke=Ha(['"'],!0,!1),ze=/^[^']/,tr=Ha(["'"],!0,!1),er=Ba("`",!1),rr=/^[^`]/,nr=Ha(["`"],!0,!1),or=function(t,e){return t+e.join("")},ar=/^[A-Za-z_]/,ur=Ha([["A","Z"],["a","z"],"_"],!1,!1),ir=/^[A-Za-z0-9_\-]/,sr=Ha([["A","Z"],["a","z"],["0","9"],"_","-"],!1,!1),cr=/^[A-Za-z0-9_:]/,lr=Ha([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),fr=Ba(":",!1),pr=Ba("string_agg",!0),br=Ba("ANY_VALUE",!0),vr=Ba("YEAR_MONTH",!0),hr=Ba("DAY_HOUR",!0),yr=Ba("DAY_MINUTE",!0),dr=Ba("DAY_SECOND",!0),mr=Ba("DAY_MICROSECOND",!0),jr=Ba("HOUR_MINUTE",!0),Or=Ba("HOUR_SECOND",!0),wr=Ba("HOUR_MICROSECOND",!0),Lr=Ba("MINUTE_SECOND",!0),Cr=Ba("MINUTE_MICROSECOND",!0),gr=Ba("SECOND_MICROSECOND",!0),Sr=Ba("TIMEZONE_HOUR",!0),Ar=Ba("TIMEZONE_MINUTE",!0),Tr=Ba("CENTURY",!0),Er=Ba("DAYOFWEEK",!0),Ur=Ba("DAY",!0),_r=Ba("DATE",!0),xr=Ba("DECADE",!0),Ir=Ba("DOW",!0),Nr=Ba("DOY",!0),kr=Ba("EPOCH",!0),Rr=Ba("HOUR",!0),Vr=Ba("ISODOW",!0),Mr=Ba("ISOWEEK",!0),qr=Ba("ISOYEAR",!0),Pr=Ba("MICROSECONDS",!0),Dr=Ba("MILLENNIUM",!0),Qr=Ba("MILLISECONDS",!0),Fr=Ba("MINUTE",!0),Br=Ba("MONTH",!0),Hr=Ba("QUARTER",!0),$r=Ba("SECOND",!0),Wr=Ba("TIME",!0),Yr=Ba("TIMEZONE",!0),Gr=Ba("WEEK",!0),Zr=Ba("YEAR",!0),Jr=Ba("DATE_TRUNC",!0),Xr=Ba("R",!0),Kr=function(t,e){return{type:t.toLowerCase(),value:e[1].join("")}},zr=/^[^"\\\0-\x1F\x7F]/,tn=Ha(['"',"\\",["\0",""],""],!0,!1),en=/^[^'\\]/,rn=Ha(["'","\\"],!0,!1),nn=Ba("\\'",!1),on=Ba('\\"',!1),an=Ba("\\\\",!1),un=Ba("\\/",!1),sn=Ba("\\b",!1),cn=Ba("\\f",!1),ln=Ba("\\n",!1),fn=Ba("\\r",!1),pn=Ba("\\t",!1),bn=Ba("\\u",!1),vn=Ba("\\",!1),hn=Ba("''",!1),yn=Ba('""',!1),dn=Ba("``",!1),mn=/^[\n\r]/,jn=Ha(["\n","\r"],!1,!1),On=/^[0-9]/,wn=Ha([["0","9"]],!1,!1),Ln=/^[0-9a-fA-F]/,Cn=Ha([["0","9"],["a","f"],["A","F"]],!1,!1),gn=/^[eE]/,Sn=Ha(["e","E"],!1,!1),An=/^[+\-]/,Tn=Ha(["+","-"],!1,!1),En=Ba("NULL",!0),Un=Ba("NOT NULL",!0),_n=Ba("TRUE",!0),xn=Ba("TO",!0),In=Ba("FALSE",!0),Nn=Ba("DROP",!0),kn=Ba("USE",!0),Rn=Ba("SELECT",!0),Vn=Ba("RECURSIVE",!0),Mn=Ba("IGNORE",!0),qn=(Ba("EXPLAIN",!0),Ba("PARTITION",!0)),Pn=Ba("INTO",!0),Dn=Ba("FROM",!0),Qn=Ba("UNLOCK",!0),Fn=Ba("TABLE",!0),Bn=Ba("TABLES",!0),Hn=Ba("ON",!0),$n=Ba("LEFT",!0),Wn=Ba("RIGHT",!0),Yn=Ba("FULL",!0),Gn=Ba("INNER",!0),Zn=Ba("CROSS",!0),Jn=Ba("JOIN",!0),Xn=Ba("OUTER",!0),Kn=Ba("OVER",!0),zn=Ba("UNION",!0),to=Ba("VALUE",!0),eo=Ba("VALUES",!0),ro=Ba("USING",!0),no=Ba("WHERE",!0),oo=Ba("GROUP",!0),ao=Ba("ORDER",!0),uo=Ba("HAVING",!0),io=Ba("QUALIFY",!0),so=Ba("WINDOW",!0),co=Ba("ORDINAL",!0),lo=Ba("SAFE_ORDINAL",!0),fo=Ba("LIMIT",!0),po=Ba("OFFSET",!0),bo=Ba("SAFE_OFFSET",!0),vo=Ba("ASC",!0),ho=Ba("DESC",!0),yo=Ba("ALL",!0),mo=Ba("DISTINCT",!0),jo=Ba("BETWEEN",!0),Oo=Ba("IN",!0),wo=Ba("IS",!0),Lo=Ba("LIKE",!0),Co=Ba("EXISTS",!0),go=Ba("AND",!0),So=Ba("OR",!0),Ao=Ba("COUNT",!0),To=Ba("MAX",!0),Eo=Ba("MIN",!0),Uo=Ba("SUM",!0),_o=Ba("AVG",!0),xo=Ba("EXTRACT",!0),Io=Ba("CALL",!0),No=Ba("CASE",!0),ko=Ba("WHEN",!0),Ro=Ba("THEN",!0),Vo=Ba("ELSE",!0),Mo=Ba("END",!0),qo=Ba("CAST",!0),Po=Ba("SAFE_CAST",!0),Do=Ba("ARRAY",!0),Qo=Ba("BYTES",!0),Fo=Ba("BOOL",!0),Bo=(Ba("CHAR",!0),Ba("GEOGRAPHY",!0)),Ho=(Ba("VARCHAR",!0),Ba("NUMERIC",!0)),$o=Ba("DECIMAL",!0),Wo=Ba("SIGNED",!0),Yo=Ba("UNSIGNED",!0),Go=Ba("INT64",!0),Zo=(Ba("ZEROFILL",!0),Ba("INTEGER",!0)),Jo=Ba("JSON",!0),Xo=(Ba("SMALLINT",!0),Ba("STRING",!0)),Ko=Ba("STRUCT",!0),zo=(Ba("TINYINT",!0),Ba("TINYTEXT",!0),Ba("TEXT",!0),Ba("MEDIUMTEXT",!0),Ba("LONGTEXT",!0),Ba("BIGINT",!0),Ba("FLOAT64",!0)),ta=(Ba("DOUBLE",!0),Ba("DATETIME",!0)),ea=Ba("TIMESTAMP",!0),ra=Ba("TRUNCATE",!0),na=(Ba("USER",!0),Ba("CURRENT_DATE",!0)),oa=(Ba("ADDDATE",!0),Ba("INTERVAL",!0)),aa=Ba("CURRENT_TIME",!0),ua=Ba("CURRENT_TIMESTAMP",!0),ia=Ba("SESSION_USER",!0),sa=Ba("GLOBAL",!0),ca=Ba("SESSION",!0),la=Ba("PIVOT",!0),fa=Ba("PERSIST",!0),pa=Ba("PERSIST_ONLY",!0),ba=Ba("VIEW",!0),va=Ba("ADD",!0),ha=Ba("COLUMN",!0),ya=Ba("INDEX",!0),da=Ba("FULLTEXT",!0),ma=Ba("COMMENT",!0),ja=(Ba("CONSTRAINT",!0),Ba("REFERENCES",!0)),Oa=Ba(",",!1),wa=Ba("[",!1),La=Ba("]",!1),Ca=Ba(";",!1),ga=Ba("||",!1),Sa=Ba("&&",!1),Aa=Ba("/*",!1),Ta=Ba("*/",!1),Ea=Ba("--",!1),Ua=Ba("#",!1),_a={type:"any"},xa=/^[ \t\n\r]/,Ia=Ha([" ","\t","\n","\r"],!1,!1),Na=function(t){return{dataType:t}},ka=Ba("MAX",!1),Ra=Ba("max",!1),Va=function(t,e){return{dataType:t,definition:e,anglebracket:!0}},Ma=0,qa=0,Pa=[{line:1,column:1}],Da=0,Qa=[],Fa=0;if("startRule"in e){if(!(e.startRule in u))throw new Error("Can't start parsing from rule \""+e.startRule+'".');i=u[e.startRule]}function Ba(t,e){return{type:"literal",text:t,ignoreCase:e}}function Ha(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function $a(e){var r,n=Pa[e];if(n)return n;for(r=e-1;!Pa[r];)r--;for(n={line:(n=Pa[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return Pa[e]=n,n}function Wa(t,e){var r=$a(t),n=$a(e);return{start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function Ya(t){Ma<Da||(Ma>Da&&(Da=Ma,Qa=[]),Qa.push(t))}function Ga(t,e,r){return new o(o.buildMessage(t,e),t,e,r)}function Za(){var t,e;return t=Ma,zc()!==a&&(e=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Ja())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Xc())!==a&&(i=zc())!==a&&(s=Ja())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Xc())!==a&&(i=zc())!==a&&(s=Ja())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=function(t,e){const r=t&&t.ast||t,n=e&&e.length&&e[0].length>=4?[r]:r;for(let t=0;t<e.length;t++)e[t][3]&&0!==e[t][3].length&&n.push(e[t][3]&&e[t][3].ast||e[t][3]);return{tableList:Array.from(wl),columnList:jl(Ll),ast:n}}(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=t,t=e):(Ma=t,t=a),t}function Ja(){var e;return(e=function(){var e,r,n,o,u,i,s;(e=function(){var t,e,r,n;t=Ma,(e=Wu())!==a&&zc()!==a?((r=gi())===a&&(r=null),r!==a&&zc()!==a?((n=Ti())===a&&(n=null),n!==a&&zc()!==a?(qa=t,o=e,u=r,i=n,e={tableList:Array.from(wl),columnList:jl(Ll),ast:{...o.ast,_orderby:u,_limit:i,_parentheses:o._parentheses}},t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);var o,u,i;return t}())===a&&(e=Ma,r=Ma,40===t.charCodeAt(Ma)?(n="(",Ma++):(n=a,0===Fa&&Ya(ie)),n!==a&&(o=zc())!==a&&(u=Gu())!==a&&(i=zc())!==a?(41===t.charCodeAt(Ma)?(s=")",Ma++):(s=a,0===Fa&&Ya(se)),s!==a?r=n=[n,o,u,i,s]:(Ma=r,r=a)):(Ma=r,r=a),r!==a&&(qa=e,r={...r[2],parentheses_symbol:!0}),e=r);return e}())===a&&(e=function(){var e;(e=Wu())===a&&(e=function(){var t,e,r,n,o,u,i,s;t=Ma,(e=ku())!==a&&zc()!==a&&(r=si())!==a&&zc()!==a&&Gs()!==a&&zc()!==a&&(n=au())!==a&&zc()!==a?((o=ui())===a&&(o=null),o!==a&&zc()!==a?((u=yi())===a&&(u=null),u!==a&&zc()!==a?((i=gi())===a&&(i=null),i!==a&&zc()!==a?((s=Ti())===a&&(s=null),s!==a?(qa=t,e=function(t,e,r,n,o,a){const u=t=>{const{server:e,db:r,schema:n,as:o,table:a,join:u}=t,i=u?"select":"update",s=[e,r,n].filter(Boolean).join(".")||null;r&&(dbObj[a]=s),a&&wl.add(`${i}::${s}::${a}`)};return t&&t.forEach(u),r&&r.forEach(u),e&&e.forEach(t=>Ll.add(`update::${t.table}::${t.column}`)),{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"update",table:t,set:e,where:n,orderby:o,limit:a}}}(r,n,o,u,i,s),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(e=function(){var t,e,r,n,o,u,i,s;t=Ma,(e=iu())!==a&&zc()!==a?((r=Ws())===a&&(r=null),r!==a&&zc()!==a&&(n=bi())!==a&&zc()!==a?((o=su())===a&&(o=null),o!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(u=ts())!==a&&zc()!==a&&Wc()!==a&&zc()!==a&&(i=cu())!==a&&zc()!==a?((s=lu())===a&&(s=null),s!==a?(qa=t,e=function(t,e,r,n,o,a){if(e&&(wl.add(`insert::${e.db}::${e.table}`),e.as=null),n){let t=e&&e.table||null;Array.isArray(o)&&o.forEach((t,e)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(e+1))}),n.forEach(e=>Ll.add(`insert::${t}::${e}`))}return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:t,table:[e],columns:n,values:o,partition:r,on_duplicate_update:a}}}(e,n,o,u,i,s),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(e=function(){var e,r,n,o,u,i,s,c;e=Ma,(r=iu())!==a&&zc()!==a?((n=function(){var e,r,n,o;e=Ma,"ignore"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Mn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=null),n!==a&&zc()!==a?((o=Ws())===a&&(o=null),o!==a&&zc()!==a&&(u=bi())!==a&&zc()!==a?((i=su())===a&&(i=null),i!==a&&zc()!==a&&(s=cu())!==a&&zc()!==a?((c=lu())===a&&(c=null),c!==a?(qa=e,r=function(t,e,r,n,o,a,u){n&&(wl.add(`insert::${n.db}::${n.table}`),Ll.add(`insert::${n.table}::(.*)`),n.as=null);const i=[e,r].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:i,on_duplicate_update:u}}}(r,n,o,u,i,s,c),e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var t,e,r,n,o,u,i;t=Ma,(e=iu())!==a&&zc()!==a?((r=Ws())===a&&(r=null),r!==a&&zc()!==a&&(n=bi())!==a&&zc()!==a?((o=su())===a&&(o=null),o!==a&&zc()!==a&&Gs()!==a&&zc()!==a&&(u=au())!==a&&zc()!==a?((i=lu())===a&&(i=null),i!==a?(qa=t,s=e,l=o,f=u,p=i,(c=n)&&(wl.add(`insert::${c.db}::${c.table}`),Ll.add(`insert::${c.table}::(.*)`),c.as=null),e={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:s,table:[c],columns:null,partition:l,set:f,on_duplicate_update:p}},t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);var s,c,l,f,p;return t}())===a&&(e=function(){var t,e,r,n,o,u,i;t=Ma,(e=Vu())!==a&&zc()!==a?((r=si())===a&&(r=null),r!==a&&zc()!==a?((n=ui())===a&&(n=null),n!==a&&zc()!==a?((o=yi())===a&&(o=null),o!==a&&zc()!==a?((u=gi())===a&&(u=null),u!==a&&zc()!==a?((i=Ti())===a&&(i=null),i!==a?(qa=t,e=function(t,e,r,n,o){if(t&&t.forEach(t=>wl.add(`delete::${t.db}::${t.table}`)),e&&e.forEach(t=>{const{db:e,as:r,table:n,join:o}=t,a=o?"select":"delete";n&&wl.add(`${a}::${e}::${n}`),o||Ll.add(`delete::${n}::(.*)`)}),null===t&&1===e.length){const r=e[0];t=[{db:r.db,table:r.table,as:r.as,addition:!0}]}return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"delete",table:t,from:e,where:r,orderby:n,limit:o}}}(r,n,o,u,i),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(e=function(){var e;(e=function(){var e,r,n;e=Ma,(r=function(){var e,r,n,o;e=Ma,"analyze"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Yt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=bi())!==a&&zc()!==a?(qa=e,o=r,u=n,wl.add(`${o}::${u.db}::${u.table}`),r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:o.toLowerCase(),table:u}},e=r):(Ma=e,e=a);var o,u;return e}())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,(r=function(){var e,r,n,o;e=Ma,"attach"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Gt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=Pu())!==a&&zc()!==a&&(o=_i())!==a&&zc()!==a&&(u=Zs())!==a&&zc()!==a&&(i=rs())!==a&&zc()!==a?(qa=e,s=r,c=n,l=o,f=u,p=i,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:s.toLowerCase(),database:c,expr:l,as:f&&f[0].toLowerCase(),schema:p}},e=r):(Ma=e,e=a);var s,c,l,f,p;return e}())===a&&(e=function(){var t,e,r,n,o,u;t=Ma,(e=Hs())!==a&&zc()!==a&&(r=Js())!==a&&zc()!==a&&(n=si())!==a?(qa=t,i=e,s=r,(c=n)&&c.forEach(t=>wl.add(`${i}::${t.db}::${t.table}`)),e={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:i.toLowerCase(),keyword:s.toLowerCase(),name:c}},t=e):(Ma=t,t=a);var i,s,c;t===a&&(t=Ma,(e=Hs())!==a&&zc()!==a&&(r=Pc())!==a&&zc()!==a&&(n=zi())!==a&&zc()!==a&&Ks()!==a&&zc()!==a&&(o=bi())!==a&&zc()!==a?((u=function(){var t,e,r,n,o,u;t=Ma,(e=yu())===a&&(e=du());if(e!==a){for(r=[],n=Ma,(o=zc())!==a?((u=yu())===a&&(u=du()),u!==a?n=o=[o,u]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=yu())===a&&(u=du()),u!==a?n=o=[o,u]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,e=w(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())===a&&(u=null),u!==a&&zc()!==a?(qa=t,e=function(t,e,r,n,o){return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:t.toLowerCase(),keyword:e.toLowerCase(),name:r,table:n,options:o}}}(e,r,n,o,u),t=e):(Ma=t,t=a)):(Ma=t,t=a));return t}())===a&&(e=function(){var e;(e=function(){var t,e,r,n,o,u,i,s,c,f,p,b,v;t=Ma,(e=Ru())!==a&&zc()!==a?(r=Ma,(n=wc())!==a&&(o=zc())!==a&&(u=qu())!==a?r=n=[n,o,u]:(Ma=r,r=a),r===a&&(r=null),r!==a&&(n=zc())!==a?((o=Hu())===a&&(o=Bu()),o===a&&(o=null),o!==a&&(u=zc())!==a&&(i=Js())!==a&&zc()!==a?((s=vu())===a&&(s=null),s!==a&&zc()!==a&&(c=bi())!==a&&zc()!==a?((f=function(){var t,e,r,n,o,u,i,s,c;if(t=Ma,(e=$c())!==a)if(zc()!==a)if((r=mu())!==a){for(n=[],o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=mu())!==a?o=u=[u,i,s,c]:(Ma=o,o=a);o!==a;)n.push(o),o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=mu())!==a?o=u=[u,i,s,c]:(Ma=o,o=a);n!==a&&(o=zc())!==a&&(u=Wc())!==a?(qa=t,e=l(r,n),t=e):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;return t}())===a&&(f=null),f!==a&&zc()!==a?((p=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Au())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=Bc())===a&&(u=null),u!==a&&(i=zc())!==a&&(s=Au())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=Bc())===a&&(u=null),u!==a&&(i=zc())!==a&&(s=Au())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,e=yl(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())===a&&(p=null),p!==a&&zc()!==a?((b=Zs())===a&&(b=null),b!==a&&zc()!==a?((v=Wu())===a&&(v=null),v!==a?(qa=t,h=e,y=r,d=o,m=s,O=f,w=p,L=b,C=v,(j=c)&&wl.add(`create::${j.db}::${j.table}`),e={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:h[0].toLowerCase(),keyword:"table",temporary:d&&d[0].toLowerCase(),if_not_exists:m,table:[j],replace:y&&"or replace",as:L&&L[0].toLowerCase(),query_expr:C&&C.ast,create_definitions:O,table_options:w}},t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);var h,y,d,m,j,O,w,L,C;t===a&&(t=Ma,(e=Ru())!==a&&zc()!==a?((r=Bu())===a&&(r=null),r!==a&&(n=zc())!==a&&(o=Js())!==a&&(u=zc())!==a?((i=vu())===a&&(i=null),i!==a&&zc()!==a&&(s=si())!==a&&zc()!==a&&(c=function t(){var e,r;(e=function(){var t,e;t=Ma,dc()!==a&&zc()!==a&&(e=si())!==a?(qa=t,t={type:"like",table:e}):(Ma=t,t=a);return t}())===a&&(e=Ma,$c()!==a&&zc()!==a&&(r=t())!==a&&zc()!==a&&Wc()!==a?(qa=e,(n=r).parentheses=!0,e=n):(Ma=e,e=a));var n;return e}())!==a?(qa=t,e=function(t,e,r,n,o){return n&&n.forEach(t=>wl.add(`create::${t.db}::${t.table}`)),{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:e&&e[0].toLowerCase(),if_not_exists:r,table:n,like:o}}}(e,r,i,s,c),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a));return t}())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,(r=Ru())!==a&&zc()!==a?((n=Pu())===a&&(n=function(){var e,r,n,o;e=Ma,"schema"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(oe));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()),n!==a&&zc()!==a?((o=vu())===a&&(o=null),o!==a&&zc()!==a&&(u=ws())!==a&&zc()!==a?((i=function(){var t,e,r,n,o,u;if(t=Ma,(e=Tu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Tu())!==a?n=o=[o,u]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Tu())!==a?n=o=[o,u]:(Ma=n,n=a);r!==a?(qa=t,e=w(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())===a&&(i=null),i!==a?(qa=e,r=function(t,e,r,n,o){const a=e.toLowerCase();return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:r,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(r,n,o,u,i),e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,u,i,s,c,f,p,b,v,h,y,d,m,j,O;e=Ma,(r=Ru())!==a&&zc()!==a?(n=Ma,(o=wc())!==a&&(u=zc())!==a&&(i=qu())!==a?n=o=[o,u,i]:(Ma=n,n=a),n===a&&(n=null),n!==a&&(o=zc())!==a?((u=Hu())===a&&(u=Bu()),u===a&&(u=null),u!==a&&(i=zc())!==a?((s=function(){var e,r,n,o;e=Ma,"recursive"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(Vn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(s=null),s!==a&&zc()!==a&&function(){var e,r,n,o;e=Ma,"view"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(ba));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="VIEW"):(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(c=bi())!==a&&zc()!==a?(f=Ma,(p=$c())!==a&&(b=zc())!==a&&(v=ts())!==a&&(h=zc())!==a&&(y=Wc())!==a?f=p=[p,b,v,h,y]:(Ma=f,f=a),f===a&&(f=null),f!==a&&(p=zc())!==a?(b=Ma,(v=oc())!==a&&(h=zc())!==a&&(y=$c())!==a&&(d=zc())!==a&&(m=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=hu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=hu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=hu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a&&(j=zc())!==a&&(O=Wc())!==a?b=v=[v,h,y,d,m,j,O]:(Ma=b,b=a),b===a&&(b=null),b!==a&&(v=zc())!==a&&(h=Zs())!==a&&(y=zc())!==a&&(d=Gu())!==a&&(m=zc())!==a?((j=function(){var e,r,n,o,u;e=Ma,(r=oc())!==a&&zc()!==a?("cascaded"===t.substr(Ma,8).toLowerCase()?(n=t.substr(Ma,8),Ma+=8):(n=a,0===Fa&&Ya(C)),n===a&&("local"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(g))),n!==a&&zc()!==a?("check"===t.substr(Ma,5).toLowerCase()?(o=t.substr(Ma,5),Ma+=5):(o=a,0===Fa&&Ya(S)),o!==a&&zc()!==a?("OPTION"===t.substr(Ma,6)?(u="OPTION",Ma+=6):(u=a,0===Fa&&Ya(A)),u!==a?(qa=e,r=`with ${n.toLowerCase()} check option`,e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);e===a&&(e=Ma,(r=oc())!==a&&zc()!==a?("check"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(S)),n!==a&&zc()!==a?("OPTION"===t.substr(Ma,6)?(o="OPTION",Ma+=6):(o=a,0===Fa&&Ya(A)),o!==a?(qa=e,e=r="with check option"):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a));return e}())===a&&(j=null),j!==a?(qa=e,w=r,L=n,T=u,E=s,_=f,x=b,I=d,N=j,(U=c).view=U.table,delete U.table,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:w[0].toLowerCase(),keyword:"view",replace:L&&"or replace",temporary:T&&T[0].toLowerCase(),recursive:E&&E.toLowerCase(),columns:_&&_[2],select:I,view:U,with_options:x&&x[4],with:N}},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var w,L,T,E,U,_,x,I,N;return e}());return e}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=function(){var e,r,n,o;e=Ma,"truncate"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(ra));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="TRUNCATE"):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=Js())===a&&(n=null),n!==a&&zc()!==a&&(o=si())!==a?(qa=e,u=r,i=n,(s=o)&&s.forEach(t=>wl.add(`${u}::${t.db}::${t.table}`)),r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:u.toLowerCase(),keyword:i&&i.toLowerCase()||"table",name:s}},e=r):(Ma=e,e=a)):(Ma=e,e=a);var u,i,s;return e}())===a&&(e=function(){var t,e,r;t=Ma,(e=Du())!==a&&zc()!==a&&Js()!==a&&zc()!==a&&(r=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ii())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=t,(n=r).forEach(t=>t.forEach(t=>t.table&&wl.add(`rename::${t.db}::${t.table}`))),e={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"rename",table:n}},t=e):(Ma=t,t=a);var n;return t}())===a&&(e=function(){var e,r,n;e=Ma,(r=function(){var e,r,n,o;e=Ma,"call"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Io));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="CALL"):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=nu())!==a?(qa=e,o=n,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"call",expr:o}},e=r):(Ma=e,e=a);var o;return e}())===a&&(e=function(){var e,r,n;e=Ma,(r=function(){var e,r,n,o;e=Ma,"use"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(kn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=rs())!==a?(qa=e,o=n,wl.add(`use::${o}::null`),r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"use",db:o}},e=r):(Ma=e,e=a);var o;return e}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=function(){var e,r,n,o;e=Ma,"alter"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(ae));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&Js()!==a&&zc()!==a&&(n=si())!==a&&zc()!==a&&(o=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Eu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Eu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Eu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=e,i=o,(u=n)&&u.length>0&&u.forEach(t=>wl.add(`alter::${t.db}::${t.table}`)),r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"alter",table:u,expr:i}},e=r):(Ma=e,e=a);var u,i;return e}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=Gs())!==a&&zc()!==a?((n=function(){var e,r,n,o;e=Ma,"global"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(sa));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="GLOBAL"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ma,"session"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(ca));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SESSION"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ma,"local"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(g));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="LOCAL"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ma,"persist"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(fa));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="PERSIST"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=function(){var e,r,n,o;e=Ma,"persist_only"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(pa));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="PERSIST_ONLY"):(Ma=e,e=a)):(Ma=e,e=a);return e}()),n===a&&(n=null),n!==a&&zc()!==a&&(o=function(){var t,e,r,n,o,u,i,c;if(t=Ma,(e=Ka())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=Ka())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=Ka())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);r!==a?(qa=t,e=s(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=e,u=n,(i=o).keyword=u,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"set",keyword:u,expr:i}},e=r):(Ma=e,e=a)):(Ma=e,e=a);var u,i;return e}())===a&&(e=function(){var e,r,n;e=Ma,(r=function(){var e,r,n,o;e=Ma,"lock"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(M));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&Xs()!==a&&zc()!==a&&(n=function(){var t,e,r,n,o,u,i,c;if(t=Ma,(e=wu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=wu())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=wu())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);r!==a?(qa=t,e=s(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=e,o=n,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"lock",keyword:"tables",tables:o}},e=r):(Ma=e,e=a);var o;return e}())===a&&(e=function(){var e,r;e=Ma,(r=function(){var e,r,n,o;e=Ma,"unlock"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Qn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&Xs()!==a?(qa=e,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"unlock",keyword:"tables"}},e=r):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o,u,i,c,l,f;e=Ma,(r=Qu())!==a&&zc()!==a?("binary"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(b)),n===a&&("master"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(v))),n!==a&&(o=zc())!==a?("logs"===t.substr(Ma,4).toLowerCase()?(u=t.substr(Ma,4),Ma+=4):(u=a,0===Fa&&Ya(h)),u!==a?(qa=e,p=n,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"show",suffix:"logs",keyword:p.toLowerCase()}},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var p;e===a&&(e=Ma,(r=Qu())!==a&&zc()!==a?("binlog"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(y)),n!==a&&(o=zc())!==a?("events"===t.substr(Ma,6).toLowerCase()?(u=t.substr(Ma,6),Ma+=6):(u=a,0===Fa&&Ya(d)),u!==a&&(i=zc())!==a?((c=Bi())===a&&(c=null),c!==a&&zc()!==a?((l=ui())===a&&(l=null),l!==a&&zc()!==a?((f=Ti())===a&&(f=null),f!==a?(qa=e,w=c,L=l,C=f,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"show",suffix:"events",keyword:"binlog",in:w,from:L,limit:C}},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=Qu())!==a&&zc()!==a?(n=Ma,"character"===t.substr(Ma,9).toLowerCase()?(o=t.substr(Ma,9),Ma+=9):(o=a,0===Fa&&Ya(m)),o!==a&&(u=zc())!==a?("set"===t.substr(Ma,3).toLowerCase()?(i=t.substr(Ma,3),Ma+=3):(i=a,0===Fa&&Ya(j)),i!==a?n=o=[o,u,i]:(Ma=n,n=a)):(Ma=n,n=a),n===a&&("collation"===t.substr(Ma,9).toLowerCase()?(n=t.substr(Ma,9),Ma+=9):(n=a,0===Fa&&Ya(O))),n!==a&&(o=zc())!==a?((u=Fi())===a&&(u=yi()),u===a&&(u=null),u!==a?(qa=e,r=function(t,e){let r=Array.isArray(t)&&t||[t];return{tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"show",suffix:r[2]&&r[2].toLowerCase(),keyword:r[0].toLowerCase(),expr:e}}}(n,u),e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=function(){var e,r,n,o;e=Ma,(r=Qu())!==a&&zc()!==a?("grants"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(_)),n!==a&&zc()!==a?((o=function(){var e,r,n,o,u,i,c;e=Ma,"for"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya($));r!==a&&zc()!==a&&(n=rs())!==a&&zc()!==a?(o=Ma,(u=Fu())!==a&&(i=zc())!==a&&(c=rs())!==a?o=u=[u,i,c]:(Ma=o,o=a),o===a&&(o=null),o!==a&&(u=zc())!==a?((i=function(){var t,e;t=Ma,nc()!==a&&zc()!==a&&(e=function(){var t,e,r,n,o,u,i,c;if(t=Ma,(e=rs())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=rs())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=rs())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);r!==a?(qa=t,e=s(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=t,t=e):(Ma=t,t=a);return t}())===a&&(i=null),i!==a?(qa=e,f=i,r={user:n,host:(l=o)&&l[2],role_list:f},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var l,f;return e}())===a&&(o=null),o!==a?(qa=e,u=o,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"show",keyword:"grants",for:u}},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var u;return e}())));var w,L,C;return e}())===a&&(e=function(){var e,r,n;e=Ma,(r=fc())===a&&(r=function(){var e,r,n,o;e=Ma,"describe"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Kt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}());r!==a&&zc()!==a&&(n=rs())!==a?(qa=e,o=n,r={tableList:Array.from(wl),columnList:jl(Ll),ast:{type:"desc",table:o}},e=r):(Ma=e,e=a);var o;return e}());return e}())===a&&(e=function(){var t,e;t=[],e=Xa();for(;e!==a;)t.push(e),e=Xa();return t}());return e}()),e}function Xa(){var e,r;return e=Ma,qa=Ma,Ol=[],(!0?void 0:a)!==a&&zc()!==a?((r=Ka())===a&&(r=function(){var e,r;e=Ma,function(){var e;"return"===t.substr(Ma,6).toLowerCase()?(e=t.substr(Ma,6),Ma+=6):(e=a,0===Fa&&Ya($t));return e}()!==a&&zc()!==a&&(r=za())!==a?(qa=e,e={type:"return",expr:r}):(Ma=e,e=a);return e}()),r!==a?(qa=e,e={stmt:r,vars:Ol}):(Ma=e,e=a)):(Ma=e,e=a),e}function Ka(){var e,r,n,o;return e=Ma,(r=fu())===a&&(r=pu()),r!==a&&zc()!==a?((n=function(){var e;":="===t.substr(Ma,2)?(e=":=",Ma+=2):(e=a,0===Fa&&Ya(Ht));return e}())===a&&(n=Mu()),n!==a&&zc()!==a&&(o=za())!==a?(qa=e,e=r={type:"assign",left:r,symbol:n,right:o}):(Ma=e,e=a)):(Ma=e,e=a),e}function za(){var t;return(t=Gu())===a&&(t=function(){var t,e,r,n,o;t=Ma,(e=fu())!==a&&zc()!==a&&(r=pi())!==a&&zc()!==a&&(n=fu())!==a&&zc()!==a&&(o=hi())!==a?(qa=t,t=e={type:"join",ltable:e,rtable:n,op:r,on:o}):(Ma=t,t=a);return t}())===a&&(t=tu())===a&&(t=function(){var t,e;t=Ma,Zc()!==a&&zc()!==a&&(e=ou())!==a&&zc()!==a&&Jc()!==a?(qa=t,t={type:"array",value:e,brackets:!0}):(Ma=t,t=a);return t}()),t}function tu(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=eu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=$i())!==a&&(i=zc())!==a&&(s=eu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=$i())!==a&&(i=zc())!==a&&(s=eu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=c(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function eu(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ru())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Yi())!==a&&(i=zc())!==a&&(s=ru())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Yi())!==a&&(i=zc())!==a&&(s=ru())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=c(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function ru(){var t,e,r;return(t=As())===a&&(t=fu())===a&&(t=nu())===a&&(t=ys())===a&&(t=Ma,$c()!==a&&zc()!==a&&(e=tu())!==a&&zc()!==a&&Wc()!==a?(qa=t,(r=e).parentheses=!0,t=r):(Ma=t,t=a)),t}function nu(){var t,e,r;return t=Ma,(e=ws())!==a&&zc()!==a&&$c()!==a&&zc()!==a?((r=ou())===a&&(r=null),r!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e={type:"function",name:e,args:{type:"expr_list",value:r},...pl()}):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=ws())!==a&&(qa=t,e=function(t){return{type:"function",name:t,args:null,...pl()}}(e)),t=e),t}function ou(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ru())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ru())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ru())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function au(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=uu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=uu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=uu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function uu(){var e,r,n,o,u,i,s,c,l;return e=Ma,r=Ma,(n=rs())!==a&&(o=zc())!==a&&(u=Fc())!==a?r=n=[n,o,u]:(Ma=r,r=a),r===a&&(r=null),r!==a&&(n=zc())!==a&&(o=ss())!==a&&(u=zc())!==a?(61===t.charCodeAt(Ma)?(i="=",Ma++):(i=a,0===Fa&&Ya(f)),i!==a&&zc()!==a&&(s=Hi())!==a?(qa=e,e=r={column:o,value:s,table:(l=r)&&l[0]}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,r=Ma,(n=rs())!==a&&(o=zc())!==a&&(u=Fc())!==a?r=n=[n,o,u]:(Ma=r,r=a),r===a&&(r=null),r!==a&&(n=zc())!==a&&(o=ss())!==a&&(u=zc())!==a?(61===t.charCodeAt(Ma)?(i="=",Ma++):(i=a,0===Fa&&Ya(f)),i!==a&&zc()!==a&&(s=rc())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(c=zi())!==a&&zc()!==a&&Wc()!==a?(qa=e,e=r=function(t,e,r){return{column:e,value:r,table:t&&t[0],keyword:"values"}}(r,o,c)):(Ma=e,e=a)):(Ma=e,e=a)),e}function iu(){var e,r;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"insert"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Bt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r="insert"),(e=r)===a&&(e=Ma,(r=qu())!==a&&(qa=e,r="replace"),e=r),e}function su(){var t,e,r,n,o,u,i,s,c;if(t=Ma,$s()!==a)if(zc()!==a)if((e=$c())!==a)if(zc()!==a)if((r=ps())!==a){for(n=[],o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=ps())!==a?o=u=[u,i,s,c]:(Ma=o,o=a);o!==a;)n.push(o),o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=ps())!==a?o=u=[u,i,s,c]:(Ma=o,o=a);n!==a&&(o=zc())!==a&&(u=Wc())!==a?(qa=t,t=yl(r,n)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;return t===a&&(t=Ma,$s()!==a&&zc()!==a&&(e=bu())!==a?(qa=t,t=e):(Ma=t,t=a)),t}function cu(){var t;return(t=function(){var t,e;t=Ma,rc()!==a&&zc()!==a&&(e=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=bu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=t,t=e):(Ma=t,t=a);return t}())===a&&(t=Ju()),t}function lu(){var e,r,n;return e=Ma,Ks()!==a&&zc()!==a?("duplicate"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(p)),r!==a&&zc()!==a&&Dc()!==a&&zc()!==a&&ku()!==a&&zc()!==a&&(n=au())!==a?(qa=e,e={keyword:"on duplicate key update",set:n}):(Ma=e,e=a)):(Ma=e,e=a),e}function fu(){var e,r,n,o,u;return e=Ma,(r=function(){var e;(e=function(){var e;"@@"===t.substr(Ma,2)?(e="@@",Ma+=2):(e=a,0===Fa&&Ya(te));return e}())===a&&(e=Fu())===a&&(e=function(){var e;36===t.charCodeAt(Ma)?(e="$",Ma++):(e=a,0===Fa&&Ya(ee));return e}());return e}())!==a&&(n=pu())!==a?(qa=e,o=r,u=n,e=r={type:"var",...u,prefix:o}):(Ma=e,e=a),e}function pu(){var e,r,n,o,u;return e=Ma,(r=ps())!==a&&(n=function(){var e,r,n,o,u;e=Ma,r=[],n=Ma,46===t.charCodeAt(Ma)?(o=".",Ma++):(o=a,0===Fa&&Ya(x));o!==a&&(u=ps())!==a?n=o=[o,u]:(Ma=n,n=a);for(;n!==a;)r.push(n),n=Ma,46===t.charCodeAt(Ma)?(o=".",Ma++):(o=a,0===Fa&&Ya(x)),o!==a&&(u=ps())!==a?n=o=[o,u]:(Ma=n,n=a);r!==a&&(qa=e,r=function(t){const e=[];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(r));return e=r}())!==a?(qa=e,o=r,u=n,Ol.push(o),e=r={type:"var",name:o,members:u,prefix:null}):(Ma=e,e=a),e===a&&(e=Ma,(r=ks())!==a&&(qa=e,r={type:"var",name:r.value,members:[],quoted:null,prefix:null}),e=r),e}function bu(){var t,e;return t=Ma,$c()!==a&&zc()!==a&&(e=Ei())!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e):(Ma=t,t=a),t}function vu(){var e,r;return e=Ma,"if"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(L)),r!==a&&zc()!==a&&jc()!==a&&zc()!==a&&mc()!==a?(qa=e,e=r="IF NOT EXISTS"):(Ma=e,e=a),e}function hu(){var e,r,n;return e=Ma,"check_option"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(T)),r!==a&&zc()!==a&&Mu()!==a&&zc()!==a?("cascaded"===t.substr(Ma,8).toLowerCase()?(n=t.substr(Ma,8),Ma+=8):(n=a,0===Fa&&Ya(C)),n===a&&("local"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(g))),n!==a?(qa=e,e=r={type:"check_option",value:n,symbol:"="}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"security_barrier"===t.substr(Ma,16).toLowerCase()?(r=t.substr(Ma,16),Ma+=16):(r=a,0===Fa&&Ya(E)),r===a&&("security_invoker"===t.substr(Ma,16).toLowerCase()?(r=t.substr(Ma,16),Ma+=16):(r=a,0===Fa&&Ya(U))),r!==a&&zc()!==a&&Mu()!==a&&zc()!==a&&(n=Us())!==a?(qa=e,e=r=function(t,e){return{type:t.toLowerCase(),value:e.value?"true":"false",symbol:"="}}(r,n)):(Ma=e,e=a)),e}function yu(){var e,r,n,o;return e=Ma,"algorithm"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(I)),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a?("default"===t.substr(Ma,7).toLowerCase()?(o=t.substr(Ma,7),Ma+=7):(o=a,0===Fa&&Ya(N)),o===a&&("instant"===t.substr(Ma,7).toLowerCase()?(o=t.substr(Ma,7),Ma+=7):(o=a,0===Fa&&Ya(k)),o===a&&("inplace"===t.substr(Ma,7).toLowerCase()?(o=t.substr(Ma,7),Ma+=7):(o=a,0===Fa&&Ya(R)),o===a&&("copy"===t.substr(Ma,4).toLowerCase()?(o=t.substr(Ma,4),Ma+=4):(o=a,0===Fa&&Ya(V))))),o!==a?(qa=e,e=r={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function du(){var e,r,n,o;return e=Ma,"lock"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(M)),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a?("default"===t.substr(Ma,7).toLowerCase()?(o=t.substr(Ma,7),Ma+=7):(o=a,0===Fa&&Ya(N)),o===a&&("none"===t.substr(Ma,4).toLowerCase()?(o=t.substr(Ma,4),Ma+=4):(o=a,0===Fa&&Ya(q)),o===a&&("shared"===t.substr(Ma,6).toLowerCase()?(o=t.substr(Ma,6),Ma+=6):(o=a,0===Fa&&Ya(P)),o===a&&("exclusive"===t.substr(Ma,9).toLowerCase()?(o=t.substr(Ma,9),Ma+=9):(o=a,0===Fa&&Ya(D))))),o!==a?(qa=e,e=r={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function mu(){var e;return(e=Ou())===a&&(e=function(){var t,e,r,n,o,u;t=Ma,(e=Pc())===a&&(e=Dc());e!==a&&zc()!==a?((r=ls())===a&&(r=null),r!==a&&zc()!==a?((n=Uu())===a&&(n=null),n!==a&&zc()!==a&&(o=_u())!==a&&zc()!==a?((u=xu())===a&&(u=null),u!==a&&zc()!==a?(qa=t,i=n,s=u,e={index:r,definition:o,keyword:e.toLowerCase(),index_type:i,resource:"index",index_options:s},t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);var i,s;return t}())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,(r=function(){var e,r,n,o;e=Ma,"fulltext"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(da));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="FULLTEXT"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(r=function(){var e,r,n,o;e=Ma,"spatial"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(ue));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}());r!==a&&zc()!==a?((n=Pc())===a&&(n=Dc()),n===a&&(n=null),n!==a&&zc()!==a?((o=ls())===a&&(o=null),o!==a&&zc()!==a&&(u=_u())!==a&&zc()!==a?((i=xu())===a&&(i=null),i!==a?(qa=e,s=r,l=i,r={index:o,definition:u,keyword:(c=n)&&`${s.toLowerCase()} ${c.toLowerCase()}`||s.toLowerCase(),index_options:l,resource:"index"},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var s,c,l;return e}()),e}function ju(){var e,r,n,o;return e=Ma,(r=function(){var e,r;e=Ma,(r=function(){var e,r,n,o;e=Ma,"not null"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Un));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={type:"not null",value:"not null"});return e=r}())===a&&(r=Es()),r!==a&&(qa=e,(o=r)&&!o.value&&(o.value="null"),r={nullable:o}),(e=r)===a&&(e=Ma,(r=function(){var t,e;t=Ma,Fs()!==a&&zc()!==a&&(e=_i())!==a?(qa=t,t={type:"default",value:e}):(Ma=t,t=a);return t}())!==a&&(qa=e,r={default_val:r}),(e=r)===a&&(e=Ma,"auto_increment"===t.substr(Ma,14).toLowerCase()?(r=t.substr(Ma,14),Ma+=14):(r=a,0===Fa&&Ya(Q)),r!==a&&(qa=e,r={auto_increment:r.toLowerCase()}),(e=r)===a&&(e=Ma,"unique"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(F)),r!==a&&zc()!==a?("key"===t.substr(Ma,3).toLowerCase()?(n=t.substr(Ma,3),Ma+=3):(n=a,0===Fa&&Ya(B)),n===a&&(n=null),n!==a?(qa=e,e=r=function(t){const e=["unique"];return t&&e.push(t),{unique:e.join(" ").toLowerCase("")}}(n)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"primary"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(H)),r===a&&(r=null),r!==a&&zc()!==a?("key"===t.substr(Ma,3).toLowerCase()?(n=t.substr(Ma,3),Ma+=3):(n=a,0===Fa&&Ya(B)),n!==a?(qa=e,e=r=function(t){const e=[];return t&&e.push("primary"),e.push("key"),{primary_key:e.join(" ").toLowerCase("")}}(r)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=Lu())!==a&&(qa=e,r={comment:r}),(e=r)===a&&(e=Ma,(r=Cu())!==a&&(qa=e,r={collate:r}),(e=r)===a&&(e=Ma,(r=function(){var e,r,n;e=Ma,"column_format"===t.substr(Ma,13).toLowerCase()?(r=t.substr(Ma,13),Ma+=13):(r=a,0===Fa&&Ya(W));r!==a&&zc()!==a?("fixed"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(Y)),n===a&&("dynamic"===t.substr(Ma,7).toLowerCase()?(n=t.substr(Ma,7),Ma+=7):(n=a,0===Fa&&Ya(G)),n===a&&("default"===t.substr(Ma,7).toLowerCase()?(n=t.substr(Ma,7),Ma+=7):(n=a,0===Fa&&Ya(N)))),n!==a?(qa=e,r={type:"column_format",value:n.toLowerCase()},e=r):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={column_format:r}),(e=r)===a&&(e=Ma,(r=function(){var e,r,n;e=Ma,"storage"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Z));r!==a&&zc()!==a?("disk"===t.substr(Ma,4).toLowerCase()?(n=t.substr(Ma,4),Ma+=4):(n=a,0===Fa&&Ya(J)),n===a&&("memory"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(X))),n!==a?(qa=e,r={type:"storage",value:n.toLowerCase()},e=r):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={storage:r}),(e=r)===a&&(e=Ma,(r=gu())!==a&&(qa=e,r={reference_definition:r}),e=r))))))))),e}function Ou(){var t,e,r,n,o,u,i;return t=Ma,(e=zi())!==a&&zc()!==a&&(r=il())!==a&&zc()!==a?((n=function(){var t,e,r,n,o,u;if(t=Ma,(e=ju())!==a)if(zc()!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=ju())!==a?n=o=[o,u]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=ju())!==a?n=o=[o,u]:(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){let r=t;for(let t=0;t<e.length;t++)r={...r,...e[t][1]};return r}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;return t}())===a&&(n=null),n!==a?(qa=t,o=e,u=r,i=n,Ll.add(`create::${o.table}::${o.column}`),t=e={column:o,definition:u,resource:"column",...i||{}}):(Ma=t,t=a)):(Ma=t,t=a),t}function wu(){var e,r,n,o,u;return e=Ma,(r=fi())!==a&&zc()!==a&&(n=function(){var e,r,n;e=Ma,"read"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(St));r!==a&&zc()!==a?("local"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(g)),n===a&&(n=null),n!==a?(qa=e,e=r={type:"read",suffix:n&&"local"}):(Ma=e,e=a)):(Ma=e,e=a);e===a&&(e=Ma,"low_priority"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(At)),r===a&&(r=null),r!==a&&zc()!==a?("write"===t.substr(Ma,5).toLowerCase()?(n=t.substr(Ma,5),Ma+=5):(n=a,0===Fa&&Ya(Tt)),n!==a?(qa=e,e=r={type:"write",prefix:r&&"low_priority"}):(Ma=e,e=a)):(Ma=e,e=a));return e}())!==a?(qa=e,o=r,u=n,wl.add(`lock::${o.db}::${o.table}`),e=r={table:o,lock_type:u}):(Ma=e,e=a),e}function Lu(){var t,e,r,n,o,u,i;return t=Ma,(e=Qc())!==a&&zc()!==a?((r=Mu())===a&&(r=null),r!==a&&zc()!==a&&(n=_s())!==a?(qa=t,u=r,i=n,t=e={type:(o=e).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:i}):(Ma=t,t=a)):(Ma=t,t=a),t}function Cu(){var e,r,n;return e=Ma,function(){var e,r,n,o;e=Ma,"collate"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(gt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="COLLATE"):(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a?((r=Mu())===a&&(r=null),r!==a&&zc()!==a&&(n=rs())!==a?(qa=e,e={type:"collate",keyword:"collate",collate:{name:n,symbol:r}}):(Ma=e,e=a)):(Ma=e,e=a),e}function gu(){var e,r,n,o,u,i,s,c,l,f;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"references"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(ja));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="REFERENCES"):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=si())!==a&&zc()!==a&&(o=_u())!==a&&zc()!==a?("match full"===t.substr(Ma,10).toLowerCase()?(u=t.substr(Ma,10),Ma+=10):(u=a,0===Fa&&Ya(K)),u===a&&("match partial"===t.substr(Ma,13).toLowerCase()?(u=t.substr(Ma,13),Ma+=13):(u=a,0===Fa&&Ya(z)),u===a&&("match simple"===t.substr(Ma,12).toLowerCase()?(u=t.substr(Ma,12),Ma+=12):(u=a,0===Fa&&Ya(tt)))),u===a&&(u=null),u!==a&&zc()!==a?((i=Nu())===a&&(i=null),i!==a&&zc()!==a?((s=Nu())===a&&(s=null),s!==a?(qa=e,c=u,l=i,f=s,e=r={definition:o,table:n,keyword:r.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=Nu())!==a&&(qa=e,r={on_action:[r]}),e=r),e}function Su(){var e,r,n,o;return e=Ma,"expiration_timestamp"===t.substr(Ma,20).toLowerCase()?(r=t.substr(Ma,20),Ma+=20):(r=a,0===Fa&&Ya(et)),r===a&&("partition_expiration_days"===t.substr(Ma,25).toLowerCase()?(r=t.substr(Ma,25),Ma+=25):(r=a,0===Fa&&Ya(rt)),r===a&&("require_partition_filter"===t.substr(Ma,24).toLowerCase()?(r=t.substr(Ma,24),Ma+=24):(r=a,0===Fa&&Ya(nt)),r===a&&("kms_key_name"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(ot)),r===a&&("friendly_name"===t.substr(Ma,13).toLowerCase()?(r=t.substr(Ma,13),Ma+=13):(r=a,0===Fa&&Ya(at)),r===a&&("description"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(ut)),r===a&&("labels"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(it)),r===a&&("default_rounding_mode"===t.substr(Ma,21).toLowerCase()?(r=t.substr(Ma,21),Ma+=21):(r=a,0===Fa&&Ya(st))))))))),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a&&(o=_i())!==a?(qa=e,e=r={keyword:r,symbol:"=",value:o}):(Ma=e,e=a)):(Ma=e,e=a),e}function Au(){var e,r,n,o,u,i,c,l,f;return e=Ma,"auto_increment"===t.substr(Ma,14).toLowerCase()?(r=t.substr(Ma,14),Ma+=14):(r=a,0===Fa&&Ya(Q)),r===a&&("avg_row_length"===t.substr(Ma,14).toLowerCase()?(r=t.substr(Ma,14),Ma+=14):(r=a,0===Fa&&Ya(ct)),r===a&&("key_block_size"===t.substr(Ma,14).toLowerCase()?(r=t.substr(Ma,14),Ma+=14):(r=a,0===Fa&&Ya(lt)),r===a&&("max_rows"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(ft)),r===a&&("min_rows"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(pt)),r===a&&("stats_sample_pages"===t.substr(Ma,18).toLowerCase()?(r=t.substr(Ma,18),Ma+=18):(r=a,0===Fa&&Ya(bt))))))),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a&&(o=ks())!==a?(qa=e,l=n,f=o,e=r={keyword:r.toLowerCase(),symbol:l,value:f.value}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Tu())===a&&(e=Ma,(r=Qc())===a&&("connection"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(vt))),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a&&(o=_s())!==a?(qa=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:`'${r.value}'`}}(r,n,o)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"compression"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(ht)),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a?(o=Ma,39===t.charCodeAt(Ma)?(u="'",Ma++):(u=a,0===Fa&&Ya(yt)),u!==a?("zlib"===t.substr(Ma,4).toLowerCase()?(i=t.substr(Ma,4),Ma+=4):(i=a,0===Fa&&Ya(dt)),i===a&&("lz4"===t.substr(Ma,3).toLowerCase()?(i=t.substr(Ma,3),Ma+=3):(i=a,0===Fa&&Ya(mt)),i===a&&("none"===t.substr(Ma,4).toLowerCase()?(i=t.substr(Ma,4),Ma+=4):(i=a,0===Fa&&Ya(q)))),i!==a?(39===t.charCodeAt(Ma)?(c="'",Ma++):(c=a,0===Fa&&Ya(yt)),c!==a?o=u=[u,i,c]:(Ma=o,o=a)):(Ma=o,o=a)):(Ma=o,o=a),o!==a?(qa=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.join("").toUpperCase()}}(r,n,o)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"engine"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(jt)),r!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a&&(o=ps())!==a?(qa=e,e=r=function(t,e,r){return{keyword:t.toLowerCase(),symbol:e,value:r.toUpperCase()}}(r,n,o)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=$s())!==a&&zc()!==a&&(n=ac())!==a&&zc()!==a&&(o=_i())!==a?(qa=e,e=r=function(t){return{keyword:"partition by",value:t}}(o)):(Ma=e,e=a),e===a&&(e=Ma,"cluster"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Ot)),r!==a&&zc()!==a?("by"===t.substr(Ma,2).toLowerCase()?(n=t.substr(Ma,2),Ma+=2):(n=a,0===Fa&&Ya(wt)),n!==a&&zc()!==a&&(o=ts())!==a?(qa=e,e=r={keyword:"cluster by",value:o}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"options"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Lt)),r!==a&&zc()!==a&&(n=$c())!==a&&zc()!==a&&(o=function(){var t,e,r,n,o,u,i,c;if(t=Ma,(e=Su())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=Su())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(c=Su())!==a?n=o=[o,u,i,c]:(Ma=n,n=a);r!==a?(qa=t,t=e=s(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a&&(u=zc())!==a&&(i=Wc())!==a?(qa=e,e=r=function(t){return{keyword:"options",parentheses:!0,value:t}}(o)):(Ma=e,e=a))))))),e}function Tu(){var e,r,n,o,u,i,s,c,l;return e=Ma,(r=Fs())===a&&(r=null),r!==a&&zc()!==a?((n=function(){var e,r,n;e=Ma,"character"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(m));r!==a&&zc()!==a?("set"===t.substr(Ma,3).toLowerCase()?(n=t.substr(Ma,3),Ma+=3):(n=a,0===Fa&&Ya(j)),n!==a?(qa=e,e=r="CHARACTER SET"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&("charset"===t.substr(Ma,7).toLowerCase()?(n=t.substr(Ma,7),Ma+=7):(n=a,0===Fa&&Ya(Ct)),n===a&&("collate"===t.substr(Ma,7).toLowerCase()?(n=t.substr(Ma,7),Ma+=7):(n=a,0===Fa&&Ya(gt)))),n!==a&&zc()!==a?((o=Mu())===a&&(o=null),o!==a&&zc()!==a&&(u=es())!==a?(qa=e,s=n,c=o,l=u,e=r={keyword:(i=r)&&`${i[0].toLowerCase()} ${s.toLowerCase()}`||s.toLowerCase(),symbol:c,value:l}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function Eu(){var e;return(e=function(){var e,r,n,o;e=Ma,(r=function(){var e,r,n,o;e=Ma,"add"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(va));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ADD"):(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=qc())===a&&(n=null),n!==a&&zc()!==a&&(o=Ou())!==a?(qa=e,u=n,i=o,r={action:"add",...i,keyword:u,resource:"column",type:"alter"},e=r):(Ma=e,e=a)):(Ma=e,e=a);var u,i;return e}())===a&&(e=function(){var t,e,r;t=Ma,Hs()!==a&&zc()!==a?((e=qc())===a&&(e=null),e!==a&&zc()!==a&&(r=zi())!==a?(qa=t,t={action:"drop",column:r,keyword:e,resource:"column",type:"alter"}):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(e=function(){var t,e,r,n;t=Ma,(e=Du())!==a&&zc()!==a?((r=Bs())===a&&(r=Zs()),r===a&&(r=null),r!==a&&zc()!==a&&(n=rs())!==a?(qa=t,u=n,e={action:"rename",type:"alter",resource:"table",keyword:(o=r)&&o[0].toLowerCase(),table:u},t=e):(Ma=t,t=a)):(Ma=t,t=a);var o,u;return t}()),e}function Uu(){var e,r;return e=Ma,nc()!==a&&zc()!==a?("btree"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Ut)),r===a&&("hash"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(_t))),r!==a?(qa=e,e={keyword:"using",type:r.toLowerCase()}):(Ma=e,e=a)):(Ma=e,e=a),e}function _u(){var t,e,r,n,o,u,i,s;if(t=Ma,$c()!==a)if(zc()!==a)if((e=ls())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ls())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ls())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a&&(n=zc())!==a&&(o=Wc())!==a?(qa=t,t=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;return t}function xu(){var t,e,r,n,o,u;if(t=Ma,(e=Iu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Iu())!==a?n=o=[o,u]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Iu())!==a?n=o=[o,u]:(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){const r=[t];for(let t=0;t<e.length;t++)r.push(e[t][1]);return r}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Iu(){var e,r,n,o,u,i;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"key_block_size"===t.substr(Ma,14).toLowerCase()?(r=t.substr(Ma,14),Ma+=14):(r=a,0===Fa&&Ya(lt));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=Mu())===a&&(n=null),n!==a&&zc()!==a&&(o=ks())!==a?(qa=e,u=n,i=o,e=r={type:r.toLowerCase(),symbol:u,expr:i}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Uu())===a&&(e=Ma,"with"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(xt)),r!==a&&zc()!==a?("parser"===t.substr(Ma,6).toLowerCase()?(n=t.substr(Ma,6),Ma+=6):(n=a,0===Fa&&Ya(It)),n!==a&&zc()!==a&&(o=ps())!==a?(qa=e,e=r={type:"with parser",expr:o}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"visible"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Nt)),r===a&&("invisible"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(kt))),r!==a&&(qa=e,r=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(r)),(e=r)===a&&(e=Lu()))),e}function Nu(){var e,r,n,o;return e=Ma,Ks()!==a&&zc()!==a?((r=Vu())===a&&(r=ku()),r!==a&&zc()!==a&&(n=function(){var e,r,n;e=Ma,(r=Mc())!==a&&zc()!==a&&$c()!==a&&zc()!==a?((n=Ei())===a&&(n=null),n!==a&&zc()!==a&&Wc()!==a?(qa=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},args:n}):(Ma=e,e=a)):(Ma=e,e=a);e===a&&(e=Ma,"restrict"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Rt)),r===a&&("cascade"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Vt)),r===a&&("set null"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Mt)),r===a&&("no action"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(qt)),r===a&&("set default"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(Pt)),r===a&&(r=Mc()))))),r!==a&&(qa=e,r={type:"origin",value:r.toLowerCase()}),e=r);return e}())!==a?(qa=e,o=n,e={type:"on "+r[0].toLowerCase(),value:o}):(Ma=e,e=a)):(Ma=e,e=a),e}function ku(){var e,r,n,o;return e=Ma,"update"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Dt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Ru(){var e,r,n,o;return e=Ma,"create"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Qt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Vu(){var e,r,n,o;return e=Ma,"delete"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Ft)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Mu(){var e;return 61===t.charCodeAt(Ma)?(e="=",Ma++):(e=a,0===Fa&&Ya(f)),e}function qu(){var e,r,n,o;return e=Ma,"replace"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Wt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Pu(){var e,r,n,o;return e=Ma,"database"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Zt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Du(){var e,r,n,o;return e=Ma,"rename"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Jt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Qu(){var e,r,n,o;return e=Ma,"show"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Xt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Fu(){var e;return 64===t.charCodeAt(Ma)?(e="@",Ma++):(e=a,0===Fa&&Ya(zt)),e}function Bu(){var e,r,n,o;return e=Ma,"temporary"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(re)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Hu(){var e,r,n,o;return e=Ma,"temp"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(ne)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function $u(){var e,r,n,o;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"union"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(zn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=pc())===a&&(n=bc()),n===a&&(n=null),n!==a?(qa=e,e=r=(o=n)?"union "+o.toLowerCase():"union"):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,"intersect"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(ce)),r===a&&("except"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(le))),r!==a&&zc()!==a&&(n=bc())!==a?(qa=e,e=r=function(t,e){return`${t.toLowerCase()} ${e.toLowerCase()}`}(r,n)):(Ma=e,e=a)),e}function Wu(){var e,r,n,o,u,i,s;return(e=Yu())===a&&(e=Ma,r=Ma,40===t.charCodeAt(Ma)?(n="(",Ma++):(n=a,0===Fa&&Ya(ie)),n!==a&&(o=zc())!==a&&(u=Yu())!==a&&(i=zc())!==a?(41===t.charCodeAt(Ma)?(s=")",Ma++):(s=a,0===Fa&&Ya(se)),s!==a?r=n=[n,o,u,i,s]:(Ma=r,r=a)):(Ma=r,r=a),r!==a&&(qa=e,r={...r[2],_parentheses:!0}),e=r),e}function Yu(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Gu())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=$u())===a&&(u=null),u!==a&&(i=zc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=$u())===a&&(u=null),u!==a&&(i=zc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a&&(n=zc())!==a?((o=gi())===a&&(o=null),o!==a&&(u=zc())!==a?((i=Ti())===a&&(i=null),i!==a?(qa=t,t=e=function(t,e,r,n){let o=t;for(let t=0;t<e.length;t++)o._next=e[t][3],o.set_op=e[t][1],o=o._next;return{tableList:Array.from(wl),columnList:jl(Ll),ast:t}}(e,r)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Gu(){var e,r,n,o,u,i,s;return(e=Ju())===a&&(e=Ma,r=Ma,40===t.charCodeAt(Ma)?(n="(",Ma++):(n=a,0===Fa&&Ya(ie)),n!==a&&(o=zc())!==a&&(u=Gu())!==a&&(i=zc())!==a?(41===t.charCodeAt(Ma)?(s=")",Ma++):(s=a,0===Fa&&Ya(se)),s!==a?r=n=[n,o,u,i,s]:(Ma=r,r=a)):(Ma=r,r=a),r!==a&&(qa=e,r={...r[2],parentheses_symbol:!0}),e=r),e}function Zu(){var t,e,r,n;return t=Ma,(e=_s())===a&&(e=ps()),e!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(r=Wu())!==a&&zc()!==a&&Wc()!==a?(qa=t,"string"==typeof(n=e)&&(n={type:"default",value:n}),t=e={name:n,stmt:r}):(Ma=t,t=a),t}function Ju(){var e,r,n,o,u,i,s,c,f,p,b,v,h,y,d,m,j,O,w,L,C,g,S,A,T,E,U;return e=Ma,zc()!==a?((r=function(){var t,e,r,n,o,u,i,s;if(t=Ma,oc()!==a)if(zc()!==a)if((e=Zu())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;return t}())===a&&(r=null),r!==a&&zc()!==a&&function(){var e,r,n,o;e=Ma,"select"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Rn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&tl()!==a?((n=function(){var e,r,n;e=Ma,(r=Zs())!==a&&zc()!==a?((n=_c())===a&&(n=function(){var e,r,n,o;e=Ma,"value"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(to));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="VALUE"):(Ma=e,e=a)):(Ma=e,e=a);return e}()),n!==a?(qa=e,o=n,r=`${r[0].toLowerCase()} ${o.toLowerCase()}`,e=r):(Ma=e,e=a)):(Ma=e,e=a);var o;return e}())===a&&(n=null),n!==a&&zc()!==a?((o=pc())===a&&(o=bc()),o===a&&(o=null),o!==a&&zc()!==a&&(u=Ku())!==a&&zc()!==a?((i=ui())===a&&(i=null),i!==a&&zc()!==a?((s=function(){var e,r,n,o,u,i;e=Ma,"for"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya($));r!==a&&zc()!==a?("system_time"===t.substr(Ma,11).toLowerCase()?(n=t.substr(Ma,11),Ma+=11):(n=a,0===Fa&&Ya(fe)),n!==a&&zc()!==a?("as"===t.substr(Ma,2).toLowerCase()?(o=t.substr(Ma,2),Ma+=2):(o=a,0===Fa&&Ya(pe)),o!==a&&zc()!==a?("of"===t.substr(Ma,2).toLowerCase()?(u=t.substr(Ma,2),Ma+=2):(u=a,0===Fa&&Ya(be)),u!==a&&zc()!==a&&(i=_i())!==a?(qa=e,e=r={keyword:"for system_time as of",expr:i}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(s=null),s!==a&&zc()!==a?((c=yi())===a&&(c=null),c!==a&&zc()!==a?((f=function(){var e,r,n;e=Ma,(r=function(){var e,r,n,o;e=Ma,"group"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(oo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&ac()!==a&&zc()!==a&&(n=Ei())!==a?(qa=e,r={columns:n.value},e=r):(Ma=e,e=a);return e}())===a&&(f=null),f!==a&&zc()!==a?((p=function(){var t,e;t=Ma,uc()!==a&&zc()!==a&&(e=Vi())!==a?(qa=t,t=e):(Ma=t,t=a);return t}())===a&&(p=null),p!==a&&zc()!==a?((b=function(){var e,r;e=Ma,function(){var e,r,n,o;e=Ma,"qualify"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(io));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=_i())!==a?(qa=e,e=r):(Ma=e,e=a);return e}())===a&&(b=null),b!==a&&zc()!==a?((v=gi())===a&&(v=null),v!==a&&zc()!==a?((h=Ti())===a&&(h=null),h!==a&&zc()!==a?((y=function(){var e,r;e=Ma,function(){var e,r,n,o;e=Ma,"window"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(so));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=di())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=di())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=di())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=e,e={keyword:"window",type:"window",expr:r}):(Ma=e,e=a);return e}())===a&&(y=null),y!==a?(qa=e,d=r,m=n,j=o,O=u,w=i,L=s,C=c,g=f,S=p,A=b,T=v,E=h,U=y,Array.isArray(w)&&w.forEach(t=>t.table&&wl.add(`select::${t.db}::${t.table}`)),e={type:"select",as_struct_val:m,distinct:j,columns:O,from:w,for_sys_time_as_of:L,where:C,with:d,groupby:g,having:S,qualify:A,orderby:T,limit:E,window:U,...pl()}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function Xu(){var t,e,r;return t=Ma,(e=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=_i())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc())===a&&(u=Kc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc())===a&&(u=Kc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,e=function(t,e){const r=t.ast;if(r&&"select"===r.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==r.columns.length||"*"===r.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!e||0===e.length)return t;const n=e.length;let o=e[n-1][3];for(let r=n-1;r>=0;r--){const n=0===r?t:e[r-1][3];o=vl(e[r][1],n,o)}return o}(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a&&zc()!==a?((r=oi())===a&&(r=null),r!==a?(qa=t,t=e={expr:e,as:r,...pl()}):(Ma=t,t=a)):(Ma=t,t=a),t}function Ku(){var t,e,r;return t=Ma,(e=zu())!==a&&zc()!==a?((r=Bc())===a&&(r=null),r!==a?(qa=t,t=e=e):(Ma=t,t=a)):(Ma=t,t=a),t}function zu(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ni())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ni())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ni())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function ti(){var t,e;return t=Ma,Zc()!==a&&zc()!==a?((e=ks())===a&&(e=_s()),e!==a&&zc()!==a&&Jc()!==a?(qa=t,t={value:e}):(Ma=t,t=a)):(Ma=t,t=a),t}function ei(){var t,e,r,n,o,u,i,s,c,l,f,p,b,v;if(t=Ma,e=[],(r=ti())!==a)for(;r!==a;)e.push(r),r=ti();else e=a;if(e!==a&&(qa=t,e=e),(t=e)===a){if(t=Ma,e=[],r=Ma,(n=Zc())!==a&&(o=zc())!==a?((u=cc())===a&&(u=ic())===a&&(u=lc())===a&&(u=sc()),u!==a&&(i=zc())!==a&&(s=$c())!==a&&(c=zc())!==a?((l=ks())===a&&(l=_s()),l!==a&&(f=zc())!==a&&(p=Wc())!==a&&(b=zc())!==a&&(v=Jc())!==a?r=n=[n,o,u,i,s,c,l,f,p,b,v]:(Ma=r,r=a)):(Ma=r,r=a)):(Ma=r,r=a),r!==a)for(;r!==a;)e.push(r),r=Ma,(n=Zc())!==a&&(o=zc())!==a?((u=cc())===a&&(u=ic())===a&&(u=lc())===a&&(u=sc()),u!==a&&(i=zc())!==a&&(s=$c())!==a&&(c=zc())!==a?((l=ks())===a&&(l=_s()),l!==a&&(f=zc())!==a&&(p=Wc())!==a&&(b=zc())!==a&&(v=Jc())!==a?r=n=[n,o,u,i,s,c,l,f,p,b,v]:(Ma=r,r=a)):(Ma=r,r=a)):(Ma=r,r=a);else e=a;e!==a&&(qa=t,e=e.map(t=>({name:t[2],value:t[6]}))),t=e}return t}function ri(){var t,e,r;return t=Ma,(e=_i())!==a&&zc()!==a&&(r=ei())!==a?(qa=t,t=e={expr:e,offset:r}):(Ma=t,t=a),t}function ni(){var e,r,n,o,u,i,s,c,l,f,p,b;return e=Ma,r=Ma,(n=ss())!==a&&(o=zc())!==a&&(u=Fc())!==a?r=n=[n,o,u]:(Ma=r,r=a),r===a&&(r=null),r!==a&&(n=Hc())!==a&&(o=zc())!==a?("except"===t.substr(Ma,6).toLowerCase()?(u=t.substr(Ma,6),Ma+=6):(u=a,0===Fa&&Ya(le)),u===a&&("replace"===t.substr(Ma,7).toLowerCase()?(u=t.substr(Ma,7),Ma+=7):(u=a,0===Fa&&Ya(Wt))),u!==a&&(i=zc())!==a&&(s=$c())!==a&&(c=zc())!==a&&(l=zu())!==a&&zc()!==a&&Wc()!==a?(qa=e,e=r=function(t,e,r){const n=t&&t[0];return Ll.add(`select::${n}::(.*)`),{expr_list:r,parentheses:!0,expr:{type:"column_ref",table:n,column:"*"},type:e.toLowerCase(),...pl()}}(r,u,l)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=pc())===a&&(r=Ma,(n=Hc())!==a?(o=Ma,Fa++,u=bs(),Fa--,u===a?o=void 0:(Ma=o,o=a),o!==a?r=n=[n,o]:(Ma=r,r=a)):(Ma=r,r=a),r===a&&(r=Hc())),r!==a&&(qa=e,r=function(t){Ll.add("select::null::(.*)");return{expr:{type:"column_ref",table:null,column:"*"},as:null,...pl()}}()),(e=r)===a&&(e=Ma,(r=ss())!==a&&(n=zc())!==a&&(o=Fc())!==a?(u=Ma,(i=ri())===a&&(i=ss()),i!==a&&(s=zc())!==a&&(c=Fc())!==a?u=i=[i,s,c]:(Ma=u,u=a),u===a&&(u=null),u!==a&&(i=zc())!==a&&(s=Hc())!==a?(qa=e,e=r=function(t,e){Ll.add(`select::${t}::(.*)`);let r="*";const n=e&&e[0];return"string"==typeof n&&(r=n+".*"),n&&n.expr&&n.offset&&(r={...n,suffix:".*"}),{expr:{type:"column_ref",table:t,column:r},as:null,...pl()}}(r,u)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=ri())!==a&&(n=zc())!==a?(o=Ma,(u=Fc())!==a&&(i=zc())!==a&&(s=ss())!==a?o=u=[u,i,s]:(Ma=o,o=a),o===a&&(o=null),o!==a&&(u=zc())!==a?((i=oi())===a&&(i=null),i!==a?(qa=e,f=r,b=i,(p=o)&&(f.suffix="."+p[2]),e=r={expr:{type:"column_ref",table:null,column:f},as:b,...pl()}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Xu())))),e}function oi(){var t,e,r;return t=Ma,(e=Zs())!==a&&zc()!==a&&(r=function(){var t,e;t=Ma,(e=fs())!==a?(qa=Ma,(function(t){if(!0===ll[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(e)?a:void 0)!==a?(qa=t,t=e=e):(Ma=t,t=a)):(Ma=t,t=a);t===a&&(t=Ma,(e=ns())!==a&&(qa=t,e=e),t=e);return t}())!==a?(qa=t,t=e=r):(Ma=t,t=a),t===a&&(t=Ma,(e=Zs())===a&&(e=null),e!==a&&zc()!==a&&(r=ls())!==a?(qa=t,t=e=r):(Ma=t,t=a)),t}function ai(){var e,r,n,o,u;return e=Ma,"unnest"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(ve)),r!==a&&zc()!==a&&$c()!==a&&zc()!==a?((n=_i())===a&&(n=null),n!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((o=oi())===a&&(o=null),o!==a&&zc()!==a?((u=function(){var t,e;t=Ma,oc()!==a&&zc()!==a&&cc()!==a&&zc()!==a?((e=oi())===a&&(e=null),e!==a?(qa=t,t={keyword:"with offset as",as:e}):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(u=null),u!==a?(qa=e,e=r={type:"unnest",expr:n,parentheses:!0,as:o,with_offset:u}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function ui(){var e,r,n,o,u;return e=Ma,Ys()!==a&&zc()!==a&&(r=si())!==a&&zc()!==a?((n=function(){var e,r,n,o,u,i;e=Ma,function(){var e,r,n,o;e=Ma,"pivot"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(la));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="PIVOT"):(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(r=function(){var t,e,r,n,o,u,i,s,c,l,f;if(t=Ma,(e=ds())!==a)if(zc()!==a)if((r=oi())===a&&(r=null),r!==a){for(n=[],o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=ds())!==a&&(l=zc())!==a?((f=oi())===a&&(f=null),f!==a?o=u=[u,i,s,c,l,f]:(Ma=o,o=a)):(Ma=o,o=a);o!==a;)n.push(o),o=Ma,(u=zc())!==a&&(i=Bc())!==a&&(s=zc())!==a&&(c=ds())!==a&&(l=zc())!==a?((f=oi())===a&&(f=null),f!==a?o=u=[u,i,s,c,l,f]:(Ma=o,o=a)):(Ma=o,o=a);n!==a?(qa=t,e=function(t,e,r){const n={type:"expr_list"};return n.value=yl(t,r),n}(e,0,n),t=e):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;return t}())!==a&&zc()!==a?("for"===t.substr(Ma,3).toLowerCase()?(n=t.substr(Ma,3),Ma+=3):(n=a,0===Fa&&Ya($)),n!==a&&zc()!==a&&(o=zi())!==a&&zc()!==a&&(u=Bi())!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((i=oi())===a&&(i=null),i!==a?(qa=e,s=r,c=o,f=i,(l=u).operator="=",e={type:"pivot",expr:s,column:c,in_expr:l,as:f}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);var s,c,l,f;return e}())===a&&(n=null),n!==a?(qa=e,u=n,(o=r)[0]&&(o[0].operator=u),e=o):(Ma=e,e=a)):(Ma=e,e=a),e}function ii(){var t,e,r;return t=Ma,(e=bi())!==a&&zc()!==a&&Bs()!==a&&zc()!==a&&(r=bi())!==a?(qa=t,t=e=[e,r]):(Ma=t,t=a),t}function si(){var t,e,r,n;if(t=Ma,(e=fi())!==a){for(r=[],n=ci();n!==a;)r.push(n),n=ci();r!==a?(qa=t,t=e=he(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function ci(){var t,e,r;return t=Ma,zc()!==a&&(e=Bc())!==a&&zc()!==a&&(r=fi())!==a?(qa=t,t=r):(Ma=t,t=a),t===a&&(t=Ma,zc()!==a&&(e=function(){var t,e,r,n,o,u,i,s,c,l,f;if(t=Ma,(e=pi())!==a)if(zc()!==a)if((r=fi())!==a)if(zc()!==a)if((n=nc())!==a)if(zc()!==a)if($c()!==a)if(zc()!==a)if((o=es())!==a){for(u=[],i=Ma,(s=zc())!==a&&(c=Bc())!==a&&(l=zc())!==a&&(f=es())!==a?i=s=[s,c,l,f]:(Ma=i,i=a);i!==a;)u.push(i),i=Ma,(s=zc())!==a&&(c=Bc())!==a&&(l=zc())!==a&&(f=es())!==a?i=s=[s,c,l,f]:(Ma=i,i=a);u!==a&&(i=zc())!==a&&(s=Wc())!==a?(qa=t,p=e,v=o,h=u,(b=r).join=p,b.using=yl(v,h),t=e=b):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;else Ma=t,t=a;var p,b,v,h;t===a&&(t=Ma,(e=pi())!==a&&zc()!==a&&(r=fi())!==a&&zc()!==a?((n=hi())===a&&(n=null),n!==a?(qa=t,e=function(t,e,r){return e.join=t,e.on=r,e}(e,r,n),t=e):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=pi())===a&&(e=$u()),e!==a&&zc()!==a&&(r=$c())!==a&&zc()!==a&&(n=Wu())!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((o=oi())===a&&(o=null),o!==a&&(u=zc())!==a?((i=hi())===a&&(i=null),i!==a?(qa=t,e=function(t,e,r,n){return e.parentheses=!0,{expr:e,as:r,join:t,on:n}}(e,n,o,i),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)));return t}())!==a?(qa=t,t=e):(Ma=t,t=a)),t}function li(){var e,r,n,o,u,i,s,c,l,f,p,b;return e=Ma,"tablesample"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(ge)),r!==a&&(n=zc())!==a?("bernoulli"===t.substr(Ma,9).toLowerCase()?(o=t.substr(Ma,9),Ma+=9):(o=a,0===Fa&&Ya(Se)),o===a&&("reservoir"===t.substr(Ma,9).toLowerCase()?(o=t.substr(Ma,9),Ma+=9):(o=a,0===Fa&&Ya(Ae))),o!==a&&(u=zc())!==a?(40===t.charCodeAt(Ma)?(i="(",Ma++):(i=a,0===Fa&&Ya(ie)),i!==a&&(s=zc())!==a&&(c=Rs())!==a&&(l=zc())!==a?("percent"===t.substr(Ma,7).toLowerCase()?(f=t.substr(Ma,7),Ma+=7):(f=a,0===Fa&&Ya(Te)),f===a&&("rows"===t.substr(Ma,4).toLowerCase()?(f=t.substr(Ma,4),Ma+=4):(f=a,0===Fa&&Ya(Ee))),f!==a&&(p=zc())!==a?(41===t.charCodeAt(Ma)?(b=")",Ma++):(b=a,0===Fa&&Ya(se)),b!==a?e=r=[r,n,o,u,i,s,c,l,f,p,b]:(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function fi(){var e,r,n,o,u,i,s,c;return(e=ai())===a&&(e=Ma,(r=Os())!==a&&(n=zc())!==a?((o=oi())===a&&(o=null),o!==a?(qa=e,e=r={type:"expr",expr:r,as:o}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=bi())!==a?((n=function(){var e,r,n,o,u,i,s,c,l,f,p;return e=Ma,ye.test(t.charAt(Ma))?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(de)),r!==a?(me.test(t.charAt(Ma))?(n=t.charAt(Ma),Ma++):(n=a,0===Fa&&Ya(je)),n!==a&&(o=zc())!==a&&(u=ps())!==a&&(i=zc())!==a?(Oe.test(t.charAt(Ma))?(s=t.charAt(Ma),Ma++):(s=a,0===Fa&&Ya(we)),s!==a&&(c=zc())!==a&&(l=ps())!==a&&(f=zc())!==a?(Le.test(t.charAt(Ma))?(p=t.charAt(Ma),Ma++):(p=a,0===Fa&&Ya(Ce)),p!==a?e=r=[r,n,o,u,i,s,c,l,f,p]:(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}())===a&&(n=null),n!==a&&(o=zc())!==a?((u=li())===a&&(u=null),u!==a&&zc()!==a?((i=oi())===a&&(i=null),i!==a?(qa=e,e=r=function(t,e,r,n){return"var"===t.type?(t.as=n,t):{...t,as:n,...pl()}}(r,0,0,i)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=$c())!==a&&(n=zc())!==a&&(o=Wu())!==a&&(u=zc())!==a&&Wc()!==a&&(i=zc())!==a?((s=li())===a&&(s=null),s!==a&&zc()!==a?((c=oi())===a&&(c=null),c!==a?(qa=e,e=r=function(t,e,r){return t.parentheses=!0,{expr:t,as:r,...pl()}}(o,0,c)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)))),e}function pi(){var e,r,n;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"left"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya($n));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=tc())===a&&(n=null),n!==a&&zc()!==a&&zs()!==a?(qa=e,e=r="LEFT JOIN"):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=function(){var e,r,n,o;e=Ma,"right"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Wn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=tc())===a&&(n=null),n!==a&&zc()!==a&&zs()!==a?(qa=e,e=r="RIGHT JOIN"):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=function(){var e,r,n,o;e=Ma,"full"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Yn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a?((n=tc())===a&&(n=null),n!==a&&zc()!==a&&zs()!==a?(qa=e,e=r="FULL JOIN"):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=function(){var e,r,n,o;e=Ma,"cross"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Zn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&zc()!==a&&(n=zs())!==a?(qa=e,e=r=r[0].toUpperCase()+" JOIN"):(Ma=e,e=a),e===a&&(e=Ma,(r=function(){var e,r,n,o;e=Ma,"inner"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Gn));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(r=null),r!==a&&zc()!==a&&(n=zs())!==a?(qa=e,e=r=function(t){return t?t[0].toUpperCase()+" JOIN":"JOIN"}(r)):(Ma=e,e=a))))),e}function bi(){var t,e,r,n,o,u,i,s;return t=Ma,(e=cs())!==a?(r=Ma,(n=zc())!==a&&(o=Fc())!==a&&(u=zc())!==a&&(i=cs())!==a?r=n=[n,o,u,i]:(Ma=r,r=a),r!==a?(n=Ma,(o=zc())!==a&&(u=Fc())!==a&&(i=zc())!==a&&(s=cs())!==a?n=o=[o,u,i,s]:(Ma=n,n=a),n!==a?(qa=t,t=e=function(t,e,r){const n={db:null,table:t};return null!==r&&(n.db=t,n.catalog=t,n.schema=e[3],n.table=r[3]),n}(e,r,n)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=cs())!==a?(r=Ma,(n=zc())!==a&&(o=Fc())!==a&&(u=zc())!==a&&(i=cs())!==a?r=n=[n,o,u,i]:(Ma=r,r=a),r===a&&(r=null),r!==a?(qa=t,t=e=function(t,e){const r={db:null,table:t};return null!==e&&(r.db=t,r.table=e[3]),r}(e,r)):(Ma=t,t=a)):(Ma=t,t=a)),t}function vi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=_i())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){const r=e.length;let n=t;for(let t=0;t<r;++t)n=vl(e[t][1],n,e[t][3]);return n}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function hi(){var t,e;return t=Ma,Ks()!==a&&zc()!==a&&(e=Vi())!==a?(qa=t,t=e):(Ma=t,t=a),t}function yi(){var e,r;return e=Ma,function(){var e,r,n,o;e=Ma,"where"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(no));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=Vi())!==a?(qa=e,e=r):(Ma=e,e=a),e}function di(){var t,e,r;return t=Ma,(e=ps())!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&(r=mi())!==a?(qa=t,t=e={name:e,as_window_specification:r}):(Ma=t,t=a),t}function mi(){var e,r,n;return e=Ma,(r=ps())!==a&&(qa=e,r=r),(e=r)===a&&(e=Ma,(r=$c())!==a&&zc()!==a?((n=function(){var e,r,n,o,u;e=Ma,(r=rs())===a&&(r=null);r!==a&&zc()!==a?((n=Ci())===a&&(n=null),n!==a&&zc()!==a?((o=gi())===a&&(o=null),o!==a&&zc()!==a?((u=function(){var e,r,n,o,u;e=Ma,(r=Nc())!==a&&zc()!==a?((n=ji())===a&&(n=Oi()),n!==a?(qa=e,e=r={type:"rows",expr:n}):(Ma=e,e=a)):(Ma=e,e=a);e===a&&(e=Ma,(r=Nc())===a&&("range"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Ue))),r!==a&&zc()!==a&&(n=vc())!==a&&zc()!==a&&(o=Oi())!==a&&zc()!==a&&Oc()!==a&&zc()!==a&&(u=ji())!==a?(qa=e,i=o,s=u,r=vl(n,{type:"origin",value:r.toLowerCase()},{type:"expr_list",value:[i,s]}),e=r):(Ma=e,e=a));var i,s;return e}())===a&&(u=null),u!==a?(qa=e,e=r={name:r,partitionby:n,orderby:o,window_frame_clause:u}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(n=null),n!==a&&zc()!==a&&Wc()!==a?(qa=e,e=r={window_specification:n,parentheses:!0}):(Ma=e,e=a)):(Ma=e,e=a)),e}function ji(){var e,r,n,o,u;return e=Ma,(r=Li())!==a&&zc()!==a?("following"===t.substr(Ma,9).toLowerCase()?(n=t.substr(Ma,9),Ma+=9):(n=a,0===Fa&&Ya(_e)),n===a&&("preceding"===t.substr(Ma,9).toLowerCase()?(n=t.substr(Ma,9),Ma+=9):(n=a,0===Fa&&Ya(xe))),n!==a?(qa=e,u=n,(o=r).value+=" "+u.toUpperCase(),e=r=o):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=wi()),e}function Oi(){var e,r,n,o,u;return e=Ma,(r=Li())!==a&&zc()!==a?("preceding"===t.substr(Ma,9).toLowerCase()?(n=t.substr(Ma,9),Ma+=9):(n=a,0===Fa&&Ya(xe)),n===a&&("following"===t.substr(Ma,9).toLowerCase()?(n=t.substr(Ma,9),Ma+=9):(n=a,0===Fa&&Ya(_e))),n!==a?(qa=e,u=n,(o=r).value+=" "+u.toUpperCase(),e=r=o):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=wi()),e}function wi(){var e,r,n;return e=Ma,"current"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Ie)),r!==a&&zc()!==a?("row"===t.substr(Ma,3).toLowerCase()?(n=t.substr(Ma,3),Ma+=3):(n=a,0===Fa&&Ya(Ne)),n!==a?(qa=e,e=r={type:"origin",value:"current row",...pl()}):(Ma=e,e=a)):(Ma=e,e=a),e}function Li(){var e,r;return e=Ma,"unbounded"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(ke)),r!==a&&(qa=e,r={type:"origin",value:r.toUpperCase(),...pl()}),(e=r)===a&&(e=ks()),e}function Ci(){var t,e;return t=Ma,$s()!==a&&zc()!==a&&ac()!==a&&zc()!==a&&(e=Ku())!==a?(qa=t,t=e):(Ma=t,t=a),t}function gi(){var e,r;return e=Ma,function(){var e,r,n,o;e=Ma,"order"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(ao));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&ac()!==a&&zc()!==a&&(r=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Si())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Si())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Si())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=l(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())!==a?(qa=e,e=r):(Ma=e,e=a),e}function Si(){var e,r,n,o,u,i;return e=Ma,(r=_i())!==a&&zc()!==a?(n=Ma,"collate"===t.substr(Ma,7).toLowerCase()?(o=t.substr(Ma,7),Ma+=7):(o=a,0===Fa&&Ya(gt)),o!==a&&(u=zc())!==a&&(i=_s())!==a?n=o=[o,u,i]:(Ma=n,n=a),n===a&&(n=null),n!==a&&(o=zc())!==a?((u=fc())===a&&(u=function(){var e,r,n,o;e=Ma,"asc"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(vo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ASC"):(Ma=e,e=a)):(Ma=e,e=a);return e}()),u===a&&(u=null),u!==a?(qa=e,e=r={expr:r,type:u}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e}function Ai(){var t;return(t=ks())===a&&(t=ys()),t}function Ti(){var e,r,n,o,u,i;return e=Ma,function(){var e,r,n,o;e=Ma,"limit"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(fo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=Ai())!==a&&zc()!==a?(n=Ma,(o=Bc())===a&&(o=cc()),o!==a&&(u=zc())!==a&&(i=Ai())!==a?n=o=[o,u,i]:(Ma=n,n=a),n===a&&(n=null),n!==a?(qa=e,e=function(t,e){const r=[t];return e&&r.push(e[2]),{seperator:e&&e[0]&&e[0].toLowerCase()||"",value:r,...pl()}}(r,n)):(Ma=e,e=a)):(Ma=e,e=a),e}function Ei(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=_i())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){const r={type:"expr_list"};return r.value=yl(t,e),r}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Ui(){var t;return(t=Ri())===a&&(t=ki())===a&&(t=function(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Mi())!==a){for(r=[],n=Ma,(o=tl())!==a&&(u=wc())!==a&&(i=zc())!==a&&(s=Mi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=tl())!==a&&(u=wc())!==a&&(i=zc())!==a&&(s=Mi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,e=c(e,r),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())===a&&(t=function(){var t,e,r,n,o,u;if(t=Ma,(e=$i())!==a){if(r=[],n=Ma,(o=zc())!==a&&(u=Gi())!==a?n=o=[o,u]:(Ma=n,n=a),n!==a)for(;n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Gi())!==a?n=o=[o,u]:(Ma=n,n=a);else r=a;r!==a?(qa=t,e=bl(e,r[0][1]),t=e):(Ma=t,t=a)}else Ma=t,t=a;return t}())===a&&(t=Ni()),t}function _i(){var t;return(t=Ui())===a&&(t=Wu()),t}function xi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Ii())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Ii())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=Ii())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Ii(){var t,e;return t=Ma,$c()!==a&&zc()!==a&&(e=Ku())!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e):(Ma=t,t=a),t}function Ni(){var t,e,r,n,o;return t=Ma,(e=Zc())!==a&&zc()!==a?((r=Ku())===a&&(r=null),r!==a&&(n=zc())!==a&&(o=Jc())!==a?(qa=t,t=e={array_path:r,type:"array",brackets:!0,keyword:""}):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=sl())===a&&(e=Tc()),e===a&&(e=null),e!==a&&Zc()!==a&&(r=zc())!==a&&(n=Ts())!==a&&(o=zc())!==a&&Jc()!==a?(qa=t,t=e=function(t,e){return{definition:t,array_path:e.map(t=>({expr:t,as:null})),type:"array",keyword:t&&"array",brackets:!0}}(e,n)):(Ma=t,t=a),t===a&&(t=Ma,(e=sl())===a&&(e=Tc()),e===a&&(e=null),e!==a&&zc()!==a&&(r=Zc())!==a&&(n=zc())!==a?((o=xi())===a&&(o=_i()),o!==a&&zc()!==a&&Jc()!==a?(qa=t,t=e=function(t,e,r,n){return{definition:t,expr_list:r,type:"array",keyword:t&&"array",brackets:!0,parentheses:!1}}(e,0,o)):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=sl())===a&&(e=Tc()),e!==a&&zc()!==a&&(r=$c())!==a&&(n=zc())!==a?((o=xi())===a&&(o=_i()),o!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e=function(t,e,r,n){return{definition:t,expr_list:r,type:"array",keyword:t&&"array",brackets:!1,parentheses:!0}}(e,0,o)):(Ma=t,t=a)):(Ma=t,t=a)))),t}function ki(){var e,r;return e=Ma,function(){var e,r,n,o;e=Ma,"json"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Jo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="JSON"):(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=Ts())!==a?(qa=e,e={type:"json",keyword:"json",expr_list:r}):(Ma=e,e=a),e}function Ri(){var t,e,r,n;return t=Ma,(e=cl())===a&&(e=_c()),e!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(r=Ku())!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e={definition:n=e,expr_list:r,type:"struct",keyword:n&&"struct",parentheses:!0}):(Ma=t,t=a),t}function Vi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=_i())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc())===a&&(u=Bc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=Oc())===a&&(u=wc())===a&&(u=Bc()),u!==a&&(i=zc())!==a&&(s=_i())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){const r=e.length;let n=t,o="";for(let t=0;t<r;++t)","===e[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(e[t][3])):n=vl(e[t][1],n,e[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Mi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=qi())!==a){for(r=[],n=Ma,(o=tl())!==a&&(u=Oc())!==a&&(i=zc())!==a&&(s=qi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=tl())!==a&&(u=Oc())!==a&&(i=zc())!==a&&(s=qi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=c(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function qi(){var e,r,n,o,u;return(e=Pi())===a&&(e=function(){var t,e,r;t=Ma,(e=function(){var t,e,r,n,o;t=Ma,e=Ma,(r=jc())!==a&&(n=zc())!==a&&(o=mc())!==a?e=r=[r,n,o]:(Ma=e,e=a);e!==a&&(qa=t,e=Ve(e));(t=e)===a&&(t=mc());return t}())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(r=Wu())!==a&&zc()!==a&&Wc()!==a?(qa=t,n=e,(o=r).parentheses=!0,e=bl(n,o),t=e):(Ma=t,t=a);var n,o;return t}())===a&&(e=Ma,(r=jc())===a&&(r=Ma,33===t.charCodeAt(Ma)?(n="!",Ma++):(n=a,0===Fa&&Ya(Re)),n!==a?(o=Ma,Fa++,61===t.charCodeAt(Ma)?(u="=",Ma++):(u=a,0===Fa&&Ya(f)),Fa--,u===a?o=void 0:(Ma=o,o=a),o!==a?r=n=[n,o]:(Ma=r,r=a)):(Ma=r,r=a)),r!==a&&(n=zc())!==a&&(o=qi())!==a?(qa=e,e=r=bl("NOT",o)):(Ma=e,e=a)),e}function Pi(){var t,e,r,n,o;return t=Ma,(e=Hi())!==a&&zc()!==a?((r=function(){var t;(t=function(){var t,e,r,n,o,u,i;t=Ma,e=[],r=Ma,(n=zc())!==a&&(o=Di())!==a&&(u=zc())!==a&&(i=Hi())!==a?r=n=[n,o,u,i]:(Ma=r,r=a);if(r!==a)for(;r!==a;)e.push(r),r=Ma,(n=zc())!==a&&(o=Di())!==a&&(u=zc())!==a&&(i=Hi())!==a?r=n=[n,o,u,i]:(Ma=r,r=a);else e=a;e!==a&&(qa=t,e={type:"arithmetic",tail:e});return t=e}())===a&&(t=Bi())===a&&(t=function(){var t,e,r,n;t=Ma,(e=function(){var t,e,r,n,o;t=Ma,e=Ma,(r=jc())!==a&&(n=zc())!==a&&(o=vc())!==a?e=r=[r,n,o]:(Ma=e,e=a);e!==a&&(qa=t,e=Ve(e));(t=e)===a&&(t=vc());return t}())!==a&&zc()!==a&&(r=Hi())!==a&&zc()!==a&&Oc()!==a&&zc()!==a&&(n=Hi())!==a?(qa=t,t=e={op:e,right:{type:"expr_list",value:[r,n]}}):(Ma=t,t=a);return t}())===a&&(t=function(){var t,e,r,n,o;t=Ma,(e=yc())!==a&&(r=zc())!==a&&(n=Hi())!==a?(qa=t,t=e={op:"IS",right:n}):(Ma=t,t=a);t===a&&(t=Ma,e=Ma,(r=yc())!==a&&(n=zc())!==a&&(o=jc())!==a?e=r=[r,n,o]:(Ma=e,e=a),e!==a&&(r=zc())!==a&&(n=Hi())!==a?(qa=t,e=function(t){return{op:"IS NOT",right:t}}(n),t=e):(Ma=t,t=a));return t}())===a&&(t=Fi());return t}())===a&&(r=null),r!==a?(qa=t,n=e,t=e=null===(o=r)?n:"arithmetic"===o.type?dl(n,o.tail):vl(o.op,n,o.right)):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=_s())===a&&(t=zi()),t}function Di(){var e;return">="===t.substr(Ma,2)?(e=">=",Ma+=2):(e=a,0===Fa&&Ya(Me)),e===a&&(62===t.charCodeAt(Ma)?(e=">",Ma++):(e=a,0===Fa&&Ya(qe)),e===a&&("<="===t.substr(Ma,2)?(e="<=",Ma+=2):(e=a,0===Fa&&Ya(Pe)),e===a&&("<>"===t.substr(Ma,2)?(e="<>",Ma+=2):(e=a,0===Fa&&Ya(De)),e===a&&(60===t.charCodeAt(Ma)?(e="<",Ma++):(e=a,0===Fa&&Ya(Qe)),e===a&&(61===t.charCodeAt(Ma)?(e="=",Ma++):(e=a,0===Fa&&Ya(f)),e===a&&("!="===t.substr(Ma,2)?(e="!=",Ma+=2):(e=a,0===Fa&&Ya(Fe)))))))),e}function Qi(){var t,e,r,n,o;return t=Ma,e=Ma,(r=jc())!==a&&(n=zc())!==a&&(o=hc())!==a?e=r=[r,n,o]:(Ma=e,e=a),e!==a&&(qa=t,e=Ve(e)),(t=e)===a&&(t=hc()),t}function Fi(){var t,e,r;return t=Ma,(e=function(){var t,e,r,n,o;return t=Ma,e=Ma,(r=jc())!==a&&(n=zc())!==a&&(o=dc())!==a?e=r=[r,n,o]:(Ma=e,e=a),e!==a&&(qa=t,e=Ve(e)),(t=e)===a&&(t=dc()),t}())!==a&&zc()!==a?((r=As())===a&&(r=Pi()),r!==a?(qa=t,t=e={op:e,right:r}):(Ma=t,t=a)):(Ma=t,t=a),t}function Bi(){var t,e,r,n;return t=Ma,(e=Qi())!==a&&zc()!==a&&(r=$c())!==a&&zc()!==a&&(n=Ei())!==a&&zc()!==a&&Wc()!==a?(qa=t,t=e={op:e,right:n}):(Ma=t,t=a),t===a&&(t=Ma,(e=Qi())!==a&&zc()!==a?((r=_s())===a&&(r=ai()),r!==a?(qa=t,t=e=function(t,e){return{op:t,right:e}}(e,r)):(Ma=t,t=a)):(Ma=t,t=a)),t}function Hi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Wi())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=$i())!==a&&(i=zc())!==a&&(s=Wi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=$i())!==a&&(i=zc())!==a&&(s=Wi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){if(e&&e.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...pl()}));return dl(t,e)}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function $i(){var e;return 43===t.charCodeAt(Ma)?(e="+",Ma++):(e=a,0===Fa&&Ya(Be)),e===a&&(45===t.charCodeAt(Ma)?(e="-",Ma++):(e=a,0===Fa&&Ya(He))),e}function Wi(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=Zi())!==a){for(r=[],n=Ma,(o=zc())!==a?((u=Yi())===a&&(u=Kc()),u!==a&&(i=zc())!==a&&(s=Zi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a?((u=Yi())===a&&(u=Kc()),u!==a&&(i=zc())!==a&&(s=Zi())!==a?n=o=[o,u,i,s]:(Ma=n,n=a)):(Ma=n,n=a);r!==a?(qa=t,t=e=dl(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Yi(){var e;return 42===t.charCodeAt(Ma)?(e="*",Ma++):(e=a,0===Fa&&Ya($e)),e===a&&(47===t.charCodeAt(Ma)?(e="/",Ma++):(e=a,0===Fa&&Ya(We)),e===a&&(37===t.charCodeAt(Ma)?(e="%",Ma++):(e=a,0===Fa&&Ya(Ye)))),e}function Gi(){var e,r,n;return(e=Ni())===a&&(e=ds())===a&&(e=Os())===a&&(e=Ri())===a&&(e=ki())===a&&(e=function(){var e,r,n,o,u,i,s;e=Ma,(r=Ss())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=gs())!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&(o=il())!==a&&zc()!==a&&(u=Wc())!==a?(qa=e,c=n,l=o,r={type:"cast",keyword:r.toLowerCase(),...c,symbol:"as",target:[l]},e=r):(Ma=e,e=a);var c,l;e===a&&(e=Ma,(r=Ss())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=gs())!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&(o=Ec())!==a&&zc()!==a&&(u=$c())!==a&&zc()!==a&&(i=Vs())!==a&&zc()!==a&&Wc()!==a&&zc()!==a&&(s=Wc())!==a?(qa=e,r=function(t,e,r){return{type:"cast",keyword:t.toLowerCase(),...e,symbol:"as",target:[{dataType:"DECIMAL("+r+")"}]}}(r,n,i),e=r):(Ma=e,e=a),e===a&&(e=Ma,(r=Ss())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=gs())!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&(o=Ec())!==a&&zc()!==a&&(u=$c())!==a&&zc()!==a&&(i=Vs())!==a&&zc()!==a&&Bc()!==a&&zc()!==a&&(s=Vs())!==a&&zc()!==a&&Wc()!==a&&zc()!==a&&Wc()!==a?(qa=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),...e,symbol:"as",target:[{dataType:"DECIMAL("+r+", "+n+")"}]}}(r,n,i,s),e=r):(Ma=e,e=a),e===a&&(e=Ma,(r=Ss())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=gs())!==a&&zc()!==a&&Zs()!==a&&zc()!==a&&(o=function(){var e;(e=function(){var e,r,n,o;e=Ma,"signed"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Wo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SIGNED"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"unsigned"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Yo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="UNSIGNED"):(Ma=e,e=a)):(Ma=e,e=a);return e}());return e}())!==a&&zc()!==a?((u=Uc())===a&&(u=null),u!==a&&zc()!==a&&(i=Wc())!==a?(qa=e,r=function(t,e,r,n){return{type:"cast",keyword:t.toLowerCase(),...e,symbol:"as",target:[{dataType:r+(n?" "+n:"")}]}}(r,n,o,u),e=r):(Ma=e,e=a)):(Ma=e,e=a))));return e}())===a&&(e=As())===a&&(e=function(){var t,e,r,n,o,u,i;t=Ma,(e=Sc())!==a&&zc()!==a&&(r=Ji())!==a&&zc()!==a?((n=Ki())===a&&(n=null),n!==a&&zc()!==a&&(o=Ac())!==a&&zc()!==a?((u=Sc())===a&&(u=null),u!==a?(qa=t,s=r,(c=n)&&s.push(c),t=e={type:"case",expr:null,args:s}):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a);var s,c;t===a&&(t=Ma,(e=Sc())!==a&&zc()!==a&&(r=_i())!==a&&zc()!==a&&(n=Ji())!==a&&zc()!==a?((o=Ki())===a&&(o=null),o!==a&&zc()!==a&&(u=Ac())!==a&&zc()!==a?((i=Sc())===a&&(i=null),i!==a?(qa=t,e=function(t,e,r){return r&&e.push(r),{type:"case",expr:t,args:e}}(r,n,o),t=e):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a));return t}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=Vc())!==a&&zc()!==a&&(n=_i())!==a&&zc()!==a&&(o=function(){var e;(e=function(){var e,r,n,o;e=Ma,"year"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Zr));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="YEAR"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"isoyear"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(qr));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ISOYEAR"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"month"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Br));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="MONTH"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"day"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Ur));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DAY"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"hour"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Rr));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="HOUR"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"minute"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Fr));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="MINUTE"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"second"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya($r));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SECOND"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"week"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Gr));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="WEEK"):(Ma=e,e=a)):(Ma=e,e=a);return e}());return e}())!==a?(qa=e,r={type:"interval",expr:n,unit:o.toLowerCase()},e=r):(Ma=e,e=a);return e}())===a&&(e=zi())===a&&(e=ys())===a&&(e=Ma,$c()!==a&&zc()!==a&&(r=Vi())!==a&&zc()!==a&&Wc()!==a?(qa=e,(n=r).parentheses=!0,e=n):(Ma=e,e=a)),e}function Zi(){var e,r,n,o,u;return(e=Gi())===a&&(e=Ma,(r=function(){var e;33===t.charCodeAt(Ma)?(e="!",Ma++):(e=a,0===Fa&&Ya(Re));e===a&&(45===t.charCodeAt(Ma)?(e="-",Ma++):(e=a,0===Fa&&Ya(He)),e===a&&(43===t.charCodeAt(Ma)?(e="+",Ma++):(e=a,0===Fa&&Ya(Be)),e===a&&(126===t.charCodeAt(Ma)?(e="~",Ma++):(e=a,0===Fa&&Ya(Ge)))));return e}())!==a?(n=Ma,(o=zc())!==a&&(u=Zi())!==a?n=o=[o,u]:(Ma=n,n=a),n!==a?(qa=e,e=r=bl(r,n[1])):(Ma=e,e=a)):(Ma=e,e=a)),e}function Ji(){var t,e,r,n,o,u;if(t=Ma,(e=Xi())!==a)if(zc()!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Xi())!==a?n=o=[o,u]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Xi())!==a?n=o=[o,u]:(Ma=n,n=a);r!==a?(qa=t,t=e=w(e,r)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;return t}function Xi(){var e,r,n;return e=Ma,function(){var e,r,n,o;e=Ma,"when"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(ko));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=Vi())!==a&&zc()!==a&&function(){var e,r,n,o;e=Ma,"then"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Ro));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(n=_i())!==a?(qa=e,e={type:"when",cond:r,result:n}):(Ma=e,e=a),e}function Ki(){var e,r;return e=Ma,function(){var e,r,n,o;e=Ma,"else"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Vo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}()!==a&&zc()!==a&&(r=_i())!==a?(qa=e,e={type:"else",result:r}):(Ma=e,e=a),e}function zi(){var t,e,r,n,o,u,i,s,c,l,f;if(t=Ma,(e=ss())!==a){if(r=[],n=Ma,(o=zc())!==a&&(u=Fc())!==a&&(i=zc())!==a&&(s=ss())!==a?n=o=[o,u,i,s]:(Ma=n,n=a),n!==a)for(;n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Fc())!==a&&(i=zc())!==a&&(s=ss())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);else r=a;r!==a&&(n=zc())!==a?(o=Ma,(u=ei())!==a&&(i=zc())!==a?(s=Ma,(c=Fc())!==a&&(l=zc())!==a&&(f=ss())!==a?s=c=[c,l,f]:(Ma=s,s=a),s===a&&(s=null),s!==a?o=u=[u,i,s]:(Ma=o,o=a)):(Ma=o,o=a),o===a&&(o=null),o!==a?(u=Ma,(i=zc())!==a&&(s=Cu())!==a?u=i=[i,s]:(Ma=u,u=a),u===a&&(u=null),u!==a?(qa=t,t=e=function(t,e,r,n){const o=e.map(t=>t[3]);Ll.add(`select::${t}::${o[0]}`);return{type:"column_ref",table:t,...r?{column:{expr:{type:"column_ref",table:null,column:o[0],subFields:o.slice(1)},offset:r&&r[0],suffix:r&&r[2]&&"."+r[2][2]}}:{column:o[0],subFields:o.slice(1)},collate:n&&n[1],...pl()}}(e,r,o,u)):(Ma=t,t=a)):(Ma=t,t=a)):(Ma=t,t=a)}else Ma=t,t=a;if(t===a)if(t=Ma,(e=ns())===a&&(e=ls()),e!==a)if((r=zc())!==a){for(n=[],o=ti();o!==a;)n.push(o),o=ti();n!==a?(o=Ma,(u=zc())!==a&&(i=Cu())!==a?o=u=[u,i]:(Ma=o,o=a),o===a&&(o=null),o!==a?(qa=t,t=e=function(t,e,r){const n="string"==typeof t?t:t.value;Ll.add("select::null::"+n);const o="string"==typeof t?{expr:{type:"default",value:t}}:{expr:t};return e&&(o.offset=e),{type:"column_ref",table:null,column:o,collate:r&&r[1],...pl()}}(e,n,o)):(Ma=t,t=a)):(Ma=t,t=a)}else Ma=t,t=a;else Ma=t,t=a;return t}function ts(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ls())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ls())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ls())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function es(){var t,e;return t=Ma,(e=ps())!==a&&(qa=t,e={type:"default",value:e}),(t=e)===a&&(t=ns()),t}function rs(){var t,e;return t=Ma,(e=ps())!==a?(qa=Ma,(!0===ll[(""+e).toUpperCase()]?a:void 0)!==a?(qa=t,t=e=e):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=Ma,(e=os())!==a&&(qa=t,e=e),t=e),t}function ns(){var t;return(t=as())===a&&(t=us())===a&&(t=is()),t}function os(){var t,e;return t=Ma,(e=as())===a&&(e=us())===a&&(e=is()),e!==a&&(qa=t,e=e.value),t=e}function as(){var e,r,n,o;if(e=Ma,34===t.charCodeAt(Ma)?(r='"',Ma++):(r=a,0===Fa&&Ya(Je)),r!==a){if(n=[],Xe.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(Ke)),o!==a)for(;o!==a;)n.push(o),Xe.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(Ke));else n=a;n!==a?(34===t.charCodeAt(Ma)?(o='"',Ma++):(o=a,0===Fa&&Ya(Je)),o!==a?(qa=e,e=r={type:"double_quote_string",value:n.join("")}):(Ma=e,e=a)):(Ma=e,e=a)}else Ma=e,e=a;return e}function us(){var e,r,n,o;if(e=Ma,39===t.charCodeAt(Ma)?(r="'",Ma++):(r=a,0===Fa&&Ya(yt)),r!==a){if(n=[],ze.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(tr)),o!==a)for(;o!==a;)n.push(o),ze.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(tr));else n=a;n!==a?(39===t.charCodeAt(Ma)?(o="'",Ma++):(o=a,0===Fa&&Ya(yt)),o!==a?(qa=e,e=r={type:"single_quote_string",value:n.join("")}):(Ma=e,e=a)):(Ma=e,e=a)}else Ma=e,e=a;return e}function is(){var e,r,n,o;if(e=Ma,96===t.charCodeAt(Ma)?(r="`",Ma++):(r=a,0===Fa&&Ya(er)),r!==a){if(n=[],rr.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(nr)),o!==a)for(;o!==a;)n.push(o),rr.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(nr));else n=a;n!==a?(96===t.charCodeAt(Ma)?(o="`",Ma++):(o=a,0===Fa&&Ya(er)),o!==a?(qa=e,e=r={type:"backticks_quote_string",value:n.join("")}):(Ma=e,e=a)):(Ma=e,e=a)}else Ma=e,e=a;return e}function ss(){var t;return(t=fs())===a&&(t=os()),t}function cs(){var t;return(t=ps())===a&&(t=os()),t}function ls(){var t,e;return t=Ma,(e=fs())!==a?(qa=Ma,(Ze(e)?a:void 0)!==a?(qa=t,t=e=e):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=os()),t}function fs(){var t,e,r,n;if(t=Ma,(e=bs())!==a){for(r=[],n=hs();n!==a;)r.push(n),n=hs();r!==a?(qa=t,t=e=or(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function ps(){var t,e,r,n;if(t=Ma,(e=bs())!==a){for(r=[],n=vs();n!==a;)r.push(n),n=vs();r!==a?(qa=t,t=e=or(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function bs(){var e;return ar.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(ur)),e}function vs(){var e;return ir.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(sr)),e}function hs(){var e;return cr.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(lr)),e}function ys(){var e,r,n;return e=Ma,58===t.charCodeAt(Ma)?(r=":",Ma++):(r=a,0===Fa&&Ya(fr)),r===a&&(64===t.charCodeAt(Ma)?(r="@",Ma++):(r=a,0===Fa&&Ya(zt))),r!==a&&(n=ps())!==a?(qa=e,e=r={type:"param",value:n,prefix:r}):(Ma=e,e=a),e}function ds(){var e;return(e=function(){var e,r,n,o;e=Ma,(r=function(){var e,r,n,o;e=Ma,"count"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Ao));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="COUNT"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&("string_agg"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(pr)));r!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=function(){var e,r,n,o,u,i,s,c,l,f;e=Ma,(r=function(){var e,r;e=Ma,42===t.charCodeAt(Ma)?(r="*",Ma++):(r=a,0===Fa&&Ya($e));r!==a&&(qa=e,r={type:"star",value:"*"});return e=r}())!==a&&(qa=e,r={expr:r,...pl()});if((e=r)===a){if(e=Ma,(r=bc())===a&&(r=null),r!==a)if(zc()!==a)if((n=$c())!==a)if(zc()!==a)if((o=_i())!==a)if(zc()!==a)if(Wc()!==a){for(u=[],i=Ma,(s=zc())!==a?((c=Oc())===a&&(c=wc()),c!==a&&(l=zc())!==a&&(f=_i())!==a?i=s=[s,c,l,f]:(Ma=i,i=a)):(Ma=i,i=a);i!==a;)u.push(i),i=Ma,(s=zc())!==a?((c=Oc())===a&&(c=wc()),c!==a&&(l=zc())!==a&&(f=_i())!==a?i=s=[s,c,l,f]:(Ma=i,i=a)):(Ma=i,i=a);u!==a&&(i=zc())!==a?((s=gi())===a&&(s=null),s!==a?(qa=e,r=function(t,e,r,n){const o=r.length;let a=e;a.parentheses=!0;for(let t=0;t<o;++t)a=vl(r[t][1],a,r[t][3]);return{distinct:t,expr:a,orderby:n,...pl()}}(r,o,u,s),e=r):(Ma=e,e=a)):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;e===a&&(e=Ma,(r=bc())===a&&(r=null),r!==a&&zc()!==a&&(n=vi())!==a&&zc()!==a?((o=gi())===a&&(o=null),o!==a?(qa=e,r={distinct:r,expr:n,orderby:o,...pl()},e=r):(Ma=e,e=a)):(Ma=e,e=a))}return e}())!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((o=js())===a&&(o=null),o!==a?(qa=e,r={type:"aggr_func",name:r,args:n,over:o,...pl()},e=r):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=function(){var e;(e=function(){var e,r,n,o;e=Ma,"sum"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Uo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SUM"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=Lc())===a&&(e=Cc())===a&&(e=function(){var e,r,n,o;e=Ma,"avg"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(_o));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="AVG"):(Ma=e,e=a)):(Ma=e,e=a);return e}());return e}())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=Hi())!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((o=js())===a&&(o=null),o!==a?(qa=e,r={type:"aggr_func",name:r,args:{expr:n},over:o,...pl()},e=r):(Ma=e,e=a)):(Ma=e,e=a);return e}()),e}function ms(){var e,r,n,o;return e=Ma,Ks()!==a&&zc()!==a?("update"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Dt)),r!==a&&zc()!==a&&(n=Mc())!==a&&zc()!==a&&$c()!==a&&zc()!==a?((o=Ei())===a&&(o=null),o!==a&&zc()!==a&&Wc()!==a?(qa=e,e={type:"on update",keyword:n,parentheses:!0,expr:o}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,Ks()!==a&&zc()!==a?("update"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Dt)),r!==a&&zc()!==a&&(n=Mc())!==a?(qa=e,e=function(t){return{type:"on update",keyword:t}}(n)):(Ma=e,e=a)):(Ma=e,e=a)),e}function js(){var t,e,r,n;return t=Ma,ec()!==a&&zc()!==a&&(e=mi())!==a?(qa=t,t={type:"window",as_window_specification:e}):(Ma=t,t=a),t===a&&(t=Ma,ec()!==a&&zc()!==a&&(e=$c())!==a&&zc()!==a&&(r=Ci())!==a&&zc()!==a?((n=gi())===a&&(n=null),n!==a&&zc()!==a&&Wc()!==a?(qa=t,t={partitionby:r,orderby:n}):(Ma=t,t=a)):(Ma=t,t=a),t===a&&(t=ms())),t}function Os(){var e,r,n,o,u;return(e=function(){var e,r,n,o,u;e=Ma,(r=gc())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=Cs())!==a&&zc()!==a&&Ys()!==a&&zc()!==a?((o=Rc())===a&&(o=Vc())===a&&(o=kc())===a&&(o=xc()),o!==a&&zc()!==a&&(u=_i())!==a&&zc()!==a&&Wc()!==a?(qa=e,i=n,s=o,c=u,r={type:r.toLowerCase(),args:{field:i,cast_type:s,source:c},...pl()},e=r):(Ma=e,e=a)):(Ma=e,e=a);var i,s,c;e===a&&(e=Ma,(r=gc())!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=Cs())!==a&&zc()!==a&&Ys()!==a&&zc()!==a&&(o=_i())!==a&&zc()!==a&&(u=Wc())!==a?(qa=e,r=function(t,e,r){return{type:t.toLowerCase(),args:{field:e,source:r},...pl()}}(r,n,o),e=r):(Ma=e,e=a),e===a&&(e=Ma,"date_trunc"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(Jr)),r!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=_i())!==a&&zc()!==a&&Bc()!==a&&zc()!==a&&(o=Cs())!==a&&zc()!==a&&(u=Wc())!==a?(qa=e,r=function(t,e){return{type:"function",name:{name:[{type:"origin",value:"date_trunc"}]},args:{type:"expr_list",value:[t,{type:"origin",value:e}]},over:null,...pl()}}(n,o),e=r):(Ma=e,e=a)));return e}())===a&&(e=function(){var e,r,n,o,u;e=Ma,"any_value"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(br));r!==a&&zc()!==a&&$c()!==a&&zc()!==a&&(n=Vi())!==a&&zc()!==a?((o=function(){var t,e,r;t=Ma,uc()!==a&&zc()!==a?((e=Lc())===a&&(e=Cc()),e!==a&&zc()!==a&&(r=Vi())!==a?(qa=t,t={prefix:e,expr:r}):(Ma=t,t=a)):(Ma=t,t=a);return t}())===a&&(o=null),o!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((u=js())===a&&(u=null),u!==a?(qa=e,r={type:"any_value",args:{expr:n,having:o},over:u,...pl()},e=r):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=Ma,(r=function(){var e;(e=Ls())===a&&(e=function(){var e,r,n,o;e=Ma,"session_user"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(ia));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SESSION_USER"):(Ma=e,e=a)):(Ma=e,e=a);return e}());return e}())!==a&&zc()!==a&&(n=$c())!==a&&zc()!==a?((o=Ei())===a&&(o=null),o!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((u=js())===a&&(u=null),u!==a?(qa=e,e=r={type:"function",name:{name:[{type:"default",value:r}]},args:o||{type:"expr_list",value:[]},over:u,...pl()}):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=Ls())!==a&&zc()!==a?((n=ms())===a&&(n=null),n!==a?(qa=e,e=r={type:"function",name:{name:[{type:"origin",value:r}]},over:n,...pl()}):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=Ma,(r=ws())!==a&&zc()!==a&&(n=$c())!==a&&zc()!==a?((o=Vi())===a&&(o=null),o!==a&&zc()!==a&&Wc()!==a&&zc()!==a?((u=js())===a&&(u=null),u!==a?(qa=e,e=r=function(t,e,r){return e&&"expr_list"!==e.type&&(e={type:"expr_list",value:[e]}),{type:"function",name:t,args:e||{type:"expr_list",value:[]},over:r,...pl()}}(r,o,u)):(Ma=e,e=a)):(Ma=e,e=a)):(Ma=e,e=a)))),e}function ws(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=es())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Fc())!==a&&(i=zc())!==a&&(s=es())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Fc())!==a&&(i=zc())!==a&&(s=es())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=function(t,e){const r={name:[t]};return null!==e&&(r.schema=t,r.name=e.map(t=>t[3])),r}(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Ls(){var e;return(e=function(){var e,r,n,o;e=Ma,"current_date"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(na));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="CURRENT_DATE"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"current_time"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(aa));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="CURRENT_TIME"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=Mc()),e}function Cs(){var e,r;return e=Ma,"year_month"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(vr)),r===a&&("day_hour"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(hr)),r===a&&("day_minute"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(yr)),r===a&&("day_second"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(dr)),r===a&&("day_microsecond"===t.substr(Ma,15).toLowerCase()?(r=t.substr(Ma,15),Ma+=15):(r=a,0===Fa&&Ya(mr)),r===a&&("hour_minute"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(jr)),r===a&&("hour_second"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(Or)),r===a&&("hour_microsecond"===t.substr(Ma,16).toLowerCase()?(r=t.substr(Ma,16),Ma+=16):(r=a,0===Fa&&Ya(wr)),r===a&&("minute_second"===t.substr(Ma,13).toLowerCase()?(r=t.substr(Ma,13),Ma+=13):(r=a,0===Fa&&Ya(Lr)),r===a&&("minute_microsecond"===t.substr(Ma,18).toLowerCase()?(r=t.substr(Ma,18),Ma+=18):(r=a,0===Fa&&Ya(Cr)),r===a&&("second_microsecond"===t.substr(Ma,18).toLowerCase()?(r=t.substr(Ma,18),Ma+=18):(r=a,0===Fa&&Ya(gr)),r===a&&("timezone_hour"===t.substr(Ma,13).toLowerCase()?(r=t.substr(Ma,13),Ma+=13):(r=a,0===Fa&&Ya(Sr)),r===a&&("timezone_minute"===t.substr(Ma,15).toLowerCase()?(r=t.substr(Ma,15),Ma+=15):(r=a,0===Fa&&Ya(Ar)),r===a&&("century"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Tr)),r===a&&("dayofweek"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(Er)),r===a&&("day"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Ur)),r===a&&("date"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(_r)),r===a&&("decade"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(xr)),r===a&&("dow"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Ir)),r===a&&("doy"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Nr)),r===a&&("epoch"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(kr)),r===a&&("hour"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Rr)),r===a&&("isodow"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Vr)),r===a&&("isoweek"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Mr)),r===a&&("isoyear"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(qr)),r===a&&("microseconds"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(Pr)),r===a&&("millennium"===t.substr(Ma,10).toLowerCase()?(r=t.substr(Ma,10),Ma+=10):(r=a,0===Fa&&Ya(Dr)),r===a&&("milliseconds"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(Qr)),r===a&&("minute"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Fr)),r===a&&("month"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Br)),r===a&&("quarter"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Hr)),r===a&&("second"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya($r)),r===a&&("time"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Wr)),r===a&&("timezone"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(Yr)),r===a&&("week"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Gr)),r===a&&("year"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Zr))))))))))))))))))))))))))))))))))))),r!==a&&(qa=e,r=r),e=r}function gs(){var t,e,r;return t=Ma,(e=_i())!==a&&zc()!==a?((r=ei())===a&&(r=null),r!==a?(qa=t,t=e=function(t,e){const r={expr:t};return e&&(r.offset=e),r}(e,r)):(Ma=t,t=a)):(Ma=t,t=a),t}function Ss(){var e;return(e=function(){var e,r,n,o;e=Ma,"cast"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(qo));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="CAST"):(Ma=e,e=a)):(Ma=e,e=a);return e}())===a&&(e=function(){var e,r,n,o;e=Ma,"safe_cast"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(Po));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SAFE_CAST"):(Ma=e,e=a)):(Ma=e,e=a);return e}()),e}function As(){var e;return(e=_s())===a&&(e=ks())===a&&(e=Us())===a&&(e=Es())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,(r=kc())===a&&(r=xc())===a&&(r=Rc())===a&&(r=Ic());if(r!==a)if(zc()!==a){if(n=Ma,39===t.charCodeAt(Ma)?(o="'",Ma++):(o=a,0===Fa&&Ya(yt)),o!==a){for(u=[],i=Is();i!==a;)u.push(i),i=Is();u!==a?(39===t.charCodeAt(Ma)?(i="'",Ma++):(i=a,0===Fa&&Ya(yt)),i!==a?n=o=[o,u,i]:(Ma=n,n=a)):(Ma=n,n=a)}else Ma=n,n=a;n!==a?(qa=e,r=Kr(r,n),e=r):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;if(e===a)if(e=Ma,(r=kc())===a&&(r=xc())===a&&(r=Rc())===a&&(r=Ic()),r!==a)if(zc()!==a){if(n=Ma,34===t.charCodeAt(Ma)?(o='"',Ma++):(o=a,0===Fa&&Ya(Je)),o!==a){for(u=[],i=xs();i!==a;)u.push(i),i=xs();u!==a?(34===t.charCodeAt(Ma)?(i='"',Ma++):(i=a,0===Fa&&Ya(Je)),i!==a?n=o=[o,u,i]:(Ma=n,n=a)):(Ma=n,n=a)}else Ma=n,n=a;n!==a?(qa=e,r=Kr(r,n),e=r):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;return e}()),e}function Ts(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=As())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=As())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=As())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function Es(){var e,r;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"null"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(En));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={type:"null",value:null}),e=r}function Us(){var e,r;return e=Ma,(r=function(){var e,r,n,o;e=Ma,"true"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(_n));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={type:"bool",value:!0}),(e=r)===a&&(e=Ma,(r=function(){var e,r,n,o;e=Ma,"false"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(In));r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a);return e}())!==a&&(qa=e,r={type:"bool",value:!1}),e=r),e}function _s(){var e,r,n,o,u,i;if(e=Ma,"r"===t.substr(Ma,1).toLowerCase()?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(Xr)),r===a&&(r=null),r!==a)if(zc()!==a){if(n=Ma,39===t.charCodeAt(Ma)?(o="'",Ma++):(o=a,0===Fa&&Ya(yt)),o!==a){for(u=[],i=Is();i!==a;)u.push(i),i=Is();u!==a?(39===t.charCodeAt(Ma)?(i="'",Ma++):(i=a,0===Fa&&Ya(yt)),i!==a?n=o=[o,u,i]:(Ma=n,n=a)):(Ma=n,n=a)}else Ma=n,n=a;n!==a?(qa=e,e=r={type:r?"regex_string":"single_quote_string",value:n[1].join(""),...pl()}):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;if(e===a)if(e=Ma,"r"===t.substr(Ma,1).toLowerCase()?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(Xr)),r===a&&(r=null),r!==a)if(zc()!==a){if(n=Ma,34===t.charCodeAt(Ma)?(o='"',Ma++):(o=a,0===Fa&&Ya(Je)),o!==a){for(u=[],i=xs();i!==a;)u.push(i),i=xs();u!==a?(34===t.charCodeAt(Ma)?(i='"',Ma++):(i=a,0===Fa&&Ya(Je)),i!==a?n=o=[o,u,i]:(Ma=n,n=a)):(Ma=n,n=a)}else Ma=n,n=a;n!==a?(qa=e,e=r=function(t,e){return{type:t?"regex_string":"string",value:e[1].join(""),...pl()}}(r,n)):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;return e}function xs(){var e;return zr.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(tn)),e===a&&(e=Ns()),e}function Is(){var e;return en.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(rn)),e===a&&(e=Ns()),e}function Ns(){var e,r,n,o,u,i,s,c,l,f;return e=Ma,"\\'"===t.substr(Ma,2)?(r="\\'",Ma+=2):(r=a,0===Fa&&Ya(nn)),r!==a&&(qa=e,r="\\'"),(e=r)===a&&(e=Ma,'\\"'===t.substr(Ma,2)?(r='\\"',Ma+=2):(r=a,0===Fa&&Ya(on)),r!==a&&(qa=e,r='\\"'),(e=r)===a&&(e=Ma,"\\\\"===t.substr(Ma,2)?(r="\\\\",Ma+=2):(r=a,0===Fa&&Ya(an)),r!==a&&(qa=e,r="\\\\"),(e=r)===a&&(e=Ma,"\\/"===t.substr(Ma,2)?(r="\\/",Ma+=2):(r=a,0===Fa&&Ya(un)),r!==a&&(qa=e,r="\\/"),(e=r)===a&&(e=Ma,"\\b"===t.substr(Ma,2)?(r="\\b",Ma+=2):(r=a,0===Fa&&Ya(sn)),r!==a&&(qa=e,r="\b"),(e=r)===a&&(e=Ma,"\\f"===t.substr(Ma,2)?(r="\\f",Ma+=2):(r=a,0===Fa&&Ya(cn)),r!==a&&(qa=e,r="\f"),(e=r)===a&&(e=Ma,"\\n"===t.substr(Ma,2)?(r="\\n",Ma+=2):(r=a,0===Fa&&Ya(ln)),r!==a&&(qa=e,r="\n"),(e=r)===a&&(e=Ma,"\\r"===t.substr(Ma,2)?(r="\\r",Ma+=2):(r=a,0===Fa&&Ya(fn)),r!==a&&(qa=e,r="\r"),(e=r)===a&&(e=Ma,"\\t"===t.substr(Ma,2)?(r="\\t",Ma+=2):(r=a,0===Fa&&Ya(pn)),r!==a&&(qa=e,r="\t"),(e=r)===a&&(e=Ma,"\\u"===t.substr(Ma,2)?(r="\\u",Ma+=2):(r=a,0===Fa&&Ya(bn)),r!==a&&(n=Qs())!==a&&(o=Qs())!==a&&(u=Qs())!==a&&(i=Qs())!==a?(qa=e,s=n,c=o,l=u,f=i,e=r=String.fromCharCode(parseInt("0x"+s+c+l+f))):(Ma=e,e=a),e===a&&(e=Ma,92===t.charCodeAt(Ma)?(r="\\",Ma++):(r=a,0===Fa&&Ya(vn)),r!==a&&(qa=e,r="\\"),(e=r)===a&&(e=Ma,"''"===t.substr(Ma,2)?(r="''",Ma+=2):(r=a,0===Fa&&Ya(hn)),r!==a&&(qa=e,r="''"),(e=r)===a&&(e=Ma,'""'===t.substr(Ma,2)?(r='""',Ma+=2):(r=a,0===Fa&&Ya(yn)),r!==a&&(qa=e,r='""'),(e=r)===a&&(e=Ma,"``"===t.substr(Ma,2)?(r="``",Ma+=2):(r=a,0===Fa&&Ya(dn)),r!==a&&(qa=e,r="``"),e=r))))))))))))),e}function ks(){var t,e,r;return t=Ma,(e=Rs())!==a&&(qa=t,e=(r=e)&&"bigint"===r.type?r:{type:"number",value:r}),t=e}function Rs(){var t,e,r,n;return t=Ma,(e=Vs())!==a&&(r=Ms())!==a&&(n=qs())!==a?(qa=t,t=e={type:"bigint",value:e+r+n}):(Ma=t,t=a),t===a&&(t=Ma,(e=Vs())!==a&&(r=Ms())!==a?(qa=t,t=e=function(t,e){const r=t+e;return hl(t)?{type:"bigint",value:r}:parseFloat(r)}(e,r)):(Ma=t,t=a),t===a&&(t=Ma,(e=Vs())!==a&&(r=qs())!==a?(qa=t,t=e=function(t,e){return{type:"bigint",value:t+e}}(e,r)):(Ma=t,t=a),t===a&&(t=Ma,(e=Vs())!==a&&(qa=t,e=function(t){return hl(t)?{type:"bigint",value:t}:parseFloat(t)}(e)),t=e))),t}function Vs(){var e,r,n;return(e=Ps())===a&&(e=Ds())===a&&(e=Ma,45===t.charCodeAt(Ma)?(r="-",Ma++):(r=a,0===Fa&&Ya(He)),r===a&&(43===t.charCodeAt(Ma)?(r="+",Ma++):(r=a,0===Fa&&Ya(Be))),r!==a&&(n=Ps())!==a?(qa=e,e=r=r+n):(Ma=e,e=a),e===a&&(e=Ma,45===t.charCodeAt(Ma)?(r="-",Ma++):(r=a,0===Fa&&Ya(He)),r===a&&(43===t.charCodeAt(Ma)?(r="+",Ma++):(r=a,0===Fa&&Ya(Be))),r!==a&&(n=Ds())!==a?(qa=e,e=r=function(t,e){return t+e}(r,n)):(Ma=e,e=a))),e}function Ms(){var e,r,n;return e=Ma,46===t.charCodeAt(Ma)?(r=".",Ma++):(r=a,0===Fa&&Ya(x)),r!==a&&(n=Ps())!==a?(qa=e,e=r="."+n):(Ma=e,e=a),e}function qs(){var e,r,n;return e=Ma,(r=function(){var e,r,n;e=Ma,gn.test(t.charAt(Ma))?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(Sn));r!==a?(An.test(t.charAt(Ma))?(n=t.charAt(Ma),Ma++):(n=a,0===Fa&&Ya(Tn)),n===a&&(n=null),n!==a?(qa=e,e=r=r+(null!==(o=n)?o:"")):(Ma=e,e=a)):(Ma=e,e=a);var o;return e}())!==a&&(n=Ps())!==a?(qa=e,e=r=r+n):(Ma=e,e=a),e}function Ps(){var t,e,r;if(t=Ma,e=[],(r=Ds())!==a)for(;r!==a;)e.push(r),r=Ds();else e=a;return e!==a&&(qa=t,e=e.join("")),t=e}function Ds(){var e;return On.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(wn)),e}function Qs(){var e;return Ln.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(Cn)),e}function Fs(){var e,r,n,o;return e=Ma,"default"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(N)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Bs(){var e,r,n,o;return e=Ma,"to"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(xn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Hs(){var e,r,n,o;return e=Ma,"drop"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Nn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DROP"):(Ma=e,e=a)):(Ma=e,e=a),e}function $s(){var e,r,n,o;return e=Ma,"partition"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(qn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="PARTITION"):(Ma=e,e=a)):(Ma=e,e=a),e}function Ws(){var e,r,n,o;return e=Ma,"into"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Pn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Ys(){var e,r,n,o;return e=Ma,"from"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Dn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Gs(){var e,r,n,o;return e=Ma,"set"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(j)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SET"):(Ma=e,e=a)):(Ma=e,e=a),e}function Zs(){var e,r,n,o;return e=Ma,"as"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(pe)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Js(){var e,r,n,o;return e=Ma,"table"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Fn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="TABLE"):(Ma=e,e=a)):(Ma=e,e=a),e}function Xs(){var e,r,n,o;return e=Ma,"tables"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Bn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="TABLES"):(Ma=e,e=a)):(Ma=e,e=a),e}function Ks(){var e,r,n,o;return e=Ma,"on"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(Hn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function zs(){var e,r,n,o;return e=Ma,"join"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Jn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function tc(){var e,r,n,o;return e=Ma,"outer"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Xn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function ec(){var e,r,n,o;return e=Ma,"over"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Kn)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function rc(){var e,r,n,o;return e=Ma,"values"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(eo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function nc(){var e,r,n,o;return e=Ma,"using"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(ro)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function oc(){var e,r,n,o;return e=Ma,"with"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(xt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function ac(){var e,r,n,o;return e=Ma,"by"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(wt)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function uc(){var e,r,n,o;return e=Ma,"having"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(uo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function ic(){var e,r,n,o;return e=Ma,"ordinal"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(co)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ORDINAL"):(Ma=e,e=a)):(Ma=e,e=a),e}function sc(){var e,r,n,o;return e=Ma,"safe_ordinal"===t.substr(Ma,12).toLowerCase()?(r=t.substr(Ma,12),Ma+=12):(r=a,0===Fa&&Ya(lo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SAFE_ORDINAL"):(Ma=e,e=a)):(Ma=e,e=a),e}function cc(){var e,r,n,o;return e=Ma,"offset"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(po)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="OFFSET"):(Ma=e,e=a)):(Ma=e,e=a),e}function lc(){var e,r,n,o;return e=Ma,"safe_offset"===t.substr(Ma,11).toLowerCase()?(r=t.substr(Ma,11),Ma+=11):(r=a,0===Fa&&Ya(bo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="SAFE_OFFSET"):(Ma=e,e=a)):(Ma=e,e=a),e}function fc(){var e,r,n,o;return e=Ma,"desc"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(ho)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DESC"):(Ma=e,e=a)):(Ma=e,e=a),e}function pc(){var e,r,n,o;return e=Ma,"all"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(yo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ALL"):(Ma=e,e=a)):(Ma=e,e=a),e}function bc(){var e,r,n,o;return e=Ma,"distinct"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(mo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DISTINCT"):(Ma=e,e=a)):(Ma=e,e=a),e}function vc(){var e,r,n,o;return e=Ma,"between"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(jo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="BETWEEN"):(Ma=e,e=a)):(Ma=e,e=a),e}function hc(){var e,r,n,o;return e=Ma,"in"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(Oo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="IN"):(Ma=e,e=a)):(Ma=e,e=a),e}function yc(){var e,r,n,o;return e=Ma,"is"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(wo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="IS"):(Ma=e,e=a)):(Ma=e,e=a),e}function dc(){var e,r,n,o;return e=Ma,"like"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Lo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="LIKE"):(Ma=e,e=a)):(Ma=e,e=a),e}function mc(){var e,r,n,o;return e=Ma,"exists"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Co)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="EXISTS"):(Ma=e,e=a)):(Ma=e,e=a),e}function jc(){var e,r,n,o;return e=Ma,"not"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Et)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="NOT"):(Ma=e,e=a)):(Ma=e,e=a),e}function Oc(){var e,r,n,o;return e=Ma,"and"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(go)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="AND"):(Ma=e,e=a)):(Ma=e,e=a),e}function wc(){var e,r,n,o;return e=Ma,"or"===t.substr(Ma,2).toLowerCase()?(r=t.substr(Ma,2),Ma+=2):(r=a,0===Fa&&Ya(So)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="OR"):(Ma=e,e=a)):(Ma=e,e=a),e}function Lc(){var e,r,n,o;return e=Ma,"max"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(To)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="MAX"):(Ma=e,e=a)):(Ma=e,e=a),e}function Cc(){var e,r,n,o;return e=Ma,"min"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Eo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="MIN"):(Ma=e,e=a)):(Ma=e,e=a),e}function gc(){var e,r,n,o;return e=Ma,"extract"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(xo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="EXTRACT"):(Ma=e,e=a)):(Ma=e,e=a),e}function Sc(){var e,r,n,o;return e=Ma,"case"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(No)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Ac(){var e,r,n,o;return e=Ma,"end"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(Mo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?e=r=[r,n]:(Ma=e,e=a)):(Ma=e,e=a),e}function Tc(){var e,r,n,o;return e=Ma,"array"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Do)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ARRAY"):(Ma=e,e=a)):(Ma=e,e=a),e}function Ec(){var e,r,n,o;return e=Ma,"decimal"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya($o)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DECIMAL"):(Ma=e,e=a)):(Ma=e,e=a),e}function Uc(){var e,r,n,o;return e=Ma,"integer"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Zo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="INTEGER"):(Ma=e,e=a)):(Ma=e,e=a),e}function _c(){var e,r,n,o;return e=Ma,"struct"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Ko)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="STRUCT"):(Ma=e,e=a)):(Ma=e,e=a),e}function xc(){var e,r,n,o;return e=Ma,"date"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(_r)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DATE"):(Ma=e,e=a)):(Ma=e,e=a),e}function Ic(){var e,r,n,o;return e=Ma,"datetime"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(ta)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="DATETIME"):(Ma=e,e=a)):(Ma=e,e=a),e}function Nc(){var e,r,n,o;return e=Ma,"rows"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Ee)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="ROWS"):(Ma=e,e=a)):(Ma=e,e=a),e}function kc(){var e,r,n,o;return e=Ma,"time"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Wr)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="TIME"):(Ma=e,e=a)):(Ma=e,e=a),e}function Rc(){var e,r,n,o;return e=Ma,"timestamp"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(ea)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="TIMESTAMP"):(Ma=e,e=a)):(Ma=e,e=a),e}function Vc(){var e,r,n,o;return e=Ma,"interval"===t.substr(Ma,8).toLowerCase()?(r=t.substr(Ma,8),Ma+=8):(r=a,0===Fa&&Ya(oa)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="INTERVAL"):(Ma=e,e=a)):(Ma=e,e=a),e}function Mc(){var e,r,n,o;return e=Ma,"current_timestamp"===t.substr(Ma,17).toLowerCase()?(r=t.substr(Ma,17),Ma+=17):(r=a,0===Fa&&Ya(ua)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="CURRENT_TIMESTAMP"):(Ma=e,e=a)):(Ma=e,e=a),e}function qc(){var e,r,n,o;return e=Ma,"column"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(ha)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="COLUMN"):(Ma=e,e=a)):(Ma=e,e=a),e}function Pc(){var e,r,n,o;return e=Ma,"index"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(ya)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="INDEX"):(Ma=e,e=a)):(Ma=e,e=a),e}function Dc(){var e,r,n,o;return e=Ma,"key"===t.substr(Ma,3).toLowerCase()?(r=t.substr(Ma,3),Ma+=3):(r=a,0===Fa&&Ya(B)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="KEY"):(Ma=e,e=a)):(Ma=e,e=a),e}function Qc(){var e,r,n,o;return e=Ma,"comment"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(ma)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="COMMENT"):(Ma=e,e=a)):(Ma=e,e=a),e}function Fc(){var e;return 46===t.charCodeAt(Ma)?(e=".",Ma++):(e=a,0===Fa&&Ya(x)),e}function Bc(){var e;return 44===t.charCodeAt(Ma)?(e=",",Ma++):(e=a,0===Fa&&Ya(Oa)),e}function Hc(){var e;return 42===t.charCodeAt(Ma)?(e="*",Ma++):(e=a,0===Fa&&Ya($e)),e}function $c(){var e;return 40===t.charCodeAt(Ma)?(e="(",Ma++):(e=a,0===Fa&&Ya(ie)),e}function Wc(){var e;return 41===t.charCodeAt(Ma)?(e=")",Ma++):(e=a,0===Fa&&Ya(se)),e}function Yc(){var e;return 60===t.charCodeAt(Ma)?(e="<",Ma++):(e=a,0===Fa&&Ya(Qe)),e}function Gc(){var e;return 62===t.charCodeAt(Ma)?(e=">",Ma++):(e=a,0===Fa&&Ya(qe)),e}function Zc(){var e;return 91===t.charCodeAt(Ma)?(e="[",Ma++):(e=a,0===Fa&&Ya(wa)),e}function Jc(){var e;return 93===t.charCodeAt(Ma)?(e="]",Ma++):(e=a,0===Fa&&Ya(La)),e}function Xc(){var e;return 59===t.charCodeAt(Ma)?(e=";",Ma++):(e=a,0===Fa&&Ya(Ca)),e}function Kc(){var e;return(e=function(){var e;return"||"===t.substr(Ma,2)?(e="||",Ma+=2):(e=a,0===Fa&&Ya(ga)),e}())===a&&(e=function(){var e;return"&&"===t.substr(Ma,2)?(e="&&",Ma+=2):(e=a,0===Fa&&Ya(Sa)),e}()),e}function zc(){var t,e;for(t=[],(e=nl())===a&&(e=el());e!==a;)t.push(e),(e=nl())===a&&(e=el());return t}function tl(){var t,e;if(t=[],(e=nl())===a&&(e=el()),e!==a)for(;e!==a;)t.push(e),(e=nl())===a&&(e=el());else t=a;return t}function el(){var e;return(e=function(){var e,r,n,o,u,i;e=Ma,"/*"===t.substr(Ma,2)?(r="/*",Ma+=2):(r=a,0===Fa&&Ya(Aa));if(r!==a){for(n=[],o=Ma,u=Ma,Fa++,"*/"===t.substr(Ma,2)?(i="*/",Ma+=2):(i=a,0===Fa&&Ya(Ta)),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);o!==a;)n.push(o),o=Ma,u=Ma,Fa++,"*/"===t.substr(Ma,2)?(i="*/",Ma+=2):(i=a,0===Fa&&Ya(Ta)),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);n!==a?("*/"===t.substr(Ma,2)?(o="*/",Ma+=2):(o=a,0===Fa&&Ya(Ta)),o!==a?e=r=[r,n,o]:(Ma=e,e=a)):(Ma=e,e=a)}else Ma=e,e=a;return e}())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,"--"===t.substr(Ma,2)?(r="--",Ma+=2):(r=a,0===Fa&&Ya(Ea));if(r!==a){for(n=[],o=Ma,u=Ma,Fa++,i=ol(),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);o!==a;)n.push(o),o=Ma,u=Ma,Fa++,i=ol(),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);n!==a?e=r=[r,n]:(Ma=e,e=a)}else Ma=e,e=a;return e}())===a&&(e=function(){var e,r,n,o,u,i;e=Ma,35===t.charCodeAt(Ma)?(r="#",Ma++):(r=a,0===Fa&&Ya(Ua));if(r!==a){for(n=[],o=Ma,u=Ma,Fa++,i=ol(),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);o!==a;)n.push(o),o=Ma,u=Ma,Fa++,i=ol(),Fa--,i===a?u=void 0:(Ma=u,u=a),u!==a&&(i=rl())!==a?o=u=[u,i]:(Ma=o,o=a);n!==a?e=r=[r,n]:(Ma=e,e=a)}else Ma=e,e=a;return e}()),e}function rl(){var e;return t.length>Ma?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(_a)),e}function nl(){var e;return xa.test(t.charAt(Ma))?(e=t.charAt(Ma),Ma++):(e=a,0===Fa&&Ya(Ia)),e}function ol(){var e,r;if((e=function(){var e,r;e=Ma,Fa++,t.length>Ma?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(_a));Fa--,r===a?e=void 0:(Ma=e,e=a);return e}())===a)if(e=[],mn.test(t.charAt(Ma))?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(jn)),r!==a)for(;r!==a;)e.push(r),mn.test(t.charAt(Ma))?(r=t.charAt(Ma),Ma++):(r=a,0===Fa&&Ya(jn));else e=a;return e}function al(){var t,e,r,n,o,u,i,s;if(t=Ma,(e=ul())!==a){for(r=[],n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ul())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);n!==a;)r.push(n),n=Ma,(o=zc())!==a&&(u=Bc())!==a&&(i=zc())!==a&&(s=ul())!==a?n=o=[o,u,i,s]:(Ma=n,n=a);r!==a?(qa=t,t=e=l(e,r)):(Ma=t,t=a)}else Ma=t,t=a;return t}function ul(){var t,e,r,n;return t=Ma,e=Ma,(r=ps())!==a?(qa=Ma,(n=(n=!0===fl[r.toUpperCase()])?a:void 0)!==a?(qa=e,e=r=r):(Ma=e,e=a)):(Ma=e,e=a),e===a&&(e=null),e!==a&&(r=zc())!==a&&(n=il())!==a?(qa=t,t=e=function(t,e){return{field_name:t,field_type:e}}(e,n)):(Ma=t,t=a),t}function il(){var e;return(e=cl())===a&&(e=sl())===a&&(e=function(){var e,r,n,o,u,i,s,c,l,f;if(e=Ma,(r=function(){var e,r,n,o;return e=Ma,"string"===t.substr(Ma,6).toLowerCase()?(r=t.substr(Ma,6),Ma+=6):(r=a,0===Fa&&Ya(Xo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="STRING"):(Ma=e,e=a)):(Ma=e,e=a),e}())!==a){if(n=[],o=Ma,(u=zc())!==a)if((i=$c())!==a)if((s=zc())!==a){if(c=[],On.test(t.charAt(Ma))?(l=t.charAt(Ma),Ma++):(l=a,0===Fa&&Ya(wn)),l!==a)for(;l!==a;)c.push(l),On.test(t.charAt(Ma))?(l=t.charAt(Ma),Ma++):(l=a,0===Fa&&Ya(wn));else c=a;c!==a&&(l=zc())!==a&&(f=Wc())!==a?o=u=[u,i,s,c,l,f]:(Ma=o,o=a)}else Ma=o,o=a;else Ma=o,o=a;else Ma=o,o=a;for(;o!==a;)if(n.push(o),o=Ma,(u=zc())!==a)if((i=$c())!==a)if((s=zc())!==a){if(c=[],On.test(t.charAt(Ma))?(l=t.charAt(Ma),Ma++):(l=a,0===Fa&&Ya(wn)),l!==a)for(;l!==a;)c.push(l),On.test(t.charAt(Ma))?(l=t.charAt(Ma),Ma++):(l=a,0===Fa&&Ya(wn));else c=a;c!==a&&(l=zc())!==a&&(f=Wc())!==a?o=u=[u,i,s,c,l,f]:(Ma=o,o=a)}else Ma=o,o=a;else Ma=o,o=a;else Ma=o,o=a;n!==a?(qa=e,r=function(t,e){const r={dataType:t};return e&&0!==e.length?{...r,length:parseInt(e[3].join(""),10),parentheses:!0}:r}(r,n),e=r):(Ma=e,e=a)}else Ma=e,e=a;return e}())===a&&(e=function(){var e,r;e=Ma,(r=function(){var e,r,n,o;return e=Ma,"numeric"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(Ho)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="NUMERIC"):(Ma=e,e=a)):(Ma=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ma,"int64"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Go)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="INT64"):(Ma=e,e=a)):(Ma=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ma,"float64"===t.substr(Ma,7).toLowerCase()?(r=t.substr(Ma,7),Ma+=7):(r=a,0===Fa&&Ya(zo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="FLOAT64"):(Ma=e,e=a)):(Ma=e,e=a),e}())===a&&(r=Uc());r!==a&&(qa=e,r=Na(r));return e=r}())===a&&(e=function(){var e,r,n,o;e=Ma,(r=xc())===a&&(r=Ic())===a&&(r=kc())===a&&(r=Rc());if(r!==a)if(zc()!==a)if($c()!==a)if(zc()!==a){if(n=[],On.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(wn)),o!==a)for(;o!==a;)n.push(o),On.test(t.charAt(Ma))?(o=t.charAt(Ma),Ma++):(o=a,0===Fa&&Ya(wn));else n=a;n!==a&&(o=zc())!==a&&Wc()!==a?(qa=e,r={dataType:r,length:parseInt(n.join(""),10),parentheses:!0},e=r):(Ma=e,e=a)}else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;else Ma=e,e=a;e===a&&(e=Ma,(r=xc())===a&&(r=Ic())===a&&(r=kc())===a&&(r=Rc()),r!==a&&(qa=e,r=Na(r)),e=r);return e}())===a&&(e=function(){var e,r,n,o,u,i,s,c;if(e=Ma,r=Ma,(n=function(){var e,r,n,o;return e=Ma,"bytes"===t.substr(Ma,5).toLowerCase()?(r=t.substr(Ma,5),Ma+=5):(r=a,0===Fa&&Ya(Qo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="BYTES"):(Ma=e,e=a)):(Ma=e,e=a),e}())!==a)if((o=$c())!==a)if((u=zc())!==a){if(i=[],On.test(t.charAt(Ma))?(s=t.charAt(Ma),Ma++):(s=a,0===Fa&&Ya(wn)),s!==a)for(;s!==a;)i.push(s),On.test(t.charAt(Ma))?(s=t.charAt(Ma),Ma++):(s=a,0===Fa&&Ya(wn));else i=a;i===a&&("MAX"===t.substr(Ma,3)?(i="MAX",Ma+=3):(i=a,0===Fa&&Ya(ka)),i===a&&("max"===t.substr(Ma,3)?(i="max",Ma+=3):(i=a,0===Fa&&Ya(Ra)))),i!==a&&(s=zc())!==a&&(c=Wc())!==a?r=n=[n,o,u,i,s,c]:(Ma=r,r=a)}else Ma=r,r=a;else Ma=r,r=a;else Ma=r,r=a;r===a&&(r=function(){var e,r,n,o;return e=Ma,"bool"===t.substr(Ma,4).toLowerCase()?(r=t.substr(Ma,4),Ma+=4):(r=a,0===Fa&&Ya(Fo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="BOOL"):(Ma=e,e=a)):(Ma=e,e=a),e}())===a&&(r=function(){var e,r,n,o;return e=Ma,"geography"===t.substr(Ma,9).toLowerCase()?(r=t.substr(Ma,9),Ma+=9):(r=a,0===Fa&&Ya(Bo)),r!==a?(n=Ma,Fa++,o=bs(),Fa--,o===a?n=void 0:(Ma=n,n=a),n!==a?(qa=e,e=r="GEOGRAPHY"):(Ma=e,e=a)):(Ma=e,e=a),e}());r!==a&&(qa=e,r=Na(r));return e=r}()),e}function sl(){var t,e,r;return t=Ma,(e=Tc())!==a&&zc()!==a&&Yc()!==a&&zc()!==a&&(r=al())!==a&&zc()!==a&&Gc()!==a?(qa=t,t=e=Va(e,r)):(Ma=t,t=a),t}function cl(){var t,e,r;return t=Ma,(e=_c())!==a&&zc()!==a&&Yc()!==a&&zc()!==a&&(r=al())!==a&&zc()!==a&&Gc()!==a?(qa=t,t=e=Va(e,r)):(Ma=t,t=a),t}const ll={ARRAY:!0,ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CROSS:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,FOR:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!1,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,PARTITION:!0,PIVOT:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!1,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WINDOW:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0,UNNEST:!0},fl={BOOL:!0,BYTE:!0,DATE:!0,DATETIME:!0,FLOAT64:!0,INT64:!0,NUMERIC:!0,STRING:!0,TIME:!0,TIMESTAMP:!0,ARRAY:!0,STRUCT:!0};function pl(){return e.includeLocations?{loc:Wa(qa,Ma)}:{}}function bl(t,e){return{type:"unary_expr",operator:t,expr:e}}function vl(t,e,r){return{type:"binary_expr",operator:t,left:e,right:r,...pl()}}function hl(t){const e=n(Number.MAX_SAFE_INTEGER);return!(n(t)<e)}function yl(t,e,r=3){const n=[t];for(let t=0;t<e.length;t++)delete e[t][r].tableList,delete e[t][r].columnList,n.push(e[t][r]);return n}function dl(t,e){let r=t;for(let t=0;t<e.length;t++)r=vl(e[t][1],r,e[t][3]);return r}function ml(t){const e=Cl[t];return e||(t||null)}function jl(t){const e=new Set;for(let r of t.keys()){const t=r.split("::");if(!t){e.add(r);break}t&&t[1]&&(t[1]=ml(t[1])),e.add(t.join("::"))}return Array.from(e)}let Ol=[];const wl=new Set,Ll=new Set,Cl={};if((r=i())!==a&&Ma===t.length)return r;throw r!==a&&Ma<t.length&&Ya({type:"end"}),Ga(Qa,Da<t.length?t.charAt(Da):null,Da<t.length?Wa(Da,Da+1):Wa(Da,Da))}}},function(t,e,r){t.exports=r(27)},function(t,e,r){"use strict";r.r(e),function(t){var n=r(24);r.d(e,"Parser",(function(){return n.a}));var o=r(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,r(28))},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){(function(t){var n,o=function(t){"use strict";var e=1e7,r=9007199254740992,n=f(r),a="function"==typeof BigInt;function u(t,e,r,n){return void 0===t?u[0]:void 0!==e&&(10!=+e||r)?D(t,e,r,n):$(t)}function i(t,e){this.value=t,this.sign=e,this.isSmall=!1}function s(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-r<t&&t<r}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var r=t.length;if(r<4&&E(t,n)<0)switch(r){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*e;default:return t[0]+(t[1]+t[2]*e)*e}return t}function b(t){for(var e=t.length;0===t[--e];);t.length=e+1}function v(t){for(var e=new Array(t),r=-1;++r<t;)e[r]=0;return e}function h(t){return t>0?Math.floor(t):Math.ceil(t)}function y(t,r){var n,o,a=t.length,u=r.length,i=new Array(a),s=0,c=e;for(o=0;o<u;o++)s=(n=t[o]+r[o]+s)>=c?1:0,i[o]=n-s*c;for(;o<a;)s=(n=t[o]+s)===c?1:0,i[o++]=n-s*c;return s>0&&i.push(s),i}function d(t,e){return t.length>=e.length?y(t,e):y(e,t)}function m(t,r){var n,o,a=t.length,u=new Array(a),i=e;for(o=0;o<a;o++)n=t[o]-i+r,r=Math.floor(n/i),u[o]=n-r*i,r+=1;for(;r>0;)u[o++]=r%i,r=Math.floor(r/i);return u}function j(t,e){var r,n,o=t.length,a=e.length,u=new Array(o),i=0;for(r=0;r<a;r++)(n=t[r]-i-e[r])<0?(n+=1e7,i=1):i=0,u[r]=n;for(r=a;r<o;r++){if(!((n=t[r]-i)<0)){u[r++]=n;break}n+=1e7,u[r]=n}for(;r<o;r++)u[r]=t[r];return b(u),u}function O(t,e,r){var n,o,a=t.length,u=new Array(a),c=-e;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,u[n]=o<0?o+1e7:o;return"number"==typeof(u=p(u))?(r&&(u=-u),new s(u)):new i(u,r)}function w(t,e){var r,n,o,a,u=t.length,i=e.length,s=v(u+i);for(o=0;o<u;++o){a=t[o];for(var c=0;c<i;++c)r=a*e[c]+s[o+c],n=Math.floor(r/1e7),s[o+c]=r-1e7*n,s[o+c+1]+=n}return b(s),s}function L(t,r){var n,o,a=t.length,u=new Array(a),i=e,s=0;for(o=0;o<a;o++)n=t[o]*r+s,s=Math.floor(n/i),u[o]=n-s*i;for(;s>0;)u[o++]=s%i,s=Math.floor(s/i);return u}function C(t,e){for(var r=[];e-- >0;)r.push(0);return r.concat(t)}function g(t,r,n){return new i(t<e?L(r,t):w(r,f(t)),n)}function S(t){var e,r,n,o,a=t.length,u=v(a+a);for(n=0;n<a;n++){r=0-(o=t[n])*o;for(var i=n;i<a;i++)e=o*t[i]*2+u[n+i]+r,r=Math.floor(e/1e7),u[n+i]=e-1e7*r;u[n+a]=r}return b(u),u}function A(t,e){var r,n,o,a,u=t.length,i=v(u);for(o=0,r=u-1;r>=0;--r)o=(a=1e7*o+t[r])-(n=h(a/e))*e,i[r]=0|n;return[i,0|o]}function T(t,r){var n,o=$(r);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,y=t.value,d=o.value;if(0===d)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new s(h(y/d)),new s(y%d)]:[u[0],t];if(o.isSmall){if(1===d)return[t,u[0]];if(-1==d)return[t.negate(),u[0]];var m=Math.abs(d);if(m<e){l=p((n=A(y,m))[0]);var O=n[1];return t.sign&&(O=-O),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new s(l),new s(O)]):[new i(l,t.sign!==o.sign),new s(O)]}d=f(m)}var w=E(y,d);if(-1===w)return[u[0],t];if(0===w)return[u[t.sign===o.sign?1:-1],u[0]];l=(n=y.length+d.length<=200?function(t,r){var n,o,a,u,i,s,c,l=t.length,f=r.length,b=e,h=v(r.length),y=r[f-1],d=Math.ceil(b/(2*y)),m=L(t,d),j=L(r,d);for(m.length<=l&&m.push(0),j.push(0),y=j[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==y&&(n=Math.floor((m[o+f]*b+m[o+f-1])/y)),a=0,u=0,s=j.length,i=0;i<s;i++)a+=n*j[i],c=Math.floor(a/b),u+=m[o+i]-(a-c*b),a=c,u<0?(m[o+i]=u+b,u=-1):(m[o+i]=u,u=0);for(;0!==u;){for(n-=1,a=0,i=0;i<s;i++)(a+=m[o+i]-b+j[i])<0?(m[o+i]=a+b,a=0):(m[o+i]=a,a=1);u+=a}h[o]=n}return m=A(m,d)[0],[p(h),p(m)]}(y,d):function(t,e){for(var r,n,o,a,u,i=t.length,s=e.length,c=[],l=[];i;)if(l.unshift(t[--i]),b(l),E(l,e)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*e[s-1]+e[s-2],n>s&&(o=1e7*(o+1)),r=Math.ceil(o/a);do{if(E(u=L(e,r),l)<=0)break;r--}while(r);c.push(r),l=j(l,u)}return c.reverse(),[p(c),p(l)]}(y,d))[0];var C=t.sign!==o.sign,g=n[1],S=t.sign;return"number"==typeof l?(C&&(l=-l),l=new s(l)):l=new i(l,C),"number"==typeof g?(S&&(g=-g),g=new s(g)):g=new i(g,S),[l,g]}function E(t,e){if(t.length!==e.length)return t.length>e.length?1:-1;for(var r=t.length-1;r>=0;r--)if(t[r]!==e[r])return t[r]>e[r]?1:-1;return 0}function U(t){var e=t.abs();return!e.isUnit()&&(!!(e.equals(2)||e.equals(3)||e.equals(5))||!(e.isEven()||e.isDivisibleBy(3)||e.isDivisibleBy(5))&&(!!e.lesser(49)||void 0))}function _(t,e){for(var r,n,a,u=t.prev(),i=u,s=0;i.isEven();)i=i.divide(2),s++;t:for(n=0;n<e.length;n++)if(!t.lesser(e[n])&&!(a=o(e[n]).modPow(i,t)).isUnit()&&!a.equals(u)){for(r=s-1;0!=r;r--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(u))continue t}return!1}return!0}i.prototype=Object.create(u.prototype),s.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),i.prototype.add=function(t){var e=$(t);if(this.sign!==e.sign)return this.subtract(e.negate());var r=this.value,n=e.value;return e.isSmall?new i(m(r,Math.abs(n)),this.sign):new i(d(r,n),this.sign)},i.prototype.plus=i.prototype.add,s.prototype.add=function(t){var e=$(t),r=this.value;if(r<0!==e.sign)return this.subtract(e.negate());var n=e.value;if(e.isSmall){if(l(r+n))return new s(r+n);n=f(Math.abs(n))}return new i(m(n,Math.abs(r)),r<0)},s.prototype.plus=s.prototype.add,c.prototype.add=function(t){return new c(this.value+$(t).value)},c.prototype.plus=c.prototype.add,i.prototype.subtract=function(t){var e=$(t);if(this.sign!==e.sign)return this.add(e.negate());var r=this.value,n=e.value;return e.isSmall?O(r,Math.abs(n),this.sign):function(t,e,r){var n;return E(t,e)>=0?n=j(t,e):(n=j(e,t),r=!r),"number"==typeof(n=p(n))?(r&&(n=-n),new s(n)):new i(n,r)}(r,n,this.sign)},i.prototype.minus=i.prototype.subtract,s.prototype.subtract=function(t){var e=$(t),r=this.value;if(r<0!==e.sign)return this.add(e.negate());var n=e.value;return e.isSmall?new s(r-n):O(n,Math.abs(r),r>=0)},s.prototype.minus=s.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-$(t).value)},c.prototype.minus=c.prototype.subtract,i.prototype.negate=function(){return new i(this.value,!this.sign)},s.prototype.negate=function(){var t=this.sign,e=new s(-this.value);return e.sign=!t,e},c.prototype.negate=function(){return new c(-this.value)},i.prototype.abs=function(){return new i(this.value,!1)},s.prototype.abs=function(){return new s(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},i.prototype.multiply=function(t){var r,n,o,a=$(t),s=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((r=Math.abs(c))<e)return new i(L(s,r),l);c=f(r)}return n=s.length,o=c.length,new i(-.012*n-.012*o+15e-6*n*o>0?function t(e,r){var n=Math.max(e.length,r.length);if(n<=30)return w(e,r);n=Math.ceil(n/2);var o=e.slice(n),a=e.slice(0,n),u=r.slice(n),i=r.slice(0,n),s=t(a,i),c=t(o,u),l=t(d(a,o),d(i,u)),f=d(d(s,C(j(j(l,s),c),n)),C(c,2*n));return b(f),f}(s,c):w(s,c),l)},i.prototype.times=i.prototype.multiply,s.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new s(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},i.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},s.prototype.multiply=function(t){return $(t)._multiplyBySmall(this)},s.prototype.times=s.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*$(t).value)},c.prototype.times=c.prototype.multiply,i.prototype.square=function(){return new i(S(this.value),!1)},s.prototype.square=function(){var t=this.value*this.value;return l(t)?new s(t):new i(S(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},i.prototype.divmod=function(t){var e=T(this,t);return{quotient:e[0],remainder:e[1]}},c.prototype.divmod=s.prototype.divmod=i.prototype.divmod,i.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/$(t).value)},s.prototype.over=s.prototype.divide=i.prototype.over=i.prototype.divide,i.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%$(t).value)},s.prototype.remainder=s.prototype.mod=i.prototype.remainder=i.prototype.mod,i.prototype.pow=function(t){var e,r,n,o=$(t),a=this.value,i=o.value;if(0===i)return u[1];if(0===a)return u[0];if(1===a)return u[1];if(-1===a)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(e=Math.pow(a,i)))return new s(h(e));for(r=this,n=u[1];!0&i&&(n=n.times(r),--i),0!==i;)i/=2,r=r.square();return n},s.prototype.pow=i.prototype.pow,c.prototype.pow=function(t){var e=$(t),r=this.value,n=e.value,o=BigInt(0),a=BigInt(1),i=BigInt(2);if(n===o)return u[1];if(r===o)return u[0];if(r===a)return u[1];if(r===BigInt(-1))return e.isEven()?u[1]:u[-1];if(e.isNegative())return new c(o);for(var s=this,l=u[1];(n&a)===a&&(l=l.times(s),--n),n!==o;)n/=i,s=s.square();return l},i.prototype.modPow=function(t,e){if(t=$(t),(e=$(e)).isZero())throw new Error("Cannot take modPow with modulus 0");var r=u[1],n=this.mod(e);for(t.isNegative()&&(t=t.multiply(u[-1]),n=n.modInv(e));t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(r=r.multiply(n).mod(e)),t=t.divide(2),n=n.square().mod(e)}return r},c.prototype.modPow=s.prototype.modPow=i.prototype.modPow,i.prototype.compareAbs=function(t){var e=$(t),r=this.value,n=e.value;return e.isSmall?1:E(r,n)},s.prototype.compareAbs=function(t){var e=$(t),r=Math.abs(this.value),n=e.value;return e.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},c.prototype.compareAbs=function(t){var e=this.value,r=$(t).value;return(e=e>=0?e:-e)===(r=r>=0?r:-r)?0:e>r?1:-1},i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=$(t),r=this.value,n=e.value;return this.sign!==e.sign?e.sign?1:-1:e.isSmall?this.sign?-1:1:E(r,n)*(this.sign?-1:1)},i.prototype.compareTo=i.prototype.compare,s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=$(t),r=this.value,n=e.value;return e.isSmall?r==n?0:r>n?1:-1:r<0!==e.sign?r<0?-1:1:r<0?1:-1},s.prototype.compareTo=s.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=this.value,r=$(t).value;return e===r?0:e>r?1:-1},c.prototype.compareTo=c.prototype.compare,i.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=s.prototype.eq=s.prototype.equals=i.prototype.eq=i.prototype.equals,i.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=s.prototype.neq=s.prototype.notEquals=i.prototype.neq=i.prototype.notEquals,i.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=s.prototype.gt=s.prototype.greater=i.prototype.gt=i.prototype.greater,i.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=s.prototype.lt=s.prototype.lesser=i.prototype.lt=i.prototype.lesser,i.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals,i.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals,i.prototype.isEven=function(){return 0==(1&this.value[0])},s.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},i.prototype.isOdd=function(){return 1==(1&this.value[0])},s.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},i.prototype.isPositive=function(){return!this.sign},s.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=s.prototype.isPositive,i.prototype.isNegative=function(){return this.sign},s.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=s.prototype.isNegative,i.prototype.isUnit=function(){return!1},s.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},i.prototype.isZero=function(){return!1},s.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},i.prototype.isDivisibleBy=function(t){var e=$(t);return!e.isZero()&&(!!e.isUnit()||(0===e.compareAbs(2)?this.isEven():this.mod(e).isZero()))},c.prototype.isDivisibleBy=s.prototype.isDivisibleBy=i.prototype.isDivisibleBy,i.prototype.isPrime=function(t){var e=U(this);if(void 0!==e)return e;var r=this.abs(),n=r.bitLength();if(n<=64)return _(r,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),u=Math.ceil(!0===t?2*Math.pow(a,2):a),i=[],s=0;s<u;s++)i.push(o(s+2));return _(r,i)},c.prototype.isPrime=s.prototype.isPrime=i.prototype.isPrime,i.prototype.isProbablePrime=function(t,e){var r=U(this);if(void 0!==r)return r;for(var n=this.abs(),a=void 0===t?5:t,u=[],i=0;i<a;i++)u.push(o.randBetween(2,n.minus(2),e));return _(n,u)},c.prototype.isProbablePrime=s.prototype.isProbablePrime=i.prototype.isProbablePrime,i.prototype.modInv=function(t){for(var e,r,n,a=o.zero,u=o.one,i=$(t),s=this.abs();!s.isZero();)e=i.divide(s),r=a,n=i,a=u,i=s,u=r.subtract(e.multiply(u)),s=n.subtract(e.multiply(s));if(!i.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=s.prototype.modInv=i.prototype.modInv,i.prototype.next=function(){var t=this.value;return this.sign?O(t,1,this.sign):new i(m(t,1),this.sign)},s.prototype.next=function(){var t=this.value;return t+1<r?new s(t+1):new i(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},i.prototype.prev=function(){var t=this.value;return this.sign?new i(m(t,1),!0):O(t,1,this.sign)},s.prototype.prev=function(){var t=this.value;return t-1>-r?new s(t-1):new i(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=e;)x.push(2*x[x.length-1]);var I=x.length,N=x[I-1];function k(t){return Math.abs(t)<=e}function R(t,e,r){e=$(e);for(var n=t.isNegative(),a=e.isNegative(),u=n?t.not():t,i=a?e.not():e,s=0,c=0,l=null,f=null,p=[];!u.isZero()||!i.isZero();)s=(l=T(u,N))[1].toJSNumber(),n&&(s=N-1-s),c=(f=T(i,N))[1].toJSNumber(),a&&(c=N-1-c),u=l[0],i=f[0],p.push(r(s,c));for(var b=0!==r(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(N).add(o(p[v]));return b}i.prototype.shiftLeft=function(t){var e=$(t).toJSNumber();if(!k(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftRight(-e);var r=this;if(r.isZero())return r;for(;e>=I;)r=r.multiply(N),e-=I-1;return r.multiply(x[e])},c.prototype.shiftLeft=s.prototype.shiftLeft=i.prototype.shiftLeft,i.prototype.shiftRight=function(t){var e,r=$(t).toJSNumber();if(!k(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=I;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(e=T(n,N))[1].isNegative()?e[0].prev():e[0],r-=I-1}return(e=T(n,x[r]))[1].isNegative()?e[0].prev():e[0]},c.prototype.shiftRight=s.prototype.shiftRight=i.prototype.shiftRight,i.prototype.not=function(){return this.negate().prev()},c.prototype.not=s.prototype.not=i.prototype.not,i.prototype.and=function(t){return R(this,t,(function(t,e){return t&e}))},c.prototype.and=s.prototype.and=i.prototype.and,i.prototype.or=function(t){return R(this,t,(function(t,e){return t|e}))},c.prototype.or=s.prototype.or=i.prototype.or,i.prototype.xor=function(t){return R(this,t,(function(t,e){return t^e}))},c.prototype.xor=s.prototype.xor=i.prototype.xor;function V(t){var r=t.value,n="number"==typeof r?r|1<<30:"bigint"==typeof r?r|BigInt(1<<30):r[0]+r[1]*e|1073758208;return n&-n}function M(t,e){return t=$(t),e=$(e),t.greater(e)?t:e}function q(t,e){return t=$(t),e=$(e),t.lesser(e)?t:e}function P(t,e){if(t=$(t).abs(),e=$(e).abs(),t.equals(e))return t;if(t.isZero())return e;if(e.isZero())return t;for(var r,n,o=u[1];t.isEven()&&e.isEven();)r=q(V(t),V(e)),t=t.divide(r),e=e.divide(r),o=o.multiply(r);for(;t.isEven();)t=t.divide(V(t));do{for(;e.isEven();)e=e.divide(V(e));t.greater(e)&&(n=e,e=t,t=n),e=e.subtract(t)}while(!e.isZero());return o.isUnit()?t:t.multiply(o)}i.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(e,r){if(r.compareTo(e)<=0){var n=t(e,r.square(r)),a=n.p,u=n.e,i=a.multiply(r);return i.compareTo(e)<=0?{p:i,e:2*u+1}:{p:a,e:2*u}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=s.prototype.bitLength=i.prototype.bitLength;var D=function(t,e,r,n){r=r||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),r=r.toLowerCase());var o,a=t.length,u=Math.abs(e),i={};for(o=0;o<r.length;o++)i[r[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in i&&i[l]>=u)){if("1"===l&&1===u)continue;throw new Error(l+" is not a valid digit in base "+e+".")}}e=$(e);var s=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in i)s.push($(i[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);s.push($(t.slice(f+1,o)))}}return Q(s,e,c)};function Q(t,e,r){var n,o=u[0],a=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(e);return r?o.negate():o}function F(t,e){if((e=o(e)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(e.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var n=!1;if(t.isNegative()&&e.isPositive()&&(n=!0,t=t.abs()),e.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,u=[],i=t;i.isNegative()||i.compareAbs(e)>=0;){a=i.divmod(e),i=a.quotient;var s=a.remainder;s.isNegative()&&(s=e.minus(s).abs(),i=i.next()),u.push(s.toJSNumber())}return u.push(i.toJSNumber()),{value:u.reverse(),isNegative:n}}function B(t,e,r){var n=F(t,e);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,e){return t<(e=e||"0123456789abcdefghijklmnopqrstuvwxyz").length?e[t]:"<"+t+">"}(t,r)})).join("")}function H(t){if(l(+t)){var e=+t;if(e===h(e))return a?new c(BigInt(e)):new s(e);throw new Error("Invalid integer: "+t)}var r="-"===t[0];r&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==h(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var u=n[0],f=u.indexOf(".");if(f>=0&&(o-=u.length-f-1,u=u.slice(0,f)+u.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(r?"-"+t:t));for(var p=[],v=t.length,y=v-7;v>0;)p.push(+t.slice(y,v)),(y-=7)<0&&(y=0),v-=7;return b(p),new i(p,r)}function $(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==h(t))throw new Error(t+" is not an integer.");return new s(t)}return H(t.toString())}(t):"string"==typeof t?H(t):"bigint"==typeof t?new c(t):t}i.prototype.toArray=function(t){return F(this,t)},s.prototype.toArray=function(t){return F(this,t)},c.prototype.toArray=function(t){return F(this,t)},i.prototype.toString=function(t,e){if(void 0===t&&(t=10),10!==t||e)return B(this,t,e);for(var r,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)r=String(n[o]),a+="0000000".slice(r.length)+r;return(this.sign?"-":"")+a},s.prototype.toString=function(t,e){return void 0===t&&(t=10),10!=t||e?B(this,t,e):String(this.value)},c.prototype.toString=s.prototype.toString,c.prototype.toJSON=i.prototype.toJSON=s.prototype.toJSON=function(){return this.toString()},i.prototype.valueOf=function(){return parseInt(this.toString(),10)},i.prototype.toJSNumber=i.prototype.valueOf,s.prototype.valueOf=function(){return this.value},s.prototype.toJSNumber=s.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var W=0;W<1e3;W++)u[W]=$(W),W>0&&(u[-W]=$(-W));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=M,u.min=q,u.gcd=P,u.lcm=function(t,e){return t=$(t).abs(),e=$(e).abs(),t.divide(P(t,e)).multiply(e)},u.isInstance=function(t){return t instanceof i||t instanceof s||t instanceof c},u.randBetween=function(t,r,n){t=$(t),r=$(r);var o=n||Math.random,a=q(t,r),i=M(t,r).subtract(a).add(1);if(i.isSmall)return a.add(Math.floor(o()*i));for(var s=F(i,e).value,c=[],l=!0,f=0;f<s.length;f++){var p=l?s[f]+(f+1<s.length?s[f+1]/e:0):e,b=h(o()*p);c.push(b),b<s[f]&&(l=!1)}return a.add(u.fromArray(c,e,!1))},u.fromArray=function(t,e,r){return Q(t.map($),$(e||10),r)},u}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(e,r,e,t))||(t.exports=n)}).call(this,r(30)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=bigquery.umd.js.map