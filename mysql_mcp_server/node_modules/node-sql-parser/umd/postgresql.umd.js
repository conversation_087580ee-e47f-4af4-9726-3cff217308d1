!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=26)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return E})),e.d(r,"autoIncrementToSQL",(function(){return _})),e.d(r,"columnOrderListToSQL",(function(){return x})),e.d(r,"commonKeywordArgsToSQL",(function(){return U})),e.d(r,"commonOptionConnector",(function(){return i})),e.d(r,"connector",(function(){return c})),e.d(r,"commonTypeValue",(function(){return C})),e.d(r,"commentToSQL",(function(){return A})),e.d(r,"createBinaryExpr",(function(){return f})),e.d(r,"createValueExpr",(function(){return l})),e.d(r,"dataTypeToSQL",(function(){return g})),e.d(r,"DEFAULT_OPT",(function(){return u})),e.d(r,"escape",(function(){return p})),e.d(r,"literalToSQL",(function(){return L})),e.d(r,"columnIdentifierToSql",(function(){return d})),e.d(r,"getParserOpt",(function(){return b})),e.d(r,"identifierToSql",(function(){return h})),e.d(r,"onPartitionsToSQL",(function(){return j})),e.d(r,"replaceParams",(function(){return O})),e.d(r,"returningToSQL",(function(){return S})),e.d(r,"hasVal",(function(){return m})),e.d(r,"setParserOpt",(function(){return v})),e.d(r,"toUpper",(function(){return w})),e.d(r,"topToSQL",(function(){return y})),e.d(r,"triggerEventToSQL",(function(){return T}));var n=e(2),o=e(11);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u={database:"postgresql",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},a=u;function i(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function c(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function l(t){var r=s(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function f(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:l(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(e[0]),l(e[1])]},n):(n.right=e.type?e:l(e),n)}function p(t){return t}function b(){return a}function v(t){a=t}function y(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function d(t){var r=b().database;if(t)switch(r&&r.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function h(t,r){var e=b().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function w(t){if(t)return t.toUpperCase()}function m(t){return t}function L(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,u=t.suffix,a=t.value,i="object"===s(t)?a:t;switch(e){case"backticks_quote_string":i="`".concat(a,"`");break;case"string":i="'".concat(a,"'");break;case"regex_string":i='r"'.concat(a,'"');break;case"hex_string":i="X'".concat(a,"'");break;case"full_hex_string":i="0x".concat(a);break;case"natural_string":i="N'".concat(a,"'");break;case"bit_string":i="b'".concat(a,"'");break;case"double_quote_string":i='"'.concat(a,'"');break;case"single_quote_string":i="'".concat(a,"'");break;case"boolean":case"bool":i=a?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(r||":").concat(a),r=null;break;case"origin":i=a.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(e.toUpperCase()," '").concat(a,"'");break;case"var_string":i="N'".concat(a,"'");break;case"unicode_string":i="U&'".concat(a,"'")}var c=[];return r&&c.push(w(r)),c.push(i),u&&("string"==typeof u&&c.push(u),"object"===s(u)&&(u.collate?c.push(Object(o.a)(u.collate)):c.push(L(u)))),i=c.join(" "),n?"(".concat(i,")"):i}}function C(t){if(!t)return[];var r=t.type,e=t.symbol,n=t.value;return[r.toUpperCase(),e,"string"==typeof n?n.toUpperCase():L(n)].filter(m)}function O(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===s(e)&&null!==e})).forEach((function(n){var o=r[n];if("object"!==s(o)||"param"!==o.type)return t(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return r[n]=l(e[o.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function j(t){var r=t.type,e=t.partitions;return[w(r),"(".concat(e.map((function(t){if("range"!==t.type)return L(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(L(r)," ").concat(w(n)," ").concat(L(e))})).join(", "),")")].join(" ")}function g(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,s=t.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),s&&s.length&&(u+=" ".concat(s.join(" "))),"".concat(r).concat(u)}function E(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=w(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var s=e&&e.map((function(t){return[t.field_name,E(t.field_type)].filter(m).join(" ")})).join(", ");return n?"".concat(o,"<").concat(s,">"):"".concat(o," ").concat(s)}}function A(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(L(o)),r.join(" ")}}function T(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[w(r)];if(e){var s=e.keyword,u=e.columns;o.push(w(s),u.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function S(t){return t?["RETURNING",t.columns.map(n.h).filter(m).join(", ")].join(" "):""}function U(t){return t?[w(t.keyword),w(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var r=b().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,s=t.parentheses,u=w(e);return s&&(u+="(".concat(L(n),", ").concat(L(o),")")),u}}function x(t){if(t)return t.map(n.e).filter(m).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return j})),e.d(r,"b",(function(){return g})),e.d(r,"d",(function(){return O})),e.d(r,"c",(function(){return E}));var n=e(0),o=e(9),s=e(13);var u=e(22),a=e(21);var i=e(11),c=e(2),l=e(6),f=e(18);var p=e(7),b=e(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(c.i)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,s=t.parentheses;if(!o)return"[".concat(Object(c.i)(r),"]");var u=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):j(o);return e?"[".concat(u,"]"):s?"(".concat(u,")"):u}(t);default:return""}}function d(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===v(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(y(t)),o.filter(n.hasVal).join("")}var h=e(3),w=e(5),m=e(20);function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var C={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,u=t.within_group_orderby,a=j(r.expr);a=Array.isArray(a)?a.join(", "):a;var i=t.name,c=Object(s.a)(o);r.distinct&&(a=["DISTINCT",a].join(" ")),r.separator&&r.separator.delimiter&&(a=[a,Object(n.literalToSQL)(r.separator.delimiter)].join("".concat(r.separator.symbol," "))),r.separator&&r.separator.expr&&(a=[a,j(r.separator.expr)].join(" ")),r.orderby&&(a=[a,E(r.orderby,"order by")].join(" ")),r.separator&&r.separator.value&&(a=[a,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=u?"WITHIN GROUP (".concat(E(u,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(j(e.where),")"):"";return["".concat(i,"(").concat(a,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:m.c,array:d,assign:u.a,binary_expr:a.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(j(n));for(var s=0,u=e.length;s<u;++s)r.push(e[s].type.toUpperCase()),e[s].cond&&(r.push(j(e[s].cond)),r.push("THEN")),r.push(j(e[s].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:l.c,collate:i.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:w.b,interval:f.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return j(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,j(t.expr)].join("")},func_arg:l.f,show:b.a,struct:d,tablefunc:l.j,tables:h.c,unnest:h.d,window:m.b};function O(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,s=t.members,u=t.quoted,a=t.suffix,i=[],c=s&&s.length>0?"".concat(o,".").concat(s.join(".")):o,l="".concat(e||"").concat(c);return a&&(l+=a),i.push(l),[u,i.join(" "),u].filter(n.hasVal).join("")}function j(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,s=Object.keys(e);o<s.length;o++){var u=s[o];r[u]=e[u]}}var a=r.type;return"expr"===a?j(r.expr):C[a]?C[a](r):Object(n.literalToSQL)(r)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(j)):[]}function E(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[j(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return j(t.expr)}))}return Object(n.connector)(o,e.join(", "))}C.var=O,C.expr_list=function(t){var r=g(t.value),e=t.parentheses,n=t.separator;if(!e&&!n)return r;var o=n||", ",s=r.join(o);return e?"(".concat(s,")"):s},C.select=function(t){var r="object"===L(t._next)?Object(w.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(r,")"):r},C.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",s="".concat(r).concat(o).concat(j(n));return e?"(".concat(s,")"):s},C.map_object=function(t){var r=t.keyword,e=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(r),"[".concat(e,"]")].join("")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"c",(function(){return L})),e.d(r,"f",(function(){return y})),e.d(r,"h",(function(){return j})),e.d(r,"i",(function(){return E})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return b})),e.d(r,"e",(function(){return m})),e.d(r,"g",(function(){return h})),e.d(r,"j",(function(){return O})),e.d(r,"k",(function(){return g}));var n=e(11),o=e(19),s=e(1),u=e(6),a=e(3),i=e(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function b(t,r){if("string"==typeof t)return Object(i.identifierToSql)(t,r);var e=t.expr,n=t.offset,o=t.suffix,u=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(s.a)(e),u,o].filter(i.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var r,e=[],n=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=f(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,s=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(t);try{for(n.s();!(r=n.n()).done;){var o=r.value,s=o.brackets?"[".concat(Object(i.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(i.literalToSQL)(o.index));o.property&&(s="".concat(s,".").concat(Object(i.literalToSQL)(o.property))),e.push(s)}}catch(t){n.e(t)}finally{n.f()}return e.join("")}function y(t){var r=t.array_index,e=t.as,o=t.column,u=t.collate,a=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,y=t.options,d=t.schema,h=t.table,w=t.parentheses,m=t.suffix,L=t.order_by,C=t.subFields,O=void 0===C?[]:C,j="*"===o?"*":b(o,c),g=[a,d,h].filter(i.hasVal).map((function(t){return"".concat("string"==typeof t?Object(i.identifierToSql)(t):Object(s.a)(t))})),E=g[0];if(E){for(var A=1;A<g.length;++A)E="".concat(E).concat(p[A]||".").concat(g[A]);j="".concat(E).concat(p[A]||".").concat(j)}var T=[j=["".concat(j).concat(v(r))].concat(l(O)).join("."),Object(n.a)(u),Object(s.a)(y),Object(i.commonOptionConnector)("AS",s.a,e)];T.push("string"==typeof m?Object(i.toUpper)(m):Object(s.a)(m)),T.push(Object(i.toUpper)(L));var S=T.filter(i.hasVal).join(" ");return w?"(".concat(S,")"):S}function d(t){if(t){var r=t.dataType,e=t.length,n=t.suffix,o=t.scale,a=t.expr,c=null!=e,l=Object(i.dataTypeToSQL)({dataType:r,length:e,suffix:n,scale:o,parentheses:c});if(a&&(l+=Object(s.a)(a)),t.array){var f=Object(u.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function h(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,o=t.match,u=t.table,c=t.on_action;return r.push(Object(i.toUpper)(n)),r.push(Object(a.c)(u)),r.push(e&&"(".concat(e.map((function(t){return Object(s.a)(t)})).join(", "),")")),r.push(Object(i.toUpper)(o)),c.map((function(t){return r.push(Object(i.toUpper)(t.type),Object(s.a)(t.value))})),r.filter(i.hasVal)}function w(t){var r=[],e=t.nullable,n=t.character_set,u=t.check,a=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,y=t.generated,d=t.auto_increment,w=t.unique,m=t.primary_key,L=t.column_format,C=t.reference_definition,O=[Object(i.toUpper)(e&&e.action),Object(i.toUpper)(e&&e.value)].filter(i.hasVal).join(" ");if(y||r.push(O),v){var j=v.type,g=v.value;r.push(j.toUpperCase(),Object(s.a)(g))}var E=Object(i.getParserOpt)().database;return c&&r.push(Object(i.toUpper)(c.keyword),Object(i.literalToSQL)(c.constraint)),r.push(Object(o.a)(u)),r.push(function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(s.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(y)),y&&r.push(O),r.push(Object(i.autoIncrementToSQL)(d),Object(i.toUpper)(m),Object(i.toUpper)(w),Object(i.commentToSQL)(a)),r.push.apply(r,l(Object(i.commonTypeValue)(n))),"sqlite"!==E.toLowerCase()&&r.push(Object(s.a)(f)),r.push.apply(r,l(Object(i.commonTypeValue)(L))),r.push.apply(r,l(Object(i.commonTypeValue)(p))),r.push.apply(r,l(h(C))),r.push(Object(i.commonOptionConnector)("USING",s.a,b)),r.filter(i.hasVal).join(" ")}function m(t){var r=t.column,e=t.collate,n=t.nulls,o=t.opclass,u=t.order_by,a="string"==typeof r?{type:"column_ref",table:t.table,column:r}:t;return a.collate=null,[Object(s.a)(a),Object(s.a)(e),o,Object(i.toUpper)(u),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function L(t){var r=[],e=y(t.column),n=d(t.definition);return r.push(e),r.push(n),r.push(w(t)),r.filter(i.hasVal).join(" ")}function C(t){return t?"object"===c(t)?["AS",Object(s.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function O(t){var r=t.against,e=t.as,n=t.columns,o=t.match,u=t.mode;return[[Object(i.toUpper)(o),"(".concat(n.map((function(t){return y(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(r),["(",Object(s.a)(t.expr),u&&" ".concat(Object(i.literalToSQL)(u)),")"].filter(i.hasVal).join("")].join(" "),C(e)].filter(i.hasVal).join(" ")}function j(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(u.c)(t);r&&(e.isDual=r);var o=Object(s.a)(e),a=t.expr_list;if(a){var c=[o],l=a.map((function(t){return j(t,r)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(v(e.array_index))),[o,C(t.as)].filter(i.hasVal).join(" ")}function g(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}function E(t,r){if(!t||"*"===t)return t;var e=g(r);return t.map((function(t){return j(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"c",(function(){return h})),e.d(r,"a",(function(){return w})),e.d(r,"b",(function(){return d})),e.d(r,"d",(function(){return f}));var n=e(21),o=e(2),s=e(1),u=e(17),a=e(18),i=e(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(i.toUpper)(r),"(").concat(n&&Object(s.a)(n)||"",")"),Object(i.commonOptionConnector)("AS","string"==typeof e?i.identifierToSql:s.a,e),Object(i.commonOptionConnector)(Object(i.toUpper)(o&&o.keyword),i.identifierToSql,o&&o.as)].filter(i.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,u=t.expr,a=t.in_expr,c=t.type,l=[Object(s.a)(u),"FOR",Object(o.f)(e),Object(n.a)(a)],f=["".concat(Object(i.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(i.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function b(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,u=t.parentheses,a=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(i.toUpper)(r),"(".concat(Object(i.identifierToSql)(n)),"(".concat(o.map(s.a).filter(i.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(i.toUpper)(r),"=",Object(s.a)(e));break;case"index":c.push(Object(i.toUpper)(a),Object(i.toUpper)(r),u?"(".concat(e.map(i.identifierToSql).join(", "),")"):"= ".concat(Object(i.identifierToSql)(e)));break;default:c.push(Object(s.a)(e))}return c.filter(i.hasVal).join(" ")}}function v(t,r){var e=t.name,n=t.symbol;return[Object(i.toUpper)(e),n,r].filter(i.hasVal).join(" ")}function y(t){var r=[];switch(t.keyword){case"as":r.push("AS","OF",Object(s.a)(t.of));break;case"from_to":r.push("FROM",Object(s.a)(t.from),"TO",Object(s.a)(t.to));break;case"between_and":r.push("BETWEEN",Object(s.a)(t.between),"AND",Object(s.a)(t.and));break;case"contained":r.push("CONTAINED","IN",Object(s.a)(t.in))}return r.filter(i.hasVal).join(" ")}function d(t){if("UNNEST"===Object(i.toUpper)(t.type))return f(t);var r,e,n,c,l=t.table,d=t.db,h=t.as,w=t.expr,m=t.operator,L=t.prefix,C=t.schema,O=t.server,j=t.suffix,g=t.tablesample,E=t.temporal_table,A=t.table_hint,T=Object(i.identifierToSql)(O),S=Object(i.identifierToSql)(d),U=Object(i.identifierToSql)(C),_=l&&Object(i.identifierToSql)(l);if(w)switch(w.type){case"values":var x=w.parentheses,I=w.values,N=w.prefix,R=[x&&"(","",x&&")"],k=Object(u.b)(I);N&&(k=k.split("(").slice(1).map((function(t){return"".concat(Object(i.toUpper)(N),"(").concat(t)})).join("")),R[1]="VALUES ".concat(k),_=R.filter(i.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.offset,s=t.size,u=[Object(i.identifierToSql)(r.expr.db),Object(i.identifierToSql)(r.expr.schema),Object(i.identifierToSql)(r.expr.table)].filter(i.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(e.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(r,u)),v(e,c)],f=v(s,Object(a.a)(s.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(a.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(i.hasVal).join(", ")}(w);break;case"generator":e=(r=w).keyword,n=r.type,c=r.generators.map((function(t){return Object(i.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(i.toUpper)(e),"(").concat(Object(i.toUpper)(n),"(").concat(c,"))");break;default:_=Object(s.a)(w)}var M=[[T,S,U,_=[Object(i.toUpper)(L),_,Object(i.toUpper)(j)].filter(i.hasVal).join(" ")].filter(i.hasVal).join(".")];if(g){var V=["TABLESAMPLE",Object(s.a)(g.expr),Object(i.literalToSQL)(g.repeatable)].filter(i.hasVal).join(" ");M.push(V)}M.push(function(t){if(t){var r=t.keyword,e=t.expr;return[Object(i.toUpper)(r),y(e)].filter(i.hasVal).join(" ")}}(E),Object(i.commonOptionConnector)("AS","string"==typeof h?i.identifierToSql:s.a,h),p(m)),A&&M.push(Object(i.toUpper)(A.keyword),"(".concat(A.expr.map(b).filter(i.hasVal).join(", "),")"));var q=M.filter(i.hasVal).join(" ");return t.parentheses?"(".concat(q,")"):q}function h(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=t.joins,o=h(r);if(e){for(var u=[],a=[],l=!0===e?1:e.length,f=0;f++<l;)u.push("("),a.push(")");var p=n&&n.length>0?h([""].concat(c(n))):"";return u.join("")+o+a.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(d(b));for(var y=1;y<t.length;++y){var w=t[y],m=w.on,L=w.using,C=w.join,O=[];O.push(C?" ".concat(Object(i.toUpper)(C)):","),O.push(d(w)),O.push(Object(i.commonOptionConnector)("ON",s.a,m)),L&&O.push("USING (".concat(L.map(i.literalToSQL).join(", "),")")),v.push(O.filter(i.hasVal).join(" "))}return v.filter(i.hasVal).join("")}function w(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var u=Object(i.literalToSQL)(n);switch(r){case"partition by":case"default collate":u=Object(s.a)(n);break;case"options":u="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(s.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(s.a).join(", ")}return o.push(u),o.filter(i.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return y})),e.d(r,"c",(function(){return g})),e.d(r,"d",(function(){return E})),e.d(r,"e",(function(){return d})),e.d(r,"f",(function(){return h})),e.d(r,"g",(function(){return w})),e.d(r,"h",(function(){return S})),e.d(r,"i",(function(){return T})),e.d(r,"j",(function(){return A})),e.d(r,"l",(function(){return m})),e.d(r,"m",(function(){return L})),e.d(r,"o",(function(){return C})),e.d(r,"n",(function(){return O})),e.d(r,"k",(function(){return j}));var n=e(2),o=e(14),s=e(0),u=e(1),a=e(3),i=e(16),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,s=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(u.a)(t.expr);return"".concat("CALL"," ").concat(r)}function y(t){var r=t.type,e=t.keyword,o=t.name,i=t.prefix,c=t.suffix,l=[Object(s.toUpper)(r),Object(s.toUpper)(e),Object(s.toUpper)(i)];switch(e){case"table":l.push(Object(a.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(s.identifierToSql)(o[0].schema),"."):"",Object(s.identifierToSql)(o[0].trigger)].filter(s.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(s.identifierToSql)(o));break;case"view":l.push(Object(a.c)(o),t.options&&t.options.map(u.a).filter(s.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(a.b)(t.table)]:[]),[t.options&&t.options.map(u.a).filter(s.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(u.a).filter(s.hasVal).join(" "))}return c&&l.push(c.map(u.a).filter(s.hasVal).join(" ")),l.filter(s.hasVal).join(" ")}function d(t){var r=t.type,e=t.table,n=Object(s.toUpper)(r);return"".concat(n," ").concat(Object(s.identifierToSql)(e))}function h(t){var r=t.type,e=t.name,n=t.args,o=[Object(s.toUpper)(r)],a=[e];return n&&a.push("(".concat(Object(u.a)(n).join(", "),")")),o.push(a.join("")),o.filter(s.hasVal).join(" ")}function w(t){var r=t.type,e=t.label,n=t.target,o=t.query,u=t.stmts;return[e,Object(s.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(u),"END LOOP",e].filter(s.hasVal).join(" ")}function m(t){var r=t.type,e=t.level,n=t.raise,o=t.using,a=[Object(s.toUpper)(r),Object(s.toUpper)(e)];return n&&a.push([Object(s.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(s.hasVal).join(""),n.expr.map((function(t){return Object(u.a)(t)})).join(", ")),o&&a.push(Object(s.toUpper)(o.type),Object(s.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(u.a)(t)})).join(", ")),a.filter(s.hasVal).join(" ")}function L(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var s,u=l(e);try{for(u.s();!(s=u.n()).done;){var i=s.value.map(a.b);n.push(i.join(" TO "))}}catch(t){u.e(t)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))}function C(t){var r=t.type,e=t.db,n=Object(s.toUpper)(r),o=Object(s.identifierToSql)(e);return"".concat(n," ").concat(o)}function O(t){var r=t.type,e=t.expr,n=t.keyword,o=Object(s.toUpper)(r),a=e.map(u.a).join(", ");return[o,Object(s.toUpper)(n),a].filter(s.hasVal).join(" ")}function j(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(s.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var u,i=[],c=l(n);try{var p=function(){var t=u.value,r=t.table,e=t.lock_type,n=[Object(a.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(s.toUpper)(e[t])})).filter(s.hasVal).join(" "))}i.push(n.join(" "))};for(c.s();!(u=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[i.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(s.hasVal).join(" ")}function g(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(s.toUpper)(r),Object(s.toUpper)(e),Object(u.a)(n)].filter(s.hasVal).join(" ")}function E(t){var r=t.type,e=t.declare,a=t.symbol,i=[Object(s.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,a=t.as,i=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[r,e].filter(s.hasVal).join(""),Object(s.toUpper)(a),Object(s.toUpper)(i)];switch(b){case"variable":v.push(Object(n.b)(c),Object(u.a)(t.collate),Object(s.toUpper)(l)),p&&v.push(Object(s.toUpper)(p.keyword),Object(u.a)(p.value));break;case"cursor":v.push(Object(s.toUpper)(f));break;case"table":v.push(Object(s.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(s.hasVal).join(" ")})).join("".concat(a," "));return i.push(c),i.join(" ")}function A(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,a=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(s.toUpper)(p),Object(u.a)(r),Object(s.literalToSQL)(a),"".concat(Object(i.a)(o.ast||o)).concat(l[0]),Object(s.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(s.toUpper)(t.type),Object(u.a)(t.boolean_expr),"THEN",Object(i.a)(t.then.ast||t.then),t.semicolon].filter(s.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(i.a)(e.ast||e)).concat(l[1])),b.push(Object(s.literalToSQL)(f)),b.filter(s.hasVal).join(" ")}function T(t){var r=t.name,e=t.host,n=[Object(s.literalToSQL)(r)];return e&&n.push("@",Object(s.literalToSQL)(e)),n.join("")}function S(t){var r=t.type,e=t.grant_option_for,o=t.keyword,a=t.objects,i=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(s.toUpper)(r),Object(s.literalToSQL)(e)],b=a.map((function(t){var r=t.priv,e=t.columns,o=[Object(u.a)(r)];return e&&o.push("(".concat(e.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),i)switch(p.push("ON"),o){case"priv":p.push(Object(s.literalToSQL)(i.object_type),i.priv_level.map((function(t){return[Object(s.identifierToSql)(t.prefix),Object(s.identifierToSql)(t.name)].filter(s.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(i))}return p.push(Object(s.toUpper)(c),l.map(T).join(", ")),p.push(Object(s.literalToSQL)(f)),p.filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return C})),e.d(r,"a",(function(){return O}));var n=e(9),o=e(1),s=e(3),u=e(0);var a=e(14),i=e(2);function c(t){var r=t.name,e=t.type;switch(e){case"table":case"view":var n=[Object(u.identifierToSql)(r.db),Object(u.identifierToSql)(r.table)].filter(u.hasVal).join(".");return"".concat(Object(u.toUpper)(e)," ").concat(n);case"column":return"COLUMN ".concat(Object(i.f)(r));default:return"".concat(Object(u.toUpper)(e)," ").concat(Object(u.literalToSQL)(r))}}function l(t){var r=t.keyword,e=t.expr;return[Object(u.toUpper)(r),Object(u.literalToSQL)(e)].filter(u.hasVal).join(" ")}var f=e(7);var p=e(8),b=e(15);var v=e(12),y=e(17),d=e(4);function h(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(u.hasVal).join(" ")}var w=e(22);var m=e(23),L={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(u.toUpper)(r),Object(s.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,s=t.as,a=t.schema;return[Object(u.toUpper)(r),Object(u.toUpper)(e),Object(o.a)(n),Object(u.toUpper)(s),Object(u.identifierToSql)(a)].filter(u.hasVal).join(" ")},create:a.b,comment:function(t){var r=t.expr,e=t.keyword,n=t.target,o=t.type;return[Object(u.toUpper)(o),Object(u.toUpper)(e),c(n),l(r)].filter(u.hasVal).join(" ")},select:f.a,deallocate:d.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,a=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,y=[Object(b.a)(l),"DELETE"],d=Object(i.i)(r,e);return y.push(d),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||y.push(Object(s.c)(n))),y.push(Object(u.commonOptionConnector)("FROM",s.c,e)),y.push(Object(u.commonOptionConnector)("WHERE",o.a,a)),y.push(Object(o.c)(c,"order by")),y.push(Object(p.a)(f)),y.push(Object(u.returningToSQL)(v)),y.filter(u.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(u.toUpper)(r),Object(s.b)(e),(n||[]).map(h).filter(u.hasVal).join(", ")].filter(u.hasVal).join(" ")},execute:d.f,explain:function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(f.a)(e)].join(" ")},for:d.g,update:v.b,if:d.j,insert:y.a,drop:d.b,truncate:d.b,replace:y.a,declare:d.d,use:d.o,rename:d.m,call:d.a,desc:d.e,set:d.n,lock:d.k,unlock:d.k,show:m.a,grant:d.h,revoke:d.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(w.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:d.l,transaction:function(t){var r=t.expr,e=r.action,n=r.keyword,o=r.modes,s=[Object(u.literalToSQL)(e),Object(u.toUpper)(n)];return o&&s.push(o.map(u.literalToSQL).join(", ")),s.filter(u.hasVal).join(" ")}};function C(t){if(!t)return"";for(var r=L[t.type],e=t,n=e._parentheses,s=e._orderby,a=e._limit,i=[n&&"(",r(t)];t._next;){var c=L[t._next.type],l=Object(u.toUpper)(t.set_op);i.push(l,c(t._next)),t=t._next}return i.push(n&&")",Object(o.c)(s,"order by"),Object(p.a)(a)),i.filter(u.hasVal).join(" ")}function O(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],s=C(o);e===n-1&&"transaction"===o.type&&(s="".concat(s," ;")),r.push(s)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a})),e.d(r,"b",(function(){return i})),e.d(r,"c",(function(){return c})),e.d(r,"d",(function(){return l})),e.d(r,"e",(function(){return p})),e.d(r,"f",(function(){return b})),e.d(r,"g",(function(){return v})),e.d(r,"h",(function(){return f})),e.d(r,"i",(function(){return d})),e.d(r,"j",(function(){return y}));var n=e(2),o=e(1),s=e(0),u=e(13);function a(t){var r=t.args,e=t.type,n=t.over,a=r.expr,i=r.having,c="".concat(Object(s.toUpper)(e),"(").concat(Object(o.a)(a));return i&&(c="".concat(c," HAVING ").concat(Object(s.toUpper)(i.prefix)," ").concat(Object(o.a)(i.expr))),[c="".concat(c,")"),Object(u.a)(n)].filter(s.hasVal).join(" ")}function i(t){if(!t||!t.array)return"";var r=t.array.keyword;if(r)return Object(s.toUpper)(r);for(var e=t.array,n=e.dimension,o=e.length,u=[],a=0;a<n;a++)u.push("["),o&&o[a]&&u.push(Object(s.literalToSQL)(o[a])),u.push("]");return u.join("")}function c(t){for(var r=t.target,e=t.expr,u=t.keyword,a=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:e,offset:l}),b=[],v=0,y=r.length;v<y;++v){var d=r[v],h=d.angle_brackets,w=d.length,m=d.dataType,L=d.parentheses,C=d.quoted,O=d.scale,j=d.suffix,g=d.expr,E=g?Object(o.a)(g):"";null!=w&&(E=O?"".concat(w,", ").concat(O):w),L&&(E="(".concat(E,")")),h&&(E="<".concat(E,">")),j&&j.length&&(E+=" ".concat(j.map(s.literalToSQL).join(" ")));var A="::",T="",S=[];"as"===a&&(0===v&&(p="".concat(Object(s.toUpper)(u),"(").concat(p)),T=")",A=" ".concat(a.toUpperCase()," ")),0===v&&S.push(p);var U=i(d);S.push(A,C,m,C,U,E,T),b.push(S.filter(s.hasVal).join(""))}c&&b.push(" AS ".concat(Object(s.identifierToSql)(c)));var _=b.filter(s.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var r=t.args,e=t.type,n=r.field,u=r.cast_type,a=r.source,i=["".concat(Object(s.toUpper)(e),"(").concat(Object(s.toUpper)(n)),"FROM",Object(s.toUpper)(u),Object(o.a)(a)];return"".concat(i.filter(s.hasVal).join(" "),")")}function f(t){var r=t.expr,e=r.key,n=r.value,u=r.on,a=[Object(o.a)(e),"VALUE",Object(o.a)(n)];return u&&a.push("ON","NULL",Object(o.a)(u)),a.filter(s.hasVal).join(" ")}function p(t){var r=t.args,e=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,n=t.value;return[Object(s.toUpper)(r),e,Object(o.a)(n)].filter(s.hasVal).join(" ")}(r[t])})).filter(s.hasVal).join(", ");return"".concat(Object(s.toUpper)(e),"(").concat(n,")")}function b(t){var r=t.value,e=r.name,n=r.symbol,u=r.expr;return[e,n,Object(o.a)(u)].filter(s.hasVal).join(" ")}function v(t){var r=t.args,e=t.array_index,a=t.name,i=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(u.a)(f),v=function(t){if(!t)return"";var r=t.type,e=t.keyword,n=t.orderby;return[Object(s.toUpper)(r),Object(s.toUpper)(e),"(".concat(Object(o.c)(n,"order by"),")")].filter(s.hasVal).join(" ")}(l),y=Object(o.a)(p),d=[Object(s.literalToSQL)(a.schema),a.name.map(s.literalToSQL).join(".")].filter(s.hasVal).join(".");if(!r)return[d,v,b].filter(s.hasVal).join(" ");var h=t.separator||", ";"TRIM"===Object(s.toUpper)(d)&&(h=" ");var w=[d];w.push(!1===i?" ":"(");var m=Object(o.a)(r);if(Array.isArray(h)){for(var L=m[0],C=1,O=m.length;C<O;++C)L=[L,m[C]].join(" ".concat(Object(o.a)(h[C-1])," "));w.push(L)}else w.push(m.join(h));return!1!==i&&w.push(")"),w.push(Object(n.a)(e)),w=[w.join(""),y].filter(s.hasVal).join(" "),[c?"(".concat(w,")"):w,v,b].filter(s.hasVal).join(" ")}function y(t){var r=t.as,e=t.name,n=t.args,u=[Object(s.literalToSQL)(e.schema),e.name.map(s.literalToSQL).join(".")].filter(s.hasVal).join(".");return["".concat(u,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(r)].join(" ")}function d(t){var r=t.args,e=t.expr,n=r.value,s=r.parentheses,u=n.map(o.a).join(", ");return[s?"(".concat(u,")"):u,"->",Object(o.a)(e)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f}));var n=e(1),o=e(2),s=e(8),u=e(15),a=e(3),i=e(0),c=e(11);function l(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],s=Object(i.toUpper)(r);switch(s){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(s,"string"==typeof e?Object(i.identifierToSql)(e):Object(n.a)(e))}return o.filter(i.hasVal).join(" ")}}function f(t){var r=t.as_struct_val,e=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,y=t.for_sys_time_as_of,d=void 0===y?{}:y,h=t.locking_read,w=t.groupby,m=t.having,L=t.into,C=void 0===L?{}:L,O=t.isolation,j=t.limit,g=t.options,E=t.orderby,A=t.parentheses_symbol,T=t.qualify,S=t.top,U=t.window,_=t.with,x=t.where,I=[Object(u.a)(_),"SELECT",Object(i.toUpper)(r)];Array.isArray(g)&&I.push(g.join(" ")),I.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,o=[Object(i.toUpper)(r)];return e&&o.push("(".concat(e.map(n.a).join(", "),")")),o.filter(i.hasVal).join(" ")}}(p),Object(i.topToSQL)(S),Object(o.i)(e,v));var N=C.position,R="";N&&(R=Object(i.commonOptionConnector)("INTO",l,C)),"column"===N&&I.push(R),I.push(Object(i.commonOptionConnector)("FROM",a.c,v)),"from"===N&&I.push(R);var k=d||{},M=k.keyword,V=k.expr;I.push(Object(i.commonOptionConnector)(M,n.a,V)),I.push(Object(i.commonOptionConnector)("WHERE",n.a,x)),w&&(I.push(Object(i.connector)("GROUP BY",Object(n.b)(w.columns).join(", "))),I.push(Object(n.b)(w.modifiers).join(", "))),I.push(Object(i.commonOptionConnector)("HAVING",n.a,m)),I.push(Object(i.commonOptionConnector)("QUALIFY",n.a,T)),I.push(Object(i.commonOptionConnector)("WINDOW",n.a,U)),I.push(Object(n.c)(E,"order by")),I.push(Object(c.a)(f)),I.push(Object(s.a)(j)),O&&I.push(Object(i.commonOptionConnector)(O.keyword,i.literalToSQL,O.expr)),I.push(Object(i.toUpper)(h)),"end"===N&&I.push(R),I.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,s=[Object(i.toUpper)(o),Object(i.toUpper)(e)];return r?"".concat(s.join(" "),"(").concat(Object(n.a)(r),")"):s.join(" ")}}(b));var q=I.filter(i.hasVal).join(" ");return A?"(".concat(q,")"):q}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(1);function s(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function a(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function i(t){return t?t.fetch?(e=(r=t).fetch,u=r.offset,[].concat(s(a(u)),s(a(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,u}},function(t,r,e){"use strict";e.d(r,"a",(function(){return p})),e.d(r,"c",(function(){return b})),e.d(r,"b",(function(){return f}));var n=e(2),o=e(14),s=e(10),u=e(3),a=e(1),i=e(7),c=e(0);function l(t,r){switch(t){case"add":var e=r.map((function(t){var r=t.name,e=t.value;return["PARTITION",Object(c.literalToSQL)(r),"VALUES",Object(c.toUpper)(e.type),"(".concat(Object(c.literalToSQL)(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Object(n.i)(r)}}function f(t){if(!t)return"";var r=t.action,e=t.create_definitions,u=t.if_not_exists,a=t.keyword,i=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,y=t.suffix,d="",h=[];switch(b){case"column":h=[Object(n.c)(t)];break;case"index":h=Object(s.c)(t),d=t[b];break;case"table":case"schema":d=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":d=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":d=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":d=Object(c.identifierToSql)(t[b]),h=[Object(o.a)(e)];break;case"partition":h=[l(r,t.partitions)];break;case"key":d=Object(c.identifierToSql)(t[b]);break;default:d=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var w=[Object(c.toUpper)(r),Object(c.toUpper)(a),Object(c.toUpper)(u),Object(c.toUpper)(i),f&&Object(n.f)(f),Object(c.toUpper)(p),d&&d.trim(),h.filter(c.hasVal).join(" ")];return y&&w.push(Object(c.toUpper)(y.keyword),y.expr&&Object(n.f)(y.expr)),w.filter(c.hasVal).join(" ")}function p(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(a.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function b(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,s=t.type,u=r.expr,a=r.orderby;return[Object(c.toUpper)(s),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(u.map(p).join(", ")).concat(a?[" ORDER","BY",a.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.if_exists,o=t.prefix,s=t.expr,i=void 0===s?[]:s,l=Object(c.toUpper)(r),f=Object(u.c)(e),p=i.map(a.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),f(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,s=t.type;return[Object(c.toUpper)(s),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,s=t.select,a=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(u.b)(a)];return e&&f.push("(".concat(e.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(i.a)(s)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return a})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function s(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function a(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function i(t){if(t){var r=t.type,e=t.expr,o=t.symbol,u=r.toUpperCase(),i=[];switch(i.push(u),u){case"KEY_BLOCK_SIZE":o&&i.push(o),i.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":i.length=0,i.push.apply(i,s(a(t)));break;case"WITH PARSER":i.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":i.shift(),i.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":i.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:i.push(o,Object(n.literalToSQL)(e))}return i.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(i):[]}function l(t){var r=t.constraint_type,e=t.index_type,u=t.index_options,i=void 0===u?[]:u,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,s(a(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(i).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,s(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(1),o=e(0);function s(t){if(t){var r=t.keyword,e=t.collate,s=e.name,u=e.symbol,a=e.value,i=[Object(o.toUpper)(r)];return a||i.push(u),i.push(Array.isArray(s)?s.map(o.literalToSQL).join("."):Object(o.literalToSQL)(s)),a&&i.push(u),i.push(Object(n.a)(a)),i.filter(o.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return p})),e.d(r,"a",(function(){return f}));var n=e(3),o=e(1),s=e(2),u=e(8),a=e(0),i=e(15);function c(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,s=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){if(!t||0===t.length)return"";var r,e=[],n=c(t);try{for(n.s();!(r=n.n()).done;){var u=r.value,i={},l=u.value;for(var f in u)"value"!==f&&"keyword"!==f&&(i[f]=u[f]);var p=[Object(s.f)(i)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),e.push(p.filter(a.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function p(t){var r=t.from,e=t.table,s=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(e),Object(a.commonOptionConnector)("SET",f,s),Object(a.commonOptionConnector)("FROM",n.c,r),Object(a.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(u.a)(b),Object(a.returningToSQL)(v)].filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(0),o=e(1),s=e(20);function u(t){if(t){var r=t.as_window_specification,e=t.expr,u=t.keyword,a=t.type,i=t.parentheses,c=Object(n.toUpper)(a);if("WINDOW"===c)return"OVER ".concat(Object(s.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(a)," ").concat(Object(n.toUpper)(u)),f=Object(o.a)(e)||[];return i&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return E})),e.d(r,"a",(function(){return h}));var n=e(9),o=e(1),s=e(10),u=e(2),a=e(4),i=e(19),c=e(6),l=e(3),f=e(12),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,r){if(t){if("string"==typeof t)return d(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?d(t,r):void 0}}function d(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function h(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(u.c)(t);case"index":return Object(s.a)(t);case"constraint":return Object(i.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function w(t){var r=[];switch(t.keyword){case"from":r.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":r.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":r.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return r.filter(b.hasVal).join(" ")}function m(t){var r=t.keyword,e=t.table,n=t.for_values,o=t.tablespace,s=[Object(b.toUpper)(r),Object(l.b)(e),Object(b.toUpper)(n.keyword),w(n.expr)];return o&&s.push("TABLESPACE",Object(b.literalToSQL)(o)),s.filter(b.hasVal).join(" ")}function L(t){var r=t.as,e=t.domain,n=t.type,s=t.keyword,u=t.target,a=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(s),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(u)];if(a&&a.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=y(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,s=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}(a);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(i.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function C(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function O(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(r),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function j(t){var r=t.type,e=t.replace,o=t.keyword,s=t.name,a=t.args,i=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(s.schema),s.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=a.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(u.c).join(", "),")"):C(n)].filter(b.hasVal).join(" ")}(i),c.map(O).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var r=t.type,e=t.symbol,n=t.value,s=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":s.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":s.push(Object(b.dataTypeToSQL)(n));break;default:s.push(Object(o.a)(n))}return s.filter(b.hasVal).join(" ")}function E(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,s=t.name,u=t.args,a=t.options,i=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(s.schema),s.name].filter(b.hasVal).join("."),l="".concat(u.expr.map(n.a).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(n.a).join(", ")].join(" "):"");return i.push("".concat(c,"(").concat(l,")"),"(".concat(a.map(g).join(", "),")")),i.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,s=t.as,u=t.temporary,a=t.if_not_exists,i=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,y=t.partition_of,d=t.query_expr,w=t.unlogged,L=t.with,C=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(u),Object(b.toUpper)(w),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];if(o){var O=o.type,j=o.table,g=Object(l.c)(j);return C.push(Object(b.toUpper)(O),g),C.filter(b.hasVal).join(" ")}if(y)return C.concat([m(y)]).filter(b.hasVal).join(" ");if(i&&C.push("(".concat(i.map(h).join(", "),")")),c){var E=Object(b.getParserOpt)().database,A=E&&"sqlite"===E.toLowerCase()?", ":" ";C.push(c.map(l.a).join(A))}if(L){var T=L.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");C.push("WITH (".concat(T,")"))}return C.push(Object(b.toUpper)(f),Object(b.toUpper)(s)),d&&C.push(Object(p.b)(d)),C.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,s=t.events,u=t.execute,a=t.for_each,i=t.from,f=t.location,p=t.keyword,y=t.or,d=t.type,h=t.table,w=t.when,m=[Object(b.toUpper)(d),Object(b.toUpper)(y),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],L=Object(b.triggerEventToSQL)(s);return m.push(L,"ON",Object(l.b)(h)),i&&m.push("FROM",Object(l.b)(i)),m.push.apply(m,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(a)))),w&&m.push(Object(b.toUpper)(w.type),Object(o.a)(w.cond)),m.push(Object(b.toUpper)(u.keyword),Object(c.g)(u.expr)),m.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,s=t.execute,a=t.type,i=t.table,c=t.if_not_exists,v=t.temporary,y=t.trigger,d=t.events,h=t.order,w=t.time,m=t.when,L=[Object(b.toUpper)(a),Object(b.toUpper)(v),Object(o.a)(r),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(y),Object(b.toUpper)(w),d.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(u.f).join(", ")),r.join(" ")})),"ON",Object(l.b)(i),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),h&&"".concat(Object(b.toUpper)(h.keyword)," ").concat(Object(b.identifierToSql)(h.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,m),Object(b.toUpper)(s.prefix)];switch(s.type){case"set":L.push(Object(b.commonOptionConnector)("SET",f.a,s.expr));break;case"multiple":L.push(Object(p.a)(s.expr.ast))}return L.push(Object(b.toUpper)(s.suffix)),L.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,s=t.schema,u=t.type,a=t.with,i=t.version;return[Object(b.toUpper)(u),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(a),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,s),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,i),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=j(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,u=t.keyword,a=t.if_not_exists,i=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,y=t.index,d=t.on,h=t.index_options,w=t.algorithm_option,m=t.lock_option,L=t.on_kw,C=t.table,O=t.tablespace,j=t.type,g=t.where,E=t.with,A=t.with_before_where,T=E&&"WITH (".concat(Object(s.b)(E).join(", "),")"),S=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=y;y&&(U="string"==typeof y?Object(b.identifierToSql)(y):[Object(b.identifierToSql)(y.schema),Object(b.identifierToSql)(y.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(j),Object(b.toUpper)(f),Object(b.toUpper)(u),Object(b.toUpper)(a),Object(b.toUpper)(r),U,Object(b.toUpper)(L),Object(l.b)(C)].concat(v(Object(s.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),S,Object(s.b)(h).join(" "),Object(n.b)(w),Object(n.b)(m),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,O)]);return A?_.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),_.push(Object(b.commonOptionConnector)("ON",o.a,d),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,s=t.if_not_exists,u=t.create_definitions,a=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(s),Object(l.c)(n)];return u&&a.push(u.map(h).join(" ")),a.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":e=function(t){var r=t.type,e=t.keyword,n=t.replace,o=t.if_not_exists,s=t.create_definitions,u=t[e],a=u.db,i=u.schema,c=[Object(b.literalToSQL)(a),i.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(r),Object(b.toUpper)(n),Object(b.toUpper)(e),Object(b.toUpper)(o),c];return s&&f.push(s.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,s=t.if_not_exists,u=t.keyword,a=t.recursive,i=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,y=t.view,d=t.with,h=t.with_options,w=y.db,m=y.schema,L=y.view,C=[Object(b.identifierToSql)(w),Object(b.identifierToSql)(m),Object(b.identifierToSql)(L)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(f),Object(b.toUpper)(a),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(u),Object(b.toUpper)(s),C,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),h&&["WITH","(".concat(h.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(d)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=L(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,s=t.name,u=t.resource,a=t.type,i=[Object(b.toUpper)(a),Object(b.toUpper)(n),[Object(b.identifierToSql)(s.schema),Object(b.identifierToSql)(s.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(u)];if(e){var c=[];switch(u){case"enum":case"range":c.push(Object(o.a)(e));break;default:c.push("(".concat(e.map(h).join(", "),")"))}i.push(c.filter(b.hasVal).join(" "))}return i.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,s=t.if_not_exists,u=t.keyword,i=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(a.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),y=[Object(b.toUpper)(p),Object(b.toUpper)(u),Object(b.toUpper)(s),v];return n&&y.push(Object(b.toUpper)(n.keyword),n.value.map(a.i).join(", ")),y.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&y.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return y.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),y.push(Object(b.literalToSQL)(i),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),y.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(2),o=e(1),s=e(0);function u(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,u=t.columns,a=Array.isArray(u)?"(".concat(u.map(n.f).join(", "),")"):"";return"".concat("default"===r.type?Object(s.identifierToSql)(r.value):Object(s.literalToSQL)(r)).concat(a," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function s(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function u(t){return Array.isArray(t)?(t.forEach(s),Object(n.a)(t)):(s(t),Object(n.b)(t))}function a(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[u(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):u(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(3),o=e(1),s=e(2),u=e(0),a=e(7),i=e(12);function c(t){if("select"===t.type)return Object(a.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(u.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(u.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(s.f).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,s=[Object(u.toUpper)(e)];switch(n){case"origin":s.push(Object(u.literalToSQL)(r));break;case"update":s.push("UPDATE",Object(u.commonOptionConnector)("SET",i.a,r.set),Object(u.commonOptionConnector)("WHERE",o.a,r.where))}return s.filter(u.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(u.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,s=t.prefix,a=void 0===s?"into":s,f=t.columns,p=t.conflict,v=t.values,y=t.where,d=t.on_duplicate_update,h=t.partition,w=t.returning,m=t.set,L=d||{},C=L.keyword,O=L.set,j=[Object(u.toUpper)(e),Object(u.toUpper)(a),Object(n.c)(r),l(h)];return Array.isArray(f)&&j.push("(".concat(f.map(u.literalToSQL).join(", "),")")),j.push(Object(u.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),j.push(Object(u.commonOptionConnector)("ON CONFLICT",b,p)),j.push(Object(u.commonOptionConnector)("SET",i.a,m)),j.push(Object(u.commonOptionConnector)("WHERE",o.a,y)),j.push(Object(u.commonOptionConnector)(C,i.a,O)),j.push(Object(u.returningToSQL)(w)),j.filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(1);function s(t){var r=t.expr,e=t.unit,s=t.suffix;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e),Object(o.a)(s)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(10),s=e(2);function u(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return a(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?a(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(t){var r=t.constraint,e=t.constraint_type,a=t.enforced,i=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(r));var y=Object(n.toUpper)(e);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===y&&(y="UNIQUE"),b.push(y),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(i)),b.push.apply(b,u(Object(o.c)(t))),b.push.apply(b,u(Object(s.g)(l))),b.push(Object(n.toUpper)(a)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),s=e(13);function u(t){if(t){var r=t.type;return"rows"===r?[Object(n.toUpper)(r),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function a(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,s=t.orderby,a=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(s,"order by"),u(a)].filter(n.hasVal).join(" ")}(r),")")}function i(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(a(e))}function c(t){return t.expr.map(i).join(", ")}function l(t){var r=t.over;return[function(t){var r=t.args,e=t.name,s=t.consider_nulls,u=void 0===s?"":s,a=t.separator,i=void 0===a?", ":a;return[e,"(",r?Object(o.a)(r).join(i):"",")",u&&" ",u].filter(n.hasVal).join("")}(t),Object(s.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(1),o=e(0);function s(t){var r=t.operator||t.op,e=Object(n.a)(t.right),s=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":s=!0,e="".concat(e[0]," AND ").concat(e[1])}s||(e="(".concat(e.join(", "),")"))}var u=t.right.escape||{},a=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),r,e,Object(o.toUpper)(u.type),Object(n.a)(u.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(a,")"):a].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(1),o=e(0);function s(t){var r=t.left,e=t.right,s=t.symbol,u=t.keyword;r.keyword=u;var a=Object(n.a)(r),i=Object(n.a)(e);return[a,Object(o.toUpper)(s),i].filter(o.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(8),s=e(3),u=e(0);function a(t){var r,e,a,i,c=t.keyword,l=t.suffix,f="";switch(Object(u.toUpper)(c)){case"BINLOG":e=(r=t).in,a=r.from,i=r.limit,f=[Object(u.commonOptionConnector)("IN",u.literalToSQL,e&&e.right),Object(u.commonOptionConnector)("FROM",s.c,a),Object(o.a)(i)].filter(u.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(u.toUpper)(e)?Object(u.commonOptionConnector)("LIKE",u.literalToSQL,r.right):Object(u.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(u.commonOptionConnector)("FROM",s.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,s="'".concat(e,"'");return n&&(s+="@'".concat(n,"'")),["FOR",s,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(u.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(u.commonOptionConnector)("",s.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(u.toUpper)(c),Object(u.toUpper)(l),f].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(2),o=e(1),s=e(25);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var a,i,c,l=(a={},i="postgresql",c=s.parse,(i=function(t){var r=function(t,r){if("object"!=u(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==u(r)?r:r+""}(i))in a?Object.defineProperty(a,i,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[i]=c,a),f=e(16),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return y(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?y(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,s=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw s}}}}function y(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function d(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:r+""}var w=function(){return function(t,r,e){return r&&d(t.prototype,r),e&&d(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(e),!t||"*"===t)return[];var o=Object(n.k)(r);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"postgresql":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](!1===r.trimQuery?t:t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var s,u=this["".concat(o,"List")].bind(this),a=u(t,e),i=!0,c="",l=v(a);try{for(l.s();!(s=l.n()).done;){var f,b=s.value,y=!1,d=v(r);try{for(d.s();!(f=d.n()).done;){var h=f.value,w=new RegExp("^".concat(h,"$"),"i");if(w.test(b)){y=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!y){c=b,i=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])}();r.a=w},function(t,r,e){"use strict";var n=e(29);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?s(t.parts[r][0])+"-"+s(t.parts[r][1]):s(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function s(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,s=new Array(t.length);for(r=0;r<t.length;r++)s[r]=(o=t[r],e[o.type](o));if(s.sort(),s.length>0){for(r=1,n=1;r<s.length;r++)s[r-1]!==s[r]&&(s[n]=s[r],n++);s.length=n}switch(s.length){case 1:return s[0];case 2:return s[0]+" or "+s[1];default:return s.slice(0,-1).join(", ")+", or "+s[s.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,s={},u={start:qc},a=qc,i=Ic("IF",!0),l=Ic("if",!0),f=Ic("exists",!0),p=Ic("EXTENSION",!0),b=Ic("SCHEMA",!0),v=Ic("VERSION",!0),y=Ic("CASCADED",!0),d=Ic("LOCAL",!0),h=Ic("CHECK",!0),w=Ic("OPTION",!1),m=Ic("check_option",!0),L=Ic("security_barrier",!0),C=Ic("security_invoker",!0),O=Ic("SFUNC",!0),j=Ic("STYPE",!0),g=Ic("AGGREGATE",!0),E=Ic("RETURNS",!0),A=Ic("SETOF",!0),T=Ic("CONSTANT",!0),S=Ic(":=",!1),U=Ic("BEGIN",!0),_=Ic("DECLARE",!0),x=Ic("LANGUAGE",!1),I=Ic("TRANSORM",!0),N=Ic("FOR",!1),R=Ic("TYPE",!1),k=Ic("WINDOW",!0),M=Ic("IMMUTABLE",!0),V=Ic("STABLE",!0),q=Ic("VOLATILE",!0),P=Ic("STRICT",!0),D=Ic("NOT",!0),G=Ic("LEAKPROOF",!0),Q=Ic("CALLED",!0),F=Ic("NULL",!0),$=Ic("ON",!0),B=Ic("INPUT",!0),H=Ic("EXTERNAL",!0),Y=Ic("SECURITY",!0),W=Ic("INVOKER",!0),X=Ic("DEFINER",!0),Z=Ic("PARALLEL",!0),z=Ic("UNSAFE",!0),K=Ic("RESTRICTED",!0),J=Ic("SAFE",!0),tt=/^[^ s\t\n\r]/,rt=Nc([" ","s","\t","\n","\r"],!0,!1),et=/^[^ s\t\n\r;]/,nt=Nc([" ","s","\t","\n","\r",";"],!0,!1),ot=Ic("COST",!0),st=Ic("ROWS",!0),ut=Ic("SUPPORT",!0),at=Ic("TO",!0),it=Ic("=",!1),ct=Ic("CURRENT",!0),lt=Ic("FUNCTION",!0),ft=Ic("RANGE",!0),pt=Ic("TYPE",!0),bt=Ic("DOMAIN",!0),vt=Ic("INCREMENT",!0),yt=Ic("MINVALUE",!0),dt=function(t,r){return{resource:"sequence",prefix:t.toLowerCase(),value:r}},ht=Ic("NO",!0),wt=Ic("MAXVALUE",!0),mt=Ic("START",!0),Lt=Ic("CACHE",!0),Ct=Ic("CYCLE",!0),Ot=Ic("OWNED",!0),jt=Ic("NONE",!0),gt=Ic("INCLUDE",!0),Et=Ic("NULLS",!0),At=Ic("FIRST",!0),Tt=Ic("LAST",!0),St=Ic("MODULUS",!0),Ut=Ic("REMAINDER",!0),_t=Ic("FOR",!0),xt=Ic("OF",!0),It=Ic("AUTO_INCREMENT",!0),Nt=Ic("UNIQUE",!0),Rt=Ic("KEY",!0),kt=Ic("PRIMARY",!0),Mt=Ic("COLUMN_FORMAT",!0),Vt=Ic("FIXED",!0),qt=Ic("DYNAMIC",!0),Pt=Ic("DEFAULT",!0),Dt=Ic("STORAGE",!0),Gt=Ic("DISK",!0),Qt=Ic("MEMORY",!0),Ft=Ic("CASCADE",!0),$t=Ic("RESTRICT",!0),Bt=Ic("ONLY",!0),Ht=Ic("RESTART",!0),Yt=Ic("CONTINUE",!0),Wt=Ic("IDENTITY",!0),Xt=Ic("OUT",!0),Zt=Ic("VARIADIC",!0),zt=Ic("only",!0),Kt=Ic("OWNER",!0),Jt=Ic("CURRENT_ROLE",!0),tr=Ic("CURRENT_USER",!0),rr=Ic("SESSION_USER",!0),er=Ic("ALGORITHM",!0),nr=Ic("INSTANT",!0),or=Ic("INPLACE",!0),sr=Ic("COPY",!0),ur=Ic("LOCK",!0),ar=Ic("SHARED",!0),ir=Ic("EXCLUSIVE",!0),cr=Ic("data",!0),lr=Ic("type",!0),fr=Ic("PRIMARY KEY",!0),pr=Ic("FOREIGN KEY",!0),br=Ic("ENFORCED",!0),vr=Ic("MATCH FULL",!0),yr=Ic("MATCH PARTIAL",!0),dr=Ic("MATCH SIMPLE",!0),hr=Ic("SET NULL",!0),wr=Ic("NO ACTION",!0),mr=Ic("SET DEFAULT",!0),Lr=Ic("TRIGGER",!0),Cr=Ic("BEFORE",!0),Or=Ic("AFTER",!0),jr=Ic("INSTEAD OF",!0),gr=Ic("EXECUTE",!0),Er=Ic("PROCEDURE",!0),Ar=Ic("DEFERRABLE",!0),Tr=Ic("INITIALLY IMMEDIATE",!0),Sr=Ic("INITIALLY DEFERRED",!0),Ur=Ic("EACH",!0),_r=Ic("ROW",!0),xr=Ic("STATEMENT",!0),Ir=Ic("CHARACTER",!0),Nr=Ic("SET",!0),Rr=Ic("CHARSET",!0),kr=Ic("COLLATE",!0),Mr=Ic("AVG_ROW_LENGTH",!0),Vr=Ic("KEY_BLOCK_SIZE",!0),qr=Ic("MAX_ROWS",!0),Pr=Ic("MIN_ROWS",!0),Dr=Ic("STATS_SAMPLE_PAGES",!0),Gr=Ic("CONNECTION",!0),Qr=Ic("COMPRESSION",!0),Fr=Ic("'",!1),$r=Ic("ZLIB",!0),Br=Ic("LZ4",!0),Hr=Ic("ENGINE",!0),Yr=Ic("IN",!0),Wr=Ic("ACCESS SHARE",!0),Xr=Ic("ROW SHARE",!0),Zr=Ic("ROW EXCLUSIVE",!0),zr=Ic("SHARE UPDATE EXCLUSIVE",!0),Kr=Ic("SHARE ROW EXCLUSIVE",!0),Jr=Ic("ACCESS EXCLUSIVE",!0),te=Ic("SHARE",!0),re=Ic("MODE",!0),ee=Ic("NOWAIT",!0),ne=Ic("TABLES",!0),oe=Ic("PREPARE",!0),se=Ic("USAGE",!0),ue=function(t){return{type:"origin",value:Array.isArray(t)?t[0]:t}},ae=Ic("CONNECT",!0),ie=Ic("PRIVILEGES",!0),ce=function(t){return{type:"origin",value:t}},le=Ic("SEQUENCE",!0),fe=Ic("DATABASE",!0),pe=Ic("DOMAIN",!1),be=Ic("FUNCTION",!1),ve=Ic("ROUTINE",!0),ye=Ic("LANGUAGE",!0),de=Ic("LARGE",!0),he=Ic("SCHEMA",!1),we=Ic("FUNCTIONS",!0),me=Ic("PROCEDURES",!0),Le=Ic("ROUTINES",!0),Ce=Ic("PUBLIC",!0),Oe=Ic("GRANT",!0),je=Ic("OPTION",!0),ge=Ic("ADMIN",!0),Ee=Ic("REVOKE",!0),Ae=Ic("ELSEIF",!0),Te=Ic("THEN",!0),Se=Ic("END",!0),Ue=Ic("DEBUG",!0),_e=Ic("LOG",!0),xe=Ic("INFO",!0),Ie=Ic("NOTICE",!0),Ne=Ic("WARNING",!0),Re=Ic("EXCEPTION",!0),ke=Ic("MESSAGE",!0),Me=Ic("DETAIL",!0),Ve=Ic("HINT",!0),qe=Ic("ERRCODE",!0),Pe=Ic("COLUMN",!0),De=Ic("CONSTRAINT",!0),Ge=Ic("DATATYPE",!0),Qe=Ic("TABLE",!0),Fe=Ic("SQLSTATE",!0),$e=Ic("RAISE",!0),Be=Ic("LOOP",!0),He=Ic("SERIALIZABLE",!0),Ye=Ic("REPEATABLE",!0),We=Ic("READ",!0),Xe=Ic("COMMITTED",!0),Ze=Ic("UNCOMMITTED",!0),ze=function(t){return{type:"origin",value:"read "+t.toLowerCase()}},Ke=Ic("ISOLATION",!0),Je=Ic("LEVEL",!0),tn=Ic("WRITE",!0),rn=Ic("commit",!0),en=Ic("rollback",!0),nn=Ic("begin",!0),on=Ic("WORK",!0),sn=Ic("TRANSACTION",!0),un=Ic("start",!0),an=Ic("transaction",!0),cn=Ic("ROLE",!0),ln=Ic("SERVER",!0),fn=Ic("SUBSCRIPTION",!0),pn=Ic("IS",!0),bn=Ic("COMMENT",!0),vn=Ic("(",!1),yn=Ic(")",!1),dn=Ic(";",!1),hn=Ic("AT",!0),wn=Ic("ZONE",!0),mn=Ic("OUTFILE",!0),Ln=Ic("DUMPFILE",!0),Cn=Ic("BTREE",!0),On=Ic("HASH",!0),jn=Ic("GIST",!0),gn=Ic("GIN",!0),En=Ic("WITH",!0),An=Ic("PARSER",!0),Tn=Ic("VISIBLE",!0),Sn=Ic("INVISIBLE",!0),Un=function(t,r){return r.unshift(t),r.forEach(t=>{const{table:r,as:e}=t;Zy[r]=r,e&&(Zy[e]=r),function(t){const r=$y(t);t.clear(),r.forEach(r=>t.add(r))}(Wy)}),r},_n=Ic("LATERAL",!0),xn=Ic("TABLESAMPLE",!0),In=Ic("CROSS",!0),Nn=Ic("FOLLOWING",!0),Rn=Ic("PRECEDING",!0),kn=Ic("UNBOUNDED",!0),Mn=Ic("DO",!0),Vn=Ic("NOTHING",!0),qn=Ic("CONFLICT",!0),Pn=function(t,r){return Qy(t,r)},Dn=Ic("!",!1),Gn=Ic(">=",!1),Qn=Ic(">",!1),Fn=Ic("<=",!1),$n=Ic("<>",!1),Bn=Ic("<",!1),Hn=Ic("!=",!1),Yn=Ic("SIMILAR",!0),Wn=Ic("!~*",!1),Xn=Ic("~*",!1),Zn=Ic("~",!1),zn=Ic("!~",!1),Kn=Ic("ESCAPE",!0),Jn=Ic("+",!1),to=Ic("-",!1),ro=Ic("*",!1),eo=Ic("/",!1),no=Ic("%",!1),oo=Ic("||",!1),so=Ic("$",!1),uo=Ic("?|",!1),ao=Ic("?&",!1),io=Ic("?",!1),co=Ic("#-",!1),lo=Ic("#>>",!1),fo=Ic("#>",!1),po=Ic("@>",!1),bo=Ic("<@",!1),vo=Ic("E",!0),yo=function(t){return{type:"default",value:t}},ho=function(t){return!0===My[t.toUpperCase()]},wo=Ic('"',!1),mo=/^[^"]/,Lo=Nc(['"'],!0,!1),Co=/^[^']/,Oo=Nc(["'"],!0,!1),jo=Ic("`",!1),go=/^[^`]/,Eo=Nc(["`"],!0,!1),Ao=/^[A-Za-z_\u4E00-\u9FA5]/,To=Nc([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),So=/^[A-Za-z0-9_\-$\u4E00-\u9FA5\xC0-\u017F]/,Uo=Nc([["A","Z"],["a","z"],["0","9"],"_","-","$",["一","龥"],["À","ſ"]],!1,!1),_o=/^[A-Za-z0-9_\u4E00-\u9FA5\xC0-\u017F]/,xo=Nc([["A","Z"],["a","z"],["0","9"],"_",["一","龥"],["À","ſ"]],!1,!1),Io=Ic(":",!1),No=Ic("OVER",!0),Ro=Ic("FILTER",!0),ko=Ic("FIRST_VALUE",!0),Mo=Ic("LAST_VALUE",!0),Vo=Ic("ROW_NUMBER",!0),qo=Ic("DENSE_RANK",!0),Po=Ic("RANK",!0),Do=Ic("LAG",!0),Go=Ic("LEAD",!0),Qo=Ic("NTH_VALUE",!0),Fo=Ic("IGNORE",!0),$o=Ic("RESPECT",!0),Bo=Ic("percentile_cont",!0),Ho=Ic("percentile_disc",!0),Yo=Ic("within",!0),Wo=Ic("mode",!0),Xo=Ic("BOTH",!0),Zo=Ic("LEADING",!0),zo=Ic("TRAILING",!0),Ko=Ic("trim",!0),Jo=Ic("crosstab",!0),ts=Ic("jsonb_to_recordset",!0),rs=Ic("jsonb_to_record",!0),es=Ic("json_to_recordset",!0),ns=Ic("json_to_record",!0),os=Ic("substring",!0),ss=Ic("years",!0),us=Ic("months",!0),as=Ic("weeks",!0),is=Ic("days",!0),cs=Ic("hours",!0),ls=Ic("mins",!0),fs=Ic("=>",!1),ps=Ic("secs",!0),bs=Ic("make_interval",!0),vs=Ic("now",!0),ys=Ic("at",!0),ds=Ic("zone",!0),hs=Ic("CENTURY",!0),ws=Ic("DAY",!0),ms=Ic("DATE",!0),Ls=Ic("DECADE",!0),Cs=Ic("DOW",!0),Os=Ic("DOY",!0),js=Ic("EPOCH",!0),gs=Ic("HOUR",!0),Es=Ic("ISODOW",!0),As=Ic("ISOYEAR",!0),Ts=Ic("MICROSECONDS",!0),Ss=Ic("MILLENNIUM",!0),Us=Ic("MILLISECONDS",!0),_s=Ic("MINUTE",!0),xs=Ic("MONTH",!0),Is=Ic("QUARTER",!0),Ns=Ic("SECOND",!0),Rs=Ic("TIMEZONE",!0),ks=Ic("TIMEZONE_HOUR",!0),Ms=Ic("TIMEZONE_MINUTE",!0),Vs=Ic("WEEK",!0),qs=Ic("YEAR",!0),Ps=Ic("NTILE",!0),Ds=/^[\n]/,Gs=Nc(["\n"],!1,!1),Qs=/^[^"\\\0-\x1F\x7F]/,Fs=Nc(['"',"\\",["\0",""],""],!0,!1),$s=/^[^'\\]/,Bs=Nc(["'","\\"],!0,!1),Hs=Ic("\\'",!1),Ys=Ic('\\"',!1),Ws=Ic("\\\\",!1),Xs=Ic("\\/",!1),Zs=Ic("\\b",!1),zs=Ic("\\f",!1),Ks=Ic("\\n",!1),Js=Ic("\\r",!1),tu=Ic("\\t",!1),ru=Ic("\\u",!1),eu=Ic("\\",!1),nu=Ic("''",!1),ou=/^[\n\r]/,su=Nc(["\n","\r"],!1,!1),uu=Ic(".",!1),au=/^[0-9]/,iu=Nc([["0","9"]],!1,!1),cu=/^[0-9a-fA-F]/,lu=Nc([["0","9"],["a","f"],["A","F"]],!1,!1),fu=/^[eE]/,pu=Nc(["e","E"],!1,!1),bu=/^[+\-]/,vu=Nc(["+","-"],!1,!1),yu=Ic("NOT NULL",!0),du=Ic("TRUE",!0),hu=Ic("FALSE",!0),wu=Ic("SHOW",!0),mu=Ic("DROP",!0),Lu=Ic("USE",!0),Cu=Ic("ALTER",!0),Ou=Ic("SELECT",!0),ju=Ic("UPDATE",!0),gu=Ic("CREATE",!0),Eu=Ic("TEMPORARY",!0),Au=Ic("UNLOGGED",!1),Tu=Ic("TEMP",!0),Su=Ic("DELETE",!0),Uu=Ic("INSERT",!0),_u=Ic("RECURSIVE",!0),xu=Ic("REPLACE",!0),Iu=Ic("RETURN",!0),Nu=Ic("RETURNING",!0),Ru=Ic("RENAME",!0),ku=(Ic("EXPLAIN",!0),Ic("PARTITION",!0)),Mu=Ic("INTO",!0),Vu=Ic("FROM",!0),qu=Ic("AS",!0),Pu=Ic("TABLESPACE",!0),Du=Ic("COLLATION",!0),Gu=Ic("DEALLOCATE",!0),Qu=Ic("LEFT",!0),Fu=Ic("RIGHT",!0),$u=Ic("FULL",!0),Bu=Ic("INNER",!0),Hu=Ic("JOIN",!0),Yu=Ic("OUTER",!0),Wu=Ic("UNION",!0),Xu=Ic("INTERSECT",!0),Zu=Ic("EXCEPT",!0),zu=Ic("VALUES",!0),Ku=Ic("USING",!0),Ju=Ic("WHERE",!0),ta=Ic("GROUP",!0),ra=Ic("BY",!0),ea=Ic("ORDER",!0),na=Ic("HAVING",!0),oa=Ic("LIMIT",!0),sa=Ic("OFFSET",!0),ua=Ic("ASC",!0),aa=Ic("DESC",!0),ia=Ic("ALL",!0),ca=Ic("DISTINCT",!0),la=Ic("BETWEEN",!0),fa=Ic("LIKE",!0),pa=Ic("ILIKE",!0),ba=Ic("EXISTS",!0),va=Ic("AND",!0),ya=Ic("OR",!0),da=Ic("ARRAY",!0),ha=Ic("ARRAY_AGG",!0),wa=Ic("STRING_AGG",!0),ma=Ic("COUNT",!0),La=Ic("GROUP_CONCAT",!0),Ca=Ic("MAX",!0),Oa=Ic("MIN",!0),ja=Ic("SUM",!0),ga=Ic("AVG",!0),Ea=Ic("EXTRACT",!0),Aa=Ic("CALL",!0),Ta=Ic("CASE",!0),Sa=Ic("WHEN",!0),Ua=Ic("ELSE",!0),_a=Ic("CAST",!0),xa=Ic("BOOL",!0),Ia=Ic("BOOLEAN",!0),Na=Ic("CHAR",!0),Ra=Ic("VARCHAR",!0),ka=Ic("NUMERIC",!0),Ma=Ic("DECIMAL",!0),Va=Ic("SIGNED",!0),qa=Ic("UNSIGNED",!0),Pa=Ic("INT",!0),Da=Ic("ZEROFILL",!0),Ga=Ic("INTEGER",!0),Qa=Ic("JSON",!0),Fa=Ic("JSONB",!0),$a=Ic("GEOMETRY",!0),Ba=Ic("SMALLINT",!0),Ha=Ic("SERIAL",!0),Ya=Ic("TINYINT",!0),Wa=Ic("TINYTEXT",!0),Xa=Ic("TEXT",!0),Za=Ic("MEDIUMTEXT",!0),za=Ic("LONGTEXT",!0),Ka=Ic("MEDIUMINT",!0),Ja=Ic("BIGINT",!0),ti=Ic("ENUM",!0),ri=Ic("FLOAT",!0),ei=Ic("DOUBLE",!0),ni=Ic("BIGSERIAL",!0),oi=Ic("REAL",!0),si=Ic("DATETIME",!0),ui=Ic("TIME",!0),ai=Ic("TIMESTAMP",!0),ii=Ic("TIMESTAMPTZ",!0),ci=Ic("TRUNCATE",!0),li=Ic("USER",!0),fi=Ic("UUID",!0),pi=Ic("OID",!0),bi=Ic("REGCLASS",!0),vi=Ic("REGCOLLATION",!0),yi=Ic("REGCONFIG",!0),di=Ic("REGDICTIONARY",!0),hi=Ic("REGNAMESPACE",!0),wi=Ic("REGOPER",!0),mi=Ic("REGOPERATOR",!0),Li=Ic("REGPROC",!0),Ci=Ic("REGPROCEDURE",!0),Oi=Ic("REGROLE",!0),ji=Ic("REGTYPE",!0),gi=Ic("CURRENT_DATE",!0),Ei=(Ic("ADDDATE",!0),Ic("INTERVAL",!0)),Ai=Ic("CURRENT_TIME",!0),Ti=Ic("CURRENT_TIMESTAMP",!0),Si=Ic("SYSTEM_USER",!0),Ui=Ic("GLOBAL",!0),_i=Ic("SESSION",!0),xi=Ic("PERSIST",!0),Ii=Ic("PERSIST_ONLY",!0),Ni=Ic("VIEW",!0),Ri=Ic("@",!1),ki=Ic("@@",!1),Mi=Ic("$$",!1),Vi=Ic("::",!1),qi=Ic("DUAL",!0),Pi=Ic("ADD",!0),Di=Ic("INDEX",!0),Gi=Ic("FULLTEXT",!0),Qi=Ic("SPATIAL",!0),Fi=Ic("CONCURRENTLY",!0),$i=Ic("REFERENCES",!0),Bi=Ic("SQL_CALC_FOUND_ROWS",!0),Hi=Ic("SQL_CACHE",!0),Yi=Ic("SQL_NO_CACHE",!0),Wi=Ic("SQL_SMALL_RESULT",!0),Xi=Ic("SQL_BIG_RESULT",!0),Zi=Ic("SQL_BUFFER_RESULT",!0),zi=Ic(",",!1),Ki=Ic("[",!1),Ji=Ic("]",!1),tc=Ic("->",!1),rc=Ic("->>",!1),ec=Ic("&&",!1),nc=Ic("/*",!1),oc=Ic("*/",!1),sc=Ic("--",!1),uc=(Ic("#",!1),{type:"any"}),ac=/^[ \t\n\r]/,ic=Nc([" ","\t","\n","\r"],!1,!1),cc=Ic("default",!0),lc=/^[^$]/,fc=Nc(["$"],!0,!1),pc=function(t){return{dataType:t}},bc=Ic("bytea",!0),vc=Ic("varying",!0),yc=Ic("PRECISION",!0),dc=Ic("WITHOUT",!0),hc=function(t){return{dataType:t}},wc=Ic("POINT",!0),mc=Ic("LINESTRING",!0),Lc=Ic("POLYGON",!0),Cc=Ic("MULTIPOINT",!0),Oc=Ic("MULTILINESTRING",!0),jc=Ic("MULTIPOLYGON",!0),gc=Ic("GEOMETRYCOLLECTION",!0),Ec=Ic("RECORD",!0),Ac=0,Tc=0,Sc=[{line:1,column:1}],Uc=0,_c=[],xc=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');a=u[r.startRule]}function Ic(t,r){return{type:"literal",text:t,ignoreCase:r}}function Nc(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function Rc(r){var e,n=Sc[r];if(n)return n;for(e=r-1;!Sc[e];)e--;for(n={line:(n=Sc[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Sc[r]=n,n}function kc(t,r){var e=Rc(t),n=Rc(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function Mc(t){Ac<Uc||(Ac>Uc&&(Uc=Ac,_c=[]),_c.push(t))}function Vc(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function qc(){var t,r;return t=Ac,fy()!==s?((r=tl())===s&&(r=Gc()),r!==s?(Tc=t,t=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=tl())===s&&(t=Gc()),t}function Pc(){var r;return(r=function(){var r,e,n,o,u,a,i;r=Ac,(e=yb())!==s&&fy()!==s&&(n=Rb())!==s&&fy()!==s?((o=Bc())===s&&(o=null),o!==s&&fy()!==s&&(u=pf())!==s?(Tc=r,c=e,l=n,f=o,(p=u)&&p.forEach(t=>Yy.add(`${c}::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:c.toLowerCase(),keyword:l.toLowerCase(),prefix:f,name:p}},r=e):(Ac=r,r=s)):(Ac=r,r=s);var c,l,f,p;r===s&&(r=Ac,(e=yb())!==s&&fy()!==s&&(n=Yv())!==s&&fy()!==s?((o=Kv())===s&&(o=null),o!==s&&fy()!==s?((u=Bc())===s&&(u=null),u!==s&&fy()!==s&&(a=ip())!==s&&fy()!==s?("cascade"===t.substr(Ac,7).toLowerCase()?(i=t.substr(Ac,7),Ac+=7):(i=s,0===xc&&Mc(Ft)),i===s&&("restrict"===t.substr(Ac,8).toLowerCase()?(i=t.substr(Ac,8),Ac+=8):(i=s,0===xc&&Mc($t))),i===s&&(i=null),i!==s?(Tc=r,e=function(t,r,e,n,o,s){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:[e,n].filter(t=>t).join(" "),name:o,options:s&&[{type:"origin",value:s}]}}}(e,n,o,u,a,i),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=yb())!==s&&fy()!==s&&(n=function(){var r,e,n,o;r=Ac,"type"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(pt));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TYPE"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?((o=Bc())===s&&(o=null),o!==s&&fy()!==s&&(u=Lf())!==s&&fy()!==s?("cascade"===t.substr(Ac,7).toLowerCase()?(a=t.substr(Ac,7),Ac+=7):(a=s,0===xc&&Mc(Ft)),a===s&&("restrict"===t.substr(Ac,8).toLowerCase()?(a=t.substr(Ac,8),Ac+=8):(a=s,0===xc&&Mc($t))),a===s&&(a=null),a!==s?(Tc=r,e=function(t,r,e,n,o){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:[e].filter(t=>t).join(" "),name:n,options:o&&[{type:"origin",value:o}]}}}(e,n,o,u,a),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=yb())!==s&&fy()!==s&&(n=Pv())!==s&&fy()!==s?((o=Bc())===s&&(o=null),o!==s&&fy()!==s&&(u=pf())!==s&&fy()!==s?((a=function(){var r,e;r=Ac,"restrict"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc($t));e===s&&("cascade"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ft)));e!==s&&(Tc=r,e=e.toLowerCase());return r=e}())===s&&(a=null),a!==s?(Tc=r,e=function(t,r,e,n,o){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:n,options:o&&[{type:"origin",value:o}]}}}(e,n,o,u,a),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s))));return r}())===s&&(r=function(){var r;(r=function(){var r,e,n,o,u,a,i,c,l,f,p;r=Ac,(e=mb())!==s&&fy()!==s?((n=Lb())===s&&(n=null),n!==s&&fy()!==s?((o=Cb())===s&&(o=null),o!==s&&fy()!==s&&Rb()!==s&&fy()!==s?((u=$c())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=Ub())!==s&&fy()!==s?("of"===t.substr(Ac,2).toLowerCase()?(n=t.substr(Ac,2),Ac+=2):(n=s,0===xc&&Mc(xt)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s&&(u=function(){var r,e,n;r=Ac,"for"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(_t));e!==s&&fy()!==s&&Gb()!==s&&fy()!==s&&(n=function(){var r,e,n,o,u;r=Ac,xb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=zp())!==s&&fy()!==s&&(n=oy())!==s&&fy()!==s&&bb()!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s&&(u=zp())!==s&&fy()!==s&&oy()!==s?(Tc=r,r={type:"for_values_item",keyword:"from",from:e,to:u}):(Ac=r,r=s);r===s&&(r=Ac,Kb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Df())!==s&&fy()!==s&&(n=oy())!==s?(Tc=r,r={type:"for_values_item",keyword:"in",in:e}):(Ac=r,r=s),r===s&&(r=Ac,Fb()!==s&&fy()!==s&&ny()!==s&&fy()!==s?("modulus"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(St)),e!==s&&fy()!==s&&(n=eb())!==s&&fy()!==s&&ry()!==s&&fy()!==s?("remainder"===t.substr(Ac,9).toLowerCase()?(o=t.substr(Ac,9),Ac+=9):(o=s,0===xc&&Mc(Ut)),o!==s&&fy()!==s&&(u=eb())!==s&&fy()!==s&&oy()!==s?(Tc=r,r={type:"for_values_item",keyword:"with",modulus:n,remainder:u}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)));return r}())!==s?(Tc=r,r=e={type:"for_values",keyword:"for values",expr:n}):(Ac=r,r=s);return r}())!==s&&fy()!==s?(a=Ac,(i=Vb())!==s&&(c=fy())!==s&&(l=fp())!==s?a=i=[i,c,l]:(Ac=a,a=s),a===s&&(a=null),a!==s?(Tc=r,e={type:"partition_of",keyword:"partition of",table:o,for_values:u,tablespace:(f=a)&&f[2]},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var f;return r}())!==s?(Tc=r,b=e,v=n,y=o,d=u,w=i,(h=a)&&h.forEach(t=>Yy.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:b[0].toLowerCase(),keyword:"table",temporary:v&&v[0].toLowerCase(),unlogged:y,if_not_exists:d,table:h,partition_of:w}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var b,v,y,d,h,w;r===s&&(r=Ac,(e=mb())!==s&&fy()!==s?((n=Lb())===s&&(n=null),n!==s&&fy()!==s?((o=Cb())===s&&(o=null),o!==s&&fy()!==s&&Rb()!==s&&fy()!==s?((u=$c())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s?((i=function(){var t,r,e,n,o,u,a,i,c;if(t=Ac,(r=ny())!==s)if(fy()!==s)if((e=nl())!==s){for(n=[],o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=nl())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);o!==s;)n.push(o),o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=nl())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);n!==s&&(o=fy())!==s&&(u=oy())!==s?(Tc=t,r=Gy(e,n),t=r):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;return t}())===s&&(i=null),i!==s&&fy()!==s?((c=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=_l())!==s){for(e=[],n=Ac,(o=fy())!==s?((u=ry())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=_l())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s?((u=ry())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=_l())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(c=null),c!==s&&fy()!==s?((l=Sb())===s&&(l=Ab()),l===s&&(l=null),l!==s&&fy()!==s?((f=Nb())===s&&(f=null),f!==s&&fy()!==s?((p=Fc())===s&&(p=null),p!==s?(Tc=r,e=function(t,r,e,n,o,s,u,a,i,c){return o&&o.forEach(t=>Yy.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),unlogged:e,if_not_exists:n,table:o,ignore_replace:a&&a[0].toLowerCase(),as:i&&i[0].toLowerCase(),query_expr:c&&c.ast,create_definitions:s,table_options:u}}}(e,n,o,u,a,i,c,l,f,p),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=mb())!==s&&fy()!==s?((n=Lb())===s&&(n=null),n!==s&&fy()!==s?((o=Cb())===s&&(o=null),o!==s&&fy()!==s&&Rb()!==s&&fy()!==s?((u=$c())===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function t(){var r,e;(r=function(){var t,r;t=Ac,tv()!==s&&fy()!==s&&(r=pf())!==s?(Tc=t,t={type:"like",table:r}):(Ac=t,t=s);return t}())===s&&(r=Ac,ny()!==s&&fy()!==s&&(e=t())!==s&&fy()!==s&&oy()!==s?(Tc=r,(n=e).parentheses=!0,r=n):(Ac=r,r=s));var n;return r}())!==s?(Tc=r,e=function(t,r,e,n,o,s){return o&&o.forEach(t=>Yy.add(`create::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),unlogged:e,if_not_exists:n,table:o,like:s}}}(e,n,o,u,a,i),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)));return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y,d,h,w,m,L,C,O;r=Ac,(e=mb())!==s&&fy()!==s?(n=Ac,(o=sv())!==s&&(u=fy())!==s&&(a=Ab())!==s?n=o=[o,u,a]:(Ac=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?((u=zv())===s&&(u=null),u!==s&&(a=fy())!==s?("trigger"===t.substr(Ac,7).toLowerCase()?(i=t.substr(Ac,7),Ac+=7):(i=s,0===xc&&Mc(Lr)),i!==s&&fy()!==s&&(c=Ep())!==s&&fy()!==s?("before"===t.substr(Ac,6).toLowerCase()?(l=t.substr(Ac,6),Ac+=6):(l=s,0===xc&&Mc(Cr)),l===s&&("after"===t.substr(Ac,5).toLowerCase()?(l=t.substr(Ac,5),Ac+=5):(l=s,0===xc&&Mc(Or)),l===s&&("instead of"===t.substr(Ac,10).toLowerCase()?(l=t.substr(Ac,10),Ac+=10):(l=s,0===xc&&Mc(jr)))),l!==s&&fy()!==s&&(f=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Tl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Tl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&fy()!==s?("on"===t.substr(Ac,2).toLowerCase()?(p=t.substr(Ac,2),Ac+=2):(p=s,0===xc&&Mc($)),p!==s&&fy()!==s&&(b=df())!==s&&fy()!==s?(v=Ac,(y=xb())!==s&&(d=fy())!==s&&(h=df())!==s?v=y=[y,d,h]:(Ac=v,v=s),v===s&&(v=null),v!==s&&(y=fy())!==s?((d=function(){var r,e,n,o,u;r=Ac,e=Ac,"not"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(D));n===s&&(n=null);n!==s&&(o=fy())!==s?("deferrable"===t.substr(Ac,10).toLowerCase()?(u=t.substr(Ac,10),Ac+=10):(u=s,0===xc&&Mc(Ar)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s);e!==s&&(n=fy())!==s?("initially immediate"===t.substr(Ac,19).toLowerCase()?(o=t.substr(Ac,19),Ac+=19):(o=s,0===xc&&Mc(Tr)),o===s&&("initially deferred"===t.substr(Ac,18).toLowerCase()?(o=t.substr(Ac,18),Ac+=18):(o=s,0===xc&&Mc(Sr))),o!==s?(Tc=r,i=o,e={keyword:(a=e)&&a[0]?a[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},r=e):(Ac=r,r=s)):(Ac=r,r=s);var a,i;return r}())===s&&(d=null),d!==s&&(h=fy())!==s?((w=function(){var r,e,n,o;r=Ac,"for"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(_t));e!==s&&fy()!==s?("each"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(Ur)),n===s&&(n=null),n!==s&&fy()!==s?("row"===t.substr(Ac,3).toLowerCase()?(o=t.substr(Ac,3),Ac+=3):(o=s,0===xc&&Mc(_r)),o===s&&("statement"===t.substr(Ac,9).toLowerCase()?(o=t.substr(Ac,9),Ac+=9):(o=s,0===xc&&Mc(xr))),o!==s?(Tc=r,u=e,i=o,e={keyword:(a=n)?`${u.toLowerCase()} ${a.toLowerCase()}`:u.toLowerCase(),args:i.toLowerCase()},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var u,a,i;return r}())===s&&(w=null),w!==s&&fy()!==s?((m=function(){var t,r;t=Ac,cv()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(r=Hf())!==s&&fy()!==s&&oy()!==s?(Tc=t,t={type:"when",cond:r,parentheses:!0}):(Ac=t,t=s);return t}())===s&&(m=null),m!==s&&fy()!==s?("execute"===t.substr(Ac,7).toLowerCase()?(L=t.substr(Ac,7),Ac+=7):(L=s,0===xc&&Mc(gr)),L!==s&&fy()!==s?("procedure"===t.substr(Ac,9).toLowerCase()?(C=t.substr(Ac,9),Ac+=9):(C=s,0===xc&&Mc(Er)),C===s&&("function"===t.substr(Ac,8).toLowerCase()?(C=t.substr(Ac,8),Ac+=8):(C=s,0===xc&&Mc(lt))),C!==s&&fy()!==s&&(O=Ty())!==s?(Tc=r,e=function(t,r,e,n,o,s,u,a,i,c,l,f,p,b,v,y){return{type:"create",replace:r&&"or replace",constraint:o,location:s&&s.toLowerCase(),events:u,table:i,from:c&&c[2],deferrable:l,for_each:f,when:p,execute:{keyword:"execute "+v.toLowerCase(),expr:y},constraint_type:n&&n.toLowerCase(),keyword:n&&n.toLowerCase(),constraint_kw:e&&e.toLowerCase(),resource:"constraint"}}(0,n,u,i,c,l,f,0,b,v,d,w,m,0,C,O),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,y,d,h,w;r=Ac,(e=mb())!==s&&fy()!==s?("extension"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(p)),n!==s&&fy()!==s?((o=$c())===s&&(o=null),o!==s&&fy()!==s?((u=Ep())===s&&(u=zp()),u!==s&&fy()!==s?((a=Fb())===s&&(a=null),a!==s&&fy()!==s?(i=Ac,"schema"===t.substr(Ac,6).toLowerCase()?(c=t.substr(Ac,6),Ac+=6):(c=s,0===xc&&Mc(b)),c!==s&&(l=fy())!==s&&(f=Ep())!==s?i=c=[c,l,f]:(Ac=i,i=s),i===s&&(i=zp()),i===s&&(i=null),i!==s&&(c=fy())!==s?(l=Ac,"version"===t.substr(Ac,7).toLowerCase()?(f=t.substr(Ac,7),Ac+=7):(f=s,0===xc&&Mc(v)),f!==s&&(y=fy())!==s?((d=Ep())===s&&(d=zp()),d!==s?l=f=[f,y,d]:(Ac=l,l=s)):(Ac=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?(y=Ac,(d=xb())!==s&&(h=fy())!==s?((w=Ep())===s&&(w=zp()),w!==s?y=d=[d,h,w]:(Ac=y,y=s)):(Ac=y,y=s),y===s&&(y=null),y!==s?(Tc=r,m=o,L=u,C=a,O=i,j=l,g=y,e={type:"create",keyword:n.toLowerCase(),if_not_exists:m,extension:By(L),with:C&&C[0].toLowerCase(),schema:By(O&&O[2].toLowerCase()),version:By(j&&j[2]),from:By(g&&g[2])},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var m,L,C,O,j,g;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y,d,h,w,m,L,C;r=Ac,(e=mb())!==s&&fy()!==s?((n=Xv())===s&&(n=null),n!==s&&fy()!==s&&(o=Yv())!==s&&fy()!==s?((u=$c())===s&&(u=null),u!==s?((a=Kv())===s&&(a=null),a!==s&&fy()!==s?((i=bp())===s&&(i=null),i!==s&&fy()!==s&&(c=qb())!==s&&fy()!==s&&(l=df())!==s&&fy()!==s?((f=cf())===s&&(f=null),f!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(p=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=el())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=el())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=el())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((b=function(){var r,e,n;r=Ac,"include"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(gt));e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=lp())!==s&&fy()!==s&&oy()!==s?(Tc=r,e=function(t,r){return{type:t.toLowerCase(),keyword:t.toLowerCase(),columns:r}}(e,n),r=e):(Ac=r,r=s);return r}())===s&&(b=null),b!==s&&fy()!==s?(v=Ac,(y=Fb())!==s&&(d=fy())!==s&&(h=ny())!==s&&(w=fy())!==s&&(m=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=ff())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ff())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ff())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&(L=fy())!==s&&(C=oy())!==s?v=y=[y,d,h,w,m,L,C]:(Ac=v,v=s),v===s&&(v=null),v!==s&&(y=fy())!==s?(d=Ac,(h=Vb())!==s&&(w=fy())!==s&&(m=Ep())!==s?d=h=[h,w,m]:(Ac=d,d=s),d===s&&(d=null),d!==s&&(h=fy())!==s?((w=mf())===s&&(w=null),w!==s&&(m=fy())!==s?(Tc=r,O=e,j=n,g=o,E=u,A=a,T=i,S=c,U=l,_=f,x=p,I=b,N=v,R=d,k=w,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:O[0].toLowerCase(),index_type:j&&j.toLowerCase(),keyword:g.toLowerCase(),concurrently:A&&A.toLowerCase(),index:T,if_not_exists:E,on_kw:S[0].toLowerCase(),table:U,index_using:_,index_columns:x,include:I,with:N&&N[4],with_before_where:!0,tablespace:R&&{type:"origin",value:R[2]},where:k}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var O,j,g,E,A,T,S,U,_,x,I,N,R,k;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=mb())!==s&&fy()!==s?((n=Lb())===s&&(n=Ob()),n===s&&(n=null),n!==s&&fy()!==s&&function(){var r,e,n,o;r=Ac,"sequence"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(le));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SEQUENCE"):(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s?((o=$c())===s&&(o=null),o!==s&&fy()!==s&&(u=df())!==s&&fy()!==s?(a=Ac,(i=Nb())!==s&&(c=fy())!==s&&(l=yp())!==s?a=i=[i,c,l]:(Ac=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?((c=function(){var t,r,e,n,o,u;if(t=Ac,(r=rl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=rl())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=rl())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e,1),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(c=null),c!==s?(Tc=r,e=function(t,r,e,n,o,s){return n.as=o&&o[2],{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:"sequence",temporary:r&&r[0].toLowerCase(),if_not_exists:e,sequence:[n],create_definitions:s}}}(e,n,o,u,a,c),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var t,r,e,n,o,u;t=Ac,(r=mb())!==s&&fy()!==s?((e=kb())===s&&(e=Mb()),e!==s&&fy()!==s?((n=$c())===s&&(n=null),n!==s&&fy()!==s&&(o=Ay())!==s&&fy()!==s?((u=function(){var t,r,e,n,o,u;if(t=Ac,(r=Ul())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Ul())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Ul())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e,1),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(u=null),u!==s?(Tc=t,r=function(t,r,e,n,o){const s=r.toLowerCase();return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:s,if_not_exists:e,[s]:{db:n.schema,schema:n.name},create_definitions:o}}}(r,e,n,o,u),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=mb())!==s&&fy()!==s?("domain"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(bt)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Nb())===s&&(u=null),u!==s&&fy()!==s&&(a=xy())!==s&&fy()!==s?((i=ul())===s&&(i=null),i!==s&&fy()!==s?((c=il())===s&&(c=null),c!==s&&fy()!==s?((l=gl())===s&&(l=null),l!==s?(Tc=r,e=function(t,r,e,n,o,s,u,a){a&&(a.type="constraint");const i=[s,u,a].filter(t=>t);return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:r.toLowerCase(),domain:{schema:e.db,name:e.table},as:n&&n[0]&&n[0].toLowerCase(),target:o,create_definitions:i}}}(e,n,o,u,a,i,c,l),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u;r=Ac,(e=mb())!==s&&fy()!==s?("type"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(pt)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=function(){var r,e,n,o,u;r=Ac,(e=Nb())!==s&&fy()!==s?((n=jv())===s&&("range"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(ft))),n!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s?((u=Df())===s&&(u=null),u!==s&&fy()!==s&&oy()!==s?(Tc=r,a=n,(i=u).parentheses=!0,e={as:"as",resource:a.toLowerCase(),create_definitions:i},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var a,i;r===s&&(r=Ac,(e=Nb())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=sl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=sl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(o=null),o!==s&&fy()!==s&&(u=oy())!==s?(Tc=r,e=function(t){return{as:"as",create_definitions:t}}(o),r=e):(Ac=r,r=s)):(Ac=r,r=s));return r}())===s&&(u=null),u!==s?(Tc=r,a=e,i=n,c=o,l=u,Xy.add([c.db,c.table].filter(t=>t).join(".")),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:a[0].toLowerCase(),keyword:i.toLowerCase(),name:{schema:c.db,name:c.table},...l}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var a,i,c,l;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p,b,v,m,L,C,O,j;r=Ac,(e=mb())!==s&&fy()!==s?(n=Ac,(o=sv())!==s&&(u=fy())!==s&&(a=Ab())!==s?n=o=[o,u,a]:(Ac=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?((u=Ob())===s&&(u=Lb()),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=Eb())===s&&(i=null),i!==s&&fy()!==s&&Pv()!==s&&fy()!==s&&(c=df())!==s&&fy()!==s?(l=Ac,(f=ny())!==s&&(p=fy())!==s&&(b=lp())!==s&&(v=fy())!==s&&(m=oy())!==s?l=f=[f,p,b,v,m]:(Ac=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?(p=Ac,(b=Fb())!==s&&(v=fy())!==s&&(m=ny())!==s&&(L=fy())!==s&&(C=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Hc())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Hc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Hc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&(O=fy())!==s&&(j=oy())!==s?p=b=[b,v,m,L,C,O,j]:(Ac=p,p=s),p===s&&(p=null),p!==s&&(b=fy())!==s&&(v=Nb())!==s&&(m=fy())!==s&&(L=Fl())!==s&&(C=fy())!==s?((O=function(){var r,e,n,o,u;r=Ac,(e=Fb())!==s&&fy()!==s?("cascaded"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(y)),n===s&&("local"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(d))),n!==s&&fy()!==s?("check"===t.substr(Ac,5).toLowerCase()?(o=t.substr(Ac,5),Ac+=5):(o=s,0===xc&&Mc(h)),o!==s&&fy()!==s?("OPTION"===t.substr(Ac,6)?(u="OPTION",Ac+=6):(u=s,0===xc&&Mc(w)),u!==s?(Tc=r,e=function(t){return`with ${t.toLowerCase()} check option`}(n),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);r===s&&(r=Ac,(e=Fb())!==s&&fy()!==s?("check"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(h)),n!==s&&fy()!==s?("OPTION"===t.substr(Ac,6)?(o="OPTION",Ac+=6):(o=s,0===xc&&Mc(w)),o!==s?(Tc=r,r=e="with check option"):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s));return r}())===s&&(O=null),O!==s?(Tc=r,e=function(t,r,e,n,o,s,u,a,i){return o.view=o.table,delete o.table,{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t[0].toLowerCase(),keyword:"view",replace:r&&"or replace",temporary:e&&e[0].toLowerCase(),recursive:n&&n.toLowerCase(),columns:s&&s[2],select:a,view:o,with_options:u&&u[4],with:i}}}(e,n,u,i,c,l,p,L,O),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=mb())!==s&&fy()!==s?(n=Ac,(o=sv())!==s&&(u=fy())!==s&&(a=Ab())!==s?n=o=[o,u,a]:(Ac=n,n=s),n===s&&(n=null),n!==s&&(o=fy())!==s?("aggregate"===t.substr(Ac,9).toLowerCase()?(u=t.substr(Ac,9),Ac+=9):(u=s,0===xc&&Mc(g)),u!==s&&(a=fy())!==s&&(i=df())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(c=ll())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(l=function(){var r,e,n,o,u,a,i,c;if(r=Ac,(e=function(){var r,e,n,o,u;r=Ac,"sfunc"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(O));e!==s&&fy()!==s&&$v()!==s&&fy()!==s&&(n=df())!==s&&fy()!==s&&ry()!==s&&fy()!==s?("stype"===t.substr(Ac,5).toLowerCase()?(o=t.substr(Ac,5),Ac+=5):(o=s,0===xc&&Mc(j)),o!==s&&fy()!==s&&$v()!==s&&fy()!==s&&(u=xy())!==s?(Tc=r,i=u,e=[{type:"sfunc",symbol:"=",value:{schema:(a=n).db,name:a.table}},{type:"stype",symbol:"=",value:i}],r=e):(Ac=r,r=s)):(Ac=r,r=s);var a,i;return r}())!==s){for(n=[],o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Yc())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);o!==s;)n.push(o),o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Yc())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);n!==s?(Tc=r,e=Gy(e,n),r=e):(Ac=r,r=s)}else Ac=r,r=s;return r}())!==s&&fy()!==s&&oy()!==s?(Tc=r,f=i,p=c,b=l,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"create",keyword:"aggregate",name:{schema:f.db,name:f.table},args:{parentheses:!0,expr:p,orderby:p.orderby},options:b}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var f,p,b;return r}());return r}())===s&&(r=Kc())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=Nv())!==s&&fy()!==s?((n=Rb())===s&&(n=null),n!==s&&fy()!==s?("only"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(Bt)),o===s&&(o=null),o!==s&&fy()!==s&&(u=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=cl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=cl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=cl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&fy()!==s?(a=Ac,"restart"===t.substr(Ac,7).toLowerCase()?(i=t.substr(Ac,7),Ac+=7):(i=s,0===xc&&Mc(Ht)),i===s&&("continue"===t.substr(Ac,8).toLowerCase()?(i=t.substr(Ac,8),Ac+=8):(i=s,0===xc&&Mc(Yt))),i!==s&&(c=fy())!==s?("identity"===t.substr(Ac,8).toLowerCase()?(l=t.substr(Ac,8),Ac+=8):(l=s,0===xc&&Mc(Wt)),l!==s?a=i=[i,c,l]:(Ac=a,a=s)):(Ac=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?("cascade"===t.substr(Ac,7).toLowerCase()?(c=t.substr(Ac,7),Ac+=7):(c=s,0===xc&&Mc(Ft)),c===s&&("restrict"===t.substr(Ac,8).toLowerCase()?(c=t.substr(Ac,8),Ac+=8):(c=s,0===xc&&Mc($t))),c===s&&(c=null),c!==s?(Tc=r,f=e,p=n,b=o,v=u,y=a,d=c,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:f.toLowerCase(),keyword:p&&p.toLowerCase()||"table",prefix:b,name:v,suffix:[y&&[y[0],y[2]].join(" "),d].filter(t=>t).map(t=>({type:"origin",value:t}))}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var f,p,b,v,y,d;return r}())===s&&(r=function(){var t,r,e;t=Ac,(r=Tb())!==s&&fy()!==s&&Rb()!==s&&fy()!==s&&(e=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=af())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=af())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=t,(n=e).forEach(t=>t.forEach(t=>t.table&&Yy.add(`rename::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`))),r={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"rename",table:n}},t=r):(Ac=t,t=s);var n;return t}())===s&&(r=function(){var r,e,n;r=Ac,(e=function(){var r,e,n,o;r=Ac,"call"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Aa));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CALL"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s&&(n=Ty())!==s?(Tc=r,o=n,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"call",expr:o}},r=e):(Ac=r,r=s);var o;return r}())===s&&(r=function(){var r,e,n;r=Ac,(e=function(){var r,e,n,o;r=Ac,"use"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Lu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s&&(n=bp())!==s?(Tc=r,o=n,Yy.add(`use::${o}::null`),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"use",db:o}},r=e):(Ac=r,r=s);var o;return r}())===s&&(r=function(){var r;(r=function(){var r,e,n,o,u,a,i;r=Ac,(e=db())!==s&&fy()!==s?((n=Rb())===s&&(n=null),n!==s&&fy()!==s?((o=Bc())===s&&(o=null),o!==s&&fy()!==s?("only"===t.substr(Ac,4).toLowerCase()?(u=t.substr(Ac,4),Ac+=4):(u=s,0===xc&&Mc(zt)),u===s&&(u=null),u!==s&&fy()!==s&&(a=pf())!==s&&fy()!==s&&(i=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=vl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=vl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=r,c=o,l=u,p=i,(f=a)&&f.length>0&&f.forEach(t=>Yy.add(`alter::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"alter",keyword:"table",if_exists:c,prefix:l&&{type:"origin",value:l},table:f,expr:p}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var c,l,f,p;return r}())===s&&(r=function(){var t,r,e,n,o;t=Ac,(r=db())!==s&&fy()!==s&&(e=Mb())!==s&&fy()!==s&&(n=Ep())!==s&&fy()!==s?((o=yl())===s&&(o=dl())===s&&(o=hl()),o!==s?(Tc=t,r=function(t,r,e){const n=t.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"alter",keyword:n,schema:r,expr:e}}}(e,n,o),t=r):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=function(){var r,e,n,o,u;r=Ac,(e=db())!==s&&fy()!==s?("domain"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(bt)),n===s&&("type"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(pt))),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=yl())===s&&(u=dl())===s&&(u=hl()),u!==s?(Tc=r,e=function(t,r,e){const n=t.toLowerCase();return e.resource=n,e[n]=e.table,delete e.table,{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"alter",keyword:n,name:{schema:r.db,name:r.table},expr:e}}}(n,o,u),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f;r=Ac,(e=db())!==s&&fy()!==s?("function"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(lt)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?(u=Ac,(a=ny())!==s&&(i=fy())!==s?((c=bl())===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s?u=a=[a,i,c,l,f]:(Ac=u,u=s)):(Ac=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=yl())===s&&(i=dl())===s&&(i=hl()),i!==s?(Tc=r,e=function(t,r,e,n){const o=t.toLowerCase();n.resource=o,n[o]=n.table,delete n.table;const s={};return e&&e[0]&&(s.parentheses=!0),s.expr=e&&e[2],{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"alter",keyword:o,name:{schema:r.db,name:r.table},args:s,expr:n}}}(n,o,u,i),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,(e=db())!==s&&fy()!==s?("aggregate"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(g)),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(u=ll())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((a=yl())===s&&(a=dl())===s&&(a=hl()),a!==s?(Tc=r,e=function(t,r,e,n){const o=t.toLowerCase();return n.resource=o,n[o]=n.table,delete n.table,{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"alter",keyword:o,name:{schema:r.db,name:r.table},args:{parentheses:!0,expr:e,orderby:e.orderby},expr:n}}}(n,o,u,a),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}());return r}())===s&&(r=function(){var r,e,n,o;r=Ac,(e=Ib())!==s&&fy()!==s?((n=function(){var r,e,n,o;r=Ac,"global"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ui));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="GLOBAL"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(n=function(){var r,e,n,o;r=Ac,"session"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(_i));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SESSION"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(n=qv())===s&&(n=function(){var r,e,n,o;r=Ac,"persist"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(xi));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="PERSIST"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(n=function(){var r,e,n,o;r=Ac,"persist_only"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Ii));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="PERSIST_ONLY"):(Ac=r,r=s)):(Ac=r,r=s);return r}()),n===s&&(n=null),n!==s&&fy()!==s&&(o=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Ly())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ly())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ly())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=r,u=n,a=o,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"set",keyword:u,expr:a}},r=e):(Ac=r,r=s)):(Ac=r,r=s);var u,a;return r}())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,(e=function(){var r,e,n,o;r=Ac,"lock"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ur));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?((n=Rb())===s&&(n=null),n!==s&&fy()!==s&&(o=pf())!==s&&fy()!==s?((u=function(){var r,e,n,o;r=Ac,"in"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(Yr));e!==s&&fy()!==s?("access share"===t.substr(Ac,12).toLowerCase()?(n=t.substr(Ac,12),Ac+=12):(n=s,0===xc&&Mc(Wr)),n===s&&("row share"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(Xr)),n===s&&("row exclusive"===t.substr(Ac,13).toLowerCase()?(n=t.substr(Ac,13),Ac+=13):(n=s,0===xc&&Mc(Zr)),n===s&&("share update exclusive"===t.substr(Ac,22).toLowerCase()?(n=t.substr(Ac,22),Ac+=22):(n=s,0===xc&&Mc(zr)),n===s&&("share row exclusive"===t.substr(Ac,19).toLowerCase()?(n=t.substr(Ac,19),Ac+=19):(n=s,0===xc&&Mc(Kr)),n===s&&("exclusive"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(ir)),n===s&&("access exclusive"===t.substr(Ac,16).toLowerCase()?(n=t.substr(Ac,16),Ac+=16):(n=s,0===xc&&Mc(Jr)),n===s&&("share"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(te))))))))),n!==s&&fy()!==s?("mode"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(re)),o!==s?(Tc=r,e={mode:`in ${n.toLowerCase()} mode`},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(u=null),u!==s&&fy()!==s?("nowait"===t.substr(Ac,6).toLowerCase()?(a=t.substr(Ac,6),Ac+=6):(a=s,0===xc&&Mc(ee)),a===s&&(a=null),a!==s?(Tc=r,i=n,l=u,f=a,(c=o)&&c.forEach(t=>Yy.add(`lock::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(t=>({table:t})),lock_mode:l,nowait:f}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var i,c,l,f;return r}())===s&&(r=function(){var r,e,n;r=Ac,(e=vb())!==s&&fy()!==s?("tables"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(ne)),n!==s?(Tc=r,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"show",keyword:"tables"}},r=e):(Ac=r,r=s)):(Ac=r,r=s);r===s&&(r=Ac,(e=vb())!==s&&fy()!==s&&(n=_y())!==s?(Tc=r,e=function(t){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"show",keyword:"var",var:t}}}(n),r=e):(Ac=r,r=s));return r}())===s&&(r=function(){var r,e,n,o;r=Ac,(e=function(){var r,e,n,o;r=Ac,"deallocate"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Gu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DEALLOCATE"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?("prepare"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(oe)),n===s&&(n=null),n!==s&&fy()!==s?((o=Ep())===s&&(o=Xb()),o!==s?(Tc=r,u=n,a=o,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"deallocate",keyword:u,expr:{type:"default",value:a}}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var u,a;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p;r=Ac,(e=ql())!==s&&fy()!==s&&(n=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Rl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Rl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&fy()!==s&&(o=qb())!==s&&fy()!==s?((u=function(){var r,e,n;r=Ac,(e=Rb())===s&&("sequence"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(le)),e===s&&("database"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(fe)),e===s&&("DOMAIN"===t.substr(Ac,6)?(e="DOMAIN",Ac+=6):(e=s,0===xc&&Mc(pe)),e===s&&("FUNCTION"===t.substr(Ac,8)?(e="FUNCTION",Ac+=8):(e=s,0===xc&&Mc(be)),e===s&&("procedure"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Er)),e===s&&("routine"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ve)),e===s&&("language"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(ye)),e===s&&("large"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(de)),e===s&&("SCHEMA"===t.substr(Ac,6)?(e="SCHEMA",Ac+=6):(e=s,0===xc&&Mc(he)))))))))));e!==s&&(Tc=r,e={type:"origin",value:e.toUpperCase()});(r=e)===s&&(r=Ac,(e=Xb())!==s&&fy()!==s?("tables"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(ne)),n===s&&("sequence"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(le)),n===s&&("functions"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(we)),n===s&&("procedures"===t.substr(Ac,10).toLowerCase()?(n=t.substr(Ac,10),Ac+=10):(n=s,0===xc&&Mc(me)),n===s&&("routines"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(Le)))))),n!==s&&fy()!==s&&Kb()!==s&&fy()!==s&&Mb()!==s?(Tc=r,r=e={type:"origin",value:`all ${n} in schema`}):(Ac=r,r=s)):(Ac=r,r=s));return r}())===s&&(u=null),u!==s&&(a=fy())!==s&&(i=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=kl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=kl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=kl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&(c=fy())!==s?((l=bb())===s&&(l=xb()),l!==s?(Tc=Ac,b=l,({revoke:"from",grant:"to"}[e.type].toLowerCase()===b[0].toLowerCase()?void 0:s)!==s&&fy()!==s&&(f=Vl())!==s&&fy()!==s?((p=function(){var r,e,n;r=Ac,Fb()!==s&&fy()!==s?("grant"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Oe)),e!==s&&fy()!==s?("option"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(je)),n!==s?(Tc=r,r={type:"origin",value:"with grant option"}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(p=null),p!==s?(Tc=r,e=function(t,r,e,n,o,s,u){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{...t,keyword:"priv",objects:r,on:{object_type:e,priv_level:n},to_from:o[0],user_or_roles:s,with:u}}}(e,n,u,i,l,f,p),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var b;r===s&&(r=Ac,(e=ql())!==s&&fy()!==s&&(n=vp())!==s&&fy()!==s?((o=bb())===s&&(o=xb()),o!==s?(Tc=Ac,(function(t,r,e){return{revoke:"from",grant:"to"}[t.type].toLowerCase()===e[0].toLowerCase()}(e,0,o)?void 0:s)!==s&&(u=fy())!==s&&(a=Vl())!==s&&(i=fy())!==s?((c=function(){var r,e,n;r=Ac,Fb()!==s&&fy()!==s?("admin"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ge)),e!==s&&fy()!==s?("option"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(je)),n!==s?(Tc=r,r={type:"origin",value:"with admin option"}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(c=null),c!==s?(Tc=r,e=function(t,r,e,n,o){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{...t,keyword:"role",objects:r.map(t=>({priv:{type:"string",value:t}})),to_from:e[0],user_or_roles:n,with:o}}}(e,n,o,a,c),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s));return r}())===s&&(r=function(){var r,e,n,o,u,a,c,l,f,p,b,v,y;r=Ac,"if"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(i));e!==s&&fy()!==s&&(n=Hf())!==s&&fy()!==s?("then"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(Te)),o!==s&&fy()!==s&&(u=Dc())!==s&&fy()!==s?((a=ay())===s&&(a=null),a!==s&&fy()!==s?((c=function(){var t,r,e,n,o,u;if(t=Ac,(r=Pl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Pl())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Pl())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e,1),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(c=null),c!==s&&fy()!==s?(l=Ac,(f=lv())!==s&&(p=fy())!==s&&(b=Dc())!==s?l=f=[f,p,b]:(Ac=l,l=s),l===s&&(l=null),l!==s&&(f=fy())!==s?((p=ay())===s&&(p=null),p!==s&&(b=fy())!==s?("end"===t.substr(Ac,3).toLowerCase()?(v=t.substr(Ac,3),Ac+=3):(v=s,0===xc&&Mc(Se)),v!==s&&fy()!==s?("if"===t.substr(Ac,2).toLowerCase()?(y=t.substr(Ac,2),Ac+=2):(y=s,0===xc&&Mc(i)),y!==s?(Tc=r,d=n,h=u,w=a,m=c,L=l,C=p,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"if",keyword:"if",boolean_expr:d,semicolons:[w||"",C||""],prefix:{type:"origin",value:"then"},if_expr:h,elseif_expr:m,else_expr:L&&L[2],suffix:{type:"origin",value:"end if"}}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var d,h,w,m,L,C;return r}())===s&&(r=function(){var r,e,n,o,u;r=Ac,"raise"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc($e));e!==s&&fy()!==s?((n=function(){var r;"debug"===t.substr(Ac,5).toLowerCase()?(r=t.substr(Ac,5),Ac+=5):(r=s,0===xc&&Mc(Ue));r===s&&("log"===t.substr(Ac,3).toLowerCase()?(r=t.substr(Ac,3),Ac+=3):(r=s,0===xc&&Mc(_e)),r===s&&("info"===t.substr(Ac,4).toLowerCase()?(r=t.substr(Ac,4),Ac+=4):(r=s,0===xc&&Mc(xe)),r===s&&("notice"===t.substr(Ac,6).toLowerCase()?(r=t.substr(Ac,6),Ac+=6):(r=s,0===xc&&Mc(Ie)),r===s&&("warning"===t.substr(Ac,7).toLowerCase()?(r=t.substr(Ac,7),Ac+=7):(r=s,0===xc&&Mc(Ne)),r===s&&("exception"===t.substr(Ac,9).toLowerCase()?(r=t.substr(Ac,9),Ac+=9):(r=s,0===xc&&Mc(Re)))))));return r}())===s&&(n=null),n!==s&&fy()!==s?((o=function(){var r,e,n,o,u,a,i,c;if(r=Ac,(e=zp())!==s){for(n=[],o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Ey())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);o!==s;)n.push(o),o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Ey())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);n!==s?(Tc=r,e={type:"format",keyword:e,expr:(l=n)&&l.map(t=>t[3])},r=e):(Ac=r,r=s)}else Ac=r,r=s;var l;r===s&&(r=Ac,"sqlstate"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Fe)),e!==s&&(n=fy())!==s&&(o=zp())!==s?(Tc=r,r=e={type:"sqlstate",keyword:{type:"origin",value:"SQLSTATE"},expr:[o]}):(Ac=r,r=s),r===s&&(r=Ac,(e=bp())!==s&&(Tc=r,e={type:"condition",expr:[{type:"default",value:e}]}),r=e));return r}())===s&&(o=null),o!==s&&fy()!==s?((u=function(){var r,e,n,o,u,a,i,c,l,f;if(r=Ac,(e=Qb())!==s)if(fy()!==s)if("message"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(ke)),n===s&&("detail"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(Me)),n===s&&("hint"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(Ve)),n===s&&("errcode"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(qe)),n===s&&("column"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(Pe)),n===s&&("constraint"===t.substr(Ac,10).toLowerCase()?(n=t.substr(Ac,10),Ac+=10):(n=s,0===xc&&Mc(De)),n===s&&("datatype"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(Ge)),n===s&&("table"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(Qe)),n===s&&("schema"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(b)))))))))),n!==s)if(fy()!==s)if($v()!==s)if(fy()!==s)if((o=Hf())!==s){for(u=[],a=Ac,(i=fy())!==s&&(c=ry())!==s&&(l=fy())!==s&&(f=Hf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s);a!==s;)u.push(a),a=Ac,(i=fy())!==s&&(c=ry())!==s&&(l=fy())!==s&&(f=Hf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s);u!==s?(Tc=r,e=function(t,r,e){const n=[r];return e&&e.forEach(t=>n.push(t[3])),{type:"using",option:t,symbol:"=",expr:n}}(n,o,u),r=e):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;return r}())===s&&(u=null),u!==s?(Tc=r,a=n,i=o,c=u,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"raise",level:a,using:c,raise:i}},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var a,i,c;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,"execute"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(gr));e!==s&&fy()!==s&&(n=bp())!==s&&fy()!==s?(o=Ac,(u=ny())!==s&&(a=fy())!==s&&(i=Sy())!==s&&(c=fy())!==s&&(l=oy())!==s?o=u=[u,a,i,c,l]:(Ac=o,o=s),o===s&&(o=null),o!==s?(Tc=r,f=n,p=o,e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"execute",name:f,args:p&&{type:"expr_list",value:p[2]}}},r=e):(Ac=r,r=s)):(Ac=r,r=s);var f,p;return r}())===s&&(r=function(){var r,e,n,o,u,a,i,c;r=Ac,(e=function(){var r,e,n;r=Ac,"for"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(_t));e!==s&&(Tc=r,e={label:null,keyword:"for"});(r=e)===s&&(r=Ac,(e=bp())!==s&&fy()!==s?("for"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(_t)),n!==s?(Tc=r,r=e={label:e,keyword:"for"}):(Ac=r,r=s)):(Ac=r,r=s));return r}())!==s&&fy()!==s&&(n=bp())!==s&&fy()!==s&&Kb()!==s&&fy()!==s&&(o=Fl())!==s&&fy()!==s?("loop"===t.substr(Ac,4).toLowerCase()?(u=t.substr(Ac,4),Ac+=4):(u=s,0===xc&&Mc(Be)),u!==s&&fy()!==s&&(a=Gc())!==s&&fy()!==s&&fv()!==s&&fy()!==s?("loop"===t.substr(Ac,4).toLowerCase()?(i=t.substr(Ac,4),Ac+=4):(i=s,0===xc&&Mc(Be)),i!==s&&fy()!==s?((c=bp())===s&&(c=null),c!==s?(Tc=Ac,f=c,(!(!(l=e).label||!f||l.label!==f)||!l.label&&!f?void 0:s)!==s?(Tc=r,e=function(t,r,e,n,o){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"for",label:o,target:r,query:e,stmts:n.ast}}}(0,n,o,a,c),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var l,f;return r}())===s&&(r=function(){var r,e,n,o;r=Ac,"commit"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(rn));e===s&&("rollback"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(en)));e!==s&&(Tc=r,e={type:"transaction",expr:{action:{type:"origin",value:e}}});(r=e)===s&&(r=Ac,"begin"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(nn)),e!==s&&fy()!==s?("work"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(on)),n===s&&("transaction"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(sn))),n===s&&(n=null),n!==s&&fy()!==s?((o=Gl())===s&&(o=null),o!==s?(Tc=r,e=function(t,r){return{type:"transaction",expr:{action:{type:"origin",value:"begin"},keyword:t,modes:r}}}(n,o),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"start"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(un)),e!==s&&fy()!==s?("transaction"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(an)),n!==s&&fy()!==s?((o=Gl())===s&&(o=null),o!==s?(Tc=r,e=function(t,r){return{type:"transaction",expr:{action:{type:"origin",value:"start"},keyword:t,modes:r}}}(n,o),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)));return r}())===s&&(r=function(){var r,e,n,o,u;r=Ac,"comment"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(bn));e!==s&&fy()!==s?("on"===t.substr(Ac,2).toLowerCase()?(n=t.substr(Ac,2),Ac+=2):(n=s,0===xc&&Mc($)),n!==s&&fy()!==s&&(o=function(){var r,e,n;r=Ac,(e=Rb())===s&&(e=Pv())===s&&(e=Vb());e!==s&&fy()!==s&&(n=df())!==s?(Tc=r,o=n,e={type:e.toLowerCase(),name:o},r=e):(Ac=r,r=s);var o;r===s&&(r=Ac,(e=Hv())!==s&&fy()!==s&&(n=ip())!==s?(Tc=r,e=function(t,r){return{type:t.toLowerCase(),name:r}}(e,n),r=e):(Ac=r,r=s),r===s&&(r=Ac,(e=Yv())===s&&(e=function(){var r,e,n,o;r=Ac,"collation"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Du));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="COLLATION"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(e=Vb())===s&&(e=Mb())===s&&("domain"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(bt)),e===s&&(e=kb())===s&&("role"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(cn)),e===s&&("sequence"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(le)),e===s&&("server"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(ln)),e===s&&("subscription"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(fn))))))),e!==s&&fy()!==s&&(n=pp())!==s?(Tc=r,e=function(t,r){return{type:t.toLowerCase(),name:r}}(e,n),r=e):(Ac=r,r=s)));return r}())!==s&&fy()!==s&&(u=function(){var r,e,n;r=Ac,"is"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(pn));e!==s&&fy()!==s?((n=zp())===s&&(n=Wp()),n!==s?(Tc=r,r=e={keyword:"is",expr:n}):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s?(Tc=r,r=e={type:"comment",keyword:"on",target:o,expr:u}):(Ac=r,r=s)):(Ac=r,r=s);return r}()),r}function Dc(){var r;return(r=Fc())===s&&(r=function(){var t,r,e,n,o,u,a,i;t=Ac,(r=fy())!==s?((e=$l())===s&&(e=null),e!==s&&fy()!==s&&wb()!==s&&fy()!==s&&(n=pf())!==s&&fy()!==s&&Ib()!==s&&fy()!==s&&(o=If())!==s&&fy()!==s?((u=uf())===s&&(u=null),u!==s&&fy()!==s?((a=mf())===s&&(a=null),a!==s&&fy()!==s?((i=Rf())===s&&(i=null),i!==s?(Tc=t,r=function(t,r,e,n,o,s){const u={},a=t=>{const{server:r,db:e,schema:n,as:o,table:s,join:a}=t,i=a?"select":"update",c=[r,e,n].filter(Boolean).join(".")||null;e&&(u[s]=c),s&&Yy.add(`${i}::${c}::${s}`)};return r&&r.forEach(a),n&&n.forEach(a),e&&e.forEach(t=>{if(t.table){const r=Fy(t.table);Yy.add(`update::${u[r]||null}::${r}`)}Wy.add(`update::${t.table}::${t.column.expr.value}`)}),{tableList:Array.from(Yy),columnList:$y(Wy),ast:{with:t,type:"update",table:r,set:e,from:n,where:o,returning:s}}}(e,n,o,u,a,i),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=Vf())!==s&&fy()!==s?((n=_b())===s&&(n=null),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Mf())===s&&(u=null),u!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(a=lp())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(i=kf())!==s&&fy()!==s?((c=function(){var r,e,n,o;r=Ac,qb()!==s&&fy()!==s?("conflict"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(qn)),e!==s&&fy()!==s?((n=function(){var t,r,e;t=Ac,(r=ny())!==s&&fy()!==s&&(e=Lf())!==s&&fy()!==s&&oy()!==s?(Tc=t,r=function(t){return{type:"column",expr:t,parentheses:!0}}(e),t=r):(Ac=t,t=s);return t}())===s&&(n=null),n!==s&&fy()!==s&&(o=function(){var r,e,n,o,u;r=Ac,"do"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(Mn));e!==s&&fy()!==s?("nothing"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(Vn)),n!==s?(Tc=r,r=e={keyword:"do",expr:{type:"origin",value:"nothing"}}):(Ac=r,r=s)):(Ac=r,r=s);r===s&&(r=Ac,"do"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(Mn)),e!==s&&fy()!==s&&(n=wb())!==s&&fy()!==s&&Ib()!==s&&fy()!==s&&(o=If())!==s&&fy()!==s?((u=mf())===s&&(u=null),u!==s?(Tc=r,r=e={keyword:"do",expr:{type:"update",set:o,where:u}}):(Ac=r,r=s)):(Ac=r,r=s));return r}())!==s?(Tc=r,r={type:"conflict",keyword:"on",target:n,action:o}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(c=null),c!==s&&fy()!==s?((l=Rf())===s&&(l=null),l!==s?(Tc=r,e=function(t,r,e,n,o,s,u){if(r&&(Yy.add(`insert::${[r.db,r.schema].filter(Boolean).join(".")||null}::${r.table}`),r.as=null),n){let t=r&&r.table||null;Array.isArray(o)&&o.forEach((t,r)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),n.forEach(r=>Wy.add(`insert::${t}::${r.value}`))}return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t,table:[r],columns:n,values:o,partition:e,conflict:s,returning:u}}}(e,o,u,a,i,c,l),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var t,r,e,n,o,u,a,i;t=Ac,(r=Vf())!==s&&fy()!==s?((e=Sb())===s&&(e=null),e!==s&&fy()!==s?((n=_b())===s&&(n=null),n!==s&&fy()!==s&&(o=df())!==s&&fy()!==s?((u=Mf())===s&&(u=null),u!==s&&fy()!==s&&(a=kf())!==s&&fy()!==s?((i=Rf())===s&&(i=null),i!==s?(Tc=t,r=function(t,r,e,n,o,s,u){n&&(Yy.add(`insert::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),Wy.add(`insert::${n.table}::(.*)`),n.as=null);const a=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:t,table:[n],columns:null,values:s,partition:o,prefix:a,returning:u}}}(r,e,n,o,u,a,i),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=function(){var t,r,e,n,o;t=Ac,(r=jb())!==s&&fy()!==s?((e=pf())===s&&(e=null),e!==s&&fy()!==s&&(n=uf())!==s&&fy()!==s?((o=mf())===s&&(o=null),o!==s?(Tc=t,r=function(t,r,e){if(r&&r.forEach(t=>{const{db:r,as:e,schema:n,table:o,join:s}=t,u=s?"select":"delete",a=[r,n].filter(Boolean).join(".")||null;o&&Yy.add(`${u}::${a}::${o}`),s||Wy.add(`delete::${o}::(.*)`)}),null===t&&1===r.length){const e=r[0];t=[{db:e.db,schema:e.schema,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"delete",table:t,from:r,where:e}}}(e,n,o),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=Pc())===s&&(r=function(){var t,r;t=[],r=my();for(;r!==s;)t.push(r),r=my();return t}()),r}function Gc(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Dc())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ay())!==s&&(a=fy())!==s&&(i=Dc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ay())!==s&&(a=fy())!==s&&(i=Dc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(Yy),columnList:$y(Wy),ast:n}}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Qc(){var r,e,n,o;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"union"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Wu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?((n=Xb())===s&&(n=Zb()),n===s&&(n=null),n!==s?(Tc=r,r=e=(o=n)?"union "+o.toLowerCase():"union"):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=function(){var r,e,n,o;r=Ac,"intersect"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Xu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e="intersect"),(r=e)===s&&(r=Ac,(e=function(){var r,e,n,o;r=Ac,"except"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Zu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e="except"),r=e)),r}function Fc(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Fl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Qc())!==s&&(a=fy())!==s&&(i=Fl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Qc())!==s&&(a=fy())!==s&&(i=Fl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s&&(n=fy())!==s?((o=Sf())===s&&(o=null),o!==s&&(u=fy())!==s?((a=xf())===s&&(a=null),a!==s?(Tc=t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&n.value&&n.value.length>0&&(t._limit=n),{tableList:Array.from(Yy),columnList:$y(Wy),ast:t}}(r,e,o,a)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)}else Ac=t,t=s;return t}function $c(){var r,e;return r=Ac,"if"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(i)),e!==s&&fy()!==s&&nv()!==s&&fy()!==s&&ev()!==s?(Tc=r,r=e="IF NOT EXISTS"):(Ac=r,r=s),r}function Bc(){var r,e,n;return r=Ac,"if"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(l)),e!==s&&fy()!==s?("exists"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(f)),n!==s?(Tc=r,r=e="IF EXISTS"):(Ac=r,r=s)):(Ac=r,r=s),r}function Hc(){var r,e,n;return r=Ac,"check_option"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(m)),e!==s&&fy()!==s&&$v()!==s&&fy()!==s?("cascaded"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(y)),n===s&&("local"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(d))),n!==s?(Tc=r,r=e={type:"check_option",value:n,symbol:"="}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"security_barrier"===t.substr(Ac,16).toLowerCase()?(e=t.substr(Ac,16),Ac+=16):(e=s,0===xc&&Mc(L)),e===s&&("security_invoker"===t.substr(Ac,16).toLowerCase()?(e=t.substr(Ac,16),Ac+=16):(e=s,0===xc&&Mc(C))),e!==s&&fy()!==s&&$v()!==s&&fy()!==s&&(n=Zp())!==s?(Tc=r,r=e=function(t,r){return{type:t.toLowerCase(),value:r.value?"true":"false",symbol:"="}}(e,n)):(Ac=r,r=s)),r}function Yc(){var t,r,e,n;return t=Ac,(r=bp())!==s&&fy()!==s&&$v()!==s&&fy()!==s?((e=bp())===s&&(e=Hf()),e!==s?(Tc=t,t=r={type:r,symbol:"=",value:"string"==typeof(n=e)?{type:"default",value:n}:n}):(Ac=t,t=s)):(Ac=t,t=s),t}function Wc(){var t,r,e;return t=Ac,(r=ip())!==s&&fy()!==s&&(e=xy())!==s?(Tc=t,t=r=function(t,r){return{column:t,definition:r}}(r,e)):(Ac=t,t=s),t}function Xc(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Wc())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Wc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Wc())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Zc(){var r,e,n,o,u,a,i,c,l,f,p,b;return r=Ac,(e=Ep())!==s?(Tc=Ac,("begin"!==e.toLowerCase()?void 0:s)!==s&&fy()!==s?("constant"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(T)),n===s&&(n=null),n!==s&&fy()!==s&&(o=xy())!==s&&fy()!==s?((u=ul())===s&&(u=null),u!==s&&fy()!==s?(a=Ac,(i=nv())!==s&&(c=fy())!==s&&(l=fb())!==s?a=i=[i,c,l]:(Ac=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s?(c=Ac,(l=pb())===s&&(":="===t.substr(Ac,2)?(l=":=",Ac+=2):(l=s,0===xc&&Mc(S))),l===s&&(l=null),l!==s&&(f=fy())!==s?(p=Ac,xc++,"begin"===t.substr(Ac,5).toLowerCase()?(b=t.substr(Ac,5),Ac+=5):(b=s,0===xc&&Mc(U)),xc--,b!==s?(Ac=p,p=void 0):p=s,p===s&&(p=Hp())===s&&(p=Hf()),p!==s?c=l=[l,f,p]:(Ac=c,c=s)):(Ac=c,c=s),c===s&&(c=null),c!==s&&(l=fy())!==s?((f=ay())===s&&(f=null),f!==s?(Tc=r,r=e=function(t,r,e,n,o,s,u){return{keyword:"variable",name:t,constant:r,datatype:e,collate:n,not_null:o&&"not null",definition:s&&s[0]&&{type:"default",keyword:s[0],value:s[2]}}}(e,n,o,u,a,c)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function zc(){var t,r,e,n,o,u;if(t=Ac,(r=Zc())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Zc())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Zc())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e,1)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Kc(){var r,e,n,o;return r=Ac,"declare"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(_)),e!==s&&fy()!==s&&(n=zc())!==s?(Tc=r,o=n,r=e={tableList:Array.from(Yy),columnList:$y(Wy),ast:{type:"declare",declare:o,symbol:";"}}):(Ac=r,r=s),r}function Jc(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y,d,h,w;if(r=Ac,"LANGUAGE"===t.substr(Ac,8)?(e="LANGUAGE",Ac+=8):(e=s,0===xc&&Mc(x)),e!==s&&(n=fy())!==s&&(o=Ep())!==s&&(u=fy())!==s?(Tc=r,r=e={prefix:"LANGUAGE",type:"default",value:o}):(Ac=r,r=s),r===s&&(r=Ac,"transorm"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(I)),e!==s&&(n=fy())!==s?(o=Ac,"FOR"===t.substr(Ac,3)?(u="FOR",Ac+=3):(u=s,0===xc&&Mc(N)),u!==s&&(a=fy())!==s?("TYPE"===t.substr(Ac,4)?(i="TYPE",Ac+=4):(i=s,0===xc&&Mc(R)),i!==s&&(c=fy())!==s&&(l=Ep())!==s?o=u=[u,a,i,c,l]:(Ac=o,o=s)):(Ac=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s?(Tc=r,r=e=(w=o)?{prefix:["TRANSORM",w[0].toUpperCase(),w[2].toUpperCase()].join(" "),type:"default",value:w[4]}:{type:"origin",value:"TRANSORM"}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"window"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(k)),e===s&&("immutable"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(M)),e===s&&("stable"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(V)),e===s&&("volatile"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(q)),e===s&&("strict"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(P)))))),e!==s&&(n=fy())!==s?(Tc=r,r=e={type:"origin",value:e}):(Ac=r,r=s),r===s&&(r=Ac,"not"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(D)),e===s&&(e=null),e!==s&&(n=fy())!==s?("leakproof"===t.substr(Ac,9).toLowerCase()?(o=t.substr(Ac,9),Ac+=9):(o=s,0===xc&&Mc(G)),o!==s&&(u=fy())!==s?(Tc=r,r=e={type:"origin",value:[e,"LEAKPROOF"].filter(t=>t).join(" ")}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"called"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Q)),e===s&&(e=Ac,"returns"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(E)),n!==s&&(o=fy())!==s?("null"===t.substr(Ac,4).toLowerCase()?(u=t.substr(Ac,4),Ac+=4):(u=s,0===xc&&Mc(F)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s)),e===s&&(e=null),e!==s&&(n=fy())!==s?("on"===t.substr(Ac,2).toLowerCase()?(o=t.substr(Ac,2),Ac+=2):(o=s,0===xc&&Mc($)),o!==s&&(u=fy())!==s?("null"===t.substr(Ac,4).toLowerCase()?(a=t.substr(Ac,4),Ac+=4):(a=s,0===xc&&Mc(F)),a!==s&&(i=fy())!==s?("input"===t.substr(Ac,5).toLowerCase()?(c=t.substr(Ac,5),Ac+=5):(c=s,0===xc&&Mc(B)),c!==s&&(l=fy())!==s?(Tc=r,r=e=function(t){return Array.isArray(t)&&(t=[t[0],t[2]].join(" ")),{type:"origin",value:t+" ON NULL INPUT"}}(e)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"external"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(H)),e===s&&(e=null),e!==s&&(n=fy())!==s?("security"===t.substr(Ac,8).toLowerCase()?(o=t.substr(Ac,8),Ac+=8):(o=s,0===xc&&Mc(Y)),o!==s&&(u=fy())!==s?("invoker"===t.substr(Ac,7).toLowerCase()?(a=t.substr(Ac,7),Ac+=7):(a=s,0===xc&&Mc(W)),a===s&&("definer"===t.substr(Ac,7).toLowerCase()?(a=t.substr(Ac,7),Ac+=7):(a=s,0===xc&&Mc(X))),a!==s&&(i=fy())!==s?(Tc=r,r=e=function(t,r){return{type:"origin",value:[t,"SECURITY",r].filter(t=>t).join(" ")}}(e,a)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"parallel"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Z)),e!==s&&(n=fy())!==s?("unsafe"===t.substr(Ac,6).toLowerCase()?(o=t.substr(Ac,6),Ac+=6):(o=s,0===xc&&Mc(z)),o===s&&("restricted"===t.substr(Ac,10).toLowerCase()?(o=t.substr(Ac,10),Ac+=10):(o=s,0===xc&&Mc(K)),o===s&&("safe"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(J)))),o!==s&&(u=fy())!==s?(Tc=r,r=e=function(t){return{type:"origin",value:["PARALLEL",t].join(" ")}}(o)):(Ac=r,r=s)):(Ac=r,r=s),r===s))))))){if(r=Ac,(e=Nb())!==s)if((n=fy())!==s){if(o=[],tt.test(t.charAt(Ac))?(u=t.charAt(Ac),Ac++):(u=s,0===xc&&Mc(rt)),u!==s)for(;u!==s;)o.push(u),tt.test(t.charAt(Ac))?(u=t.charAt(Ac),Ac++):(u=s,0===xc&&Mc(rt));else o=s;if(o!==s)if((u=fy())!==s)if((a=Kc())===s&&(a=null),a!==s)if((i=fy())!==s)if("begin"===t.substr(Ac,5).toLowerCase()?(c=t.substr(Ac,5),Ac+=5):(c=s,0===xc&&Mc(U)),c===s&&(c=null),c!==s)if((l=fy())!==s)if((f=Gc())!==s)if(fy()!==s)if((p=fv())===s&&(p=null),p!==s)if(Tc=Ac,h=p,((d=c)&&h||!d&&!h?void 0:s)!==s)if(fy()!==s)if((b=ay())===s&&(b=null),b!==s)if(fy()!==s){if(v=[],et.test(t.charAt(Ac))?(y=t.charAt(Ac),Ac++):(y=s,0===xc&&Mc(nt)),y!==s)for(;y!==s;)v.push(y),et.test(t.charAt(Ac))?(y=t.charAt(Ac),Ac++):(y=s,0===xc&&Mc(nt));else v=s;v!==s&&(y=fy())!==s?(Tc=r,r=e=function(t,r,e,n,o,s){const u=t.join(""),a=s.join("");if(u!==a)throw new Error(`start symbol '${u}'is not same with end symbol '${a}'`);return{type:"as",declare:r&&r.ast,begin:e,expr:Array.isArray(n.ast)?n.ast.flat():[n.ast],end:o&&o[0],symbol:u}}(o,a,c,f,p,v)):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s}else Ac=r,r=s;else Ac=r,r=s;r===s&&(r=Ac,"cost"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ot)),e===s&&("rows"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(st))),e!==s&&(n=fy())!==s&&(o=eb())!==s&&(u=fy())!==s?(Tc=r,r=e=function(t,r){return r.prefix=t,r}(e,o)):(Ac=r,r=s),r===s&&(r=Ac,"support"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ut)),e!==s&&(n=fy())!==s&&(o=Ay())!==s&&(u=fy())!==s?(Tc=r,r=e=function(t){return{prefix:"support",type:"default",value:[t.schema&&t.schema.value,t.name.value].filter(t=>t).join(".")}}(o)):(Ac=r,r=s),r===s&&(r=Ac,(e=Ib())!==s&&(n=fy())!==s&&(o=Ep())!==s&&(u=fy())!==s?(a=Ac,"to"===t.substr(Ac,2).toLowerCase()?(i=t.substr(Ac,2),Ac+=2):(i=s,0===xc&&Mc(at)),i===s&&(61===t.charCodeAt(Ac)?(i="=",Ac++):(i=s,0===xc&&Mc(it))),i!==s&&(c=fy())!==s&&(l=vp())!==s?a=i=[i,c,l]:(Ac=a,a=s),a===s&&(a=Ac,(i=xb())!==s&&(c=fy())!==s?("current"===t.substr(Ac,7).toLowerCase()?(l=t.substr(Ac,7),Ac+=7):(l=s,0===xc&&Mc(ct)),l!==s?a=i=[i,c,l]:(Ac=a,a=s)):(Ac=a,a=s)),a===s&&(a=null),a!==s&&(i=fy())!==s?(Tc=r,r=e=function(t,r){let e;if(r){const t=Array.isArray(r[2])?r[2]:[r[2]];e={prefix:r[0],expr:t.map(t=>({type:"default",value:t}))}}return{type:"set",parameter:t,value:e}}(o,a)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Cy()))))}return r}function tl(){var r,e,n,o,u,a,i,c,l,f,p;if(r=Ac,mb()!==s)if(fy()!==s)if(e=Ac,(n=sv())!==s&&(o=fy())!==s&&(u=Ab())!==s?e=n=[n,o,u]:(Ac=e,e=s),e===s&&(e=null),e!==s)if((n=fy())!==s)if("function"===t.substr(Ac,8).toLowerCase()?(o=t.substr(Ac,8),Ac+=8):(o=s,0===xc&&Mc(lt)),o!==s)if((u=fy())!==s)if((a=Ay())!==s)if(fy()!==s)if(ny()!==s)if(fy()!==s)if((i=bl())===s&&(i=null),i!==s)if(fy()!==s)if(oy()!==s)if(fy()!==s)if((c=function(){var r,e,n,o,u;return r=Ac,"returns"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(E)),e!==s&&fy()!==s?("setof"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(A)),n===s&&(n=null),n!==s&&fy()!==s?((o=xy())===s&&(o=df()),o!==s?(Tc=r,r=e={type:"returns",keyword:n,expr:o}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"returns"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(E)),e!==s&&fy()!==s&&(n=Rb())!==s&&fy()!==s&&(o=ny())!==s&&fy()!==s&&(u=Xc())!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={type:"returns",keyword:"table",expr:u}):(Ac=r,r=s)),r}())===s&&(c=null),c!==s)if(fy()!==s){for(l=[],f=Jc();f!==s;)l.push(f),f=Jc();l!==s&&(f=fy())!==s?((p=ay())===s&&(p=null),p!==s&&fy()!==s?(Tc=r,r=function(t,r,e,n,o,s,u){return{tableList:Array.from(Yy),columnList:$y(Wy),ast:{args:o||[],type:"create",replace:r&&"or replace",name:n,returns:s,keyword:e&&e.toLowerCase(),options:u||[]}}}(0,e,o,a,i,c,l)):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;return r}function rl(){var r;return(r=function(){var r,e,n,o,u,a;return r=Ac,"increment"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(vt)),e!==s&&fy()!==s?((n=Bb())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Tc=r,u=e,a=o,r=e={resource:"sequence",prefix:n?u.toLowerCase()+" by":u.toLowerCase(),value:a}):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n;return r=Ac,"minvalue"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(yt)),e!==s&&fy()!==s&&(n=eb())!==s?(Tc=r,r=e=dt(e,n)):(Ac=r,r=s),r===s&&(r=Ac,"no"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(ht)),e!==s&&fy()!==s?("minvalue"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(yt)),n!==s?(Tc=r,r=e={resource:"sequence",value:{type:"origin",value:"no minvalue"}}):(Ac=r,r=s)):(Ac=r,r=s)),r}())===s&&(r=function(){var r,e,n;return r=Ac,"maxvalue"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(wt)),e!==s&&fy()!==s&&(n=eb())!==s?(Tc=r,r=e=dt(e,n)):(Ac=r,r=s),r===s&&(r=Ac,"no"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(ht)),e!==s&&fy()!==s?("maxvalue"===t.substr(Ac,8).toLowerCase()?(n=t.substr(Ac,8),Ac+=8):(n=s,0===xc&&Mc(wt)),n!==s?(Tc=r,r=e={resource:"sequence",value:{type:"origin",value:"no maxvalue"}}):(Ac=r,r=s)):(Ac=r,r=s)),r}())===s&&(r=function(){var r,e,n,o,u,a;return r=Ac,"start"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(mt)),e!==s&&fy()!==s?((n=Fb())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Tc=r,u=e,a=o,r=e={resource:"sequence",prefix:n?u.toLowerCase()+" with":u.toLowerCase(),value:a}):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n;return r=Ac,"cache"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Lt)),e!==s&&fy()!==s&&(n=eb())!==s?(Tc=r,r=e=dt(e,n)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n;return r=Ac,"no"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(ht)),e===s&&(e=null),e!==s&&fy()!==s?("cycle"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(Ct)),n!==s?(Tc=r,r=e={resource:"sequence",value:{type:"origin",value:e?"no cycle":"cycle"}}):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n;return r=Ac,"owned"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Ot)),e!==s&&fy()!==s&&Bb()!==s&&fy()!==s?("none"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(jt)),n!==s?(Tc=r,r=e={resource:"sequence",prefix:"owned by",value:{type:"origin",value:"none"}}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"owned"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Ot)),e!==s&&fy()!==s&&Bb()!==s&&fy()!==s&&(n=ip())!==s?(Tc=r,r=e={resource:"sequence",prefix:"owned by",value:n}):(Ac=r,r=s)),r}()),r}function el(){var r,e,n,o,u,a,i,c,l;return r=Ac,(e=Hf())!==s&&fy()!==s?((n=ul())===s&&(n=null),n!==s&&fy()!==s?((o=bp())===s&&(o=null),o!==s&&fy()!==s?((u=Yb())===s&&(u=Wb()),u===s&&(u=null),u!==s&&fy()!==s?(a=Ac,"nulls"===t.substr(Ac,5).toLowerCase()?(i=t.substr(Ac,5),Ac+=5):(i=s,0===xc&&Mc(Et)),i!==s&&(c=fy())!==s?("first"===t.substr(Ac,5).toLowerCase()?(l=t.substr(Ac,5),Ac+=5):(l=s,0===xc&&Mc(At)),l===s&&("last"===t.substr(Ac,4).toLowerCase()?(l=t.substr(Ac,4),Ac+=4):(l=s,0===xc&&Mc(Tt))),l!==s?a=i=[i,c,l]:(Ac=a,a=s)):(Ac=a,a=s),a===s&&(a=null),a!==s?(Tc=r,r=e=function(t,r,e,n,o){return{collate:r,...t,opclass:e,order_by:n&&n.toLowerCase(),nulls:o&&`${o[0].toLowerCase()} ${o[2].toLowerCase()}`}}(e,n,o,u,a)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function nl(){var t;return(t=sl())===s&&(t=Ll())===s&&(t=Cl())===s&&(t=Ol()),t}function ol(){var r,e,n,o;return(r=function(){var t,r,e;t=Ac,(r=jl())!==s&&(Tc=t,r={constraint:r});(t=r)===s&&(t=Ac,(r=Xp())===s&&(r=Wp()),r!==s&&fy()!==s?((e=il())===s&&(e=null),e!==s?(Tc=t,r=function(t,r){return t&&!t.value&&(t.value="null"),{default_val:r,nullable:t}}(r,e),t=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=il())!==s&&fy()!==s?((e=Xp())===s&&(e=Wp()),e===s&&(e=null),e!==s?(Tc=t,r=function(t,r){return r&&!r.value&&(r.value="null"),{default_val:t,nullable:r}}(r,e),t=r):(Ac=t,t=s)):(Ac=t,t=s)));return t}())===s&&(r=Ac,"auto_increment"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(It)),e!==s&&(Tc=r,e={auto_increment:e.toLowerCase()}),(r=e)===s&&(r=Ac,"unique"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Nt)),e!==s&&fy()!==s?("key"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(Rt)),n===s&&(n=null),n!==s?(Tc=r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"primary"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(kt)),e===s&&(e=null),e!==s&&fy()!==s?("key"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(Rt)),n!==s?(Tc=r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=vy())!==s&&(Tc=r,e={comment:e}),(r=e)===s&&(r=Ac,(e=ul())!==s&&(Tc=r,e={collate:e}),(r=e)===s&&(r=Ac,(e=function(){var r,e,n;r=Ac,"column_format"===t.substr(Ac,13).toLowerCase()?(e=t.substr(Ac,13),Ac+=13):(e=s,0===xc&&Mc(Mt));e!==s&&fy()!==s?("fixed"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(Vt)),n===s&&("dynamic"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(qt)),n===s&&("default"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(Pt)))),n!==s?(Tc=r,e={type:"column_format",value:n.toLowerCase()},r=e):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={column_format:e}),(r=e)===s&&(r=Ac,(e=function(){var r,e,n;r=Ac,"storage"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Dt));e!==s&&fy()!==s?("disk"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(Gt)),n===s&&("memory"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(Qt))),n!==s?(Tc=r,e={type:"storage",value:n.toLowerCase()},r=e):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={storage:e}),(r=e)===s&&(r=Ac,(e=El())!==s&&(Tc=r,e={reference_definition:e}),(r=e)===s&&(r=Ac,(e=function(){var r,e,n,o,u,a,i,c;r=Ac,(e=jl())===s&&(e=null);e!==s&&fy()!==s?("check"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(h)),n!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(o=hf())!==s&&fy()!==s&&oy()!==s&&fy()!==s?(u=Ac,(a=nv())===s&&(a=null),a!==s&&(i=fy())!==s?("enforced"===t.substr(Ac,8).toLowerCase()?(c=t.substr(Ac,8),Ac+=8):(c=s,0===xc&&Mc(br)),c!==s?u=a=[a,i,c]:(Ac=u,u=s)):(Ac=u,u=s),u===s&&(u=null),u!==s?(Tc=r,e=function(t,r,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:r.toLowerCase(),keyword:t&&t.keyword,constraint:t&&t.constraint,definition:[e],enforced:o.filter(t=>t).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,u),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={check:e}),(r=e)===s&&(r=Ac,(e=Sl())!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s&&(o=fp())!==s?(Tc=r,r=e=function(t,r,e){return{character_set:{type:t,value:e,symbol:r}}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s))))))))))),r}function sl(){var t,r,e,n;return t=Ac,(r=ip())!==s&&fy()!==s?((e=xy())===s&&(e=wp()),e!==s&&fy()!==s?((n=function(){var t,r,e,n,o,u;if(t=Ac,(r=ol())!==s)if(fy()!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ol())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ol())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;return t}())===s&&(n=null),n!==s?(Tc=t,t=r=function(t,r,e){return Wy.add(`create::${t.table}::${t.column.expr.value}`),"double_quote_string"===r.type&&(r={dataType:`"${r.value}"`}),{column:t,definition:r,resource:"column",...e||{}}}(r,e,n)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t}function ul(){var r,e,n,o,u,a,i,c,l,f;return r=Ac,function(){var r,e,n,o;r=Ac,"collate"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(kr));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="COLLATE"):(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s?((e=$v())===s&&(e=null),e!==s&&fy()!==s?(n=Ac,(o=pp())!==s&&(u=fy())!==s&&(a=ty())!==s&&(i=fy())!==s?n=o=[o,u,a,i]:(Ac=n,n=s),n===s&&(n=null),n!==s&&(o=pp())!==s?(Tc=r,c=e,f=o,r={type:"collate",keyword:"collate",collate:{name:(l=n)?[l[0],f]:f,symbol:c}}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function al(){var t,r,e,n,o;return t=Ac,(r=pb())===s&&(r=$v()),r===s&&(r=null),r!==s&&fy()!==s&&(e=Hf())!==s?(Tc=t,o=e,t=r={type:"default",keyword:(n=r)&&n[0],value:o}):(Ac=t,t=s),t}function il(){var t,r;return t=Ac,pb()!==s&&fy()!==s&&(r=Hf())!==s?(Tc=t,t={type:"default",value:r}):(Ac=t,t=s),t}function cl(){var t,r,e,n,o;return t=Ac,(r=df())!==s&&fy()!==s?((e=ey())===s&&(e=null),e!==s?(Tc=t,n=r,o=e,Yy.add(`truncate::${[n.db,n.schema].filter(Boolean).join(".")||null}::${n.table}`),o&&(n.suffix=o),t=r=n):(Ac=t,t=s)):(Ac=t,t=s),t}function ll(){var t,r,e;return t=Ac,(r=ey())!==s&&(Tc=t,r=[{name:"*"}]),(t=r)===s&&(t=Ac,(r=bl())===s&&(r=null),r!==s&&fy()!==s&&Hb()!==s&&fy()!==s&&Bb()!==s&&fy()!==s&&(e=bl())!==s?(Tc=t,t=r=function(t,r){const e=t||[];return e.orderby=r,e}(r,e)):(Ac=t,t=s),t===s&&(t=bl())),t}function fl(){var r,e;return r=Ac,(e=Kb())===s&&("out"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Xt)),e===s&&("variadic"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Zt)))),e!==s&&(Tc=r,e=e.toUpperCase()),r=e}function pl(){var t,r,e,n,o;return t=Ac,(r=fl())===s&&(r=null),r!==s&&fy()!==s&&(e=xy())!==s&&fy()!==s?((n=al())===s&&(n=null),n!==s?(Tc=t,t=r={mode:r,type:e,default:n}):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=fl())===s&&(r=null),r!==s&&fy()!==s&&(e=Ep())!==s&&fy()!==s&&(n=xy())!==s&&fy()!==s?((o=al())===s&&(o=null),o!==s?(Tc=t,t=r=function(t,r,e,n){return{mode:t,name:r,type:e,default:n}}(r,e,n,o)):(Ac=t,t=s)):(Ac=t,t=s)),t}function bl(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=pl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=pl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=pl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function vl(){var r;return(r=function(){var t,r,e,n,o;t=Ac,(r=Bv())!==s&&fy()!==s?((e=Hv())===s&&(e=null),e!==s&&fy()!==s?((n=$c())===s&&(n=null),n!==s&&fy()!==s&&(o=sl())!==s?(Tc=t,u=e,a=o,r={action:"add",if_not_exists:n,...a,keyword:u,resource:"column",type:"alter"},t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);var u,a;return t}())===s&&(r=function(){var t,r,e;t=Ac,(r=Bv())!==s&&fy()!==s&&(e=Ol())!==s?(Tc=t,r=function(t){return{action:"add",create_definitions:t,resource:"constraint",type:"alter"}}(e),t=r):(Ac=t,t=s);return t}())===s&&(r=function(){var t,r,e,n,o;t=Ac,(r=yb())!==s&&fy()!==s?((e=Hv())===s&&(e=null),e!==s&&fy()!==s?((n=Bc())===s&&(n=null),n!==s&&fy()!==s&&(o=ip())!==s?(Tc=t,r=function(t,r,e){return{action:"drop",column:e,if_exists:r,keyword:t,resource:"column",type:"alter"}}(e,n,o),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=function(){var t,r,e;t=Ac,(r=Bv())!==s&&fy()!==s&&(e=Ll())!==s?(Tc=t,n=e,r={action:"add",type:"alter",...n},t=r):(Ac=t,t=s);var n;return t}())===s&&(r=function(){var t,r,e;t=Ac,(r=Bv())!==s&&fy()!==s&&(e=Cl())!==s?(Tc=t,n=e,r={action:"add",type:"alter",...n},t=r):(Ac=t,t=s);var n;return t}())===s&&(r=yl())===s&&(r=wl())===s&&(r=ml())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y;r=Ac,(e=db())!==s&&fy()!==s?((n=Hv())===s&&(n=null),n!==s&&fy()!==s&&(o=ip())!==s&&fy()!==s?(u=Ac,(a=Ib())!==s&&(i=fy())!==s?("data"===t.substr(Ac,4).toLowerCase()?(c=t.substr(Ac,4),Ac+=4):(c=s,0===xc&&Mc(cr)),c!==s?u=a=[a,i,c]:(Ac=u,u=s)):(Ac=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?("type"===t.substr(Ac,4).toLowerCase()?(i=t.substr(Ac,4),Ac+=4):(i=s,0===xc&&Mc(lr)),i!==s&&(c=fy())!==s&&(l=xy())!==s&&fy()!==s?((f=ul())===s&&(f=null),f!==s&&fy()!==s?(p=Ac,(b=Qb())!==s&&(v=fy())!==s&&(y=Hf())!==s?p=b=[b,v,y]:(Ac=p,p=s),p===s&&(p=null),p!==s?(Tc=r,e=function(t,r,e,n,o,s){return r.suffix=e?"set data type":"type",{action:"alter",column:r,keyword:t,resource:"column",definition:n,collate:o,using:s&&s[2],type:"alter"}}(n,o,u,l,f,p),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var t,r,e,n,o;t=Ac,(r=db())!==s&&fy()!==s?((e=Hv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s&&Ib()!==s&&fy()!==s&&pb()!==s&&fy()!==s&&(o=Hf())!==s?(Tc=t,r=function(t,r,e){return{action:"alter",column:r,keyword:t,resource:"column",default_val:{type:"set default",value:e},type:"alter"}}(e,n,o),t=r):(Ac=t,t=s)):(Ac=t,t=s);t===s&&(t=Ac,(r=db())!==s&&fy()!==s?((e=Hv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s&&yb()!==s&&fy()!==s&&pb()!==s?(Tc=t,r=function(t,r){return{action:"alter",column:r,keyword:t,resource:"column",default_val:{type:"drop default"},type:"alter"}}(e,n),t=r):(Ac=t,t=s)):(Ac=t,t=s));return t}())===s&&(r=function(){var t,r,e,n,o,u;t=Ac,(r=db())!==s&&fy()!==s?((e=Hv())===s&&(e=null),e!==s&&fy()!==s&&(n=ip())!==s&&fy()!==s?((o=Ib())===s&&(o=yb()),o!==s&&fy()!==s&&(u=Xp())!==s?(Tc=t,r=function(t,r,e,n){return n.action=e.toLowerCase(),{action:"alter",column:r,keyword:t,resource:"column",nullable:n,type:"alter"}}(e,n,o,u),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}()),r}function yl(){var t,r,e,n,o;return t=Ac,Tb()!==s&&fy()!==s?((r=bb())===s&&(r=Nb()),r===s&&(r=null),r!==s&&fy()!==s&&(e=bp())!==s?(Tc=t,o=e,t={action:"rename",type:"alter",resource:"table",keyword:(n=r)&&n[0].toLowerCase(),table:o}):(Ac=t,t=s)):(Ac=t,t=s),t}function dl(){var r,e,n;return r=Ac,"owner"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Kt)),e!==s&&fy()!==s&&bb()!==s&&fy()!==s?((n=bp())===s&&("current_role"===t.substr(Ac,12).toLowerCase()?(n=t.substr(Ac,12),Ac+=12):(n=s,0===xc&&Mc(Jt)),n===s&&("current_user"===t.substr(Ac,12).toLowerCase()?(n=t.substr(Ac,12),Ac+=12):(n=s,0===xc&&Mc(tr)),n===s&&("session_user"===t.substr(Ac,12).toLowerCase()?(n=t.substr(Ac,12),Ac+=12):(n=s,0===xc&&Mc(rr))))),n!==s?(Tc=r,r=e={action:"owner",type:"alter",resource:"table",keyword:"to",table:n}):(Ac=r,r=s)):(Ac=r,r=s),r}function hl(){var t,r;return t=Ac,Ib()!==s&&fy()!==s&&Mb()!==s&&fy()!==s&&(r=bp())!==s?(Tc=t,t={action:"set",type:"alter",resource:"table",keyword:"schema",table:r}):(Ac=t,t=s),t}function wl(){var r,e,n,o;return r=Ac,"algorithm"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(er)),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s?("default"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(Pt)),o===s&&("instant"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(nr)),o===s&&("inplace"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(or)),o===s&&("copy"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(sr))))),o!==s?(Tc=r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function ml(){var r,e,n,o;return r=Ac,"lock"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ur)),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s?("default"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(Pt)),o===s&&("none"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(jt)),o===s&&("shared"===t.substr(Ac,6).toLowerCase()?(o=t.substr(Ac,6),Ac+=6):(o=s,0===xc&&Mc(ar)),o===s&&("exclusive"===t.substr(Ac,9).toLowerCase()?(o=t.substr(Ac,9),Ac+=9):(o=s,0===xc&&Mc(ir))))),o!==s?(Tc=r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function Ll(){var t,r,e,n,o,u;return t=Ac,(r=Yv())===s&&(r=Wv()),r!==s&&fy()!==s?((e=jp())===s&&(e=null),e!==s&&fy()!==s?((n=cf())===s&&(n=null),n!==s&&fy()!==s&&(o=Hl())!==s&&fy()!==s?((u=lf())===s&&(u=null),u!==s&&fy()!==s?(Tc=t,t=r=function(t,r,e,n,o){return{index:r,definition:n,keyword:t.toLowerCase(),index_type:e,resource:"index",index_options:o}}(r,e,n,o,u)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t}function Cl(){var r,e,n,o,u,a;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"fulltext"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Gi));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="FULLTEXT"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(e=function(){var r,e,n,o;r=Ac,"spatial"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Qi));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SPATIAL"):(Ac=r,r=s)):(Ac=r,r=s);return r}()),e!==s&&fy()!==s?((n=Yv())===s&&(n=Wv()),n===s&&(n=null),n!==s&&fy()!==s?((o=jp())===s&&(o=null),o!==s&&fy()!==s&&(u=Hl())!==s&&fy()!==s?((a=lf())===s&&(a=null),a!==s&&fy()!==s?(Tc=r,r=e=function(t,r,e,n,o){return{index:e,definition:n,keyword:r&&`${t.toLowerCase()} ${r.toLowerCase()}`||t.toLowerCase(),index_options:o,resource:"index"}}(e,n,o,u,a)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function Ol(){var r;return(r=function(){var r,e,n,o,u,a;r=Ac,(e=jl())===s&&(e=null);e!==s&&fy()!==s?("primary key"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(fr)),n!==s&&fy()!==s?((o=cf())===s&&(o=null),o!==s&&fy()!==s&&(u=Hl())!==s&&fy()!==s?((a=lf())===s&&(a=null),a!==s?(Tc=r,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var i,c,l,f,p;return r}())===s&&(r=function(){var t,r,e,n,o,u,a,i;t=Ac,(r=jl())===s&&(r=null);r!==s&&fy()!==s&&(e=Xv())!==s&&fy()!==s?((n=Yv())===s&&(n=Wv()),n===s&&(n=null),n!==s&&fy()!==s?((o=jp())===s&&(o=null),o!==s&&fy()!==s?((u=cf())===s&&(u=null),u!==s&&fy()!==s&&(a=Hl())!==s&&fy()!==s?((i=lf())===s&&(i=null),i!==s?(Tc=t,l=e,f=n,p=o,b=u,v=a,y=i,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:y},t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);var c,l,f,p,b,v,y;return t}())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,(e=jl())===s&&(e=null);e!==s&&fy()!==s?("foreign key"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(pr)),n!==s&&fy()!==s?((o=jp())===s&&(o=null),o!==s&&fy()!==s&&(u=Hl())!==s&&fy()!==s?((a=El())===s&&(a=null),a!==s?(Tc=r,c=n,l=o,f=u,p=a,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var i,c,l,f,p;return r}())===s&&(r=gl()),r}function jl(){var t,r,e;return t=Ac,(r=zv())!==s&&fy()!==s?((e=bp())===s&&(e=null),e!==s?(Tc=t,t=r=function(t,r){return{keyword:t.toLowerCase(),constraint:r}}(r,e)):(Ac=t,t=s)):(Ac=t,t=s),t}function gl(){var r,e,n,o,u,a,i;return r=Ac,(e=jl())===s&&(e=null),e!==s&&fy()!==s?("check"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(h)),n!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(o=Yf())!==s&&fy()!==s&&oy()!==s?(Tc=r,a=n,i=o,r=e={constraint:(u=e)&&u.constraint,definition:[i],constraint_type:a.toLowerCase(),keyword:u&&u.keyword,resource:"constraint"}):(Ac=r,r=s)):(Ac=r,r=s),r}function El(){var r,e,n,o,u,a,i,c,l,f;return r=Ac,(e=Jv())!==s&&fy()!==s&&(n=df())!==s&&fy()!==s&&(o=Hl())!==s&&fy()!==s?("match full"===t.substr(Ac,10).toLowerCase()?(u=t.substr(Ac,10),Ac+=10):(u=s,0===xc&&Mc(vr)),u===s&&("match partial"===t.substr(Ac,13).toLowerCase()?(u=t.substr(Ac,13),Ac+=13):(u=s,0===xc&&Mc(yr)),u===s&&("match simple"===t.substr(Ac,12).toLowerCase()?(u=t.substr(Ac,12),Ac+=12):(u=s,0===xc&&Mc(dr)))),u===s&&(u=null),u!==s&&fy()!==s?((a=Al())===s&&(a=null),a!==s&&fy()!==s?((i=Al())===s&&(i=null),i!==s?(Tc=r,c=u,l=a,f=i,r=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=Al())!==s&&(Tc=r,e={on_action:[e]}),r=e),r}function Al(){var r,e,n,o;return r=Ac,qb()!==s&&fy()!==s?((e=jb())===s&&(e=wb()),e!==s&&fy()!==s&&(n=function(){var r,e,n;r=Ac,(e=kv())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=Df())===s&&(n=null),n!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(Ac=r,r=s)):(Ac=r,r=s);r===s&&(r=Ac,"restrict"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc($t)),e===s&&("cascade"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ft)),e===s&&("set null"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(hr)),e===s&&("no action"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(wr)),e===s&&("set default"===t.substr(Ac,11).toLowerCase()?(e=t.substr(Ac,11),Ac+=11):(e=s,0===xc&&Mc(mr)),e===s&&(e=kv()))))),e!==s&&(Tc=r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==s?(Tc=r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(Ac=r,r=s)):(Ac=r,r=s),r}function Tl(){var r,e,n,o,u,a,i;return r=Ac,(e=gb())===s&&(e=jb())===s&&(e=Nv()),e!==s&&(Tc=r,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(r=e)===s&&(r=Ac,(e=wb())!==s&&fy()!==s?(n=Ac,"of"===t.substr(Ac,2).toLowerCase()?(o=t.substr(Ac,2),Ac+=2):(o=s,0===xc&&Mc(xt)),o!==s&&(u=fy())!==s&&(a=Lf())!==s?n=o=[o,u,a]:(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=r,r=e=function(t,r){return{keyword:t&&t[0]&&t[0].toLowerCase(),args:r&&{keyword:r[0],columns:r[2]}||null}}(e,n)):(Ac=r,r=s)):(Ac=r,r=s)),r}function Sl(){var r,e,n;return r=Ac,"character"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Ir)),e!==s&&fy()!==s?("set"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(Nr)),n!==s?(Tc=r,r=e="CHARACTER SET"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ul(){var r,e,n,o,u,a,i,c,l;return r=Ac,(e=pb())===s&&(e=null),e!==s&&fy()!==s?((n=Sl())===s&&("charset"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(Rr)),n===s&&("collate"===t.substr(Ac,7).toLowerCase()?(n=t.substr(Ac,7),Ac+=7):(n=s,0===xc&&Mc(kr)))),n!==s&&fy()!==s?((o=$v())===s&&(o=null),o!==s&&fy()!==s&&(u=fp())!==s?(Tc=r,i=n,c=o,l=u,r=e={keyword:(a=e)&&`${a[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function _l(){var r,e,n,o,u,a,i,c,l;return r=Ac,"auto_increment"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(It)),e===s&&("avg_row_length"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(Mr)),e===s&&("key_block_size"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(Vr)),e===s&&("max_rows"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(qr)),e===s&&("min_rows"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Pr)),e===s&&("stats_sample_pages"===t.substr(Ac,18).toLowerCase()?(e=t.substr(Ac,18),Ac+=18):(e=s,0===xc&&Mc(Dr))))))),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Tc=r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ul())===s&&(r=Ac,(e=Zv())===s&&("connection"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Gr))),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s&&(o=zp())!==s?(Tc=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"compression"===t.substr(Ac,11).toLowerCase()?(e=t.substr(Ac,11),Ac+=11):(e=s,0===xc&&Mc(Qr)),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s?(o=Ac,39===t.charCodeAt(Ac)?(u="'",Ac++):(u=s,0===xc&&Mc(Fr)),u!==s?("zlib"===t.substr(Ac,4).toLowerCase()?(a=t.substr(Ac,4),Ac+=4):(a=s,0===xc&&Mc($r)),a===s&&("lz4"===t.substr(Ac,3).toLowerCase()?(a=t.substr(Ac,3),Ac+=3):(a=s,0===xc&&Mc(Br)),a===s&&("none"===t.substr(Ac,4).toLowerCase()?(a=t.substr(Ac,4),Ac+=4):(a=s,0===xc&&Mc(jt)))),a!==s?(39===t.charCodeAt(Ac)?(i="'",Ac++):(i=s,0===xc&&Mc(Fr)),i!==s?o=u=[u,a,i]:(Ac=o,o=s)):(Ac=o,o=s)):(Ac=o,o=s),o!==s?(Tc=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"engine"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Hr)),e!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s&&(o=Ep())!==s?(Tc=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=Ub())!==s&&fy()!==s&&(n=Bb())!==s&&fy()!==s&&(o=Hf())!==s?(Tc=r,r=e=function(t){return{keyword:"partition by",value:t}}(o)):(Ac=r,r=s))))),r}function xl(){var r,e,n;return r=Ac,(e=hb())===s&&(e=gb())===s&&(e=wb())===s&&(e=jb())===s&&(e=Nv())===s&&(e=Jv())===s&&("trigger"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Lr))),e!==s&&(Tc=r,n=e,e={type:"origin",value:Array.isArray(n)?n[0]:n}),r=e}function Il(){var r,e,n,o;return r=Ac,Xb()!==s?(e=Ac,(n=fy())!==s?("privileges"===t.substr(Ac,10).toLowerCase()?(o=t.substr(Ac,10),Ac+=10):(o=s,0===xc&&Mc(ie)),o!==s?e=n=[n,o]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=null),e!==s?(Tc=r,r={type:"origin",value:e?"all privileges":"all"}):(Ac=r,r=s)):(Ac=r,r=s),r}function Nl(){var r;return(r=xl())===s&&(r=function(){var r,e;return r=Ac,"usage"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(se)),e===s&&(e=hb())===s&&(e=wb()),e!==s&&(Tc=r,e=ue(e)),r=e}())===s&&(r=function(){var r,e;return r=Ac,(e=mb())===s&&("connect"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ae)),e===s&&(e=Lb())===s&&(e=Ob())),e!==s&&(Tc=r,e=ue(e)),r=e}())===s&&(r=function(){var r,e;return r=Ac,"usage"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(se)),e!==s&&(Tc=r,e=ce(e)),(r=e)===s&&(r=Il()),r}())===s&&(r=function(){var r,e;return r=Ac,"execute"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(gr)),e!==s&&(Tc=r,e=ce(e)),(r=e)===s&&(r=Il()),r}()),r}function Rl(){var t,r,e,n,o,u,a,i;return t=Ac,(r=Nl())!==s&&fy()!==s?(e=Ac,(n=ny())!==s&&(o=fy())!==s&&(u=Lf())!==s&&(a=fy())!==s&&(i=oy())!==s?e=n=[n,o,u,a,i]:(Ac=e,e=s),e===s&&(e=null),e!==s?(Tc=t,t=r=function(t,r){return{priv:t,columns:r&&r[2]}}(r,e)):(Ac=t,t=s)):(Ac=t,t=s),t}function kl(){var t,r,e,n,o,u,a;return t=Ac,r=Ac,(e=bp())!==s&&(n=fy())!==s&&(o=ty())!==s?r=e=[e,n,o]:(Ac=r,r=s),r===s&&(r=null),r!==s&&(e=fy())!==s?((n=bp())===s&&(n=ey()),n!==s?(Tc=t,a=n,t=r={prefix:(u=r)&&u[0],name:a}):(Ac=t,t=s)):(Ac=t,t=s),t}function Ml(){var r,e,n,o;return r=Ac,(e=$b())===s&&(e=null),e!==s&&fy()!==s&&(n=bp())!==s?(Tc=r,o=n,r=e={name:{type:"origin",value:e?`${group} ${o}`:o}}):(Ac=r,r=s),r===s&&(r=Ac,"public"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ce)),e===s&&(e=function(){var r,e,n,o;r=Ac,"current_role"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Jt));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CURRENT_ROLE"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(e=Mv())===s&&(e=Vv()),e!==s&&(Tc=r,e=function(t){return{name:{type:"origin",value:t}}}(e)),r=e),r}function Vl(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Ml())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ml())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function ql(){var r,e,n,o,u,a,i,c;return r=Ac,"grant"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Oe)),e!==s&&(Tc=r,e={type:"grant"}),(r=e)===s&&(r=Ac,"revoke"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ee)),e!==s&&fy()!==s?(n=Ac,"grant"===t.substr(Ac,5).toLowerCase()?(o=t.substr(Ac,5),Ac+=5):(o=s,0===xc&&Mc(Oe)),o!==s&&(u=fy())!==s?("option"===t.substr(Ac,6).toLowerCase()?(a=t.substr(Ac,6),Ac+=6):(a=s,0===xc&&Mc(je)),a!==s&&(i=fy())!==s?("for"===t.substr(Ac,3).toLowerCase()?(c=t.substr(Ac,3),Ac+=3):(c=s,0===xc&&Mc(_t)),c!==s?n=o=[o,u,a,i,c]:(Ac=n,n=s)):(Ac=n,n=s)):(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=r,r=e={type:"revoke",grant_option_for:n&&{type:"origin",value:"grant option for"}}):(Ac=r,r=s)):(Ac=r,r=s)),r}function Pl(){var r,e,n,o,u,a;return r=Ac,"elseif"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ae)),e!==s&&fy()!==s&&(n=Hf())!==s&&fy()!==s?("then"===t.substr(Ac,4).toLowerCase()?(o=t.substr(Ac,4),Ac+=4):(o=s,0===xc&&Mc(Te)),o!==s&&fy()!==s&&(u=Dc())!==s&&fy()!==s?((a=ay())===s&&(a=null),a!==s?(Tc=r,r=e={type:"elseif",boolean_expr:n,then:u,semicolon:a}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function Dl(){var r,e,n,o;return r=Ac,"isolation"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Ke)),e!==s&&fy()!==s?("level"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(Je)),n!==s&&fy()!==s&&(o=function(){var r,e,n;return r=Ac,"serializable"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(He)),e!==s&&(Tc=r,e={type:"origin",value:"serializable"}),(r=e)===s&&(r=Ac,"repeatable"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Ye)),e!==s&&fy()!==s?("read"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(We)),n!==s?(Tc=r,r=e={type:"origin",value:"repeatable read"}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"read"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(We)),e!==s&&fy()!==s?("committed"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(Xe)),n===s&&("uncommitted"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(Ze))),n!==s?(Tc=r,r=e=ze(n)):(Ac=r,r=s)):(Ac=r,r=s))),r}())!==s?(Tc=r,r=e={type:"origin",value:"isolation level "+o.value}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"read"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(We)),e!==s&&fy()!==s?("write"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(tn)),n===s&&("only"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(Bt))),n!==s?(Tc=r,r=e=ze(n)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=nv())===s&&(e=null),e!==s&&fy()!==s?("deferrable"===t.substr(Ac,10).toLowerCase()?(n=t.substr(Ac,10),Ac+=10):(n=s,0===xc&&Mc(Ar)),n!==s?(Tc=r,r=e={type:"origin",value:e?"not deferrable":"deferrable"}):(Ac=r,r=s)):(Ac=r,r=s))),r}function Gl(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Dl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Dl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Dl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Ql(){var r,e,n,o,u,a,i;return r=Ac,e=Ac,40===t.charCodeAt(Ac)?(n="(",Ac++):(n=s,0===xc&&Mc(vn)),n!==s&&(o=fy())!==s&&(u=Fl())!==s&&(a=fy())!==s?(41===t.charCodeAt(Ac)?(i=")",Ac++):(i=s,0===xc&&Mc(yn)),i!==s?e=n=[n,o,u,a,i]:(Ac=e,e=s)):(Ac=e,e=s),e!==s&&(Tc=r,e={...e[2],parentheses_symbol:!0}),r=e}function Fl(){var r,e;return r=Ac,hb()!==s&&fy()!==s?(59===t.charCodeAt(Ac)?(e=";",Ac++):(e=s,0===xc&&Mc(dn)),e!==s?(Tc=r,r={type:"select"}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Wl())===s&&(r=Ql()),r}function $l(){var t,r,e,n,o,u,a,i,c;if(t=Ac,Fb()!==s)if(fy()!==s)if((r=Bl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Bl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Bl())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;return t===s&&(t=Ac,fy()!==s&&Fb()!==s&&(r=fy())!==s&&(e=Eb())!==s&&(n=fy())!==s&&(o=Bl())!==s?(Tc=t,(c=o).recursive=!0,t=[c]):(Ac=t,t=s)),t}function Bl(){var t,r,e,n,o,u;return t=Ac,(r=zp())===s&&(r=Ep()),r!==s&&fy()!==s?((e=Hl())===s&&(e=null),e!==s&&fy()!==s&&Nb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Dc())!==s&&fy()!==s&&oy()!==s?(Tc=t,u=e,"string"==typeof(o=r)&&(o={type:"default",value:o}),t=r={name:o,stmt:n.ast,columns:u}):(Ac=t,t=s)):(Ac=t,t=s),t}function Hl(){var t,r;return t=Ac,ny()!==s&&fy()!==s&&(r=Lf())!==s&&fy()!==s&&oy()!==s?(Tc=t,t=r):(Ac=t,t=s),t}function Yl(){var t,r,e;return t=Ac,(r=Zb())!==s&&fy()!==s&&qb()!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Zl())!==s&&fy()!==s&&oy()!==s?(Tc=t,t=r=function(t,r,e){return console.lo,{type:t+" ON",columns:e}}(r,0,e)):(Ac=t,t=s),t===s&&(t=Ac,(r=Zb())===s&&(r=null),r!==s&&(Tc=t,r={type:r}),t=r),t}function Wl(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y,d;return r=Ac,fy()!==s?((e=$l())===s&&(e=null),e!==s&&fy()!==s&&hb()!==s&&py()!==s?((n=function(){var t,r,e,n,o,u;if(t=Ac,(r=Xl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Xl())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Xl())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())===s&&(n=null),n!==s&&fy()!==s?((o=Yl())===s&&(o=null),o!==s&&fy()!==s&&(u=zl())!==s&&fy()!==s?((a=sf())===s&&(a=null),a!==s&&fy()!==s?((i=uf())===s&&(i=null),i!==s&&fy()!==s?((c=sf())===s&&(c=null),c!==s&&fy()!==s?((l=mf())===s&&(l=null),l!==s&&fy()!==s?((f=function(){var t,r,e;t=Ac,(r=$b())!==s&&fy()!==s&&Bb()!==s&&fy()!==s&&(e=Df())!==s?(Tc=t,r={columns:e.value},t=r):(Ac=t,t=s);return t}())===s&&(f=null),f!==s&&fy()!==s?((p=function(){var r,e;r=Ac,function(){var r,e,n,o;r=Ac,"having"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(na));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s&&(e=Yf())!==s?(Tc=r,r=e):(Ac=r,r=s);return r}())===s&&(p=null),p!==s&&fy()!==s?((b=Sf())===s&&(b=null),b!==s&&fy()!==s?((v=xf())===s&&(v=null),v!==s&&fy()!==s?((y=function(){var r,e;r=Ac,function(){var r,e,n,o;r=Ac,"window"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(k));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s&&(e=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Cf())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Cf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Cf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=r,r={keyword:"window",type:"window",expr:e}):(Ac=r,r=s);return r}())===s&&(y=null),y!==s&&fy()!==s?((d=sf())===s&&(d=null),d!==s?(Tc=r,r=function(t,r,e,n,o,s,u,a,i,c,l,f,p,b){if(o&&u||o&&b||u&&b||o&&u&&b)throw new Error("A given SQL statement can contain at most one INTO clause");return s&&s.forEach(t=>t.table&&Yy.add(`select::${[t.db,t.schema].filter(Boolean).join(".")||null}::${t.table}`)),{with:t,type:"select",options:r,distinct:e,columns:n,into:{...o||u||b||{},position:(o?"column":u&&"from")||b&&"end"},from:s,where:a,groupby:i,having:c,orderby:l,limit:f,window:p}}(e,n,o,u,a,i,c,l,f,p,b,v,y,d)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function Xl(){var r,e;return r=Ac,(e=function(){var r;"sql_calc_found_rows"===t.substr(Ac,19).toLowerCase()?(r=t.substr(Ac,19),Ac+=19):(r=s,0===xc&&Mc(Bi));return r}())===s&&((e=function(){var r;"sql_cache"===t.substr(Ac,9).toLowerCase()?(r=t.substr(Ac,9),Ac+=9):(r=s,0===xc&&Mc(Hi));return r}())===s&&(e=function(){var r;"sql_no_cache"===t.substr(Ac,12).toLowerCase()?(r=t.substr(Ac,12),Ac+=12):(r=s,0===xc&&Mc(Yi));return r}()),e===s&&(e=function(){var r;"sql_big_result"===t.substr(Ac,14).toLowerCase()?(r=t.substr(Ac,14),Ac+=14):(r=s,0===xc&&Mc(Xi));return r}())===s&&(e=function(){var r;"sql_small_result"===t.substr(Ac,16).toLowerCase()?(r=t.substr(Ac,16),Ac+=16):(r=s,0===xc&&Mc(Wi));return r}())===s&&(e=function(){var r;"sql_buffer_result"===t.substr(Ac,17).toLowerCase()?(r=t.substr(Ac,17),Ac+=17):(r=s,0===xc&&Mc(Zi));return r}())),e!==s&&(Tc=r,e=e),r=e}function Zl(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=ef())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function zl(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Xb())===s&&(r=Ac,(e=ey())!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=ey())),r!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ef())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){Wy.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?Gy(e,r):[e]}(0,e)):(Ac=t,t=s)}else Ac=t,t=s;return t===s&&(t=Zl()),t}function Kl(){var t,r;return t=Ac,sy()!==s&&fy()!==s?((r=eb())===s&&(r=zp()),r!==s&&fy()!==s&&uy()!==s?(Tc=t,t={brackets:!0,index:r}):(Ac=t,t=s)):(Ac=t,t=s),t}function Jl(){var t,r,e,n,o,u;if(t=Ac,(r=Kl())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Kl())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Kl())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e,1)):(Ac=t,t=s)}else Ac=t,t=s;return t}function tf(){var t,r,e,n,o;return t=Ac,(r=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Hf())!==s){for(e=[],n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);e!==s?(Tc=t,r=function(t,r){const e=t.ast;if(e&&"select"===e.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!r||0===r.length)return t;const n=r.length;let o=r[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?t:r[e-1][3];o=Py(r[e][1],n,o)}return o}(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s&&fy()!==s?((e=Jl())===s&&(e=null),e!==s?(Tc=t,n=r,(o=e)&&(n.array_index=o),t=r=n):(Ac=t,t=s)):(Ac=t,t=s),t}function rf(){var r,e,n,o;return r=Ac,"at"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(hn)),e!==s&&fy()!==s&&xv()!==s&&fy()!==s?("zone"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(wn)),n!==s&&fy()!==s?((o=dp())===s&&(o=ip()),o!==s?(Tc=r,r=e=[{type:"origin",value:"at time zone"},o]):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function ef(){var t,r,e,n,o,u,a,i,c,l,f;if(t=Ac,(r=ap())!==s&&(Tc=t,r=function(t){return{expr:t,as:null}}(r)),(t=r)===s){if(t=Ac,(r=ip())!==s)if((e=fy())!==s)if((n=Bp())!==s)if((o=fy())!==s){if(u=[],(a=rf())!==s)for(;a!==s;)u.push(a),a=rf();else u=s;u!==s&&(a=fy())!==s?((i=of())===s&&(i=null),i!==s?(Tc=t,t=r=function(t,r,e,n){return r.target[r.target.length-1].suffix=e.flat(),{...r,as:n,type:"cast",expr:t,suffix:e.flat()}}(r,n,u,i)):(Ac=t,t=s)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;if(t===s){if(t=Ac,(r=cp())===s&&(r=tf()),r!==s)if((e=fy())!==s)if((n=Bp())!==s)if((o=fy())!==s){for(u=[],a=Ac,(i=fy())!==s?((c=rp())===s&&(c=np()),c!==s&&(l=fy())!==s&&(f=tf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s)):(Ac=a,a=s);a!==s;)u.push(a),a=Ac,(i=fy())!==s?((c=rp())===s&&(c=np()),c!==s&&(l=fy())!==s&&(f=tf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s)):(Ac=a,a=s);if(u!==s)if((a=fy())!==s){for(i=[],c=rf();c!==s;)i.push(c),c=rf();i!==s&&(c=fy())!==s?((l=of())===s&&(l=null),l!==s?(Tc=t,t=r=function(t,r,e,n,o){return"column_ref"===t.type&&n.length&&(t.column.options={type:"expr_list",value:n.flat(),separator:" "}),{...r,as:o,type:"cast",expr:t,tail:e&&e[0]&&{operator:e[0][1],expr:e[0][3]}}}(r,n,u,i,l)):(Ac=t,t=s)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;t===s&&(t=Ac,(r=pp())!==s&&(e=fy())!==s&&(n=ty())!==s?(o=Ac,(u=fp())!==s&&(a=fy())!==s&&(i=ty())!==s?o=u=[u,a,i]:(Ac=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s&&(a=ey())!==s?(Tc=t,t=r=function(t,r){const e=r&&r[0];let n;e&&(n=t,t=e),Wy.add(`select::${t?t.value:null}::(.*)`);return{expr:{type:"column_ref",table:t,schema:n,column:"*"},as:null}}(r,o)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,r=Ac,(e=pp())!==s&&(n=fy())!==s&&(o=ty())!==s?r=e=[e,n,o]:(Ac=r,r=s),r===s&&(r=null),r!==s&&(e=fy())!==s&&(n=ey())!==s?(Tc=t,t=r=function(t){const r=t&&t[0]||null;return Wy.add(`select::${r?r.value:null}::(.*)`),{expr:{type:"column_ref",table:r,column:"*"},as:null}}(r)):(Ac=t,t=s),t===s&&(t=Ac,(r=tf())!==s&&(e=fy())!==s?((n=of())===s&&(n=null),n!==s?(Tc=t,t=r={type:"expr",expr:r,as:n}):(Ac=t,t=s)):(Ac=t,t=s))))}}return t}function nf(){var t,r,e;return t=Ac,(r=Nb())===s&&(r=null),r!==s&&fy()!==s&&(e=yp())!==s?(Tc=t,t=r=e):(Ac=t,t=s),t}function of(){var t,r,e;return t=Ac,(r=Nb())!==s&&fy()!==s&&(e=yp())!==s?(Tc=t,t=r=e):(Ac=t,t=s),t===s&&(t=Ac,(r=Nb())===s&&(r=null),r!==s&&fy()!==s&&(e=yp())!==s?(Tc=t,t=r=e):(Ac=t,t=s)),t}function sf(){var r,e,n;return r=Ac,_b()!==s&&fy()!==s&&(e=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Uy())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Uy())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Uy())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=r,r={keyword:"var",type:"into",expr:e}):(Ac=r,r=s),r===s&&(r=Ac,_b()!==s&&fy()!==s?("outfile"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(mn)),e===s&&("dumpfile"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Ln))),e===s&&(e=null),e!==s&&fy()!==s?((n=zp())===s&&(n=bp()),n!==s?(Tc=r,r={keyword:e,type:"into",expr:n}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)),r}function uf(){var t,r;return t=Ac,xb()!==s&&fy()!==s&&(r=pf())!==s?(Tc=t,t=r):(Ac=t,t=s),t}function af(){var t,r,e;return t=Ac,(r=df())!==s&&fy()!==s&&bb()!==s&&fy()!==s&&(e=df())!==s?(Tc=t,t=r=[r,e]):(Ac=t,t=s),t}function cf(){var r,e;return r=Ac,Qb()!==s&&fy()!==s?("btree"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Cn)),e===s&&("hash"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(On)),e===s&&("gist"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(jn)),e===s&&("gin"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(gn))))),e!==s?(Tc=r,r={keyword:"using",type:e.toLowerCase()}):(Ac=r,r=s)):(Ac=r,r=s),r}function lf(){var t,r,e,n,o,u;if(t=Ac,(r=ff())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ff())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ff())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function ff(){var r,e,n,o,u,a;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"key_block_size"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(Vr));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="KEY_BLOCK_SIZE"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?((n=$v())===s&&(n=null),n!==s&&fy()!==s&&(o=eb())!==s?(Tc=r,u=n,a=o,r=e={type:e.toLowerCase(),symbol:u,expr:a}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=Ep())!==s&&fy()!==s&&(n=$v())!==s&&fy()!==s?((o=eb())===s&&(o=bp()),o!==s?(Tc=r,r=e=function(t,r,e){return{type:t.toLowerCase(),symbol:r,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=cf())===s&&(r=Ac,"with"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(En)),e!==s&&fy()!==s?("parser"===t.substr(Ac,6).toLowerCase()?(n=t.substr(Ac,6),Ac+=6):(n=s,0===xc&&Mc(An)),n!==s&&fy()!==s&&(o=Ep())!==s?(Tc=r,r=e={type:"with parser",expr:o}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"visible"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Tn)),e===s&&("invisible"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Sn))),e!==s&&(Tc=r,e=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(e)),(r=e)===s&&(r=vy())))),r}function pf(){var t,r,e,n;if(t=Ac,(r=vf())!==s){for(e=[],n=bf();n!==s;)e.push(n),n=bf();e!==s?(Tc=t,t=r=Un(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function bf(){var t,r,e;return t=Ac,fy()!==s&&(r=ry())!==s&&fy()!==s&&(e=vf())!==s?(Tc=t,t=e):(Ac=t,t=s),t===s&&(t=Ac,fy()!==s&&(r=function(){var t,r,e,n,o,u,a,i,c,l,f;if(t=Ac,(r=yf())!==s)if(fy()!==s)if((e=vf())!==s)if(fy()!==s)if((n=Qb())!==s)if(fy()!==s)if(ny()!==s)if(fy()!==s)if((o=fp())!==s){for(u=[],a=Ac,(i=fy())!==s&&(c=ry())!==s&&(l=fy())!==s&&(f=fp())!==s?a=i=[i,c,l,f]:(Ac=a,a=s);a!==s;)u.push(a),a=Ac,(i=fy())!==s&&(c=ry())!==s&&(l=fy())!==s&&(f=fp())!==s?a=i=[i,c,l,f]:(Ac=a,a=s);u!==s&&(a=fy())!==s&&(i=oy())!==s?(Tc=t,p=r,v=o,y=u,(b=e).join=p,b.using=Gy(v,y),t=r=b):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;var p,b,v,y;t===s&&(t=Ac,(r=yf())!==s&&fy()!==s&&(e=vf())!==s&&fy()!==s?((n=wf())===s&&(n=null),n!==s?(Tc=t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=yf())===s&&(r=Qc()),r!==s&&fy()!==s&&(e=ny())!==s&&fy()!==s?((n=Fc())===s&&(n=pf()),n!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=of())===s&&(o=null),o!==s&&(u=fy())!==s?((a=wf())===s&&(a=null),a!==s?(Tc=t,r=function(t,r,e,n){return Array.isArray(r)&&(r={type:"tables",expr:r}),r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,a),t=r):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)));return t}())!==s?(Tc=t,t=r):(Ac=t,t=s)),t}function vf(){var r,e,n,o,u,a,i,c,l,f,p,b;return r=Ac,(e=function(){var r;"dual"===t.substr(Ac,4).toLowerCase()?(r=t.substr(Ac,4),Ac+=4):(r=s,0===xc&&Mc(qi));return r}())!==s&&(Tc=r,e={type:"dual"}),(r=e)===s&&(r=Ac,(e=qf())!==s&&fy()!==s?((n=nf())===s&&(n=null),n!==s?(Tc=r,r=e={expr:{type:"values",values:e},as:n}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"lateral"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(_n)),e===s&&(e=null),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Fc())===s&&(o=qf()),o!==s&&fy()!==s&&(u=oy())!==s&&(a=fy())!==s?((i=nf())===s&&(i=null),i!==s?(Tc=r,r=e=function(t,r,e){return Array.isArray(r)&&(r={type:"values",values:r}),r.parentheses=!0,{prefix:t,expr:r,as:e}}(e,o,i)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"lateral"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(_n)),e===s&&(e=null),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=pf())!==s&&fy()!==s&&(u=oy())!==s&&(a=fy())!==s?((i=nf())===s&&(i=null),i!==s?(Tc=r,r=e=function(t,r,e){return{prefix:t,expr:r={type:"tables",expr:r,parentheses:!0},as:e}}(e,o,i)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"lateral"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(_n)),e===s&&(e=null),e!==s&&fy()!==s&&(n=Gp())!==s&&fy()!==s?((o=of())===s&&(o=null),o!==s?(Tc=r,r=e=function(t,r,e){return{prefix:t,type:"expr",expr:r,as:e}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=df())!==s&&fy()!==s?("tablesample"===t.substr(Ac,11).toLowerCase()?(n=t.substr(Ac,11),Ac+=11):(n=s,0===xc&&Mc(xn)),n!==s&&fy()!==s&&(o=Gp())!==s&&fy()!==s?(u=Ac,"repeatable"===t.substr(Ac,10).toLowerCase()?(a=t.substr(Ac,10),Ac+=10):(a=s,0===xc&&Mc(Ye)),a!==s&&(i=fy())!==s&&(c=ny())!==s&&(l=fy())!==s&&(f=eb())!==s&&(p=fy())!==s&&(b=oy())!==s?u=a=[a,i,c,l,f,p,b]:(Ac=u,u=s),u===s&&(u=null),u!==s&&(a=fy())!==s?((i=of())===s&&(i=null),i!==s?(Tc=r,r=e=function(t,r,e,n){return{...t,as:n,tablesample:{expr:r,repeatable:e&&e[4]}}}(e,o,u,i)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=df())!==s&&fy()!==s?((n=of())===s&&(n=null),n!==s?(Tc=r,r=e=function(t,r){return"var"===t.type?(t.as=r,t):{...t,as:r}}(e,n)):(Ac=r,r=s)):(Ac=r,r=s))))))),r}function yf(){var r,e,n,o;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"left"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Qu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(n=fy())!==s?((o=Db())===s&&(o=null),o!==s&&fy()!==s&&Pb()!==s?(Tc=r,r=e="LEFT JOIN"):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=function(){var r,e,n,o;r=Ac,"right"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Fu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(n=fy())!==s?((o=Db())===s&&(o=null),o!==s&&fy()!==s&&Pb()!==s?(Tc=r,r=e="RIGHT JOIN"):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=function(){var r,e,n,o;r=Ac,"full"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc($u));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(n=fy())!==s?((o=Db())===s&&(o=null),o!==s&&fy()!==s&&Pb()!==s?(Tc=r,r=e="FULL JOIN"):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"cross"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(In)),e!==s&&(n=fy())!==s&&(o=Pb())!==s?(Tc=r,r=e="CROSS JOIN"):(Ac=r,r=s),r===s&&(r=Ac,e=Ac,(n=function(){var r,e,n,o;r=Ac,"inner"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Bu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(o=fy())!==s?e=n=[n,o]:(Ac=e,e=s),e===s&&(e=null),e!==s&&(n=Pb())!==s?(Tc=r,r=e="INNER JOIN"):(Ac=r,r=s))))),r}function df(){var t,r,e,n,o,u,a,i,c;return t=Ac,(r=bp())!==s?(e=Ac,(n=fy())!==s&&(o=ty())!==s&&(u=fy())!==s?((a=bp())===s&&(a=ey()),a!==s?e=n=[n,o,u,a]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=null),e!==s?(n=Ac,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s?((i=bp())===s&&(i=ey()),i!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=t,t=r=function(t,r,e){const n={db:null,table:t};return null!==e?(n.db=t,n.schema=r[3],n.table=e[3],n):(null!==r&&(n.db=t,n.table=r[3]),n)}(r,e,n)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=Uy())!==s&&(Tc=t,(c=r).db=null,c.table=c.name,r=c),t=r),t}function hf(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Hf())!==s){for(e=[],n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=Py(r[t][1],n,r[t][3]);return n}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function wf(){var t,r;return t=Ac,qb()!==s&&fy()!==s&&(r=Yf())!==s?(Tc=t,t=r):(Ac=t,t=s),t}function mf(){var r,e;return r=Ac,function(){var r,e,n,o;r=Ac,"where"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Ju));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s&&(e=Yf())!==s?(Tc=r,r=e):(Ac=r,r=s),r}function Lf(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=ip())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ip())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=ip())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Cf(){var t,r,e;return t=Ac,(r=Ep())!==s&&fy()!==s&&Nb()!==s&&fy()!==s&&(e=Of())!==s?(Tc=t,t=r={name:r,as_window_specification:e}):(Ac=t,t=s),t}function Of(){var t,r;return(t=Ep())===s&&(t=Ac,ny()!==s&&fy()!==s?((r=function(){var t,r,e,n;t=Ac,(r=Tf())===s&&(r=null);r!==s&&fy()!==s?((e=Sf())===s&&(e=null),e!==s&&fy()!==s?((n=function(){var t,r,e,n,o;t=Ac,(r=_v())!==s&&fy()!==s?((e=jf())===s&&(e=gf()),e!==s?(Tc=t,t=r={type:"rows",expr:e}):(Ac=t,t=s)):(Ac=t,t=s);t===s&&(t=Ac,(r=_v())!==s&&fy()!==s&&(e=zb())!==s&&fy()!==s&&(n=gf())!==s&&fy()!==s&&ov()!==s&&fy()!==s&&(o=jf())!==s?(Tc=t,r=Py(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),t=r):(Ac=t,t=s));return t}())===s&&(n=null),n!==s?(Tc=t,t=r={name:null,partitionby:r,orderby:e,window_frame_clause:n}):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s);return t}())===s&&(r=null),r!==s&&fy()!==s&&oy()!==s?(Tc=t,t={window_specification:r||{},parentheses:!0}):(Ac=t,t=s)):(Ac=t,t=s)),t}function jf(){var r,e,n,o;return r=Ac,(e=Af())!==s&&fy()!==s?("following"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(Nn)),n!==s?(Tc=r,(o=e).value+=" FOLLOWING",r=e=o):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ef()),r}function gf(){var r,e,n,o,u;return r=Ac,(e=Af())!==s&&fy()!==s?("preceding"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(Rn)),n===s&&("following"===t.substr(Ac,9).toLowerCase()?(n=t.substr(Ac,9),Ac+=9):(n=s,0===xc&&Mc(Nn))),n!==s?(Tc=r,u=n,(o=e).value+=" "+u.toUpperCase(),r=e=o):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ef()),r}function Ef(){var r,e,n;return r=Ac,"current"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ct)),e!==s&&fy()!==s?("row"===t.substr(Ac,3).toLowerCase()?(n=t.substr(Ac,3),Ac+=3):(n=s,0===xc&&Mc(_r)),n!==s?(Tc=r,r=e={type:"origin",value:"current row"}):(Ac=r,r=s)):(Ac=r,r=s),r}function Af(){var r,e;return r=Ac,"unbounded"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(kn)),e!==s&&(Tc=r,e={type:"origin",value:e.toUpperCase()}),(r=e)===s&&(r=eb()),r}function Tf(){var t,r,e;return t=Ac,Ub()!==s&&fy()!==s&&Bb()!==s&&fy()!==s?((r=Lf())===s&&(r=Gp()),r!==s?(Tc=t,e=r,t=Array.isArray(e)?e.map(t=>({type:"expr",expr:t})):[{type:"expr",expr:e}]):(Ac=t,t=s)):(Ac=t,t=s),t}function Sf(){var t,r;return t=Ac,Hb()!==s&&fy()!==s&&Bb()!==s&&fy()!==s&&(r=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Uf())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Uf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Uf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=t,t=r):(Ac=t,t=s),t}function Uf(){var r,e,n,o,u,a,i;return r=Ac,(e=Hf())!==s&&fy()!==s?((n=Wb())===s&&(n=Yb()),n===s&&(n=null),n!==s&&fy()!==s?(o=Ac,"nulls"===t.substr(Ac,5).toLowerCase()?(u=t.substr(Ac,5),Ac+=5):(u=s,0===xc&&Mc(Et)),u!==s&&(a=fy())!==s?("first"===t.substr(Ac,5).toLowerCase()?(i=t.substr(Ac,5),Ac+=5):(i=s,0===xc&&Mc(At)),i===s&&("last"===t.substr(Ac,4).toLowerCase()?(i=t.substr(Ac,4),Ac+=4):(i=s,0===xc&&Mc(Tt))),i===s&&(i=null),i!==s?o=u=[u,a,i]:(Ac=o,o=s)):(Ac=o,o=s),o===s&&(o=null),o!==s?(Tc=r,r=e=function(t,r,e){const n={expr:t,type:r};return n.nulls=e&&[e[0],e[2]].filter(t=>t).join(" "),n}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r}function _f(){var t;return(t=eb())===s&&(t=Uy())===s&&(t=Up()),t}function xf(){var r,e,n,o,u,a,i;return r=Ac,e=Ac,(n=function(){var r,e,n,o;r=Ac,"limit"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(oa));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(o=fy())!==s?((u=_f())===s&&(u=Xb())===s&&(u=Ql()),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=null),e!==s&&(n=fy())!==s?(o=Ac,(u=function(){var r,e,n,o;r=Ac,"offset"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(sa));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="OFFSET"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(a=fy())!==s&&(i=_f())!==s?o=u=[u,a,i]:(Ac=o,o=s),o===s&&(o=null),o!==s?(Tc=r,r=e=function(t,r){const e=[];return t&&e.push("string"==typeof t[2]?{type:"origin",value:"all"}:t[2]),r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,o)):(Ac=r,r=s)):(Ac=r,r=s),r}function If(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Nf())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Nf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Nf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Nf(){var r,e,n,o,u;return r=Ac,(e=op())!==s&&fy()!==s?(61===t.charCodeAt(Ac)?(n="=",Ac++):(n=s,0===xc&&Mc(it)),n!==s&&fy()!==s&&(o=tp())!==s?(Tc=r,r=e=function(t,r){return{...t,value:r}}(e,o)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=op())!==s&&fy()!==s?(61===t.charCodeAt(Ac)?(n="=",Ac++):(n=s,0===xc&&Mc(it)),n!==s&&fy()!==s&&(o=Gb())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(u=ip())!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={...c,value:u,keyword:"values"}):(Ac=r,r=s)):(Ac=r,r=s)),r}function Rf(){var r,e,n;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"returning"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Nu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="RETURNING"):(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&fy()!==s?((n=zl())===s&&(n=Fl()),n!==s?(Tc=r,r=e=function(t,r){return{type:t&&t.toLowerCase()||"returning",columns:"*"===r&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||r}}(e,n)):(Ac=r,r=s)):(Ac=r,r=s),r}function kf(){var t;return(t=qf())===s&&(t=Wl()),t}function Mf(){var t,r,e,n,o,u,a,i,c;if(t=Ac,Ub()!==s)if(fy()!==s)if((r=ny())!==s)if(fy()!==s)if((e=Ep())!==s){for(n=[],o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Ep())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);o!==s;)n.push(o),o=Ac,(u=fy())!==s&&(a=ry())!==s&&(i=fy())!==s&&(c=Ep())!==s?o=u=[u,a,i,c]:(Ac=o,o=s);n!==s&&(o=fy())!==s&&(u=oy())!==s?(Tc=t,t=Gy(e,n)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;return t===s&&(t=Ac,Ub()!==s&&fy()!==s&&(r=Pf())!==s?(Tc=t,t=r):(Ac=t,t=s)),t}function Vf(){var t,r;return t=Ac,(r=gb())!==s&&(Tc=t,r="insert"),(t=r)===s&&(t=Ac,(r=Ab())!==s&&(Tc=t,r="replace"),t=r),t}function qf(){var t,r;return t=Ac,Gb()!==s&&fy()!==s&&(r=function(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Pf())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Pf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Pf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,r=Gy(r,e),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}())!==s?(Tc=t,t=r):(Ac=t,t=s),t}function Pf(){var t,r;return t=Ac,ny()!==s&&fy()!==s&&(r=Df())!==s&&fy()!==s&&oy()!==s?(Tc=t,t=r):(Ac=t,t=s),t}function Df(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Hf())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){const e={type:"expr_list"};return e.value=Gy(t,r),e}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Gf(){var t,r,e;return t=Ac,Rv()!==s&&fy()!==s&&(r=Hf())!==s&&fy()!==s&&(e=dy())!==s?(Tc=t,t={type:"interval",expr:r,unit:e.toLowerCase()}):(Ac=t,t=s),t===s&&(t=Ac,Rv()!==s&&fy()!==s&&(r=zp())!==s?(Tc=t,t=function(t){return{type:"interval",expr:t,unit:""}}(r)):(Ac=t,t=s)),t}function Qf(){var t,r,e,n,o,u;if(t=Ac,(r=Ff())!==s)if(fy()!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=Ff())!==s?n=o=[o,u]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=Ff())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e,1)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;return t}function Ff(){var r,e,n;return r=Ac,cv()!==s&&fy()!==s&&(e=Yf())!==s&&fy()!==s&&function(){var r,e,n,o;r=Ac,"then"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Te));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}()!==s&&fy()!==s&&(n=Hf())!==s?(Tc=r,r={type:"when",cond:e,result:n}):(Ac=r,r=s),r}function $f(){var t,r;return t=Ac,lv()!==s&&fy()!==s&&(r=Hf())!==s?(Tc=t,t={type:"else",result:r}):(Ac=t,t=s),t}function Bf(){var t;return(t=Wf())===s&&(t=function(){var t,r,e,n,o,u;if(t=Ac,(r=rp())!==s){if(e=[],n=Ac,(o=fy())!==s&&(u=sp())!==s?n=o=[o,u]:(Ac=n,n=s),n!==s)for(;n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=sp())!==s?n=o=[o,u]:(Ac=n,n=s);else e=s;e!==s?(Tc=t,r=qy(r,e[0][1]),t=r):(Ac=t,t=s)}else Ac=t,t=s;return t}()),t}function Hf(){var t;return(t=Bf())===s&&(t=Fc()),t}function Yf(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Hf())!==s){for(e=[],n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ry()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s?((u=ov())===s&&(u=sv())===s&&(u=ry()),u!==s&&(a=fy())!==s&&(i=Hf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=Py(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Wf(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Xf())!==s){for(e=[],n=Ac,(o=py())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Xf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=py())!==s&&(u=sv())!==s&&(a=fy())!==s&&(i=Xf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Pn(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Xf(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Zf())!==s){for(e=[],n=Ac,(o=py())!==s&&(u=ov())!==s&&(a=fy())!==s&&(i=Zf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=py())!==s&&(u=ov())!==s&&(a=fy())!==s&&(i=Zf())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Pn(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Zf(){var r,e,n,o,u;return(r=zf())===s&&(r=function(){var t,r,e;t=Ac,(r=function(){var t,r,e,n,o;t=Ac,r=Ac,(e=nv())!==s&&(n=fy())!==s&&(o=ev())!==s?r=e=[e,n,o]:(Ac=r,r=s);r!==s&&(Tc=t,r=(u=r)[0]+" "+u[2]);var u;(t=r)===s&&(t=ev());return t}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Fc())!==s&&fy()!==s&&oy()!==s?(Tc=t,n=r,(o=e).parentheses=!0,r=qy(n,o),t=r):(Ac=t,t=s);var n,o;return t}())===s&&(r=Ac,(e=nv())===s&&(e=Ac,33===t.charCodeAt(Ac)?(n="!",Ac++):(n=s,0===xc&&Mc(Dn)),n!==s?(o=Ac,xc++,61===t.charCodeAt(Ac)?(u="=",Ac++):(u=s,0===xc&&Mc(it)),xc--,u===s?o=void 0:(Ac=o,o=s),o!==s?e=n=[n,o]:(Ac=e,e=s)):(Ac=e,e=s)),e!==s&&(n=fy())!==s&&(o=Zf())!==s?(Tc=r,r=e=qy("NOT",o)):(Ac=r,r=s)),r}function zf(){var r,e,n,o,u;return r=Ac,(e=tp())!==s&&fy()!==s?((n=function(){var r;(r=function(){var t,r,e,n,o,u,a;t=Ac,r=[],e=Ac,(n=fy())!==s&&(o=Kf())!==s&&(u=fy())!==s&&(a=tp())!==s?e=n=[n,o,u,a]:(Ac=e,e=s);if(e!==s)for(;e!==s;)r.push(e),e=Ac,(n=fy())!==s&&(o=Kf())!==s&&(u=fy())!==s&&(a=tp())!==s?e=n=[n,o,u,a]:(Ac=e,e=s);else r=s;r!==s&&(Tc=t,r={type:"arithmetic",tail:r});return t=r}())===s&&(r=function(){var t,r,e,n;t=Ac,(r=Jf())!==s&&fy()!==s&&(e=ny())!==s&&fy()!==s&&(n=Df())!==s&&fy()!==s&&oy()!==s?(Tc=t,t=r={op:r,right:n}):(Ac=t,t=s);t===s&&(t=Ac,(r=Jf())!==s&&fy()!==s?((e=Uy())===s&&(e=zp())===s&&(e=Gp()),e!==s?(Tc=t,r=function(t,r){return{op:t,right:r}}(r,e),t=r):(Ac=t,t=s)):(Ac=t,t=s));return t}())===s&&(r=function(){var t,r,e,n;t=Ac,(r=function(){var t,r,e,n,o;t=Ac,r=Ac,(e=nv())!==s&&(n=fy())!==s&&(o=zb())!==s?r=e=[e,n,o]:(Ac=r,r=s);r!==s&&(Tc=t,r=(u=r)[0]+" "+u[2]);var u;(t=r)===s&&(t=zb());return t}())!==s&&fy()!==s&&(e=tp())!==s&&fy()!==s&&ov()!==s&&fy()!==s&&(n=tp())!==s?(Tc=t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(Ac=t,t=s);return t}())===s&&(r=function(){var t,r,e,n,o,u,a,i,c;t=Ac,(r=Jb())!==s&&(e=fy())!==s&&(n=tp())!==s?(Tc=t,t=r={op:"IS",right:n}):(Ac=t,t=s);t===s&&(t=Ac,(r=Jb())!==s&&(e=fy())!==s?(n=Ac,(o=Zb())!==s&&(u=fy())!==s&&(a=xb())!==s&&(i=fy())!==s&&(c=df())!==s?n=o=[o,u,a,i,c]:(Ac=n,n=s),n!==s?(Tc=t,r=function(t){const{db:r,table:e}=t.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"default",value:"DISTINCT FROM "+(r?`"${r}".${n}`:n)}}}(n),t=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,r=Ac,(e=Jb())!==s&&(n=fy())!==s&&(o=nv())!==s?r=e=[e,n,o]:(Ac=r,r=s),r!==s&&(e=fy())!==s&&(n=tp())!==s?(Tc=t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(Ac=t,t=s)));return t}())===s&&(r=function(){var r,e,n,o;r=Ac,(e=function(){var r,e,n,o,u;r=Ac,e=Ac,(n=nv())!==s&&(o=fy())!==s?((u=tv())===s&&(u=rv()),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s);e!==s&&(Tc=r,e=(a=e)[0]+" "+a[2]);var a;(r=e)===s&&(r=tv())===s&&(r=rv())===s&&(r=Ac,"similar"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Yn)),e!==s&&(n=fy())!==s&&(o=bb())!==s?(Tc=r,r=e="SIMILAR TO"):(Ac=r,r=s),r===s&&(r=Ac,(e=nv())!==s&&(n=fy())!==s?("similar"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(Yn)),o!==s&&(u=fy())!==s&&bb()!==s?(Tc=r,r=e="NOT SIMILAR TO"):(Ac=r,r=s)):(Ac=r,r=s)));return r}())!==s&&fy()!==s?((n=Hp())===s&&(n=zf()),n!==s&&fy()!==s?((o=function(){var r,e,n;r=Ac,"escape"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Kn));e!==s&&fy()!==s&&(n=zp())!==s?(Tc=r,e=function(t,r){return{type:"ESCAPE",value:r}}(0,n),r=e):(Ac=r,r=s);return r}())===s&&(o=null),o!==s?(Tc=r,u=e,a=n,(i=o)&&(a.escape=i),r=e={op:u,right:a}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s);var u,a,i;return r}())===s&&(r=function(){var r,e,n;r=Ac,(e=function(){var r;"!~*"===t.substr(Ac,3)?(r="!~*",Ac+=3):(r=s,0===xc&&Mc(Wn));r===s&&("~*"===t.substr(Ac,2)?(r="~*",Ac+=2):(r=s,0===xc&&Mc(Xn)),r===s&&(126===t.charCodeAt(Ac)?(r="~",Ac++):(r=s,0===xc&&Mc(Zn)),r===s&&("!~"===t.substr(Ac,2)?(r="!~",Ac+=2):(r=s,0===xc&&Mc(zn)))));return r}())!==s&&fy()!==s?((n=Hp())===s&&(n=zf()),n!==s?(Tc=r,r=e={op:e,right:n}):(Ac=r,r=s)):(Ac=r,r=s);return r}());return r}())===s&&(n=null),n!==s?(Tc=r,o=e,r=e=null===(u=n)?o:"arithmetic"===u.type?Qy(o,u.tail):Py(u.op,o,u.right)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=zp())===s&&(r=ip()),r}function Kf(){var r;return">="===t.substr(Ac,2)?(r=">=",Ac+=2):(r=s,0===xc&&Mc(Gn)),r===s&&(62===t.charCodeAt(Ac)?(r=">",Ac++):(r=s,0===xc&&Mc(Qn)),r===s&&("<="===t.substr(Ac,2)?(r="<=",Ac+=2):(r=s,0===xc&&Mc(Fn)),r===s&&("<>"===t.substr(Ac,2)?(r="<>",Ac+=2):(r=s,0===xc&&Mc($n)),r===s&&(60===t.charCodeAt(Ac)?(r="<",Ac++):(r=s,0===xc&&Mc(Bn)),r===s&&(61===t.charCodeAt(Ac)?(r="=",Ac++):(r=s,0===xc&&Mc(it)),r===s&&("!="===t.substr(Ac,2)?(r="!=",Ac+=2):(r=s,0===xc&&Mc(Hn)))))))),r}function Jf(){var t,r,e,n,o,u;return t=Ac,r=Ac,(e=nv())!==s&&(n=fy())!==s&&(o=Kb())!==s?r=e=[e,n,o]:(Ac=r,r=s),r!==s&&(Tc=t,r=(u=r)[0]+" "+u[2]),(t=r)===s&&(t=Kb()),t}function tp(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=ep())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=rp())!==s&&(a=fy())!==s&&(i=ep())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=rp())!==s&&(a=fy())!==s&&(i=ep())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=function(t,r){if(r&&r.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...Vy()}));return Qy(t,r)}(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function rp(){var r;return 43===t.charCodeAt(Ac)?(r="+",Ac++):(r=s,0===xc&&Mc(Jn)),r===s&&(45===t.charCodeAt(Ac)?(r="-",Ac++):(r=s,0===xc&&Mc(to))),r}function ep(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=up())!==s){for(e=[],n=Ac,(o=fy())!==s?((u=np())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=up())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s?((u=np())===s&&(u=ly()),u!==s&&(a=fy())!==s&&(i=up())!==s?n=o=[o,u,a,i]:(Ac=n,n=s)):(Ac=n,n=s);e!==s?(Tc=t,t=r=Qy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function np(){var r;return 42===t.charCodeAt(Ac)?(r="*",Ac++):(r=s,0===xc&&Mc(ro)),r===s&&(47===t.charCodeAt(Ac)?(r="/",Ac++):(r=s,0===xc&&Mc(eo)),r===s&&(37===t.charCodeAt(Ac)?(r="%",Ac++):(r=s,0===xc&&Mc(no)),r===s&&("||"===t.substr(Ac,2)?(r="||",Ac+=2):(r=s,0===xc&&Mc(oo))))),r}function op(){var t,r,e,n,o;if(t=Ac,(r=ip())!==s)if(fy()!==s)if((e=Jl())===s&&(e=null),e!==s)if(fy()!==s){for(n=[],o=rf();o!==s;)n.push(o),o=rf();n!==s?(Tc=t,t=r=function(t,r,e){return r&&(t.array_index=r),e.length&&(t.options={type:"expr_list",value:e.flat(),separator:" "}),t}(r,e,n)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;return t}function sp(){var r,e,n,o,u,a;return(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&(u=Nb())!==s&&fy()!==s&&(a=xy())!==s&&fy()!==s&&(i=oy())!==s?(Tc=r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[e]}}(e,o,a),r=e):(Ac=r,r=s);r===s&&(r=Ac,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&(u=Nb())!==s&&fy()!==s&&(a=vv())!==s&&fy()!==s&&(i=ny())!==s&&fy()!==s&&(c=sb())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(l=oy())!==s?(Tc=r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,o,c),r=e):(Ac=r,r=s),r===s&&(r=Ac,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&(u=Nb())!==s&&fy()!==s&&(a=vv())!==s&&fy()!==s&&(i=ny())!==s&&fy()!==s&&(c=sb())!==s&&fy()!==s&&ry()!==s&&fy()!==s&&(l=sb())!==s&&fy()!==s&&oy()!==s&&fy()!==s&&oy()!==s?(Tc=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,o,c,l),r=e):(Ac=r,r=s),r===s&&(r=Ac,(e=pv())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&(u=Nb())!==s&&fy()!==s&&(a=function(){var r;(r=function(){var r,e,n,o;r=Ac,"signed"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Va));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SIGNED"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=yv());return r}())!==s&&fy()!==s?((i=hv())===s&&(i=null),i!==s&&fy()!==s&&(c=oy())!==s?(Tc=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,o,a,i),r=e):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=ny())!==s&&fy()!==s?((n=Wf())===s&&(n=op())===s&&(n=Up()),n!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?((u=Bp())===s&&(u=null),u!==s?(Tc=r,e=function(t,r){return t.parentheses=!0,r?{...r,type:"cast",keyword:"cast",expr:t}:t}(n,u),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=cp())===s&&(e=Hp())===s&&(e=function(){var r,e,n;r=Ac,(e=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=function(){var r,e,n,o;r=Ac,"count"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ma));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="COUNT"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(e=function(){var r,e,n,o;r=Ac,"group_concat"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(La));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="GROUP_CONCAT"):(Ac=r,r=s)):(Ac=r,r=s);return r}());e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=function(){var r,e;r=Ac,(e=function(){var r,e;r=Ac,42===t.charCodeAt(Ac)?(e="*",Ac++):(e=s,0===xc&&Mc(ro));e!==s&&(Tc=r,e={type:"star",value:"*"});return r=e}())!==s&&(Tc=r,e={expr:e});(r=e)===s&&(r=Rp());return r}())!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?((u=xp())===s&&(u=null),u!==s?(Tc=r,r=e={type:"aggr_func",name:e,args:n,over:u}):(Ac=r,r=s)):(Ac=r,r=s);r===s&&(r=Ac,"percentile_cont"===t.substr(Ac,15).toLowerCase()?(e=t.substr(Ac,15),Ac+=15):(e=s,0===xc&&Mc(Bo)),e===s&&("percentile_disc"===t.substr(Ac,15).toLowerCase()?(e=t.substr(Ac,15),Ac+=15):(e=s,0===xc&&Mc(Ho))),e!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=eb())===s&&(n=Yp()),n!==s&&fy()!==s&&(o=oy())!==s&&fy()!==s?("within"===t.substr(Ac,6).toLowerCase()?(u=t.substr(Ac,6),Ac+=6):(u=s,0===xc&&Mc(Yo)),u!==s&&fy()!==s&&$b()!==s&&fy()!==s&&(a=ny())!==s&&fy()!==s&&(i=Sf())!==s&&fy()!==s&&(c=oy())!==s&&fy()!==s?((l=xp())===s&&(l=null),l!==s?(Tc=r,e=function(t,r,e,n){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:r},within_group_orderby:e,over:n}}(e,n,i,l),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"mode"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Wo)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=oy())!==s&&fy()!==s?("within"===t.substr(Ac,6).toLowerCase()?(o=t.substr(Ac,6),Ac+=6):(o=s,0===xc&&Mc(Yo)),o!==s&&fy()!==s&&(u=$b())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(a=Sf())!==s&&fy()!==s&&(i=oy())!==s&&fy()!==s?((c=xp())===s&&(c=null),c!==s?(Tc=r,e=function(t,r,e){return{type:"aggr_func",name:t.toUpperCase(),args:{expr:{}},within_group_orderby:r,over:e}}(e,a,c),r=e):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)));return r}())===s&&(e=function(){var r,e,n,o;r=Ac,(e=function(){var r;(r=function(){var r,e,n,o;r=Ac,"sum"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ja));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SUM"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o;r=Ac,"max"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Ca));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MAX"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o;r=Ac,"min"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Oa));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MIN"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o;r=Ac,"avg"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ga));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="AVG"):(Ac=r,r=s)):(Ac=r,r=s);return r}());return r}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=tp())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=xp())===s&&(o=null),o!==s?(Tc=r,e={type:"aggr_func",name:e,args:{expr:n},over:o,...Vy()},r=e):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(e=function(){var r,e,n,o,u,a;r=Ac,e=Ac,(n=bp())!==s&&(o=fy())!==s&&(u=ty())!==s?e=n=[n,o,u]:(Ac=e,e=s);e===s&&(e=null);e!==s&&(n=fy())!==s?((o=function(){var r,e,n,o;r=Ac,"array_agg"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(ha));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ARRAY_AGG"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(o=function(){var r,e,n,o;r=Ac,"string_agg"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(wa));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="STRING_AGG"):(Ac=r,r=s)):(Ac=r,r=s);return r}()),o!==s&&(u=fy())!==s&&ny()!==s&&fy()!==s&&(a=Rp())!==s&&fy()!==s&&oy()!==s?(Tc=r,c=o,l=a,e={type:"aggr_func",name:(i=e)?`${i[0]}.${c}`:c,args:l},r=e):(Ac=r,r=s)):(Ac=r,r=s);var i,c,l;return r}());e!==s&&fy()!==s?((n=function(){var r,e,n;r=Ac,"filter"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ro));e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=mf())!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={keyword:"filter",parentheses:!0,where:n}):(Ac=r,r=s);return r}())===s&&(n=null),n!==s?(Tc=r,o=e,(u=n)&&(o.filter=u),r=e=o):(Ac=r,r=s)):(Ac=r,r=s);var o,u;return r}())===s&&(e=function(){var r;(r=function(){var r,e,n;r=Ac,(e=function(){var r;"row_number"===t.substr(Ac,10).toLowerCase()?(r=t.substr(Ac,10),Ac+=10):(r=s,0===xc&&Mc(Vo));r===s&&("dense_rank"===t.substr(Ac,10).toLowerCase()?(r=t.substr(Ac,10),Ac+=10):(r=s,0===xc&&Mc(qo)),r===s&&("rank"===t.substr(Ac,4).toLowerCase()?(r=t.substr(Ac,4),Ac+=4):(r=s,0===xc&&Mc(Po))));return r}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&oy()!==s&&fy()!==s&&(n=xp())!==s?(Tc=r,r=e={type:"window_func",name:e,over:n}):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u;r=Ac,(e=function(){var r;"lag"===t.substr(Ac,3).toLowerCase()?(r=t.substr(Ac,3),Ac+=3):(r=s,0===xc&&Mc(Do));r===s&&("lead"===t.substr(Ac,4).toLowerCase()?(r=t.substr(Ac,4),Ac+=4):(r=s,0===xc&&Mc(Go)),r===s&&("nth_value"===t.substr(Ac,9).toLowerCase()?(r=t.substr(Ac,9),Ac+=9):(r=s,0===xc&&Mc(Qo))));return r}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Df())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=Ip())===s&&(o=null),o!==s&&fy()!==s&&(u=xp())!==s?(Tc=r,r=e={type:"window_func",name:e,args:n,over:u,consider_nulls:o}):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o,u;r=Ac,(e=function(){var r;"first_value"===t.substr(Ac,11).toLowerCase()?(r=t.substr(Ac,11),Ac+=11):(r=s,0===xc&&Mc(ko));r===s&&("last_value"===t.substr(Ac,10).toLowerCase()?(r=t.substr(Ac,10),Ac+=10):(r=s,0===xc&&Mc(Mo)));return r}())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Hf())!==s&&fy()!==s&&oy()!==s&&fy()!==s?((o=Ip())===s&&(o=null),o!==s&&fy()!==s&&(u=xp())!==s?(Tc=r,r=e={type:"window_func",name:e,args:{type:"expr_list",value:[n]},over:u,consider_nulls:o}):(Ac=r,r=s)):(Ac=r,r=s);return r}());return r}())===s&&(e=Gp())===s&&(e=function(){var t,r,e,n,o,u,a,i;return t=Ac,iv()!==s&&fy()!==s&&(r=Qf())!==s&&fy()!==s?((e=$f())===s&&(e=null),e!==s&&fy()!==s&&(n=fv())!==s&&fy()!==s?((o=iv())===s&&(o=null),o!==s?(Tc=t,a=r,(i=e)&&a.push(i),t={type:"case",expr:null,args:a}):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,iv()!==s&&fy()!==s&&(r=Hf())!==s&&fy()!==s&&(e=Qf())!==s&&fy()!==s?((n=$f())===s&&(n=null),n!==s&&fy()!==s&&(o=fv())!==s&&fy()!==s?((u=iv())===s&&(u=null),u!==s?(Tc=t,t=function(t,r,e){return e&&r.push(e),{type:"case",expr:t,args:r}}(r,e,n)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)),t}())===s&&(e=Gf())===s&&(e=op())===s&&(e=Up()),e!==s&&fy()!==s?((n=Bp())===s&&(n=null),n!==s?(Tc=r,e=function(t,r){return r?{...r,type:"cast",keyword:"cast",expr:t}:t}(e,n),r=e):(Ac=r,r=s)):(Ac=r,r=s))))));return r}())===s&&(r=Ac,ny()!==s&&(e=fy())!==s&&(n=Yf())!==s&&(o=fy())!==s&&(u=oy())!==s?(Tc=r,(a=n).parentheses=!0,r=a):(Ac=r,r=s),r===s&&(r=Uy())===s&&(r=Ac,fy()!==s?(36===t.charCodeAt(Ac)?(e="$",Ac++):(e=s,0===xc&&Mc(so)),e!==s?(60===t.charCodeAt(Ac)?(n="<",Ac++):(n=s,0===xc&&Mc(Bn)),n!==s&&(o=eb())!==s?(62===t.charCodeAt(Ac)?(u=">",Ac++):(u=s,0===xc&&Mc(Qn)),u!==s?(Tc=r,r={type:"origin",value:`$<${o.value}>`}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s))),r}function up(){var r,e,n,o,u;return(r=function(){var r,e,n,o,u,a,i,c;if(r=Ac,(e=sp())!==s)if(fy()!==s){for(n=[],o=Ac,(u=fy())!==s?("?|"===t.substr(Ac,2)?(a="?|",Ac+=2):(a=s,0===xc&&Mc(uo)),a===s&&("?&"===t.substr(Ac,2)?(a="?&",Ac+=2):(a=s,0===xc&&Mc(ao)),a===s&&(63===t.charCodeAt(Ac)?(a="?",Ac++):(a=s,0===xc&&Mc(io)),a===s&&("#-"===t.substr(Ac,2)?(a="#-",Ac+=2):(a=s,0===xc&&Mc(co)),a===s&&("#>>"===t.substr(Ac,3)?(a="#>>",Ac+=3):(a=s,0===xc&&Mc(lo)),a===s&&("#>"===t.substr(Ac,2)?(a="#>",Ac+=2):(a=s,0===xc&&Mc(fo)),a===s&&(a=cy())===s&&(a=iy())===s&&("@>"===t.substr(Ac,2)?(a="@>",Ac+=2):(a=s,0===xc&&Mc(po)),a===s&&("<@"===t.substr(Ac,2)?(a="<@",Ac+=2):(a=s,0===xc&&Mc(bo))))))))),a!==s&&(i=fy())!==s&&(c=sp())!==s?o=u=[u,a,i,c]:(Ac=o,o=s)):(Ac=o,o=s);o!==s;)n.push(o),o=Ac,(u=fy())!==s?("?|"===t.substr(Ac,2)?(a="?|",Ac+=2):(a=s,0===xc&&Mc(uo)),a===s&&("?&"===t.substr(Ac,2)?(a="?&",Ac+=2):(a=s,0===xc&&Mc(ao)),a===s&&(63===t.charCodeAt(Ac)?(a="?",Ac++):(a=s,0===xc&&Mc(io)),a===s&&("#-"===t.substr(Ac,2)?(a="#-",Ac+=2):(a=s,0===xc&&Mc(co)),a===s&&("#>>"===t.substr(Ac,3)?(a="#>>",Ac+=3):(a=s,0===xc&&Mc(lo)),a===s&&("#>"===t.substr(Ac,2)?(a="#>",Ac+=2):(a=s,0===xc&&Mc(fo)),a===s&&(a=cy())===s&&(a=iy())===s&&("@>"===t.substr(Ac,2)?(a="@>",Ac+=2):(a=s,0===xc&&Mc(po)),a===s&&("<@"===t.substr(Ac,2)?(a="<@",Ac+=2):(a=s,0===xc&&Mc(bo))))))))),a!==s&&(i=fy())!==s&&(c=sp())!==s?o=u=[u,a,i,c]:(Ac=o,o=s)):(Ac=o,o=s);n!==s?(Tc=r,l=e,e=(f=n)&&0!==f.length?Qy(l,f):l,r=e):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;var l,f;return r}())===s&&(r=Ac,(e=function(){var r;33===t.charCodeAt(Ac)?(r="!",Ac++):(r=s,0===xc&&Mc(Dn));r===s&&(45===t.charCodeAt(Ac)?(r="-",Ac++):(r=s,0===xc&&Mc(to)),r===s&&(43===t.charCodeAt(Ac)?(r="+",Ac++):(r=s,0===xc&&Mc(Jn)),r===s&&(126===t.charCodeAt(Ac)?(r="~",Ac++):(r=s,0===xc&&Mc(Zn)))));return r}())!==s?(n=Ac,(o=fy())!==s&&(u=up())!==s?n=o=[o,u]:(Ac=n,n=s),n!==s?(Tc=r,r=e=qy(e,n[1])):(Ac=r,r=s)):(Ac=r,r=s)),r}function ap(){var r,e,n,o,u,a;if(r=Ac,"e"===t.substr(Ac,1).toLowerCase()?(e=t.charAt(Ac),Ac++):(e=s,0===xc&&Mc(vo)),e!==s)if(39===t.charCodeAt(Ac)?(n="'",Ac++):(n=s,0===xc&&Mc(Fr)),n!==s)if(fy()!==s){for(o=[],u=tb();u!==s;)o.push(u),u=tb();o!==s&&(u=fy())!==s?(39===t.charCodeAt(Ac)?(a="'",Ac++):(a=s,0===xc&&Mc(Fr)),a!==s?(Tc=r,r=e={type:"origin",value:`E'${o.join("")}'`}):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;return r}function ip(){var t,r,e,n,o,u,a,i,c,l,f,p,b;return(t=ap())===s&&(t=Ac,r=Ac,(e=bp())!==s&&(n=fy())!==s&&(o=ty())!==s?r=e=[e,n,o]:(Ac=r,r=s),r===s&&(r=null),r!==s&&(e=fy())!==s&&(n=ey())!==s?(Tc=t,t=r=function(t){const r=t&&t[0]||null;return Wy.add(`select::${r}::(.*)`),{type:"column_ref",table:r,column:"*"}}(r)):(Ac=t,t=s),t===s&&(t=Ac,(r=bp())!==s?(e=Ac,(n=fy())!==s&&(o=ty())!==s&&(u=fy())!==s&&(a=bp())!==s?e=n=[n,o,u,a]:(Ac=e,e=s),e!==s?(n=Ac,(o=fy())!==s&&(u=ty())!==s&&(a=fy())!==s&&(i=Cp())!==s?n=o=[o,u,a,i]:(Ac=n,n=s),n!==s?(o=Ac,(u=fy())!==s&&(a=ul())!==s?o=u=[u,a]:(Ac=o,o=s),o===s&&(o=null),o!==s?(Tc=t,l=r,f=e,p=n,b=o,Wy.add(`select::${l}.${f[3]}::${p[3].value}`),t=r={type:"column_ref",schema:l,table:f[3],column:{expr:p[3]},collate:b&&b[1]}):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=bp())!==s&&(e=fy())!==s&&(n=ty())!==s&&(o=fy())!==s&&(u=Cp())!==s?(a=Ac,(i=fy())!==s&&(c=ul())!==s?a=i=[i,c]:(Ac=a,a=s),a===s&&(a=null),a!==s?(Tc=t,t=r=function(t,r,e){return Wy.add(`select::${t}::${r.value}`),{type:"column_ref",table:t,column:{expr:r},collate:e&&e[1]}}(r,u,a)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=Op())!==s?(e=Ac,xc++,n=ny(),xc--,n===s?e=void 0:(Ac=e,e=s),e!==s?(n=Ac,(o=fy())!==s&&(u=ul())!==s?n=o=[o,u]:(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=t,t=r=function(t,r){return Wy.add("select::null::"+t.value),{type:"column_ref",table:null,column:{expr:t},collate:r&&r[1]}}(r,n)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s))))),t}function cp(){var t,r,e;return t=Ac,(r=Kp())!==s&&(Tc=t,e=r,Wy.add("select::null::"+e.value),r={type:"column_ref",table:null,column:{expr:e}}),t=r}function lp(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Op())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Op())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Op())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function fp(){var t,r;return t=Ac,(r=Ep())!==s&&(Tc=t,r=yo(r)),(t=r)===s&&(t=dp()),t}function pp(){var t,r;return t=Ac,(r=Ep())!==s?(Tc=Ac,(ho(r)?s:void 0)!==s?(Tc=t,t=r={type:"default",value:r}):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=dp()),t}function bp(){var t,r;return t=Ac,(r=Ep())!==s?(Tc=Ac,(ho(r)?s:void 0)!==s?(Tc=t,t=r=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=hp()),t}function vp(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=bp())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=bp())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=bp())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function yp(){var t,r,e,n,o,u,a,i,c;return t=Ac,(r=gp())!==s?(Tc=Ac,(!0===My[r.toUpperCase()]?s:void 0)!==s?(e=Ac,(n=fy())!==s&&(o=ny())!==s&&(u=fy())!==s&&(a=lp())!==s&&(i=fy())!==s&&(c=oy())!==s?e=n=[n,o,u,a,i,c]:(Ac=e,e=s),e===s&&(e=null),e!==s?(Tc=t,t=r=function(t,r){return r?`${t}(${r[3].map(t=>t.value).join(", ")})`:t}(r,e)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=wp())!==s&&(Tc=t,r=function(t){return t.value}(r)),t=r),t}function dp(){var t;return(t=wp())===s&&(t=mp())===s&&(t=Lp()),t}function hp(){var t,r;return t=Ac,(r=wp())===s&&(r=mp())===s&&(r=Lp()),r!==s&&(Tc=t,r=r.value),t=r}function wp(){var r,e,n,o;if(r=Ac,34===t.charCodeAt(Ac)?(e='"',Ac++):(e=s,0===xc&&Mc(wo)),e!==s){if(n=[],mo.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Lo)),o!==s)for(;o!==s;)n.push(o),mo.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Lo));else n=s;n!==s?(34===t.charCodeAt(Ac)?(o='"',Ac++):(o=s,0===xc&&Mc(wo)),o!==s?(Tc=r,r=e={type:"double_quote_string",value:n.join("")}):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;return r}function mp(){var r,e,n,o;if(r=Ac,39===t.charCodeAt(Ac)?(e="'",Ac++):(e=s,0===xc&&Mc(Fr)),e!==s){if(n=[],Co.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Oo)),o!==s)for(;o!==s;)n.push(o),Co.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Oo));else n=s;n!==s?(39===t.charCodeAt(Ac)?(o="'",Ac++):(o=s,0===xc&&Mc(Fr)),o!==s?(Tc=r,r=e={type:"single_quote_string",value:n.join("")}):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;return r}function Lp(){var r,e,n,o;if(r=Ac,96===t.charCodeAt(Ac)?(e="`",Ac++):(e=s,0===xc&&Mc(jo)),e!==s){if(n=[],go.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Eo)),o!==s)for(;o!==s;)n.push(o),go.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Eo));else n=s;n!==s?(96===t.charCodeAt(Ac)?(o="`",Ac++):(o=s,0===xc&&Mc(jo)),o!==s?(Tc=r,r=e={type:"backticks_quote_string",value:n.join("")}):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;return r}function Cp(){var t,r;return t=Ac,(r=gp())!==s&&(Tc=t,r=yo(r)),(t=r)===s&&(t=dp()),t}function Op(){var t,r;return t=Ac,(r=gp())!==s?(Tc=Ac,(ho(r)?s:void 0)!==s?(Tc=t,t=r={type:"default",value:r}):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=dp()),t}function jp(){var t,r;return t=Ac,(r=gp())!==s?(Tc=Ac,(ho(r)?s:void 0)!==s?(Tc=t,t=r=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=hp()),t}function gp(){var t,r,e,n;if(t=Ac,(r=Ap())!==s){for(e=[],n=Sp();n!==s;)e.push(n),n=Sp();e!==s?(Tc=t,t=r=r+e.join("")):(Ac=t,t=s)}else Ac=t,t=s;return t}function Ep(){var t,r,e,n;if(t=Ac,(r=Ap())!==s){for(e=[],n=Tp();n!==s;)e.push(n),n=Tp();e!==s?(Tc=t,t=r=r+e.join("")):(Ac=t,t=s)}else Ac=t,t=s;return t}function Ap(){var r;return Ao.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(To)),r}function Tp(){var r;return So.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(Uo)),r}function Sp(){var r;return _o.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(xo)),r}function Up(){var r,e,n,o;return r=Ac,e=Ac,58===t.charCodeAt(Ac)?(n=":",Ac++):(n=s,0===xc&&Mc(Io)),n!==s&&(o=Ep())!==s?e=n=[n,o]:(Ac=e,e=s),e!==s&&(Tc=r,e={type:"param",value:e[1]}),r=e}function _p(){var t,r,e;return t=Ac,qb()!==s&&fy()!==s&&wb()!==s&&fy()!==s&&(r=kv())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((e=Df())===s&&(e=null),e!==s&&fy()!==s&&oy()!==s?(Tc=t,t={type:"on update",keyword:r,parentheses:!0,expr:e}):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,qb()!==s&&fy()!==s&&wb()!==s&&fy()!==s&&(r=kv())!==s?(Tc=t,t=function(t){return{type:"on update",keyword:t}}(r)):(Ac=t,t=s)),t}function xp(){var r,e,n,o,u;return r=Ac,"over"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(No)),e!==s&&fy()!==s&&(n=Of())!==s?(Tc=r,r=e={type:"window",as_window_specification:n}):(Ac=r,r=s),r===s&&(r=Ac,"over"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(No)),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Tf())===s&&(o=null),o!==s&&fy()!==s?((u=Sf())===s&&(u=null),u!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={partitionby:o,orderby:u}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=_p())),r}function Ip(){var r,e,n;return r=Ac,"ignore"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Fo)),e===s&&("respect"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc($o))),e!==s&&fy()!==s?("nulls"===t.substr(Ac,5).toLowerCase()?(n=t.substr(Ac,5),Ac+=5):(n=s,0===xc&&Mc(Et)),n!==s?(Tc=r,r=e=e.toUpperCase()+" NULLS"):(Ac=r,r=s)):(Ac=r,r=s),r}function Np(){var t,r,e;return t=Ac,(r=ry())!==s&&fy()!==s&&(e=zp())!==s?(Tc=t,t=r={symbol:r,delimiter:e}):(Ac=t,t=s),t}function Rp(){var t,r,e,n,o,u,a,i,c,l,f;if(t=Ac,(r=Zb())===s&&(r=null),r!==s)if(fy()!==s)if((e=ny())!==s)if(fy()!==s)if((n=Hf())!==s)if(fy()!==s)if((o=oy())!==s)if(fy()!==s){for(u=[],a=Ac,(i=fy())!==s?((c=ov())===s&&(c=sv()),c!==s&&(l=fy())!==s&&(f=Hf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s)):(Ac=a,a=s);a!==s;)u.push(a),a=Ac,(i=fy())!==s?((c=ov())===s&&(c=sv()),c!==s&&(l=fy())!==s&&(f=Hf())!==s?a=i=[i,c,l,f]:(Ac=a,a=s)):(Ac=a,a=s);u!==s&&(a=fy())!==s?((i=Np())===s&&(i=null),i!==s&&(c=fy())!==s?((l=Sf())===s&&(l=null),l!==s?(Tc=t,t=r=function(t,r,e,n,o){const s=e.length;let u=r;u.parentheses=!0;for(let t=0;t<s;++t)u=Py(e[t][1],u,e[t][3]);return{distinct:t,expr:u,orderby:o,separator:n}}(r,n,u,i,l)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)}else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;else Ac=t,t=s;return t===s&&(t=Ac,(r=Zb())===s&&(r=null),r!==s&&fy()!==s&&(e=hf())!==s&&fy()!==s?((n=Np())===s&&(n=null),n!==s&&fy()!==s?((o=Sf())===s&&(o=null),o!==s?(Tc=t,t=r=function(t,r,e,n){return{distinct:t,expr:r,orderby:n,separator:e}}(r,e,n,o)):(Ac=t,t=s)):(Ac=t,t=s)):(Ac=t,t=s)),t}function kp(){var r,e,n;return r=Ac,(e=function(){var r;return"both"===t.substr(Ac,4).toLowerCase()?(r=t.substr(Ac,4),Ac+=4):(r=s,0===xc&&Mc(Xo)),r===s&&("leading"===t.substr(Ac,7).toLowerCase()?(r=t.substr(Ac,7),Ac+=7):(r=s,0===xc&&Mc(Zo)),r===s&&("trailing"===t.substr(Ac,8).toLowerCase()?(r=t.substr(Ac,8),Ac+=8):(r=s,0===xc&&Mc(zo)))),r}())===s&&(e=null),e!==s&&fy()!==s?((n=Hf())===s&&(n=null),n!==s&&fy()!==s&&xb()!==s?(Tc=r,r=e=function(t,r,e){let n=[];return t&&n.push({type:"origin",value:t}),r&&n.push(r),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Ac=r,r=s)):(Ac=r,r=s),r}function Mp(){var r,e,n,o;return r=Ac,"trim"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Ko)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s?((n=kp())===s&&(n=null),n!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e=function(t,r){let e=t||{type:"expr_list",value:[]};return e.value.push(r),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...Vy()}}(n,o)):(Ac=r,r=s)):(Ac=r,r=s),r}function Vp(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y;return r=Ac,"crosstab"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Jo)),e===s&&("jsonb_to_recordset"===t.substr(Ac,18).toLowerCase()?(e=t.substr(Ac,18),Ac+=18):(e=s,0===xc&&Mc(ts)),e===s&&("jsonb_to_record"===t.substr(Ac,15).toLowerCase()?(e=t.substr(Ac,15),Ac+=15):(e=s,0===xc&&Mc(rs)),e===s&&("json_to_recordset"===t.substr(Ac,17).toLowerCase()?(e=t.substr(Ac,17),Ac+=17):(e=s,0===xc&&Mc(es)),e===s&&("json_to_record"===t.substr(Ac,14).toLowerCase()?(e=t.substr(Ac,14),Ac+=14):(e=s,0===xc&&Mc(ns)))))),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Df())!==s&&fy()!==s&&oy()!==s&&fy()!==s?(o=Ac,(u=Nb())!==s&&(a=fy())!==s&&(i=Ep())!==s&&(c=fy())!==s&&(l=ny())!==s&&(f=fy())!==s&&(p=Xc())!==s&&(b=fy())!==s&&(v=oy())!==s?o=u=[u,a,i,c,l,f,p,b,v]:(Ac=o,o=s),o===s&&(o=null),o!==s?(Tc=r,r=e={type:"tablefunc",name:{name:[{type:"default",value:e}]},args:n,as:(y=o)&&{type:"function",name:{name:[{type:"default",value:y[2]}]},args:{type:"expr_list",value:y[6].map(t=>({...t,type:"column_definition"}))},...Vy()},...Vy()}):(Ac=r,r=s)):(Ac=r,r=s),r}function qp(){var r,e,n,o;return r=Ac,"years"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ss)),e===s&&("months"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(us)),e===s&&("weeks"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(as)),e===s&&("days"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(is)),e===s&&("hours"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(cs)),e===s&&("mins"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ls))))))),e!==s&&fy()!==s?("=>"===t.substr(Ac,2)?(n="=>",Ac+=2):(n=s,0===xc&&Mc(fs)),n!==s&&fy()!==s?((o=nb())===s&&(o=Hf()),o!==s?(Tc=r,r=e={type:"func_arg",value:{name:e,symbol:"=>",expr:o}}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"secs"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ps)),e!==s&&fy()!==s?("=>"===t.substr(Ac,2)?(n="=>",Ac+=2):(n=s,0===xc&&Mc(fs)),n!==s&&fy()!==s?((o=ob())===s&&(o=Hf()),o!==s?(Tc=r,r=e=function(t,r){return{type:"func_arg",value:{name:t,symbol:"=>",expr:r}}}(e,o)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)),r}function Pp(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=qp())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=qp())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=qp())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r={type:"expr_list",value:Gy(r,e)}):(Ac=t,t=s)}else Ac=t,t=s;return t===s&&(t=Df()),t}function Dp(){var r,e,n;return r=Ac,"make_interval"===t.substr(Ac,13).toLowerCase()?(e=t.substr(Ac,13),Ac+=13):(e=s,0===xc&&Mc(bs)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=Pp())!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n,...Vy()}):(Ac=r,r=s),r}function Gp(){var r,e,n,o,u,a,i,c,l,f;return(r=Mp())===s&&(r=Vp())===s&&(r=function(){var r,e,n,o,u,a,i,c,l,f,p;return r=Ac,"substring"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s&&(o=ry())!==s&&(u=fy())!==s&&(a=eb())!==s&&(i=fy())!==s&&(c=ry())!==s&&(l=fy())!==s&&(f=eb())!==s&&(p=fy())!==s&&oy()!==s?(Tc=r,r=e={type:"function",name:{name:[{type:"origin",value:"substring"}]},args:{type:"expr_list",value:[n,a,f]}}):(Ac=r,r=s),r===s&&(r=Ac,"substring"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s&&(o=xb())!==s&&(u=fy())!==s&&(a=dp())!==s&&(i=fy())!==s?(c=Ac,"for"===t.substr(Ac,3).toLowerCase()?(l=t.substr(Ac,3),Ac+=3):(l=s,0===xc&&Mc(_t)),l!==s&&(f=fy())!==s&&(p=dp())!==s?c=l=[l,f,p]:(Ac=c,c=s),c===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s?(Tc=r,r=e=function(t,r,e){const n=[{type:"origin",value:"from"}],o={type:"expr_list",value:[t,r]};return e&&(n.push({type:"origin",value:"for"}),o.value.push(e[2])),{type:"function",name:{name:[{type:"origin",value:"substring"}]},args:o,separator:n}}(n,a,c)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,"substring"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(os)),e!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(n=dp())!==s&&fy()!==s?(o=Ac,(u=xb())!==s&&(a=fy())!==s&&(i=eb())!==s?o=u=[u,a,i]:(Ac=o,o=s),o===s&&(o=null),o!==s&&(u=fy())!==s?(a=Ac,"for"===t.substr(Ac,3).toLowerCase()?(i=t.substr(Ac,3),Ac+=3):(i=s,0===xc&&Mc(_t)),i!==s&&(c=fy())!==s&&(l=eb())!==s?a=i=[i,c,l]:(Ac=a,a=s),a===s&&(a=null),a!==s&&(i=fy())!==s&&(c=oy())!==s?(Tc=r,r=e=function(t,r,e){const n=[],o={type:"expr_list",value:[t]};return r&&(n.push({type:"origin",value:"from"}),o.value.push(r[2])),e&&(n.push({type:"origin",value:"for"}),o.value.push(e[2])),{type:"function",name:{name:[{type:"origin",value:"substring"}]},args:o,separator:n}}(n,o,a)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s))),r}())===s&&(r=Dp())===s&&(r=Ac,"now"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(vs)),e!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Df())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s&&fy()!==s?("at"===t.substr(Ac,2).toLowerCase()?(u=t.substr(Ac,2),Ac+=2):(u=s,0===xc&&Mc(ys)),u!==s&&fy()!==s&&xv()!==s&&fy()!==s?("zone"===t.substr(Ac,4).toLowerCase()?(a=t.substr(Ac,4),Ac+=4):(a=s,0===xc&&Mc(ds)),a!==s&&fy()!==s&&(i=zp())!==s?(Tc=r,c=e,l=o,(f=i).prefix="at time zone",r=e={type:"function",name:{name:[{type:"default",value:c}]},args:l||{type:"expr_list",value:[]},suffix:f,...Vy()}):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=function(){var r;(r=Fp())===s&&(r=Mv())===s&&(r=function(){var r,e,n,o;r=Ac,"user"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(li));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="USER"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=Vv())===s&&(r=function(){var r,e,n,o;r=Ac,"system_user"===t.substr(Ac,11).toLowerCase()?(e=t.substr(Ac,11),Ac+=11):(e=s,0===xc&&Mc(Si));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SYSTEM_USER"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&("ntile"===t.substr(Ac,5).toLowerCase()?(r=t.substr(Ac,5),Ac+=5):(r=s,0===xc&&Mc(Ps)));return r}())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Df())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s&&fy()!==s?((u=xp())===s&&(u=null),u!==s?(Tc=r,r=e=function(t,r,e){return{type:"function",name:{name:[{type:"origin",value:t}]},args:r||{type:"expr_list",value:[]},over:e,...Vy()}}(e,o,u)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=function(){var t,r,e,n,o;t=Ac,(r=av())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Qp())!==s&&fy()!==s&&xb()!==s&&fy()!==s?((n=Iv())===s&&(n=Rv())===s&&(n=xv())===s&&(n=Sv()),n===s&&(n=null),n!==s&&fy()!==s&&(o=Hf())!==s&&fy()!==s&&oy()!==s?(Tc=t,u=e,a=n,i=o,r={type:r.toLowerCase(),args:{field:u,cast_type:a,source:i},...Vy()},t=r):(Ac=t,t=s)):(Ac=t,t=s);var u,a,i;t===s&&(t=Ac,(r=av())!==s&&fy()!==s&&ny()!==s&&fy()!==s&&(e=Qp())!==s&&fy()!==s&&xb()!==s&&fy()!==s&&(n=Hf())!==s&&fy()!==s&&(o=oy())!==s?(Tc=t,r=function(t,r,e){return{type:t.toLowerCase(),args:{field:r,source:e},...Vy()}}(r,e,n),t=r):(Ac=t,t=s));return t}())===s&&(r=Ac,(e=Fp())!==s&&fy()!==s?((n=rf())===s&&(n=null),n!==s&&fy()!==s?((o=_p())===s&&(o=null),o!==s?(Tc=r,r=e=function(t,r,e){const n={};r&&(n.args={type:"expr_list",value:r},n.args_parentheses=!1,n.separator=" ");return{type:"function",name:{name:[{type:"origin",value:t}]},over:e,...n,...Vy()}}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=Ay())!==s&&fy()!==s&&(n=ny())!==s&&fy()!==s?((o=Yf())===s&&(o=null),o!==s&&fy()!==s&&oy()!==s?(Tc=r,r=e=function(t,r){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},...Vy()}}(e,o)):(Ac=r,r=s)):(Ac=r,r=s))))),r}function Qp(){var r,e;return r=Ac,"century"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(hs)),e===s&&("day"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ws)),e===s&&("date"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ms)),e===s&&("decade"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ls)),e===s&&("dow"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Cs)),e===s&&("doy"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Os)),e===s&&("epoch"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(js)),e===s&&("hour"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(gs)),e===s&&("isodow"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Es)),e===s&&("isoyear"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(As)),e===s&&("microseconds"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Ts)),e===s&&("millennium"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Ss)),e===s&&("milliseconds"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Us)),e===s&&("minute"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(_s)),e===s&&("month"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(xs)),e===s&&("quarter"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Is)),e===s&&("second"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ns)),e===s&&("timezone"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Rs)),e===s&&("timezone_hour"===t.substr(Ac,13).toLowerCase()?(e=t.substr(Ac,13),Ac+=13):(e=s,0===xc&&Mc(ks)),e===s&&("timezone_minute"===t.substr(Ac,15).toLowerCase()?(e=t.substr(Ac,15),Ac+=15):(e=s,0===xc&&Mc(Ms)),e===s&&("week"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Vs)),e===s&&("year"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(qs))))))))))))))))))))))),e!==s&&(Tc=r,e=e),r=e}function Fp(){var r;return(r=function(){var r,e,n,o;r=Ac,"current_date"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(gi));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CURRENT_DATE"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e,n,o;r=Ac,"current_time"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Ai));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CURRENT_TIME"):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=kv()),r}function $p(){var r,e,n,o;return r=Ac,34===t.charCodeAt(Ac)?(e='"',Ac++):(e=s,0===xc&&Mc(wo)),e===s&&(e=null),e!==s&&(n=xy())!==s?(34===t.charCodeAt(Ac)?(o='"',Ac++):(o=s,0===xc&&Mc(wo)),o===s&&(o=null),o!==s?(Tc=r,r=e=function(t,r,e){if(t&&!e||!t&&e)throw new Error("double quoted not match");return t&&e&&(r.quoted='"'),r}(e,n,o)):(Ac=r,r=s)):(Ac=r,r=s),r}function Bp(){var t,r,e,n,o,u;if(t=Ac,r=[],e=Ac,(n=Fv())!==s&&(o=fy())!==s&&(u=$p())!==s?e=n=[n,o,u]:(Ac=e,e=s),e!==s)for(;e!==s;)r.push(e),e=Ac,(n=Fv())!==s&&(o=fy())!==s&&(u=$p())!==s?e=n=[n,o,u]:(Ac=e,e=s);else r=s;return r!==s&&(e=fy())!==s?((n=of())===s&&(n=null),n!==s?(Tc=t,t=r={as:n,symbol:"::",target:r.map(t=>t[2])}):(Ac=t,t=s)):(Ac=t,t=s),t}function Hp(){var r;return(r=zp())===s&&(r=eb())===s&&(r=Zp())===s&&(r=Wp())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,(e=xv())===s&&(e=Sv())===s&&(e=Iv())===s&&(e=Uv());if(e!==s)if(fy()!==s){if(n=Ac,39===t.charCodeAt(Ac)?(o="'",Ac++):(o=s,0===xc&&Mc(Fr)),o!==s){for(u=[],a=tb();a!==s;)u.push(a),a=tb();u!==s?(39===t.charCodeAt(Ac)?(a="'",Ac++):(a=s,0===xc&&Mc(Fr)),a!==s?n=o=[o,u,a]:(Ac=n,n=s)):(Ac=n,n=s)}else Ac=n,n=s;n!==s?(Tc=r,i=n,e={type:e.toLowerCase(),value:i[1].join("")},r=e):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;var i;if(r===s)if(r=Ac,(e=xv())===s&&(e=Sv())===s&&(e=Iv())===s&&(e=Uv()),e!==s)if(fy()!==s){if(n=Ac,34===t.charCodeAt(Ac)?(o='"',Ac++):(o=s,0===xc&&Mc(wo)),o!==s){for(u=[],a=Jp();a!==s;)u.push(a),a=Jp();u!==s?(34===t.charCodeAt(Ac)?(a='"',Ac++):(a=s,0===xc&&Mc(wo)),a!==s?n=o=[o,u,a]:(Ac=n,n=s)):(Ac=n,n=s)}else Ac=n,n=s;n!==s?(Tc=r,e=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}}(e,n),r=e):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;return r}())===s&&(r=Yp()),r}function Yp(){var t,r;return t=Ac,uv()!==s&&fy()!==s&&sy()!==s&&fy()!==s?((r=Df())===s&&(r=null),r!==s&&fy()!==s&&uy()!==s?(Tc=t,t=function(t,r){return{expr_list:r||{type:"origin",value:""},type:"array",keyword:"array",brackets:!0}}(0,r)):(Ac=t,t=s)):(Ac=t,t=s),t}function Wp(){var t,r;return t=Ac,(r=fb())!==s&&(Tc=t,r={type:"null",value:null}),t=r}function Xp(){var r,e;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"not null"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(yu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={type:"not null",value:"not null"}),r=e}function Zp(){var r,e;return r=Ac,(e=function(){var r,e,n,o;r=Ac,"true"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(du));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={type:"bool",value:!0}),(r=e)===s&&(r=Ac,(e=function(){var r,e,n,o;r=Ac,"false"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(hu));e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s);return r}())!==s&&(Tc=r,e={type:"bool",value:!1}),r=e),r}function zp(){var r,e,n,o,u,a,i,c,l;if(r=Ac,e=Ac,39===t.charCodeAt(Ac)?(n="'",Ac++):(n=s,0===xc&&Mc(Fr)),n!==s){for(o=[],u=tb();u!==s;)o.push(u),u=tb();o!==s?(39===t.charCodeAt(Ac)?(u="'",Ac++):(u=s,0===xc&&Mc(Fr)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s)}else Ac=e,e=s;if(e!==s){if(n=[],Ds.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Gs)),o!==s)for(;o!==s;)n.push(o),Ds.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(Gs));else n=s;if(n!==s)if((o=fy())!==s){if(u=Ac,39===t.charCodeAt(Ac)?(a="'",Ac++):(a=s,0===xc&&Mc(Fr)),a!==s){for(i=[],c=tb();c!==s;)i.push(c),c=tb();i!==s?(39===t.charCodeAt(Ac)?(c="'",Ac++):(c=s,0===xc&&Mc(Fr)),c!==s?u=a=[a,i,c]:(Ac=u,u=s)):(Ac=u,u=s)}else Ac=u,u=s;u!==s?(Tc=r,l=u,r=e={type:"single_quote_string",value:`${e[1].join("")}${l[1].join("")}`}):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s}else Ac=r,r=s;if(r===s){if(r=Ac,e=Ac,39===t.charCodeAt(Ac)?(n="'",Ac++):(n=s,0===xc&&Mc(Fr)),n!==s){for(o=[],u=tb();u!==s;)o.push(u),u=tb();o!==s?(39===t.charCodeAt(Ac)?(u="'",Ac++):(u=s,0===xc&&Mc(Fr)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s)}else Ac=e,e=s;e!==s&&(Tc=r,e=function(t){return{type:"single_quote_string",value:t[1].join("")}}(e)),(r=e)===s&&(r=Kp())}return r}function Kp(){var r,e,n,o,u;if(r=Ac,e=Ac,34===t.charCodeAt(Ac)?(n='"',Ac++):(n=s,0===xc&&Mc(wo)),n!==s){for(o=[],u=Jp();u!==s;)o.push(u),u=Jp();o!==s?(34===t.charCodeAt(Ac)?(u='"',Ac++):(u=s,0===xc&&Mc(wo)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s)}else Ac=e,e=s;return e!==s?(n=Ac,xc++,o=ty(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e={type:"double_quote_string",value:e[1].join("")}):(Ac=r,r=s)):(Ac=r,r=s),r}function Jp(){var r;return Qs.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(Fs)),r===s&&(r=rb()),r}function tb(){var r;return $s.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(Bs)),r===s&&(r=rb()),r}function rb(){var r,e,n,o,u,a,i,c,l,f;return r=Ac,"\\'"===t.substr(Ac,2)?(e="\\'",Ac+=2):(e=s,0===xc&&Mc(Hs)),e!==s&&(Tc=r,e="\\'"),(r=e)===s&&(r=Ac,'\\"'===t.substr(Ac,2)?(e='\\"',Ac+=2):(e=s,0===xc&&Mc(Ys)),e!==s&&(Tc=r,e='\\"'),(r=e)===s&&(r=Ac,"\\\\"===t.substr(Ac,2)?(e="\\\\",Ac+=2):(e=s,0===xc&&Mc(Ws)),e!==s&&(Tc=r,e="\\\\"),(r=e)===s&&(r=Ac,"\\/"===t.substr(Ac,2)?(e="\\/",Ac+=2):(e=s,0===xc&&Mc(Xs)),e!==s&&(Tc=r,e="\\/"),(r=e)===s&&(r=Ac,"\\b"===t.substr(Ac,2)?(e="\\b",Ac+=2):(e=s,0===xc&&Mc(Zs)),e!==s&&(Tc=r,e="\b"),(r=e)===s&&(r=Ac,"\\f"===t.substr(Ac,2)?(e="\\f",Ac+=2):(e=s,0===xc&&Mc(zs)),e!==s&&(Tc=r,e="\f"),(r=e)===s&&(r=Ac,"\\n"===t.substr(Ac,2)?(e="\\n",Ac+=2):(e=s,0===xc&&Mc(Ks)),e!==s&&(Tc=r,e="\n"),(r=e)===s&&(r=Ac,"\\r"===t.substr(Ac,2)?(e="\\r",Ac+=2):(e=s,0===xc&&Mc(Js)),e!==s&&(Tc=r,e="\r"),(r=e)===s&&(r=Ac,"\\t"===t.substr(Ac,2)?(e="\\t",Ac+=2):(e=s,0===xc&&Mc(tu)),e!==s&&(Tc=r,e="\t"),(r=e)===s&&(r=Ac,"\\u"===t.substr(Ac,2)?(e="\\u",Ac+=2):(e=s,0===xc&&Mc(ru)),e!==s&&(n=lb())!==s&&(o=lb())!==s&&(u=lb())!==s&&(a=lb())!==s?(Tc=r,i=n,c=o,l=u,f=a,r=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Ac=r,r=s),r===s&&(r=Ac,92===t.charCodeAt(Ac)?(e="\\",Ac++):(e=s,0===xc&&Mc(eu)),e!==s&&(Tc=r,e="\\"),(r=e)===s&&(r=Ac,"''"===t.substr(Ac,2)?(e="''",Ac+=2):(e=s,0===xc&&Mc(nu)),e!==s&&(Tc=r,e="''"),r=e))))))))))),r}function eb(){var t,r,e;return t=Ac,(r=function(){var t;(t=ob())===s&&(t=nb());return t}())!==s&&(Tc=t,r=(e=r)&&"object"==typeof e?e:{type:"number",value:e}),t=r}function nb(){var t,r,e;return t=Ac,(r=sb())!==s&&(e=ab())!==s?(Tc=t,t=r={type:"bigint",value:r+e}):(Ac=t,t=s),t===s&&(t=Ac,(r=sb())!==s&&(Tc=t,r=function(t){return Dy(t)?{type:"bigint",value:t}:{type:"number",value:parseFloat(t)}}(r)),t=r),t}function ob(){var t,r,e,n;return t=Ac,(r=sb())===s&&(r=null),r!==s&&(e=ub())!==s&&(n=ab())!==s?(Tc=t,t=r={type:"bigint",value:(r||"")+e+n}):(Ac=t,t=s),t===s&&(t=Ac,(r=sb())===s&&(r=null),r!==s&&(e=ub())!==s?(Tc=t,t=r=function(t,r){const e=(t||"")+r;return t&&Dy(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e)):(Ac=t,t=s)),t}function sb(){var r,e,n;return(r=ib())===s&&(r=cb())===s&&(r=Ac,45===t.charCodeAt(Ac)?(e="-",Ac++):(e=s,0===xc&&Mc(to)),e===s&&(43===t.charCodeAt(Ac)?(e="+",Ac++):(e=s,0===xc&&Mc(Jn))),e!==s&&(n=ib())!==s?(Tc=r,r=e=e+n):(Ac=r,r=s),r===s&&(r=Ac,45===t.charCodeAt(Ac)?(e="-",Ac++):(e=s,0===xc&&Mc(to)),e===s&&(43===t.charCodeAt(Ac)?(e="+",Ac++):(e=s,0===xc&&Mc(Jn))),e!==s&&(n=cb())!==s?(Tc=r,r=e=function(t,r){return t+r}(e,n)):(Ac=r,r=s))),r}function ub(){var r,e,n;return r=Ac,46===t.charCodeAt(Ac)?(e=".",Ac++):(e=s,0===xc&&Mc(uu)),e!==s&&(n=ib())!==s?(Tc=r,r=e="."+n):(Ac=r,r=s),r}function ab(){var r,e,n;return r=Ac,(e=function(){var r,e,n;r=Ac,fu.test(t.charAt(Ac))?(e=t.charAt(Ac),Ac++):(e=s,0===xc&&Mc(pu));e!==s?(bu.test(t.charAt(Ac))?(n=t.charAt(Ac),Ac++):(n=s,0===xc&&Mc(vu)),n===s&&(n=null),n!==s?(Tc=r,r=e=e+(null!==(o=n)?o:"")):(Ac=r,r=s)):(Ac=r,r=s);var o;return r}())!==s&&(n=ib())!==s?(Tc=r,r=e=e+n):(Ac=r,r=s),r}function ib(){var t,r,e;if(t=Ac,r=[],(e=cb())!==s)for(;e!==s;)r.push(e),e=cb();else r=s;return r!==s&&(Tc=t,r=r.join("")),t=r}function cb(){var r;return au.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(iu)),r}function lb(){var r;return cu.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(lu)),r}function fb(){var r,e,n,o;return r=Ac,"null"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(F)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function pb(){var r,e,n,o;return r=Ac,"default"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Pt)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function bb(){var r,e,n,o;return r=Ac,"to"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(at)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function vb(){var r,e,n,o;return r=Ac,"show"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(wu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function yb(){var r,e,n,o;return r=Ac,"drop"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(mu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DROP"):(Ac=r,r=s)):(Ac=r,r=s),r}function db(){var r,e,n,o;return r=Ac,"alter"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Cu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function hb(){var r,e,n,o;return r=Ac,"select"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ou)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function wb(){var r,e,n,o;return r=Ac,"update"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(ju)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function mb(){var r,e,n,o;return r=Ac,"create"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(gu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Lb(){var r,e,n,o;return r=Ac,"temporary"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Eu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Cb(){var r,e,n,o;return r=Ac,"UNLOGGED"===t.substr(Ac,8)?(e="UNLOGGED",Ac+=8):(e=s,0===xc&&Mc(Au)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="UNLOGGED"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ob(){var r,e,n,o;return r=Ac,"temp"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Tu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function jb(){var r,e,n,o;return r=Ac,"delete"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Su)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function gb(){var r,e,n,o;return r=Ac,"insert"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Uu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Eb(){var r,e,n,o;return r=Ac,"recursive"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(_u)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="RECURSIVE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ab(){var r,e,n,o;return r=Ac,"replace"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(xu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Tb(){var r,e,n,o;return r=Ac,"rename"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ru)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Sb(){var r,e,n,o;return r=Ac,"ignore"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Fo)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Ub(){var r,e,n,o;return r=Ac,"partition"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(ku)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="PARTITION"):(Ac=r,r=s)):(Ac=r,r=s),r}function _b(){var r,e,n,o;return r=Ac,"into"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Mu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function xb(){var r,e,n,o;return r=Ac,"from"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Vu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Ib(){var r,e,n,o;return r=Ac,"set"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Nr)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SET"):(Ac=r,r=s)):(Ac=r,r=s),r}function Nb(){var r,e,n,o;return r=Ac,"as"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(qu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Rb(){var r,e,n,o;return r=Ac,"table"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Qe)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TABLE"):(Ac=r,r=s)):(Ac=r,r=s),r}function kb(){var r,e,n,o;return r=Ac,"database"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(fe)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DATABASE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Mb(){var r,e,n,o;return r=Ac,"schema"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(b)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SCHEMA"):(Ac=r,r=s)):(Ac=r,r=s),r}function Vb(){var r,e,n,o;return r=Ac,"tablespace"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Pu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TABLESPACE"):(Ac=r,r=s)):(Ac=r,r=s),r}function qb(){var r,e,n,o;return r=Ac,"on"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc($)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Pb(){var r,e,n,o;return r=Ac,"join"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Hu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Db(){var r,e,n,o;return r=Ac,"outer"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Yu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Gb(){var r,e,n,o;return r=Ac,"values"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(zu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Qb(){var r,e,n,o;return r=Ac,"using"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Ku)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Fb(){var r,e,n,o;return r=Ac,"with"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(En)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function $b(){var r,e,n,o;return r=Ac,"group"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ta)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Bb(){var r,e,n,o;return r=Ac,"by"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(ra)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Hb(){var r,e,n,o;return r=Ac,"order"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ea)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function Yb(){var r,e,n,o;return r=Ac,"asc"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ua)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ASC"):(Ac=r,r=s)):(Ac=r,r=s),r}function Wb(){var r,e,n,o;return r=Ac,"desc"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(aa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DESC"):(Ac=r,r=s)):(Ac=r,r=s),r}function Xb(){var r,e,n,o;return r=Ac,"all"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ia)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ALL"):(Ac=r,r=s)):(Ac=r,r=s),r}function Zb(){var r,e,n,o;return r=Ac,"distinct"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(ca)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DISTINCT"):(Ac=r,r=s)):(Ac=r,r=s),r}function zb(){var r,e,n,o;return r=Ac,"between"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(la)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="BETWEEN"):(Ac=r,r=s)):(Ac=r,r=s),r}function Kb(){var r,e,n,o;return r=Ac,"in"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(Yr)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="IN"):(Ac=r,r=s)):(Ac=r,r=s),r}function Jb(){var r,e,n,o;return r=Ac,"is"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(pn)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="IS"):(Ac=r,r=s)):(Ac=r,r=s),r}function tv(){var r,e,n,o;return r=Ac,"like"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(fa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="LIKE"):(Ac=r,r=s)):(Ac=r,r=s),r}function rv(){var r,e,n,o;return r=Ac,"ilike"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(pa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ILIKE"):(Ac=r,r=s)):(Ac=r,r=s),r}function ev(){var r,e,n,o;return r=Ac,"exists"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(ba)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="EXISTS"):(Ac=r,r=s)):(Ac=r,r=s),r}function nv(){var r,e,n,o;return r=Ac,"not"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(D)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="NOT"):(Ac=r,r=s)):(Ac=r,r=s),r}function ov(){var r,e,n,o;return r=Ac,"and"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(va)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="AND"):(Ac=r,r=s)):(Ac=r,r=s),r}function sv(){var r,e,n,o;return r=Ac,"or"===t.substr(Ac,2).toLowerCase()?(e=t.substr(Ac,2),Ac+=2):(e=s,0===xc&&Mc(ya)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="OR"):(Ac=r,r=s)):(Ac=r,r=s),r}function uv(){var r,e,n,o;return r=Ac,"array"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(da)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ARRAY"):(Ac=r,r=s)):(Ac=r,r=s),r}function av(){var r,e,n,o;return r=Ac,"extract"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ea)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="EXTRACT"):(Ac=r,r=s)):(Ac=r,r=s),r}function iv(){var r,e,n,o;return r=Ac,"case"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Ta)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function cv(){var r,e,n,o;return r=Ac,"when"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Sa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function lv(){var r,e,n,o;return r=Ac,"else"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Ua)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function fv(){var r,e,n,o;return r=Ac,"end"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Se)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?r=e=[e,n]:(Ac=r,r=s)):(Ac=r,r=s),r}function pv(){var r,e,n,o;return r=Ac,"cast"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(_a)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CAST"):(Ac=r,r=s)):(Ac=r,r=s),r}function bv(){var r,e,n,o;return r=Ac,"numeric"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ka)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="NUMERIC"):(Ac=r,r=s)):(Ac=r,r=s),r}function vv(){var r,e,n,o;return r=Ac,"decimal"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ma)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DECIMAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function yv(){var r,e,n,o;return r=Ac,"unsigned"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(qa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="UNSIGNED"):(Ac=r,r=s)):(Ac=r,r=s),r}function dv(){var r,e,n,o;return r=Ac,"int"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Pa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="INT"):(Ac=r,r=s)):(Ac=r,r=s),r}function hv(){var r,e,n,o;return r=Ac,"integer"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ga)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="INTEGER"):(Ac=r,r=s)):(Ac=r,r=s),r}function wv(){var r,e,n,o;return r=Ac,"smallint"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Ba)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SMALLINT"):(Ac=r,r=s)):(Ac=r,r=s),r}function mv(){var r,e,n,o;return r=Ac,"serial"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ha)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SERIAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function Lv(){var r,e,n,o;return r=Ac,"tinyint"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ya)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TINYINT"):(Ac=r,r=s)):(Ac=r,r=s),r}function Cv(){var r,e,n,o;return r=Ac,"mediumint"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Ka)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MEDIUMINT"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ov(){var r,e,n,o;return r=Ac,"bigint"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ja)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="BIGINT"):(Ac=r,r=s)):(Ac=r,r=s),r}function jv(){var r,e,n,o;return r=Ac,"enum"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ti)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ENUM"):(Ac=r,r=s)):(Ac=r,r=s),r}function gv(){var r,e,n,o;return r=Ac,"float"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(ri)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="FLOAT"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ev(){var r,e,n,o;return r=Ac,"double"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(ei)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DOUBLE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Av(){var r,e,n,o;return r=Ac,"bigserial"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(ni)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="BIGSERIAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function Tv(){var r,e,n,o;return r=Ac,"real"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(oi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function Sv(){var r,e,n,o;return r=Ac,"date"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ms)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DATE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Uv(){var r,e,n,o;return r=Ac,"datetime"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(si)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DATETIME"):(Ac=r,r=s)):(Ac=r,r=s),r}function _v(){var r,e,n,o;return r=Ac,"rows"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(st)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ROWS"):(Ac=r,r=s)):(Ac=r,r=s),r}function xv(){var r,e,n,o;return r=Ac,"time"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(ui)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TIME"):(Ac=r,r=s)):(Ac=r,r=s),r}function Iv(){var r,e,n,o;return r=Ac,"timestamp"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(ai)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TIMESTAMP"):(Ac=r,r=s)):(Ac=r,r=s),r}function Nv(){var r,e,n,o;return r=Ac,"truncate"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(ci)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TRUNCATE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Rv(){var r,e,n,o;return r=Ac,"interval"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Ei)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="INTERVAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function kv(){var r,e,n,o;return r=Ac,"current_timestamp"===t.substr(Ac,17).toLowerCase()?(e=t.substr(Ac,17),Ac+=17):(e=s,0===xc&&Mc(Ti)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CURRENT_TIMESTAMP"):(Ac=r,r=s)):(Ac=r,r=s),r}function Mv(){var r,e,n,o;return r=Ac,"current_user"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(tr)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CURRENT_USER"):(Ac=r,r=s)):(Ac=r,r=s),r}function Vv(){var r,e,n,o;return r=Ac,"session_user"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(rr)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SESSION_USER"):(Ac=r,r=s)):(Ac=r,r=s),r}function qv(){var r,e,n,o;return r=Ac,"local"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(d)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="LOCAL"):(Ac=r,r=s)):(Ac=r,r=s),r}function Pv(){var r,e,n,o;return r=Ac,"view"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Ni)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="VIEW"):(Ac=r,r=s)):(Ac=r,r=s),r}function Dv(){var r;return 36===t.charCodeAt(Ac)?(r="$",Ac++):(r=s,0===xc&&Mc(so)),r}function Gv(){var r;return"$$"===t.substr(Ac,2)?(r="$$",Ac+=2):(r=s,0===xc&&Mc(Mi)),r}function Qv(){var r;return(r=function(){var r;return"@@"===t.substr(Ac,2)?(r="@@",Ac+=2):(r=s,0===xc&&Mc(ki)),r}())===s&&(r=function(){var r;return 64===t.charCodeAt(Ac)?(r="@",Ac++):(r=s,0===xc&&Mc(Ri)),r}())===s&&(r=Dv())===s&&(r=Dv()),r}function Fv(){var r;return"::"===t.substr(Ac,2)?(r="::",Ac+=2):(r=s,0===xc&&Mc(Vi)),r}function $v(){var r;return 61===t.charCodeAt(Ac)?(r="=",Ac++):(r=s,0===xc&&Mc(it)),r}function Bv(){var r,e,n,o;return r=Ac,"add"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Pi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ADD"):(Ac=r,r=s)):(Ac=r,r=s),r}function Hv(){var r,e,n,o;return r=Ac,"column"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Pe)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="COLUMN"):(Ac=r,r=s)):(Ac=r,r=s),r}function Yv(){var r,e,n,o;return r=Ac,"index"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Di)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="INDEX"):(Ac=r,r=s)):(Ac=r,r=s),r}function Wv(){var r,e,n,o;return r=Ac,"key"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(Rt)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="KEY"):(Ac=r,r=s)):(Ac=r,r=s),r}function Xv(){var r,e,n,o;return r=Ac,"unique"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Nt)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="UNIQUE"):(Ac=r,r=s)):(Ac=r,r=s),r}function Zv(){var r,e,n,o;return r=Ac,"comment"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(bn)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="COMMENT"):(Ac=r,r=s)):(Ac=r,r=s),r}function zv(){var r,e,n,o;return r=Ac,"constraint"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(De)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CONSTRAINT"):(Ac=r,r=s)):(Ac=r,r=s),r}function Kv(){var r,e,n,o;return r=Ac,"concurrently"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Fi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CONCURRENTLY"):(Ac=r,r=s)):(Ac=r,r=s),r}function Jv(){var r,e,n,o;return r=Ac,"references"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc($i)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REFERENCES"):(Ac=r,r=s)):(Ac=r,r=s),r}function ty(){var r;return 46===t.charCodeAt(Ac)?(r=".",Ac++):(r=s,0===xc&&Mc(uu)),r}function ry(){var r;return 44===t.charCodeAt(Ac)?(r=",",Ac++):(r=s,0===xc&&Mc(zi)),r}function ey(){var r;return 42===t.charCodeAt(Ac)?(r="*",Ac++):(r=s,0===xc&&Mc(ro)),r}function ny(){var r;return 40===t.charCodeAt(Ac)?(r="(",Ac++):(r=s,0===xc&&Mc(vn)),r}function oy(){var r;return 41===t.charCodeAt(Ac)?(r=")",Ac++):(r=s,0===xc&&Mc(yn)),r}function sy(){var r;return 91===t.charCodeAt(Ac)?(r="[",Ac++):(r=s,0===xc&&Mc(Ki)),r}function uy(){var r;return 93===t.charCodeAt(Ac)?(r="]",Ac++):(r=s,0===xc&&Mc(Ji)),r}function ay(){var r;return 59===t.charCodeAt(Ac)?(r=";",Ac++):(r=s,0===xc&&Mc(dn)),r}function iy(){var r;return"->"===t.substr(Ac,2)?(r="->",Ac+=2):(r=s,0===xc&&Mc(tc)),r}function cy(){var r;return"->>"===t.substr(Ac,3)?(r="->>",Ac+=3):(r=s,0===xc&&Mc(rc)),r}function ly(){var r;return(r=function(){var r;return"||"===t.substr(Ac,2)?(r="||",Ac+=2):(r=s,0===xc&&Mc(oo)),r}())===s&&(r=function(){var r;return"&&"===t.substr(Ac,2)?(r="&&",Ac+=2):(r=s,0===xc&&Mc(ec)),r}()),r}function fy(){var t,r;for(t=[],(r=hy())===s&&(r=by());r!==s;)t.push(r),(r=hy())===s&&(r=by());return t}function py(){var t,r;if(t=[],(r=hy())===s&&(r=by()),r!==s)for(;r!==s;)t.push(r),(r=hy())===s&&(r=by());else t=s;return t}function by(){var r;return(r=function r(){var e,n,o,u,a,i,c;e=Ac,"/*"===t.substr(Ac,2)?(n="/*",Ac+=2):(n=s,0===xc&&Mc(nc));if(n!==s){for(o=[],u=Ac,a=Ac,xc++,"*/"===t.substr(Ac,2)?(i="*/",Ac+=2):(i=s,0===xc&&Mc(oc)),xc--,i===s?a=void 0:(Ac=a,a=s),a!==s?(i=Ac,xc++,"/*"===t.substr(Ac,2)?(c="/*",Ac+=2):(c=s,0===xc&&Mc(nc)),xc--,c===s?i=void 0:(Ac=i,i=s),i!==s&&(c=yy())!==s?u=a=[a,i,c]:(Ac=u,u=s)):(Ac=u,u=s),u===s&&(u=r());u!==s;)o.push(u),u=Ac,a=Ac,xc++,"*/"===t.substr(Ac,2)?(i="*/",Ac+=2):(i=s,0===xc&&Mc(oc)),xc--,i===s?a=void 0:(Ac=a,a=s),a!==s?(i=Ac,xc++,"/*"===t.substr(Ac,2)?(c="/*",Ac+=2):(c=s,0===xc&&Mc(nc)),xc--,c===s?i=void 0:(Ac=i,i=s),i!==s&&(c=yy())!==s?u=a=[a,i,c]:(Ac=u,u=s)):(Ac=u,u=s),u===s&&(u=r());o!==s?("*/"===t.substr(Ac,2)?(u="*/",Ac+=2):(u=s,0===xc&&Mc(oc)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s)}else Ac=e,e=s;return e}())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,"--"===t.substr(Ac,2)?(e="--",Ac+=2):(e=s,0===xc&&Mc(sc));if(e!==s){for(n=[],o=Ac,u=Ac,xc++,a=wy(),xc--,a===s?u=void 0:(Ac=u,u=s),u!==s&&(a=yy())!==s?o=u=[u,a]:(Ac=o,o=s);o!==s;)n.push(o),o=Ac,u=Ac,xc++,a=wy(),xc--,a===s?u=void 0:(Ac=u,u=s),u!==s&&(a=yy())!==s?o=u=[u,a]:(Ac=o,o=s);n!==s?r=e=[e,n]:(Ac=r,r=s)}else Ac=r,r=s;return r}()),r}function vy(){var t,r,e,n;return t=Ac,(r=Zv())!==s&&fy()!==s?((e=$v())===s&&(e=null),e!==s&&fy()!==s&&(n=zp())!==s?(Tc=t,t=r=function(t,r,e){return{type:t.toLowerCase(),keyword:t.toLowerCase(),symbol:r,value:e}}(r,e,n)):(Ac=t,t=s)):(Ac=t,t=s),t}function yy(){var r;return t.length>Ac?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(uc)),r}function dy(){var r;return(r=function(){var r,e,n,o;return r=Ac,"year"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(qs)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="YEAR"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n,o;return r=Ac,"month"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(xs)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MONTH"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n,o;return r=Ac,"day"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(ws)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="DAY"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n,o;return r=Ac,"hour"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(gs)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="HOUR"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n,o;return r=Ac,"minute"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(_s)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MINUTE"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(r=function(){var r,e,n,o;return r=Ac,"second"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ns)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="SECOND"):(Ac=r,r=s)):(Ac=r,r=s),r}()),r}function hy(){var r;return ac.test(t.charAt(Ac))?(r=t.charAt(Ac),Ac++):(r=s,0===xc&&Mc(ic)),r}function wy(){var r,e;if((r=function(){var r,e;r=Ac,xc++,t.length>Ac?(e=t.charAt(Ac),Ac++):(e=s,0===xc&&Mc(uc));xc--,e===s?r=void 0:(Ac=r,r=s);return r}())===s)if(r=[],ou.test(t.charAt(Ac))?(e=t.charAt(Ac),Ac++):(e=s,0===xc&&Mc(su)),e!==s)for(;e!==s;)r.push(e),ou.test(t.charAt(Ac))?(e=t.charAt(Ac),Ac++):(e=s,0===xc&&Mc(su));else r=s;return r}function my(){var t,r;return t=Ac,Tc=Ac,Hy=[],(!0?void 0:s)!==s&&fy()!==s?((r=Ly())===s&&(r=Cy()),r!==s?(Tc=t,t={type:"proc",stmt:r,vars:Hy}):(Ac=t,t=s)):(Ac=t,t=s),t}function Ly(){var r,e,n,o,u,a;return(r=function(){var r,e,n,o,u;return r=Ac,xv()!==s&&fy()!==s?("zone"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(wn)),e!==s&&fy()!==s&&(n=Gf())!==s&&fy()!==s&&(o=bb())!==s&&fy()!==s&&(u=dy())!==s?(Tc=r,r={type:"assign",left:{type:"expr_list",value:[{type:"origin",value:"time zone"},n],separator:" "},symbol:"to",right:{type:"origin",value:u}}):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,xv()!==s&&fy()!==s?("zone"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(wn)),e!==s&&fy()!==s?((n=bb())===s&&(n=null),n!==s&&fy()!==s?((o=eb())===s&&(o=zp())===s&&(o=qv())===s&&("default"===t.substr(Ac,7).toLowerCase()?(o=t.substr(Ac,7),Ac+=7):(o=s,0===xc&&Mc(cc))),o!==s?(Tc=r,r=function(t,r){return{type:"assign",left:{type:"origin",value:"time zone"},symbol:t?"to":null,right:"string"==typeof r?{type:"origin",value:r}:r}}(n,o)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)):(Ac=r,r=s)),r}())===s&&(r=Ac,(e=Uy())===s&&(e=_y()),e!==s&&fy()!==s?((n=function(){var r;return":="===t.substr(Ac,2)?(r=":=",Ac+=2):(r=s,0===xc&&Mc(S)),r}())===s&&(n=$v())===s&&(n=bb()),n!==s&&fy()!==s&&(o=Oy())!==s?(Tc=r,u=n,a=o,r=e={type:"assign",left:e,symbol:Array.isArray(u)?u[0]:u,right:a}):(Ac=r,r=s)):(Ac=r,r=s)),r}function Cy(){var r,e;return r=Ac,function(){var r,e,n,o;return r=Ac,"return"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Iu)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="RETURN"):(Ac=r,r=s)):(Ac=r,r=s),r}()!==s&&fy()!==s&&(e=Oy())!==s?(Tc=r,r={type:"return",expr:e}):(Ac=r,r=s),r}function Oy(){var t;return(t=Fl())===s&&(t=function(){var t,r,e,n,o;t=Ac,(r=Uy())!==s&&fy()!==s&&(e=yf())!==s&&fy()!==s&&(n=Uy())!==s&&fy()!==s&&(o=wf())!==s?(Tc=t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(Ac=t,t=s);return t}())===s&&(t=jy())===s&&(t=function(){var t,r;t=Ac,sy()!==s&&fy()!==s&&(r=Sy())!==s&&fy()!==s&&uy()!==s?(Tc=t,t={type:"array",value:r}):(Ac=t,t=s);return t}()),t}function jy(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=gy())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=rp())!==s&&(a=fy())!==s&&(i=gy())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=rp())!==s&&(a=fy())!==s&&(i=gy())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Pn(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function gy(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Ey())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=np())!==s&&(a=fy())!==s&&(i=Ey())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=np())!==s&&(a=fy())!==s&&(i=Ey())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Pn(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Ey(){var t,r,e,n,o,u,a,i,c;return(t=Hp())===s&&(t=Uy())===s&&(t=Ty())===s&&(t=Up())===s&&(t=Ac,(r=ny())!==s&&(e=fy())!==s&&(n=jy())!==s&&(o=fy())!==s&&(u=oy())!==s?(Tc=t,(c=n).parentheses=!0,t=r=c):(Ac=t,t=s),t===s&&(t=Ac,(r=Ep())!==s?(e=Ac,(n=ty())!==s&&(o=fy())!==s&&(u=Ep())!==s?e=n=[n,o,u]:(Ac=e,e=s),e===s&&(e=null),e!==s?(Tc=t,a=r,t=r=(i=e)?{type:"column_ref",table:a,column:i[2]}:{type:"var",name:a,prefix:null}):(Ac=t,t=s)):(Ac=t,t=s))),t}function Ay(){var t,r,e,n,o,u,a;return t=Ac,(r=fp())!==s?(e=Ac,(n=fy())!==s&&(o=ty())!==s&&(u=fy())!==s&&(a=fp())!==s?e=n=[n,o,u,a]:(Ac=e,e=s),e===s&&(e=null),e!==s?(Tc=t,t=r=function(t,r){const e={name:[t]};return null!==r&&(e.schema=t,e.name=[r[3]]),e}(r,e)):(Ac=t,t=s)):(Ac=t,t=s),t}function Ty(){var t,r,e;return t=Ac,(r=Ay())!==s&&fy()!==s&&ny()!==s&&fy()!==s?((e=Sy())===s&&(e=null),e!==s&&fy()!==s&&oy()!==s?(Tc=t,t=r={type:"function",name:r,args:{type:"expr_list",value:e},...Vy()}):(Ac=t,t=s)):(Ac=t,t=s),t}function Sy(){var t,r,e,n,o,u,a,i;if(t=Ac,(r=Ey())!==s){for(e=[],n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ey())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);n!==s;)e.push(n),n=Ac,(o=fy())!==s&&(u=ry())!==s&&(a=fy())!==s&&(i=Ey())!==s?n=o=[o,u,a,i]:(Ac=n,n=s);e!==s?(Tc=t,t=r=Gy(r,e)):(Ac=t,t=s)}else Ac=t,t=s;return t}function Uy(){var r,e,n,o,u,a,i;if(r=Ac,(e=Gv())!==s){for(n=[],lc.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(fc));o!==s;)n.push(o),lc.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(fc));n!==s&&(o=Gv())!==s?(Tc=r,r=e={type:"var",name:n.join(""),prefix:"$$",suffix:"$$"}):(Ac=r,r=s)}else Ac=r,r=s;if(r===s){if(r=Ac,(e=Dv())!==s)if((n=jp())!==s)if((o=Dv())!==s){for(u=[],lc.test(t.charAt(Ac))?(a=t.charAt(Ac),Ac++):(a=s,0===xc&&Mc(fc));a!==s;)u.push(a),lc.test(t.charAt(Ac))?(a=t.charAt(Ac),Ac++):(a=s,0===xc&&Mc(fc));u!==s&&(a=Dv())!==s&&(i=jp())!==s?(Tc=Ac,(function(t,r,e){if(t!==e)return!0}(n,0,i)?s:void 0)!==s&&Dv()!==s?(Tc=r,r=e=function(t,r,e){return{type:"var",name:r.join(""),prefix:`$${t}$`,suffix:`$${e}$`}}(n,u,i)):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;r===s&&(r=Ac,(e=Qv())!==s&&(n=_y())!==s?(Tc=r,r=e=function(t,r){return{type:"var",...r,prefix:t}}(e,n)):(Ac=r,r=s))}return r}function _y(){var r,e,n,o,u;return r=Ac,34===t.charCodeAt(Ac)?(e='"',Ac++):(e=s,0===xc&&Mc(wo)),e===s&&(e=null),e!==s&&(n=Ep())!==s&&(o=function(){var r,e,n,o,u;r=Ac,e=[],n=Ac,46===t.charCodeAt(Ac)?(o=".",Ac++):(o=s,0===xc&&Mc(uu));o!==s&&(u=Ep())!==s?n=o=[o,u]:(Ac=n,n=s);for(;n!==s;)e.push(n),n=Ac,46===t.charCodeAt(Ac)?(o=".",Ac++):(o=s,0===xc&&Mc(uu)),o!==s&&(u=Ep())!==s?n=o=[o,u]:(Ac=n,n=s);e!==s&&(Tc=r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==s?(34===t.charCodeAt(Ac)?(u='"',Ac++):(u=s,0===xc&&Mc(wo)),u===s&&(u=null),u!==s?(Tc=r,r=e=function(t,r,e,n){if(t&&!n||!t&&n)throw new Error("double quoted not match");return Hy.push(r),{type:"var",name:r,members:e,quoted:t&&n?'"':null,prefix:null}}(e,n,o,u)):(Ac=r,r=s)):(Ac=r,r=s),r===s&&(r=Ac,(e=eb())!==s&&(Tc=r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function xy(){var r;return(r=function(){var t,r,e;t=Ac,(r=ky())===s&&(r=Ny());r!==s&&fy()!==s&&sy()!==s&&fy()!==s&&(e=uy())!==s&&fy()!==s&&sy()!==s&&fy()!==s&&uy()!==s?(Tc=t,n=r,r={...n,array:{dimension:2}},t=r):(Ac=t,t=s);var n;t===s&&(t=Ac,(r=ky())===s&&(r=Ny()),r!==s&&fy()!==s&&sy()!==s&&fy()!==s?((e=eb())===s&&(e=null),e!==s&&fy()!==s&&uy()!==s?(Tc=t,r=function(t,r){return{...t,array:{dimension:1,length:[r]}}}(r,e),t=r):(Ac=t,t=s)):(Ac=t,t=s),t===s&&(t=Ac,(r=ky())===s&&(r=Ny()),r!==s&&fy()!==s&&uv()!==s?(Tc=t,r=function(t){return{...t,array:{keyword:"array"}}}(r),t=r):(Ac=t,t=s)));return t}())===s&&(r=Ny())===s&&(r=ky())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=Sv())===s&&(e=Uv());if(e!==s){if(n=Ac,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(Ac=n,n=s)}else Ac=n,n=s;else Ac=n,n=s;else Ac=n,n=s;n===s&&(n=null),n!==s?(Tc=r,e=function(t,r){const e={dataType:t};return r&&(e.length=parseInt(r[3].join(""),10),e.parentheses=!0),e}(e,n),r=e):(Ac=r,r=s)}else Ac=r,r=s;r===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=xv())===s&&(e=Iv())===s&&(e=function(){var r,e,n,o;return r=Ac,"timestamptz"===t.substr(Ac,11).toLowerCase()?(e=t.substr(Ac,11),Ac+=11):(e=s,0===xc&&Mc(ii)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TIMESTAMPTZ"):(Ac=r,r=s)):(Ac=r,r=s),r}());if(e!==s){if(n=Ac,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(Ac=n,n=s)}else Ac=n,n=s;else Ac=n,n=s;else Ac=n,n=s;n===s&&(n=null),n!==s&&(o=fy())!==s?((u=function(){var r,e,n;r=Ac,"without"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(dc));e===s&&("with"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(En)));e!==s&&fy()!==s&&xv()!==s&&fy()!==s?("zone"===t.substr(Ac,4).toLowerCase()?(n=t.substr(Ac,4),Ac+=4):(n=s,0===xc&&Mc(wn)),n!==s?(Tc=r,e=[e.toUpperCase(),"TIME","ZONE"],r=e):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(u=null),u!==s?(Tc=r,e=function(t,r,e){const n={dataType:t};return r&&(n.length=parseInt(r[3].join(""),10),n.parentheses=!0),e&&(n.suffix=e),n}(e,n,u),r=e):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;return r}());return r}())===s&&(r=function(){var r,e;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"json"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Qa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="JSON"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"jsonb"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(Fa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="JSONB"):(Ac=r,r=s)):(Ac=r,r=s),r}());e!==s&&(Tc=r,e=hc(e));return r=e}())===s&&(r=function(){var r,e,n,o,u,a,i,c,l;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"geometry"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc($a)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="GEOMETRY"):(Ac=r,r=s)):(Ac=r,r=s),r}())!==s?(n=Ac,(o=fy())!==s&&(u=ny())!==s&&(a=fy())!==s&&(i=function(){var r,e,n,o,u,a,i;r=Ac,"point"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(wc));e===s&&("linestring"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(mc)),e===s&&("polygon"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Lc)),e===s&&("multipoint"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Cc)),e===s&&("multilinestring"===t.substr(Ac,15).toLowerCase()?(e=t.substr(Ac,15),Ac+=15):(e=s,0===xc&&Mc(Oc)),e===s&&("multipolygon"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(jc)),e===s&&("geometrycollection"===t.substr(Ac,18).toLowerCase()?(e=t.substr(Ac,18),Ac+=18):(e=s,0===xc&&Mc(gc))))))));if(e!==s)if(fy()!==s){if(n=Ac,(o=ry())!==s)if((u=fy())!==s){if(a=[],au.test(t.charAt(Ac))?(i=t.charAt(Ac),Ac++):(i=s,0===xc&&Mc(iu)),i!==s)for(;i!==s;)a.push(i),au.test(t.charAt(Ac))?(i=t.charAt(Ac),Ac++):(i=s,0===xc&&Mc(iu));else a=s;a!==s?n=o=[o,u,a]:(Ac=n,n=s)}else Ac=n,n=s;else Ac=n,n=s;n===s&&(n=null),n!==s?(Tc=r,e={length:e,scale:(c=n)&&c[2]&&parseInt(c[2].join(""),10)},r=e):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s;var c;return r}())!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=r,e={dataType:e,...(f=n)&&f[3]||{},parentheses:!!f},r=e):(Ac=r,r=s)):(Ac=r,r=s);var f;return r}())===s&&(r=function(){var r,e,n,o,u,a;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"tinytext"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Wa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TINYTEXT"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"text"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Xa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="TEXT"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"mediumtext"===t.substr(Ac,10).toLowerCase()?(e=t.substr(Ac,10),Ac+=10):(e=s,0===xc&&Mc(Za)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="MEDIUMTEXT"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"longtext"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(za)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="LONGTEXT"):(Ac=r,r=s)):(Ac=r,r=s),r}());e!==s?(n=Ac,(o=sy())!==s&&(u=fy())!==s&&(a=uy())!==s?n=o=[o,u,a]:(Ac=n,n=s),n===s&&(n=null),n!==s?(Tc=r,r=e={dataType:`${e}${n?"[]":""}`}):(Ac=r,r=s)):(Ac=r,r=s);return r}())===s&&(r=function(){var r,e;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"uuid"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(fi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="UUID"):(Ac=r,r=s)):(Ac=r,r=s),r}())!==s&&(Tc=r,e={dataType:e});return r=e}())===s&&(r=function(){var r,e;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"bool"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(xa)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="BOOL"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"boolean"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ia)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="BOOLEAN"):(Ac=r,r=s)):(Ac=r,r=s),r}());e!==s&&(Tc=r,e=pc(e));return r=e}())===s&&(r=function(){var t,r,e;t=Ac,(r=jv())!==s&&fy()!==s&&(e=Pf())!==s?(Tc=t,n=r,(o=e).parentheses=!0,t=r={dataType:n,expr:o}):(Ac=t,t=s);var n,o;return t}())===s&&(r=function(){var t,r;t=Ac,(r=mv())===s&&(r=Rv());r!==s&&(Tc=t,r=hc(r));return t=r}())===s&&(r=function(){var r,e;r=Ac,"bytea"===t.substr(Ac,5).toLowerCase()?(e=t.substr(Ac,5),Ac+=5):(e=s,0===xc&&Mc(bc));e!==s&&(Tc=r,e={dataType:"BYTEA"});return r=e}())===s&&(r=function(){var r,e;r=Ac,(e=function(){var r,e,n,o;return r=Ac,"oid"===t.substr(Ac,3).toLowerCase()?(e=t.substr(Ac,3),Ac+=3):(e=s,0===xc&&Mc(pi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="OID"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regclass"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(bi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGCLASS"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regcollation"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(vi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGCOLLATION"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regconfig"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(yi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGCONFIG"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regdictionary"===t.substr(Ac,13).toLowerCase()?(e=t.substr(Ac,13),Ac+=13):(e=s,0===xc&&Mc(di)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGDICTIONARY"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regnamespace"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(hi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGNAMESPACE"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regoper"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(wi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGOPER"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regoperator"===t.substr(Ac,11).toLowerCase()?(e=t.substr(Ac,11),Ac+=11):(e=s,0===xc&&Mc(mi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGOPERATOR"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regproc"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Li)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGPROC"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regprocedure"===t.substr(Ac,12).toLowerCase()?(e=t.substr(Ac,12),Ac+=12):(e=s,0===xc&&Mc(Ci)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGPROCEDURE"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regrole"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Oi)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGROLE"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"regtype"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(ji)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="REGTYPE"):(Ac=r,r=s)):(Ac=r,r=s),r}());e!==s&&(Tc=r,e=pc(e));return r=e}())===s&&(r=function(){var r,e;r=Ac,"record"===t.substr(Ac,6).toLowerCase()?(e=t.substr(Ac,6),Ac+=6):(e=s,0===xc&&Mc(Ec));e!==s&&(Tc=r,e={dataType:"RECORD"});return r=e}())===s&&(r=function(){var t,r;t=Ac,(r=Ep())!==s?(Tc=Ac,e=r,(Xy.has(e)?void 0:s)!==s?(Tc=t,r=function(t){return{dataType:t}}(r),t=r):(Ac=t,t=s)):(Ac=t,t=s);var e;return t}()),r}function Iy(){var r,e;return r=Ac,function(){var r,e,n,o;return r=Ac,"character"===t.substr(Ac,9).toLowerCase()?(e=t.substr(Ac,9),Ac+=9):(e=s,0===xc&&Mc(Ir)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CHARACTER"):(Ac=r,r=s)):(Ac=r,r=s),r}()!==s&&fy()!==s?("varying"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(vc)),e===s&&(e=null),e!==s?(Tc=r,r="CHARACTER VARYING"):(Ac=r,r=s)):(Ac=r,r=s),r}function Ny(){var r,e,n,o,u,a,i,c,l;if(r=Ac,(e=function(){var r,e,n,o;return r=Ac,"char"===t.substr(Ac,4).toLowerCase()?(e=t.substr(Ac,4),Ac+=4):(e=s,0===xc&&Mc(Na)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="CHAR"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=function(){var r,e,n,o;return r=Ac,"varchar"===t.substr(Ac,7).toLowerCase()?(e=t.substr(Ac,7),Ac+=7):(e=s,0===xc&&Mc(Ra)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="VARCHAR"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(e=Iy()),e!==s){if(n=Ac,(o=fy())!==s)if((u=ny())!==s)if((a=fy())!==s){if(i=[],au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu)),c!==s)for(;c!==s;)i.push(c),au.test(t.charAt(Ac))?(c=t.charAt(Ac),Ac++):(c=s,0===xc&&Mc(iu));else i=s;i!==s&&(c=fy())!==s&&(l=oy())!==s?n=o=[o,u,a,i,c,l]:(Ac=n,n=s)}else Ac=n,n=s;else Ac=n,n=s;else Ac=n,n=s;n===s&&(n=null),n!==s?(Tc=r,r=e=function(t,r){const e={dataType:t};return r&&(e.length=parseInt(r[3].join(""),10),e.parentheses=!0),e}(e,n)):(Ac=r,r=s)}else Ac=r,r=s;return r}function Ry(){var r,e,n;return r=Ac,(e=yv())===s&&(e=null),e!==s&&fy()!==s?((n=function(){var r,e,n,o;return r=Ac,"zerofill"===t.substr(Ac,8).toLowerCase()?(e=t.substr(Ac,8),Ac+=8):(e=s,0===xc&&Mc(Da)),e!==s?(n=Ac,xc++,o=Ap(),xc--,o===s?n=void 0:(Ac=n,n=s),n!==s?(Tc=r,r=e="ZEROFILL"):(Ac=r,r=s)):(Ac=r,r=s),r}())===s&&(n=null),n!==s?(Tc=r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(Ac=r,r=s)):(Ac=r,r=s),r}function ky(){var r,e,n,o,u,a,i,c,l,f,p,b,v,y,d,h;if(r=Ac,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=hv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=Ov())===s&&(e=gv())===s&&(e=Ac,(n=Ev())!==s&&(o=fy())!==s?("precision"===t.substr(Ac,9).toLowerCase()?(u=t.substr(Ac,9),Ac+=9):(u=s,0===xc&&Mc(yc)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=Ev())===s&&(e=mv())===s&&(e=Av())===s&&(e=Tv())),e!==s)if((n=fy())!==s)if((o=ny())!==s)if((u=fy())!==s){if(a=[],au.test(t.charAt(Ac))?(i=t.charAt(Ac),Ac++):(i=s,0===xc&&Mc(iu)),i!==s)for(;i!==s;)a.push(i),au.test(t.charAt(Ac))?(i=t.charAt(Ac),Ac++):(i=s,0===xc&&Mc(iu));else a=s;if(a!==s)if((i=fy())!==s){if(c=Ac,(l=ry())!==s)if((f=fy())!==s){if(p=[],au.test(t.charAt(Ac))?(b=t.charAt(Ac),Ac++):(b=s,0===xc&&Mc(iu)),b!==s)for(;b!==s;)p.push(b),au.test(t.charAt(Ac))?(b=t.charAt(Ac),Ac++):(b=s,0===xc&&Mc(iu));else p=s;p!==s?c=l=[l,f,p]:(Ac=c,c=s)}else Ac=c,c=s;else Ac=c,c=s;c===s&&(c=null),c!==s&&(l=fy())!==s&&(f=oy())!==s&&(p=fy())!==s?((b=Ry())===s&&(b=null),b!==s?(Tc=r,v=e,y=a,d=c,h=b,r=e={dataType:Array.isArray(v)?`${v[0].toUpperCase()} ${v[2].toUpperCase()}`:v,length:parseInt(y.join(""),10),scale:d&&parseInt(d[2].join(""),10),parentheses:!0,suffix:h}):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;else Ac=r,r=s}else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;else Ac=r,r=s;if(r===s){if(r=Ac,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=hv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=Ov())===s&&(e=gv())===s&&(e=Ac,(n=Ev())!==s&&(o=fy())!==s?("precision"===t.substr(Ac,9).toLowerCase()?(u=t.substr(Ac,9),Ac+=9):(u=s,0===xc&&Mc(yc)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=Ev())===s&&(e=mv())===s&&(e=Av())===s&&(e=Tv())),e!==s){if(n=[],au.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(iu)),o!==s)for(;o!==s;)n.push(o),au.test(t.charAt(Ac))?(o=t.charAt(Ac),Ac++):(o=s,0===xc&&Mc(iu));else n=s;n!==s&&(o=fy())!==s?((u=Ry())===s&&(u=null),u!==s?(Tc=r,r=e=function(t,r,e){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,length:parseInt(r.join(""),10),suffix:e}}(e,n,u)):(Ac=r,r=s)):(Ac=r,r=s)}else Ac=r,r=s;r===s&&(r=Ac,(e=bv())===s&&(e=vv())===s&&(e=dv())===s&&(e=hv())===s&&(e=wv())===s&&(e=Lv())===s&&(e=Cv())===s&&(e=Ov())===s&&(e=gv())===s&&(e=Ac,(n=Ev())!==s&&(o=fy())!==s?("precision"===t.substr(Ac,9).toLowerCase()?(u=t.substr(Ac,9),Ac+=9):(u=s,0===xc&&Mc(yc)),u!==s?e=n=[n,o,u]:(Ac=e,e=s)):(Ac=e,e=s),e===s&&(e=Ev())===s&&(e=mv())===s&&(e=Av())===s&&(e=Tv())),e!==s&&(n=fy())!==s?((o=Ry())===s&&(o=null),o!==s&&(u=fy())!==s?(Tc=r,r=e=function(t,r){return{dataType:Array.isArray(t)?`${t[0].toUpperCase()} ${t[2].toUpperCase()}`:t,suffix:r}}(e,o)):(Ac=r,r=s)):(Ac=r,r=s))}return r}const My={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,AT:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CONSTRAINT:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,EXCEPT:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,ILIKE:!0,JOIN:!0,JSON:!0,LEFT:!0,LIKE:!0,LIMIT:!0,NOT:!0,NULL:!0,NULLS:!0,OFFSET:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,PARTITION:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,WITH:!0,WHEN:!0,WHERE:!0,WINDOW:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function Vy(){return r.includeLocations?{loc:kc(Tc,Ac)}:{}}function qy(t,r){return{type:"unary_expr",operator:t,expr:r}}function Py(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e,...Vy()}}function Dy(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function Gy(t,r,e=3){const n=Array.isArray(t)?t:[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function Qy(t,r){let e=t;for(let t=0;t<r.length;t++)e=Py(r[t][1],e,r[t][3]);return e}function Fy(t){const r=Zy[t];return r||(t||null)}function $y(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=Fy(t[1])),r.add(t.join("::"))}return Array.from(r)}function By(t){return"string"==typeof t?{type:"same",value:t}:t}let Hy=[];const Yy=new Set,Wy=new Set,Xy=new Set,Zy={};if((e=a())!==s&&Ac===t.length)return e;throw e!==s&&Ac<t.length&&Mc({type:"end"}),Vc(_c,Uc<t.length?t.charAt(Uc):null,Uc<t.length?kc(Uc,Uc+1):kc(Uc,Uc))}}},function(t,r,e){t.exports=e(27)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(24);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":s(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":s(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":s(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(28))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),s="function"==typeof BigInt;function u(t,r,e,n){return void 0===t?u[0]:void 0!==r&&(10!=+r||e)?D(t,r,e,n):B(t)}function a(t,r){this.value=t,this.sign=r,this.isSmall=!1}function i(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&S(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function y(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,e){var n,o,s=t.length,u=e.length,a=new Array(s),i=0,c=r;for(o=0;o<u;o++)i=(n=t[o]+e[o]+i)>=c?1:0,a[o]=n-i*c;for(;o<s;)i=(n=t[o]+i)===c?1:0,a[o++]=n-i*c;return i>0&&a.push(i),a}function h(t,r){return t.length>=r.length?d(t,r):d(r,t)}function w(t,e){var n,o,s=t.length,u=new Array(s),a=r;for(o=0;o<s;o++)n=t[o]-a+e,e=Math.floor(n/a),u[o]=n-e*a,e+=1;for(;e>0;)u[o++]=e%a,e=Math.floor(e/a);return u}function m(t,r){var e,n,o=t.length,s=r.length,u=new Array(o),a=0;for(e=0;e<s;e++)(n=t[e]-a-r[e])<0?(n+=1e7,a=1):a=0,u[e]=n;for(e=s;e<o;e++){if(!((n=t[e]-a)<0)){u[e++]=n;break}n+=1e7,u[e]=n}for(;e<o;e++)u[e]=t[e];return b(u),u}function L(t,r,e){var n,o,s=t.length,u=new Array(s),c=-r;for(n=0;n<s;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,u[n]=o<0?o+1e7:o;return"number"==typeof(u=p(u))?(e&&(u=-u),new i(u)):new a(u,e)}function C(t,r){var e,n,o,s,u=t.length,a=r.length,i=v(u+a);for(o=0;o<u;++o){s=t[o];for(var c=0;c<a;++c)e=s*r[c]+i[o+c],n=Math.floor(e/1e7),i[o+c]=e-1e7*n,i[o+c+1]+=n}return b(i),i}function O(t,e){var n,o,s=t.length,u=new Array(s),a=r,i=0;for(o=0;o<s;o++)n=t[o]*e+i,i=Math.floor(n/a),u[o]=n-i*a;for(;i>0;)u[o++]=i%a,i=Math.floor(i/a);return u}function j(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function g(t,e,n){return new a(t<r?O(e,t):C(e,f(t)),n)}function E(t){var r,e,n,o,s=t.length,u=v(s+s);for(n=0;n<s;n++){e=0-(o=t[n])*o;for(var a=n;a<s;a++)r=o*t[a]*2+u[n+a]+e,e=Math.floor(r/1e7),u[n+a]=r-1e7*e;u[n+s]=e}return b(u),u}function A(t,r){var e,n,o,s,u=t.length,a=v(u);for(o=0,e=u-1;e>=0;--e)o=(s=1e7*o+t[e])-(n=y(s/r))*r,a[e]=0|n;return[a,0|o]}function T(t,e){var n,o=B(e);if(s)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,h=o.value;if(0===h)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new i(y(d/h)),new i(d%h)]:[u[0],t];if(o.isSmall){if(1===h)return[t,u[0]];if(-1==h)return[t.negate(),u[0]];var w=Math.abs(h);if(w<r){l=p((n=A(d,w))[0]);var L=n[1];return t.sign&&(L=-L),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new i(l),new i(L)]):[new a(l,t.sign!==o.sign),new i(L)]}h=f(w)}var C=S(d,h);if(-1===C)return[u[0],t];if(0===C)return[u[t.sign===o.sign?1:-1],u[0]];l=(n=d.length+h.length<=200?function(t,e){var n,o,s,u,a,i,c,l=t.length,f=e.length,b=r,y=v(e.length),d=e[f-1],h=Math.ceil(b/(2*d)),w=O(t,h),m=O(e,h);for(w.length<=l&&w.push(0),m.push(0),d=m[f-1],o=l-f;o>=0;o--){for(n=b-1,w[o+f]!==d&&(n=Math.floor((w[o+f]*b+w[o+f-1])/d)),s=0,u=0,i=m.length,a=0;a<i;a++)s+=n*m[a],c=Math.floor(s/b),u+=w[o+a]-(s-c*b),s=c,u<0?(w[o+a]=u+b,u=-1):(w[o+a]=u,u=0);for(;0!==u;){for(n-=1,s=0,a=0;a<i;a++)(s+=w[o+a]-b+m[a])<0?(w[o+a]=s+b,s=0):(w[o+a]=s,s=1);u+=s}y[o]=n}return w=A(w,h)[0],[p(y),p(w)]}(d,h):function(t,r){for(var e,n,o,s,u,a=t.length,i=r.length,c=[],l=[];a;)if(l.unshift(t[--a]),b(l),S(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],s=1e7*r[i-1]+r[i-2],n>i&&(o=1e7*(o+1)),e=Math.ceil(o/s);do{if(S(u=O(r,e),l)<=0)break;e--}while(e);c.push(e),l=m(l,u)}return c.reverse(),[p(c),p(l)]}(d,h))[0];var j=t.sign!==o.sign,g=n[1],E=t.sign;return"number"==typeof l?(j&&(l=-l),l=new i(l)):l=new a(l,j),"number"==typeof g?(E&&(g=-g),g=new i(g)):g=new a(g,E),[l,g]}function S(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function U(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function _(t,r){for(var e,n,s,u=t.prev(),a=u,i=0;a.isEven();)a=a.divide(2),i++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(s=o(r[n]).modPow(a,t)).isUnit()&&!s.equals(u)){for(e=i-1;0!=e;e--){if((s=s.square().mod(t)).isUnit())return!1;if(s.equals(u))continue t}return!1}return!0}a.prototype=Object.create(u.prototype),i.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),a.prototype.add=function(t){var r=B(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new a(w(e,Math.abs(n)),this.sign):new a(h(e,n),this.sign)},a.prototype.plus=a.prototype.add,i.prototype.add=function(t){var r=B(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new i(e+n);n=f(Math.abs(n))}return new a(w(n,Math.abs(e)),e<0)},i.prototype.plus=i.prototype.add,c.prototype.add=function(t){return new c(this.value+B(t).value)},c.prototype.plus=c.prototype.add,a.prototype.subtract=function(t){var r=B(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?L(e,Math.abs(n),this.sign):function(t,r,e){var n;return S(t,r)>=0?n=m(t,r):(n=m(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new i(n)):new a(n,e)}(e,n,this.sign)},a.prototype.minus=a.prototype.subtract,i.prototype.subtract=function(t){var r=B(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new i(e-n):L(n,Math.abs(e),e>=0)},i.prototype.minus=i.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-B(t).value)},c.prototype.minus=c.prototype.subtract,a.prototype.negate=function(){return new a(this.value,!this.sign)},i.prototype.negate=function(){var t=this.sign,r=new i(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},a.prototype.abs=function(){return new a(this.value,!1)},i.prototype.abs=function(){return new i(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},a.prototype.multiply=function(t){var e,n,o,s=B(t),i=this.value,c=s.value,l=this.sign!==s.sign;if(s.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new a(O(i,e),l);c=f(e)}return n=i.length,o=c.length,new a(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return C(r,e);n=Math.ceil(n/2);var o=r.slice(n),s=r.slice(0,n),u=e.slice(n),a=e.slice(0,n),i=t(s,a),c=t(o,u),l=t(h(s,o),h(a,u)),f=h(h(i,j(m(m(l,i),c),n)),j(c,2*n));return b(f),f}(i,c):C(i,c),l)},a.prototype.times=a.prototype.multiply,i.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new i(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},a.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},i.prototype.multiply=function(t){return B(t)._multiplyBySmall(this)},i.prototype.times=i.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*B(t).value)},c.prototype.times=c.prototype.multiply,a.prototype.square=function(){return new a(E(this.value),!1)},i.prototype.square=function(){var t=this.value*this.value;return l(t)?new i(t):new a(E(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},a.prototype.divmod=function(t){var r=T(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=i.prototype.divmod=a.prototype.divmod,a.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/B(t).value)},i.prototype.over=i.prototype.divide=a.prototype.over=a.prototype.divide,a.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%B(t).value)},i.prototype.remainder=i.prototype.mod=a.prototype.remainder=a.prototype.mod,a.prototype.pow=function(t){var r,e,n,o=B(t),s=this.value,a=o.value;if(0===a)return u[1];if(0===s)return u[0];if(1===s)return u[1];if(-1===s)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(s,a)))return new i(y(r));for(e=this,n=u[1];!0&a&&(n=n.times(e),--a),0!==a;)a/=2,e=e.square();return n},i.prototype.pow=a.prototype.pow,c.prototype.pow=function(t){var r=B(t),e=this.value,n=r.value,o=BigInt(0),s=BigInt(1),a=BigInt(2);if(n===o)return u[1];if(e===o)return u[0];if(e===s)return u[1];if(e===BigInt(-1))return r.isEven()?u[1]:u[-1];if(r.isNegative())return new c(o);for(var i=this,l=u[1];(n&s)===s&&(l=l.times(i),--n),n!==o;)n/=a,i=i.square();return l},a.prototype.modPow=function(t,r){if(t=B(t),(r=B(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=u[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(u[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=i.prototype.modPow=a.prototype.modPow,a.prototype.compareAbs=function(t){var r=B(t),e=this.value,n=r.value;return r.isSmall?1:S(e,n)},i.prototype.compareAbs=function(t){var r=B(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=B(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},a.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=B(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:S(e,n)*(this.sign?-1:1)},a.prototype.compareTo=a.prototype.compare,i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=B(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},i.prototype.compareTo=i.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=B(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,a.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=i.prototype.eq=i.prototype.equals=a.prototype.eq=a.prototype.equals,a.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=i.prototype.neq=i.prototype.notEquals=a.prototype.neq=a.prototype.notEquals,a.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=i.prototype.gt=i.prototype.greater=a.prototype.gt=a.prototype.greater,a.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=i.prototype.lt=i.prototype.lesser=a.prototype.lt=a.prototype.lesser,a.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals=a.prototype.geq=a.prototype.greaterOrEquals,a.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals=a.prototype.leq=a.prototype.lesserOrEquals,a.prototype.isEven=function(){return 0==(1&this.value[0])},i.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},a.prototype.isOdd=function(){return 1==(1&this.value[0])},i.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},a.prototype.isPositive=function(){return!this.sign},i.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=i.prototype.isPositive,a.prototype.isNegative=function(){return this.sign},i.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=i.prototype.isNegative,a.prototype.isUnit=function(){return!1},i.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},a.prototype.isZero=function(){return!1},i.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},a.prototype.isDivisibleBy=function(t){var r=B(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=i.prototype.isDivisibleBy=a.prototype.isDivisibleBy,a.prototype.isPrime=function(t){var r=U(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return _(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var s=Math.log(2)*n.toJSNumber(),u=Math.ceil(!0===t?2*Math.pow(s,2):s),a=[],i=0;i<u;i++)a.push(o(i+2));return _(e,a)},c.prototype.isPrime=i.prototype.isPrime=a.prototype.isPrime,a.prototype.isProbablePrime=function(t,r){var e=U(this);if(void 0!==e)return e;for(var n=this.abs(),s=void 0===t?5:t,u=[],a=0;a<s;a++)u.push(o.randBetween(2,n.minus(2),r));return _(n,u)},c.prototype.isProbablePrime=i.prototype.isProbablePrime=a.prototype.isProbablePrime,a.prototype.modInv=function(t){for(var r,e,n,s=o.zero,u=o.one,a=B(t),i=this.abs();!i.isZero();)r=a.divide(i),e=s,n=a,s=u,a=i,u=e.subtract(r.multiply(u)),i=n.subtract(r.multiply(i));if(!a.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===s.compare(0)&&(s=s.add(t)),this.isNegative()?s.negate():s},c.prototype.modInv=i.prototype.modInv=a.prototype.modInv,a.prototype.next=function(){var t=this.value;return this.sign?L(t,1,this.sign):new a(w(t,1),this.sign)},i.prototype.next=function(){var t=this.value;return t+1<e?new i(t+1):new a(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},a.prototype.prev=function(){var t=this.value;return this.sign?new a(w(t,1),!0):L(t,1,this.sign)},i.prototype.prev=function(){var t=this.value;return t-1>-e?new i(t-1):new a(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=r;)x.push(2*x[x.length-1]);var I=x.length,N=x[I-1];function R(t){return Math.abs(t)<=r}function k(t,r,e){r=B(r);for(var n=t.isNegative(),s=r.isNegative(),u=n?t.not():t,a=s?r.not():r,i=0,c=0,l=null,f=null,p=[];!u.isZero()||!a.isZero();)i=(l=T(u,N))[1].toJSNumber(),n&&(i=N-1-i),c=(f=T(a,N))[1].toJSNumber(),s&&(c=N-1-c),u=l[0],a=f[0],p.push(e(i,c));for(var b=0!==e(n?1:0,s?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(N).add(o(p[v]));return b}a.prototype.shiftLeft=function(t){var r=B(t).toJSNumber();if(!R(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=I;)e=e.multiply(N),r-=I-1;return e.multiply(x[r])},c.prototype.shiftLeft=i.prototype.shiftLeft=a.prototype.shiftLeft,a.prototype.shiftRight=function(t){var r,e=B(t).toJSNumber();if(!R(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=I;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=T(n,N))[1].isNegative()?r[0].prev():r[0],e-=I-1}return(r=T(n,x[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=i.prototype.shiftRight=a.prototype.shiftRight,a.prototype.not=function(){return this.negate().prev()},c.prototype.not=i.prototype.not=a.prototype.not,a.prototype.and=function(t){return k(this,t,(function(t,r){return t&r}))},c.prototype.and=i.prototype.and=a.prototype.and,a.prototype.or=function(t){return k(this,t,(function(t,r){return t|r}))},c.prototype.or=i.prototype.or=a.prototype.or,a.prototype.xor=function(t){return k(this,t,(function(t,r){return t^r}))},c.prototype.xor=i.prototype.xor=a.prototype.xor;function M(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function V(t,r){return t=B(t),r=B(r),t.greater(r)?t:r}function q(t,r){return t=B(t),r=B(r),t.lesser(r)?t:r}function P(t,r){if(t=B(t).abs(),r=B(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=u[1];t.isEven()&&r.isEven();)e=q(M(t),M(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(M(t));do{for(;r.isEven();)r=r.divide(M(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}a.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),s=n.p,u=n.e,a=s.multiply(e);return a.compareTo(r)<=0?{p:a,e:2*u+1}:{p:s,e:2*u}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=i.prototype.bitLength=a.prototype.bitLength;var D=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,s=t.length,u=Math.abs(r),a={};for(o=0;o<e.length;o++)a[e[o]]=o;for(o=0;o<s;o++){if("-"!==(l=t[o])&&(l in a&&a[l]>=u)){if("1"===l&&1===u)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=B(r);var i=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in a)i.push(B(a[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);i.push(B(t.slice(f+1,o)))}}return G(i,r,c)};function G(t,r,e){var n,o=u[0],s=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(s)),s=s.times(r);return e?o.negate():o}function Q(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var s,u=[],a=t;a.isNegative()||a.compareAbs(r)>=0;){s=a.divmod(r),a=s.quotient;var i=s.remainder;i.isNegative()&&(i=r.minus(i).abs(),a=a.next()),u.push(i.toJSNumber())}return u.push(a.toJSNumber()),{value:u.reverse(),isNegative:n}}function F(t,r,e){var n=Q(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function $(t){if(l(+t)){var r=+t;if(r===y(r))return s?new c(BigInt(r)):new i(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==y(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var u=n[0],f=u.indexOf(".");if(f>=0&&(o-=u.length-f-1,u=u.slice(0,f)+u.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(s)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new a(p,e)}function B(t){return"number"==typeof t?function(t){if(s)return new c(BigInt(t));if(l(t)){if(t!==y(t))throw new Error(t+" is not an integer.");return new i(t)}return $(t.toString())}(t):"string"==typeof t?$(t):"bigint"==typeof t?new c(t):t}a.prototype.toArray=function(t){return Q(this,t)},i.prototype.toArray=function(t){return Q(this,t)},c.prototype.toArray=function(t){return Q(this,t)},a.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return F(this,t,r);for(var e,n=this.value,o=n.length,s=String(n[--o]);--o>=0;)e=String(n[o]),s+="0000000".slice(e.length)+e;return(this.sign?"-":"")+s},i.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?F(this,t,r):String(this.value)},c.prototype.toString=i.prototype.toString,c.prototype.toJSON=a.prototype.toJSON=i.prototype.toJSON=function(){return this.toString()},a.prototype.valueOf=function(){return parseInt(this.toString(),10)},a.prototype.toJSNumber=a.prototype.valueOf,i.prototype.valueOf=function(){return this.value},i.prototype.toJSNumber=i.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var H=0;H<1e3;H++)u[H]=B(H),H>0&&(u[-H]=B(-H));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=V,u.min=q,u.gcd=P,u.lcm=function(t,r){return t=B(t).abs(),r=B(r).abs(),t.divide(P(t,r)).multiply(r)},u.isInstance=function(t){return t instanceof a||t instanceof i||t instanceof c},u.randBetween=function(t,e,n){t=B(t),e=B(e);var o=n||Math.random,s=q(t,e),a=V(t,e).subtract(s).add(1);if(a.isSmall)return s.add(Math.floor(o()*a));for(var i=Q(a,r).value,c=[],l=!0,f=0;f<i.length;f++){var p=l?i[f]+(f+1<i.length?i[f+1]/r:0):r,b=y(o()*p);c.push(b),b<i[f]&&(l=!1)}return s.add(u.fromArray(c,r,!1))},u.fromArray=function(t,r,e){return G(t.map(B),B(r||10),e)},u}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(30)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=postgresql.umd.js.map