!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=26)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return E})),e.d(r,"autoIncrementToSQL",(function(){return U})),e.d(r,"columnOrderListToSQL",(function(){return I})),e.d(r,"commonKeywordArgsToSQL",(function(){return _})),e.d(r,"commonOptionConnector",(function(){return i})),e.d(r,"connector",(function(){return c})),e.d(r,"commonTypeValue",(function(){return L})),e.d(r,"commentToSQL",(function(){return A})),e.d(r,"createBinaryExpr",(function(){return f})),e.d(r,"createValueExpr",(function(){return l})),e.d(r,"dataTypeToSQL",(function(){return g})),e.d(r,"DEFAULT_OPT",(function(){return s})),e.d(r,"escape",(function(){return p})),e.d(r,"literalToSQL",(function(){return O})),e.d(r,"columnIdentifierToSql",(function(){return y})),e.d(r,"getParserOpt",(function(){return b})),e.d(r,"identifierToSql",(function(){return h})),e.d(r,"onPartitionsToSQL",(function(){return C})),e.d(r,"replaceParams",(function(){return j})),e.d(r,"returningToSQL",(function(){return S})),e.d(r,"hasVal",(function(){return w})),e.d(r,"setParserOpt",(function(){return v})),e.d(r,"toUpper",(function(){return m})),e.d(r,"topToSQL",(function(){return d})),e.d(r,"triggerEventToSQL",(function(){return T}));var n=e(2),o=e(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s={database:"mariadb",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},u=s;function i(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function c(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function l(t){var r=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function f(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:l(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(e[0]),l(e[1])]},n):(n.right=e.type?e:l(e),n)}function p(t){return t}function b(){return u}function v(t){u=t}function d(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(t){var r=b().database;if(t)switch(r&&r.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function h(t,r){var e=b().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function w(t){return t}function O(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,s=t.suffix,u=t.value,i="object"===a(t)?u:t;switch(e){case"backticks_quote_string":i="`".concat(u,"`");break;case"string":i="'".concat(u,"'");break;case"regex_string":i='r"'.concat(u,'"');break;case"hex_string":i="X'".concat(u,"'");break;case"full_hex_string":i="0x".concat(u);break;case"natural_string":i="N'".concat(u,"'");break;case"bit_string":i="b'".concat(u,"'");break;case"double_quote_string":i='"'.concat(u,'"');break;case"single_quote_string":i="'".concat(u,"'");break;case"boolean":case"bool":i=u?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(r||":").concat(u),r=null;break;case"origin":i=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":i="N'".concat(u,"'");break;case"unicode_string":i="U&'".concat(u,"'")}var c=[];return r&&c.push(m(r)),c.push(i),s&&("string"==typeof s&&c.push(s),"object"===a(s)&&(s.collate?c.push(Object(o.a)(s.collate)):c.push(O(s)))),i=c.join(" "),n?"(".concat(i,")"):i}}function L(t){if(!t)return[];var r=t.type,e=t.symbol,n=t.value;return[r.toUpperCase(),e,"string"==typeof n?n.toUpperCase():O(n)].filter(w)}function j(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===a(e)&&null!==e})).forEach((function(n){var o=r[n];if("object"!==a(o)||"param"!==o.type)return t(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return r[n]=l(e[o.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function C(t){var r=t.type,e=t.partitions;return[m(r),"(".concat(e.map((function(t){if("range"!==t.type)return O(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(O(r)," ").concat(m(n)," ").concat(O(e))})).join(", "),")")].join(" ")}function g(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,a=t.suffix,s="";return null!=e&&(s=o?"".concat(e,", ").concat(o):e),n&&(s="(".concat(s,")")),a&&a.length&&(s+=" ".concat(a.join(" "))),"".concat(r).concat(s)}function E(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=m(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(t){return[t.field_name,E(t.field_type)].filter(w).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function A(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(O(o)),r.join(" ")}}function T(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[m(r)];if(e){var a=e.keyword,s=e.columns;o.push(m(a),s.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function S(t){return t?["RETURNING",t.columns.map(n.h).filter(w).join(", ")].join(" "):""}function _(t){return t?[m(t.keyword),m(t.args)]:[]}function U(t){if(t){if("string"==typeof t){var r=b().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,s=m(e);return a&&(s+="(".concat(O(n),", ").concat(O(o),")")),s}}function I(t){if(t)return t.map(n.e).filter(w).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return C})),e.d(r,"b",(function(){return g})),e.d(r,"d",(function(){return j})),e.d(r,"c",(function(){return E}));var n=e(0),o=e(9),a=e(13);var s=e(22),u=e(21);var i=e(11),c=e(2),l=e(6),f=e(18);var p=e(7),b=e(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(c.i)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(r),"]");var s=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return e?"[".concat(s,"]"):a?"(".concat(s,")"):s}(t);default:return""}}function y(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===v(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(d(t)),o.filter(n.hasVal).join("")}var h=e(3),m=e(5),w=e(20);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var L={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,s=t.within_group_orderby,u=C(r.expr);u=Array.isArray(u)?u.join(", "):u;var i=t.name,c=Object(a.a)(o);r.distinct&&(u=["DISTINCT",u].join(" ")),r.separator&&r.separator.delimiter&&(u=[u,Object(n.literalToSQL)(r.separator.delimiter)].join("".concat(r.separator.symbol," "))),r.separator&&r.separator.expr&&(u=[u,C(r.separator.expr)].join(" ")),r.orderby&&(u=[u,E(r.orderby,"order by")].join(" ")),r.separator&&r.separator.value&&(u=[u,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=s?"WITHIN GROUP (".concat(E(s,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(C(e.where),")"):"";return["".concat(i,"(").concat(u,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:w.c,array:y,assign:s.a,binary_expr:u.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(C(n));for(var a=0,s=e.length;a<s;++a)r.push(e[a].type.toUpperCase()),e[a].cond&&(r.push(C(e[a].cond)),r.push("THEN")),r.push(C(e[a].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:l.c,collate:i.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:y,tablefunc:l.j,tables:h.c,unnest:h.d,window:w.b};function j(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,a=t.members,s=t.quoted,u=t.suffix,i=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(e||"").concat(c);return u&&(l+=u),i.push(l),[s,i.join(" "),s].filter(n.hasVal).join("")}function C(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,a=Object.keys(e);o<a.length;o++){var s=a[o];r[s]=e[s]}}var u=r.type;return"expr"===u?C(r.expr):L[u]?L[u](r):Object(n.literalToSQL)(r)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function E(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,e.join(", "))}L.var=j,L.expr_list=function(t){var r=g(t.value),e=t.parentheses,n=t.separator;if(!e&&!n)return r;var o=n||", ",a=r.join(o);return e?"(".concat(a,")"):a},L.select=function(t){var r="object"===O(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(r,")"):r},L.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",a="".concat(r).concat(o).concat(C(n));return e?"(".concat(a,")"):a},L.map_object=function(t){var r=t.keyword,e=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(r),"[".concat(e,"]")].join("")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"c",(function(){return O})),e.d(r,"f",(function(){return d})),e.d(r,"h",(function(){return C})),e.d(r,"i",(function(){return E})),e.d(r,"b",(function(){return y})),e.d(r,"d",(function(){return b})),e.d(r,"e",(function(){return w})),e.d(r,"g",(function(){return h})),e.d(r,"j",(function(){return j})),e.d(r,"k",(function(){return g}));var n=e(11),o=e(19),a=e(1),s=e(6),u=e(3),i=e(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function b(t,r){if("string"==typeof t)return Object(i.identifierToSql)(t,r);var e=t.expr,n=t.offset,o=t.suffix,s=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(a.a)(e),s,o].filter(i.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var r,e=[],n=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=f(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}(t);try{for(n.s();!(r=n.n()).done;){var o=r.value,a=o.brackets?"[".concat(Object(i.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(i.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(i.literalToSQL)(o.property))),e.push(a)}}catch(t){n.e(t)}finally{n.f()}return e.join("")}function d(t){var r=t.array_index,e=t.as,o=t.column,s=t.collate,u=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,d=t.options,y=t.schema,h=t.table,m=t.parentheses,w=t.suffix,O=t.order_by,L=t.subFields,j=void 0===L?[]:L,C="*"===o?"*":b(o,c),g=[u,y,h].filter(i.hasVal).map((function(t){return"".concat("string"==typeof t?Object(i.identifierToSql)(t):Object(a.a)(t))})),E=g[0];if(E){for(var A=1;A<g.length;++A)E="".concat(E).concat(p[A]||".").concat(g[A]);C="".concat(E).concat(p[A]||".").concat(C)}var T=[C=["".concat(C).concat(v(r))].concat(l(j)).join("."),Object(n.a)(s),Object(a.a)(d),Object(i.commonOptionConnector)("AS",a.a,e)];T.push("string"==typeof w?Object(i.toUpper)(w):Object(a.a)(w)),T.push(Object(i.toUpper)(O));var S=T.filter(i.hasVal).join(" ");return m?"(".concat(S,")"):S}function y(t){if(t){var r=t.dataType,e=t.length,n=t.suffix,o=t.scale,u=t.expr,c=null!=e,l=Object(i.dataTypeToSQL)({dataType:r,length:e,suffix:n,scale:o,parentheses:c});if(u&&(l+=Object(a.a)(u)),t.array){var f=Object(s.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function h(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,o=t.match,s=t.table,c=t.on_action;return r.push(Object(i.toUpper)(n)),r.push(Object(u.c)(s)),r.push(e&&"(".concat(e.map((function(t){return Object(a.a)(t)})).join(", "),")")),r.push(Object(i.toUpper)(o)),c.map((function(t){return r.push(Object(i.toUpper)(t.type),Object(a.a)(t.value))})),r.filter(i.hasVal)}function m(t){var r=[],e=t.nullable,n=t.character_set,s=t.check,u=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,d=t.generated,y=t.auto_increment,m=t.unique,w=t.primary_key,O=t.column_format,L=t.reference_definition,j=[Object(i.toUpper)(e&&e.action),Object(i.toUpper)(e&&e.value)].filter(i.hasVal).join(" ");if(d||r.push(j),v){var C=v.type,g=v.value;r.push(C.toUpperCase(),Object(a.a)(g))}var E=Object(i.getParserOpt)().database;return c&&r.push(Object(i.toUpper)(c.keyword),Object(i.literalToSQL)(c.constraint)),r.push(Object(o.a)(s)),r.push(function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(d)),d&&r.push(j),r.push(Object(i.autoIncrementToSQL)(y),Object(i.toUpper)(w),Object(i.toUpper)(m),Object(i.commentToSQL)(u)),r.push.apply(r,l(Object(i.commonTypeValue)(n))),"sqlite"!==E.toLowerCase()&&r.push(Object(a.a)(f)),r.push.apply(r,l(Object(i.commonTypeValue)(O))),r.push.apply(r,l(Object(i.commonTypeValue)(p))),r.push.apply(r,l(h(L))),r.push(Object(i.commonOptionConnector)("USING",a.a,b)),r.filter(i.hasVal).join(" ")}function w(t){var r=t.column,e=t.collate,n=t.nulls,o=t.opclass,s=t.order_by,u="string"==typeof r?{type:"column_ref",table:t.table,column:r}:t;return u.collate=null,[Object(a.a)(u),Object(a.a)(e),o,Object(i.toUpper)(s),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function O(t){var r=[],e=d(t.column),n=y(t.definition);return r.push(e),r.push(n),r.push(m(t)),r.filter(i.hasVal).join(" ")}function L(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function j(t){var r=t.against,e=t.as,n=t.columns,o=t.match,s=t.mode;return[[Object(i.toUpper)(o),"(".concat(n.map((function(t){return d(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(r),["(",Object(a.a)(t.expr),s&&" ".concat(Object(i.literalToSQL)(s)),")"].filter(i.hasVal).join("")].join(" "),L(e)].filter(i.hasVal).join(" ")}function C(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(s.c)(t);r&&(e.isDual=r);var o=Object(a.a)(e),u=t.expr_list;if(u){var c=[o],l=u.map((function(t){return C(t,r)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(v(e.array_index))),[o,L(t.as)].filter(i.hasVal).join(" ")}function g(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}function E(t,r){if(!t||"*"===t)return t;var e=g(r);return t.map((function(t){return C(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"c",(function(){return h})),e.d(r,"a",(function(){return m})),e.d(r,"b",(function(){return y})),e.d(r,"d",(function(){return f}));var n=e(21),o=e(2),a=e(1),s=e(17),u=e(18),i=e(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(i.toUpper)(r),"(").concat(n&&Object(a.a)(n)||"",")"),Object(i.commonOptionConnector)("AS","string"==typeof e?i.identifierToSql:a.a,e),Object(i.commonOptionConnector)(Object(i.toUpper)(o&&o.keyword),i.identifierToSql,o&&o.as)].filter(i.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,s=t.expr,u=t.in_expr,c=t.type,l=[Object(a.a)(s),"FOR",Object(o.f)(e),Object(n.a)(u)],f=["".concat(Object(i.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(i.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function b(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,s=t.parentheses,u=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(i.toUpper)(r),"(".concat(Object(i.identifierToSql)(n)),"(".concat(o.map(a.a).filter(i.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(i.toUpper)(r),"=",Object(a.a)(e));break;case"index":c.push(Object(i.toUpper)(u),Object(i.toUpper)(r),s?"(".concat(e.map(i.identifierToSql).join(", "),")"):"= ".concat(Object(i.identifierToSql)(e)));break;default:c.push(Object(a.a)(e))}return c.filter(i.hasVal).join(" ")}}function v(t,r){var e=t.name,n=t.symbol;return[Object(i.toUpper)(e),n,r].filter(i.hasVal).join(" ")}function d(t){var r=[];switch(t.keyword){case"as":r.push("AS","OF",Object(a.a)(t.of));break;case"from_to":r.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":r.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":r.push("CONTAINED","IN",Object(a.a)(t.in))}return r.filter(i.hasVal).join(" ")}function y(t){if("UNNEST"===Object(i.toUpper)(t.type))return f(t);var r,e,n,c,l=t.table,y=t.db,h=t.as,m=t.expr,w=t.operator,O=t.prefix,L=t.schema,j=t.server,C=t.suffix,g=t.tablesample,E=t.temporal_table,A=t.table_hint,T=Object(i.identifierToSql)(j),S=Object(i.identifierToSql)(y),_=Object(i.identifierToSql)(L),U=l&&Object(i.identifierToSql)(l);if(m)switch(m.type){case"values":var I=m.parentheses,x=m.values,N=m.prefix,k=[I&&"(","",I&&")"],R=Object(s.b)(x);N&&(R=R.split("(").slice(1).map((function(t){return"".concat(Object(i.toUpper)(N),"(").concat(t)})).join("")),k[1]="VALUES ".concat(R),U=k.filter(i.hasVal).join("");break;case"tumble":U=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.offset,a=t.size,s=[Object(i.identifierToSql)(r.expr.db),Object(i.identifierToSql)(r.expr.schema),Object(i.identifierToSql)(r.expr.table)].filter(i.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(e.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(r,s)),v(e,c)],f=v(a,Object(u.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(u.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(i.hasVal).join(", ")}(m);break;case"generator":e=(r=m).keyword,n=r.type,c=r.generators.map((function(t){return Object(i.commonTypeValue)(t).join(" ")})).join(", "),U="".concat(Object(i.toUpper)(e),"(").concat(Object(i.toUpper)(n),"(").concat(c,"))");break;default:U=Object(a.a)(m)}var M=[[T,S,_,U=[Object(i.toUpper)(O),U,Object(i.toUpper)(C)].filter(i.hasVal).join(" ")].filter(i.hasVal).join(".")];if(g){var V=["TABLESAMPLE",Object(a.a)(g.expr),Object(i.literalToSQL)(g.repeatable)].filter(i.hasVal).join(" ");M.push(V)}M.push(function(t){if(t){var r=t.keyword,e=t.expr;return[Object(i.toUpper)(r),d(e)].filter(i.hasVal).join(" ")}}(E),Object(i.commonOptionConnector)("AS","string"==typeof h?i.identifierToSql:a.a,h),p(w)),A&&M.push(Object(i.toUpper)(A.keyword),"(".concat(A.expr.map(b).filter(i.hasVal).join(", "),")"));var D=M.filter(i.hasVal).join(" ");return t.parentheses?"(".concat(D,")"):D}function h(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=t.joins,o=h(r);if(e){for(var s=[],u=[],l=!0===e?1:e.length,f=0;f++<l;)s.push("("),u.push(")");var p=n&&n.length>0?h([""].concat(c(n))):"";return s.join("")+o+u.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(y(b));for(var d=1;d<t.length;++d){var m=t[d],w=m.on,O=m.using,L=m.join,j=[];j.push(L?" ".concat(Object(i.toUpper)(L)):","),j.push(y(m)),j.push(Object(i.commonOptionConnector)("ON",a.a,w)),O&&j.push("USING (".concat(O.map(i.literalToSQL).join(", "),")")),v.push(j.filter(i.hasVal).join(" "))}return v.filter(i.hasVal).join("")}function m(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var s=Object(i.literalToSQL)(n);switch(r){case"partition by":case"default collate":s=Object(a.a)(n);break;case"options":s="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":s=n.map(a.a).join(", ")}return o.push(s),o.filter(i.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return d})),e.d(r,"c",(function(){return g})),e.d(r,"d",(function(){return E})),e.d(r,"e",(function(){return y})),e.d(r,"f",(function(){return h})),e.d(r,"g",(function(){return m})),e.d(r,"h",(function(){return S})),e.d(r,"i",(function(){return T})),e.d(r,"j",(function(){return A})),e.d(r,"l",(function(){return w})),e.d(r,"m",(function(){return O})),e.d(r,"o",(function(){return L})),e.d(r,"n",(function(){return j})),e.d(r,"k",(function(){return C}));var n=e(2),o=e(14),a=e(0),s=e(1),u=e(3),i=e(16),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(s.a)(t.expr);return"".concat("CALL"," ").concat(r)}function d(t){var r=t.type,e=t.keyword,o=t.name,i=t.prefix,c=t.suffix,l=[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(a.toUpper)(i)];switch(e){case"table":l.push(Object(u.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(u.c)(o),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(u.b)(t.table)]:[]),[t.options&&t.options.map(s.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(s.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(s.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function y(t){var r=t.type,e=t.table,n=Object(a.toUpper)(r);return"".concat(n," ").concat(Object(a.identifierToSql)(e))}function h(t){var r=t.type,e=t.name,n=t.args,o=[Object(a.toUpper)(r)],u=[e];return n&&u.push("(".concat(Object(s.a)(n).join(", "),")")),o.push(u.join("")),o.filter(a.hasVal).join(" ")}function m(t){var r=t.type,e=t.label,n=t.target,o=t.query,s=t.stmts;return[e,Object(a.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(s),"END LOOP",e].filter(a.hasVal).join(" ")}function w(t){var r=t.type,e=t.level,n=t.raise,o=t.using,u=[Object(a.toUpper)(r),Object(a.toUpper)(e)];return n&&u.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(s.a)(t)})).join(", ")),o&&u.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(s.a)(t)})).join(", ")),u.filter(a.hasVal).join(" ")}function O(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var a,s=l(e);try{for(s.s();!(a=s.n()).done;){var i=a.value.map(u.b);n.push(i.join(" TO "))}}catch(t){s.e(t)}finally{s.f()}}return"".concat(o," ").concat(n.join(", "))}function L(t){var r=t.type,e=t.db,n=Object(a.toUpper)(r),o=Object(a.identifierToSql)(e);return"".concat(n," ").concat(o)}function j(t){var r=t.type,e=t.expr,n=t.keyword,o=Object(a.toUpper)(r),u=e.map(s.a).join(", ");return[o,Object(a.toUpper)(n),u].filter(a.hasVal).join(" ")}function C(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(a.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var s,i=[],c=l(n);try{var p=function(){var t=s.value,r=t.table,e=t.lock_type,n=[Object(u.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(e[t])})).filter(a.hasVal).join(" "))}i.push(n.join(" "))};for(c.s();!(s=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[i.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(s.a)(n)].filter(a.hasVal).join(" ")}function E(t){var r=t.type,e=t.declare,u=t.symbol,i=[Object(a.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,u=t.as,i=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[r,e].filter(a.hasVal).join(""),Object(a.toUpper)(u),Object(a.toUpper)(i)];switch(b){case"variable":v.push(Object(n.b)(c),Object(s.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(s.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(u," "));return i.push(c),i.join(" ")}function A(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,u=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(s.a)(r),Object(a.literalToSQL)(u),"".concat(Object(i.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(s.a)(t.boolean_expr),"THEN",Object(i.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(i.a)(e.ast||e)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function T(t){var r=t.name,e=t.host,n=[Object(a.literalToSQL)(r)];return e&&n.push("@",Object(a.literalToSQL)(e)),n.join("")}function S(t){var r=t.type,e=t.grant_option_for,o=t.keyword,u=t.objects,i=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(r),Object(a.literalToSQL)(e)],b=u.map((function(t){var r=t.priv,e=t.columns,o=[Object(s.a)(r)];return e&&o.push("(".concat(e.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),i)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(i.object_type),i.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(i))}return p.push(Object(a.toUpper)(c),l.map(T).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return L})),e.d(r,"a",(function(){return j}));var n=e(9),o=e(1),a=e(3),s=e(0);var u=e(14),i=e(2);function c(t){var r=t.name,e=t.type;switch(e){case"table":case"view":var n=[Object(s.identifierToSql)(r.db),Object(s.identifierToSql)(r.table)].filter(s.hasVal).join(".");return"".concat(Object(s.toUpper)(e)," ").concat(n);case"column":return"COLUMN ".concat(Object(i.f)(r));default:return"".concat(Object(s.toUpper)(e)," ").concat(Object(s.literalToSQL)(r))}}function l(t){var r=t.keyword,e=t.expr;return[Object(s.toUpper)(r),Object(s.literalToSQL)(e)].filter(s.hasVal).join(" ")}var f=e(7);var p=e(8),b=e(15);var v=e(12),d=e(17),y=e(4);function h(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(s.hasVal).join(" ")}var m=e(22);var w=e(23),O={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(s.toUpper)(r),Object(a.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,a=t.as,u=t.schema;return[Object(s.toUpper)(r),Object(s.toUpper)(e),Object(o.a)(n),Object(s.toUpper)(a),Object(s.identifierToSql)(u)].filter(s.hasVal).join(" ")},create:u.b,comment:function(t){var r=t.expr,e=t.keyword,n=t.target,o=t.type;return[Object(s.toUpper)(o),Object(s.toUpper)(e),c(n),l(r)].filter(s.hasVal).join(" ")},select:f.a,deallocate:y.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,u=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,d=[Object(b.a)(l),"DELETE"],y=Object(i.i)(r,e);return d.push(y),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||d.push(Object(a.c)(n))),d.push(Object(s.commonOptionConnector)("FROM",a.c,e)),d.push(Object(s.commonOptionConnector)("WHERE",o.a,u)),d.push(Object(o.c)(c,"order by")),d.push(Object(p.a)(f)),d.push(Object(s.returningToSQL)(v)),d.filter(s.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(s.toUpper)(r),Object(a.b)(e),(n||[]).map(h).filter(s.hasVal).join(", ")].filter(s.hasVal).join(" ")},execute:y.f,explain:function(t){var r=t.type,e=t.expr;return[Object(s.toUpper)(r),Object(f.a)(e)].join(" ")},for:y.g,update:v.b,if:y.j,insert:d.a,drop:y.b,truncate:y.b,replace:d.a,declare:y.d,use:y.o,rename:y.m,call:y.a,desc:y.e,set:y.n,lock:y.k,unlock:y.k,show:w.a,grant:y.h,revoke:y.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(m.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(s.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:y.l,transaction:function(t){var r=t.expr,e=r.action,n=r.keyword,o=r.modes,a=[Object(s.literalToSQL)(e),Object(s.toUpper)(n)];return o&&a.push(o.map(s.literalToSQL).join(", ")),a.filter(s.hasVal).join(" ")}};function L(t){if(!t)return"";for(var r=O[t.type],e=t,n=e._parentheses,a=e._orderby,u=e._limit,i=[n&&"(",r(t)];t._next;){var c=O[t._next.type],l=Object(s.toUpper)(t.set_op);i.push(l,c(t._next)),t=t._next}return i.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(u)),i.filter(s.hasVal).join(" ")}function j(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],a=L(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),r.push(a)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return i})),e.d(r,"c",(function(){return c})),e.d(r,"d",(function(){return l})),e.d(r,"e",(function(){return p})),e.d(r,"f",(function(){return b})),e.d(r,"g",(function(){return v})),e.d(r,"h",(function(){return f})),e.d(r,"i",(function(){return y})),e.d(r,"j",(function(){return d}));var n=e(2),o=e(1),a=e(0),s=e(13);function u(t){var r=t.args,e=t.type,n=t.over,u=r.expr,i=r.having,c="".concat(Object(a.toUpper)(e),"(").concat(Object(o.a)(u));return i&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(i.prefix)," ").concat(Object(o.a)(i.expr))),[c="".concat(c,")"),Object(s.a)(n)].filter(a.hasVal).join(" ")}function i(t){if(!t||!t.array)return"";var r=t.array.keyword;if(r)return Object(a.toUpper)(r);for(var e=t.array,n=e.dimension,o=e.length,s=[],u=0;u<n;u++)s.push("["),o&&o[u]&&s.push(Object(a.literalToSQL)(o[u])),s.push("]");return s.join("")}function c(t){for(var r=t.target,e=t.expr,s=t.keyword,u=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:e,offset:l}),b=[],v=0,d=r.length;v<d;++v){var y=r[v],h=y.angle_brackets,m=y.length,w=y.dataType,O=y.parentheses,L=y.quoted,j=y.scale,C=y.suffix,g=y.expr,E=g?Object(o.a)(g):"";null!=m&&(E=j?"".concat(m,", ").concat(j):m),O&&(E="(".concat(E,")")),h&&(E="<".concat(E,">")),C&&C.length&&(E+=" ".concat(C.map(a.literalToSQL).join(" ")));var A="::",T="",S=[];"as"===u&&(0===v&&(p="".concat(Object(a.toUpper)(s),"(").concat(p)),T=")",A=" ".concat(u.toUpperCase()," ")),0===v&&S.push(p);var _=i(y);S.push(A,L,w,L,_,E,T),b.push(S.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var U=b.filter(a.hasVal).join("");return f?"(".concat(U,")"):U}function l(t){var r=t.args,e=t.type,n=r.field,s=r.cast_type,u=r.source,i=["".concat(Object(a.toUpper)(e),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(s),Object(o.a)(u)];return"".concat(i.filter(a.hasVal).join(" "),")")}function f(t){var r=t.expr,e=r.key,n=r.value,s=r.on,u=[Object(o.a)(e),"VALUE",Object(o.a)(n)];return s&&u.push("ON","NULL",Object(o.a)(s)),u.filter(a.hasVal).join(" ")}function p(t){var r=t.args,e=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,n=t.value;return[Object(a.toUpper)(r),e,Object(o.a)(n)].filter(a.hasVal).join(" ")}(r[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(e),"(").concat(n,")")}function b(t){var r=t.value,e=r.name,n=r.symbol,s=r.expr;return[e,n,Object(o.a)(s)].filter(a.hasVal).join(" ")}function v(t){var r=t.args,e=t.array_index,u=t.name,i=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(s.a)(f),v=function(t){if(!t)return"";var r=t.type,e=t.keyword,n=t.orderby;return[Object(a.toUpper)(r),Object(a.toUpper)(e),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),d=Object(o.a)(p),y=[Object(a.literalToSQL)(u.schema),u.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!r)return[y,v,b].filter(a.hasVal).join(" ");var h=t.separator||", ";"TRIM"===Object(a.toUpper)(y)&&(h=" ");var m=[y];m.push(!1===i?" ":"(");var w=Object(o.a)(r);if(Array.isArray(h)){for(var O=w[0],L=1,j=w.length;L<j;++L)O=[O,w[L]].join(" ".concat(Object(o.a)(h[L-1])," "));m.push(O)}else m.push(w.join(h));return!1!==i&&m.push(")"),m.push(Object(n.a)(e)),m=[m.join(""),d].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function d(t){var r=t.as,e=t.name,n=t.args,s=[Object(a.literalToSQL)(e.schema),e.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(s,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(r)].join(" ")}function y(t){var r=t.args,e=t.expr,n=r.value,a=r.parentheses,s=n.map(o.a).join(", ");return[a?"(".concat(s,")"):s,"->",Object(o.a)(e)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f}));var n=e(1),o=e(2),a=e(8),s=e(15),u=e(3),i=e(0),c=e(11);function l(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],a=Object(i.toUpper)(r);switch(a){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(a,"string"==typeof e?Object(i.identifierToSql)(e):Object(n.a)(e))}return o.filter(i.hasVal).join(" ")}}function f(t){var r=t.as_struct_val,e=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,d=t.for_sys_time_as_of,y=void 0===d?{}:d,h=t.locking_read,m=t.groupby,w=t.having,O=t.into,L=void 0===O?{}:O,j=t.isolation,C=t.limit,g=t.options,E=t.orderby,A=t.parentheses_symbol,T=t.qualify,S=t.top,_=t.window,U=t.with,I=t.where,x=[Object(s.a)(U),"SELECT",Object(i.toUpper)(r)];Array.isArray(g)&&x.push(g.join(" ")),x.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,o=[Object(i.toUpper)(r)];return e&&o.push("(".concat(e.map(n.a).join(", "),")")),o.filter(i.hasVal).join(" ")}}(p),Object(i.topToSQL)(S),Object(o.i)(e,v));var N=L.position,k="";N&&(k=Object(i.commonOptionConnector)("INTO",l,L)),"column"===N&&x.push(k),x.push(Object(i.commonOptionConnector)("FROM",u.c,v)),"from"===N&&x.push(k);var R=y||{},M=R.keyword,V=R.expr;x.push(Object(i.commonOptionConnector)(M,n.a,V)),x.push(Object(i.commonOptionConnector)("WHERE",n.a,I)),m&&(x.push(Object(i.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),x.push(Object(n.b)(m.modifiers).join(", "))),x.push(Object(i.commonOptionConnector)("HAVING",n.a,w)),x.push(Object(i.commonOptionConnector)("QUALIFY",n.a,T)),x.push(Object(i.commonOptionConnector)("WINDOW",n.a,_)),x.push(Object(n.c)(E,"order by")),x.push(Object(c.a)(f)),x.push(Object(a.a)(C)),j&&x.push(Object(i.commonOptionConnector)(j.keyword,i.literalToSQL,j.expr)),x.push(Object(i.toUpper)(h)),"end"===N&&x.push(k),x.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,a=[Object(i.toUpper)(o),Object(i.toUpper)(e)];return r?"".concat(a.join(" "),"(").concat(Object(n.a)(r),")"):a.join(" ")}}(b));var D=x.filter(i.hasVal).join(" ");return A?"(".concat(D,")"):D}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return s(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?s(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function u(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function i(t){return t?t.fetch?(e=(r=t).fetch,s=r.offset,[].concat(a(u(s)),a(u(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,s}},function(t,r,e){"use strict";e.d(r,"a",(function(){return p})),e.d(r,"c",(function(){return b})),e.d(r,"b",(function(){return f}));var n=e(2),o=e(14),a=e(10),s=e(3),u=e(1),i=e(7),c=e(0);function l(t,r){switch(t){case"add":var e=r.map((function(t){var r=t.name,e=t.value;return["PARTITION",Object(c.literalToSQL)(r),"VALUES",Object(c.toUpper)(e.type),"(".concat(Object(c.literalToSQL)(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Object(n.i)(r)}}function f(t){if(!t)return"";var r=t.action,e=t.create_definitions,s=t.if_not_exists,u=t.keyword,i=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,d=t.suffix,y="",h=[];switch(b){case"column":h=[Object(n.c)(t)];break;case"index":h=Object(a.c)(t),y=t[b];break;case"table":case"schema":y=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":y=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":y=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":y=Object(c.identifierToSql)(t[b]),h=[Object(o.a)(e)];break;case"partition":h=[l(r,t.partitions)];break;case"key":y=Object(c.identifierToSql)(t[b]);break;default:y=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(r),Object(c.toUpper)(u),Object(c.toUpper)(s),Object(c.toUpper)(i),f&&Object(n.f)(f),Object(c.toUpper)(p),y&&y.trim(),h.filter(c.hasVal).join(" ")];return d&&m.push(Object(c.toUpper)(d.keyword),d.expr&&Object(n.f)(d.expr)),m.filter(c.hasVal).join(" ")}function p(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(u.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function b(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type,s=r.expr,u=r.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(s.map(p).join(", ")).concat(u?[" ORDER","BY",u.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.if_exists,o=t.prefix,a=t.expr,i=void 0===a?[]:a,l=Object(c.toUpper)(r),f=Object(s.c)(e),p=i.map(u.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),f(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,a=t.select,u=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(s.b)(u)];return e&&f.push("(".concat(e.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(i.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return u})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return s(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?s(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function u(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function i(t){if(t){var r=t.type,e=t.expr,o=t.symbol,s=r.toUpperCase(),i=[];switch(i.push(s),s){case"KEY_BLOCK_SIZE":o&&i.push(o),i.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":i.length=0,i.push.apply(i,a(u(t)));break;case"WITH PARSER":i.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":i.shift(),i.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":i.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:i.push(o,Object(n.literalToSQL)(e))}return i.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(i):[]}function l(t){var r=t.constraint_type,e=t.index_type,s=t.index_options,i=void 0===s?[]:s,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(u(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(i).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,a(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){if(t){var r=t.keyword,e=t.collate,a=e.name,s=e.symbol,u=e.value,i=[Object(o.toUpper)(r)];return u||i.push(s),i.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),u&&i.push(s),i.push(Object(n.a)(u)),i.filter(o.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return p})),e.d(r,"a",(function(){return f}));var n=e(3),o=e(1),a=e(2),s=e(8),u=e(0),i=e(15);function c(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){if(!t||0===t.length)return"";var r,e=[],n=c(t);try{for(n.s();!(r=n.n()).done;){var s=r.value,i={},l=s.value;for(var f in s)"value"!==f&&"keyword"!==f&&(i[f]=s[f]);var p=[Object(a.f)(i)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),e.push(p.filter(u.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function p(t){var r=t.from,e=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(e),Object(u.commonOptionConnector)("SET",f,a),Object(u.commonOptionConnector)("FROM",n.c,r),Object(u.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(s.a)(b),Object(u.returningToSQL)(v)].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(1),a=e(20);function s(t){if(t){var r=t.as_window_specification,e=t.expr,s=t.keyword,u=t.type,i=t.parentheses,c=Object(n.toUpper)(u);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(u)," ").concat(Object(n.toUpper)(s)),f=Object(o.a)(e)||[];return i&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return E})),e.d(r,"a",(function(){return h}));var n=e(9),o=e(1),a=e(10),s=e(2),u=e(4),i=e(19),c=e(6),l=e(3),f=e(12),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||d(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,r){if(t){if("string"==typeof t)return y(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?y(t,r):void 0}}function y(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function h(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(s.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(i.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function m(t){var r=[];switch(t.keyword){case"from":r.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":r.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":r.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return r.filter(b.hasVal).join(" ")}function w(t){var r=t.keyword,e=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(r),Object(l.b)(e),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function O(t){var r=t.as,e=t.domain,n=t.type,a=t.keyword,s=t.target,u=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(s)];if(u&&u.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=d(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}(u);try{for(p.s();!(l=p.n()).done;){var v=l.value,y=v.type;switch(y){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(y),Object(o.a)(v.value));break;case"constraint":f.push(Object(i.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function L(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function j(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(r),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=u.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(s.c).join(", "),")"):L(n)].filter(b.hasVal).join(" ")}(i),c.map(j).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var r=t.type,e=t.symbol,n=t.value,a=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function E(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,s=t.args,u=t.options,i=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(s.expr.map(n.a).join(", ")).concat(s.orderby?[" ORDER","BY",s.orderby.map(n.a).join(", ")].join(" "):"");return i.push("".concat(c,"(").concat(l,")"),"(".concat(u.map(g).join(", "),")")),i.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,a=t.as,s=t.temporary,u=t.if_not_exists,i=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,d=t.partition_of,y=t.query_expr,m=t.unlogged,O=t.with,L=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(m),Object(b.toUpper)(e),Object(b.toUpper)(u),Object(l.c)(n)];if(o){var j=o.type,C=o.table,g=Object(l.c)(C);return L.push(Object(b.toUpper)(j),g),L.filter(b.hasVal).join(" ")}if(d)return L.concat([w(d)]).filter(b.hasVal).join(" ");if(i&&L.push("(".concat(i.map(h).join(", "),")")),c){var E=Object(b.getParserOpt)().database,A=E&&"sqlite"===E.toLowerCase()?", ":" ";L.push(c.map(l.a).join(A))}if(O){var T=O.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");L.push("WITH (".concat(T,")"))}return L.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),y&&L.push(Object(p.b)(y)),L.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,a=t.events,s=t.execute,u=t.for_each,i=t.from,f=t.location,p=t.keyword,d=t.or,y=t.type,h=t.table,m=t.when,w=[Object(b.toUpper)(y),Object(b.toUpper)(d),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],O=Object(b.triggerEventToSQL)(a);return w.push(O,"ON",Object(l.b)(h)),i&&w.push("FROM",Object(l.b)(i)),w.push.apply(w,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(u)))),m&&w.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),w.push(Object(b.toUpper)(s.keyword),Object(c.g)(s.expr)),w.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,a=t.execute,u=t.type,i=t.table,c=t.if_not_exists,v=t.temporary,d=t.trigger,y=t.events,h=t.order,m=t.time,w=t.when,O=[Object(b.toUpper)(u),Object(b.toUpper)(v),Object(o.a)(r),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(d),Object(b.toUpper)(m),y.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(s.f).join(", ")),r.join(" ")})),"ON",Object(l.b)(i),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),h&&"".concat(Object(b.toUpper)(h.keyword)," ").concat(Object(b.identifierToSql)(h.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,w),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":O.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":O.push(Object(p.a)(a.expr.ast))}return O.push(Object(b.toUpper)(a.suffix)),O.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,s=t.type,u=t.with,i=t.version;return[Object(b.toUpper)(s),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(u),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,i),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=C(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,s=t.keyword,u=t.if_not_exists,i=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,d=t.index,y=t.on,h=t.index_options,m=t.algorithm_option,w=t.lock_option,O=t.on_kw,L=t.table,j=t.tablespace,C=t.type,g=t.where,E=t.with,A=t.with_before_where,T=E&&"WITH (".concat(Object(a.b)(E).join(", "),")"),S=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),_=d;d&&(_="string"==typeof d?Object(b.identifierToSql)(d):[Object(b.identifierToSql)(d.schema),Object(b.identifierToSql)(d.name)].filter(b.hasVal).join("."));var U=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(s),Object(b.toUpper)(u),Object(b.toUpper)(r),_,Object(b.toUpper)(O),Object(l.b)(L)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),S,Object(a.b)(h).join(" "),Object(n.b)(m),Object(n.b)(w),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,j)]);return A?U.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):U.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),U.push(Object(b.commonOptionConnector)("ON",o.a,y),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),U.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,s=t.create_definitions,u=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];return s&&u.push(s.map(h).join(" ")),u.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":e=function(t){var r=t.type,e=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,s=t[e],u=s.db,i=s.schema,c=[Object(b.literalToSQL)(u),i.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(r),Object(b.toUpper)(n),Object(b.toUpper)(e),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,a=t.if_not_exists,s=t.keyword,u=t.recursive,i=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,d=t.view,y=t.with,h=t.with_options,m=d.db,w=d.schema,O=d.view,L=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(w),Object(b.identifierToSql)(O)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(i),Object(b.toUpper)(f),Object(b.toUpper)(u),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(s),Object(b.toUpper)(a),L,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),h&&["WITH","(".concat(h.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(y)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=O(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,a=t.name,s=t.resource,u=t.type,i=[Object(b.toUpper)(u),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(s)];if(e){var c=[];switch(s){case"enum":case"range":c.push(Object(o.a)(e));break;default:c.push("(".concat(e.map(h).join(", "),")"))}i.push(c.filter(b.hasVal).join(" "))}return i.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,a=t.if_not_exists,s=t.keyword,i=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(u.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),d=[Object(b.toUpper)(p),Object(b.toUpper)(s),Object(b.toUpper)(a),v];return n&&d.push(Object(b.toUpper)(n.keyword),n.value.map(u.i).join(", ")),d.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&d.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return d.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),d.push(Object(b.literalToSQL)(i),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),d.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(2),o=e(1),a=e(0);function s(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,s=t.columns,u=Array.isArray(s)?"(".concat(s.map(n.f).join(", "),")"):"";return"".concat("default"===r.type?Object(a.identifierToSql)(r.value):Object(a.literalToSQL)(r)).concat(u," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function s(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function u(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[s(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):s(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(3),o=e(1),a=e(2),s=e(0),u=e(7),i=e(12);function c(t){if("select"===t.type)return Object(u.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(s.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(s.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,a=[Object(s.toUpper)(e)];switch(n){case"origin":a.push(Object(s.literalToSQL)(r));break;case"update":a.push("UPDATE",Object(s.commonOptionConnector)("SET",i.a,r.set),Object(s.commonOptionConnector)("WHERE",o.a,r.where))}return a.filter(s.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(s.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,a=t.prefix,u=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,d=t.where,y=t.on_duplicate_update,h=t.partition,m=t.returning,w=t.set,O=y||{},L=O.keyword,j=O.set,C=[Object(s.toUpper)(e),Object(s.toUpper)(u),Object(n.c)(r),l(h)];return Array.isArray(f)&&C.push("(".concat(f.map(s.literalToSQL).join(", "),")")),C.push(Object(s.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(s.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(s.commonOptionConnector)("SET",i.a,w)),C.push(Object(s.commonOptionConnector)("WHERE",o.a,d)),C.push(Object(s.commonOptionConnector)(L,i.a,j)),C.push(Object(s.returningToSQL)(m)),C.filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(0),o=e(1);function a(t){var r=t.expr,e=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(0),o=e(10),a=e(2);function s(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(t){var r=t.constraint,e=t.constraint_type,u=t.enforced,i=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(r));var d=Object(n.toUpper)(e);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===d&&(d="UNIQUE"),b.push(d),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(i)),b.push.apply(b,s(Object(o.c)(t))),b.push.apply(b,s(Object(a.g)(l))),b.push(Object(n.toUpper)(u)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),a=e(13);function s(t){if(t){var r=t.type;return"rows"===r?[Object(n.toUpper)(r),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function u(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,a=t.orderby,u=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(a,"order by"),s(u)].filter(n.hasVal).join(" ")}(r),")")}function i(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(u(e))}function c(t){return t.expr.map(i).join(", ")}function l(t){var r=t.over;return[function(t){var r=t.args,e=t.name,a=t.consider_nulls,s=void 0===a?"":a,u=t.separator,i=void 0===u?", ":u;return[e,"(",r?Object(o.a)(r).join(i):"",")",s&&" ",s].filter(n.hasVal).join("")}(t),Object(a.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.operator||t.op,e=Object(n.a)(t.right),a=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,e="".concat(e[0]," AND ").concat(e[1])}a||(e="(".concat(e.join(", "),")"))}var s=t.right.escape||{},u=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),r,e,Object(o.toUpper)(s.type),Object(n.a)(s.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(u,")"):u].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.left,e=t.right,a=t.symbol,s=t.keyword;r.keyword=s;var u=Object(n.a)(r),i=Object(n.a)(e);return[u,Object(o.toUpper)(a),i].filter(o.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(1),o=e(8),a=e(3),s=e(0);function u(t){var r,e,u,i,c=t.keyword,l=t.suffix,f="";switch(Object(s.toUpper)(c)){case"BINLOG":e=(r=t).in,u=r.from,i=r.limit,f=[Object(s.commonOptionConnector)("IN",s.literalToSQL,e&&e.right),Object(s.commonOptionConnector)("FROM",a.c,u),Object(o.a)(i)].filter(s.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(s.toUpper)(e)?Object(s.commonOptionConnector)("LIKE",s.literalToSQL,r.right):Object(s.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(s.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(s.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(s.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(s.toUpper)(c),Object(s.toUpper)(l),f].filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(2),o=e(1),a=e(25);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u,i,c,l=(u={},i="mariadb",c=a.parse,(i=function(t){var r=function(t,r){if("object"!=s(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==s(r)?r:r+""}(i))in u?Object.defineProperty(u,i,{value:c,enumerable:!0,configurable:!0,writable:!0}):u[i]=c,u),f=e(16),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return d(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?d(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==e.return||e.return()}finally{if(u)throw a}}}}function d(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function y(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:r+""}var m=function(){return function(t,r,e){return r&&y(t.prototype,r),e&&y(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(e),!t||"*"===t)return[];var o=Object(n.k)(r);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"mariadb":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](!1===r.trimQuery?t:t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,s=this["".concat(o,"List")].bind(this),u=s(t,e),i=!0,c="",l=v(u);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,d=!1,y=v(r);try{for(y.s();!(f=y.n()).done;){var h=f.value,m=new RegExp("^".concat(h,"$"),"i");if(m.test(b)){d=!0;break}}}catch(t){y.e(t)}finally{y.f()}if(!d){c=b,i=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])}();r.a=m},function(t,r,e){"use strict";var n=e(29);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?a(t.parts[r][0])+"-"+a(t.parts[r][1]):a(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,a=new Array(t.length);for(r=0;r<t.length;r++)a[r]=(o=t[r],e[o.type](o));if(a.sort(),a.length>0){for(r=1,n=1;r<a.length;r++)a[r-1]!==a[r]&&(a[n]=a[r],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,a={},s={start:Ai},u=Ai,i=function(t,r){return Lb(t,r)},c=function(t,r){return{...t,order_by:r&&r.toLowerCase()}},l=function(t,r){return Lb(t,r,1)},f=Oi("IF",!0),p="IDENTIFIED",b=Oi("IDENTIFIED",!1),v=Oi("WITH",!0),d=Oi("BY",!0),y=Oi("RANDOM",!0),h=Oi("PASSWORD",!0),m=Oi("AS",!0),w=function(t,r){return Lb(t,r)},O=Oi("role",!0),L=Oi("NONE",!0),j=Oi("SSL",!0),C=Oi("X509",!0),g=Oi("CIPHER",!0),E=Oi("ISSUER",!0),A=Oi("SUBJECT",!0),T=function(t,r){return r.prefix=t.toLowerCase(),r},S=Oi("REQUIRE",!0),_=Oi("MAX_QUERIES_PER_HOUR",!0),U=Oi("MAX_UPDATES_PER_HOUR",!0),I=Oi("MAX_CONNECTIONS_PER_HOUR",!0),x=Oi("MAX_USER_CONNECTIONS",!0),N=Oi("EXPIRE",!0),k=Oi("DEFAULT",!0),R=Oi("NEVER",!0),M=Oi("HISTORY",!0),V=Oi("REUSE",!1),D=Oi("CURRENT",!0),P=Oi("OPTIONAL",!0),q=Oi("FAILED_LOGIN_ATTEMPTS",!0),Q=Oi("PASSWORD_LOCK_TIME",!0),F=Oi("UNBOUNDED",!0),B=Oi("ACCOUNT",!0),$=Oi("LOCK",!0),G=Oi("UNLOCK",!0),H=Oi("ATTRIBUTE",!0),Y=Oi("CASCADED",!0),W=Oi("LOCAL",!0),X=Oi("CHECK",!0),K=Oi("OPTION",!1),Z=Oi("ALGORITHM",!0),J=Oi("UNDEFINED",!0),z=Oi("MERGE",!0),tt=Oi("TEMPTABLE",!0),rt=Oi("SQL",!0),et=Oi("SECURITY",!0),nt=Oi("DEFINER",!0),ot=Oi("INVOKER",!0),at=function(t,r){return Lb(t,r)},st=Oi("AUTO_INCREMENT",!0),ut=Oi("UNIQUE",!0),it=Oi("KEY",!0),ct=Oi("PRIMARY",!0),lt=Oi("@",!1),ft=function(){return wb("=",{type:"origin",value:"definer"},{type:"function",name:{name:[{type:"default",value:"current_user"}]},args:{type:"expr_list",value:[]}})},pt=Oi("BEFORE",!0),bt=Oi("AFTER",!0),vt=Oi("FOR",!0),dt=Oi("EACH",!0),yt=Oi("ROW",!0),ht=Oi("STATEMENT",!0),mt=Oi("FOLLOWS",!0),wt=Oi("PRECEDES",!0),Ot=Oi("COLUMN_FORMAT",!0),Lt=Oi("FIXED",!0),jt=Oi("DYNAMIC",!0),Ct=Oi("STORAGE",!0),gt=Oi("DISK",!0),Et=Oi("MEMORY",!0),At=Oi("GENERATED",!0),Tt=Oi("ALWAYS",!0),St=Oi("STORED",!0),_t=Oi("VIRTUAL",!0),Ut=Oi("if",!0),It=Oi("exists",!0),xt=Oi("first",!0),Nt=Oi("after",!0),kt=Oi("LESS",!0),Rt=Oi("THAN",!0),Mt=Oi("DROP",!0),Vt=Oi("TRUNCATE",!0),Dt=Oi("DISCARD",!0),Pt=Oi("IMPORT",!0),qt=Oi("COALESCE",!0),Qt=Oi("ANALYZE",!0),Ft=Oi("TABLESPACE",!0),Bt=Oi("FOREIGN",!0),$t=Oi("INSTANT",!0),Gt=Oi("INPLACE",!0),Ht=Oi("COPY",!0),Yt=Oi("SHARED",!0),Wt=Oi("EXCLUSIVE",!0),Xt=Oi("CHANGE",!0),Kt=/^[0-9]/,Zt=Li([["0","9"]],!1,!1),Jt=Oi("PRIMARY KEY",!0),zt=Oi("NOT",!0),tr=Oi("REPLICATION",!0),rr=Oi("FOREIGN KEY",!0),er=Oi("ENFORCED",!0),nr=Oi("MATCH FULL",!0),or=Oi("MATCH PARTIAL",!0),ar=Oi("MATCH SIMPLE",!0),sr=Oi("RESTRICT",!0),ur=Oi("CASCADE",!0),ir=Oi("SET NULL",!0),cr=Oi("NO ACTION",!0),lr=Oi("SET DEFAULT",!0),fr=Oi("CHARACTER",!0),pr=Oi("SET",!0),br=Oi("CHARSET",!0),vr=Oi("COLLATE",!0),dr=Oi("AVG_ROW_LENGTH",!0),yr=Oi("KEY_BLOCK_SIZE",!0),hr=Oi("MAX_ROWS",!0),mr=Oi("MIN_ROWS",!0),wr=Oi("STATS_SAMPLE_PAGES",!0),Or=Oi("CHECKSUM",!1),Lr=Oi("DELAY_KEY_WRITE",!1),jr=/^[01]/,Cr=Li(["0","1"],!1,!1),gr=Oi("CONNECTION",!0),Er=Oi("ENGINE_ATTRIBUTE",!0),Ar=Oi("SECONDARY_ENGINE_ATTRIBUTE",!0),Tr=Oi("DATA",!0),Sr=Oi("INDEX",!0),_r=Oi("DIRECTORY",!0),Ur=Oi("COMPRESSION",!0),Ir=Oi("'",!1),xr=Oi("ZLIB",!0),Nr=Oi("LZ4",!0),kr=Oi("ENGINE",!0),Rr=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}},Mr=Oi("ROW_FORMAT",!0),Vr=Oi("COMPRESSED",!0),Dr=Oi("REDUNDANT",!0),Pr=Oi("COMPACT",!0),qr=Oi("BINARY",!0),Qr=Oi("MASTER",!0),Fr=Oi("LOGS",!0),Br=Oi("TRIGGERS",!0),$r=Oi("STATUS",!0),Gr=Oi("PROCESSLIST",!0),Hr=Oi("PROCEDURE",!0),Yr=Oi("FUNCTION",!0),Wr=Oi("BINLOG",!0),Xr=Oi("EVENTS",!0),Kr=Oi("COLLATION",!0),Zr=Oi("DATABASES",!0),Jr=Oi("COLUMNS",!0),zr=Oi("INDEXES",!0),te=Oi("EVENT",!0),re=Oi("GRANTS",!0),ee=function(t,r){return Lb(t,r)},ne=Oi("READ",!0),oe=Oi("LOW_PRIORITY",!0),ae=Oi("WRITE",!0),se=Oi("VIEW",!0),ue=Oi("GRANT",!0),ie=Oi("OPTION",!0),ce=function(t){return{type:"origin",value:Array.isArray(t)?t[0]:t}},le=Oi("ROUTINE",!0),fe=Oi("EXECUTE",!0),pe=Oi("ADMIN",!0),be=Oi("GRANT",!1),ve=Oi("PROXY",!1),de=Oi("(",!1),ye=Oi(")",!1),he=Oi("IN",!0),me=Oi("SHARE",!0),we=Oi("MODE",!0),Oe=Oi("WAIT",!0),Le=Oi("NOWAIT",!0),je=Oi("SKIP",!0),Ce=Oi("LOCKED",!0),ge=Oi("NATURAL",!0),Ee=Oi("LANGUAGE",!0),Ae=Oi("QUERY",!0),Te=Oi("EXPANSION",!0),Se=Oi("BOOLEAN",!0),_e=Oi("MATCH",!0),Ue=Oi("AGAINST",!1),Ie=Oi("OUTFILE",!0),xe=Oi("DUMPFILE",!0),Ne=Oi("BTREE",!0),ke=Oi("HASH",!0),Re=Oi("PARSER",!0),Me=Oi("VISIBLE",!0),Ve=Oi("INVISIBLE",!0),De=Oi("LATERAL",!0),Pe=/^[_0-9]/,qe=Li(["_",["0","9"]],!1,!1),Qe=Oi("ROLLUP",!0),Fe=Oi("?",!1),Be=Oi("=",!1),$e=Oi("DUPLICATE",!0),Ge=function(t,r){return jb(t,r)},He=function(t){return t[0]+" "+t[2]},Ye=Oi(">=",!1),We=Oi(">",!1),Xe=Oi("<=",!1),Ke=Oi("<>",!1),Ze=Oi("<",!1),Je=Oi("!=",!1),ze=Oi("ESCAPE",!0),tn=Oi("+",!1),rn=Oi("-",!1),en=Oi("*",!1),nn=Oi("/",!1),on=Oi("%",!1),an=Oi("||",!1),sn=Oi("div",!0),un=Oi("mod",!0),cn=Oi("&",!1),ln=Oi(">>",!1),fn=Oi("<<",!1),pn=Oi("^",!1),bn=Oi("|",!1),vn=Oi("!",!1),dn=Oi("~",!1),yn=function(t){return{type:"default",value:t}},hn=function(t){return!0===db[t.toUpperCase()]},mn=Oi('"',!1),wn=/^[^"]/,On=Li(['"'],!0,!1),Ln=/^[^']/,jn=Li(["'"],!0,!1),Cn=Oi("`",!1),gn=/^[^`\\]/,En=Li(["`","\\"],!0,!1),An=function(t,r){return t+r.join("")},Tn=/^[A-Za-z_\u4E00-\u9FA5]/,Sn=Li([["A","Z"],["a","z"],"_",["一","龥"]],!1,!1),_n=/^[A-Za-z0-9_$$\u4E00-\u9FA5\xC0-\u017F]/,Un=Li([["A","Z"],["a","z"],["0","9"],"_","$","$",["一","龥"],["À","ſ"]],!1,!1),In=/^[A-Za-z0-9_:]/,xn=Li([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),Nn=Oi(":",!1),kn=Oi("NOW",!0),Rn=Oi("OVER",!0),Mn=Oi("WINDOW",!0),Vn=Oi("FOLLOWING",!0),Dn=Oi("PRECEDING",!0),Pn=Oi("SEPARATOR",!0),qn=Oi("YEAR_MONTH",!0),Qn=Oi("DAY_HOUR",!0),Fn=Oi("DAY_MINUTE",!0),Bn=Oi("DAY_SECOND",!0),$n=Oi("DAY_MICROSECOND",!0),Gn=Oi("HOUR_MINUTE",!0),Hn=Oi("HOUR_SECOND",!0),Yn=Oi("HOUR_MICROSECOND",!0),Wn=Oi("MINUTE_SECOND",!0),Xn=Oi("MINUTE_MICROSECOND",!0),Kn=Oi("SECOND_MICROSECOND",!0),Zn=Oi("TIMEZONE_HOUR",!0),Jn=Oi("TIMEZONE_MINUTE",!0),zn=Oi("CENTURY",!0),to=Oi("DAY",!0),ro=Oi("DATE",!0),eo=Oi("DECADE",!0),no=Oi("DOW",!0),oo=Oi("DOY",!0),ao=Oi("EPOCH",!0),so=Oi("HOUR",!0),uo=Oi("ISODOW",!0),io=Oi("ISOWEEK",!0),co=Oi("ISOYEAR",!0),lo=Oi("MICROSECONDS",!0),fo=Oi("MILLENNIUM",!0),po=Oi("MILLISECONDS",!0),bo=Oi("MINUTE",!0),vo=Oi("MONTH",!0),yo=Oi("QUARTER",!0),ho=Oi("SECOND",!0),mo=Oi("TIME",!0),wo=Oi("TIMEZONE",!0),Oo=Oi("WEEK",!0),Lo=Oi("YEAR",!0),jo=Oi("DATE_TRUNC",!0),Co=Oi("BOTH",!0),go=Oi("LEADING",!0),Eo=Oi("TRAILING",!0),Ao=Oi("trim",!0),To=Oi("convert",!0),So=Oi("binary",!0),_o=Oi("_binary",!0),Uo=Oi("_latin1",!0),Io=Oi("X",!0),xo=/^[0-9A-Fa-f]/,No=Li([["0","9"],["A","F"],["a","f"]],!1,!1),ko=Oi("b",!0),Ro=Oi("0x",!0),Mo=Oi("N",!0),Vo=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}},Do=/^[^"\\\0-\x1F\x7F]/,Po=Li(['"',"\\",["\0",""],""],!0,!1),qo=/^[\n]/,Qo=Li(["\n"],!1,!1),Fo=/^[^'\\]/,Bo=Li(["'","\\"],!0,!1),$o=Oi("\\'",!1),Go=Oi('\\"',!1),Ho=Oi("\\\\",!1),Yo=Oi("\\/",!1),Wo=Oi("\\b",!1),Xo=Oi("\\f",!1),Ko=Oi("\\n",!1),Zo=Oi("\\r",!1),Jo=Oi("\\t",!1),zo=Oi("\\u",!1),ta=Oi("\\",!1),ra=Oi("''",!1),ea=Oi('""',!1),na=Oi("``",!1),oa=/^[\n\r]/,aa=Li(["\n","\r"],!1,!1),sa=Oi(".",!1),ua=/^[0-9a-fA-F]/,ia=Li([["0","9"],["a","f"],["A","F"]],!1,!1),ca=/^[eE]/,la=Li(["e","E"],!1,!1),fa=/^[+\-]/,pa=Li(["+","-"],!1,!1),ba=Oi("NULL",!0),va=Oi("NOT NULL",!0),da=Oi("TRUE",!0),ya=Oi("TO",!0),ha=Oi("FALSE",!0),ma=Oi("SHOW",!0),wa=Oi("USE",!0),Oa=Oi("ALTER",!0),La=Oi("SELECT",!0),ja=Oi("UPDATE",!0),Ca=Oi("CREATE",!0),ga=Oi("TEMPORARY",!0),Ea=Oi("DELETE",!0),Aa=Oi("INSERT",!0),Ta=Oi("RECURSIVE",!0),Sa=Oi("REPLACE",!0),_a=Oi("RETURNING",!0),Ua=Oi("RENAME",!0),Ia=Oi("IGNORE",!0),xa=Oi("EXPLAIN",!0),Na=Oi("PARTITION",!0),ka=Oi("INTO",!0),Ra=Oi("FROM",!0),Ma=Oi("TABLE",!0),Va=Oi("TRIGGER",!0),Da=Oi("TABLES",!0),Pa=Oi("DATABASE",!0),qa=Oi("SCHEMA",!0),Qa=Oi("ON",!0),Fa=Oi("LEFT",!0),Ba=Oi("RIGHT",!0),$a=Oi("FULL",!0),Ga=Oi("INNER",!0),Ha=Oi("CROSS",!0),Ya=Oi("JOIN",!0),Wa=Oi("OUTER",!0),Xa=Oi("UNION",!0),Ka=Oi("MINUS",!0),Za=Oi("INTERSECT",!0),Ja=Oi("VALUES",!0),za=Oi("USING",!0),ts=Oi("WHERE",!0),rs=Oi("GROUP",!0),es=Oi("ORDER",!0),ns=Oi("HAVING",!0),os=Oi("LIMIT",!0),as=Oi("OFFSET",!0),ss=Oi("ASC",!0),us=Oi("DESC",!0),is=Oi("DESCRIBE",!0),cs=Oi("ALL",!0),ls=Oi("DISTINCT",!0),fs=Oi("BETWEEN",!0),ps=Oi("IS",!0),bs=Oi("LIKE",!0),vs=Oi("RLIKE",!0),ds=Oi("REGEXP",!0),ys=Oi("EXISTS",!0),hs=Oi("AND",!0),ms=Oi("OR",!0),ws=Oi("COUNT",!0),Os=Oi("GROUP_CONCAT",!0),Ls=Oi("MAX",!0),js=Oi("MIN",!0),Cs=Oi("SUM",!0),gs=Oi("AVG",!0),Es=Oi("EXTRACT",!0),As=Oi("CALL",!0),Ts=Oi("CASE",!0),Ss=Oi("WHEN",!0),_s=Oi("THEN",!0),Us=Oi("ELSE",!0),Is=Oi("END",!0),xs=Oi("CAST",!0),Ns=Oi("VARBINARY",!0),ks=Oi("BIT",!0),Rs=Oi("CHAR",!0),Ms=Oi("VARCHAR",!0),Vs=Oi("NUMERIC",!0),Ds=Oi("DECIMAL",!0),Ps=Oi("SIGNED",!0),qs=Oi("UNSIGNED",!0),Qs=Oi("INT",!0),Fs=Oi("ZEROFILL",!0),Bs=Oi("INTEGER",!0),$s=Oi("JSON",!0),Gs=Oi("SMALLINT",!0),Hs=Oi("MEDIUMINT",!0),Ys=Oi("TINYINT",!0),Ws=Oi("TINYTEXT",!0),Xs=Oi("TEXT",!0),Ks=Oi("MEDIUMTEXT",!0),Zs=Oi("LONGTEXT",!0),Js=Oi("BIGINT",!0),zs=Oi("ENUM",!0),tu=Oi("FLOAT",!0),ru=Oi("DOUBLE",!0),eu=Oi("DATETIME",!0),nu=Oi("ROWS",!0),ou=Oi("TIMESTAMP",!0),au=Oi("USER",!0),su=Oi("UUID",!0),uu=Oi("CURRENT_DATE",!0),iu=(Oi("ADDDATE",!0),Oi("INTERVAL",!0)),cu=Oi("CURRENT_TIME",!0),lu=Oi("CURRENT_TIMESTAMP",!0),fu=Oi("CURRENT_USER",!0),pu=Oi("SESSION_USER",!0),bu=Oi("SYSTEM_USER",!0),vu=Oi("GLOBAL",!0),du=Oi("SESSION",!0),yu=Oi("PERSIST",!0),hu=Oi("PERSIST_ONLY",!0),mu=Oi("GEOMETRY",!0),wu=Oi("POINT",!0),Ou=Oi("LINESTRING",!0),Lu=Oi("POLYGON",!0),ju=Oi("MULTIPOINT",!0),Cu=Oi("MULTILINESTRING",!0),gu=Oi("MULTIPOLYGON",!0),Eu=Oi("GEOMETRYCOLLECTION",!0),Au=Oi("@@",!1),Tu=Oi("$",!1),Su=Oi("return",!0),_u=Oi(":=",!1),Uu=Oi("DUAL",!0),Iu=Oi("ADD",!0),xu=Oi("COLUMN",!0),Nu=Oi("MODIFY",!0),ku=Oi("FULLTEXT",!0),Ru=Oi("SPATIAL",!0),Mu=Oi("COMMENT",!0),Vu=Oi("CONSTRAINT",!0),Du=Oi("REFERENCES",!0),Pu=Oi("SQL_CALC_FOUND_ROWS",!0),qu=Oi("SQL_CACHE",!0),Qu=Oi("SQL_NO_CACHE",!0),Fu=Oi("SQL_SMALL_RESULT",!0),Bu=Oi("SQL_BIG_RESULT",!0),$u=Oi("SQL_BUFFER_RESULT",!0),Gu=Oi(",",!1),Hu=Oi("[",!1),Yu=Oi("]",!1),Wu=Oi(";",!1),Xu=Oi("&&",!1),Ku=Oi("XOR",!0),Zu=Oi("/*",!1),Ju=Oi("*/",!1),zu=Oi("--",!1),ti=Oi("#",!1),ri={type:"any"},ei=/^[ \t\n\r]/,ni=Li([" ","\t","\n","\r"],!1,!1),oi=function(t,r,e){return{type:"assign",left:t,symbol:r,right:e}},ai=Oi("boolean",!0),si=Oi("blob",!0),ui=Oi("tinyblob",!0),ii=Oi("mediumblob",!0),ci=Oi("longblob",!0),li=function(t,r){return{dataType:t,...r||{}}},fi=Oi("ARRAY",!0),pi=/^[0-6]/,bi=Li([["0","6"]],!1,!1),vi=0,di=0,yi=[{line:1,column:1}],hi=0,mi=[],wi=0;if("startRule"in r){if(!(r.startRule in s))throw new Error("Can't start parsing from rule \""+r.startRule+'".');u=s[r.startRule]}function Oi(t,r){return{type:"literal",text:t,ignoreCase:r}}function Li(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function ji(r){var e,n=yi[r];if(n)return n;for(e=r-1;!yi[e];)e--;for(n={line:(n=yi[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return yi[r]=n,n}function Ci(t,r){var e=ji(t),n=ji(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function gi(t){vi<hi||(vi>hi&&(hi=vi,mi=[]),mi.push(t))}function Ei(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function Ai(){var t,r;return t=vi,Gp()!==a&&(r=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Si())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Bp())!==a&&(u=Gp())!==a&&(i=Si())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Bp())!==a&&(u=Gp())!==a&&(i=Si())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(Tb),columnList:gb(Sb),ast:n}}(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=t,t=r):(vi=t,t=a),t}function Ti(){var r;return(r=function(){var t,r,e,n,o,s,u;t=vi,(r=vf())!==a&&Gp()!==a&&(e=Uf())!==a&&Gp()!==a?((n=Fi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Sc())!==a?(di=t,i=r,c=e,f=n,(p=o)&&p.forEach(t=>Tb.add(`${i}::${t.db}::${t.table}`)),r={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:i.toLowerCase(),keyword:c.toLowerCase(),prefix:f,name:p}},t=r):(vi=t,t=a)):(vi=t,t=a);var i,c,f,p;t===a&&(t=vi,(r=vf())!==a&&Gp()!==a&&(e=gp())!==a&&Gp()!==a?((n=Fi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Sc())!==a&&Gp()!==a?((s=rc())===a&&(s=null),s!==a?(di=t,r=function(t,r,e,n,o){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:n,options:o&&[{type:"origin",value:o}]}}}(r,e,n,o,s),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=vf())!==a&&Gp()!==a&&(e=Ip())!==a&&Gp()!==a&&(n=yl())!==a&&Gp()!==a&&(o=Rf())!==a&&Gp()!==a&&(s=xc())!==a&&Gp()!==a?((u=function(){var t,r,e,n,o,s;t=vi,(r=Hi())===a&&(r=Yi());if(r!==a){for(e=[],n=vi,(o=Gp())!==a?((s=Hi())===a&&(s=Yi()),s!==a?n=o=[o,s]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=Hi())===a&&(s=Yi()),s!==a?n=o=[o,s]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,r=l(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(u=null),u!==a&&Gp()!==a?(di=t,r=function(t,r,e,n,o){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),name:e,table:n,options:o}}}(r,e,n,s,u),t=r):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=vf())!==a&&Gp()!==a?((e=Nf())===a&&(e=kf()),e!==a&&Gp()!==a?((n=Fi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Ul())!==a?(di=t,r=function(t,r,e,n){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:n}}}(r,e,n,o),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=vf())!==a&&Gp()!==a&&(e=If())!==a&&Gp()!==a?((n=Fi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Uc())!==a?(di=t,r=function(t,r,e,n){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:[{schema:n.db,trigger:n.table}]}}}(r,e,n,o),t=r):(vi=t,t=a)):(vi=t,t=a)))));return t}())===a&&(r=function(){var r;(r=function(){var t,r,e,n,o,s,u,c,l,f;t=vi,(r=mf())!==a&&Gp()!==a?((e=wf())===a&&(e=null),e!==a&&Gp()!==a&&Uf()!==a&&Gp()!==a?((n=xi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Sc())!==a&&Gp()!==a&&(s=function(){var t,r,e,n,o,s,u,i,c;if(t=vi,(r=Qp())!==a)if(Gp()!==a)if((e=Vi())!==a){for(n=[],o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=Vi())!==a?o=s=[s,u,i,c]:(vi=o,o=a);o!==a;)n.push(o),o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=Vi())!==a?o=s=[s,u,i,c]:(vi=o,o=a);n!==a&&(o=Gp())!==a&&(s=Fp())!==a?(di=t,r=at(e,n),t=r):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;return t}())!==a&&Gp()!==a?((u=function(){var t,r,e,n,o,s,u,c;if(t=vi,(r=oc())!==a){for(e=[],n=vi,(o=Gp())!==a?((s=Pp())===a&&(s=null),s!==a&&(u=Gp())!==a&&(c=oc())!==a?n=o=[o,s,u,c]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=Pp())===a&&(s=null),s!==a&&(u=Gp())!==a&&(c=oc())!==a?n=o=[o,s,u,c]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,r=i(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(u=null),u!==a&&Gp()!==a?((c=gf())===a&&(c=jf()),c===a&&(c=null),c!==a&&Gp()!==a?((l=_f())===a&&(l=null),l!==a&&Gp()!==a?((f=Ui())===a&&(f=null),f!==a?(di=t,p=r,b=e,v=n,y=s,h=u,m=c,w=l,O=f,(d=o)&&d.forEach(t=>Tb.add(`create::${t.db}::${t.table}`)),r={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:v,table:d,ignore_replace:m&&m[0].toLowerCase(),as:w&&w[0].toLowerCase(),query_expr:O&&O.ast,create_definitions:y,table_options:h}},t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);var p,b,v,d,y,h,m,w,O;t===a&&(t=vi,(r=mf())!==a&&Gp()!==a?((e=wf())===a&&(e=null),e!==a&&Gp()!==a&&Uf()!==a&&Gp()!==a?((n=xi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Sc())!==a&&Gp()!==a&&(s=function t(){var r,e;(r=function(){var t,r,e;t=vi,(r=Xf())!==a&&Gp()!==a&&(e=Sc())!==a?(di=t,r=function(t){return{type:"like",table:t}}(e),t=r):(vi=t,t=a);return t}())===a&&(r=vi,Qp()!==a&&Gp()!==a&&(e=t())!==a&&Gp()!==a&&Fp()!==a?(di=r,(n=e).parentheses=!0,r=n):(vi=r,r=a));var n;return r}())!==a?(di=t,r=function(t,r,e,n,o){return n&&n.forEach(t=>Tb.add(`create::${t.db}::${t.table}`)),{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(r,e,n,o,s),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p;r=vi,(e=mf())!==a&&Gp()!==a?((n=qi())===a&&(n=null),n!==a&&Gp()!==a&&If()!==a&&Gp()!==a?((o=xi())===a&&(o=null),o!==a&&Gp()!==a&&(s=xc())!==a&&Gp()!==a&&(u=function(){var r;"before"===t.substr(vi,6).toLowerCase()?(r=t.substr(vi,6),vi+=6):(r=a,0===wi&&gi(pt));r===a&&("after"===t.substr(vi,5).toLowerCase()?(r=t.substr(vi,5),vi+=5):(r=a,0===wi&&gi(bt)));return r}())!==a&&Gp()!==a&&(i=function(){var t,r;t=vi,(r=Lf())===a&&(r=hf())===a&&(r=Of());r!==a&&(di=t,r={keyword:r[0].toLowerCase()});return t=r}())!==a&&Gp()!==a&&Rf()!==a&&Gp()!==a&&(c=xc())!==a&&Gp()!==a&&(l=function(){var r,e,n,o;r=vi,"for"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(vt));e!==a&&Gp()!==a?("each"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(dt)),n===a&&(n=null),n!==a&&Gp()!==a?("row"===t.substr(vi,3).toLowerCase()?(o=t.substr(vi,3),vi+=3):(o=a,0===wi&&gi(yt)),o===a&&("statement"===t.substr(vi,9).toLowerCase()?(o=t.substr(vi,9),vi+=9):(o=a,0===wi&&gi(ht))),o!==a?(di=r,s=e,i=o,e={keyword:(u=n)?`${s.toLowerCase()} ${u.toLowerCase()}`:s.toLowerCase(),args:i.toLowerCase()},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var s,u,i;return r}())!==a&&Gp()!==a?((f=function(){var r,e,n;r=vi,"follows"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(mt));e===a&&("precedes"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(wt)));e!==a&&Gp()!==a&&(n=Ul())!==a?(di=r,r=e={keyword:e,trigger:n}):(vi=r,r=a);return r}())===a&&(f=null),f!==a&&Gp()!==a&&(p=function(){var t,r;t=vi,Sf()!==a&&Gp()!==a&&(r=Qc())!==a?(di=t,t={type:"set",expr:r}):(vi=t,t=a);return t}())!==a?(di=r,b=e,v=n,d=o,y=s,h=u,m=i,w=c,O=l,L=f,j=p,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:b[0].toLowerCase(),definer:v,keyword:"trigger",for_each:O,if_not_exists:d,trigger:y,time:h,events:[m],order:L,table:w,execute:j}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var b,v,d,y,h,m,w,O,L,j;return r}())===a&&(r=function(){var t,r,e,n,o,s,u,c,l,f,p,b;t=vi,(r=mf())!==a&&Gp()!==a?((e=Rp())===a&&(e=Np())===a&&(e=kp()),e===a&&(e=null),e!==a&&Gp()!==a&&(n=Ip())!==a&&Gp()!==a&&(o=Ll())!==a&&Gp()!==a?((s=Ec())===a&&(s=null),s!==a&&Gp()!==a&&(u=Rf())!==a&&Gp()!==a&&(c=xc())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(l=function(){var t,r,e,n,o,s,u,c;if(t=vi,(r=Ii())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=Ii())!==a?n=o=[o,s,u,c]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=Ii())!==a?n=o=[o,s,u,c]:(vi=n,n=a);e!==a?(di=t,r=i(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((f=Ac())===a&&(f=null),f!==a&&Gp()!==a?((p=Hi())===a&&(p=null),p!==a&&Gp()!==a?((b=Yi())===a&&(b=null),b!==a&&Gp()!==a?(di=t,v=r,d=e,y=n,h=o,m=s,w=u,O=c,L=l,j=f,C=p,g=b,r={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:v[0].toLowerCase(),index_type:d&&d.toLowerCase(),keyword:y.toLowerCase(),index:h,on_kw:w[0].toLowerCase(),table:O,index_columns:L,index_using:m,index_options:j,algorithm_option:C,lock_option:g}},t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);var v,d,y,h,m,w,O,L,j,C,g;return t}())===a&&(r=function(){var t,r,e,n,o,s;t=vi,(r=mf())!==a&&Gp()!==a?((e=Nf())===a&&(e=kf()),e!==a&&Gp()!==a?((n=xi())===a&&(n=null),n!==a&&Gp()!==a&&(o=ob())!==a&&Gp()!==a?((s=function(){var t,r,e,n,o,s;if(t=vi,(r=nc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=nc())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=nc())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,r=l(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(s=null),s!==a?(di=t,r=function(t,r,e,n,o){const a=r.toLowerCase();return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:e,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(r,e,n,o,s),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p,b,v,d,y,h,m,w,O,L,j;r=vi,(e=mf())!==a&&Gp()!==a?(n=vi,(o=zf())!==a&&(s=Gp())!==a&&(u=jf())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=null),n!==a&&(o=Gp())!==a?(s=vi,"algorithm"===t.substr(vi,9).toLowerCase()?(u=t.substr(vi,9),vi+=9):(u=a,0===wi&&gi(Z)),u!==a&&(i=Gp())!==a&&(c=Sp())!==a&&(l=Gp())!==a?("undefined"===t.substr(vi,9).toLowerCase()?(f=t.substr(vi,9),vi+=9):(f=a,0===wi&&gi(J)),f===a&&("merge"===t.substr(vi,5).toLowerCase()?(f=t.substr(vi,5),vi+=5):(f=a,0===wi&&gi(z)),f===a&&("temptable"===t.substr(vi,9).toLowerCase()?(f=t.substr(vi,9),vi+=9):(f=a,0===wi&&gi(tt)))),f!==a?s=u=[u,i,c,l,f]:(vi=s,s=a)):(vi=s,s=a),s===a&&(s=null),s!==a&&(u=Gp())!==a?((i=qi())===a&&(i=null),i!==a&&(c=Gp())!==a?(l=vi,"sql"===t.substr(vi,3).toLowerCase()?(f=t.substr(vi,3),vi+=3):(f=a,0===wi&&gi(rt)),f!==a&&(p=Gp())!==a?("security"===t.substr(vi,8).toLowerCase()?(b=t.substr(vi,8),vi+=8):(b=a,0===wi&&gi(et)),b!==a&&(v=Gp())!==a?("definer"===t.substr(vi,7).toLowerCase()?(d=t.substr(vi,7),vi+=7):(d=a,0===wi&&gi(nt)),d===a&&("invoker"===t.substr(vi,7).toLowerCase()?(d=t.substr(vi,7),vi+=7):(d=a,0===wi&&gi(ot))),d!==a?l=f=[f,p,b,v,d]:(vi=l,l=a)):(vi=l,l=a)):(vi=l,l=a),l===a&&(l=null),l!==a&&(f=Gp())!==a&&(p=gp())!==a&&(b=Gp())!==a&&(v=xc())!==a&&(d=Gp())!==a?(y=vi,(h=Qp())!==a&&(m=Gp())!==a&&(w=hl())!==a&&(O=Gp())!==a&&(L=Fp())!==a?y=h=[h,m,w,O,L]:(vi=y,y=a),y===a&&(y=null),y!==a&&(h=Gp())!==a&&(m=_f())!==a&&(w=Gp())!==a&&(O=yc())!==a&&(L=Gp())!==a?((j=function(){var r,e,n,o,s;r=vi,(e=qf())!==a&&Gp()!==a?("cascaded"===t.substr(vi,8).toLowerCase()?(n=t.substr(vi,8),vi+=8):(n=a,0===wi&&gi(Y)),n===a&&("local"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(W))),n!==a&&Gp()!==a?("check"===t.substr(vi,5).toLowerCase()?(o=t.substr(vi,5),vi+=5):(o=a,0===wi&&gi(X)),o!==a&&Gp()!==a?("OPTION"===t.substr(vi,6)?(s="OPTION",vi+=6):(s=a,0===wi&&gi(K)),s!==a?(di=r,e=`with ${n.toLowerCase()} check option`,r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);r===a&&(r=vi,(e=qf())!==a&&Gp()!==a?("check"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(X)),n!==a&&Gp()!==a?("OPTION"===t.substr(vi,6)?(o="OPTION",vi+=6):(o=a,0===wi&&gi(K)),o!==a?(di=r,r=e="with check option"):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a));return r}())===a&&(j=null),j!==a?(di=r,C=e,g=n,E=s,A=i,T=l,_=y,U=O,I=j,(S=v).view=S.table,delete S.table,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:C[0].toLowerCase(),keyword:"view",replace:g&&"or replace",algorithm:E&&E[4],definer:A,sql_security:T&&T[4],columns:_&&_[2],select:U,view:S,with:I}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var C,g,E,A,T,S,_,U,I;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p;r=vi,(e=mf())!==a&&Gp()!==a&&Op()!==a&&Gp()!==a?((n=xi())===a&&(n=null),n!==a&&Gp()!==a&&(o=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Ni())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ni())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ni())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=w(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&Gp()!==a?((s=function(){var r,e,n;r=vi,ff()!==a&&Gp()!==a?("role"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(O)),e!==a&&Gp()!==a&&(n=cc())!==a?(di=r,r={keyword:"default role",value:n}):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(s=null),s!==a&&Gp()!==a?((u=function(){var r,e,n;r=vi,"require"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(S));e!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=ki())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Jf())!==a&&(u=Gp())!==a&&(i=ki())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Jf())!==a&&(u=Gp())!==a&&(i=ki())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=jb(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,r=e={keyword:"require",value:n}):(vi=r,r=a);return r}())===a&&(u=null),u!==a&&Gp()!==a?((i=function(){var t,r,e,n,o,s,u;if(t=vi,(r=qf())!==a)if(Gp()!==a)if((e=Ri())!==a){for(n=[],o=vi,(s=Gp())!==a&&(u=Ri())!==a?o=s=[s,u]:(vi=o,o=a);o!==a;)n.push(o),o=vi,(s=Gp())!==a&&(u=Ri())!==a?o=s=[s,u]:(vi=o,o=a);n!==a?(di=t,r=function(t,r){const e=[t];if(r)for(const t of r)e.push(t[1]);return{keyword:"with",value:e}}(e,n),t=r):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;return t}())===a&&(i=null),i!==a&&Gp()!==a?((c=function(){var t,r,e,n,o,s;if(t=vi,(r=Mi())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Mi())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Mi())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,r=Lb(r,e,1),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(c=null),c!==a&&Gp()!==a?((l=function(){var r,e,n;r=vi,"account"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(B));e!==a&&Gp()!==a?("lock"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi($)),n===a&&("unlock"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(G))),n!==a?(di=r,e=function(t){const r={type:"origin",value:t.toLowerCase(),prefix:"account"};return r}(n),r=e):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(l=null),l!==a&&Gp()!==a?((f=Wp())===a&&(f=null),f!==a&&Gp()!==a?((p=function(){var r,e,n;r=vi,"attribute"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(H));e!==a&&Gp()!==a&&(n=zl())!==a?(di=r,(o=n).prefix="attribute",r=e=o):(vi=r,r=a);var o;return r}())===a&&(p=null),p!==a?(di=r,b=e,v=n,d=o,y=s,h=u,m=i,L=c,j=l,C=f,g=p,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:b[0].toLowerCase(),keyword:"user",if_not_exists:v,user:d,default_role:y,require:h,resource_options:m,password_options:L,lock_option:j,comment:C,attribute:g}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var b,v,d,y,h,m,L,j,C,g;return r}());return r}())===a&&(r=function(){var r,e,n,o;r=vi,(e=function(){var r,e,n,o;r=vi,"truncate"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Vt));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TRUNCATE"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=Uf())===a&&(n=null),n!==a&&Gp()!==a&&(o=Sc())!==a?(di=r,s=e,u=n,(i=o)&&i.forEach(t=>Tb.add(`${s}::${t.db}::${t.table}`)),e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:s.toLowerCase(),keyword:u&&u.toLowerCase()||"table",name:i}},r=e):(vi=r,r=a)):(vi=r,r=a);var s,u,i;return r}())===a&&(r=function(){var t,r,e;t=vi,(r=Cf())!==a&&Gp()!==a&&Uf()!==a&&Gp()!==a&&(e=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=gc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=gc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=gc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=at(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=t,(n=e).forEach(t=>t.forEach(t=>t.table&&Tb.add(`rename::${t.db}::${t.table}`))),r={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"rename",table:n}},t=r):(vi=t,t=a);var n;return t}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;r=vi,"call"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(As));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CALL"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&(n=function(){var t;(t=ab())===a&&(t=sb());return t}())!==a?(di=r,o=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"call",expr:o}},r=e):(vi=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;r=vi,"use"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(wa));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&(n=Ll())!==a?(di=r,o=n,Tb.add(`use::${o}::null`),e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"use",db:o}},r=e):(vi=r,r=a);var o;return r}())===a&&(r=function(){var t,r,e,n;t=vi,(r=df())!==a&&Gp()!==a&&Uf()!==a&&Gp()!==a&&(e=xc())!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=$i())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=$i())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=$i())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=at(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=t,o=e,s=n,Tb.add(`alter::${o.db}::${o.table}`),r={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"alter",table:[o],expr:s}},t=r):(vi=t,t=a);var o,s;return t}())===a&&(r=function(){var r,e,n,o;r=vi,(e=Sf())!==a&&Gp()!==a?((n=function(){var r,e,n,o;r=vi,"global"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(vu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="GLOBAL"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=vi,"session"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(du));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SESSION"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=vi,"local"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(W));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="LOCAL"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=vi,"persist"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(yu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="PERSIST"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=vi,"persist_only"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(hu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="PERSIST_ONLY"):(vi=r,r=a)):(vi=r,r=a);return r}()),n===a&&(n=null),n!==a&&Gp()!==a&&(o=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=zp())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=zp())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=zp())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=ee(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,s=n,(u=o).keyword=s,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"set",keyword:s,expr:u}},r=e):(vi=r,r=a)):(vi=r,r=a);var s,u;return r}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;r=vi,"lock"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi($));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&xf()!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=ac())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=ac())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=ac())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=ee(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,o=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"lock",keyword:"tables",tables:o}},r=e):(vi=r,r=a);var o;return r}())===a&&(r=function(){var r,e;r=vi,(e=function(){var r,e,n,o;r=vi,"unlock"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(G));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&xf()!==a?(di=r,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"unlock",keyword:"tables"}},r=e):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=vi,(e=bf())!==a&&Gp()!==a?("binary"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(qr)),n===a&&("master"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(Qr))),n!==a&&(o=Gp())!==a?("logs"===t.substr(vi,4).toLowerCase()?(s=t.substr(vi,4),vi+=4):(s=a,0===wi&&gi(Fr)),s!==a?(di=r,f=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",suffix:"logs",keyword:f.toLowerCase()}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var f;r===a&&(r=vi,(e=bf())!==a&&Gp()!==a&&(n=xf())!==a?(di=r,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:"tables"}},r=e):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a?("triggers"===t.substr(vi,8).toLowerCase()?(n=t.substr(vi,8),vi+=8):(n=a,0===wi&&gi(Br)),n===a&&("status"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi($r)),n===a&&("processlist"===t.substr(vi,11).toLowerCase()?(n=t.substr(vi,11),vi+=11):(n=a,0===wi&&gi(Gr)))),n!==a?(di=r,d=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:d.toLowerCase()}},r=e):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a?("procedure"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Hr)),n===a&&("function"===t.substr(vi,8).toLowerCase()?(n=t.substr(vi,8),vi+=8):(n=a,0===wi&&gi(Yr))),n!==a&&(o=Gp())!==a?("status"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi($r)),s!==a?(di=r,e=function(t){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:t.toLowerCase(),suffix:"status"}}}(n),r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a?("binlog"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(Wr)),n!==a&&(o=Gp())!==a?("events"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi(Xr)),s!==a&&(u=Gp())!==a?((i=ll())===a&&(i=null),i!==a&&Gp()!==a?((c=Cc())===a&&(c=null),c!==a&&Gp()!==a?((l=qc())===a&&(l=null),l!==a?(di=r,p=i,b=c,v=l,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",suffix:"events",keyword:"binlog",in:p,from:b,limit:v}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a?(n=vi,"character"===t.substr(vi,9).toLowerCase()?(o=t.substr(vi,9),vi+=9):(o=a,0===wi&&gi(fr)),o!==a&&(s=Gp())!==a?("set"===t.substr(vi,3).toLowerCase()?(u=t.substr(vi,3),vi+=3):(u=a,0===wi&&gi(pr)),u!==a?n=o=[o,s,u]:(vi=n,n=a)):(vi=n,n=a),n===a&&("collation"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Kr)),n===a&&("databases"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Zr)))),n!==a&&(o=Gp())!==a?((s=cl())===a&&(s=kc()),s===a&&(s=null),s!==a?(di=r,e=function(t,r){let e=Array.isArray(t)&&t||[t];return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",suffix:e[2]&&e[2].toLowerCase(),keyword:e[0].toLowerCase(),expr:r}}}(n,s),r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a?("columns"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(Jr)),n===a&&("indexes"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(zr)),n===a&&("index"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(Sr)))),n!==a&&(o=Gp())!==a&&(s=Cc())!==a?(di=r,e=function(t,r){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:t.toLowerCase(),from:r}}}(n,s),r=e):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=bf())!==a&&Gp()!==a&&(n=mf())!==a&&(o=Gp())!==a?((s=gp())===a&&(s=Uf())===a&&("event"===t.substr(vi,5).toLowerCase()?(s=t.substr(vi,5),vi+=5):(s=a,0===wi&&gi(te)),s===a&&(s=If())===a&&("procedure"===t.substr(vi,9).toLowerCase()?(s=t.substr(vi,9),vi+=9):(s=a,0===wi&&gi(Hr)))),s!==a&&(u=Gp())!==a&&(i=xc())!==a?(di=r,e=function(t,r){const e=t.toLowerCase();return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:"create",suffix:e,[e]:r}}}(s,i),r=e):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=function(){var r,e,n,o;r=vi,(e=bf())!==a&&Gp()!==a?("grants"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(re)),n!==a&&Gp()!==a?((o=function(){var r,e,n,o,s,u,i;r=vi,"for"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(vt));e!==a&&Gp()!==a&&(n=Ll())!==a&&Gp()!==a?(o=vi,(s=Ep())!==a&&(u=Gp())!==a&&(i=Ll())!==a?o=s=[s,u,i]:(vi=o,o=a),o===a&&(o=null),o!==a&&(s=Gp())!==a?((u=function(){var t,r;t=vi,Pf()!==a&&Gp()!==a&&(r=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Ll())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ll())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ll())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=ee(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=t,t=r):(vi=t,t=a);return t}())===a&&(u=null),u!==a?(di=r,l=u,e={user:n,host:(c=o)&&c[2],role_list:l},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var c,l;return r}())===a&&(o=null),o!==a?(di=r,s=o,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"show",keyword:"grants",for:s}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var s;return r}()))))))));var p,b,v;var d;return r}())===a&&(r=function(){var r,e,n;r=vi,(e=Bf())===a&&(e=function(){var r,e,n,o;r=vi,"describe"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(is));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DESCRIBE"):(vi=r,r=a)):(vi=r,r=a);return r}());e!==a&&Gp()!==a&&(n=Ll())!==a?(di=r,o=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"desc",table:o}},r=e):(vi=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l;r=vi,"grant"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ue));e!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=uc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=uc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=uc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=w(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&Gp()!==a&&(o=Rf())!==a&&Gp()!==a?((s=function(){var r,e;r=vi,(e=Uf())===a&&("function"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Yr)),e===a&&("procedure"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Hr))));e!==a&&(di=r,e={type:"origin",value:e.toUpperCase()});return r=e}())===a&&(s=null),s!==a&&Gp()!==a&&(u=function(){var t,r,e,n,o;t=vi,r=vi,(e=Ll())===a&&(e=qp());e!==a&&(n=Gp())!==a&&(o=Dp())!==a?r=e=[e,n,o]:(vi=r,r=a);r===a&&(r=null);r!==a&&(e=Gp())!==a?((n=Ll())===a&&(n=qp()),n!==a?(di=t,u=n,r={prefix:(s=r)&&s[0],name:u},t=r):(vi=t,t=a)):(vi=t,t=a);var s,u;return t}())!==a&&Gp()!==a&&(i=pf())!==a&&Gp()!==a&&(c=cc())!==a&&Gp()!==a?((l=function(){var r,e,n;r=vi,qf()!==a&&Gp()!==a?("grant"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ue)),e!==a&&Gp()!==a?("option"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(ie)),n!==a?(di=r,r={type:"origin",value:"with grant option"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(l=null),l!==a?(di=r,f=n,p=s,b=u,v=i,d=c,y=l,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"grant",keyword:"priv",objects:f,on:{object_type:p,priv_level:[b]},to_from:v[0],user_or_roles:d,with:y}},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var f,p,b,v,d,y;r===a&&(r=vi,"GRANT"===t.substr(vi,5)?(e="GRANT",vi+=5):(e=a,0===wi&&gi(be)),e!==a&&Gp()!==a?("PROXY"===t.substr(vi,5)?(n="PROXY",vi+=5):(n=a,0===wi&&gi(ve)),n!==a&&Gp()!==a&&(o=Rf())!==a&&Gp()!==a&&(s=ic())!==a&&Gp()!==a&&(u=pf())!==a&&Gp()!==a&&(i=cc())!==a&&Gp()!==a?((c=lc())===a&&(c=null),c!==a?(di=r,e=function(t,r,e,n){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"grant",keyword:"proxy",objects:[{priv:{type:"origin",value:"proxy"}}],on:t,to_from:r[0],user_or_roles:e,with:n}}}(s,u,i,c),r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"GRANT"===t.substr(vi,5)?(e="GRANT",vi+=5):(e=a,0===wi&&gi(be)),e!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Ll())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ll())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Ll())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=w(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&Gp()!==a&&(o=pf())!==a&&Gp()!==a&&(s=cc())!==a&&Gp()!==a?((u=lc())===a&&(u=null),u!==a?(di=r,e=function(t,r,e,n){return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"grant",keyword:"role",objects:t.map(t=>({priv:{type:"string",value:t}})),to_from:r[0],user_or_roles:e,with:n}}}(n,o,s,u),r=e):(vi=r,r=a)):(vi=r,r=a)));return r}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;r=vi,"explain"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(xa));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&(n=yc())!==a?(di=r,o=n,e={tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:"explain",expr:o}},r=e):(vi=r,r=a);var o;return r}()),r}function Si(){var t;return(t=Ui())===a&&(t=function(){var t,r,e,n,o,s;t=vi,(r=Gp())!==a?((e=pc())===a&&(e=null),e!==a&&Gp()!==a&&hf()!==a&&Gp()!==a&&(n=Sc())!==a&&Gp()!==a&&Sf()!==a&&Gp()!==a&&(o=Qc())!==a&&Gp()!==a?((s=kc())===a&&(s=null),s!==a?(di=t,r=function(t,r,e,n){const o={};return r&&r.forEach(t=>{const{db:r,as:e,table:n,join:a}=t,s=a?"select":"update";r&&(o[n]=r),n&&Tb.add(`${s}::${r}::${n}`)}),e&&e.forEach(t=>{if(t.table){const r=Cb(t.table);Tb.add(`update::${o[r]||null}::${r}`)}Sb.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(Tb),columnList:gb(Sb),ast:{with:t,type:"update",table:r,set:e,where:n}}}(e,n,o,s),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,s,u,i,c,l;t=vi,(r=Yc())!==a&&Gp()!==a?((e=gf())===a&&(e=null),e!==a&&Gp()!==a?((n=Af())===a&&(n=null),n!==a&&Gp()!==a&&(o=xc())!==a&&Gp()!==a?((s=Gc())===a&&(s=null),s!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(u=hl())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a&&(i=$c())!==a&&Gp()!==a?((c=Hc())===a&&(c=null),c!==a&&Gp()!==a?((l=Bc())===a&&(l=null),l!==a?(di=t,r=function(t,r,e,n,o,a,s,u,i){if(n&&(Tb.add(`insert::${n.db}::${n.table}`),n.as=null),a){let t=n&&n.table||null;Array.isArray(s)&&s.forEach((t,r)=>{if(t.value.length!=a.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),a.forEach(r=>Sb.add(`insert::${t}::${r}`))}let c=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t,table:[n],columns:a,values:s,partition:o,prefix:c,on_duplicate_update:u,returning:i}}}(r,e,n,o,s,u,i,c,l),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,s,u,i,c;t=vi,(r=Yc())!==a&&Gp()!==a?((e=gf())===a&&(e=null),e!==a&&Gp()!==a?((n=Af())===a&&(n=null),n!==a&&Gp()!==a&&(o=xc())!==a&&Gp()!==a?((s=Gc())===a&&(s=null),s!==a&&Gp()!==a&&(u=$c())!==a&&Gp()!==a?((i=Hc())===a&&(i=null),i!==a&&Gp()!==a?((c=Bc())===a&&(c=null),c!==a?(di=t,r=function(t,r,e,n,o,a,s,u){n&&(Tb.add(`insert::${n.db}::${n.table}`),Sb.add(`insert::${n.table}::(.*)`),n.as=null);const i=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:i,on_duplicate_update:s,returning:u}}}(r,e,n,o,s,u,i,c),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,s,u,i,c;t=vi,(r=Yc())!==a&&Gp()!==a?((e=gf())===a&&(e=null),e!==a&&Gp()!==a?((n=Af())===a&&(n=null),n!==a&&Gp()!==a&&(o=xc())!==a&&Gp()!==a?((s=Gc())===a&&(s=null),s!==a&&Gp()!==a&&Sf()!==a&&Gp()!==a&&(u=Qc())!==a&&Gp()!==a?((i=Hc())===a&&(i=null),i!==a&&Gp()!==a?((c=Bc())===a&&(c=null),c!==a?(di=t,r=function(t,r,e,n,o,a,s,u){n&&(Tb.add(`insert::${n.db}::${n.table}`),Sb.add(`insert::${n.table}::(.*)`),n.as=null);const i=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{type:t,table:[n],columns:null,partition:o,prefix:i,set:a,on_duplicate_update:s,returning:u}}}(r,e,n,o,s,u,i,c),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,s,u;t=vi,(r=Gp())!==a?((e=pc())===a&&(e=null),e!==a&&Gp()!==a&&Of()!==a&&Gp()!==a?((n=Sc())===a&&(n=null),n!==a&&Gp()!==a&&(o=Cc())!==a&&Gp()!==a?((s=kc())===a&&(s=null),s!==a&&Gp()!==a?((u=Bc())===a&&(u=null),u!==a?(di=t,r=function(t,r,e,n,o){if(e){(Array.isArray(e)?e:e.expr).forEach(t=>{const{db:r,as:e,table:n,join:o}=t,a=o?"select":"delete";n&&Tb.add(`${a}::${r}::${n}`),o||Sb.add(`delete::${n}::(.*)`)})}if(null===r&&1===e.length){const t=e[0];r=[{db:t.db,table:t.table,as:t.as,addition:!0}]}return{tableList:Array.from(Tb),columnList:gb(Sb),ast:{with:t,type:"delete",table:r,from:e,where:n,returning:o}}}(e,n,o,s,u),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(t=Ti())===a&&(t=function(){var t,r;t=[],r=Jp();for(;r!==a;)t.push(r),r=Jp();return t}()),t}function _i(){var r,e,n,o;return r=vi,(e=function(){var r,e,n,o;r=vi,"union"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Xa));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=$f())===a&&(n=Gf()),n===a&&(n=null),n!==a?(di=r,r=e=(o=n)?"union "+o.toLowerCase():"union"):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"minus"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ka));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e="minus"),(r=e)===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"intersect"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Za));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e="intersect"),r=e)),r}function Ui(){var t,r,e,n,o,s,u,i;if(t=vi,(r=fc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=_i())!==a&&(u=Gp())!==a&&(i=fc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=_i())!==a&&(u=Gp())!==a&&(i=fc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a&&(n=Gp())!==a?((o=Vc())===a&&(o=null),o!==a&&(s=Gp())!==a?((u=qc())===a&&(u=null),u!==a?(di=t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&(t._limit=n),{tableList:Array.from(Tb),columnList:gb(Sb),ast:t}}(r,e,o,u)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)}else vi=t,t=a;return t}function Ii(){var t,r,e;return t=vi,(r=rl())!==a&&Gp()!==a?((e=Ff())===a&&(e=Bf()),e===a&&(e=null),e!==a?(di=t,t=r=c(r,e)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=function(){var t,r,e;t=vi,(r=yl())!==a&&Gp()!==a?((e=Ff())===a&&(e=Bf()),e===a&&(e=null),e!==a?(di=t,r=c(r,e),t=r):(vi=t,t=a)):(vi=t,t=a);return t}()),t}function xi(){var r,e;return r=vi,"if"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(f)),e!==a&&Gp()!==a&&Zf()!==a&&Gp()!==a&&Kf()!==a?(di=r,r=e="IF NOT EXISTS"):(vi=r,r=a),r}function Ni(){var r,e,n;return r=vi,(e=ic())!==a&&Gp()!==a?((n=function(){var r,e,n,o,s,u,i,c,l;return r=vi,t.substr(vi,10)===p?(e=p,vi+=10):(e=a,0===wi&&gi(b)),e!==a&&Gp()!==a?(n=vi,"with"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(v)),o!==a&&(s=Gp())!==a&&(u=Ll())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=null),n!==a&&(o=Gp())!==a?("by"===t.substr(vi,2).toLowerCase()?(s=t.substr(vi,2),vi+=2):(s=a,0===wi&&gi(d)),s!==a&&(u=Gp())!==a?("random"===t.substr(vi,6).toLowerCase()?(i=t.substr(vi,6),vi+=6):(i=a,0===wi&&gi(y)),i!==a&&Gp()!==a?("password"===t.substr(vi,8).toLowerCase()?(c=t.substr(vi,8),vi+=8):(c=a,0===wi&&gi(h)),c!==a?(di=r,r=e={keyword:["identified",(l=n)&&l[0].toLowerCase()].filter(t=>t).join(" "),auth_plugin:l&&l[2],value:{prefix:"by",type:"origin",value:"random password"}}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,t.substr(vi,10)===p?(e=p,vi+=10):(e=a,0===wi&&gi(b)),e!==a&&Gp()!==a?(n=vi,"with"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(v)),o!==a&&(s=Gp())!==a&&(u=Ll())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=null),n!==a&&(o=Gp())!==a?("by"===t.substr(vi,2).toLowerCase()?(s=t.substr(vi,2),vi+=2):(s=a,0===wi&&gi(d)),s!==a&&(u=Gp())!==a&&(i=zl())!==a?(di=r,r=e=function(t,r){return r.prefix="by",{keyword:["identified",t&&t[0].toLowerCase()].filter(t=>t).join(" "),auth_plugin:t&&t[2],value:r}}(n,i)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,t.substr(vi,10)===p?(e=p,vi+=10):(e=a,0===wi&&gi(b)),e!==a&&Gp()!==a?("with"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(v)),n!==a&&(o=Gp())!==a&&(s=Ll())!==a&&(u=Gp())!==a?("as"===t.substr(vi,2).toLowerCase()?(i=t.substr(vi,2),vi+=2):(i=a,0===wi&&gi(m)),i!==a&&Gp()!==a&&(c=zl())!==a?(di=r,r=e=function(t,r){return r.prefix="as",{keyword:"identified with",auth_plugin:t&&t[2],value:r}}(s,c)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a))),r}())===a&&(n=null),n!==a?(di=r,r=e={user:e,auth_option:n}):(vi=r,r=a)):(vi=r,r=a),r}function ki(){var r,e,n;return r=vi,"none"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(L)),e===a&&("ssl"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(j)),e===a&&("x509"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(C)))),e!==a&&(di=r,e={type:"origin",value:e}),(r=e)===a&&(r=vi,"cipher"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(g)),e===a&&("issuer"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(E)),e===a&&("subject"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(A)))),e!==a&&Gp()!==a&&(n=zl())!==a?(di=r,r=e=T(e,n)):(vi=r,r=a)),r}function Ri(){var r,e,n;return r=vi,"max_queries_per_hour"===t.substr(vi,20).toLowerCase()?(e=t.substr(vi,20),vi+=20):(e=a,0===wi&&gi(_)),e===a&&("max_updates_per_hour"===t.substr(vi,20).toLowerCase()?(e=t.substr(vi,20),vi+=20):(e=a,0===wi&&gi(U)),e===a&&("max_connections_per_hour"===t.substr(vi,24).toLowerCase()?(e=t.substr(vi,24),vi+=24):(e=a,0===wi&&gi(I)),e===a&&("max_user_connections"===t.substr(vi,20).toLowerCase()?(e=t.substr(vi,20),vi+=20):(e=a,0===wi&&gi(x))))),e!==a&&Gp()!==a&&(n=nf())!==a?(di=r,r=e=T(e,n)):(vi=r,r=a),r}function Mi(){var r,e,n,o,s,u;return r=vi,"password"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(h)),e!==a&&Gp()!==a?("expire"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(N)),n!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(k)),o===a&&("never"===t.substr(vi,5).toLowerCase()?(o=t.substr(vi,5),vi+=5):(o=a,0===wi&&gi(R)),o===a&&(o=Zc())),o!==a?(di=r,r=e={keyword:"password expire",value:"string"==typeof(u=o)?{type:"origin",value:u}:u}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"password"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(h)),e!==a&&Gp()!==a?("history"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(M)),n!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(k)),o===a&&(o=nf()),o!==a?(di=r,r=e=function(t){return{keyword:"password history",value:"string"==typeof t?{type:"origin",value:t}:t}}(o)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"password"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(h)),e!==a&&Gp()!==a?("REUSE"===t.substr(vi,5)?(n="REUSE",vi+=5):(n=a,0===wi&&gi(V)),n!==a&&Gp()!==a&&(o=Zc())!==a?(di=r,r=e=function(t){return{keyword:"password reuse",value:t}}(o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"password"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(h)),e!==a&&Gp()!==a?("require"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(S)),n!==a&&Gp()!==a?("current"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(D)),o!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(s=t.substr(vi,7),vi+=7):(s=a,0===wi&&gi(k)),s===a&&("optional"===t.substr(vi,8).toLowerCase()?(s=t.substr(vi,8),vi+=8):(s=a,0===wi&&gi(P))),s!==a?(di=r,r=e=function(t){return{keyword:"password require current",value:{type:"origin",value:t}}}(s)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"failed_login_attempts"===t.substr(vi,21).toLowerCase()?(e=t.substr(vi,21),vi+=21):(e=a,0===wi&&gi(q)),e!==a&&Gp()!==a&&(n=nf())!==a?(di=r,r=e=function(t){return{keyword:"failed_login_attempts",value:t}}(n)):(vi=r,r=a),r===a&&(r=vi,"password_lock_time"===t.substr(vi,18).toLowerCase()?(e=t.substr(vi,18),vi+=18):(e=a,0===wi&&gi(Q)),e!==a&&Gp()!==a?((n=nf())===a&&("unbounded"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(F))),n!==a?(di=r,r=e=function(t){return{keyword:"password_lock_time",value:"string"==typeof t?{type:"origin",value:t}:t}}(n)):(vi=r,r=a)):(vi=r,r=a)))))),r}function Vi(){var r;return(r=Pi())===a&&(r=Ki())===a&&(r=Zi())===a&&(r=function(){var r;(r=function(){var r,e,n,o,s,u;r=vi,(e=Ji())===a&&(e=null);e!==a&&Gp()!==a?("primary key"===t.substr(vi,11).toLowerCase()?(n=t.substr(vi,11),vi+=11):(n=a,0===wi&&gi(Jt)),n!==a&&Gp()!==a?((o=Ec())===a&&(o=null),o!==a&&Gp()!==a&&(s=Xi())!==a&&Gp()!==a?((u=Ac())===a&&(u=null),u!==a?(di=r,c=n,l=o,f=s,p=u,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var i,c,l,f,p;return r}())===a&&(r=function(){var t,r,e,n,o,s,u,i;t=vi,(r=Ji())===a&&(r=null);r!==a&&Gp()!==a&&(e=Rp())!==a&&Gp()!==a?((n=Ip())===a&&(n=xp()),n===a&&(n=null),n!==a&&Gp()!==a?((o=Sl())===a&&(o=null),o!==a&&Gp()!==a?((s=Ec())===a&&(s=null),s!==a&&Gp()!==a&&(u=Xi())!==a&&Gp()!==a?((i=Ac())===a&&(i=null),i!==a?(di=t,l=e,f=n,p=o,b=s,v=u,d=i,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:d},t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);var c,l,f,p,b,v,d;return t}())===a&&(r=function(){var r,e,n,o,s,u;r=vi,(e=Ji())===a&&(e=null);e!==a&&Gp()!==a?("foreign key"===t.substr(vi,11).toLowerCase()?(n=t.substr(vi,11),vi+=11):(n=a,0===wi&&gi(rr)),n!==a&&Gp()!==a?((o=Sl())===a&&(o=null),o!==a&&Gp()!==a&&(s=vc())!==a&&Gp()!==a?((u=zi())===a&&(u=null),u!==a?(di=r,c=n,l=o,f=s,p=u,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var i,c,l,f,p;return r}())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f;r=vi,(e=Ji())===a&&(e=null);e!==a&&Gp()!==a?("check"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(X)),n!==a&&Gp()!==a?(o=vi,"not"===t.substr(vi,3).toLowerCase()?(s=t.substr(vi,3),vi+=3):(s=a,0===wi&&gi(zt)),s!==a&&(u=Gp())!==a?("for"===t.substr(vi,3).toLowerCase()?(i=t.substr(vi,3),vi+=3):(i=a,0===wi&&gi(vt)),i!==a&&(c=Gp())!==a?("replication"===t.substr(vi,11).toLowerCase()?(l=t.substr(vi,11),vi+=11):(l=a,0===wi&&gi(tr)),l!==a&&(f=Gp())!==a?o=s=[s,u,i,c,l,f]:(vi=o,o=a)):(vi=o,o=a)):(vi=o,o=a),o===a&&(o=null),o!==a&&(s=Qp())!==a&&(u=Gp())!==a&&(i=el())!==a&&(c=Gp())!==a&&(l=Fp())!==a?(di=r,p=e,b=o,v=i,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication"},definition:[v],resource:"constraint"},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var p,b,v;return r}());return r}()),r}function Di(){var r,e,n,o,s;return r=vi,(e=function(){var r,e;r=vi,(e=function(){var r,e,n,o;r=vi,"not null"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(va));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={type:"not null",value:"not null"});return r=e}())===a&&(e=Jl()),e!==a&&(di=r,(s=e)&&!s.value&&(s.value="null"),e={nullable:s}),(r=e)===a&&(r=vi,(e=function(){var t,r;t=vi,ff()!==a&&Gp()!==a&&(r=rl())!==a?(di=t,t={type:"default",value:r}):(vi=t,t=a);return t}())!==a&&(di=r,e={default_val:e}),(r=e)===a&&(r=vi,"auto_increment"===t.substr(vi,14).toLowerCase()?(e=t.substr(vi,14),vi+=14):(e=a,0===wi&&gi(st)),e!==a&&(di=r,e={auto_increment:e.toLowerCase()}),(r=e)===a&&(r=vi,"unique"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ut)),e!==a&&Gp()!==a?("key"===t.substr(vi,3).toLowerCase()?(n=t.substr(vi,3),vi+=3):(n=a,0===wi&&gi(it)),n===a&&(n=null),n!==a?(di=r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"primary"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(ct)),e===a&&(e=null),e!==a&&Gp()!==a?("key"===t.substr(vi,3).toLowerCase()?(n=t.substr(vi,3),vi+=3):(n=a,0===wi&&gi(it)),n!==a?(di=r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Wp())!==a&&(di=r,e={comment:e}),(r=e)===a&&(r=vi,(e=Qi())!==a&&(di=r,e={collate:e}),(r=e)===a&&(r=vi,(e=function(){var r,e,n;r=vi,"column_format"===t.substr(vi,13).toLowerCase()?(e=t.substr(vi,13),vi+=13):(e=a,0===wi&&gi(Ot));e!==a&&Gp()!==a?("fixed"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(Lt)),n===a&&("dynamic"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(jt)),n===a&&("default"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(k)))),n!==a?(di=r,e={type:"column_format",value:n.toLowerCase()},r=e):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={column_format:e}),(r=e)===a&&(r=vi,(e=function(){var r,e,n;r=vi,"storage"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ct));e!==a&&Gp()!==a?("disk"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(gt)),n===a&&("memory"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(Et))),n!==a?(di=r,e={type:"storage",value:n.toLowerCase()},r=e):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={storage:e}),(r=e)===a&&(r=vi,(e=zi())!==a&&(di=r,e={reference_definition:e}),(r=e)===a&&(r=vi,(e=function(){var r,e,n,o,s,u,i,c;r=vi,(e=Ji())===a&&(e=null);e!==a&&Gp()!==a?("check"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(X)),n!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(o=el())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?(s=vi,(u=Zf())===a&&(u=null),u!==a&&(i=Gp())!==a?("enforced"===t.substr(vi,8).toLowerCase()?(c=t.substr(vi,8),vi+=8):(c=a,0===wi&&gi(er)),c!==a?s=u=[u,i,c]:(vi=s,s=a)):(vi=s,s=a),s===a&&(s=null),s!==a?(di=r,e=function(t,r,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:r.toLowerCase(),keyword:t&&t.keyword,constraint:t&&t.constraint,definition:[e],enforced:o.filter(t=>t).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,s),r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={check:e}),(r=e)===a&&(r=vi,(e=ec())!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a&&(o=wl())!==a?(di=r,r=e=function(t,r,e){return{character_set:{type:t,value:e,symbol:r}}}(e,n,o)):(vi=r,r=a)):(vi=r,r=a)))))))))))),r}function Pi(){var r,e,n,o,s,u,i,c,l;return r=vi,(e=yl())!==a&&Gp()!==a&&(n=lb())!==a&&Gp()!==a?((o=function(){var r,e,n,o,s,u,i,c;r=vi,e=vi,(n=function(){var r,e,n,o,s;r=vi,e=vi,"generated"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(At));n!==a&&(o=Gp())!==a?("always"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi(Tt)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a);e!==a&&(di=r,e=e.join("").toLowerCase());return r=e}())===a&&(n=null);n!==a&&(o=Gp())!==a?("as"===t.substr(vi,2).toLowerCase()?(s=t.substr(vi,2),vi+=2):(s=a,0===wi&&gi(m)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a);if(e!==a)if((n=Gp())!==a)if((o=Qp())!==a)if((s=Gp())!==a)if((u=Zl())===a&&(u=rl()),u!==a)if(Gp()!==a)if(Fp()!==a)if(Gp()!==a){for(i=[],"stored"===t.substr(vi,6).toLowerCase()?(c=t.substr(vi,6),vi+=6):(c=a,0===wi&&gi(St)),c===a&&("virtual"===t.substr(vi,7).toLowerCase()?(c=t.substr(vi,7),vi+=7):(c=a,0===wi&&gi(_t)));c!==a;)i.push(c),"stored"===t.substr(vi,6).toLowerCase()?(c=t.substr(vi,6),vi+=6):(c=a,0===wi&&gi(St)),c===a&&("virtual"===t.substr(vi,7).toLowerCase()?(c=t.substr(vi,7),vi+=7):(c=a,0===wi&&gi(_t)));i!==a?(di=r,l=i,e={type:"generated",expr:u,value:e.filter(t=>"string"==typeof t).join(" ").toLowerCase(),storage_type:l&&l[0]&&l[0].toLowerCase()},r=e):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;var l;return r}())===a&&(o=null),o!==a&&Gp()!==a?((s=function(){var t,r,e,n,o,s;if(t=vi,(r=Di())!==a)if(Gp()!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Di())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Di())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;return t}())===a&&(s=null),s!==a?(di=r,u=e,i=n,c=o,l=s,Sb.add(`create::${u.table}::${u.column}`),r=e={column:u,definition:i,generated:c,resource:"column",...l||{}}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function qi(){var r,e,n,o,s;return r=vi,"definer"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(nt)),e!==a&&Gp()!==a&&Sp()!==a&&Gp()!==a?((n=Al())===a&&(n=zl()),n!==a&&Gp()!==a?(64===t.charCodeAt(vi)?(o="@",vi++):(o=a,0===wi&&gi(lt)),o!==a&&Gp()!==a?((s=Al())===a&&(s=zl()),s!==a?(di=r,r=e=function(t,r){const e=wb(t,"@",r);return wb("=",{type:"origin",value:"definer"},e)}(n,s)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"definer"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(nt)),e!==a&&Gp()!==a&&Sp()!==a&&Gp()!==a&&(n=Cp())!==a&&Gp()!==a&&(o=Qp())!==a&&Gp()!==a&&(s=Fp())!==a?(di=r,r=e=ft()):(vi=r,r=a),r===a&&(r=vi,"definer"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(nt)),e!==a&&Gp()!==a&&Sp()!==a&&Gp()!==a&&(n=Cp())!==a?(di=r,r=e=ft()):(vi=r,r=a))),r}function Qi(){var r,e,n;return r=vi,function(){var r,e,n,o;r=vi,"collate"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(vr));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="COLLATE"):(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a?((e=Sp())===a&&(e=null),e!==a&&Gp()!==a&&(n=Ll())!==a?(di=r,r={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(vi=r,r=a)):(vi=r,r=a),r}function Fi(){var r,e,n;return r=vi,"if"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(Ut)),e!==a&&Gp()!==a?("exists"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(It)),n!==a?(di=r,r=e="if exists"):(vi=r,r=a)):(vi=r,r=a),r}function Bi(){var r,e,n;return r=vi,"first"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(xt)),e!==a&&(di=r,e={keyword:e}),(r=e)===a&&(r=vi,"after"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Nt)),e!==a&&Gp()!==a&&(n=yl())!==a?(di=r,r=e=function(t,r){return{keyword:t,expr:r}}(e,n)):(vi=r,r=a)),r}function $i(){var r,e,n;return(r=function(){var t,r,e,n,o,s;t=vi,(r=_p())!==a&&Gp()!==a?((e=Up())===a&&(e=null),e!==a&&Gp()!==a?((n=xi())===a&&(n=null),n!==a&&Gp()!==a&&(o=Pi())!==a&&Gp()!==a?((s=Bi())===a&&(s=null),s!==a?(di=t,u=e,i=n,c=o,l=s,r={action:"add",...c,suffix:l,keyword:u,if_not_exists:i,resource:"column",type:"alter"},t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);var u,i,c,l;t===a&&(t=vi,(r=_p())!==a&&Gp()!==a&&(e=Pi())!==a&&Gp()!==a?((n=Bi())===a&&(n=null),n!==a?(di=t,r=function(t,r){return{action:"add",...t,suffix:r,resource:"column",type:"alter"}}(e,n),t=r):(vi=t,t=a)):(vi=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,s,u;r=vi,(e=vf())!==a&&Gp()!==a?("primary"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(ct)),n!==a&&(o=Gp())!==a&&(s=xp())!==a?(di=r,r=e={action:"drop",key:"",keyword:"primary key",resource:"key",type:"alter"}):(vi=r,r=a)):(vi=r,r=a);r===a&&(r=vi,(e=vf())!==a&&Gp()!==a?(n=vi,"foreign"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(Bt)),o===a&&(o=null),o!==a&&(s=Gp())!==a&&(u=xp())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=Ip()),n!==a&&(o=Gp())!==a&&(s=Ll())!==a?(di=r,e=function(t,r){const e=Array.isArray(t)?"key":"index";return{action:"drop",[e]:r,keyword:Array.isArray(t)?""+[t[0],t[2]].filter(t=>t).join(" ").toLowerCase():t.toLowerCase(),resource:e,type:"alter"}}(n,s),r=e):(vi=r,r=a)):(vi=r,r=a));return r}())===a&&(r=function(){var t,r,e,n;t=vi,(r=vf())!==a&&Gp()!==a&&(e=Up())!==a&&Gp()!==a&&(n=yl())!==a?(di=t,t=r={action:"drop",column:n,keyword:e,resource:"column",type:"alter"}):(vi=t,t=a);t===a&&(t=vi,(r=vf())!==a&&Gp()!==a&&(e=yl())!==a?(di=t,r=function(t){return{action:"drop",column:t,resource:"column",type:"alter"}}(e),t=r):(vi=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,s;r=vi,(e=function(){var r,e,n,o;r=vi,"modify"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Nu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MODIFY"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=Up())===a&&(n=null),n!==a&&Gp()!==a&&(o=Pi())!==a&&Gp()!==a?((s=Bi())===a&&(s=null),s!==a?(di=r,u=o,i=s,e={action:"modify",keyword:n,...u,suffix:i,resource:"column",type:"alter"},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var u,i;return r}())===a&&(r=function(){var t,r,e;t=vi,(r=_p())!==a&&Gp()!==a&&(e=Ki())!==a?(di=t,n=e,r={action:"add",type:"alter",...n},t=r):(vi=t,t=a);var n;return t}())===a&&(r=function(){var t,r,e;t=vi,(r=_p())!==a&&Gp()!==a&&(e=Zi())!==a?(di=t,n=e,r={action:"add",type:"alter",...n},t=r):(vi=t,t=a);var n;return t}())===a&&(r=function(){var t,r,e,n,o;t=vi,(r=Cf())!==a&&Gp()!==a&&Up()!==a&&Gp()!==a&&(e=yl())!==a&&Gp()!==a?((n=pf())===a&&(n=_f()),n===a&&(n=null),n!==a&&Gp()!==a&&(o=yl())!==a?(di=t,u=o,r={action:"rename",type:"alter",resource:"column",keyword:"column",old_column:e,prefix:(s=n)&&s[0].toLowerCase(),column:u},t=r):(vi=t,t=a)):(vi=t,t=a);var s,u;return t}())===a&&(r=function(){var t,r,e,n;t=vi,(r=Cf())!==a&&Gp()!==a?((e=pf())===a&&(e=_f()),e===a&&(e=null),e!==a&&Gp()!==a&&(n=Ll())!==a?(di=t,s=n,r={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:s},t=r):(vi=t,t=a)):(vi=t,t=a);var o,s;return t}())===a&&(r=Hi())===a&&(r=Yi())===a&&(r=function(){var r,e,n,o,s,u;r=vi,"change"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Xt));e!==a&&Gp()!==a?((n=Up())===a&&(n=null),n!==a&&Gp()!==a&&(o=yl())!==a&&Gp()!==a&&(s=Pi())!==a&&Gp()!==a?((u=Bi())===a&&(u=null),u!==a?(di=r,i=n,c=s,l=u,e={action:"change",old_column:o,...c,keyword:i,resource:"column",type:"alter",suffix:l},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var i,c,l;return r}())===a&&(r=function(){var r,e,n,o,s;r=vi,"drop"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Mt));e===a&&("truncate"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Vt)),e===a&&("discard"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Dt)),e===a&&("import"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Pt)),e===a&&("coalesce"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(qt)),e===a&&("analyze"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Qt)),e===a&&("check"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(X))))))));e!==a&&Gp()!==a&&(n=Ef())!==a&&Gp()!==a&&(o=mc())!==a&&Gp()!==a?("tablespace"===t.substr(vi,10).toLowerCase()?(s=t.substr(vi,10),vi+=10):(s=a,0===wi&&gi(Ft)),s===a&&(s=null),s!==a?(di=r,e=function(t,r,e,n){const o={action:t.toLowerCase(),keyword:r,resource:"partition",type:"alter",partitions:e};return n&&(o.suffix={keyword:n}),o}(e,n,o,s),r=e):(vi=r,r=a)):(vi=r,r=a);r===a&&(r=vi,(e=_p())!==a&&Gp()!==a&&(n=Ef())!==a&&Gp()!==a&&(o=Qp())!==a&&Gp()!==a&&(s=function(){var t,r,e,n,o,s,u,c;if(t=vi,(r=Gi())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=Gi())!==a?n=o=[o,s,u,c]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=Gi())!==a?n=o=[o,s,u,c]:(vi=n,n=a);e!==a?(di=t,r=i(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e={action:"add",keyword:n,resource:"partition",type:"alter",partitions:s}):(vi=r,r=a));return r}())===a&&(r=vi,(e=oc())!==a&&(di=r,(n=e).resource=n.keyword,n[n.keyword]=n.value,delete n.value,e={type:"alter",...n}),r=e),r}function Gi(){var r,e,n,o,s;return r=vi,Ef()!==a&&Gp()!==a&&(e=wl())!==a&&Gp()!==a&&Df()!==a&&Gp()!==a?("less"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(kt)),n!==a&&Gp()!==a?("than"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(Rt)),o!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(s=nf())!==a&&Gp()!==a&&Fp()!==a?(di=r,r={name:e,value:{type:"less than",expr:s,parentheses:!0}}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function Hi(){var r,e,n,o;return r=vi,"algorithm"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Z)),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(k)),o===a&&("instant"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi($t)),o===a&&("inplace"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(Gt)),o===a&&("copy"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(Ht))))),o!==a?(di=r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function Yi(){var r,e,n,o;return r=vi,"lock"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi($)),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(k)),o===a&&("none"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(L)),o===a&&("shared"===t.substr(vi,6).toLowerCase()?(o=t.substr(vi,6),vi+=6):(o=a,0===wi&&gi(Yt)),o===a&&("exclusive"===t.substr(vi,9).toLowerCase()?(o=t.substr(vi,9),vi+=9):(o=a,0===wi&&gi(Wt))))),o!==a?(di=r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function Wi(){var r,e,n,o,s,u,i;if(r=vi,(e=Tl())!==a)if(Gp()!==a)if((n=Qp())!==a)if(Gp()!==a){if(o=[],Kt.test(t.charAt(vi))?(s=t.charAt(vi),vi++):(s=a,0===wi&&gi(Zt)),s!==a)for(;s!==a;)o.push(s),Kt.test(t.charAt(vi))?(s=t.charAt(vi),vi++):(s=a,0===wi&&gi(Zt));else o=a;o!==a&&(s=Gp())!==a&&Fp()!==a&&Gp()!==a?((u=Ff())===a&&(u=Bf()),u===a&&(u=null),u!==a?(di=r,i=u,r=e={type:"column_ref",column:e,suffix:`(${parseInt(o.join(""),10)})`,order_by:i}):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;return r===a&&(r=vi,(e=Tl())!==a&&Gp()!==a?((n=Ff())===a&&(n=Bf()),n===a&&(n=null),n!==a?(di=r,r=e=function(t,r){return{type:"column_ref",column:t,order_by:r}}(e,n)):(vi=r,r=a)):(vi=r,r=a)),r}function Xi(){var t,r,e;return t=vi,Qp()!==a&&Gp()!==a?((r=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Wi())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Wi())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Wi())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(r=Kc()),r!==a&&Gp()!==a&&Fp()!==a?(di=t,t=(e=r).type?e.value:e):(vi=t,t=a)):(vi=t,t=a),t}function Ki(){var t,r,e,n,o,s,u,i;return t=vi,(r=Ip())===a&&(r=xp()),r!==a&&Gp()!==a?((e=Sl())===a&&(e=null),e!==a&&Gp()!==a?((n=Ec())===a&&(n=null),n!==a&&Gp()!==a&&(o=Xi())!==a&&Gp()!==a?((s=Ac())===a&&(s=null),s!==a&&Gp()!==a?(di=t,u=n,i=s,t=r={index:e,definition:o,keyword:r.toLowerCase(),index_type:u,resource:"index",index_options:i}):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t}function Zi(){var t,r,e,n,o,s,u,i,c;return t=vi,(r=Np())===a&&(r=kp()),r!==a&&Gp()!==a?((e=Ip())===a&&(e=xp()),e===a&&(e=null),e!==a&&Gp()!==a?((n=Sl())===a&&(n=null),n!==a&&Gp()!==a&&(o=vc())!==a&&Gp()!==a?((s=Ac())===a&&(s=null),s!==a&&Gp()!==a?(di=t,u=r,c=s,t=r={index:n,definition:o,keyword:(i=e)&&`${u.toLowerCase()} ${i.toLowerCase()}`||u.toLowerCase(),index_options:c,resource:"index"}):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t}function Ji(){var r,e,n,o;return r=vi,(e=function(){var r,e,n,o;r=vi,"constraint"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Vu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CONSTRAINT"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=Ll())===a&&(n=null),n!==a?(di=r,o=n,r=e={keyword:e.toLowerCase(),constraint:o}):(vi=r,r=a)):(vi=r,r=a),r}function zi(){var r,e,n,o,s,u,i,c,l,f;return r=vi,(e=Vp())!==a&&Gp()!==a&&(n=Sc())!==a&&Gp()!==a&&(o=vc())!==a&&Gp()!==a?("match full"===t.substr(vi,10).toLowerCase()?(s=t.substr(vi,10),vi+=10):(s=a,0===wi&&gi(nr)),s===a&&("match partial"===t.substr(vi,13).toLowerCase()?(s=t.substr(vi,13),vi+=13):(s=a,0===wi&&gi(or)),s===a&&("match simple"===t.substr(vi,12).toLowerCase()?(s=t.substr(vi,12),vi+=12):(s=a,0===wi&&gi(ar)))),s===a&&(s=null),s!==a&&Gp()!==a?((u=tc())===a&&(u=null),u!==a&&Gp()!==a?((i=tc())===a&&(i=null),i!==a?(di=r,c=s,l=u,f=i,r=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=tc())!==a&&(di=r,e={on_action:[e]}),r=e),r}function tc(){var r,e,n,o;return r=vi,Rf()!==a&&Gp()!==a?((e=Of())===a&&(e=hf()),e!==a&&Gp()!==a&&(n=function(){var r,e,n;r=vi,(e=jp())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a?((n=Kc())===a&&(n=null),n!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(vi=r,r=a)):(vi=r,r=a);r===a&&(r=vi,(e=rc())===a&&("set null"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(ir)),e===a&&("no action"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(cr)),e===a&&("set default"===t.substr(vi,11).toLowerCase()?(e=t.substr(vi,11),vi+=11):(e=a,0===wi&&gi(lr)),e===a&&(e=jp())))),e!==a&&(di=r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==a?(di=r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(vi=r,r=a)):(vi=r,r=a),r}function rc(){var r,e;return r=vi,"restrict"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(sr)),e===a&&("cascade"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(ur))),e!==a&&(di=r,e=e.toLowerCase()),r=e}function ec(){var r,e,n;return r=vi,"character"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(fr)),e!==a&&Gp()!==a?("set"===t.substr(vi,3).toLowerCase()?(n=t.substr(vi,3),vi+=3):(n=a,0===wi&&gi(pr)),n!==a?(di=r,r=e="CHARACTER SET"):(vi=r,r=a)):(vi=r,r=a),r}function nc(){var r,e,n,o,s,u,i,c,l;return r=vi,(e=ff())===a&&(e=null),e!==a&&Gp()!==a?((n=ec())===a&&("charset"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(br)),n===a&&("collate"===t.substr(vi,7).toLowerCase()?(n=t.substr(vi,7),vi+=7):(n=a,0===wi&&gi(vr)))),n!==a&&Gp()!==a?((o=Sp())===a&&(o=null),o!==a&&Gp()!==a&&(s=wl())!==a?(di=r,i=n,c=o,l=s,r=e={keyword:(u=e)&&`${u[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function oc(){var r,e,n,o,s,u,i,c,l;return r=vi,"auto_increment"===t.substr(vi,14).toLowerCase()?(e=t.substr(vi,14),vi+=14):(e=a,0===wi&&gi(st)),e===a&&("avg_row_length"===t.substr(vi,14).toLowerCase()?(e=t.substr(vi,14),vi+=14):(e=a,0===wi&&gi(dr)),e===a&&("key_block_size"===t.substr(vi,14).toLowerCase()?(e=t.substr(vi,14),vi+=14):(e=a,0===wi&&gi(yr)),e===a&&("max_rows"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(hr)),e===a&&("min_rows"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(mr)),e===a&&("stats_sample_pages"===t.substr(vi,18).toLowerCase()?(e=t.substr(vi,18),vi+=18):(e=a,0===wi&&gi(wr))))))),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a&&(o=nf())!==a?(di=r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=nc())===a&&(r=vi,"CHECKSUM"===t.substr(vi,8)?(e="CHECKSUM",vi+=8):(e=a,0===wi&&gi(Or)),e===a&&("DELAY_KEY_WRITE"===t.substr(vi,15)?(e="DELAY_KEY_WRITE",vi+=15):(e=a,0===wi&&gi(Lr))),e!==a&&Gp()!==a&&(n=Sp())!==a&&Gp()!==a?(jr.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(Cr)),o!==a?(di=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e}}(e,n,o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Mp())===a&&("connection"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(gr)),e===a&&("engine_attribute"===t.substr(vi,16).toLowerCase()?(e=t.substr(vi,16),vi+=16):(e=a,0===wi&&gi(Er)),e===a&&("secondary_engine_attribute"===t.substr(vi,26).toLowerCase()?(e=t.substr(vi,26),vi+=26):(e=a,0===wi&&gi(Ar))))),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a&&(o=zl())!==a?(di=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"data"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Tr)),e===a&&("index"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Sr))),e!==a&&Gp()!==a?("directory"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(_r)),n!==a&&Gp()!==a?((o=Sp())===a&&(o=null),o!==a&&(s=Gp())!==a&&(u=zl())!==a?(di=r,r=e=function(t,r,e){return{keyword:t.toLowerCase()+" directory",symbol:r,value:`'${e.value}'`}}(e,o,u)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"compression"===t.substr(vi,11).toLowerCase()?(e=t.substr(vi,11),vi+=11):(e=a,0===wi&&gi(Ur)),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a?(o=vi,39===t.charCodeAt(vi)?(s="'",vi++):(s=a,0===wi&&gi(Ir)),s!==a?("zlib"===t.substr(vi,4).toLowerCase()?(u=t.substr(vi,4),vi+=4):(u=a,0===wi&&gi(xr)),u===a&&("lz4"===t.substr(vi,3).toLowerCase()?(u=t.substr(vi,3),vi+=3):(u=a,0===wi&&gi(Nr)),u===a&&("none"===t.substr(vi,4).toLowerCase()?(u=t.substr(vi,4),vi+=4):(u=a,0===wi&&gi(L)))),u!==a?(39===t.charCodeAt(vi)?(i="'",vi++):(i=a,0===wi&&gi(Ir)),i!==a?o=s=[s,u,i]:(vi=o,o=a)):(vi=o,o=a)):(vi=o,o=a),o!==a?(di=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"engine"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(kr)),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a&&(o=Ul())!==a?(di=r,r=e=Rr(e,n,o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"row_format"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Mr)),e!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a?("default"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(k)),o===a&&("dynamic"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(jt)),o===a&&("fixed"===t.substr(vi,5).toLowerCase()?(o=t.substr(vi,5),vi+=5):(o=a,0===wi&&gi(Lt)),o===a&&("compressed"===t.substr(vi,10).toLowerCase()?(o=t.substr(vi,10),vi+=10):(o=a,0===wi&&gi(Vr)),o===a&&("redundant"===t.substr(vi,9).toLowerCase()?(o=t.substr(vi,9),vi+=9):(o=a,0===wi&&gi(Dr)),o===a&&("compact"===t.substr(vi,7).toLowerCase()?(o=t.substr(vi,7),vi+=7):(o=a,0===wi&&gi(Pr))))))),o!==a?(di=r,r=e=Rr(e,n,o)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a))))))),r}function ac(){var r,e,n,o,s;return r=vi,(e=Uc())!==a&&Gp()!==a&&(n=function(){var r,e,n;return r=vi,"read"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ne)),e!==a&&Gp()!==a?("local"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(W)),n===a&&(n=null),n!==a?(di=r,r=e={type:"read",suffix:n&&"local"}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"low_priority"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(oe)),e===a&&(e=null),e!==a&&Gp()!==a?("write"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(ae)),n!==a?(di=r,r=e={type:"write",prefix:e&&"low_priority"}):(vi=r,r=a)):(vi=r,r=a)),r}())!==a?(di=r,o=e,s=n,Tb.add(`lock::${o.db}::${o.table}`),r=e={table:o,lock_type:s}):(vi=r,r=a),r}function sc(){var r;return(r=function(){var r,e,n,o,s;return r=vi,(e=$f())===a&&(e=df())===a&&(e=vi,(n=mf())!==a&&(o=Gp())!==a?("view"===t.substr(vi,4).toLowerCase()?(s=t.substr(vi,4),vi+=4):(s=a,0===wi&&gi(se)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a),e===a&&(e=mf())===a&&(e=Of())===a&&(e=vf())===a&&(e=vi,"grant"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(ue)),n!==a&&(o=Gp())!==a?("option"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi(ie)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a),e===a&&(e=Ip())===a&&(e=Lf())===a&&(e=Vp())===a&&(e=yf())===a&&(e=vi,(n=bf())!==a&&(o=Gp())!==a&&(s=gp())!==a?e=n=[n,o,s]:(vi=e,e=a),e===a&&(e=If())===a&&(e=hf())))),e!==a&&(di=r,e=ce(e)),r=e}())===a&&(r=function(){var r,e,n,o,s;return r=vi,e=vi,(n=df())!==a&&(o=Gp())!==a?("routine"===t.substr(vi,7).toLowerCase()?(s=t.substr(vi,7),vi+=7):(s=a,0===wi&&gi(le)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a),e===a&&("execute"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(fe)),e===a&&(e=vi,"grant"===t.substr(vi,5).toLowerCase()?(n=t.substr(vi,5),vi+=5):(n=a,0===wi&&gi(ue)),n!==a&&(o=Gp())!==a?("option"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi(ie)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a),e===a&&(e=vi,(n=mf())!==a&&(o=Gp())!==a?("routine"===t.substr(vi,7).toLowerCase()?(s=t.substr(vi,7),vi+=7):(s=a,0===wi&&gi(le)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a)))),e!==a&&(di=r,e=ce(e)),r=e}()),r}function uc(){var t,r,e,n,o,s,u,i,c;return t=vi,(r=sc())!==a&&Gp()!==a?(e=vi,(n=Qp())!==a&&(o=Gp())!==a&&(s=Rc())!==a&&(u=Gp())!==a&&(i=Fp())!==a?e=n=[n,o,s,u,i]:(vi=e,e=a),e===a&&(e=null),e!==a?(di=t,t=r={priv:r,columns:(c=e)&&c[2]}):(vi=t,t=a)):(vi=t,t=a),t}function ic(){var r,e,n,o,s,u,i;return r=vi,(e=Ll())!==a&&Gp()!==a?(n=vi,64===t.charCodeAt(vi)?(o="@",vi++):(o=a,0===wi&&gi(lt)),o!==a&&(s=Gp())!==a&&(u=Ll())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=null),n!==a?(di=r,r=e={name:{type:"single_quote_string",value:e},host:(i=n)?{type:"single_quote_string",value:i[2]}:null}):(vi=r,r=a)):(vi=r,r=a),r}function cc(){var t,r,e,n,o,s,u,i;if(t=vi,(r=ic())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=ic())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=ic())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=w(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function lc(){var r,e,n;return r=vi,qf()!==a&&Gp()!==a?("admin"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(pe)),e!==a&&Gp()!==a?("option"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(ie)),n!==a?(di=r,r={type:"origin",value:"with admin option"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function fc(){var r,e,n,o,s,u,i;return(r=yc())===a&&(r=vi,e=vi,40===t.charCodeAt(vi)?(n="(",vi++):(n=a,0===wi&&gi(de)),n!==a&&(o=Gp())!==a&&(s=fc())!==a&&(u=Gp())!==a?(41===t.charCodeAt(vi)?(i=")",vi++):(i=a,0===wi&&gi(ye)),i!==a?e=n=[n,o,s,u,i]:(vi=e,e=a)):(vi=e,e=a),e!==a&&(di=r,e={...e[2],parentheses_symbol:!0}),r=e),r}function pc(){var r,e,n,o,s,u,i,c,l;if(r=vi,qf()!==a)if(Gp()!==a)if((e=bc())!==a){for(n=[],o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=bc())!==a?o=s=[s,u,i,c]:(vi=o,o=a);o!==a;)n.push(o),o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=bc())!==a?o=s=[s,u,i,c]:(vi=o,o=a);n!==a?(di=r,r=at(e,n)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;return r===a&&(r=vi,Gp()!==a&&qf()!==a&&(e=Gp())!==a&&(n=function(){var r,e,n,o;r=vi,"recursive"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Ta));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(o=Gp())!==a&&(s=bc())!==a?(di=r,(l=s).recursive=!0,r=[l]):(vi=r,r=a)),r}function bc(){var t,r,e,n,o,s,u;return t=vi,(r=zl())===a&&(r=Ul())===a&&(r=xc()),r!==a&&Gp()!==a?((e=vc())===a&&(e=null),e!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=Ui())!==a&&Gp()!==a&&Fp()!==a?(di=t,s=e,u=n,"string"==typeof(o=r)&&(o={type:"default",value:o}),o.table&&(o={type:"default",value:o.table}),t=r={name:o,stmt:u,columns:s}):(vi=t,t=a)):(vi=t,t=a),t}function vc(){var t,r;return t=vi,Qp()!==a&&Gp()!==a&&(r=function(){var t;(t=Rc())===a&&(t=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Zl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Zl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Zl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=at(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}());return t}())!==a&&Gp()!==a&&Fp()!==a?(di=t,t=r):(vi=t,t=a),t}function dc(){var r,e,n,o;return r=vi,(e=function(){var r,e,n,o,s,u;return r=vi,e=vi,"for"===t.substr(vi,3).toLowerCase()?(n=t.substr(vi,3),vi+=3):(n=a,0===wi&&gi(vt)),n!==a&&(o=Gp())!==a&&(s=hf())!==a?e=n=[n,o,s]:(vi=e,e=a),e!==a&&(di=r,e=`${(u=e)[0]} ${u[2][0]}`),r=e}())===a&&(e=function(){var r,e,n,o,s,u,i,c,l,f;return r=vi,e=vi,"lock"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi($)),n!==a&&(o=Gp())!==a?("in"===t.substr(vi,2).toLowerCase()?(s=t.substr(vi,2),vi+=2):(s=a,0===wi&&gi(he)),s!==a&&(u=Gp())!==a?("share"===t.substr(vi,5).toLowerCase()?(i=t.substr(vi,5),vi+=5):(i=a,0===wi&&gi(me)),i!==a&&(c=Gp())!==a?("mode"===t.substr(vi,4).toLowerCase()?(l=t.substr(vi,4),vi+=4):(l=a,0===wi&&gi(we)),l!==a?e=n=[n,o,s,u,i,c,l]:(vi=e,e=a)):(vi=e,e=a)):(vi=e,e=a)):(vi=e,e=a),e!==a&&(di=r,e=`${(f=e)[0]} ${f[2]} ${f[4]} ${f[6]}`),r=e}()),e!==a&&Gp()!==a?((n=function(){var r,e,n,o,s,u,i;return r=vi,e=vi,"wait"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(Oe)),n!==a&&(o=Gp())!==a&&(s=nf())!==a?e=n=[n,o,s]:(vi=e,e=a),e!==a&&(di=r,e=`${(u=e)[0]} ${u[2].value}`),(r=e)===a&&("nowait"===t.substr(vi,6).toLowerCase()?(r=t.substr(vi,6),vi+=6):(r=a,0===wi&&gi(Le)),r===a&&(r=vi,e=vi,"skip"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(je)),n!==a&&(o=Gp())!==a?("locked"===t.substr(vi,6).toLowerCase()?(s=t.substr(vi,6),vi+=6):(s=a,0===wi&&gi(Ce)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a),e!==a&&(di=r,e=`${(i=e)[0]} ${i[2]}`),r=e)),r}())===a&&(n=null),n!==a?(di=r,r=e=e+((o=n)?" "+o:"")):(vi=r,r=a)):(vi=r,r=a),r}function yc(){var r,e,n,o,s,u,i,c,l,f,p,b,v,d,y,h,m;return r=vi,Gp()!==a?((e=pc())===a&&(e=null),e!==a&&Gp()!==a&&yf()!==a&&Hp()!==a?((n=function(){var t,r,e,n,o,s;if(t=vi,(r=hc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=hc())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=hc())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(n=null),n!==a&&Gp()!==a?((o=Gf())===a&&(o=null),o!==a&&Gp()!==a&&(s=mc())!==a&&Gp()!==a?((u=jc())===a&&(u=null),u!==a&&Gp()!==a?((i=Cc())===a&&(i=null),i!==a&&Gp()!==a?((c=jc())===a&&(c=null),c!==a&&Gp()!==a?((l=kc())===a&&(l=null),l!==a&&Gp()!==a?((f=function(){var r,e,n,o;r=vi,(e=function(){var r,e,n,o;r=vi,"group"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(rs));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a&&Qf()!==a&&Gp()!==a&&(n=Kc())!==a&&Gp()!==a?((o=function(){var r,e;r=vi,qf()!==a&&Gp()!==a?("rollup"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Qe)),e!==a?(di=r,r={type:"origin",value:"with rollup"}):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(o=null),o!==a?(di=r,s=o,e={columns:n.value,modifiers:[s]},r=e):(vi=r,r=a)):(vi=r,r=a);var s;return r}())===a&&(f=null),f!==a&&Gp()!==a?((p=function(){var r,e;r=vi,function(){var r,e,n,o;r=vi,"having"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ns));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(e=nl())!==a?(di=r,r=e):(vi=r,r=a);return r}())===a&&(p=null),p!==a&&Gp()!==a?((b=Vc())===a&&(b=null),b!==a&&Gp()!==a?((v=Qi())===a&&(v=null),v!==a&&Gp()!==a?((d=qc())===a&&(d=null),d!==a&&Gp()!==a?((y=dc())===a&&(y=null),y!==a&&Gp()!==a?((h=function(){var r,e,n;r=vi,"window"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Mn));e!==a&&Gp()!==a&&(n=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Vl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Vl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Vl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=Lb(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,r=e={keyword:"window",type:"window",expr:n}):(vi=r,r=a);return r}())===a&&(h=null),h!==a&&Gp()!==a?((m=jc())===a&&(m=null),m!==a?(di=r,r=function(t,r,e,n,o,a,s,u,i,c,l,f,p,b,v,d){if(o&&s||o&&d||s&&d||o&&s&&d)throw new Error("A given SQL statement can contain at most one INTO clause");if(a){(Array.isArray(a)?a:a.expr).forEach(t=>t.table&&Tb.add(`select::${t.db}::${t.table}`))}return{with:t,type:"select",options:r,distinct:e,columns:n,into:{...o||s||d||{},position:(o?"column":s&&"from")||d&&"end"},from:a,where:u,groupby:i,having:c,orderby:l,collate:f,limit:p,locking_read:b&&b,window:v}}(e,n,o,s,u,i,c,l,f,p,b,v,d,y,h,m)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function hc(){var r,e;return r=vi,(e=function(){var r;"sql_calc_found_rows"===t.substr(vi,19).toLowerCase()?(r=t.substr(vi,19),vi+=19):(r=a,0===wi&&gi(Pu));return r}())===a&&((e=function(){var r;"sql_cache"===t.substr(vi,9).toLowerCase()?(r=t.substr(vi,9),vi+=9):(r=a,0===wi&&gi(qu));return r}())===a&&(e=function(){var r;"sql_no_cache"===t.substr(vi,12).toLowerCase()?(r=t.substr(vi,12),vi+=12):(r=a,0===wi&&gi(Qu));return r}()),e===a&&(e=function(){var r;"sql_big_result"===t.substr(vi,14).toLowerCase()?(r=t.substr(vi,14),vi+=14):(r=a,0===wi&&gi(Bu));return r}())===a&&(e=function(){var r;"sql_small_result"===t.substr(vi,16).toLowerCase()?(r=t.substr(vi,16),vi+=16):(r=a,0===wi&&gi(Fu));return r}())===a&&(e=function(){var r;"sql_buffer_result"===t.substr(vi,17).toLowerCase()?(r=t.substr(vi,17),vi+=17):(r=a,0===wi&&gi($u));return r}())),e!==a&&(di=r,e=e),r=e}function mc(){var t,r,e,n,o,s,u,i;if(t=vi,(r=$f())===a&&(r=vi,(e=qp())!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r===a&&(r=qp())),r!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Oc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Oc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){Sb.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?Lb(e,r):[e]}(0,e)):(vi=t,t=a)}else vi=t,t=a;if(t===a)if(t=vi,(r=Oc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Oc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Oc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function wc(){var r,e,n,o,s,u,i;return r=vi,"match"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(_e)),e!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=Rc())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?("AGAINST"===t.substr(vi,7)?(o="AGAINST",vi+=7):(o=a,0===wi&&gi(Ue)),o!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(s=rl())!==a&&Gp()!==a?((u=function(){var r,e,n,o,s,u,i;return r=vi,Yf()!==a&&Gp()!==a?("natural"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(ge)),e!==a&&Gp()!==a?("language"===t.substr(vi,8).toLowerCase()?(n=t.substr(vi,8),vi+=8):(n=a,0===wi&&gi(Ee)),n!==a&&Gp()!==a?("mode"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(we)),o!==a&&Gp()!==a?("with"===t.substr(vi,4).toLowerCase()?(s=t.substr(vi,4),vi+=4):(s=a,0===wi&&gi(v)),s!==a&&Gp()!==a?("query"===t.substr(vi,5).toLowerCase()?(u=t.substr(vi,5),vi+=5):(u=a,0===wi&&gi(Ae)),u!==a&&Gp()!==a?("expansion"===t.substr(vi,9).toLowerCase()?(i=t.substr(vi,9),vi+=9):(i=a,0===wi&&gi(Te)),i!==a?(di=r,r={type:"origin",value:"IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,Yf()!==a&&Gp()!==a?("natural"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(ge)),e!==a&&Gp()!==a?("language"===t.substr(vi,8).toLowerCase()?(n=t.substr(vi,8),vi+=8):(n=a,0===wi&&gi(Ee)),n!==a&&Gp()!==a?("mode"===t.substr(vi,4).toLowerCase()?(o=t.substr(vi,4),vi+=4):(o=a,0===wi&&gi(we)),o!==a?(di=r,r={type:"origin",value:"IN NATURAL LANGUAGE MODE"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,Yf()!==a&&Gp()!==a?("boolean"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Se)),e!==a&&Gp()!==a?("mode"===t.substr(vi,4).toLowerCase()?(n=t.substr(vi,4),vi+=4):(n=a,0===wi&&gi(we)),n!==a?(di=r,r={type:"origin",value:"IN BOOLEAN MODE"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,qf()!==a&&Gp()!==a?("query"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ae)),e!==a&&Gp()!==a?("expansion"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Te)),n!==a?(di=r,r={type:"origin",value:"WITH QUERY EXPANSION"}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)))),r}())===a&&(u=null),u!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((i=Lc())===a&&(i=null),i!==a?(di=r,r=e={against:"against",columns:n,expr:s,match:"match",mode:u,type:"fulltext_search",as:i}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function Oc(){var t,r,e,n,o,s,u,i;return t=vi,(r=wc())!==a&&(di=t,r=function(t){const{as:r,...e}=t;return{expr:e,as:r}}(r)),(t=r)===a&&(t=vi,(r=Ll())!==a&&(e=Gp())!==a&&(n=Dp())!==a&&(o=Gp())!==a&&(s=Ll())!==a&&Gp()!==a&&Dp()!==a&&Gp()!==a&&qp()!==a?(di=t,u=r,i=s,Sb.add(`select::${u}::${i}::(.*)`),t=r={expr:{type:"column_ref",db:u,table:i,column:"*"},as:null}):(vi=t,t=a),t===a&&(t=vi,r=vi,(e=Ll())!==a&&(n=Gp())!==a&&(o=Dp())!==a?r=e=[e,n,o]:(vi=r,r=a),r===a&&(r=null),r!==a&&(e=Gp())!==a&&(n=qp())!==a?(di=t,t=r=function(t){return Sb.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t&&t[0]||null,column:"*"},as:null}}(r)):(vi=t,t=a),t===a&&(t=vi,(r=function(){var t,r,e,n;t=vi,(r=ib())===a&&(r=cb());r!==a&&Gp()!==a&&(e=Tp())!==a&&Gp()!==a&&(n=tb())!==a?(di=t,r=oi(r,e,n),t=r):(vi=t,t=a);return t}())!==a&&(e=Gp())!==a?((n=Lc())===a&&(n=null),n!==a?(di=t,t=r={expr:r,as:n}):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=rl())!==a){for(e=[],n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf())===a&&(s=$p()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf())===a&&(s=$p()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,r=function(t,r){const e=t.ast;if(e&&"select"===e.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!r||0===r.length)return t;const n=r.length;let o=r[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?t:r[e-1][3];o=wb(r[e][1],n,o)}return o}(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a&&(e=Gp())!==a?((n=Lc())===a&&(n=null),n!==a?(di=t,t=r=function(t,r){return{expr:t,as:r}}(r,n)):(vi=t,t=a)):(vi=t,t=a))))),t}function Lc(){var t,r,e;return t=vi,(r=_f())!==a&&Gp()!==a&&(e=function(){var t,r;t=vi,(r=Ul())!==a?(di=vi,(function(t){if(!0===db[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(r)?a:void 0)!==a?(di=t,t=r=r):(vi=t,t=a)):(vi=t,t=a);t===a&&(t=Cl());return t}())!==a?(di=t,t=r=e):(vi=t,t=a),t===a&&(t=vi,(r=_f())===a&&(r=null),r!==a&&Gp()!==a&&(e=Ll())!==a?(di=t,t=r=e):(vi=t,t=a)),t}function jc(){var r,e,n;return r=vi,Af()!==a&&Gp()!==a&&(e=function(){var t,r,e,n,o,s,u,c;if(t=vi,(r=ib())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=ib())!==a?n=o=[o,s,u,c]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(c=ib())!==a?n=o=[o,s,u,c]:(vi=n,n=a);e!==a?(di=t,r=i(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,r={keyword:"var",type:"into",expr:e}):(vi=r,r=a),r===a&&(r=vi,Af()!==a&&Gp()!==a?("outfile"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ie)),e===a&&("dumpfile"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(xe))),e===a&&(e=null),e!==a&&Gp()!==a?((n=zl())===a&&(n=Ll()),n!==a?(di=r,r={keyword:e,type:"into",expr:n}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)),r}function Cc(){var t,r;return t=vi,Tf()!==a&&Gp()!==a&&(r=Sc())!==a?(di=t,t=r):(vi=t,t=a),t}function gc(){var t,r,e;return t=vi,(r=xc())!==a&&Gp()!==a&&pf()!==a&&Gp()!==a&&(e=xc())!==a?(di=t,t=r=[r,e]):(vi=t,t=a),t}function Ec(){var r,e;return r=vi,Pf()!==a&&Gp()!==a?("btree"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ne)),e===a&&("hash"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ke))),e!==a?(di=r,r={keyword:"using",type:e.toLowerCase()}):(vi=r,r=a)):(vi=r,r=a),r}function Ac(){var t,r,e,n,o,s;if(t=vi,(r=Tc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Tc())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Tc())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Tc(){var r,e,n,o,s,u;return r=vi,(e=function(){var r,e,n,o;r=vi,"key_block_size"===t.substr(vi,14).toLowerCase()?(e=t.substr(vi,14),vi+=14):(e=a,0===wi&&gi(yr));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="KEY_BLOCK_SIZE"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=Sp())===a&&(n=null),n!==a&&Gp()!==a&&(o=nf())!==a?(di=r,s=n,u=o,r=e={type:e.toLowerCase(),symbol:s,expr:u}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=Ec())===a&&(r=vi,"with"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(v)),e!==a&&Gp()!==a?("parser"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(Re)),n!==a&&Gp()!==a&&(o=Ul())!==a?(di=r,r=e={type:"with parser",expr:o}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"visible"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Me)),e===a&&("invisible"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Ve))),e!==a&&(di=r,e=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(e)),(r=e)===a&&(r=Wp()))),r}function Sc(){var t,r,e,n,o,s,u,i,c,l,f,p;if(t=vi,(r=Uc())!==a){for(e=[],n=_c();n!==a;)e.push(n),n=_c();e!==a?(di=t,f=r,(p=e).unshift(f),p.forEach(t=>{const{table:r,as:e}=t;_b[r]=r,e&&(_b[e]=r),Eb(Sb)}),t=r=p):(vi=t,t=a)}else vi=t,t=a;if(t===a){if(t=vi,r=[],(e=Qp())!==a)for(;e!==a;)r.push(e),e=Qp();else r=a;if(r!==a)if((e=Gp())!==a)if((n=Uc())!==a){for(o=[],s=_c();s!==a;)o.push(s),s=_c();if(o!==a)if((s=Gp())!==a){if(u=[],(i=Fp())!==a)for(;i!==a;)u.push(i),i=Fp();else u=a;if(u!==a)if((i=Gp())!==a){for(c=[],l=_c();l!==a;)c.push(l),l=_c();c!==a?(di=t,t=r=function(t,r,e,n,o){if(t.length!==n.length)throw new Error(`parentheses not match in from clause: ${t.length} != ${n.length}`);return e.unshift(r),e.forEach(t=>{const{table:r,as:e}=t;_b[r]=r,e&&(_b[e]=r),Eb(Sb)}),o.forEach(t=>{const{table:r,as:e}=t;_b[r]=r,e&&(_b[e]=r),Eb(Sb)}),{expr:e,parentheses:{length:n.length},joins:o}}(r,n,o,u,c)):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a}else vi=t,t=a;else vi=t,t=a}else vi=t,t=a;else vi=t,t=a;else vi=t,t=a}return t}function _c(){var t,r,e;return t=vi,Gp()!==a&&(r=Pp())!==a&&Gp()!==a&&(e=Uc())!==a?(di=t,t=e):(vi=t,t=a),t===a&&(t=vi,Gp()!==a&&(r=function(){var t,r,e,n,o,s,u,i,c,l,f;if(t=vi,(r=Ic())!==a)if(Gp()!==a)if((e=Uc())!==a)if(Gp()!==a)if((n=Pf())!==a)if(Gp()!==a)if(Qp()!==a)if(Gp()!==a)if((o=wl())!==a){for(s=[],u=vi,(i=Gp())!==a&&(c=Pp())!==a&&(l=Gp())!==a&&(f=wl())!==a?u=i=[i,c,l,f]:(vi=u,u=a);u!==a;)s.push(u),u=vi,(i=Gp())!==a&&(c=Pp())!==a&&(l=Gp())!==a&&(f=wl())!==a?u=i=[i,c,l,f]:(vi=u,u=a);s!==a&&(u=Gp())!==a&&(i=Fp())!==a?(di=t,p=r,v=o,d=s,(b=e).join=p,b.using=Lb(v,d),t=r=b):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;var p,b,v,d;t===a&&(t=vi,(r=Ic())!==a&&Gp()!==a&&(e=Uc())!==a&&Gp()!==a?((n=Nc())===a&&(n=null),n!==a?(di=t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=Ic())===a&&(r=_i()),r!==a&&Gp()!==a&&(e=Qp())!==a&&Gp()!==a&&(n=Ui())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((o=Lc())===a&&(o=null),o!==a&&(s=Gp())!==a?((u=Nc())===a&&(u=null),u!==a?(di=t,r=function(t,r,e,n){return r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,u),t=r):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)));return t}())!==a?(di=t,t=r):(vi=t,t=a)),t}function Uc(){var r,e,n,o,s,u,i;return r=vi,(e=function(){var r;"dual"===t.substr(vi,4).toLowerCase()?(r=t.substr(vi,4),vi+=4):(r=a,0===wi&&gi(Uu));return r}())!==a&&(di=r,e={type:"dual"}),(r=e)===a&&(r=vi,(e=xc())!==a&&Gp()!==a?((n=Lc())===a&&(n=null),n!==a?(di=r,i=n,r=e="var"===(u=e).type?(u.as=i,u):{db:u.db,table:u.table,as:i}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Qp())!==a&&Gp()!==a&&(n=xc())!==a&&Gp()!==a?((o=Lc())===a&&(o=null),o!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e=function(t,r,e){return"var"===t.type?(t.as=r,t.parentheses=!0,t):{db:t.db,table:t.table,as:r,parentheses:!0}}(n,o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Wc())!==a&&Gp()!==a?((n=Lc())===a&&(n=null),n!==a?(di=r,r=e=function(t,r){return{expr:{type:"values",values:t,prefix:"row"},as:r}}(e,n)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,"lateral"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(De)),e===a&&(e=null),e!==a&&Gp()!==a&&(n=Qp())!==a&&Gp()!==a?((o=Ui())===a&&(o=Wc()),o!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((s=Lc())===a&&(s=null),s!==a?(di=r,r=e=function(t,r,e){Array.isArray(r)&&(r={type:"values",values:r,prefix:"row"}),r.parentheses=!0;const n={expr:r,as:e};return t&&(n.prefix=t),n}(e,o,s)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a))))),r}function Ic(){var r,e,n,o;return r=vi,(e=function(){var r,e,n,o;r=vi,"left"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Fa));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(n=Gp())!==a?((o=Vf())===a&&(o=null),o!==a&&Gp()!==a&&Mf()!==a?(di=r,r=e="LEFT JOIN"):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"right"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ba));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(n=Gp())!==a?((o=Vf())===a&&(o=null),o!==a&&Gp()!==a&&Mf()!==a?(di=r,r=e="RIGHT JOIN"):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"full"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi($a));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(n=Gp())!==a?((o=Vf())===a&&(o=null),o!==a&&Gp()!==a&&Mf()!==a?(di=r,r=e="FULL JOIN"):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"cross"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ha));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(n=Gp())!==a&&(o=Mf())!==a?(di=r,r=e="CROSS JOIN"):(vi=r,r=a),r===a&&(r=vi,e=vi,(n=function(){var r,e,n,o;r=vi,"inner"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ga));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(o=Gp())!==a?e=n=[n,o]:(vi=e,e=a),e===a&&(e=null),e!==a&&(n=Mf())!==a?(di=r,r=e="INNER JOIN"):(vi=r,r=a))))),r}function xc(){var r,e,n,o,s,u,i,c,l;if(r=vi,e=[],Pe.test(t.charAt(vi))?(n=t.charAt(vi),vi++):(n=a,0===wi&&gi(qe)),n!==a)for(;n!==a;)e.push(n),Pe.test(t.charAt(vi))?(n=t.charAt(vi),vi++):(n=a,0===wi&&gi(qe));else e=a;return e!==a&&(n=Ol())!==a?(o=vi,(s=Gp())!==a&&(u=Dp())!==a&&(i=Gp())!==a&&(c=Ol())!==a?o=s=[s,u,i,c]:(vi=o,o=a),o===a&&(o=null),o!==a?(di=r,r=e=function(t,r,e){const n=`${t.join("")}${r}`,o={db:null,table:n};return null!==e&&(o.db=n,o.table=e[3]),o}(e,n,o)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Ll())!==a?(n=vi,(o=Gp())!==a&&(s=Dp())!==a&&(u=Gp())!==a&&(i=Ll())!==a?n=o=[o,s,u,i]:(vi=n,n=a),n===a&&(n=null),n!==a?(di=r,r=e=function(t,r){const e={db:null,table:t};return null!==r&&(e.db=t,e.table=r[3]),e}(e,n)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=ib())!==a&&(di=r,(l=e).db=null,l.table=l.name,e=l),r=e)),r}function Nc(){var t,r;return t=vi,Rf()!==a&&Gp()!==a&&(r=el())!==a?(di=t,t=r):(vi=t,t=a),t}function kc(){var r,e;return r=vi,function(){var r,e,n,o;r=vi,"where"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ts));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(e=nl())!==a?(di=r,r=e):(vi=r,r=a),r}function Rc(){var t,r,e,n,o,s,u,i;if(t=vi,(r=yl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=yl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=yl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Mc(){var t,r;return t=vi,Ef()!==a&&Gp()!==a&&Qf()!==a&&Gp()!==a&&(r=mc())!==a?(di=t,t=r):(vi=t,t=a),t}function Vc(){var r,e;return r=vi,function(){var r,e,n,o;r=vi,"order"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(es));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&Qf()!==a&&Gp()!==a&&(e=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Dc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Dc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Dc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=at(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=r,r=e):(vi=r,r=a),r}function Dc(){var t,r,e;return t=vi,(r=rl())!==a&&Gp()!==a?((e=Bf())===a&&(e=Ff()),e===a&&(e=null),e!==a?(di=t,t=r={expr:r,type:e}):(vi=t,t=a)):(vi=t,t=a),t}function Pc(){var r,e;return(r=nf())===a&&(r=kl())===a&&(r=vi,63===t.charCodeAt(vi)?(e="?",vi++):(e=a,0===wi&&gi(Fe)),e!==a&&(di=r,e={type:"origin",value:"?"}),r=e),r}function qc(){var r,e,n,o,s,u;return r=vi,function(){var r,e,n,o;r=vi,"limit"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(os));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(e=Pc())!==a&&Gp()!==a?(n=vi,(o=Pp())===a&&(o=function(){var r,e,n,o;r=vi,"offset"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(as));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="OFFSET"):(vi=r,r=a)):(vi=r,r=a);return r}()),o!==a&&(s=Gp())!==a&&(u=Pc())!==a?n=o=[o,s,u]:(vi=n,n=a),n===a&&(n=null),n!==a?(di=r,r=function(t,r){const e=[t];return r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,n)):(vi=r,r=a)):(vi=r,r=a),r}function Qc(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Fc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Fc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Fc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Fc(){var r,e,n,o,s,u,i,c,l;return r=vi,e=vi,(n=Ll())!==a&&(o=Gp())!==a&&(s=Dp())!==a?e=n=[n,o,s]:(vi=e,e=a),e===a&&(e=null),e!==a&&(n=Gp())!==a&&(o=Tl())!==a&&(s=Gp())!==a?(61===t.charCodeAt(vi)?(u="=",vi++):(u=a,0===wi&&gi(Be)),u!==a&&Gp()!==a&&(i=fl())!==a?(di=r,r=e={column:o,value:i,table:(l=e)&&l[0]}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,e=vi,(n=Ll())!==a&&(o=Gp())!==a&&(s=Dp())!==a?e=n=[n,o,s]:(vi=e,e=a),e===a&&(e=null),e!==a&&(n=Gp())!==a&&(o=Tl())!==a&&(s=Gp())!==a?(61===t.charCodeAt(vi)?(u="=",vi++):(u=a,0===wi&&gi(Be)),u!==a&&Gp()!==a&&(i=Df())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(c=yl())!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e=function(t,r,e){return{column:r,value:e,table:t&&t[0],keyword:"values"}}(e,o,c)):(vi=r,r=a)):(vi=r,r=a)),r}function Bc(){var r,e,n,o,s;return r=vi,(e=function(){var r,e,n,o;r=vi,"returning"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(_a));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="RETURNING"):(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&Gp()!==a?((n=mc())===a&&(n=fc()),n!==a?(di=r,s=n,r=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===s&&[{type:"expr",expr:{type:"column_ref",table:null,column:"*"},as:null}]||s}):(vi=r,r=a)):(vi=r,r=a),r}function $c(){var t;return(t=Wc())===a&&(t=yc()),t}function Gc(){var t,r,e,n,o,s,u,i,c;if(t=vi,Ef()!==a)if(Gp()!==a)if((r=Qp())!==a)if(Gp()!==a)if((e=Ul())!==a){for(n=[],o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=Ul())!==a?o=s=[s,u,i,c]:(vi=o,o=a);o!==a;)n.push(o),o=vi,(s=Gp())!==a&&(u=Pp())!==a&&(i=Gp())!==a&&(c=Ul())!==a?o=s=[s,u,i,c]:(vi=o,o=a);n!==a&&(o=Gp())!==a&&(s=Fp())!==a?(di=t,t=w(e,n)):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;else vi=t,t=a;return t===a&&(t=vi,Ef()!==a&&Gp()!==a&&(r=Xc())!==a?(di=t,t=r):(vi=t,t=a)),t}function Hc(){var r,e,n;return r=vi,Rf()!==a&&Gp()!==a?("duplicate"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi($e)),e!==a&&Gp()!==a&&xp()!==a&&Gp()!==a&&hf()!==a&&Gp()!==a&&(n=Qc())!==a?(di=r,r={keyword:"on duplicate key update",set:n}):(vi=r,r=a)):(vi=r,r=a),r}function Yc(){var t,r;return t=vi,(r=Lf())!==a&&(di=t,r="insert"),(t=r)===a&&(t=vi,(r=jf())!==a&&(di=t,r="replace"),t=r),t}function Wc(){var t,r;return t=vi,Df()!==a&&Gp()!==a&&(r=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Xc())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Xc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Xc())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=at(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())!==a?(di=t,t=r):(vi=t,t=a),t}function Xc(){var r,e,n;return r=vi,"row"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(yt)),e===a&&(e=null),e!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=Kc())!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e=n):(vi=r,r=a),r}function Kc(){var t,r,e,n,o,s,u,i;if(t=vi,(r=rl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){const e={type:"expr_list"};return e.value=Lb(t,r),e}(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Zc(){var r,e,n;return r=vi,Lp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&(n=function(){var r;(r=function(){var r,e,n,o;r=vi,"year"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Lo));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="YEAR"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"month"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(vo));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MONTH"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"week"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Oo));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="WEEK"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"day"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(to));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DAY"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"hour"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(so));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="HOUR"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"minute"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(bo));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MINUTE"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"second"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ho));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SECOND"):(vi=r,r=a)):(vi=r,r=a);return r}());return r}())!==a?(di=r,r={type:"interval",expr:e,unit:n.toLowerCase()}):(vi=r,r=a),r}function Jc(){var t,r,e,n,o,s;if(t=vi,(r=zc())!==a)if(Gp()!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=zc())!==a?n=o=[o,s]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=zc())!==a?n=o=[o,s]:(vi=n,n=a);e!==a?(di=t,t=r=l(r,e)):(vi=t,t=a)}else vi=t,t=a;else vi=t,t=a;return t}function zc(){var r,e,n;return r=vi,function(){var r,e,n,o;r=vi,"when"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Ss));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(e=nl())!==a&&Gp()!==a&&function(){var r,e,n,o;r=vi,"then"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(_s));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(n=rl())!==a?(di=r,r={type:"when",cond:e,result:n}):(vi=r,r=a),r}function tl(){var r,e;return r=vi,function(){var r,e,n,o;r=vi,"else"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Us));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}()!==a&&Gp()!==a&&(e=rl())!==a?(di=r,r={type:"else",result:e}):(vi=r,r=a),r}function rl(){var t;return(t=function(){var t,r,e,n,o,s,u,i;if(t=vi,(r=ol())!==a){for(e=[],n=vi,(o=Hp())!==a&&(s=zf())!==a&&(u=Gp())!==a&&(i=ol())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Hp())!==a&&(s=zf())!==a&&(u=Gp())!==a&&(i=ol())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,r=Ge(r,e),t=r):(vi=t,t=a)}else vi=t,t=a;return t}())===a&&(t=Ui()),t}function el(){var t,r,e,n,o,s,u,i;if(t=vi,(r=rl())!==a){for(e=[],n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=wb(r[t][1],n,r[t][3]);return n}(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function nl(){var t,r,e,n,o,s,u,i;if(t=vi,(r=rl())!==a){for(e=[],n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf())===a&&(s=Pp()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=Jf())===a&&(s=zf())===a&&(s=Pp()),s!==a&&(u=Gp())!==a&&(i=rl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=wb(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=Array.isArray(n)?n:[n],t}return n}(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function ol(){var t,r,e,n,o,s,u,i;if(t=vi,(r=al())!==a){for(e=[],n=vi,(o=Hp())!==a&&(s=Jf())!==a&&(u=Gp())!==a&&(i=al())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Hp())!==a&&(s=Jf())!==a&&(u=Gp())!==a&&(i=al())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=Ge(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function al(){var t,r;return(t=sl())===a&&(t=function(){var t,r,e;t=vi,(r=function(){var t,r,e,n,o;t=vi,r=vi,(e=Zf())!==a&&(n=Gp())!==a&&(o=Kf())!==a?r=e=[e,n,o]:(vi=r,r=a);r!==a&&(di=t,r=He(r));(t=r)===a&&(t=Kf());return t}())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=Ui())!==a&&Gp()!==a&&Fp()!==a?(di=t,n=r,(o=e).parentheses=!0,r=mb(n,o),t=r):(vi=t,t=a);var n,o;return t}())===a&&(t=vi,Zf()!==a&&Gp()!==a&&(r=al())!==a?(di=t,t=mb("NOT",r)):(vi=t,t=a)),t}function sl(){var r,e,n,o,s;return r=vi,(e=fl())!==a&&Gp()!==a?((n=function(){var r;(r=function(){var t,r,e,n,o,s,u;t=vi,r=[],e=vi,(n=Gp())!==a&&(o=ul())!==a&&(s=Gp())!==a&&(u=fl())!==a?e=n=[n,o,s,u]:(vi=e,e=a);if(e!==a)for(;e!==a;)r.push(e),e=vi,(n=Gp())!==a&&(o=ul())!==a&&(s=Gp())!==a&&(u=fl())!==a?e=n=[n,o,s,u]:(vi=e,e=a);else r=a;r!==a&&(di=t,r={type:"arithmetic",tail:r});return t=r}())===a&&(r=ll())===a&&(r=function(){var t,r,e,n;t=vi,(r=function(){var t,r,e,n,o;t=vi,r=vi,(e=Zf())!==a&&(n=Gp())!==a&&(o=Hf())!==a?r=e=[e,n,o]:(vi=r,r=a);r!==a&&(di=t,r=He(r));(t=r)===a&&(t=Hf());return t}())!==a&&Gp()!==a&&(e=fl())!==a&&Gp()!==a&&Jf()!==a&&Gp()!==a&&(n=fl())!==a?(di=t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(vi=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o;t=vi,(r=Wf())!==a&&(e=Gp())!==a&&(n=fl())!==a?(di=t,t=r={op:"IS",right:n}):(vi=t,t=a);t===a&&(t=vi,r=vi,(e=Wf())!==a&&(n=Gp())!==a&&(o=Zf())!==a?r=e=[e,n,o]:(vi=r,r=a),r!==a&&(e=Gp())!==a&&(n=fl())!==a?(di=t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(vi=t,t=a));return t}())===a&&(r=cl())===a&&(r=function(){var r,e,n,o;r=vi,(e=function(){var r,e,n;r=vi,(e=Zf())===a&&(e=null);e!==a&&Gp()!==a?((n=function(){var r,e,n,o;r=vi,"regexp"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ds));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="REGEXP"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=vi,"rlike"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(vs));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="RLIKE"):(vi=r,r=a)):(vi=r,r=a);return r}()),n!==a?(di=r,s=n,r=e=(o=e)?`${o} ${s}`:s):(vi=r,r=a)):(vi=r,r=a);var o,s;return r}())!==a&&Gp()!==a?("binary"===t.substr(vi,6).toLowerCase()?(n=t.substr(vi,6),vi+=6):(n=a,0===wi&&gi(qr)),n===a&&(n=null),n!==a&&Gp()!==a?((o=Yl())===a&&(o=zl())===a&&(o=yl()),o!==a?(di=r,s=e,r=e={op:(u=n)?`${s} ${u}`:s,right:o}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a);var s,u;return r}());return r}())===a&&(n=null),n!==a?(di=r,o=e,r=e=null===(s=n)?o:"arithmetic"===s.type?jb(o,s.tail):wb(s.op,o,s.right)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=zl())===a&&(r=yl()),r}function ul(){var r;return">="===t.substr(vi,2)?(r=">=",vi+=2):(r=a,0===wi&&gi(Ye)),r===a&&(62===t.charCodeAt(vi)?(r=">",vi++):(r=a,0===wi&&gi(We)),r===a&&("<="===t.substr(vi,2)?(r="<=",vi+=2):(r=a,0===wi&&gi(Xe)),r===a&&("<>"===t.substr(vi,2)?(r="<>",vi+=2):(r=a,0===wi&&gi(Ke)),r===a&&(60===t.charCodeAt(vi)?(r="<",vi++):(r=a,0===wi&&gi(Ze)),r===a&&(61===t.charCodeAt(vi)?(r="=",vi++):(r=a,0===wi&&gi(Be)),r===a&&("!="===t.substr(vi,2)?(r="!=",vi+=2):(r=a,0===wi&&gi(Je)))))))),r}function il(){var t,r,e,n,o;return t=vi,r=vi,(e=Zf())!==a&&(n=Gp())!==a&&(o=Yf())!==a?r=e=[e,n,o]:(vi=r,r=a),r!==a&&(di=t,r=He(r)),(t=r)===a&&(t=Yf()),t}function cl(){var r,e,n,o,s,u,i;return r=vi,(e=function(){var t,r,e,n,o;return t=vi,r=vi,(e=Zf())!==a&&(n=Gp())!==a&&(o=Xf())!==a?r=e=[e,n,o]:(vi=r,r=a),r!==a&&(di=t,r=He(r)),(t=r)===a&&(t=Xf()),t}())!==a&&Gp()!==a?((n=Zl())===a&&(n=kl())===a&&(n=sl()),n!==a&&Gp()!==a?((o=function(){var r,e,n;return r=vi,"escape"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ze)),e!==a&&Gp()!==a&&(n=zl())!==a?(di=r,r=e={type:"ESCAPE",value:n}):(vi=r,r=a),r}())===a&&(o=null),o!==a?(di=r,s=e,u=n,(i=o)&&(u.escape=i),r=e={op:s,right:u}):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r}function ll(){var t,r,e,n;return t=vi,(r=il())!==a&&Gp()!==a&&(e=Qp())!==a&&Gp()!==a&&(n=Kc())!==a&&Gp()!==a&&Fp()!==a?(di=t,t=r={op:r,right:n}):(vi=t,t=a),t===a&&(t=vi,(r=il())!==a&&Gp()!==a?((e=ib())===a&&(e=yl())===a&&(e=zl()),e!==a?(di=t,t=r=function(t,r){return{op:t,right:r}}(r,e)):(vi=t,t=a)):(vi=t,t=a)),t}function fl(){var t,r,e,n,o,s,u,i;if(t=vi,(r=bl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=pl())!==a&&(u=Gp())!==a&&(i=bl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=pl())!==a&&(u=Gp())!==a&&(i=bl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=function(t,r){if(r&&r.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...hb()}));return jb(t,r)}(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function pl(){var r;return 43===t.charCodeAt(vi)?(r="+",vi++):(r=a,0===wi&&gi(tn)),r===a&&(45===t.charCodeAt(vi)?(r="-",vi++):(r=a,0===wi&&gi(rn))),r}function bl(){var t,r,e,n,o,s,u,i;if(t=vi,(r=dl())!==a){for(e=[],n=vi,(o=Gp())!==a?((s=vl())===a&&(s=$p()),s!==a&&(u=Gp())!==a&&(i=dl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a?((s=vl())===a&&(s=$p()),s!==a&&(u=Gp())!==a&&(i=dl())!==a?n=o=[o,s,u,i]:(vi=n,n=a)):(vi=n,n=a);e!==a?(di=t,t=r=jb(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function vl(){var r,e;return 42===t.charCodeAt(vi)?(r="*",vi++):(r=a,0===wi&&gi(en)),r===a&&(47===t.charCodeAt(vi)?(r="/",vi++):(r=a,0===wi&&gi(nn)),r===a&&(37===t.charCodeAt(vi)?(r="%",vi++):(r=a,0===wi&&gi(on)),r===a&&("||"===t.substr(vi,2)?(r="||",vi+=2):(r=a,0===wi&&gi(an)),r===a&&(r=vi,"div"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(sn)),e===a&&("mod"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(un))),e!==a&&(di=r,e=e.toUpperCase()),(r=e)===a&&(38===t.charCodeAt(vi)?(r="&",vi++):(r=a,0===wi&&gi(cn)),r===a&&(">>"===t.substr(vi,2)?(r=">>",vi+=2):(r=a,0===wi&&gi(ln)),r===a&&("<<"===t.substr(vi,2)?(r="<<",vi+=2):(r=a,0===wi&&gi(fn)),r===a&&(94===t.charCodeAt(vi)?(r="^",vi++):(r=a,0===wi&&gi(pn)),r===a&&(124===t.charCodeAt(vi)?(r="|",vi++):(r=a,0===wi&&gi(bn))))))))))),r}function dl(){var r,e,n,o,s;return(r=function(){var r,e,n;(r=function(){var r;(r=function(){var r,e,n,o;r=vi,(e=function(){var r,e,n,o;r=vi,"count"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ws));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="COUNT"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=vi,"group_concat"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(Os));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="GROUP_CONCAT"):(vi=r,r=a)):(vi=r,r=a);return r}());e!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=function(){var r,e,n,o,s;r=vi,(e=function(){var r,e;r=vi,42===t.charCodeAt(vi)?(e="*",vi++):(e=a,0===wi&&gi(en));e!==a&&(di=r,e={type:"star",value:"*"});return r=e}())!==a&&(di=r,e={expr:e,...hb()});(r=e)===a&&(r=vi,(e=Gf())===a&&(e=null),e!==a&&Gp()!==a&&(n=nl())!==a&&Gp()!==a?((o=Vc())===a&&(o=null),o!==a&&Gp()!==a?((s=function(){var r,e,n;r=vi,"separator"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Pn));e===a&&(e=null);e!==a&&Gp()!==a&&(n=zl())!==a?(di=r,r=e={keyword:e,value:n}):(vi=r,r=a);return r}())===a&&(s=null),s!==a?(di=r,e={distinct:e,expr:n,orderby:o,separator:s,...hb()},r=e):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a));return r}())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((o=Ml())===a&&(o=null),o!==a?(di=r,r=e={type:"aggr_func",name:e,args:n,over:o}):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,(e=function(){var r;(r=function(){var r,e,n,o;r=vi,"sum"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Cs));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SUM"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"max"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Ls));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MAX"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"min"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(js));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MIN"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"avg"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(gs));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="AVG"):(vi=r,r=a)):(vi=r,r=a);return r}());return r}())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=rl())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a?((o=Ml())===a&&(o=null),o!==a?(di=r,e={type:"aggr_func",name:e,args:{expr:n},over:o,...hb()},r=e):(vi=r,r=a)):(vi=r,r=a);return r}());return r}())===a&&(r=wc())===a&&(r=Yl())===a&&(r=function(){var t,r,e,n,o,s,u;t=vi,(r=np())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(n=pb())!==a&&Gp()!==a&&(o=ec())!==a&&Gp()!==a&&(s=wl())!==a&&Gp()!==a&&Fp()!==a?(di=t,r=function(t,r,e,n,o){const{dataType:a,length:s}=e;let u=a;return void 0!==s&&(u=`${u}(${s})`),{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:u,suffix:[{type:"origin",value:n},o]}]}}(r,e,n,o,s),t=r):(vi=t,t=a);t===a&&(t=vi,(r=np())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(n=lb())!==a&&Gp()!==a&&(o=Fp())!==a?(di=t,i=e,c=n,r={type:"cast",keyword:r.toLowerCase(),expr:i,symbol:"as",target:[c]},t=r):(vi=t,t=a),t===a&&(t=vi,(r=np())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(n=sp())!==a&&Gp()!==a&&(o=Qp())!==a&&Gp()!==a&&(s=of())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a&&(u=Fp())!==a?(di=t,r=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(r,e,s),t=r):(vi=t,t=a),t===a&&(t=vi,(r=np())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(n=sp())!==a&&Gp()!==a&&(o=Qp())!==a&&Gp()!==a&&(s=of())!==a&&Gp()!==a&&Pp()!==a&&Gp()!==a&&(u=of())!==a&&Gp()!==a&&Fp()!==a&&Gp()!==a&&Fp()!==a?(di=t,r=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(r,e,s,u),t=r):(vi=t,t=a),t===a&&(t=vi,(r=np())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(e=rl())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(n=Xl())!==a&&Gp()!==a?((o=ip())===a&&(o=null),o!==a&&Gp()!==a&&(s=Fp())!==a?(di=t,r=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:[e,n].filter(Boolean).join(" ")}]}}(r,e,n,o),t=r):(vi=t,t=a)):(vi=t,t=a)))));var i,c;return t}())===a&&(r=function(){var t,r,e,n,o,s,u,i;return t=vi,rp()!==a&&Gp()!==a&&(r=Jc())!==a&&Gp()!==a?((e=tl())===a&&(e=null),e!==a&&Gp()!==a&&(n=ep())!==a&&Gp()!==a?((o=rp())===a&&(o=null),o!==a?(di=t,u=r,(i=e)&&u.push(i),t={type:"case",expr:null,args:u}):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,rp()!==a&&Gp()!==a&&(r=rl())!==a&&Gp()!==a&&(e=Jc())!==a&&Gp()!==a?((n=tl())===a&&(n=null),n!==a&&Gp()!==a&&(o=ep())!==a&&Gp()!==a?((s=rp())===a&&(s=null),s!==a?(di=t,t=function(t,r,e){return e&&r.push(e),{type:"case",expr:t,args:r}}(r,e,n)):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a)),t}())===a&&(r=Zc())===a&&(r=Kl())===a&&(r=yl())===a&&(r=nf())===a&&(r=kl())===a&&(r=vi,Qp()!==a&&(e=Gp())!==a&&(n=nl())!==a&&Gp()!==a&&Fp()!==a?(di=r,(o=n).parentheses=!0,r=o):(vi=r,r=a),r===a&&(r=ib())===a&&(r=vi,Gp()!==a?(63===t.charCodeAt(vi)?(e="?",vi++):(e=a,0===wi&&gi(Fe)),e!==a?(di=r,r={type:"origin",value:e}):(vi=r,r=a)):(vi=r,r=a)));var o;return r}())===a&&(r=vi,(e=function(){var r;33===t.charCodeAt(vi)?(r="!",vi++):(r=a,0===wi&&gi(vn));r===a&&(45===t.charCodeAt(vi)?(r="-",vi++):(r=a,0===wi&&gi(rn)),r===a&&(43===t.charCodeAt(vi)?(r="+",vi++):(r=a,0===wi&&gi(tn)),r===a&&(126===t.charCodeAt(vi)?(r="~",vi++):(r=a,0===wi&&gi(dn)))));return r}())!==a?(n=vi,(o=Gp())!==a&&(s=dl())!==a?n=o=[o,s]:(vi=n,n=a),n!==a?(di=r,r=e=mb(e,n[1])):(vi=r,r=a)):(vi=r,r=a)),r}function yl(){var t,r,e,n,o,s,u,i,c,l,f,p,b,v,d,y;return t=vi,(r=Ul())===a&&(r=Al()),r!==a&&Gp()!==a&&(e=Dp())!==a&&(n=Gp())!==a?((o=Ul())===a&&(o=Al()),o!==a&&(s=Gp())!==a&&(u=Dp())!==a&&(i=Gp())!==a&&(c=Tl())!==a?(l=vi,(f=Gp())!==a&&(p=Qi())!==a?l=f=[f,p]:(vi=l,l=a),l===a&&(l=null),l!==a?(di=t,b=r,v=o,d=c,y=l,Sb.add(`select::${b}::${v}::${d}`),t=r={type:"column_ref",db:b,table:v,column:d,collate:y&&y[1],...hb()}):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=Ul())===a&&(r=Al()),r!==a&&Gp()!==a&&(e=Dp())!==a&&(n=Gp())!==a&&(o=Tl())!==a?(s=vi,(u=Gp())!==a&&(i=Qi())!==a?s=u=[u,i]:(vi=s,s=a),s===a&&(s=null),s!==a?(di=t,t=r=function(t,r,e){return Sb.add(`select::${t}::${r}`),{type:"column_ref",table:t,column:r,collate:e&&e[1],...hb()}}(r,o,s)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=Sl())!==a&&Gp()!==a?(e=vi,(n=Gp())!==a&&(o=Qi())!==a?e=n=[n,o]:(vi=e,e=a),e===a&&(e=null),e!==a?(di=t,t=r=function(t,r){return Sb.add("select::null::"+t),{type:"column_ref",table:null,column:t,collate:r&&r[1],...hb()}}(r,e)):(vi=t,t=a)):(vi=t,t=a))),t}function hl(){var t,r,e,n,o,s,u,i;if(t=vi,(r=Sl())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Sl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=Sl())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function ml(){var t,r;return t=vi,(r=Ul())!==a&&(di=t,r=yn(r)),t=r}function wl(){var t,r;return t=vi,(r=Ul())!==a&&(di=t,r=yn(r)),(t=r)===a&&(t=jl()),t}function Ol(){var t;return(t=Ul())===a&&(t=Cl()),t}function Ll(){var t,r;return t=vi,(r=Ul())!==a?(di=vi,(hn(r)?a:void 0)!==a?(di=t,t=r=r):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=Cl()),t}function jl(){var t;return(t=gl())===a&&(t=El())===a&&(t=Al()),t}function Cl(){var t,r;return t=vi,(r=gl())===a&&(r=El())===a&&(r=Al()),r!==a&&(di=t,r=r.value),t=r}function gl(){var r,e,n,o;if(r=vi,34===t.charCodeAt(vi)?(e='"',vi++):(e=a,0===wi&&gi(mn)),e!==a){if(n=[],wn.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(On)),o!==a)for(;o!==a;)n.push(o),wn.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(On));else n=a;n!==a?(34===t.charCodeAt(vi)?(o='"',vi++):(o=a,0===wi&&gi(mn)),o!==a?(di=r,r=e={type:"double_quote_string",value:n.join("")}):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;return r}function El(){var r,e,n,o;if(r=vi,39===t.charCodeAt(vi)?(e="'",vi++):(e=a,0===wi&&gi(Ir)),e!==a){if(n=[],Ln.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(jn)),o!==a)for(;o!==a;)n.push(o),Ln.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(jn));else n=a;n!==a?(39===t.charCodeAt(vi)?(o="'",vi++):(o=a,0===wi&&gi(Ir)),o!==a?(di=r,r=e={type:"single_quote_string",value:n.join("")}):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;return r}function Al(){var r,e,n,o;if(r=vi,96===t.charCodeAt(vi)?(e="`",vi++):(e=a,0===wi&&gi(Cn)),e!==a){if(n=[],gn.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(En)),o===a&&(o=ef()),o!==a)for(;o!==a;)n.push(o),gn.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(En)),o===a&&(o=ef());else n=a;n!==a?(96===t.charCodeAt(vi)?(o="`",vi++):(o=a,0===wi&&gi(Cn)),o!==a?(di=r,r=e={type:"backticks_quote_string",value:n.join("")}):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;return r}function Tl(){var t,r;return t=vi,(r=_l())!==a&&(di=t,r=r),(t=r)===a&&(t=Cl()),t}function Sl(){var t,r;return t=vi,(r=_l())!==a?(di=vi,(hn(r)?a:void 0)!==a?(di=t,t=r=r):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=Al())!==a&&(di=t,r=r.value),t=r),t}function _l(){var t,r,e,n;if(t=vi,(r=Il())!==a){for(e=[],n=Nl();n!==a;)e.push(n),n=Nl();e!==a?(di=t,t=r=An(r,e)):(vi=t,t=a)}else vi=t,t=a;if(t===a)if(t=vi,(r=uf())!==a){if(e=[],(n=Nl())!==a)for(;n!==a;)e.push(n),n=Nl();else e=a;e!==a?(di=t,t=r=An(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Ul(){var t,r,e,n;if(t=vi,(r=Il())!==a){for(e=[],n=xl();n!==a;)e.push(n),n=xl();e!==a?(di=t,t=r=An(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function Il(){var r;return Tn.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Sn)),r}function xl(){var r;return _n.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Un)),r}function Nl(){var r;return In.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(xn)),r}function kl(){var r,e,n,o;return r=vi,e=vi,58===t.charCodeAt(vi)?(n=":",vi++):(n=a,0===wi&&gi(Nn)),n!==a&&(o=Ul())!==a?e=n=[n,o]:(vi=e,e=a),e!==a&&(di=r,e={type:"param",value:e[1]}),r=e}function Rl(){var r,e,n,o,s,u,i,c,l;return r=vi,Rf()!==a&&Gp()!==a&&hf()!==a&&Gp()!==a&&(e=jp())!==a&&Gp()!==a?(n=vi,(o=Qp())!==a&&(s=Gp())!==a?((u=Kc())===a&&(u=null),u!==a&&(i=Gp())!==a&&(c=Fp())!==a?n=o=[o,s,u,i,c]:(vi=n,n=a)):(vi=n,n=a),n===a&&(n=null),n!==a?(di=r,r={type:"on update",keyword:e,parentheses:!!(l=n),expr:l?l[2]:null}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,Rf()!==a&&Gp()!==a&&hf()!==a&&Gp()!==a?("now"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(kn)),e!==a&&Gp()!==a&&(n=Qp())!==a&&(o=Gp())!==a&&(s=Fp())!==a?(di=r,r=function(t){return{type:"on update",keyword:t,parentheses:!0}}(e)):(vi=r,r=a)):(vi=r,r=a)),r}function Ml(){var r,e,n;return r=vi,"over"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Rn)),e!==a&&Gp()!==a&&(n=Dl())!==a?(di=r,r=e={type:"window",as_window_specification:n}):(vi=r,r=a),r===a&&(r=Rl()),r}function Vl(){var t,r,e;return t=vi,(r=Ul())!==a&&Gp()!==a&&_f()!==a&&Gp()!==a&&(e=Dl())!==a?(di=t,t=r={name:r,as_window_specification:e}):(vi=t,t=a),t}function Dl(){var t,r;return(t=Ul())===a&&(t=vi,Qp()!==a&&Gp()!==a?((r=function(){var t,r,e,n;t=vi,(r=Mc())===a&&(r=null);r!==a&&Gp()!==a?((e=Vc())===a&&(e=null),e!==a&&Gp()!==a?((n=function(){var t,r,e,n,o;t=vi,(r=hp())!==a&&Gp()!==a?((e=Pl())===a&&(e=ql()),e!==a?(di=t,t=r={type:"rows",expr:e}):(vi=t,t=a)):(vi=t,t=a);t===a&&(t=vi,(r=hp())!==a&&Gp()!==a&&(e=Hf())!==a&&Gp()!==a&&(n=ql())!==a&&Gp()!==a&&Jf()!==a&&Gp()!==a&&(o=Pl())!==a?(di=t,r=wb(e,{type:"origin",value:"rows"},{type:"expr_list",value:[n,o]}),t=r):(vi=t,t=a));return t}())===a&&(n=null),n!==a?(di=t,t=r={name:null,partitionby:r,orderby:e,window_frame_clause:n}):(vi=t,t=a)):(vi=t,t=a)):(vi=t,t=a);return t}())===a&&(r=null),r!==a&&Gp()!==a&&Fp()!==a?(di=t,t={window_specification:r||{},parentheses:!0}):(vi=t,t=a)):(vi=t,t=a)),t}function Pl(){var r,e,n,o;return r=vi,(e=Fl())!==a&&Gp()!==a?("following"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Vn)),n!==a?(di=r,(o=e).value+=" FOLLOWING",r=e=o):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=Ql()),r}function ql(){var r,e,n,o,s;return r=vi,(e=Fl())!==a&&Gp()!==a?("preceding"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Dn)),n===a&&("following"===t.substr(vi,9).toLowerCase()?(n=t.substr(vi,9),vi+=9):(n=a,0===wi&&gi(Vn))),n!==a?(di=r,s=n,(o=e).value+=" "+s.toUpperCase(),r=e=o):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=Ql()),r}function Ql(){var r,e,n;return r=vi,"current"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(D)),e!==a&&Gp()!==a?("row"===t.substr(vi,3).toLowerCase()?(n=t.substr(vi,3),vi+=3):(n=a,0===wi&&gi(yt)),n!==a?(di=r,r=e={type:"origin",value:"current row"}):(vi=r,r=a)):(vi=r,r=a),r}function Fl(){var r,e;return r=vi,"unbounded"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(F)),e!==a&&(di=r,e={type:"origin",value:e.toUpperCase()}),(r=e)===a&&(r=nf()),r}function Bl(){var r,e;return r=vi,"year_month"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(qn)),e===a&&("day_hour"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Qn)),e===a&&("day_minute"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Fn)),e===a&&("day_second"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Bn)),e===a&&("day_microsecond"===t.substr(vi,15).toLowerCase()?(e=t.substr(vi,15),vi+=15):(e=a,0===wi&&gi($n)),e===a&&("hour_minute"===t.substr(vi,11).toLowerCase()?(e=t.substr(vi,11),vi+=11):(e=a,0===wi&&gi(Gn)),e===a&&("hour_second"===t.substr(vi,11).toLowerCase()?(e=t.substr(vi,11),vi+=11):(e=a,0===wi&&gi(Hn)),e===a&&("hour_microsecond"===t.substr(vi,16).toLowerCase()?(e=t.substr(vi,16),vi+=16):(e=a,0===wi&&gi(Yn)),e===a&&("minute_second"===t.substr(vi,13).toLowerCase()?(e=t.substr(vi,13),vi+=13):(e=a,0===wi&&gi(Wn)),e===a&&("minute_microsecond"===t.substr(vi,18).toLowerCase()?(e=t.substr(vi,18),vi+=18):(e=a,0===wi&&gi(Xn)),e===a&&("second_microsecond"===t.substr(vi,18).toLowerCase()?(e=t.substr(vi,18),vi+=18):(e=a,0===wi&&gi(Kn)),e===a&&("timezone_hour"===t.substr(vi,13).toLowerCase()?(e=t.substr(vi,13),vi+=13):(e=a,0===wi&&gi(Zn)),e===a&&("timezone_minute"===t.substr(vi,15).toLowerCase()?(e=t.substr(vi,15),vi+=15):(e=a,0===wi&&gi(Jn)),e===a&&("century"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(zn)),e===a&&("day"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(to)),e===a&&("date"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ro)),e===a&&("decade"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(eo)),e===a&&("dow"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(no)),e===a&&("doy"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(oo)),e===a&&("epoch"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ao)),e===a&&("hour"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(so)),e===a&&("isodow"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(uo)),e===a&&("isoweek"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(io)),e===a&&("isoyear"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(co)),e===a&&("microseconds"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(lo)),e===a&&("millennium"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(fo)),e===a&&("milliseconds"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(po)),e===a&&("minute"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(bo)),e===a&&("month"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(vo)),e===a&&("quarter"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(yo)),e===a&&("second"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ho)),e===a&&("time"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(mo)),e===a&&("timezone"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(wo)),e===a&&("week"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Oo)),e===a&&("year"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Lo)))))))))))))))))))))))))))))))))))),e!==a&&(di=r,e=e),r=e}function $l(){var r,e,n,o,s,u,i,c;return r=vi,(e=tp())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=Bl())!==a&&Gp()!==a&&Tf()!==a&&Gp()!==a?((o=wp())===a&&(o=Lp())===a&&(o=mp())===a&&(o=dp()),o!==a&&Gp()!==a&&(s=rl())!==a&&Gp()!==a&&Fp()!==a?(di=r,u=n,i=o,c=s,r=e={type:e.toLowerCase(),args:{field:u,cast_type:i,source:c},...hb()}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=tp())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=Bl())!==a&&Gp()!==a&&Tf()!==a&&Gp()!==a&&(o=rl())!==a&&Gp()!==a&&(s=Fp())!==a?(di=r,r=e=function(t,r,e){return{type:t.toLowerCase(),args:{field:r,source:e},...hb()}}(e,n,o)):(vi=r,r=a),r===a&&(r=vi,"date_trunc"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(jo)),e!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a&&(n=rl())!==a&&Gp()!==a&&Pp()!==a&&Gp()!==a&&(o=Bl())!==a&&Gp()!==a&&(s=Fp())!==a?(di=r,r=e=function(t,r){return{type:"function",name:{name:[{type:"origin",value:"date_trunc"}]},args:{type:"expr_list",value:[t,{type:"origin",value:r}]},over:null,...hb()}}(n,o)):(vi=r,r=a))),r}function Gl(){var r,e,n;return r=vi,(e=function(){var r;return"both"===t.substr(vi,4).toLowerCase()?(r=t.substr(vi,4),vi+=4):(r=a,0===wi&&gi(Co)),r===a&&("leading"===t.substr(vi,7).toLowerCase()?(r=t.substr(vi,7),vi+=7):(r=a,0===wi&&gi(go)),r===a&&("trailing"===t.substr(vi,8).toLowerCase()?(r=t.substr(vi,8),vi+=8):(r=a,0===wi&&gi(Eo)))),r}())===a&&(e=null),e!==a&&Gp()!==a?((n=rl())===a&&(n=null),n!==a&&Gp()!==a&&Tf()!==a?(di=r,r=e=function(t,r,e){let n=[];return t&&n.push({type:"origin",value:t}),r&&n.push(r),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(vi=r,r=a)):(vi=r,r=a),r}function Hl(){var r,e,n,o;return r=vi,"trim"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Ao)),e!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a?((n=Gl())===a&&(n=null),n!==a&&Gp()!==a&&(o=rl())!==a&&Gp()!==a&&Fp()!==a?(di=r,r=e=function(t,r){let e=t||{type:"expr_list",value:[]};return e.value.push(r),{type:"function",name:{name:[{type:"origin",value:"trim"}]},args:e,...hb()}}(n,o)):(vi=r,r=a)):(vi=r,r=a),r}function Yl(){var r,e,n,o,s,u,i,c;return(r=$l())===a&&(r=Hl())===a&&(r=vi,"convert"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(To)),e!==a&&Gp()!==a&&(n=Qp())!==a&&Gp()!==a&&(o=function(){var t,r,e,n,o,s;return t=vi,(r=rb())!==a&&Gp()!==a&&Pp()!==a&&Gp()!==a?((e=pb())===a&&(e=vb()),e!==a&&Gp()!==a&&(n=ec())!==a&&Gp()!==a&&(o=wl())!==a?(di=t,t=r=function(t,r,e,n){const{dataType:o,length:a}=r;let s=o;return void 0!==a&&(s=`${s}(${a})`),{type:"expr_list",value:[t,{type:"origin",value:s,suffix:{prefix:e,...n}}]}}(r,e,n,o)):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=rb())!==a&&Gp()!==a&&Pp()!==a&&Gp()!==a?((e=Xl())===a&&(e=lb()),e!==a?(di=t,t=r={type:"expr_list",value:[r,{type:"datatype",..."string"==typeof(s=e)?{dataType:s}:s}]}):(vi=t,t=a)):(vi=t,t=a),t===a&&(t=vi,(r=nl())!==a&&Gp()!==a&&Pf()!==a&&Gp()!==a&&(e=Ul())!==a?(di=t,t=r=function(t,r){return t.suffix="USING "+r.toUpperCase(),{type:"expr_list",value:[t]}}(r,e)):(vi=t,t=a))),t}())!==a&&(s=Gp())!==a&&Fp()!==a?(di=r,r=e={type:"function",name:{name:[{type:"origin",value:"convert"}]},args:o,...hb()}):(vi=r,r=a),r===a&&(r=vi,(e=function(){var r;(r=Wl())===a&&(r=Cp())===a&&(r=Op())===a&&(r=function(){var r,e,n,o;r=vi,"session_user"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(pu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SESSION_USER"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"system_user"===t.substr(vi,11).toLowerCase()?(e=t.substr(vi,11),vi+=11):(e=a,0===wi&&gi(bu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SYSTEM_USER"):(vi=r,r=a)):(vi=r,r=a);return r}());return r}())!==a&&Gp()!==a&&(n=Qp())!==a&&Gp()!==a?((o=Kc())===a&&(o=null),o!==a&&(s=Gp())!==a&&Fp()!==a&&Gp()!==a?((u=Ml())===a&&(u=null),u!==a?(di=r,r=e=function(t,r,e){return{type:"function",name:{name:[{type:"default",value:t}]},args:r||{type:"expr_list",value:[]},over:e,...hb()}}(e,o,u)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=Wl())!==a&&Gp()!==a?((n=Rl())===a&&(n=null),n!==a?(di=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...hb()}):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=vi,(e=ob())!==a?(di=vi,(!yb[(c=e).name[0]&&c.name[0].value.toLowerCase()]?void 0:a)!==a&&(n=Gp())!==a&&Qp()!==a&&(o=Gp())!==a?((s=nl())===a&&(s=null),s!==a&&Gp()!==a&&Fp()!==a&&(u=Gp())!==a?((i=Ml())===a&&(i=null),i!==a?(di=r,r=e=function(t,r,e){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),(t.name[0]&&"TIMESTAMPDIFF"===t.name[0].value.toUpperCase()||t.name[0]&&"TIMESTAMPADD"===t.name[0].value.toUpperCase())&&r.value&&r.value[0]&&(r.value[0]={type:"origin",value:r.value[0].column}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},over:e,...hb()}}(e,s,i)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a)):(vi=r,r=a))))),r}function Wl(){var r;return(r=function(){var r,e,n,o;r=vi,"current_date"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(uu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CURRENT_DATE"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"current_time"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(cu));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CURRENT_TIME"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=jp()),r}function Xl(){var r;return(r=function(){var r,e,n,o;r=vi,"signed"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ps));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SIGNED"):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=vi,"unsigned"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(qs));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="UNSIGNED"):(vi=r,r=a)):(vi=r,r=a);return r}()),r}function Kl(){var r,e,n,o,s,u,i,c,l;return r=vi,"binary"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(So)),e===a&&("_binary"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(_o))),e===a&&(e=null),e!==a&&Gp()!==a&&(n=zl())!==a?(o=vi,(s=Gp())!==a&&(u=Qi())!==a?o=s=[s,u]:(vi=o,o=a),o===a&&(o=null),o!==a?(di=r,c=n,l=o,(i=e)&&(c.prefix=i.toLowerCase()),l&&(c.suffix={collate:l[1]}),r=e=c):(vi=r,r=a)):(vi=r,r=a),r===a&&(r=function(){var r,e;r=vi,(e=function(){var r,e,n,o;r=vi,"true"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(da));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={type:"bool",value:!0});(r=e)===a&&(r=vi,(e=function(){var r,e,n,o;r=vi,"false"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(ha));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={type:"bool",value:!1}),r=e);return r}())===a&&(r=Jl())===a&&(r=function(){var r,e,n,o,s,u;r=vi,(e=mp())===a&&(e=dp())===a&&(e=wp())===a&&(e=yp());if(e!==a)if(Gp()!==a){if(n=vi,39===t.charCodeAt(vi)?(o="'",vi++):(o=a,0===wi&&gi(Ir)),o!==a){for(s=[],u=rf();u!==a;)s.push(u),u=rf();s!==a?(39===t.charCodeAt(vi)?(u="'",vi++):(u=a,0===wi&&gi(Ir)),u!==a?n=o=[o,s,u]:(vi=n,n=a)):(vi=n,n=a)}else vi=n,n=a;n!==a?(di=r,e=Vo(e,n),r=e):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;if(r===a)if(r=vi,(e=mp())===a&&(e=dp())===a&&(e=wp())===a&&(e=yp()),e!==a)if(Gp()!==a){if(n=vi,34===t.charCodeAt(vi)?(o='"',vi++):(o=a,0===wi&&gi(mn)),o!==a){for(s=[],u=tf();u!==a;)s.push(u),u=tf();s!==a?(34===t.charCodeAt(vi)?(u='"',vi++):(u=a,0===wi&&gi(mn)),u!==a?n=o=[o,s,u]:(vi=n,n=a)):(vi=n,n=a)}else vi=n,n=a;n!==a?(di=r,e=Vo(e,n),r=e):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;return r}()),r}function Zl(){var t;return(t=Kl())===a&&(t=nf()),t}function Jl(){var r,e;return r=vi,(e=function(){var r,e,n,o;r=vi,"null"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ba));e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a);return r}())!==a&&(di=r,e={type:"null",value:null}),r=e}function zl(){var r,e,n,o,s,u,i,c;if(r=vi,"_binary"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(_o)),e===a&&("_latin1"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Uo))),e===a&&(e=null),e!==a)if((n=Gp())!==a)if("x"===t.substr(vi,1).toLowerCase()?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(Io)),o!==a){if(s=vi,39===t.charCodeAt(vi)?(u="'",vi++):(u=a,0===wi&&gi(Ir)),u!==a){for(i=[],xo.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(No));c!==a;)i.push(c),xo.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(No));i!==a?(39===t.charCodeAt(vi)?(c="'",vi++):(c=a,0===wi&&gi(Ir)),c!==a?s=u=[u,i,c]:(vi=s,s=a)):(vi=s,s=a)}else vi=s,s=a;s!==a?(di=r,r=e={type:"hex_string",prefix:e,value:s[1].join("")}):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;if(r===a){if(r=vi,"_binary"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(_o)),e===a&&("_latin1"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Uo))),e===a&&(e=null),e!==a)if((n=Gp())!==a)if("b"===t.substr(vi,1).toLowerCase()?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(ko)),o!==a){if(s=vi,39===t.charCodeAt(vi)?(u="'",vi++):(u=a,0===wi&&gi(Ir)),u!==a){for(i=[],xo.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(No));c!==a;)i.push(c),xo.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(No));i!==a?(39===t.charCodeAt(vi)?(c="'",vi++):(c=a,0===wi&&gi(Ir)),c!==a?s=u=[u,i,c]:(vi=s,s=a)):(vi=s,s=a)}else vi=s,s=a;s!==a?(di=r,r=e=function(t,r,e){return{type:"bit_string",prefix:t,value:e[1].join("")}}(e,0,s)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;if(r===a){if(r=vi,"_binary"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(_o)),e===a&&("_latin1"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Uo))),e===a&&(e=null),e!==a)if((n=Gp())!==a)if("0x"===t.substr(vi,2).toLowerCase()?(o=t.substr(vi,2),vi+=2):(o=a,0===wi&&gi(Ro)),o!==a){for(s=[],xo.test(t.charAt(vi))?(u=t.charAt(vi),vi++):(u=a,0===wi&&gi(No));u!==a;)s.push(u),xo.test(t.charAt(vi))?(u=t.charAt(vi),vi++):(u=a,0===wi&&gi(No));s!==a?(di=r,r=e=function(t,r,e){return{type:"full_hex_string",prefix:t,value:e.join("")}}(e,0,s)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;if(r===a){if(r=vi,"n"===t.substr(vi,1).toLowerCase()?(e=t.charAt(vi),vi++):(e=a,0===wi&&gi(Mo)),e!==a){if(n=vi,39===t.charCodeAt(vi)?(o="'",vi++):(o=a,0===wi&&gi(Ir)),o!==a){for(s=[],u=rf();u!==a;)s.push(u),u=rf();s!==a?(39===t.charCodeAt(vi)?(u="'",vi++):(u=a,0===wi&&gi(Ir)),u!==a?n=o=[o,s,u]:(vi=n,n=a)):(vi=n,n=a)}else vi=n,n=a;n!==a?(di=r,r=e=function(t,r){return{type:"natural_string",value:r[1].join("")}}(0,n)):(vi=r,r=a)}else vi=r,r=a;if(r===a){if(r=vi,e=vi,39===t.charCodeAt(vi)?(n="'",vi++):(n=a,0===wi&&gi(Ir)),n!==a){for(o=[],s=rf();s!==a;)o.push(s),s=rf();o!==a?(39===t.charCodeAt(vi)?(s="'",vi++):(s=a,0===wi&&gi(Ir)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a)}else vi=e,e=a;if(e!==a&&(di=r,e=function(t){return{type:"single_quote_string",value:t[1].join("")}}(e)),(r=e)===a){if(r=vi,e=vi,34===t.charCodeAt(vi)?(n='"',vi++):(n=a,0===wi&&gi(mn)),n!==a){for(o=[],s=tf();s!==a;)o.push(s),s=tf();o!==a?(34===t.charCodeAt(vi)?(s='"',vi++):(s=a,0===wi&&gi(mn)),s!==a?e=n=[n,o,s]:(vi=e,e=a)):(vi=e,e=a)}else vi=e,e=a;e!==a&&(di=r,e=function(t){return{type:"double_quote_string",value:t[1].join("")}}(e)),r=e}}}}}return r}function tf(){var r;return Do.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Po)),r===a&&(r=ef())===a&&(qo.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Qo))),r}function rf(){var r;return Fo.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Bo)),r===a&&(r=ef()),r}function ef(){var r,e,n,o,s,u,i,c,l,f;return r=vi,"\\'"===t.substr(vi,2)?(e="\\'",vi+=2):(e=a,0===wi&&gi($o)),e!==a&&(di=r,e="\\'"),(r=e)===a&&(r=vi,'\\"'===t.substr(vi,2)?(e='\\"',vi+=2):(e=a,0===wi&&gi(Go)),e!==a&&(di=r,e='\\"'),(r=e)===a&&(r=vi,"\\\\"===t.substr(vi,2)?(e="\\\\",vi+=2):(e=a,0===wi&&gi(Ho)),e!==a&&(di=r,e="\\\\"),(r=e)===a&&(r=vi,"\\/"===t.substr(vi,2)?(e="\\/",vi+=2):(e=a,0===wi&&gi(Yo)),e!==a&&(di=r,e="\\/"),(r=e)===a&&(r=vi,"\\b"===t.substr(vi,2)?(e="\\b",vi+=2):(e=a,0===wi&&gi(Wo)),e!==a&&(di=r,e="\b"),(r=e)===a&&(r=vi,"\\f"===t.substr(vi,2)?(e="\\f",vi+=2):(e=a,0===wi&&gi(Xo)),e!==a&&(di=r,e="\f"),(r=e)===a&&(r=vi,"\\n"===t.substr(vi,2)?(e="\\n",vi+=2):(e=a,0===wi&&gi(Ko)),e!==a&&(di=r,e="\n"),(r=e)===a&&(r=vi,"\\r"===t.substr(vi,2)?(e="\\r",vi+=2):(e=a,0===wi&&gi(Zo)),e!==a&&(di=r,e="\r"),(r=e)===a&&(r=vi,"\\t"===t.substr(vi,2)?(e="\\t",vi+=2):(e=a,0===wi&&gi(Jo)),e!==a&&(di=r,e="\t"),(r=e)===a&&(r=vi,"\\u"===t.substr(vi,2)?(e="\\u",vi+=2):(e=a,0===wi&&gi(zo)),e!==a&&(n=lf())!==a&&(o=lf())!==a&&(s=lf())!==a&&(u=lf())!==a?(di=r,i=n,c=o,l=s,f=u,r=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(vi=r,r=a),r===a&&(r=vi,92===t.charCodeAt(vi)?(e="\\",vi++):(e=a,0===wi&&gi(ta)),e!==a&&(di=r,e="\\"),(r=e)===a&&(r=vi,"''"===t.substr(vi,2)?(e="''",vi+=2):(e=a,0===wi&&gi(ra)),e!==a&&(di=r,e="''"),(r=e)===a&&(r=vi,'""'===t.substr(vi,2)?(e='""',vi+=2):(e=a,0===wi&&gi(ea)),e!==a&&(di=r,e='""'),(r=e)===a&&(r=vi,"``"===t.substr(vi,2)?(e="``",vi+=2):(e=a,0===wi&&gi(na)),e!==a&&(di=r,e="``"),r=e))))))))))))),r}function nf(){var t,r,e;return t=vi,(r=function(){var t,r,e,n;t=vi,(r=of())!==a&&(e=af())!==a&&(n=sf())!==a?(di=t,t=r={type:"bigint",value:r+e+n}):(vi=t,t=a);t===a&&(t=vi,(r=of())!==a&&(e=af())!==a?(di=t,r=function(t,r){const e=t+r;return Ob(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e),t=r):(vi=t,t=a),t===a&&(t=vi,(r=of())!==a&&(e=sf())!==a?(di=t,r=function(t,r){return{type:"bigint",value:t+r}}(r,e),t=r):(vi=t,t=a),t===a&&(t=vi,(r=of())!==a&&(di=t,r=function(t){return Ob(t)?{type:"bigint",value:t}:parseFloat(t)}(r)),t=r)));return t}())!==a&&(di=t,r=(e=r)&&"bigint"===e.type?e:{type:"number",value:e}),t=r}function of(){var r,e,n;return(r=uf())===a&&(r=cf())===a&&(r=vi,45===t.charCodeAt(vi)?(e="-",vi++):(e=a,0===wi&&gi(rn)),e===a&&(43===t.charCodeAt(vi)?(e="+",vi++):(e=a,0===wi&&gi(tn))),e!==a&&(n=uf())!==a?(di=r,r=e=e+n):(vi=r,r=a),r===a&&(r=vi,45===t.charCodeAt(vi)?(e="-",vi++):(e=a,0===wi&&gi(rn)),e===a&&(43===t.charCodeAt(vi)?(e="+",vi++):(e=a,0===wi&&gi(tn))),e!==a&&(n=cf())!==a?(di=r,r=e=function(t,r){return t+r}(e,n)):(vi=r,r=a))),r}function af(){var r,e,n,o;return r=vi,46===t.charCodeAt(vi)?(e=".",vi++):(e=a,0===wi&&gi(sa)),e!==a?((n=uf())===a&&(n=null),n!==a?(di=r,r=e=(o=n)?"."+o:""):(vi=r,r=a)):(vi=r,r=a),r}function sf(){var r,e,n;return r=vi,(e=function(){var r,e,n;r=vi,ca.test(t.charAt(vi))?(e=t.charAt(vi),vi++):(e=a,0===wi&&gi(la));e!==a?(fa.test(t.charAt(vi))?(n=t.charAt(vi),vi++):(n=a,0===wi&&gi(pa)),n===a&&(n=null),n!==a?(di=r,r=e=e+(null!==(o=n)?o:"")):(vi=r,r=a)):(vi=r,r=a);var o;return r}())!==a&&(n=uf())!==a?(di=r,r=e=e+n):(vi=r,r=a),r}function uf(){var t,r,e;if(t=vi,r=[],(e=cf())!==a)for(;e!==a;)r.push(e),e=cf();else r=a;return r!==a&&(di=t,r=r.join("")),t=r}function cf(){var r;return Kt.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(Zt)),r}function lf(){var r;return ua.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(ia)),r}function ff(){var r,e,n,o;return r=vi,"default"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(k)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function pf(){var r,e,n,o;return r=vi,"to"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(ya)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function bf(){var r,e,n,o;return r=vi,"show"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ma)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function vf(){var r,e,n,o;return r=vi,"drop"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Mt)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DROP"):(vi=r,r=a)):(vi=r,r=a),r}function df(){var r,e,n,o;return r=vi,"alter"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Oa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function yf(){var r,e,n,o;return r=vi,"select"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(La)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function hf(){var r,e,n,o;return r=vi,"update"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ja)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function mf(){var r,e,n,o;return r=vi,"create"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ca)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function wf(){var r,e,n,o;return r=vi,"temporary"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(ga)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Of(){var r,e,n,o;return r=vi,"delete"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ea)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Lf(){var r,e,n,o;return r=vi,"insert"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Aa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function jf(){var r,e,n,o;return r=vi,"replace"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Sa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Cf(){var r,e,n,o;return r=vi,"rename"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ua)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function gf(){var r,e,n,o;return r=vi,"ignore"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ia)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Ef(){var r,e,n,o;return r=vi,"partition"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Na)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="PARTITION"):(vi=r,r=a)):(vi=r,r=a),r}function Af(){var r,e,n,o;return r=vi,"into"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ka)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Tf(){var r,e,n,o;return r=vi,"from"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Ra)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Sf(){var r,e,n,o;return r=vi,"set"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(pr)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SET"):(vi=r,r=a)):(vi=r,r=a),r}function _f(){var r,e,n,o;return r=vi,"as"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(m)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Uf(){var r,e,n,o;return r=vi,"table"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Ma)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TABLE"):(vi=r,r=a)):(vi=r,r=a),r}function If(){var r,e,n,o;return r=vi,"trigger"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Va)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TRIGGER"):(vi=r,r=a)):(vi=r,r=a),r}function xf(){var r,e,n,o;return r=vi,"tables"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Da)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TABLES"):(vi=r,r=a)):(vi=r,r=a),r}function Nf(){var r,e,n,o;return r=vi,"database"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Pa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DATABASE"):(vi=r,r=a)):(vi=r,r=a),r}function kf(){var r,e,n,o;return r=vi,"schema"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(qa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SCHEMA"):(vi=r,r=a)):(vi=r,r=a),r}function Rf(){var r,e,n,o;return r=vi,"on"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(Qa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Mf(){var r,e,n,o;return r=vi,"join"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Ya)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Vf(){var r,e,n,o;return r=vi,"outer"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Wa)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Df(){var r,e,n,o;return r=vi,"values"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Ja)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Pf(){var r,e,n,o;return r=vi,"using"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(za)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function qf(){var r,e,n,o;return r=vi,"with"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(v)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Qf(){var r,e,n,o;return r=vi,"by"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(d)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function Ff(){var r,e,n,o;return r=vi,"asc"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(ss)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ASC"):(vi=r,r=a)):(vi=r,r=a),r}function Bf(){var r,e,n,o;return r=vi,"desc"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(us)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DESC"):(vi=r,r=a)):(vi=r,r=a),r}function $f(){var r,e,n,o;return r=vi,"all"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(cs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ALL"):(vi=r,r=a)):(vi=r,r=a),r}function Gf(){var r,e,n,o;return r=vi,"distinct"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(ls)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DISTINCT"):(vi=r,r=a)):(vi=r,r=a),r}function Hf(){var r,e,n,o;return r=vi,"between"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(fs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="BETWEEN"):(vi=r,r=a)):(vi=r,r=a),r}function Yf(){var r,e,n,o;return r=vi,"in"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(he)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="IN"):(vi=r,r=a)):(vi=r,r=a),r}function Wf(){var r,e,n,o;return r=vi,"is"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(ps)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="IS"):(vi=r,r=a)):(vi=r,r=a),r}function Xf(){var r,e,n,o;return r=vi,"like"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(bs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="LIKE"):(vi=r,r=a)):(vi=r,r=a),r}function Kf(){var r,e,n,o;return r=vi,"exists"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ys)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="EXISTS"):(vi=r,r=a)):(vi=r,r=a),r}function Zf(){var r,e,n,o;return r=vi,"not"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(zt)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="NOT"):(vi=r,r=a)):(vi=r,r=a),r}function Jf(){var r,e,n,o;return r=vi,"and"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(hs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="AND"):(vi=r,r=a)):(vi=r,r=a),r}function zf(){var r,e,n,o;return r=vi,"or"===t.substr(vi,2).toLowerCase()?(e=t.substr(vi,2),vi+=2):(e=a,0===wi&&gi(ms)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="OR"):(vi=r,r=a)):(vi=r,r=a),r}function tp(){var r,e,n,o;return r=vi,"extract"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Es)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="EXTRACT"):(vi=r,r=a)):(vi=r,r=a),r}function rp(){var r,e,n,o;return r=vi,"case"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Ts)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function ep(){var r,e,n,o;return r=vi,"end"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Is)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?r=e=[e,n]:(vi=r,r=a)):(vi=r,r=a),r}function np(){var r,e,n,o;return r=vi,"cast"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(xs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CAST"):(vi=r,r=a)):(vi=r,r=a),r}function op(){var r,e,n,o;return r=vi,"bit"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(ks)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="BIT"):(vi=r,r=a)):(vi=r,r=a),r}function ap(){var r,e,n,o;return r=vi,"numeric"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Vs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="NUMERIC"):(vi=r,r=a)):(vi=r,r=a),r}function sp(){var r,e,n,o;return r=vi,"decimal"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ds)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DECIMAL"):(vi=r,r=a)):(vi=r,r=a),r}function up(){var r,e,n,o;return r=vi,"int"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Qs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="INT"):(vi=r,r=a)):(vi=r,r=a),r}function ip(){var r,e,n,o;return r=vi,"integer"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Bs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="INTEGER"):(vi=r,r=a)):(vi=r,r=a),r}function cp(){var r,e,n,o;return r=vi,"smallint"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Gs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SMALLINT"):(vi=r,r=a)):(vi=r,r=a),r}function lp(){var r,e,n,o;return r=vi,"mediumint"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Hs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MEDIUMINT"):(vi=r,r=a)):(vi=r,r=a),r}function fp(){var r,e,n,o;return r=vi,"tinyint"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ys)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TINYINT"):(vi=r,r=a)):(vi=r,r=a),r}function pp(){var r,e,n,o;return r=vi,"bigint"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(Js)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="BIGINT"):(vi=r,r=a)):(vi=r,r=a),r}function bp(){var r,e,n,o;return r=vi,"float"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(tu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="FLOAT"):(vi=r,r=a)):(vi=r,r=a),r}function vp(){var r,e,n,o;return r=vi,"double"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ru)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DOUBLE"):(vi=r,r=a)):(vi=r,r=a),r}function dp(){var r,e,n,o;return r=vi,"date"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(ro)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DATE"):(vi=r,r=a)):(vi=r,r=a),r}function yp(){var r,e,n,o;return r=vi,"datetime"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(eu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="DATETIME"):(vi=r,r=a)):(vi=r,r=a),r}function hp(){var r,e,n,o;return r=vi,"rows"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(nu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ROWS"):(vi=r,r=a)):(vi=r,r=a),r}function mp(){var r,e,n,o;return r=vi,"time"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(mo)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TIME"):(vi=r,r=a)):(vi=r,r=a),r}function wp(){var r,e,n,o;return r=vi,"timestamp"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(ou)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TIMESTAMP"):(vi=r,r=a)):(vi=r,r=a),r}function Op(){var r,e,n,o;return r=vi,"user"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(au)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="USER"):(vi=r,r=a)):(vi=r,r=a),r}function Lp(){var r,e,n,o;return r=vi,"interval"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(iu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="INTERVAL"):(vi=r,r=a)):(vi=r,r=a),r}function jp(){var r,e,n,o;return r=vi,"current_timestamp"===t.substr(vi,17).toLowerCase()?(e=t.substr(vi,17),vi+=17):(e=a,0===wi&&gi(lu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CURRENT_TIMESTAMP"):(vi=r,r=a)):(vi=r,r=a),r}function Cp(){var r,e,n,o;return r=vi,"current_user"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(fu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CURRENT_USER"):(vi=r,r=a)):(vi=r,r=a),r}function gp(){var r,e,n,o;return r=vi,"view"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(se)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="VIEW"):(vi=r,r=a)):(vi=r,r=a),r}function Ep(){var r;return 64===t.charCodeAt(vi)?(r="@",vi++):(r=a,0===wi&&gi(lt)),r}function Ap(){var r;return(r=function(){var r;return"@@"===t.substr(vi,2)?(r="@@",vi+=2):(r=a,0===wi&&gi(Au)),r}())===a&&(r=Ep())===a&&(r=function(){var r;return 36===t.charCodeAt(vi)?(r="$",vi++):(r=a,0===wi&&gi(Tu)),r}()),r}function Tp(){var r;return":="===t.substr(vi,2)?(r=":=",vi+=2):(r=a,0===wi&&gi(_u)),r}function Sp(){var r;return 61===t.charCodeAt(vi)?(r="=",vi++):(r=a,0===wi&&gi(Be)),r}function _p(){var r,e,n,o;return r=vi,"add"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Iu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ADD"):(vi=r,r=a)):(vi=r,r=a),r}function Up(){var r,e,n,o;return r=vi,"column"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(xu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="COLUMN"):(vi=r,r=a)):(vi=r,r=a),r}function Ip(){var r,e,n,o;return r=vi,"index"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(Sr)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="INDEX"):(vi=r,r=a)):(vi=r,r=a),r}function xp(){var r,e,n,o;return r=vi,"key"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(it)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="KEY"):(vi=r,r=a)):(vi=r,r=a),r}function Np(){var r,e,n,o;return r=vi,"fulltext"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(ku)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="FULLTEXT"):(vi=r,r=a)):(vi=r,r=a),r}function kp(){var r,e,n,o;return r=vi,"spatial"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ru)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="SPATIAL"):(vi=r,r=a)):(vi=r,r=a),r}function Rp(){var r,e,n,o;return r=vi,"unique"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(ut)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="UNIQUE"):(vi=r,r=a)):(vi=r,r=a),r}function Mp(){var r,e,n,o;return r=vi,"comment"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Mu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="COMMENT"):(vi=r,r=a)):(vi=r,r=a),r}function Vp(){var r,e,n,o;return r=vi,"references"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Du)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="REFERENCES"):(vi=r,r=a)):(vi=r,r=a),r}function Dp(){var r;return 46===t.charCodeAt(vi)?(r=".",vi++):(r=a,0===wi&&gi(sa)),r}function Pp(){var r;return 44===t.charCodeAt(vi)?(r=",",vi++):(r=a,0===wi&&gi(Gu)),r}function qp(){var r;return 42===t.charCodeAt(vi)?(r="*",vi++):(r=a,0===wi&&gi(en)),r}function Qp(){var r;return 40===t.charCodeAt(vi)?(r="(",vi++):(r=a,0===wi&&gi(de)),r}function Fp(){var r;return 41===t.charCodeAt(vi)?(r=")",vi++):(r=a,0===wi&&gi(ye)),r}function Bp(){var r;return 59===t.charCodeAt(vi)?(r=";",vi++):(r=a,0===wi&&gi(Wu)),r}function $p(){var r;return(r=function(){var r;return"||"===t.substr(vi,2)?(r="||",vi+=2):(r=a,0===wi&&gi(an)),r}())===a&&(r=function(){var r;return"&&"===t.substr(vi,2)?(r="&&",vi+=2):(r=a,0===wi&&gi(Xu)),r}())===a&&(r=function(){var r,e,n,o;return r=vi,"xor"===t.substr(vi,3).toLowerCase()?(e=t.substr(vi,3),vi+=3):(e=a,0===wi&&gi(Ku)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="XOR"):(vi=r,r=a)):(vi=r,r=a),r}()),r}function Gp(){var t,r;for(t=[],(r=Kp())===a&&(r=Yp());r!==a;)t.push(r),(r=Kp())===a&&(r=Yp());return t}function Hp(){var t,r;if(t=[],(r=Kp())===a&&(r=Yp()),r!==a)for(;r!==a;)t.push(r),(r=Kp())===a&&(r=Yp());else t=a;return t}function Yp(){var r;return(r=function(){var r,e,n,o,s,u;r=vi,"/*"===t.substr(vi,2)?(e="/*",vi+=2):(e=a,0===wi&&gi(Zu));if(e!==a){for(n=[],o=vi,s=vi,wi++,"*/"===t.substr(vi,2)?(u="*/",vi+=2):(u=a,0===wi&&gi(Ju)),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);o!==a;)n.push(o),o=vi,s=vi,wi++,"*/"===t.substr(vi,2)?(u="*/",vi+=2):(u=a,0===wi&&gi(Ju)),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);n!==a?("*/"===t.substr(vi,2)?(o="*/",vi+=2):(o=a,0===wi&&gi(Ju)),o!==a?r=e=[e,n,o]:(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;return r}())===a&&(r=function(){var r,e,n,o,s,u;r=vi,"--"===t.substr(vi,2)?(e="--",vi+=2):(e=a,0===wi&&gi(zu));if(e!==a){for(n=[],o=vi,s=vi,wi++,u=Zp(),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);o!==a;)n.push(o),o=vi,s=vi,wi++,u=Zp(),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);n!==a?r=e=[e,n]:(vi=r,r=a)}else vi=r,r=a;return r}())===a&&(r=function(){var r,e,n,o,s,u;r=vi,35===t.charCodeAt(vi)?(e="#",vi++):(e=a,0===wi&&gi(ti));if(e!==a){for(n=[],o=vi,s=vi,wi++,u=Zp(),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);o!==a;)n.push(o),o=vi,s=vi,wi++,u=Zp(),wi--,u===a?s=void 0:(vi=s,s=a),s!==a&&(u=Xp())!==a?o=s=[s,u]:(vi=o,o=a);n!==a?r=e=[e,n]:(vi=r,r=a)}else vi=r,r=a;return r}()),r}function Wp(){var t,r,e,n,o,s,u;return t=vi,(r=Mp())!==a&&Gp()!==a?((e=Sp())===a&&(e=null),e!==a&&Gp()!==a&&(n=zl())!==a?(di=t,s=e,u=n,t=r={type:(o=r).toLowerCase(),keyword:o.toLowerCase(),symbol:s,value:u}):(vi=t,t=a)):(vi=t,t=a),t}function Xp(){var r;return t.length>vi?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(ri)),r}function Kp(){var r;return ei.test(t.charAt(vi))?(r=t.charAt(vi),vi++):(r=a,0===wi&&gi(ni)),r}function Zp(){var r,e;if((r=function(){var r,e;r=vi,wi++,t.length>vi?(e=t.charAt(vi),vi++):(e=a,0===wi&&gi(ri));wi--,e===a?r=void 0:(vi=r,r=a);return r}())===a)if(r=[],oa.test(t.charAt(vi))?(e=t.charAt(vi),vi++):(e=a,0===wi&&gi(aa)),e!==a)for(;e!==a;)r.push(e),oa.test(t.charAt(vi))?(e=t.charAt(vi),vi++):(e=a,0===wi&&gi(aa));else r=a;return r}function Jp(){var r,e;return r=vi,di=vi,Ab=[],(!0?void 0:a)!==a&&Gp()!==a?((e=zp())===a&&(e=function(){var r,e;r=vi,function(){var r;return"return"===t.substr(vi,6).toLowerCase()?(r=t.substr(vi,6),vi+=6):(r=a,0===wi&&gi(Su)),r}()!==a&&Gp()!==a&&(e=tb())!==a?(di=r,r={type:"return",expr:e}):(vi=r,r=a);return r}()),e!==a?(di=r,r={stmt:e,vars:Ab}):(vi=r,r=a)):(vi=r,r=a),r}function zp(){var t,r,e,n;return t=vi,(r=ib())===a&&(r=cb()),r!==a&&Gp()!==a?((e=Tp())===a&&(e=Sp()),e!==a&&Gp()!==a&&(n=tb())!==a?(di=t,t=r=oi(r,e,n)):(vi=t,t=a)):(vi=t,t=a),t}function tb(){var r;return(r=fc())===a&&(r=function(){var t,r,e,n,o;t=vi,(r=ib())!==a&&Gp()!==a&&(e=Ic())!==a&&Gp()!==a&&(n=ib())!==a&&Gp()!==a&&(o=Nc())!==a?(di=t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(vi=t,t=a);return t}())===a&&(r=rb())===a&&(r=function(){var r,e;r=vi,function(){var r;return 91===t.charCodeAt(vi)?(r="[",vi++):(r=a,0===wi&&gi(Hu)),r}()!==a&&Gp()!==a&&(e=ub())!==a&&Gp()!==a&&function(){var r;return 93===t.charCodeAt(vi)?(r="]",vi++):(r=a,0===wi&&gi(Yu)),r}()!==a?(di=r,r={type:"array",value:e}):(vi=r,r=a);return r}()),r}function rb(){var t,r,e,n,o,s,u,i;if(t=vi,(r=eb())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=pl())!==a&&(u=Gp())!==a&&(i=eb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=pl())!==a&&(u=Gp())!==a&&(i=eb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=Ge(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function eb(){var t,r,e,n,o,s,u,i;if(t=vi,(r=nb())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=vl())!==a&&(u=Gp())!==a&&(i=nb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=vl())!==a&&(u=Gp())!==a&&(i=nb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=Ge(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function nb(){var t,r,e;return(t=ab())===a&&(t=Zl())===a&&(t=ib())===a&&(t=yl())===a&&(t=sb())===a&&(t=kl())===a&&(t=vi,Qp()!==a&&Gp()!==a&&(r=rb())!==a&&Gp()!==a&&Fp()!==a?(di=t,(e=r).parentheses=!0,t=e):(vi=t,t=a)),t}function ob(){var t,r,e,n,o,s,u;return t=vi,(r=ml())===a&&(r=Al()),r!==a?(e=vi,(n=Gp())!==a&&(o=Dp())!==a&&(s=Gp())!==a?((u=ml())===a&&(u=Al()),u!==a?e=n=[n,o,s,u]:(vi=e,e=a)):(vi=e,e=a),e===a&&(e=null),e!==a?(di=t,t=r=function(t,r){const e={name:[t]};return null!==r&&(e.schema=t,e.name=[r[3]]),e}(r,e)):(vi=t,t=a)):(vi=t,t=a),t}function ab(){var t,r,e;return t=vi,(r=ob())!==a&&Gp()!==a&&Qp()!==a&&Gp()!==a?((e=ub())===a&&(e=null),e!==a&&Gp()!==a&&Fp()!==a?(di=t,t=r={type:"function",name:r,args:{type:"expr_list",value:e},...hb()}):(vi=t,t=a)):(vi=t,t=a),t}function sb(){var t,r;return t=vi,(r=ob())!==a&&(di=t,r={type:"function",name:r,args:null,...hb()}),t=r}function ub(){var t,r,e,n,o,s,u,i;if(t=vi,(r=nb())!==a){for(e=[],n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=nb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);n!==a;)e.push(n),n=vi,(o=Gp())!==a&&(s=Pp())!==a&&(u=Gp())!==a&&(i=nb())!==a?n=o=[o,s,u,i]:(vi=n,n=a);e!==a?(di=t,t=r=at(r,e)):(vi=t,t=a)}else vi=t,t=a;return t}function ib(){var t,r,e,n,o;return t=vi,(r=Ap())!==a&&(e=cb())!==a?(di=t,n=r,o=e,t=r={type:"var",...o,prefix:n}):(vi=t,t=a),t}function cb(){var r,e,n,o,s;return r=vi,(e=Ul())!==a&&(n=function(){var r,e,n,o,s;r=vi,e=[],n=vi,46===t.charCodeAt(vi)?(o=".",vi++):(o=a,0===wi&&gi(sa));o!==a&&(s=Ul())!==a?n=o=[o,s]:(vi=n,n=a);for(;n!==a;)e.push(n),n=vi,46===t.charCodeAt(vi)?(o=".",vi++):(o=a,0===wi&&gi(sa)),o!==a&&(s=Ul())!==a?n=o=[o,s]:(vi=n,n=a);e!==a&&(di=r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==a?(di=r,o=e,s=n,Ab.push(o),r=e={type:"var",name:o,members:s,prefix:null}):(vi=r,r=a),r===a&&(r=vi,(e=nf())!==a&&(di=r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function lb(){var r;return(r=pb())===a&&(r=function(){var r,e,n,o,s,u,i,c,l,f,p,b;r=vi,(e=ap())===a&&(e=sp())===a&&(e=up())===a&&(e=ip())===a&&(e=cp())===a&&(e=lp())===a&&(e=fp())===a&&(e=pp())===a&&(e=bp())===a&&(e=vp())===a&&(e=op());if(e!==a)if((n=Gp())!==a)if((o=Qp())!==a)if((s=Gp())!==a){if(u=[],Kt.test(t.charAt(vi))?(i=t.charAt(vi),vi++):(i=a,0===wi&&gi(Zt)),i!==a)for(;i!==a;)u.push(i),Kt.test(t.charAt(vi))?(i=t.charAt(vi),vi++):(i=a,0===wi&&gi(Zt));else u=a;if(u!==a)if((i=Gp())!==a){if(c=vi,(l=Pp())!==a)if((f=Gp())!==a){if(p=[],Kt.test(t.charAt(vi))?(b=t.charAt(vi),vi++):(b=a,0===wi&&gi(Zt)),b!==a)for(;b!==a;)p.push(b),Kt.test(t.charAt(vi))?(b=t.charAt(vi),vi++):(b=a,0===wi&&gi(Zt));else p=a;p!==a?c=l=[l,f,p]:(vi=c,c=a)}else vi=c,c=a;else vi=c,c=a;c===a&&(c=null),c!==a&&(l=Gp())!==a&&(f=Fp())!==a&&(p=Gp())!==a?((b=bb())===a&&(b=null),b!==a?(di=r,v=c,d=b,e={dataType:e,length:parseInt(u.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:d},r=e):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a}else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;else vi=r,r=a;var v,d;if(r===a){if(r=vi,(e=ap())===a&&(e=sp())===a&&(e=up())===a&&(e=ip())===a&&(e=cp())===a&&(e=lp())===a&&(e=fp())===a&&(e=pp())===a&&(e=bp())===a&&(e=vp())===a&&(e=op()),e!==a){if(n=[],Kt.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(Zt)),o!==a)for(;o!==a;)n.push(o),Kt.test(t.charAt(vi))?(o=t.charAt(vi),vi++):(o=a,0===wi&&gi(Zt));else n=a;n!==a&&(o=Gp())!==a?((s=bb())===a&&(s=null),s!==a?(di=r,e=function(t,r,e){return{dataType:t,length:parseInt(r.join(""),10),suffix:e}}(e,n,s),r=e):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;r===a&&(r=vi,(e=ap())===a&&(e=sp())===a&&(e=up())===a&&(e=ip())===a&&(e=cp())===a&&(e=lp())===a&&(e=fp())===a&&(e=pp())===a&&(e=bp())===a&&(e=vp())===a&&(e=op()),e!==a&&(n=Gp())!==a?((o=bb())===a&&(o=null),o!==a&&(s=Gp())!==a?(di=r,e=function(t,r){return{dataType:t,suffix:r}}(e,o),r=e):(vi=r,r=a)):(vi=r,r=a))}return r}())===a&&(r=vb())===a&&(r=function(){var r,e;r=vi,(e=function(){var r,e,n,o;return r=vi,"json"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi($s)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="JSON"):(vi=r,r=a)):(vi=r,r=a),r}())!==a&&(di=r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;return r=vi,"tinytext"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Ws)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TINYTEXT"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"text"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Xs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="TEXT"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"mediumtext"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Ks)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MEDIUMTEXT"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"longtext"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Zs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="LONGTEXT"):(vi=r,r=a)):(vi=r,r=a),r}());e!==a?((n=fb())===a&&(n=null),n!==a?(di=r,e=li(e,n),r=e):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;return r=vi,"enum"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(zs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ENUM"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=Sf());e!==a&&Gp()!==a&&(n=Xc())!==a?(di=r,o=e,(s=n).parentheses=!0,r=e={dataType:o,expr:s}):(vi=r,r=a);var o,s;return r}())===a&&(r=function(){var r,e;r=vi,(e=function(){var r,e,n,o;return r=vi,"uuid"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(su)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="UUID"):(vi=r,r=a)):(vi=r,r=a),r}())!==a&&(di=r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e;r=vi,"boolean"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(ai));e!==a&&(di=r,e={dataType:"BOOLEAN"});return r=e}())===a&&(r=function(){var r,e,n;r=vi,(e=function(){var r,e,n,o;return r=vi,"binary"===t.substr(vi,6).toLowerCase()?(e=t.substr(vi,6),vi+=6):(e=a,0===wi&&gi(qr)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="BINARY"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"varbinary"===t.substr(vi,9).toLowerCase()?(e=t.substr(vi,9),vi+=9):(e=a,0===wi&&gi(Ns)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="VARBINARY"):(vi=r,r=a)):(vi=r,r=a),r}());e!==a&&Gp()!==a?((n=fb())===a&&(n=null),n!==a?(di=r,e=li(e,n),r=e):(vi=r,r=a)):(vi=r,r=a);return r}())===a&&(r=function(){var r,e;r=vi,"blob"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(si));e===a&&("tinyblob"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(ui)),e===a&&("mediumblob"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(ii)),e===a&&("longblob"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(ci)))));e!==a&&(di=r,e={dataType:e.toUpperCase()});return r=e}())===a&&(r=function(){var r,e;r=vi,(e=function(){var r,e,n,o;return r=vi,"geometry"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(mu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="GEOMETRY"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"point"===t.substr(vi,5).toLowerCase()?(e=t.substr(vi,5),vi+=5):(e=a,0===wi&&gi(wu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="POINT"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"linestring"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(Ou)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="LINESTRING"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"polygon"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Lu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="POLYGON"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"multipoint"===t.substr(vi,10).toLowerCase()?(e=t.substr(vi,10),vi+=10):(e=a,0===wi&&gi(ju)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MULTIPOINT"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"multilinestring"===t.substr(vi,15).toLowerCase()?(e=t.substr(vi,15),vi+=15):(e=a,0===wi&&gi(Cu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MULTILINESTRING"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"multipolygon"===t.substr(vi,12).toLowerCase()?(e=t.substr(vi,12),vi+=12):(e=a,0===wi&&gi(gu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="MULTIPOLYGON"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"geometrycollection"===t.substr(vi,18).toLowerCase()?(e=t.substr(vi,18),vi+=18):(e=a,0===wi&&gi(Eu)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="GEOMETRYCOLLECTION"):(vi=r,r=a)):(vi=r,r=a),r}());e!==a&&(di=r,e={dataType:e});return r=e}()),r}function fb(){var r,e,n,o,s;if(r=vi,Qp()!==a)if(Gp()!==a){if(e=[],Kt.test(t.charAt(vi))?(n=t.charAt(vi),vi++):(n=a,0===wi&&gi(Zt)),n!==a)for(;n!==a;)e.push(n),Kt.test(t.charAt(vi))?(n=t.charAt(vi),vi++):(n=a,0===wi&&gi(Zt));else e=a;e!==a&&(n=Gp())!==a&&Fp()!==a&&Gp()!==a?((o=bb())===a&&(o=null),o!==a?(di=r,s=o,r={length:parseInt(e.join(""),10),parentheses:!0,suffix:s}):(vi=r,r=a)):(vi=r,r=a)}else vi=r,r=a;else vi=r,r=a;return r}function pb(){var r,e,n,o,s,u,i,c,l,f,p;if(r=vi,(e=function(){var r,e,n,o;return r=vi,"char"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Rs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="CHAR"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=vi,"varchar"===t.substr(vi,7).toLowerCase()?(e=t.substr(vi,7),vi+=7):(e=a,0===wi&&gi(Ms)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="VARCHAR"):(vi=r,r=a)):(vi=r,r=a),r}()),e!==a){if(n=vi,(o=Gp())!==a)if((s=Qp())!==a)if((u=Gp())!==a){if(i=[],Kt.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(Zt)),c!==a)for(;c!==a;)i.push(c),Kt.test(t.charAt(vi))?(c=t.charAt(vi),vi++):(c=a,0===wi&&gi(Zt));else i=a;i!==a&&(c=Gp())!==a&&(l=Fp())!==a&&(f=Gp())!==a?("array"===t.substr(vi,5).toLowerCase()?(p=t.substr(vi,5),vi+=5):(p=a,0===wi&&gi(fi)),p===a&&(p=null),p!==a?n=o=[o,s,u,i,c,l,f,p]:(vi=n,n=a)):(vi=n,n=a)}else vi=n,n=a;else vi=n,n=a;else vi=n,n=a;n===a&&(n=null),n!==a?(di=r,r=e=function(t,r){const e={dataType:t};return r&&(e.length=parseInt(r[3].join(""),10),e.parentheses=!0,e.suffix=r[7]&&["ARRAY"]),e}(e,n)):(vi=r,r=a)}else vi=r,r=a;return r}function bb(){var r,e,n;return r=vi,(e=Xl())===a&&(e=null),e!==a&&Gp()!==a?((n=function(){var r,e,n,o;return r=vi,"zerofill"===t.substr(vi,8).toLowerCase()?(e=t.substr(vi,8),vi+=8):(e=a,0===wi&&gi(Fs)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="ZEROFILL"):(vi=r,r=a)):(vi=r,r=a),r}())===a&&(n=null),n!==a?(di=r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(vi=r,r=a)):(vi=r,r=a),r}function vb(){var r,e,n,o,s,u,i,c,l,f,p;return r=vi,(e=dp())===a&&(e=yp())===a&&(e=mp())===a&&(e=wp())===a&&(e=function(){var r,e,n,o;return r=vi,"year"===t.substr(vi,4).toLowerCase()?(e=t.substr(vi,4),vi+=4):(e=a,0===wi&&gi(Lo)),e!==a?(n=vi,wi++,o=Il(),wi--,o===a?n=void 0:(vi=n,n=a),n!==a?(di=r,r=e="YEAR"):(vi=r,r=a)):(vi=r,r=a),r}()),e!==a?(n=vi,(o=Gp())!==a&&(s=Qp())!==a&&(u=Gp())!==a?(pi.test(t.charAt(vi))?(i=t.charAt(vi),vi++):(i=a,0===wi&&gi(bi)),i!==a&&(c=Gp())!==a&&(l=Fp())!==a&&(f=Gp())!==a?((p=bb())===a&&(p=null),p!==a?n=o=[o,s,u,i,c,l,f,p]:(vi=n,n=a)):(vi=n,n=a)):(vi=n,n=a),n===a&&(n=null),n!==a?(di=r,r=e=function(t,r){const e={dataType:t};return r&&(e.length=parseInt(r[3],10),e.parentheses=!0,e.suffix=r[7]),e}(e,n)):(vi=r,r=a)):(vi=r,r=a),r}const db={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,BOOLEAN:!0,CALL:!0,CASCADE:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CROSS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FOR:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTERSECT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LATERAL:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NATURAL:!0,MINUS:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,RIGHT:!0,READ:!0,RETURNING:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,STATUS:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0},yb={avg:!0,sum:!0,count:!0,max:!0,min:!0,group_concat:!0,std:!0,variance:!0,current_date:!0,current_time:!0,current_timestamp:!0,current_user:!0,user:!0,session_user:!0,system_user:!0};function hb(){return r.includeLocations?{loc:Ci(di,vi)}:{}}function mb(t,r){return{type:"unary_expr",operator:t,expr:r}}function wb(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e}}function Ob(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function Lb(t,r,e=3){const n=[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function jb(t,r){let e=t;for(let t=0;t<r.length;t++)e=wb(r[t][1],e,r[t][3]);return e}function Cb(t){const r=_b[t];return r||(t||null)}function gb(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=Cb(t[1])),r.add(t.join("::"))}return Array.from(r)}function Eb(t){const r=gb(t);t.clear(),r.forEach(r=>t.add(r))}let Ab=[];const Tb=new Set,Sb=new Set,_b={};if((e=u())!==a&&vi===t.length)return e;throw e!==a&&vi<t.length&&gi({type:"end"}),Ei(mi,hi<t.length?t.charAt(hi):null,hi<t.length?Ci(hi,hi+1):Ci(hi,hi))}}},function(t,r,e){t.exports=e(27)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(24);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(28))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),a="function"==typeof BigInt;function s(t,r,e,n){return void 0===t?s[0]:void 0!==r&&(10!=+r||e)?q(t,r,e,n):G(t)}function u(t,r){this.value=t,this.sign=r,this.isSmall=!1}function i(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&S(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function d(t){return t>0?Math.floor(t):Math.ceil(t)}function y(t,e){var n,o,a=t.length,s=e.length,u=new Array(a),i=0,c=r;for(o=0;o<s;o++)i=(n=t[o]+e[o]+i)>=c?1:0,u[o]=n-i*c;for(;o<a;)i=(n=t[o]+i)===c?1:0,u[o++]=n-i*c;return i>0&&u.push(i),u}function h(t,r){return t.length>=r.length?y(t,r):y(r,t)}function m(t,e){var n,o,a=t.length,s=new Array(a),u=r;for(o=0;o<a;o++)n=t[o]-u+e,e=Math.floor(n/u),s[o]=n-e*u,e+=1;for(;e>0;)s[o++]=e%u,e=Math.floor(e/u);return s}function w(t,r){var e,n,o=t.length,a=r.length,s=new Array(o),u=0;for(e=0;e<a;e++)(n=t[e]-u-r[e])<0?(n+=1e7,u=1):u=0,s[e]=n;for(e=a;e<o;e++){if(!((n=t[e]-u)<0)){s[e++]=n;break}n+=1e7,s[e]=n}for(;e<o;e++)s[e]=t[e];return b(s),s}function O(t,r,e){var n,o,a=t.length,s=new Array(a),c=-r;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,s[n]=o<0?o+1e7:o;return"number"==typeof(s=p(s))?(e&&(s=-s),new i(s)):new u(s,e)}function L(t,r){var e,n,o,a,s=t.length,u=r.length,i=v(s+u);for(o=0;o<s;++o){a=t[o];for(var c=0;c<u;++c)e=a*r[c]+i[o+c],n=Math.floor(e/1e7),i[o+c]=e-1e7*n,i[o+c+1]+=n}return b(i),i}function j(t,e){var n,o,a=t.length,s=new Array(a),u=r,i=0;for(o=0;o<a;o++)n=t[o]*e+i,i=Math.floor(n/u),s[o]=n-i*u;for(;i>0;)s[o++]=i%u,i=Math.floor(i/u);return s}function C(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function g(t,e,n){return new u(t<r?j(e,t):L(e,f(t)),n)}function E(t){var r,e,n,o,a=t.length,s=v(a+a);for(n=0;n<a;n++){e=0-(o=t[n])*o;for(var u=n;u<a;u++)r=o*t[u]*2+s[n+u]+e,e=Math.floor(r/1e7),s[n+u]=r-1e7*e;s[n+a]=e}return b(s),s}function A(t,r){var e,n,o,a,s=t.length,u=v(s);for(o=0,e=s-1;e>=0;--e)o=(a=1e7*o+t[e])-(n=d(a/r))*r,u[e]=0|n;return[u,0|o]}function T(t,e){var n,o=G(e);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,y=t.value,h=o.value;if(0===h)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new i(d(y/h)),new i(y%h)]:[s[0],t];if(o.isSmall){if(1===h)return[t,s[0]];if(-1==h)return[t.negate(),s[0]];var m=Math.abs(h);if(m<r){l=p((n=A(y,m))[0]);var O=n[1];return t.sign&&(O=-O),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new i(l),new i(O)]):[new u(l,t.sign!==o.sign),new i(O)]}h=f(m)}var L=S(y,h);if(-1===L)return[s[0],t];if(0===L)return[s[t.sign===o.sign?1:-1],s[0]];l=(n=y.length+h.length<=200?function(t,e){var n,o,a,s,u,i,c,l=t.length,f=e.length,b=r,d=v(e.length),y=e[f-1],h=Math.ceil(b/(2*y)),m=j(t,h),w=j(e,h);for(m.length<=l&&m.push(0),w.push(0),y=w[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==y&&(n=Math.floor((m[o+f]*b+m[o+f-1])/y)),a=0,s=0,i=w.length,u=0;u<i;u++)a+=n*w[u],c=Math.floor(a/b),s+=m[o+u]-(a-c*b),a=c,s<0?(m[o+u]=s+b,s=-1):(m[o+u]=s,s=0);for(;0!==s;){for(n-=1,a=0,u=0;u<i;u++)(a+=m[o+u]-b+w[u])<0?(m[o+u]=a+b,a=0):(m[o+u]=a,a=1);s+=a}d[o]=n}return m=A(m,h)[0],[p(d),p(m)]}(y,h):function(t,r){for(var e,n,o,a,s,u=t.length,i=r.length,c=[],l=[];u;)if(l.unshift(t[--u]),b(l),S(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*r[i-1]+r[i-2],n>i&&(o=1e7*(o+1)),e=Math.ceil(o/a);do{if(S(s=j(r,e),l)<=0)break;e--}while(e);c.push(e),l=w(l,s)}return c.reverse(),[p(c),p(l)]}(y,h))[0];var C=t.sign!==o.sign,g=n[1],E=t.sign;return"number"==typeof l?(C&&(l=-l),l=new i(l)):l=new u(l,C),"number"==typeof g?(E&&(g=-g),g=new i(g)):g=new u(g,E),[l,g]}function S(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function _(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function U(t,r){for(var e,n,a,s=t.prev(),u=s,i=0;u.isEven();)u=u.divide(2),i++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(a=o(r[n]).modPow(u,t)).isUnit()&&!a.equals(s)){for(e=i-1;0!=e;e--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(s))continue t}return!1}return!0}u.prototype=Object.create(s.prototype),i.prototype=Object.create(s.prototype),c.prototype=Object.create(s.prototype),u.prototype.add=function(t){var r=G(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new u(m(e,Math.abs(n)),this.sign):new u(h(e,n),this.sign)},u.prototype.plus=u.prototype.add,i.prototype.add=function(t){var r=G(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new i(e+n);n=f(Math.abs(n))}return new u(m(n,Math.abs(e)),e<0)},i.prototype.plus=i.prototype.add,c.prototype.add=function(t){return new c(this.value+G(t).value)},c.prototype.plus=c.prototype.add,u.prototype.subtract=function(t){var r=G(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?O(e,Math.abs(n),this.sign):function(t,r,e){var n;return S(t,r)>=0?n=w(t,r):(n=w(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new i(n)):new u(n,e)}(e,n,this.sign)},u.prototype.minus=u.prototype.subtract,i.prototype.subtract=function(t){var r=G(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new i(e-n):O(n,Math.abs(e),e>=0)},i.prototype.minus=i.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-G(t).value)},c.prototype.minus=c.prototype.subtract,u.prototype.negate=function(){return new u(this.value,!this.sign)},i.prototype.negate=function(){var t=this.sign,r=new i(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},u.prototype.abs=function(){return new u(this.value,!1)},i.prototype.abs=function(){return new i(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},u.prototype.multiply=function(t){var e,n,o,a=G(t),i=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return s[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new u(j(i,e),l);c=f(e)}return n=i.length,o=c.length,new u(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return L(r,e);n=Math.ceil(n/2);var o=r.slice(n),a=r.slice(0,n),s=e.slice(n),u=e.slice(0,n),i=t(a,u),c=t(o,s),l=t(h(a,o),h(u,s)),f=h(h(i,C(w(w(l,i),c),n)),C(c,2*n));return b(f),f}(i,c):L(i,c),l)},u.prototype.times=u.prototype.multiply,i.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new i(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},u.prototype._multiplyBySmall=function(t){return 0===t.value?s[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},i.prototype.multiply=function(t){return G(t)._multiplyBySmall(this)},i.prototype.times=i.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*G(t).value)},c.prototype.times=c.prototype.multiply,u.prototype.square=function(){return new u(E(this.value),!1)},i.prototype.square=function(){var t=this.value*this.value;return l(t)?new i(t):new u(E(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},u.prototype.divmod=function(t){var r=T(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=i.prototype.divmod=u.prototype.divmod,u.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/G(t).value)},i.prototype.over=i.prototype.divide=u.prototype.over=u.prototype.divide,u.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%G(t).value)},i.prototype.remainder=i.prototype.mod=u.prototype.remainder=u.prototype.mod,u.prototype.pow=function(t){var r,e,n,o=G(t),a=this.value,u=o.value;if(0===u)return s[1];if(0===a)return s[0];if(1===a)return s[1];if(-1===a)return o.isEven()?s[1]:s[-1];if(o.sign)return s[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(a,u)))return new i(d(r));for(e=this,n=s[1];!0&u&&(n=n.times(e),--u),0!==u;)u/=2,e=e.square();return n},i.prototype.pow=u.prototype.pow,c.prototype.pow=function(t){var r=G(t),e=this.value,n=r.value,o=BigInt(0),a=BigInt(1),u=BigInt(2);if(n===o)return s[1];if(e===o)return s[0];if(e===a)return s[1];if(e===BigInt(-1))return r.isEven()?s[1]:s[-1];if(r.isNegative())return new c(o);for(var i=this,l=s[1];(n&a)===a&&(l=l.times(i),--n),n!==o;)n/=u,i=i.square();return l},u.prototype.modPow=function(t,r){if(t=G(t),(r=G(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=s[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(s[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return s[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=i.prototype.modPow=u.prototype.modPow,u.prototype.compareAbs=function(t){var r=G(t),e=this.value,n=r.value;return r.isSmall?1:S(e,n)},i.prototype.compareAbs=function(t){var r=G(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=G(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},u.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=G(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:S(e,n)*(this.sign?-1:1)},u.prototype.compareTo=u.prototype.compare,i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=G(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},i.prototype.compareTo=i.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=G(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,u.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=i.prototype.eq=i.prototype.equals=u.prototype.eq=u.prototype.equals,u.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=i.prototype.neq=i.prototype.notEquals=u.prototype.neq=u.prototype.notEquals,u.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=i.prototype.gt=i.prototype.greater=u.prototype.gt=u.prototype.greater,u.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=i.prototype.lt=i.prototype.lesser=u.prototype.lt=u.prototype.lesser,u.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals=u.prototype.geq=u.prototype.greaterOrEquals,u.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals=u.prototype.leq=u.prototype.lesserOrEquals,u.prototype.isEven=function(){return 0==(1&this.value[0])},i.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},u.prototype.isOdd=function(){return 1==(1&this.value[0])},i.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},u.prototype.isPositive=function(){return!this.sign},i.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=i.prototype.isPositive,u.prototype.isNegative=function(){return this.sign},i.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=i.prototype.isNegative,u.prototype.isUnit=function(){return!1},i.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},u.prototype.isZero=function(){return!1},i.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},u.prototype.isDivisibleBy=function(t){var r=G(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=i.prototype.isDivisibleBy=u.prototype.isDivisibleBy,u.prototype.isPrime=function(t){var r=_(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return U(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),s=Math.ceil(!0===t?2*Math.pow(a,2):a),u=[],i=0;i<s;i++)u.push(o(i+2));return U(e,u)},c.prototype.isPrime=i.prototype.isPrime=u.prototype.isPrime,u.prototype.isProbablePrime=function(t,r){var e=_(this);if(void 0!==e)return e;for(var n=this.abs(),a=void 0===t?5:t,s=[],u=0;u<a;u++)s.push(o.randBetween(2,n.minus(2),r));return U(n,s)},c.prototype.isProbablePrime=i.prototype.isProbablePrime=u.prototype.isProbablePrime,u.prototype.modInv=function(t){for(var r,e,n,a=o.zero,s=o.one,u=G(t),i=this.abs();!i.isZero();)r=u.divide(i),e=a,n=u,a=s,u=i,s=e.subtract(r.multiply(s)),i=n.subtract(r.multiply(i));if(!u.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=i.prototype.modInv=u.prototype.modInv,u.prototype.next=function(){var t=this.value;return this.sign?O(t,1,this.sign):new u(m(t,1),this.sign)},i.prototype.next=function(){var t=this.value;return t+1<e?new i(t+1):new u(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},u.prototype.prev=function(){var t=this.value;return this.sign?new u(m(t,1),!0):O(t,1,this.sign)},i.prototype.prev=function(){var t=this.value;return t-1>-e?new i(t-1):new u(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var I=[1];2*I[I.length-1]<=r;)I.push(2*I[I.length-1]);var x=I.length,N=I[x-1];function k(t){return Math.abs(t)<=r}function R(t,r,e){r=G(r);for(var n=t.isNegative(),a=r.isNegative(),s=n?t.not():t,u=a?r.not():r,i=0,c=0,l=null,f=null,p=[];!s.isZero()||!u.isZero();)i=(l=T(s,N))[1].toJSNumber(),n&&(i=N-1-i),c=(f=T(u,N))[1].toJSNumber(),a&&(c=N-1-c),s=l[0],u=f[0],p.push(e(i,c));for(var b=0!==e(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(N).add(o(p[v]));return b}u.prototype.shiftLeft=function(t){var r=G(t).toJSNumber();if(!k(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=x;)e=e.multiply(N),r-=x-1;return e.multiply(I[r])},c.prototype.shiftLeft=i.prototype.shiftLeft=u.prototype.shiftLeft,u.prototype.shiftRight=function(t){var r,e=G(t).toJSNumber();if(!k(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=x;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=T(n,N))[1].isNegative()?r[0].prev():r[0],e-=x-1}return(r=T(n,I[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=i.prototype.shiftRight=u.prototype.shiftRight,u.prototype.not=function(){return this.negate().prev()},c.prototype.not=i.prototype.not=u.prototype.not,u.prototype.and=function(t){return R(this,t,(function(t,r){return t&r}))},c.prototype.and=i.prototype.and=u.prototype.and,u.prototype.or=function(t){return R(this,t,(function(t,r){return t|r}))},c.prototype.or=i.prototype.or=u.prototype.or,u.prototype.xor=function(t){return R(this,t,(function(t,r){return t^r}))},c.prototype.xor=i.prototype.xor=u.prototype.xor;function M(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function V(t,r){return t=G(t),r=G(r),t.greater(r)?t:r}function D(t,r){return t=G(t),r=G(r),t.lesser(r)?t:r}function P(t,r){if(t=G(t).abs(),r=G(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=s[1];t.isEven()&&r.isEven();)e=D(M(t),M(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(M(t));do{for(;r.isEven();)r=r.divide(M(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}u.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),a=n.p,s=n.e,u=a.multiply(e);return u.compareTo(r)<=0?{p:u,e:2*s+1}:{p:a,e:2*s}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=i.prototype.bitLength=u.prototype.bitLength;var q=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,a=t.length,s=Math.abs(r),u={};for(o=0;o<e.length;o++)u[e[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in u&&u[l]>=s)){if("1"===l&&1===s)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=G(r);var i=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in u)i.push(G(u[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);i.push(G(t.slice(f+1,o)))}}return Q(i,r,c)};function Q(t,r,e){var n,o=s[0],a=s[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(r);return e?o.negate():o}function F(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,s=[],u=t;u.isNegative()||u.compareAbs(r)>=0;){a=u.divmod(r),u=a.quotient;var i=a.remainder;i.isNegative()&&(i=r.minus(i).abs(),u=u.next()),s.push(i.toJSNumber())}return s.push(u.toJSNumber()),{value:s.reverse(),isNegative:n}}function B(t,r,e){var n=F(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function $(t){if(l(+t)){var r=+t;if(r===d(r))return a?new c(BigInt(r)):new i(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==d(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var s=n[0],f=s.indexOf(".");if(f>=0&&(o-=s.length-f-1,s=s.slice(0,f)+s.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=s+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,y=v-7;v>0;)p.push(+t.slice(y,v)),(y-=7)<0&&(y=0),v-=7;return b(p),new u(p,e)}function G(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==d(t))throw new Error(t+" is not an integer.");return new i(t)}return $(t.toString())}(t):"string"==typeof t?$(t):"bigint"==typeof t?new c(t):t}u.prototype.toArray=function(t){return F(this,t)},i.prototype.toArray=function(t){return F(this,t)},c.prototype.toArray=function(t){return F(this,t)},u.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return B(this,t,r);for(var e,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)e=String(n[o]),a+="0000000".slice(e.length)+e;return(this.sign?"-":"")+a},i.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?B(this,t,r):String(this.value)},c.prototype.toString=i.prototype.toString,c.prototype.toJSON=u.prototype.toJSON=i.prototype.toJSON=function(){return this.toString()},u.prototype.valueOf=function(){return parseInt(this.toString(),10)},u.prototype.toJSNumber=u.prototype.valueOf,i.prototype.valueOf=function(){return this.value},i.prototype.toJSNumber=i.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var H=0;H<1e3;H++)s[H]=G(H),H>0&&(s[-H]=G(-H));return s.one=s[1],s.zero=s[0],s.minusOne=s[-1],s.max=V,s.min=D,s.gcd=P,s.lcm=function(t,r){return t=G(t).abs(),r=G(r).abs(),t.divide(P(t,r)).multiply(r)},s.isInstance=function(t){return t instanceof u||t instanceof i||t instanceof c},s.randBetween=function(t,e,n){t=G(t),e=G(e);var o=n||Math.random,a=D(t,e),u=V(t,e).subtract(a).add(1);if(u.isSmall)return a.add(Math.floor(o()*u));for(var i=F(u,r).value,c=[],l=!0,f=0;f<i.length;f++){var p=l?i[f]+(f+1<i.length?i[f+1]/r:0):r,b=d(o()*p);c.push(b),b<i[f]&&(l=!1)}return a.add(s.fromArray(c,r,!1))},s.fromArray=function(t,r,e){return Q(t.map(G),G(r||10),e)},s}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(30)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=mariadb.umd.js.map