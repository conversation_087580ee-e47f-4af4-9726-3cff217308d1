!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=26)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return A})),e.d(r,"autoIncrementToSQL",(function(){return _})),e.d(r,"columnOrderListToSQL",(function(){return x})),e.d(r,"commonKeywordArgsToSQL",(function(){return U})),e.d(r,"commonOptionConnector",(function(){return s})),e.d(r,"connector",(function(){return c})),e.d(r,"commonTypeValue",(function(){return w})),e.d(r,"commentToSQL",(function(){return S})),e.d(r,"createBinaryExpr",(function(){return f})),e.d(r,"createValueExpr",(function(){return l})),e.d(r,"dataTypeToSQL",(function(){return g})),e.d(r,"DEFAULT_OPT",(function(){return u})),e.d(r,"escape",(function(){return p})),e.d(r,"literalToSQL",(function(){return O})),e.d(r,"columnIdentifierToSql",(function(){return y})),e.d(r,"getParserOpt",(function(){return b})),e.d(r,"identifierToSql",(function(){return d})),e.d(r,"onPartitionsToSQL",(function(){return C})),e.d(r,"replaceParams",(function(){return L})),e.d(r,"returningToSQL",(function(){return E})),e.d(r,"hasVal",(function(){return j})),e.d(r,"setParserOpt",(function(){return v})),e.d(r,"toUpper",(function(){return m})),e.d(r,"topToSQL",(function(){return h})),e.d(r,"triggerEventToSQL",(function(){return T}));var n=e(2),o=e(11);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u={database:"sqlite",type:"table",trimQuery:!0,parseOptions:{includeLocations:!1}},i=u;function s(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function c(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function l(t){var r=a(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(l)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function f(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:l(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[l(e[0]),l(e[1])]},n):(n.right=e.type?e:l(e),n)}function p(t){return t}function b(){return i}function v(t){i=t}function h(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function y(t){var r=b().database;if(t)switch(r&&r.toLowerCase()){case"athena":case"db2":case"postgresql":case"redshift":case"snowflake":case"noql":case"trino":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function d(t,r){var e=b().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":return"`".concat(t,"`");case"athena":case"postgresql":case"redshift":case"snowflake":case"trino":case"noql":case"sqlite":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function m(t){if(t)return t.toUpperCase()}function j(t){return t}function O(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,u=t.suffix,i=t.value,s="object"===a(t)?i:t;switch(e){case"backticks_quote_string":s="`".concat(i,"`");break;case"string":s="'".concat(i,"'");break;case"regex_string":s='r"'.concat(i,'"');break;case"hex_string":s="X'".concat(i,"'");break;case"full_hex_string":s="0x".concat(i);break;case"natural_string":s="N'".concat(i,"'");break;case"bit_string":s="b'".concat(i,"'");break;case"double_quote_string":s='"'.concat(i,'"');break;case"single_quote_string":s="'".concat(i,"'");break;case"boolean":case"bool":s=i?"TRUE":"FALSE";break;case"null":s="NULL";break;case"star":s="*";break;case"param":s="".concat(r||":").concat(i),r=null;break;case"origin":s=i.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":s="".concat(e.toUpperCase()," '").concat(i,"'");break;case"var_string":s="N'".concat(i,"'");break;case"unicode_string":s="U&'".concat(i,"'")}var c=[];return r&&c.push(m(r)),c.push(s),u&&("string"==typeof u&&c.push(u),"object"===a(u)&&(u.collate?c.push(Object(o.a)(u.collate)):c.push(O(u)))),s=c.join(" "),n?"(".concat(s,")"):s}}function w(t){if(!t)return[];var r=t.type,e=t.symbol,n=t.value;return[r.toUpperCase(),e,"string"==typeof n?n.toUpperCase():O(n)].filter(j)}function L(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===a(e)&&null!==e})).forEach((function(n){var o=r[n];if("object"!==a(o)||"param"!==o.type)return t(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return r[n]=l(e[o.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function C(t){var r=t.type,e=t.partitions;return[m(r),"(".concat(e.map((function(t){if("range"!==t.type)return O(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(O(r)," ").concat(m(n)," ").concat(O(e))})).join(", "),")")].join(" ")}function g(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,a=t.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),a&&a.length&&(u+=" ".concat(a.join(" "))),"".concat(r).concat(u)}function A(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=m(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(t){return[t.field_name,A(t.field_type)].filter(j).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function S(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(O(o)),r.join(" ")}}function T(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[m(r)];if(e){var a=e.keyword,u=e.columns;o.push(m(a),u.map(n.f).join(", "))}return o.join(" ")})).join(" OR ")}function E(t){return t?["RETURNING",t.columns.map(n.h).filter(j).join(", ")].join(" "):""}function U(t){return t?[m(t.keyword),m(t.args)]:[]}function _(t){if(t){if("string"==typeof t){var r=b().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,u=m(e);return a&&(u+="(".concat(O(n),", ").concat(O(o),")")),u}}function x(t){if(t)return t.map(n.e).filter(j).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return C})),e.d(r,"b",(function(){return g})),e.d(r,"d",(function(){return L})),e.d(r,"c",(function(){return A}));var n=e(0),o=e(9),a=e(13);var u=e(22),i=e(21);var s=e(11),c=e(2),l=e(6),f=e(18);var p=e(7),b=e(23);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(c.i)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(c.i)(r),"]");var u=Array.isArray(o)?o.map((function(t){return"(".concat(Object(c.i)(t),")")})).filter(n.hasVal).join(", "):C(o);return e?"[".concat(u,"]"):a?"(".concat(u,")"):u}(t);default:return""}}function y(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===v(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(h(t)),o.filter(n.hasVal).join("")}var d=e(3),m=e(5),j=e(20);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,u=t.within_group_orderby,i=C(r.expr);i=Array.isArray(i)?i.join(", "):i;var s=t.name,c=Object(a.a)(o);r.distinct&&(i=["DISTINCT",i].join(" ")),r.separator&&r.separator.delimiter&&(i=[i,Object(n.literalToSQL)(r.separator.delimiter)].join("".concat(r.separator.symbol," "))),r.separator&&r.separator.expr&&(i=[i,C(r.separator.expr)].join(" ")),r.orderby&&(i=[i,A(r.orderby,"order by")].join(" ")),r.separator&&r.separator.value&&(i=[i,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=u?"WITHIN GROUP (".concat(A(u,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(C(e.where),")"):"";return["".concat(s,"(").concat(i,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:l.a,window_func:j.c,array:y,assign:u.a,binary_expr:i.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(C(n));for(var a=0,u=e.length;a<u;++a)r.push(e[a].type.toUpperCase()),e[a].cond&&(r.push(C(e[a].cond)),r.push("THEN")),r.push(C(e[a].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:l.c,collate:s.a,column_ref:c.f,column_definition:c.c,datatype:n.dataTypeToSQL,extract:l.d,flatten:l.e,fulltext_search:c.j,function:l.g,lambda:l.i,insert:m.b,interval:f.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return C(t)})).join(", ")].join(" ")},json_object_arg:l.h,json_visitor:function(t){return[t.symbol,C(t.expr)].join("")},func_arg:l.f,show:b.a,struct:y,tablefunc:l.j,tables:d.c,unnest:d.d,window:j.b};function L(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,a=t.members,u=t.quoted,i=t.suffix,s=[],c=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,l="".concat(e||"").concat(c);return i&&(l+=i),s.push(l),[u,s.join(" "),u].filter(n.hasVal).join("")}function C(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,a=Object.keys(e);o<a.length;o++){var u=a[o];r[u]=e[u]}}var i=r.type;return"expr"===i?C(r.expr):w[i]?w[i](r):Object(n.literalToSQL)(r)}}function g(t){return t?(Array.isArray(t)||(t=[t]),t.map(C)):[]}function A(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[C(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return C(t.expr)}))}return Object(n.connector)(o,e.join(", "))}w.var=L,w.expr_list=function(t){var r=g(t.value),e=t.parentheses,n=t.separator;if(!e&&!n)return r;var o=n||", ",a=r.join(o);return e?"(".concat(a,")"):a},w.select=function(t){var r="object"===O(t._next)?Object(m.b)(t):Object(p.a)(t);return t.parentheses?"(".concat(r,")"):r},w.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",a="".concat(r).concat(o).concat(C(n));return e?"(".concat(a,")"):a},w.map_object=function(t){var r=t.keyword,e=t.expr.map((function(t){return[Object(n.literalToSQL)(t.key),Object(n.literalToSQL)(t.value)].join(", ")})).join(", ");return[Object(n.toUpper)(r),"[".concat(e,"]")].join("")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"c",(function(){return O})),e.d(r,"f",(function(){return h})),e.d(r,"h",(function(){return C})),e.d(r,"i",(function(){return A})),e.d(r,"b",(function(){return y})),e.d(r,"d",(function(){return b})),e.d(r,"e",(function(){return j})),e.d(r,"g",(function(){return d})),e.d(r,"j",(function(){return L})),e.d(r,"k",(function(){return g}));var n=e(11),o=e(19),a=e(1),u=e(6),i=e(3),s=e(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||f(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function b(t,r){if("string"==typeof t)return Object(s.identifierToSql)(t,r);var e=t.expr,n=t.offset,o=t.suffix,u=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(s.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(s.hasVal).join("")})).join("");return[Object(a.a)(e),u,o].filter(s.hasVal).join("")}function v(t){if(!t||0===t.length)return"";var r,e=[],n=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=f(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}(t);try{for(n.s();!(r=n.n()).done;){var o=r.value,a=o.brackets?"[".concat(Object(s.literalToSQL)(o.index),"]"):"".concat(o.notation).concat(Object(s.literalToSQL)(o.index));o.property&&(a="".concat(a,".").concat(Object(s.literalToSQL)(o.property))),e.push(a)}}catch(t){n.e(t)}finally{n.f()}return e.join("")}function h(t){var r=t.array_index,e=t.as,o=t.column,u=t.collate,i=t.db,c=t.isDual,f=t.notations,p=void 0===f?[]:f,h=t.options,y=t.schema,d=t.table,m=t.parentheses,j=t.suffix,O=t.order_by,w=t.subFields,L=void 0===w?[]:w,C="*"===o?"*":b(o,c),g=[i,y,d].filter(s.hasVal).map((function(t){return"".concat("string"==typeof t?Object(s.identifierToSql)(t):Object(a.a)(t))})),A=g[0];if(A){for(var S=1;S<g.length;++S)A="".concat(A).concat(p[S]||".").concat(g[S]);C="".concat(A).concat(p[S]||".").concat(C)}var T=[C=["".concat(C).concat(v(r))].concat(l(L)).join("."),Object(n.a)(u),Object(a.a)(h),Object(s.commonOptionConnector)("AS",a.a,e)];T.push("string"==typeof j?Object(s.toUpper)(j):Object(a.a)(j)),T.push(Object(s.toUpper)(O));var E=T.filter(s.hasVal).join(" ");return m?"(".concat(E,")"):E}function y(t){if(t){var r=t.dataType,e=t.length,n=t.suffix,o=t.scale,i=t.expr,c=null!=e,l=Object(s.dataTypeToSQL)({dataType:r,length:e,suffix:n,scale:o,parentheses:c});if(i&&(l+=Object(a.a)(i)),t.array){var f=Object(u.b)(t);l+=[/^\[.*\]$/.test(f)?"":" ",f].join("")}return l}}function d(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,o=t.match,u=t.table,c=t.on_action;return r.push(Object(s.toUpper)(n)),r.push(Object(i.c)(u)),r.push(e&&"(".concat(e.map((function(t){return Object(a.a)(t)})).join(", "),")")),r.push(Object(s.toUpper)(o)),c.map((function(t){return r.push(Object(s.toUpper)(t.type),Object(a.a)(t.value))})),r.filter(s.hasVal)}function m(t){var r=[],e=t.nullable,n=t.character_set,u=t.check,i=t.comment,c=t.constraint,f=t.collate,p=t.storage,b=t.using,v=t.default_val,h=t.generated,y=t.auto_increment,m=t.unique,j=t.primary_key,O=t.column_format,w=t.reference_definition,L=[Object(s.toUpper)(e&&e.action),Object(s.toUpper)(e&&e.value)].filter(s.hasVal).join(" ");if(h||r.push(L),v){var C=v.type,g=v.value;r.push(C.toUpperCase(),Object(a.a)(g))}var A=Object(s.getParserOpt)().database;return c&&r.push(Object(s.toUpper)(c.keyword),Object(s.literalToSQL)(c.constraint)),r.push(Object(o.a)(u)),r.push(function(t){if(t)return[Object(s.toUpper)(t.value),"(".concat(Object(a.a)(t.expr),")"),Object(s.toUpper)(t.storage_type)].filter(s.hasVal).join(" ")}(h)),h&&r.push(L),r.push(Object(s.autoIncrementToSQL)(y),Object(s.toUpper)(j),Object(s.toUpper)(m),Object(s.commentToSQL)(i)),r.push.apply(r,l(Object(s.commonTypeValue)(n))),"sqlite"!==A.toLowerCase()&&r.push(Object(a.a)(f)),r.push.apply(r,l(Object(s.commonTypeValue)(O))),r.push.apply(r,l(Object(s.commonTypeValue)(p))),r.push.apply(r,l(d(w))),r.push(Object(s.commonOptionConnector)("USING",a.a,b)),r.filter(s.hasVal).join(" ")}function j(t){var r=t.column,e=t.collate,n=t.nulls,o=t.opclass,u=t.order_by,i="string"==typeof r?{type:"column_ref",table:t.table,column:r}:t;return i.collate=null,[Object(a.a)(i),Object(a.a)(e),o,Object(s.toUpper)(u),Object(s.toUpper)(n)].filter(s.hasVal).join(" ")}function O(t){var r=[],e=h(t.column),n=y(t.definition);return r.push(e),r.push(n),r.push(m(t)),r.filter(s.hasVal).join(" ")}function w(t){return t?"object"===c(t)?["AS",Object(a.a)(t)].join(" "):["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(s.identifierToSql)(t):Object(s.columnIdentifierToSql)(t)].join(" "):""}function L(t){var r=t.against,e=t.as,n=t.columns,o=t.match,u=t.mode;return[[Object(s.toUpper)(o),"(".concat(n.map((function(t){return h(t)})).join(", "),")")].join(" "),[Object(s.toUpper)(r),["(",Object(a.a)(t.expr),u&&" ".concat(Object(s.literalToSQL)(u)),")"].filter(s.hasVal).join("")].join(" "),w(e)].filter(s.hasVal).join(" ")}function C(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(u.c)(t);r&&(e.isDual=r);var o=Object(a.a)(e),i=t.expr_list;if(i){var c=[o],l=i.map((function(t){return C(t,r)})).join(", ");return c.push([Object(s.toUpper)(n),n&&"(",l,n&&")"].filter(s.hasVal).join("")),c.filter(s.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&"cast"!==e.type&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o).concat(v(e.array_index))),[o,w(t.as)].filter(s.hasVal).join(" ")}function g(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}function A(t,r){if(!t||"*"===t)return t;var e=g(r);return t.map((function(t){return C(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"c",(function(){return d})),e.d(r,"a",(function(){return m})),e.d(r,"b",(function(){return y})),e.d(r,"d",(function(){return f}));var n=e(21),o=e(2),a=e(1),u=e(17),i=e(18),s=e(0);function c(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(s.toUpper)(r),"(").concat(n&&Object(a.a)(n)||"",")"),Object(s.commonOptionConnector)("AS","string"==typeof e?s.identifierToSql:a.a,e),Object(s.commonOptionConnector)(Object(s.toUpper)(o&&o.keyword),s.identifierToSql,o&&o.as)].filter(s.hasVal).join(" ")}function p(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,u=t.expr,i=t.in_expr,c=t.type,l=[Object(a.a)(u),"FOR",Object(o.f)(e),Object(n.a)(i)],f=["".concat(Object(s.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(s.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function b(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,u=t.parentheses,i=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(s.toUpper)(r),"(".concat(Object(s.identifierToSql)(n)),"(".concat(o.map(a.a).filter(s.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(s.toUpper)(r),"=",Object(a.a)(e));break;case"index":c.push(Object(s.toUpper)(i),Object(s.toUpper)(r),u?"(".concat(e.map(s.identifierToSql).join(", "),")"):"= ".concat(Object(s.identifierToSql)(e)));break;default:c.push(Object(a.a)(e))}return c.filter(s.hasVal).join(" ")}}function v(t,r){var e=t.name,n=t.symbol;return[Object(s.toUpper)(e),n,r].filter(s.hasVal).join(" ")}function h(t){var r=[];switch(t.keyword){case"as":r.push("AS","OF",Object(a.a)(t.of));break;case"from_to":r.push("FROM",Object(a.a)(t.from),"TO",Object(a.a)(t.to));break;case"between_and":r.push("BETWEEN",Object(a.a)(t.between),"AND",Object(a.a)(t.and));break;case"contained":r.push("CONTAINED","IN",Object(a.a)(t.in))}return r.filter(s.hasVal).join(" ")}function y(t){if("UNNEST"===Object(s.toUpper)(t.type))return f(t);var r,e,n,c,l=t.table,y=t.db,d=t.as,m=t.expr,j=t.operator,O=t.prefix,w=t.schema,L=t.server,C=t.suffix,g=t.tablesample,A=t.temporal_table,S=t.table_hint,T=Object(s.identifierToSql)(L),E=Object(s.identifierToSql)(y),U=Object(s.identifierToSql)(w),_=l&&Object(s.identifierToSql)(l);if(m)switch(m.type){case"values":var x=m.parentheses,k=m.values,I=m.prefix,N=[x&&"(","",x&&")"],R=Object(u.b)(k);I&&(R=R.split("(").slice(1).map((function(t){return"".concat(Object(s.toUpper)(I),"(").concat(t)})).join("")),N[1]="VALUES ".concat(R),_=N.filter(s.hasVal).join("");break;case"tumble":_=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.offset,a=t.size,u=[Object(s.identifierToSql)(r.expr.db),Object(s.identifierToSql)(r.expr.schema),Object(s.identifierToSql)(r.expr.table)].filter(s.hasVal).join("."),c="DESCRIPTOR(".concat(Object(o.f)(e.expr),")"),l=["TABLE(TUMBLE(TABLE ".concat(v(r,u)),v(e,c)],f=v(a,Object(i.a)(a.expr));return n&&n.expr?l.push(f,"".concat(v(n,Object(i.a)(n.expr)),"))")):l.push("".concat(f,"))")),l.filter(s.hasVal).join(", ")}(m);break;case"generator":e=(r=m).keyword,n=r.type,c=r.generators.map((function(t){return Object(s.commonTypeValue)(t).join(" ")})).join(", "),_="".concat(Object(s.toUpper)(e),"(").concat(Object(s.toUpper)(n),"(").concat(c,"))");break;default:_=Object(a.a)(m)}var V=[[T,E,U,_=[Object(s.toUpper)(O),_,Object(s.toUpper)(C)].filter(s.hasVal).join(" ")].filter(s.hasVal).join(".")];if(g){var q=["TABLESAMPLE",Object(a.a)(g.expr),Object(s.literalToSQL)(g.repeatable)].filter(s.hasVal).join(" ");V.push(q)}V.push(function(t){if(t){var r=t.keyword,e=t.expr;return[Object(s.toUpper)(r),h(e)].filter(s.hasVal).join(" ")}}(A),Object(s.commonOptionConnector)("AS","string"==typeof d?s.identifierToSql:a.a,d),p(j)),S&&V.push(Object(s.toUpper)(S.keyword),"(".concat(S.expr.map(b).filter(s.hasVal).join(", "),")"));var M=V.filter(s.hasVal).join(" ");return t.parentheses?"(".concat(M,")"):M}function d(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=t.joins,o=d(r);if(e){for(var u=[],i=[],l=!0===e?1:e.length,f=0;f++<l;)u.push("("),i.push(")");var p=n&&n.length>0?d([""].concat(c(n))):"";return u.join("")+o+i.join("")+p}return o}var b=t[0],v=[];if("dual"===b.type)return"DUAL";v.push(y(b));for(var h=1;h<t.length;++h){var m=t[h],j=m.on,O=m.using,w=m.join,L=[];L.push(w?" ".concat(Object(s.toUpper)(w)):","),L.push(y(m)),L.push(Object(s.commonOptionConnector)("ON",a.a,j)),O&&L.push("USING (".concat(O.map(s.literalToSQL).join(", "),")")),v.push(L.filter(s.hasVal).join(" "))}return v.filter(s.hasVal).join("")}function m(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var u=Object(s.literalToSQL)(n);switch(r){case"partition by":case"default collate":u=Object(a.a)(n);break;case"options":u="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(a.a).join(", ")}return o.push(u),o.filter(s.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return h})),e.d(r,"c",(function(){return g})),e.d(r,"d",(function(){return A})),e.d(r,"e",(function(){return y})),e.d(r,"f",(function(){return d})),e.d(r,"g",(function(){return m})),e.d(r,"h",(function(){return E})),e.d(r,"i",(function(){return T})),e.d(r,"j",(function(){return S})),e.d(r,"l",(function(){return j})),e.d(r,"m",(function(){return O})),e.d(r,"o",(function(){return w})),e.d(r,"n",(function(){return L})),e.d(r,"k",(function(){return C}));var n=e(2),o=e(14),a=e(0),u=e(1),i=e(3),s=e(16),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(u.a)(t.expr);return"".concat("CALL"," ").concat(r)}function h(t){var r=t.type,e=t.keyword,o=t.name,s=t.prefix,c=t.suffix,l=[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(a.toUpper)(s)];switch(e){case"table":l.push(Object(i.c)(o));break;case"trigger":l.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":l.push(Object(a.identifierToSql)(o));break;case"view":l.push(Object(i.c)(o),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "));break;case"index":l.push.apply(l,[Object(n.f)(o)].concat(f(t.table?["ON",Object(i.b)(t.table)]:[]),[t.options&&t.options.map(u.a).filter(a.hasVal).join(" ")]));break;case"type":l.push(o.map(n.f).join(", "),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "))}return c&&l.push(c.map(u.a).filter(a.hasVal).join(" ")),l.filter(a.hasVal).join(" ")}function y(t){var r=t.type,e=t.table,n=Object(a.toUpper)(r);return"".concat(n," ").concat(Object(a.identifierToSql)(e))}function d(t){var r=t.type,e=t.name,n=t.args,o=[Object(a.toUpper)(r)],i=[e];return n&&i.push("(".concat(Object(u.a)(n).join(", "),")")),o.push(i.join("")),o.filter(a.hasVal).join(" ")}function m(t){var r=t.type,e=t.label,n=t.target,o=t.query,u=t.stmts;return[e,Object(a.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(u),"END LOOP",e].filter(a.hasVal).join(" ")}function j(t){var r=t.type,e=t.level,n=t.raise,o=t.using,i=[Object(a.toUpper)(r),Object(a.toUpper)(e)];return n&&i.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(u.a)(t)})).join(", ")),o&&i.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(u.a)(t)})).join(", ")),i.filter(a.hasVal).join(" ")}function O(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var a,u=l(e);try{for(u.s();!(a=u.n()).done;){var s=a.value.map(i.b);n.push(s.join(" TO "))}}catch(t){u.e(t)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))}function w(t){var r=t.type,e=t.db,n=Object(a.toUpper)(r),o=Object(a.identifierToSql)(e);return"".concat(n," ").concat(o)}function L(t){var r=t.type,e=t.expr,n=t.keyword,o=Object(a.toUpper)(r),i=e.map(u.a).join(", ");return[o,Object(a.toUpper)(n),i].filter(a.hasVal).join(" ")}function C(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(a.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var u,s=[],c=l(n);try{var p=function(){var t=u.value,r=t.table,e=t.lock_type,n=[Object(i.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(e[t])})).filter(a.hasVal).join(" "))}s.push(n.join(" "))};for(c.s();!(u=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[s.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function g(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(u.a)(n)].filter(a.hasVal).join(" ")}function A(t){var r=t.type,e=t.declare,i=t.symbol,s=[Object(a.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,i=t.as,s=t.constant,c=t.datatype,l=t.not_null,f=t.prefix,p=t.definition,b=t.keyword,v=[[r,e].filter(a.hasVal).join(""),Object(a.toUpper)(i),Object(a.toUpper)(s)];switch(b){case"variable":v.push(Object(n.b)(c),Object(u.a)(t.collate),Object(a.toUpper)(l)),p&&v.push(Object(a.toUpper)(p.keyword),Object(u.a)(p.value));break;case"cursor":v.push(Object(a.toUpper)(f));break;case"table":v.push(Object(a.toUpper)(f),"(".concat(p.map(o.a).join(", "),")"))}return v.filter(a.hasVal).join(" ")})).join("".concat(i," "));return s.push(c),s.join(" ")}function S(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,i=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(u.a)(r),Object(a.literalToSQL)(i),"".concat(Object(s.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(u.a)(t.boolean_expr),"THEN",Object(s.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(s.a)(e.ast||e)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function T(t){var r=t.name,e=t.host,n=[Object(a.literalToSQL)(r)];return e&&n.push("@",Object(a.literalToSQL)(e)),n.join("")}function E(t){var r=t.type,e=t.grant_option_for,o=t.keyword,i=t.objects,s=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(r),Object(a.literalToSQL)(e)],b=i.map((function(t){var r=t.priv,e=t.columns,o=[Object(u.a)(r)];return e&&o.push("(".concat(e.map(n.f).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),s)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(s.object_type),s.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(T(s))}return p.push(Object(a.toUpper)(c),l.map(T).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return w})),e.d(r,"a",(function(){return L}));var n=e(9),o=e(1),a=e(3),u=e(0);var i=e(14),s=e(2);function c(t){var r=t.name,e=t.type;switch(e){case"table":case"view":var n=[Object(u.identifierToSql)(r.db),Object(u.identifierToSql)(r.table)].filter(u.hasVal).join(".");return"".concat(Object(u.toUpper)(e)," ").concat(n);case"column":return"COLUMN ".concat(Object(s.f)(r));default:return"".concat(Object(u.toUpper)(e)," ").concat(Object(u.literalToSQL)(r))}}function l(t){var r=t.keyword,e=t.expr;return[Object(u.toUpper)(r),Object(u.literalToSQL)(e)].filter(u.hasVal).join(" ")}var f=e(7);var p=e(8),b=e(15);var v=e(12),h=e(17),y=e(4);function d(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(u.hasVal).join(" ")}var m=e(22);var j=e(23),O={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(u.toUpper)(r),Object(a.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,a=t.as,i=t.schema;return[Object(u.toUpper)(r),Object(u.toUpper)(e),Object(o.a)(n),Object(u.toUpper)(a),Object(u.identifierToSql)(i)].filter(u.hasVal).join(" ")},create:i.b,comment:function(t){var r=t.expr,e=t.keyword,n=t.target,o=t.type;return[Object(u.toUpper)(o),Object(u.toUpper)(e),c(n),l(r)].filter(u.hasVal).join(" ")},select:f.a,deallocate:y.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,i=t.where,c=t.orderby,l=t.with,f=t.limit,v=t.returning,h=[Object(b.a)(l),"DELETE"],y=Object(s.i)(r,e);return h.push(y),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||h.push(Object(a.c)(n))),h.push(Object(u.commonOptionConnector)("FROM",a.c,e)),h.push(Object(u.commonOptionConnector)("WHERE",o.a,i)),h.push(Object(o.c)(c,"order by")),h.push(Object(p.a)(f)),h.push(Object(u.returningToSQL)(v)),h.filter(u.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(u.toUpper)(r),Object(a.b)(e),(n||[]).map(d).filter(u.hasVal).join(", ")].filter(u.hasVal).join(" ")},execute:y.f,explain:function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(f.a)(e)].join(" ")},for:y.g,update:v.b,if:y.j,insert:h.a,drop:y.b,truncate:y.b,replace:h.a,declare:y.d,use:y.o,rename:y.m,call:y.a,desc:y.e,set:y.n,lock:y.k,unlock:y.k,show:j.a,grant:y.h,revoke:y.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(m.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:y.l,transaction:function(t){var r=t.expr,e=r.action,n=r.keyword,o=r.modes,a=[Object(u.literalToSQL)(e),Object(u.toUpper)(n)];return o&&a.push(o.map(u.literalToSQL).join(", ")),a.filter(u.hasVal).join(" ")}};function w(t){if(!t)return"";for(var r=O[t.type],e=t,n=e._parentheses,a=e._orderby,i=e._limit,s=[n&&"(",r(t)];t._next;){var c=O[t._next.type],l=Object(u.toUpper)(t.set_op);s.push(l,c(t._next)),t=t._next}return s.push(n&&")",Object(o.c)(a,"order by"),Object(p.a)(i)),s.filter(u.hasVal).join(" ")}function L(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],a=w(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),r.push(a)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i})),e.d(r,"b",(function(){return s})),e.d(r,"c",(function(){return c})),e.d(r,"d",(function(){return l})),e.d(r,"e",(function(){return p})),e.d(r,"f",(function(){return b})),e.d(r,"g",(function(){return v})),e.d(r,"h",(function(){return f})),e.d(r,"i",(function(){return y})),e.d(r,"j",(function(){return h}));var n=e(2),o=e(1),a=e(0),u=e(13);function i(t){var r=t.args,e=t.type,n=t.over,i=r.expr,s=r.having,c="".concat(Object(a.toUpper)(e),"(").concat(Object(o.a)(i));return s&&(c="".concat(c," HAVING ").concat(Object(a.toUpper)(s.prefix)," ").concat(Object(o.a)(s.expr))),[c="".concat(c,")"),Object(u.a)(n)].filter(a.hasVal).join(" ")}function s(t){if(!t||!t.array)return"";var r=t.array.keyword;if(r)return Object(a.toUpper)(r);for(var e=t.array,n=e.dimension,o=e.length,u=[],i=0;i<n;i++)u.push("["),o&&o[i]&&u.push(Object(a.literalToSQL)(o[i])),u.push("]");return u.join("")}function c(t){for(var r=t.target,e=t.expr,u=t.keyword,i=t.symbol,c=t.as,l=t.offset,f=t.parentheses,p=Object(n.d)({expr:e,offset:l}),b=[],v=0,h=r.length;v<h;++v){var y=r[v],d=y.angle_brackets,m=y.length,j=y.dataType,O=y.parentheses,w=y.quoted,L=y.scale,C=y.suffix,g=y.expr,A=g?Object(o.a)(g):"";null!=m&&(A=L?"".concat(m,", ").concat(L):m),O&&(A="(".concat(A,")")),d&&(A="<".concat(A,">")),C&&C.length&&(A+=" ".concat(C.map(a.literalToSQL).join(" ")));var S="::",T="",E=[];"as"===i&&(0===v&&(p="".concat(Object(a.toUpper)(u),"(").concat(p)),T=")",S=" ".concat(i.toUpperCase()," ")),0===v&&E.push(p);var U=s(y);E.push(S,w,j,w,U,A,T),b.push(E.filter(a.hasVal).join(""))}c&&b.push(" AS ".concat(Object(a.identifierToSql)(c)));var _=b.filter(a.hasVal).join("");return f?"(".concat(_,")"):_}function l(t){var r=t.args,e=t.type,n=r.field,u=r.cast_type,i=r.source,s=["".concat(Object(a.toUpper)(e),"(").concat(Object(a.toUpper)(n)),"FROM",Object(a.toUpper)(u),Object(o.a)(i)];return"".concat(s.filter(a.hasVal).join(" "),")")}function f(t){var r=t.expr,e=r.key,n=r.value,u=r.on,i=[Object(o.a)(e),"VALUE",Object(o.a)(n)];return u&&i.push("ON","NULL",Object(o.a)(u)),i.filter(a.hasVal).join(" ")}function p(t){var r=t.args,e=t.type,n=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,n=t.value;return[Object(a.toUpper)(r),e,Object(o.a)(n)].filter(a.hasVal).join(" ")}(r[t])})).filter(a.hasVal).join(", ");return"".concat(Object(a.toUpper)(e),"(").concat(n,")")}function b(t){var r=t.value,e=r.name,n=r.symbol,u=r.expr;return[e,n,Object(o.a)(u)].filter(a.hasVal).join(" ")}function v(t){var r=t.args,e=t.array_index,i=t.name,s=t.args_parentheses,c=t.parentheses,l=t.within_group,f=t.over,p=t.suffix,b=Object(u.a)(f),v=function(t){if(!t)return"";var r=t.type,e=t.keyword,n=t.orderby;return[Object(a.toUpper)(r),Object(a.toUpper)(e),"(".concat(Object(o.c)(n,"order by"),")")].filter(a.hasVal).join(" ")}(l),h=Object(o.a)(p),y=[Object(a.literalToSQL)(i.schema),i.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");if(!r)return[y,v,b].filter(a.hasVal).join(" ");var d=t.separator||", ";"TRIM"===Object(a.toUpper)(y)&&(d=" ");var m=[y];m.push(!1===s?" ":"(");var j=Object(o.a)(r);if(Array.isArray(d)){for(var O=j[0],w=1,L=j.length;w<L;++w)O=[O,j[w]].join(" ".concat(Object(o.a)(d[w-1])," "));m.push(O)}else m.push(j.join(d));return!1!==s&&m.push(")"),m.push(Object(n.a)(e)),m=[m.join(""),h].filter(a.hasVal).join(" "),[c?"(".concat(m,")"):m,v,b].filter(a.hasVal).join(" ")}function h(t){var r=t.as,e=t.name,n=t.args,u=[Object(a.literalToSQL)(e.schema),e.name.map(a.literalToSQL).join(".")].filter(a.hasVal).join(".");return["".concat(u,"(").concat(Object(o.a)(n).join(", "),")"),"AS",v(r)].join(" ")}function y(t){var r=t.args,e=t.expr,n=r.value,a=r.parentheses,u=n.map(o.a).join(", ");return[a?"(".concat(u,")"):u,"->",Object(o.a)(e)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f}));var n=e(1),o=e(2),a=e(8),u=e(15),i=e(3),s=e(0),c=e(11);function l(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],a=Object(s.toUpper)(r);switch(a){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(a,"string"==typeof e?Object(s.identifierToSql)(e):Object(n.a)(e))}return o.filter(s.hasVal).join(" ")}}function f(t){var r=t.as_struct_val,e=t.columns,f=t.collate,p=t.distinct,b=t.for,v=t.from,h=t.for_sys_time_as_of,y=void 0===h?{}:h,d=t.locking_read,m=t.groupby,j=t.having,O=t.into,w=void 0===O?{}:O,L=t.isolation,C=t.limit,g=t.options,A=t.orderby,S=t.parentheses_symbol,T=t.qualify,E=t.top,U=t.window,_=t.with,x=t.where,k=[Object(u.a)(_),"SELECT",Object(s.toUpper)(r)];Array.isArray(g)&&k.push(g.join(" ")),k.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,o=[Object(s.toUpper)(r)];return e&&o.push("(".concat(e.map(n.a).join(", "),")")),o.filter(s.hasVal).join(" ")}}(p),Object(s.topToSQL)(E),Object(o.i)(e,v));var I=w.position,N="";I&&(N=Object(s.commonOptionConnector)("INTO",l,w)),"column"===I&&k.push(N),k.push(Object(s.commonOptionConnector)("FROM",i.c,v)),"from"===I&&k.push(N);var R=y||{},V=R.keyword,q=R.expr;k.push(Object(s.commonOptionConnector)(V,n.a,q)),k.push(Object(s.commonOptionConnector)("WHERE",n.a,x)),m&&(k.push(Object(s.connector)("GROUP BY",Object(n.b)(m.columns).join(", "))),k.push(Object(n.b)(m.modifiers).join(", "))),k.push(Object(s.commonOptionConnector)("HAVING",n.a,j)),k.push(Object(s.commonOptionConnector)("QUALIFY",n.a,T)),k.push(Object(s.commonOptionConnector)("WINDOW",n.a,U)),k.push(Object(n.c)(A,"order by")),k.push(Object(c.a)(f)),k.push(Object(a.a)(C)),L&&k.push(Object(s.commonOptionConnector)(L.keyword,s.literalToSQL,L.expr)),k.push(Object(s.toUpper)(d)),"end"===I&&k.push(N),k.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,a=[Object(s.toUpper)(o),Object(s.toUpper)(e)];return r?"".concat(a.join(" "),"(").concat(Object(n.a)(r),")"):a.join(" ")}}(b));var M=k.filter(s.hasVal).join(" ");return S?"(".concat(M,")"):M}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){return t?[t.prefix.map(n.literalToSQL).join(" "),Object(o.a)(t.value),t.suffix.map(n.literalToSQL).join(" ")]:[]}function s(t){return t?t.fetch?(e=(r=t).fetch,u=r.offset,[].concat(a(i(u)),a(i(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,u}},function(t,r,e){"use strict";e.d(r,"a",(function(){return p})),e.d(r,"c",(function(){return b})),e.d(r,"b",(function(){return f}));var n=e(2),o=e(14),a=e(10),u=e(3),i=e(1),s=e(7),c=e(0);function l(t,r){switch(t){case"add":var e=r.map((function(t){var r=t.name,e=t.value;return["PARTITION",Object(c.literalToSQL)(r),"VALUES",Object(c.toUpper)(e.type),"(".concat(Object(c.literalToSQL)(e.expr),")")].join(" ")})).join(", ");return"(".concat(e,")");default:return Object(n.i)(r)}}function f(t){if(!t)return"";var r=t.action,e=t.create_definitions,u=t.if_not_exists,i=t.keyword,s=t.if_exists,f=t.old_column,p=t.prefix,b=t.resource,v=t.symbol,h=t.suffix,y="",d=[];switch(b){case"column":d=[Object(n.c)(t)];break;case"index":d=Object(a.c)(t),y=t[b];break;case"table":case"schema":y=Object(c.identifierToSql)(t[b]);break;case"aggregate":case"function":case"domain":case"type":y=Object(c.identifierToSql)(t[b]);break;case"algorithm":case"lock":case"table-option":y=[v,Object(c.toUpper)(t[b])].filter(c.hasVal).join(" ");break;case"constraint":y=Object(c.identifierToSql)(t[b]),d=[Object(o.a)(e)];break;case"partition":d=[l(r,t.partitions)];break;case"key":y=Object(c.identifierToSql)(t[b]);break;default:y=[v,t[b]].filter((function(t){return null!==t})).join(" ")}var m=[Object(c.toUpper)(r),Object(c.toUpper)(i),Object(c.toUpper)(u),Object(c.toUpper)(s),f&&Object(n.f)(f),Object(c.toUpper)(p),y&&y.trim(),d.filter(c.hasVal).join(" ")];return h&&m.push(Object(c.toUpper)(h.keyword),h.expr&&Object(n.f)(h.expr)),m.filter(c.hasVal).join(" ")}function p(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(i.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function b(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type,u=r.expr,i=r.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(u.map(p).join(", ")).concat(i?[" ORDER","BY",i.map(p).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.if_exists,o=t.prefix,a=t.expr,s=void 0===a?[]:a,l=Object(c.toUpper)(r),f=Object(u.c)(e),p=s.map(i.a);return[l,"TABLE",Object(c.toUpper)(n),Object(c.literalToSQL)(o),f,p.join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),f(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),f(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(p).join(", "):"",")")].filter(c.hasVal).join(""),f(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,a=t.select,i=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(u.b)(i)];return e&&f.push("(".concat(e.map(n.f).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(s.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return i})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return u(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function s(t){if(t){var r=t.type,e=t.expr,o=t.symbol,u=r.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":o&&s.push(o),s.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,a(i(t)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":s.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:s.push(o,Object(n.literalToSQL)(e))}return s.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(s):[]}function l(t){var r=t.constraint_type,e=t.index_type,u=t.index_options,s=void 0===u?[]:u,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(i(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(s).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,a(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){if(t){var r=t.keyword,e=t.collate,a=e.name,u=e.symbol,i=e.value,s=[Object(o.toUpper)(r)];return i||s.push(u),s.push(Array.isArray(a)?a.map(o.literalToSQL).join("."):Object(o.literalToSQL)(a)),i&&s.push(u),s.push(Object(n.a)(i)),s.filter(o.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return p})),e.d(r,"a",(function(){return f}));var n=e(3),o=e(1),a=e(2),u=e(8),i=e(0),s=e(15);function c(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return l(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?l(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function f(t){if(!t||0===t.length)return"";var r,e=[],n=c(t);try{for(n.s();!(r=n.n()).done;){var u=r.value,s={},l=u.value;for(var f in u)"value"!==f&&"keyword"!==f&&(s[f]=u[f]);var p=[Object(a.f)(s)],b="";l&&(b=Object(o.a)(l),p.push("=",b)),e.push(p.filter(i.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function p(t){var r=t.from,e=t.table,a=t.set,c=t.where,l=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(s.a)(p),"UPDATE",Object(n.c)(e),Object(i.commonOptionConnector)("SET",f,a),Object(i.commonOptionConnector)("FROM",n.c,r),Object(i.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(l,"order by"),Object(u.a)(b),Object(i.returningToSQL)(v)].filter(i.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(0),o=e(1),a=e(20);function u(t){if(t){var r=t.as_window_specification,e=t.expr,u=t.keyword,i=t.type,s=t.parentheses,c=Object(n.toUpper)(i);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(i)," ").concat(Object(n.toUpper)(u)),f=Object(o.a)(e)||[];return s&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return A})),e.d(r,"a",(function(){return d}));var n=e(9),o=e(1),a=e(10),u=e(2),i=e(4),s=e(19),c=e(6),l=e(3),f=e(12),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,r){if(t){if("string"==typeof t)return y(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?y(t,r):void 0}}function y(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function d(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(u.c)(t);case"index":return Object(a.a)(t);case"constraint":return Object(s.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function m(t){var r=[];switch(t.keyword){case"from":r.push("FROM","(".concat(Object(b.literalToSQL)(t.from),")"),"TO","(".concat(Object(b.literalToSQL)(t.to),")"));break;case"in":r.push("IN","(".concat(Object(o.a)(t.in),")"));break;case"with":r.push("WITH","(MODULUS ".concat(Object(b.literalToSQL)(t.modulus),", REMAINDER ").concat(Object(b.literalToSQL)(t.remainder),")"))}return r.filter(b.hasVal).join(" ")}function j(t){var r=t.keyword,e=t.table,n=t.for_values,o=t.tablespace,a=[Object(b.toUpper)(r),Object(l.b)(e),Object(b.toUpper)(n.keyword),m(n.expr)];return o&&a.push("TABLESPACE",Object(b.literalToSQL)(o)),a.filter(b.hasVal).join(" ")}function O(t){var r=t.as,e=t.domain,n=t.type,a=t.keyword,u=t.target,i=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(u)];if(i&&i.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=h(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}(i);try{for(p.s();!(l=p.n()).done;){var v=l.value,y=v.type;switch(y){case"collate":f.push(Object(o.a)(v));break;case"default":f.push(Object(b.toUpper)(y),Object(o.a)(v.value));break;case"constraint":f.push(Object(s.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function w(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function L(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");case"return":return[Object(b.toUpper)(r),Object(o.a)(t.expr)].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function C(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,i=t.args,s=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.literalToSQL)(a.schema),a.name.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),v=i.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(u.c).join(", "),")"):w(n)].filter(b.hasVal).join(" ")}(s),c.map(L).join(" "),l),f.filter(b.hasVal).join(" ")}function g(t){var r=t.type,e=t.symbol,n=t.value,a=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function A(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.options,s=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(u.expr.map(n.a).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(n.a).join(", ")].join(" "):"");return s.push("".concat(c,"(").concat(l,")"),"(".concat(i.map(g).join(", "),")")),s.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,a=t.as,u=t.temporary,i=t.if_not_exists,s=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.replace,h=t.partition_of,y=t.query_expr,m=t.unlogged,O=t.with,w=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(u),Object(b.toUpper)(m),Object(b.toUpper)(e),Object(b.toUpper)(i),Object(l.c)(n)];if(o){var L=o.type,C=o.table,g=Object(l.c)(C);return w.push(Object(b.toUpper)(L),g),w.filter(b.hasVal).join(" ")}if(h)return w.concat([j(h)]).filter(b.hasVal).join(" ");if(s&&w.push("(".concat(s.map(d).join(", "),")")),c){var A=Object(b.getParserOpt)().database,S=A&&"sqlite"===A.toLowerCase()?", ":" ";w.push(c.map(l.a).join(S))}if(O){var T=O.map((function(t){return[Object(b.literalToSQL)(t.keyword),Object(b.toUpper)(t.symbol),Object(b.literalToSQL)(t.value)].join(" ")})).join(", ");w.push("WITH (".concat(T,")"))}return w.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),y&&w.push(Object(p.b)(y)),w.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,a=t.events,u=t.execute,i=t.for_each,s=t.from,f=t.location,p=t.keyword,h=t.or,y=t.type,d=t.table,m=t.when,j=[Object(b.toUpper)(y),Object(b.toUpper)(h),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],O=Object(b.triggerEventToSQL)(a);return j.push(O,"ON",Object(l.b)(d)),s&&j.push("FROM",Object(l.b)(s)),j.push.apply(j,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(i)))),m&&j.push(Object(b.toUpper)(m.type),Object(o.a)(m.cond)),j.push(Object(b.toUpper)(u.keyword),Object(c.g)(u.expr)),j.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,a=t.execute,i=t.type,s=t.table,c=t.if_not_exists,v=t.temporary,h=t.trigger,y=t.events,d=t.order,m=t.time,j=t.when,O=[Object(b.toUpper)(i),Object(b.toUpper)(v),Object(o.a)(r),Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(h),Object(b.toUpper)(m),y.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(u.f).join(", ")),r.join(" ")})),"ON",Object(l.b)(s),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),d&&"".concat(Object(b.toUpper)(d.keyword)," ").concat(Object(b.identifierToSql)(d.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,j),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":O.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":O.push(Object(p.a)(a.expr.ast))}return O.push(Object(b.toUpper)(a.suffix)),O.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,u=t.type,i=t.with,s=t.version;return[Object(b.toUpper)(u),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(i),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,s),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=C(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,u=t.keyword,i=t.if_not_exists,s=t.include,c=t.index_columns,f=t.index_type,p=t.index_using,h=t.index,y=t.on,d=t.index_options,m=t.algorithm_option,j=t.lock_option,O=t.on_kw,w=t.table,L=t.tablespace,C=t.type,g=t.where,A=t.with,S=t.with_before_where,T=A&&"WITH (".concat(Object(a.b)(A).join(", "),")"),E=s&&"".concat(Object(b.toUpper)(s.keyword)," (").concat(s.columns.map((function(t){return"string"==typeof t?Object(b.identifierToSql)(t):Object(o.a)(t)})).join(", "),")"),U=h;h&&(U="string"==typeof h?Object(b.identifierToSql)(h):[Object(b.identifierToSql)(h.schema),Object(b.identifierToSql)(h.name)].filter(b.hasVal).join("."));var _=[Object(b.toUpper)(C),Object(b.toUpper)(f),Object(b.toUpper)(u),Object(b.toUpper)(i),Object(b.toUpper)(r),U,Object(b.toUpper)(O),Object(l.b)(w)].concat(v(Object(a.d)(p)),["(".concat(Object(b.columnOrderListToSQL)(c),")"),E,Object(a.b)(d).join(" "),Object(n.b)(m),Object(n.b)(j),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,L)]);return S?_.push(T,Object(b.commonOptionConnector)("WHERE",o.a,g)):_.push(Object(b.commonOptionConnector)("WHERE",o.a,g),T),_.push(Object(b.commonOptionConnector)("ON",o.a,y),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),_.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,u=t.create_definitions,i=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];return u&&i.push(u.map(d).join(" ")),i.filter(b.hasVal).join(" ")}(t);break;case"database":case"schema":e=function(t){var r=t.type,e=t.keyword,n=t.replace,o=t.if_not_exists,a=t.create_definitions,u=t[e],i=u.db,s=u.schema,c=[Object(b.literalToSQL)(i),s.map(b.literalToSQL).join(".")].filter(b.hasVal).join("."),f=[Object(b.toUpper)(r),Object(b.toUpper)(n),Object(b.toUpper)(e),Object(b.toUpper)(o),c];return a&&f.push(a.map(l.a).join(" ")),f.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,a=t.if_not_exists,u=t.keyword,i=t.recursive,s=t.replace,c=t.select,l=t.sql_security,f=t.temporary,v=t.type,h=t.view,y=t.with,d=t.with_options,m=h.db,j=h.schema,O=h.view,w=[Object(b.identifierToSql)(m),Object(b.identifierToSql)(j),Object(b.identifierToSql)(O)].filter(b.hasVal).join(".");return[Object(b.toUpper)(v),Object(b.toUpper)(s),Object(b.toUpper)(f),Object(b.toUpper)(i),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),Object(o.a)(n),l&&"SQL SECURITY ".concat(Object(b.toUpper)(l)),Object(b.toUpper)(u),Object(b.toUpper)(a),w,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),d&&["WITH","(".concat(d.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(c),Object(b.toUpper)(y)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=O(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,a=t.name,u=t.resource,i=t.type,s=[Object(b.toUpper)(i),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(u)];if(e){var c=[];switch(u){case"enum":case"range":c.push(Object(o.a)(e));break;default:c.push("(".concat(e.map(d).join(", "),")"))}s.push(c.filter(b.hasVal).join(" "))}return s.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,a=t.if_not_exists,u=t.keyword,s=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(i.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),h=[Object(b.toUpper)(p),Object(b.toUpper)(u),Object(b.toUpper)(a),v];return n&&h.push(Object(b.toUpper)(n.keyword),n.value.map(i.i).join(", ")),h.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&h.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return h.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),h.push(Object(b.literalToSQL)(s),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),h.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(2),o=e(1),a=e(0);function u(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,u=t.columns,i=Array.isArray(u)?"(".concat(u.map(n.f).join(", "),")"):"";return"".concat("default"===r.type?Object(a.identifierToSql)(r.value):Object(a.literalToSQL)(r)).concat(i," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction","explain","comment"];function a(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function u(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function i(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[u(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):u(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(3),o=e(1),a=e(2),u=e(0),i=e(7),s=e(12);function c(t){if("select"===t.type)return Object(i.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(u.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(u.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.f).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,a=[Object(u.toUpper)(e)];switch(n){case"origin":a.push(Object(u.literalToSQL)(r));break;case"update":a.push("UPDATE",Object(u.commonOptionConnector)("SET",s.a,r.set),Object(u.commonOptionConnector)("WHERE",o.a,r.where))}return a.filter(u.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(u.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,a=t.prefix,i=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,h=t.where,y=t.on_duplicate_update,d=t.partition,m=t.returning,j=t.set,O=y||{},w=O.keyword,L=O.set,C=[Object(u.toUpper)(e),Object(u.toUpper)(i),Object(n.c)(r),l(d)];return Array.isArray(f)&&C.push("(".concat(f.map(u.literalToSQL).join(", "),")")),C.push(Object(u.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),C.push(Object(u.commonOptionConnector)("ON CONFLICT",b,p)),C.push(Object(u.commonOptionConnector)("SET",s.a,j)),C.push(Object(u.commonOptionConnector)("WHERE",o.a,h)),C.push(Object(u.commonOptionConnector)(w,s.a,L)),C.push(Object(u.returningToSQL)(m)),C.filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(0),o=e(1);function a(t){var r=t.expr,e=t.unit,a=t.suffix;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e),Object(o.a)(a)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(10),a=e(2);function u(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return i(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?i(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function s(t){if(t){var r=t.constraint,e=t.constraint_type,i=t.enforced,s=t.index,c=t.keyword,l=t.reference_definition,f=t.for,p=t.with_values,b=[],v=Object(n.getParserOpt)().database;b.push(Object(n.toUpper)(c)),b.push(Object(n.identifierToSql)(r));var h=Object(n.toUpper)(e);return"sqlite"===v.toLowerCase()&&"UNIQUE KEY"===h&&(h="UNIQUE"),b.push(h),b.push("sqlite"!==v.toLowerCase()&&Object(n.identifierToSql)(s)),b.push.apply(b,u(Object(o.c)(t))),b.push.apply(b,u(Object(a.g)(l))),b.push(Object(n.toUpper)(i)),b.push(Object(n.commonOptionConnector)("FOR",n.identifierToSql,f)),b.push(Object(n.literalToSQL)(p)),b.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),a=e(13);function u(t){if(t){var r=t.type;return"rows"===r?[Object(n.toUpper)(r),Object(o.a)(t.expr)].filter(n.hasVal).join(" "):Object(o.a)(t)}}function i(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,a=t.orderby,i=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(a,"order by"),u(i)].filter(n.hasVal).join(" ")}(r),")")}function s(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(i(e))}function c(t){return t.expr.map(s).join(", ")}function l(t){var r=t.over;return[function(t){var r=t.args,e=t.name,a=t.consider_nulls,u=void 0===a?"":a,i=t.separator,s=void 0===i?", ":i;return[e,"(",r?Object(o.a)(r).join(s):"",")",u&&" ",u].filter(n.hasVal).join("")}(t),Object(a.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.operator||t.op,e=Object(n.a)(t.right),a=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,e="".concat(e[0]," AND ").concat(e[1])}a||(e="(".concat(e.join(", "),")"))}var u=t.right.escape||{},i=[Array.isArray(t.left)?t.left.map(n.a).join(", "):Object(n.a)(t.left),r,e,Object(o.toUpper)(u.type),Object(n.a)(u.value)].filter(o.hasVal).join(" ");return[t.parentheses?"(".concat(i,")"):i].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.left,e=t.right,a=t.symbol,u=t.keyword;r.keyword=u;var i=Object(n.a)(r),s=Object(n.a)(e);return[i,Object(o.toUpper)(a),s].filter(o.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(1),o=e(8),a=e(3),u=e(0);function i(t){var r,e,i,s,c=t.keyword,l=t.suffix,f="";switch(Object(u.toUpper)(c)){case"BINLOG":e=(r=t).in,i=r.from,s=r.limit,f=[Object(u.commonOptionConnector)("IN",u.literalToSQL,e&&e.right),Object(u.commonOptionConnector)("FROM",a.c,i),Object(o.a)(s)].filter(u.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(u.toUpper)(e)?Object(u.commonOptionConnector)("LIKE",u.literalToSQL,r.right):Object(u.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":case"INDEX":f=Object(u.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(u.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(u.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(u.toUpper)(c),Object(u.toUpper)(l),f].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(2),o=e(1),a=e(25);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var i,s,c,l=(i={},s="sqlite",c=a.parse,(s=function(t){var r=function(t,r){if("object"!=u(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==u(r)?r:r+""}(s))in i?Object.defineProperty(i,s,{value:c,enumerable:!0,configurable:!0,writable:!0}):i[s]=c,i),f=e(16),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return h(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?h(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function h(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function y(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function d(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:r+""}var m=function(){return function(t,r,e){return r&&y(t.prototype,r),e&&y(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}((function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}),[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(o.a)(t)}},{key:"columnsToSQL",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(Object(p.setParserOpt)(e),!t||"*"===t)return[];var o=Object(n.k)(r);return t.map((function(t){return Object(n.h)(t,o)}))}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"sqlite":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](!1===r.trimQuery?t:t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,u=this["".concat(o,"List")].bind(this),i=u(t,e),s=!0,c="",l=v(i);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,h=!1,y=v(r);try{for(y.s();!(f=y.n()).done;){var d=f.value,m=new RegExp("^".concat(d,"$"),"i");if(m.test(b)){h=!0;break}}}catch(t){y.e(t)}finally{y.f()}if(!h){c=b,s=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!s)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])}();r.a=m},function(t,r,e){"use strict";var n=e(29);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?a(t.parts[r][0])+"-"+a(t.parts[r][1]):a(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,a=new Array(t.length);for(r=0;r<t.length;r++)a[r]=(o=t[r],e[o.type](o));if(a.sort(),a.length>0){for(r=1,n=1;r<a.length;r++)a[r-1]!==a[r]&&(a[n]=a[r],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,a={},u={start:Ia},i=Ia,s=function(t,r){return Vc(t,r,1)},c=Ta("IF",!0),l=Ta("if",!0),f=Ta("exists",!0),p=Ta("TRIGGER",!0),b=Ta("BEFORE",!0),v=Ta("AFTER",!0),h=Ta("INSTEAD OF",!0),y=Ta("ON",!0),d=Ta("OF",!0),m=function(t,r){return Vc(t,r)},j=Ta("BEGIN",!0),O=Ta("END",!0),w=Ta("FOR",!0),L=Ta("EACH",!0),C=Ta("ROW",!0),g=Ta("STATEMENT",!0),A=(Ta("CASCADED",!0),Ta("LOCAL",!0)),S=Ta("CHECK",!0),T=(Ta("OPTION",!1),function(t,r){return Vc(t,r)}),E=Ta("AUTO_INCREMENT",!0),U=Ta("AUTOINCREMENT",!0),_=Ta("UNIQUE",!0),x=Ta("KEY",!0),k=Ta("PRIMARY",!0),I=Ta("COLUMN_FORMAT",!0),N=Ta("FIXED",!0),R=Ta("DYNAMIC",!0),V=Ta("DEFAULT",!0),q=Ta("STORAGE",!0),M=Ta("DISK",!0),P=Ta("MEMORY",!0),Q=Ta("first",!0),D=Ta("after",!0),B=Ta("FOREIGN",!0),F=Ta("CHANGE",!0),$=Ta("ALGORITHM",!0),H=Ta("INSTANT",!0),G=Ta("INPLACE",!0),W=Ta("COPY",!0),Y=Ta("LOCK",!0),Z=Ta("NONE",!0),X=Ta("SHARED",!0),J=Ta("EXCLUSIVE",!0),K=Ta("NOT",!0),z=Ta("REPLICATION",!0),tt=Ta("FOREIGN KEY",!0),rt=Ta("ENFORCED",!0),et=Ta("MATCH FULL",!0),nt=Ta("MATCH PARTIAL",!0),ot=Ta("MATCH SIMPLE",!0),at=Ta("RESTRICT",!0),ut=Ta("CASCADE",!0),it=Ta("SET NULL",!0),st=Ta("NO ACTION",!0),ct=Ta("SET DEFAULT",!0),lt=Ta("CHARACTER",!0),ft=Ta("SET",!0),pt=Ta("CHARSET",!0),bt=Ta("COLLATE",!0),vt=Ta("AVG_ROW_LENGTH",!0),ht=Ta("KEY_BLOCK_SIZE",!0),yt=Ta("MAX_ROWS",!0),dt=Ta("MIN_ROWS",!0),mt=Ta("STATS_SAMPLE_PAGES",!0),jt=Ta("CONNECTION",!0),Ot=Ta("COMPRESSION",!0),wt=Ta("'",!1),Lt=Ta("ZLIB",!0),Ct=Ta("LZ4",!0),gt=Ta("ENGINE",!0),At=Ta("WITHOUT",!0),St=Ta("ROWID",!0),Tt=Ta("STRICT",!0),Et=Ta("READ",!0),Ut=Ta("LOW_PRIORITY",!0),_t=Ta("WRITE",!0),xt=function(t,r){return Vc(t,r)},kt=Ta("BINARY",!0),It=Ta("MASTER",!0),Nt=Ta("LOGS",!0),Rt=Ta("BINLOG",!0),Vt=Ta("EVENTS",!0),qt=Ta("COLLATION",!0),Mt=Ta("GRANTS",!0),Pt=Ta("(",!1),Qt=Ta(")",!1),Dt=Ta("BTREE",!0),Bt=Ta("HASH",!0),Ft=Ta("WITH",!0),$t=Ta("PARSER",!0),Ht=Ta("VISIBLE",!0),Gt=Ta("INVISIBLE",!0),Wt=function(t,r){return r.unshift(t),r.forEach(t=>{const{table:r,as:e}=t;Fc[r]=r,e&&(Fc[e]=r),function(t){const r=Pc(t);t.clear(),r.forEach(r=>t.add(r))}(Bc)}),r},Yt=Ta("=",!1),Zt=Ta("DUPLICATE",!0),Xt=function(t,r){return qc(t,r)},Jt=Ta("!",!1),Kt=function(t){return t[0]+" "+t[2]},zt=Ta(">=",!1),tr=Ta(">",!1),rr=Ta("<=",!1),er=Ta("<>",!1),nr=Ta("<",!1),or=Ta("==",!1),ar=Ta("!=",!1),ur=Ta("glob",!0),ir=Ta("+",!1),sr=Ta("-",!1),cr=Ta("*",!1),lr=Ta("/",!1),fr=Ta("%",!1),pr=Ta("||",!1),br=Ta("?",!1),vr=Ta("~",!1),hr=Ta("?|",!1),yr=Ta("?&",!1),dr=Ta("#-",!1),mr=Ta("#>>",!1),jr=Ta("#>",!1),Or=Ta("@>",!1),wr=Ta("<@",!1),Lr=function(t){return!0===xc[t.toUpperCase()]},Cr=Ta('"',!1),gr=/^[^"]/,Ar=Ea(['"'],!0,!1),Sr=/^[^']/,Tr=Ea(["'"],!0,!1),Er=Ta("`",!1),Ur=/^[^`]/,_r=Ea(["`"],!0,!1),xr=function(t,r){return t+r.join("")},kr=/^[A-Za-z_]/,Ir=Ea([["A","Z"],["a","z"],"_"],!1,!1),Nr=/^[A-Za-z0-9_]/,Rr=Ea([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),Vr=/^[A-Za-z0-9_:]/,qr=Ea([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),Mr=Ta(":",!1),Pr=Ta("_binary",!0),Qr=Ta("X",!0),Dr=/^[0-9A-Fa-f]/,Br=Ea([["0","9"],["A","F"],["a","f"]],!1,!1),Fr=Ta("b",!0),$r=Ta("0x",!1),Hr=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}},Gr=/^[^"\\\0-\x1F\x7F]/,Wr=Ea(['"',"\\",["\0",""],""],!0,!1),Yr=/^[^'\\]/,Zr=Ea(["'","\\"],!0,!1),Xr=Ta("\\'",!1),Jr=Ta('\\"',!1),Kr=Ta("\\\\",!1),zr=Ta("\\/",!1),te=Ta("\\b",!1),re=Ta("\\f",!1),ee=Ta("\\n",!1),ne=Ta("\\r",!1),oe=Ta("\\t",!1),ae=Ta("\\u",!1),ue=Ta("\\",!1),ie=Ta("''",!1),se=Ta('""',!1),ce=Ta("``",!1),le=/^[\n\r]/,fe=Ea(["\n","\r"],!1,!1),pe=Ta(".",!1),be=/^[0-9]/,ve=Ea([["0","9"]],!1,!1),he=/^[0-9a-fA-F]/,ye=Ea([["0","9"],["a","f"],["A","F"]],!1,!1),de=/^[eE]/,me=Ea(["e","E"],!1,!1),je=/^[+\-]/,Oe=Ea(["+","-"],!1,!1),we=Ta("ANALYZE",!0),Le=Ta("ATTACH",!0),Ce=Ta("NULL",!0),ge=Ta("NOT NULL",!0),Ae=Ta("TRUE",!0),Se=Ta("TO",!0),Te=Ta("FALSE",!0),Ee=Ta("SHOW",!0),Ue=Ta("DROP",!0),_e=Ta("USE",!0),xe=Ta("ALTER",!0),ke=Ta("SELECT",!0),Ie=Ta("UPDATE",!0),Ne=Ta("CREATE",!0),Re=Ta("TEMPORARY",!0),Ve=Ta("TEMP",!0),qe=Ta("DELETE",!0),Me=Ta("INSERT",!0),Pe=Ta("RECURSIVE",!0),Qe=Ta("REPLACE",!0),De=Ta("RENAME",!0),Be=Ta("IGNORE",!0),Fe=(Ta("EXPLAIN",!0),Ta("PARTITION",!0)),$e=Ta("INTO",!0),He=Ta("FROM",!0),Ge=Ta("UNLOCK",!0),We=Ta("AS",!0),Ye=Ta("TABLE",!0),Ze=Ta("TABLES",!0),Xe=Ta("DATABASE",!0),Je=Ta("SCHEMA",!0),Ke=Ta("LEFT",!0),ze=Ta("INNER",!0),tn=Ta("JOIN",!0),rn=Ta("OUTER",!0),en=Ta("OVER",!0),nn=Ta("UNION",!0),on=Ta("VALUES",!0),an=Ta("USING",!0),un=Ta("WHERE",!0),sn=Ta("GROUP",!0),cn=Ta("BY",!0),ln=Ta("ORDER",!0),fn=Ta("HAVING",!0),pn=Ta("LIMIT",!0),bn=Ta("OFFSET",!0),vn=Ta("ASC",!0),hn=Ta("DESC",!0),yn=Ta("DESCRIBE",!0),dn=Ta("ALL",!0),mn=Ta("DISTINCT",!0),jn=Ta("BETWEEN",!0),On=Ta("IN",!0),wn=Ta("IS",!0),Ln=Ta("LIKE",!0),Cn=Ta("RLIKE",!0),gn=Ta("REGEXP",!0),An=Ta("EXISTS",!0),Sn=Ta("AND",!0),Tn=Ta("OR",!0),En=Ta("COUNT",!0),Un=Ta("MAX",!0),_n=Ta("MIN",!0),xn=Ta("SUM",!0),kn=Ta("AVG",!0),In=Ta("CALL",!0),Nn=Ta("CASE",!0),Rn=Ta("WHEN",!0),Vn=Ta("THEN",!0),qn=Ta("ELSE",!0),Mn=Ta("CAST",!0),Pn=Ta("BIT",!0),Qn=Ta("CHAR",!0),Dn=Ta("VARCHAR",!0),Bn=Ta("NUMERIC",!0),Fn=Ta("DECIMAL",!0),$n=Ta("SIGNED",!0),Hn=Ta("UNSIGNED",!0),Gn=Ta("INT",!0),Wn=Ta("ZEROFILL",!0),Yn=Ta("INTEGER",!0),Zn=Ta("JSON",!0),Xn=Ta("SMALLINT",!0),Jn=Ta("TINYINT",!0),Kn=Ta("TINYTEXT",!0),zn=Ta("TEXT",!0),to=Ta("MEDIUMTEXT",!0),ro=Ta("LONGTEXT",!0),eo=Ta("BIGINT",!0),no=Ta("ENUM",!0),oo=Ta("FLOAT",!0),ao=Ta("DOUBLE",!0),uo=Ta("REAL",!0),io=Ta("DATE",!0),so=Ta("DATETIME",!0),co=Ta("TIME",!0),lo=Ta("TIMESTAMP",!0),fo=Ta("TRUNCATE",!0),po=Ta("USER",!0),bo=Ta("CURRENT_DATE",!0),vo=(Ta("ADDDATE",!0),Ta("INTERVAL",!0)),ho=Ta("YEAR",!0),yo=Ta("MONTH",!0),mo=Ta("DAY",!0),jo=Ta("HOUR",!0),Oo=Ta("MINUTE",!0),wo=Ta("SECOND",!0),Lo=Ta("CURRENT_TIME",!0),Co=Ta("CURRENT_TIMESTAMP",!0),go=Ta("CURRENT_USER",!0),Ao=Ta("SESSION_USER",!0),So=Ta("SYSTEM_USER",!0),To=Ta("GLOBAL",!0),Eo=Ta("SESSION",!0),Uo=Ta("PERSIST",!0),_o=Ta("PERSIST_ONLY",!0),xo=Ta("VIEW",!0),ko=Ta("@",!1),Io=Ta("@@",!1),No=Ta("$",!1),Ro=Ta("return",!0),Vo=Ta(":=",!1),qo=Ta("DUAL",!0),Mo=Ta("ADD",!0),Po=Ta("COLUMN",!0),Qo=Ta("INDEX",!0),Do=Ta("MODIFY",!0),Bo=Ta("FULLTEXT",!0),Fo=Ta("SPATIAL",!0),$o=Ta("COMMENT",!0),Ho=Ta("CONSTRAINT",!0),Go=Ta("REFERENCES",!0),Wo=Ta("SQL_CALC_FOUND_ROWS",!0),Yo=Ta("SQL_CACHE",!0),Zo=Ta("SQL_NO_CACHE",!0),Xo=Ta("SQL_SMALL_RESULT",!0),Jo=Ta("SQL_BIG_RESULT",!0),Ko=Ta("SQL_BUFFER_RESULT",!0),zo=Ta(",",!1),ta=Ta("[",!1),ra=Ta("]",!1),ea=Ta(";",!1),na=Ta("->",!1),oa=Ta("->>",!1),aa=Ta("&&",!1),ua=Ta("/*",!1),ia=Ta("*/",!1),sa=Ta("--",!1),ca=Ta("#",!1),la={type:"any"},fa=/^[ \t\n\r]/,pa=Ea([" ","\t","\n","\r"],!1,!1),ba=Ta("blob",!0),va=Ta("tinyblob",!0),ha=Ta("mediumblob",!0),ya=Ta("longblob",!0),da=Ta("boolean",!0),ma=function(t){return{dataType:t}},ja=/^[0-6]/,Oa=Ea([["0","6"]],!1,!1),wa=0,La=0,Ca=[{line:1,column:1}],ga=0,Aa=[],Sa=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');i=u[r.startRule]}function Ta(t,r){return{type:"literal",text:t,ignoreCase:r}}function Ea(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function Ua(r){var e,n=Ca[r];if(n)return n;for(e=r-1;!Ca[e];)e--;for(n={line:(n=Ca[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Ca[r]=n,n}function _a(t,r){var e=Ua(t),n=Ua(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function xa(t){wa<ga||(wa>ga&&(ga=wa,Aa=[]),Aa.push(t))}function ka(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function Ia(){var t,r;return t=wa,fc()!==a&&(r=Va())!==a?(La=t,t=r):(wa=t,t=a),t}function Na(){var r;return(r=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;r=wa,"analyze"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(we));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ANALYZE"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&(n=gu())!==a&&fc()!==a?(La=r,o=e,u=n,Dc.add(`${o}::${u.db}::${u.table}`),e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:o.toLowerCase(),table:u}},r=e):(wa=r,r=a);var o,u;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=function(){var r,e,n,o;r=wa,"attach"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Le));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ATTACH"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&(n=os())!==a&&fc()!==a&&(o=Gu())!==a&&fc()!==a&&(u=rs())!==a&&fc()!==a&&(i=li())!==a&&fc()!==a?(La=r,s=e,c=n,l=o,f=u,p=i,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:s.toLowerCase(),database:c,expr:l,as:f&&f[0].toLowerCase(),schema:p}},r=e):(wa=r,r=a);var s,c,l,f,p;return r}())===a&&(r=function(){var t,r,e,n,o,u,i;t=wa,(r=$i())!==a&&fc()!==a&&(e=es())!==a&&fc()!==a?((n=Qa())===a&&(n=null),n!==a&&fc()!==a&&(o=Ou())!==a?(La=t,c=r,l=e,f=n,(p=o)&&p.forEach(t=>Dc.add(`${c}::${t.db}::${t.table}`)),r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:c.toLowerCase(),keyword:l.toLowerCase(),prefix:f,name:p}},t=r):(wa=t,t=a)):(wa=t,t=a);var c,l,f,p;t===a&&(t=wa,(r=$i())!==a&&fc()!==a&&(e=Hs())!==a&&fc()!==a?((n=Qa())===a&&(n=null),n!==a&&fc()!==a&&(o=Ou())!==a?(La=t,r=function(t,r,e,n){return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),prefix:e,name:n}}}(r,e,n,o),t=r):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=$i())!==a&&fc()!==a&&(e=Js())!==a&&fc()!==a&&(n=ii())!==a&&fc()!==a&&(o=as())!==a&&fc()!==a&&(u=gu())!==a&&fc()!==a?((i=function(){var t,r,e,n,o,u;t=wa,(r=Za())===a&&(r=Xa());if(r!==a){for(e=[],n=wa,(o=fc())!==a?((u=Za())===a&&(u=Xa()),u!==a?n=o=[o,u]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=Za())===a&&(u=Xa()),u!==a?n=o=[o,u]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,r=s(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())===a&&(i=null),i!==a&&fc()!==a?(La=t,r=function(t,r,e,n,o){return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),name:e,table:n,options:o}}}(r,e,n,u,i),t=r):(wa=t,t=a)):(wa=t,t=a)));return t}())===a&&(r=function(){var r;(r=function(){var t,r,e,n,o,u,i;t=wa,(r=Gi())!==a&&fc()!==a?((e=Wi())===a&&(e=Yi()),e===a&&(e=null),e!==a&&fc()!==a&&es()!==a&&fc()!==a?((n=Pa())===a&&(n=null),n!==a&&fc()!==a&&(o=gu())!==a&&fc()!==a&&(u=function(){var t,r,e,n,o,u,i,s,c;if(t=wa,(r=ac())!==a)if(fc()!==a)if((e=Fa())!==a){for(n=[],o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=Fa())!==a?o=u=[u,i,s,c]:(wa=o,o=a);o!==a;)n.push(o),o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=Fa())!==a?o=u=[u,i,s,c]:(wa=o,o=a);n!==a&&(o=fc())!==a&&(u=uc())!==a?(La=t,r=T(e,n),t=r):(wa=t,t=a)}else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;return t}())!==a&&fc()!==a?((i=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=ou())!==a){for(e=[],n=wa,(o=fc())!==a?((u=nc())===a&&(u=null),u!==a&&(i=fc())!==a&&(s=ou())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=nc())===a&&(u=null),u!==a&&(i=fc())!==a&&(s=ou())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,r=m(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())===a&&(i=null),i!==a?(La=t,s=r,c=e,l=n,p=u,b=i,(f=o)&&Dc.add(`create::${f.db}::${f.table}`),r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:s[0].toLowerCase(),keyword:"table",temporary:c&&c[0].toLowerCase(),if_not_exists:l,table:[f],create_definitions:p,table_options:b}},t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);var s,c,l,f,p,b;t===a&&(t=wa,(r=Gi())!==a&&fc()!==a?((e=Wi())===a&&(e=Yi()),e===a&&(e=null),e!==a&&fc()!==a&&es()!==a&&fc()!==a?((n=Pa())===a&&(n=null),n!==a&&fc()!==a&&(o=gu())!==a&&fc()!==a&&(u=rs())!==a&&fc()!==a&&(i=uu())!==a?(La=t,r=function(t,r,e,n,o,a){return n&&Dc.add(`create::${n.db}::${n.table}`),{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),if_not_exists:e,table:[n],as:"as",query_expr:a}}}(r,e,n,o,0,i),t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=Gi())!==a&&fc()!==a?((n=os())===a&&(n=function(){var r,e,n,o;r=wa,"schema"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Je));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SCHEMA"):(wa=r,r=a)):(wa=r,r=a);return r}()),n!==a&&fc()!==a?((o=Pa())===a&&(o=null),o!==a&&fc()!==a&&(u=gc())!==a&&fc()!==a?((i=function(){var t,r,e,n,o,u;if(t=wa,(r=nu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nu())!==a?n=o=[o,u]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nu())!==a?n=o=[o,u]:(wa=n,n=a);e!==a?(La=t,r=s(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())===a&&(i=null),i!==a?(La=r,e=function(t,r,e,n,o){const a=r.toLowerCase();return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t[0].toLowerCase(),keyword:a,if_not_exists:e,[a]:{db:n.schema,schema:n.name},create_definitions:o}}}(e,n,o,u,i),r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var t,r,e,n,o,u,i,s,c,l,f;t=wa,(r=Gi())!==a&&fc()!==a?((e=zs())===a&&(e=null),e!==a&&fc()!==a&&(n=Js())!==a&&fc()!==a?((o=Pa())===a&&(o=null),o!==a&&fc()!==a&&(u=gu())!==a&&fc()!==a?((i=du())===a&&(i=null),i!==a&&fc()!==a&&(s=as())!==a&&fc()!==a&&(c=gu())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(l=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Ba())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ba())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ba())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=m(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a&&fc()!==a&&uc()!==a&&fc()!==a?((f=Tu())===a&&(f=null),f!==a?(La=t,p=r,b=e,v=n,h=o,y=u,d=s,j=c,O=l,w=f,r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:p[0].toLowerCase(),index_type:b&&b.toLowerCase(),keyword:v.toLowerCase(),if_not_exists:h,index:{schema:y.db,name:y.table},on_kw:d[0].toLowerCase(),table:j,index_columns:O,where:w}},t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);var p,b,v,h,y,d,j,O,w;return t}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f,d,A,S;r=wa,(e=Gi())!==a&&fc()!==a?((n=Wi())===a&&(n=Yi()),n===a&&(n=null),n!==a&&fc()!==a?("trigger"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(p)),o!==a&&fc()!==a?((u=Pa())===a&&(u=null),u!==a&&fc()!==a&&(i=gu())!==a&&fc()!==a?("before"===t.substr(wa,6).toLowerCase()?(s=t.substr(wa,6),wa+=6):(s=a,0===Sa&&xa(b)),s===a&&("after"===t.substr(wa,5).toLowerCase()?(s=t.substr(wa,5),wa+=5):(s=a,0===Sa&&xa(v)),s===a&&("instead of"===t.substr(wa,10).toLowerCase()?(s=t.substr(wa,10),wa+=10):(s=a,0===Sa&&xa(h)))),s===a&&(s=null),s!==a&&fc()!==a&&(c=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Da())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=Ls())!==a&&(i=fc())!==a&&(s=Da())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=Ls())!==a&&(i=fc())!==a&&(s=Da())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=m(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a&&fc()!==a?("on"===t.substr(wa,2).toLowerCase()?(l=t.substr(wa,2),wa+=2):(l=a,0===Sa&&xa(y)),l!==a&&fc()!==a&&(f=gu())!==a&&fc()!==a?((d=function(){var r,e,n,o;r=wa,"for"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(w));e!==a&&fc()!==a?("each"===t.substr(wa,4).toLowerCase()?(n=t.substr(wa,4),wa+=4):(n=a,0===Sa&&xa(L)),n===a&&(n=null),n!==a&&fc()!==a?("row"===t.substr(wa,3).toLowerCase()?(o=t.substr(wa,3),wa+=3):(o=a,0===Sa&&xa(C)),o===a&&("statement"===t.substr(wa,9).toLowerCase()?(o=t.substr(wa,9),wa+=9):(o=a,0===Sa&&xa(g))),o!==a?(La=r,u=e,s=o,e={keyword:(i=n)?`${u.toLowerCase()} ${i.toLowerCase()}`:u.toLowerCase(),args:s.toLowerCase()},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var u,i,s;return r}())===a&&(d=null),d!==a&&fc()!==a?((A=function(){var t,r;t=wa,gs()!==a&&fc()!==a&&(r=Gu())!==a?(La=t,t={type:"when",cond:r}):(wa=t,t=a);return t}())===a&&(A=null),A!==a&&fc()!==a&&(S=function(){var r,e,n,o;r=wa,"begin"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(j));e!==a&&fc()!==a&&(n=Va())!==a&&fc()!==a?("end"===t.substr(wa,3).toLowerCase()?(o=t.substr(wa,3),wa+=3):(o=a,0===Sa&&xa(O)),o!==a?(La=r,r=e={type:"multiple",prefix:e,expr:n,suffix:o}):(wa=r,r=a)):(wa=r,r=a);return r}())!==a?(La=r,E=o,U=u,_=i,x=s,k=c,I=f,N=d,R=A,V=S,e={type:"create",temporary:(T=n)&&T[0].toLowerCase(),time:x&&x.toLowerCase(),events:k,trigger:_,table:I,for_each:N,if_not_exists:U,when:R,execute:V,keyword:E&&E.toLowerCase()},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var T,E,U,_,x,k,I,N,R,V;return r}())===a&&(r=function(){var t,r,e,n,o,u,i,s,c,l,f;t=wa,(r=Gi())!==a&&fc()!==a?((e=Yi())===a&&(e=Wi()),e===a&&(e=null),e!==a&&fc()!==a&&Hs()!==a&&fc()!==a?((n=Pa())===a&&(n=null),n!==a&&fc()!==a&&(o=gu())!==a&&fc()!==a?(u=wa,(i=ac())!==a&&(s=fc())!==a&&(c=si())!==a&&(l=fc())!==a&&(f=uc())!==a?u=i=[i,s,c,l,f]:(wa=u,u=a),u===a&&(u=null),u!==a&&(i=fc())!==a&&(s=rs())!==a&&(c=fc())!==a&&(l=lu())!==a?(La=t,p=r,b=e,v=n,y=u,d=l,(h=o).view=h.table,delete h.table,r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:p[0].toLowerCase(),keyword:"view",if_not_exists:v,temporary:b&&b[0].toLowerCase(),columns:y&&y[2],select:d,view:h}},t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);var p,b,v,h,y,d;return t}());return r}())===a&&(r=function(){var r,e,n,o;r=wa,(e=function(){var r,e,n,o;r=wa,"truncate"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(fo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TRUNCATE"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a?((n=es())===a&&(n=null),n!==a&&fc()!==a&&(o=Ou())!==a?(La=r,u=e,i=n,(s=o)&&s.forEach(t=>Dc.add(`${u}::${t.db}::${t.table}`)),e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:u.toLowerCase(),keyword:i&&i.toLowerCase()||"table",name:s}},r=e):(wa=r,r=a)):(wa=r,r=a);var u,i,s;return r}())===a&&(r=function(){var t,r,e;t=wa,(r=Ji())!==a&&fc()!==a&&es()!==a&&fc()!==a&&(e=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=yu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=yu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=yu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=T(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=t,(n=e).forEach(t=>t.forEach(t=>t.table&&Dc.add(`rename::${t.db}::${t.table}`))),r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"rename",table:n}},t=r):(wa=t,t=a);var n;return t}())===a&&(r=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;r=wa,"call"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(In));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CALL"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&(n=Ac())!==a?(La=r,o=n,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"call",expr:o}},r=e):(wa=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;r=wa,"use"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(_e));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&(n=li())!==a?(La=r,o=n,Dc.add(`use::${o}::null`),e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"use",db:o}},r=e):(wa=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n,o;r=wa,(e=function(){var r,e,n,o;r=wa,"alter"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(xe));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&es()!==a&&fc()!==a&&(n=Ou())!==a&&fc()!==a&&(o=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Ya())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ya())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ya())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=T(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=r,i=o,(u=n)&&u.length>0&&u.forEach(t=>Dc.add(`alter::${t.db}::${t.table}`)),e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"alter",table:u,expr:i}},r=e):(wa=r,r=a);var u,i;return r}())===a&&(r=function(){var r,e,n,o;r=wa,(e=ts())!==a&&fc()!==a?((n=function(){var r,e,n,o;r=wa,"global"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(To));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="GLOBAL"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=wa,"session"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Eo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SESSION"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=wa,"local"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(A));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="LOCAL"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=wa,"persist"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Uo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="PERSIST"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=wa,"persist_only"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(_o));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="PERSIST_ONLY"):(wa=r,r=a)):(wa=r,r=a);return r}()),n===a&&(n=null),n!==a&&fc()!==a&&(o=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=jc())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=jc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=jc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=xt(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=r,u=n,(i=o).keyword=u,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"set",keyword:u,expr:i}},r=e):(wa=r,r=a)):(wa=r,r=a);var u,i;return r}())===a&&(r=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;r=wa,"lock"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Y));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&ns()!==a&&fc()!==a&&(n=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=au())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=au())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=au())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=xt(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=r,o=n,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"lock",keyword:"tables",tables:o}},r=e):(wa=r,r=a);var o;return r}())===a&&(r=function(){var r,e;r=wa,(e=function(){var r,e,n,o;r=wa,"unlock"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Ge));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&ns()!==a?(La=r,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"unlock",keyword:"tables"}},r=e):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l;r=wa,(e=Fi())!==a&&fc()!==a?("binary"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(kt)),n===a&&("master"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(It))),n!==a&&(o=fc())!==a?("logs"===t.substr(wa,4).toLowerCase()?(u=t.substr(wa,4),wa+=4):(u=a,0===Sa&&xa(Nt)),u!==a?(La=r,f=n,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"show",suffix:"logs",keyword:f.toLowerCase()}},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var f;r===a&&(r=wa,(e=Fi())!==a&&fc()!==a?("binlog"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(Rt)),n!==a&&(o=fc())!==a?("events"===t.substr(wa,6).toLowerCase()?(u=t.substr(wa,6),wa+=6):(u=a,0===Sa&&xa(Vt)),u!==a&&(i=fc())!==a?((s=ti())===a&&(s=null),s!==a&&fc()!==a?((c=hu())===a&&(c=null),c!==a&&fc()!==a?((l=ku())===a&&(l=null),l!==a?(La=r,p=s,b=c,v=l,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"show",suffix:"events",keyword:"binlog",in:p,from:b,limit:v}},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=Fi())!==a&&fc()!==a?(n=wa,"character"===t.substr(wa,9).toLowerCase()?(o=t.substr(wa,9),wa+=9):(o=a,0===Sa&&xa(lt)),o!==a&&(u=fc())!==a?("set"===t.substr(wa,3).toLowerCase()?(i=t.substr(wa,3),wa+=3):(i=a,0===Sa&&xa(ft)),i!==a?n=o=[o,u,i]:(wa=n,n=a)):(wa=n,n=a),n===a&&("collation"===t.substr(wa,9).toLowerCase()?(n=t.substr(wa,9),wa+=9):(n=a,0===Sa&&xa(qt))),n!==a&&(o=fc())!==a?((u=zu())===a&&(u=Tu()),u===a&&(u=null),u!==a?(La=r,e=function(t,r){let e=Array.isArray(t)&&t||[t];return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"show",suffix:e[2]&&e[2].toLowerCase(),keyword:e[0].toLowerCase(),expr:r}}}(n,u),r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=function(){var r,e,n,o;r=wa,(e=Fi())!==a&&fc()!==a?("grants"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(Mt)),n!==a&&fc()!==a?((o=function(){var r,e,n,o,u,i,s;r=wa,"for"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(w));e!==a&&fc()!==a&&(n=li())!==a&&fc()!==a?(o=wa,(u=Gs())!==a&&(i=fc())!==a&&(s=li())!==a?o=u=[u,i,s]:(wa=o,o=a),o===a&&(o=null),o!==a&&(u=fc())!==a?((i=function(){var t,r;t=wa,ss()!==a&&fc()!==a&&(r=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=li())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=li())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=li())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=xt(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=t,t=r):(wa=t,t=a);return t}())===a&&(i=null),i!==a?(La=r,l=i,e={user:n,host:(c=o)&&c[2],role_list:l},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var c,l;return r}())===a&&(o=null),o!==a?(La=r,u=o,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"show",keyword:"grants",for:u}},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var u;return r}())));var p,b,v;return r}())===a&&(r=function(){var r,e,n;r=wa,(e=ps())===a&&(e=function(){var r,e,n,o;r=wa,"describe"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(yn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DESCRIBE"):(wa=r,r=a)):(wa=r,r=a);return r}());e!==a&&fc()!==a&&(n=li())!==a?(La=r,o=n,e={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"desc",table:o}},r=e):(wa=r,r=a);var o;return r}()),r}function Ra(){var r;return(r=Ma())===a&&(r=function(){var t,r,e,n,o,u,i;t=wa,(r=Hi())!==a&&fc()!==a&&(e=Ou())!==a&&fc()!==a&&ts()!==a&&fc()!==a&&(n=Iu())!==a&&fc()!==a?((o=Tu())===a&&(o=null),o!==a&&fc()!==a?((u=Uu())===a&&(u=null),u!==a&&fc()!==a?((i=ku())===a&&(i=null),i!==a?(La=t,r=function(t,r,e,n,o){const a={};return t&&t.forEach(t=>{const{server:r,db:e,schema:n,as:o,table:u,join:i}=t,s=i?"select":"update",c=[r,e,n].filter(Boolean).join(".")||null;e&&(a[u]=c),u&&Dc.add(`${s}::${c}::${u}`)}),r&&r.forEach(t=>{if(t.table){const r=Mc(t.table);Dc.add(`update::${a[r]||null}::${r}`)}Bc.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"update",table:t,set:r,where:e,orderby:n,limit:o}}}(e,n,o,u,i),t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o,u,i,s;t=wa,(r=Mu())!==a&&fc()!==a?((e=zi())===a&&(e=null),e!==a&&fc()!==a&&(n=gu())!==a&&fc()!==a?((o=Vu())===a&&(o=null),o!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(u=si())!==a&&fc()!==a&&uc()!==a&&fc()!==a&&(i=Ru())!==a&&fc()!==a?((s=qu())===a&&(s=null),s!==a?(La=t,r=function(t,r,e,n,o,a){if(r&&(Dc.add(`insert::${r.db}::${r.table}`),r.as=null),n){let t=r&&r.table||null;Array.isArray(o)&&o.forEach((t,r)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),n.forEach(r=>Bc.add(`insert::${t}::${r}`))}return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t,table:[r],columns:n,values:o,partition:e,on_duplicate_update:a}}}(r,n,o,u,i,s),t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);return t}())===a&&(r=function(){var r,e,n,o,u,i,s,c;r=wa,(e=Mu())!==a&&fc()!==a?((n=function(){var r,e,n,o;r=wa,"ignore"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Be));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=null),n!==a&&fc()!==a?((o=zi())===a&&(o=null),o!==a&&fc()!==a&&(u=gu())!==a&&fc()!==a?((i=Vu())===a&&(i=null),i!==a&&fc()!==a&&(s=Ru())!==a&&fc()!==a?((c=qu())===a&&(c=null),c!==a?(La=r,e=function(t,r,e,n,o,a,u){n&&(Dc.add(`insert::${n.db}::${n.table}`),Bc.add(`insert::${n.table}::(.*)`),n.as=null);const i=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:i,on_duplicate_update:u}}}(e,n,o,u,i,s,c),r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var t,r,e,n,o,u;t=wa,(r=Mu())!==a&&fc()!==a&&zi()!==a&&fc()!==a&&(e=gu())!==a&&fc()!==a?((n=Vu())===a&&(n=null),n!==a&&fc()!==a&&ts()!==a&&fc()!==a&&(o=Iu())!==a&&fc()!==a?((u=qu())===a&&(u=null),u!==a?(La=t,i=r,c=n,l=o,f=u,(s=e)&&(Dc.add(`insert::${s.db}::${s.table}`),Bc.add(`insert::${s.table}::(.*)`),s.as=null),r={tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:i,table:[s],columns:null,partition:c,set:l,on_duplicate_update:f}},t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);var i,s,c,l,f;return t}())===a&&(r=function(){var t,r,e,n,o,u,i;t=wa,(r=Zi())!==a&&fc()!==a?((e=Ou())===a&&(e=null),e!==a&&fc()!==a&&(n=hu())!==a&&fc()!==a?((o=Tu())===a&&(o=null),o!==a&&fc()!==a?((u=Uu())===a&&(u=null),u!==a&&fc()!==a?((i=ku())===a&&(i=null),i!==a?(La=t,r=function(t,r,e,n,o){if(r&&r.forEach(t=>{const{db:r,as:e,table:n,join:o}=t,a=o?"select":"delete";n&&Dc.add(`${a}::${r}::${n}`),o||Bc.add(`delete::${n}::(.*)`)}),null===t&&1===r.length){const e=r[0];t=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:{type:"delete",table:t,from:r,where:e,orderby:n,limit:o}}}(e,n,o,u,i),t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);return t}())===a&&(r=Na())===a&&(r=function(){var t,r;t=[],r=mc();for(;r!==a;)t.push(r),r=mc();return t}()),r}function Va(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Ra())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=ic())!==a&&(i=fc())!==a&&(s=Ra())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=ic())!==a&&(i=fc())!==a&&(s=Ra())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;r||(r=[]);for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(Dc),columnList:Pc(Bc),ast:n}}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function qa(){var r,e,n;return r=wa,function(){var r,e,n,o;r=wa,"union"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(nn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a?((e=bs())===a&&(e=vs()),e===a&&(e=null),e!==a?(La=r,r=(n=e)?"union "+n.toLowerCase():"union"):(wa=r,r=a)):(wa=r,r=a),r}function Ma(){var t,r,e,n,o,u,i,s;if(t=wa,(r=uu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=qa())!==a&&(i=fc())!==a&&(s=uu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=qa())!==a&&(i=fc())!==a&&(s=uu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a&&(n=fc())!==a?((o=Uu())===a&&(o=null),o!==a&&(u=fc())!==a?((i=ku())===a&&(i=null),i!==a?(La=t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&(t._limit=n),{tableList:Array.from(Dc),columnList:Pc(Bc),ast:t}}(r,e,o,i)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)}else wa=t,t=a;return t}function Pa(){var r,e;return r=wa,"if"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(c)),e!==a&&fc()!==a&&Os()!==a&&fc()!==a&&js()!==a?(La=r,r=e="IF NOT EXISTS"):(wa=r,r=a),r}function Qa(){var r,e,n;return r=wa,"if"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(l)),e!==a&&fc()!==a?("exists"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(f)),n!==a?(La=r,r=e="if exists"):(wa=r,r=a)):(wa=r,r=a),r}function Da(){var r,e,n,o,u,i;return r=wa,(e=Xi())===a&&(e=Zi()),e!==a&&(La=r,e={keyword:e[0].toLowerCase()}),(r=e)===a&&(r=wa,(e=Hi())!==a&&fc()!==a?(n=wa,"of"===t.substr(wa,2).toLowerCase()?(o=t.substr(wa,2),wa+=2):(o=a,0===Sa&&xa(d)),o!==a&&(u=fc())!==a&&(i=Eu())!==a?n=o=[o,u,i]:(wa=n,n=a),n===a&&(n=null),n!==a?(La=r,r=e=function(t,r){return{keyword:t[0].toLowerCase(),args:r&&{keyword:r[0],columns:r[2]}||null}}(e,n)):(wa=r,r=a)):(wa=r,r=a)),r}function Ba(){var t,r,e,n,o,u;return t=wa,(r=Gu())!==a&&fc()!==a?((e=Ga())===a&&(e=null),e!==a&&fc()!==a?((n=fs())===a&&(n=ps()),n===a&&(n=null),n!==a?(La=t,o=r,u=n,t=r={collate:e,...o,order_by:u&&u.toLowerCase()}):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=function(){var t,r,e;t=wa,(r=ii())!==a&&fc()!==a?((e=fs())===a&&(e=ps()),e===a&&(e=null),e!==a?(La=t,r=function(t,r){return{...t,order_by:r&&r.toLowerCase()}}(r,e),t=r):(wa=t,t=a)):(wa=t,t=a);return t}()),t}function Fa(){var r;return(r=Ka())===a&&(r=Ha())===a&&(r=Ja())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=function(){var r,e,n,o;r=wa,"fulltext"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Bo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="FULLTEXT"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=wa,"spatial"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Fo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SPATIAL"):(wa=r,r=a)):(wa=r,r=a);return r}());e!==a&&fc()!==a?((n=Js())===a&&(n=Ks()),n===a&&(n=null),n!==a&&fc()!==a?((o=di())===a&&(o=null),o!==a&&fc()!==a&&(u=cu())!==a&&fc()!==a?((i=mu())===a&&(i=null),i!==a&&fc()!==a?(La=r,s=e,l=i,e={index:o,definition:u,keyword:(c=n)&&`${s.toLowerCase()} ${c.toLowerCase()}`||s.toLowerCase(),index_options:l,resource:"index"},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var s,c,l;return r}()),r}function $a(){var r,e,n,o,u;return r=wa,(e=function(){var r,e;r=wa,(e=function(){var r,e,n,o;r=wa,"not null"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(ge));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={type:"not null",value:"not null"});return r=e}())===a&&(e=Ui()),e!==a&&(La=r,(u=e)&&!u.value&&(u.value="null"),e={nullable:u}),(r=e)===a&&(r=wa,(e=function(){var t,r;t=wa,Di()!==a&&fc()!==a&&(r=Gu())!==a?(La=t,t={type:"default",value:r}):(wa=t,t=a);return t}())!==a&&(La=r,e={default_val:e}),(r=e)===a&&(r=wa,"auto_increment"===t.substr(wa,14).toLowerCase()?(e=t.substr(wa,14),wa+=14):(e=a,0===Sa&&xa(E)),e===a&&("autoincrement"===t.substr(wa,13).toLowerCase()?(e=t.substr(wa,13),wa+=13):(e=a,0===Sa&&xa(U))),e!==a&&(La=r,e={auto_increment:e.toLowerCase()}),(r=e)===a&&(r=wa,"unique"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(_)),e!==a&&fc()!==a?("key"===t.substr(wa,3).toLowerCase()?(n=t.substr(wa,3),wa+=3):(n=a,0===Sa&&xa(x)),n===a&&(n=null),n!==a?(La=r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"primary"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(k)),e===a&&(e=null),e!==a&&fc()!==a?("key"===t.substr(wa,3).toLowerCase()?(n=t.substr(wa,3),wa+=3):(n=a,0===Sa&&xa(x)),n!==a?(La=r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=vc())!==a&&(La=r,e={comment:e}),(r=e)===a&&(r=wa,(e=rc())!==a&&fc()!==a&&(n=ci())!==a?(La=r,r=e=function(t,r){return{constraint:{keyword:t.toLowerCase(),constraint:r}}}(e,n)):(wa=r,r=a),r===a&&(r=wa,(e=Ga())!==a&&(La=r,e={collate:e}),(r=e)===a&&(r=wa,(e=function(){var r,e,n;r=wa,"column_format"===t.substr(wa,13).toLowerCase()?(e=t.substr(wa,13),wa+=13):(e=a,0===Sa&&xa(I));e!==a&&fc()!==a?("fixed"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(N)),n===a&&("dynamic"===t.substr(wa,7).toLowerCase()?(n=t.substr(wa,7),wa+=7):(n=a,0===Sa&&xa(R)),n===a&&("default"===t.substr(wa,7).toLowerCase()?(n=t.substr(wa,7),wa+=7):(n=a,0===Sa&&xa(V)))),n!==a?(La=r,e={type:"column_format",value:n.toLowerCase()},r=e):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={column_format:e}),(r=e)===a&&(r=wa,(e=function(){var r,e,n;r=wa,"storage"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(q));e!==a&&fc()!==a?("disk"===t.substr(wa,4).toLowerCase()?(n=t.substr(wa,4),wa+=4):(n=a,0===Sa&&xa(M)),n===a&&("memory"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(P))),n!==a?(La=r,e={type:"storage",value:n.toLowerCase()},r=e):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={storage:e}),(r=e)===a&&(r=wa,(e=tu())!==a&&(La=r,e={reference_definition:e}),(r=e)===a&&(r=wa,(e=function(){var r,e,n,o,u,i,s,c;r=wa,(e=za())===a&&(e=null);e!==a&&fc()!==a?("check"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(S)),n!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(o=Au())!==a&&fc()!==a&&uc()!==a&&fc()!==a?(u=wa,(i=Os())===a&&(i=null),i!==a&&(s=fc())!==a?("enforced"===t.substr(wa,8).toLowerCase()?(c=t.substr(wa,8),wa+=8):(c=a,0===Sa&&xa(rt)),c!==a?u=i=[i,s,c]:(wa=u,u=a)):(wa=u,u=a),u===a&&(u=null),u!==a?(La=r,e=function(t,r,e,n){const o=[];return n&&o.push(n[0],n[2]),{constraint_type:r.toLowerCase(),keyword:t&&t.keyword,constraint:t&&t.constraint,definition:[e],enforced:o.filter(t=>t).join(" ").toLowerCase(),resource:"constraint"}}(e,n,o,u),r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={check:e}),(r=e)===a&&(r=wa,(e=eu())!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a&&(o=ci())!==a?(La=r,r=e=function(t,r,e){return{character_set:{type:t,value:e,symbol:r}}}(e,n,o)):(wa=r,r=a)):(wa=r,r=a))))))))))))),r}function Ha(){var t,r,e,n,o,u,i;return t=wa,(r=yi())!==a&&fc()!==a?((e=Uc())===a&&(e=null),e!==a&&fc()!==a?((n=function(){var t,r,e,n,o,u;if(t=wa,(r=$a())!==a)if(fc()!==a){for(e=[],n=wa,(o=fc())!==a&&(u=$a())!==a?n=o=[o,u]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=$a())!==a?n=o=[o,u]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(wa=t,t=a)}else wa=t,t=a;else wa=t,t=a;return t}())===a&&(n=null),n!==a?(La=t,o=r,u=e,i=n,Bc.add(`create::${o.table}::${o.value||o}`),t=r={column:{type:"column_ref",table:null,column:o},definition:u,resource:"column",...i||{}}):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a),t}function Ga(){var r,e,n;return r=wa,function(){var r,e,n,o;r=wa,"collate"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(bt));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="COLLATE"):(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a?((e=Ys())===a&&(e=null),e!==a&&fc()!==a&&(n=li())!==a?(La=r,r={type:"collate",keyword:"collate",collate:{name:n,symbol:e}}):(wa=r,r=a)):(wa=r,r=a),r}function Wa(){var r,e,n;return r=wa,"first"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Q)),e!==a&&(La=r,e={keyword:e}),(r=e)===a&&(r=wa,"after"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(D)),e!==a&&fc()!==a&&(n=ii())!==a?(La=r,r=e=function(t,r){return{keyword:t,expr:r}}(e,n)):(wa=r,r=a)),r}function Ya(){var r,e,n;return(r=function(){var t,r;t=wa,Zs()!==a&&fc()!==a&&(r=Ka())!==a?(La=t,t={action:"add",create_definitions:r,resource:"constraint",type:"alter"}):(wa=t,t=a);return t}())===a&&(r=function(){var r,e,n,o;r=wa,(e=$i())!==a&&fc()!==a?("check"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(S)),n!==a&&fc()!==a&&(o=ji())!==a?(La=r,e={action:"drop",constraint:o,keyword:n.toLowerCase(),resource:"constraint",type:"alter"},r=e):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=$i())!==a&&fc()!==a?("primary"===t.substr(wa,7).toLowerCase()?(n=t.substr(wa,7),wa+=7):(n=a,0===Sa&&xa(k)),n!==a&&(o=fc())!==a&&(u=Ks())!==a?(La=r,r=e={action:"drop",key:"",keyword:"primary key",resource:"key",type:"alter"}):(wa=r,r=a)):(wa=r,r=a);r===a&&(r=wa,(e=$i())!==a&&fc()!==a?(n=wa,"foreign"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(B)),o===a&&(o=null),o!==a&&(u=fc())!==a&&(i=Ks())!==a?n=o=[o,u,i]:(wa=n,n=a),n===a&&(n=Js()),n!==a&&(o=fc())!==a&&(u=li())!==a?(La=r,e=function(t,r){const e=Array.isArray(t)?"key":"index";return{action:"drop",[e]:r,keyword:Array.isArray(t)?""+[t[0],t[2]].filter(t=>t).join(" ").toLowerCase():t.toLowerCase(),resource:e,type:"alter"}}(n,u),r=e):(wa=r,r=a)):(wa=r,r=a));return r}())===a&&(r=function(){var t,r,e,n;t=wa,(r=Zs())!==a&&fc()!==a?((e=Xs())===a&&(e=null),e!==a&&fc()!==a&&(n=Ha())!==a?(La=t,o=e,u=n,r={action:"add",...u,keyword:o,resource:"column",type:"alter"},t=r):(wa=t,t=a)):(wa=t,t=a);var o,u;return t}())===a&&(r=function(){var t,r,e;t=wa,$i()!==a&&fc()!==a?((r=Xs())===a&&(r=null),r!==a&&fc()!==a&&(e=ii())!==a?(La=t,t={action:"drop",column:e,keyword:r,resource:"column",type:"alter"}):(wa=t,t=a)):(wa=t,t=a);return t}())===a&&(r=function(){var r,e,n,o,u;r=wa,(e=function(){var r,e,n,o;r=wa,"modify"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Do));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MODIFY"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a?((n=Xs())===a&&(n=null),n!==a&&fc()!==a&&(o=Ha())!==a&&fc()!==a?((u=Wa())===a&&(u=null),u!==a?(La=r,i=o,s=u,e={action:"modify",keyword:n,...i,suffix:s,resource:"column",type:"alter"},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var i,s;return r}())===a&&(r=function(){var t,r,e;t=wa,(r=Zs())!==a&&fc()!==a&&(e=Ja())!==a?(La=t,n=e,r={action:"add",type:"alter",...n},t=r):(wa=t,t=a);var n;return t}())===a&&(r=function(){var t,r,e,n,o;t=wa,(r=Ji())!==a&&fc()!==a&&Xs()!==a&&fc()!==a&&(e=ii())!==a&&fc()!==a?((n=Bi())===a&&(n=rs()),n===a&&(n=null),n!==a&&fc()!==a&&(o=ii())!==a?(La=t,i=o,r={action:"rename",type:"alter",resource:"column",keyword:"column",old_column:e,prefix:(u=n)&&u[0].toLowerCase(),column:i},t=r):(wa=t,t=a)):(wa=t,t=a);var u,i;return t}())===a&&(r=function(){var t,r,e,n;t=wa,(r=Ji())!==a&&fc()!==a?((e=Bi())===a&&(e=rs()),e===a&&(e=null),e!==a&&fc()!==a&&(n=li())!==a?(La=t,u=n,r={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},t=r):(wa=t,t=a)):(wa=t,t=a);var o,u;return t}())===a&&(r=Za())===a&&(r=Xa())===a&&(r=function(){var r,e,n,o,u,i;r=wa,"change"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(F));e!==a&&fc()!==a?((n=Xs())===a&&(n=null),n!==a&&fc()!==a&&(o=ii())!==a&&fc()!==a&&(u=Ha())!==a&&fc()!==a?((i=Wa())===a&&(i=null),i!==a?(La=r,s=n,c=u,l=i,e={action:"change",old_column:o,...c,keyword:s,resource:"column",type:"alter",suffix:l},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var s,c,l;return r}())===a&&(r=wa,(e=ou())!==a&&(La=r,(n=e).resource=n.keyword,n[n.keyword]=n.value,delete n.value,e={type:"alter",...n}),r=e),r}function Za(){var r,e,n,o;return r=wa,"algorithm"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa($)),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a?("default"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(V)),o===a&&("instant"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(H)),o===a&&("inplace"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(G)),o===a&&("copy"===t.substr(wa,4).toLowerCase()?(o=t.substr(wa,4),wa+=4):(o=a,0===Sa&&xa(W))))),o!==a?(La=r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r}function Xa(){var r,e,n,o;return r=wa,"lock"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Y)),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a?("default"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(V)),o===a&&("none"===t.substr(wa,4).toLowerCase()?(o=t.substr(wa,4),wa+=4):(o=a,0===Sa&&xa(Z)),o===a&&("shared"===t.substr(wa,6).toLowerCase()?(o=t.substr(wa,6),wa+=6):(o=a,0===Sa&&xa(X)),o===a&&("exclusive"===t.substr(wa,9).toLowerCase()?(o=t.substr(wa,9),wa+=9):(o=a,0===Sa&&xa(J))))),o!==a?(La=r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r}function Ja(){var t,r,e,n,o,u,i,s;return t=wa,(r=Js())===a&&(r=Ks()),r!==a&&fc()!==a?((e=di())===a&&(e=null),e!==a&&fc()!==a?((n=du())===a&&(n=null),n!==a&&fc()!==a&&(o=cu())!==a&&fc()!==a?((u=mu())===a&&(u=null),u!==a&&fc()!==a?(La=t,i=n,s=u,t=r={index:e,definition:o,keyword:r.toLowerCase(),index_type:i,resource:"index",index_options:s}):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a),t}function Ka(){var r;return(r=function(){var r,e,n,o,u,i,s,c;r=wa,(e=za())===a&&(e=null);e!==a&&fc()!==a?(n=wa,"primary"===t.substr(wa,7).toLowerCase()?(o=t.substr(wa,7),wa+=7):(o=a,0===Sa&&xa(k)),o!==a&&(u=fc())!==a?("key"===t.substr(wa,3).toLowerCase()?(i=t.substr(wa,3),wa+=3):(i=a,0===Sa&&xa(x)),i!==a?n=o=[o,u,i]:(wa=n,n=a)):(wa=n,n=a),n!==a&&(o=fc())!==a?((u=du())===a&&(u=null),u!==a&&(i=fc())!==a&&(s=cu())!==a&&fc()!==a?((c=mu())===a&&(c=null),c!==a?(La=r,f=n,p=u,b=s,v=c,e={constraint:(l=e)&&l.constraint,definition:b,constraint_type:`${f[0].toLowerCase()} ${f[2].toLowerCase()}`,keyword:l&&l.keyword,index_type:p,resource:"constraint",index_options:v},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var l,f,p,b,v;return r}())===a&&(r=function(){var t,r,e,n,o,u,i,s;t=wa,(r=za())===a&&(r=null);r!==a&&fc()!==a&&(e=zs())!==a&&fc()!==a?((n=Js())===a&&(n=Ks()),n===a&&(n=null),n!==a&&fc()!==a?((o=di())===a&&(o=null),o!==a&&fc()!==a?((u=du())===a&&(u=null),u!==a&&fc()!==a&&(i=cu())!==a&&fc()!==a?((s=mu())===a&&(s=null),s!==a?(La=t,l=e,f=n,p=o,b=u,v=i,h=s,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:h},t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a);var c,l,f,p,b,v,h;return t}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=za())===a&&(e=null);e!==a&&fc()!==a?("foreign key"===t.substr(wa,11).toLowerCase()?(n=t.substr(wa,11),wa+=11):(n=a,0===Sa&&xa(tt)),n!==a&&fc()!==a?((o=di())===a&&(o=null),o!==a&&fc()!==a&&(u=cu())!==a&&fc()!==a?((i=tu())===a&&(i=null),i!==a?(La=r,c=n,l=o,f=u,p=i,e={constraint:(s=e)&&s.constraint,definition:f,constraint_type:c,keyword:s&&s.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var s,c,l,f,p;return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f;r=wa,(e=za())===a&&(e=null);e!==a&&fc()!==a?("check"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(S)),n!==a&&fc()!==a?(o=wa,"not"===t.substr(wa,3).toLowerCase()?(u=t.substr(wa,3),wa+=3):(u=a,0===Sa&&xa(K)),u!==a&&(i=fc())!==a?("for"===t.substr(wa,3).toLowerCase()?(s=t.substr(wa,3),wa+=3):(s=a,0===Sa&&xa(w)),s!==a&&(c=fc())!==a?("replication"===t.substr(wa,11).toLowerCase()?(l=t.substr(wa,11),wa+=11):(l=a,0===Sa&&xa(z)),l!==a&&(f=fc())!==a?o=u=[u,i,s,c,l,f]:(wa=o,o=a)):(wa=o,o=a)):(wa=o,o=a),o===a&&(o=null),o!==a&&(u=ac())!==a&&(i=fc())!==a&&(s=Au())!==a&&(c=fc())!==a&&(l=uc())!==a?(La=r,p=e,b=o,v=s,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:b&&{keyword:"not for replication"},definition:[v],resource:"constraint"},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var p,b,v;return r}()),r}function za(){var t,r,e,n;return t=wa,(r=rc())!==a&&fc()!==a?((e=li())===a&&(e=null),e!==a?(La=t,n=e,t=r={keyword:r.toLowerCase(),constraint:n}):(wa=t,t=a)):(wa=t,t=a),t}function tu(){var r,e,n,o,u,i,s,c,l,f;return r=wa,(e=function(){var r,e,n,o;r=wa,"references"===t.substr(wa,10).toLowerCase()?(e=t.substr(wa,10),wa+=10):(e=a,0===Sa&&xa(Go));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="REFERENCES"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&(n=gu())!==a&&fc()!==a&&(o=cu())!==a&&fc()!==a?("match full"===t.substr(wa,10).toLowerCase()?(u=t.substr(wa,10),wa+=10):(u=a,0===Sa&&xa(et)),u===a&&("match partial"===t.substr(wa,13).toLowerCase()?(u=t.substr(wa,13),wa+=13):(u=a,0===Sa&&xa(nt)),u===a&&("match simple"===t.substr(wa,12).toLowerCase()?(u=t.substr(wa,12),wa+=12):(u=a,0===Sa&&xa(ot)))),u===a&&(u=null),u!==a&&fc()!==a?((i=ru())===a&&(i=null),i!==a&&fc()!==a?((s=ru())===a&&(s=null),s!==a?(La=r,c=u,l=i,f=s,r=e={definition:o,table:[n],keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=ru())!==a&&(La=r,e={on_action:[e]}),r=e),r}function ru(){var r,e,n,o;return r=wa,as()!==a&&fc()!==a?((e=Zi())===a&&(e=Hi()),e!==a&&fc()!==a&&(n=function(){var r,e,n;r=wa,(e=$s())!==a&&fc()!==a&&ac()!==a&&fc()!==a?((n=Qu())===a&&(n=null),n!==a&&fc()!==a&&uc()!==a?(La=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},args:n}):(wa=r,r=a)):(wa=r,r=a);r===a&&(r=wa,"restrict"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(at)),e===a&&("cascade"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(ut)),e===a&&("set null"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(it)),e===a&&("no action"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(st)),e===a&&("set default"===t.substr(wa,11).toLowerCase()?(e=t.substr(wa,11),wa+=11):(e=a,0===Sa&&xa(ct)),e===a&&(e=$s()))))),e!==a&&(La=r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==a?(La=r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(wa=r,r=a)):(wa=r,r=a),r}function eu(){var r,e,n;return r=wa,"character"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(lt)),e!==a&&fc()!==a?("set"===t.substr(wa,3).toLowerCase()?(n=t.substr(wa,3),wa+=3):(n=a,0===Sa&&xa(ft)),n!==a?(La=r,r=e="CHARACTER SET"):(wa=r,r=a)):(wa=r,r=a),r}function nu(){var r,e,n,o,u,i,s,c,l;return r=wa,(e=Di())===a&&(e=null),e!==a&&fc()!==a?((n=eu())===a&&("charset"===t.substr(wa,7).toLowerCase()?(n=t.substr(wa,7),wa+=7):(n=a,0===Sa&&xa(pt)),n===a&&("collate"===t.substr(wa,7).toLowerCase()?(n=t.substr(wa,7),wa+=7):(n=a,0===Sa&&xa(bt)))),n!==a&&fc()!==a?((o=Ys())===a&&(o=null),o!==a&&fc()!==a&&(u=ci())!==a?(La=r,s=n,c=o,l=u,r=e={keyword:(i=e)&&`${i[0].toLowerCase()} ${s.toLowerCase()}`||s.toLowerCase(),symbol:c,value:l}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r}function ou(){var r,e,n,o,u,i,s,c,l;return r=wa,"auto_increment"===t.substr(wa,14).toLowerCase()?(e=t.substr(wa,14),wa+=14):(e=a,0===Sa&&xa(E)),e===a&&("avg_row_length"===t.substr(wa,14).toLowerCase()?(e=t.substr(wa,14),wa+=14):(e=a,0===Sa&&xa(vt)),e===a&&("key_block_size"===t.substr(wa,14).toLowerCase()?(e=t.substr(wa,14),wa+=14):(e=a,0===Sa&&xa(ht)),e===a&&("max_rows"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(yt)),e===a&&("min_rows"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(dt)),e===a&&("stats_sample_pages"===t.substr(wa,18).toLowerCase()?(e=t.substr(wa,18),wa+=18):(e=a,0===Sa&&xa(mt))))))),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a&&(o=Ni())!==a?(La=r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=nu())===a&&(r=wa,(e=tc())===a&&("connection"===t.substr(wa,10).toLowerCase()?(e=t.substr(wa,10),wa+=10):(e=a,0===Sa&&xa(jt))),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a&&(o=_i())!==a?(La=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"compression"===t.substr(wa,11).toLowerCase()?(e=t.substr(wa,11),wa+=11):(e=a,0===Sa&&xa(Ot)),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a?(o=wa,39===t.charCodeAt(wa)?(u="'",wa++):(u=a,0===Sa&&xa(wt)),u!==a?("zlib"===t.substr(wa,4).toLowerCase()?(i=t.substr(wa,4),wa+=4):(i=a,0===Sa&&xa(Lt)),i===a&&("lz4"===t.substr(wa,3).toLowerCase()?(i=t.substr(wa,3),wa+=3):(i=a,0===Sa&&xa(Ct)),i===a&&("none"===t.substr(wa,4).toLowerCase()?(i=t.substr(wa,4),wa+=4):(i=a,0===Sa&&xa(Z)))),i!==a?(39===t.charCodeAt(wa)?(s="'",wa++):(s=a,0===Sa&&xa(wt)),s!==a?o=u=[u,i,s]:(wa=o,o=a)):(wa=o,o=a)):(wa=o,o=a),o!==a?(La=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"engine"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(gt)),e!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a&&(o=ji())!==a?(La=r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}}(e,n,o)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"without"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(At)),e!==a&&fc()!==a?("rowid"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(St)),n!==a?(La=r,r=e={keyword:"without rowid"}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"strict"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Tt)),e!==a&&(La=r,e={keyword:"strict"}),r=e))))),r}function au(){var r,e,n,o,u;return r=wa,(e=Lu())!==a&&fc()!==a&&(n=function(){var r,e,n;return r=wa,"read"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Et)),e!==a&&fc()!==a?("local"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(A)),n===a&&(n=null),n!==a?(La=r,r=e={type:"read",suffix:n&&"local"}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"low_priority"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(Ut)),e===a&&(e=null),e!==a&&fc()!==a?("write"===t.substr(wa,5).toLowerCase()?(n=t.substr(wa,5),wa+=5):(n=a,0===Sa&&xa(_t)),n!==a?(La=r,r=e={type:"write",prefix:e&&"low_priority"}):(wa=r,r=a)):(wa=r,r=a)),r}())!==a?(La=r,o=e,u=n,Dc.add(`lock::${o.db}::${o.table}`),r=e={table:o,lock_type:u}):(wa=r,r=a),r}function uu(){var r,e,n,o,u,i,s;return(r=lu())===a&&(r=wa,e=wa,40===t.charCodeAt(wa)?(n="(",wa++):(n=a,0===Sa&&xa(Pt)),n!==a&&(o=fc())!==a&&(u=uu())!==a&&(i=fc())!==a?(41===t.charCodeAt(wa)?(s=")",wa++):(s=a,0===Sa&&xa(Qt)),s!==a?e=n=[n,o,u,i,s]:(wa=e,e=a)):(wa=e,e=a),e!==a&&(La=r,e={...e[2],parentheses_symbol:!0}),r=e),r}function iu(){var r,e,n,o,u,i,s,c,l;if(r=wa,cs()!==a)if(fc()!==a)if((e=su())!==a){for(n=[],o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=su())!==a?o=u=[u,i,s,c]:(wa=o,o=a);o!==a;)n.push(o),o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=su())!==a?o=u=[u,i,s,c]:(wa=o,o=a);n!==a?(La=r,r=T(e,n)):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;return r===a&&(r=wa,fc()!==a&&cs()!==a&&(e=fc())!==a&&(n=function(){var r,e,n,o;r=wa,"recursive"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(Pe));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(o=fc())!==a&&(u=su())!==a?(La=r,(l=u).recursive=!0,r=[l]):(wa=r,r=a)),r}function su(){var t,r,e,n,o,u,i;return t=wa,(r=_i())===a&&(r=ji())===a&&(r=gu()),r!==a&&fc()!==a?((e=cu())===a&&(e=null),e!==a&&fc()!==a&&rs()!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=Ma())!==a&&fc()!==a&&uc()!==a?(La=t,u=e,i=n,"string"==typeof(o=r)&&(o={type:"default",value:o}),o.table&&(o={type:"default",value:o.table}),t=r={name:o,stmt:i,columns:u}):(wa=t,t=a)):(wa=t,t=a),t}function cu(){var t,r;return t=wa,ac()!==a&&fc()!==a&&(r=function(){var t;(t=Eu())===a&&(t=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Ei())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ei())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Ei())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=T(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}());return t}())!==a&&fc()!==a&&uc()!==a?(La=t,t=r):(wa=t,t=a),t}function lu(){var r,e,n,o,u,i,s,c,l,f,p,b,v,h,y,d,m,j,O,L,C,g,A,S,T,E;return r=wa,fc()!==a?((e=iu())===a&&(e=null),e!==a&&fc()!==a&&function(){var r,e,n,o;r=wa,"select"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(ke));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&pc()!==a?((n=function(){var t,r,e,n,o,u;if(t=wa,(r=fu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=fu())!==a?n=o=[o,u]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=fu())!==a?n=o=[o,u]:(wa=n,n=a);e!==a?(La=t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())===a&&(n=null),n!==a&&fc()!==a?((o=vs())===a&&(o=null),o!==a&&fc()!==a&&(u=pu())!==a&&fc()!==a?((i=hu())===a&&(i=null),i!==a&&fc()!==a?((s=Tu())===a&&(s=null),s!==a&&fc()!==a?((c=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;r=wa,"group"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(sn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&ls()!==a&&fc()!==a&&(n=Qu())!==a?(La=r,e={columns:n.value},r=e):(wa=r,r=a);return r}())===a&&(c=null),c!==a&&fc()!==a?((l=function(){var r,e;r=wa,function(){var r,e,n,o;r=wa,"having"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(fn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=Wu())!==a?(La=r,r=e):(wa=r,r=a);return r}())===a&&(l=null),l!==a&&fc()!==a?((f=Uu())===a&&(f=null),f!==a&&fc()!==a?((p=ku())===a&&(p=null),p!==a?(b=wa,"for"===t.substr(wa,3).toLowerCase()?(v=t.substr(wa,3),wa+=3):(v=a,0===Sa&&xa(w)),v!==a&&(h=fc())!==a&&(y=Hi())!==a?b=v=[v,h,y]:(wa=b,b=a),b===a&&(b=null),b!==a?(La=r,d=e,m=n,j=o,O=u,C=s,g=c,A=l,S=f,T=p,E=b,(L=i)&&L.forEach(t=>t.table&&Dc.add(`select::${t.db}::${t.table}`)),r={with:d,type:"select",options:m,distinct:j,columns:O,from:L,where:C,groupby:g,having:A,orderby:S,limit:T,for_update:E&&`${E[0]} ${E[2][0]}`}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r}function fu(){var r,e;return r=wa,(e=function(){var r;"sql_calc_found_rows"===t.substr(wa,19).toLowerCase()?(r=t.substr(wa,19),wa+=19):(r=a,0===Sa&&xa(Wo));return r}())===a&&((e=function(){var r;"sql_cache"===t.substr(wa,9).toLowerCase()?(r=t.substr(wa,9),wa+=9):(r=a,0===Sa&&xa(Yo));return r}())===a&&(e=function(){var r;"sql_no_cache"===t.substr(wa,12).toLowerCase()?(r=t.substr(wa,12),wa+=12):(r=a,0===Sa&&xa(Zo));return r}()),e===a&&(e=function(){var r;"sql_big_result"===t.substr(wa,14).toLowerCase()?(r=t.substr(wa,14),wa+=14):(r=a,0===Sa&&xa(Jo));return r}())===a&&(e=function(){var r;"sql_small_result"===t.substr(wa,16).toLowerCase()?(r=t.substr(wa,16),wa+=16):(r=a,0===Sa&&xa(Xo));return r}())===a&&(e=function(){var r;"sql_buffer_result"===t.substr(wa,17).toLowerCase()?(r=t.substr(wa,17),wa+=17):(r=a,0===Sa&&xa(Ko));return r}())),e!==a&&(La=r,e=e),r=e}function pu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=bs())===a&&(r=wa,(e=oc())!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r===a&&(r=oc())),r!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){Bc.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?Vc(e,r):[e]}(0,e)):(wa=t,t=a)}else wa=t,t=a;if(t===a)if(t=wa,(r=bu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=bu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=T(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function bu(){var t,r,e,n,o;return t=wa,r=wa,(e=li())!==a&&(n=fc())!==a&&(o=ec())!==a?r=e=[e,n,o]:(wa=r,r=a),r===a&&(r=null),r!==a&&(e=fc())!==a&&(n=oc())!==a?(La=t,t=r=function(t){const r=t&&t[0]||null;return Bc.add(`select::${r}::(.*)`),{expr:{type:"column_ref",table:r,column:"*"},as:null}}(r)):(wa=t,t=a),t===a&&(t=wa,(r=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Gu())!==a){for(e=[],n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls())===a&&(u=lc()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls())===a&&(u=lc()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,r=function(t,r){const e=t.ast;if(e&&"select"===e.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!r||0===r.length)return t;const n=r.length;let o=r[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?t:r[e-1][3];o=Nc(r[e][1],n,o)}return o}(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a&&(e=fc())!==a?((n=vu())===a&&(n=null),n!==a?(La=t,t=r={expr:r,as:n}):(wa=t,t=a)):(wa=t,t=a)),t}function vu(){var t,r,e;return t=wa,(r=rs())!==a&&pc()!==a&&(e=function(){var t,r;t=wa,(r=ji())!==a?(La=wa,(function(t){if(!0===xc[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(r)?a:void 0)!==a?(La=t,t=r=r):(wa=t,t=a)):(wa=t,t=a);t===a&&(t=wa,(r=pi())!==a&&(La=t,r=r),t=r);return t}())!==a?(La=t,t=r=e):(wa=t,t=a),t===a&&(t=wa,(r=rs())===a&&(r=null),r!==a&&fc()!==a&&(e=li())!==a?(La=t,t=r=e):(wa=t,t=a)),t}function hu(){var r,e;return r=wa,function(){var r,e,n,o;r=wa,"from"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(He));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=Ou())!==a?(La=r,r=e):(wa=r,r=a),r}function yu(){var t,r,e;return t=wa,(r=gu())!==a&&fc()!==a&&Bi()!==a&&fc()!==a&&(e=gu())!==a?(La=t,t=r=[r,e]):(wa=t,t=a),t}function du(){var r,e;return r=wa,ss()!==a&&fc()!==a?("btree"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Dt)),e===a&&("hash"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Bt))),e!==a?(La=r,r={keyword:"using",type:e.toLowerCase()}):(wa=r,r=a)):(wa=r,r=a),r}function mu(){var t,r,e,n,o,u;if(t=wa,(r=ju())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=ju())!==a?n=o=[o,u]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=ju())!==a?n=o=[o,u]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function ju(){var r,e,n,o,u,i;return r=wa,(e=function(){var r,e,n,o;r=wa,"key_block_size"===t.substr(wa,14).toLowerCase()?(e=t.substr(wa,14),wa+=14):(e=a,0===Sa&&xa(ht));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="KEY_BLOCK_SIZE"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a?((n=Ys())===a&&(n=null),n!==a&&fc()!==a&&(o=Ni())!==a?(La=r,u=n,i=o,r=e={type:e.toLowerCase(),symbol:u,expr:i}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=du())===a&&(r=wa,"with"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ft)),e!==a&&fc()!==a?("parser"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa($t)),n!==a&&fc()!==a&&(o=ji())!==a?(La=r,r=e={type:"with parser",expr:o}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,"visible"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Ht)),e===a&&("invisible"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(Gt))),e!==a&&(La=r,e=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(e)),(r=e)===a&&(r=vc()))),r}function Ou(){var t,r,e,n;if(t=wa,(r=Lu())!==a){for(e=[],n=wu();n!==a;)e.push(n),n=wu();e!==a?(La=t,t=r=Wt(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function wu(){var t,r,e;return t=wa,fc()!==a&&(r=nc())!==a&&fc()!==a&&(e=Lu())!==a?(La=t,t=e):(wa=t,t=a),t===a&&(t=wa,fc()!==a&&(r=function(){var t,r,e,n,o,u,i,s,c,l,f;if(t=wa,(r=Cu())!==a)if(fc()!==a)if((e=Lu())!==a)if(fc()!==a)if((n=ss())!==a)if(fc()!==a)if(ac()!==a)if(fc()!==a)if((o=ci())!==a){for(u=[],i=wa,(s=fc())!==a&&(c=nc())!==a&&(l=fc())!==a&&(f=ci())!==a?i=s=[s,c,l,f]:(wa=i,i=a);i!==a;)u.push(i),i=wa,(s=fc())!==a&&(c=nc())!==a&&(l=fc())!==a&&(f=ci())!==a?i=s=[s,c,l,f]:(wa=i,i=a);u!==a&&(i=fc())!==a&&(s=uc())!==a?(La=t,p=r,v=o,h=u,(b=e).join=p,b.using=Vc(v,h),t=r=b):(wa=t,t=a)}else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;var p,b,v,h;t===a&&(t=wa,(r=Cu())!==a&&fc()!==a&&(e=Lu())!==a&&fc()!==a?((n=Su())===a&&(n=null),n!==a?(La=t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=Cu())===a&&(r=qa()),r!==a&&fc()!==a&&(e=ac())!==a&&fc()!==a&&(n=Ma())!==a&&fc()!==a&&uc()!==a&&fc()!==a?((o=vu())===a&&(o=null),o!==a&&(u=fc())!==a?((i=Su())===a&&(i=null),i!==a?(La=t,r=function(t,r,e,n){return r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,i),t=r):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)));return t}())!==a?(La=t,t=r):(wa=t,t=a)),t}function Lu(){var r,e,n,o,u,i;return r=wa,(e=function(){var r;"dual"===t.substr(wa,4).toLowerCase()?(r=t.substr(wa,4),wa+=4):(r=a,0===Sa&&xa(qo));return r}())!==a&&(La=r,e={type:"dual"}),(r=e)===a&&(r=wa,(e=ji())!==a&&fc()!==a&&(n=ac())!==a&&fc()!==a&&(o=Qu())!==a&&fc()!==a&&(u=uc())!==a&&fc()!==a?((i=vu())===a&&(i=null),i!==a?(La=r,r=e={expr:{type:"function",name:{name:[{type:"default",value:e}]},args:o},as:i}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=gu())!==a&&fc()!==a?((n=vu())===a&&(n=null),n!==a?(La=r,r=e=function(t,r){return"var"===t.type?(t.as=r,t):{db:t.db,table:t.table,as:r}}(e,n)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=ac())!==a&&fc()!==a&&(n=Ma())!==a&&fc()!==a&&(o=uc())!==a&&fc()!==a?((u=vu())===a&&(u=null),u!==a?(La=r,r=e=function(t,r){return t.parentheses=!0,{expr:t,as:r}}(n,u)):(wa=r,r=a)):(wa=r,r=a)))),r}function Cu(){var r,e,n,o;return r=wa,(e=function(){var r,e,n,o;r=wa,"left"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ke));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(n=fc())!==a?((o=function(){var r,e,n,o;r=wa,"outer"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(rn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(o=null),o!==a&&fc()!==a&&us()!==a?(La=r,r=e="LEFT JOIN"):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,e=wa,(n=function(){var r,e,n,o;r=wa,"inner"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(ze));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(o=fc())!==a?e=n=[n,o]:(wa=e,e=a),e===a&&(e=null),e!==a&&(n=us())!==a?(La=r,r=e="INNER JOIN"):(wa=r,r=a)),r}function gu(){var t,r,e,n,o,u,i,s;return t=wa,(r=li())!==a?(e=wa,(n=fc())!==a&&(o=ec())!==a&&(u=fc())!==a&&(i=li())!==a?e=n=[n,o,u,i]:(wa=e,e=a),e===a&&(e=null),e!==a?(La=t,t=r=function(t,r){const e={db:null,table:t};return null!==r&&(e.db=t,e.table=r[3]),e}(r,e)):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=Tc())!==a&&(La=t,(s=r).db=null,s.table=s.name,r=s),t=r),t}function Au(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Gu())!==a){for(e=[],n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=Nc(r[t][1],n,r[t][3]);return n}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Su(){var t,r;return t=wa,as()!==a&&fc()!==a&&(r=Wu())!==a?(La=t,t=r):(wa=t,t=a),t}function Tu(){var r,e;return r=wa,function(){var r,e,n,o;r=wa,"where"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(un));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=Wu())!==a?(La=r,r=e):(wa=r,r=a),r}function Eu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=ii())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=T(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Uu(){var r,e;return r=wa,function(){var r,e,n,o;r=wa,"order"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(ln));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&ls()!==a&&fc()!==a&&(e=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=_u())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=_u())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=_u())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=T(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=r,r=e):(wa=r,r=a),r}function _u(){var t,r,e;return t=wa,(r=Gu())!==a&&fc()!==a?((e=ps())===a&&(e=fs()),e===a&&(e=null),e!==a?(La=t,t=r={expr:r,type:e}):(wa=t,t=a)):(wa=t,t=a),t}function xu(){var t;return(t=Ni())===a&&(t=Ci()),t}function ku(){var r,e,n,o,u,i;return r=wa,function(){var r,e,n,o;r=wa,"limit"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(pn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=xu())!==a&&fc()!==a?(n=wa,(o=nc())===a&&(o=function(){var r,e,n,o;r=wa,"offset"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(bn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="OFFSET"):(wa=r,r=a)):(wa=r,r=a);return r}()),o!==a&&(u=fc())!==a&&(i=xu())!==a?n=o=[o,u,i]:(wa=n,n=a),n===a&&(n=null),n!==a?(La=r,r=function(t,r){const e=[t];return r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,n)):(wa=r,r=a)):(wa=r,r=a),r}function Iu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Nu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Nu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Nu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=T(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Nu(){var r,e,n,o,u,i,s,c,l;return r=wa,e=wa,(n=li())!==a&&(o=fc())!==a&&(u=ec())!==a?e=n=[n,o,u]:(wa=e,e=a),e===a&&(e=null),e!==a&&(n=fc())!==a&&(o=yi())!==a&&(u=fc())!==a?(61===t.charCodeAt(wa)?(i="=",wa++):(i=a,0===Sa&&xa(Yt)),i!==a&&fc()!==a&&(s=ri())!==a?(La=r,r=e={column:o,value:s,table:(l=e)&&l[0]}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,e=wa,(n=li())!==a&&(o=fc())!==a&&(u=ec())!==a?e=n=[n,o,u]:(wa=e,e=a),e===a&&(e=null),e!==a&&(n=fc())!==a&&(o=yi())!==a&&(u=fc())!==a?(61===t.charCodeAt(wa)?(i="=",wa++):(i=a,0===Sa&&xa(Yt)),i!==a&&fc()!==a&&(s=is())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(c=ii())!==a&&fc()!==a&&uc()!==a?(La=r,r=e=function(t,r,e){return{column:r,value:e,table:t&&t[0],keyword:"values"}}(e,o,c)):(wa=r,r=a)):(wa=r,r=a)),r}function Ru(){var t;return(t=function(){var t,r;t=wa,is()!==a&&fc()!==a&&(r=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Pu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Pu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Pu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=T(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())!==a?(La=t,t=r):(wa=t,t=a);return t}())===a&&(t=lu()),t}function Vu(){var t,r,e,n,o,u,i,s,c;if(t=wa,Ki()!==a)if(fc()!==a)if((r=ac())!==a)if(fc()!==a)if((e=ji())!==a){for(n=[],o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=ji())!==a?o=u=[u,i,s,c]:(wa=o,o=a);o!==a;)n.push(o),o=wa,(u=fc())!==a&&(i=nc())!==a&&(s=fc())!==a&&(c=ji())!==a?o=u=[u,i,s,c]:(wa=o,o=a);n!==a&&(o=fc())!==a&&(u=uc())!==a?(La=t,t=Vc(e,n)):(wa=t,t=a)}else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;else wa=t,t=a;return t===a&&(t=wa,Ki()!==a&&fc()!==a&&(r=Pu())!==a?(La=t,t=r):(wa=t,t=a)),t}function qu(){var r,e,n;return r=wa,as()!==a&&fc()!==a?("duplicate"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(Zt)),e!==a&&fc()!==a&&Ks()!==a&&fc()!==a&&Hi()!==a&&fc()!==a&&(n=Iu())!==a?(La=r,r={keyword:"on duplicate key update",set:n}):(wa=r,r=a)):(wa=r,r=a),r}function Mu(){var r,e;return r=wa,(e=Xi())!==a&&(La=r,e="insert"),(r=e)===a&&(r=wa,(e=function(){var r,e,n,o;r=wa,"replace"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Qe));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e="replace"),r=e),r}function Pu(){var t,r;return t=wa,ac()!==a&&fc()!==a&&(r=Qu())!==a&&fc()!==a&&uc()!==a?(La=t,t=r):(wa=t,t=a),t}function Qu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Gu())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){const e={type:"expr_list"};return e.value=Vc(t,r),e}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Du(){var r,e,n;return r=wa,function(){var r,e,n,o;r=wa,"interval"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(vo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="INTERVAL"):(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=Gu())!==a&&fc()!==a&&(n=function(){var r;(r=function(){var r,e,n,o;r=wa,"year"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(ho));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="YEAR"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"month"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(yo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MONTH"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"day"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(mo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DAY"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"hour"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(jo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="HOUR"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"minute"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Oo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MINUTE"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"second"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(wo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SECOND"):(wa=r,r=a)):(wa=r,r=a);return r}());return r}())!==a?(La=r,r={type:"interval",expr:e,unit:n.toLowerCase()}):(wa=r,r=a),r}function Bu(){var t,r,e,n,o,u;if(t=wa,(r=Fu())!==a)if(fc()!==a){for(e=[],n=wa,(o=fc())!==a&&(u=Fu())!==a?n=o=[o,u]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=Fu())!==a?n=o=[o,u]:(wa=n,n=a);e!==a?(La=t,t=r=s(r,e)):(wa=t,t=a)}else wa=t,t=a;else wa=t,t=a;return t}function Fu(){var r,e,n;return r=wa,gs()!==a&&fc()!==a&&(e=Wu())!==a&&fc()!==a&&function(){var r,e,n,o;r=wa,"then"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Vn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(n=Gu())!==a?(La=r,r={type:"when",cond:e,result:n}):(wa=r,r=a),r}function $u(){var r,e;return r=wa,function(){var r,e,n,o;r=wa,"else"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(qn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&(e=Gu())!==a?(La=r,r={type:"else",result:e}):(wa=r,r=a),r}function Hu(){var t;return(t=function(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Yu())!==a){for(e=[],n=wa,(o=pc())!==a&&(u=Ls())!==a&&(i=fc())!==a&&(s=Yu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=pc())!==a&&(u=Ls())!==a&&(i=fc())!==a&&(s=Yu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,r=Xt(r,e),t=r):(wa=t,t=a)}else wa=t,t=a;return t}())===a&&(t=function(){var t,r,e,n,o,u;if(t=wa,(r=ei())!==a){if(e=[],n=wa,(o=fc())!==a&&(u=ai())!==a?n=o=[o,u]:(wa=n,n=a),n!==a)for(;n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=ai())!==a?n=o=[o,u]:(wa=n,n=a);else e=a;e!==a?(La=t,r=Ic(r,e[0][1]),t=r):(wa=t,t=a)}else wa=t,t=a;return t}()),t}function Gu(){var t;return(t=Hu())===a&&(t=Ma()),t}function Wu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Gu())!==a){for(e=[],n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls())===a&&(u=nc()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=ws())===a&&(u=Ls())===a&&(u=nc()),u!==a&&(i=fc())!==a&&(s=Gu())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=Nc(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Yu(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Zu())!==a){for(e=[],n=wa,(o=pc())!==a&&(u=ws())!==a&&(i=fc())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=pc())!==a&&(u=ws())!==a&&(i=fc())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=Xt(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Zu(){var r,e,n,o,u;return(r=Xu())===a&&(r=function(){var t,r,e;t=wa,(r=function(){var t,r,e,n,o;t=wa,r=wa,(e=Os())!==a&&(n=fc())!==a&&(o=js())!==a?r=e=[e,n,o]:(wa=r,r=a);r!==a&&(La=t,r=Kt(r));(t=r)===a&&(t=js());return t}())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(e=Ma())!==a&&fc()!==a&&uc()!==a?(La=t,n=r,(o=e).parentheses=!0,r=Ic(n,o),t=r):(wa=t,t=a);var n,o;return t}())===a&&(r=wa,(e=Os())===a&&(e=wa,33===t.charCodeAt(wa)?(n="!",wa++):(n=a,0===Sa&&xa(Jt)),n!==a?(o=wa,Sa++,61===t.charCodeAt(wa)?(u="=",wa++):(u=a,0===Sa&&xa(Yt)),Sa--,u===a?o=void 0:(wa=o,o=a),o!==a?e=n=[n,o]:(wa=e,e=a)):(wa=e,e=a)),e!==a&&(n=fc())!==a&&(o=Zu())!==a?(La=r,r=e=Ic("NOT",o)):(wa=r,r=a)),r}function Xu(){var r,e,n,o,u;return r=wa,(e=ri())!==a&&fc()!==a?((n=function(){var r;(r=function(){var t,r,e,n,o,u,i;t=wa,r=[],e=wa,(n=fc())!==a&&(o=Ju())!==a&&(u=fc())!==a&&(i=ri())!==a?e=n=[n,o,u,i]:(wa=e,e=a);if(e!==a)for(;e!==a;)r.push(e),e=wa,(n=fc())!==a&&(o=Ju())!==a&&(u=fc())!==a&&(i=ri())!==a?e=n=[n,o,u,i]:(wa=e,e=a);else r=a;r!==a&&(La=t,r={type:"arithmetic",tail:r});return t=r}())===a&&(r=ti())===a&&(r=function(){var t,r,e,n;t=wa,(r=function(){var t,r,e,n,o;t=wa,r=wa,(e=Os())!==a&&(n=fc())!==a&&(o=hs())!==a?r=e=[e,n,o]:(wa=r,r=a);r!==a&&(La=t,r=Kt(r));(t=r)===a&&(t=hs());return t}())!==a&&fc()!==a&&(e=ri())!==a&&fc()!==a&&ws()!==a&&fc()!==a&&(n=ri())!==a?(La=t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(wa=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o;t=wa,(r=ds())!==a&&(e=fc())!==a&&(n=ri())!==a?(La=t,t=r={op:"IS",right:n}):(wa=t,t=a);t===a&&(t=wa,r=wa,(e=ds())!==a&&(n=fc())!==a&&(o=Os())!==a?r=e=[e,n,o]:(wa=r,r=a),r!==a&&(e=fc())!==a&&(n=ri())!==a?(La=t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(wa=t,t=a));return t}())===a&&(r=zu())===a&&(r=function(){var r,e,n,o;r=wa,(e=function(){var r,e,n;r=wa,(e=Os())===a&&(e=null);e!==a&&fc()!==a?((n=function(){var r,e,n,o;r=wa,"regexp"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(gn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="REGEXP"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=wa,"rlike"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Cn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="RLIKE"):(wa=r,r=a)):(wa=r,r=a);return r}()),n!==a?(La=r,u=n,r=e=(o=e)?`${o} ${u}`:u):(wa=r,r=a)):(wa=r,r=a);var o,u;return r}())!==a&&fc()!==a?("binary"===t.substr(wa,6).toLowerCase()?(n=t.substr(wa,6),wa+=6):(n=a,0===Sa&&xa(kt)),n===a&&(n=null),n!==a&&fc()!==a?((o=Si())===a&&(o=_i())===a&&(o=ii()),o!==a?(La=r,u=e,r=e={op:(i=n)?`${u} ${i}`:u,right:o}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);var u,i;r===a&&(r=wa,"glob"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(ur)),e!==a&&fc()!==a&&(n=_i())!==a?(La=r,e=function(t){return{op:"GLOB",right:t}}(n),r=e):(wa=r,r=a));return r}());return r}())===a&&(n=null),n!==a?(La=r,o=e,r=e=null===(u=n)?o:"arithmetic"===u.type?qc(o,u.tail):Nc(u.op,o,u.right)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=_i())===a&&(r=ii()),r}function Ju(){var r;return">="===t.substr(wa,2)?(r=">=",wa+=2):(r=a,0===Sa&&xa(zt)),r===a&&(62===t.charCodeAt(wa)?(r=">",wa++):(r=a,0===Sa&&xa(tr)),r===a&&("<="===t.substr(wa,2)?(r="<=",wa+=2):(r=a,0===Sa&&xa(rr)),r===a&&("<>"===t.substr(wa,2)?(r="<>",wa+=2):(r=a,0===Sa&&xa(er)),r===a&&(60===t.charCodeAt(wa)?(r="<",wa++):(r=a,0===Sa&&xa(nr)),r===a&&("=="===t.substr(wa,2)?(r="==",wa+=2):(r=a,0===Sa&&xa(or)),r===a&&(61===t.charCodeAt(wa)?(r="=",wa++):(r=a,0===Sa&&xa(Yt)),r===a&&("!="===t.substr(wa,2)?(r="!=",wa+=2):(r=a,0===Sa&&xa(ar))))))))),r}function Ku(){var t,r,e,n,o;return t=wa,r=wa,(e=Os())!==a&&(n=fc())!==a&&(o=ys())!==a?r=e=[e,n,o]:(wa=r,r=a),r!==a&&(La=t,r=Kt(r)),(t=r)===a&&(t=ys()),t}function zu(){var t,r,e;return t=wa,(r=function(){var t,r,e,n,o;return t=wa,r=wa,(e=Os())!==a&&(n=fc())!==a&&(o=ms())!==a?r=e=[e,n,o]:(wa=r,r=a),r!==a&&(La=t,r=Kt(r)),(t=r)===a&&(t=ms()),t}())!==a&&fc()!==a?((e=Ei())===a&&(e=Xu()),e!==a?(La=t,t=r={op:r,right:e}):(wa=t,t=a)):(wa=t,t=a),t}function ti(){var t,r,e,n;return t=wa,(r=Ku())!==a&&fc()!==a&&(e=ac())!==a&&fc()!==a&&(n=Qu())!==a&&fc()!==a&&uc()!==a?(La=t,t=r={op:r,right:n}):(wa=t,t=a),t===a&&(t=wa,(r=Ku())!==a&&fc()!==a?((e=Tc())===a&&(e=_i())===a&&(e=Si()),e!==a?(La=t,t=r=function(t,r){return{op:t,right:r}}(r,e)):(wa=t,t=a)):(wa=t,t=a)),t}function ri(){var t,r,e,n,o,u,i,s;if(t=wa,(r=ni())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=ei())!==a&&(i=fc())!==a&&(s=ni())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=ei())!==a&&(i=fc())!==a&&(s=ni())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=function(t,r){if(r&&r.length&&"column_ref"===t.type&&"*"===t.column)throw new Error(JSON.stringify({message:"args could not be star column in additive expr",...kc()}));return qc(t,r)}(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function ei(){var r;return 43===t.charCodeAt(wa)?(r="+",wa++):(r=a,0===Sa&&xa(ir)),r===a&&(45===t.charCodeAt(wa)?(r="-",wa++):(r=a,0===Sa&&xa(sr))),r}function ni(){var t,r,e,n,o,u,i,s;if(t=wa,(r=ui())!==a){for(e=[],n=wa,(o=fc())!==a?((u=oi())===a&&(u=lc()),u!==a&&(i=fc())!==a&&(s=ui())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a?((u=oi())===a&&(u=lc()),u!==a&&(i=fc())!==a&&(s=ui())!==a?n=o=[o,u,i,s]:(wa=n,n=a)):(wa=n,n=a);e!==a?(La=t,t=r=qc(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function oi(){var r;return 42===t.charCodeAt(wa)?(r="*",wa++):(r=a,0===Sa&&xa(cr)),r===a&&(47===t.charCodeAt(wa)?(r="/",wa++):(r=a,0===Sa&&xa(lr)),r===a&&(37===t.charCodeAt(wa)?(r="%",wa++):(r=a,0===Sa&&xa(fr)),r===a&&("||"===t.substr(wa,2)?(r="||",wa+=2):(r=a,0===Sa&&xa(pr))))),r}function ai(){var r,e,n,o;return(r=function(){var r,e,n,o,u,i,s;r=wa,(e=Ss())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=Gu())!==a&&fc()!==a&&rs()!==a&&fc()!==a&&(o=Uc())!==a&&fc()!==a&&(u=uc())!==a?(La=r,c=n,l=o,e={type:"cast",keyword:e.toLowerCase(),expr:c,symbol:"as",target:[l]},r=e):(wa=r,r=a);var c,l;r===a&&(r=wa,(e=Ss())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=Gu())!==a&&fc()!==a&&rs()!==a&&fc()!==a&&(o=_s())!==a&&fc()!==a&&(u=ac())!==a&&fc()!==a&&(i=Ri())!==a&&fc()!==a&&uc()!==a&&fc()!==a&&(s=uc())!==a?(La=r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+")"}]}}(e,n,i),r=e):(wa=r,r=a),r===a&&(r=wa,(e=Ss())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=Gu())!==a&&fc()!==a&&rs()!==a&&fc()!==a&&(o=_s())!==a&&fc()!==a&&(u=ac())!==a&&fc()!==a&&(i=Ri())!==a&&fc()!==a&&nc()!==a&&fc()!==a&&(s=Ri())!==a&&fc()!==a&&uc()!==a&&fc()!==a&&uc()!==a?(La=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:"DECIMAL("+e+", "+n+")"}]}}(e,n,i,s),r=e):(wa=r,r=a),r===a&&(r=wa,(e=Ss())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=Gu())!==a&&fc()!==a&&rs()!==a&&fc()!==a&&(o=function(){var r;(r=function(){var r,e,n,o;r=wa,"signed"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa($n));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SIGNED"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=xs());return r}())!==a&&fc()!==a?((u=Is())===a&&(u=null),u!==a&&fc()!==a&&(i=uc())!==a?(La=r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:[{dataType:e+(n?" "+n:"")}]}}(e,n,o,u),r=e):(wa=r,r=a)):(wa=r,r=a))));return r}())===a&&(r=Ei())===a&&(r=function(){var r;(r=function(){var r,e,n,o;r=wa,(e=function(){var r,e,n,o;r=wa,"count"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(En));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="COUNT"):(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=function(){var r,e,n,o,u,i,s,c,l,f;r=wa,(e=function(){var r,e;r=wa,42===t.charCodeAt(wa)?(e="*",wa++):(e=a,0===Sa&&xa(cr));e!==a&&(La=r,e={type:"star",value:"*"});return r=e}())!==a&&(La=r,e={expr:e});if((r=e)===a){if(r=wa,(e=vs())===a&&(e=null),e!==a)if(fc()!==a)if((n=ac())!==a)if(fc()!==a)if((o=Gu())!==a)if(fc()!==a)if(uc()!==a){for(u=[],i=wa,(s=fc())!==a?((c=ws())===a&&(c=Ls()),c!==a&&(l=fc())!==a&&(f=Gu())!==a?i=s=[s,c,l,f]:(wa=i,i=a)):(wa=i,i=a);i!==a;)u.push(i),i=wa,(s=fc())!==a?((c=ws())===a&&(c=Ls()),c!==a&&(l=fc())!==a&&(f=Gu())!==a?i=s=[s,c,l,f]:(wa=i,i=a)):(wa=i,i=a);u!==a&&(i=fc())!==a?((s=Uu())===a&&(s=null),s!==a?(La=r,e=function(t,r,e,n){const o=e.length;let a=r;a.parentheses=!0;for(let t=0;t<o;++t)a=Nc(e[t][1],a,e[t][3]);return{distinct:t,expr:a,orderby:n}}(e,o,u,s),r=e):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;r===a&&(r=wa,(e=vs())===a&&(e=null),e!==a&&fc()!==a&&(n=Au())!==a&&fc()!==a?((o=Uu())===a&&(o=null),o!==a?(La=r,r=e={distinct:e,expr:n,orderby:o}):(wa=r,r=a)):(wa=r,r=a))}return r}())!==a&&fc()!==a&&uc()!==a&&fc()!==a?((o=Ai())===a&&(o=null),o!==a?(La=r,r=e={type:"aggr_func",name:e,args:n,over:o}):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,(e=function(){var r;(r=function(){var r,e,n,o;r=wa,"sum"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(xn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SUM"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"max"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(Un));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MAX"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"min"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(_n));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MIN"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"avg"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(kn));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="AVG"):(wa=r,r=a)):(wa=r,r=a);return r}());return r}())!==a&&fc()!==a&&ac()!==a&&fc()!==a&&(n=ri())!==a&&fc()!==a&&uc()!==a&&fc()!==a?((o=Ai())===a&&(o=null),o!==a?(La=r,e={type:"aggr_func",name:e,args:{expr:n},over:o,...kc()},r=e):(wa=r,r=a)):(wa=r,r=a);return r}());return r}())===a&&(r=Si())===a&&(r=function(){var t,r,e,n,o,u,i,s;return t=wa,Cs()!==a&&fc()!==a&&(r=Bu())!==a&&fc()!==a?((e=$u())===a&&(e=null),e!==a&&fc()!==a&&(n=As())!==a&&fc()!==a?((o=Cs())===a&&(o=null),o!==a?(La=t,i=r,(s=e)&&i.push(s),t={type:"case",expr:null,args:i}):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,Cs()!==a&&fc()!==a&&(r=Gu())!==a&&fc()!==a&&(e=Bu())!==a&&fc()!==a?((n=$u())===a&&(n=null),n!==a&&fc()!==a&&(o=As())!==a&&fc()!==a?((u=Cs())===a&&(u=null),u!==a?(La=t,t=function(t,r,e){return e&&r.push(e),{type:"case",expr:t,args:r}}(r,e,n)):(wa=t,t=a)):(wa=t,t=a)):(wa=t,t=a)),t}())===a&&(r=Du())===a&&(r=ii())===a&&(r=Ci())===a&&(r=wa,ac()!==a&&(e=fc())!==a&&(n=Wu())!==a&&fc()!==a&&uc()!==a?(La=r,(o=n).parentheses=!0,r=o):(wa=r,r=a),r===a&&(r=Tc())===a&&(r=wa,fc()!==a?(63===t.charCodeAt(wa)?(e="?",wa++):(e=a,0===Sa&&xa(br)),e!==a?(La=r,r={type:"origin",value:e}):(wa=r,r=a)):(wa=r,r=a))),r}function ui(){var r,e,n,o,u;return(r=function(){var r,e,n,o,u,i,s,c;if(r=wa,(e=ai())!==a)if(fc()!==a){for(n=[],o=wa,(u=fc())!==a?("?|"===t.substr(wa,2)?(i="?|",wa+=2):(i=a,0===Sa&&xa(hr)),i===a&&("?&"===t.substr(wa,2)?(i="?&",wa+=2):(i=a,0===Sa&&xa(yr)),i===a&&(63===t.charCodeAt(wa)?(i="?",wa++):(i=a,0===Sa&&xa(br)),i===a&&("#-"===t.substr(wa,2)?(i="#-",wa+=2):(i=a,0===Sa&&xa(dr)),i===a&&("#>>"===t.substr(wa,3)?(i="#>>",wa+=3):(i=a,0===Sa&&xa(mr)),i===a&&("#>"===t.substr(wa,2)?(i="#>",wa+=2):(i=a,0===Sa&&xa(jr)),i===a&&(i=cc())===a&&(i=sc())===a&&("@>"===t.substr(wa,2)?(i="@>",wa+=2):(i=a,0===Sa&&xa(Or)),i===a&&("<@"===t.substr(wa,2)?(i="<@",wa+=2):(i=a,0===Sa&&xa(wr))))))))),i!==a&&(s=fc())!==a&&(c=ai())!==a?o=u=[u,i,s,c]:(wa=o,o=a)):(wa=o,o=a);o!==a;)n.push(o),o=wa,(u=fc())!==a?("?|"===t.substr(wa,2)?(i="?|",wa+=2):(i=a,0===Sa&&xa(hr)),i===a&&("?&"===t.substr(wa,2)?(i="?&",wa+=2):(i=a,0===Sa&&xa(yr)),i===a&&(63===t.charCodeAt(wa)?(i="?",wa++):(i=a,0===Sa&&xa(br)),i===a&&("#-"===t.substr(wa,2)?(i="#-",wa+=2):(i=a,0===Sa&&xa(dr)),i===a&&("#>>"===t.substr(wa,3)?(i="#>>",wa+=3):(i=a,0===Sa&&xa(mr)),i===a&&("#>"===t.substr(wa,2)?(i="#>",wa+=2):(i=a,0===Sa&&xa(jr)),i===a&&(i=cc())===a&&(i=sc())===a&&("@>"===t.substr(wa,2)?(i="@>",wa+=2):(i=a,0===Sa&&xa(Or)),i===a&&("<@"===t.substr(wa,2)?(i="<@",wa+=2):(i=a,0===Sa&&xa(wr))))))))),i!==a&&(s=fc())!==a&&(c=ai())!==a?o=u=[u,i,s,c]:(wa=o,o=a)):(wa=o,o=a);n!==a?(La=r,l=e,e=(f=n)&&0!==f.length?qc(l,f):l,r=e):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;var l,f;return r}())===a&&(r=wa,(e=function(){var r;33===t.charCodeAt(wa)?(r="!",wa++):(r=a,0===Sa&&xa(Jt));r===a&&(45===t.charCodeAt(wa)?(r="-",wa++):(r=a,0===Sa&&xa(sr)),r===a&&(43===t.charCodeAt(wa)?(r="+",wa++):(r=a,0===Sa&&xa(ir)),r===a&&(126===t.charCodeAt(wa)?(r="~",wa++):(r=a,0===Sa&&xa(vr)))));return r}())!==a?(n=wa,(o=fc())!==a&&(u=ui())!==a?n=o=[o,u]:(wa=n,n=a),n!==a?(La=r,r=e=Ic(e,n[1])):(wa=r,r=a)):(wa=r,r=a)),r}function ii(){var t,r,e,n,o,u,i,s,c,l,f,p;return t=wa,(r=li())!==a&&(e=fc())!==a&&(n=ec())!==a&&(o=fc())!==a&&(u=yi())!==a?(i=wa,(s=fc())!==a&&(c=Ga())!==a?i=s=[s,c]:(wa=i,i=a),i===a&&(i=null),i!==a?(La=t,l=r,f=u,p=i,Bc.add(`select::${l}::${f}`),t=r={type:"column_ref",table:l,column:f,collate:p&&p[1]}):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=di())!==a?(e=wa,(n=fc())!==a&&(o=Ga())!==a?e=n=[n,o]:(wa=e,e=a),e===a&&(e=null),e!==a?(La=t,t=r=function(t,r){return Bc.add("select::null::"+t),{type:"column_ref",table:null,column:t,collate:r&&r[1]}}(r,e)):(wa=t,t=a)):(wa=t,t=a)),t}function si(){var t,r,e,n,o,u,i,s;if(t=wa,(r=di())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=di())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=di())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=T(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function ci(){var t,r;return t=wa,(r=ji())!==a&&(La=t,r={type:"default",value:r}),(t=r)===a&&(t=fi()),t}function li(){var t,r;return t=wa,(r=ji())!==a?(La=wa,(Lr(r)?a:void 0)!==a?(La=t,t=r=r):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=pi())!==a&&(La=t,r=r),t=r),t}function fi(){var t;return(t=bi())===a&&(t=vi())===a&&(t=hi()),t}function pi(){var t,r;return t=wa,(r=bi())===a&&(r=vi())===a&&(r=hi()),r!==a&&(La=t,r=r.value),t=r}function bi(){var r,e,n,o;if(r=wa,34===t.charCodeAt(wa)?(e='"',wa++):(e=a,0===Sa&&xa(Cr)),e!==a){if(n=[],gr.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Ar)),o!==a)for(;o!==a;)n.push(o),gr.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Ar));else n=a;n!==a?(34===t.charCodeAt(wa)?(o='"',wa++):(o=a,0===Sa&&xa(Cr)),o!==a?(La=r,r=e={type:"double_quote_string",value:n.join("")}):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;return r}function vi(){var r,e,n,o;if(r=wa,39===t.charCodeAt(wa)?(e="'",wa++):(e=a,0===Sa&&xa(wt)),e!==a){if(n=[],Sr.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Tr)),o!==a)for(;o!==a;)n.push(o),Sr.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Tr));else n=a;n!==a?(39===t.charCodeAt(wa)?(o="'",wa++):(o=a,0===Sa&&xa(wt)),o!==a?(La=r,r=e={type:"single_quote_string",value:n.join("")}):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;return r}function hi(){var r,e,n,o;if(r=wa,96===t.charCodeAt(wa)?(e="`",wa++):(e=a,0===Sa&&xa(Er)),e!==a){if(n=[],Ur.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(_r)),o!==a)for(;o!==a;)n.push(o),Ur.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(_r));else n=a;n!==a?(96===t.charCodeAt(wa)?(o="`",wa++):(o=a,0===Sa&&xa(Er)),o!==a?(La=r,r=e={type:"backticks_quote_string",value:n.join("")}):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;return r}function yi(){var t,r;return t=wa,(r=mi())!==a&&(La=t,r=r),(t=r)===a&&(t=pi()),t}function di(){var t,r;return t=wa,(r=mi())!==a?(La=wa,(Lr(r)?a:void 0)!==a?(La=t,t=r=r):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=pi()),t}function mi(){var t,r,e,n;if(t=wa,(r=Oi())!==a){for(e=[],n=Li();n!==a;)e.push(n),n=Li();e!==a?(La=t,t=r=xr(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function ji(){var t,r,e,n;if(t=wa,(r=Oi())!==a){for(e=[],n=wi();n!==a;)e.push(n),n=wi();e!==a?(La=t,t=r=xr(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Oi(){var r;return kr.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(Ir)),r}function wi(){var r;return Nr.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(Rr)),r}function Li(){var r;return Vr.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(qr)),r}function Ci(){var r,e,n,o;return r=wa,e=wa,58===t.charCodeAt(wa)?(n=":",wa++):(n=a,0===Sa&&xa(Mr)),n!==a&&(o=ji())!==a?e=n=[n,o]:(wa=e,e=a),e!==a&&(La=r,e={type:"param",value:e[1]}),r=e}function gi(){var t,r,e;return t=wa,as()!==a&&fc()!==a&&Hi()!==a&&fc()!==a&&(r=$s())!==a&&fc()!==a&&ac()!==a&&fc()!==a?((e=Qu())===a&&(e=null),e!==a&&fc()!==a&&uc()!==a?(La=t,t={type:"on update",keyword:r,parentheses:!0,expr:e}):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,as()!==a&&fc()!==a&&Hi()!==a&&fc()!==a&&(r=$s())!==a?(La=t,t=function(t){return{type:"on update",keyword:t}}(r)):(wa=t,t=a)),t}function Ai(){var r,e,n;return r=wa,function(){var r,e,n,o;r=wa,"over"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(en));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}()!==a&&fc()!==a&&ac()!==a&&fc()!==a&&Ki()!==a&&fc()!==a&&ls()!==a&&fc()!==a&&(e=pu())!==a&&fc()!==a?((n=Uu())===a&&(n=null),n!==a&&fc()!==a&&uc()!==a?(La=r,r={partitionby:e,orderby:n}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=gi()),r}function Si(){var r,e,n,o,u;return r=wa,(e=function(){var r;(r=Ti())===a&&(r=function(){var r,e,n,o;r=wa,"current_user"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(go));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CURRENT_USER"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"user"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(po));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="USER"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"session_user"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(Ao));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SESSION_USER"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"system_user"===t.substr(wa,11).toLowerCase()?(e=t.substr(wa,11),wa+=11):(e=a,0===Sa&&xa(So));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SYSTEM_USER"):(wa=r,r=a)):(wa=r,r=a);return r}());return r}())!==a&&fc()!==a&&(n=ac())!==a&&fc()!==a?((o=Qu())===a&&(o=null),o!==a&&fc()!==a&&uc()!==a&&fc()!==a?((u=Ai())===a&&(u=null),u!==a?(La=r,r=e={type:"function",name:{name:[{type:"default",value:e}]},args:o||{type:"expr_list",value:[]},over:u,...kc()}):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=Ti())!==a&&fc()!==a?((n=gi())===a&&(n=null),n!==a?(La=r,r=e={type:"function",name:{name:[{type:"origin",value:e}]},over:n,...kc()}):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=wa,(e=gc())!==a&&fc()!==a&&(n=ac())!==a&&fc()!==a?((o=Wu())===a&&(o=null),o!==a&&fc()!==a&&uc()!==a&&fc()!==a?((u=Ai())===a&&(u=null),u!==a?(La=r,r=e=function(t,r,e){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},over:e,...kc()}}(e,o,u)):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a))),r}function Ti(){var r;return(r=function(){var r,e,n,o;r=wa,"current_date"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(bo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CURRENT_DATE"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=wa,"current_time"===t.substr(wa,12).toLowerCase()?(e=t.substr(wa,12),wa+=12):(e=a,0===Sa&&xa(Lo));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CURRENT_TIME"):(wa=r,r=a)):(wa=r,r=a);return r}())===a&&(r=$s()),r}function Ei(){var r,e,n,o,u,i,s,c,l;return r=wa,"binary"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(kt)),e===a&&(e=null),e!==a&&fc()!==a&&(n=_i())!==a?(o=wa,(u=fc())!==a&&(i=Ga())!==a?o=u=[u,i]:(wa=o,o=a),o===a&&(o=null),o!==a?(La=r,c=n,l=o,(s=e)&&(c.prefix=s.toLowerCase()),l&&(c.suffix={collate:l[1]}),r=e=c):(wa=r,r=a)):(wa=r,r=a),r===a&&(r=Ni())===a&&(r=function(){var r,e;r=wa,(e=function(){var r,e,n,o;r=wa,"true"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ae));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={type:"bool",value:!0});(r=e)===a&&(r=wa,(e=function(){var r,e,n,o;r=wa,"false"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Te));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={type:"bool",value:!1}),r=e);return r}())===a&&(r=Ui())===a&&(r=function(){var r,e,n,o,u,i;r=wa,(e=Bs())===a&&(e=Qs())===a&&(e=Fs())===a&&(e=Ds());if(e!==a)if(fc()!==a){if(n=wa,39===t.charCodeAt(wa)?(o="'",wa++):(o=a,0===Sa&&xa(wt)),o!==a){for(u=[],i=ki();i!==a;)u.push(i),i=ki();u!==a?(39===t.charCodeAt(wa)?(i="'",wa++):(i=a,0===Sa&&xa(wt)),i!==a?n=o=[o,u,i]:(wa=n,n=a)):(wa=n,n=a)}else wa=n,n=a;n!==a?(La=r,e=Hr(e,n),r=e):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;if(r===a)if(r=wa,(e=Bs())===a&&(e=Qs())===a&&(e=Fs())===a&&(e=Ds()),e!==a)if(fc()!==a){if(n=wa,34===t.charCodeAt(wa)?(o='"',wa++):(o=a,0===Sa&&xa(Cr)),o!==a){for(u=[],i=xi();i!==a;)u.push(i),i=xi();u!==a?(34===t.charCodeAt(wa)?(i='"',wa++):(i=a,0===Sa&&xa(Cr)),i!==a?n=o=[o,u,i]:(wa=n,n=a)):(wa=n,n=a)}else wa=n,n=a;n!==a?(La=r,e=Hr(e,n),r=e):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;return r}()),r}function Ui(){var r,e;return r=wa,(e=function(){var r,e,n,o;r=wa,"null"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ce));e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a);return r}())!==a&&(La=r,e={type:"null",value:null}),r=e}function _i(){var r,e,n,o,u,i,s,c;if(r=wa,"_binary"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Pr)),e===a&&(e=null),e!==a)if((n=fc())!==a)if("x"===t.substr(wa,1).toLowerCase()?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Qr)),o!==a){if(u=wa,39===t.charCodeAt(wa)?(i="'",wa++):(i=a,0===Sa&&xa(wt)),i!==a){for(s=[],Dr.test(t.charAt(wa))?(c=t.charAt(wa),wa++):(c=a,0===Sa&&xa(Br));c!==a;)s.push(c),Dr.test(t.charAt(wa))?(c=t.charAt(wa),wa++):(c=a,0===Sa&&xa(Br));s!==a?(39===t.charCodeAt(wa)?(c="'",wa++):(c=a,0===Sa&&xa(wt)),c!==a?u=i=[i,s,c]:(wa=u,u=a)):(wa=u,u=a)}else wa=u,u=a;u!==a?(La=r,r=e={type:"hex_string",prefix:e,value:u[1].join("")}):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;if(r===a){if(r=wa,"_binary"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Pr)),e===a&&(e=null),e!==a)if((n=fc())!==a)if("b"===t.substr(wa,1).toLowerCase()?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(Fr)),o!==a){if(u=wa,39===t.charCodeAt(wa)?(i="'",wa++):(i=a,0===Sa&&xa(wt)),i!==a){for(s=[],Dr.test(t.charAt(wa))?(c=t.charAt(wa),wa++):(c=a,0===Sa&&xa(Br));c!==a;)s.push(c),Dr.test(t.charAt(wa))?(c=t.charAt(wa),wa++):(c=a,0===Sa&&xa(Br));s!==a?(39===t.charCodeAt(wa)?(c="'",wa++):(c=a,0===Sa&&xa(wt)),c!==a?u=i=[i,s,c]:(wa=u,u=a)):(wa=u,u=a)}else wa=u,u=a;u!==a?(La=r,r=e=function(t,r,e){return{type:"bit_string",prefix:t,value:e[1].join("")}}(e,0,u)):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;if(r===a){if(r=wa,"_binary"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Pr)),e===a&&(e=null),e!==a)if((n=fc())!==a)if("0x"===t.substr(wa,2)?(o="0x",wa+=2):(o=a,0===Sa&&xa($r)),o!==a){for(u=[],Dr.test(t.charAt(wa))?(i=t.charAt(wa),wa++):(i=a,0===Sa&&xa(Br));i!==a;)u.push(i),Dr.test(t.charAt(wa))?(i=t.charAt(wa),wa++):(i=a,0===Sa&&xa(Br));u!==a?(La=r,r=e=function(t,r,e){return{type:"full_hex_string",prefix:t,value:e.join("")}}(e,0,u)):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;if(r===a){if(r=wa,e=wa,39===t.charCodeAt(wa)?(n="'",wa++):(n=a,0===Sa&&xa(wt)),n!==a){for(o=[],u=ki();u!==a;)o.push(u),u=ki();o!==a?(39===t.charCodeAt(wa)?(u="'",wa++):(u=a,0===Sa&&xa(wt)),u!==a?e=n=[n,o,u]:(wa=e,e=a)):(wa=e,e=a)}else wa=e,e=a;if(e!==a&&(La=r,e=function(t){return{type:"single_quote_string",value:t[1].join("")}}(e)),(r=e)===a){if(r=wa,e=wa,34===t.charCodeAt(wa)?(n='"',wa++):(n=a,0===Sa&&xa(Cr)),n!==a){for(o=[],u=xi();u!==a;)o.push(u),u=xi();o!==a?(34===t.charCodeAt(wa)?(u='"',wa++):(u=a,0===Sa&&xa(Cr)),u!==a?e=n=[n,o,u]:(wa=e,e=a)):(wa=e,e=a)}else wa=e,e=a;e!==a&&(La=r,e=function(t){return{type:"double_quote_string",value:t[1].join("")}}(e)),r=e}}}}return r}function xi(){var r;return Gr.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(Wr)),r===a&&(r=Ii()),r}function ki(){var r;return Yr.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(Zr)),r===a&&(r=Ii()),r}function Ii(){var r,e,n,o,u,i,s,c,l,f;return r=wa,"\\'"===t.substr(wa,2)?(e="\\'",wa+=2):(e=a,0===Sa&&xa(Xr)),e!==a&&(La=r,e="\\'"),(r=e)===a&&(r=wa,'\\"'===t.substr(wa,2)?(e='\\"',wa+=2):(e=a,0===Sa&&xa(Jr)),e!==a&&(La=r,e='\\"'),(r=e)===a&&(r=wa,"\\\\"===t.substr(wa,2)?(e="\\\\",wa+=2):(e=a,0===Sa&&xa(Kr)),e!==a&&(La=r,e="\\\\"),(r=e)===a&&(r=wa,"\\/"===t.substr(wa,2)?(e="\\/",wa+=2):(e=a,0===Sa&&xa(zr)),e!==a&&(La=r,e="\\/"),(r=e)===a&&(r=wa,"\\b"===t.substr(wa,2)?(e="\\b",wa+=2):(e=a,0===Sa&&xa(te)),e!==a&&(La=r,e="\b"),(r=e)===a&&(r=wa,"\\f"===t.substr(wa,2)?(e="\\f",wa+=2):(e=a,0===Sa&&xa(re)),e!==a&&(La=r,e="\f"),(r=e)===a&&(r=wa,"\\n"===t.substr(wa,2)?(e="\\n",wa+=2):(e=a,0===Sa&&xa(ee)),e!==a&&(La=r,e="\n"),(r=e)===a&&(r=wa,"\\r"===t.substr(wa,2)?(e="\\r",wa+=2):(e=a,0===Sa&&xa(ne)),e!==a&&(La=r,e="\r"),(r=e)===a&&(r=wa,"\\t"===t.substr(wa,2)?(e="\\t",wa+=2):(e=a,0===Sa&&xa(oe)),e!==a&&(La=r,e="\t"),(r=e)===a&&(r=wa,"\\u"===t.substr(wa,2)?(e="\\u",wa+=2):(e=a,0===Sa&&xa(ae)),e!==a&&(n=Qi())!==a&&(o=Qi())!==a&&(u=Qi())!==a&&(i=Qi())!==a?(La=r,s=n,c=o,l=u,f=i,r=e=String.fromCharCode(parseInt("0x"+s+c+l+f))):(wa=r,r=a),r===a&&(r=wa,92===t.charCodeAt(wa)?(e="\\",wa++):(e=a,0===Sa&&xa(ue)),e!==a&&(La=r,e="\\"),(r=e)===a&&(r=wa,"''"===t.substr(wa,2)?(e="''",wa+=2):(e=a,0===Sa&&xa(ie)),e!==a&&(La=r,e="''"),(r=e)===a&&(r=wa,'""'===t.substr(wa,2)?(e='""',wa+=2):(e=a,0===Sa&&xa(se)),e!==a&&(La=r,e='""'),(r=e)===a&&(r=wa,"``"===t.substr(wa,2)?(e="``",wa+=2):(e=a,0===Sa&&xa(ce)),e!==a&&(La=r,e="``"),r=e))))))))))))),r}function Ni(){var t,r,e;return t=wa,(r=function(){var t,r,e,n;t=wa,(r=Ri())!==a&&(e=Vi())!==a&&(n=qi())!==a?(La=t,t=r={type:"bigint",value:r+e+n}):(wa=t,t=a);t===a&&(t=wa,(r=Ri())!==a&&(e=Vi())!==a?(La=t,r=function(t,r){const e=t+r;return Rc(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e),t=r):(wa=t,t=a),t===a&&(t=wa,(r=Ri())!==a&&(e=qi())!==a?(La=t,r=function(t,r){return{type:"bigint",value:t+r}}(r,e),t=r):(wa=t,t=a),t===a&&(t=wa,(r=Ri())!==a&&(La=t,r=function(t){return Rc(t)?{type:"bigint",value:t}:parseFloat(t)}(r)),t=r)));return t}())!==a&&(La=t,r=(e=r)&&"bigint"===e.type?e:{type:"number",value:e}),t=r}function Ri(){var r,e,n;return(r=Mi())===a&&(r=Pi())===a&&(r=wa,45===t.charCodeAt(wa)?(e="-",wa++):(e=a,0===Sa&&xa(sr)),e===a&&(43===t.charCodeAt(wa)?(e="+",wa++):(e=a,0===Sa&&xa(ir))),e!==a&&(n=Mi())!==a?(La=r,r=e=e+n):(wa=r,r=a),r===a&&(r=wa,45===t.charCodeAt(wa)?(e="-",wa++):(e=a,0===Sa&&xa(sr)),e===a&&(43===t.charCodeAt(wa)?(e="+",wa++):(e=a,0===Sa&&xa(ir))),e!==a&&(n=Pi())!==a?(La=r,r=e=function(t,r){return t+r}(e,n)):(wa=r,r=a))),r}function Vi(){var r,e,n,o;return r=wa,46===t.charCodeAt(wa)?(e=".",wa++):(e=a,0===Sa&&xa(pe)),e!==a?((n=Mi())===a&&(n=null),n!==a?(La=r,r=e=(o=n)?"."+o:""):(wa=r,r=a)):(wa=r,r=a),r}function qi(){var r,e,n;return r=wa,(e=function(){var r,e,n;r=wa,de.test(t.charAt(wa))?(e=t.charAt(wa),wa++):(e=a,0===Sa&&xa(me));e!==a?(je.test(t.charAt(wa))?(n=t.charAt(wa),wa++):(n=a,0===Sa&&xa(Oe)),n===a&&(n=null),n!==a?(La=r,r=e=e+(null!==(o=n)?o:"")):(wa=r,r=a)):(wa=r,r=a);var o;return r}())!==a&&(n=Mi())!==a?(La=r,r=e=e+n):(wa=r,r=a),r}function Mi(){var t,r,e;if(t=wa,r=[],(e=Pi())!==a)for(;e!==a;)r.push(e),e=Pi();else r=a;return r!==a&&(La=t,r=r.join("")),t=r}function Pi(){var r;return be.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(ve)),r}function Qi(){var r;return he.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(ye)),r}function Di(){var r,e,n,o;return r=wa,"default"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(V)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Bi(){var r,e,n,o;return r=wa,"to"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(Se)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Fi(){var r,e,n,o;return r=wa,"show"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ee)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function $i(){var r,e,n,o;return r=wa,"drop"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ue)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DROP"):(wa=r,r=a)):(wa=r,r=a),r}function Hi(){var r,e,n,o;return r=wa,"update"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Ie)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Gi(){var r,e,n,o;return r=wa,"create"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Ne)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Wi(){var r,e,n,o;return r=wa,"temporary"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(Re)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Yi(){var r,e,n,o;return r=wa,"temp"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ve)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Zi(){var r,e,n,o;return r=wa,"delete"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(qe)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Xi(){var r,e,n,o;return r=wa,"insert"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Me)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Ji(){var r,e,n,o;return r=wa,"rename"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(De)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Ki(){var r,e,n,o;return r=wa,"partition"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(Fe)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="PARTITION"):(wa=r,r=a)):(wa=r,r=a),r}function zi(){var r,e,n,o;return r=wa,"into"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa($e)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function ts(){var r,e,n,o;return r=wa,"set"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(ft)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SET"):(wa=r,r=a)):(wa=r,r=a),r}function rs(){var r,e,n,o;return r=wa,"as"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(We)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function es(){var r,e,n,o;return r=wa,"table"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Ye)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TABLE"):(wa=r,r=a)):(wa=r,r=a),r}function ns(){var r,e,n,o;return r=wa,"tables"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Ze)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TABLES"):(wa=r,r=a)):(wa=r,r=a),r}function os(){var r,e,n,o;return r=wa,"database"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Xe)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DATABASE"):(wa=r,r=a)):(wa=r,r=a),r}function as(){var r,e,n,o;return r=wa,"on"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(y)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function us(){var r,e,n,o;return r=wa,"join"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(tn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function is(){var r,e,n,o;return r=wa,"values"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(on)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function ss(){var r,e,n,o;return r=wa,"using"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(an)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function cs(){var r,e,n,o;return r=wa,"with"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ft)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function ls(){var r,e,n,o;return r=wa,"by"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(cn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function fs(){var r,e,n,o;return r=wa,"asc"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(vn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ASC"):(wa=r,r=a)):(wa=r,r=a),r}function ps(){var r,e,n,o;return r=wa,"desc"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(hn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DESC"):(wa=r,r=a)):(wa=r,r=a),r}function bs(){var r,e,n,o;return r=wa,"all"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(dn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ALL"):(wa=r,r=a)):(wa=r,r=a),r}function vs(){var r,e,n,o;return r=wa,"distinct"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(mn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DISTINCT"):(wa=r,r=a)):(wa=r,r=a),r}function hs(){var r,e,n,o;return r=wa,"between"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(jn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="BETWEEN"):(wa=r,r=a)):(wa=r,r=a),r}function ys(){var r,e,n,o;return r=wa,"in"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(On)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="IN"):(wa=r,r=a)):(wa=r,r=a),r}function ds(){var r,e,n,o;return r=wa,"is"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(wn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="IS"):(wa=r,r=a)):(wa=r,r=a),r}function ms(){var r,e,n,o;return r=wa,"like"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Ln)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="LIKE"):(wa=r,r=a)):(wa=r,r=a),r}function js(){var r,e,n,o;return r=wa,"exists"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(An)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="EXISTS"):(wa=r,r=a)):(wa=r,r=a),r}function Os(){var r,e,n,o;return r=wa,"not"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(K)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="NOT"):(wa=r,r=a)):(wa=r,r=a),r}function ws(){var r,e,n,o;return r=wa,"and"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(Sn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="AND"):(wa=r,r=a)):(wa=r,r=a),r}function Ls(){var r,e,n,o;return r=wa,"or"===t.substr(wa,2).toLowerCase()?(e=t.substr(wa,2),wa+=2):(e=a,0===Sa&&xa(Tn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="OR"):(wa=r,r=a)):(wa=r,r=a),r}function Cs(){var r,e,n,o;return r=wa,"case"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Nn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function gs(){var r,e,n,o;return r=wa,"when"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Rn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function As(){var r,e,n,o;return r=wa,"end"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(O)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?r=e=[e,n]:(wa=r,r=a)):(wa=r,r=a),r}function Ss(){var r,e,n,o;return r=wa,"cast"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Mn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CAST"):(wa=r,r=a)):(wa=r,r=a),r}function Ts(){var r,e,n,o;return r=wa,"char"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Qn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CHAR"):(wa=r,r=a)):(wa=r,r=a),r}function Es(){var r,e,n,o;return r=wa,"varchar"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Dn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="VARCHAR"):(wa=r,r=a)):(wa=r,r=a),r}function Us(){var r,e,n,o;return r=wa,"numeric"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Bn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="NUMERIC"):(wa=r,r=a)):(wa=r,r=a),r}function _s(){var r,e,n,o;return r=wa,"decimal"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Fn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DECIMAL"):(wa=r,r=a)):(wa=r,r=a),r}function xs(){var r,e,n,o;return r=wa,"unsigned"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Hn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="UNSIGNED"):(wa=r,r=a)):(wa=r,r=a),r}function ks(){var r,e,n,o;return r=wa,"int"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(Gn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="INT"):(wa=r,r=a)):(wa=r,r=a),r}function Is(){var r,e,n,o;return r=wa,"integer"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Yn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="INTEGER"):(wa=r,r=a)):(wa=r,r=a),r}function Ns(){var r,e,n,o;return r=wa,"smallint"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Xn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="SMALLINT"):(wa=r,r=a)):(wa=r,r=a),r}function Rs(){var r,e,n,o;return r=wa,"tinyint"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(Jn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TINYINT"):(wa=r,r=a)):(wa=r,r=a),r}function Vs(){var r,e,n,o;return r=wa,"bigint"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(eo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="BIGINT"):(wa=r,r=a)):(wa=r,r=a),r}function qs(){var r,e,n,o;return r=wa,"float"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(oo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="FLOAT"):(wa=r,r=a)):(wa=r,r=a),r}function Ms(){var r,e,n,o;return r=wa,"double"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(ao)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DOUBLE"):(wa=r,r=a)):(wa=r,r=a),r}function Ps(){var r,e,n,o;return r=wa,"real"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(uo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="REAL"):(wa=r,r=a)):(wa=r,r=a),r}function Qs(){var r,e,n,o;return r=wa,"date"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(io)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DATE"):(wa=r,r=a)):(wa=r,r=a),r}function Ds(){var r,e,n,o;return r=wa,"datetime"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(so)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="DATETIME"):(wa=r,r=a)):(wa=r,r=a),r}function Bs(){var r,e,n,o;return r=wa,"time"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(co)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TIME"):(wa=r,r=a)):(wa=r,r=a),r}function Fs(){var r,e,n,o;return r=wa,"timestamp"===t.substr(wa,9).toLowerCase()?(e=t.substr(wa,9),wa+=9):(e=a,0===Sa&&xa(lo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TIMESTAMP"):(wa=r,r=a)):(wa=r,r=a),r}function $s(){var r,e,n,o;return r=wa,"current_timestamp"===t.substr(wa,17).toLowerCase()?(e=t.substr(wa,17),wa+=17):(e=a,0===Sa&&xa(Co)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CURRENT_TIMESTAMP"):(wa=r,r=a)):(wa=r,r=a),r}function Hs(){var r,e,n,o;return r=wa,"view"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(xo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="VIEW"):(wa=r,r=a)):(wa=r,r=a),r}function Gs(){var r;return 64===t.charCodeAt(wa)?(r="@",wa++):(r=a,0===Sa&&xa(ko)),r}function Ws(){var r;return(r=function(){var r;return"@@"===t.substr(wa,2)?(r="@@",wa+=2):(r=a,0===Sa&&xa(Io)),r}())===a&&(r=Gs())===a&&(r=function(){var r;return 36===t.charCodeAt(wa)?(r="$",wa++):(r=a,0===Sa&&xa(No)),r}()),r}function Ys(){var r;return 61===t.charCodeAt(wa)?(r="=",wa++):(r=a,0===Sa&&xa(Yt)),r}function Zs(){var r,e,n,o;return r=wa,"add"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(Mo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ADD"):(wa=r,r=a)):(wa=r,r=a),r}function Xs(){var r,e,n,o;return r=wa,"column"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(Po)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="COLUMN"):(wa=r,r=a)):(wa=r,r=a),r}function Js(){var r,e,n,o;return r=wa,"index"===t.substr(wa,5).toLowerCase()?(e=t.substr(wa,5),wa+=5):(e=a,0===Sa&&xa(Qo)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="INDEX"):(wa=r,r=a)):(wa=r,r=a),r}function Ks(){var r,e,n,o;return r=wa,"key"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(x)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="KEY"):(wa=r,r=a)):(wa=r,r=a),r}function zs(){var r,e,n,o;return r=wa,"unique"===t.substr(wa,6).toLowerCase()?(e=t.substr(wa,6),wa+=6):(e=a,0===Sa&&xa(_)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="UNIQUE"):(wa=r,r=a)):(wa=r,r=a),r}function tc(){var r,e,n,o;return r=wa,"comment"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa($o)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="COMMENT"):(wa=r,r=a)):(wa=r,r=a),r}function rc(){var r,e,n,o;return r=wa,"constraint"===t.substr(wa,10).toLowerCase()?(e=t.substr(wa,10),wa+=10):(e=a,0===Sa&&xa(Ho)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="CONSTRAINT"):(wa=r,r=a)):(wa=r,r=a),r}function ec(){var r;return 46===t.charCodeAt(wa)?(r=".",wa++):(r=a,0===Sa&&xa(pe)),r}function nc(){var r;return 44===t.charCodeAt(wa)?(r=",",wa++):(r=a,0===Sa&&xa(zo)),r}function oc(){var r;return 42===t.charCodeAt(wa)?(r="*",wa++):(r=a,0===Sa&&xa(cr)),r}function ac(){var r;return 40===t.charCodeAt(wa)?(r="(",wa++):(r=a,0===Sa&&xa(Pt)),r}function uc(){var r;return 41===t.charCodeAt(wa)?(r=")",wa++):(r=a,0===Sa&&xa(Qt)),r}function ic(){var r;return 59===t.charCodeAt(wa)?(r=";",wa++):(r=a,0===Sa&&xa(ea)),r}function sc(){var r;return"->"===t.substr(wa,2)?(r="->",wa+=2):(r=a,0===Sa&&xa(na)),r}function cc(){var r;return"->>"===t.substr(wa,3)?(r="->>",wa+=3):(r=a,0===Sa&&xa(oa)),r}function lc(){var r;return(r=function(){var r;return"||"===t.substr(wa,2)?(r="||",wa+=2):(r=a,0===Sa&&xa(pr)),r}())===a&&(r=function(){var r;return"&&"===t.substr(wa,2)?(r="&&",wa+=2):(r=a,0===Sa&&xa(aa)),r}()),r}function fc(){var t,r;for(t=[],(r=yc())===a&&(r=bc());r!==a;)t.push(r),(r=yc())===a&&(r=bc());return t}function pc(){var t,r;if(t=[],(r=yc())===a&&(r=bc()),r!==a)for(;r!==a;)t.push(r),(r=yc())===a&&(r=bc());else t=a;return t}function bc(){var r;return(r=function(){var r,e,n,o,u,i;r=wa,"/*"===t.substr(wa,2)?(e="/*",wa+=2):(e=a,0===Sa&&xa(ua));if(e!==a){for(n=[],o=wa,u=wa,Sa++,"*/"===t.substr(wa,2)?(i="*/",wa+=2):(i=a,0===Sa&&xa(ia)),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);o!==a;)n.push(o),o=wa,u=wa,Sa++,"*/"===t.substr(wa,2)?(i="*/",wa+=2):(i=a,0===Sa&&xa(ia)),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);n!==a?("*/"===t.substr(wa,2)?(o="*/",wa+=2):(o=a,0===Sa&&xa(ia)),o!==a?r=e=[e,n,o]:(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,"--"===t.substr(wa,2)?(e="--",wa+=2):(e=a,0===Sa&&xa(sa));if(e!==a){for(n=[],o=wa,u=wa,Sa++,i=dc(),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);o!==a;)n.push(o),o=wa,u=wa,Sa++,i=dc(),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);n!==a?r=e=[e,n]:(wa=r,r=a)}else wa=r,r=a;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=wa,35===t.charCodeAt(wa)?(e="#",wa++):(e=a,0===Sa&&xa(ca));if(e!==a){for(n=[],o=wa,u=wa,Sa++,i=dc(),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);o!==a;)n.push(o),o=wa,u=wa,Sa++,i=dc(),Sa--,i===a?u=void 0:(wa=u,u=a),u!==a&&(i=hc())!==a?o=u=[u,i]:(wa=o,o=a);n!==a?r=e=[e,n]:(wa=r,r=a)}else wa=r,r=a;return r}()),r}function vc(){var t,r,e,n,o,u,i;return t=wa,(r=tc())!==a&&fc()!==a?((e=Ys())===a&&(e=null),e!==a&&fc()!==a&&(n=_i())!==a?(La=t,u=e,i=n,t=r={type:(o=r).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:i}):(wa=t,t=a)):(wa=t,t=a),t}function hc(){var r;return t.length>wa?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(la)),r}function yc(){var r;return fa.test(t.charAt(wa))?(r=t.charAt(wa),wa++):(r=a,0===Sa&&xa(pa)),r}function dc(){var r,e;if((r=function(){var r,e;r=wa,Sa++,t.length>wa?(e=t.charAt(wa),wa++):(e=a,0===Sa&&xa(la));Sa--,e===a?r=void 0:(wa=r,r=a);return r}())===a)if(r=[],le.test(t.charAt(wa))?(e=t.charAt(wa),wa++):(e=a,0===Sa&&xa(fe)),e!==a)for(;e!==a;)r.push(e),le.test(t.charAt(wa))?(e=t.charAt(wa),wa++):(e=a,0===Sa&&xa(fe));else r=a;return r}function mc(){var r,e;return r=wa,La=wa,Qc=[],(!0?void 0:a)!==a&&fc()!==a?((e=jc())===a&&(e=function(){var r,e;r=wa,function(){var r;return"return"===t.substr(wa,6).toLowerCase()?(r=t.substr(wa,6),wa+=6):(r=a,0===Sa&&xa(Ro)),r}()!==a&&fc()!==a&&(e=Oc())!==a?(La=r,r={type:"return",expr:e}):(wa=r,r=a);return r}()),e!==a?(La=r,r={stmt:e,vars:Qc}):(wa=r,r=a)):(wa=r,r=a),r}function jc(){var r,e,n,o;return r=wa,(e=Tc())===a&&(e=Ec()),e!==a&&fc()!==a?((n=function(){var r;return":="===t.substr(wa,2)?(r=":=",wa+=2):(r=a,0===Sa&&xa(Vo)),r}())===a&&(n=Ys()),n!==a&&fc()!==a&&(o=Oc())!==a?(La=r,r=e={type:"assign",left:e,symbol:n,right:o}):(wa=r,r=a)):(wa=r,r=a),r}function Oc(){var r;return(r=uu())===a&&(r=function(){var t,r,e,n,o;t=wa,(r=Tc())!==a&&fc()!==a&&(e=Cu())!==a&&fc()!==a&&(n=Tc())!==a&&fc()!==a&&(o=Su())!==a?(La=t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(wa=t,t=a);return t}())===a&&(r=wc())===a&&(r=function(){var r,e;r=wa,function(){var r;return 91===t.charCodeAt(wa)?(r="[",wa++):(r=a,0===Sa&&xa(ta)),r}()!==a&&fc()!==a&&(e=Sc())!==a&&fc()!==a&&function(){var r;return 93===t.charCodeAt(wa)?(r="]",wa++):(r=a,0===Sa&&xa(ra)),r}()!==a?(La=r,r={type:"array",value:e}):(wa=r,r=a);return r}()),r}function wc(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Lc())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=ei())!==a&&(i=fc())!==a&&(s=Lc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=ei())!==a&&(i=fc())!==a&&(s=Lc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=Xt(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Lc(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Cc())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=oi())!==a&&(i=fc())!==a&&(s=Cc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=oi())!==a&&(i=fc())!==a&&(s=Cc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=Xt(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Cc(){var t,r,e;return(t=Ei())===a&&(t=Tc())===a&&(t=Ac())===a&&(t=Ci())===a&&(t=wa,ac()!==a&&fc()!==a&&(r=wc())!==a&&fc()!==a&&uc()!==a?(La=t,(e=r).parentheses=!0,t=e):(wa=t,t=a)),t}function gc(){var t,r,e,n,o,u,i;return t=wa,(r=ci())!==a?(e=wa,(n=fc())!==a&&(o=ec())!==a&&(u=fc())!==a&&(i=ci())!==a?e=n=[n,o,u,i]:(wa=e,e=a),e===a&&(e=null),e!==a?(La=t,t=r=function(t,r){const e={name:[t]};return null!==r&&(e.schema=t,e.name=[r[3]]),e}(r,e)):(wa=t,t=a)):(wa=t,t=a),t}function Ac(){var t,r,e;return t=wa,(r=gc())!==a&&fc()!==a&&ac()!==a&&fc()!==a?((e=Sc())===a&&(e=null),e!==a&&fc()!==a&&uc()!==a?(La=t,t=r={type:"function",name:r,args:{type:"expr_list",value:e},...kc()}):(wa=t,t=a)):(wa=t,t=a),t===a&&(t=wa,(r=gc())!==a&&(La=t,r=function(t){return{type:"function",name:t,args:null,...kc()}}(r)),t=r),t}function Sc(){var t,r,e,n,o,u,i,s;if(t=wa,(r=Cc())!==a){for(e=[],n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Cc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);n!==a;)e.push(n),n=wa,(o=fc())!==a&&(u=nc())!==a&&(i=fc())!==a&&(s=Cc())!==a?n=o=[o,u,i,s]:(wa=n,n=a);e!==a?(La=t,t=r=T(r,e)):(wa=t,t=a)}else wa=t,t=a;return t}function Tc(){var t,r,e,n,o;return t=wa,(r=Ws())!==a&&(e=Ec())!==a?(La=t,n=r,o=e,t=r={type:"var",...o,prefix:n}):(wa=t,t=a),t}function Ec(){var r,e,n,o,u;return r=wa,(e=ji())!==a&&(n=function(){var r,e,n,o,u;r=wa,e=[],n=wa,46===t.charCodeAt(wa)?(o=".",wa++):(o=a,0===Sa&&xa(pe));o!==a&&(u=ji())!==a?n=o=[o,u]:(wa=n,n=a);for(;n!==a;)e.push(n),n=wa,46===t.charCodeAt(wa)?(o=".",wa++):(o=a,0===Sa&&xa(pe)),o!==a&&(u=ji())!==a?n=o=[o,u]:(wa=n,n=a);e!==a&&(La=r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==a?(La=r,o=e,u=n,Qc.push(o),r=e={type:"var",name:o,members:u,prefix:null}):(wa=r,r=a),r===a&&(r=wa,(e=Ni())!==a&&(La=r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function Uc(){var r;return(r=function(){var r,e,n,o;r=wa,(e=Ts())===a&&(e=Es());if(e!==a)if(fc()!==a)if(ac()!==a)if(fc()!==a){if(n=[],be.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(ve)),o!==a)for(;o!==a;)n.push(o),be.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(ve));else n=a;n!==a&&(o=fc())!==a&&uc()!==a?(La=r,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},r=e):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;r===a&&(r=wa,(e=Ts())!==a&&(La=r,e=ma(e)),(r=e)===a&&(r=wa,(e=Es())!==a&&(La=r,e=ma(e)),r=e));return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f,p,b;r=wa,(e=Us())===a&&(e=_s())===a&&(e=ks())===a&&(e=Is())===a&&(e=Ns())===a&&(e=Rs())===a&&(e=Vs())===a&&(e=qs())===a&&(e=Ms())===a&&(e=function(){var r,e,n,o;return r=wa,"bit"===t.substr(wa,3).toLowerCase()?(e=t.substr(wa,3),wa+=3):(e=a,0===Sa&&xa(Pn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="BIT"):(wa=r,r=a)):(wa=r,r=a),r}())===a&&(e=Ps());if(e!==a)if((n=fc())!==a)if((o=ac())!==a)if((u=fc())!==a){if(i=[],be.test(t.charAt(wa))?(s=t.charAt(wa),wa++):(s=a,0===Sa&&xa(ve)),s!==a)for(;s!==a;)i.push(s),be.test(t.charAt(wa))?(s=t.charAt(wa),wa++):(s=a,0===Sa&&xa(ve));else i=a;if(i!==a)if((s=fc())!==a){if(c=wa,(l=nc())!==a)if((f=fc())!==a){if(p=[],be.test(t.charAt(wa))?(b=t.charAt(wa),wa++):(b=a,0===Sa&&xa(ve)),b!==a)for(;b!==a;)p.push(b),be.test(t.charAt(wa))?(b=t.charAt(wa),wa++):(b=a,0===Sa&&xa(ve));else p=a;p!==a?c=l=[l,f,p]:(wa=c,c=a)}else wa=c,c=a;else wa=c,c=a;c===a&&(c=null),c!==a&&(l=fc())!==a&&(f=uc())!==a&&(p=fc())!==a?((b=_c())===a&&(b=null),b!==a?(La=r,v=c,h=b,e={dataType:e,length:parseInt(i.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:h},r=e):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;else wa=r,r=a}else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;else wa=r,r=a;var v,h;if(r===a){if(r=wa,(e=Us())===a&&(e=_s())===a&&(e=ks())===a&&(e=Is())===a&&(e=Ns())===a&&(e=Rs())===a&&(e=Vs())===a&&(e=qs())===a&&(e=Ms())===a&&(e=Ps()),e!==a){if(n=[],be.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(ve)),o!==a)for(;o!==a;)n.push(o),be.test(t.charAt(wa))?(o=t.charAt(wa),wa++):(o=a,0===Sa&&xa(ve));else n=a;n!==a&&(o=fc())!==a?((u=_c())===a&&(u=null),u!==a?(La=r,e=function(t,r,e){return{dataType:t,length:parseInt(r.join(""),10),suffix:e}}(e,n,u),r=e):(wa=r,r=a)):(wa=r,r=a)}else wa=r,r=a;r===a&&(r=wa,(e=Us())===a&&(e=_s())===a&&(e=ks())===a&&(e=Is())===a&&(e=Ns())===a&&(e=Rs())===a&&(e=Vs())===a&&(e=qs())===a&&(e=Ms())===a&&(e=Ps()),e!==a&&(n=fc())!==a?((o=_c())===a&&(o=null),o!==a&&(u=fc())!==a?(La=r,e=function(t,r){return{dataType:t,suffix:r}}(e,o),r=e):(wa=r,r=a)):(wa=r,r=a))}return r}())===a&&(r=function(){var r,e,n,o;r=wa,(e=Qs())===a&&(e=Ds())===a&&(e=Bs())===a&&(e=Fs());e!==a&&fc()!==a&&ac()!==a&&fc()!==a?(ja.test(t.charAt(wa))?(n=t.charAt(wa),wa++):(n=a,0===Sa&&xa(Oa)),n!==a&&fc()!==a&&uc()!==a&&fc()!==a?((o=_c())===a&&(o=null),o!==a?(La=r,e={dataType:e,length:parseInt(n,10),parentheses:!0},r=e):(wa=r,r=a)):(wa=r,r=a)):(wa=r,r=a);r===a&&(r=wa,(e=Qs())===a&&(e=Ds())===a&&(e=Bs())===a&&(e=Fs()),e!==a&&(La=r,e=ma(e)),r=e);return r}())===a&&(r=function(){var r,e;r=wa,(e=function(){var r,e,n,o;return r=wa,"json"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(Zn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="JSON"):(wa=r,r=a)):(wa=r,r=a),r}())!==a&&(La=r,e=ma(e));return r=e}())===a&&(r=function(){var r,e;r=wa,(e=function(){var r,e,n,o;return r=wa,"tinytext"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Kn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TINYTEXT"):(wa=r,r=a)):(wa=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=wa,"text"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(zn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="TEXT"):(wa=r,r=a)):(wa=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=wa,"mediumtext"===t.substr(wa,10).toLowerCase()?(e=t.substr(wa,10),wa+=10):(e=a,0===Sa&&xa(to)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="MEDIUMTEXT"):(wa=r,r=a)):(wa=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=wa,"longtext"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(ro)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="LONGTEXT"):(wa=r,r=a)):(wa=r,r=a),r}());e!==a&&(La=r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e,n;r=wa,(e=function(){var r,e,n,o;return r=wa,"enum"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(no)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ENUM"):(wa=r,r=a)):(wa=r,r=a),r}())!==a&&fc()!==a&&(n=Pu())!==a?(La=r,o=e,(u=n).parentheses=!0,r=e={dataType:o,expr:u}):(wa=r,r=a);var o,u;return r}())===a&&(r=function(){var r,e;r=wa,"boolean"===t.substr(wa,7).toLowerCase()?(e=t.substr(wa,7),wa+=7):(e=a,0===Sa&&xa(da));e!==a&&(La=r,e={dataType:"BOOLEAN"});return r=e}())===a&&(r=function(){var r,e;r=wa,"blob"===t.substr(wa,4).toLowerCase()?(e=t.substr(wa,4),wa+=4):(e=a,0===Sa&&xa(ba));e===a&&("tinyblob"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(va)),e===a&&("mediumblob"===t.substr(wa,10).toLowerCase()?(e=t.substr(wa,10),wa+=10):(e=a,0===Sa&&xa(ha)),e===a&&("longblob"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(ya)))));e!==a&&(La=r,e={dataType:e.toUpperCase()});return r=e}()),r}function _c(){var r,e,n;return r=wa,(e=xs())===a&&(e=null),e!==a&&fc()!==a?((n=function(){var r,e,n,o;return r=wa,"zerofill"===t.substr(wa,8).toLowerCase()?(e=t.substr(wa,8),wa+=8):(e=a,0===Sa&&xa(Wn)),e!==a?(n=wa,Sa++,o=Oi(),Sa--,o===a?n=void 0:(wa=n,n=a),n!==a?(La=r,r=e="ZEROFILL"):(wa=r,r=a)):(wa=r,r=a),r}())===a&&(n=null),n!==a?(La=r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(wa=r,r=a)):(wa=r,r=a),r}const xc={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function kc(){return r.includeLocations?{loc:_a(La,wa)}:{}}function Ic(t,r){return{type:"unary_expr",operator:t,expr:r}}function Nc(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e}}function Rc(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function Vc(t,r,e=3){const n=[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function qc(t,r){let e=t;for(let t=0;t<r.length;t++)e=Nc(r[t][1],e,r[t][3]);return e}function Mc(t){const r=Fc[t];return r||(t||null)}function Pc(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=Mc(t[1])),r.add(t.join("::"))}return Array.from(r)}let Qc=[];const Dc=new Set,Bc=new Set,Fc={};if((e=i())!==a&&wa===t.length)return e;throw e!==a&&wa<t.length&&xa({type:"end"}),ka(Aa,ga<t.length?t.charAt(ga):null,ga<t.length?_a(ga,ga+1):_a(ga,ga))}}},function(t,r,e){t.exports=e(27)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(24);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(28))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),a="function"==typeof BigInt;function u(t,r,e,n){return void 0===t?u[0]:void 0!==r&&(10!=+r||e)?Q(t,r,e,n):H(t)}function i(t,r){this.value=t,this.sign=r,this.isSmall=!1}function s(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&E(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function h(t){return t>0?Math.floor(t):Math.ceil(t)}function y(t,e){var n,o,a=t.length,u=e.length,i=new Array(a),s=0,c=r;for(o=0;o<u;o++)s=(n=t[o]+e[o]+s)>=c?1:0,i[o]=n-s*c;for(;o<a;)s=(n=t[o]+s)===c?1:0,i[o++]=n-s*c;return s>0&&i.push(s),i}function d(t,r){return t.length>=r.length?y(t,r):y(r,t)}function m(t,e){var n,o,a=t.length,u=new Array(a),i=r;for(o=0;o<a;o++)n=t[o]-i+e,e=Math.floor(n/i),u[o]=n-e*i,e+=1;for(;e>0;)u[o++]=e%i,e=Math.floor(e/i);return u}function j(t,r){var e,n,o=t.length,a=r.length,u=new Array(o),i=0;for(e=0;e<a;e++)(n=t[e]-i-r[e])<0?(n+=1e7,i=1):i=0,u[e]=n;for(e=a;e<o;e++){if(!((n=t[e]-i)<0)){u[e++]=n;break}n+=1e7,u[e]=n}for(;e<o;e++)u[e]=t[e];return b(u),u}function O(t,r,e){var n,o,a=t.length,u=new Array(a),c=-r;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,u[n]=o<0?o+1e7:o;return"number"==typeof(u=p(u))?(e&&(u=-u),new s(u)):new i(u,e)}function w(t,r){var e,n,o,a,u=t.length,i=r.length,s=v(u+i);for(o=0;o<u;++o){a=t[o];for(var c=0;c<i;++c)e=a*r[c]+s[o+c],n=Math.floor(e/1e7),s[o+c]=e-1e7*n,s[o+c+1]+=n}return b(s),s}function L(t,e){var n,o,a=t.length,u=new Array(a),i=r,s=0;for(o=0;o<a;o++)n=t[o]*e+s,s=Math.floor(n/i),u[o]=n-s*i;for(;s>0;)u[o++]=s%i,s=Math.floor(s/i);return u}function C(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function g(t,e,n){return new i(t<r?L(e,t):w(e,f(t)),n)}function A(t){var r,e,n,o,a=t.length,u=v(a+a);for(n=0;n<a;n++){e=0-(o=t[n])*o;for(var i=n;i<a;i++)r=o*t[i]*2+u[n+i]+e,e=Math.floor(r/1e7),u[n+i]=r-1e7*e;u[n+a]=e}return b(u),u}function S(t,r){var e,n,o,a,u=t.length,i=v(u);for(o=0,e=u-1;e>=0;--e)o=(a=1e7*o+t[e])-(n=h(a/r))*r,i[e]=0|n;return[i,0|o]}function T(t,e){var n,o=H(e);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,y=t.value,d=o.value;if(0===d)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new s(h(y/d)),new s(y%d)]:[u[0],t];if(o.isSmall){if(1===d)return[t,u[0]];if(-1==d)return[t.negate(),u[0]];var m=Math.abs(d);if(m<r){l=p((n=S(y,m))[0]);var O=n[1];return t.sign&&(O=-O),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new s(l),new s(O)]):[new i(l,t.sign!==o.sign),new s(O)]}d=f(m)}var w=E(y,d);if(-1===w)return[u[0],t];if(0===w)return[u[t.sign===o.sign?1:-1],u[0]];l=(n=y.length+d.length<=200?function(t,e){var n,o,a,u,i,s,c,l=t.length,f=e.length,b=r,h=v(e.length),y=e[f-1],d=Math.ceil(b/(2*y)),m=L(t,d),j=L(e,d);for(m.length<=l&&m.push(0),j.push(0),y=j[f-1],o=l-f;o>=0;o--){for(n=b-1,m[o+f]!==y&&(n=Math.floor((m[o+f]*b+m[o+f-1])/y)),a=0,u=0,s=j.length,i=0;i<s;i++)a+=n*j[i],c=Math.floor(a/b),u+=m[o+i]-(a-c*b),a=c,u<0?(m[o+i]=u+b,u=-1):(m[o+i]=u,u=0);for(;0!==u;){for(n-=1,a=0,i=0;i<s;i++)(a+=m[o+i]-b+j[i])<0?(m[o+i]=a+b,a=0):(m[o+i]=a,a=1);u+=a}h[o]=n}return m=S(m,d)[0],[p(h),p(m)]}(y,d):function(t,r){for(var e,n,o,a,u,i=t.length,s=r.length,c=[],l=[];i;)if(l.unshift(t[--i]),b(l),E(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*r[s-1]+r[s-2],n>s&&(o=1e7*(o+1)),e=Math.ceil(o/a);do{if(E(u=L(r,e),l)<=0)break;e--}while(e);c.push(e),l=j(l,u)}return c.reverse(),[p(c),p(l)]}(y,d))[0];var C=t.sign!==o.sign,g=n[1],A=t.sign;return"number"==typeof l?(C&&(l=-l),l=new s(l)):l=new i(l,C),"number"==typeof g?(A&&(g=-g),g=new s(g)):g=new i(g,A),[l,g]}function E(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function U(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function _(t,r){for(var e,n,a,u=t.prev(),i=u,s=0;i.isEven();)i=i.divide(2),s++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(a=o(r[n]).modPow(i,t)).isUnit()&&!a.equals(u)){for(e=s-1;0!=e;e--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(u))continue t}return!1}return!0}i.prototype=Object.create(u.prototype),s.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),i.prototype.add=function(t){var r=H(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new i(m(e,Math.abs(n)),this.sign):new i(d(e,n),this.sign)},i.prototype.plus=i.prototype.add,s.prototype.add=function(t){var r=H(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new s(e+n);n=f(Math.abs(n))}return new i(m(n,Math.abs(e)),e<0)},s.prototype.plus=s.prototype.add,c.prototype.add=function(t){return new c(this.value+H(t).value)},c.prototype.plus=c.prototype.add,i.prototype.subtract=function(t){var r=H(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?O(e,Math.abs(n),this.sign):function(t,r,e){var n;return E(t,r)>=0?n=j(t,r):(n=j(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new s(n)):new i(n,e)}(e,n,this.sign)},i.prototype.minus=i.prototype.subtract,s.prototype.subtract=function(t){var r=H(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new s(e-n):O(n,Math.abs(e),e>=0)},s.prototype.minus=s.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-H(t).value)},c.prototype.minus=c.prototype.subtract,i.prototype.negate=function(){return new i(this.value,!this.sign)},s.prototype.negate=function(){var t=this.sign,r=new s(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},i.prototype.abs=function(){return new i(this.value,!1)},s.prototype.abs=function(){return new s(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},i.prototype.multiply=function(t){var e,n,o,a=H(t),s=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new i(L(s,e),l);c=f(e)}return n=s.length,o=c.length,new i(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return w(r,e);n=Math.ceil(n/2);var o=r.slice(n),a=r.slice(0,n),u=e.slice(n),i=e.slice(0,n),s=t(a,i),c=t(o,u),l=t(d(a,o),d(i,u)),f=d(d(s,C(j(j(l,s),c),n)),C(c,2*n));return b(f),f}(s,c):w(s,c),l)},i.prototype.times=i.prototype.multiply,s.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new s(t.value*this.value):g(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},i.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():g(Math.abs(t.value),this.value,this.sign!==t.sign)},s.prototype.multiply=function(t){return H(t)._multiplyBySmall(this)},s.prototype.times=s.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*H(t).value)},c.prototype.times=c.prototype.multiply,i.prototype.square=function(){return new i(A(this.value),!1)},s.prototype.square=function(){var t=this.value*this.value;return l(t)?new s(t):new i(A(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},i.prototype.divmod=function(t){var r=T(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=s.prototype.divmod=i.prototype.divmod,i.prototype.divide=function(t){return T(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/H(t).value)},s.prototype.over=s.prototype.divide=i.prototype.over=i.prototype.divide,i.prototype.mod=function(t){return T(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%H(t).value)},s.prototype.remainder=s.prototype.mod=i.prototype.remainder=i.prototype.mod,i.prototype.pow=function(t){var r,e,n,o=H(t),a=this.value,i=o.value;if(0===i)return u[1];if(0===a)return u[0];if(1===a)return u[1];if(-1===a)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(a,i)))return new s(h(r));for(e=this,n=u[1];!0&i&&(n=n.times(e),--i),0!==i;)i/=2,e=e.square();return n},s.prototype.pow=i.prototype.pow,c.prototype.pow=function(t){var r=H(t),e=this.value,n=r.value,o=BigInt(0),a=BigInt(1),i=BigInt(2);if(n===o)return u[1];if(e===o)return u[0];if(e===a)return u[1];if(e===BigInt(-1))return r.isEven()?u[1]:u[-1];if(r.isNegative())return new c(o);for(var s=this,l=u[1];(n&a)===a&&(l=l.times(s),--n),n!==o;)n/=i,s=s.square();return l},i.prototype.modPow=function(t,r){if(t=H(t),(r=H(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=u[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(u[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=s.prototype.modPow=i.prototype.modPow,i.prototype.compareAbs=function(t){var r=H(t),e=this.value,n=r.value;return r.isSmall?1:E(e,n)},s.prototype.compareAbs=function(t){var r=H(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=H(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=H(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:E(e,n)*(this.sign?-1:1)},i.prototype.compareTo=i.prototype.compare,s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=H(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},s.prototype.compareTo=s.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=H(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,i.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=s.prototype.eq=s.prototype.equals=i.prototype.eq=i.prototype.equals,i.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=s.prototype.neq=s.prototype.notEquals=i.prototype.neq=i.prototype.notEquals,i.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=s.prototype.gt=s.prototype.greater=i.prototype.gt=i.prototype.greater,i.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=s.prototype.lt=s.prototype.lesser=i.prototype.lt=i.prototype.lesser,i.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals,i.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals,i.prototype.isEven=function(){return 0==(1&this.value[0])},s.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},i.prototype.isOdd=function(){return 1==(1&this.value[0])},s.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},i.prototype.isPositive=function(){return!this.sign},s.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=s.prototype.isPositive,i.prototype.isNegative=function(){return this.sign},s.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=s.prototype.isNegative,i.prototype.isUnit=function(){return!1},s.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},i.prototype.isZero=function(){return!1},s.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},i.prototype.isDivisibleBy=function(t){var r=H(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=s.prototype.isDivisibleBy=i.prototype.isDivisibleBy,i.prototype.isPrime=function(t){var r=U(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return _(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),u=Math.ceil(!0===t?2*Math.pow(a,2):a),i=[],s=0;s<u;s++)i.push(o(s+2));return _(e,i)},c.prototype.isPrime=s.prototype.isPrime=i.prototype.isPrime,i.prototype.isProbablePrime=function(t,r){var e=U(this);if(void 0!==e)return e;for(var n=this.abs(),a=void 0===t?5:t,u=[],i=0;i<a;i++)u.push(o.randBetween(2,n.minus(2),r));return _(n,u)},c.prototype.isProbablePrime=s.prototype.isProbablePrime=i.prototype.isProbablePrime,i.prototype.modInv=function(t){for(var r,e,n,a=o.zero,u=o.one,i=H(t),s=this.abs();!s.isZero();)r=i.divide(s),e=a,n=i,a=u,i=s,u=e.subtract(r.multiply(u)),s=n.subtract(r.multiply(s));if(!i.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=s.prototype.modInv=i.prototype.modInv,i.prototype.next=function(){var t=this.value;return this.sign?O(t,1,this.sign):new i(m(t,1),this.sign)},s.prototype.next=function(){var t=this.value;return t+1<e?new s(t+1):new i(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},i.prototype.prev=function(){var t=this.value;return this.sign?new i(m(t,1),!0):O(t,1,this.sign)},s.prototype.prev=function(){var t=this.value;return t-1>-e?new s(t-1):new i(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var x=[1];2*x[x.length-1]<=r;)x.push(2*x[x.length-1]);var k=x.length,I=x[k-1];function N(t){return Math.abs(t)<=r}function R(t,r,e){r=H(r);for(var n=t.isNegative(),a=r.isNegative(),u=n?t.not():t,i=a?r.not():r,s=0,c=0,l=null,f=null,p=[];!u.isZero()||!i.isZero();)s=(l=T(u,I))[1].toJSNumber(),n&&(s=I-1-s),c=(f=T(i,I))[1].toJSNumber(),a&&(c=I-1-c),u=l[0],i=f[0],p.push(e(s,c));for(var b=0!==e(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(I).add(o(p[v]));return b}i.prototype.shiftLeft=function(t){var r=H(t).toJSNumber();if(!N(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=k;)e=e.multiply(I),r-=k-1;return e.multiply(x[r])},c.prototype.shiftLeft=s.prototype.shiftLeft=i.prototype.shiftLeft,i.prototype.shiftRight=function(t){var r,e=H(t).toJSNumber();if(!N(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=k;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=T(n,I))[1].isNegative()?r[0].prev():r[0],e-=k-1}return(r=T(n,x[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=s.prototype.shiftRight=i.prototype.shiftRight,i.prototype.not=function(){return this.negate().prev()},c.prototype.not=s.prototype.not=i.prototype.not,i.prototype.and=function(t){return R(this,t,(function(t,r){return t&r}))},c.prototype.and=s.prototype.and=i.prototype.and,i.prototype.or=function(t){return R(this,t,(function(t,r){return t|r}))},c.prototype.or=s.prototype.or=i.prototype.or,i.prototype.xor=function(t){return R(this,t,(function(t,r){return t^r}))},c.prototype.xor=s.prototype.xor=i.prototype.xor;function V(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function q(t,r){return t=H(t),r=H(r),t.greater(r)?t:r}function M(t,r){return t=H(t),r=H(r),t.lesser(r)?t:r}function P(t,r){if(t=H(t).abs(),r=H(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=u[1];t.isEven()&&r.isEven();)e=M(V(t),V(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(V(t));do{for(;r.isEven();)r=r.divide(V(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}i.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),a=n.p,u=n.e,i=a.multiply(e);return i.compareTo(r)<=0?{p:i,e:2*u+1}:{p:a,e:2*u}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=s.prototype.bitLength=i.prototype.bitLength;var Q=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,a=t.length,u=Math.abs(r),i={};for(o=0;o<e.length;o++)i[e[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in i&&i[l]>=u)){if("1"===l&&1===u)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=H(r);var s=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in i)s.push(H(i[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);s.push(H(t.slice(f+1,o)))}}return D(s,r,c)};function D(t,r,e){var n,o=u[0],a=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(r);return e?o.negate():o}function B(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,u=[],i=t;i.isNegative()||i.compareAbs(r)>=0;){a=i.divmod(r),i=a.quotient;var s=a.remainder;s.isNegative()&&(s=r.minus(s).abs(),i=i.next()),u.push(s.toJSNumber())}return u.push(i.toJSNumber()),{value:u.reverse(),isNegative:n}}function F(t,r,e){var n=B(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function $(t){if(l(+t)){var r=+t;if(r===h(r))return a?new c(BigInt(r)):new s(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==h(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var u=n[0],f=u.indexOf(".");if(f>=0&&(o-=u.length-f-1,u=u.slice(0,f)+u.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,y=v-7;v>0;)p.push(+t.slice(y,v)),(y-=7)<0&&(y=0),v-=7;return b(p),new i(p,e)}function H(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==h(t))throw new Error(t+" is not an integer.");return new s(t)}return $(t.toString())}(t):"string"==typeof t?$(t):"bigint"==typeof t?new c(t):t}i.prototype.toArray=function(t){return B(this,t)},s.prototype.toArray=function(t){return B(this,t)},c.prototype.toArray=function(t){return B(this,t)},i.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return F(this,t,r);for(var e,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)e=String(n[o]),a+="0000000".slice(e.length)+e;return(this.sign?"-":"")+a},s.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?F(this,t,r):String(this.value)},c.prototype.toString=s.prototype.toString,c.prototype.toJSON=i.prototype.toJSON=s.prototype.toJSON=function(){return this.toString()},i.prototype.valueOf=function(){return parseInt(this.toString(),10)},i.prototype.toJSNumber=i.prototype.valueOf,s.prototype.valueOf=function(){return this.value},s.prototype.toJSNumber=s.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var G=0;G<1e3;G++)u[G]=H(G),G>0&&(u[-G]=H(-G));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=q,u.min=M,u.gcd=P,u.lcm=function(t,r){return t=H(t).abs(),r=H(r).abs(),t.divide(P(t,r)).multiply(r)},u.isInstance=function(t){return t instanceof i||t instanceof s||t instanceof c},u.randBetween=function(t,e,n){t=H(t),e=H(e);var o=n||Math.random,a=M(t,e),i=q(t,e).subtract(a).add(1);if(i.isSmall)return a.add(Math.floor(o()*i));for(var s=B(i,r).value,c=[],l=!0,f=0;f<s.length;f++){var p=l?s[f]+(f+1<s.length?s[f+1]/r:0):r,b=h(o()*p);c.push(b),b<s[f]&&(l=!1)}return a.add(u.fromArray(c,r,!1))},u.fromArray=function(t,r,e){return D(t.map(H),H(r||10),e)},u}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(30)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=sqlite.umd.js.map