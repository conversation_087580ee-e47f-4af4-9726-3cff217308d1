{"name": "@benborla29/mcp-server-mysql", "version": "2.0.2", "description": "MCP server for interacting with MySQL databases based on Node", "license": "MIT", "author": "<PERSON> (https://github.com/ben<PERSON><PERSON>)", "type": "module", "main": "dist/index.js", "preferGlobal": true, "bin": {"mcp-server-mysql": "dist/index.js"}, "files": ["dist", "README.md", "assets"], "scripts": {"start": "node dist/index.js", "dev": "ts-node index.ts", "build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch", "setup:test:db": "tsx scripts/setup-test-db.ts", "pretest": "pnpm run setup:test:db", "test": "pnpm run setup:test:db && vitest run", "test:socket": "pnpm run setup:test:db && vitest run tests/integration/socket-connection.test.ts", "test:watch": "pnpm run setup:test:db && vitest", "test:coverage": "vitest run --coverage", "test:unit": "vitest run --config vitest.unit.config.ts", "test:integration": "vitest run --config vitest.integration.config.ts", "test:e2e": "vitest run --config vitest.e2e.config.ts", "stdio": "node dist/index.js --stdio", "exec": " pnpm build && npx node --env-file=.env dist/index.js"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@modelcontextprotocol/sdk": "1.8.0", "dotenv": "^16.5.0", "mcp-evals": "^1.0.18", "mysql2": "^3.14.1", "node-sql-parser": "^5.3.9"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.17.50", "@typescript-eslint/parser": "^7.18.0", "eslint": "^9.27.0", "shx": "^0.3.4", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^1.6.1"}, "publishConfig": {"access": "public"}, "keywords": ["node", "mcp", "ai", "cursor", "mcp-server", "modelcontextprotocol", "smithery", "mcp-get", "mcp-put", "mcp-post", "mcp-delete", "mcp-patch", "mcp-options", "mcp-head"]}