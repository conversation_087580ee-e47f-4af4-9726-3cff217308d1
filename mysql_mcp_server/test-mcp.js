#!/usr/bin/env node

/**
 * Test script to manually test the MCP server
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing MCP Server...');

// Start the MCP server
const serverPath = path.join(__dirname, 'simple-mcp-server.js');
const mcpProcess = spawn('node', [serverPath], {
  env: {
    ...process.env,
    MYSQL_HOST: '***********',
    MYSQL_PORT: '3306',
    MYSQL_USER: 'root',
    MYSQL_PASSWORD: '44033@lzd',
    MYSQL_DATABASE: 'bailun'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // Process complete JSON responses
  const lines = responseBuffer.split('\n');
  responseBuffer = lines.pop(); // Keep incomplete line
  
  lines.forEach(line => {
    if (line.trim()) {
      try {
        const response = JSON.parse(line);
        console.log('📥 Response:', JSON.stringify(response, null, 2));
      } catch (e) {
        console.log('📥 Raw output:', line);
      }
    }
  });
});

mcpProcess.stderr.on('data', (data) => {
  console.log('📋 Server log:', data.toString().trim());
});

// Wait for server to start
setTimeout(() => {
  console.log('🔧 Sending initialize request...');
  
  // Send initialize request
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(initRequest) + '\n');
  
  // Send tools/list request
  setTimeout(() => {
    console.log('🛠️  Sending tools/list request...');
    
    const toolsRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/list'
    };
    
    mcpProcess.stdin.write(JSON.stringify(toolsRequest) + '\n');
    
    // Send a test query
    setTimeout(() => {
      console.log('📊 Sending test query...');
      
      const queryRequest = {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'list_tables',
          arguments: {}
        }
      };
      
      mcpProcess.stdin.write(JSON.stringify(queryRequest) + '\n');
      
      // Clean up after 3 seconds
      setTimeout(() => {
        console.log('✅ Test complete, shutting down...');
        mcpProcess.kill('SIGTERM');
        process.exit(0);
      }, 3000);
      
    }, 1000);
  }, 1000);
}, 2000);

mcpProcess.on('error', (error) => {
  console.error('❌ Process error:', error);
});

mcpProcess.on('exit', (code) => {
  console.log(`🏁 MCP server exited with code ${code}`);
});
