#!/usr/bin/env node

// This is a wrapper script to start the MySQL MCP Server
// The actual server is implemented in @benborla29/mcp-server-mysql

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting MySQL MCP Server...');

// Set environment variables for the MCP server
const env = {
  ...process.env,
  MYSQL_HOST: process.env.MYSQL_HOST || '***********',
  MYSQL_PORT: process.env.MYSQL_PORT || '3306',
  MYSQL_USER: process.env.MYSQL_USER || 'root',
  MYSQL_PASSWORD: process.env.MYSQL_PASSWORD || '44033@lzd',
  MYSQL_DATABASE: process.env.MYSQL_DATABASE || 'bailun',
  ALLOW_INSERT_OPERATION: process.env.ALLOW_INSERT_OPERATION || 'true',
  ALLOW_UPDATE_OPERATION: process.env.ALLOW_UPDATE_OPERATION || 'true',
  ALLOW_DELETE_OPERATION: process.env.ALLOW_DELETE_OPERATION || 'false'
};

console.log('📋 Configuration:');
console.log(`   Host: ${env.MYSQL_HOST}`);
console.log(`   Port: ${env.MYSQL_PORT}`);
console.log(`   User: ${env.MYSQL_USER}`);
console.log(`   Database: ${env.MYSQL_DATABASE}`);
console.log(`   Insert: ${env.ALLOW_INSERT_OPERATION}`);
console.log(`   Update: ${env.ALLOW_UPDATE_OPERATION}`);
console.log(`   Delete: ${env.ALLOW_DELETE_OPERATION}`);

// Path to the MCP server executable
const mcpServerPath = path.join(__dirname, 'node_modules', '@benborla29', 'mcp-server-mysql', 'dist', 'index.js');

console.log('🔧 Starting MCP server process...');

// Spawn the MCP server process
const mcpProcess = spawn('node', [mcpServerPath], {
  env: env,
  stdio: 'inherit'
});

mcpProcess.on('error', (error) => {
  console.error('❌ Error starting MySQL MCP Server:', error);
  process.exit(1);
});

mcpProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(`❌ MySQL MCP Server exited with code ${code} and signal ${signal}`);
    process.exit(code || 1);
  } else {
    console.log('✅ MySQL MCP Server exited successfully');
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down...');
  mcpProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down...');
  mcpProcess.kill('SIGTERM');
});

console.log('✅ MySQL MCP Server wrapper started successfully!');
