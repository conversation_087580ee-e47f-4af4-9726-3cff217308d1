#!/usr/bin/env node

// Debug script to test MCP server startup

console.log('🔍 Debug: Testing MCP Server startup...');

// Set environment variables
process.env.MYSQL_HOST = '***********';
process.env.MYSQL_PORT = '3306';
process.env.MYSQL_USER = 'root';
process.env.MYSQL_PASSWORD = '44033@lzd';
process.env.MYSQL_DATABASE = 'bailun';
process.env.ALLOW_INSERT_OPERATION = 'true';
process.env.ALLOW_UPDATE_OPERATION = 'true';
process.env.ALLOW_DELETE_OPERATION = 'false';

console.log('🔧 Environment variables set:');
console.log('MYSQL_HOST:', process.env.MYSQL_HOST);
console.log('MYSQL_PORT:', process.env.MYSQL_PORT);
console.log('MYSQL_USER:', process.env.MYSQL_USER);
console.log('MYSQL_DATABASE:', process.env.MYSQL_DATABASE);

const { spawn } = require('child_process');
const path = require('path');

const mcpServerPath = path.join(__dirname, 'node_modules', '@benborla29', 'mcp-server-mysql', 'dist', 'index.js');

console.log('📁 MCP Server path:', mcpServerPath);

// Check if file exists
const fs = require('fs');
if (fs.existsSync(mcpServerPath)) {
  console.log('✅ MCP Server file exists');
} else {
  console.log('❌ MCP Server file not found');
  process.exit(1);
}

console.log('🚀 Starting MCP server with full output...');

const mcpProcess = spawn('node', [mcpServerPath], {
  env: process.env,
  stdio: ['pipe', 'pipe', 'pipe']
});

mcpProcess.stdout.on('data', (data) => {
  console.log('📤 STDOUT:', data.toString());
});

mcpProcess.stderr.on('data', (data) => {
  console.log('📥 STDERR:', data.toString());
});

mcpProcess.on('error', (error) => {
  console.error('❌ Process error:', error);
});

mcpProcess.on('exit', (code, signal) => {
  console.log(`🏁 Process exited with code ${code} and signal ${signal}`);
});

// Keep the process alive for a few seconds
setTimeout(() => {
  console.log('⏰ Timeout reached, killing process...');
  mcpProcess.kill('SIGTERM');
}, 5000);
