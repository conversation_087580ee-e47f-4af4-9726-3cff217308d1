# MySQL MCP Server Setup

This directory contains the MySQL Model Context Protocol (MCP) server configuration for connecting to your MySQL database.

## Configuration

The server is configured to connect to:
- **Host**: ***********
- **Port**: 3306
- **Database**: bailun
- **User**: root

## Files

- `index.js` - Main server entry point
- `package.json` - Node.js dependencies
- `mcp.config.json` - MCP server configuration
- `README.md` - This file

## Available Operations

- ✅ **SELECT** - Read data from tables
- ✅ **INSERT** - Add new records (enabled)
- ✅ **UPDATE** - Modify existing records (enabled)
- ❌ **DELETE** - Remove records (disabled for safety)

## Usage in VSCode

### Starting the Server

1. **Using Command Palette**:
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Tasks: Run Task"
   - Select "Start MySQL MCP Server"

2. **Using Debug Configuration**:
   - Go to Run and Debug view (`Ctrl+Shift+D`)
   - Select "Start MySQL MCP Server" configuration
   - Press F5 to start

3. **Using Terminal**:
   ```bash
   cd mysql_mcp_server
   node index.js
   ```

### Testing Connection

Run the "Test MySQL Connection" task to verify your database connection:
- Press `Ctrl+Shift+P`
- Type "Tasks: Run Task"
- Select "Test MySQL Connection"

## Available MCP Tools

Once the server is running, you'll have access to these tools:

1. **list_tables_mysql** - List all tables in the database
2. **describe_table_mysql** - Get table structure and column information
3. **query_mysql** - Execute SELECT queries
4. **insert_mysql** - Insert new records (if enabled)
5. **update_mysql** - Update existing records (if enabled)

## Security Notes

- DELETE operations are disabled by default for safety
- Consider using environment variables for sensitive credentials in production
- The current setup is configured for development use

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Verify MySQL server is running on ***********:3306
   - Check firewall settings
   - Verify credentials

2. **Permission Denied**:
   - Ensure the user 'root' has proper permissions
   - Check if the database 'bailun' exists

3. **Module Not Found**:
   - Run `npm install` in the mysql_mcp_server directory
   - Ensure all dependencies are installed

### Logs

The server runs with debug logging enabled. Check the console output for detailed information about:
- Connection status
- Query execution
- Error messages

## Environment Variables

You can override configuration using environment variables:

- `MYSQL_HOST` - Database host
- `MYSQL_PORT` - Database port
- `MYSQL_USER` - Database user
- `MYSQL_PASSWORD` - Database password
- `MYSQL_DATABASE` - Database name
- `ALLOW_INSERT_OPERATION` - Enable/disable INSERT operations
- `ALLOW_UPDATE_OPERATION` - Enable/disable UPDATE operations
- `ALLOW_DELETE_OPERATION` - Enable/disable DELETE operations
