import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class MySQLTest {
    public static void main(String[] args) {
        String url = "******************************************************************************";
        String username = "root";
        String password = "44033@lzd";

        System.out.println("Testing MySQL connection...");
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        
        try {
            System.out.println("Loading MySQL JDBC driver...");
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("Driver loaded successfully!");
            
            System.out.println("Attempting to connect...");
            Connection connection = DriverManager.getConnection(url, username, password);
            
            if (connection != null) {
                System.out.println("\nConnection successful!");
                System.out.println("Connected to: " + connection.getMetaData().getURL());
                System.out.println("Database: " + connection.getCatalog());
                System.out.println("Product: " + connection.getMetaData().getDatabaseProductName());
                System.out.println("Version: " + connection.getMetaData().getDatabaseProductVersion());
                
                connection.close();
                System.out.println("\nConnection closed successfully!");
            }
        } catch (ClassNotFoundException e) {
            System.out.println("\nError: MySQL JDBC Driver not found!");
            e.printStackTrace();
        } catch (SQLException e) {
            System.out.println("\nError: Database connection failed!");
            System.out.println("Error Code: " + e.getErrorCode());
            System.out.println("SQL State: " + e.getSQLState());
            System.out.println("Message: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
