import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class MySQLConnectionTest {
    public static void main(String[] args) {
        String url = "******************************************************************************";
        String username = "root";
        String password = "44033@lzd";

        System.out.println("Connecting to MySQL database...");
        
        try {
            // 加载MySQL JDBC驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 尝试连接
            Connection connection = DriverManager.getConnection(url, username, password);
            
            if (connection != null) {
                System.out.println("Database connection successful!");
                System.out.println("Connected to: " + connection.getMetaData().getURL());
                System.out.println("Database product name: " + connection.getMetaData().getDatabaseProductName());
                System.out.println("Database product version: " + connection.getMetaData().getDatabaseProductVersion());
                connection.close();
            }
        } catch (ClassNotFoundException e) {
            System.out.println("MySQL JDBC Driver not found!");
            e.printStackTrace();
        } catch (SQLException e) {
            System.out.println("Connection failed! Error details:");
            e.printStackTrace();
        }
    }
}
